CREATE OR R<PERSON>LACE 
PROCEDURE             RIS_UPDATE_KETQUA_CDHA_CT(P_SO_PHIEU_CDHA          VARCHAR2,
                                                                  P_DVTT                   VARCHAR2,
                                                                  P_MA_CDHA                NUMBER,
                                                                  P_KET_QUA                CLOB,
                                                                  P_KET_LUAN               CLOB,
                                                                  P_LOIDAN                 VARCHAR2,
                                                                  P_BACSITHUCHIEN          VARCHAR2 DEFAULT NULL,
                                                                  P_KYTHUATVIEN            VARCHAR2 DEFAULT NULL,
                                                                  P_THOIGIANBATDAUTHUCHIEN VARCHAR2 DEFAULT NULL,
                                                                  CUR                      OUT SYS_REFCURSOR) IS
  VCOUNT                   NUMBER(11);
  P_NOITRU                 NUMBER;
  V_TENBACSITHUCHIEN       VARCHAR2(300);
  V_THAMSOCMU              VARCHAR2(255) := 0;
  V_KHOA                   NUMBER(11) := 0;
  V_SOVAOVIEN              NUMBER(11) := 0;
  V_SOVAOVIEN_DT           NUMBER(11) := 0;

  V_KETQUA                 CLOB := REPLACE(REPLACE(P_KET_QUA,
                                                   CHR(13) || CHR(13),
                                                   CHR(13)),
                                           CHR(10) || CHR(10),
                                           CHR(10));
  V_KETLUAN                CLOB := REPLACE(REPLACE(P_KET_LUAN,
                                                   CHR(13) || CHR(13),
                                                   CHR(13)),
                                           CHR(10) || CHR(10),
                                           CHR(10));
  V_THOIGIANBATDAUTHUCHIEN DATE := TO_DATE(P_THOIGIANBATDAUTHUCHIEN,
                                           'dd/mm/yyyy HH24:MI:SS');
	v_result number;
	p_mabenhnhan number;
BEGIN
  VCOUNT := 0;
BEGIN
SELECT COUNT(1)
INTO P_NOITRU
FROM HIS_MANAGER.NOITRU_CD_CDHA T
WHERE T.SO_PHIEU_CDHA = P_SO_PHIEU_CDHA
  AND T.DVTT = P_DVTT;
EXCEPTION
    WHEN NO_DATA_FOUND THEN
      P_NOITRU := 0;
END;

BEGIN
SELECT NV.TEN_NHANVIEN
INTO V_TENBACSITHUCHIEN
FROM HIS_FW.DM_NHANVIEN NV
WHERE NV.MA_NHANVIEN = P_BACSITHUCHIEN;
EXCEPTION
    WHEN NO_DATA_FOUND THEN
      V_TENBACSITHUCHIEN := '';
END;

  IF P_NOITRU = 0 THEN
SELECT SOVAOVIEN, mabenhnhan
INTO V_SOVAOVIEN, p_mabenhnhan
FROM HIS_MANAGER.KB_CD_CDHA
WHERE DVTT = P_DVTT
  AND SO_PHIEU_CDHA = P_SO_PHIEU_CDHA;
IF V_THAMSOCMU = '1' THEN


SELECT KHOA_THANHTOAN
INTO V_KHOA
FROM KB_PHIEUTHANHTOAN
WHERE DVTT = P_DVTT
  AND SOVAOVIEN = V_SOVAOVIEN;
IF V_KHOA = 1 THEN
        OPEN CUR FOR
SELECT VCOUNT AS TT FROM DUAL;
RETURN;
END IF;
END IF;
UPDATE HIS_MANAGER.KB_CD_CDHA
SET TRANG_THAI_CHUAN_DOAN = 1, KY_THUAT_VIEN = P_KYTHUATVIEN
WHERE DVTT = P_DVTT
  AND SO_PHIEU_CDHA = P_SO_PHIEU_CDHA;
UPDATE KB_CD_CDHA_CT CT
SET KET_QUA         = V_KETQUA,
    MO_TA           = V_KETLUAN,
    MO_TA_XML5      = UTL_I18N.UNESCAPE_REFERENCE(REGEXP_REPLACE(V_KETLUAN,
                                                                 '<.*?>',
                                                                 '')),
    KET_QUA_XML5    = UTL_I18N.UNESCAPE_REFERENCE(REGEXP_REPLACE(V_KETQUA,
                                                                 '<.*?>',
                                                                 '')),
    LOIDANBACSI     = P_LOIDAN,
    DA_CHAN_DOAN    = 1,
    KYTHUATVIEN     = P_KYTHUATVIEN,
    BACSI_THUCHIEN  = V_TENBACSITHUCHIEN,
    MABS_THUCHIEN   = P_BACSITHUCHIEN,
    NGUOI_THUC_HIEN = P_BACSITHUCHIEN,
    NGAY_TH_YL      = V_THOIGIANBATDAUTHUCHIEN,
    MA_BS_DOC_KQ    = P_BACSITHUCHIEN,
    NGAY_THUC_HIEN  = SYSDATE--NVL(NGAY_THUC_HIEN,SYSDATE)

WHERE DVTT = P_DVTT
  AND SO_PHIEU_CDHA = P_SO_PHIEU_CDHA
  AND MA_CDHA = P_MA_CDHA;

VCOUNT := SQL%ROWCOUNT;

COMMIT;

ELSE
SELECT SOVAOVIEN, SOVAOVIEN_DT, MABENHNHAN
INTO V_SOVAOVIEN, V_SOVAOVIEN_DT, p_mabenhnhan
FROM HIS_MANAGER.NOITRU_CD_CDHA
WHERE DVTT = P_DVTT
  AND SO_PHIEU_CDHA = P_SO_PHIEU_CDHA;
IF V_THAMSOCMU = '1' THEN


SELECT KHOA_THANHTOAN
INTO V_KHOA
FROM NOITRU_PHIEUTHANHTOAN
WHERE DVTT = P_DVTT
  AND SOVAOVIEN = V_SOVAOVIEN
  AND SOVAOVIEN_DT = V_SOVAOVIEN_DT;
IF V_KHOA = 1 THEN
        OPEN CUR FOR
SELECT VCOUNT AS TT FROM DUAL;
RETURN;
END IF;
END IF;
UPDATE HIS_MANAGER.NOITRU_CD_CDHA
SET TRANG_THAI_CHUAN_DOAN = 1, KY_THUAT_VIEN = P_KYTHUATVIEN
WHERE DVTT = P_DVTT
  AND SO_PHIEU_CDHA = P_SO_PHIEU_CDHA;

UPDATE NOITRU_CD_CDHA_CHI_TIET CT
SET KET_QUA         = V_KETQUA,
    MO_TA           = V_KETLUAN,
    KET_QUA_XML5    = REGEXP_REPLACE(V_KETQUA, '<[^>]+>', ''),
    MO_TA_XML5      = REGEXP_REPLACE(V_KETLUAN, '<[^>]+>', ''),
    LOIDANBACSI     = P_LOIDAN,
    DA_CHAN_DOAN    = 1,
    KYTHUATVIEN     = P_KYTHUATVIEN,
    BACSI_THUCHIEN  = V_TENBACSITHUCHIEN,
    MABS_THUCHIEN   = P_BACSITHUCHIEN,
    NGUOI_THUC_HIEN = P_BACSITHUCHIEN,
    NGAY_TH_YL      = V_THOIGIANBATDAUTHUCHIEN,
    MA_BS_DOC_KQ    = P_BACSITHUCHIEN,
    NGAY_THUC_HIEN  = SYSDATE --NVL(NGAY_THUC_HIEN,SYSDATE)
WHERE DVTT = P_DVTT
  AND SO_PHIEU_CDHA = P_SO_PHIEU_CDHA
  AND MA_CDHA = P_MA_CDHA;
VCOUNT := SQL%ROWCOUNT;

COMMIT;
END IF;
	v_result:=CMU_THOIGIAN_NHANVIEN_INS(
		P_DVTT ,
		P_BACSITHUCHIEN ,
		V_SOVAOVIEN ,
		P_MABENHNHAN ,
		P_NOITRU  ,
		to_char(V_THOIGIANBATDAUTHUCHIEN, 'DD/MM/YYYY/ HH24:MI'),
		to_char(SYSDATE, 'DD/MM/YYYY/ HH24:MI'),
		P_SO_PHIEU_CDHA,
		P_MA_CDHA,
		'CDHA'
	);
OPEN CUR FOR
SELECT VCOUNT AS TT FROM DUAL;
END;
