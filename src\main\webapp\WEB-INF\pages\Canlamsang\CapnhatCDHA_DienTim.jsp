﻿﻿
<%@page import="l2.ThamSoManager" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="google-site-verification" content="u6uNEfD4cb3gidezi4r_6aI8Wb1E07-ufBeCQpvmlqQ"/>
    <title>Hệ thống chăm sóc sức khỏe</title>
    <link rel="icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
    <link rel="shortcut icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>

    <!-- jQuery file -->
    <link href="<c:url value="/resources/css/divheader.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/css/style_new.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/css/magiczoomplus.css" />" rel="stylesheet"/>

    <!--Jquery-->
    <link rel="stylesheet" href="<c:url value="/resources/css/jquery-ui-redmond.1.9.1.css" />"/>
    <script src="<c:url value="/resources/js/jquery.min.1.8.3.js" />"></script>
    <script src="<c:url value="/resources/js/jquery-ui.1.9.1.js" />"></script>
    <!--Grid-->
    <link href="<c:url value="/resources/jqgrid/css/ui.jqgrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/jqgrid/js/i18n/grid.locale-en.js" />"></script>
    <script src="<c:url value="/resources/jqgrid/js/jquery.jqGrid.src.js" />"></script>
    <script src="<c:url value="/resources/js/common_function.js" />"></script>
    <script src="<c:url value="/resources/js/jquery.inputmask.bundle.min.js" />"></script>
    <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
    <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/dialog/jquery.alerts.1.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/dialog/jquery.alerts.js" />"></script>
    <script src="<c:url value="/resources/js/read_file.js" />"></script>
    <link href="<c:url value="/resources/dialog/jBox.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/dialog/jBox.js" />"></script>

    <link href="<c:url value="/resources/combogrid/css/smoothness/jquery.ui.combogrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/combogrid/plugin/jquery.ui.combogrid-1.6.3.js" />"></script>
    <link href="<c:url value="/resources/combogrid/css/smoothness/jquery-ui-1.10.1.custom.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/combogrid/jquery/jquery-ui-1.10.1.custom.min.js" />"></script>
    <script src="<c:url value="/resources/editor/ckeditor.js" /> "language="javascript"></script>
    <script src="<c:url value="/resources/ckeditor/adapters/jquery.js" />"></script>
    <script src="<c:url value="/resources/webcam/say-cheese.js" />"></script>
    <link href="<c:url value="/resources/webcam/pygments.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/webcam/say-cheese.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
    <script src="<c:url value="/resources/js/magiczoomplus.js" />"></script>
    <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/jqueryui/themes/redmond/jquery-ui.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/ris/deployJava.js"/>"></script>
    <script src="<c:url value="/resources/js/canlamsang/grid_danhsachchidinh.js" />"></script>
    <script src="<c:url value="/resources/smoothdivscroll/smoothdivscroll.js" />"></script>

    <link href="<c:url value="/resources/bootstrap/css/select2.min.css"/>" rel="stylesheet">
    <script src="<c:url value="/resources/bootstrap/js/select2.min.js"/>" ></script>
    <script src="<c:url value="/resources/camau/vnpt-plugin_v692020.js" />"></script>
    <script src="<c:url value="/resources/camau/vgcaplugin.js" />"></script>
    <script src="<c:url value="/resources/camau/keytichhop.js" />"></script>
    <script src="<c:url value="/resources/camau/sha256.min.js"/>" ></script>
    <script src="<c:url value="/resources/camau/material/moment.js" />"></script>
    <script src="<c:url value="/resources/camau/js/moment.js" />"></script>

    <%--datetimepicker--%>
    <script src="/web_his/resources/js/datetimepicker.js"></script>
    <link href="/web_his/resources/css/datetimepicker.css" rel="stylesheet"/>
    <style>
        .width1 {
            width: 200px;
        }

        .width2 {
            width: 545px;
        }

        .width3 {
            width: 150px;
        }

        .width4 {
            width: 325px;
        }

        .width5 {
            width: 810px;
        }

        .width6 {
            width: 500px;
        }

        legend {

            color: red;
        }

        .width100 {
            width: 100%;
        }

        span.cellWithoutBackground {
            display: block;
            background-image: none;
            margin-right: -2px;
            margin-left: -2px;
            height: 14px;
            padding: 4px;
        }

        /*CMU 2606/2017*/
        #list_lichsuCDHA tr.jqgrow td {
            white-space: normal !important;
            height: auto;
            vertical-align: text-top;
        }
    </style>
    <script>
        var sovaovien;
        var sovaovien_noi;
        var sovaovien_dt_noi;
        var da_thanh_toan;
        var flag_noitru = "-1";
        var matoathuoc;
        var makhovattu;
        var url_loadtonkho;
        var url_loadthuoc;
        var ngay_kb;
        var cobhyt;
        var sophieuthanhtoan;
        var tlmg;
        var showtime_giothuchien_cls_cancel = 0;
        var showtime_gioth_ct_cancel = 0;
        var giothuchien_cls_timer_is_on = false;
        var giothuchien_cls_timer_previous_status = false;
        var gioth_ct_timer_is_on = false;
        var gioth_ct_timer_previous_status = false;
        var giothuchien_cls_timer;
        var gioth_ct_timer;
        var ma_cdha = 0;
        var sophieu = "";
        var mabenhnhan = "";
        var stt_benhan = "";
        var stt_dotdieutri = "";
        var stt_dieutri = "";
        var phongcdha_ss;
        var noitru_ngoaitru;
        var bacsi_chidinh;
        var co_bao_hiem;
        var sobenhan_noitru_tt;
        var sobenhan_noitru;
        var icd_benhnhan;
        var ten_icd_benhnhan;
        var soba;
        var ngay_chi_dinh;
        var ngaychidinh;
        var giochidinh;
        var tatAutoTime = "<%= ThamSoManager.instance(session).getThamSoString(ThamSoManager.THAMSO_28084881,"0")%>";
        var SESS_PHONG_BAN = "<%= session.getAttribute("Sess_PhongBan").toString()%>";
        var SESS_USER_ID = "<%= session.getAttribute("Sess_UserID").toString()%>";
        var THAMSO_828449 = "<%= ThamSoManager.instance(session).getThamSoString("828449", "0")%>";
        var THAMSO_960627 = "<%= ThamSoManager.instance(session).getThamSoString("960627", "0")%>";
        var THAMSO_960626 = "<%= ThamSoManager.instance(session).getThamSoString("960626", "0")%>";

        function checkNguoiDocKetQua() {
            return $('#cboNguoiDocKetQua').val() == '';
        }

        function setNguoiDocKetQua(maBacSi) {
            var _selector = $('#cboNguoiDocKetQua');
            _selector.val(maBacSi||SESS_USER_ID);
        }

        function loadBacSiTheoKhoa(strListMaKhoa = "-1") {
            // Fetch the preselected item, and add to the control
            var _selector = $('#cboNguoiDocKetQua');
            $.ajax({
                type: 'GET',
                url: 'cmu_getlist?url='+convertArray(["${Sess_DVTT}",strListMaKhoa,"SEL_LIST_BAC_SI_THEO_KHOA"]),
            }).then(function (data) {
                if(jQuery.isArray(data) ) {
                    _selector.empty();
                    // $('#cboDoiTuongTiepNhan').append($('<option>', { value: '0', text : 'Khác', color: '' }));
                    _selector.append($('<option>', {
                        value: SESS_USER_ID,
                        text : "${Sess_User}",
                        "data-khoa": SESS_PHONG_BAN
                    }));
                    $.each(data, function (i, item) {
                        if(!!item && item.MA_NHANVIEN && item.TEN_NHANVIEN && item.MA_PHONGBAN && item.MA_NHANVIEN != SESS_USER_ID) {
                            _selector.append($('<option>', {
                                value: item.MA_NHANVIEN,
                                text : item.TEN_NHANVIEN,
                                "data-khoa": item.MA_PHONGBAN
                            }));
                        }
                    });
                    _selector.val(SESS_USER_ID);
                    if(!!!_selector.val()) {
                        // jAlert("Tài khoản đăng nhập không thuộc khoa đã cấu hình", "Cảnh báo");
                    }
                }
            }).always(function( data, textStatus, jqXHR ) {

            });
        }

        function luuNguoiDocKetQua(_soPhieu, _soVaoVien, _soVaoVienDt = 0, _noiTru = 0, _listMaCls = "-1") {
            $.post("cmu_post", {
                url: ["${Sess_UserID}", "${Sess_DVTT}",
                    _soPhieu,
                    _soVaoVien,
                    _soVaoVienDt,
                    _noiTru,
                    SESS_PHONG_BAN,
                    $('#cboNguoiDocKetQua').val(),
                    _listMaCls,
                    'CDHA',
                    'UPD_CLS_NGUOI_DOC_KET_QUA'].join("```")
            })
        }

        function getNguoiDocKetQua(_soPhieu, _soVaoVien, _soVaoVienDt = 0, _noiTru = 0, _listMaCls = "-1") {

            $.ajax({
                type: 'GET',
                url: 'select-thong-tin-theo-benh-nhan',
                data: {
                    p_soPhieu: _soPhieu,
                    p_soVaoVien: _soVaoVien,
                    p_soVaoVienDt: _soVaoVienDt,
                    p_noiTru: _noiTru,
                    p_sessKhoaId: SESS_PHONG_BAN,
                    p_loai_cls: 'CDHA',
                    p_listMaCls: _listMaCls,
                    action: "SEL_CLS_NGUOI_DOC_KET_QUA"
                }
            }).then(function (data) {
                if(!!data && !!data[0]){
                    var obj = data[0];
                    setNguoiDocKetQua(obj.MA_NHANVIEN);
                }
            }).always(function( data, textStatus, jqXHR ) {

            });
        }
        //VLG chinh textbox gio tra kq chay thoi gian
        function addZero(i) {
            if (i < 10) {
                i = "0" + i;
            }
            return i;
        }

        function gioThucHienClsTimerChange() {
            if("${Sess_DVTT}"=="14017" || "${Sess_DVTT}".substring(0,2) == "96"){
                stopGioThucHienClsTimer();
            } else if (tatAutoTime == 1) {
                stopGioThucHienClsTimer();
            } else if (giothuchien_cls_timer_is_on || ($("#dathuchien").prop('checked') && ${capnhat_cls_timer_off} == 1))
                stopGioThucHienClsTimer();
            else
                showtime_giothuchien_cls();
        }

        function gioThCtTimerChange() {
            if("${Sess_DVTT}"=="14017" || "${Sess_DVTT}".substring(0,2) == "96"){
                stopGioThCtTimer();
            } else if (tatAutoTime == 1) {
                stopGioThCtTimer();
            }else if (gioth_ct_timer_is_on || ($("#gioth_ct").data('da-thuc-hien') && ${capnhat_cls_timer_off} == 1))
                stopGioThCtTimer();
            else
                showtime_gioth_ct();
        }

        function showtime_giothuchien_cls() {
            var thoigian = new Date();
            var gio = addZero(thoigian.getHours());
            var phut = addZero(thoigian.getMinutes());
            var giay = addZero(thoigian.getSeconds());
            /*if(showtime_giothuchien_cls_cancel !== 1) {
                $('#giothuchien_cls').val(gio + ":" + phut + ":" + giay);
            }
            t = setTimeout(showtime_giothuchien_cls, 1000);*/
            $('#giothuchien_cls').val(gio + ":" + phut + ":" + giay);
            giothuchien_cls_timer = setTimeout(showtime_giothuchien_cls, 1000);
            giothuchien_cls_timer_is_on = true;
        }

        function showtime_gioth_ct() {
            var thoigian = new Date();
            var gio = addZero(thoigian.getHours());
            var phut = addZero(thoigian.getMinutes());
            var giay = addZero(thoigian.getSeconds());
            /*if(showtime_gioth_ct_cancel !== 1) {
                $('#gioth_ct').val(gio + ":" + phut + ":" + giay);
            }
            t = setTimeout(showtime_gioth_ct, 1000);*/
            $('#gioth_ct').val(gio + ":" + phut + ":" + giay);
            gioth_ct_timer = setTimeout(showtime_gioth_ct, 1000);
            gioth_ct_timer_is_on = true;
        }

        function stopGioThucHienClsTimer() {
            clearTimeout(giothuchien_cls_timer);
            giothuchien_cls_timer_is_on = false;
        }

        function stopGioThCtTimer() {
            clearTimeout(gioth_ct_timer);
            gioth_ct_timer_is_on = false;
        }

        function stopCount() {
            clearTimeout(t);
            timer_is_on = 0;
        }

        //VLG chinh textbox gio tra kq chay thoi gian

        function delete_thuocnoitru(list, nghiepvu) {
            var id = $("#list_thuocdichvu").jqGrid('getGridParam', 'selrow');
            if (id) {
                var ret = $("#list_thuocdichvu").jqGrid('getRowData', id);
                if (ret.MA_BAC_SI_THEMTHUOC === undefined || ret.MA_BAC_SI_THEMTHUOC === "${Sess_UserID}") {
                    jConfirm('Bạn có muốn xóa không?', 'Thông báo', function (r) {
                        if (r.toString() === "true") {
                            var url = "noitru_toathuoc_delete";
                            if (list === "list_thuoctonghopall")
                                nghiepvu = ret.NGHIEP_VU;
                            var arr = [ret.STT_TOATHUOC, matoathuoc, "${Sess_DVTT}", $("#sttdieutri").val(), $("#sttbenhan").val(), $("#sttdotdieutri").val(), sophieuthanhtoan,
                                ret.THANHTIEN_THUOC, ret.MAKHOVATTU, "0", nghiepvu, ret.MAVATTU, ret.TEN_VAT_TU, ret.SO_LUONG];
                            $.post(url, {
                                url: convertArray(arr)
                            }).done(function (data) {
                                if (data === "1")
                                    jAlert("Bệnh nhân đã thanh toán viện phí", 'Cảnh báo');
                                else if (data === "2")
                                    jAlert("Bệnh nhân đã được xuất thuốc", 'Cảnh báo');
                                else if (data == '100') {
                                    jAlert("Đã chốt báo cáo dược, không thể sửa/xóa", 'Cảnh báo');
                                }
                                else {
                                    $("#list_thuocdichvu").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                                }
                            });
                        }
                    });
                } else {
                    jAlert("Bạn không được xóa thuốc trong toa của bác sĩ khác!", 'Cảnh báo');
                }
            } else {
                jAlert("Chọn một dòng thuốc để xóa", 'Cảnh báo');
            }
        }

        function delete_toathuocngoaitru(list, nghiepvu) {
            var id = $("#list_thuocdichvu").jqGrid('getGridParam', 'selrow');
            if (id) {
                var ret = $("#list_thuocdichvu").jqGrid('getRowData', id);
                var dongia = ret.DONGIA_BAN_BH;
                var arr = [ret.STT_TOATHUOC, matoathuoc, ${Sess_DVTT}, $("#makhambenh").val(), sophieuthanhtoan, ret.THANHTIEN_THUOC,
                    ret.MAKHOVATTU, dongia, nghiepvu, ret.MAVATTU, $("#mabenhnhan").val(), ngay_kb, sovaovien, encodeURIComponent(ret.TEN_VAT_TU), ret.SO_LUONG];
                var url = "xoathuocngoaitru_giamtai?url=" + convertArray(arr);
                $.ajax({
                    url: url
                }).done(function (data) {
                    if (data === "1")
                        jAlert("Bệnh nhân đã thanh toán", 'Cảnh báo');
                    else if (data === "2")
                        jAlert("Bệnh nhân đã được xuất thuốc", 'Cảnh báo');
                    else if (data === "3")
                        jAlert("Bệnh nhân đã được trả thuốc về kho", 'Cảnh báo');
                    else if (data == '100') {
                        jAlert("Đã chốt báo cáo dược, không thể sửa/xóa", 'Cảnh báo');
                    }
                    else
                        $("#list_thuocdichvu").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                });
            } else {
                jAlert("Chọn 1 dòng thuốc để xóa", 'Cảnh báo');
            }
        }
        function newStringDateTime() {
            var date = new Date();
            var dateStr =
                ("00" + date.getDate()).slice(-2) + "/" +
                ("00" + (date.getMonth() + 1)).slice(-2) + "/" +
                date.getFullYear() + " " +
                ("00" + date.getHours()).slice(-2) + ":" +
                ("00" + date.getMinutes()).slice(-2) + ":" +
                ("00" + date.getSeconds()).slice(-2);
            return dateStr;
        }
        window.listIdTimer = [];
        window.listIdTimerRunning = [];
        function runningInputTimer() {
            var listIdTimer = window.listIdTimer;
            var listIdTimerRunning = window.listIdTimerRunning;
            var timerSubfix = "_timer";
            listIdTimerRunning.forEach(function (item) {
                clearTimeout(window[item + timerSubfix]);// stop all timer in list
            });
            window.listIdTimerRunning = [];
            listIdTimer.forEach(function (item) {
                window[item + timerSubfix] = setTimeout(runningInputTimer, 1000);
                window.listIdTimerRunning.push(item);
                $("#" + item).val(newStringDateTime());
                // console.log(newStringDateTime());
                // console.log($("#" + item).val());
            });
        }

        function changeInputTimerStatus(selectorId, isStopTimer = false) {
            if (!isStopTimer && $.inArray(selectorId, window.listIdTimer) == -1) {
                window.listIdTimer.push(selectorId);
            }
            if (isStopTimer) {
                window.listIdTimer = $.grep(window.listIdTimer, function(n) {
                    return n != selectorId;
                });
            }
            if(window.listIdTimer && window.listIdTimer.length > 0) {
                runningInputTimer();
            }
            // console.log($("#" + selectorId).val());
        }
        function dsKhoakythuatvien() {
            var url = "cmu_list_CMU_DS_NHANVIEN_ALL?url=" + convertArray([$("#khoakythuatvien").val()]);
            $.ajax({
                url: url
            }).done(function (data) {
                if (data) {
                    $("#cmu_kythuatvien").empty();
                    $.each(data, function (i) {
                        var selected = (i == 0) || data[i].MA_NHANVIEN == '${Sess_UserID}'?"selected":"";
                        $("<option value='" + data[i].MA_NHANVIEN + "' " + selected + ">" + data[i].TEN_NHANVIEN + "</option>").appendTo("#cmu_kythuatvien");
                    });
                }
            });
        }
        function dsKhoabacsidockq() {
            var url = "laybacsi_theokhoa?khoa=" + $("#khoabacsidocketqua").val();
            $.ajax({
                url: url
            }).done(function (data) {
                if (data) {
                    $("#cboNguoiDocKetQua").empty();
                    $.each(data, function (i) {
                        var selected = (i == 0) || data[i].MA_NHANVIEN == '${Sess_UserID}'?"selected":"";
                        $("<option value='" + data[i].MA_NHANVIEN + "' " + selected + ">" + data[i].TEN_NHANVIEN + "</option>").appendTo("#cboNguoiDocKetQua");
                    });
                }
            });
        }
        $(function () {
            if ("${Sess_DVTT}".substring(0, 2) == "94") {
                $("#loaiin").val("2");
            }
            $(":input").inputmask();
            $("#ngaythuchien_cls").datepicker();
            $("#ngaythuchien_cls").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngaythuchien_cls").val("${ngayhientai}");

            $("#ngayth_ct").datepicker();
            $("#ngayth_ct").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngayth_ct").val("${ngayhientai}");

            $(":input").inputmask();
            $("#ngaythuchien").datepicker();
            $("#ngaythuchien").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngaythuchien").val("${ngayhientai}");

            $("#nhanvien").select2();

            khoiTaoSuKienInputIcd("#icdChanDoanCanLamSang","#btnChanDoanCanLamSang","#tenChanDoanCanLamSang","#maBenhLyChanDoanCanLamSang");
            $("#thoiGianBatDau_cls").inputmask({
                mask: "1/2/y h:s:s",
                placeholder: "dd/mm/yyyy 00:00:00",
                alias: "datetime",
                hourFormat: "24"
            });
            $("#thoiGianBatDau_cls").datetimepicker({
                dateFormat: 'dd/mm/yy',
                timeFormat: 'HH:mm:ss',
                hourFormat: "24"
            });
            /*$("#thoiGianBatDau_cls").click(function (evt) {
                changeInputTimerStatus("thoiGianBatDau_cls", true);
            }).change(function (evt) {
                changeInputTimerStatus("thoiGianBatDau_cls", true);
            }).dblclick(function(){
                changeInputTimerStatus("thoiGianBatDau_cls");
            });*/
            $("#thoiGianBatDau_cls").val(newStringDateTime());
            //changeInputTimerStatus("thoiGianBatDau_cls");
            // ĐắkLắk: tùy chọn hiển thị lọc theo khoa, phòng, đối tượng
            if ("${timkiem_cls}" == "1") {
                $(".hpg_tmp").show();
                $("#tungay").datepicker();
                $("#tungay").datepicker("option", "dateFormat", "dd/mm/yy");
                $("#tungay").val("${ngayhientai}");
                $("#tungay").change(function (evt) {
                    reload_grid();
                });
            } else {
                $(".hpg_tmp").hide();
            }

            if ("${choloctheokhoaphong_capnhatketquacls}" == "1")
                $(".dlk_tmp").show();
            else
                $(".dlk_tmp").hide();
            if ("${hienthi_mausac_bn}" == "1")
                $(".ghichutrangthai").show();
            else
                $(".ghichutrangthai").hide();
            // End ĐắkLắk
            if ("${thuocdvthcls}" == "1") {
                $("#thuoccls").show();
            } else {
                $("#thuoccls").hide();
            }
            var dataURL = "";
            $("#list_benhnhan").jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 440,
                width: 300,
                colNames: ["Mã y tế", "Họ tên", "TENBENHNHAN", "Tuổi", "Nội trú", "gioitinh", "diachi", "sobhyt", "SO_PHIEU", "MA_KHAM_BENH",
                    "stt_benhan", "stt_dotdieutri", "stt_dieutri", "NGUOI_CHI_DINH", "BACSI_THUCHIEN", "PHONG_CHI_DINH", "MA_PHONG_XN", "KET_QUA_CDHA", "STT", "cannang", "chieucao", "khoa", "Buong", "Giuong", "chuandoanicd", "SOVAOVIEN", "SOVAOVIEN_NOI", "SOVAOVIEN_DT_NOI", "DA_THANH_TOAN",
                    // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: thêm thông tin khoa chỉ định, chẩn đoán icd
                    "CHUANDOANICD", "TENKHOA", "CAPCUU", "MOTA_CDHA", "sophieuthanhtoan", "tilemiengiam", "ngay_kb", "CO_BHYT","SOBENHAN", "SOBENHAN_TT", "ICD", "NGAYTHUCHIEN", "NGUOITHUCHIEN", "NGAY_CHI_DINH"], // TGGDEV-36575 thêm NGAYTHUCHIEN
                // End ĐắkLắk,
                colModel: [
                    {name: 'MABENHNHAN', index: 'MABENHNHAN', width: 100},
                    {
                        name: 'TENBENHNHAN_HT',
                        index: 'TENBENHNHAN_HT',
                        width: 200,
                        formatter: function (cellvalue, options, rowObject) {
                            var color;
                            var color_text;
                            if ("${hienthi_mausac_bn}" == "0") {
                                if (rowObject.DA_THANH_TOAN == "1") {
                                    color = '#009900';
                                    color_text = 'white';
                                }   //END VTU:25/10/2016
                                else {
                                    if ("${hienthi_mausac_bn}" == "0") {
                                        color = 'white';
                                        color_text = 'black';
                                    }
                                }
                            }
                            return '<span class="cellWithoutBackground" style="background-color:' + color + ';font-weight:bold ;color:' + color_text + '">' + cellvalue + '</span>';
                        }
                    },
                    {
                        name: 'TENBENHNHAN',
                        index: 'TENBENHNHAN',
                        width: 200,
                        hidden: true,
                        sorttype: 'string',
                        searchoptions: {
                            dataInit: function (el) {
                                setTimeout(function () {
                                    $(el).focus().trigger({type: 'keypress', charCode: 13});
                                }, 20);
                            }
                        }
                    },
                    {name: 'TUOI', index: 'TUOI', width: 50, align: 'right'},
                    {name: 'NOITRU', index: 'NOITRU', width: 50, align: 'center'},
                    {name: 'GIOITINH', index: 'GIOITINH', hidden: true},
                    {name: 'DIACHI', index: 'DIACHI', hidden: true},
                    {name: 'SOTHEBHYT', index: 'SOTHEBHYT', hidden: true},
                    {name: 'SO_PHIEU', index: 'SO_PHIEU', hidden: true},
                    {name: 'MA_KHAM_BENH', index: 'MA_KHAM_BENH', hidden: true},
                    {name: 'STT_BENHAN', index: 'STT_BENHAN', hidden: true},
                    {name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', hidden: true},
                    {name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', hidden: true},
                    {name: 'NGUOI_CHI_DINH', index: 'NGUOI_CHI_DINH', hidden: true},
                    {name: 'BACSI_THUCHIEN', index: 'BACSI_THUCHIEN', hidden: true},
                    {name: 'PHONG_CHI_DINH', index: 'PHONG_CHI_DINH', hidden: true},
                    {name: 'MA_PHONG_CDHA', index: 'MA_PHONG_XN', hidden: true},
                    {name: 'KET_QUA_CDHA', index: 'KET_QUA_CDHA', hidden: true},
                    {name: 'STT_HANGNGAY', index: 'STT_HANGNGAY', width: 50, sorttype: 'int'},
                    {name: 'CANNANG', index: 'CANNANG', hidden: true},
                    {name: 'CHIEUCAO', index: 'CHIEUCAO', hidden: true},
                    {name: 'KHOA', index: 'KHOA', hidden: true},
                    {name: 'BUONG', index: 'BUONG', hidden: true},
                    {name: 'GIUONG', index: 'GIUONG', hidden: true},
                    {name: 'CHUANDOANICD', index: 'CHUANDOANICD', hidden: true},
                    {name: 'SOVAOVIEN', index: 'SOVAOVIEN', hidden: true},
                    {name: 'SOVAOVIEN_NOI', index: 'SOVAOVIEN_NOI', hidden: true},
                    {name: 'SOVAOVIEN_DT_NOI', index: 'SOVAOVIEN_DT_NOI', hidden: true},
                    {name: 'DA_THANH_TOAN', index: 'DA_THANH_TOAN', hidden: true},
                    {name: 'CHUANDOANICD', index: 'CHUANDOANICD', hidden: true},
                    {name: 'TENKHOA', index: 'TENKHOA', hidden: true},
                    {name: 'CAPCUU', index: 'CAPCUU', hidden: true},
                    {name: 'MOTA_CDHA', index: 'MOTA_CDHA', hidden: true},
                    {name: 'SOPHIEUTHANHTOAN', index: 'SOPHIEUTHANHTOAN', hidden: true},
                    {name: 'TI_LE_MIEN_GIAM', index: 'TI_LE_MIEN_GIAM', hidden: true},
                    {name: 'NGAY_KB', index: 'NGAY_KB', hidden: true},
                    {name: 'CO_BHYT', index: 'CO_BHYT', hidden: true},
                    {name: 'SOBENHAN', index: 'SOBENHAN', hidden: true},
                    {name: 'SOBENHAN_TT', index: 'SOBENHAN_TT', hidden: true},
                    {name: 'ICD', index: 'ICD', hidden: true},
                    {name: 'NGAY_THUC_HIEN', index: 'NGAY_THUC_HIEN', hidden: true}, // TGGDEV-36575 thêm
                    {name: 'NGUOI_THUC_HIEN', index: 'NGUOI_THUC_HIEN', hidden: true},
                    {name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', hidden: true}
                ],
                caption: "Danh sách bệnh nhân",
                ignoreCase: true,
                viewrecords: true,
                rowNum: 200,
                pager: '#pager2',
                gridComplete: function () {
                    var c = $("#list_benhnhan").getGridParam("records");
                    $("#list_benhnhan").jqGrid('setCaption', "Danh sách bệnh nhân (" + c + " bệnh nhân)");
                    if ("${hienthi_mausac_bn}" == "1") {
                        var rows = $("#list_benhnhan").getDataIDs();
                        for (var i = 0; i < rows.length; i++) {
                            var CAPCUU = $("#list_benhnhan").jqGrid('getRowData')[i]["CAPCUU"];
                            //var SOTHEBHYT = $("#list_benhnhan").jqGrid('getRowData')[i]["SOTHEBHYT"].toString().length;
                            var CO_BHYT = $("#list_benhnhan").jqGrid('getRowData')[i]["CO_BHYT"].toString().length;
                            var tuoi = $("#list_benhnhan").jqGrid('getRowData')[i]["TUOI"];
                            var thanhtoan = $("#list_benhnhan").jqGrid('getRowData')[i]["DA_THANH_TOAN"];
                            if (CAPCUU === "1") {
                                $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {
                                    color: 'red',
                                    weightfont: 'bold'
                                });
                            } else if (CO_BHYT == 0&& thanhtoan == 0) {//CMU:26/10/2017
                                $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {
                                    color: '#bf00ff',
                                    weightfont: 'bold'
                                });
                            } else if (CO_BHYT == 0 && thanhtoan == 1) {
                                $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {
                                    color: '##EE7600',
                                    weightfont: 'bold'
                                });
                            } else if (tuoi.indexOf("tháng") != -1) {
                                $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {
                                    color: '#00ff00',
                                    weightfont: 'bold'
                                });
                            } else {
                                $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {
                                    color: 'black',
                                    weightfont: 'bold',
                                    background: 'white'
                                });
                            }
                        }
                    }
                },
                ondblClickRow: function (id) {
                    if (id) {
                        var ret = $("#list_benhnhan").jqGrid('getRowData', id);
                        //CMU Cảnh báo ngoại trú chưa đóng tiền
                        if ("${cdhacanhbao}" > 0 && ret.NOITRU == 0 && ret.CO_BHYT == 0 && ret.DA_THANH_TOAN == 0){
                            jAlert('Bệnh nhân ngoại trú chưa đóng viện phí.', 'Thông báo');
                            if ("${cdhacanhbao}" == 2) {
                                clear_benhnhan();
                                $("#list_dientim_bhyt").jqGrid('clearGridData');
                                return;
                            }
                        }
                        noitru_ngoaitru = ret.NOITRU;
                        co_bao_hiem = ret.CO_BHYT;
                        bacsi_chidinh = ret.NGUOI_CHI_DINH;
                        phongcdha_ss = ret.MA_PHONG_CDHA;
                        sobenhan_noitru = ret.SOBENHAN;
                        sobenhan_noitru_tt = ret.SOBENHAN_TT;
                        icd_benhnhan = ret.ICD;
                        ten_icd_benhnhan = ret.CHUANDOANICD.replace(ret.ICD + ' - ', '');
                        ngay_chi_dinh = ret.NGAY_CHI_DINH;
                        if(ngay_chi_dinh !== null){
                            var ngaygio_chichinh_cls = ngay_chi_dinh.split(" ");
                            ngaychidinh = ngaygio_chichinh_cls[0];
                            giochidinh = ngaygio_chichinh_cls[1];
                            $("#ngaychidinh_cls").val(ngaychidinh);
                            $("#giochidinh_cls").val(giochidinh);
                            $("#ngaychidinh_kq").val(ngaychidinh);
                            $("#giochidinh_kq").val(giochidinh);
                        }
                        $("#ma_maycdha_md").val("");
                        $("#mabenhnhan").val(ret.MABENHNHAN);
                        $("#hoten").val(ret.TENBENHNHAN);
                        $("#tuoi").val(ret.TUOI);
                        $("#gioitinh").val(ret.GIOITINH.toString());
                        $("#diachi").val(ret.DIACHI);
                        $("#sothebhyt").val(ret.SOTHEBHYT);
                        $("#sophieu").val(ret.SO_PHIEU);
                        $("#makhambenh").val(ret.MA_KHAM_BENH);
                        $("#noitru").val(ret.NOITRU);
                        $("#sttbenhan").val(ret.STT_BENHAN);
                        $("#sttdotdieutri").val(ret.STT_DOTDIEUTRI);
                        $("#sttdieutri").val(ret.STT_DIEUTRI);
                        $("#mabacsichidinh").val(ret.NGUOI_CHI_DINH);
                        // Sang start

                        if (ret.BACSI_THUCHIEN == '') {
                            $("#nhanvien").val(${Sess_UserID}).change();
                        }else{
                            $("#nhanvien").val(ret.NGUOI_THUC_HIEN).change();
                        }
                        // Sang end

                        // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: thêm thông tin khoa chỉ định, chẩn đoán icd
                        $("#_chandoan").val(ret.CHUANDOANICD);
                        $("#chandoan").val(ret.CHUANDOANICD);
                        $("#tenkhoa").val(ret.TENKHOA);
                        // End ĐắkLắk

                        // ĐắkLắk (An Giang yêu cầu) - Ninh 09/12/2016: view thông tin hành chánh của BN lên form nhập kết quả
                        $("#hoten_ct").val(ret.TENBENHNHAN);
                        $("#tuoi_ct").val(ret.TUOI);
                        $("#gioitinh_ct").val(ret.GIOITINH.toString() == "true" ? "Nam" : "Nữ");
                        $("#mabenhnhan_ct").val(ret.MABENHNHAN);
                        $("#tenkhoa_ct").val(ret.TENKHOA);
                        $("#sothebhyt_ct").val(ret.SOTHEBHYT);
                        // End ĐắkLắk

                        $("#cannang").val(ret.CANNANG);
                        $("#chieucao").val(ret.CHIEUCAO);
                        $("#Khoa").val(ret.TENKHOA);
                        $("#buong").val(ret.BUONG);
                        $("#giuong").val(ret.GIUONG);
                        $("#chandoan").val(ret.CHUANDOANICD);
                        $("#solan").val(ret.STT_HANGNGAY);
                        $("#mota_cdha").val(ret.MOTA_CDHA);
                        $("#cmu_ngaychidinh").val(ret.NGAY_CHI_DINH)
                        sovaovien = ret.SOVAOVIEN;
                        sovaovien_noi = ret.SOVAOVIEN_NOI;
                        $("#sovaovien").val(sovaovien == 0 ? sovaovien_noi : sovaovien);
                        sovaovien_dt_noi = ret.SOVAOVIEN_DT_NOI;
                        da_thanh_toan = ret.DA_THANH_TOAN;
                        sophieuthanhtoan = ret.SOPHIEUTHANHTOAN;
                        cobhyt = ret.TI_LE_MIEN_GIAM.replace('.00', '');
                        ngay_kb = ret.NGAY_KB;
                        tlmg = ret.TI_LE_MIEN_GIAM;
                        flag_noitru = ret.NOITRU;
                        mabenhnhan = ret.MABENHNHAN;
                        stt_dieutri = ret.STT_DIEUTRI;
                        stt_benhan = ret.STT_BENHAN;
                        stt_dotdieutri = ret.STT_DOTDIEUTRI;
                        var url_bs = "select_tenbacsi?mabacsi=" + ret.NGUOI_CHI_DINH + "&dvtt= " + "${Sess_DVTT}";
                        $.ajax({
                            url: url_bs
                        }).done(function (data) {
                            $("#bacsichidinh").val(data);
                        });

                        if (ret.NOITRU === "0") {
                            $("#kho_dv_ngoai").show();
                            $("#kho_dv_noi").hide();
                        } else {
                            $("#kho_dv_noi").show();
                            $("#kho_dv_ngoai").hide();
                        }
                        matoathuoc = flag_noitru === '1' ? (ret.SO_PHIEU.split('.')[2]).split('_')[0] : ret.MA_KHAM_BENH.replace("kb_", "tt_");
                        var dathuchien = $("#dathuchien").prop('checked');
                        var idath;
                        if (dathuchien == true) {
                            idath = 1;
                        } else {
                            idath = 0;
                        }
                        var arr = [ret.NOITRU, ret.SO_PHIEU, ret.STT_BENHAN, ret.STT_DOTDIEUTRI, ret.STT_DIEUTRI, "${Sess_DVTT}", sovaovien, sovaovien_noi, sovaovien_dt_noi, idath];
                        var url = "dientim_hienthi_chitiet_svv?url=" + convertArray(arr);
                        $("#list_dientim_bhyt").jqGrid('setGridParam', {
                            datatype: 'json',
                            url: url
                        }).trigger('reloadGrid');
                        if ("${hienthi_them_cls}" == "1") {
                            var hpg_STT_BENHAN = "0";
                            var hpg_STT_DIEUTRI = "0";
                            var hpg_STT_DOTDIEUTRI = "0";
                            if (ret.STT_DIEUTRI != "")
                                hpg_STT_BENHAN = ret.STT_BENHAN;
                            if (ret.STT_DIEUTRI != "")
                                hpg_STT_DOTDIEUTRI = ret.STT_DOTDIEUTRI;
                            if (ret.STT_DIEUTRI != "")
                                hpg_STT_DIEUTRI = ret.STT_DIEUTRI;
                            var arr1 = [ret.MA_KHAM_BENH, ret.SO_PHIEU, "${Sess_DVTT}", ret.NOITRU, hpg_STT_BENHAN, hpg_STT_DOTDIEUTRI, hpg_STT_DIEUTRI, 3]
                            var url1 = "hpg_thongtin_mo_rong_bn_cls?url=" + convertArray(arr1);
                            hienthi_them_cls(url1);
                        }
                        $.post('cmu_post_CMU_TONGTIEN_MONEY_NOIDUNG', {
                            url: ["${Sess_DVTT}",  sovaovien == 0? sovaovien_noi: sovaovien, sovaovien_dt_noi? sovaovien_dt_noi: 0, ret.SO_PHIEU].join('```')
                        }).done(function (data) {
                            if(data == 0) {
                                $("#vnptmoney").hide();
                            } else {
                                $("#vnptmoney").show();
                                $("#vnptmoney").html("Đã thanh toán qua VNPT-MONEY: "+ data);
                            }
                        })
                        $.post('cmu_post_CMU_TONGTIEN_BANK_NOIDUNG', {
                            url: ["${Sess_DVTT}",
                                sovaovien == 0? sovaovien_noi: sovaovien, sovaovien_dt_noi? sovaovien_dt_noi: 0, ret.SO_PHIEU, 'BIDV'].join('```')
                        }).done(function (data) {
                            if(data == 0) {
                                $("#bidv").hide();
                            } else {
                                $("#bidv").show();
                                $("#bidv").html("Đã thanh toán qua BIDV: "+ new Intl.NumberFormat().format(data));
                            }
                        })
                        $.post('cmu_post_CMU_TONGTIEN_BANK_NOIDUNG', {
                            url: ["${Sess_DVTT}",
                                sovaovien == 0? sovaovien_noi: sovaovien, sovaovien_dt_noi? sovaovien_dt_noi: 0, ret.SO_PHIEU, 'VIETINBANK'].join('```')
                        }).done(function (data) {
                            if(data == 0) {
                                $("#vietinbank").hide();
                            } else {
                                $("#vietinbank").show();
                                $("#vietinbank").html("Đã thanh toán qua VIETINBANK: "+ new Intl.NumberFormat().format(data));
                            }
                        })
                        if("${Sess_DVTT}"=="96029" && !$("#dathuchien").prop('checked')){
                            $("#nhanvien").val(0).change();//reset
                        }
                    }
                },
                onRightClickRow: function (id1) {
                    if (id1) {
                        $('#list_benhnhan').jqGrid('setSelection', id1);
                        //alert(id1);
                        $.contextMenu({
                            selector: '#list_benhnhan tr',
                            callback: function (key, options) {
                                if (key == "trathuocvekho") {
                                    if("${Sess_DVTT}" == 96004 && "${Sess_UserID}" != "1344516") {
                                        return jAlert("Bạn không có quyền hủy kết quả xét nghiệm, vui lòng liên hệ Minh", 'Thông báo');
                                    }
                                    var id = $("#list_benhnhan").jqGrid('getGridParam', 'selrow');
                                    var ret = $("#list_benhnhan").jqGrid('getRowData', id);
                                    var arr = ["${Sess_DVTT}", ret.NOITRU, ret.SO_PHIEU, ret.MA_KHAM_BENH, ret.STT_BENHAN, ret.STT_DOTDIEUTRI, ret.STT_DIEUTRI, 0];
                                    // TGGDEV-36575: admin được phép hủy kết quả, nhân viên chỉ được hủy trong ngày, thêm tham số cho phép nhân viên hủy qua ngày
                                    if ("${Sess_Admin}" != "0" || (ret.NGUOI_THUC_HIEN == "${Sess_UserID}" && "${tsktvhuyKQ}" == "0") || (ret.NGUOI_THUC_HIEN == "${Sess_UserID}" && ret.NGAY_THUC_HIEN == "${ngayhientai}" && "${tsktvhuyKQ}" == "1")) {
                                        var risarr = [ret.SO_PHIEU, ret.MABENHNHAN, ret.NOITRU, "TDCN", "0"];
                                        var urlarr = "ris_kiemtra_trangthai_cachup";
                                        $.post(urlarr, {
                                            url: convertArray(risarr)
                                        }).done(function (data) {
                                            if (data != "0") {
                                                return jAlert("Có ca chụp đang thực hiện bởi RIS. Không thể hủy phiếu. Vui lòng liên hệ khoa CDHA...", "Thông báo");
                                            } else {
                                                jConfirm('Xác nhận hủy kết quả?', 'Thông báo', function (r) {
                                                    if (r.toString() == "true") {
                                                        if (ret.SO_PHIEU != "") {
                                                            var url = "huyketquacdha?url=" + convertArray(arr);
                                                            $.ajax({
                                                                url: url
                                                            }).done(function (data) {
                                                                if (data == "RIS.1") {
                                                                    jAlert("Trạng thái phiếu hiện tại trên RIS không cho phép hủy kết quả CĐHA của phiếu chỉ định này", "Thông báo");
                                                                } else if (data == "ERRLOGIN") {
                                                                    jAlert("Xác thực đăng nhập RIS Connector thất bại, Vui lòng kiểm tra lại thông tin cấu hình kết nối RIS", "Thông báo");
                                                                } else if (data == "RISFAIL") {
                                                                    jConfirm("Hủy kết quả CĐHA của phiếu trên RIS thất bại. Bạn có muốn tiếp tục?", "Thông báo", function (r) {
                                                                        if (r.toString() == "true") {
                                                                            $.post("ris_update_huytrangthai", {
                                                                                url: convertArray(arr)
                                                                            }).always(function () {
                                                                                //code HIS
                                                                                var arr1 = ["${Sess_DVTT}", "Hủy kết quả CDHA cho bệnh nhân " + ret.TEN_BENH_NHAN + " với phiếu CDHA " + ret.SO_PHIEU, "${Sess_UserID}" + "-" + "${Sess_User}", "Hủy kết quả CDHA"];
                                                                                $.post("lichsusudung_insert", {url: convertArray(arr1)});
                                                                                jAlert("Hủy kết quả thành công", "Thông báo");
                                                                                $("#lammoi").click();
                                                                            });
                                                                        }
                                                                    });
                                                                } else if (data == "SUCCESS") {
                                                                    var arr1 = ["${Sess_DVTT}", "Hủy kết quả CDHA cho bệnh nhân " + ret.TEN_BENH_NHAN + " với phiếu CDHA " + ret.SO_PHIEU, "${Sess_UserID}" + "-" + "${Sess_User}", "Hủy kết quả CDHA"];
                                                                    $.post("lichsusudung_insert", {url: convertArray(arr1)});
                                                                    jAlert("Hủy kết quả thành công", "Thông báo");
                                                                    $("#lammoi").click();
                                                                }
                                                            });
                                                        }
                                                        else {
                                                            jAlert("Chọn phiếu để hủy", 'Thông báo');
                                                        }
                                                    }
                                                });
                                            }
                                        });
                                    } else {
                                        jAlert("Chỉ Admin hoặc bác sĩ thực hiện mới có quyền hủy!", 'Thông báo');
                                    }
                                }
                                if (key == "goiso") {
                                    var id = $("#list_benhnhan").jqGrid('getGridParam', 'selrow');
                                    var ret = $("#list_benhnhan").jqGrid('getRowData', id);
                                    var chuoi = "";
                                    if("${Sess_DVTT}".slice(0,2) == "96"){
                                        chuoi = ${Sess_PhongDuocSet} +"|" + ret.STT_HANGNGAY + "|" + ret.TENBENHNHAN + "|" + "0" + "|" + "CDHA" + "|" + "0";
                                    } else {
                                        chuoi = ${Sess_PhongDuocSet} +"|" + ret.STT_HANGNGAY.toString().replace('<span class="cellWithoutBackground" style="background-color:white;color:black">', '').replace('</span>', '') + "|" + ret.TENBENHNHAN + "|" + ret.CAPCUU;
                                    }
                                    <c:choose>
                                    <c:when test="${kgggoiso == '3'}">
                                    chuoihinhanh = goisolayhinhanh($("#list_benhnhan"), id);
                                    saveTextAsFile(chuoihinhanh);
                                    </c:when>
                                    <c:otherwise>
                                    saveTextAsFile(chuoi);
                                    </c:otherwise>
                                    </c:choose>
                                }
                            },
                            items: {
                                "trathuocvekho": {name: "Hủy kết quả"},
                                "goiso": {
                                    name: "<span style='color:red'>Gọi số bệnh nhân</span>", icon: "goiso"
                                }
                            }
                        });
                    }
                }
            });
            $("#list_benhnhan").jqGrid('filterToolbar', {
                stringResult: true,
                searchOnEnter: false,
                defaultSearch: "cn"
            });
            $("#khoabacsidocketqua").on("change", function (evt) {
                dsKhoabacsidockq()
            });
            $("#khoakythuatvien").on("change", function (evt) {
                dsKhoakythuatvien()
            });
            $("#list_dientim_bhyt").jqGrid({
                datatype: "local",
                loadonce: true,
                height: 158,
                width: 660,
                colNames: [
                    "DA_THANH_TOAN", "ma_CDHA", "Yêu cầu chẩn đoán", 'TRUE_TEN_CDHA',
                    "Kỹ thuật viên","Bác sĩ đọc kết quả","KYTHUATVIEN",
                    "Kết quả", "Trangthai", "Ma", "DVT", "mota", "NGUOI_THUC_HIEN", "STT_MAYCDHA"  <c:if test="${thamso_dongia != 0}">,"Đơn giá" </c:if>
                ],
                colModel: [
                    {name: 'DA_THANH_TOAN', index: 'DA_THANH_TOAN', hidden: true},
                    {name: 'MA_CDHA', index: 'MA_CDHA', hidden: true},
                    {
                        name: 'TEN_CDHA', index: 'TEN_CDHA', width: 150,
                        formatter: function (cellvalue, options, rowObject) {
                            var color_text;
                            if (rowObject.DA_THANH_TOAN.toString() === "0") {
                                color_text = 'red';
                            } else {
                                color_text = 'black';
                            }
                            return '<span class="cellWithoutBackground" style="color:' + color_text + '">' + (cellvalue == null ? "" : cellvalue) + '</span>';
                        }
                    },
                    {
                        name: 'TRUE_TEN_CDHA', index: 'TRUE_TEN_CDHA', hidden: true,
                        formatter: function (cellvalue, options, rowObject) {
                            return rowObject.TEN_CDHA;
                        }
                    },
                    {name: 'KTVTHUCHIEN', index: 'KTVTHUCHIEN', width: 50},
                    {name: 'BACSIDOCKQ', index: 'BACSIDOCKQ', width: 50},
                    {name: 'KYTHUATVIEN', index: 'KYTHUATVIEN', width: 60, hidden: true},
                    {name: 'KET_QUA', index: 'KET_QUA', width: 60, hidden: true},
                    {name: 'TRANGTHAI_BHYT', index: 'TRANGTHAI_BHYT', hidden: true},
                    {name: 'MABAOCAO', index: 'MABAOCAO', hidden: true},
                    {name: 'DVT_CDHA', index: 'DVT_CDHA', hidden: true},
                    {name: 'MOTA_CDHA', index: 'MOTA_CDHA', hidden: true},
                    {name: 'NGUOI_THUC_HIEN', index: 'NGUOI_THUC_HIEN', hidden: true},
                    {name: 'STT_MAYCDHA', index: 'STT_MAYCDHA', hidden: true}
                    <c:if test="${thamso_dongia != 0}">
                    ,{name: 'DON_GIA', index: 'DON_GIA', width: 40}
                    </c:if>
                ],
                caption: "Yêu cầu chẩn đoán hình ảnh",
                afterSaveCell: function (rowid, name, val, iRow, iCol) {
                    var ret = $("#list_dientim_bhyt").jqGrid('getRowData', rowid);
                    var arr = [$("#sophieu").val(), ret.MA_CDHA, val, "${Sess_DVTT}"];
                    var url = "cdha_update_ketqua_chitiet?url=" + convertArray(arr);
                    $.ajax({
                        url: url
                    }).done(function () {
                        var sophieu = $("#sophieu").val();
                        var noitru = $("#noitru").val();
                        var sttbenhan = $("#sttbenhan").val();
                        var sttdotdieutri = $("#sttdotdieutri").val();
                        var sttdieutri = $("#sttdieutri").val();
                        var arr1 = [noitru, sophieu, sttbenhan, sttdotdieutri, sttdieutri, "${Sess_DVTT}"];
                        var url1 = "cdha_hienthi_chitiet?url=" + convertArray(arr1);
                        $("#list_dientim_bhyt").jqGrid('setGridParam', {
                            datatype: 'json',
                            url: url1
                        }).trigger('reloadGrid');
                    });
                },
                ondblClickRow: function (id) {
                    if (id) {
                        console.log('here1111', $('#chandoan').val())
                        var ret = $("#list_dientim_bhyt").jqGrid('getRowData', id);
                        $("#macdha").val(ret.MA_CDHA);
                        $("#mota_cdha").val(ret.MOTA_CDHA);
                        $("#nguoithuchien").val(ret.NGUOI_THUC_HIEN);
                        var sophieu = $("#sophieu").val();
                        var ma_cdha = $("#macdha").val();
                        var makhambenh = $("#makhambenh").val();
                        var noitru = $("#noitru").val();
                        var sttbenhan = $("#sttbenhan").val();
                        var sttdotdieutri = $("#sttdotdieutri").val();
                        var sttdieutri = $("#sttdieutri").val();

                        if (flag_noitru === '1') {
                            load_cttoathuoc("noitru_toadichvu", "list_thuocdichvu");
                        } else {
                            var url = 'chitiettoathuocngoatru_svv?matt=' + matoathuoc + "&nghiepvu=ngoaitru_toadichvu&dvtt=${Sess_DVTT}" + "&sovaovien=" + sovaovien + "&ma_cdha=" + ma_cdha + "&sophieu=" + sophieu;
                            $("#list_thuocdichvu").jqGrid('setGridParam', {
                                datatype: 'json',
                                url: url
                            }).trigger('reloadGrid');
                        }
                        $("#cboNguoiDocKetQua").html("")
                        $("#cmu_kythuatvien").html("")
                        console.log("ret", ret)
                        if(ret.KYTHUATVIEN != null && ret.KYTHUATVIEN != "") {
                            $("#cmu_kythuatvien").append("<option value="+ret.KYTHUATVIEN+">"+ret.KTVTHUCHIEN+"</option>")
                            $("#cmu_kythuatvien").val(ret.KYTHUATVIEN)
                        } else {
                            $("#cmu_kythuatvien").append("<option value=${Sess_UserID}>${Sess_User}</option>")
                            $("#cmu_kythuatvien").val("${Sess_UserID}")
                        }
                        if(ret.NGUOI_THUC_HIEN != 0) {
                            $("#cboNguoiDocKetQua").append("<option value="+ret.NGUOI_THUC_HIEN+">"+ret.BACSIDOCKQ+"</option>")
                            $("#cboNguoiDocKetQua").val(ret.NGUOI_THUC_HIEN)

                        } else {
                            dsKhoabacsidockq()
                        }
                        var arr = [sophieu, "${Sess_DVTT}", ret.MA_CDHA, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0", sovaovien, sovaovien_noi, sovaovien_dt_noi];
                        var url = "dientim_select_ketqua_svv?url=" + convertArray(arr);
                        $.getJSON(url, function (result) {
                            $.each(result, function (i, field) {
                                $("#bacsichidinh").val(field.BACSI_CHIDINH);
                                if(!!field.BACSI_THUCHIEN)
                                    $("#bacsichuyenkhoa").val(field.BACSI_THUCHIEN);

                                if (field.MA_MAUSIEUAM == null || field.MA_MAUSIEUAM == "") {
                                    $("#maudientim").val("0");
                                } else {
                                    $("#maudientim").val(field.MA_MAUSIEUAM);
                                }
                                // STG
                                if (field.NGAYTHUCHIEN == null) {
                                    $("#ngayth_ct").val("${ngayhientai}");
                                } else {
                                    $("#ngayth_ct").val(field.NGAYTHUCHIEN);
                                }

                                /*if(field.GIOTHUCHIEN == null) {
                                    showtime_gioth_ct_cancel = 0;
                                } else {
                                    showtime_gioth_ct_cancel = 1;
                                    $("#gioth_ct").val(field.GIOTHUCHIEN);
                                } */
                                $("#gioth_ct").data('da-thuc-hien', field.DA_CHAN_DOAN == 1);
                                if (field.GIOTHUCHIEN == null || field.DA_CHAN_DOAN != 1) {
                                    if(tatAutoTime == 1) {
                                        var ngayHienTai = new Date();
                                        var gioHienTai = addZero(ngayHienTai.getHours());
                                        var phutHienTai = addZero(ngayHienTai.getMinutes());
                                        var giayHienTai = addZero(ngayHienTai.getSeconds());
                                        $('#gioth_ct').val(gioHienTai + ":" + phutHienTai + ":" + giayHienTai);
                                    } else
                                        showtime_gioth_ct();
                                } else {
                                    stopGioThCtTimer();
                                    $("#gioth_ct").val(field.GIOTHUCHIEN);
                                }
                                // STG

                                if ($("#bacsichidinh").val() == "") {
                                    var url_bs = "select_tenbacsi?mabacsi=" + $("#mabacsichidinh").val() + "&dvtt= " + "${Sess_DVTT}";
                                    $.ajax({
                                        url: url_bs
                                    }).done(function (data) {
                                        $("#bacsichidinh").val(data);
                                    });
                                }
                                if ($("#bacsisieuam").val() == "") {
                                    $("#bacsisieuam").val(nv_with_chucdanh);
                                }
                                $("#loidanbacsi").val(field.LOIDANBACSI);
                                $("#ketqua").val(field.KET_QUA);
                                $("#ketluan").val(field.MO_TA);
                                $("#mabenhly_truoccdha").val(field.MABENHLY_TRUOCCDHA);
                                $("#mabenhly_saucdha").val(field.MABENHLY_SAUCDHA);
                                $("#icd_truoccdha").val(field.ICD_TRUOCCDHA);
                                $("#icd_saucdha").val(field.ICD_SAUCDHA);
                                $("#chandoan_truoccdha").val(field.CHANDOAN_TRUOCCDHA);
                                $("#chandoan_saucdha").val(field.CHANDOAN_SAUCDHA);
                                if(field.DA_CHAN_DOAN==1) {
                                    $("#thoiGianBatDau_cls").val(field.NGAY_TH_YL).change();
                                    // changeInputTimerStatus("thoiGianBatDau_cls", true);
                                } else if (field.DA_CHAN_DOAN ==0) {
                                    $("#thoiGianBatDau_cls").val(newStringDateTime());
                                    //changeInputTimerStatus("thoiGianBatDau_cls");
                                }
                                $("#icdChanDoanCanLamSang").val(field.MA_ICD);
                                $("#tenChanDoanCanLamSang").val(field.TEN_ICD);
                                $("#maBenhLyChanDoanCanLamSang").val(field.MA_BENH_LY_THEO_ICD);
                                var getChanDoan = $('#chandoan').val();
                                if("${Sess_DVTT}" == '96029') {
                                    $('#tenChanDoanCanLamSang').val(getChanDoan);
                                    $('#chandoan_truoccdha').val(getChanDoan);
                                    $('#chandoan_saucdha').val(getChanDoan);
                                }
                            });
                        });
                        var arr = [sophieu, "${Sess_DVTT}", ret.MA_CDHA, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                        var url1 = "sieuam_danhsach_hinhanh?url=" + convertArray(arr);
                        $("#list_hinhanhnoisoi").jqGrid('setGridParam', {
                            datatype: 'json',
                            url: url1
                        }).trigger('reloadGrid');
                        $("#tab_cdha").tabs("option", "active", 1);
                        //$(location).attr('href', '#cdha_tabs_2');
                        hinhAnhDT();
                    }
                },
                onSelectRow: function (id) {
                    if (id) {
                        var ret = $("#list_dientim_bhyt").jqGrid('getRowData', id);
                        $("#macdha").val(ret.MA_CDHA);
                        $("#ma_maycdha_md").val(ret.STT_MAYCDHA);
                    }
                },
                gridComplete: function () {
                    var str = $("#list_dientim_bhyt").getGridParam("records");
                    if (str != "0") {
                        $('#list_dientim_bhyt').jqGrid('setSelection', "1");
                    }
                    //ĐắkLắk (An Giang yêu cầu) - Tân 09/12/2016: hiển thị tổng số bệnh nhân
                    var c = $("#list_benhnhan").getGridParam("records");
                    $("#list_benhnhan").jqGrid('setCaption', "Danh sách bệnh nhân (" + c + " bệnh nhân)");
                    //End ĐắkLắk
                }

            });
            $("#luu_tt_maycdha").click(function (evt) {
                var sophieu = $("#sophieu").val();
                var noitru = $("#noitru").val();
                var makhambenh = $("#makhambenh").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var macdha = $("#macdha").val();
                if (sophieu != "" && macdha != "" && (sovaovien != "" || sovaovien_noi != "")) {
                    $.post("luu_maycdha_vaobangchitiet", {
                        sophieu: sophieu, macdha: macdha, noitru: noitru,
                        sttbenhan: sttbenhan, sttdotdieutri: sttdotdieutri, sttdieutri: sttdieutri,
                        makhambenh: makhambenh, sovaovien: sovaovien,
                        sovaovien_noi: sovaovien_noi,
                        sovaovien_dt_noi: sovaovien_dt_noi,
                        stt_may_cdha: ($("#ma_maycdha_md").val())
                    }).done(function (data) {
                        if (data == "1") {
                            jAlert("Cập nhật thành công!", "Thông báo");
                        } else {
                            jAlert("Vui lòng kiểm tra lại thông tin!", "Thông báo");
                        }
                    }).fail(function () {
                        jAlert("Vui lòng thử lại!", "Thông báo");
                    });
                }
            });


            $("#thuocdichvu_div").keyup(function (evt) {
                if (evt.keyCode === 46 && flag_noitru !== "-1") {
                    if (flag_noitru === "1")
                        delete_thuocnoitru("list_thuocdichvu", "noitru_toadichvu");
                    else
                        jConfirm('Bạn có muốn xóa thuốc?', 'Thông báo', function (r) {
                            if (r.toString() === "true") {
                                delete_toathuocngoaitru("list_thuocdichvu", "ngoaitru_toadichvu");
                            }
                        });
                }
            });

            function delete_thuocnoitru(list, nghiepvu) {
                var id = $("#list_thuocdichvu").jqGrid('getGridParam', 'selrow');
                if (id) {
                    var ret = $("#list_thuocdichvu").jqGrid('getRowData', id);
                    if (ret.MA_BAC_SI_THEMTHUOC === undefined || ret.MA_BAC_SI_THEMTHUOC === "${Sess_UserID}") {
                        jConfirm('Bạn có muốn xóa không?', 'Thông báo', function (r) {
                            if (r.toString() === "true") {
                                var url = "noitru_toathuoc_delete";
                                if (list === "list_thuoctonghopall")
                                    nghiepvu = ret.NGHIEP_VU;
                                var arr = [ret.STT_TOATHUOC, matoathuoc, "${Sess_DVTT}", $("#sttdieutri").val(), $("#sttbenhan").val(), $("#sttdotdieutri").val(), sophieuthanhtoan,
                                    ret.THANHTIEN_THUOC, ret.MAKHOVATTU, "0", nghiepvu, ret.MAVATTU, encodeURIComponent(ret.TEN_VAT_TU), ret.SO_LUONG];
                                var sophieu = $("#sophieu").val();
                                var ma_cdha = $("#macdha").val();
                                url += "?url=" + convertArray(arr);
                                if (sophieu) {
                                    url += "&sophieu=" + sophieu;
                                }
                                if (ma_cdha) {
                                    url += "&ma_cdha=" + ma_cdha;
                                }
                                $.post(url).done(function (data) {
                                    if (data === "1")
                                        jAlert("Bệnh nhân đã thanh toán viện phí", 'Cảnh báo');
                                    else if (data === "2")
                                        jAlert("Bệnh nhân đã được xuất thuốc", 'Cảnh báo');
                                    else if (data == '100') {
                                        jAlert("Đã chốt báo cáo dược, không thể sửa/xóa", 'Cảnh báo');
                                    }
                                    else {
                                        $("#list_thuocdichvu").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                                    }
                                });
                            }
                        });
                    } else {
                        jAlert("Bạn không được xóa thuốc trong toa của bác sĩ khác!", 'Cảnh báo');
                    }
                } else {
                    jAlert("Chọn một dòng thuốc để xóa", 'Cảnh báo');
                }
            }

            function delete_toathuocngoaitru(list, nghiepvu) {
                var id = $("#list_thuocdichvu").jqGrid('getGridParam', 'selrow');
                if (id) {
                    var url = "xuatduoc_giamtai_svv";
                    $.post(url, {
                        nghiepvu: "ngoaitru_toadichvu",
                        matoathuoc: matoathuoc,
                        makhambenh: $("#makhambenh").val(),
                        xacnhan: "false",
                        mabenhnhan: mabenhnhan,
                        ngaykhambenh: ngay_kb
                    }).done(function (data) {
                        if (data == "0") {
                            var ret = $("#list_thuocdichvu").jqGrid('getRowData', id);
                            var dongia = ret.DONGIA_BAN_BH;
                            var arr = [ret.STT_TOATHUOC, matoathuoc, ${Sess_DVTT}, $("#makhambenh").val(), sophieuthanhtoan, ret.THANHTIEN_THUOC,
                                ret.MAKHOVATTU, dongia, nghiepvu, ret.MAVATTU, $("#mabenhnhan").val(), ngay_kb, sovaovien, encodeURIComponent(ret.TEN_VAT_TU), ret.SO_LUONG];
                            var url = "xoathuocngoaitru_giamtai?url=" + convertArray(arr);
                            var sophieu = $("#sophieu").val();
                            var ma_cdha = $("#macdha").val();
                            if (sophieu) {
                                url += "&sophieu=" + sophieu;
                            }
                            if (ma_cdha) {
                                url += "&ma_cdha=" + ma_cdha;
                            }
                            $.ajax({
                                url: url
                            }).done(function (data) {
                                if (data === "1")
                                    jAlert("Bệnh nhân đã thanh toán", 'Cảnh báo');
                                else if (data === "2")
                                    jAlert("Bệnh nhân đã được xuất thuốc", 'Cảnh báo');
                                else if (data === "3")
                                    jAlert("Bệnh nhân đã được trả thuốc về kho", 'Cảnh báo');
                                else if (data == '100') {
                                    jAlert("Đã chốt báo cáo dược, không thể sửa/xóa", 'Cảnh báo');
                                }
                                else
                                    $("#list_thuocdichvu").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                                var url = "xuatduoc_giamtai_svv";
                                $.post(url, {
                                    nghiepvu: "ngoaitru_toadichvu",
                                    matoathuoc: matoathuoc,
                                    makhambenh: $("#makhambenh").val(),
                                    xacnhan: "true",
                                    mabenhnhan: mabenhnhan,
                                    ngaykhambenh: ngay_kb
                                }).done(function (data) {

                                });
                            });

                        }
                    });
                } else {
                    jAlert("Chọn 1 dòng thuốc để xóa", 'Cảnh báo');
                }
            }

            function imageFormat(cellvalue, options, rowObject) {
                return '<img src="' + cellvalue + '" width="100px" height="80px" />';
            }

            $("#lammoi").click(function (evt) {
                reload_grid();
            });
            $("#list_thuocdichvu").jqGrid({
                url: 'chitiettoathuocngoatru?matt=' + matoathuoc + "&nghiepvu=ngoaitru_toaxquang&dvtt=${Sess_DVTT}",
                datatype: "local",
                loadonce: true,
                height: 230,
                width: 300,
                colNames: ["stt_toathuoc", "Tên thương mại", "DVT", "Số lượng", "", "", "", "", ""
                ],
                colModel: [
                    {name: 'STT_TOATHUOC', index: 'STT_TOATHUOC', hidden: true},
                    {name: 'TEN_VAT_TU', index: 'TEN_VAT_TU', width: 80},
                    {name: 'DVT', index: 'DVT', width: 60},
                    {
                        name: "SO_LUONG",
                        index: "SO_LUONG",
                        align: 'center',
                        width: 45,
                        edittype: 'custom',
                        editoptions: {custom_element: myelem, custom_value: myvalue}
                    },
                    {name: 'THANHTIEN_THUOC', index: 'THANHTIEN_THUOC', hidden: true},
                    {name: 'MAVATTU', index: 'MAVATTU', hidden: true},
                    {name: 'MAKHOVATTU', index: 'MAKHOVATTU', hidden: true},
                    {name: 'DONGIA_BAN_BV', index: 'DONGIA_BAN_BV', hidden: true},
                    {name: 'DONGIA_BAN_BH', index: 'DONGIA_BAN_BH', hidden: true}
                ],
                //viewrecords: true,
                rowNum: 1000000,
                //multiselect: true,
                caption: "Toa Siêu Âm"
            });

            function myelem(value, options) {
                var el = document.createElement("input");
                el.type = "text";
                el.value = value;
                el.onkeypress = function (e) {
                    var theEvent = e || window.event;
                    var key = theEvent.keyCode || theEvent.which;
                    key = String.fromCharCode(key);
                    var regex = /[0-9]|\./;
                    if (!regex.test(key)) {
                        theEvent.returnValue = false;
                        if (theEvent.preventDefault)
                            theEvent.preventDefault();
                    }
                };
                return el;
            }

            function myvalue(elem, operation, value) {
                if (operation === 'get') {
                    return $(elem).val();
                } else if (operation === 'set') {
                    $('input', elem).val(value);
                }
            }

            function load_cttoathuoc(nghiepvu, list) {
                var url = 'noitru_load_chitiet_film?matt=' + matoathuoc + '&nghiepvu=' + nghiepvu + '&dvtt=${Sess_DVTT}&stt_benhan=' + stt_benhan +
                    '&stt_dotdieutri=' + stt_dotdieutri + '&sovaovien=' + sovaovien_noi + '&sovaovien_dt=' + sovaovien_dt_noi +
                    '&phongban=${Sess_PhongBan}' + '&ma_cdha=' + $("#macdha").val();
                +'&sophieu=' + $("#sophieu").val();
                ;
                $('#' + list).jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
            };

            function clear_thuoc_dv() {
                $("#thuocdichvu_tt_div input[type='text']").val("");
                $("#thuocdichvu_tt_div input[type='hidden']").val("");
            }

            $("#tenthuongmai_dv").combogrid({
                url: "noitru_load_tonkhohientai?makhovt=" + $("#kho_dv").val() + "&dvtt=${Sess_DVTT}",
                debug: true,
                width: "700px",
                //replaceNull: true,
                colModel: [{'columnName': 'MAVATTU', 'label': 'mavattu', hidden: true},
                    {'columnName': 'TENVATTU', 'width': '40', 'label': 'Tên vật tư', 'align': 'left'},
                    {'columnName': 'DVT', 'label': 'dvt', hidden: true},
                    {'columnName': 'SOLUONG', 'width': '10', 'label': 'Số lượng', 'align': 'right'},
                ],
                select: function (event, ui) {
                    $("#tenthuongmai_dv").val(ui.item.TENVATTU);
                    $("#mavattu_dv").val(ui.item.MAVATTU);
                    $("#dvt_dv").val(ui.item.DVT);
                    $("#dongia_dv").val(ui.item.DONGIA);
                    //$("#dongia_bv").val(ui.item.dongia);
                    return false;
                }
            });
            $("#tengoc_dv").combogrid({
                url: 'layvattu_theotengoc_theoloai?makhovt=' + $("#kho_dv").val() + '&loaivattu=',
                debug: true,
                width: "700px",
                //replaceNull: true,
                colModel: [{'columnName': 'MAVATTU', 'label': 'mavattu', hidden: true},
                    {'columnName': 'HOATCHAT', 'width': '30', 'label': 'Hoạt chất', 'align': 'left'},
                    {'columnName': 'TENVATTU', 'width': '40', 'label': 'Tên vật tư', 'align': 'left'},
                    {'columnName': 'HAMLUONG', 'width': '20', 'label': 'Hàm lượng', 'align': 'left'},
                    {'columnName': 'DVT', 'label': 'dvt', hidden: true},
                    {'columnName': 'SOLUONG', 'width': '10', 'label': 'Số lượng', 'align': 'right'},
                    {'columnName': 'DONGIA', 'label': 'dongia_bv', hidden: true},
                    {'columnName': 'CACHSUDUNG', 'label': 'cachsudung', hidden: true}
                ],
                select: function (event, ui) {
                    $("#tenthuongmai_dv").val(ui.item.TENVATTU);
                    $("#tengoc_dv").val(ui.item.HOATCHAT);
                    $("#mavattu_dv").val(ui.item.MAVATTU);
                    $("#dvt_dv").val(ui.item.DVT);
                    $("#dongia_dv").val(ui.item.DONGIA);
                    $("#cachdung_dv").val(ui.item.CACHSUDUNG);
                    return false;
                }
            });
            $("#tenthuongmai_dv").keypress(function (evt) {
                if (evt.keyCode == 13) {
                    $("#soluong_dv").focus();
                }
            });
            $("#tengoc_dv").keypress(function (evt) {
                if (evt.keyCode == 13) {
                    $("#soluong_dv").focus();
                }
            });
            $("#soluong_dv").keypress(function (evt) {
                if (evt.keyCode === 13 && flag_noitru !== "-1") {
                    var makhovattu = $("#kho_dv").val();
                    makho = -1;
                    makhokhoaphong = 0;
                    var url_kho = "noitru_kiemtra_tututhuoc";
                    $.post(url_kho, {dvtt: "${Sess_DVTT}", maphongban: "${Sess_PhongBan}", makho: makhovattu})
                        .done(function (data) {
                            var tu_tuthuoc = 1;
                            var mavattu = $("#mavattu_dv").val();
                            var tenthuongmai = $("#tenthuongmai_dv").val();
                            var tengoc = " ";
                            var dvt = $("#dvt_dv").val();
                            var soluong = $("#soluong_dv").val();
                            var sl = (soluong !== "") ? parseFloat(soluong) : 0;
                            var dongia_bh = $("#dongia_dv").val();
                            var dongia_bv = dongia_bh;
                            var songay = "1";
                            var sang = "1";
                            var trua = "0";
                            var chieu = "0";
                            var toi = "0";
                            var dangthuoc = " ";
                            var ma_cdha = $("#macdha").val();
                            var ghichu = $("#sophieu").val() + "-" + ma_cdha;
                            var cachdung = " ";
                            var thanhtien = sl * parseFloat(dongia_bh);
                            var sophieu = $("#sophieu").val();

                            if (sl <= 0)
                                jAlert("Số lượng không hợp lệ", 'Cảnh báo', function (r) {
                                    $("#soluong_dv").focus();
                                });
                            else {
                                var arr = ["${Sess_DVTT}", matoathuoc, makhovattu, mavattu, encodeURIComponent(tenthuongmai), encodeURIComponent(tengoc), dvt, "noitru_toadichvu", soluong, soluong, dongia_bv, dongia_bh, thanhtien, songay, sang, trua,
                                    chieu, toi, ghichu, "${Sess_UserID}", dangthuoc, stt_dieutri, stt_benhan, stt_dotdieutri, tu_tuthuoc, cobhyt, mabenhnhan, sophieuthanhtoan, "${Sess_PhongBan}", sovaovien_noi, sovaovien_dt_noi];
                                var url = "noitru_toathuoc_insert";
                                if (flag_noitru !== "1") {   //Ngoại trú
                                    var makhambenh = $("#makhambenh").val();
                                    var idtiepnhan = makhambenh.replace("kb_", "");
                                    var url = "xuatduoc_giamtai_svv";
                                    $.post(url, {
                                        nghiepvu: "ngoaitru_toadichvu",
                                        matoathuoc: matoathuoc,
                                        makhambenh: makhambenh,
                                        xacnhan: "false",
                                        mabenhnhan: mabenhnhan,
                                        ngaykhambenh: ngay_kb
                                    }).done(function (data) {
                                        if (data == "0") {
                                            var arr = [matoathuoc, mavattu, encodeURIComponent(tenthuongmai), encodeURIComponent(tengoc), soluong, dongia_bv, dongia_bh, songay, sang, trua,
                                                chieu, toi, dangthuoc, ghichu, thanhtien, cobhyt, mabenhnhan, sophieuthanhtoan, idtiepnhan, makhambenh, sovaovien, "${Sess_Phong}", "1", "0"];
                                            var url = "themtoathuocngoaitru_giamtai?dongia_bv=" + parseFloat(dongia_bv) +
                                                "&dongia_bh=" + parseFloat(dongia_bh) + "&thanhtien=" + parseFloat(thanhtien) +
                                                "&sang=" + sang + "&trua=" + trua + "&chieu=" + chieu + "&toi=" + toi +
                                                "&url=" + convertArray(arr) + "&nghiepvu=ngoaitru_toadichvu" +
                                                "&kho=" + makhovattu + "&ngaykb=" + ngay_kb;
                                            if (sophieu) {
                                                url += "&sophieu=" + sophieu;
                                            }
                                            if (ma_cdha) {
                                                url += "&ma_cdha=" + ma_cdha;
                                            }
                                            $.ajax({
                                                url: url
                                            }).done(function (data) {
                                                if (data === "4") {
                                                    jAlert("Bệnh nhân đã thanh toán rồi", 'Cảnh báo');
                                                    var url = "xuatduoc_giamtai_svv";
                                                    $.post(url, {
                                                        nghiepvu: "ngoaitru_toadichvu",
                                                        matoathuoc: matoathuoc,
                                                        makhambenh: makhambenh,
                                                        xacnhan: "true",
                                                        mabenhnhan: mabenhnhan,
                                                        ngaykhambenh: ngay_kb
                                                    }).done(function (data) {

                                                    });
                                                } else if (data === "5") {
                                                    jAlert("Bệnh nhân đã xuất thuốc rồi", 'Cảnh báo');
                                                    var url = "xuatduoc_giamtai_svv";
                                                    $.post(url, {
                                                        nghiepvu: "ngoaitru_toadichvu",
                                                        matoathuoc: matoathuoc,
                                                        makhambenh: makhambenh,
                                                        xacnhan: "true",
                                                        mabenhnhan: mabenhnhan,
                                                        ngaykhambenh: ngay_kb
                                                    }).done(function (data) {

                                                    });
                                                } else if (data === "3") {
                                                    jAlert("Thuốc đã có trong toa", 'Cảnh báo');
                                                    var url = "xuatduoc_giamtai_svv";
                                                    $.post(url, {
                                                        nghiepvu: "ngoaitru_toadichvu",
                                                        matoathuoc: matoathuoc,
                                                        makhambenh: makhambenh,
                                                        xacnhan: "true",
                                                        mabenhnhan: mabenhnhan,
                                                        ngaykhambenh: ngay_kb
                                                    }).done(function (data) {

                                                    });
                                                } else if (data === "6") {
                                                    jAlert("Số lượng thuốc vượt số lượng tồn kho", 'Cảnh báo');
                                                    var url = "xuatduoc_giamtai_svv";
                                                    $.post(url, {
                                                        nghiepvu: "ngoaitru_toadichvu",
                                                        matoathuoc: matoathuoc,
                                                        makhambenh: makhambenh,
                                                        xacnhan: "true",
                                                        mabenhnhan: mabenhnhan,
                                                        ngaykhambenh: ngay_kb
                                                    }).done(function (data) {

                                                    });
                                                } else if (data == '100') {
                                                    jAlert("Đã chốt báo cáo dược, không thể xóa/sửa", 'Cảnh báo');
                                                    var url = "xuatduoc_giamtai_svv";
                                                    $.post(url, {
                                                        nghiepvu: "ngoaitru_toadichvu",
                                                        matoathuoc: matoathuoc,
                                                        makhambenh: makhambenh,
                                                        xacnhan: "true",
                                                        mabenhnhan: mabenhnhan,
                                                        ngaykhambenh: ngay_kb
                                                    }).done(function (data) {

                                                    });
                                                } else {
                                                    var url = 'chitiettoathuocngoatru_svv?matt=' + matoathuoc + "&nghiepvu=ngoaitru_toadichvu&dvtt=${Sess_DVTT}" + "&sovaovien=" + sovaovien + "&ma_cdha=" + ma_cdha + "&sophieu=" + sophieu;
                                                    $("#list_thuocdichvu").jqGrid('setGridParam', {
                                                        datatype: 'json',
                                                        url: url
                                                    }).trigger('reloadGrid');
                                                    var url = "xuatduoc_giamtai_svv";
                                                    $.post(url, {
                                                        nghiepvu: "ngoaitru_toadichvu",
                                                        matoathuoc: matoathuoc,
                                                        makhambenh: makhambenh,
                                                        xacnhan: "true",
                                                        mabenhnhan: mabenhnhan,
                                                        ngaykhambenh: ngay_kb
                                                    }).done(function (data) {

                                                    });
                                                }
                                                clear_thuoc_dv();
                                                $("#tenthuongmai_dv").focus();
                                            });
                                        }
                                    });
                                } else {
                                    url += "?url=" + convertArray(arr);
                                    var sophieu = $("#sophieu").val();
                                    if (sophieu) {
                                        url += "&sophieu=" + sophieu;
                                    }
                                    var ma_cdha = $("#macdha").val();
                                    if (ma_cdha) {
                                        url += "&ma_cdha=" + ma_cdha;
                                    }
                                    $.post(url).done(function (data) {
                                        if (data === "4") {
                                            jAlert("Bệnh nhân đã thanh toán rồi", 'Cảnh báo');
                                        } else if (data === "5") {
                                            jAlert("Bệnh nhân đã xuất thuốc rồi", 'Cảnh báo');
                                        } else if (data === "3") {
                                            jAlert("Thuốc đã có trong toa", 'Cảnh báo');
                                        } else if (data === "6") {
                                            jAlert("Số lượng thuốc vượt số lượng tồn kho", 'Cảnh báo');
                                        } else {
                                            load_cttoathuoc("noitru_toadichvu", "list_thuocdichvu");
                                        }
                                        clear_thuoc_dv();
                                        $("#tenthuongmai_dv").focus();
                                    });
                                }
                            }
                        });
                }
            });
            $("#thuocdichvu_div").keyup(function (evt) {
                if (evt.keyCode === 46 && flag_noitru !== "-1") {
                    if (flag_noitru === "1")
                        delete_thuocnoitru("list_thuocdichvu", "noitru_toadichvu");
                    else
                        jConfirm('Bạn có muốn xóa thuốc?', 'Thông báo', function (r) {
                            if (r.toString() === "true") {
                                delete_toathuocngoaitru("list_thuocdichvu", "ngoaitru_toadichvu");
                            }
                        });
                }
            });
            $("#kho_dv_noi").change(function (evt) {
                $("#tenthuongmai_dv").combogrid("option", "url", 'layvattu?makhovt=' + $("#kho_dv_noi").val());
                $("#tengoc_dv").combogrid("option", "url", 'layvattu_theotengoc?makhovt=' + $("#kho_dv_noi").val());
            });

            $("#kho_dv_ngoai").change(function (evt) {
                $("#tenthuongmai_dv").combogrid("option", "url", 'layvattu?makhovt=' + $("#kho_dv_ngoai").val());
                $("#tengoc_dv").combogrid("option", "url", 'layvattu_theotengoc?makhovt=' + $("#kho_dv_ngoai").val());
            });
            //CMU: 26062017
            var dialog_lichsuCHHA =
                new jBox('Modal', {
                    title: "Lịch sử điện tim",
                    overlay: false,
                    content: $('#dialog_lichsuCDHA'),
                    draggable: 'title'
                });
            $("#lichsuCDHA").click(function (evt) {
                //var stt_benhan = $("#sttbenhan").val();
                var mabenhnhan = $("#mabenhnhan").val();
                var hoten = $("#hoten").val();
                if (hoten !== "") {
                    load_lscdha_bn(mabenhnhan);
                    dialog_lichsuCHHA.open();
                } else {
                    jAlert("Vui lòng chọn một bệnh nhân!", "Thông báo");
                }
            });
            $("#list_lichsuCDHA").jqGrid({
                datatype: "local",
                loadonce: true,
                height: 450,
                width: 900,
                colNames: ["Ngày", "Đơn vị", "Chẩn đoán", "Tên điện tim", "Số lượng", "Đơn giá", "Thành tiền", "BHYT không chi", "Kết quả", "Kết luận",
                    "sophieu", "stt_benhan", "Kết quả"],
                colModel: [
                    {name: 'NGAY_THUC_HIEN', index: 'NGAY_THUC_HIEN', width: 55},
                    {name: 'TEN_DONVI', index: 'TEN_DONVI', width: 80},
                    {name: 'CHANDOAN', index: 'CHANDOAN', width: 100},
                    {name: 'TEN_CDHA', index: 'TEN_CDHA'},
                    {name: 'SO_LUONG', index: 'SO_LUONG', width: 100, hidden: true},
                    {
                        name: 'DON_GIA',
                        index: 'DON_GIA',
                        width: 100,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        hidden: true
                    },
                    {
                        name: 'THANH_TIEN',
                        index: 'THANH_TIEN',
                        width: 100,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        hidden: true
                    },
                    {
                        name: 'BHYTKCHI',
                        index: 'BHYTKCHI',
                        width: 110,
                        align: 'center',
                        formatter: 'checkbox',
                        formatoptions: {value: 'true:false'},
                        hidden: true
                    },
                    {name: 'KET_QUA', index: 'KET_QUA', width: 100, hidden: true},
                    {name: 'MO_TA', index: 'MO_TA', width: 80},
                    {name: 'SO_PHIEU_XN', index: 'SO_PHIEU_XN', width: 100, hidden: true},
                    {name: 'STT_BENHAN', index: 'STT_BENHAN', width: 100, hidden: true},
                    {name: 'KET_QUA', index: 'KET_QUA', width: 150}
                ],
                caption: "Lịch sử điện tim",
                rowNum: 1000
            });
            $("#tab_ls_cdha").tabs();

            //END CMU: 26062017
            function decodeHTMLEntities(str) {
                if (str && typeof str === 'string') {
                    // strip script/html tags
                    str = str.replace(/<script[^>]*>([\S\s]*?)<\/script>/gmi, '');
                    str = str.replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gmi, '');
                    //element.innerHTML = str;
                    //str = element.textContent;
                    //element.textContent = '';
                }

                return str;
            }
            loadBacSiTheoKhoa(SESS_PHONG_BAN);
            $("#cboNguoiDocKetQua").change(function() {
                $("#nhanvien").val($(this).val()).change();
            })

            function checkTrungTG() {
                var thoiGianTHYL = $('#thoiGianBatDau_cls').val().substring(0, 16);
                var ngayKQ = $('#ngayth_ct').val();
                var gioKQ = $('#gioth_ct').val();
                var thoiGianKQ = ngayKQ + ' ' + gioKQ;
                var noitru = $("#noitru").val();
                var arr = ["${Sess_DVTT}", $('#cboNguoiDocKetQua').val(), thoiGianTHYL, thoiGianKQ.substring(0, 16), noitru];
                var url = "cmu_list_CMU_KT_THOIGIAN_CLS?url=" + convertArray(arr);
                var result = true;
                $.ajax({
                    url: url,
                    async: false,
                    success: function (data) {
                        if (data && data.length > 0) {
                            result = false;
                            $.alert({
                                title: 'Cảnh báo!',
                                content: '<b style="color:#c10606">Dữ liệu không hợp lệ, vui lòng kiểm tra lại.</b> </br><table id="kt_trung_thoigian_cls" class="jqx-grid-cell-wrap"></table>',
                                type: 'red',
                                boxWidth: '750px',
                                useBootstrap: false,
                                escapeKey: true,
                                closeIcon: true,
                                typeAnimated: true,
                                onContentReady: function () {
                                    $("#kt_trung_thoigian_cls").jqGrid({
                                        datatype: 'local',
                                        data: data,
                                        rownumbers: true,
                                        height: 'auto',
                                        colModel: [
                                            { name: 'LOI', label: 'Lỗi', width: 380, align: 'left' },
                                            { name: 'NGAY_THUC_HIEN_YL', label: 'Ngày thực hiện YL', width: 160, align: 'center'},
                                            { name: 'NGAY_THUC_HIEN', label: 'Ngày thực hiện', width: 160, align: 'center' },
                                        ],
                                    });
                                }
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error("Error: ", error);
                    }
                });

                return result;
            }

            $("#luu_tt").click(function (evt) {
                var ketQuaKiemTra = THAMSO_828449=="1"?kiemTraThoiGianHopLe():"1";
                if(ketQuaKiemTra!="1"){
                    jAlert(ketQuaKiemTra, 'Thông báo');
                    return false;
                }
                if (checkNguoiDocKetQua()) {
                    jAlert("Người đọc kết quả không hợp lệ", "Cảnh báo");
                    return false;
                }
                var ngaythuchien = $("#ngayth_ct").val() + " " + $("#gioth_ct").val();
                var ngayThuchienArr = $("#ngayth_ct").val().split("/");
                var gioThucHienArr = $("#gioth_ct").val().split(":");
                var ngayGioChiDinhArr = ngay_chi_dinh.split(" ");
                var ngayChiDinhArr = ngayGioChiDinhArr[0].split("/");
                var gioChiDinhArr = ngayGioChiDinhArr[1].split(":");
                var dateNgayThucHien = new  Date(ngayThuchienArr[2],ngayThuchienArr[1],ngayThuchienArr[0],gioThucHienArr[0],gioThucHienArr[1],gioThucHienArr[2]);
                var dateNgayChiDinh = new Date(ngayChiDinhArr[2],ngayChiDinhArr[1],ngayChiDinhArr[0],gioChiDinhArr[0],gioChiDinhArr[1],gioChiDinhArr[2]);
                if ("${thamso_82860467}" == "1" && (dateNgayThucHien < dateNgayChiDinh)) {
                    jAlert("Thời gian cập nhật kết quả nhỏ hơn thời gian chỉ định", "Cảnh báo")
                }else if ($("#nguoithuchien").val() != "0" && $("#nguoithuchien").val() != "${Sess_UserID}" && $("#nguoithuchien").val().trim() != "" && $("#dathuchien").prop("checked") == true) {
                    jAlert("Bạn không thể chỉnh sửa KQ điện tim của nhân viên khác!", 'Thông báo');
                } else if ("${nhapketluan_cls}" == "1" && ($("#ketluan").val() == "" || $("#ketluan").val() == null)) { // 20171102 NAN VINHVT HISHD-21197 ADD
                    jAlert("Chưa nhập kết luận cho bệnh nhân!", 'Cảnh báo');
                }
                else {
                    var ngayChiDinh = $('#ngaychidinh_kq').val();
                    var gioChiDinh = $('#giochidinh_kq').val();
                    var thoiGianChiDinh = ngayChiDinh + ' ' + gioChiDinh
                    var thoiGianTHYL = $('#thoiGianBatDau_cls').val();
                    var ngayKQ = $('#ngayth_ct').val();
                    var gioKQ = $('#gioth_ct').val();
                    var thoiGianKQ = ngayKQ + ' ' + gioKQ;

                    var momentChiDinh = moment(thoiGianChiDinh, ['DD/MM/YYYY HH:mm:ss']);
                    var momentThucHienYLenh = moment(thoiGianTHYL, ['DD/MM/YYYY HH:mm:ss']);
                    var momentKetQua = moment(thoiGianKQ, ['DD/MM/YYYY HH:mm:ss']);
                    var soTheBHYT = $('#sothebhyt_ct').val();

                    if (soTheBHYT !== '') {
                        if(momentThucHienYLenh.diff(momentChiDinh, 'minutes') < 1){
                            jAlert('THỜI GIAN THỰC HIỆN Y LỆNH : '+thoiGianTHYL+'<br> KHÔNG ĐƯỢC NHỎ HƠN HOẶC BẰNG'+'<br>THỜI GIAN CHỈ ĐỊNH : ' + thoiGianChiDinh , 'Thông báo');
                            return;
                        }

                        if(momentKetQua.diff(momentThucHienYLenh, 'minutes') < 1){
                            jAlert('THỜI GIAN KẾT QUẢ : '+thoiGianKQ+'<br> KHÔNG ĐƯỢC NHỎ HƠN HOẶC BẰNG'+'<br>THỜI GIAN THỰC HIỆN Y LỆNH : ' + thoiGianTHYL , 'Thông báo');
                            return;
                        }

                        if(momentKetQua.diff(momentThucHienYLenh, 'minutes') < 5){
                            jAlert('THỜI GIAN THỰC HIỆN Y LỆNH: '+thoiGianTHYL+'<br> ĐẾN GIỜ '+'<br>THỜI GIAN KẾT QUẢ : ' + thoiGianKQ + " KHÔNG ĐƯỢC NHỎ HƠN 5 PHÚT", 'Thông báo');
                            return;
                        }
                        if(THAMSO_960627 == "1" && typeof cmuKiemtratungThoigianCLSTheoNV == 'function' &&  !cmuKiemtratungThoigianCLSTheoNV({
                            dvtt: "${Sess_DVTT}",
                            sovaovien: $("#noitru").val()==1?sovaovien_noi:sovaovien,
                            userId: $("#cboNguoiDocKetQua").val(),
                            thoigianbd: momentThucHienYLenh.format('DD/MM/YYYY HH:mm'),
                            thoigiankt: momentKetQua.format('DD/MM/YYYY HH:mm'),
                            loaikythuat: "DT"
                        })) {
                            return false;
                        }
                        if(THAMSO_960627 == "1" && typeof cmuKiemtratungThoigianCLSTheoNV == 'function' &&  !cmuKiemtratungThoigianCLSTheoNV({
                            dvtt: "${Sess_DVTT}",
                            sovaovien: $("#noitru").val()==1?sovaovien_noi:sovaovien,
                            userId: $("#cmu_kythuatvien").val(),
                            thoigianbd: momentThucHienYLenh.format('DD/MM/YYYY HH:mm'),
                            thoigiankt: momentKetQua.format('DD/MM/YYYY HH:mm'),
                            loaikythuat: "DT"
                        })) {
                            return false;
                        }
                    }

                    var sophieu = $("#sophieu").val();
                    var makhambenh = $("#makhambenh").val();
                    var noitru = $("#noitru").val();
                    var sttbenhan = $("#sttbenhan").val();
                    var sttdotdieutri = $("#sttdotdieutri").val();
                    var sttdieutri = $("#sttdieutri").val();
                    var macdha = $("#macdha").val();
                    var dvtt = "${Sess_DVTT}";
                    var ketqua = $("#ketqua").val();
                    var ketluan = $("#ketluan").val();
                    // chỉnh lỗi font trong xml4
                    var mota_xml5 = $('<textarea />').html(ketluan).text().replace(/<[^>]+>/g, '');
                    var kl_xml5 = $('<textarea />').html(ketqua).text();
                    var ketqua_xml5 = decodeHTMLEntities(kl_xml5);
                    //
                    var bacsichidinh = $("#bacsichidinh").val();
                    var bacsithuchien = $("#bacsichuyenkhoa").val();
                    if (bacsithuchien == "") {
                        bacsithuchien = nv_with_chucdanh;
                    }
                    var loidanbacsi = $("#loidanbacsi").val();
                    var maudientim = $("#maudientim").val();
                    var maBenhLyTruocCdha = $("#mabenhly_truoccdha").val();
                    var maBenhLySauCdha = $("#mabenhly_saucdha").val();
                    var chanDoanTruocCdha = $("#chandoan_truoccdha").val();
                    var chanDoanSauCdha = $("#chandoan_saucdha").val();
                    //var nguoithuchien = "${Sess_UserID}";
                    var nguoithuchien = $("#nhanvien").val();
                    var ngaygioth_ct = "";
                    $("#thoiGianBatDau_cls").change();//stop timer
                    var thoiGianBatDauCls = $("#thoiGianBatDau_cls").val();
                    if ($("#ngayth_ct").val() != '' && $("#gioth_ct").val() != '') {
                        ngaygioth_ct = convertStr_MysqlDate($("#ngayth_ct").val()) + " " + $("#gioth_ct").val();
                    }
                    if (nguoithuchien.trim() == "" || nguoithuchien == 0) {
                        jAlert("Chưa chọn người thực hiện"); return;//nguoithuchien = '${Sess_UserID}'
                    }
                    if ($("#cmu_kythuatvien").val() == "" ) {
                        jAlert("Chưa chọn kỹ thuật viên thực hiện"); return;
                    }
                    if (maudientim == "" || maudientim == null) {
                        jAlert("Chưa chọn mẫu điện tim", 'Cảnh báo');
                    } else if (macdha == "") {
                        jAlert("Chưa chọn điện tim để thực hiện", 'Cảnh báo');
                    } else if (sophieu != "") {
                        var thamso960543;
                        var url = 'laythamso_donvi_motthamso?mathamso=960543';
                        $.post(url).done(function(data) {
                            thamso960543 = data
                        });
                        $.post('cmu_post', {
                            url: [
                                sophieu, "${Sess_DVTT}", macdha, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri,
                                sovaovien, sovaovien_noi, sovaovien_dt_noi,
                                'CMU_CHECKMAY_CDHA_SA'
                            ].join('```')
                        }).done(function(data) {
                            if(thamso960543 == 1 && data > 0){
                                jAlert("Vui lòng Lưu máy thực hiện trước khi Lưu thông tin", "Cảnh báo");
                            } else if(ketluan.length < 5){
                                jAlert("Kết luận quá ngắn, vui lòng nhập nhiều hơn 5 ký tự", "Cảnh báo");
                            } else {
                                $.post('cmu_post', {
                                    url: ["${Sess_DVTT}", sophieu, macdha, ngaygioth_ct,'CMU_KTRA_GIOTHU_DIENTIM'].join("```")
                                }).then(function(data) {
                                    if(data == 1) {
                                        jAlert("Ngày giờ thực hiện không được nhỏ hơn ngày giờ chỉ định. Ngày chỉ định:" + $("#cmu_ngaychidinh").val())
                                    } else {
                                        var ktrathoigian = $.ajax({type: "POST", url: "cmu_post", async: false, //Chỉ định CLS Viện Phí lấy giá trên grid. cho phép sửa giá
                                            data: {url: ["${Sess_DVTT}", $("#sophieu").val(),ngaygioth_ct,
                                                    sovaovien_noi == 0? sovaovien:sovaovien_noi, noitru,'CMU_KTRATHOI_CDVAKQ'].join('```')}
                                        }).responseText;
                                        if(ktrathoigian == 1) {
                                            jAlert("Thời gian thực hiện không được dưới 5 phút", 'Cảnh báo');
                                            return false;
                                        }
                                        luuNguoiDocKetQua(sophieu, noitru==1?sovaovien_noi:sovaovien, sovaovien_dt_noi, noitru, macdha);
                                        $.post("capnhatketqua_dientim_svv", {
                                            sophieu: sophieu,
                                            macdha: macdha,
                                            dvtt: dvtt,
                                            ketqua: ketqua,
                                            ketluan: ketluan,
                                            bacsichidinh: bacsichidinh,
                                            bacsithuchien: bacsithuchien,
                                            loidanbacsi: loidanbacsi,
                                            maudientim: maudientim,
                                            noitru: noitru,
                                            sttbenhan: sttbenhan,
                                            sttdotdieutri: sttdotdieutri,
                                            sttdieutri: sttdieutri,
                                            makhambenh: makhambenh,
                                            sovaovien: sovaovien,
                                            sovaovien_noi: sovaovien_noi,
                                            sovaovien_dt_noi: sovaovien_dt_noi,
                                            nguoithuchien: $("#cboNguoiDocKetQua").val(),
                                            ngaygioth_ct: ngaygioth_ct,
                                            mota_xml5: mota_xml5,
                                            ketqua_xml5: ketqua_xml5,
                                            maBenhLyTruocCdha: maBenhLyTruocCdha,
                                            maBenhLySauCdha: maBenhLySauCdha,
                                            chanDoanTruocCdha: chanDoanTruocCdha,
                                            chanDoanSauCdha: chanDoanSauCdha
                                            ,thoiGianBatDauCls: thoiGianBatDauCls
                                            , maIcd: $("#icdChanDoanCanLamSang").val(),
                                            tenIcd: $("#tenChanDoanCanLamSang").val(),
                                            maBenhLy:  $("#maBenhLyChanDoanCanLamSang").val()
                                        })
                                            .done(function (datares) {
                                                if (datares == -1) {
                                                    jAlert("Dữ liệu bệnh nhân đã khóa không thể chỉnh sửa, vui lòng liên hệ admin.", 'Cảnh báo');
                                                    return false;
                                                }
                                                $.post('cmu_post', {
                                                    url: [
                                                        "${Sess_DVTT}", sophieu, macdha,
                                                        sovaovien == 0? sovaovien_noi: sovaovien, sovaovien_dt_noi,
                                                        $("#cmu_kythuatvien").val(), $("#cboNguoiDocKetQua").val(),
                                                        'CMU_UPDATE_KTV_XQUANG'
                                                    ].join('```')
                                                })

                                                jAlert("Cập nhật thành công", 'Thông báo');
                                                reload_grid();
                                            });
                                    }
                                })
                            }
                        });
                    }
                }
            });

            function kiemTraThoiGianHopLe() {
                var objThoiGianChiDinhChiTiet = {name: "Thời gian chỉ định", value: $("#ngaycddt").val()||($("#ngaychidinh_kq").val() + " " + $("#giochidinh_kq").val())};
                var objThoiGianThucHienYLenh = {name: "Thời gian thực hiện Y lệnh", value: $("#thoiGianBatDau_cls").val()};
                var objThoiGianKetQua = {name: "Thời gian kết quả", value: $("#ngayth_ct").val() + " " + $("#gioth_ct").val()};
                //ThoiGianKetQua > ThoiGianThucHienYLenh > ThoiGianChiDinhChiTiet
                var objCompare = validateAndCompareDatesToMinute(objThoiGianKetQua,objThoiGianThucHienYLenh,objThoiGianChiDinhChiTiet);
                if ((objCompare.errorCode=="-1" || objCompare.errorCode=="-2") && objCompare.objects.length>0) {
                    if(objCompare.errorCode=="-1") {
                        return "Lỗi định dạng " + objCompare.objects[0].name;
                    } else {
                        return "Lỗi " + objCompare.objects[0].name + " phải sau " + objCompare.objects[1].name + " tính đến phút";
                    }
                }
                return "1"; //Hợp lệ
            }

            $('#save-snapshot').on('click', function (evt) {
                var macdha = $("#macdha").val();
                var sophieu = $("#sophieu").val();
                var noitru = $("#noitru").val();
                var makhambenh = $("#makhambenh").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                if (dataURL != "" && sophieu != "" && macdha != "") {
                    $.post("luuhinhanh_sieuam", {
                        sophieu: sophieu,
                        macdha: macdha,
                        dvtt: "${Sess_DVTT}",
                        hinh: dataURL,
                        noitru: noitru,
                        sttbenhan: sttbenhan,
                        sttdotdieutri: sttdotdieutri,
                        sttdieutri: sttdieutri,
                        makhambenh: makhambenh
                    })
                        .done(function () {
                            jAlert("Lưu hình ảnh thành công", 'Thông báo');
                        }).fail(function () {
                        jAlert('Lưu hình ảnh thất bại');
                    });
                }
            });
            $('#browse-file').on('change', function (evt) {
                if(this.files[0].size > 5097152) {
                    jAlert("File quá lớn (tối đa 5MB)");
                    this.value = "";
                }
                var bg_preview = document.getElementById('say-cheese-snapshots').childNodes[0];
                var bg_file = document.getElementById('browse-file').files[0];
                var bg_reader = new FileReader();
                rotate = 0;

                bg_reader.addEventListener("load", function () {
                    bg_preview.src = bg_reader.result;
                    dataURL = bg_reader.result;
                }, false);

                if (bg_file) {
                    bg_reader.readAsDataURL(bg_file);
                }
            });
            var rotate = 0;
            $("#snapshot-rotate").click(function (evt) {
                var canvas = document.getElementById("canvas");
                var imageSrc = document.getElementById("preview-image");
                var ctx = canvas.getContext("2d");
                var imageInput = document.getElementById("browse-file");
                var file = imageInput.files[0];
                rotate += 90;
                var angle = rotate;

                if (!file) {
                    jAlert("Vui lòng up ảnh");
                    return;
                }

                const reader = new FileReader();
                reader.onload = function (e) {
                    const img = new Image();
                    img.onload = function () {
                        // Calculate canvas size after rotation
                        var radians = (Math.PI / 180) * angle;
                        var sin = Math.abs(Math.sin(radians));
                        var cos = Math.abs(Math.cos(radians));
                        var newWidth = Math.ceil(img.width * cos + img.height * sin);
                        var newHeight = Math.ceil(img.width * sin + img.height * cos);

                        canvas.width = newWidth;
                        canvas.height = newHeight;

                        // Move the origin to the center and rotate
                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                        ctx.translate(newWidth / 2, newHeight / 2);
                        ctx.rotate(radians);

                        // Draw the image
                        ctx.drawImage(img, -img.width / 2, -img.height / 2);

                        // Get the rotated image as base64
                        const base64Image = canvas.toDataURL("image/jpeg");
                        imageSrc.src = base64Image;
                        dataURL = base64Image;

                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            });
            $("#snapshot-xoa").click(function (evt) {

                var macdha = $("#macdha").val();
                var sophieu = $("#sophieu").val();
                var noitru = $("#noitru").val();
                var makhambenh = $("#makhambenh").val();
                if ( sophieu != "" && macdha != "") {
                    $.post("cmu_post",
                        {
                            url: [sophieu,"${Sess_DVTT}", macdha, $("#snapshot-stt").val(),noitru,"CLS_SIEUAM_DELETE_HINHANH_F"].join('```')
                        }).done(function () {
                        jAlert("Xóa thành công", 'Thông báo');
                        var imageSrc = document.getElementById("preview-image");
                        imageSrc.src = "resources/webcam/camera_png.jpg";
                        var imageInput = document.getElementById("browse-file");
                        imageInput.value = "";
                        dataURL = "";
                    });
                } else {
                    jAlert("Chưa chọn điện tim để thực hiện", 'Cảnh báo');
                }
            });
            function hinhAnhDT(){
                var macdha = $("#macdha").val();
                var sophieu = $("#sophieu").val();
                var noitru = $("#noitru").val();
                var makhambenh = $("#makhambenh").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var arr = [sophieu, "${Sess_DVTT}", macdha, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                var url = 'sieuam_danhsach_hinhanh?url=' + convertArray(arr);
                var imageSrc = document.getElementById("preview-image");
                imageSrc.src = "resources/webcam/camera_png.jpg";
                var imageInput = document.getElementById("browse-file");
                imageInput.value = "";
                dataURL = "";
                $('#snapshot-stt').val("");
                $.get(url).done(function(data){
                    if (data && data.length > 0){
                        data.forEach(function(obj) {
                            $('#preview-image').attr('src', obj.HINHANH);
                            dataURL = obj.HINHANH;
                            $('#snapshot-stt').val( obj.STT);
                            const url = obj.HINHANH;
                            fetch(url)
                                .then(res => res.blob())
                                .then(blob => {
                                    var file = new File([blob], "dt.jpg",{ type: "image/jpeg" })
                                    const dataTransfer = new DataTransfer();
                                    dataTransfer.items.add(file);
                                    imageInput.files = dataTransfer.files;
                                })
                        })
                    }
                })
            }

            $("#luuthongtin").click(function (evt) {
                $("#luu_tt").click();
            });
            $("#tenthuongmai_dv").keypress(function (evt) {
                if ($("#kho_dv").val() == null) {
                    jAlert("Tủ thuốc trống vui lòng kiểm tra thông tin đăng nhập khoa phòng, tủ thuốc", 'Thông báo');
                }
            });
            $("#inphieu_dientim").click(function (evt) {
                var mabenhnhan = $("#mabenhnhan").val();
                var hoten = $("#hoten").val();
                var diachi = $("#diachi").val();
                var tuoi = $("#tuoi").val();
                var phai = $("#gioitinh").val();
                if (phai == "true") {
                    phai = "Nam";
                } else {
                    phai = "Nữ";
                }
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var macdha = $("#macdha").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var cannang = $("#cannang").val();
                var chieucao = $("#chieucao").val();
                var khoa = $("#Khoa").val();
                var giuong = $("#giuong").val();
                var buong = $("#buong").val();
                var sothebaohiem = $("#sothebhyt").val();
                var chandoan = $("#chandoan").val();
                var yeucaukiemtra = $("#yeucaukiemtra").val();
                var bacsidieutri = $("#bacsichidinh").val();
                var bacsichuyenkhoa = $("#bacsichuyenkhoa").val();
                if (bacsichuyenkhoa == "") {
                    bacsichuyenkhoa = nv_with_chucdanh;
                }
                var solan = $("#solan").val();
                var tencdha = $("#tencdha").val();
                var motacdha = $("#loaimauin").val();
                //VNPTHIS-4697 23/11/2017
                var type = $("#loaiin").val();
                var ngayth = $("#ngayth_ct").val();
                if(ngayth == ""){
                    ngayth = $("#ngaythuchien_cls").val();
                }
                var gioth = $("#gioth_ct").val();
                if(gioth == ""){
                    gioth = $("#giothuchien_cls").val();
                }

                let gridDienTim = $("#list_dientim_bhyt");
                let rowId = gridDienTim.jqGrid('getGridParam', 'selrow');
                let rowData = gridDienTim.jqGrid('getRowData', rowId);
                yeucaukiemtra = yeucaukiemtra ? yeucaukiemtra + ". " + rowData.TRUE_TEN_CDHA : rowData.TRUE_TEN_CDHA;

                //VNPTHIS-4697 23/11/2017
                if (sophieu != "" && macdha != "") {
                    var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha,
                        dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, cannang, chieucao,
                        khoa, giuong, buong, sothebaohiem, chandoan, yeucaukiemtra,
                        bacsidieutri, bacsichuyenkhoa, solan, tencdha, "0", motacdha, sovaovien
                        , sovaovien_noi
                        , sovaovien_dt_noi, 0, type, $("#ngayth_ct").val(), $("#gioth_ct").val() =="" ? "00:00:00" : $("#gioth_ct").val(),"0"];  //VNPTHIS-4697 23/11/2017 thêm loại in
                    var url = "inketquadientim_svv?url=" + convertArray(arr);
                    $(location).attr('href', url);
                }
            });

            $("#phieu_cdha_dientim").click(function (evt) {
                if (noitru_ngoaitru == "0") {
                    var makhambenh = $("#makhambenh").val();
                    var sophieu = $("#sophieu").val();
                    var bhytkhongchi = co_bao_hiem == 1 ? 0 : 1;
                    var arr = [makhambenh, bhytkhongchi, sophieu, "${Sess_DVTT}", "0", "1", noitru_ngoaitru];
                    //Ðo?n mã c?a BDH
                    var hoten = $("#hoten").val();
                    var tuoi = $("#tuoi").val();
                    var phai = $("#gioitinh").val();
                    if (phai.toString() == "true") {
                        phai = "Nam";
                    } else {
                        phai = "Nữ";
                    }
                    var gioitinh = phai;
                    var diachi = $("#diachi").val();
                    var bschidinh = bacsi_chidinh;
                    var sothebaohiem = "";
                    //Ki?m tra b?nh nhân có BHYT
                    if (bhytkhongchi == "0")
                        sothebaohiem = $("#sothebhyt").val();
                    var maphong = phongcdha_ss;
                    if ("${thanhtoannhieunac}" == "1" && bhytkhongchi == "0") {
                        var url_taobk = "taobangke_truocin?makb=" + makhambenh + "&dvtt=" + "${Sess_DVTT}" + "&sophieu=0";
                        $.ajax({
                            url: url_taobk
                        }).done(function (data) {
                            if ("${Sess_DVTT}".indexOf("52") == 0 || "${Sess_DVTT}" == "82023") {
                                var url = "laykyhieubaocaophongcdha?maphongcdha=" + maphong;
                                $.ajax({
                                    url: url
                                }).done(function (data) {
                                    //1:PXQ
                                    if (data == "1" && sophieu != "") {
                                        arr = ["", hoten, diachi, tuoi, gioitinh, makhambenh, sophieu, "0",
                                            "${Sess_DVTT}", "0", "", "", "", "", "", "", sothebaohiem, "", bschidinh, ""];
                                        url = "bdh_inketquaxquang?url=" + convertArray(arr);
                                        $(location).attr('href', url);
                                    } else {
                                        url = "inphieucdha?url=" + convertArray(arr);
                                        $(location).attr('href', url);
                                    }
                                });
                            } else {
                                url = "inphieucdha?url=" + convertArray(arr);
                                var dvtt = "${Sess_DVTT}";
                                if ("${taibaocaovemay}" == "1") {
                                    var redirectWindow = window.open(url, '_blank');
                                    redirectWindow.location;
                                    return false;
                                } else
                                    $(location).attr('href', url);
                            }
                        });
                    } else {
                        if ("${Sess_DVTT}".indexOf("52") == 0 || "${Sess_DVTT}" == "82023") {
                            var url = "laykyhieubaocaophongcdha?maphongcdha=" + maphong;
                            $.ajax({
                                url: url
                            }).done(function (data) {
                                if (data == "1" && sophieu != "") {
                                    arr = ["", hoten, diachi, tuoi, gioitinh, makhambenh, sophieu, "0",
                                        "${Sess_DVTT}", "0", "", "", "", "", "", "", sothebaohiem, "", bschidinh, ""];
                                    url = "bdh_inketquaxquang?url=" + convertArray(arr);
                                    $(location).attr('href', url);
                                } else {
                                    url = "inphieucdha?url=" + convertArray(arr);
                                    $(location).attr('href', url);
                                }
                            });
                        } else {
                            url = "inphieucdha?url=" + convertArray(arr);
                            var dvtt = "${Sess_DVTT}";
                            if ("${taibaocaovemay}" == "1") {
                                var redirectWindow = window.open(url, '_blank');
                                redirectWindow.location;
                                return false;
                            } else
                                $(location).attr('href', url);
                        }
                    }
                }else{
                    var sophieucdha = $("#sophieu").val();
                    var bhytkhongchi = co_bao_hiem == 1 ? 0 : 1;
                    var sobenhantt = sobenhan_noitru_tt;
                    var sobenhan = sobenhan_noitru;
                    var icd_khoadt = icd_benhnhan;
                    var ten_khoadt = ten_icd_benhnhan;
                    if (sobenhan != "")
                        soba = sobenhan;
                    else
                        soba = sobenhantt;
                    var arr = [mabenhnhan, bhytkhongchi, sophieucdha, "${Sess_DVTT}", soba, stt_benhan, stt_dotdieutri, stt_dieutri, icd_khoadt, ten_khoadt, "", "0"
                        , sovaovien_noi, sovaovien_dt_noi, 0, noitru_ngoaitru];
                    var url = "noitru_inphieucdha_svv?url=" + convertArray(arr);
                    $(location).attr('href', url);
                }
            });

            $("#inphieu").click(function (evt) {
                $("#inphieu_dientim").click();
            });
            reload_grid();
            $('textarea#ketqua').ckeditor();
            $("#tab_cdha").tabs();
            $('#tab_cdha').tabs({
                select: function (event, ui) {
                    // alert('selected: '+ui.index);
                    if (ui.index == 0) {
                        gioth_ct_timer_previous_status = gioth_ct_timer_is_on;
                        stopGioThCtTimer();
                        if (giothuchien_cls_timer_previous_status) {
                            if (tatAutoTime != 1)
                                showtime_giothuchien_cls();
                        }
                    } else if (ui.index == 1) {
                        giothuchien_cls_timer_previous_status = giothuchien_cls_timer_is_on;
                        stopGioThucHienClsTimer();
                        if (gioth_ct_timer_previous_status) {
                            if (tatAutoTime != 1)
                                showtime_gioth_ct();
                        }
                    }
                }
            });
            $("#maudientim").change(function (evt) {
                var id = $("#maudientim").val();
                if (id !== "0") {
                    var url = "select_maudientim_theoma?ma=" + id + "&dvtt=${Sess_DVTT}";
                    $.ajax({
                        url: url
                    }).done(function (data) {
                        CKEDITOR.instances.ketqua.setData(data[0]["NOIDUNG"]);
                        $('#ketluan').val(data[0]["KET_LUAN"]);
                    });
                }
            });
            // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Tân 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT
            $("#phongban").change(function (evt) {
                var url = "layphongbenh_theokhoaxn?khoa=" + $("#phongban").val() + "&dvtt=${Sess_DVTT}";
                $.ajax({
                    url: url
                }).done(function (data) {
                    if (data) {
                        $("#phongbenh").empty();
                        $.each(data, function (i) {
                            $("<option value='" + data[i].MA_PHONG_BENH + "'>" + data[i].TEN_PHONG_BENH + "</option>").appendTo("#phongbenh");
                        });
                    }
                });
                $("#phongbenh").val(-1);
                reload_grid();
            });
            $("#phongbenh").change(function (evt) {
                reload_grid();
            });
            $("#doituong").change(function (evt) {
                reload_grid();
            });
            // End ĐắkLắk
            $("#dathuchien").change(function (evt) {
                reload_grid();
            });
            $("#maudientim").keypress(function (evt) {
                if (evt.keyCode == 13) {
                    $("#ketqua").focus();
                }
            });
            $("#ketqua").keypress(function (evt) {
                if (evt.keyCode == 13) {
                    $("#ketluan").focus();
                }
            });
            $("#ketluan").keypress(function (evt) {
                if (evt.keyCode == 13) {
                    //$("#loidanbacsi").focus();
                }
            });

            function clear_benhnhan() {
                $("#sovaovien").val('');
                $("#bacsichidinh").val('');
                $("#mabenhnhan").val('');
                $("#hoten").val('');
                $("#tuoi").val('');
                $("#gioitinh").val('');
                $("#diachi").val('');
                $("#sothebhyt").val('');
                $("#sophieu").val('');
                $("#makhambenh").val('');
                $("#noitru").val('');
                $("#sttbenhan").val('');
                $("#sttdotdieutri").val('');
                $("#sttdieutri").val('');
                $("#mabacsichidinh").val('');
                $("#bacsichuyenkhoa").val('');
                $("#_chandoan").val('');
                $("#chandoan").val('');
                $("#tenkhoa").val('');
                $("#hoten_ct").val('');
                $("#tuoi_ct").val('');
                $("#gioitinh_ct").val('');
                $("#mabenhnhan_ct").val('');
                $("#tenkhoa_ct").val('');
                $("#sothebhyt_ct").val('');
                $("#cannang").val('');
                $("#chieucao").val('');
                $("#Khoa").val('');
                $("#buong").val('');
                $("#giuong").val('');
                $("#chandoan").val('');
                $("#solan").val('');
                $("#mota_cdha").val('');
                sovaovien = '';
                sovaovien_noi = '';
                sovaovien_dt_noi = '';
                da_thanh_toan = '';
                sophieuthanhtoan = '';
                cobhyt = '';
                ngay_kb = '';
                tlmg = '';
                flag_noitru = '';
            }

            // STG
            $("#gioth_ct").click(function (evt) {
                //showtime_gioth_ct_cancel = (showtime_gioth_ct_cancel == 1 ? 0 : 1);
                gioThCtTimerChange();
            });

            $("#giothuchien_cls").click(function (evt) {
                //showtime_giothuchien_cls_cancel = (showtime_giothuchien_cls_cancel == 1 ? 0 : 1);
                gioThucHienClsTimerChange();
            });
            // STG
            //--Start VNPT Bình Định
            if ("${timtheophong_th}" == "1") {
                $("#tr_phong").show();
                $("#dlk_phong").hide();
            } else {
                $("#tr_phong").hide();
            }
            //   if ("${Sess_DVTT}".indexOf("82") != "0") {
            //      document.getElementById("tr_phong").style.display = "hidden";
            //  document.getElementById("tr_phong").style.display = "none";
            //  }
            $("#phongban").change(function (evt) {
                reload_grid();
            });
            $("#phongthuchiencdha").change(function (evt) {
                reload_grid();
            });
            //--End VNPT Bình Định

            //-- RIS
            $("#viewimageweb").click(function (evt) {
                var sophieu = $("#sophieu").val();
                var macdha = $("#macdha").val();
                var arr = [sophieu, macdha];
                if (sophieu != "") {
                    var url = "ris_viewimage_web?url=" + convertArray(arr);
                    $.ajax({
                        url: url
                    }).done(function (urlweb) {
                        if (urlweb == "ERRLOGIN") {
                            jAlert("Xác thực đăng nhập RIS Connector thất bại, Vui lòng kiểm tra lại thông tin cấu hình kết nối RIS", "Thông báo");
                        } else if (urlweb == "ERROR") {
                            jAlert("Đã xảy ra lỗi", 'Thông báo');
                        } else if (urlweb == "RIS.7") {
                            jAlert("Không thể tìm thấy dữ liệu hình ảnh trên PACS", 'Thông báo');
                        } else if (urlweb == "RIS.6") {
                            jAlert("Không thể tìm thấy ca chụp trên RIS", 'Thông báo');
                        } else if (urlweb == "RIS.4") {
                            jAlert("Lấy đường dẫn xem ảnh không thành công", 'Thông báo');
                        } else if (urlweb == "NOTRECEIVE") {
                            jAlert("RIS chưa nhận ca chụp này", 'Thông báo');
                        } else {
                            var redirectWindow = window.open(urlweb, '_blank');
                            redirectWindow.location;
                            return false;
                        }
                    });
                }
            });

            $("#viewimageapp").click(function (evt) {
                var sophieu = $("#sophieu").val();
                var macdha = $("#macdha").val();
                var arr = [sophieu, macdha];
                if (sophieu != "") {
                    var url = "ris_viewimage_app?url=" + convertArray(arr);
                    $.ajax({
                        url: url
                    }).done(function (urlapp) {
                        if (urlapp == "ERRLOGIN") {
                            jAlert("Xác thực đăng nhập RIS Connector thất bại, Vui lòng kiểm tra lại thông tin cấu hình kết nối RIS", "Thông báo");
                        } else if (urlapp == "ERROR") {
                            jAlert("Đã có lỗi xảy ra", 'Thông báo');
                        } else if (urlapp == "RIS.7") {
                            jAlert("Không thể tìm thấy dữ liệu hình ảnh trên PACS", 'Thông báo');
                        } else if (urlapp == "RIS.6") {
                            jAlert("Không thể tìm thấy ca chụp trên RIS", 'Thông báo');
                        } else if (urlapp == "RIS.4") {
                            jAlert("Lấy đường dẫn xem ảnh không thành công", 'Thông báo');
                        } else if (urlapp == "NOTRECEIVE") {
                            jAlert("RIS chưa nhận ca chụp này", 'Thông báo');
                        } else {
                            if (!deployJava.isWebStartInstalled("1.7.0")) {
                                if (deployJava.installLatestJRE()) {
                                    if (deployJava.launch(urlapp)) {
                                    }
                                }
                            } else {
                                if (deployJava.launch(urlapp)) {
                                }
                            }
                        }
                    });
                }
            });
            //Lấy Chức Danh
            $.ajax({
                url: "select_tenbacsi?mabacsi=" + ${Sess_UserID} + "&dvtt= " + "${Sess_DVTT}"
            }).done(function (data) {
                nv_with_chucdanh = data;
                $("<option value='${Sess_UserID}' selected>" + data + "</option>").appendTo("#nhanvien");
                $("#bacsichuyenkhoa").val(data);
            });
            function reload_grid() {
                var ngay = convertStr_MysqlDate($("#ngaythuchien").val());
                var dvtt = "${Sess_DVTT}";
                var phong = "${Sess_Phong}";
                // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Tân 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT
                var phongban = $("#phongban").val();
                var phongbenh = $("#phongbenh").val();
                var doituong = $("#doituong").val();
                var loaidientim = $("#loai_dientim").val();
                // End ĐắkLắk

                var dathuchien = $("#dathuchien").prop('checked');
                if (dathuchien == true) {
                    dath = 1;
                    if ("${useris}" === "1") {
                        $('#viewimageweb').show();
                        $('#viewimageapp').show();
                    } else {
                        $('#viewimageweb').hide();
                        $('#viewimageapp').hide();
                    }
                } else {
                    dath = 0;
                    $('#viewimageweb').hide();
                    $('#viewimageapp').hide();
                }
                //--Start VNPT Bình Định
                if ("${timtheophong_th}" == "1") {
                    var maphongban = $("#phongban").val();
                    var maphongcdha = $("#phongthuchiencdha").val();
                    var arr = [dvtt, ngay, phong, dath, maphongban, maphongcdha];
                    var url = 'bdh_dientim_ds_benhnhan_cothamso?url=' + convertArray(arr);
                    //--End VNPT Bình Định
                } else {
                    //Đoạn code ban đầu
                    //var arr = [dvtt, ngay, phong, dath];
                    //var url = 'dientim_ds_benhnhan_cothamso?url=' + convertArray(arr);
                    // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT
                    var tungay = convertStr_MysqlDate($("#tungay").val());
                    if (tungay == undefined) {
                        tungay = convertStr_MysqlDate($("#ngaythuchien").val());
                    }
                    var arr = [dvtt, ngay, phong, dath, phongban, phongbenh, doituong, loaidientim, ngay, "0"];
                    if ("${timkiem_cls}" == "1")
                    {
                        arr = [dvtt, ngay, phong, dath, phongban, phongbenh, doituong, loaidientim, tungay,"0"];
                    }
                    var url = 'dientim_ds_benhnhan_cothamso?url=' + convertArray(arr);
                    // End ĐắkLắk
                }
                $("#list_benhnhan").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
            }

            function hienthi_them_cls(url) {
                $.ajax({
                    url: url
                }).done(function (data) {
                    if (data.length > 0) {
                        $('#trieuchungls').val(data[0].TRIEUCHUNGLS);
                        $('#benhtheobs').val(data[0].TEN_BENH_THEOBS);
                        // STG
                        if (data[0].NGAY_THUC_HIEN == null) {
                            $("#ngaythuchien_cls").val("${ngayhientai}");
                        } else {
                            $("#ngaythuchien_cls").val(data[0].NGAY_THUC_HIEN);
                        }

                        /*if(data[0].GIO_TRA_KETQUA == null) {
                            showtime_giothuchien_cls_cancel = 0;
                        } else {
                            showtime_giothuchien_cls_cancel = 1;
                            $("#giothuchien_cls").val(data[0].GIO_TRA_KETQUA);
                        }*/
                        if (data[0].GIO_TRA_KETQUA == null || !$("#dathuchien").prop('checked')) {
                            if (!giothuchien_cls_timer_is_on) {
                                if(tatAutoTime == 1) {
                                    var ngayHienTai = new Date();
                                    var gioHienTai = addZero(ngayHienTai.getHours());
                                    var phutHienTai = addZero(ngayHienTai.getMinutes());
                                    var giayHienTai = addZero(ngayHienTai.getSeconds());
                                    $('#giothuchien_cls').val(gioHienTai + ":" + phutHienTai + ":" + giayHienTai);
                                } else
                                    showtime_giothuchien_cls();
                            }
                        } else {
                            stopGioThucHienClsTimer();
                            $("#giothuchien_cls").val(data[0].GIO_TRA_KETQUA);
                        }
                        // STG
                        if (data[0].TT_THANHTOAN == "0")
                            $('#tt_thanhtoan').val("Chưa thanh toán");
                        else
                            $('#tt_thanhtoan').val("Đã thanh toán");
                    } else {
                        $('#trieuchungls').val("");
                        $('#benhtheobs').val("");
                        $('#ngaythuchien_cls').val("");
                        $('#tt_thanhtoan').val("");
                    }
                });
            }

            //CMU: 26062017
            function load_lscdha_bn(mabenhnhan) {
                var url = "cmu_danhsach_lichsu_cdha?mabenhnhan=" + mabenhnhan + "&type=TDCN";
                $("#list_lichsuCDHA").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
            }



            if ("${thamSo8448811}" == 1) {
                $(".icdTruocSau").show();
            }
            else {
                $(".icdTruocSau").hide();
            }

            $("#chandoan_truoccdha").combogrid({
                url: 'laydanhmucicd',
                debug: true,
                width: "400px",
                colModel: [{'columnName': 'ICD', 'label': 'ICD'},
                    {'columnName': 'MO_TA_BENH_LY', 'width': '40', 'label': 'Mô tả bệnh lý'},
                    {'columnName': 'MA_BENH_LY', 'width': '30', 'label': 'mabenhly', hidden: true}
                ],
                select: function (event, ui) {
                    $("#chandoan_truoccdha").val(ui.item.MO_TA_BENH_LY);
                    $("#icd_truoccdha").val(ui.item.ICD);
                    $("#mabenhly_truoccdha").val(ui.item.MA_BENH_LY);
                    return false;
                }
            });
            $("#chandoan_saucdha").combogrid({
                url: 'laydanhmucicd',
                debug: true,
                width: "400px",
                colModel: [{'columnName': 'ICD', 'label': 'ICD'},
                    {'columnName': 'MO_TA_BENH_LY', 'width': '40', 'label': 'Mô tả bệnh lý'},
                    {'columnName': 'MA_BENH_LY', 'width': '30', 'label': 'mabenhly', hidden: true}
                ],
                select: function (event, ui) {
                    $("#chandoan_saucdha").val(ui.item.MO_TA_BENH_LY);
                    $("#icd_saucdha").val(ui.item.ICD);
                    $("#mabenhly_saucdha").val(ui.item.MA_BENH_LY);
                    return false;
                }
            });
            $("#icd_truoccdha").keyup(function (evt) {
                if ($("#icd_truoccdha").val() != "") {
                    var url = "laymotabenhly?icd=" + $("#icd_truoccdha").val();
                    $.ajax({
                        url: url
                    }).done(function (data) {
                        if (data && data != "" && data != " ") {
                            arr = data.split("!!!");
                            $("#icd_truoccdha").val($("#icd_truoccdha").val().toString().toUpperCase());
                            $("#chandoan_truoccdha").val(arr[1]);
                            $("#mabenhly_truoccdha").val(arr[0]);
                        } else {
                            $("#chandoan_truoccdha").val("");
                            $("#mabenhly_truoccdha").val("");
                        }
                    }).error(function () {
                        $("#chandoan_truoccdha").val("");
                        $("#mabenhly_truoccdha").val("");
                    });
                }
            });

            $("#icd_saucdha").keyup(function (evt) {
                if ($("#icd_saucdha").val() != "") {
                    var url = "laymotabenhly?icd=" + $("#icd_saucdha").val();
                    $.ajax({
                        url: url
                    }).done(function (data) {
                        if (data && data != "" && data != " ") {
                            arr = data.split("!!!");
                            $("#icd_saucdha").val($("#icd_saucdha").val().toString().toUpperCase());
                            $("#chandoan_saucdha").val(arr[1]);
                            $("#mabenhly_saucdha").val(arr[0]);
                        } else {
                            $("#chandoan_saucdha").val("");
                            $("#mabenhly_saucdha").val("");
                        }
                    }).error(function () {
                        $("#chandoan_saucdha").val("");
                        $("#mabenhly_saucdha").val("");
                    });
                }

            });
        });
        function doSignPlugin(url, typeSign) {
            var x = new XMLHttpRequest();
            x.onload = function() {
                // Create a form
                var reader = new FileReader();
                reader.readAsDataURL(x.response);
                reader.onloadend = function() {

                    var base64data = reader.result.replace("data:application/pdf;base64,", "");
                    console.log("base64data", base64data)
                    var sigOptions = null;
                    sigOptions = new PdfSigner();
                    sigOptions.AdvancedCustom = true;
                    SignAdvanced(base64data, 'pdf', sigOptions, typeSign);
                }

            };
            x.responseType = 'blob';    // <-- This is necessary!
            x.open('GET', url, true);
            x.send();
        }
        function kyketquadientimtoken() {
            var mabenhnhan = $("#mabenhnhan").val();
            var hoten = $("#hoten").val();
            var diachi = $("#diachi").val();
            var tuoi = $("#tuoi").val();
            var phai = $("#gioitinh").val();
            if (phai == "true") {
                phai = "Nam";
            } else {
                phai = "Nữ";
            }
            var sophieu = $("#sophieu").val();
            var makhambenh = $("#makhambenh").val();
            var macdha = $("#macdha").val();
            dvtt = "${Sess_DVTT}";
            Sess_UserID = "${Sess_UserID}";
            var noitru = $("#noitru").val();
            var sttbenhan = $("#sttbenhan").val();
            var sttdotdieutri = $("#sttdotdieutri").val();
            var sttdieutri = $("#sttdieutri").val();
            var cannang = $("#cannang").val();
            var chieucao = $("#chieucao").val();
            var khoa = $("#Khoa").val();
            var giuong = $("#giuong").val();
            var buong = $("#buong").val();
            var sothebaohiem = $("#sothebhyt").val();
            var chandoan = $("#chandoan").val();
            var yeucaukiemtra = $("#yeucaukiemtra").val();
            var bacsidieutri = $("#bacsichidinh").val();
            var bacsichuyenkhoa = $("#bacsichuyenkhoa").val();
            if (bacsichuyenkhoa == "") {
                bacsichuyenkhoa = nv_with_chucdanh;
            }
            CMU_SOPHIEU_CDHA = sophieu;
            var solan = $("#solan").val();
            var tencdha = $("#tencdha").val();
            var motacdha = $("#loaimauin").val();
            //VNPTHIS-4697 23/11/2017
            var type = $("#loaiin").val();
            var ngayth = $("#ngayth_ct").val();
            if(ngayth == ""){
                ngayth = $("#ngaythuchien_cls").val();
            }
            var gioth = $("#gioth_ct").val();
            if(gioth == ""){
                gioth = $("#giothuchien_cls").val();
            }
            //VNPTHIS-4697 23/11/2017
            if (sophieu != "" && macdha != "") {
                var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha,
                    dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, cannang, chieucao,
                    khoa, giuong, buong, sothebaohiem, chandoan, yeucaukiemtra,
                    bacsidieutri, bacsichuyenkhoa, solan, tencdha, "0", motacdha, sovaovien
                    , sovaovien_noi
                    , sovaovien_dt_noi, 0, type, $("#ngayth_ct").val(), $("#gioth_ct").val() =="" ? "00:00:00" : $("#gioth_ct").val(),"0"];  //VNPTHIS-4697 23/11/2017 thêm loại in
                var url = "inketquadientim_svv?url=" + convertArray(arr);
                doSignPlugin(url);
            }
        }

        function SignAdvanced(data, type, sigOption, typeSign)
        {
            var dataJS = {};

            var arrData = [];
            // 1
            dataJS.data = data;
            dataJS.type = type;
            dataJS.sigOptions = JSON.stringify(sigOption);

            var jsData = "";
            jsData += JSON.stringify(dataJS);
            //
            arrData.push(jsData);
            var serial = "";
            vnpt_plugin.signArrDataAdvanced(arrData, serial, true, showMessageCDHAKQ);

        }
    </script>

</head>
<body onload="gioThucHienClsTimerChange();">
<div id="panel_all">
    <%@include file="../../../resources/Theme/include_pages/menu.jsp" %>
    <div id="panelwrap">
        <div class="center_content">
            <div id="tab_cdha">
                <ul>
                    <li><a href="#cdha_tabs_1" id="xn_cobhyt">Thông tin bệnh nhân</a></li>
                    <li><a href="#cdha_tabs_2" id="xn_bnyc">Kết quả</a></li>
                </ul>
                <div id="cdha_tabs_1">
                    <form id="form1" name="form1" method="post" action="">
                        <table width="100%">
                            <tr>
                                <td width="302" valign="top">
                                    <table width="302">
                                        <tr class="hpg_tmp">
                                            <td width="62px"><span style=" display:inline-block;">Từ Ngày </span></td>
                                            <td><input type="text" name="tungay" id="tungay"/></td>
                                        </tr>
                                        <tr>
                                            <td width="62px">
                                                        <span style=" display:inline-block;">
                                                            <span class="hpg_tmp">Đến </span>Ngày
                                                        </span>

                                            </td>
                                            <td width="238">
                                                <input type="text" name="ngaythuchien" id="ngaythuchien"/>
                                                <input type="button" name="lammoi" id="lammoi" value="Làm mới"/></td>
                                        </tr>
                                        <tr>
                                            <td>Loại</td>
                                            <td>
                                                <select id="loai_dientim" name="loai_dientim">
                                                    <option value="-1"> -- Tất cả --</option>
                                                    <option value="DIENTIM"> Điện tim</option>
                                                    <option value="LUUHUYETNAO">Lưu huyết não</option>
                                                    <option value="DIENNAO">Điện não</option>
                                                    <option value="DIENCO">Điện cơ</option>
                                                </select>
                                            </td>
                                        </tr>
                                        <!-- ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT -->
                                        <tr class="dlk_tmp">
                                            <td>Khoa</td>
                                            <td><select name="phongban" id="phongban" class="width100">
                                                <c:forEach var="i" items="${phongban}">
                                                    <option value="${i.ma_phongban}">${i.ten_phongban}</option>
                                                </c:forEach>
                                            </select>
                                            </td>
                                        </tr>
                                        <tr class="dlk_tmp" id="dlk_phong">
                                            <td>Phòng</td>
                                            <td><select name="phongbenh" id="phongbenh" class="width100">
                                                <c:forEach var="i" items="${phongbenh}">
                                                    <option value="${i.ma_phong_benh}">${i.ten_phong_benh}</option>
                                                </c:forEach>
                                            </select></td>
                                        </tr>
                                        <!--Start VNPT Bình Định-->
                                        <tr id="tr_phong">
                                            <td><label name="lbl_phongthuchiencdha" id="lbl_phongthuchiencdha"
                                                       style="display: inline-block; width:30">Phòng TH</label></td>
                                            <td><select name="phongthuchiencdha" id="phongthuchiencdha"
                                                        style="width:230px;">
                                                <option value="-1" selected>-- Tất cả --</option>
                                                <c:forEach var="i" items="${phongthuchiencdha}">
                                                    <option value="${i.MA_PHONG_BENH}"> ${i.TEN_PHONG_BENH}</option>
                                                </c:forEach>
                                            </select></td>
                                        </tr>
                                        <!--END VNPT Bình Định-->
                                        <tr class="dlk_tmp">
                                            <td>Đối tượng</td>
                                            <td><select name="doituong" id="doituong" class="width100">
                                                <option value="-1">--Tất cả--</option>
                                                <option value="1">Có BHYT</option>
                                                <option value="0">Không BHYT</option>
                                            </select></td>
                                        </tr>
                                        <!-- End ĐắkLắk -->
                                        <tr>
                                            <td colspan="2"><label><input type="checkbox" name="dathuchien"
                                                                          id="dathuchien">
                                                <b>Đã thực hiện</b></label></td>
                                        </tr>
                                        <tr>
                                            <td colspan="2">
                                                <table id="list_benhnhan"></table>
                                                <div id="pager2"></div>
                                            </td>
                                        </tr>
                                        <!-- Begin An Giang - Tâm 0918197999 28/12/2016: Hiển thị màu sắc theo loại bệnh nhân CLS-->
                                        <tr id="ghichutrangthai">
                                            <td colspan="2" style="padding-top:10px">
                                                <!--CMU: 27/10/2017-->
                                                <label style="color:red;font-weight: normal;">BN cấp cứu</label>
                                                <label style="color:#00ff00;margin-left:20px;font-weight: normal;">BN <
                                                    6 tuổi</label><br>
                                                <label style="color:#bf00ff;font-weight: normal;">Bệnh nhân VP, chưa
                                                    đóng tiền</label><br>
                                                <label style="color:#EE7600;font-weight: normal;">Bệnh nhân VP, đã đóng
                                                    tiền</label>
                                            </td>
                                        </tr>
                                        <!-- End An Giang - Tâm 0918197999 28/12/2016: Hiển thị màu sắc theo loại bệnh nhân CLS-->
                                    </table>
                                </td>
                                <td width="676" valign="top">
                                    <div>
                                        <fieldset>
                                            <legend>Thông tin bệnh nhân</legend>
                                            <table width="100%">
                                                <tr>
                                                    <td width="127">Số vào viện
                                                    </td>

                                                    <td width="756">
                                                        <input name="sovaovien" type="text" disabled="disabled"
                                                               class="width3" id="sovaovien"/>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td width="127">Họ tên<input name="mabenhnhan" type="hidden"
                                                                                 id="mabenhnhan"/>
                                                        <input name="macdha" type="hidden" id="macdha"/>
                                                        <input name="sophieu" type="hidden" id="sophieu"/>
                                                        <input name="makhambenh" type="hidden" id="makhambenh"/>
                                                        <input name="noitru" type="hidden" id="noitru"/>
                                                        <input name="tencdha" type="hidden" id="tencdha"/>
                                                        <input name="mota_cdha" type="hidden" id="mota_cdha"/>
                                                        <input name="nguoithuchien" type="hidden" id="nguoithuchien"/>
                                                    </td>
                                                    <input name="solan" type="hidden" id="solan"/></td>
                                <td width="756"><input name="hoten" type="text" disabled="disabled"
                                                       class="width1" id="hoten" style=""/>
                                    Tuổi
                                    <input name="tuoi" type="text" disabled="disabled"
                                           class="width3" id="tuoi"/>
                                    <select name="gioitinh" id="gioitinh" disabled="disabled">
                                        <option value="true">Nam</option>
                                        <option value="false">Nữ</option>
                                    </select></td>
                            </tr>
                            <tr>
                                <td width="127">Cân nặng
                                </td>
                                <td width="745"><input name="cannang" type="text"
                                                       disabled="disabled" class="width1"
                                                       id="cannang" style="width:153px"
                                                       ;/><span>kg</span>
                                    Chiều cao
                                    <input name="chieucao" type="text" disabled="disabled"
                                           class="width3" id="chieucao"/><span>cm</span>
                                </td>

                            </tr>
                            <tr>
                                <td width="127">Khoa
                                </td>
                                <td width="745"><input name="Khoa" type="text" disabled="disabled"
                                                       class="width1" id="Khoa" style="width:188px;"
                                                       ;/>
                                    Buồng
                                    <input name="buong" type="text" disabled="disabled"
                                           class="width3" id="buong"/>
                                </td>
                            </tr>
                            <tr>
                                <td>Giường</td>
                                <td><input name="giuong" type="text" disabled="disabled"
                                           class="width100" id="giuong" style="width:385px"/></td>
                            </tr>
                            <tr>
                                <td>Địa chỉ</td>
                                <td><input name="diachi" type="text" disabled="disabled"
                                           class="width100" id="diachi"/></td>
                            </tr>

                            <tr>
                                <td>Số thẻ BHYT</td>
                                <td><input name="sothebhyt" type="text" disabled="disabled"
                                           class="width100" id="sothebhyt"/></td>
                            </tr>
                            <tr>
                                <td>Chẩn đoán</td>
                                <td><input name="chandoan" type="text" disabled="disabled"
                                           class="width100" id="chandoan"/></td>
                            </tr>
                            <tr>
                                <td>Yêu cầu KT</td>
                                <td><input name="yeucaukiemtra" type="text" disabled="disabled"
                                           class="width100" id="yeucaukiemtra"/></td>
                            </tr>
                            <tr>
                                <td>Ngày Chỉ Định</td>
                                <td><input name="cmu_ngaychidinh" type="text" disabled="disabled"
                                           class="width100" id="cmu_ngaychidinh"/></td>
                            </tr>
                            <tr>
                                <td>BS điều trị
                                    <input name="mabacsichidinh" type="hidden" id="mabacsichidinh"/></td>
                                <td><input name="bacsichidinh" type="text" class="width100" id="bacsichidinh" readonly disabled/></td>
                            </tr>
                            <tr>
                                <td>BS chuyên khoa</td>
                                <td>
                                    <input name="bacsichuyenkhoa" type="text" class="" id="bacsichuyenkhoa"
                                           style="width: 300px; font-weight: bold; color: #ef0303;" readonly disabled/>
                                    <select name="nhanvien"  id="nhanvien"  style="width: 260px">
                                        <option value="0">-Chọn BS chuyên khoa--</option>
                                        <c:forEach var="i" items="${nhanvien}">
                                            <option value="${i.ma_nhanvien}">${i.ten_nhanvien}</option>
                                        </c:forEach>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td>Chọn máy</td>
                                <td>
                                    <select name="ma_maycdha_md" id="ma_maycdha_md"
                                            class="width100">
                                        <option value="" selected>Chọn máy CDHA-TDCN mặc định
                                        </option>
                                        <c:forEach var="e" items="${dsmaycdha}">
                                            <option value="${e.STT}">${e.TEN_MAY}</option>
                                        </c:forEach>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td>Ngày chỉ định</td>
                                <td>
                                    <input name="ngaychidinh_cls" type="text" disabled="disabled" id="ngaychidinh_cls" size="10"
                                           data-inputmask="'alias': 'date'"/>
                                    <input name="giochidinh_cls" type="text" disabled="disabled" id="giochidinh_cls" size="10"
                                           data-inputmask="'alias': 'hh:mm:ss'"/>
                                </td>
                            </tr>
                            <tr>
                                <td>Ngày TH</td>
                                <td>
                                    <input name="ngaythuchien_cls" type="text" id="ngaythuchien_cls"
                                           size="10" data-inputmask="'alias': 'date'"/>
                                    <input name="giothuchien_cls" type="text" id="giothuchien_cls"
                                           size="10" data-inputmask="'alias': 'hh:mm:ss'"/>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    <p id="vnptmoney" style="color:#21409A;;font-weight: bold;"></p>
                                    <p id="bidv" style="color:#219a5f;;font-weight: bold;margin-top: 10px"></p>
                                    <p id="vietinbank" style="color:#216b9a;;font-weight: bold;margin-top: 10px"></p>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    <!--<input type="button" name="luufile" id="luufile" value="Chọn file" class="button_shadow"/>-->
                                    <input type="button" name="luuthongtin" id="luuthongtin"
                                           value="Lưu thông tin" class="button_shadow"
                                           style="width: 110px"/>
                                    <input type="button" name="inphieu" id="inphieu"
                                           value="In phiếu" class="button_shadow"/>
                                    <input type="button" name="phieu_cdha_dientim" id="phieu_cdha_dientim"
                                           value="In phiếu CĐ" class="button_shadow"/>
                                    <!--CMU: 26062017-->
                                    <input name="lichsuCDHA" type="button" class="button_shadow"
                                           id="lichsuCDHA" style="width: 150" value="Lịch sử NS">
                                    <input type="button" name="luu_tt_maycdha" id="luu_tt_maycdha"
                                           value="Lưu máy thực hiện" style="width: 150px;"
                                           class="button_shadow"/>
                                    <input name="sttbenhan" type="hidden" id="sttbenhan"/>
                                    <input name="sttdotdieutri" type="hidden" id="sttdotdieutri"/>
                                    <input name="sttdieutri" type="hidden" id="sttdieutri"/></td>
                            </tr>
                        </table>

                        </fieldset>
                </div>
                <div style="padding-top: 5px">
                    <table id="list_dientim_bhyt"></table>
                </div>
                </td>
                </tr>
                </table>
                </form>
            </div>
            <div id="cdha_tabs_2">
                <form id="form2" name="form2" method="post" action="">
                    <!-- An Giang - Linh Tâm 01/09/2016: view thông tin hành chành của bệnh nhân ở form nhập kết quả -->
                    <div>
                        <fieldset>
                            <legend>Thông tin hành chánh của bệnh nhân</legend>
                            <table width="100%">
                                <tr>
                                    <td width="10%">Họ tên</td>
                                    <td width="40%"><input name="hoten_ct" type="text" disabled="disabled"
                                                           id="hoten_ct"
                                                           style="width: 320px; color: red; font-weight: bold"/>
                                    </td>
                                    <td>Tuổi
                                        <input name="tuoi_ct" type="text" disabled="disabled" id="tuoi_ct"
                                               style="width: 80px"/>
                                        Giới tính
                                        <input name="gioitinh_ct" type="text" disabled="disabled" id="gioitinh_ct"
                                               style="width: 70px"/>
                                        Mã y tế
                                        <input name="mabenhnhan_ct" type="text" disabled="disabled"
                                               id="mabenhnhan_ct" style="width: 170px"/>
                                    </td>
                                <tr>
                                    <td>Khoa</td>
                                    <td><input name="tenkhoa_ct" type="text" disabled="disabled"
                                               style="width: 320px" id="tenkhoa_ct"/></td>
                                    <td>Số thẻ BHYT
                                        <input name="sothebhyt_ct" type="text" disabled="disabled"
                                               style="width: 379px" id="sothebhyt_ct"/></td>
                                </tr>
                                <tr>
                                    <td>Thời gian chỉ định</td>
                                    <td colspan="2"><input name="ngaychidinh_kq" type="text" disabled="disabled" id="ngaychidinh_kq" size="10"
                                                           data-inputmask="'alias': 'date'"/>
                                        <input name="giochidinh_kq" type="text" disabled="disabled" id="giochidinh_kq" size="10"
                                               data-inputmask="'alias': 'hh:mm:ss'"/></td>
                                </tr>
                            </table>
                        </fieldset>
                    </div>
                    <!-- An Giang - Linh Tâm 01/09/2016: view thông tin hành chành của bệnh nhân ở form nhập kết quả -->
                    <div>
                        <fieldset>
                            <legend>Kết quả điện tim</legend>
                            <div style="display: flex; align-items: flex-start; justify-content: flex-start; width: 100%;">
                                <div style="flex: 0 0 65%;">
                                    <table width="100%">
                                        <tr>
                                            <td><label for="thoiGianBatDau_cls">Thời gian thực hiện y lệnh:</label></td>
                                            <td>
                                                <input type="text" id="thoiGianBatDau_cls"
                                                <%--                                               data-inputmask="'alias': 'datetime'"--%>
                                                       width="auto"/>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td width="11%">Thời gian kết quả:</td>
                                            <!--    <td width="89%"><input name="chandoan" type="text" class="width100" id="chandoan" /></td> -->
                                            <td>
                                                <input name="ngayth_ct" type="text" id="ngayth_ct" size="10"
                                                       data-inputmask="'alias': 'date'"/>
                                                <!--            //VLG chinh textbox gio tra kq chay thoi gian-->
                                                <input name="gioth_ct" type="text" id="gioth_ct" size="10"
                                                       data-inputmask="'alias': 'hh:mm:ss'"/>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Chẩn đoán CLS</td>
                                            <td>
                                                <input name="maBenhLyChanDoanCanLamSang" type="hidden" id="maBenhLyChanDoanCanLamSang"/>
                                                <input name="icdChanDoanCanLamSang" type="text" class="icd" id="icdChanDoanCanLamSang" style="width: 50px"/>
                                                <input name="btnChanDoanCanLamSang" type="button" id="btnChanDoanCanLamSang" class="icd" value="..."/>
                                                <label>
                                                    <input id="tenChanDoanCanLamSang" name="tenChanDoanCanLamSang" class="icd" style="width:342px"/>
                                                </label>
                                            </td>
                                        </tr>
                                        <td>Người đọc kết quả</td>
                                        <td>
                                            <select id="cboNguoiDocKetQua">
                                            </select>
                                            <select name="khoabacsidocketqua" style="width:49%; float:right" id="khoabacsidocketqua">
                                                <c:forEach var="i" items="${phongban}">
                                                    <c:if test="${i.makhoa != '0' && i.ma_phongban != '-1'}">
                                                        <option value="${i.ma_phongban}">
                                                                ${i.ten_phongban}
                                                        </option>
                                                    </c:if>
                                                </c:forEach>
                                            </select>
                                        </td>
                                        </tr>
                                        <tr>
                                            <td width="11%">Kỹ thuật viên</td>
                                            <td colspan="">
                                                <select name="cmu_kythuatvien" style="width:50%" id="cmu_kythuatvien">
                                                </select>
                                                <select name="khoakythuatvien" style="width:49%; float:right" id="khoakythuatvien">
                                                    <c:forEach var="i" items="${phongban}">
                                                        <c:if test="${i.makhoa != '0' && i.ma_phongban != '-1'}">
                                                            <option value="${i.ma_phongban}">
                                                                    ${i.ten_phongban}
                                                            </option>
                                                        </c:if>
                                                    </c:forEach>
                                                </select>
                                            </td>
                                        </tr>
                                        <%--<tr>
                                            <td width="11%">Chẩn đoán</td>
                                            <td width="89%"><input name="chandoan" type="text" class="width100" id="chandoan" /></td>
                                        </tr>--%>
                                        <tr class = "icdTruocSau">
                                            <td>Chẩn đoán trước cdha<span style="color: red">(*):</span></td>
                                            <input name="mabenhly_truoccdha" type="hidden" id="mabenhly_truoccdha" />
                                            <td>
                                                <input name="icd_truoccdha" type="text"  style="width:10%"  id="icd_truoccdha" />
                                                <input name="chandoan_truoccdha" type="text" style="width:89%"  id="chandoan_truoccdha" />
                                            </td>
                                        </tr>
                                        <tr class = "icdTruocSau">
                                            <td>Chẩn đoán sau cdha<span style="color: red">(*):</span></td>
                                            <input name="mabenhly_saucdha" type="hidden" id="mabenhly_saucdha"/>
                                            <td>
                                                <input name="icd_saucdha" type="text"  style="width:10%"  id="icd_saucdha" />
                                                <input name="chandoan_saucdha" type="text" style="width:89%"  id="chandoan_saucdha" />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td width="11%">Mẫu điện tim 2</td>
                                            <td width="89%"><select name="maudientim" class="width100" id="maudientim">
                                                <option value="0" selected>-- Chọn mẫu điện tim--</option>
                                                <c:forEach var="i" items="${maudientim}">
                                                    <option value="${i.MA_MAUDIENTIM}"> ${i.TEN_MAUDIENTIM}</option>
                                                </c:forEach>
                                            </select></td>
                                        </tr>
                                    </table>
                                </div>
                                <div style="flex: 0 0 35%; display: flex; justify-content: center; align-items: center;">
                                    <table>
                                        <tr>
                                            <td>
                                                <div id="say-cheese-snapshots"><img  class="width100" id="preview-image"
                                                                                     src="<c:url value="/resources/webcam/camera_png.jpg" />"
                                                                                     width="230px" height="230px"/></div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="text-align: center">
                                                <input type="button" name="snapshot-rotate" id="snapshot-xoa" value="Xóa ảnh"
                                                       class="button_shadow"/>
                                                <input type="button" name="save-snapshot" id="save-snapshot" value="Lưu ảnh"
                                                       class="button_shadow"/>
                                                <label class="button_shadow" style="height: 26px;"> Duyệt ảnh <input
                                                        type="file" accept="image/png, image/gif, image/jpeg" name="browse-file" id="browse-file"
                                                        style="display: none;"></label>
                                                <input type="button" name="snapshot-rotate" id="snapshot-rotate" value="Xoay ảnh"
                                                       class="button_shadow"/>
                                                <input type="hidden" name="snapshot-stt" id="snapshot-stt"
                                                       class="button_shadow"/>
                                                <canvas id="canvas" style="display: none;"></canvas>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            <table>
                                <tr>
                                    <td>Mô tả</td>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td colspan="2"><textarea name="ketqua" cols="80" rows="10" class="width100"
                                                              id="ketqua"></textarea></td>
                                </tr>
                                <tr hidden="hidden">
                                    <td colspan="2">
                                        <div>
                                            <table width="300">
                                                <tr>
                                                    <td width="70">Chọn kho:
                                                        <input name="matoathuoc_dv" type="hidden"
                                                               id="matoathuoc_dv"/></td>
                                                    <td>
                                                        <select name="kho_dv" id="kho_dv" style="width:220px">
                                                            <c:forEach var="i" items="${khodichvu}">
                                                                <option value="${i.MAKHO}">${i.TENKHO}</option>
                                                            </c:forEach>
                                                        </select>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div style="width:300px">
                                            <div class="table_beauty">
                                                <c:if test="${AnNhapThuocToaDichVu == '0'}">
                                                    <table width="300">
                                                        <thead>
                                                        <tr>
                                                            <th width="132">
                                                                Tên thuốc, vật tư
                                                            </th>
                                                            <th width="51">ĐVT</th>
                                                            <th width="72"><input name="dongia_dv" type="hidden"
                                                                                  id="dongia_dv"/>
                                                                Số lượng
                                                            </th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        <tr id="thuocdichvu_tt_div">
                                                            <td><input name="makhovattu_dv" type="hidden"
                                                                       id="makhovattu_dv"/><input name="mavattu_dv"
                                                                                                  type="hidden"
                                                                                                  id="mavattu_dv"/>
                                                                <input name="sott_toathuoc_dv" type="hidden"
                                                                       id="sott_toathuoc_dv"/>
                                                                <input name="tenthuongmai_dv" type="text"
                                                                       id="tenthuongmai_dv" size="13"
                                                                       class="width_100per"/></td>
                                                            <td><input name="dvt_dv" type="text" id="dvt_dv"
                                                                       size="4" readonly class="width_100per"/></td>
                                                            <td><input name="soluong_dv" type="text" id="soluong_dv"
                                                                       size="12" onKeyPress="validate_number(event)"
                                                                       class="width_100per"/></td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </c:if>
                                            </div>
                                            <div id="thuocdichvu_div" style="width:300px">
                                                <table id="list_thuocdichvu" width="300"></table>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Kết luận</td>
                                    <td><textarea style="height: 80px;" name="ketluan" rows="2" class="width100"
                                                  id="ketluan"></textarea></td>
                                </tr>
                                <tr>
                                    <td>Lời dặn bác sĩ</td>
                                    <td><input name="loidanbacsi" type="text" class="width100" id="loidanbacsi"/>
                                    </td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td><input name="luu_tt" type="button" class="button_shadow" id="luu_tt"
                                               value="Lưu"/>
                                        <input type="button" name="cmuekip" onclick="opencmuekipt()" id="cmuekip" value="EKIP"
                                               class="button_shadow"/>
                                        <input name="inphieu_dientim" type="button" class="button_shadow"
                                               id="inphieu_dientim" value="In phiếu"/>
                                        <!--VNPTHIS-4697 23/11/2017 thêm loại in-->
                                        <select name="loaiin" id="loaiin">
                                            <option value="1">RTF</option>
                                            <option value="2">PDF</option>
                                        </select>
                                        <select name="loaimauin" id="loaimauin">
                                            <option value="DIENTIM">Điện tim</option>
                                            <option value="DONHANAP">Đo nhãn áp</option>
                                            <option value="GHIDIENCO">Ghi điện cơ</option>
                                            <option value="DIENNAO">Ghi điện não</option>
                                            <option value="DOTOCDOTHANKINH">ĐO TỐC ĐỘ DẪN TRUYỀN THẦN KINH</option>
                                        </select>

                                        <input style="width: 150px" type="button" onclick='kyketquadientim("${Sess_UserID}", "${Sess_DVTT}")' class="button_shadow" value="Ký Số SMARTCA">
                                        <input style="width: 150px" type="button" onclick='inketquadientim("${Sess_UserID}", "${Sess_DVTT}")' class="button_shadow" value="In Ký Số SMARTCA">
                                        <input style="width: 150px" type="button" onclick='kyketquadientimtoken("${Sess_UserID}")' class="button_shadow" value="Ký Số Token">
                                        <input style="width: 150px" type="button" onclick='cmuinkisotokensieuamkq("${Sess_DVTT}")' class="button_shadow" value="In Ký Số Token">

                                        <!--VNPTHIS-4697 23/11/2017 thêm loại in-->
                                        <input type="button" name="viewimageweb" id="viewimageweb"
                                               value="Xem ảnh Web" class="button_shadow" style="width: auto"/>
                                        <input type="button" name="viewimageapp" id="viewimageapp"
                                               value="Xem ảnh App " class="button_shadow" style="width: auto"/>
                                    </td>
                                </tr>
                            </table>

                        </fieldset>
                    </div>
                </form>
            </div>
        </div>
    </div> <!--end of center_content-->
    <%@include file="../camau/canlamsang/cmuekipdientim.jsp" %>
    <%@include file="../../../resources/Theme/include_pages/footer.jsp"%>
</div>
<!--CMU: 26062017 -->
<div id="dialog_lichsuCDHA" title="Lịch sử điện tim" style="display: none">
    <div id="tab_ls_cdha">
        <ul>
            <li><a href="#cdha_ls_tabs_1" id="xn_cobhyt">Thông tin bệnh nhân</a></li>
            <li style="display:none"><a href="#cdha_ls_tabs_2" id="xn_bnyc">Kết quả</a></li>
        </ul>
        <div id="cdha_ls_tabs_1">
            <table id="list_lichsuCDHA"></table>
        </div>
        <div id="cdha_ls_tabs_2" style="display:none">
            <form id="form2" name="form2" method="post" action="">
                <div>
                    <fieldset>
                        <legend>Kết quả điện tim</legend>
                        <table width="100%">
                            <tr>
                                <td width="11%">Chẩn đoán</td>
                                <td width="89%"><input name="ls_chandoan" type="text" class="width100"
                                                       id="ls_chandoan"/></td>
                            </tr>
                            <tr>
                                <td>Kết quả</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                            <textarea name="ls_ketqua" cols="80" rows="10" class="width100"
                                                      id="ls_ketqua">

                                            </textarea>
                                </td>
                            </tr>
                            <tr>
                                <td>Kết luận</td>
                                <td>
                                    <textarea name="ls_ketluan" rows="2" class="width100" id="ketluan"></textarea>
                                </td>
                            </tr>
                            <tr>
                                <td>Lời dặn bác sĩ</td>
                                <td><input name="ls_loidanbacsi" type="text" class="width100" id="loidanbacsi"/>
                                </td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>
                                    <input name="ls_inphieu_dientim" type="button" class="button_shadow"
                                           id="ls_inphieu_dientim" value="In phiếu"/>
                                    <input name="ls_inphieu_dientim_ngang" type="button" class="button_shadow"
                                           id="ls_inphieu_dientim_ngang" style="width: 150"
                                           value="In phiếu (ngang)"/>
                                </td>
                            </tr>
                        </table>

                    </fieldset>
                </div>
            </form>
        </div>
    </div>
</div>
<jsp:include page="../Canlamsang/dialog/diaglog-tim-kiem-icd.jsp"/>
<script>
    //vlg
    $('#nhanvien').change(function () {
        if ($('#nhanvien').val() != '0') {
            $('#bacsichuyenkhoa').val($('#nhanvien').find(":selected").text());
        } else {
            $('#bacsichuyenkhoa').val("");
        }
    });
    //end vlg
</script>

</body>


</html>