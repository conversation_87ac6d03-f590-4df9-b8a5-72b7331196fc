var thongtinhsba = {
    todieutri: [],
    xetnghiem: [],
    thongtinbn: {}
}
var formKetQuaXetNghiem, luuLogOld;
var keyLuuLogXetNghiem = {
    "MA_BS_DOC_KQ": "Người đọc kết quả",
    "KY_THUAT_VIEN": "Kỹ thuật viên",
    "NGUOI_GIAO_MAU": "Người giao mẫu",
    "NGUOI_NHAN_MAU": "Người nhận mẫu",
    "NGAY_LAY_MAU": "Ng<PERSON>y lấy mẫu",
    "NGAY_NHAN_MAU": "Ngày nhận mẫu",
    "NGAY_TH_YL": "<PERSON><PERSON><PERSON> bắt đầu",
    "NGAY_TRA_KET_QUA": "Ngày trả kết quả",
}
var rowDataXNSelected;
var selfRowId;
var listXNSelected = [];
var allDataXN = [];
$(function() {

    $(".input-date").inputmask({
        mask: "1/2/y h:s",
        placeholder: "dd/mm/yyyy hh:mm",
        alias: "datetime",
        hourFormat: "24"
    });
    $(".input-date").datetimepicker({dateFormat: "dd/mm/yy"});
    $(".input-date").val(singletonObject.ngayhientai + " 00:00");

    $(".input-date-second").inputmask({
        mask: "1/2/y h:s:s",
        placeholder: "dd/mm/yyyy hh:mm:ss",
        alias: "datetime",
        hourFormat: "24"
    });
    $(".input-date-second").datetimepicker({
        dateFormat: "dd/mm/yy",
        timeFormat: 'HH:mm:ss',
        showSecond: true,
    });
    $(".input-date-second").val(singletonObject.ngayhientai + " 00:00:00");

    $(".input-only-date").inputmask({
        mask: "1/2/y",
        placeholder: "dd/mm/yyyy",
        alias: "datetime",
        hourFormat: "24"
    });
    $(".input-only-date").datepicker({dateFormat: "dd/mm/yy"});
    $(".input-only-date").val(singletonObject.ngayhientai);

    $(".input-only-time").inputmask({
        mask: "h:s",
        placeholder: "hh:mm",
        alias: "datetime",
        hourFormat: "24"
    });
    $(".input-only-time-second").inputmask({
        mask: "h:s:s",
        placeholder: "hh:mm:ss",
        alias: "datetime",
        hourFormat: "24"
    });
    $(".unbinddbclick").unbind("dblclick");
    $("#hsba_tabs").tabs();

    $("#list_benhnhan").jqGrid({
        url: "",
        datatype: "json",
        loadonce: true,
        height: 400,
        width: null,
        shrinkToFit: false,
        rowattr: function (rd) {
            if(singletonObject.hienThiMauSacBNCoVTXN == "1" && singletonObject.hienThiMauSacBN == "1")  {
                if (rd.CAPCUU === "1" && !rd.KEVATTUCLS == 1) {
                    return {"class": "bncapcuuClass"};
                }
                else if (rd.CAPCUU === "1" && rd.KEVATTUCLS == 1) {
                    return {"class": "bncapcuucoclsClass"};
                }
                //Khong bhyt va da thanh toan
                else if (!rd.SOTHEBHYT && rd.DA_THANH_TOAN == 0 &&!rd.KEVATTUCLS == 1) {
                    return {"class": "kobhytClass"};
                }
                else if (!rd.SOTHEBHYT && rd.DA_THANH_TOAN == 0 && rd.KEVATTUCLS == 1) {
                    return {"class": "kobhytcoclsClass"};
                }
                //Doi tuong ko bao hiem va ko phai la tre em
                else if(!rd.SOTHEBHYT && rd.DA_THANH_TOAN == 1 && !rd.KEVATTUCLS == 1) {
                    return {"class": "vienphiClass"};
                }
                else if(!rd.SOTHEBHYT && rd.DA_THANH_TOAN == 1 && rd.KEVATTUCLS == 1) {
                    return {"class": "vienphicoclsClass"};
                }
                //la tre em
                else if (rd.TUOI.indexOf("tháng") != -1 && !rd.KEVATTUCLS == 1) {
                    return {"class": "treemClass"};
                }
                else if (rd.TUOI.indexOf("tháng") != -1 && rd.KEVATTUCLS == 1) {
                    return {"class": "treemcoclsClass"};
                }
                else if (rd.KEVATTUCLS == 1) {
                    return {"class": "kevattuclsClass"};
                }


            }
            else if(singletonObject.hienThiMauSacBNCoVTXN !== "1" && singletonObject.hienThiMauSacBN == "1") {
                if(Number(rd.THANHTOANMONEY) > 0) {
                    return {"class": "moneyClass"}
                }
                if (rd.CAPCUU === "1") {
                    return {"class": "bncapcuuClass"};
                }
                //Khong bhyt va da thanh toan
                else if (rd.CO_BHYT == 0 && rd.DA_THANH_TOAN == 0) {
                    return {"class": "kobhytClass"};
                }
                //Doi tuong ko bao hiem va ko phai la tre em
                else if (rd.CO_BHYT == 0 && rd.DA_THANH_TOAN == 1) {
                    return {"class": "vienphiClass"};
                }
                //la tre em
                else if (rd.TUOI.indexOf("tháng") != -1) {
                    return {"class": "treemClass"};
                }
            }
        },
        colModel: [
            {label: "Ký số", name: 'KEYSIGN_HT', index: 'KEYSIGN_HT', hidden: true, formatter: function (cellvalue, options, rowObject) {
                    return rowObject.KEYSIGN ? "Đã ký" : "Chưa ký";
                },
                cellattr: function (rowId, tv, rawObject, cm, rdata) {
                    return rawObject.KEYSIGN ? 'style="color:green; font-weight:bold"' : 'style="color:red; font-weight:bold"';
                }
            },
            {label: "KEYSIGN", name: 'KEYSIGN', index: 'KEYSIGN', hidden: true},
            {label: "Ký số", name: 'KEYSIGN_HT', index: 'KEYSIGN_HT', width: 80, formatter: function (cellvalue, options, rowObject) {
                    return rowObject.KEYSIGN ? "Đã ký" : "Chưa ký";
                },
                cellattr: function (rowId, tv, rawObject, cm, rdata) {
                    return rawObject.KEYSIGN ? 'style="color:green; font-weight:bold"' : 'style="color:red; font-weight:bold"';
                }
            },
            {label: "STT",name: 'STT', index: 'STT',width: 100, fixed: true, hidden: singletonObject.dvtt.startsWith("96")},
            {label: "SID",name: 'SID', index: 'SID',width: 80, fixed: true, hidden: !singletonObject.dvtt.startsWith("96")},
            {label: "STT_HANGNGAY", name: 'STT_HANGNGAY', index: 'STT_HANGNGAY', hidden: true},
            {label: "Mã BN", name: 'MABENHNHAN', index: 'MABENHNHAN',align: "center", fixed: true, width: 100, hidden: singletonObject.layMaBenhNhanGrid ? true : false},
            {label: "Họ tên", name: 'TENBENHNHAN_HT', index: 'TENBENHNHAN_HT', fixed: true, width: 250,
                formatter: function (cellvalue, options, rowObject) {
                    var color;
                    var color_text;
                    if (singletonObject.hienThiMauSacBN == "0") {
                        if (rowObject.CAPCUU.toString() === "1") {
                            color = 'red';
                            color_text = 'white';
                        } else if (rowObject.DA_THANH_TOAN == "1") {
                            color = '#009900';
                            color_text = 'white';
                        } else {
                            color = 'white';
                            color_text = 'black';
                        }
                    }
                    return '<span class="cellWithoutBackground" style="font-weight:bold ;background-color:' + color + ';color:' + color_text + '">' + cellvalue + '</span>';
                }
            },
            {label: "TENBENHNHAN", name: 'TENBENHNHAN', index: 'TENBENHNHAN', width: 200, hidden: true},
            {label: "Tuổi", name: 'TUOI_HT', index: 'TUOI_HT', width: 100, align: "center", fixed: true,
                formatter: function (cellvalue, options, rowObject) {
                    var tuoiHt = rowObject.TUOI == 0? (rowObject.THANG > 0? rowObject.THANG + ' tháng': rowObject.NGAY + ' ngày'): rowObject.TUOI + ' tuổi';
                    return tuoiHt;
                }
            },
            {label: "Tuổi",name: 'TUOI', index: 'TUOI', width: 150, align: "center", fixed: true, hidden: true},
            {label: "Giới tính", name: 'GIOITINH_HT', index: 'GIOITINH_HT', width: 50, formatter: function (cellvalue, options, rowObject) {
                    return rowObject.GIOITINH == "true" ? "Nam" : "Nữ";
                }
            },
            {label: 'Số phiếu', name: 'SO_PHIEU', index: 'SO_PHIEU', width: 200, fixed: true},
            {label: 'Tên xét nghiệm', name: 'TEN_XET_NGHIEM', index: 'TEN_XET_NGHIEM', width: 600, fixed: true},
            {label: "Nội trú",name: 'NOITRU', index: 'NOITRU', width: 60, align: "center", fixed: true, hidden: true},
            {label: "Nội trú",name: 'NOITRU_HT', index: 'NOITRU', width: 60, align: "center", fixed: true, formatter: 'checkbox'},
            {label: 'GIOITINH', name: 'GIOITINH', index: 'GIOITINH', hidden: true, width: 30, fixed: true},
            {label: 'Lấy mẫu', name: 'DALAYMAU', index: 'DALAYMAU', width: 80, fixed: true, formatter: 'checkbox', align: 'center'},
            {label: 'Khoa chỉ định', name: 'TEN_KHOA_CHI_DINH', index: 'TEN_KHOA_CHI_DINH', width: 150, fixed: true},
            {label: 'Phòng chỉ định', name: 'TEN_PHONG_CHI_DINH', index: 'TEN_PHONG_CHI_DINH', width: 150, fixed: true},
            {label: 'DIACHI', name: 'DIACHI', index: 'DIACHI', hidden: true, width: 30, fixed: true},
            {label: 'NAM_SINH', name: 'NAM_SINH', index: 'NAM_SINH', hidden: true, width: 30, fixed: true},
            {label: 'SOTHEBHYT', name: 'SOTHEBHYT', index: 'SOTHEBHYT', hidden: true, width: 30, fixed: true},
            {label: 'MA_KHAM_BENH', name: 'MA_KHAM_BENH', index: 'MA_KHAM_BENH', hidden: true, width: 30, fixed: true},
            {label: 'STT_BENHAN', name: 'STT_BENHAN', index: 'STT_BENHAN', hidden: true, width: 30, fixed: true},
            {label: 'STT_DOTDIEUTRI', name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', hidden: true, width: 30, fixed: true},
            {label: 'STT_DIEUTRI', name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', hidden: true, width: 30, fixed: true},
            {label: 'NGUOI_CHI_DINH', name: 'NGUOI_CHI_DINH', index: 'NGUOI_CHI_DINH', hidden: true, width: 30, fixed: true},
            {label: 'PHONG_CHI_DINH', name: 'PHONG_CHI_DINH', index: 'PHONG_CHI_DINH', hidden: true, width: 30, fixed: true},
            {label: 'MA_PHONG_XN', name: 'MA_PHONG_XN', index: 'MA_PHONG_XN', hidden: true, width: 30, fixed: true},
            {label: 'KET_LUAN_TONG', name: 'KET_LUAN_TONG', index: 'KET_LUAN_TONG', hidden: true, width: 30, fixed: true},
            {label: 'SOPHIEUTHANHTOAN', name: 'SOPHIEUTHANHTOAN', index: 'SOPHIEUTHANHTOAN', hidden: true, width: 30, fixed: true},
            {label: 'TEN_PHONGBAN', name: 'TEN_PHONGBAN', index: 'TEN_PHONGBAN', hidden: true, width: 30, fixed: true},
            {label: 'CAPCUU', name: 'CAPCUU', index: 'CAPCUU', hidden: true, width: 30, fixed: true},
            {label: 'NGAY_CHI_DINH', name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', hidden: true, width: 30, fixed: true},
            {label: 'NGAYTAO', name: 'NGAYTAO', index: 'NGAYTAO', hidden: true, width: 30, fixed: true},
            {label: 'STTBENHVIENCAP', name: 'STTBENHVIENCAP', index: 'STTBENHVIENCAP', hidden: true, width: 30, fixed: true},
            {label: 'TRANG_THAI_XET_NGHIEM', name: 'TRANG_THAI_XET_NGHIEM', index: 'TRANG_THAI_XET_NGHIEM', hidden: true, width: 30, fixed: true},
            {label: 'SOVAOVIEN', name: 'SOVAOVIEN', index: 'SOVAOVIEN', hidden: true, width: 30, fixed: true},
            {label: 'SOVAOVIEN_NOI', name: 'SOVAOVIEN_NOI', index: 'SOVAOVIEN_NOI', hidden: true, width: 30, fixed: true},
            {label: 'SOVAOVIEN_DT_NOI', name: 'SOVAOVIEN_DT_NOI', index: 'SOVAOVIEN_DT_NOI', hidden: true, width: 30, fixed: true},
            {label: 'DA_THANH_TOAN', name: 'DA_THANH_TOAN', index: 'DA_THANH_TOAN', hidden: true, width: 30, fixed: true},
            {label: 'NGAY_LAY_MAU', name: 'NGAY_LAY_MAU', index: 'NGAY_LAY_MAU', hidden: true, width: 30, fixed: true},
            {label: 'GIO_LAY_MAU', name: 'GIO_LAY_MAU', index: 'GIO_LAY_MAU', hidden: true, width: 30, fixed: true},
            {label: 'CO_BAO_HIEM', name: 'CO_BAO_HIEM', index: 'CO_BAO_HIEM', hidden: true, width: 30, fixed: true},
            {label: 'SOBENHAN', name: 'SOBENHAN', index: 'SOBENHAN', hidden: true, width: 30, fixed: true},
            {label: 'SOBENHAN_TT', name: 'SOBENHAN_TT', index: 'SOBENHAN_TT', hidden: true, width: 30, fixed: true},
            {label: 'SOPHIEUTHANHTOAN', name: 'SOPHIEUTHANHTOAN', index: 'SOPHIEUTHANHTOAN', hidden: true, width: 30, fixed: true},
            {label: 'TI_LE_MIEN_GIAM', name: 'TI_LE_MIEN_GIAM', index: 'TI_LE_MIEN_GIAM', hidden: true, width: 30, fixed: true},
            {label: 'NGAY_KB', name: 'NGAY_KB', index: 'NGAY_KB', hidden: true, width: 30, fixed: true},
            {label: 'NGAY_TH_YL', name: 'NGAY_TH_YL', index: 'NGAY_TH_YL', hidden: true, width: 30, fixed: true},
            {label: 'MA_BS_DOC_KQ', name: 'MA_BS_DOC_KQ', index: 'MA_BS_DOC_KQ', hidden: true, width: 30, fixed: true},
            {label: 'NGAY_CHI_DINH_F', name: 'NGAY_CHI_DINH_F', index: 'NGAY_CHI_DINH_F', hidden: true, width: 30, fixed: true},
            {label: 'NGUOI_LAY_MAU', name: 'NGUOI_LAY_MAU', index: 'NGUOI_LAY_MAU', hidden: true, width: 30, fixed: true},
            {label: 'NGUOI_THUC_HIEN', name: 'NGUOI_THUC_HIEN', index: 'NGUOI_THUC_HIEN', hidden: true, width: 30, fixed: true},
            {label: 'CO_BHYT', name: 'CO_BHYT', index: 'CO_BHYT', hidden: true, width: 30, fixed: true},
            {label: 'KEVATTUCLS', name: 'KEVATTUCLS', index: 'KEVATTUCLS', hidden: true, width: 30, fixed: true},
            {label: 'MIENPHI', name: 'MIENPHI', index: 'MIENPHI', hidden: true, width: 30, fixed: true},
            {label: 'THANHTOANMONEY', name: 'THANHTOANMONEY', index: 'THANHTOANMONEY', hidden: true, width: 30, fixed: true},
            {label: 'SOPHIEULUTRU', name: 'SOPHIEULUTRU', index: 'SOPHIEULUTRU', hidden: true, width: 30, fixed: true},
            {label: 'DA_XET_NGHIEM', name: 'DA_XET_NGHIEM', index: 'DA_XET_NGHIEM', hidden: true, width: 30, fixed: true},
            {label: 'NGUOI_GIAO_MAU', name: 'NGUOI_GIAO_MAU', index: 'NGUOI_GIAO_MAU', hidden: true, width: 30, fixed: true},
            {label: 'NGUOI_NHAN_MAU', name: 'NGUOI_NHAN_MAU', index: 'NGUOI_NHAN_MAU', hidden: true, width: 30, fixed: true},
            {label: 'NGAY_NHAN_MAU', name: 'NGAY_NHAN_MAU', index: 'NGAY_NHAN_MAU', hidden: true, width: 30, fixed: true},
            {label: 'KY_THUAT_VIEN', name: 'KY_THUAT_VIEN', index: 'KY_THUAT_VIEN', hidden: true, width: 30, fixed: true},
            {label: "KHOA_CHI_DINH", name: 'KHOA_CHI_DINH', index: 'KHOA_CHI_DINH', hidden: true},
        ],
        rowNum: 100000,
        ignoreCase: true,
        caption: "Danh sách bệnh nhân",
        onSelectRow: function (id) {
            $("#hsba_tabs").tabs("option", "disabled", [1]);
        },
        footerrow: true,
        beforeRequest: function () {
            showLoaderIntoWrapId("list_benhnhan_wrap");
        },
        loadComplete: function () {
            hideLoaderIntoWrapId("list_benhnhan_wrap");
        },
        gridComplete: function () {
            var grid = $("#list_benhnhan");
            var sl = grid.getGridParam("records");
            sl = "Tổng số ca: " + sl;
            grid.jqGrid("footerData", "set", {TENBENHNHAN_HT: sl});
        },
        onRightClickRow: function (id) {
            if(id) {
                var rowData = getThongtinRowSelected("list_benhnhan");
                $.contextMenu('destroy', '#list_benhnhan tr');
                var items = {
                    "inbarcode": {name: '<p><i class="fa fa-print text-warning" aria-hidden="true"></i> In barcode</p>'},
                };
                if (singletonObject.khoaKham == "0") {
                    items = {
                        ...items,
                        "goiso": {name: '<p><i class="fa fa-volume-up text-primary" aria-hidden="true"></i> Gọi số</p>'},
                        "dalaymau": {name: '<p><i class="fa fa-check-circle text-success" aria-hidden="true"></i> Đã lấy mẫu</p>'},
                    }
                    if (rowData.KEYSIGN != "") {
                        items = {
                            ...items,
                            "huykyso": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Huỷ ký số</p>'},
                        }
                    } else {
                        items = {
                            ...items,
                            "huyketqua": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Huỷ kết quả</p>'},
                        }
                    }
                }
                $.contextMenu({
                    selector: '#list_benhnhan tr',
                    reposition : false,
                    callback: function (key, options) {
                        var idWrap = "list_benhnhan_wrap"
                        if (key === "huyketqua" ) {
                            var arr = [singletonObject.dvtt, rowData.NOITRU, rowData.SO_PHIEU, rowData.MA_KHAM_BENH, rowData.STT_BENHAN,
                                rowData.STT_DOTDIEUTRI, rowData.STT_DIEUTRI, 0];
                            if (singletonObject.admin != "0" || (singletonObject.dvtt == '96029' && rowData.MA_BS_DOC_KQ == singletonObject.userId)
                                || (singletonObject.dvtt != '96029' || rowData.KY_THUAT_VIEN == singletonObject.userId && rowData.NGAYTAO == singletonObject.ngayhientai && singletonObject.kyThuatVienHuyKetQua == "1")
                                || (singletonObject.dvtt != '96029' || rowData.KY_THUAT_VIEN == singletonObject.userId && singletonObject.kyThuatVienHuyKetQua == "0")) {
                                if(singletonObject.dvtt == 96145 && singletonObject.admin == "0") {
                                    return notifiToClient("Red", "Bạn không có quyền hủy kết quả xét nghiệm, vui lòng liên hệ admin");
                                }
                                if(singletonObject.dvtt == 96004 && singletonObject.userId != "1344516") {
                                    return notifiToClient("Red", "Bạn không có quyền hủy kết quả xét nghiệm, vui lòng liên hệ Minh");
                                }
                                confirmToClient("Xác nhận hủy kết quả xét nghiệm?", function() {
                                    if (rowData.SO_PHIEU != "") {
                                        var url = "huyketquaxetnghiem?url=" + convertArray(arr);
                                        $.ajax({
                                            url: url
                                        }).always(function () {
                                            var soVaoVien = rowData.NOITRU == "0" ? rowData.SOVAOVIEN : rowData.SOVAOVIEN_NOI;
                                            luuLogHSBATheoBN({
                                                SOVAOVIEN: soVaoVien,
                                                LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                                                NOIDUNGBANDAU: '',
                                                NOIDUNGMOI: "Huỷ kết quả xét nghiệm " + rowData.SO_PHIEU,
                                                USERID: singletonObject.userId,
                                                ACTION: LOGHSBAACTION.EDIT.KEY,
                                                NGOAI: rowData.NOITRU == "0" ? 1 : 0
                                            })
                                            $("#xn_lammoi").click();
                                        });
                                    } else {
                                        notifiToClient("Red", "Chọn phiếu để hủy");
                                    }
                                }, function() {

                                })
                            } else {
                                notifiToClient("Red", "Chỉ Admin mới có quyền hủy!");
                            }
                        }
                        if(key === "huykyso") {
                            huykysoFilesign769("PHIEUKQ_XETNGHIEM",
                                rowData.SO_PHIEU, singletonObject.userId, singletonObject.dvtt,
                                rowData.NOITRU == "0" ? rowData.SOVAOVIEN : rowData.SOVAOVIEN_NOI,
                                rowData.SOVAOVIEN_DT_NOI, -1, function(data) {
                                    luuLogHSBATheoBN({
                                        SOVAOVIEN: rowData.NOITRU == "0" ? rowData.SOVAOVIEN : rowData.SOVAOVIEN_NOI,
                                        LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                                        NOIDUNGBANDAU: "",
                                        NOIDUNGMOI: "Huỷ ký số phiếu kết quả xét nghiệm " + rowData.SO_PHIEU,
                                        USERID: singletonObject.userId,
                                        ACTION: LOGHSBAACTION.EDIT.KEY,
                                        NGOAI: rowData.NOITRU == "0" ? 1 : 0
                                    });
                                    $("#xn_lammoi").click();
                                })
                        }
                        if (key == "goiso") {
                            var chuoi = singletonObject.phongDuocSet + "|" + rowData.STT_HANGNGAY.toString().replace('<span class="cellWithoutBackground" style="background-color:white;color:black">', '').replace('</span>', '')+ "|" + rowData.TENBENHNHAN + "|" + "0" + "|" + "XETNGHIEM" + "|" + "0";
                            saveTextAsFile(chuoi);
                        }
                        if (key === "dalaymau") {
                            var arr = [singletonObject.dvtt, rowData.NOITRU, rowData.SO_PHIEU, rowData.MA_KHAM_BENH, rowData.STT_BENHAN, rowData.STT_DOTDIEUTRI, rowData.STT_DIEUTRI, 0];
                            var url = "capnhat_trangthai_dalaymau?url=" + convertArray(arr);
                            $.ajax({
                                url: url
                            }).always(function () {
                                var soVaoVien = rowData.NOITRU == "0" ? rowData.SOVAOVIEN : rowData.SOVAOVIEN_NOI;
                                var trangThaiLayMau = rowData.DALAYMAU == "No" ? "Đã lấy mẫu" : "Chưa lấy mẫu";
                                luuLogHSBATheoBN({
                                    SOVAOVIEN: soVaoVien,
                                    LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                                    NOIDUNGBANDAU: "",
                                    NOIDUNGMOI: "Cập nhật trạng thái lấy mẫu phiếu  " + rowData.SO_PHIEU + ": " + trangThaiLayMau,
                                    USERID: singletonObject.userId,
                                    ACTION: LOGHSBAACTION.EDIT.KEY,
                                    NGOAI: rowData.NOITRU == "0" ? 1 : 0
                                });
                                $("#xn_lammoi").click();
                            });
                        }
                        if (key == "inbarcode") {
                            var arr = [rowData.SID, rowData.TENBENHNHAN, rowData.NAM_SINH, 1];
                            var param = ['stt','ten_benh_nhan','ngay_sinh','so_luong'];
                            var url = "cmu_injasper?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf&jasper=phieumavach_cmu";
                            var xhr = new XMLHttpRequest();
                            xhr.open("GET", url, true);
                            xhr.responseType = "blob";

                            xhr.onload = function (e) {
                                if (xhr.readyState === xhr.DONE) {
                                    if (this.status === 200) {
                                        var file = window.URL.createObjectURL(this.response);
                                        var iframe = document.createElement('iframe');
                                        iframe.style.display = 'none';
                                        iframe.src = file;
                                        document.body.appendChild(iframe);
                                        iframe.onload = function () {
                                            iframe.contentWindow.print();
                                            URL.revokeObjectURL(file);
                                        };
                                    }
                                }
                            };

// Send the request to fetch the PDF
                            xhr.send();
                        }
                    },
                    items: items
                });
            }
        },
        ondblClickRow: function (id) {
            if (singletonObject.khoaKham == "0") {
                var rowData = getThongtinRowSelected("list_benhnhan");
                getThongTinBenhNhan(rowData);
                listXNSelected = [];
                allDataXN = [];
            }
        }
    });
    $("#list_benhnhan").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});


    $("#list_xn_bhyt").jqGrid({
        datatype: "local",
        loadonce: false,
        height: 400,
        width: null,
        shrinkToFit: false,
        colModel: [
            {label: "DA_THANH_TOAN", name: 'DA_THANH_TOAN', index: 'DA_THANH_TOAN', hidden: true},
            {label: "Mã", name: 'MA_XETNGHIEM', index: 'MA_XETNGHIEM', align: "center", width: 80},
            {label: "TEN_XETNGHIEM", name: 'TEN_XETNGHIEM', index: 'TEN_XETNGHIEM', hidden: true},
            {label: "Yêu cầu chẩn đoán", name: 'TEN_XETNGHIEM_HT', index: 'TEN_XETNGHIEM_HT', width: 350,
                formatter: function (cellvalue, options, rowObject) {
                    var color;
                    var color_text;
                    if (rowObject.DA_THANH_TOAN === 0) {
                        color = 'yellow';
                        color_text = 'red';
                    } else {
                        color = 'white';
                        color_text = 'black';
                    }
                    return '<span class="cellWithoutBackground" style="background-color:' + color + ';color:' + color_text + '">' + (rowObject.TEN_XETNGHIEM== null ? "" : rowObject.TEN_XETNGHIEM) + '</span>';
                }
            },
            {label: "Chỉ số min", name: 'CHISO_MIN_XETNGHIEM', index: 'CHISO_MIN_XETNGHIEM', width: 100, hidden: true},
            {label: "Chỉ số max", name: 'CHISO_MAX_XETNGHIEM', index: 'CHISO_MAX_XETNGHIEM', width: 100, hidden: true},
            {label: "Chỉ số bình thường", name: 'CHI_SO_BINH_THUONG', index: 'CHI_SO_BINH_THUONG', width: 200,
                formatter: function (cellvalue, options, rowObject) {
                    var arr = [];
                    if (rowObject.CHISO_MIN_XETNGHIEM) {
                        arr.push(rowObject.CHISO_MIN_XETNGHIEM ? rowObject.CHISO_MIN_XETNGHIEM.trim() : "");
                    }
                    if (rowObject.CHISO_MAX_XETNGHIEM) {
                        arr.push(rowObject.CHISO_MAX_XETNGHIEM ? rowObject.CHISO_MAX_XETNGHIEM.trim() : "");
                    }
                    return arr.join(" - ");
                }
            },
            {label: "Kết quả", name: 'KET_QUA', index: 'KET_QUA', width: 100, editable: true, edittype: 'text',
                cellattr: function (rowId, cellValue, rowObject) {
                    return "style='text-align:center; font-weight: bold; color:"+(rowObject.CANHBAO  == 0? 'red': 'black')+"'";
                },
                editoptions: {
                    dataInit: function (elem) {
                        $(elem).focus(function () {
                            this.select();
                        });
                    },
                    dataEvents: [
                        {
                            type: 'keydown',
                            fn: function (e) {
                                var key = e.charCode || e.keyCode;
                                if (key === 13)
                                {
                                    var grid = $('#list_xn_bhyt');
                                    grid.jqGrid('saveCell', selIRow, 8);
                                    var count = grid.jqGrid('getDataIDs').length;
                                    if (selfRowId >= count) return;
                                    if (selfRowId <= grid.getDataIDs().length) {
                                        var nextRowid = parseInt(selfRowId) + 1;
                                        var rowIndex = grid.getInd(nextRowid);
                                        setTimeout(function () {
                                            $("#list_xn_bhyt").editCell(rowIndex, 8, true);
                                        }, 50);
                                    }

                                }
                            }
                        }
                    ]
                }
            },
            {label: "Tiền sử", name: 'TIENSU', index: 'TIENSU', width: 100},
            {label: "Bình thường", name: 'BINHTHUONG', index: 'BINHTHUONG', width: 100, editable: true, edittype: 'text',
                editoptions: {
                    dataInit: function (elem) {
                        $(elem).focus(function () {
                            this.select();
                        });
                    },
                    dataEvents: [
                        {
                            type: 'keydown',
                            fn: function (e) {
                                var key = e.charCode || e.keyCode;
                                if (key === 13)
                                {
                                    var grid = $('#list_xn_bhyt');
                                    grid.jqGrid('saveCell', selIRow, 10);
                                    var count = grid.jqGrid('getDataIDs').length;
                                    if (selfRowId >= count) return;
                                    if (selfRowId <= grid.getDataIDs().length) {
                                        var nextRowid = parseInt(selfRowId) + 1;
                                        var rowIndex = grid.getInd(nextRowid);
                                        setTimeout(function () {
                                            $("#list_xn_bhyt").editCell(rowIndex, 10, true);
                                        }, 50);
                                    }
                                }
                            }
                        }
                    ]
                }
            },
            {label: "Máy", name: "STT_MAMAY_HT", index: 'STT_MAMAY_HT', width: 150, editable: true, edittype: "select",
                formatter: function (cellvalue, options, rowObject) {
                    var value;
                    if (!cellvalue) {
                        if (rowObject.STT_MAMAY) {
                            var mayObject = singletonObject.danhsachmayxn.find(x => x.STT_MAY_4210 == rowObject.STT_MAMAY);
                            value = mayObject? mayObject.TENMAY: ""
                        }
                    } else {
                        value = cellvalue;
                    }

                    return value ? value : "";
                },
                editoptions: {

                }
            },
            {label: "TRANGTHAI_BHYT", name: 'TRANGTHAI_BHYT', index: 'TRANGTHAI_BHYT', hidden: true},
            {label: "MABAOCAO", name: 'MABAOCAO', index: 'MABAOCAO', hidden: true},
            {label: "DVT_XETNGHIEM", name: 'DVT_XETNGHIEM', index: 'DVT_XETNGHIEM', hidden: true},
            {label: "CO_KETQUA", name: 'CO_KETQUA', index: 'CO_KETQUA', hidden: true},
            {label: "DA_XET_NGHIEM", name: 'DA_XET_NGHIEM', index: 'DA_XET_NGHIEM', hidden: true},
            {label: "NGUOI_THUC_HIEN", name: 'NGUOI_THUC_HIEN', index: 'NGUOI_THUC_HIEN', hidden: true},
            {label: "STT_MAMAY", name: 'STT_MAMAY', index: 'STT_MAMAY', hidden: true},
            {label: "ID_XNCHA", name: 'ID_XNCHA', index: 'ID_XNCHA', hidden: true},
            {label: "KETQUA_MACDINH", name: 'KETQUA_MACDINH', index: 'KETQUA_MACDINH', hidden: true},
            {label: "CSBTNAM", name: 'CSBTNAM', index: 'CSBTNAM', hidden: true},
            {label: "CDNAM", name: 'CDNAM', index: 'CDNAM', hidden: true},
            {label: "CTNAM", name: 'CTNAM', index: 'CTNAM', hidden: true},
            {label: "CSBTNU", name: 'CSBTNU', index: 'CSBTNU', hidden: true},
            {label: "CDNU", name: 'CDNU', index: 'CDNU', hidden: true},
            {label: "CTNU", name: 'CTNU', index: 'CTNU', hidden: true},
            {label: "KHONGMAYTHUCHIEN", name: 'KHONGMAYTHUCHIEN', index: 'KHONGMAYTHUCHIEN', hidden: true},
            {label: "Thời gian TH", name: 'NGAY_TH_YL', index: 'NGAY_TH_YL', width: 100,
                cellattr: function (rowId, tv, rawObject, cm, rdata) {
                    return 'style="white-space: normal;"';
                }
            },
            {label: "Thời gian KQ ", name: 'NGAY_THUC_HIEN', index: 'NGAY_THUC_HIEN', width: 100,
                cellattr: function (rowId, tv, rawObject, cm, rdata) {
                    return 'style="white-space: normal;"';
                }
            },
            {label: "MA_ICD", name: 'MA_ICD', index: 'MA_ICD', hidden: true},
            {label: "TEN_ICD", name: 'TEN_ICD', index: 'TEN_ICD', hidden: true},
            {label: "MA_BENH_LY_THEO_ICD", name: 'MA_BENH_LY_THEO_ICD', index: 'MA_BENH_LY_THEO_ICD', hidden: true},
            {label: "TEN_LOAI_XETNGHIEM", name: 'TEN_LOAI_XETNGHIEM', index: 'TEN_LOAI_XETNGHIEM', hidden: true},
        ],
        caption: "Yêu cầu xét nghiệm",
        cellEdit: true,
        cellsubmit: 'clientArray',
        rowNum: 1000000,
        multiselect: true,
        multiSort: true,
        sortname: 'TEN_LOAI_XETNGHIEM',
        grouping: true,
        groupingView: {
            groupField: ['TEN_LOAI_XETNGHIEM'],
            groupColumnShow: [false],
            groupText: ['<input type="checkbox" class="list_m_kq_mainlist_groupcheckbox" data-cr-temp="{0}"> <b>{0}</b>'],
            groupCollapse: false,
            groupDataSorted: false,
            groupOrder: ['asc'],
        },
        beforeRequest: function () {
            showLoaderIntoWrapId("list_xetnghiem_wrap");
        },
        loadComplete: function () {
            hideLoaderIntoWrapId("list_xetnghiem_wrap");
            var str = $("#list_xn_bhyt").jqGrid('getDataIDs');

            for (var i = 0; i < str.length; i++) {
                var ret = $("#list_xn_bhyt").jqGrid('getRowData', str[i]);
                allDataXN.push(ret)
                if (listXNSelected.indexOf(ret.MA_XETNGHIEM) != -1) {
                    $('#list_xn_bhyt').jqGrid('setSelection', str[i]);
                }

            }
        },
        afterInsertRow: function(rowid, aData) {
        },
        gridComplete: function () {
            var str = $("#list_xn_bhyt").jqGrid('getDataIDs');
            if (str != "") {
                for (var i = 0; i < str.length; i++) {
                    var ret = $("#list_xn_bhyt").jqGrid('getRowData', str[i]);
                    if (ret.DA_XET_NGHIEM.toString() == "true")
                        $('#list_xn_bhyt').jqGrid('setSelection', str[i]);
                }
            }
            if (singletonObject.clsVuotNguong == "1") {
                var rows = $("#list_xn_bhyt").jqGrid('getDataIDs');
                var kq, csmin, csmax;
                for (i = 0; i < rows.length; i++) {
                    kq = parseFloat($("#list_xn_bhyt").jqGrid('getRowData', rows[i]).KET_QUA.replace(/[><= ]/g, ''));
                    var min = $("#list_xn_bhyt").jqGrid('getRowData', rows[i]).CHISO_MIN_XETNGHIEM;
                    var pos1 = min.indexOf(">",0);
                    var pos2 = min.indexOf("<",1);
                    min = min.substring(pos1+1,pos2);
                    csmin = parseFloat(min);
                    var max = $("#list_xn_bhyt").jqGrid('getRowData', rows[i]).CHISO_MAX_XETNGHIEM;
                    pos1 = max.indexOf(">",0);
                    pos2 = max.indexOf("<",1);
                    max = max.substring(pos1+1,pos2);
                    csmax = parseFloat(max);
                    if (!isNaN(csmin) && kq < csmin) {
                        $("#list_xn_bhyt").setCell(rows[i], 'KET_QUA', '', {'background-color': '#FFA500'});
                    }else if (!isNaN(csmin) && kq > csmin && kq < csmax){

                    }
                    if (!isNaN(csmax) && kq > csmax) {
                        $("#list_xn_bhyt").setCell(rows[i], 'KET_QUA', '', {'background-color': '#FF0000'});
                    }else if (!isNaN(csmin) && kq > csmin && kq < csmax){

                    }
                }
            }
            var firstRowId = $("#list_xn_bhyt").jqGrid('getDataIDs')[0];
            if (firstRowId) {
                $("#list_xn_bhyt").jqGrid('setSelection', firstRowId, true);
                $("#list_xn_bhyt").jqGrid('resetSelection');
            }
        },
        beforeSaveCell: function (rowid, name, val, iRow, iCol) {
            var ret = $("#list_xn_bhyt").jqGrid("getRowData", rowid);
            var retBN = getThongtinRowSelected("list_benhnhan");
            var dataForm = formKetQuaXetNghiem.submission.data;
            rowDataXNSelected = allDataXN.filter(function(item) {
                return item.MA_XETNGHIEM === ret.MA_XETNGHIEM;
            })[0]

            if (iCol == 8) {
                var oldValue = rowDataXNSelected.KET_QUA;
                var tenNguoiThucHien = singletonObject.user;
                var arr = [retBN.SO_PHIEU, ret.MA_XETNGHIEM, val.replace(/\+/g, "%2B"), singletonObject.dvtt, retBN.NOITRU, retBN.MA_KHAM_BENH,
                    retBN.STT_BENHAN, retBN.STT_DOTDIEUTRI, retBN.STT_DIEUTRI, "0", retBN.SOVAOVIEN, retBN.SOVAOVIEN_NOI, retBN.SOVAOVIEN_DT_NOI, tenNguoiThucHien];
                var url = "xetnghiem_update_ketqua_chitiet_svv";
                if (singletonObject.batBuocChonNguoiDoc == '1') {
                    var nguoiThucHien = dataForm.MA_BS_DOC_KQ;
                    tenNguoiThucHien = getTextSelectedFormio(formKetQuaXetNghiem.getComponent('MA_BS_DOC_KQ'));
                    var ngayTraKetQua = moment(dataForm.NGAY_TRA_KET_QUA_FORM).format("YYYY-MM-DD HH:mm:ss");
                    arr = [retBN.SO_PHIEU, singletonObject.dvtt, ret.MA_XETNGHIEM, val.replace(/\+/g, "%2B"), retBN.NOITRU, retBN.MA_KHAM_BENH,
                        retBN.STT_BENHAN, retBN.STT_DOTDIEUTRI, retBN.STT_DIEUTRI, "0", retBN.SOVAOVIEN, retBN.SOVAOVIEN_NOI, retBN.SOVAOVIEN_DT_NOI,
                        nguoiThucHien, tenNguoiThucHien, ngayTraKetQua];
                    url = "xetnghiem_update_ketqua_chitiet_cmu";
                }
                var checkCMU = retBN.NGUOI_THUC_HIEN != "" ? retBN.NGUOI_THUC_HIEN : retBN.KY_THUAT_VIEN;
                formKetQuaXetNghiem.emit("checkValidity");
                if (!formKetQuaXetNghiem.checkValidity(null, false, null, true)) {
                    loadGridXetNghiem();
                    notifiToClient("Red", "Vui lòng nhập đầy đủ thông tin");
                    return oldValue;
                }
                if (singletonObject.batBuocChonNguoiDoc == '1' && $("#xn_trangthai").val() == 1 && checkCMU != "" && checkCMU != singletonObject.userId) {
                    loadGridXetNghiem();
                    notifiToClient("Red", "Bạn không thể chỉnh sửa KQ xét nghiệm của nhân viên khác!");
                    return oldValue
                } else if (singletonObject.batBuocChonNguoiDoc == '0' && dataForm.KY_THUAT_VIEN && dataForm.KY_THUAT_VIEN != singletonObject.userId && $("#xn_trangthai").val() == 1) {
                    loadGridXetNghiem();
                    notifiToClient("Red", "Bạn không thể chỉnh sửa KQ xét nghiệm của nhân viên khác!");
                    return oldValue;
                } else {
                    $.post(url, {
                        url: convertArray(arr)
                    }).done(function (data) {
                        if(data == -1) {
                            loadGridXetNghiem();
                            notifiToClient("Red", "Bệnh nhân đã khóa số liệu không thể chỉnh sửa, vui lòng liên hệ admin");
                            return oldValue;
                        }
                        allDataXN = allDataXN.map(function(item) {
                            if (item.MA_XETNGHIEM == ret.MA_XETNGHIEM) {
                                item.KET_QUA = val;
                            }
                            return item;
                        })
                        var soVaoVien = retBN.NOITRU == "0" ? retBN.SOVAOVIEN : retBN.SOVAOVIEN_NOI;
                        var noiDungChung = "Số phiếu: " + retBN.SO_PHIEU + "; Xét nghiệm: " + ret.MA_XETNGHIEM + " - " + ret.TEN_XETNGHIEM + "; Kết quả: ";
                        var noiDungBanDau = noiDungChung + (oldValue ? oldValue : "");
                        var noiDungMoi = noiDungChung + (val == null ? "" : val);
                        luuLogHSBATheoBN({
                            SOVAOVIEN: soVaoVien,
                            LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                            NOIDUNGBANDAU: noiDungBanDau,
                            NOIDUNGMOI: noiDungMoi,
                            USERID: singletonObject.userId,
                            ACTION: LOGHSBAACTION.EDIT.KEY,
                            NGOAI: retBN.NOITRU == "0" ? 1 : 0
                        });
                        rowDataXNSelected = null;
                        if (singletonObject.clsVuotNguong == "1") {
                            var ketqua = parseFloat(ret.KET_QUA.replace(/[><= ]/g, ''));
                            var pos1 = ret.CHISO_MIN_XETNGHIEM.indexOf(">",0);
                            var pos2 = ret.CHISO_MIN_XETNGHIEM.indexOf("<",1);
                            var min = ret.CHISO_MIN_XETNGHIEM.substring(pos1+1,pos2)
                            var csmin = parseFloat(min);
                            pos1 = ret.CHISO_MAX_XETNGHIEM.indexOf(">",0);
                            pos2 = ret.CHISO_MAX_XETNGHIEM.indexOf("<",1);
                            var max = ret.CHISO_MAX_XETNGHIEM.substring(pos1+1,pos2)
                            var csmax = parseFloat(max);
                            if(ret.KETQUA_MACDINH === undefined || ret.KETQUA_MACDINH === null || ret.KETQUA_MACDINH.trim() === ""){
                                if((!ret.CSBTNAM) && (!ret.CSBTNU)){
                                    $("#list_xn_bhyt").setCell(rowid, 'KET_QUA', '', {'background-color': '#FFFFFF'});
                                    if (!isNaN(csmin) && ketqua < csmin) {
                                        $("#list_xn_bhyt").setCell(rowid, 'KET_QUA', '', {'background-color': '#FFA500'});
                                        $("#list_xn_bhyt").jqGrid('setCell', rowid, 'BINHTHUONG', 0);
                                    }else if (!isNaN(csmin) && ketqua > csmin && ketqua < csmax){
                                        $("#list_xn_bhyt").jqGrid('setCell', rowid, 'BINHTHUONG', 1);
                                    }
                                    if (!isNaN(csmax) && ketqua > csmax) {
                                        $("#list_xn_bhyt").setCell(rowid, 'KET_QUA', '', {'background-color': '#FF0000'});
                                        $("#list_xn_bhyt").jqGrid('setCell', rowid, 'BINHTHUONG', 0);
                                    }else if (!isNaN(csmin) && ketqua > csmin && ketqua < csmax){
                                        $("#list_xn_bhyt").jqGrid('setCell', rowid, 'BINHTHUONG', 1);
                                    }
                                } else {
                                    $("#list_xn_bhyt").setCell(rowid, 'KET_QUA', '', {'background-color': '#FFFFFF'});
                                    var gioitinh = dataForm.GIOITINH == "true" ? 1 : 0;
                                    if((gioitinh == 1 && ret.CSBTNAM == 1 && ketqua >= ret.CDNAM && ketqua <= ret.CTNAM) ||
                                        (gioitinh == 0 && ret.CSBTNU == 1 && ketqua >= ret.CDNU && ketqua <= ret.CTNU)){
                                        $("#list_xn_bhyt").jqGrid('setCell', rowid, 'BINHTHUONG', 1);
                                    } else if ((gioitinh == 1 && ret.CSBTNAM == 1 && ketqua < ret.CDNAM) ||
                                        (gioitinh == 0 && ret.CSBTNU == 1 && ketqua < ret.CDNU)){
                                        $("#list_xn_bhyt").setCell(rowid, 'KET_QUA', '', {'background-color': '#FFA500'});
                                        $("#list_xn_bhyt").jqGrid('setCell', rowid, 'BINHTHUONG', 0);
                                    } else if ((gioitinh == 1 && ret.CSBTNAM == 1 && ketqua > ret.CTNAM) ||
                                        (gioitinh == 0 && ret.CSBTNU == 1 && ketqua > ret.CTNU)){
                                        $("#list_xn_bhyt").setCell(rowid, 'KET_QUA', '', {'background-color': '#FF0000'});
                                        $("#list_xn_bhyt").jqGrid('setCell', rowid, 'BINHTHUONG', 0);
                                    }
                                }
                            } else {
                                if(ketqua == ret.KETQUA_MACDINH){
                                    $("#list_xn_bhyt").setCell(rowid, 'KET_QUA', '', {'background-color': '#FFA500'});
                                    $("#list_xn_bhyt").jqGrid('setCell', rowid, 'BINHTHUONG', 1);
                                } else {
                                    $("#list_xn_bhyt").setCell(rowid, 'KET_QUA', '', {'background-color': '#FFA500'});
                                    $("#list_xn_bhyt").jqGrid('setCell', rowid, 'BINHTHUONG', 0);
                                }
                            }
                        }
                        return val;
                    });
                }
            }
            if (iCol == 10) {
                var oldValue = rowDataXNSelected.BINHTHUONG;
                var tenNguoiThucHien = singletonObject.user;
                if (singletonObject.batBuocChonNguoiDoc == '1')
                    tenNguoiThucHien = getTextSelectedFormio(formKetQuaXetNghiem.getComponent('MA_BS_DOC_KQ'));

                console.log(tenNguoiThucHien)
                var arr = [retBN.SO_PHIEU, ret.MA_XETNGHIEM, val, singletonObject.dvtt, retBN.NOITRU, retBN.MA_KHAM_BENH,
                    retBN.STT_BENHAN, retBN.STT_DOTDIEUTRI, retBN.STT_DIEUTRI, "0", retBN.SOVAOVIEN, retBN.SOVAOVIEN_NOI, retBN.SOVAOVIEN_DT_NOI, tenNguoiThucHien];
                var url = "xetnghiem_update_ketqua_chitiet_svv_vlg";
                var checkCMU = retBN.NGUOI_THUC_HIEN != "" ? retBN.NGUOI_THUC_HIEN : retBN.KY_THUAT_VIEN;
                formKetQuaXetNghiem.emit("checkValidity");
                if (!formKetQuaXetNghiem.checkValidity(null, false, null, true)) {
                    loadGridXetNghiem();
                    notifiToClient("Red", "Vui lòng nhập đầy đủ thông tin");
                    return oldValue;
                }
                if (singletonObject.batBuocChonNguoiDoc == '1' && $("#xn_trangthai").val() == 1 && checkCMU != "" && checkCMU != singletonObject.userId) {
                    loadGridXetNghiem();
                    notifiToClient("Red", "Bạn không thể chỉnh sửa KQ xét nghiệm của nhân viên khác!");
                    return oldValue;
                } else if (singletonObject.batBuocChonNguoiDoc == '0' && dataForm.KY_THUAT_VIEN && dataForm.KY_THUAT_VIEN != singletonObject.userId && $("#xn_trangthai").val() == 1) {
                    loadGridXetNghiem();
                    notifiToClient("Red", "Bạn không thể chỉnh sửa KQ xét nghiệm của nhân viên khác!");
                    return oldValue;
                } else {
                    $.post(url, {
                        url: convertArray(arr)
                    }).done(function () {
                        var soVaoVien = retBN.NOITRU == "0" ? retBN.SOVAOVIEN : retBN.SOVAOVIEN_NOI;
                        var noiDungChung = "Số phiếu: " + retBN.SO_PHIEU + "; Xét nghiệm: " + ret.MA_XETNGHIEM + " - " + ret.TEN_XETNGHIEM + "; Bình thường: ";
                        var noiDungBanDau = noiDungChung + (oldValue ? oldValue : "");
                        var noiDungMoi = noiDungChung + (val == null ? "" : val);
                        luuLogHSBATheoBN({
                            SOVAOVIEN: soVaoVien,
                            LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                            NOIDUNGBANDAU: noiDungBanDau,
                            NOIDUNGMOI: noiDungMoi,
                            USERID: singletonObject.userId,
                            ACTION: LOGHSBAACTION.EDIT.KEY,
                            NGOAI: retBN.NOITRU == "0" ? 1 : 0
                        });
                        rowDataXNSelected = null;
                        return val;
                    });
                }
            }
            if (name == 'STT_MAMAY_HT') {
                if (val) {
                    var oldValue = rowDataXNSelected.STT_MAMAY;
                    var rowData = $("#list_xn_bhyt").jqGrid('getRowData', rowid);
                    $.post('luu_mamay_theo_sophieu', {
                        sophieu: dataForm.SO_PHIEU,
                        noitru: dataForm.NOITRU,
                        ma_xn: rowData.MA_XETNGHIEM + ',',
                        sovaovien: dataForm.SOVAOVIEN,
                        sovaovien_noi: dataForm.SOVAOVIEN_NOI,
                        sovaovien_dt_noi: dataForm.SOVAOVIEN_DT_NOI,
                        stt_may: val
                    }).done(function () {
                        var soVaoVien = retBN.NOITRU == "0" ? retBN.SOVAOVIEN : retBN.SOVAOVIEN_NOI;
                        var noiDungChung = "Số phiếu: " + retBN.SO_PHIEU + "; Xét nghiệm: " + ret.MA_XETNGHIEM + " - " + ret.TEN_XETNGHIEM + "; Máy xét nghiệm: ";
                        var tenMayOldValude = oldValue ? singletonObject.danhsachmayxn.find(x => x.STT_MAY_4210 == oldValue).TENMAY : "";
                        var noiDungBanDau = noiDungChung + tenMayOldValude;
                        val = val == 0 ? "" : val;
                        var tenMayNewValue = val ? singletonObject.danhsachmayxn.find(x => x.STT_MAY_4210 == val).TENMAY : "";
                        var noiDungMoi = noiDungChung + tenMayNewValue;
                        luuLogHSBATheoBN({
                            SOVAOVIEN: soVaoVien,
                            LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                            NOIDUNGBANDAU: noiDungBanDau,
                            NOIDUNGMOI: noiDungMoi,
                            USERID: singletonObject.userId,
                            ACTION: LOGHSBAACTION.EDIT.KEY,
                            NGOAI: retBN.NOITRU == "0" ? 1 : 0
                        });
                        rowDataXNSelected = null;
                        loadGridXetNghiem();
                    }).fail(function () {
                        loadGridXetNghiem();
                        notifiToClient("Red", "Lỗi lưu mã máy");
                    });
                }
                $("#list_xn_bhyt").setCell(rowid, 'STT_MAMAY', val);
                return " ";
            }
        },
        afterSaveCell: function (rowid, name, val, iRow, iCol) {

        },
        afterEditCell: function (rowid, cellname, value, iRow, iCol) {
            selIRow = iRow;
            selfRowId = rowid;
        },
        beforeEditCell: function (rowid, cellname, value, iRow, iCol) {
            var ret = getThongtinRowSelected("list_xn_bhyt");
            var retBN = getThongtinRowSelected("list_benhnhan");
            var dataForm = formKetQuaXetNghiem.submission.data;
            if (dataForm.KEYSIGN){
                notifiToClient("Red", "Phiếu đã ký không thể chỉnh sửa");
                loadGridXetNghiem();
                return false;
            }

            rowDataXNSelected = { ...ret };
            console.log("rowDataXNSelected", rowDataXNSelected)
        },
        onRightClickRow: function (id1) {

        }
    });
    $("#list_xn_bhyt").jqGrid('bindKeys');
    $("body").on("click", "#list_xn_bhyt .cbox", function(){
        console.log('a')
        var ID_XNCHA= $(this).parent().parent().find("td[aria-describedby=\"list_xn_bhyt_ID_XNCHA\"]").attr("title")
        var ID_XN= $(this).parent().parent().find("td[aria-describedby=\"list_xn_bhyt_MA_XETNGHIEM\"]").attr("title")
        var check = $(this).prop("checked");
        if(ID_XNCHA == '') {
            $("#list_xn_bhyt tr").each(function(){
                var xncha = $(this).find("td[aria-describedby=\"list_xn_bhyt_ID_XNCHA\"]").attr("title")
                if(xncha == ID_XN) {
                    $(this).find(".cbox").prop('checked', check)
                }
            })
        } else {
            $("#list_xn_bhyt tr").each(function(){
                var xncha = $(this).find("td[aria-describedby=\"list_xn_bhyt_ID_XNCHA\"]").attr("title")
                var maxn = $(this).find("td[aria-describedby=\"list_xn_bhyt_MA_XETNGHIEM\"]").attr("title")
                if(xncha == ID_XNCHA) {
                    $(this).find(".cbox").prop('checked', check)
                }
                if(maxn == ID_XNCHA) {
                    $(this).find(".cbox").prop('checked', check)
                }
            })
        }
    });
    $("#list_xn_bhyt tbody").on("change", "input[type=checkbox]", function (e) {
        var currentCB = $(this);
        var grid = $("#list_xn_bhyt");
        var isChecked = this.checked;
        if (currentCB.hasClass('list_m_kq_mainlist_groupcheckbox')) {
            var checkboxes = currentCB.closest('tr').nextUntil('tr.list_xn_bhytghead_0').find('.cbox[type="checkbox"]');
            checkboxes.each(function () {
                if (this.checked !== isChecked)
                    grid.setSelection($(this).closest('tr').attr('id'), true);
            });
        } else {
            var allCbs = currentCB.closest('tr').prevAll("tr.list_xn_bhytghead_0:first").nextUntil('tr.list_xn_bhytghead_0').andSelf().find('[type="checkbox"]');
            var allSlaves = allCbs.filter('.cbox');
            var headerCB = allCbs.filter(".list_m_kq_mainlist_groupcheckbox");
            var allChecked = !isChecked ? false : allSlaves.filter(":checked").length === allSlaves.length;
            headerCB.prop("checked", allChecked);
            if (currentCB.hasClass('cbox')) {
                var checked = $(currentCB).prop('checked');
                var tr = currentCB.closest('tr.jqgrow[role=row]');
                var rowid = $(tr).attr('id');
                if (rowid) {
                    var rowObject = $("#list_xn_bhyt").jqGrid('getRowData', rowid);
                    var capxn = rowObject.CAP_XN;
                    if (capxn == 1) {
                        // mkq_checkAllChildrenOf(rowObject.MA_XETNGHIEM, checked);
                    }
                }
            }
        }
    });
    $("#cb_list_xn_bhyt").change(function () {
        var isChecked = this.checked;
        var allHeaders = $("#list_xn_bhyt .list_m_kq_mainlist_groupcheckbox");
        allHeaders.each(function () {
            $(this).prop('checked', isChecked).trigger('change');
        });
    });

    $("#list_ketquamay").jqGrid({
        datatype: "local",
        loadonce: true,
        height: 400,
        width: null,
        shrinkToFit: false,
        colModel: [
            {label: "Máy", name: 'MAYXN', index: 'MAYXN', width: 200},
            {label: "SID", name: 'STT', index: 'STT', width: 120},
            {label: "Chỉ số", name: 'MA_XN_MAY', index: 'MAXN_MAY', width: 120},
            {label: "Kết quả", name: 'KETQUA', index: 'KETQUA', width: 300}
        ],
        caption: "Danh sách kết quả",
        rowNum: 1000,
        onRightClickRow: function (id1) {
            if (id1) {
                var rowData = getThongtinRowSelected("list_ketquamay");
                $.contextMenu('destroy', '#list_ketquamay tr');
                var items = {
                    "layketqua": {name: '<p><i class="fa fa-arrow-circle-down text-success" aria-hidden="true"></i> Lấy kết quả</p>'},
                };
                $.contextMenu({
                    selector: '#list_ketquamay tr',
                    callback: function (key, options) {
                        if(key === "layketqua"){
                            layKetQuaXetNghiem(rowData.STT, $("#xn_mayketquamay").val(), function(){
                                $("#modalDanhSachKetQuaMay").modal("hide");
                            }, function() {

                            });
                        }
                    },
                    items: items
                });
            }
        }
    });
    $("#list_ketquamay").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});

    setTimeout(function() {
        initDataNotAsync();
        initDataAsync();
    });

    $("#xn_khoa").change(function() {
        $.get("cmu_list_KB_NOT_ds_phongkhamnoitru?url="+convertArray([$("#xn_khoa").val()])).done(function(data) {
            $("#xn_phong").html("")
            initSelect2IfnotIntance('xn_phong', data, 'MA_PHONG_BENH', 'TEN_PHONG_BENH', 0, 1, "", 1);
        })
    });

    $("#xn_lammoi").click(function() {
        loadGridBenhNhan();
    });
    $("#xn_trangthai").change(function() {
        loadGridBenhNhan();
    });

    $("#hsba_tab0_header").click(function() {
        $("#xn_lammoi").click();
    });

    $("#xn_luuthongtin").click(function (evt) {
        formKetQuaXetNghiem.emit("checkValidity");
        if (!formKetQuaXetNghiem.checkValidity(null, false, null, true)) {
            return notifiToClient("Red", "Vui lòng nhập đầy đủ thông tin");
        }
        var retBN = getThongtinRowSelected("list_benhnhan");
        var dataForm = formKetQuaXetNghiem.submission.data;
        var checkcmu = retBN.NGUOI_THUC_HIEN != "" ? retBN.NGUOI_THUC_HIEN : retBN.KY_THUAT_VIEN;
        if (singletonObject.batBuocChonNguoiDoc == '1' && $("#xn_trangthai").val() == '1' && checkcmu != "" &&
            checkcmu != singletonObject.userId && singletonObject.admin == 0) {
            return notifiToClient("Red", "Bạn không thể chỉnh sửa KQ xét nghiệm của nhân viên khác!");
        }
        if(moment(dataForm.NGAY_TRA_KET_QUA_FORM).diff(moment(dataForm.NGAY_TH_YL_FORM), 'minutes') < 5) {
            return notifiToClient("Red", "Thời gian trả kết quả phải lớn hơn thời gian thực hiện y lệnh ít nhất 5 phút!");
        }
        if (singletonObject.batBuocChonNguoiDoc == '0' && dataForm.KY_THUAT_VIEN != "" && dataForm.KY_THUAT_VIEN != singletonObject.userId
                && $("#xn_trangthai").val() == '1') {
            return notifiToClient("Red", "Bạn không thể chỉnh sửa KQ xét nghiệm của nhân viên khác!");
        } else {
            showSelfLoading("xn_luuthongtin");
            var soVaoVien = retBN.NOITRU == 1 ? retBN.SOVAOVIEN_NOI : retBN.SOVAOVIEN;
            luuNguoiDocKetQua(retBN.SO_PHIEU, soVaoVien, retBN.SOVAOVIEN_DT_NOI, retBN.NOITRU);
            var sophieu = retBN.SO_PHIEU;
            var makhambenh = retBN.MA_KHAM_BENH;
            var dvtt = singletonObject.dvtt;
            var noitru = retBN.NOITRU;
            var sttbenhan = retBN.STT_BENHAN;
            var sttdotdieutri = retBN.STT_DOTDIEUTRI;
            var sttdieutri = retBN.STT_DIEUTRI;
            var ketluan = "";
            var nguoilaymau = "";
            var ngaygiolaymau = dataForm.NGAY_NHAN_MAU_FORM? moment(dataForm.NGAY_NHAN_MAU_FORM).format("YYYY-MM-DD HH:mm:ss"): "";
            var ngaygiotrakq = moment(dataForm.NGAY_TRA_KET_QUA_FORM).format("YYYY-MM-DD HH:mm:ss");
            var thoiGianBatDauCls = moment(dataForm.NGAY_TH_YL_FORM).format("DD/MM/YYYY HH:mm:ss");
            var nguoidoc = singletonObject.batBuocChonNguoiDoc == "1" && dataForm.MA_BS_DOC_KQ ? dataForm.MA_BS_DOC_KQ : singletonObject.userId;
            var maxncheck = '';
            listXNSelected = [];
            $("#list_xn_bhyt input:checked").each(function () {
                if($(this).parent().parent().hasClass('jqgrow')){
                    var maxnChecked = $(this).parent().parent().find('td[aria-describedby="list_xn_bhyt_MA_XETNGHIEM"]').html().trim();
                    maxncheck += maxnChecked + ',';
                    listXNSelected.push(maxnChecked);
                }
            })
            $.ajax({
                url: "nan_kiemtra_luuthongtin?url=" + convertArray([dvtt, noitru, sophieu, noitru == "0" ? makhambenh : " "])
            }).done(function (data) {
                if (data == "1") {
                    hideSelfLoading("xn_luuthongtin");
                    notifiToClient("Red", "Vui lòng nhập kết quả xét nghiệm trước khi lưu kết quả!!!");
                    return;
                } else {
                    //luu Log
                    var arrLogNew = [];
                    var arrLogOld = [];
                    var luuLogNew = formKetQuaXetNghiem.submission.data;
                    var diffLog = getLogHSBAChinhSua(luuLogOld, luuLogNew, keyLuuLogXetNghiem);
                    if (diffLog.length > 0) {
                        arrLogOld.push(diffLog[0]);
                        arrLogNew.push(diffLog[1]);
                    }
                    //end luu log

                    var flag  = true;
                    var string = "Xét nghiệm chưa có kết quả không thê lưu: ";
                    var selectedrow = [];
                    $("#list_xn_bhyt input:checked").each(function () {
                        if($(this).parent().parent().hasClass('jqgrow')){
                            selectedrow.push($(this).parent().parent().attr('id'));
                        }
                    })
                    selectedrow.forEach(function(value) {
                        var ret = $("#list_xn_bhyt").jqGrid ('getRowData', value)
                        var ketquacon = false;
                        if(ret.KET_QUA.trim() == "") {
                            selectedrow.forEach(function(_val) {
                                var _ret = $("#list_xn_bhyt").jqGrid ('getRowData', _val)
                                if(_ret.ID_XNCHA == ret.MA_XETNGHIEM && _ret.KET_QUA.trim() != "") {
                                    ketquacon = true;
                                }
                            })
                            if(ketquacon == false) {
                                if(ret.ID_XNCHA.trim() == "") {
                                    flag = false;
                                    string += ret.TEN_XETNGHIEM ;
                                }
                            }
                        }
                    });
                    if (singletonObject.batBuocChonNguoiDoc == "1" && flag == false) {
                        hideSelfLoading("xn_luuthongtin");
                        return notifiToClient("Red", string);
                    }
                    if (singletonObject.chanSuaKQKhiThanhToan == "1") {
                        $.ajax({
                            url: "hpg_xetnghiem_trangthai_thanhtoan?url=" + convertArray([makhambenh, sophieu, singletonObject.dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, 0])
                        }).done(function (data) {
                            if (data == "2") {
                                hideSelfLoading("xn_luuthongtin");
                                return notifiToClient("Red", "Bệnh nhân đã thanh toán viện phí. Không được thay đổi kết quả.");
                            } else {
                                $.post("xetnghiem_update_trangthai", {
                                    url: convertArray([dvtt, noitru, sophieu, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, ketluan, "1", nguoilaymau, ngaygiolaymau, singletonObject.userId, ngaygiotrakq, "0"]),
                                    thoiGianBatDauCls: thoiGianBatDauCls,
                                    maIcd:  "",
                                    tenIcd:  "",
                                    maBenhLy:  ""
                                }).done(function (datares) {
                                    if (datares == -1) {
                                        hideSelfLoading("xn_luuthongtin");
                                        notifiToClient("Red", "Dữ liệu bệnh nhân đã khóa không thể chỉnh sửa, vui lòng liên hệ admin.");
                                        return false;
                                    }
                                    if (singletonObject.batBuocChonNguoiDoc == "1") {
                                        var soVaoVien = dataForm.NOITRU == "0" ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI;
                                        $.ajax({
                                            url: "xetnghiem_update_trangthai_cmu?url=" + convertArray([dvtt, noitru, sophieu, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, ketluan, "1", nguoilaymau, ngaygiolaymau, nguoidoc, ngaygiotrakq, "0"])
                                        }).always(function(){
                                            if (singletonObject.apDungCheckBox == "1") {
                                                $.post('cmu_post', {
                                                    url: [singletonObject.dvtt, noitru, maxncheck, sophieu, ngaygiotrakq, soVaoVien, 'CMU_UPDATENGAY_THUHIEN'].join("```")
                                                }).always(function() {

                                                })
                                            }
                                            $.post("cmu_post", {
                                                url: [singletonObject.dvtt, sophieu, maxncheck, soVaoVien,
                                                    dataForm.KY_THUAT_VIEN ? dataForm.KY_THUAT_VIEN : singletonObject.userId,
                                                    "CMU_CAPNHAT_NGUOIXACNHAN"].join("```")
                                            }).then(function (data) {
                                                var soVaoVien = dataForm.NOITRU == "0" ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI;
                                                if (dataForm.TRANG_THAI_XET_NGHIEM == 1){
                                                    luuLogHSBATheoBN({
                                                        SOVAOVIEN: soVaoVien,
                                                        LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                                                        NOIDUNGBANDAU: arrLogOld.join("; "),
                                                        NOIDUNGMOI: arrLogNew.join("; "),
                                                        USERID: singletonObject.userId,
                                                        ACTION: LOGHSBAACTION.EDIT.KEY,
                                                        NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                                                    });
                                                } else {
                                                    luuLogHSBATheoBN({
                                                        SOVAOVIEN: soVaoVien,
                                                        LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                                                        NOIDUNGBANDAU: "",
                                                        NOIDUNGMOI: arrLogNew.join("; "),
                                                        USERID: singletonObject.userId,
                                                        ACTION: LOGHSBAACTION.INSERT.KEY,
                                                        NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                                                    });
                                                }
                                                getThongTinBenhNhan(retBN, false);
                                                notifiToClient("Green", "Cập nhật thành công");
                                                hideSelfLoading("xn_luuthongtin");
                                            }, function() {
                                                var soVaoVien = dataForm.NOITRU == "0" ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI;
                                                if (dataForm.TRANG_THAI_XET_NGHIEM == 1){
                                                    luuLogHSBATheoBN({
                                                        SOVAOVIEN: soVaoVien,
                                                        LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                                                        NOIDUNGBANDAU: arrLogOld.join("; "),
                                                        NOIDUNGMOI: arrLogNew.join("; "),
                                                        USERID: singletonObject.userId,
                                                        ACTION: LOGHSBAACTION.EDIT.KEY,
                                                        NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                                                    });
                                                } else {
                                                    luuLogHSBATheoBN({
                                                        SOVAOVIEN: soVaoVien,
                                                        LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                                                        NOIDUNGBANDAU: "",
                                                        NOIDUNGMOI: arrLogNew.join("; "),
                                                        USERID: singletonObject.userId,
                                                        ACTION: LOGHSBAACTION.INSERT.KEY,
                                                        NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                                                    });
                                                }
                                                getThongTinBenhNhan(retBN, false);
                                                notifiToClient("Green", "Cập nhật thành công");
                                                hideSelfLoading("xn_luuthongtin");
                                            });
                                        })

                                        $.post("cmu_post", {
                                            url: [singletonObject.dvtt, sophieu, dataForm.NGUOI_GIAO_MAU, dataForm.NGUOI_NHAN_MAU,
                                                dataForm.NGAY_LAY_MAU_FORM? moment(dataForm.NGAY_LAY_MAU_FORM).format("DD/MM/YYYY HH:mm"): "",
                                                soVaoVien, "CMU_CAPNHAT_NGUOILAYMAU"].join("```")
                                        }).then(function (data) {

                                        });
                                    }
                                });

                            }
                        });
                    } else {
                        $.post('cmu_post', {
                            url: [dvtt, noitru, sophieu, soVaoVien, retBN.SOVAOVIEN_NOI, retBN.SOVAOVIEN_DT_NOI,
                                dataForm.NGAY_CHI_DINH_F, ngaygiotrakq, 'CMU_CHECKMAY_XN'].join("```")
                        }).done(function (data) {
                            if(data == '1'){
                                hideSelfLoading("xn_luuthongtin");
                                return notifiToClient("Red", "Vui lòng Lưu máy xét nghiệm trước khi Lưu thông tin");
                            }
                            if(data == '3'){
                                hideSelfLoading("xn_luuthongtin");
                                return notifiToClient("Red", "Thời gian trả lời kết quả phải lớn hơn thời gian chỉ đinh " + singletonObject.soPhutGiuaChiDinhVaTraKetQua + " phút");
                            } else {
                                $.post("xetnghiem_update_trangthai", {
                                    url: convertArray([dvtt, noitru, sophieu, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, ketluan, "1", nguoilaymau, ngaygiolaymau, singletonObject.userId, ngaygiotrakq, "0"]),
                                    thoiGianBatDauCls: thoiGianBatDauCls,maIcd: "", tenIcd: "", maBenhLy: ""
                                }).done(function (datares) {
                                    if (datares == -1) {
                                        hideSelfLoading("xn_luuthongtin");
                                        notifiToClient("Red", "Dữ liệu bệnh nhân đã khóa không thể chỉnh sửa, vui lòng liên hệ admin.");
                                        return false;
                                    }
                                    if (singletonObject.batBuocChonNguoiDoc == "1") {
                                        var soVaoVien = dataForm.NOITRU == "0" ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI;
                                        $.ajax({
                                            url: "xetnghiem_update_trangthai_cmu?url=" + convertArray([dvtt, noitru, sophieu, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, ketluan, "1", nguoilaymau, ngaygiolaymau, nguoidoc, ngaygiotrakq, "0"])
                                        }).always(function(){
                                            loadGridXetNghiem();
                                            if (singletonObject.apDungCheckBox == "1") {
                                                $.post('cmu_post', {
                                                    url: [singletonObject.dvtt, noitru, maxncheck, sophieu, ngaygiotrakq,soVaoVien, 'CMU_UPDATENGAY_THUHIEN'].join("```")
                                                }).always(function() {
                                                    $.post("cmu_post", {
                                                        url: [singletonObject.dvtt, sophieu, maxncheck, soVaoVien,
                                                            dataForm.KY_THUAT_VIEN ? dataForm.KY_THUAT_VIEN : singletonObject.userId,
                                                            "CMU_CAPNHAT_NGUOIXACNHAN"].join("```")
                                                    }).then(function (data) {
                                                        var soVaoVien = dataForm.NOITRU == "0" ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI;
                                                        if (dataForm.TRANG_THAI_XET_NGHIEM == 1){
                                                            luuLogHSBATheoBN({
                                                                SOVAOVIEN: soVaoVien,
                                                                LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                                                                NOIDUNGBANDAU: arrLogOld.join("; "),
                                                                NOIDUNGMOI: arrLogNew.join("; "),
                                                                USERID: singletonObject.userId,
                                                                ACTION: LOGHSBAACTION.EDIT.KEY,
                                                                NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                                                            });
                                                        } else {
                                                            luuLogHSBATheoBN({
                                                                SOVAOVIEN: soVaoVien,
                                                                LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                                                                NOIDUNGBANDAU: "",
                                                                NOIDUNGMOI: arrLogNew.join("; "),
                                                                USERID: singletonObject.userId,
                                                                ACTION: LOGHSBAACTION.INSERT.KEY,
                                                                NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                                                            });
                                                        }
                                                        getThongTinBenhNhan(retBN, false);
                                                        hideSelfLoading("xn_luuthongtin");
                                                        notifiToClient("Green", "Cập nhật thành công");
                                                    }, function() {
                                                        var soVaoVien = dataForm.NOITRU == "0" ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI;
                                                        if (dataForm.TRANG_THAI_XET_NGHIEM == 1){
                                                            luuLogHSBATheoBN({
                                                                SOVAOVIEN: soVaoVien,
                                                                LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                                                                NOIDUNGBANDAU: arrLogOld.join("; "),
                                                                NOIDUNGMOI: arrLogNew.join("; "),
                                                                USERID: singletonObject.userId,
                                                                ACTION: LOGHSBAACTION.EDIT.KEY,
                                                                NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                                                            });
                                                        } else {
                                                            luuLogHSBATheoBN({
                                                                SOVAOVIEN: soVaoVien,
                                                                LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                                                                NOIDUNGBANDAU: "",
                                                                NOIDUNGMOI: arrLogNew.join("; "),
                                                                USERID: singletonObject.userId,
                                                                ACTION: LOGHSBAACTION.INSERT.KEY,
                                                                NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                                                            });
                                                        }
                                                        getThongTinBenhNhan(retBN, false);
                                                        hideSelfLoading("xn_luuthongtin");
                                                        notifiToClient("Green", "Cập nhật thành công");
                                                    });
                                                })
                                            } else {
                                                $.post("cmu_post", {
                                                    url: [singletonObject.dvtt, sophieu, maxncheck, soVaoVien,
                                                        dataForm.KY_THUAT_VIEN ? dataForm.KY_THUAT_VIEN : singletonObject.userId,
                                                        "CMU_CAPNHAT_NGUOIXACNHAN"].join("```")
                                                }).then(function (data) {
                                                    var soVaoVien = dataForm.NOITRU == "0" ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI;
                                                    if (dataForm.TRANG_THAI_XET_NGHIEM == 1){
                                                        luuLogHSBATheoBN({
                                                            SOVAOVIEN: soVaoVien,
                                                            LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                                                            NOIDUNGBANDAU: arrLogOld.join("; "),
                                                            NOIDUNGMOI: arrLogNew.join("; "),
                                                            USERID: singletonObject.userId,
                                                            ACTION: LOGHSBAACTION.EDIT.KEY,
                                                            NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                                                        });
                                                    } else {
                                                        luuLogHSBATheoBN({
                                                            SOVAOVIEN: soVaoVien,
                                                            LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                                                            NOIDUNGBANDAU: "",
                                                            NOIDUNGMOI: arrLogNew.join("; "),
                                                            USERID: singletonObject.userId,
                                                            ACTION: LOGHSBAACTION.INSERT.KEY,
                                                            NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                                                        });
                                                    }
                                                    getThongTinBenhNhan(retBN, false);
                                                    hideSelfLoading("xn_luuthongtin");
                                                    notifiToClient("Green", "Cập nhật thành công");
                                                }, function() {
                                                    var soVaoVien = dataForm.NOITRU == "0" ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI;
                                                    if (dataForm.TRANG_THAI_XET_NGHIEM == 1){
                                                        luuLogHSBATheoBN({
                                                            SOVAOVIEN: soVaoVien,
                                                            LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                                                            NOIDUNGBANDAU: arrLogOld.join("; "),
                                                            NOIDUNGMOI: arrLogNew.join("; "),
                                                            USERID: singletonObject.userId,
                                                            ACTION: LOGHSBAACTION.EDIT.KEY,
                                                            NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                                                        });
                                                    } else {
                                                        luuLogHSBATheoBN({
                                                            SOVAOVIEN: soVaoVien,
                                                            LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                                                            NOIDUNGBANDAU: "",
                                                            NOIDUNGMOI: arrLogNew.join("; "),
                                                            USERID: singletonObject.userId,
                                                            ACTION: LOGHSBAACTION.INSERT.KEY,
                                                            NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                                                        });
                                                    }
                                                    getThongTinBenhNhan(retBN, false);
                                                    hideSelfLoading("xn_luuthongtin");
                                                    notifiToClient("Green", "Cập nhật thành công");
                                                });
                                            }
                                        })
                                        $.post("cmu_post", {
                                            url: [singletonObject.dvtt, sophieu, dataForm.NGUOI_GIAO_MAU, dataForm.NGUOI_NHAN_MAU,
                                                dataForm.NGAY_LAY_MAU_FORM? moment(dataForm.NGAY_LAY_MAU_FORM).format("DD/MM/YYYY HH:mm"): "",
                                                soVaoVien, "CMU_CAPNHAT_NGUOILAYMAU"].join("```")
                                        }).then(function (data) {

                                        });
                                    } else {
                                        var soVaoVien = dataForm.NOITRU == "0" ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI;
                                        if (dataForm.TRANG_THAI_XET_NGHIEM == 1){
                                            luuLogHSBATheoBN({
                                                SOVAOVIEN: soVaoVien,
                                                LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                                                NOIDUNGBANDAU: arrLogOld.join("; "),
                                                NOIDUNGMOI: arrLogNew.join("; "),
                                                USERID: singletonObject.userId,
                                                ACTION: LOGHSBAACTION.EDIT.KEY,
                                                NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                                            });
                                        } else {
                                            luuLogHSBATheoBN({
                                                SOVAOVIEN: soVaoVien,
                                                LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                                                NOIDUNGBANDAU: "",
                                                NOIDUNGMOI: arrLogNew.join("; "),
                                                USERID: singletonObject.userId,
                                                ACTION: LOGHSBAACTION.INSERT.KEY,
                                                NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                                            });
                                        }
                                        getThongTinBenhNhan(retBN, false);
                                        hideSelfLoading("xn_luuthongtin");
                                        notifiToClient("Green", "Cập nhật thành công");
                                    }
                                });
                            }
                        });
                    }
                }
            });
        }
    });

    $("#xn_xemketqua").click(function() {
        var dataForm = formKetQuaXetNghiem.submission.data;
        getFilesign769("PHIEUKQ_XETNGHIEM", dataForm.SO_PHIEU, -1, singletonObject.dvtt,
            dataForm.NOITRU == "0" ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI, dataForm.SOVAOVIEN_DT_NOI, -1, function(dataKySo) {
                if (dataKySo.length > 0) {
                    getCMUFileSigned769(dataKySo[0].KEYMINIO, "pdf")
                } else {
                    var url = getLinkInPhieuKetQua(false);
                    formKetQuaXetNghiem.emit("checkValidity");
                    if (!formKetQuaXetNghiem.checkValidity(null, false, null, true)) {
                        return notifiToClient("Red", "Vui lòng cập nhật đầy đủ thông tin");
                    }
                    previewPdfDefaultModal(url, "tthc_ravien_bangke_preview");
                }
            });
    });

    $("#xn_danhsachketquamay").click(function() {
        $("#modalDanhSachKetQuaMay").modal("show");
    });

    $("#xn_lammoiketquamay").click(function(){
        var url = "cmu_getlist?url=" +convertArray([singletonObject.dvtt ,$("#xn_ngayketquamay").val(), $("#xn_mayketquamay").val(), "", "CMU_DSKQVNLIS"]);
        $("#list_ketquamay").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
    });

    $("#xn_kyso").click(function() {
        var url = getLinkInPhieuKetQua(true);
        formKetQuaXetNghiem.emit("checkValidity");
        if (!formKetQuaXetNghiem.checkValidity(null, false, null, true)) {
            return notifiToClient("Red", "Vui lòng cập nhật đầy đủ thông tin");
        }
        var keyWord = "KÝ DUYỆT KẾT QUẢ";
        if (singletonObject.inPhieuKQMay == "0") {
            keyWord = "KHOA XÉT NGHIỆM";
        }

        var dataForm = formKetQuaXetNghiem.submission.data;
        if(singletonObject.userId != dataForm.MA_BS_DOC_KQ) {
            return notifiToClient("Red", "Bạn không thể ký số phiếu kết quả xét nghiệm của người khác");
        }
        previewAndSignPdfDefaultModal({
            url: url,
            idButton: 'kysoketquaxn',
        }, function(){
            $("#kysoketquaxn").click(function() {
                kySoChung({
                    dvtt: singletonObject.dvtt,
                    userId: singletonObject.userId,
                    url: url,
                    loaiGiay: "PHIEUKQ_XETNGHIEM",
                    maBenhNhan: dataForm.MABENHNHAN,
                    soBenhAn: dataForm.STT_BENHAN,
                    soPhieuDichVu: dataForm.SO_PHIEU,
                    soVaoVien: dataForm.NOITRU == "0" ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI,
                    soVaoVienDT: dataForm.SOVAOVIEN_DT_NOI,
                    keyword: keyWord,
                    fileName: "Phiếu kết quả xét nghiệm " + dataForm.SO_PHIEU + " - " + dataForm.TENBENHNHAN,
                }, function(dataKySo) {
                    luuLogHSBATheoBN({
                        SOVAOVIEN: dataForm.NOITRU == "0" ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI,
                        LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                        NOIDUNGBANDAU: "",
                        NOIDUNGMOI: "Ký số phiếu kết quả xét nghiệm " + dataForm.SO_PHIEU,
                        USERID: singletonObject.userId,
                        ACTION: LOGHSBAACTION.EDIT.KEY,
                        NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                    });
                    showOrHideByClass("hsba_tab1","chuakyshow","kyso")
                    $("#modalPreviewAndSignPDF").modal("hide");
                    var rowData = getThongtinRowSelected("list_benhnhan");
                    getThongTinBenhNhan(rowData);
                });
            });
        });
    });

    $("#xn_huykyso").click(function() {
        var dataForm = formKetQuaXetNghiem.submission.data;
        if(singletonObject.userId != dataForm.MA_BS_DOC_KQ) {
            return notifiToClient("Red", "Bạn không thể hủy ký số phiếu kết quả xét nghiệm của người khác");
        }
        confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
            huykysoFilesign769("PHIEUKQ_XETNGHIEM", dataForm.SO_PHIEU, singletonObject.userId, singletonObject.dvtt,
                dataForm.NOITRU == "0" ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI, dataForm.SOVAOVIEN_DT_NOI, -1, function(data) {
                    luuLogHSBATheoBN({
                        SOVAOVIEN: dataForm.NOITRU == "0" ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI,
                        LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                        NOIDUNGBANDAU: "",
                        NOIDUNGMOI: "Huỷ ký số phiếu kết quả xét nghiệm " + dataForm.SO_PHIEU,
                        USERID: singletonObject.userId,
                        ACTION: LOGHSBAACTION.EDIT.KEY,
                        NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                    });
                    var rowData = getThongtinRowSelected("list_benhnhan");
                    getThongTinBenhNhan(rowData);
                })
        }, function () {

        })
    });

    $("#xn_layketquatudong").click(function(){
        showSelfLoading("xn_layketquatudong");
        showLoaderIntoWrapId("list_xetnghiem_wrap");
        layKetQuaXetNghiemTuDong();
    });

    function loadGridBenhNhan() {
        try {
            $("#hsba_tabs").tabs("option", "disabled", [1]);
            var ngay = convertStr_MysqlDate($("#xn_ngaychidinh").val());
            var dvtt = singletonObject.dvtt;
            var phong = singletonObject.maphongbenh;
            var phongban = $("#xn_khoa").val() ? $("#xn_khoa").val() : -1;
            var phongbenh = $("#xn_phong").val() ? $("#xn_phong").val() : -1;
            var doituong = $("#xn_doituong").val();
            var daxetnghiem = $("#xn_trangthai").val();
            var arr, url;
            if (singletonObject.locDsBnTheoPhong == "1") {
                arr = [dvtt, ngay, daxetnghiem, phongbenh, phongban];
                url = 'xetnghiem_ds_benhnhan_theophong?url=' + convertArray(arr);
            } else {
                arr = [dvtt, ngay, phong, daxetnghiem, phongban, doituong, 1];
                url = "cmu_list_CMU_DSBN_XN_EMR?url=" + convertArray(arr);
            }
            $("#list_benhnhan").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
        } catch (error) {
        }
    }

    function loadGridXetNghiem() {
        var rowData = getThongtinRowSelected("list_benhnhan");
        if (singletonObject.sapXepXetNghiem == "1") {
            var soVaoVien = rowData.NOITRU == "0" ? rowData.SOVAOVIEN : rowData.SOVAOVIEN_NOI;
            var arr = [singletonObject.dvtt, rowData.NOITRU, rowData.SO_PHIEU, soVaoVien, rowData.SOVAOVIEN_DT_NOI,
                $("#xn_trangthai").val(), singletonObject.userId];

            var url = "xetnghiem_hienthi_chitiet_sapxep?url=" + convertArray(arr);
            $("#list_xn_bhyt").jqGrid('setGridParam', {
                datatype: 'json',
                url: url
            }).trigger('reloadGrid');
        } else {
            var arr = [rowData.NOITRU, rowData.SO_PHIEU, rowData.STT_BENHAN, rowData.STT_DOTDIEUTRI, rowData.STT_DIEUTRI,
                singletonObject.dvtt, rowData.SOVAOVIEN, rowData.SOVAOVIEN_NOI, rowData.SOVAOVIEN_DT_NOI, $("#xn_trangthai").val()];
            var url = "xetnghiem_hienthi_chitiet_svv?url=" + convertArray(arr);
            $("#list_xn_bhyt").jqGrid('setGridParam', {
                datatype: 'json',
                url: url
            }).trigger('reloadGrid');
        }
    }

    async function initDataAsync() {
        showLoaderIntoWrapId("hsba_tabs");
        try {
            const danhsachphongbanPromise = $.get("cmu_list_CMU_TAM_SELECT_PHONG_BAN?url=" + convertArray([singletonObject.dvtt]));
            // const danhsachphongbenhPromise = $.get("cmu_list_KB_NOT_ds_phongkhamnoitru?url=" + convertArray([singletonObject.makhoa]));
            singletonObject.danhsachphongban = await danhsachphongbanPromise;
            // singletonObject.danhsachphongbenh = await danhsachphongbenhPromise;
            singletonObject.danhsachphongbanFormio = singletonObject.danhsachphongban.map(function(object) {
                return {
                    label: object.TENKHOA,
                    value: object.MAKHOA,
                }
            })
            initSelect2IfnotIntance('xn_khoa', singletonObject.danhsachphongban, 'MAKHOA', 'TENKHOA', 0, 1, "", 1);
            $("#xn_khoa").change();
            hideSelfLoadingByClass("btn-loading");
            $("#hsba_tabs").tabs("option", "disabled", [1]);
            loadGridBenhNhan();
            hideLoaderIntoWrapId("hsba_tabs");
        } catch (error) {
            hideLoaderIntoWrapId("hsba_tabs");
        }
    }

    function initDataNotAsync() {
        $.get("cmu_list_CMU_DSNHANVIENTOANBV?url="+convertArray([singletonObject.dvtt])).done(function(data){
            singletonObject.danhsachnhanvien = data;
            singletonObject.danhsachnhanvienFormio = data.map(function(object) {
                return {
                    label: object.TEN_NHANVIEN,
                    value: object.MA_NHANVIEN.toString(),
                }
            });
        });
        $.get("cmu_list_CMU_DSNHANVIENTOANBV_V2?url="+convertArray([singletonObject.dvtt])).done(function(data){
            singletonObject.danhsachtatcanhanvien = data.map(function(object) {
                return {
                    label: object.TEN_NHANVIEN,
                    value: object.MA_NHANVIEN.toString(),
                    tennhanvien: object.TEN_NHANVIEN,
                }
            });
        });
        $.get("cmu_list_CMU_DSNHANVIEN?url="+convertArray([singletonObject.makhoa])).done(function(data){
            singletonObject.danhsachnhanvienkhoa = data.filter(function(obj){
                return obj.MANHANVIEN != 0;
            });
            singletonObject.danhsachnhanvienkhoaFormio = singletonObject.danhsachnhanvienkhoa.map(function(object) {
                return {
                    label: object.TENNHANVIEN,
                    value: object.MANHANVIEN.toString(),
                }
            });
        });
        $.get("danhsachphongban?madv="+singletonObject.dvtt).done(function(data){
            singletonObject.danhsachtatcaphongban = data.filter(function(obj){
                return obj.HOAT_DONG == 1;
            });
            singletonObject.danhsachtatcaphongbanFormio = singletonObject.danhsachtatcaphongban.map(function(object) {
                return {
                    label: object.TEN_PHONGBAN,
                    value: object.MA_PHONGBAN.toString()
                }
            });
        });
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, "CMU_VNLIS_DM_MAY_XN"])).done(function(data){
            singletonObject.danhsachmayxn = data.filter(function(obj){
                return obj.HOATDONG == 1;
            });
            singletonObject.danhsachmayxnFormio = singletonObject.danhsachmayxn.map(function(object) {
                return {
                    label: object.TENMAY,
                    value: object.MAMAY.toString(),
                }
            });
            singletonObject.danhsachmayxnFormio.forEach(function(option) {
                $("#xn_mayketquamay").append("<option value='" + option.value + "'>" + option.label + "</option>")
            });
        });
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, "CMU_VNLIS_DM_MAY_XN_V2"])).done(function(data){
            singletonObject.danhsachmayxn = data.filter(function(obj){
                return obj.HOATDONG == 1;
            });
            // Dùng cho jqgrid
            singletonObject.danhsachmayxnJqgrid = singletonObject.danhsachmayxn.map(option => option.STT_MAY_4210.toString() + ":" + option.TENMAY).join(';');
            singletonObject.danhsachmayxnJqgrid = "0:Không sử dụng;" + singletonObject.danhsachmayxnJqgrid;
            var colModel = $("#list_xn_bhyt").jqGrid('getColProp', 'STT_MAMAY_HT');
            colModel.editoptions.value = singletonObject.danhsachmayxnJqgrid;
            $("#list_xn_bhyt").jqGrid('setColProp', 'STT_MAMAY_HT', { editoptions: colModel.editoptions });
            // End Dùng cho jqgrid
        });
    }

    function getThongTinBenhNhan(rowData, isLoadGridXetNghiem = true) {
        var sovaovien = rowData.SOVAOVIEN != '0' ? rowData.SOVAOVIEN : rowData.SOVAOVIEN_NOI;
        try {
            showLoaderIntoWrapId("hsba_tabs");
            $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, rowData.SO_PHIEU, sovaovien, rowData.SOVAOVIEN_DT_NOI, rowData.STT_BENHAN, "CMU_XN_EMR_GET_INFO"])).done(function(dataInfo){
                if(dataInfo.length == 0) {
                    return notifiToClient("Red", "Không tìm thấy thông tin bệnh nhân");
                }
                rowData = {...rowData, ...dataInfo[0]};
                var sttDieuTri = rowData.STT_DIEUTRI != "" ? rowData.STT_DIEUTRI : "0";
                var sttDotDieuTri = rowData.STT_DOTDIEUTRI != "" ? rowData.STT_DOTDIEUTRI : "0";
                var sttBenhAn = rowData.STT_BENHAN != "" ? rowData.STT_BENHAN : "0";
                var noiTru = rowData.NOITRU == "0" ? 0 : 1;
                var arr1 = [rowData.MA_KHAM_BENH, rowData.SO_PHIEU, singletonObject.dvtt, noiTru, sttBenhAn, sttDotDieuTri, sttDieuTri, 0]
                $.get("hpg_thongtin_mo_rong_bn_cls?url=" + convertArray(arr1)).done(function(dataInfoThem){
                    if(dataInfo.length == 0) {
                        return notifiToClient("Red", "Không tìm thấy thông tin bệnh nhân");
                    }
                    rowData = {...rowData, ...dataInfoThem[0]};
                    if ((singletonObject.canhBaoChuaDongVienPhi == 1 && rowData.DA_THANH_TOAN == 0) || (singletonObject.canhBaoChuaDongVienPhi == 0 && dataInfoThem[0].TT_THANHTOAN == "0"))
                        rowData.TRANG_THAI_THANH_TOAN = "Chưa thanh toán";
                    else
                        rowData.TRANG_THAI_THANH_TOAN = "Đã thanh toán";
                    // Xử lý giờ chỉ định, giờ bắt đầu, giờ trả kết quả
                    var ngayTHYL = rowData.NGAY_TH_YL ;
                    var momentNgayChiDinh = moment(rowData.NGAY_CHI_DINH, 'DD/MM/YYYY HH:mm');
                    var momentNgayTraKetQua = moment(rowData.NGAY_TRA_KETQUA + " " + rowData.GIO_TRA_KETQUA, 'DD/MM/YYYY HH:mm:ss');
                    if (!ngayTHYL) {
                        ngayTHYL = momentNgayChiDinh.add(1, 'minutes').format('DD/MM/YYYY HH:mm:ss');
                    }
                    rowData.NGAY_TH_YL_FORM = moment(ngayTHYL, 'DD/MM/YYYY HH:mm:ss').toISOString();
                    rowData.NGAY_TRA_KET_QUA_FORM = momentNgayTraKetQua.toISOString();
                     // NGAY_LAY_MAU trên DB là ngày nhận mẫu thực tế
                    if (rowData.NGAY_NHAN_MAU) {
                        rowData.NGAY_NHAN_MAU_FORM = moment(rowData.NGAY_NHAN_MAU, 'DD/MM/YYYY HH:mm').toISOString()
                    }
                    if(rowData.NGAY_LAY_MAU) {
                        rowData.NGAY_LAY_MAU_FORM = moment(rowData.NGAY_LAY_MAU, 'DD/MM/YYYY HH:mm').toISOString();
                    }
                    // End Xử lý giờ chỉ định, giờ bắt đầu, giờ trả kết quả
                    rowData.MA_BS_DOC_KQ = rowData.MA_BS_DOC_KQ ? rowData.MA_BS_DOC_KQ : singletonObject.userId;
                    var convertedObj = convertNumericToString(rowData);

                    if (singletonObject.canhBaoChuaDongVienPhi > 0 && rowData.NOITRU == "0" && rowData.CO_BHYT == 0 && rowData.DA_THANH_TOAN == 0) {
                        if (singletonObject.canhBaoChuaDongVienPhi == 2 && ret.MIENPHI == 0) {
                            return notifiToClient("Red", "Bệnh nhân ngoại trú chưa đóng viện phí.");
                        }
                        confirmToClient("Bệnh nhân ngoại trú chưa đóng viện phí. Bạn có muốn tiếp tục?", function(confirm) {
                            genFormKetQua(convertedObj);
                            if(isLoadGridXetNghiem !== false) {
                                loadGridXetNghiem();
                            }

                        }, function(){
                            hideLoaderIntoWrapId('hsba_tabs');
                            return;
                        });
                    } else {
                        genFormKetQua(convertedObj);
                        if(isLoadGridXetNghiem !== false) {
                            loadGridXetNghiem();
                        }

                    }
                });
                $(".chuakyshow").show();
                $(".kyso").show();
                if (rowData.TRANG_THAI_XET_NGHIEM == 1) {
                    $(".kyso").show();
                    if(rowData.KEYSIGN) {
                        $("#xn_kyso").hide();
                        $(".chuakyshow").hide();
                    } else {
                        $("#xn_huykyso").hide();
                        $(".chuakyshow").show();
                    }
                } else {
                    $(".kyso").hide();
                }
            });
        } catch (error) {
            hideLoaderIntoWrapId("hsba_tabs")
        }
    }

    function genFormKetQua(objData) {
        var jsonForm = getJSONObjectForm([
            {
                label: "xn",
                key: "xn",
                columns: [
                    {
                        "components": [
                            {
                                "label": "SID",
                                "customClass": "pr-2 input-text-bold",
                                "key": "SID",
                                "type": "textfield",
                                validate: {
                                    required: true,
                                },
                                others: {
                                    disabled: true,
                                }
                            }
                        ],
                        "width": 1,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Họ tên",
                                "key": "TENBENHNHAN",
                                "type": "textfield",
                                "customClass": "pr-2 input-text-bold",
                                others: {
                                    "disabled": true,
                                }
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Số phiếu",
                                "key": "SO_PHIEU",
                                "type": "textfield",
                                "customClass": "pr-2",
                                others: {
                                    "disabled": true,
                                }
                            },
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Tuổi",
                                "key": "TUOI_HT",
                                "type": "textfield",
                                "customClass": "pr-2",
                                others: {
                                    "disabled": true,
                                }
                            },
                        ],
                        "width": 1,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Giới tính",
                                "key": "GIOITINH_HT",
                                "type": "textfield",
                                "customClass": "pr-2",
                                others: {
                                    "disabled": true,
                                },
                            },
                        ],
                        "width": 1,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Mã bệnh nhân",
                                "key": "MABENHNHAN",
                                "type": "number",
                                "customClass": "pr-2",
                                others: {
                                    "disabled": true,
                                }
                            },
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "BHYT",
                                "customClass": "pr-2",
                                "key": "SOTHEBHYT",
                                "type": "textfield",
                                others: {
                                    "disabled": true,
                                }
                            }
                        ],
                        "width": 2,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "xn",
                key: "xn",
                columns: [
                    {
                        "components": [
                            {
                                "label": "Địa chỉ",
                                "key": "DIACHI",
                                "type": "textfield",
                                "customClass": "pr-2",
                                others: {
                                    "disabled": true,
                                }
                            },
                        ],
                        "width": 4,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Chẩn đoán",
                                "key": "CHANDOAN",
                                "type": "textfield",
                                "customClass": "pr-2",
                                others: {
                                    "disabled": true,
                                }
                            },
                        ],
                        "width": 8,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "xn",
                key: "xn",
                columns: [
                    {
                        "components": [
                            {
                                "label": "Thanh toán",
                                "key": "TRANG_THAI_THANH_TOAN",
                                "type": "textfield",
                                "customClass": "pr-2",
                                others: {
                                    "disabled": true,
                                },
                            },
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Ngày chỉ định",
                                "key": "NGAY_CHI_DINH",
                                "type": "textfield",
                                "customClass": "pr-2",
                                others: {
                                    "disabled": true,
                                }
                            },
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Người chỉ định",
                                "customClass": "pr-2",
                                "key": "NGUOI_CHI_DINH",
                                "type": "select",
                                others: {
                                    "data": {
                                        "values": singletonObject.danhsachtatcanhanvien
                                    },
                                    "disabled": true,
                                }
                            }
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Khoa chỉ định",
                                "key": "TEN_KHOA_CHI_DINH",
                                "type": "textfield",
                                "customClass": "pr-2",
                                others: {
                                    "disabled": true,
                                }
                            },
                        ],
                        "width": 4,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Phòng chỉ định",
                                "key": "TEN_PHONG_CHI_DINH",
                                "type": "textfield",
                                "customClass": "pr-2",
                                others: {
                                    "disabled": true,
                                },
                            },
                        ],
                        "width": 2,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "xn",
                key: "xn",
                columns: [
                    {
                        "components": [
                            {
                                "label": "Người đọc kết quả",
                                "customClass": "pr-2",
                                "key": "MA_BS_DOC_KQ",
                                "type": "select",
                                others: {
                                    "data": {
                                        "values": singletonObject.danhsachtatcanhanvien
                                    },
                                    "disabled": objData.KEYSIGN ? true : false,
                                },
                                validate: {
                                    required: singletonObject.batBuocChonNguoiDoc == '1' ? 1 : 0,
                                }
                            }
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Kỹ thuật viên",
                                "customClass": "pr-2",
                                "key": "KY_THUAT_VIEN",
                                "type": "select",
                                others: {
                                    "data": {
                                        "values": singletonObject.danhsachnhanvienkhoaFormio
                                    },
                                    "disabled": objData.KEYSIGN ? true : false,
                                },
                                validate: {
                                    required: true,
                                }
                            }
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Người giao mẫu",
                                "customClass": "pr-2",
                                "key": "NGUOI_GIAO_MAU",
                                "type": "select",
                                others: {
                                    "data": {
                                        "values": singletonObject.danhsachtatcanhanvien
                                    },
                                    "disabled": objData.KEYSIGN ? true : false,
                                },
                            }
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Người nhận mẫu",
                                "customClass": "pr-2",
                                "key": "NGUOI_NHAN_MAU",
                                "type": "select",
                                others: {
                                    "data": {
                                        "values": singletonObject.danhsachnhanvienkhoaFormio
                                    },
                                    "disabled": objData.KEYSIGN ? true : false,
                                },
                            }
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Ngày lấy mẫu",
                                "customClass": "pr-2",
                                "key": "NGAY_LAY_MAU_FORM",
                                "type": "datetime",
                                enableTime: true,
                                minDate: moment(objData.NGAY_CHI_DINH, ['DD/MM/YYYY HH:mm']).add(1, 'minutes').format("YYYY-MM-DD HH:mm"),
                                others: {
                                    "disabled": objData.KEYSIGN ? true : false,
                                },
                            }
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Ngày nhận mẫu",
                                "customClass": "pr-2",
                                "key": "NGAY_NHAN_MAU_FORM",
                                "type": "datetime",
                                enableTime: true,
                                minDate: moment(objData.NGAY_CHI_DINH, ['DD/MM/YYYY HH:mm']).add(1, 'minutes').format("YYYY-MM-DD HH:mm"),
                                others: {
                                    "disabled": objData.KEYSIGN ? true : false,
                                },
                            }
                        ],
                        "width": 2,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "xn",
                key: "xn",
                columns: [
                    {
                        "components": [
                            {
                                "label": "Ngày bắt đầu",
                                "customClass": "pr-2",
                                "key": "NGAY_TH_YL_FORM",
                                "type": "datetime",
                                enableTime: true,
                                minDate: moment(objData.NGAY_CHI_DINH, ['DD/MM/YYYY HH:mm']).add(1, 'minutes').format("YYYY-MM-DD HH:mm"),
                                validate: {
                                    required: true,
                                },
                                others: {
                                    "disabled": objData.KEYSIGN ? true : false,
                                },
                            }
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Ngày trả kết quả",
                                "customClass": "pr-2",
                                "key": "NGAY_TRA_KET_QUA_FORM",
                                "type": "datetime",
                                enableTime: true,
                                validate: {
                                    required: true,
                                },
                                others: {
                                    "disabled": objData.KEYSIGN ? true : false,
                                },
                            }
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                label: "xn",
                                key: "xn",
                                columns: [
                                    {
                                        "components": [
                                            {
                                                "label": "SID cần lấy KQ",
                                                "customClass": "pr-2",
                                                "key": "LIS_SID_CANLAYKQ",
                                                "type": "textfield",
                                                others: {
                                                    "disabled": objData.KEYSIGN ? true : false,
                                                },
                                            }
                                        ],
                                        "width": 4,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "Máy cần lấy KQ",
                                                "customClass": "pr-2",
                                                "key": "LIS_MAYXETNGHIEM",
                                                "type": "select",
                                                others: {
                                                    "data": {
                                                        "values": singletonObject.danhsachmayxnFormio
                                                    },
                                                    "disabled": objData.KEYSIGN ? true : false,
                                                },
                                            }
                                        ],
                                        "width": 5,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "Lấy KQ",
                                                "customClass": "",
                                                "key": "laykqlis",
                                                "type": "button",
                                                others: {
                                                    "leftIcon": "fa fa-save",
                                                    "action": "event",
                                                    "showValidations": false,
                                                    "event": "laykqlis",
                                                    "type": "button",
                                                    "disabled": objData.KEYSIGN ? true : false,
                                                }
                                            },
                                        ],
                                        "width": 3,
                                        "size": "md",
                                    },
                                ],
                                "customClass": "ml-0 mr-0 d-flex align-items-end",
                                "type": "columns",
                            },
                        ],
                        "width": 4,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
        ])
        Formio.createForm(document.getElementById('formKetQua'),
            jsonForm,
            {},
        ).then(function(form) {
            formKetQuaXetNghiem = form;
            var ngayBatDauElement = form.getComponent('NGAY_TH_YL_FORM');
            var ngayTraKetQuaElement = form.getComponent('NGAY_TRA_KET_QUA_FORM');
            $("#"+getIdElmentFormio(form,'NGAY_TH_YL_FORM')).on('change', function(event) {
                var ngayBatDau = moment(ngayBatDauElement.getValue(), 'YYYY-MM-DDTHH:mm:ssZ');
                var ngayTraKetQua = moment(ngayTraKetQuaElement.getValue(), 'YYYY-MM-DDTHH:mm:ssZ');
                var minDate = ngayBatDau.add(5, 'minutes')
                var minDateFormat = minDate.format('YYYY-MM-DD HH:mm');
                ngayTraKetQuaElement.component.datePicker.minDate = minDateFormat;
                ngayTraKetQuaElement.component.widget.minDate = minDateFormat;
                if (ngayTraKetQua.isBefore(minDate)) {
                    ngayTraKetQuaElement.setValue(minDate.format('YYYY-MM-DD HH:mm'));
                } else {
                    ngayTraKetQuaElement.setValue(ngayTraKetQuaElement.getValue());
                }
                ngayTraKetQuaElement.redraw();
            });
            form.on('laykqlis', function(click) {
                var dataForm = formKetQuaXetNghiem.submission.data;
                if (objData.KEYSIGN) {
                    return notifiToClient("Red", "Phiếu kết quả đã ký số");
                }
                layKetQuaXetNghiem(dataForm.LIS_SID_CANLAYKQ, dataForm.LIS_MAYXETNGHIEM, function(){
                    
                }, function() {

                });
            });
            form.on('change', function(event) {
                if(form.submission.data){
                    var dataForm = form.submission.data;
                    dataForm.NGAY_LAY_MAU = moment(dataForm.NGAY_LAY_MAU_FORM).format('DD/MM/YYYY HH:mm');
                    dataForm.NGAY_NHAN_MAU = moment(dataForm.NGAY_NHAN_MAU_FORM).format('DD/MM/YYYY HH:mm');
                    dataForm.NGAY_TH_YL = moment(dataForm.NGAY_TH_YL_FORM).format('DD/MM/YYYY HH:mm');
                    dataForm.NGAY_TRA_KET_QUA = moment(dataForm.NGAY_TRA_KET_QUA_FORM).format('DD/MM/YYYY HH:mm');
                }
            })
            form.submission = {
                data: objData,
            }
            luuLogOld = objData;
            $("#hsba_tabs").tabs("enable", 1);
            $("#hsba_tab1_header").click();
            hideLoaderIntoWrapId("hsba_tabs");
        });
    }

    function convertNumericToString(obj) {
        const newObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                if (typeof obj[key] === 'number') {
                    newObj[key] = obj[key].toString();
                } else {
                    newObj[key] = obj[key];
                }
            }
        }
        return newObj;
    }

    function luuNguoiDocKetQua(_soPhieu, _soVaoVien, _soVaoVienDt = 0, _noiTru = 0, _listMaCls = "-1") {
        $.ajax({
            type: 'POST',
            url: 'update-thong-tin-theo-benh-nhan',
            async: false,
            data: {
                P_LOG_USERID: singletonObject.userId,
                p_dvtt: singletonObject.dvtt,
                p_soPhieu: _soPhieu,
                p_soVaoVien: _soVaoVien,
                p_soVaoVienDt: _soVaoVienDt,
                p_noiTru: _noiTru,
                p_sessKhoaId: singletonObject.makhoa,
                p_nguoiDocKetQua: singletonObject.userId,
                p_listMaCls: _listMaCls,
                p_loai_cls: 'XN',
                action: "UPD_CLS_NGUOI_DOC_KET_QUA"
            }
        }).fail(function(data){
            notifiToClient("Red", "Lỗi cập nhật người đọc kết quả");
        }).then(function (data) {

        }).always(function( data, textStatus, jqXHR ) {

        });
    }

    function getLinkInPhieuKetQua(layTatCaXN = false) {
        var url = "";
        var dataForm = formKetQuaXetNghiem.submission.data;
        var ngayTao = convertStr_MysqlDate(dataForm.NGAYTAO);
        var ngayChiDinh = dataForm.NGAY_CHI_DINH_F.split(" ")[0];
        if (singletonObject.inPhieuKQMay == "0") {
            var arr = [dataForm.MA_KHAM_BENH, dataForm.TENBENHNHAN, dataForm.NAM_SINH, dataForm.GIOITINH_HT, dataForm.DIACHI, "",
                dataForm.NGUOI_CHI_DINH, "0", dataForm.NOITRU, dataForm.STT_BENHAN, dataForm.STT_DOTDIEUTRI, dataForm.STT_DIEUTRI, "0",
                dataForm.SOTHEBHYT, dataForm.SO_PHIEU, dataForm.CAPCUU, ngayTao, dataForm.TUOI, ngayChiDinh, "0", dataForm.TEN_KHOA_CHI_DINH,
                "-1", dataForm.SOVAOVIEN, dataForm.SOVAOVIEN_NOI, dataForm.SOVAOVIEN_DT_NOI, dataForm.CHANDOAN, 0, dataForm.MABENHNHAN, 0];
            var cellValues = [];
            if (layTatCaXN) {
                var allRows = $("#list_xn_bhyt").jqGrid('getRowData');
                for (var i = 0, n = allRows.length; i < n; i++) {
                    cellValues.push(allRows[i].MA_XETNGHIEM);
                }
            } else {
                var selIds = $("#list_xn_bhyt").jqGrid("getGridParam", "selarrrow");
                for (var i = 0, n = selIds.length; i < n; i++) {
                    cellValues.push($("#list_xn_bhyt").jqGrid("getCell", selIds[i], "MA_XETNGHIEM"));
                }
            }
            var maxndchon = "(" + cellValues.join(",") + ")";
            url = "xetnghiem_inketqua_svv?url=" + convertArray(arr) + "&maxetnghiem=" + maxndchon;
        } else {
            var maxndchon = '';
            if (layTatCaXN) {
                $("#list_xn_bhyt input").each(function () {
                    if($(this).parent().parent().hasClass('jqgrow')){
                        maxndchon += $(this).parent().parent().find('td[aria-describedby="list_xn_bhyt_MA_XETNGHIEM"]').html().trim() + ',';
                    }
                });
            } else {
                $("#list_xn_bhyt input:checked").each(function () {
                    if($(this).parent().parent().hasClass('jqgrow')){
                        maxndchon += $(this).parent().parent().find('td[aria-describedby="list_xn_bhyt_MA_XETNGHIEM"]').html().trim() + ',';
                    }
                });
            }
            var sttDieuTri = dataForm.STT_DIEUTRI ? dataForm.STT_DIEUTRI : "0";
            var arr = [singletonObject.dvtt,dataForm.MA_KHAM_BENH, dataForm.TENBENHNHAN, dataForm.NAM_SINH, dataForm.GIOITINH_HT, dataForm.DIACHI,
                "", dataForm.NGUOI_CHI_DINH, dataForm.NOITRU, dataForm.STT_BENHAN, dataForm.STT_DOTDIEUTRI, sttDieuTri +'--,'+maxndchon,
                dataForm.SOTHEBHYT, dataForm.SO_PHIEU, dataForm.CAPCUU, ngayTao, dataForm.TUOI, ngayChiDinh, dataForm.TEN_KHOA_CHI_DINH, "-1",
                dataForm.SOVAOVIEN, dataForm.SOVAOVIEN_NOI, dataForm.SOVAOVIEN_DT_NOI, dataForm.CHANDOAN, dataForm.MABENHNHAN,
                '/WEB-INF/pages/camau/reports/rp_phieuhoasinhmau.jasper',
                '/WEB-INF/pages/camau/reports/rp_phieuxetnghiemhoasinhnuoctieu.jasper',
                '/WEB-INF/pages/camau/reports/rp_phieuxetnghiemhuyethoc_tk.jasper',
                '/WEB-INF/pages/camau/reports/rp_phieuxetnghiembenhpham.jasper',
                '/WEB-INF/pages/camau/reports/rp_phieuxetnghiemhuyethoc_dt.jasper',
                '/WEB-INF/pages/camau/reports/rp_phieuxetnghiemhuyethoc_vs.jasper',
                '/WEB-INF/pages/camau/reports/rp_phieuxetnghiemtong.jasper',
            ];
            var phieuxn = "rp_phieuketquaxetnghiem_theomau";
            if(["96161", "96163", "96025"].includes(singletonObject.dvtt)) {
                phieuxn = "rp_phieuketquaxetnghiem_theomau_96161";
            }
            var param = ['madonvi','makhambenh',  'hoten',"namsinh",
                'gioitinh','diachi_bn','chandoan','bsdieutri','noitru',
                'stt_benhan','stt_dotdieutri','stt_dieutri','sothebaohiem',
                'sophieuxn','capcuu','ngaytao', 'tuoi','ngaydieutri',
                'khoa','loaixetnghiem','sovaovien','sovaovien_noi', 'sovaovien_dt_noi', 'chandoan',
                'mabenhnhan',
                'FILE_1',
                'FILE_2',
                'FILE_3',
                'FILE_4',
                'FILE_5',
                'FILE_6',
                'FILE_7',
            ];
            url = "cmu_injasper?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf&jasper="+phieuxn;
        }
        return url;
    }

    function layKetQuaXetNghiem(sid, may, callBackDone, callBackFail){
        var dataForm = formKetQuaXetNghiem.submission.data;
        if(!dataForm.NGAY_TRA_KET_QUA_FORM) {
            return notifiToClient("Red", "Chưa nhập ngày trả kết quả");
        }
        var soVaoVien = dataForm.SOVAOVIEN != '0' ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI;
        try {
            showLoaderIntoWrapId("hsba_tabs");
            $.post("cmu_post",{
                url: [singletonObject.dvtt,
                    dataForm.SO_PHIEU,
                    dataForm.NOITRU,
                    soVaoVien,
                    dataForm.SOVAOVIEN_DT_NOI,
                    sid,
                    "",
                    convertStr_MysqlDate($("#xn_ngaylamviec").val()),
                    may,
                    moment(dataForm.NGAY_TRA_KET_QUA_FORM).format("DD/MM/YYYY"),
                    singletonObject.userId+"-"+singletonObject.user,
                    dataForm.GIOITINH == "true" ? '1' : '0',
                    'CMU_MAPKQ_VNLIS_V2'
                ].join("```")
            }).done(function (dt) {
                if(dt !=0) {
                    luuLogHSBATheoBN({
                        SOVAOVIEN: dataForm.SOVAOVIEN != '0' ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI,
                        LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                        NOIDUNGBANDAU: "",
                        NOIDUNGMOI: "Lấy kết quả xét nghiệm với mã " + sid + " và máy " + getTextSelectedFormio(formKetQuaXetNghiem.getComponent('LIS_MAYXETNGHIEM')) + " cho phiếu xét nghiệm " + dataForm.SO_PHIEU,
                        USERID: singletonObject.userId,
                        ACTION: LOGHSBAACTION.EDIT.KEY,
                        NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                    });
                    notifiToClient("Green", "Map thành công");
                    loadGridXetNghiem();
                } else {
                    notifiToClient("Red", "Lấy kết quả thất bại");
                }
            }).always(function(){
                hideLoaderIntoWrapId("hsba_tabs");
                callBackDone && callBackDone();
            });
        } catch (error) {
            hideLoaderIntoWrapId("hsba_tabs");
            callBackFail && callBackFail();
        }
    }

    function layKetQuaXetNghiemTuDong(callBackDone, callBackFail){
        var dataForm = formKetQuaXetNghiem.submission.data;
        var url = "liskgg_xetnghiem_laykq_tudong";
        $.post(url, {
            sophieucd: dataForm.SO_PHIEU,
            noitru: dataForm.NOITRU,
            sovaovien: dataForm.NOITRU == "0" ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI,
            sovaovien_dt: dataForm.SOVAOVIEN_DT_NOI,
        }).done(function (resp) {
            if (resp.errorcode == 0) {
                if (resp.data == 0) {
                    luuLogHSBATheoBN({
                        SOVAOVIEN: dataForm.SOVAOVIEN != '0' ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI,
                        LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
                        NOIDUNGBANDAU: "",
                        NOIDUNGMOI: "Lấy kết quả xét nghiệm tự động cho phiếu xét nghiệm " + dataForm.SO_PHIEU,
                        USERID: singletonObject.userId,
                        ACTION: LOGHSBAACTION.EDIT.KEY,
                        NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                    });
                    hideSelfLoading("xn_layketquatudong");
                    hideLoaderIntoWrapId("list_xetnghiem_wrap");
                    notifiToClient("Green", "Lấy kết quả tự động thành công!");
                    loadGridXetNghiem();
                } else {
                    hideSelfLoading("xn_layketquatudong");
                    hideLoaderIntoWrapId("list_xetnghiem_wrap");
                    notifiToClient("Red", '[' + resp.data + '] Cập nhật kết quả máy thất bại');
                }
            } else {
                hideSelfLoading("xn_layketquatudong");
                hideLoaderIntoWrapId("list_xetnghiem_wrap");
                notifiToClient("Red", resp.description);
            }
        }).fail(function () {
            hideSelfLoading("xn_layketquatudong");
            hideLoaderIntoWrapId("list_xetnghiem_wrap");
            notifiToClient("Red", 'Có lỗi xảy ra');
        });
    }
})