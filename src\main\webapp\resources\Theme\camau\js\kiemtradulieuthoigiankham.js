function cmuNgoaitruktdulieutgkham(data) {
    try {
        var arr_params = [data.dvtt, data.sovaovien, data.userId, data.thoigian];
        var resCLS = $.ajax({
            url: "cmu_list_ngoaitru_kiemtra_thoigian?url=" + convertArray(arr_params),
            async: false
        }).responseText;
        var resCLSJSON = JSON.parse(resCLS);
        try {
            $.post("cmu_post_CMU_INSLOGKTRATG", {
                url: [data.dvtt, resCLS + "_"+data.thoigian+ "_"+data.userId, data.sovaovien].join("```")
            })
        } catch (ex) {

        }
        if (resCLSJSON && resCLSJSON.length > 0) {
            $.alert({
                title: 'Dữ liệu không hợp lệ!',
                content: '<table id="cmu_ngoaitru_kt_dulieu_tg" class="jqx-grid-cell-wrap"></table>',
                type: 'red',
                boxWidth: '900px',
                useBootstrap: false,
                escapeKey: true,
                closeIcon: true,
                typeAnimated: true,
                onContentReady: function () {
                    $("#cmu_ngoaitru_kt_dulieu_tg").jqGrid({
                        datatype: 'local',
                        //regional: 'en', // this is default
                        data: resCLSJSON,
                        rownumbers: true,
                        height: 400,
                        colModel: [
                            {name: 'MABENHNHAN', label: 'Mã bệnh nhân', width: 60},
                            {name: 'TEN_BENH_NHAN', label: 'Tên bệnh nhân', width: 180, align: 'center'},
                            {name: 'THOIGIAN', label: 'Thời gian y lệnh', width: 180, align: 'center'},
                            {name: 'SOPHIEU', label: 'Ghi chú', width: 200},
                            {name: 'SOVAOVIEN', label: 'SOVAOVIEN', hidden: true},
                        ],
                    });
                }
            });
            return false;
        }
        return true;
    } catch (e) {
        jAlert("Red", "Lỗi kiểm tra dữ liệu")
        return true;
    }

}

function cmuNgoaitruktdulieuhoantatkham(data) {
    try {
        var arr_params = [data.dvtt, data.sovaovien, data.userId, moment().format("DD/MM/YYYY HH:mm")];
        var resCLS = $.ajax({
            url: "cmu_list_ngoaitru_kiemtra_hoantk?url=" + convertArray(arr_params),
            async: false
        }).responseText;
        var resCLSJSON = JSON.parse(resCLS);
        try {
            $.post("cmu_post_CMU_INSLOGKTRATG", {
                url: [data.dvtt, resCLS + "_KTRAHTK_"+data.thoigian+ "_"+data.userId, data.sovaovien].join("```")
            })
        } catch (ex) {

        }
        if (resCLSJSON && resCLSJSON.length > 0) {
            $.alert({
                title: 'Dữ liệu không hợp lệ!',
                content: '<table id="cmu_ngoaitru_kt_dulieu_tg" class="jqx-grid-cell-wrap"></table>',
                type: 'red',
                boxWidth: '900px',
                useBootstrap: false,
                escapeKey: true,
                closeIcon: true,
                typeAnimated: true,
                onContentReady: function () {
                    $("#cmu_ngoaitru_kt_dulieu_tg").jqGrid({
                        datatype: 'local',
                        //regional: 'en', // this is default
                        data: resCLSJSON,
                        rownumbers: true,
                        height: 400,
                        colModel: [
                            {name: 'MABENHNHAN', label: 'Mã bệnh nhân', width: 60},
                            {name: 'TEN_BENH_NHAN', label: 'Tên bệnh nhân', width: 180, align: 'center'},
                            {name: 'THOIGIAN', label: 'Thời gian y lệnh', width: 180, align: 'center'},
                            {name: 'SOPHIEU', label: 'Ghi chú', width: 200},
                            {name: 'SOVAOVIEN', label: 'SOVAOVIEN', hidden: true},
                        ],
                    });
                }
            });
            return false;
        }
        return true;
    } catch (e) {
        jAlert("Red", "Lỗi kiểm tra dữ liệu")
        return true;
    }

}

function cmuNgoaitruktdulieutgkhamRavien(data) {
    try {
        var arr_params = [data.dvtt, data.sovaovien, data.userId, data.thoigian, data.loaikythuat];
        var resCLS = $.ajax({
            url: "cmu_list_cmu_xemct_trungylenh?url=" + convertArray(arr_params),
            async: false
        }).responseText;
        var resCLSJSON = JSON.parse(resCLS);
        if (resCLSJSON && resCLSJSON.length > 0) {
            $.alert({
                title: 'Dữ liệu không hợp lệ!',
                content: '<table id="cmu_ngoaitru_kt_dulieu_tg" class="jqx-grid-cell-wrap"></table>',
                type: 'red',
                boxWidth: '900px',
                useBootstrap: false,
                escapeKey: true,
                closeIcon: true,
                typeAnimated: true,
                onContentReady: function () {
                    $("#cmu_ngoaitru_kt_dulieu_tg").jqGrid({
                        datatype: 'local',
                        //regional: 'en', // this is default
                        data: resCLSJSON,
                        rownumbers: true,
                        height: 400,
                        colModel: [
                            {name: 'MA_BN', label: 'Mã bệnh nhân', width: 60},
                            {name: 'TEN_BENH_NHAN', label: 'Tên bệnh nhân', width: 180},
                            {name: 'TEN_DICH_VU', label: 'Tên dịch vụ', width: 180},
                            {name: 'NGAYGIOYLENH', label: 'Thời gian y lệnh', width: 180},
                            {name: 'NGAYGIOKETQUA', label: 'Thời gian kết quả', width: 180},
                            {name: 'GHICHU', label: 'Ghi chú', width: 200},
                            {name: 'SOVAOVIEN', label: 'SOVAOVIEN', hidden: true},
                        ],
                    });
                }
            });
            return false;
        }
        return true;
    } catch (e) {
        jAlert("Red", "Lỗi kiểm tra dữ liệu")
        return true;
    }

}

function cmuNgoaitruktrathuocvacls(data) {
    try {
        var arr_params = [data.dvtt, data.sovaovien, data.userId];
        var resCLS = $.ajax({
            url: "cmu_list_ngoaitru_kiemtra_thuocvacls?url=" + convertArray(arr_params),
            async: false
        }).responseText;
        var resCLSJSON = JSON.parse(resCLS);
        try {
            $.post("cmu_post_CMU_INSLOGKTRATG", {
                url: [data.dvtt, resCLS + "_"+data.thoigian+ "_KIEMTRATHUOCVACLS_"+data.userId, data.sovaovien].join("```")
            })
        } catch (ex) {

        }
        if (resCLSJSON && resCLSJSON.length > 0) {
            $.alert({
                title: 'Dữ liệu không hợp lệ!',
                content: '<table id="cmu_ngoaitru_kt_dulieu_tg" class="jqx-grid-cell-wrap"></table>',
                type: 'red',
                boxWidth: '900px',
                useBootstrap: false,
                escapeKey: true,
                closeIcon: true,
                typeAnimated: true,
                onContentReady: function () {
                    $("#cmu_ngoaitru_kt_dulieu_tg").jqGrid({
                        datatype: 'local',
                        //regional: 'en', // this is default
                        data: resCLSJSON,
                        rownumbers: true,
                        height: 400,
                        colModel: [
                            {name: 'MABENHNHAN', label: 'Mã bệnh nhân', width: 60},
                            {name: 'TEN_BENH_NHAN', label: 'Tên bệnh nhân', width: 180, align: 'center'},
                            {name: 'THOIGIAN', label: 'Thời gian kết quả', width: 180, align: 'center'},
                            {name: 'SOPHIEU', label: 'Số phiếu', width: 100, align: 'center'},
                            {name: 'GHI_CHU', label: 'Ghi chú', width: 200},
                            {name: 'SOVAOVIEN', label: 'SOVAOVIEN', hidden: true},
                        ],
                    });
                }
            });
            return false;
        }
        return true;
    } catch (e) {
        jAlert("Red", "Lỗi kiểm tra dữ liệu")
        return true;
    }

}

function cmuGetCurrentTimeServer(dvtt) {
    var thoigiankham = $.ajax({type: "POST", url: "cmu_post_cmu_getcurrenttime", async: false,
        data:  {url: [dvtt].join('```')}
    }).responseText;
    return thoigiankham;
}

function cmuNgoaitruktdulieutgthuchienCLS(data) {
    try {
        var arr_params = [data.dvtt, data.sovaovien, data.userId, data.thoigianbd, data.thoigiankt, data.loaikythuat];
        var resCLS = $.ajax({
            url: "cmu_list_cmu_xemct_trungtgthuchien?url=" + convertArray(arr_params),
            async: false
        }).responseText;
        var resCLSJSON = JSON.parse(resCLS);
        if (resCLSJSON && resCLSJSON.length > 0) {
            $.alert({
                title: 'Dữ liệu không hợp lệ!',
                content: '<table id="cmu_ngoaitru_kt_dulieu_tg" class="jqx-grid-cell-wrap"></table>',
                type: 'red',
                boxWidth: '900px',
                useBootstrap: false,
                escapeKey: true,
                closeIcon: true,
                typeAnimated: true,
                onContentReady: function () {
                    $("#cmu_ngoaitru_kt_dulieu_tg").jqGrid({
                        datatype: 'local',
                        //regional: 'en', // this is default
                        data: resCLSJSON,
                        rownumbers: true,
                        height: 400,
                        colModel: [
                            {name: 'MA_BN', label: 'Mã bệnh nhân', width: 60},
                            {name: 'TEN_BENH_NHAN', label: 'Tên bệnh nhân', width: 150},
                            {name: 'TEN_DICH_VU', label: 'Tên dịch vụ', width: 150},
                            {name: 'THOIGIANBD', label: 'Thời gian bắt đầu', width: 150, cellattr: function (cellvalue, options, rowObject) {
                                    return 'style="white-space: nowrap; white-space: normal;"';
                                }},
                            {name: 'THOIGIANKT', label: 'Thời gian kết quả', width: 150, cellattr: function (cellvalue, options, rowObject) {
                                    return 'style="white-space: nowrap; white-space: normal;"';
                                }
                            },
                            {name: 'GHICHU', label: 'Ghi chú', width: 200,
                                cellattr: function (cellvalue, options, rowObject) {
                                    return 'style="white-space: nowrap; white-space: normal;"';
                                }
                            },
                            {name: 'SOVAOVIEN', label: 'SOVAOVIEN', hidden: true},
                        ],
                    });
                }
            });
            return false;
        }
        return true;
    } catch (e) {
        jAlert("Red", "Lỗi kiểm tra dữ liệu")
        return true;
    }

}


function cmuKiemtratungThoigianCLSTheoNV(data) {
    try {
        var store = 'cmu_xemct_trungtgthuchien';
        if (data.loaikythuat == 'KTTTPT') {
            store = 'cmu_xemct_trungtgthuchien_ttpt'
        }
        var arr_params = [data.dvtt, data.sovaovien, data.userId, data.thoigianbd, data.thoigiankt, data.loaikythuat];
        var resCLS = $.ajax({
            url: "cmu_list_"+store+"?url=" + convertArray(arr_params),
            async: false
        }).responseText;
        var resCLSJSON = JSON.parse(resCLS);
        if (resCLSJSON && resCLSJSON.length > 0) {
            $.alert({
                title: 'Dữ liệu không hợp lệ!',
                content: '<table id="cmu_ngoaitru_kt_dulieu_tg" class="jqx-grid-cell-wrap"></table>',
                type: 'red',
                boxWidth: '900px',
                useBootstrap: false,
                escapeKey: true,
                closeIcon: true,
                typeAnimated: true,
                onContentReady: function () {
                    $("#cmu_ngoaitru_kt_dulieu_tg").jqGrid({
                        datatype: 'local',
                        //regional: 'en', // this is default
                        data: resCLSJSON,
                        rownumbers: true,
                        height: 400,
                        colModel: [
                            {name: 'MA_BN', label: 'Mã bệnh nhân', width: 60},
                            {name: 'TEN_BENH_NHAN', label: 'Tên bệnh nhân', width: 150},
                            {name: 'TEN_DICH_VU', label: 'Tên dịch vụ', width: 150},
                            {name: 'THOIGIANBD', label: 'Thời gian bắt đầu', width: 150, cellattr: function (cellvalue, options, rowObject) {
                                    return 'style="white-space: nowrap; white-space: normal;"';
                                }},
                            {name: 'THOIGIANKT', label: 'Thời gian kết quả', width: 150, cellattr: function (cellvalue, options, rowObject) {
                                    return 'style="white-space: nowrap; white-space: normal;"';
                                }
                            },
                            {name: 'GHICHU', label: 'Ghi chú', width: 200,
                                cellattr: function (cellvalue, options, rowObject) {
                                    return 'style="white-space: nowrap; white-space: normal;"';
                                }
                            },
                            {name: 'SOVAOVIEN', label: 'SOVAOVIEN', hidden: true},
                        ],
                    });
                }
            });
            return false;
        }
        return true;
    } catch (e) {
        jAlert("Red", "Lỗi kiểm tra dữ liệu")
        return true;
    }

}

