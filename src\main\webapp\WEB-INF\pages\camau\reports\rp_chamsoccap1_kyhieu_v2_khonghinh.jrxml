<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="phieugaymehoisuc" language="groovy" pageWidth="249" pageHeight="842" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="249" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="5f982810-2f9d-40df-b4a6-deec4a399123">
	<property name="ireport.zoom" value="2.1961500000000016"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="dvtt" class="java.lang.String"/>
	<parameter name="idphieu" class="java.lang.String"/>
	<parameter name="page" class="java.lang.String"/>
	<parameter name="lastpage" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call CMU_LCSC1_KYHIEU_PRO_V2($P{dvtt}, $P{idphieu}, $P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="KY_HIEU" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="15">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="249" height="15" uuid="8418f43d-4b27-4f20-a000-83f7bd4c14fb"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["(+): Có\n(-) : Không\n(/) : Không ghi nhận\nBT: Bình thường"]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="15" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="249" height="15" isPrintWhenDetailOverflows="true" uuid="11803292-6af9-4ef4-8892-dedb3c3d0321"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{KY_HIEU}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
