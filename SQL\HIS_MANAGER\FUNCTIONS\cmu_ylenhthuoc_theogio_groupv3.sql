create or replace FUNCTION HIS_MANAGER.cmu_ylenhthuoc_theogio_groupv3 (
    p_dvtt           VARCHAR2,
    p_id_dieutri     VARCHAR2,
    p_sovaovien      VARCHAR2,
    p_sovaovien_dt   VARCHAR2,
    p_mavattu        VARCHAR2,
    p_nghiepvu       VARCHAR2
) RETURN VARCHAR2 IS

    v_groupylenh              VARCHAR2(4000) := NULL;
    v_groupylenh_truyendich   VARCHAR2(4000) := NULL;
    v_result                  VARCHAR2(4000) := '';
BEGIN
SELECT
    concat(XMLCAST(XMLAGG(XMLELEMENT(
            e,('  + '
                || TO_CHAR(thoigianylenh, 'DD/MM/YYYY HH24:MI')
                || ' '
                || ghichu
                || '<br/>')
        )
        ORDER BY
                thoigianylenh
        ).extract('//text()') AS VARCHAR2(4000)), '')
INTO v_groupylenh
FROM
    cmu_ylenhthuoc_theogio   ylenh
        JOIN noitru_ct_toa_thuoc      thuoc ON thuoc.dvtt = ylenh.dvtt
        AND thuoc.sovaovien = ylenh.sovaovien
        AND thuoc.sovaovien_dt = ylenh.sovaovien_dt
        AND thuoc.id_dieutri = ylenh.id_dieutri
        AND thuoc.stt_toathuoc = ylenh.stt_toathuoc
        AND thuoc.mavattu = p_mavattu
        AND thuoc.sovaovien = p_sovaovien
        AND thuoc.sovaovien_dt = p_sovaovien_dt
        AND thuoc.mavattu = p_mavattu
        AND thuoc.id_dieutri = p_id_dieutri
        AND thuoc.nghiep_vu = p_nghiepvu
WHERE
        ylenh.dvtt = p_dvtt
  AND ylenh.id_dieutri = p_id_dieutri
  AND ylenh.sovaovien = p_sovaovien
  AND ( ylenh.sovaovien_dt = p_sovaovien_dt
    OR p_sovaovien_dt = '-1' )
  AND nvl(ylenh.loai_ylenh, 'thuoc') = 'thuoc';

SELECT
    concat(XMLCAST(XMLAGG(XMLELEMENT(
            e,('  + '
                || TO_CHAR(thoigianylenh, 'DD/MM/YYYY HH24:MI')
                || ' Số lượng truyền: '
                || soml_truyendich
                || ' ml'
                || '; Tốc độ: '
                || tocdo_truyendich
                || ' ('
                ||
               CASE
                   WHEN loai_tocdo_ylenh = 'ml/gio' THEN
                       'ml/giờ'
                   ELSE
                       'giọt/phút'
                   END
                || ')'
                || ' '
                || ghichu
                || '<br/>')
        )
        ORDER BY
                thoigianylenh
        ).extract('//text()') AS VARCHAR2(4000)), '')
INTO v_groupylenh_truyendich
FROM
    cmu_ylenhthuoc_theogio ylenh
        JOIN noitru_ct_toa_thuoc      thuoc ON thuoc.dvtt = ylenh.dvtt
        AND thuoc.sovaovien = ylenh.sovaovien
        AND thuoc.sovaovien_dt = ylenh.sovaovien_dt
        AND thuoc.id_dieutri = ylenh.id_dieutri
        AND thuoc.stt_toathuoc = ylenh.stt_toathuoc
        AND thuoc.mavattu = p_mavattu
        AND thuoc.sovaovien = p_sovaovien
        AND thuoc.sovaovien_dt = p_sovaovien_dt
        AND thuoc.mavattu = p_mavattu
        AND thuoc.id_dieutri = p_id_dieutri
        AND thuoc.nghiep_vu = p_nghiepvu
WHERE
        ylenh.dvtt = p_dvtt
  AND ylenh.id_dieutri = p_id_dieutri
  AND ylenh.sovaovien = p_sovaovien
  AND ( ylenh.sovaovien_dt = p_sovaovien_dt
    OR p_sovaovien_dt = '-1' )
  AND ylenh.loai_ylenh = 'truyendich';

IF v_groupylenh IS NOT NULL THEN
        v_result := ' Thời gian thực hiện: <br/>'
                    || v_groupylenh
                    || chr(10);
END IF;

    IF v_groupylenh_truyendich IS NOT NULL THEN
        v_result := v_result
                    || ' Thời gian thực hiện truyền dịch: <br/>'
                    || v_groupylenh_truyendich;
END IF;

RETURN v_result;
END;