create or replace FUNCTION          HIS_MANAGER.CMU_KHAMBENHTHEOYC_UPDATE(
    p_id                        IN NUMBER,
    p_dvtt                      IN VARCHAR2,
    p_daidien          IN VARCHAR2,
    p_tendaidien   IN VARCHAR2,
    p_tuoi           IN NUMBER,
    p_gioitinh           IN NUMBER,
    p_cmnd           IN VARCHAR,
    p_coquancap           IN VARCHAR,
    p_dantoc           IN VARCHAR,
    p_ngoaikieu           IN NUMBER,
    p_nghenghiep           IN VARCHAR,
    p_noilamviec           IN VARCHAR,
    p_diachi           IN VARCHAR,
    p_khicanbaotin           IN VARCHAR,
    p_sotienung           IN VARCHAR,
    p_sotienchu           IN VARCHAR,
    p_bacsicb           IN NUMBER,
    p_giamdocbv           IN NUMBER,
    p_ngaytao             IN VARCHAR2
)
RETURN NUMBER IS
    v_ngaytao   DATE := TO_DATE(p_ngaytao, 'dd/mm/yyyy');
BEGIN
UPDATE CMU_KHAMBENHTHEOYC
SET
    DAIDIEN = p_daidien,
    TENDAIDIEN = p_tendaidien,
    TUOI = p_tuoi,
    GIOI_TINH = p_gioitinh,
    CMND = p_cmnd,
    COQUANCAP = p_coquancap,
    DANTOC = p_dantoc,
    NGOAIKIEU = p_ngoaikieu,
    NGHENGHIEP = p_nghenghiep,
    NOILAMVIEC = p_noilamviec,
    DIACHI = p_diachi,
    KHICANBAOTIN = p_khicanbaotin,
    SOTIENUNG = p_sotienung,
    SOTIENBANGCHU = p_sotienchu,
    BACSICHUABENH = p_bacsicb,
    GIAMDOCBV = p_giamdocbv,
    NGAY_TAO_PHIEU = v_ngaytao
WHERE ID = p_id AND DVTT = p_dvtt;
RETURN 2;
END;