CREATE OR REPLACE
PROCEDURE "CMU_INPHIEUXETNGHIEM_V2" (p_dvtt           IN VARCHAR2,
                                                        p_sophieu        IN VARCHAR2,
                                                        p_noitru         IN VARCHAR2,
                                                        p_ma<PERSON><PERSON><PERSON>     IN VARCHAR2,
                                                        p_stt_<PERSON><PERSON>     IN VARCHAR2,
                                                        p_stt_dotdieutri IN VARCHAR2,
                                                        v_stt_dieutri    IN VARCHAR2,
                                                        p_loaixn         IN VARCHAR2,
                                                        cur              out SYS_REFCURSOR) IS
  p_tonghs        NUMBER(10) DEFAULT 0;
  p_tonghh        NUMBER(10) DEFAULT 0;
  p_ABO           VARCHAR2(500) DEFAULT '';
  p_Rh            VARCHAR2(500) DEFAULT '';
  p_tuoi          VARCHAR2(500) DEFAULT '';
  p_kq_ABO        VARCHAR2(500) DEFAULT '';
  p_kq_Rh         VARCHAR2(500) DEFAULT '';
  p_nguoichidinh  VARCHAR2(500) DEFAULT '';
  p_maulanggio1   VARCHAR2(500) DEFAULT '';
  p_maulanggio2   VARCHAR2(500) DEFAULT '';
  p_hematocrit    VARCHAR2(500) DEFAULT '';
  p_ngaylaymau    VARCHAR2(500) DEFAULT '';
  p_ngaychidinh   VARCHAR2(500) DEFAULT '';
  p_ngaytraketqua varchar2(500) default '';
  p_ngaysinh      DATE;
  p_ngayapdung    DATE;
  p_kq_mauchay    VARCHAR2(20) DEFAULT '';
  p_kq_maudong    VARCHAR2(20) DEFAULT ''; -- soc trang
  p_inphieuxn     VARCHAR2(20) DEFAULT '1';
  p_gioht         VARCHAR2(20) DEFAULT '';
  p_phutht        VARCHAR2(20) DEFAULT '';
  p_tuoiht        VARCHAR2(20) DEFAULT '0';
  p_ngaytiepnhan  date;
  p_binhthuong NUMBER(1) DEFAULT 1;
  p_ngayxnmax     date;
  --CMU 20/06/2017
  p_kq_ABO_binhthuong NUMBER(1) DEFAULT 1;
  p_kq_Rh_binhthuong NUMBER(1) DEFAULT 1;
  p_kq_mauchay_binhthuong NUMBER(1) DEFAULT 1;
  p_kq_maudong_binhthuong NUMBER(1) DEFAULT 1;
  v_thamsocmu 		VARCHAR2(20) DEFAULT '0';
  p_nguoithuchien 		VARCHAR2(255) DEFAULT '';
  v_thamcmulaytc varchar2(10):= HIS_MANAGER.DM_TSDV_SL_MTSO(p_dvtt,'96154');

  --END CMU 20/06/2017
  --p_noitru       NUMBER;
  -- VNPTHIS-4697 23/11/2017 thêm
  v_thamso_bacsidieutri int(2) default 0;
  -- VNPTHIS-4697 23/11/2017 thêm
	v_nguoilaymau VARCHAR2(500) DEFAULT '';
	p_stt_dieutri  VARCHAR2(500) DEFAULT '';
	v_dsxn VARCHAR2(1000) DEFAULT '';

    v_nguoilaymau_cmu VARCHAR2(500) DEFAULT '';
    v_nguoinhanmau_cmu VARCHAR2(500) DEFAULT '';
    v_benhpham_cmu VARCHAR2(500) DEFAULT '';
    v_solanin NUMBER(2) DEFAULT 1;
    v_ngthuchien_in VARCHAR2(500) DEFAULT '';
		v_ngaylaynhanmau VARCHAR2(500) DEFAULT '';
	v_thamso960616 					 number(10) := cmu_tsdv(p_dvtt, 960616, 0);
BEGIN
select
    nvl(REGEXP_SUBSTR(v_stt_dieutri,'[^--]+',1,1),' '),
    nvl(REGEXP_SUBSTR(v_stt_dieutri,'[^--]+',1,2),' ')
into p_stt_dieutri,v_dsxn
from dual;
begin
select mota_thamso into v_thamso_bacsidieutri
from his_fw.dm_thamso_donvi where dvtt= p_dvtt and ma_thamso = 94310;
exception when no_data_found then
      v_thamso_bacsidieutri := 0;
end;
  --p_noitru:= TO_NUMBER(v_noitru);
  /*SELECT mota_thamso
    INTO p_inphieuxn
    FROM his_fw.dm_thamso_donvi
   WHERE dvtt = p_dvtt
     AND ma_thamso = 59;*/
  p_gioht  := EXTRACT(HOUR FROM SYSTIMESTAMP);
  p_phutht := EXTRACT(MINUTE FROM SYSTIMESTAMP);

DELETE FROM cls_inketquaxetnghiem_cmu
WHERE dvtt = p_dvtt
  AND sophieu = p_sophieu
  AND noitru = p_noitru;
BEGIN
SELECT mota_thamso
INTO v_thamsocmu
FROM his_fw.dm_thamso_donvi
WHERE dvtt = p_dvtt
  AND ma_thamso = 96084;
EXCEPTION
		when no_data_found
			then v_thamsocmu:='0';
end;
  IF p_noitru = 0 THEN
    IF substr(p_dvtt,1,2) = '52' then
       BDH_NT_INPHIEUXN_THEOMAU_NT(p_dvtt,
                                  p_sophieu,
                                  p_noitru,
                                  p_makhambenh,
                                  p_loaixn);
else
       NGOAITRU_INPHIEUXN_THEOMAU_V2(p_dvtt,
                                  p_sophieu,
                                  p_noitru,
                                  p_makhambenh,
                                  v_dsxn,p_loaixn);
end if;

    if v_thamsocmu = '1' then
SELECT cdanh.mota_chucdanh || '. ' || nv.ten_nhanvien
INTO p_nguoichidinh
FROM his_manager.kb_cd_xet_nghiem cd,
     his_fw.dm_nhanvien           nv,
     his_fw.dm_chucdanh_nhanvien  cdanh
WHERE dvtt = p_dvtt
  AND SO_PHIEU_XN = p_sophieu
  AND MA_KHAM_BENH = p_makhambenh
  AND cd.BAC_SI_DIEU_TRI = nv.ma_nhanvien
  AND nv.chucdanh_nhanvien = cdanh.ma_chucdanh;

BEGIN
SELECT cdanh.mota_chucdanh || '. ' || nv.ten_nhanvien
INTO p_nguoithuchien
FROM his_manager.kb_cd_xet_nghiem cd,
     his_fw.dm_nhanvien           nv,
     his_fw.dm_chucdanh_nhanvien  cdanh
WHERE dvtt = p_dvtt
  AND SO_PHIEU_XN = p_sophieu
  AND MA_KHAM_BENH = p_makhambenh
  AND ( (p_dvtt = 96166 AND cd.NGUOI_THUC_HIEN = nv.ma_nhanvien) or (p_dvtt != 96166 and cd.NGUOI_THUC_HIEN = nv.ma_nhanvien))
  AND nv.chucdanh_nhanvien = cdanh.ma_chucdanh;
EXCEPTION
				WHEN no_data_found
					then p_nguoithuchien:= '';
END;

		--END CMU 20/10/2017
            --cmu 16/07/2019
BEGIN
SELECT cdanh.mota_chucdanh || '. ' || nv.ten_nhanvien
INTO v_nguoilaymau_cmu
FROM his_manager.KB_CD_XET_NGHIEM cd,
     his_fw.dm_nhanvien           nv,
     his_fw.dm_chucdanh_nhanvien  cdanh
WHERE dvtt = p_dvtt
  AND SO_PHIEU_XN = p_sophieu
  AND MA_KHAM_BENH = p_makhambenh
  --AND cd.NGUOI_LAY_MAU = nv.ma_nhanvien
  AND  NGUOI_NHAN_MAU = to_char(nv.ma_nhanvien)
  AND nv.chucdanh_nhanvien = cdanh.ma_chucdanh
  and ROWNUM = 1;
EXCEPTION
				WHEN no_data_found
					then v_nguoilaymau_cmu:= '';
END;

BEGIN
SELECT cdanh.mota_chucdanh || '. ' || nv.ten_nhanvien
INTO v_nguoinhanmau_cmu
FROM his_manager.KB_CD_XET_NGHIEM cd,
     his_fw.dm_nhanvien               nv,
     his_fw.dm_chucdanh_nhanvien      cdanh
WHERE dvtt = p_dvtt
  AND SO_PHIEU_XN = p_sophieu
  AND MA_KHAM_BENH = p_makhambenh
  AND cd.NGUOI_GIAO_MAU = nv.ma_nhanvien
  AND nv.chucdanh_nhanvien = cdanh.ma_chucdanh and ROWNUM = 1;
EXCEPTION
			when no_data_found
				THEN v_nguoinhanmau_cmu:='';
end;

BEGIN
SELECT to_char(NGAY_NHAN_MAU, 'DD/MM/YYYY HH24:MI') into v_ngaylaynhanmau

FROM his_manager.KB_CD_XET_NGHIEM cd
WHERE dvtt = p_dvtt
  AND SO_PHIEU_XN = p_sophieu
  AND MA_KHAM_BENH = p_makhambenh;
EXCEPTION
			when no_data_found
				THEN v_ngaylaynhanmau:='';
end;


            --end cmu 16/07/2019
BEGIN
SELECT MAX(CT.DAIN),TEN_NHANVIEN_CD INTO v_solanin,v_ngthuchien_in
FROM his_manager.KB_CD_XET_NGHIEM cd
         LEFT JOIN his_manager.KB_CD_XET_NGHIEM_CHI_TIET CT ON CT.DVTT=CD.DVTT AND CT.SOVAOVIEN=CD.SOVAOVIEN
         left join his_fw.DM_NHANVIEN_CD nv on nv.MA_NHANVIEN=ct.NGUOIXACNHAN
WHERE CD.dvtt = p_dvtt
  AND CD.SO_PHIEU_XN = p_sophieu
  AND CD.MA_KHAM_BENH = p_makhambenh
  AND CT.SO_PHIEU_XN=CD.SO_PHIEU_XN
  AND CT.CHISOCON=0
  and v_dsxn like '%' || MA_XET_NGHIEM || '%'
  AND ROWNUM = 1
group by ct.NGUOIXACNHAN,ct.DAIN,TEN_NHANVIEN_CD;
EXCEPTION
				WHEN no_data_found
					then v_solanin:=1;
END;
else
SELECT cdanh.mota_chucdanh || '. ' || nv.ten_nhanvien
INTO p_nguoichidinh
FROM his_manager.kb_cd_xet_nghiem cd,
     his_fw.dm_nhanvien           nv,
     his_fw.dm_chucdanh_nhanvien  cdanh
WHERE dvtt = p_dvtt
  AND SO_PHIEU_XN = p_sophieu
  AND MA_KHAM_BENH = p_makhambenh
  AND cd.NGUOI_CHI_DINH = nv.ma_nhanvien
  AND nv.chucdanh_nhanvien = cdanh.ma_chucdanh;
end if;

SELECT bn.NGAY_SINH, bn.TUOI, tn.ngay_tiep_nhan
INTO p_ngaysinh, p_tuoiht, p_ngaytiepnhan
FROM kb_kham_benh                 kb,
     his_public_list.dm_benh_nhan bn,
     kb_tiep_nhan                 tn
WHERE kb.DVTT = p_dvtt
  and tn.dvtt = p_dvtt
  and tn.sovaovien = kb.sovaovien
  AND kb.MA_KHAM_BENH = p_makhambenh
  AND kb.mabenhnhan = bn.MA_BENH_NHAN;

SELECT CASE
           WHEN p_tuoiht >= '6' THEN
               p_tuoiht
           ELSE
               hienthi_tuoi_benhnhan_2_2(p_ngaysinh, p_ngaytiepnhan)
           END
INTO p_tuoi
FROM dual;

IF p_inphieuxn = '1' OR p_inphieuxn = '3' OR substr(p_inphieuxn, 1, 2) = '93'THEN
SELECT case
           when (substr(P_DVTT, 1, 2) = '26') then
               'ngày' || ' ' || to_char(NGAY_LAY_MAU, 'DD') || ' ' || 'tháng' || ' ' ||
               to_char(NGAY_LAY_MAU, 'MM') || ' ' || 'năm' || ' ' ||
               to_char(NGAY_LAY_MAU, 'YYYY')
           else

               to_char(NGAY_LAY_MAU, 'DD/MM/YYYY HH24:MI:SS')
           end,
       case
           when (substr(P_DVTT, 1, 2) = '26') then
               'ngày' || ' ' || to_char(NGAY_CHI_DINH, 'DD') || ' ' || 'tháng' || ' ' ||
               to_char(NGAY_CHI_DINH, 'MM') || ' ' || 'năm' || ' ' ||
               to_char(NGAY_CHI_DINH, 'YYYY')
           else
               EXTRACT(HOUR FROM NGAY_CHI_DINH) || ' ' || ' ' || 'giờ' || ' ' ||
               EXTRACT(MINUTE FROM NGAY_CHI_DINH) || ' ' || 'phút' || ' ' || 'ngày' || ' ' ||
               to_char(NGAY_CHI_DINH, 'DD') || ' ' || 'tháng' || ' ' ||
               to_char(NGAY_CHI_DINH, 'MM') || ' ' || 'năm' || ' ' ||
               to_char(NGAY_CHI_DINH, 'YYYY')
           END,
       case when (substr(P_DVTT, 1, 2) = '26') then
                'ngày' || ' ' || to_char(NGAY_TRA_KETQUA, 'DD') || ' ' || 'tháng' || ' ' ||
                to_char(NGAY_TRA_KETQUA, 'MM') || ' ' || 'năm' || ' ' ||
                to_char(NGAY_TRA_KETQUA, 'YYYY')
            WHEN   (substr(P_DVTT, 1, 2) = '96') THEN
                (SELECT
                     TO_CHAR(MAX(NGAY_THUC_HIEN), 'HH24') || ' ' || ' ' || 'giờ' || ' ' ||
                     TO_CHAR(MAX(NGAY_THUC_HIEN), 'MI') || ' ' || 'phút' || ' ' || 'ngày' || ' ' ||
                     to_char(MAX(NGAY_THUC_HIEN), 'DD') || ' ' || 'tháng' || ' ' ||
                     to_char(MAX(NGAY_THUC_HIEN), 'MM') || ' ' || 'năm' || ' ' ||
                     to_char(MAX(NGAY_THUC_HIEN), 'YYYY')
                 FROM kb_cd_xet_nghiem_chi_tiet
                 WHERE dvtt=P_DVTT
                   AND MA_KHAM_BENH = p_makhambenh
                   AND SO_PHIEU_XN = p_sophieu
                   AND SOVAOVIEN = CD.SOVAOVIEN)
            else
                TO_CHAR(SYSDATE, 'HH24') || ' ' || ' ' || 'giờ' || ' ' ||
                TO_CHAR(SYSDATE, 'MI') || ' ' || 'phút' || ' ' || 'ngày' || ' ' ||
                to_char(SYSDATE, 'DD') || ' ' || 'tháng' || ' ' ||
                to_char(SYSDATE, 'MM') || ' ' || 'năm' || ' ' ||
                to_char(SYSDATE, 'YYYY')
           end
INTO p_ngaylaymau, p_ngaychidinh, p_ngaytraketqua
FROM kb_cd_xet_nghiem cd
WHERE dvtt = p_dvtt
  AND SO_PHIEU_XN = p_sophieu
  AND MA_KHAM_BENH = p_makhambenh;
--them cho quangnăm
BEGIN
select max(kb.ngay_thuc_hien)
into p_ngayxnmax
from
    kb_cd_xet_nghiem_chi_tiet kb,
    cls_xetnghiem cls,
    cls_loaixetnghiem loai
where
    kb.dvtt = p_dvtt
  and cls.dvtt = p_dvtt
  and loai.dvtt = p_dvtt
  and kb.ma_xet_nghiem = cls.ma_xetnghiem
  and cls.ma_loai_xetnghiem = loai.ma_loai_xetnghiem
  and kb.so_phieu_xn = p_sophieu
  and loai.mota_loai_xetnghiem = p_loaixn;
EXCEPTION
        WHEN NO_DATA_FOUND THEN
        p_ngayxnmax := null;
end;
       --end
ELSE
      IF p_inphieuxn = '2' THEN
        if (substr(P_DVTT, 1, 2) = '26') then
          p_ngaylaymau    := 'ngày.......tháng.......năm.......';
          p_ngaychidinh   := 'ngày.......tháng......năm.......';
          p_ngaytraketqua := 'ngày.......tháng......năm.......';
else
          p_ngaylaymau    := '.......phút.......ngày.......tháng.......năm.......';
          p_ngaychidinh   := '.......phút.......ngày.......tháng......năm.......';
          p_ngaytraketqua := '.......phút.......ngày.......tháng......năm.......';
end if;
END IF;
END IF;
ELSE

    if substr(p_dvtt,1,2) = '52' then
      bdh_ntru_inphieuXN_theomau_NT(p_dvtt,
                                  p_sophieu,
                                  p_noitru,
                                  p_stt_benhan,
                                  p_stt_dotdieutri,
                                  p_stt_dieutri,
                                  p_loaixn);
else
      NOITRU_INPHIEUXN_THEOMAU_NT_V2(p_dvtt,
                                  p_sophieu,
                                  p_noitru,
                                  p_stt_benhan,
                                  p_stt_dotdieutri,
                                  p_stt_dieutri,
                                  v_dsxn,p_loaixn);
end if;
    -- VNPTHIS-4697 thêm bác sĩ điều trị

BEGIN
SELECT MAX(CT.DAIN) ,TEN_NHANVIEN_CD INTO v_solanin,v_ngthuchien_in
FROM his_manager.NOITRU_CD_XET_NGHIEM cd
         LEFT JOIN his_manager.NOITRU_CD_XET_NGHIEM_CT CT ON CT.DVTT=CD.DVTT AND CT.SOVAOVIEN=CD.SOVAOVIEN
         left join his_fw.DM_NHANVIEN_CD nv on nv.MA_NHANVIEN=ct.NGUOIXACNHAN
WHERE CD.dvtt = p_dvtt
  AND CD.SO_PHIEU_XN = p_sophieu
  AND CD.stt_benhan = p_stt_benhan
  AND CD.stt_dotdieutri = p_stt_dotdieutri
  AND CD.stt_dieutri = p_stt_dieutri
  AND CT.SO_PHIEU_XN=CD.SO_PHIEU_XN
  AND CT.CHISOCON=0
  and v_dsxn like '%' || MA_XET_NGHIEM || '%'
  AND ROWNUM = 1
group by ct.NGUOIXACNHAN,ct.DAIN,TEN_NHANVIEN_CD;
EXCEPTION
				WHEN no_data_found
					then v_solanin:=1;
END;

BEGIN
SELECT cdanh.mota_chucdanh || '. ' || nv.ten_nhanvien
INTO v_nguoilaymau_cmu
FROM his_manager.NOITRU_CD_XET_NGHIEM cd,
     his_fw.dm_nhanvien               nv,
     his_fw.dm_chucdanh_nhanvien      cdanh
WHERE dvtt = p_dvtt
  AND SO_PHIEU_XN = p_sophieu
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri
  AND cd.NGUOI_NHAN_MAU = to_char(nv.ma_nhanvien)
  AND nv.chucdanh_nhanvien = cdanh.ma_chucdanh and ROWNUM = 1;
EXCEPTION
			when no_data_found
				THEN v_nguoilaymau_cmu:='';
end;

    if v_thamso_bacsidieutri = 1 then
SELECT cdanh.mota_chucdanh || '. ' || nv.ten_nhanvien
INTO p_nguoichidinh
FROM his_manager.noitru_cd_xet_nghiem cd,
     his_fw.dm_nhanvien               nv,
     his_fw.dm_chucdanh_nhanvien      cdanh
WHERE dvtt = p_dvtt
  AND SO_PHIEU_XN = p_sophieu
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri
  AND cd.bac_si_dieu_tri = nv.ma_nhanvien
  AND nv.chucdanh_nhanvien = cdanh.ma_chucdanh;
else
    -- VNPTHIS-4697 thêm bác sĩ điều trị
SELECT cdanh.mota_chucdanh || '. ' || nv.ten_nhanvien
INTO p_nguoichidinh
FROM his_manager.noitru_cd_xet_nghiem cd,
     his_fw.dm_nhanvien               nv,
     his_fw.dm_chucdanh_nhanvien      cdanh
WHERE dvtt = p_dvtt
  AND SO_PHIEU_XN = p_sophieu
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri
  AND cd.NGUOI_CHI_DINH = nv.ma_nhanvien
  AND nv.chucdanh_nhanvien = cdanh.ma_chucdanh;
end if; -- end VNPTHIS-4697 thêm bác sĩ điều trị
	--CMU 20/10/2017
	if v_thamsocmu = '1' then
SELECT cdanh.mota_chucdanh || '. ' || nv.ten_nhanvien
INTO p_nguoichidinh
FROM his_manager.noitru_cd_xet_nghiem cd,
     his_fw.dm_nhanvien               nv,
     his_fw.dm_chucdanh_nhanvien      cdanh
WHERE dvtt = p_dvtt
  AND SO_PHIEU_XN = p_sophieu
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri
  AND cd.BAC_SI_DIEU_TRI = nv.ma_nhanvien
  AND nv.chucdanh_nhanvien = cdanh.ma_chucdanh;
BEGIN
SELECT cdanh.mota_chucdanh || '. ' || nv.ten_nhanvien
INTO p_nguoithuchien
FROM his_manager.noitru_cd_xet_nghiem cd,
     his_fw.dm_nhanvien               nv,
     his_fw.dm_chucdanh_nhanvien      cdanh
WHERE dvtt = p_dvtt
  AND SO_PHIEU_XN = p_sophieu
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri
  --AND cd.KY_THUAT_VIEN = nv.ma_nhanvien
  AND cd.NGUOI_THUC_HIEN = nv.ma_nhanvien
  AND nv.chucdanh_nhanvien = cdanh.ma_chucdanh;
EXCEPTION
			when no_data_found
				THEN p_nguoithuchien:='';
end;

BEGIN
SELECT cdanh.mota_chucdanh || '. ' || nv.ten_nhanvien
INTO v_nguoinhanmau_cmu
FROM his_manager.NOITRU_CD_XET_NGHIEM cd,
     his_fw.dm_nhanvien               nv,
     his_fw.dm_chucdanh_nhanvien      cdanh
WHERE dvtt = p_dvtt
  AND SO_PHIEU_XN = p_sophieu
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri
  AND cd.NGUOI_GIAO_MAU = to_char(nv.ma_nhanvien)
  AND nv.chucdanh_nhanvien = cdanh.ma_chucdanh and ROWNUM = 1;
EXCEPTION
			when no_data_found
				THEN v_nguoinhanmau_cmu:='';
end;




end if;
	--END CMU 20/10/2017

BEGIN
SELECT to_char(NGAY_NHAN_MAU, 'DD/MM/YYYY HH24:MI') into v_ngaylaynhanmau

FROM his_manager.NOITRU_CD_XET_NGHIEM cd
WHERE dvtt = p_dvtt
  AND SO_PHIEU_XN = p_sophieu
  AND CD.stt_benhan = p_stt_benhan
  AND CD.stt_dotdieutri = p_stt_dotdieutri
  AND CD.stt_dieutri = p_stt_dieutri;
EXCEPTION
			when no_data_found
				THEN v_ngaylaynhanmau:='';
end;

SELECT bn.NGAY_SINH, bn.TUOI, tn.NGAYNHAPVIEN
INTO p_ngaysinh, p_tuoiht, p_ngaytiepnhan
FROM noitru_dotdieutri            kb,
     his_public_list.dm_benh_nhan bn,
     NOITRU_BENHAN                tn
WHERE kb.DVTT = p_dvtt
  and tn.dvtt = p_dvtt
  and tn.sovaovien = kb.sovaovien
  AND kb.stt_dotdieutri = p_stt_dotdieutri
  AND kb.stt_benhan = p_stt_benhan
  AND kb.mabenhnhan = bn.MA_BENH_NHAN;

SELECT CASE
           WHEN p_tuoiht >= 6 THEN
               p_tuoiht
           ELSE
               hienthi_tuoi_benhnhan_2_2(p_ngaysinh, p_ngaytiepnhan)
           END
INTO p_tuoi
FROM dual;

IF p_inphieuxn = '1' OR p_inphieuxn = '3' OR substr(p_inphieuxn, 1, 2) = '93' THEN
SELECT
    -- case when right(ngay_lay_mau,8) = '00:00:00' then Concat(_gioht,' gi? ',p_phutht,date_format(NGAY_LAY_MAU, ' Ng?y %d th?ng %m năm %Y')) else  date_format(NGAY_LAY_MAU, '%H gi? %i Ng?y %d th?ng %m năm %Y') end,  date_format(NGAY_CHI_DINH, '%H gi? %i Ng?y %d th?ng %m năm %Y')
    --TO_CHAR(NGAY_LAY_MAU, 'DD/MM/YYYY')
    --TO_CHAR(NGAY_CHI_DINH, 'DD/MM/YYYY'),
    --to_char(NGAY_LAY_MAU, 'HH24') || ' ' || 'gi?' || ' ' ||
    case
        when (substr(P_DVTT, 1, 2) = '26') then
            'ngày' || ' ' || to_char(NGAY_LAY_MAU, 'DD') || ' ' || 'tháng' || ' ' ||
            to_char(NGAY_LAY_MAU, 'MM') || ' ' || 'năm' || ' ' ||
            to_char(NGAY_LAY_MAU, 'YYYY')
        else
            to_char(NGAY_LAY_MAU, 'DD/MM/YYYY HH24:MI:SS')
        end,
    case
        when (substr(P_DVTT, 1, 2) = '26') then
            'ngày' || ' ' || to_char(NGAY_CHI_DINH, 'DD') || ' ' || 'tháng' || ' ' ||
            to_char(NGAY_CHI_DINH, 'MM') || ' ' || 'năm' || ' ' ||
            to_char(NGAY_CHI_DINH, 'YYYY')
        else
            to_char(NGAY_CHI_DINH, 'HH24') || ' ' || ' ' || 'giờ' || ' ' ||
            to_char(NGAY_CHI_DINH, 'MI') || ' ' || 'phút' || ' ' || 'ngày' || ' ' ||
            to_char(NGAY_CHI_DINH, 'DD') || ' ' || 'tháng' || ' ' ||
            to_char(NGAY_CHI_DINH, 'MM') || ' ' || 'năm' || ' ' ||
            to_char(NGAY_CHI_DINH, 'YYYY')
        end,
    case
        when (substr(P_DVTT, 1, 2) = '26') then
            'ngày' || ' ' || to_char(NGAY_TRA_KETQUA, 'DD') || ' ' || 'tháng' || ' ' ||
            to_char(NGAY_TRA_KETQUA, 'MM') || ' ' || 'năm' || ' ' ||
            to_char(NGAY_TRA_KETQUA, 'YYYY')
        WHEN   (substr(P_DVTT, 1, 2) = '96') THEN
            (SELECT
                 TO_CHAR(MAX(NGAY_THUC_HIEN), 'HH24') || ' ' || ' ' || 'giờ' || ' ' ||
                 TO_CHAR(MAX(NGAY_THUC_HIEN), 'MI') || ' ' || 'phút' || ' ' || 'ngày' || ' ' ||
                 to_char(MAX(NGAY_THUC_HIEN), 'DD') || ' ' || 'tháng' || ' ' ||
                 to_char(MAX(NGAY_THUC_HIEN), 'MM') || ' ' || 'năm' || ' ' ||
                 to_char(MAX(NGAY_THUC_HIEN), 'YYYY')
             FROM NOITRU_CD_XET_NGHIEM_CT
             WHERE dvtt=P_DVTT
               AND stt_benhan = p_stt_benhan
               AND stt_dotdieutri = p_stt_dotdieutri
               AND stt_dieutri = p_stt_dieutri
               AND SO_PHIEU_XN = p_sophieu
               AND SOVAOVIEN = CD.SOVAOVIEN
               AND SOVAOVIEN_DT = CD.SOVAOVIEN_DT)
        else
            TO_CHAR(SYSDATE, 'HH24') || ' ' || ' ' || 'giờ' || ' ' ||
            TO_CHAR(SYSDATE, 'MI') || ' ' || 'phút' || ' ' || 'ngày' || ' ' ||
            to_char(SYSDATE, 'DD') || ' ' || 'tháng' || ' ' ||
            to_char(SYSDATE, 'MM') || ' ' || 'năm' || ' ' ||
            to_char(SYSDATE, 'YYYY')
        end
INTO p_ngaylaymau, p_ngaychidinh, p_ngaytraketqua
FROM noitru_cd_xet_nghiem cd
WHERE dvtt = p_dvtt
  AND SO_PHIEU_XN = p_sophieu
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri;
--them cho quangnăm
BEGIN
select max(ct.ngay_thuc_hien)
into p_ngayxnmax
from
    noitru_cd_xet_nghiem_ct ct,
    cls_xetnghiem cls,
    cls_loaixetnghiem loai
where
    ct.dvtt = p_dvtt
  and cls.dvtt = p_dvtt
  and loai.dvtt = p_dvtt
  and ct.ma_xet_nghiem = cls.ma_xetnghiem
  and cls.ma_loai_xetnghiem = loai.ma_loai_xetnghiem
  and ct.so_phieu_xn = p_sophieu
  and loai.mota_loai_xetnghiem = p_loaixn;
EXCEPTION
        WHEN NO_DATA_FOUND THEN
        p_ngayxnmax := null;
end;
       --end
ELSE
      IF p_inphieuxn = '2' THEN
        if (substr(P_DVTT, 1, 2) = '26') then
          p_ngaylaymau    := 'ngày.......tháng.......năm.......';
          p_ngaychidinh   := 'ngày.......tháng......năm.......';
          p_ngaytraketqua := 'ngày.......tháng......năm.......';
else
          p_ngaylaymau    := '.......phút.......ngày.......tháng.......năm.......';
          p_ngaychidinh   := '.......phút.......ngày.......tháng......năm.......';
          p_ngaytraketqua := '.......phút.......ngày.......tháng......năm.......';
end if;
END IF;
END IF;
END IF;
insert into CLS_INKETQUAXETNGHIEM_CMU2 (dvtt,mota_loai_xetnghiem, SOPHIEU)
select p_dvtt,p_loaixn, p_sophieu from CLS_INKETQUAXETNGHIEM_CMU
where dvtt = p_dvtt
  AND sophieu = p_sophieu
  AND noitru = p_noitru
--and mota_loai_xetnghiem  = p_loaixn
;
if p_loaixn = 'TC' then
    open cur for
SELECT dvtt,
       sophieu,
       noitru,
       mota_loai_xetnghiem,
       ten_xetnghiem,
       dvnghiepvu_xetnghiem,
       chisobinhthuong,
       ket_qua,
       DVNGHIEPVU_XETNGHIEM as donvi,
       ket_luan_tong,
       v_nguoilaymau nguoi_lay_mau,
       p_ngaylaymau ngay_lay_mau,
       sapxep,
       nhom_xetnghiem,
       TT,
       p_ABO AS HH_ABO,
       p_Rh AS HH_Rh,
       p_nguoichidinh AS nguoi_chi_dinh,
       p_tuoi AS tuoi,
       p_kq_ABO AS kq_ABO,
       p_kq_Rh AS kq_Rh,
       p_kq_mauchay AS kq_mauchay,
       p_kq_maudong AS kq_maudong, -- soc trang th?m kq_maucha, kq_maudong
       p_ngaylaymau AS NGAYGIOLAYMAU,
       p_ngaychidinh AS NGAYCHIDINH,
       p_ngaytraketqua as NGAYTRAKETQUA,
       SUBSTR(p_ngaylaymau, 1, 9) AS giolaymau,
       binhthuong,--vlg them binhthuong, in dam khi ngoai min-max
       p_ngayxnmax as ngayxnmax,
       -- CMU 20/06/2016
       p_kq_ABO_binhthuong as kq_ABO_binhthuong,
       p_kq_Rh_binhthuong as kq_Rh_binhthuong,
       p_kq_mauchay_binhthuong as kq_mauchay_binhthuong,
       p_kq_maudong_binhthuong as kq_maudong_binhthuong,
       decode(p_dvtt,'96029',2,v_thamsocmu) as thamsocmu,
       case when p_dvtt = '96014' then '' else p_nguoithuchien end as NGUOI_THUC_HIEN	,
       --END CMU 20/06/2016
       --cmu 16/07/2019
       v_nguoilaymau_cmu nguoilaymau_cmu,
       v_nguoinhanmau_cmu nguoigiaomau_cmu,
       v_benhpham_cmu benhpham_cmu,
       v_solanin solanin,
       TEN_MAY_XN,
       CANHBAO_LAMSANG,
       v_ngthuchien_in ngthuchien_in,
       v_ngaylaynhanmau ngaylaynhanmau,
       v_thamso960616 ANCHUKY
FROM cls_inketquaxetnghiem_cmu a
WHERE dvtt = p_dvtt
  AND sophieu = p_sophieu
  AND noitru = p_noitru
  and mota_loai_xetnghiem NOT IN
      ('DM', 'HH', 'HHTK', 'HSNT', 'SH', 'HHDT', 'HHTK', 'VS', 'NDC') --KGG them 'VS'
--AND CASE WHEN p_loaixn = 'TC' THEN mota_loai_xetnghiem NOT IN ('DM','HH','HSNT','SH') ELSE mota_loai_xetnghiem := p_loaixn END
ORDER BY mota_loai_xetnghiem, sapxep;
----abc
else



    open cur for
SELECT dvtt,
       sophieu,
       noitru,
       mota_loai_xetnghiem,
       case when p_dvtt = 96177 and (ten_xetnghiem like '%WBC%' or ten_xetnghiem like '%RBC%' or ten_xetnghiem like '%PLT%') then '<b> ' || ten_xetnghiem || '</b>'
            else ten_xetnghiem end as ten_xetnghiem,
       dvnghiepvu_xetnghiem,
       chisobinhthuong,
       ket_qua,
       DVNGHIEPVU_XETNGHIEM as donvi,
       ket_luan_tong,
       v_nguoilaymau nguoi_lay_mau,
       p_ngaylaymau ngay_lay_mau,
       sapxep,
       nhom_xetnghiem,
       TT,
       p_ABO AS HH_ABO,
       p_Rh AS HH_Rh,
       p_nguoichidinh AS nguoi_chi_dinh,
       p_tuoi AS tuoi,
       p_kq_ABO AS kq_ABO,
       p_kq_Rh AS kq_Rh,
       p_kq_mauchay AS kq_mauchay,
       p_kq_maudong AS kq_maudong, -- soc trang th?m kq_maucha, kq_maudong
       p_ngaylaymau AS NGAYGIOLAYMAU,
       p_ngaytraketqua as NGAYTRAKETQUA,
       p_ngaychidinh AS NGAYCHIDINH,
       SUBSTR(p_ngaylaymau, 1, 9) AS giolaymau,
       binhthuong,--vlg them binhthuong, in dam khi ngoai min-max
       p_ngayxnmax as ngayxnmax,
       -- CMU 20/06/2016
       p_kq_ABO_binhthuong as kq_ABO_binhthuong,
       p_kq_Rh_binhthuong as kq_Rh_binhthuong,
       p_kq_mauchay_binhthuong as kq_mauchay_binhthuong,
       p_kq_maudong_binhthuong as kq_maudong_binhthuong,
       decode(p_dvtt,'96029',2,v_thamsocmu) as thamsocmu,
       case when p_dvtt = '96014' then '' else p_nguoithuchien end as NGUOI_THUC_HIEN	,
       --END CMU 20/06/2016
       --cmu 16/07/2019
       v_nguoilaymau_cmu nguoilaymau_cmu,
       v_nguoinhanmau_cmu nguoigiaomau_cmu,
       case when p_dvtt = '96029' then 'Máu' else v_benhpham_cmu end as benhpham_cmu,
       --v_benhpham_cmu benhpham_cmu,
       v_solanin solanin,
       TEN_MAY_XN,
       CANHBAO_LAMSANG,
       v_ngthuchien_in ngthuchien_in,
       v_ngaylaynhanmau ngaylaynhanmau,
       v_thamso960616 ANCHUKY
FROM cls_inketquaxetnghiem_cmu a
WHERE dvtt = p_dvtt
  AND sophieu = p_sophieu
  AND noitru = p_noitru
  and mota_loai_xetnghiem  = p_loaixn
--AND CASE WHEN p_loaixn = 'TC' THEN mota_loai_xetnghiem NOT IN ('DM','HH','HSNT','SH') ELSE mota_loai_xetnghiem := p_loaixn END
ORDER BY mota_loai_xetnghiem, sapxep;
end if;
END;
