/**
 * Test suite cho tính năng autocomplete "Lời dặn bác sĩ"
 * Chạy trong console của trình duyệt để kiểm tra các chức năng
 */

// Test object LoiDanBacSiSuggestions
function testLoiDanBacSiSuggestions() {
    console.log('=== BẮT ĐẦU TEST LOIDANBACSI SUGGESTIONS ===');
    
    // Test 1: Xóa dữ liệu cũ
    console.log('Test 1: Xóa dữ liệu cũ');
    LoiDanBacSiSuggestions.clearAll();
    var suggestions = LoiDanBacSiSuggestions.getSuggestions();
    console.assert(suggestions.length === 0, 'Dữ liệu chưa được xóa sạch');
    console.log('✓ Xóa dữ liệu thành công');
    
    // Test 2: Thêm gợi ý hợp lệ
    console.log('Test 2: Thêm gợi ý hợp lệ');
    LoiDanBacSiSuggestions.addSuggestion('Tái khám sau 1 tuần');
    suggestions = LoiDanBacSiSuggestions.getSuggestions();
    console.assert(suggestions.length === 1, 'Gợi ý không được thêm');
    console.assert(suggestions[0].text === 'Tái khám sau 1 tuần', 'Nội dung gợi ý không đúng');
    console.log('✓ Thêm gợi ý hợp lệ thành công');
    
    // Test 3: Không thêm gợi ý quá ngắn
    console.log('Test 3: Không thêm gợi ý quá ngắn');
    LoiDanBacSiSuggestions.addSuggestion('ab');
    suggestions = LoiDanBacSiSuggestions.getSuggestions();
    console.assert(suggestions.length === 1, 'Gợi ý ngắn được thêm vào');
    console.log('✓ Không thêm gợi ý quá ngắn');
    
    // Test 4: Không thêm gợi ý trùng lặp
    console.log('Test 4: Không thêm gợi ý trùng lặp');
    LoiDanBacSiSuggestions.addSuggestion('TÁI KHÁM SAU 1 TUẦN'); // Khác case
    suggestions = LoiDanBacSiSuggestions.getSuggestions();
    console.assert(suggestions.length === 1, 'Gợi ý trùng lặp được thêm vào');
    console.log('✓ Không thêm gợi ý trùng lặp');
    
    // Test 5: Thêm nhiều gợi ý
    console.log('Test 5: Thêm nhiều gợi ý');
    var testSuggestions = [
        'Uống thuốc đúng giờ',
        'Nghỉ ngơi đầy đủ',
        'Ăn nhạt, hạn chế muối',
        'Tập thể dục nhẹ nhàng',
        'Theo dõi huyết áp'
    ];
    
    testSuggestions.forEach(function(text) {
        LoiDanBacSiSuggestions.addSuggestion(text);
    });
    
    suggestions = LoiDanBacSiSuggestions.getSuggestions();
    console.assert(suggestions.length === 6, 'Số lượng gợi ý không đúng: ' + suggestions.length);
    console.log('✓ Thêm nhiều gợi ý thành công');
    
    // Test 6: Kiểm tra thứ tự (mới nhất trước)
    console.log('Test 6: Kiểm tra thứ tự gợi ý');
    console.assert(suggestions[0].text === 'Theo dõi huyết áp', 'Thứ tự gợi ý không đúng');
    console.log('✓ Thứ tự gợi ý đúng (mới nhất trước)');
    
    // Test 7: Kiểm tra giới hạn số lượng
    console.log('Test 7: Kiểm tra giới hạn số lượng');
    for (var i = 0; i < 25; i++) {
        LoiDanBacSiSuggestions.addSuggestion('Gợi ý số ' + i);
    }
    suggestions = LoiDanBacSiSuggestions.getSuggestions();
    console.assert(suggestions.length <= LoiDanBacSiSuggestions.maxSuggestions, 
                  'Vượt quá giới hạn: ' + suggestions.length);
    console.log('✓ Giới hạn số lượng hoạt động đúng');
    
    // Test 8: Tạo datalist
    console.log('Test 8: Tạo datalist');
    var datalistId = LoiDanBacSiSuggestions.createDatalist();
    var datalist = document.getElementById(datalistId);
    console.assert(datalist !== null, 'Datalist không được tạo');
    console.assert(datalist.children.length > 0, 'Datalist không có options');
    console.log('✓ Tạo datalist thành công');
    
    console.log('=== KẾT THÚC TEST - TẤT CẢ PASS ===');
    return true;
}

// Test tính năng lọc gợi ý
function testFilterSuggestions() {
    console.log('=== BẮT ĐẦU TEST LỌC GỢI Ý ===');
    
    // Chuẩn bị dữ liệu test
    LoiDanBacSiSuggestions.clearAll();
    var testData = [
        'Tái khám sau 1 tuần',
        'Uống thuốc đúng giờ',
        'Nghỉ ngơi đầy đủ',
        'Ăn nhạt, hạn chế muối',
        'Tập thể dục nhẹ nhàng'
    ];
    
    testData.forEach(function(text) {
        LoiDanBacSiSuggestions.addSuggestion(text);
    });
    
    // Test lọc theo từ khóa
    var suggestions = LoiDanBacSiSuggestions.getSuggestions();
    
    // Lọc theo "tái"
    var filtered = suggestions.filter(function(suggestion) {
        return suggestion.text.toLowerCase().indexOf('tái') !== -1;
    });
    console.assert(filtered.length === 1, 'Lọc "tái" không đúng');
    
    // Lọc theo "ăn"
    filtered = suggestions.filter(function(suggestion) {
        return suggestion.text.toLowerCase().indexOf('ăn') !== -1;
    });
    console.assert(filtered.length === 1, 'Lọc "ăn" không đúng');
    
    // Lọc theo "thuốc"
    filtered = suggestions.filter(function(suggestion) {
        return suggestion.text.toLowerCase().indexOf('thuốc') !== -1;
    });
    console.assert(filtered.length === 1, 'Lọc "thuốc" không đúng');
    
    console.log('✓ Tất cả test lọc gợi ý đều pass');
    console.log('=== KẾT THÚC TEST LỌC GỢI Ý ===');
    return true;
}

// Test localStorage
function testLocalStorage() {
    console.log('=== BẮT ĐẦU TEST LOCALSTORAGE ===');
    
    // Test lưu và đọc
    var testData = [
        { text: 'Test 1', timestamp: Date.now() },
        { text: 'Test 2', timestamp: Date.now() + 1000 }
    ];
    
    try {
        localStorage.setItem('test_suggestions', JSON.stringify(testData));
        var retrieved = JSON.parse(localStorage.getItem('test_suggestions'));
        
        console.assert(retrieved.length === 2, 'Dữ liệu không được lưu đúng');
        console.assert(retrieved[0].text === 'Test 1', 'Nội dung không đúng');
        
        // Xóa test data
        localStorage.removeItem('test_suggestions');
        
        console.log('✓ LocalStorage hoạt động bình thường');
    } catch (e) {
        console.error('LocalStorage có vấn đề:', e);
        return false;
    }
    
    console.log('=== KẾT THÚC TEST LOCALSTORAGE ===');
    return true;
}

// Test performance với dữ liệu lớn
function testPerformance() {
    console.log('=== BẮT ĐẦU TEST PERFORMANCE ===');
    
    // Tạo dữ liệu lớn
    LoiDanBacSiSuggestions.clearAll();
    
    var startTime = performance.now();
    
    // Thêm 1000 gợi ý
    for (var i = 0; i < 1000; i++) {
        LoiDanBacSiSuggestions.addSuggestion('Gợi ý performance test số ' + i + ' với nội dung dài hơn để test');
    }
    
    var addTime = performance.now() - startTime;
    console.log('Thời gian thêm 1000 gợi ý:', addTime.toFixed(2), 'ms');
    
    // Test đọc
    startTime = performance.now();
    var suggestions = LoiDanBacSiSuggestions.getSuggestions();
    var readTime = performance.now() - startTime;
    console.log('Thời gian đọc gợi ý:', readTime.toFixed(2), 'ms');
    
    // Test tạo datalist
    startTime = performance.now();
    LoiDanBacSiSuggestions.createDatalist();
    var createTime = performance.now() - startTime;
    console.log('Thời gian tạo datalist:', createTime.toFixed(2), 'ms');
    
    // Kiểm tra giới hạn
    console.assert(suggestions.length === LoiDanBacSiSuggestions.maxSuggestions, 
                  'Giới hạn không hoạt động với dữ liệu lớn');
    
    console.log('✓ Performance test hoàn thành');
    console.log('=== KẾT THÚC TEST PERFORMANCE ===');
    return true;
}

// Chạy tất cả tests
function runAllTests() {
    console.log('🚀 BẮT ĐẦU CHẠY TẤT CẢ TESTS');
    
    var results = {
        basic: false,
        filter: false,
        localStorage: false,
        performance: false
    };
    
    try {
        results.basic = testLoiDanBacSiSuggestions();
        results.filter = testFilterSuggestions();
        results.localStorage = testLocalStorage();
        results.performance = testPerformance();
        
        var allPassed = Object.values(results).every(function(result) { return result; });
        
        if (allPassed) {
            console.log('🎉 TẤT CẢ TESTS ĐỀU PASS!');
        } else {
            console.log('❌ CÓ TESTS FAIL:', results);
        }
        
        return results;
        
    } catch (error) {
        console.error('❌ LỖI KHI CHẠY TESTS:', error);
        return false;
    }
}

// Export cho sử dụng
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testLoiDanBacSiSuggestions,
        testFilterSuggestions,
        testLocalStorage,
        testPerformance,
        runAllTests
    };
}

// Tự động chạy test khi load file (nếu trong browser)
if (typeof window !== 'undefined') {
    console.log('📝 Sử dụng runAllTests() để chạy tất cả tests');
    console.log('📝 Hoặc chạy từng test riêng lẻ:');
    console.log('   - testLoiDanBacSiSuggestions()');
    console.log('   - testFilterSuggestions()');
    console.log('   - testLocalStorage()');
    console.log('   - testPerformance()');
}
