create or replace FUNCTION HIS_MANAGER."CMU_DSBN_DT_EMRV2"
(
    P_DVTT IN VARCHAR2,
    P_NGAY IN VARCHAR2,
    P_DATH IN NUMBER,
    P_<PERSON>H<PERSON><PERSON><PERSON> IN NUMBER,
    P_PHONGBENH IN NUMBER,
    P_DOITUONG IN NUMBER,
    P_TSHT IN NUMBER,
    P_LOAICDHA IN VARCHAR2,
    p_mienphi_bobat<PERSON><PERSON> in number DEFAULT  0,
    P_TUNGAY in VARCHAR2
) RETURN SYS_REFCURSOR IS
    CUR SYS_REFCURSOR;
    v_capcuu_khong_bhyt varchar2(50) default '';  --vlg
    v_thamso820123 varchar2(1) default '0';
    v_bobn_mienphi number(3) default 0;
    v_thamso8221839 varchar2(1) default '0';
    v_layicd_kdt  number(1) default 0;
    p_icdtaikhoa_hgi  number(3) default 0;
    p_icdtaikhoa_hgi1  number(3) default 0;
		p_thamso960598  number(3) default 0;
BEGIN

begin
select MOTA_THAMSO into v_layicd_kdt
from HIS_FW.dm_thamso_donvi where dvtt = p_dvtt and MA_THAMSO = '91063';
EXCEPTION when no_data_found THEN
    v_layicd_kdt := 0;
end;

begin
select MOTA_THAMSO into v_layicd_kdt
from HIS_FW.dm_thamso_donvi where dvtt = p_dvtt and MA_THAMSO = '960598';
EXCEPTION when no_data_found THEN
    p_thamso960598 := 0;
end;
begin
SELECT MOTA_THAMSO
INTO v_thamso8221839
FROM his_fw.DM_THAMSO_DONVI
WHERE DVTT = p_dvtt and MA_THAMSO = 8221839;
EXCEPTION
      WHEN no_data_found
        THEN v_thamso8221839:= '0';
end;

begin
select MOTA_THAMSO
into v_bobn_mienphi
from his_fw.dm_thamso_donvi
where dvtt = p_dvtt
  and ma_thamso = 8218086;
EXCEPTION
     WHEN NO_DATA_FOUND THEN
     v_bobn_mienphi:=0;
end;


  -- vlg
begin
SELECT MOTA_THAMSO
INTO v_capcuu_khong_bhyt
FROM his_fw.DM_THAMSO_DONVI
WHERE DVTT = p_dvtt and MA_THAMSO = 86993;
EXCEPTION
      WHEN no_data_found
        THEN v_capcuu_khong_bhyt:= '0';
end;
  -- end vlg
  --- tham số lấy tên bác sĩ diều trị hiển thị in phiếu
begin
SELECT MOTA_THAMSO
INTO v_thamso820123
FROM his_fw.DM_THAMSO_DONVI
WHERE DVTT = p_dvtt and MA_THAMSO = 820123;
EXCEPTION
      WHEN no_data_found
        THEN v_thamso820123:= '0';
end;

p_icdtaikhoa_hgi:=GET_TSDV(p_dvtt, 93031, '0');
OPEN CUR FOR
SELECT MABENHNHAN,TENBENHNHAN, TENBENHNHAN AS TENBENHNHAN_HT,TUOI,DIACHI,GIOITINH,SOTHEBHYT,NOITRU,SO_PHIEU,MA_KHAM_BENH,NGUOI_CHI_DINH,
       '' AS  BACSI_THUCHIEN,PHONG_CHI_DINH,MA_PHONG_CDHA,KET_QUA_CDHA,NGAY_CHI_DINH,STT_HANGNGAY,STT_BENHAN,STT_DOTDIEUTRI,
       STT_DIEUTRI,TENKHOA,BUONG,GIUONG,CHUANDOANICD,SOVAOVIEN,SOVAOVIEN_NOI,SOVAOVIEN_DT_NOI ,NGAYBATDAU,
       NGAYKETTHUC,NOIDK,NAMSINH, CAPCUU,SOPHIEUTHANHTOAN,TI_LE_MIEN_GIAM,NGAY_KB,DA_THANH_TOAN, CO_BHYT -- CMU: 27/10/2017
        , SOBENHAN, SOBENHAN_TT, ICD, NGAY_THUC_HIEN, NGUOI_THUC_HIEN, TEN_CDHA, MA_CDHA, CMU_XNGETKEYSIGN (
               p_dvtt,
               A.SOVAOVIEN_SIGN,
               A.SOVAOVIEN_DT_NOI,
               A.SO_PHIEU,
               'PHIEUKQ_DIENTIM'
           ) keysign
FROM
    (
        SELECT BN.MA_BENH_NHAN AS MABENHNHAN,
               BN.TEN_BENH_NHAN AS TENBENHNHAN,
               CASE WHEN BN.TUOI >= 6 THEN CAST(BN.TUOI AS VARCHAR2(4)) ELSE HIENTHI_TUOI_BENHNHAN(BN.NGAY_SINH) END AS TUOI,
               BN.DIA_CHI AS DIACHI,
               DECODE(BN.GIOI_TINH, 1, 'true', 'false') AS GIOITINH,
               TN.SO_THE_BHYT AS SOTHEBHYT,
               0 AS NOITRU,
               CD.SO_PHIEU_CDHA AS SO_PHIEU,
               CD.MA_KHAM_BENH ,
               CASE WHEN v_thamso820123 = '1' THEN CD.BAC_SI_DIEU_TRI ELSE CD.NGUOI_CHI_DINH END AS NGUOI_CHI_DINH,
               CT.BACSI_THUCHIEN ,
               CD.PHONG_CHI_DINH,
               CD.MA_PHONG_CDHA,
               CD.KET_QUA_CDHA,
               TO_CHAR(CD.NGAY_CHI_DINH,'DD/MM/YYYY HH24:MI:SS') AS NGAY_CHI_DINH,
               CD.STT_HANGNGAY,
               '' AS STT_BENHAN,
               '' AS STT_DOTDIEUTRI,
               '' AS STT_DIEUTRI,
               KHOA.TEN_PHONGBAN AS TENKHOA,
               '' AS BUONG,
               '' AS GIUONG,
               KB.ICD || ' - ' || KB.TEN_BENH_THEOBS || KB.BENH_PHU AS CHUANDOANICD,
               CD.SOVAOVIEN,0 AS SOVAOVIEN_NOI, 0 AS SOVAOVIEN_DT_NOI,
               TO_CHAR(TN.NGAY_BATDAU,'DD/MM/YYYY') AS NGAYBATDAU,
               TO_CHAR(TN.NGAY_HETHAN,'DD/MM/YYYY') AS NGAYKETTHUC,
               TN.NOIDANGKY_KCB AS NOIDK,
               TO_CHAR(BN.NGAY_SINH,'YYYY') AS NAMSINH, CD.CAPCUU,ptt.SOPHIEUTHANHTOAN,tn.ti_le_mien_giam,TO_CHAR(KB.NGAY_KB, 'YYYY-MM-DD') AS NGAY_KB,
               case when tn.MIENPHI = 1
                        then 1 else ct.DA_THANH_TOAN end DA_THANH_TOAN,
               cd.CO_BHYT -- CMU: 27/10/2017
                ,' ' SOBENHAN,' ' SOBENHAN_TT,KB.ICD as ICD,
               to_char(CD.NGAY_TAO,'DD/MM/YYYY') AS NGAY_THUC_HIEN,
               CT.NGUOI_THUC_HIEN,
               CDHA.TEN_CDHA,
               CDHA.MA_CDHA,
               CD.SOVAOVIEN SOVAOVIEN_SIGN
        FROM KB_CD_CDHA CD
                 inner join KB_CD_CDHA_CT CT on cd.dvtt = ct.dvtt and cd.sovaovien = ct.sovaovien and cd.so_phieu_cdha = ct.so_phieu_cdha
                 inner join KB_KHAM_BENH KB on cd.dvtt = kb.dvtt and cd.sovaovien = kb.sovaovien
                 inner join HIS_PUBLIC_LIST.DM_BENH_NHAN BN on cd.mabenhnhan = bn.ma_benh_nhan
                 inner join KB_TIEP_NHAN TN on cd.dvtt = tn.dvtt and cd.sovaovien = tn.sovaovien
                 inner join CLS_CDHA CDHA on ct.dvtt = cdha.dvtt and ct.ma_cdha = cdha.ma_cdha
                 inner join CLS_LOAICDHA LOAI on cdha.dvtt = loai.dvtt and cdha.ma_loai_cdha = loai.ma_loai_cdha
                 inner join DM_PHONG_BENH PHONG on cd.phong_chi_dinh = phong.ma_phong_benh
                 inner join HIS_FW.DM_PHONGBAN KHOA on phong.ma_phong_ban = khoa.ma_phongban
                 inner join kb_phieuthanhtoan ptt on cd.dvtt = ptt.dvtt and cd.sovaovien = ptt.sovaovien
            /*left join (--Ktra có cd dịch vụ ko?
                select ct1.dvtt, ct1.SOVAOVIEN, THANH_TIEN, BHYTKCHI, DA_THANH_TOAN
                from dm_dich_vu_kham dv
                     join kb_cd_dichvu_ct ct1 on dv.dvtt = ct1.dvtt and dv.ma_dv = ct1.ma_dv
                where dv.dvtt = p_dvtt
                    and dv.LOAI_DV = 'DV'
                    and ct1.THANH_TIEN > 0
                    and ct1.DA_THANH_TOAN = 0
            ) dvct on cd.dvtt = dvct.dvtt and cd.sovaovien = dvct.sovaovien*/
        WHERE CD.DVTT = P_DVTT
          AND KHOA.MA_DONVI = P_DVTT
          AND CD.NGAY_TAO BETWEEN TO_DATE(P_TUNGAY, 'yyyy-mm-dd') AND TO_DATE(P_NGAY, 'yyyy-mm-dd')
          --AND Ct.NGAY_TAO BETWEEN P_TUNGAY AND P_NGAY
          AND LOAI.MOTA_LOAI_CDHA = 'TDCN'
          AND (P_TSHT = 0 OR ((1 = DECODE(v_bobn_mienphi,1,DECODE(TN.CO_BAO_HIEM,0,nvl(MIENPHI,0),0),0)) OR (1=decode(p_mienphi_bobatdongtien,1,tn.MIENPHI,0))
            or (CT.BHYTKCHI = 0 OR CT.DA_THANH_TOAN = 1 OR CT.THANH_TIEN = 0
                or ( tn.capcuu = 1  and v_capcuu_khong_bhyt = '1')
                                  )))
          AND  NVL(CT.DA_CHAN_DOAN,0)=P_DATH
          AND (P_PHONGBAN = -1 OR KHOA.MA_PHONGBAN = P_PHONGBAN)
          AND (P_PHONGBENH = -1 OR PHONG.MA_PHONG_BENH = P_PHONGBENH)
          AND (P_LOAICDHA = '-1' OR CDHA.MOTA_CDHA = P_LOAICDHA)
          AND (P_DOITUONG = -1 OR DECODE(NVL(TN.TI_LE_MIEN_GIAM, 0), 0, 0, 1) = P_DOITUONG)
          and ct.daydulieu_vaonoitru IS NULL

        UNION ALL

        SELECT BN.MA_BENH_NHAN AS MABENHNHAN,
               BN.TEN_BENH_NHAN AS TENBENHNHAN,
               CASE WHEN BN.TUOI >= 6 THEN CAST(BN.TUOI AS VARCHAR2(4)) ELSE HIENTHI_TUOI_BENHNHAN(BN.NGAY_SINH) END AS TUOI,
               BN.DIA_CHI AS DIACHI,
               DECODE(BN.GIOI_TINH, 1, 'true', 'false') AS GIOITINH,
               DDT.SOBAOHIEMYTE AS SOTHEBHYT,
               1 AS NOITRU,
               CD.SO_PHIEU_CDHA AS SO_PHIEU,
               '' AS MA_KHAM_BENH ,
               CASE WHEN v_thamso820123 = '1' OR v_thamso8221839='1' THEN CD.BAC_SI_DIEU_TRI ELSE CD.NGUOI_CHI_DINH END AS NGUOI_CHI_DINH,
               CT.BACSI_THUCHIEN,
               CD.PHONG_CHI_DINH,
               CD.MA_PHONG_CDHA,
               CD.KET_QUA_CDHA,
               TO_CHAR(CD.NGAY_CHI_DINH,'DD/MM/YYYY HH24:MI:SS') AS NGAY_CHI_DINH,
               CD.STT_HANGNGAY,
               CT.STT_BENHAN,
               CT.STT_DOTDIEUTRI,
               CT.STT_DIEUTRI,
               KHOA.TEN_PHONGBAN AS TENKHOA,
               '' AS BUONG,
               '' AS GIUONG,
               case when v_layicd_kdt = 1 then
                            nvl(log_khoa.icd_khoadieutri,NTBA.icd_nhapvien) || '-' || nvl(log_khoa.tenicd_khoadieutri,NTBA.tenbenhchinh_nhapvien) || NTBA.tenbenhphu_nhapvien
                    else  NTBA.ICD_NHAPVIEN || ' - ' ||
                          NTBA.TENBENHCHINH_NHAPVIEN ||
                          NTBA.TENBENHPHU_NHAPVIEN end  CHUANDOANICD,
               0,CD.SOVAOVIEN, CD.SOVAOVIEN_DT,
               TO_CHAR(DDT.NGAYBATDAU_THEBHYT,'DD/MM/YYYY') AS NGAYBATDAU,
               TO_CHAR(DDT.NGAYHETHAN_THEBHYT,'DD/MM/YYYY') AS NGAYKETTHUC,
               DDT.NOIDANGKYBANDAU AS NOIDK,
               TO_CHAR(BN.NGAY_SINH,'YYYY') AS NAMSINH, CD.CAPCUU,' ' as SOPHIEUTHANHTOAN,ddt.tylebaohiem AS TI_LE_MIEN_GIAM,NULL AS NGAY_KB,
               ct.DA_THANH_TOAN, cd.CO_BHYT -- CMU: 27/10/2017
                ,NTBA.SOBENHAN,NTBA.SOBENHAN_TT,NTBA.ICD_NHAPVIEN as ICD,
               to_char(CD.NGAY_TAO,'DD/MM/YYYY') AS NGAY_THUC_HIEN,
               CT.NGUOI_THUC_HIEN,
               CDHA.TEN_CDHA || decode(CT.ghichu_tencdha, null, '', '[' || CT.ghichu_tencdha || ']') TEN_CDHA,
               CDHA.MA_CDHA,
               CD.SOVAOVIEN SOVAOVIEN_SIGN
        FROM NOITRU_CD_CDHA CD
                 /*LEFT JOIN (
                     SELECT *
                     FROM (
                         SELECT LGB.*,
                         rank() over (partition by LGB.dvtt, LGB.sovaovien, LGB.sovaovien_dt order by stt_logkhoaphong, stt_loggiuongbenh desc) rnk
                         FROM NOITRU_CD_CDHA CD
                             JOIN NOITRU_LOGGIUONGBENH LGB ON LGB.DVTT = CD.DVTT AND CD.SOVAOVIEN = LGB.SOVAOVIEN
                         WHERE CD.dvtt = p_dvtt
                             AND CD.NGAY_TAO BETWEEN P_TUNGAY AND P_NGAY
                     )
                     WHERE rnk = 1
                 ) LGB on cd.dvtt= lgb.dvtt and cd.sovaovien = lgb.sovaovien AND CD.SOVAOVIEN_DT = LGB.SOVAOVIEN_DT*/

                 --LEFT JOIN DM_GIUONGBENH GB ON (GB.MAGIUONGBENH=LGB.MAGIUONGBENH AND GB.DVTT=P_DVTT)
                 inner join NOITRU_CD_CDHA_CHI_TIET CT on cd.dvtt = ct.dvtt and cd.sovaovien = ct.sovaovien and cd.sovaovien_dt = ct.sovaovien_dt and cd.so_phieu_cdha = ct.so_phieu_cdha
                 left join noitru_logkhoaphong log_khoa on log_khoa.dvtt = ct.dvtt and log_khoa.sovaovien = ct.sovaovien and log_khoa.sovaovien_dt = ct.sovaovien_dt
                 inner join HIS_PUBLIC_LIST.DM_BENH_NHAN BN on cd.mabenhnhan = bn.ma_benh_nhan
                 inner join CLS_CDHA CDHA on ct.dvtt = cdha.dvtt and ct.ma_cdha = cdha.ma_cdha
                 inner join CLS_LOAICDHA LOAI on cdha.dvtt = loai.dvtt and cdha.ma_loai_cdha = loai.ma_loai_cdha
                 inner join NOITRU_BENHAN NTBA on cd.dvtt = ntba.dvtt and cd.sovaovien = ntba.sovaovien
                 inner join NOITRU_DOTDIEUTRI DDT on cd.dvtt = ddt.dvtt and cd.sovaovien = ddt.sovaovien and cd.sovaovien_dt = ddt.sovaovien_dt
                 inner join DM_PHONG_BENH PHONG on cd.phong_chi_dinh = phong.ma_phong_benh
                 inner join HIS_FW.DM_PHONGBAN KHOA on phong.ma_phong_ban = khoa.ma_phongban
        WHERE  CD.DVTT = P_DVTT
          AND KHOA.MA_DONVI = P_DVTT
          AND CD.NGAY_TAO BETWEEN TO_DATE(P_TUNGAY, 'yyyy-mm-dd') AND TO_DATE(P_NGAY, 'yyyy-mm-dd')
          --AND Ct.NGAY_TAO BETWEEN P_TUNGAY AND P_NGAY
          AND LOAI.MOTA_LOAI_CDHA = 'TDCN'
          AND  NVL(CT.DA_CHAN_DOAN,0)=P_DATH
          AND (P_PHONGBAN = -1 OR KHOA.MA_PHONGBAN = P_PHONGBAN)
          AND (P_PHONGBENH = -1 OR PHONG.MA_PHONG_BENH = P_PHONGBENH)
          AND (P_LOAICDHA = '-1' OR CDHA.MOTA_CDHA = P_LOAICDHA)
          AND (P_DOITUONG = -1 OR DDT.COBHYT = P_DOITUONG)
    ) A
GROUP BY MABENHNHAN,TENBENHNHAN,TUOI,DIACHI,GIOITINH,SOTHEBHYT,NOITRU,SO_PHIEU,MA_KHAM_BENH,NGUOI_CHI_DINH,
         PHONG_CHI_DINH,MA_PHONG_CDHA,KET_QUA_CDHA,NGAY_CHI_DINH,STT_HANGNGAY,STT_BENHAN,STT_DOTDIEUTRI,
         STT_DIEUTRI,TENKHOA,BUONG,GIUONG,CHUANDOANICD,SOVAOVIEN,SOVAOVIEN_NOI,SOVAOVIEN_DT_NOI,NGAYBATDAU,
         NGAYKETTHUC,NOIDK,NAMSINH, CAPCUU,SOPHIEUTHANHTOAN,TI_LE_MIEN_GIAM,NGAY_KB,DA_THANH_TOAN, CO_BHYT -- CMU: 27/10/2017
        , SOBENHAN, SOBENHAN_TT, ICD, NGAY_THUC_HIEN, NGUOI_THUC_HIEN, TEN_CDHA, MA_CDHA, SOVAOVIEN_SIGN,
         CMU_XNGETKEYSIGN (
                 p_dvtt,
                 A.SOVAOVIEN_SIGN,
                 A.SOVAOVIEN_DT_NOI,
                 A.SO_PHIEU,
                 'PHIEUKQ_DIENTIM'
             )
ORDER BY STT_HANGNGAY;

RETURN CUR;

END;