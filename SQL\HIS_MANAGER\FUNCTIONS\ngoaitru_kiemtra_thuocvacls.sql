CREATE OR REPLACE FUNCTION ngoaitru_kiemtra_thuocvacls (
    p_dvtt        VARCHAR2,
    p_sovaovien   VARCHAR2,
    p_userid      VARCHAR2
) RETURN SYS_REFCURSOR IS

    cur              SYS_REFCURSOR;
    v_thamso960584   VARCHAR2(255) := cmu_tsdv(p_dvtt, 960584, '0');
    v_cobhyt         NUMBER := 0;
    v_ngaythuoc      DATE;
    v_ngayvao        DATE;
    p_ngayra         VARCHAR2(255) := TO_CHAR(SYSDATE, 'DD/MM/YYYY HH24:MI');
    v_ngayvao_text   VARCHAR2(255);
BEGIN
    IF v_thamso960584 = '0' THEN
        OPEN cur FOR WITH tnpb AS (
                         SELECT
                             tnpb.sovaovien,
                             tnpb.nhan_vien_kham_benh,
                             MAX(thoi_gian_kham_benh) thoi_gian_kham_benh
                         FROM
                             tiep_nhan_phong_benh       tnpb
                             LEFT JOIN kb_chuyenpk_thutien_tt37   tt37 ON tnpb.sovaovien = tt37.sovaovien
                                                                        AND tt37.sovaovien = p_sovaovien
                                                                        AND tt37.dvtt = p_dvtt
                                                                        AND tnpb.ma_phong_benh = tt37.ma_phong_benh
                                                                        AND tt37.so_tiep_nhan_pb = tnpb.so_tiep_nhan_pb
                         WHERE
                             tnpb.sovaovien = p_sovaovien
                             AND tnpb.dvtt = p_dvtt
                         GROUP BY
                             tnpb.sovaovien,
                             tnpb.nhan_vien_kham_benh
                     )
SELECT
    tt.mabenhnhan,
    TO_CHAR(tt.ngay_ra_toa, 'DD/MM/YYYY HH24:MI') thoigian,
    tt.sovaovien,
    tt.ten_vat_tu sophieu,
    'Y lệnh thuốc trước thời gian khám bệnh, thời gian khám bệnh: '
        || TO_CHAR(tnpb.thoi_gian_kham_benh, 'DD/MM/YYYY HH24:MI') ghi_chu
FROM
    kb_chi_tiet_toa_thuoc          tt
        JOIN tnpb ON tt.sovaovien = tnpb.sovaovien
        AND tt.ma_bac_si_themthuoc = tnpb.nhan_vien_kham_benh
        AND tnpb.thoi_gian_kham_benh > tt.ngay_ra_toa
        INNER JOIN his_public_list.dm_benh_nhan   bn ON tt.mabenhnhan = bn.ma_benh_nhan
WHERE
    tt.sovaovien = p_sovaovien
  AND tt.dvtt = p_dvtt
  AND tt.nghiep_vu IN (
                       'ngoaitru_toathuoc',
                       'ngoaitru_toavattu'
    )
UNION ALL
SELECT
    ct.mabenhnhan,
    TO_CHAR(nvl(ct.ngay_gio_pttt_kt, ct.ngay_gio_pttt), 'DD/MM/YYYY HH24:MI') ngay_ket_qua,
    ct.sovaovien,
    ct.so_phieu_dichvu sophieu,
    CASE
        WHEN ct.ngay_gio_pttt_kt IS NULL
            AND ct.ngay_gio_pttt IS NULL THEN
            'Chưa có kết quả'
        WHEN dv.loai_dv IN (
                            'TT',
                            'PT',
                            'VLTL'
            )
            AND nvl(ct.pp_vo_cam, 0) NOT IN (
                                             1,
                                             2,
                                             3,
                                             4
                ) THEN
            'Phương pháp vô cảm không được trống'
        END
        ||
    CASE
        WHEN ct.ngay_gio_pttt <= v_ngayvao THEN
            'Ngày thực hiện trước hoặc bằng ngày vào viện '
                || v_ngayvao_text
                || ', ngày thực hiện: '
                || TO_CHAR(ct.ngay_gio_pttt, 'DD/MM/YYYY HH24:MI')
                || ' ; '
        END
        ||
    CASE
        WHEN ct.ngay_gio_pttt_kt <= v_ngayvao THEN
            'Ngày kết quả trước hoặc bằng ngày vào viện '
                || v_ngayvao_text
                || ', ngày kết quả: '
                || TO_CHAR(ct.ngay_gio_pttt_kt, 'DD/MM/YYYY HH24:MI')
                || ' ; '
        END
        ||
    CASE
        WHEN ct.ngay_gio_pttt >= SYSDATE
            AND p_dvtt != 96155 THEN
            'Ngày thực hiện lớn hơn hoặc bằng ngày ra viện '
                || p_ngayra
                || ', ngày thực hiện: '
                || TO_CHAR(ct.ngay_gio_pttt, 'DD/MM/YYYY HH24:MI')
                || ' ; '
        END
        ||
    CASE
        WHEN ct.ngay_gio_pttt_kt >= SYSDATE
            AND p_dvtt != 96155 THEN
            'Ngày kết quả lớn hơn hoặc bằng ngày ra viện '
                || p_ngayra
                || ', ngày kết quả: '
                || TO_CHAR(ct.ngay_gio_pttt_kt, 'DD/MM/YYYY HH24:MI')
                || ' ; '
        END
        ||
    CASE
        WHEN trunc(ct.ngay_gio_pttt, 'MI') <= trunc(ct.ngay_chi_dinh_ct, 'MI') THEN
            'Ngày thực hiện nhỏ hơn hoặc bằng ngày chỉ định '
                || TO_CHAR(ct.ngay_chi_dinh_ct, 'DD/MM/YYYY HH24:MI')
                || ', ngày thực hiện: '
                || TO_CHAR(ct.ngay_gio_pttt, 'DD/MM/YYYY HH24:MI')
                || ' ; '
        END
                                                                              ghichu
FROM
    kb_cd_dichvu_ct   ct
        JOIN kb_cd_dichvu      cd ON ct.sovaovien = cd.sovaovien
        AND ct.so_phieu_dichvu = cd.so_phieu_dichvu
        AND cd.dvtt = p_dvtt
        AND cd.sovaovien = p_sovaovien
        JOIN dm_dich_vu_kham   dv ON ct.dvtt = dv.dvtt
        AND ct.ma_dv = dv.ma_dv
        AND dv.boquaktra = 0
WHERE
    ct.sovaovien = p_sovaovien
  AND ct.dvtt = p_dvtt
  AND ( ( ct.ngay_gio_pttt_kt IS NULL
    AND ct.ngay_gio_pttt IS NULL )
    OR ( dv.loai_dv IN (
                        'TT',
                        'PT',
                        'VLTL'
        )
             AND nvl(ct.pp_vo_cam, 0) NOT IN (
                                              1,
                                              2,
                                              3,
                                              4
            )
        OR trunc(ct.ngay_gio_pttt, 'MI') <= trunc(ct.ngay_chi_dinh_ct, 'MI')
        OR ct.ngay_gio_pttt_kt <= v_ngayvao
        OR ct.ngay_gio_pttt <= v_ngayvao
        OR ( ct.ngay_gio_pttt >= SYSDATE
            AND p_dvtt != 96155 )
        OR ( ct.ngay_gio_pttt_kt >= SYSDATE
            AND p_dvtt != 96155 ) ) )
  AND ct.bhytkchi = 0
UNION ALL
SELECT
    ct.mabenhnhan,
    TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI') ngay_ket_qua,
    ct.sovaovien,
    ct.so_phieu_xn sophieu,
    CASE
        WHEN ct.ngay_thuc_hien IS NULL THEN
            'Chưa có kết quả'
        END
        ||
    CASE
        WHEN ct.ngay_thuc_hien IS NULL THEN
            'Ngày kết quả rỗng; '
        END
        ||
    CASE
        WHEN ct.ngay_th_yl IS NULL THEN
            'Ngày thực hiện y lệnh rỗng; '
        END
        ||
    CASE
        WHEN ct.ngay_th_yl <= v_ngayvao THEN
            'Ngày thực hiện trước hoặc bằng ngày vào viện '
                || v_ngayvao_text
                || ', ngày thực hiện: '
                || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                || ' ; '
        END
        ||
    CASE
        WHEN ct.ngay_thuc_hien <= v_ngayvao THEN
            'Ngày kết quả trước hoặc bằng ngày vào viện '
                || v_ngayvao_text
                || ', ngày kết quả: '
                || TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI')
                || ' ; '
        END
        ||
    CASE
        WHEN ct.ngay_th_yl >= SYSDATE
            AND p_dvtt != 96155 THEN
            'Ngày thực hiện lớn hơn hoặc bằng ngày ra viện '
                || p_ngayra
                || ', ngày thực hiện: '
                || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                || ' ; '
        END
        ||
    CASE
        WHEN ct.ngay_thuc_hien >= SYSDATE
            AND p_dvtt != 96155 THEN
            'Ngày kết quả lớn hơn hoặc bằng ngày ra viện '
                || p_ngayra
                || ', ngày kết quả: '
                || TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI')
                || ' ; '
        END
        ||
    CASE
        WHEN trunc(ct.ngay_th_yl, 'MI') <= trunc(ct.ngay_chi_dinh_ct, 'MI') THEN
            'Ngày thực hiện nhỏ hơn hoặc bằng ngày chỉ định '
                || TO_CHAR(ct.ngay_chi_dinh_ct, 'DD/MM/YYYY HH24:MI')
                || ', ngày thực hiện: '
                || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                || ' ; '
        END
                                                     ghichu
FROM
    kb_cd_xet_nghiem_chi_tiet   ct
        JOIN kb_cd_xet_nghiem            cd ON ct.sovaovien = ct.sovaovien
        AND ct.so_phieu_xn = cd.so_phieu_xn
        AND cd.dvtt = p_dvtt
        AND cd.sovaovien = p_sovaovien
WHERE
    ct.sovaovien = p_sovaovien
  AND ct.dvtt = p_dvtt
  AND ( ct.da_xet_nghiem = 0
    OR trunc(ct.ngay_th_yl, 'MI') <= trunc(ct.ngay_chi_dinh_ct, 'MI')
    OR ct.ngay_th_yl IS NULL
    OR ct.ngay_thuc_hien IS NULL
    OR ct.ngay_thuc_hien <= v_ngayvao
    OR ct.ngay_th_yl <= v_ngayvao
    OR ( ct.ngay_th_yl >= SYSDATE
        AND p_dvtt != 96155 )
    OR ( ct.ngay_thuc_hien >= SYSDATE
        AND p_dvtt != 96155 ) )
  AND ct.bhytkchi = 0
UNION ALL
SELECT
    ct.mabenhnhan,
    TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI') thoigian,
    ct.sovaovien,
    ct.so_phieu_cdha sophieu,
    CASE
        WHEN ct.ngay_thuc_hien IS NULL THEN
            'Chưa có kết quả'
        END
        ||
    CASE
        WHEN ct.ngay_thuc_hien IS NULL THEN
            'Ngày kết quả rỗng; '
        END
        ||
    CASE
        WHEN ct.ngay_th_yl IS NULL THEN
            'Ngày thực hiện y lệnh rỗng; '
        END
        ||
    CASE
        WHEN ct.ngay_th_yl <= v_ngayvao THEN
            'Ngày thực hiện trước hoặc bằng ngày vào viện '
                || v_ngayvao_text
                || ', ngày thực hiện: '
                || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                || ' ; '
        END
        ||
    CASE
        WHEN ct.ngay_thuc_hien <= v_ngayvao THEN
            'Ngày kết quả trước hoặc bằng ngày vào viện '
                || v_ngayvao_text
                || ', ngày kết quả: '
                || TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI')
                || ' ; '
        END
        ||
    CASE
        WHEN ct.ngay_th_yl >= SYSDATE
            AND p_dvtt != 96155 THEN
            'Ngày thực hiện lớn hơn hoặc bằng ngày ra viện '
                || p_ngayra
                || ', ngày thực hiện: '
                || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                || ' ; '
        END
        ||
    CASE
        WHEN ct.ngay_thuc_hien >= SYSDATE
            AND p_dvtt != 96155 THEN
            'Ngày kết quả lớn hơn hoặc bằng ngày ra viện '
                || p_ngayra
                || ', ngày kết quả: '
                || TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI')
                || ' ; '
        END
        ||
    CASE
        WHEN trunc(ct.ngay_th_yl, 'MI') <= trunc(ct.ngay_chi_dinh_ct, 'MI') THEN
            'Ngày thực hiện nhỏ hơn hoặc bằng ngày chỉ định '
                || TO_CHAR(ct.ngay_chi_dinh_ct, 'DD/MM/YYYY HH24:MI')
                || ', ngày thực hiện: '
                || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                || ' ; '
        END
                                                     ghichu
FROM
    kb_cd_cdha_ct   ct
        JOIN kb_cd_cdha      cd ON ct.sovaovien = cd.sovaovien
        AND ct.so_phieu_cdha = cd.so_phieu_cdha
        AND cd.dvtt = p_dvtt
        AND cd.sovaovien = p_sovaovien
WHERE
    ct.sovaovien = p_sovaovien
  AND ct.dvtt = p_dvtt
  AND ( ct.ngay_thuc_hien IS NULL
    OR trunc(ct.ngay_th_yl, 'MI') <= trunc(ct.ngay_chi_dinh_ct, 'MI')
    OR ct.ngay_th_yl IS NULL
    OR ct.ngay_thuc_hien IS NULL
    OR ct.ngay_thuc_hien <= v_ngayvao
    OR ct.ngay_th_yl <= v_ngayvao
    OR ( ct.ngay_th_yl >= SYSDATE
        AND p_dvtt != 96155 )
    OR ( ct.ngay_thuc_hien >= SYSDATE
        AND p_dvtt != 96155 ) )
  AND ct.bhytkchi = 0;

RETURN cur;
END IF;

SELECT
    MIN(ngay_ra_toa)
INTO v_ngaythuoc
FROM
    kb_chi_tiet_toa_thuoc
WHERE
    sovaovien = p_sovaovien
  AND dvtt = p_dvtt
  AND nghiep_vu = 'ngoaitru_toathuoc'
  AND ma_bac_si_themthuoc = p_userid;

SELECT
    trunc(thoi_gian_tiep_nhan, 'MI')
INTO v_ngayvao
FROM
    kb_tiep_nhan
WHERE
    sovaovien = p_sovaovien
  AND dvtt = p_dvtt;

v_ngayvao_text := TO_CHAR(v_ngayvao, 'DD/MM/YYYY HH24:MI');
OPEN cur FOR SELECT DISTINCT
                     a.mabenhnhan,
                     a.thoigian,
                     a.sovaovien,
                     a.sophieu,
                     bn.ten_benh_nhan,
                     a.ghichu ghi_chu
                 FROM
                     (
                         WITH tnpb AS (
                             SELECT
                                 tnpb.sovaovien,
                                 tnpb.nhan_vien_kham_benh,
                                 MAX(thoi_gian_kham_benh) thoi_gian_kham_benh
                             FROM
                                 tiep_nhan_phong_benh       tnpb
                                 LEFT JOIN kb_chuyenpk_thutien_tt37   tt37 ON tnpb.sovaovien = tt37.sovaovien
                                                                            AND tt37.sovaovien = p_sovaovien
                                                                            AND tt37.dvtt = p_dvtt
                                                                            AND tnpb.ma_phong_benh = tt37.ma_phong_benh
                                                                            AND tt37.so_tiep_nhan_pb = tnpb.so_tiep_nhan_pb
                             WHERE
                                 tnpb.sovaovien = p_sovaovien
                                 AND tnpb.dvtt = p_dvtt
                             GROUP BY
                                 tnpb.sovaovien,
                                 tnpb.nhan_vien_kham_benh
                         )
                         SELECT
                             ct.mabenhnhan,
                             TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI') thoigian,
                             ct.sovaovien,
                             ct.so_phieu_cdha sophieu,
                             CASE
                                     WHEN ct.ngay_thuc_hien IS NULL THEN
                                         'Chưa có kết quả'
                                     ELSE
                                         'Lớn hơn giờ ra toa, ngày giờ ra toa: ' || TO_DATE(v_ngaythuoc, 'DD/MM/YYYY HH24:MI')
                                 END
                             ||
                                 CASE
                                     WHEN ct.ngay_thuc_hien IS NULL THEN
                                         'Ngày kết quả rỗng; '
                                 END
                             ||
                                 CASE
                                     WHEN ct.ngay_th_yl IS NULL THEN
                                         'Ngày thực hiện y lệnh rỗng; '
                                 END
                             ||
                                 CASE
                                     WHEN ct.ngay_th_yl <= v_ngayvao THEN
                                         'Ngày thực hiện trước hoặc bằng ngày vào viện '
                                         || v_ngayvao_text
                                         || ', ngày thực hiện: '
                                         || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                                         || ' ; '
                                 END
                             ||
                                 CASE
                                     WHEN ct.ngay_thuc_hien <= v_ngayvao THEN
                                         'Ngày kết quả trước hoặc bằng ngày vào viện '
                                         || v_ngayvao_text
                                         || ', ngày kết quả: '
                                         || TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI')
                                         || ' ; '
                                 END
                             ||
                                 CASE
                                     WHEN ct.ngay_th_yl >= SYSDATE THEN
                                         'Ngày thực hiện lớn hơn hoặc bằng ngày ra viện '
                                         || p_ngayra
                                         || ', ngày thực hiện: '
                                         || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                                         || ' ; '
                                 END
                             ||
                                 CASE
                                     WHEN ct.ngay_thuc_hien >= SYSDATE THEN
                                         'Ngày kết quả lớn hơn hoặc bằng ngày ra viện '
                                         || p_ngayra
                                         || ', ngày kết quả: '
                                         || TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI')
                                         || ' ; '
                                 END
                             ||
                                 CASE
                                     WHEN trunc(ct.ngay_th_yl, 'MI') <= trunc(ct.ngay_chi_dinh_ct, 'MI') THEN
                                         'Ngày thực hiện nhỏ hơn hoặc bằng ngày chỉ định '
                                         || TO_CHAR(ct.ngay_chi_dinh_ct, 'DD/MM/YYYY HH24:MI')
                                         || ', ngày thực hiện: '
                                         || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                                         || ' ; '
                                 END
                             ||
                                 CASE
                                     WHEN ( CAST(ct.ngay_thuc_hien AS DATE) - CAST(ct.ngay_th_yl AS DATE) ) * 1440 < 5 THEN
                                         'Ngày thực hiện đến thời gian kết quả nhỏ hơn 5 phút '
                                         || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                                         || ', ngày kết quả: '
                                         || TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI')
                                         || ' ; '
                                 END
                             ghichu
                         FROM
                             kb_cd_cdha_ct   ct
                             JOIN kb_cd_cdha      cd ON ct.sovaovien = cd.sovaovien
                                                   AND ct.so_phieu_cdha = cd.so_phieu_cdha
                                                   AND cd.dvtt = p_dvtt
                                                   AND cd.sovaovien = p_sovaovien
                         WHERE
                             ct.sovaovien = p_sovaovien
                             AND ct.dvtt = p_dvtt
                             AND ( ( ct.ngay_thuc_hien > v_ngaythuoc
                                     AND cd.bac_si_dieu_tri = p_userid )
                                   OR ct.ngay_thuc_hien IS NULL
                                   OR ( ( CAST(ct.ngay_thuc_hien AS DATE) - CAST(ct.ngay_th_yl AS DATE) ) * 1440 < 5 )
                                   OR trunc(ct.ngay_th_yl, 'MI') <= trunc(ct.ngay_chi_dinh_ct, 'MI')
                                   OR ct.ngay_th_yl IS NULL
                                   OR ct.ngay_thuc_hien IS NULL
                                   OR ct.ngay_thuc_hien <= v_ngayvao
                                   OR ct.ngay_th_yl <= v_ngayvao
                                   OR ( ct.ngay_th_yl >= SYSDATE )
                                   OR ( ct.ngay_thuc_hien >= SYSDATE ) )
                             AND ct.bhytkchi = 0
                         UNION ALL
                         SELECT
                             ct.mabenhnhan,
                             TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI') ngay_ket_qua,
                             ct.sovaovien,
                             ct.so_phieu_xn sophieu,
                             CASE
                                     WHEN ct.ngay_thuc_hien IS NULL THEN
                                         'Chưa có kết quả'
                                     ELSE
                                         'Lớn hơn giờ ra toa, ngày giờ ra toa: ' || TO_DATE(v_ngaythuoc, 'DD/MM/YYYY HH24:MI')
                                 END
                             ||
                                 CASE
                                     WHEN ct.ngay_thuc_hien IS NULL THEN
                                         'Ngày kết quả rỗng; '
                                 END
                             ||
                                 CASE
                                     WHEN ct.ngay_th_yl IS NULL THEN
                                         'Ngày thực hiện y lệnh rỗng; '
                                 END
                             ||
                                 CASE
                                     WHEN ct.ngay_th_yl <= v_ngayvao THEN
                                         'Ngày thực hiện trước hoặc bằng ngày vào viện '
                                         || v_ngayvao_text
                                         || ', ngày thực hiện: '
                                         || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                                         || ' ; '
                                 END
                             ||
                                 CASE
                                     WHEN ct.ngay_thuc_hien <= v_ngayvao THEN
                                         'Ngày kết quả trước hoặc bằng ngày vào viện '
                                         || v_ngayvao_text
                                         || ', ngày kết quả: '
                                         || TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI')
                                         || ' ; '
                                 END
                             ||
                                 CASE
                                     WHEN ct.ngay_th_yl >= SYSDATE THEN
                                         'Ngày thực hiện lớn hơn hoặc bằng ngày ra viện '
                                         || p_ngayra
                                         || ', ngày thực hiện: '
                                         || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                                         || ' ; '
                                 END
                             ||
                                 CASE
                                     WHEN ct.ngay_thuc_hien >= SYSDATE THEN
                                         'Ngày kết quả lớn hơn hoặc bằng ngày ra viện '
                                         || p_ngayra
                                         || ', ngày kết quả: '
                                         || TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI')
                                         || ' ; '
                                 END
                             ||
                                 CASE
                                     WHEN trunc(ct.ngay_th_yl, 'MI') <= trunc(ct.ngay_chi_dinh_ct, 'MI') THEN
                                         'Ngày thực hiện nhỏ hơn hoặc bằng ngày chỉ định '
                                         || TO_CHAR(ct.ngay_chi_dinh_ct, 'DD/MM/YYYY HH24:MI')
                                         || ', ngày thực hiện: '
                                         || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                                         || ' ; '
                                 END
                             ||
                                 CASE
                                     WHEN ( CAST(ct.ngay_thuc_hien AS DATE) - CAST(ct.ngay_th_yl AS DATE) ) * 1440 < 5 THEN
                                         'Ngày thực hiện đến thời gian kết quả nhỏ hơn 5 phút '
                                         || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                                         || ', ngày kết quả: '
                                         || TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI')
                                         || ' ; '
                                 END
                             ghichu
                         FROM
                             kb_cd_xet_nghiem_chi_tiet   ct
                             JOIN kb_cd_xet_nghiem            cd ON ct.sovaovien = ct.sovaovien
                                                         AND ct.so_phieu_xn = cd.so_phieu_xn
                                                         AND cd.dvtt = p_dvtt
                                                         AND cd.sovaovien = p_sovaovien
                             JOIN cls_xetnghiem               xn ON ct.dvtt = xn.dvtt
                                                      AND ct.ma_xet_nghiem = xn.ma_xetnghiem
                                                      AND nvl(xn.boquaktra, 0) = 0
                         WHERE
                             ct.sovaovien = p_sovaovien
                             AND ct.dvtt = p_dvtt
                             AND ( ( ct.ngay_thuc_hien > v_ngaythuoc
                                     AND cd.bac_si_dieu_tri = p_userid )
                                   OR ct.da_xet_nghiem = 0
                                   OR ( ( CAST(ct.ngay_thuc_hien AS DATE) - CAST(ct.ngay_th_yl AS DATE) ) * 1440 < 5 )
                                   OR trunc(ct.ngay_th_yl, 'MI') <= trunc(ct.ngay_chi_dinh_ct, 'MI')
                                   OR ct.ngay_th_yl IS NULL
                                   OR ct.ngay_thuc_hien IS NULL
                                   OR ct.ngay_thuc_hien <= v_ngayvao
                                   OR ct.ngay_th_yl <= v_ngayvao
                                   OR ( ct.ngay_th_yl >= SYSDATE )
                                   OR ( ct.ngay_thuc_hien >= SYSDATE ) )
                             AND ct.bhytkchi = 0
                         UNION ALL
                         SELECT
                             ct.mabenhnhan,
                             TO_CHAR(nvl(ct.ngay_gio_pttt_kt, ct.ngay_gio_pttt), 'DD/MM/YYYY HH24:MI') ngay_ket_qua,
                             ct.sovaovien,
                             ct.so_phieu_dichvu sophieu,
                             CASE
                                     WHEN ct.ngay_gio_pttt_kt IS NULL
                                          AND ct.ngay_gio_pttt IS NULL THEN
                                         'Chưa có kết quả'
                                     WHEN nvl(ct.ngay_gio_pttt_kt, ct.ngay_gio_pttt) > v_ngaythuoc THEN
                                         'Lớn hơn giờ ra toa, ngày giờ ra toa: ' || TO_DATE(v_ngaythuoc, 'DD/MM/YYYY HH24:MI')
                                     WHEN dv.loai_dv IN (
                                         'TT',
                                         'PT',
                                         'VLTL'
                                     )
                                          AND nvl(ct.pp_vo_cam, 0) NOT IN (
                                         1,
                                         2,
                                         3,
                                         4
                                     ) THEN
                                         'Phương pháp vô cảm không được trống'
                                 END
                             ||
                                 CASE
                                     WHEN ct.ngay_gio_pttt <= v_ngayvao THEN
                                         'Ngày thực hiện trước hoặc bằng ngày vào viện '
                                         || v_ngayvao_text
                                         || ', ngày thực hiện: '
                                         || TO_CHAR(ct.ngay_gio_pttt, 'DD/MM/YYYY HH24:MI')
                                         || ' ; '
                                 END
                             ||
                                 CASE
                                     WHEN ct.ngay_gio_pttt_kt <= v_ngayvao THEN
                                         'Ngày kết quả trước hoặc bằng ngày vào viện '
                                         || v_ngayvao_text
                                         || ', ngày kết quả: '
                                         || TO_CHAR(ct.ngay_gio_pttt_kt, 'DD/MM/YYYY HH24:MI')
                                         || ' ; '
                                 END
                             ||
                                 CASE
                                     WHEN ct.ngay_gio_pttt >= SYSDATE THEN
                                         'Ngày thực hiện lớn hơn hoặc bằng ngày ra viện '
                                         || p_ngayra
                                         || ', ngày thực hiện: '
                                         || TO_CHAR(ct.ngay_gio_pttt, 'DD/MM/YYYY HH24:MI')
                                         || ' ; '
                                 END
                             ||
                                 CASE
                                     WHEN ct.ngay_gio_pttt_kt >= SYSDATE THEN
                                         'Ngày kết quả lớn hơn hoặc bằng ngày ra viện '
                                         || p_ngayra
                                         || ', ngày kết quả: '
                                         || TO_CHAR(ct.ngay_gio_pttt_kt, 'DD/MM/YYYY HH24:MI')
                                         || ' ; '
                                 END
                             ||
                                 CASE
                                     WHEN trunc(ct.ngay_gio_pttt, 'MI') <= trunc(ct.ngay_chi_dinh_ct, 'MI') THEN
                                         'Ngày thực hiện nhỏ hơn hoặc bằng ngày chỉ định '
                                         || TO_CHAR(ct.ngay_chi_dinh_ct, 'DD/MM/YYYY HH24:MI')
                                         || ', ngày thực hiện: '
                                         || TO_CHAR(ct.ngay_gio_pttt, 'DD/MM/YYYY HH24:MI')
                                         || ' ; '
                                 END
                             ||
                                 CASE
                                     WHEN ( CAST(ct.ngay_gio_pttt_kt AS DATE) - CAST(ct.ngay_gio_pttt AS DATE) ) * 1440 < 5 THEN
                                         'Ngày thực hiện đến thời gian kết quả nhỏ hơn 5 phút '
                                         || TO_CHAR(ct.ngay_gio_pttt, 'DD/MM/YYYY HH24:MI')
                                         || ', ngày kết quả: '
                                         || TO_CHAR(ct.ngay_gio_pttt_kt, 'DD/MM/YYYY HH24:MI')
                                         || ' ; '
                                 END
                             ghichu
                         FROM
                             kb_cd_dichvu_ct   ct
                             JOIN kb_cd_dichvu      cd ON ct.sovaovien = cd.sovaovien
                                                     AND ct.so_phieu_dichvu = cd.so_phieu_dichvu
                                                     AND cd.dvtt = p_dvtt
                                                     AND cd.sovaovien = p_sovaovien
                             JOIN dm_dich_vu_kham   dv ON ct.dvtt = dv.dvtt
                                                        AND ct.ma_dv = dv.ma_dv
                                                        AND dv.boquaktra = 0
                         WHERE
                             ct.sovaovien = p_sovaovien
                             AND ct.dvtt = p_dvtt
                             AND ( ( nvl(ct.ngay_gio_pttt_kt, ct.ngay_gio_pttt) > v_ngaythuoc
                                     AND cd.bac_si_dieu_tri = p_userid )
                                   OR ( ct.ngay_gio_pttt_kt IS NULL
                                        AND ct.ngay_gio_pttt IS NULL )
                                   OR ( dv.loai_dv IN (
                                 'TT',
                                 'PT',
                                 'VLTL'
                             )
                                        AND nvl(ct.pp_vo_cam, 0) NOT IN (
                                 1,
                                 2,
                                 3,
                                 4
                             )
                                        OR ( ( CAST(ct.ngay_gio_pttt_kt AS DATE) - CAST(ct.ngay_gio_pttt AS DATE) ) * 1440 < 5 )
                                        OR trunc(ct.ngay_gio_pttt, 'MI') <= trunc(ct.ngay_chi_dinh_ct, 'MI')
                                        OR ct.ngay_gio_pttt_kt <= v_ngayvao
                                        OR ct.ngay_gio_pttt <= v_ngayvao
                                        OR ( ct.ngay_gio_pttt >= SYSDATE )
                                        OR ( ct.ngay_gio_pttt_kt >= SYSDATE ) ) )
                             AND ct.bhytkchi = 0
                         UNION ALL
                         SELECT
                             tt.mabenhnhan,
                             TO_CHAR(tt.ngay_ra_toa, 'DD/MM/YYYY HH24:MI') thoigian,
                             tt.sovaovien,
                             tt.ten_vat_tu sophieu,
                             'Y lệnh thuốc trước thời gian khám bệnh, thời gian khám bệnh: '
                             || TO_CHAR(tnpb.thoi_gian_kham_benh, 'DD/MM/YYYY HH24:MI') ghichu
                         FROM
                             kb_chi_tiet_toa_thuoc tt
                             JOIN tnpb ON tt.sovaovien = tnpb.sovaovien
                                          AND tt.ma_bac_si_themthuoc = tnpb.nhan_vien_kham_benh
                                          AND tnpb.thoi_gian_kham_benh > tt.ngay_ra_toa
                         WHERE
                             tt.sovaovien = p_sovaovien
                             AND tt.dvtt = p_dvtt
                             AND tt.nghiep_vu IN (
                                 'ngoaitru_toathuoc',
                                 'ngoaitru_toavattu'
                             )
                     ) a
                     INNER JOIN his_public_list.dm_benh_nhan bn ON a.mabenhnhan = bn.ma_benh_nhan
                 ORDER BY
                     thoigian DESC;

RETURN cur;
END;