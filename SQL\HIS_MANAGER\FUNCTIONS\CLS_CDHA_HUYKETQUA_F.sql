CREATE OR REPLACE
FUNCTION               "CLS_CDHA_HUYKETQUA_F" (
 p_DVTT in varchar2,
 p_noitru in number,
 p_SO_PHIEU_CDHA in varchar2,
 p_MA_KHAM_BENH in  varchar2,
 p_stt_<PERSON>han in varchar2,
 p_stt_dotdieutri in varchar2,
 p_stt_dieutri in varchar2
)return number
IS
 vcount number(10) default 0;
	p_sovaovien NUMBER(18);
BEGIN

  if p_noitru = 0 then
select SOVAOVI<PERSON> into p_sovaovien from KB_CD_CDHA
where SO_PHIEU_CDHA=p_SO_PHIEU_CDHA
  and DVTT=p_DVTT and MA_KHAM_BENH = p_MA_KHAM_BENH;

UPDATE KB_CD_CDHA
set TRANG_THAI_CHUAN_DOAN= 0
where SO_PHIEU_CDHA=p_SO_PHIEU_CDHA
  and DVTT=p_DVTT and MA_KHAM_BENH = p_MA_KHAM_BENH;

UPDATE KB_CD_CDHA_CT
set  DA_CHAN_DOAN = 0 ,ket_qua= null,BACSI_THUCHIEN = null, MO_TA = null, ma_mausieuam = 0, NGUOI_THUC_HIEN = null
where SO_PHIEU_CDHA=p_SO_PHIEU_CDHA and DVTT=p_DVTT
  and MA_KHAM_BENH = p_MA_KHAM_BENH;
vcount := sql%rowcount ;
else
select sovaovien into p_sovaovien from  NOITRU_CD_CDHA
where SO_PHIEU_CDHA=p_SO_PHIEU_CDHA
  and DVTT=p_DVTT
  and stt_benhan = p_stt_benhan
  and stt_dotdieutri=p_stt_dotdieutri
  and stt_dieutri = p_stt_dieutri;

UPDATE NOITRU_CD_CDHA
set TRANG_THAI_CHUAN_DOAN= 0
where SO_PHIEU_CDHA=p_SO_PHIEU_CDHA
  and DVTT=p_DVTT
  and stt_benhan = p_stt_benhan
  and stt_dotdieutri=p_stt_dotdieutri
  and stt_dieutri = p_stt_dieutri;
UPDATE NOITRU_CD_CDHA_CHI_TIET
set DA_CHAN_DOAN= 0,ket_qua=null,BACSI_THUCHIEN = null, mo_ta = null, ma_mausieuam = 0, NGUOI_THUC_HIEN = null
where SO_PHIEU_CDHA=p_SO_PHIEU_CDHA and DVTT=p_DVTT
  and stt_benhan = p_stt_benhan
  and stt_dotdieutri=p_stt_dotdieutri
  and stt_dieutri = p_stt_dieutri;
vcount := sql%rowcount ;
end if;
DELETE FROM CMU_TIENPT
where dvtt = p_dvtt and SOPHIEU = p_SO_PHIEU_CDHA
  and SOVAOVIEN = p_sovaovien
;
DELETE  from CMU_THOIGIAN_NHANVIEN
where dvtt =p_dvtt and sovaovien = p_sovaovien
  and SOPHIEU = p_SO_PHIEU_CDHA;

return vcount;
END;
