create or replace PROCEDURE                   CMU_BIENBANHOP_PROCED(
    p_dvtt              IN     VARCHAR2,
    p_id               IN     NUMBER,
    p_sovaovien      IN     NUMBER,
    cur OUT SYS_REFCURSOR
) AS
BEGIN
open cur for
select
    phieu.ID,
    phieu.DVTT,
    phieu.SOVAOVIEN,
    phieu.MA_BENH_NHAN,
    TO_CHAR(phieu.THOIGIAN, 'hh24:mi dd/MM/yyyy') THOIGIAN,
    phieu.DAIDIENKHOA,
    phieu.THUKY,
    phieu.DAIDIENNGUOIBENH,
    phieu.MOIQUANHE,
    phieu.QUATRINHDIEUTRI,
    phieu.DIENBIEN,
    phieu.ICD_CHANDOAN,
    phieu.CHANDOAN,
    phieu.XUTRI,
    phieu.VANDETINHTRANGNGUOIBENH,
    phieu.DONGYTHONGNHAT,
    TO_CHAR(phieu.NGAY_TAO_PHIEU, 'dd/MM/yyyy') NGAY_TAO_PHIEU,
    phieu.NGUOI_TAO,
    nv.TEN_NHANVIEN_CD TEN_DAI_DIEN_KHOA,
    nv1.TEN_NHANVIEN_CD TEN_NGUOI_TAO,
    nv2.TEN_NHANVIEN_CD TEN_THU_KY

FROM CMU_BIENBANHOP phieu
         LEFT JOIN HIS_FW.DM_NHANVIEN_CD nv ON phieu.DAIDIENKHOA = nv.MA_NHANVIEN
         LEFT JOIN HIS_FW.DM_NHANVIEN_CD nv1 ON phieu.NGUOI_TAO = nv1.MA_NHANVIEN
         LEFT JOIN HIS_FW.DM_NHANVIEN_CD nv2 ON phieu.THUKY = nv2.MA_NHANVIEN

WHERE phieu.ID = p_id and phieu.SOVAOVIEN = p_sovaovien
order by phieu.NGAY_TAO_PHIEU;
END;