/* CSS cho tính năng autocomplete của trường "Lời dặn bác sĩ" */

/* Styling cho textarea có autocomplete */
textarea[list] {
    position: relative;
}

/* Styling cho datalist options */
datalist {
    display: none;
}

textarea[name="data[LOIDANBACSI]"], .loidanbacsi-autocomplete {
    border: 2px solid #e3e6f0 !important;
    border-radius: 4px !important;
    padding: 8px 12px !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
}

textarea[name="data[LOIDANBACSI]"]:focus, .loidanbacsi-autocomplete:focus {
    border-color: #80bdff !important;
    outline: 0 !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

textarea[name="data[LOIDANBACSI]"]::placeholder, .loidan<PERSON>csi-autocomplete::placeholder {
    color: #6c757d !important;
    opacity: 0.8 !important;
    font-style: italic !important;
}

.formio-component-LOIDANBACSI {
    position: relative;
}

.formio-component-LOIDANBACSI::after {
    content: "💡";
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    opacity: 0.6;
    font-size: 16px;
}

@keyframes suggestion-highlight {
    0% { background-color: transparent; }
    50% { background-color: #fff3cd; }
    100% { background-color: transparent; }
}

textarea[name="data[LOIDANBACSI]"].has-suggestions, .loidanbacsi-autocomplete.has-suggestions {
    animation: suggestion-highlight 2s ease-in-out;
}

.loidanbacsi-tooltip {
    position: relative;
    display: inline-block;
}

.loidanbacsi-tooltip .tooltiptext {
    visibility: hidden;
    width: 300px;
    background-color: #555;
    color: white;
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -150px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.loidanbacsi-tooltip .tooltiptext::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #555 transparent transparent transparent;
}

.loidanbacsi-tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

@media (max-width: 768px) {
    textarea[name="data[LOIDANBACSI]"], .loidanbacsi-autocomplete {
        font-size: 16px !important; /* Prevent zoom on iOS */
    }
}

textarea[name="data[LOIDANBACSI]"]:focus + datalist,
.loidanbacsi-autocomplete:focus + datalist {
    display: block;
}

textarea[list]:focus {
    border-left: 4px solid #28a745 !important;
}

.formio-component-LOIDANBACSI .control-label::after {
    content: " 💡";
    opacity: 0.7;
}

/* CSS cho dropdown autocomplete */
.loidanbacsi-dropdown {
    position: fixed !important;
    background-color: #ffffff !important;
    border: 2px solid #007bff !important;
    border-radius: 4px !important;
    max-height: 200px !important;
    overflow-y: auto !important;
    z-index: 9999 !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
    min-width: 300px !important;
    margin: 0 !important;
    padding: 0 !important;
}

.loidanbacsi-dropdown div {
    padding: 8px 12px !important;
    margin: 0 !important;
    cursor: pointer !important;
    border-bottom: 1px solid #f0f0f0 !important;
    font-size: 13px !important;
    line-height: 1.3 !important;
    transition: background-color 0.2s ease !important;
}

.loidanbacsi-dropdown div:hover {
    background-color: #f5f5f5 !important;
}

.loidanbacsi-dropdown div:last-child {
    border-bottom: none !important;
}

.loidanbacsi-dropdown div:first-child {
    border-top: none !important;
    margin-top: 0 !important;
    padding-top: 8px !important;
}