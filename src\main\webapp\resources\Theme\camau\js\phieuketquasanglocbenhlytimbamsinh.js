const phieuKetQuaSangLocBenhLyTimBamSinhJS = {};
let formPhieuKetQuaSangLocBenhLyTimBamSinh;
let formPhieuKetQuaSangLocBenhLyTimBamSinhTruocChinhSua;
let formPhieuKetQuaSangLocBenhLyTimBamSinhMoiNhat;

$(function () {
    let validateSLTBS = [
        {className: "formio-js-validate-1", cusMsg: "Ngày tạo phiếu là bắt buộc", type: "input"}
    ]

    let kyHieuSLTBS = [
        "SANG_LOC_TIM_BAM_SINH_NGUOI_THUC_HIEN",
        "SANG_LOC_TIM_BAM_SINH_TRUONG_KHOA"
    ].join(",");

    let dsTatCaNV = [];
    $.get("cmu_list_CMU_DSNHANVIENTOANBV_V2?url="+convertArray([
        singletonObject.dvtt
    ])).done(function(data){
        if (data && data.length > 0) {
            $.each(data, function (_, v) {
                dsTatCaNV.push({TEXT: v.TEN_NHANVIEN, VALUE: v.MA_NHANVIEN});
            });
        }
    });

    $(".themPhieuKetQuaSangLocBenhLyTimBamSinh").click(function () {
        loadFormPhieuKetQuaSangLocBenhLyTimBamSinh();
        initModalPhieuKetQuaSangLocBenhLyTimBamSinh("THEM");
    });

    $("#xemThemDSPhieuKetQuaSangLocBenhLyTimBamSinh").click(function(){
        initGridPhieuKetQuaSangLocBenhLyTimBamSinh();
        phieuKetQuaSangLocBenhLyTimBamSinhJS.reloadDSPhieuKetQuaSangLocBenhLyTimBamSinh();
    });

    $("#iconXemPhieuKetQuaSangLocBenhLyTimBamSinh").click(function () {
        getFilesign769V2(
            kyHieuSLTBS, formPhieuKetQuaSangLocBenhLyTimBamSinhMoiNhat.ID,
            -1, singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1,
            function(dataKySo) {
                viewPhieuKetQuaSangLocBenhLyTimBamSinh(
                    Number(formPhieuKetQuaSangLocBenhLyTimBamSinhMoiNhat.ID), false, dataKySo
                );
            });
    });

    $("#iconSuaPhieuKetQuaSangLocBenhLyTimBamSinh").click(function () {
        if(formPhieuKetQuaSangLocBenhLyTimBamSinhMoiNhat.NGUOI_TAO !== singletonObject.userId) {
            return notifiToClient('Red', MESSAGEAJAX.PERMISSION);
        }
        formPhieuKetQuaSangLocBenhLyTimBamSinhMoiNhat.DATA_PHIEU.ID = formPhieuKetQuaSangLocBenhLyTimBamSinhMoiNhat.ID
        loadFormPhieuKetQuaSangLocBenhLyTimBamSinh(formPhieuKetQuaSangLocBenhLyTimBamSinhMoiNhat.DATA_PHIEU);
        initModalPhieuKetQuaSangLocBenhLyTimBamSinh('SUA', formPhieuKetQuaSangLocBenhLyTimBamSinhMoiNhat);
    });

    $("#iconXoaPhieuKetQuaSangLocBenhLyTimBamSinh").click(function () {
        if(formPhieuKetQuaSangLocBenhLyTimBamSinhMoiNhat.NGUOI_TAO !== singletonObject.userId) {
            return notifiToClient('Red', MESSAGEAJAX.PERMISSION);
        }
        delelePhieuKetQuaSangLocBenhLyTimBamSinh(formPhieuKetQuaSangLocBenhLyTimBamSinhMoiNhat.ID);
    });

    $("#taoPhieuKetQuaSangLocBenhLyTimBamSinh").click(function () {
        formPhieuKetQuaSangLocBenhLyTimBamSinh.emit("checkValidity");
        if (!formPhieuKetQuaSangLocBenhLyTimBamSinh.checkValidity(null, false, null, true)) {
            xuLyValidateNotWorking(validateSLTBS);
            return;
        }
        let formData = $.extend(true, {}, formPhieuKetQuaSangLocBenhLyTimBamSinh.submission.data);
        delete formData.ID;
        let thisBtn = this.id;
        showSelfLoading(thisBtn);
        try {
            $.post("cmu_post_CMU_SANG_LOC_TIM_BAM_SINH_INS", {
                url: [
                    singletonObject.dvtt,
                    thongtinhsba.thongtinbn.MABENHNHAN,
                    JSON.stringify(formData),
                    thongtinhsba.thongtinbn.SOVAOVIEN,
                    thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                    thongtinhsba.thongtinbn.STT_BENHAN,
                    singletonObject.userId,
                    singletonObject.makhoa,
                ].join("```")
            }).fail(function() {
                notifiToClient('Red', MESSAGEAJAX.ERROR);
            }).done(function(data) {
                if (data > 0) {
                    let subData = getSubKQSLBLTBS()
                    luuLogHSBAInsertFormioV4(
                        formPhieuKetQuaSangLocBenhLyTimBamSinh,
                        LOGHSBALOAI.KETQUASANGLOCBENHLYTIMBAMSINH.KEY,
                        "Tạo " + LOGHSBALOAI.KETQUASANGLOCBENHLYTIMBAMSINH.VALUE + " - ID: " + data + " - ",
                        subData
                    );
                    notifiToClient('Green', MESSAGEAJAX.ADD_SUCCESS);
                    phieuKetQuaSangLocBenhLyTimBamSinhJS.reloadDSPhieuKetQuaSangLocBenhLyTimBamSinh();
                    $("#modalPhieuKetQuaSangLocBenhLyTimBamSinh").modal('hide');
                }
                else {
                    notifiToClient('Red', MESSAGEAJAX.FAIL);
                }
            }).always(function() {
                hideSelfLoading(thisBtn);
            });
        }
        catch (err) {
            console.error(err)
        }
    });

    $("#suaPhieuKetQuaSangLocBenhLyTimBamSinh").click(function () {
        let formData = $.extend(true, {}, formPhieuKetQuaSangLocBenhLyTimBamSinh.submission.data);
        formPhieuKetQuaSangLocBenhLyTimBamSinh.emit("checkValidity");
        if (!formPhieuKetQuaSangLocBenhLyTimBamSinh.checkValidity(null, false, null, true)) {
            xuLyValidateNotWorking(validateSLTBS);
            return;
        }
        let dataToSave = $.extend(true, {}, formPhieuKetQuaSangLocBenhLyTimBamSinh.submission.data);
        delete dataToSave.ID;
        let thisBtn = this.id;
        showSelfLoading(thisBtn);
        $.post("cmu_post_CMU_SANG_LOC_TIM_BAM_SINH_UPD", {
            url: [
                formData.ID,
                singletonObject.dvtt,
                JSON.stringify(dataToSave),
            ].join("```")
        }).fail(function() {
            notifiToClient('Red', MESSAGEAJAX.ERROR);
        }).done(function(data) {
            if (data > 0) {
                let subData = getSubKQSLBLTBS()
                luuLogHSBAChinhSuaFormioV4(
                    formPhieuKetQuaSangLocBenhLyTimBamSinhTruocChinhSua.DATA_PHIEU,
                    formPhieuKetQuaSangLocBenhLyTimBamSinh,
                    LOGHSBALOAI.KETQUASANGLOCBENHLYTIMBAMSINH.KEY,
                    "Cập nhật " + LOGHSBALOAI.KETQUASANGLOCBENHLYTIMBAMSINH.VALUE + " - ID: " + formData.ID + " - ",
                    subData
                );
                notifiToClient('Green', MESSAGEAJAX.EDIT_SUCCESS);
                phieuKetQuaSangLocBenhLyTimBamSinhJS.reloadDSPhieuKetQuaSangLocBenhLyTimBamSinh();
                $("#modalPhieuKetQuaSangLocBenhLyTimBamSinh").modal('hide');
            }
            else {
                notifiToClient('Red', MESSAGEAJAX.FAIL);
            }
        }).always(function() {
            hideSelfLoading(thisBtn);
        });
    });

    function autoScaleTextarea(textarea) {
        $(textarea).css('height', 'auto');
        $(textarea).css('height', textarea.scrollHeight + 'px');
    }

    function delelePhieuKetQuaSangLocBenhLyTimBamSinh(id) {
        confirmToClient(MESSAGEAJAX.CONFIRM, function() {
            $.post("cmu_post_CMU_SANG_LOC_TIM_BAM_SINH_DEL", {
                url: [id, singletonObject.dvtt].join("```")
            }).fail(function() {
                notifiToClient("Red", MESSAGEAJAX.ERROR);
            }).done(function(data) {
                if (data === '1') {
                    luuLogHSBADeleteFormio(
                        LOGHSBALOAI.KETQUASANGLOCBENHLYTIMBAMSINH.KEY,
                        "Xóa " + LOGHSBALOAI.KETQUASANGLOCBENHLYTIMBAMSINH.VALUE + " - ID: " + id
                    );
                    notifiToClient('Green', MESSAGEAJAX.DEL_SUCCESS);
                    phieuKetQuaSangLocBenhLyTimBamSinhJS.reloadDSPhieuKetQuaSangLocBenhLyTimBamSinh();
                } else {
                    notifiToClient('Red', MESSAGEAJAX.ERROR);
                }
            }).always(function() {});
        }, function() {});
    }

    function initGridPhieuKetQuaSangLocBenhLyTimBamSinh() {
        let listData = $('#phieuKetQuaSangLocBenhLyTimBamSinhGrid');
        if (!listData[0].grid) {
            listData.jqGrid({
                datatype: 'local',
                data: [],
                loadonce: true,
                height: 400,
                width: null,
                shrinkToFit: false,
                colModel: [
                    // show
                    {
                        name: "KYSO1",
                        label: "Ký số 1",
                        align: 'left',
                        width: 100,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.KEYSIGN_1) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold; color: green">Đã ký</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold; color: red">Chưa ký</span>';
                            }
                        },
                        cellattr: function () {
                            return ' title="Ký số Người thực hiện"';
                        }
                    },
                    {
                        name: "KYSO2",
                        label: "Ký số 2",
                        align: 'left',
                        width: 100,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.KEYSIGN_2) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold; color: green">Đã ký</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold; color: red">Chưa ký</span>';
                            }
                        },
                        cellattr: function () {
                            return ' title="Ký số Trưởng khoa"';
                        }
                    },
                    {name: 'ID', label: "ID", width: 100},


                    {name: 'TENNGUOITAO', label: 'Người tạo', width: 250},
                    {name: 'NGAYTAO', label: 'Ngày tạo', width: 150},

                    // hidden
                    {name: 'NGUOI_TAO', hidden: true},
                    {name: 'DATA_PHIEU', hidden: true},
                    {name: "KEYSIGN_1", hidden: true},
                    {name: "KEYSIGN_2", hidden: true},
                ],
                onRightClickRow: function () {
                    let ret = getThongtinRowSelected("phieuKetQuaSangLocBenhLyTimBamSinhGrid");
                    let items = {
                        "xem": { name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>' },
                    }
                    $.contextMenu('destroy', '#phieuKetQuaSangLocBenhLyTimBamSinhGrid tr');
                    if (ret.KEYSIGN_1 && ret.KEYSIGN_2) {
                        getFilesign769V2(
                            kyHieuSLTBS, ret.ID, -1, singletonObject.dvtt,
                            thongtinhsba.thongtinbn.SOVAOVIEN,
                            thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1,
                            function(dataKySo) {
                                if (dataKySo[0].KY_HIEU_PHIEU === 'SANG_LOC_TIM_BAM_SINH_NGUOI_THUC_HIEN') {
                                    items = {
                                        "huykyso1": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số Người thực hiện</p>'},
                                        ...items,
                                    }
                                }
                                else {
                                    items = {
                                        "huykyso2": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số Trưởng khoa</p>'},
                                        ...items,
                                    }
                                }
                            });
                    }
                    else if (ret.KEYSIGN_1){
                        items = {
                            "huykyso1": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số Người thực hiện</p>'},
                            "kyso2": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số Trưởng khoa</p>'},
                            ...items,
                        }
                    }
                    else if (ret.KEYSIGN_2){
                        items = {
                            "kyso1": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số Người thực hiện</p>'},
                            "huykyso2": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số Trưởng khoa</p>'},
                            ...items,
                        }
                    }
                    else {
                        items = {
                            "kyso1": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số Người thực hiện</p>'},
                            "kyso2": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số Trưởng khoa</p>'},
                            ...items,
                            "sua": {
                                name: '<p><i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Sửa</p>'
                            },
                            "xoa": {
                                name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'
                            },
                        }
                    }

                    $.contextMenu({
                        selector: '#phieuKetQuaSangLocBenhLyTimBamSinhGrid tr',
                        callback: function (key) {
                            let ret = getThongtinRowSelected("phieuKetQuaSangLocBenhLyTimBamSinhGrid");
                            let dataJson = JSON.parse(ret.DATA_PHIEU);
                            dataJson.ID = Number(ret.ID); ret.DATA_PHIEU = dataJson;
                            if (key === 'kyso1') {
                                formPhieuKetQuaSangLocBenhLyTimBamSinhTruocChinhSua = $.extend(true, {}, {
                                    ID: ret.ID, DATA_PHIEU: dataJson
                                });
                                getFilesign769V2(
                                    kyHieuSLTBS, ret.ID, -1, singletonObject.dvtt,
                                    thongtinhsba.thongtinbn.SOVAOVIEN,
                                    thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1,
                                    function(dataKySo) {
                                        viewPhieuKetQuaSangLocBenhLyTimBamSinh(
                                            Number(ret.ID), true, dataKySo, "NguoiThucHien"
                                        );
                                    });
                            }
                            if (key === 'kyso2') {
                                formPhieuKetQuaSangLocBenhLyTimBamSinhTruocChinhSua = $.extend(true, {}, {
                                    ID: ret.ID, DATA_PHIEU: dataJson
                                });
                                getFilesign769V2(
                                    kyHieuSLTBS, ret.ID, -1, singletonObject.dvtt,
                                    thongtinhsba.thongtinbn.SOVAOVIEN,
                                    thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1,
                                    function(dataKySo) {
                                        viewPhieuKetQuaSangLocBenhLyTimBamSinh(
                                            Number(ret.ID), true, dataKySo, "TruongKhoa"
                                        );
                                    });
                            }
                            if (key === 'huykyso1') {
                                confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?",
                                    function() {
                                        huykysoFilesign769V2(
                                            kyHieuSLTBS, ret.ID, singletonObject.userId, singletonObject.dvtt,
                                            thongtinhsba.thongtinbn.SOVAOVIEN,
                                            thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1,
                                            function(_) {
                                                phieuKetQuaSangLocBenhLyTimBamSinhJS.reloadDSPhieuKetQuaSangLocBenhLyTimBamSinh();
                                            }
                                        );
                                    }, function () {});
                            }
                            if (key === 'huykyso2') {
                                confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?",
                                    function() {
                                        huykysoFilesign769V2(
                                            kyHieuSLTBS, ret.ID, singletonObject.userId, singletonObject.dvtt,
                                            thongtinhsba.thongtinbn.SOVAOVIEN,
                                            thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1,
                                            function(_) {
                                                phieuKetQuaSangLocBenhLyTimBamSinhJS.reloadDSPhieuKetQuaSangLocBenhLyTimBamSinh();
                                            }
                                        );
                                    }, function () {});
                            }
                            if (key === 'xem') {
                                getFilesign769V2(
                                    kyHieuSLTBS, ret.ID, -1, singletonObject.dvtt,
                                    thongtinhsba.thongtinbn.SOVAOVIEN,
                                    thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1,
                                    function(dataKySo) {
                                        viewPhieuKetQuaSangLocBenhLyTimBamSinh(Number(ret.ID), false, dataKySo);
                                    });
                            }
                            if (key === 'sua') {
                                if (ret.NGUOI_TAO !== singletonObject.userId) {
                                    return notifiToClient('Red', MESSAGEAJAX.PERMISSION);
                                }
                                loadFormPhieuKetQuaSangLocBenhLyTimBamSinh(dataJson);
                                initModalPhieuKetQuaSangLocBenhLyTimBamSinh('SUA', ret);
                            }
                            if (key === 'xoa') {
                                if(ret.NGUOI_TAO !== singletonObject.userId) {
                                    return notifiToClient('Red', MESSAGEAJAX.PERMISSION);
                                }
                                delelePhieuKetQuaSangLocBenhLyTimBamSinh(Number(ret.ID));
                            }
                        },
                        items: items
                    })
                },
            });
            listData.jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: 'cn'});
        }
    }

    function initModalPhieuKetQuaSangLocBenhLyTimBamSinh(action, data) {
        let titleName = " Phiếu kết quả sàng lọc bệnh lý tim bẩm sinh nặng ở trẻ sơ sinh bằng phương pháp đo độ bảo hòa oxy qua da";
        $('#modalPhieuKetQuaSangLocBenhLyTimBamSinh').modal('show');
        addTextTitleModal('titlePhieuKetQuaSangLocBenhLyTimBamSinh', titleName);
        if(action === 'THEM') {
            $('#taoPhieuKetQuaSangLocBenhLyTimBamSinh').show();
            $('#suaPhieuKetQuaSangLocBenhLyTimBamSinh').hide();
        } else {
            formPhieuKetQuaSangLocBenhLyTimBamSinhTruocChinhSua = $.extend(true, {}, data);
            $('#taoPhieuKetQuaSangLocBenhLyTimBamSinh').hide();
            $('#suaPhieuKetQuaSangLocBenhLyTimBamSinh').show();
        }
    }

    function loadFormPhieuKetQuaSangLocBenhLyTimBamSinh(data, callback) {
        try {
            $.getJSON(
                "resources/camau/js/formioPhieuKetQuaSangLocBenhLyTimBamSinh.json?v="+moment().format('YYYYMMDDHHmmss')
            ).done(function(rs){
                Formio.createForm(document.getElementById('phieuKetQuaSangLocBenhLyTimBamSinh'), rs, {
                    disableAlerts: true,
                }).then(function(instance) {
                    formPhieuKetQuaSangLocBenhLyTimBamSinh = instance;
                    let nguoiThucHien = formPhieuKetQuaSangLocBenhLyTimBamSinh.getComponent("NGUOI_THUC_HIEN").component;
                    nguoiThucHien.data.json = singletonObject.danhsachtatcanhanvien;
                    let truongKhoa = formPhieuKetQuaSangLocBenhLyTimBamSinh.getComponent("TRUONG_KHOA").component;
                    truongKhoa.data.json = dsTatCaNV;

                    let initData = { data: {} };
                    if (data) {
                        initData.data = data;
                    }
                    else {
                        initData.data = {
                            NGUOI_THUC_HIEN: singletonObject.danhsachtatcanhanvien.find(
                                (item) => item.value === singletonObject.userId),
                            CAN_NANG: thongtinhsba.thongtinbn.CANNANG,
                            GIO_SINH: thongtinhsba.thongtinbn.GIO_SINH_CV130,
                        }
                    }

                    formPhieuKetQuaSangLocBenhLyTimBamSinh.setSubmission(initData).then(function () {
                        dinhKemSuKienPhieuKetQuaSangLocBenhLyTimBamSinh(true);
                    }).catch(function(err) {
                        notifiToClient('Red', 'Lỗi cài đặt dữ liệu'); console.error(err);
                    });
                    callback && callback();
                });
            })
        } catch (err) {
            console.error(err)
        }
    }

    function dinhKemSuKienPhieuKetQuaSangLocBenhLyTimBamSinh(isInit) {
        let textarea = $('.formio-js-scale-textarea textarea[type="text"]');
        textarea.each(function() {
            $(this).attr('rows', 1);
            $(this).on('input', function() { autoScaleTextarea(this); });
            autoScaleTextarea(this);
        });

        if (isInit) {
            formPhieuKetQuaSangLocBenhLyTimBamSinh.redraw();
            dinhKemSuKienPhieuKetQuaSangLocBenhLyTimBamSinh();
        }
    }

    function getSubKQSLBLTBS() {
        return {
            AM_TINH: "Âm tính", DUONG_TINH: "Dương tính",
        }
    }

    function viewPhieuKetQuaSangLocBenhLyTimBamSinh(idPhieu, sign, dataKySo, sufKey) {
        let params = {
            ID_PHIEU: idPhieu
        }
        let url = 'cmu_in_rp_phieu_ket_qua_sang_loc_benh_ly_tim_bam_sinh?type=pdf&' + $.param(params);
        if (sign) {
            if (dataKySo && dataKySo.length > 0) {
                let keyMinio = getCMUFileSigned769GetLinkV2(dataKySo[0].KEYMINIO, 'pdf');
                url = keyMinio !== '-1' ? keyMinio : url;
            }
            previewAndSignPdfDefaultModal({url: url, idButton: "kySo" + sufKey + "SLTBS",}, function(){});
        }
        else {
            if (dataKySo && dataKySo.length > 0) { getCMUFileSigned769(dataKySo[0].KEYMINIO,"pdf"); }
            else { previewPdfDefaultModal(url, "framePhieuKetQuaSangLocBenhLyTimBamSinh"); }
        }
    }

    function xuLyValidateNotWorking(validateList) {
        $.each(validateList, function (_, v) {
            let className = v.className, cusMsg = v.cusMsg, type = v.type;
            let inputTag = $("." + className + " " + type),
                labelTag = $("." + className + " label"),
                errorTag = $("." + className + " .formio-errors");
            if (inputTag.val().length === 0 && errorTag.contents().length === 0
                && errorTag.next('.error-msg-sltbs').length === 0) {
                inputTag.css({"border-color": "#dc3545"}); labelTag.css({"color": "#721c24"});
                errorTag.after('<div class="error-msg-sltbs" style="display: block; color: #c20000; font-size: 80%; margin-top: .25rem">' + cusMsg + '</div>');
            }
            else if (inputTag.val().length > 0) {
                inputTag.removeAttr("style"); labelTag.css({"color": "#222222"});
                errorTag.next('.error-msg-sltbs').remove();
            }
            inputTag.on("change", function () {
                if (inputTag.val().length > 0) {
                    inputTag.removeAttr("style"); labelTag.css({"color": "#222222"});
                    errorTag.next('.error-msg-sltbs').remove();
                }
            });
        })
    }

    phieuKetQuaSangLocBenhLyTimBamSinhJS.reloadDSPhieuKetQuaSangLocBenhLyTimBamSinh = function () {
        $("#phieuKetQuaSangLocBenhLyTimBamSinhGrid").jqGrid('clearGridData');
        let url = 'cmu_getlist?url=' + convertArray([
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            'CMU_SANG_LOC_TIM_BAM_SINH_LST'
        ]);
        $.get(url).done(function(data){
            if (data && data.length > 0) {
                let dataGanNhat = data[data.length - 1]
                let formData = JSON.parse(dataGanNhat.DATA_PHIEU);
                let khoaTao = singletonObject.danhsachphongban.find(
                    (item) => item.MAKHOA === dataGanNhat.KHOA_TAO);
                $("#dataPhieuKetQuaSangLocBenhLyTimBamSinh").html(
                    khoaTao.TENKHOA + " - " + moment(dataGanNhat.NGAYTAO, ["DD/MM/YYYY HH:mm:ss"]).format("DD/MM/YYYY")
                );
                formPhieuKetQuaSangLocBenhLyTimBamSinhMoiNhat = {
                    ...dataGanNhat, DATA_PHIEU: formData
                };
                if (dataGanNhat.KEYSIGN_1 || dataGanNhat.KEYSIGN_2){
                    $("#iconSuaPhieuKetQuaSangLocBenhLyTimBamSinh").hide();
                    $("#iconXoaPhieuKetQuaSangLocBenhLyTimBamSinh").hide();
                }
                else {
                    $("#xuLyIconPhieuKetQuaSangLocBenhLyTimBamSinh").css('visibility', 'unset');
                    $("#iconXemPhieuKetQuaSangLocBenhLyTimBamSinh").show();
                    $("#iconSuaPhieuKetQuaSangLocBenhLyTimBamSinh").show();
                    $("#iconXoaPhieuKetQuaSangLocBenhLyTimBamSinh").show();
                }
            } else  {
                $("#dataPhieuKetQuaSangLocBenhLyTimBamSinh").html("Không có dữ liệu");
                $("#xuLyIconPhieuKetQuaSangLocBenhLyTimBamSinh").css('visibility', 'hidden');
            }
            $("#phieuKetQuaSangLocBenhLyTimBamSinhGrid").jqGrid('setGridParam', {
                datatype: 'local',
                data: data.map((item) => {
                    let parseData = JSON.parse(item.DATA_PHIEU);
                    return { ...item, DATA_PARSED: parseData }
                })
            }).trigger("reloadGrid");
        });
    }

    $(document).on('click', '#kySoNguoiThucHienSLTBS', function() {
        kySoChung({
            dvtt: singletonObject.dvtt,
            userId: singletonObject.userId,
            url: $('#iframePreviewAndSign').attr('src'),
            loaiGiay: "SANG_LOC_TIM_BAM_SINH_NGUOI_THUC_HIEN",
            maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
            soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
            soPhieuDichVu: formPhieuKetQuaSangLocBenhLyTimBamSinhTruocChinhSua.ID,
            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            keyword: "SIGNATURE_1",
            fileName: LOGHSBALOAI.KETQUASANGLOCBENHLYTIMBAMSINH.VALUE + ": " +
                thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Mã phiếu: " +
                formPhieuKetQuaSangLocBenhLyTimBamSinhTruocChinhSua.ID,
        }, function(_) {
            $("#modalPreviewAndSignPDF").modal("hide");
            phieuKetQuaSangLocBenhLyTimBamSinhJS.reloadDSPhieuKetQuaSangLocBenhLyTimBamSinh();
        });
    });

    $(document).on('click', '#kySoTruongKhoaSLTBS', function() {
        kySoChung({
            dvtt: singletonObject.dvtt,
            userId: singletonObject.userId,
            url: $('#iframePreviewAndSign').attr('src'),
            loaiGiay: "SANG_LOC_TIM_BAM_SINH_TRUONG_KHOA",
            maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
            soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
            soPhieuDichVu: formPhieuKetQuaSangLocBenhLyTimBamSinhTruocChinhSua.ID,
            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            keyword: "SIGNATURE_2",
            fileName: LOGHSBALOAI.KETQUASANGLOCBENHLYTIMBAMSINH.VALUE + ": " +
                thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Mã phiếu: " +
                formPhieuKetQuaSangLocBenhLyTimBamSinhTruocChinhSua.ID,
        }, function(_) {
            $("#modalPreviewAndSignPDF").modal("hide");
            phieuKetQuaSangLocBenhLyTimBamSinhJS.reloadDSPhieuKetQuaSangLocBenhLyTimBamSinh();
        });
    });

    // Bổ sung Mẫu chuẩn bị
    $("#mauChuanBiPhieuKetQuaSangLocBenhLyTimBamSinh").click(function() {
        let element = $("#mau_danhsachmaujson_wrap");
        element.attr("function-add", 'insertMauCBPhieuKetQuaSangLocBenhLyTimBamSinh');
        element.attr("function-chinhsua", 'editMauCBPhieuKetQuaSangLocBenhLyTimBamSinh');
        element.attr("function-select", 'selectMauCBPhieuKetQuaSangLocBenhLyTimBamSinh');
        element.attr("function-getdata", 'getdataMauCBPhieuKetQuaSangLocBenhLyTimBamSinh');
        element.attr("function-validate", 'formioCBPhieuKetQuaSangLocBenhLyTimBamSinhValidate');
        element.attr("data-key", 'MAUCBPHIEUKQSANGLOCBENHLYTIMBAMSINH');
        $("#modalMauChungJSON").modal("show");
        $.loadDanhSachMauChungJSON('MAUCBPHIEUKQSANGLOCBENHLYTIMBAMSINH')
    }); $.extend({
        insertMauCBPhieuKetQuaSangLocBenhLyTimBamSinh: function () {
            generateFormMauCBPhieuKetQuaSangLocBenhLyTimBamSinh({});
        },
        editMauCBPhieuKetQuaSangLocBenhLyTimBamSinh: function (rowSelect) {
            let json = JSON.parse(rowSelect.NOIDUNG);
            let dataMau = {}
            json.forEach(function(item) {
                dataMau[item.key] = item.value
            });
            generateFormMauCBPhieuKetQuaSangLocBenhLyTimBamSinh({
                ID: rowSelect.ID,
                TENMAU: rowSelect.TENMAU,
                ...dataMau
            });
        },
        selectMauCBPhieuKetQuaSangLocBenhLyTimBamSinh: function (rowSelect) {
            let json = JSON.parse(rowSelect.NOIDUNG);
            json.forEach(function(item) {
                $('#phieuKetQuaSangLocBenhLyTimBamSinh [name="data[' + item.key + ']"]').val(item.value)
                formPhieuKetQuaSangLocBenhLyTimBamSinh.data[item.key] = item.value
            });
            dinhKemSuKienPhieuKetQuaSangLocBenhLyTimBamSinh();
            $("#modalMauChungJSON").modal("hide");
        },
        getdataMauCBPhieuKetQuaSangLocBenhLyTimBamSinh: function () {
            let objectNoidung = [];
            getObjectMauCBPhieuKetQuaSangLocBenhLyTimBamSinh().forEach(function(item) {
                console.log(item)
                if (item.key !== 'ID' && item.key !== 'TENMAU' &&
                    item.label !== 'Columns' && item.label !== 'Titles'
                ) {
                    objectNoidung.push({
                        "label": item.label,
                        "value": formioMauHSBA.submission.data[item.key],
                        "key": item.key,
                    });
                }
                else if (item.label === 'Columns') {
                    item.columns.forEach(function (v) {
                        objectNoidung.push({
                            "label": v.components[0].label,
                            "value": formioMauHSBA.submission.data[v.components[0].key],
                            "key": v.components[0].key,
                        });
                    });
                }
            })
            return {
                ID: formioMauHSBA.submission.data.ID,
                TENMAU: formioMauHSBA.submission.data.TENMAU,
                NOIDUNG: JSON.stringify(objectNoidung),
                KEYMAUCHUNG: 'MAUCBPHIEUKQSANGLOCBENHLYTIMBAMSINH'
            };
        },
        formioCBPhieuKetQuaSangLocBenhLyTimBamSinhValidate: function() {
            formioMauHSBA.emit("checkValidity");
            return formioMauHSBA.checkValidity(null, false, null, true);
        },
    });

    function generateFormMauCBPhieuKetQuaSangLocBenhLyTimBamSinh(dataForm) {
        let jsonForm = getJSONObjectForm(getObjectMauCBPhieuKetQuaSangLocBenhLyTimBamSinh());
        Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
            jsonForm, {}
        ).then(function(form) {
            formioMauHSBA = form;
            formioMauHSBA.submission = { data: { ...dataForm }}
        });
    }

    function getObjectMauCBPhieuKetQuaSangLocBenhLyTimBamSinh() {
        return [
            {
                "label": "ID",
                "key": "ID",
                "type": "textfield",
                others: {
                    hidden: true
                }
            },
            {
                "label": "Tên mẫu",
                "key": "TENMAU",
                "type": "textarea",
                validate: {
                    required: true
                },
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Titles",
                "type": "htmlelement",
                "tag": "p",
                "content": "<b>THÔNG TIN CÁ NHÂN</b>",
                "input": false
            },
            {
                "label": "Columns",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Tuổi thai: tuần",
                                "customClass": "mr-2",
                                "key": "TUOI_THAI_TUAN",
                                "type": "number"
                            }
                        ],
                        "width": 4, "size": "md", "currentWidth": 4
                    },
                    {
                        "components": [
                            {
                                "label": "Tuổi thai: ngày",
                                "customClass": "mr-2",
                                "key": "TUOI_THAI_NGAY",
                                "type": "number"
                            }
                        ],
                        "width": 4, "size": "md", "currentWidth": 4
                    },
                    {
                        "components": [
                            {
                                "label": "Mã hồ sơ",
                                "key": "MA_HO_SO",
                                "type": "textfield"
                            }
                        ],
                        "width": 4, "size": "md", "currentWidth": 4
                    }
                ],
                "key": "columns",
                "type": "columns",
                "customClass": "ml-0 mr-0"
            },
            {
                "label": "<b>Đề nghị</b>",
                "key": "DE_NGHI",
                "type": "textarea"
            }
        ];
    }
});