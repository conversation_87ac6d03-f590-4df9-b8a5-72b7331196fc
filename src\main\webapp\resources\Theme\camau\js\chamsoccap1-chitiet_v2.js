var formCSC1ThongTinChiTiet,
    formCSC1ThongTinChiTietGroup,
    formCSC1ThongTinChiTietNuocNhap,
    formCSC1ThongTinChiTietNuocXuat,
    thongTinPhieuTruocKhiChinhSua;

function getConfigByKeyCSC1(key, danhSach) {
    var options = danhSach.filter(function(object) {
        return object.LOAI == key
    }).map(function(object) {
        return {
            label: object.TENHIENTHI,
            value: object.ID
        }
    })
    return options;
}

function genJsonGroupCSC1(object) {
    return {
        "components": [
            {
                "key": "p-lydovaovien",
                "type": "panel",
                "label": object.labelGroup,
                "title": object.labelGroup,
                "collapsed": false,
                "customClass": "hsba-tabs-wrap ml-1 mr-1",
                "components": [
                    {
                        "label": "Columns",
                        "columns": [
                            {
                                "components": [
                                    {
                                        "tag": "label",
                                        "content": object.labelGroup,
                                        "key": "htmllabel",
                                        "type": "htmlelement",
                                    },
                                ],
                                "width": 2,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "",
                                        "customClass": "pl-2",
                                        "key": "TEN_" + object.key,
                                        "type": "select",
                                        "data": {
                                            "values": getConfigByKeyCSC1(object.key, singletonObject.configChamSocCap1)
                                        }
                                    }
                                ],
                                "width": 8,
                                "size": "md"
                            },
                            {
                                "components": [
                                    {
                                        "label": "Thêm",
                                        "customClass": "pl-2 w-100",
                                        "action": "event",
                                        "theme": "primary",
                                        "size": "sm",
                                        "event": "them" + object.key,
                                        "key": "THEM_" + object.key,
                                        "type": "button",
                                    }
                                ],
                                "width": 2,
                                "size": "md"
                            },
                        ],
                        "customClass": "ml-0 mr-0",
                        "hideLabel": true,
                        "key": "columns",
                        "type": "columns",
                    },
                    {
                        "label": "",
                        "disableAddingRemovingRows": true,
                        "initEmpty": true,
                        "customClass": "ml-2 datagrid-overflow-visible",
                        "key": object.key + "_ARR",
                        "type": "datagrid",
                        "components": [
                            {
                                "label": "Columns",
                                "columns": [
                                    {
                                        "components": [
                                            {
                                                "label": "",
                                                "applyMaskOn": "change",
                                                "customClass": "pr-2",
                                                "disabled": true,
                                                "key": "TEN_ITEM",
                                                "type": "textfield",
                                            }
                                        ],
                                        "width": 6,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "",
                                                "customClass": "pr-2",
                                                "key": "CHI_SO",
                                                "type": "textfield",
                                                "defaultValue": "BT",
                                            },
                                        ],
                                        "width": 4,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "M",
                                                "customClass": "w-100 pr-1",
                                                "action": "event",
                                                "theme": "primary",
                                                "size": "sm",
                                                "event": "mau" + object.key,
                                                "key": "MAU_" + object.key,
                                                "type": "button",
                                            }
                                        ],
                                        "width": 1,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "X",
                                                "customClass": "w-100 pl-1",
                                                "action": "event",
                                                "theme": "danger",
                                                "size": "sm",
                                                "event": "xoa" + object.key,
                                                "key": "XOA_" + object.key,
                                                "type": "button",
                                            }
                                        ],
                                        "width": 1,
                                        "size": "md",
                                    },
                                ],
                                "customClass": "ml-0 mr-0",
                                "hideLabel": true,
                                "key": "columns",
                                "type": "columns",
                            }
                        ]
                    },
                ]
            }
        ],
        "width": 6,
        "size": "md",
    }
}

$(function() {
    $("#modalLanChamSocCap1").on("show.bs.modal", function() {
        instanceGridChamSocCap1ChiTiet();
        addTextTitleModal("titleLanChamSocCap1")
    });

    $('#modalLanChamSocCap1ChiTiet').on('show.bs.modal', function () {
        $(this).attr('aria-hidden', 'false');
    });

    $("#modalLanChamSocCap1ChiTiet").on("hidden.bs.modal", function() {
        thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet = {};
        $("#csc1_chitiet_lammoi").click();
        $(this).attr('aria-hidden', 'true');
    });

    $("#csc1_chitiet_them").click(function(){
        var allRow = $("#ttcs_list_chamsoccap1_chitiet").jqGrid('getRowData');
        if (allRow.length >= 24){
            return notifiToClient("Red", "Số lượng chi tiết chăm sóc cấp 1 đã đạt giới hạn");
        }
        loadChiTietById({ID_CHI_TIET: null}, 1);
    });

    $("#lanchamsoccap1_chitiet_action_them").click(function() {
        var idButton = "lanchamsoccap1_chitiet_action_them";
        var dataChiTiet = formCSC1ThongTinChiTiet.submission.data;
        var dataGroup = formCSC1ThongTinChiTietGroup.submission.data;
        var dataNuocNhap = formCSC1ThongTinChiTietNuocNhap.submission.data;
        var dataNuocXuat = formCSC1ThongTinChiTietNuocXuat.submission.data;
        var arrLog = [];
        if (singletonObject.configGroupChamSocCap1.length == 0){
            return notifiToClient("Red", "Vui lòng liên hệ IT để cấu hình nhóm chỉ số chăm sóc cấp 1");
        }
        if (formCSC1ThongTinChiTiet.checkValidity()){
            showSelfLoading(idButton);
            $.post("cmu_post", {
                url:[
                    thongtinhsba.thongtinbn.lanChamSocCap1.ID_CHAM_SOC_CAP_1,
                    dataChiTiet.SPO2,
                    dataChiTiet.MACH,
                    dataChiTiet.NHIET_DO,
                    dataChiTiet.HUYET_AP_TREN,
                    dataChiTiet.HUYET_AP_DUOI,
                    dataChiTiet.XOAY_TRO,
                    dataChiTiet.CHAM_SOC_DIEU_DUONG,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    moment(dataChiTiet.NGAY).format("DD/MM/YYYY HH:mm"),
                    singletonObject.userId,
                    singletonObject.makhoa,
                    "CMU_LCSC1_CHITIET_INS"
                ].join("```")
            }).done(function (idChiTiet) {
                if(idChiTiet > 0){
                    dataChiTiet.NGAY_LUU_LOG = moment(dataChiTiet.NGAY).format("DD/MM/YYYY HH:mm")
                    dataChiTiet.HUYET_AP = dataChiTiet.HUYET_AP_TREN + "/" + dataChiTiet.HUYET_AP_DUOI;
                    arrLog.push(convertKeyToValueLogHSBA(dataChiTiet));
                    var arrTemp = [];
                    singletonObject.configGroupChamSocCap1.forEach(function (item) {
                        arrTemp = [
                            ...arrTemp,
                            ...dataGroup[item.LOAI + "_ARR"]
                        ]
                    })
                    arrTemp = [
                        ...arrTemp,
                        ...dataNuocNhap.NUOC_NHAP_ARR,
                        ...dataNuocXuat.NUOC_XUAT_ARR
                    ]
                    arrTemp.forEach(function(item){
                        if (item.CHI_SO){
                            if (item.ID_ITEM.toString().indexOf("TEMP") != -1){
                                $.ajax({
                                    url: "cmu_post",
                                    method: "POST",
                                    data: {
                                        url:[
                                            thongtinhsba.thongtinbn.lanChamSocCap1.ID_CHAM_SOC_CAP_1,
                                            item.LOAI_ITEM,
                                            item.TEN_ITEM,
                                            "CMU_LCSC1_CT_ITEM_INS"
                                        ].join("```")
                                    }
                                }).done(function (idItem) {
                                    if(idItem > 0){
                                        themChiSoChiTiet({
                                            idChiTiet: idChiTiet,
                                            idItem: idItem,
                                            CHI_SO: item.CHI_SO
                                        });
                                    }
                                });
                            } else {
                                themChiSoChiTiet({
                                    idChiTiet: idChiTiet,
                                    idItem: item.ID_ITEM,
                                    CHI_SO: item.CHI_SO
                                });
                            }
                            arrLog.push(item.TEN_ITEM + ': ' + item.CHI_SO);
                        }
                    });
                    luuLogHSBATheoBN({
                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                        LOAI: LOGHSBALOAI.CHAMSOCCAP1CHITIET.KEY,
                        NOIDUNGBANDAU: "",
                        NOIDUNGMOI: "Thêm mới chi tiết chăm sóc cấp 1 tờ số " + thongtinhsba.thongtinbn.lanChamSocCap1.TO_SO + ": " + arrLog.join("; "),
                        USERID: singletonObject.userId,
                        ACTION: LOGHSBAACTION.INSERT.KEY,
                    });

                    $("#modalLanChamSocCap1ChiTiet").modal("hide");
                } else if (idChiTiet == '-1'){
                    notifiToClient("Red", "Đợt chăm sóc cấp 1 đã tồn tại ngày giờ");
                } else {
                    notifiToClient("Red",MESSAGEAJAX.ERROR);
                }
            }).fail(function(error) {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }).always(function() {
                hideSelfLoading(idButton);
            });
        }
    });

    $("#lanchamsoccap1_chitiet_action_luu").click(function() {
        var idButton = "lanchamsoccap1_chitiet_action_luu";
        var dataChiTiet = formCSC1ThongTinChiTiet.submission.data;
        var dataGroup = formCSC1ThongTinChiTietGroup.submission.data;
        var dataNuocNhap = formCSC1ThongTinChiTietNuocNhap.submission.data;
        var dataNuocXuat = formCSC1ThongTinChiTietNuocXuat.submission.data;
        var arrLogNew = [];
        var arrLogOld = [];
        if (formCSC1ThongTinChiTiet.checkValidity()){
            showSelfLoading(idButton);
            $.post("cmu_post", {url:[thongtinhsba.thongtinbn.lanChamSocCap1.ID_CHAM_SOC_CAP_1,
                    thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.ID_CHI_TIET,
                    dataChiTiet.SPO2,
                    dataChiTiet.MACH,
                    dataChiTiet.NHIET_DO,
                    dataChiTiet.HUYET_AP_TREN,
                    dataChiTiet.HUYET_AP_DUOI,
                    dataChiTiet.XOAY_TRO,
                    dataChiTiet.CHAM_SOC_DIEU_DUONG,
                    dataChiTiet.DA_NIEM_MAC,
                    dataChiTiet.TRI_GIAC_GLASSGOW,
                    dataChiTiet.GIAC_NGU_NGHI_NGOI,
                    dataChiTiet.VE_SINH_CA_NHAN,
                    dataChiTiet.TINH_THAN,
                    dataChiTiet.VAN_DONG_PHCN,
                    dataChiTiet.GIAO_DUC_SUC_KHOE,
                    moment(dataChiTiet.NGAY).format("DD/MM/YYYY HH:mm"),
                    "CMU_LCSC1_CHITIET_UPD"].join("```")}).done(function (countUPD) {
                if(countUPD > 0){
                    dataChiTiet.NGAY_LUU_LOG = moment(dataChiTiet.NGAY).format("DD/MM/YYYY HH:mm")
                    dataChiTiet.HUYET_AP = dataChiTiet.HUYET_AP_TREN + "/" + dataChiTiet.HUYET_AP_DUOI;
                    var diffLog = getLogHSBAChinhSua(thongTinPhieuTruocKhiChinhSua, dataChiTiet, keyLuuLog);
                    if (diffLog.length > 0) {
                        arrLogOld.push(diffLog[0]);
                        arrLogNew.push(diffLog[1]);
                    }
                    var arrTemp = [];
                    singletonObject.configGroupChamSocCap1.forEach(function (item) {
                        arrTemp = [
                            ...arrTemp,
                            ...dataGroup[item.LOAI + "_ARR"]
                        ]
                    })
                    arrTemp = [
                        ...arrTemp,
                        ...dataNuocNhap.NUOC_NHAP_ARR,
                        ...dataNuocXuat.NUOC_XUAT_ARR
                    ]
                    arrTemp.forEach(function(item){
                        if (item.ID_CHI_SO) {
                            if (item.CHI_SO){
                                $.ajax({
                                    url: "cmu_post",
                                    method: "POST",
                                    data: {url:[item.ID_CHI_SO, item.CHI_SO, "CMU_LCSC1_CT_ITEM_CS_UPD"].join("```")}
                                }).done(function (idItemDetail) {

                                });
                            } else {
                                $.ajax({
                                    url: "cmu_post",
                                    method: "POST",
                                    data: {url:[item.ID_CHI_SO, "CMU_LCSC1_CT_ITEM_CS_DEL"].join("```")}
                                }).done(function (idItemDetail) {

                                });
                            }
                            var foundObject = thongTinPhieuTruocKhiChinhSua.ARRITEM.find(itemOld => itemOld.ID_CHI_SO == item.ID_CHI_SO);
                            if (foundObject && foundObject.CHI_SO != item.CHI_SO){
                                arrLogOld.push(foundObject.TEN_ITEM + ': ' + foundObject.CHI_SO);
                                arrLogNew.push(item.TEN_ITEM + ': ' + (item.CHI_SO || "0"));
                            }
                        } else {
                            if (item.CHI_SO){
                                if (item.ID_ITEM.toString().indexOf("TEMP") != -1){
                                    $.ajax({
                                        url: "cmu_post",
                                        method: "POST",
                                        data: {url:[thongtinhsba.thongtinbn.lanChamSocCap1.ID_CHAM_SOC_CAP_1, item.LOAI_ITEM, item.TEN_ITEM, "CMU_LCSC1_CT_ITEM_INS"].join("```")}
                                    }).done(function (idItem) {
                                        if(idItem > 0){
                                            themChiSoChiTiet({idChiTiet: thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.ID_CHI_TIET, idItem: idItem, CHI_SO: item.CHI_SO});
                                        }
                                    });
                                } else {
                                    themChiSoChiTiet({idChiTiet: thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.ID_CHI_TIET, idItem: item.ID_ITEM, CHI_SO: item.CHI_SO});
                                }
                                arrLogNew.push(item.TEN_ITEM + ': ' + item.CHI_SO);
                            }
                        }
                    });
                    if (arrLogOld.length > 0 || arrLogNew.length > 0){
                        luuLogHSBATheoBN({
                            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                            LOAI: LOGHSBALOAI.CHAMSOCCAP1CHITIET.KEY,
                            NOIDUNGBANDAU: arrLogOld.join("; "),
                            NOIDUNGMOI: arrLogNew.join("; "),
                            USERID: singletonObject.userId,
                            ACTION: LOGHSBAACTION.EDIT.KEY,
                        });
                    }
                    $("#modalLanChamSocCap1ChiTiet").modal("hide");
                } else if (idChiTiet == '-1'){
                    notifiToClient("Red", "Đợt chăm sóc cấp 1 đã tồn tại ngày giờ");
                } else {
                    notifiToClient("Red",MESSAGEAJAX.ERROR);
                }
            }).fail(function(error) {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }).always(function() {
                hideSelfLoading(idButton);
            });
        }
    });

    $("#lanchamsoccap1_chitiet_action_luumau").click(function () {
        $("#modalLuuMauChiTietChamSocCap1").modal("show");
    });

    $("#lanchamsoc1_action_luumau").click(function() {
        var idButton = "lanchamsoc1_action_luumau";
        var dataChiTiet = formCSC1ThongTinChiTiet.submission.data;
        var dataToanThan = formCSC1ThongTinChiTietToanThan.submission.data;
        var dataHoHap = formCSC1ThongTinChiTietHoHap.submission.data;
        var dataTuanHoan = formCSC1ThongTinChiTietTuanHoan.submission.data;
        var dataThem = formCSC1ThongTinChiTietThem.submission.data;
        var dataNuocNhap = formCSC1ThongTinChiTietNuocNhap.submission.data;
        var dataNuocXuat = formCSC1ThongTinChiTietNuocXuat.submission.data;
        var arrLog = [];
        if (formCSC1ThongTinChiTiet.checkValidity()){
            showSelfLoading(idButton);
            $.post("cmu_post", {url:[
                    singletonObject.dvtt,
                    $("#lanchamsoc1_tenmauluu").val(),
                    dataChiTiet.SPO2,
                    dataChiTiet.MACH,
                    dataChiTiet.NHIET_DO,
                    dataChiTiet.HUYET_AP_TREN,
                    dataChiTiet.HUYET_AP_DUOI,
                    dataChiTiet.XOAY_TRO,
                    dataChiTiet.CHAM_SOC_DIEU_DUONG,
                    dataChiTiet.DA_NIEM_MAC,
                    dataChiTiet.TRI_GIAC_GLASSGOW,
                    dataChiTiet.GIAC_NGU_NGHI_NGOI,
                    dataChiTiet.VE_SINH_CA_NHAN,
                    dataChiTiet.TINH_THAN,
                    dataChiTiet.VAN_DONG_PHCN,
                    dataChiTiet.GIAO_DUC_SUC_KHOE,
                    singletonObject.userId,
                    singletonObject.makhoa,
                    "CMU_LCSC1_CHITIET_MAU_INS"].join("```")}).done(function (id) {
                if(id > 0) {
                    var arrTemp = [
                        ...dataToanThan.TOAN_THAN_ARR,
                        ...dataHoHap.HO_HAP_ARR,
                        ...dataTuanHoan.TUAN_HOAN_ARR,
                        ...dataThem.THONG_TIN_THEM_ARR,
                        ...dataNuocNhap.NUOC_NHAP_ARR,
                        ...dataNuocXuat.NUOC_XUAT_ARR,
                    ];
                    arrTemp.forEach(function (item) {
                        $.ajax({
                            url: "cmu_post",
                            method: "POST",
                            data: {url: [id, item.LOAI_ITEM, item.TEN_ITEM, item.CHI_SO, "CMU_LCSC1_CT_ITEM_MAU_INS"].join("```")}
                        }).done(function (idItem) {

                        });
                    });
                    $("#modalLuuMauChiTietChamSocCap1").modal("hide");
                } else {
                    notifiToClient("Red",MESSAGEAJAX.ERROR);
                }
            }).fail(function(error) {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }).always(function() {
                hideSelfLoading(idButton);
            });
        }
    });

    $("#lanchamsoccap1_chitiet_action_laymau").click(function (){
        var idButton = "lanchamsoccap1_chitiet_action_laymau";
        showSelfLoading(idButton);
        $("#lanchamsoc1_danhsachmau").empty();
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, singletonObject.makhoa, 'CMU_CSC1_CHITIET_MAU_SEL'])).done(function(data) {
            if(data){
                data.forEach(function(item){
                    $("#lanchamsoc1_danhsachmau").append('<option value="'+item.ID+'">'+item.TEN_MAU+'</option>');
                });
            }
        });
        hideSelfLoading(idButton);
        $("#modalLayMauChiTietChamSocCap1").modal("show");
    });

    $("#lanchamsoc1_action_xoamau").click(function() {
        var idButton = "lanchamsoc1_action_xoamau";
        showSelfLoading(idButton);
        confirmToClient("Bạn có chắc chắn muốn xóa mẫu này không?", function() {
            $.ajax({
                url: "cmu_post",
                method: "POST",
                data: {url:[$("#lanchamsoc1_danhsachmau").val(), "CMU_CSC1_CHITIET_MAU_DEL"].join("```")}
            }).done(function (countDel) {
                if(countDel > 0){
                    notifiToClient("Green",MESSAGEAJAX.DEL_SUCCESS);
                    $("#modalLayMauChiTietChamSocCap1").modal("hide");
                } else {
                    notifiToClient("Red",MESSAGEAJAX.ERROR);
                }
            }).fail(function(error) {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }).always(function() {
                hideSelfLoading(idButton);
            });
        }, function() {
            hideSelfLoading(idButton);
        });
    });

    $("#lanchamsoc1_action_laymau").click(function() {
        var idButton = "lanchamsoc1_action_laymau";
        showSelfLoading(idButton);
        $.get("cmu_getlist?url="+convertArray([$("#lanchamsoc1_danhsachmau").val(),
            'CMU_LCSC1_CHITIET_MAU_SELID'])).done(function(data) {
            var temp = { ...JSON.parse(JSON.stringify(thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet)) };
            if (temp.ID_CHI_TIET) {
                thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet = JSON.parse(JSON.stringify(data[0]));
                thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.ID_CHI_TIET = temp.ID_CHI_TIET;
                thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NGAY_TAO = temp.NGAY_TAO;
            } else {
                thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet = JSON.parse(JSON.stringify(data[0]));
            }
            var index = 1000;
            var dataList = [
                ...temp.TOAN_THAN_ARR,
                ...temp.HO_HAP_ARR,
                ...temp.TUAN_HOAN_ARR,
                ...temp.THONG_TIN_THEM_ARR,
                ...temp.NUOC_NHAP_ARR,
                ...temp.NUOC_XUAT_ARR,
            ];
            data.forEach(function(item){
                if (dataList.length == 0){
                    dataList.push(
                        {
                            ID_ITEM: "TEMP_" + index,
                            LOAI_ITEM: item.LOAI_ITEM,
                            TEN_ITEM: item.TEN_ITEM,
                            CHI_SO: item.CHI_SO,
                        }
                    );
                    index++;
                } else {
                    var tempData = dataList.filter(function (itemFilter) {
                        if (itemFilter.LOAI_ITEM == item.LOAI_ITEM && itemFilter.TEN_ITEM == item.TEN_ITEM){
                            return itemFilter;
                        }
                    });
                    if (tempData.length == 0){
                        dataList.push(
                            {
                                ID_ITEM: "TEMP_" + index,
                                LOAI_ITEM: item.LOAI_ITEM,
                                TEN_ITEM: item.TEN_ITEM,
                                CHI_SO: item.CHI_SO,
                            }
                        );
                        index++;
                    } else {
                        tempData[0].CHI_SO = item.CHI_SO;
                    }
                }
            });
            var toanThanFilter = dataList.filter(function (item) {
                if (item.ID_ITEM != null && item.LOAI_ITEM == "TOAN_THAN") {
                    return item;
                }
            });
            thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.TOAN_THAN_ARR = toanThanFilter;

            var hoHapFilter = dataList.filter(function (item) {
                if (item.ID_ITEM != null && item.LOAI_ITEM == "HO_HAP") {
                    return item;
                }
            });
            thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.HO_HAP_ARR = hoHapFilter;

            var tuanHoanFilter = dataList.filter(function (item) {
                if (item.ID_ITEM != null && item.LOAI_ITEM == "TUAN_HOAN") {
                    return item;
                }
            });
            thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.TUAN_HOAN_ARR = tuanHoanFilter;

            var themFilter = dataList.filter(function (item) {
                if (item.ID_ITEM != null && item.LOAI_ITEM == "THONG_TIN_THEM") {
                    return item;
                }
            });
            thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.THONG_TIN_THEM_ARR = themFilter;

            var nuocNhapFilter = dataList.filter(function (item) {
                if (item.ID_ITEM != null && item.LOAI_ITEM == "NUOC_NHAP") {
                    return item;
                }
            });
            thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NUOC_NHAP_ARR = nuocNhapFilter;

            var nuocXuatFilter = dataList.filter(function (item) {
                if (item.ID_ITEM != null && item.LOAI_ITEM == "NUOC_XUAT") {
                    return item;
                }
            });
            thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NUOC_XUAT_ARR = nuocXuatFilter;

            createFormChiTiet();
            $("#modalLayMauChiTietChamSocCap1").modal("hide");
            hideSelfLoading(idButton);
        });
    });

    $("#dli_action_them").click(function() {
        var idButton = "dli_action_them";
        if ($("#dli_ten").val()){
            showSelfLoading(idButton);
            $.post("cmu_post", {url:[singletonObject.dvtt,
                    $("#dli_loai").val(),
                    $("#dli_ten").val(),
                    $("#dli_motaloai").val(),
                    singletonObject.makhoa,
                    singletonObject.userId,
                    "CMU_CSC1_DATALIST_ITEM_INS"].join("```")}).done(function (id) {
                if(id > 0){
                    $("#dli_ten").val("");
                    $("#dli_motaloai").val("");
                    reloadDataListGrid();
                } else {
                    if(id == '-1'){
                        return notifiToClient("Red",MESSAGEAJAX.ERROR);
                    }
                    if (id == '-2'){
                        return notifiToClient("Red", "Dữ liệu đã tồn tại");
                    }
                }
            }).fail(function(error) {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }).always(function() {
                hideSelfLoading(idButton);
            });
        } else {
            notifiToClient("Red", "Vui lòng nhập tên");
        }
    });

    $("#lanchamsoccap1_muctieu_action_luu").click(function() {
        var chanDoanSubmit = formCSC1ChanDoanMucTieu.submission.data;
        $.post("cmu_post", {url:[thongtinhsba.thongtinbn.lanChamSocCap1.ID_CHAM_SOC_CAP_1,
                chanDoanSubmit.ID_CHAN_DOAN,
                chanDoanSubmit.ICD_DIEUDUONG,
                chanDoanSubmit.TENICD_DIEUDUONG,
                "CMU_LCSC1_CHANDOAN_UPD"].join("```")
        }).done(function (id) {
            if (id == '-1'){
                return notifiToClient("Red", "Chẩn đoán điều dưỡng đã tồn tại");
            } else {
                var arrLogNew = [];
                var arrLogOld = [];
                var diffLog = getLogHSBAChinhSua(singletonObject.thongTinCSC1TruocKhiChinhSua.chanDoan, chanDoanSubmit, keyLuuLog);
                if (diffLog.length > 0) {
                    arrLogOld.push(diffLog[0]);
                    arrLogNew.push(diffLog[1]);
                }
                $.ajax({
                    url: "cmu_post",
                    method: "POST",
                    async: false,
                    data: {
                        url:[
                            chanDoanSubmit.ID_CHAN_DOAN,
                            "CMU_LCSC1_MUCTIEU_DEL"
                        ].join("```")
                    },
                    success: function (countDel) {
                        var count = 0;
                        for(var i = 0; i < chanDoanSubmit.MUC_TIEU_ARR.length; i++) {
                            $.ajax({
                                url: "cmu_post",
                                method: "POST",
                                async: false,
                                data: {
                                    url:[
                                        chanDoanSubmit.ID_CHAN_DOAN,
                                        thongtinhsba.thongtinbn.lanChamSocCap1.ID_CHAM_SOC_CAP_1,
                                        chanDoanSubmit.MUC_TIEU_ARR[i].IS_MUC_TIEU ? "1" : "0",
                                        chanDoanSubmit.MUC_TIEU_ARR[i].MUC_TIEU,
                                        chanDoanSubmit.MUC_TIEU_ARR[i].ID_MUC_TIEU ? chanDoanSubmit.MUC_TIEU_ARR[i].MA_NGUOI_TAO : singletonObject.userId,
                                        chanDoanSubmit.MUC_TIEU_ARR[i].ID_MUC_TIEU ? chanDoanSubmit.MUC_TIEU_ARR[i].MA_KHOA_TAO : singletonObject.makhoa,
                                        "CMU_LCSC1_MUCTIEU_INS"
                                    ].join("```")
                                },
                                success: function (countIns) {
                                    if(countIns > 0){
                                        count++;
                                    }
                                }
                            });
                        }
                        if (count == chanDoanSubmit.MUC_TIEU_ARR.length) {
                            var maxLength = chanDoanSubmit.MUC_TIEU_ARR.length > singletonObject.thongTinCSC1TruocKhiChinhSua.chanDoan.MUC_TIEU_ARR.length ? chanDoanSubmit.MUC_TIEU_ARR.length : singletonObject.thongTinCSC1TruocKhiChinhSua.chanDoan.MUC_TIEU_ARR.length;
                            var stringLogOld = "";
                            var stringLogNew = "";
                            for (var i = 0; i < maxLength; i++) {
                                stringLogOld += singletonObject.thongTinCSC1TruocKhiChinhSua.chanDoan.MUC_TIEU_ARR[i] ?
                                    singletonObject.thongTinCSC1TruocKhiChinhSua.chanDoan.MUC_TIEU_ARR[i].MUC_TIEU + " " + (singletonObject.thongTinCSC1TruocKhiChinhSua.chanDoan.MUC_TIEU_ARR[i].IS_MUC_TIEU == true ? "(Đã thực hiện)" : "(Chưa thực hiện)") + "; " : "";
                                stringLogNew += chanDoanSubmit.MUC_TIEU_ARR[i] ?
                                    chanDoanSubmit.MUC_TIEU_ARR[i].MUC_TIEU + " " + (chanDoanSubmit.MUC_TIEU_ARR[i].IS_MUC_TIEU == true ? "(Đã thực hiện)" : "(Chưa thực hiện)") + "; " : "";
                            }
                            if (stringLogOld != stringLogNew){
                                arrLogOld.push(stringLogOld);
                                arrLogNew.push(stringLogNew);
                            }
                            if (arrLogOld.length > 0 || arrLogNew.length > 0){
                                luuLogHSBATheoBN({
                                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                    LOAI: LOGHSBALOAI.CHAMSOCCAP1.KEY,
                                    NOIDUNGBANDAU: arrLogOld.join("; "),
                                    NOIDUNGMOI: arrLogNew.join("; "),
                                    USERID: singletonObject.userId,
                                    ACTION: LOGHSBAACTION.EDIT.KEY,
                                });
                            }
                            notifiToClient("Green",MESSAGEAJAX.EDIT_SUCCESS);
                            $("#modalFormCSC1ChanDoanMucTieu").modal("hide");
                            instanceGridChamSocCap1ChanDoanDieuDuong();
                        }
                    }
                });
            }
        });
    });

    $("#csc1_chitiet_lammoi").click(function(){
        instanceGridChamSocCap1ChiTiet();
    });

    function themChiSoChiTiet(object){
        $.ajax({
            url: "cmu_post",
            method: "POST",
            async: false,
            data: {
                url:[
                    object.idChiTiet,
                    object.idItem,
                    object.CHI_SO,
                    "CMU_LCSC1_CT_ITEM_CS_INS"
                ].join("```")
            }
        }).done(function (idItemDetail) {

        });
    }

    function loadChiTietById(dataID, upd = null) {
        $.get("cmu_getlist?url="+convertArray([
            singletonObject.dvtt,
            dataID.ID_CHI_TIET,
            thongtinhsba.thongtinbn.lanChamSocCap1.ID_CHAM_SOC_CAP_1,
            'CMU_LCSC1_CHITIET_SELID'])
        ).done(function(data) {
            if (data.length > 0 && data[0].ID_CHI_TIET) {
                $("#modalLanChamSocCap1ChiTiet .edit").show();
                $("#modalLanChamSocCap1ChiTiet .add").hide();
                thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet = JSON.parse(JSON.stringify(data[0]));
                thongTinPhieuTruocKhiChinhSua = { ...JSON.parse(JSON.stringify(data[0])) };
                thongTinPhieuTruocKhiChinhSua.ARRITEM = [ ...JSON.parse(JSON.stringify(data)) ];
                thongTinPhieuTruocKhiChinhSua.NGAY_LUU_LOG = data[0].NGAY_TAO;
            } else {
                $("#modalLanChamSocCap1ChiTiet .edit").hide();
                $("#modalLanChamSocCap1ChiTiet .add").show();
                thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet = {};
                if (dataID.NGAY) {
                    thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NGAY_TAO = dataID.NGAY;
                } else {
                    thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NGAY_TAO = moment().format("DD/MM/YYYY HH:mm");
                }
            }
            singletonObject.configGroupChamSocCap1.forEach(function (itemGroup) {
                var groupFilter = data.filter(function (item) {
                    if (item.ID_ITEM != null && item.LOAI_ITEM == itemGroup.LOAI) {
                        return item;
                    }
                });
                thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet[itemGroup.LOAI + "_ARR"] = groupFilter;
            });

            if (!thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.hasOwnProperty('NUOC_NHAP_ARR')) {
                thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NUOC_NHAP_ARR = [];
            }
            if (!thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.hasOwnProperty('NUOC_XUAT_ARR')) {
                thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NUOC_XUAT_ARR = [];
            }
            createFormChiTiet(upd);
        });
    }

    function veBieuDoChamSocCap1(objectChart) {
        var canvas = document.getElementById('myChartCSC1');
        var ctx = canvas.getContext('2d');
        var canvasWidth = canvas.width;
        var canvasHeight = canvas.height;
        ctx.clearRect(0, 0, canvasWidth, canvasHeight)
        var margin = 10;
        var Hmargin = 20;
        var chartWidth = canvasWidth - 2 * margin;
        var chartHeight = canvasHeight - 2 * Hmargin;
        var barWidth = chartWidth / 24;

        function drawChart() {
            ctx.lineWidth = 1;
            ctx.strokeStyle = '#000000';
            ctx.fillStyle = '#000000';
            ctx.beginPath();
            for (let i = 0; i <= 8; i++) {
                var yPos = Hmargin + i * (chartHeight / 8);
                ctx.beginPath();
                ctx.moveTo(margin, yPos);
                ctx.lineTo(canvasWidth - margin, yPos);
                ctx.stroke();
            }
            for (let i = 0; i <= 24; i++) {
                var xPos = margin + i * barWidth;
                ctx.beginPath();
                ctx.moveTo(xPos, Hmargin);
                ctx.lineTo(xPos, canvasHeight - Hmargin);
                ctx.setLineDash([5, 5]);
                ctx.stroke();
            }
            ctx.stroke();
        }

        function drawMachChart(data) {
            ctx.lineWidth = 1;
            ctx.setLineDash([]);
            ctx.fillStyle = '#ff4136';
            ctx.strokeStyle = '#ff4136';
            var x = 0;
            var y = 0;
            var stepH = (chartHeight / 8);
            for (let i = 0; i < data.length; i++) {
                if(data[i]){
                    x = margin + i * barWidth;
                    y = canvasHeight - Hmargin - ((data[i]-40)/20) * stepH;
                    if (y <= canvasHeight - Hmargin && y >= Hmargin) {
                        ctx.beginPath();
                        ctx.arc(x, y, 4, 0, 2 * Math.PI);
                        ctx.fill();
                    }
                    if(data[i] <= 40) {
                        ctx.font = "bold 15px Arial";
                        ctx.fillText("M:"+data[i], x - margin, canvasHeight -stepH );
                    }
                }
            }
            for (let i = 0; i < data.length; i++) {
                if (data[i]){
                    x = margin + i * barWidth;
                    y = canvasHeight - Hmargin - ((data[i]-40)/20) * stepH;
                    if (i === 0  ) {
                        ctx.moveTo(x, y);
                    } else {
                        if (y > canvasHeight - Hmargin) {
                            y = canvasHeight - Hmargin;
                        }
                        ctx.lineTo(x, y);
                    }
                }
            }
            ctx.stroke();
        }

        function drawDownArrow(x, y, size, text, draw) {
            ctx.font = "bold 14px Arial";
            ctx.fillStyle = "#09329a";
            ctx.beginPath();
            if (draw == 1) {
                ctx.moveTo(x, y);
                ctx.lineTo(x + size, y - size);
                ctx.moveTo(x, y);
                ctx.lineTo(x - size, y - size);
                ctx.fillText(text, x-margin, y - 10);
            } else {
                ctx.fillText(text, x-margin, y - 5);
            }
            ctx.stroke();
        }

        function drawUpArrow(x, y, size, text, draw) {
            ctx.font = "bold 14px Arial";
            ctx.fillStyle = "#09329a";
            ctx.beginPath();
            if (draw == 1) {
                ctx.moveTo(x, y);
                ctx.lineTo(x + size, y + size);
                ctx.moveTo(x, y);
                ctx.lineTo(x - size, y + size);
                ctx.fillText(text, x - margin, y + 20);
            } else {
                ctx.fillText(text, x - margin, y + 10);
            }
            ctx.stroke();
        }

        function drawHuyetap(dataTamtruong, dataTamthu) {
            var stepH = (chartHeight/8);
            ctx.strokeStyle = '#0074d9';
            ctx.lineWidth = 2;
            for (let i = 0; i < dataTamtruong.length; i++) {
                if (dataTamtruong[i]){
                    var xPos = margin + i * barWidth;
                    if(dataTamtruong[i] < 20 && dataTamthu[i] < 20) {
                        ctx.beginPath();
                        ctx.fillText(dataTamtruong[i]+"/"+dataTamthu[i], xPos - 10+2, chartHeight  - stepH);
                        ctx.stroke();
                    } else {
                        var yTamThu = 0;
                        var drawTamThu = 1;
                        var yTamTruong = 0;
                        var drawTamTruong = 1;
                        if (dataTamthu[i] <= 120) {
                            yTamThu = chartHeight + Hmargin - ((dataTamthu[i]-20)/20) * stepH;
                        } else {
                            var tamThuTemp = dataTamthu[i] - 120;
                            yTamThu = (stepH * 3 + Hmargin) - (tamThuTemp/50) * stepH;
                        }
                        if (dataTamtruong[i] <= 120) {
                            yTamTruong = chartHeight + Hmargin - ((dataTamtruong[i]-20)/20) * stepH;
                        } else {
                            var tamTruongTemp = dataTamtruong[i] - 120;
                            yTamTruong = (stepH * 3 + Hmargin) - (tamTruongTemp/50) * stepH;
                        }
                        if (yTamThu > canvasHeight - Hmargin) {
                            yTamThu = canvasHeight - Hmargin;
                            drawTamThu = 0;
                        }
                        if (yTamThu < Hmargin) {
                            yTamThu = Hmargin;
                            drawTamThu = 0;
                        }
                        if (yTamTruong > canvasHeight - Hmargin) {
                            yTamTruong = canvasHeight - Hmargin;
                            drawTamTruong = 0;
                        }
                        if (yTamTruong < Hmargin) {
                            yTamTruong = Hmargin;
                            drawTamTruong = 0;
                        }
                        drawDownArrow(xPos, yTamThu,8, dataTamthu[i], drawTamThu);
                        drawUpArrow(xPos, yTamTruong, 8, dataTamtruong[i], drawTamTruong);
                        ctx.beginPath();
                        ctx.moveTo(xPos, yTamThu);
                        ctx.lineTo(xPos, yTamTruong);
                        ctx.stroke();
                    }
                }
            }
        }

        function drawNhietDoChart(data) {
            ctx.lineWidth = 1;
            ctx.setLineDash([]);
            ctx.fillStyle = '#50008d';
            ctx.strokeStyle = '#50008d';
            var x = 0;
            var y = 0;
            var stepH = (chartHeight / 8);
            for (let i = 0; i < data.length; i++) {
                if (data[i]){
                    x = margin + i * barWidth;
                    y = canvasHeight - Hmargin - (data[i]-34) * stepH;
                    if (y <= canvasHeight - Hmargin && y >= Hmargin) {
                        ctx.beginPath();
                        ctx.fillRect(x-3, y-3, 6, 6);
                    }
                    if(data[i] < 34) {
                        ctx.font = "bold 15px Arial";
                        ctx.fillText("t:"+data[i], x - margin, canvasHeight - stepH - 25);
                    }
                }
            }

            for (let i = 0; i < data.length; i++) {
                if (data[i]){
                    x = margin + i * barWidth;
                    y = canvasHeight - Hmargin - (data[i]-34) * stepH;
                    if (y > canvasHeight - Hmargin) {
                        y = canvasHeight - Hmargin;
                    }
                    if (i === 0  ) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
            }
            ctx.stroke();
        }

        function drawSPO2Chart(data) {
            ctx.lineWidth = 1;
            ctx.setLineDash([]);
            ctx.fillStyle = '#1c8c00';
            ctx.strokeStyle = '#1c8c00';
            var x = 0;
            var y = 0;
            var stepH = (chartHeight / 8);
            for (let i = 0; i < data.length; i++) {
                if (data[i]){
                    x = margin + i * barWidth;
                    if(data[i] <= 92){
                        y = canvasHeight - Hmargin - ((data[i]-80)/2) * stepH;
                    } else {
                        var itemTemp = data[i] - 92;
                        y = (stepH * 2 + Hmargin) - (itemTemp/4) * stepH;
                    }
                    if (y <= canvasHeight - Hmargin && y >= Hmargin) {
                        ctx.beginPath();
                        ctx.moveTo(x, y-5);
                        ctx.lineTo(x-5, y+5);
                        ctx.lineTo(x+5, y+5);
                        ctx.fill();
                    }
                    if(data[i] < 80) {
                        ctx.font = "bold 15px Arial";
                        ctx.fillText("S:"+data[i], x - margin, canvasHeight - stepH - 10);
                    }
                }
            }
            for (let i = 0; i < data.length; i++) {
                if(data[i]){
                    x = margin + i * barWidth;
                    if(data[i] <= 92){
                        y = canvasHeight - Hmargin - ((data[i]-80)/2) * stepH;
                    } else {
                        var itemTemp = data[i] - 92;
                        y = (stepH * 2 + Hmargin) - (itemTemp/4) * stepH;
                    }
                    if (y > canvasHeight - Hmargin) {
                        y = canvasHeight - Hmargin;
                    }
                    if (y < Hmargin){
                        y = Hmargin;
                    }
                    if (i === 0  ) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
            }
            ctx.stroke();
        }
        drawChart();
        drawMachChart(objectChart.MACH);
        drawHuyetap(objectChart.HUYET_AP_DUOI, objectChart.HUYET_AP_TREN);
        drawNhietDoChart(objectChart.NHIET_DO);
        drawSPO2Chart(objectChart.SPO2);
    }

    function createFormChiTiet(upd = null) {
        Formio.createForm(document.getElementById('formCSC1ThongTinChiTiet'),
            {
                "display": "form",
                "components": [
                    {
                        label: "",
                        key: "wrap_benhchinh",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Ngày, giờ",
                                        "key": "NGAY",
                                        "type": "datetime",
                                        "format": "dd/MM/yyyy HH:mm",
                                        validate: {
                                            required: true,
                                        },
                                        "timePicker": {
                                            "showMeridian": false
                                        },
                                        customClass: "pr-2",
                                        "widget": {
                                            "type": "calendar",
                                            "enableTime": true,
                                            "time_24hr": true,
                                        },
                                        "datePicker": {
                                            minDate: moment(thongtinhsba.thongtinbn.lanChamSocCap1.NGAY_TAO_PHIEU, ['DD/MM/YYYY HH:mm']).format("YYYY-MM-DDTHH:mm:ssZ"),
                                        },
                                    }
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "SPO2",
                                        "key": "SPO2",
                                        "type": "number",
                                        customClass: "pr-2",
                                        validate: {
                                            // required: true,
                                        },
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Mạch",
                                        "key": "MACH",
                                        "type": "number",
                                        customClass: "pr-2",
                                        validate: {
                                            // required: true,
                                        },
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Nhiệt độ",
                                        "key": "NHIET_DO",
                                        "type": "number",
                                        validate: {
                                            // required: true,
                                        },
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        "label": "Columns",
                        "columns": [
                            {
                                "components": [
                                    {
                                        "label": "Huyết áp trên",
                                        "key": "HUYET_AP_TREN",
                                        "type": "number",
                                        customClass: "pr-2",
                                        validate: {
                                            // required: true,
                                        },
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Huyết áp dưới",
                                        "key": "HUYET_AP_DUOI",
                                        "type": "number",
                                        customClass: "pr-2",
                                        validate: {
                                            // required: true,
                                        },
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Xoay trở NB: (N, P, T), tự do (TD)",
                                        "customClass": "pr-2",
                                        "key": "XOAY_TRO",
                                        "type": "textfield",
                                    }
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Chăm sóc điều dưỡng",
                                        "customClass": "",
                                        "key": "CHAM_SOC_DIEU_DUONG",
                                        "type": "number",
                                    }
                                ],
                                "width": 3,
                                "size": "md",
                            },
                        ],
                        "customClass": "ml-0 mr-0",
                        "hideLabel": true,
                        "key": "columns",
                        "type": "columns",
                    },
                ]
            }
        ).then(function (form) {
            formCSC1ThongTinChiTiet = form;
            var data = thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet;
            data.NGAY = (data.NGAY_TAO? moment(data.NGAY_TAO, ['DD/MM/YYYY HH:mm']): moment()).toISOString();
            form.submission =  {
                data: {
                    ...data
                }
            };
            createFormChiTietGroup(upd);
            createFormChiTietNuocNhap();
            createFormChiTietNuocXuat();
            createFormChamSocCap1Right();
            $("#modalLanChamSocCap1ChiTiet").modal("show");
        });
    }

    function createFormChiTietNuocNhap() {
        Formio.createForm(document.getElementById('formCSC1ThongTinChiTietNuocNhap'),
            {
                "display": "form",
                "components": [
                    {
                        "key": "p-lydovaovien",
                        "type": "panel",
                        "label": "Nước nhập",
                        "title": "Nước nhập",
                        "customClass": "hsba-tabs-wrap",
                        "components": [
                            {
                                "label": "Columns",
                                "columns": [
                                    {
                                        "components": [
                                            {
                                                "tag": "label",
                                                "content": "Nước nhập",
                                                "key": "htmllabel",
                                                "type": "htmlelement",
                                            },
                                        ],
                                        "width": 2,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "",
                                                "customClass": "pl-2",
                                                "key": "TEN_NUOC_NHAP",
                                                "type": "textfield",
                                                "placeholder": "Tên nước nhập"
                                            }
                                        ],
                                        "width": 8,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "Thêm",
                                                "customClass": "pl-2 w-100",
                                                "action": "event",
                                                "theme": "primary",
                                                "size": "sm",
                                                "event": "themNuocNhap",
                                                "key": "THEM_NUOC_NHAP",
                                                "type": "button",
                                            }
                                        ],
                                        "width": 2,
                                        "size": "md",
                                    },
                                ],
                                "customClass": "ml-0 mr-0",
                                "hideLabel": true,
                                "key": "columns",
                                "type": "columns",
                            },
                            {
                                "label": "",
                                "disableAddingRemovingRows": true,
                                "addAnother": "Thêm nước nhập",
                                "initEmpty": true,
                                "customClass": "ml-2",
                                "key": "NUOC_NHAP_ARR",
                                "type": "datagrid",
                                "components": [
                                    {
                                        "label": "Columns",
                                        "columns": [
                                            {
                                                "components": [
                                                    {
                                                        "label": "",
                                                        "customClass": "pr-2",
                                                        "key": "TEN_ITEM",
                                                        "type": "textfield",
                                                        "disabled": true,
                                                    }
                                                ],
                                                "width": 8,
                                                "size": "md",
                                            },
                                            {
                                                "components": [
                                                    {
                                                        "label": "",
                                                        "customClass": "pr-2",
                                                        "key": "CHI_SO",
                                                        "type": "number",
                                                    }
                                                ],
                                                "width": 2,
                                                "size": "md",
                                            },
                                            {
                                                "components": [
                                                    {
                                                        "label": "Xoá",
                                                        "action": "event",
                                                        "theme": "danger",
                                                        "size": "sm",
                                                        "event": "xoaNuocNhap",
                                                        "key": "XOA_NUOC_NHAP",
                                                        "type": "button",
                                                    }
                                                ],
                                                "width": 2,
                                                "size": "md",
                                            },
                                        ],
                                        "customClass": "pl-2 ml-0 mr-0",
                                        "hideLabel": true,
                                        "key": "columns",
                                        "type": "columns",
                                    }
                                ]
                            },
                        ]
                    },
                ]
            }
        ).then(function (form) {
            formCSC1ThongTinChiTietNuocNhap = form;
            var idItem = 0;
            form.on("themNuocNhap", function(e) {
                var inputElement = form.getComponent('TEN_NUOC_NHAP');
                var dataGridData = thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NUOC_NHAP_ARR;
                if (!inputElement.getValue()){
                    return notifiToClient("Red","Vui lòng nhập tên nuớc nhập");
                }
                if (kiemTraSoLuongMax() == false) {
                    return notifiToClient("Red", "Chỉ được nhập tối đa " + singletonObject.maxRowCSC1 + " thông tin");
                }
                dataGridData.push({
                    ID_ITEM: "TEMP_" + (idItem + 1),
                    LOAI_ITEM: "NUOC_NHAP",
                    TEN_ITEM: inputElement.getValue(),
                    CHI_SO: null
                });
                inputElement.setValue("");
                form.submission =  {
                    data: {
                        NUOC_NHAP_ARR: dataGridData
                    }
                };
                thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NUOC_NHAP_ARR = dataGridData;
                idItem++;
            });
            form.on("xoaNuocNhap", function(e) {
                var arrTemp = []
                var dataGridData = thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NUOC_NHAP_ARR;
                var dataGridDataAfter = dataGridData.filter(function(item) {
                    return item.ID_ITEM != e.ID_ITEM;
                });
                if (e.ID_ITEM.toString().indexOf("TEMP") != -1) {
                    arrTemp = dataGridDataAfter;
                    thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NUOC_NHAP_ARR = arrTemp;
                    createFormChiTietNuocNhap();
                } else {
                    confirmToClient("Xoá phần này này sẽ xoá ở tất cả các mốc thời gian, bạn có chắc chắn xoá?", function() {
                        $.ajax({
                            url: "cmu_post",
                            method: "POST",
                            async: false,
                            data: {url:[e.ID_ITEM.toString(), "CMU_LCSC1_ITEM_DEL"].join("```")}
                        }).done(function (countDel) {
                            if(countDel > 0) {
                                luuLogHSBATheoBN({
                                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                    LOAI: LOGHSBALOAI.CHAMSOCCAP1NUOCNHAP.KEY,
                                    NOIDUNGBANDAU: "",
                                    NOIDUNGMOI: "Xoá nước nhập '" + e.TEN_ITEM + "' của tất cả mốc thời gian ở tờ chăm sóc cấp 1 số " + thongtinhsba.thongtinbn.lanChamSocCap1.TO_SO,
                                    USERID: singletonObject.userId,
                                    ACTION: LOGHSBAACTION.DELETE.KEY,
                                });
                                notifiToClient("Green", MESSAGEAJAX.DEL_SUCCESS);
                                arrTemp = dataGridDataAfter;
                                thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NUOC_NHAP_ARR = arrTemp;
                                createFormChiTietNuocNhap();
                            }
                        });
                    }, function() {
                        arrTemp = dataGridData;
                        thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NUOC_NHAP_ARR = arrTemp;
                        createFormChiTietNuocNhap();
                    });
                }
            });
            form.submission =  {
                data: {
                    NUOC_NHAP_ARR: thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NUOC_NHAP_ARR
                }
            };
        });
    }

    function createFormChiTietNuocXuat() {
        Formio.createForm(document.getElementById('formCSC1ThongTinChiTietNuocXuat'),
            {
                "display": "form",
                "components": [
                    {
                        "key": "p-lydovaovien",
                        "type": "panel",
                        "label": "Nước xuất",
                        "title": "Nước xuất",
                        "customClass": "hsba-tabs-wrap",
                        "components": [
                            {
                                "label": "Columns",
                                "columns": [
                                    {
                                        "components": [
                                            {
                                                "tag": "label",
                                                "content": "Nước xuất",
                                                "key": "htmllabel",
                                                "type": "htmlelement",
                                            },
                                        ],
                                        "width": 2,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "",
                                                "customClass": "pl-2",
                                                "key": "TEN_NUOC_XUAT",
                                                "type": "textfield",
                                                "placeholder": "Tên nước xuất",
                                            }
                                        ],
                                        "width": 8,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "Thêm",
                                                "customClass": "pl-2 w-100",
                                                "action": "event",
                                                "theme": "primary",
                                                "size": "sm",
                                                "event": "themNuocXuat",
                                                "key": "THEM_NUOC_XUAT",
                                                "type": "button",
                                            }
                                        ],
                                        "width": 2,
                                        "size": "md",
                                    },
                                ],
                                "customClass": "ml-0 mr-0",
                                "hideLabel": true,
                                "key": "columns",
                                "type": "columns",
                            },
                            {
                                "label": "",
                                "disableAddingRemovingRows": true,
                                "addAnother": "Thêm nước xuất",
                                "initEmpty": true,
                                "customClass": "ml-2",
                                "key": "NUOC_XUAT_ARR",
                                "type": "datagrid",
                                "components": [
                                    {
                                        "label": "Columns",
                                        "columns": [
                                            {
                                                "components": [
                                                    {
                                                        "label": "",
                                                        "customClass": "pr-2",
                                                        "disabled": true,
                                                        "key": "TEN_ITEM",
                                                        "type": "textfield",
                                                        "placeholder": "Nước xuất",
                                                    }
                                                ],
                                                "width": 8,
                                                "size": "md",
                                            },
                                            {
                                                "components": [
                                                    {
                                                        "label": "",
                                                        "customClass": "pr-2",
                                                        "key": "CHI_SO",
                                                        "type": "number",
                                                    }
                                                ],
                                                "width": 2,
                                                "size": "md",
                                            },
                                            {
                                                "components": [
                                                    {
                                                        "label": "Xoá",
                                                        "action": "event",
                                                        "theme": "danger",
                                                        "size": "sm",
                                                        "event": "xoaNuocXuat",
                                                        "key": "XOA_NUOC_XUAT",
                                                        "type": "button",
                                                    }
                                                ],
                                                "width": 2,
                                                "size": "md",
                                            },
                                        ],
                                        "customClass": "pl-2 ml-0 mr-0",
                                        "hideLabel": true,
                                        "key": "columns",
                                        "type": "columns",
                                    }
                                ]
                            },
                        ]
                    },
                ]
            }
        ).then(function (form) {
            formCSC1ThongTinChiTietNuocXuat = form;
            var idItem = 0;
            form.on("themNuocXuat", function(e) {
                var inputElement = form.getComponent('TEN_NUOC_XUAT');
                var dataGridData = thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NUOC_XUAT_ARR;
                if (!inputElement.getValue()){
                    return notifiToClient("Red","Vui lòng nhập tên nuớc xuất");
                }
                if (kiemTraSoLuongMax() == false) {
                    return notifiToClient("Red", "Chỉ được nhập tối đa " + singletonObject.maxRowCSC1 + " thông tin");
                }
                dataGridData.push({
                    ID_ITEM: "TEMP_" + (idItem + 1),
                    LOAI_ITEM: "NUOC_XUAT",
                    TEN_ITEM: inputElement.getValue(),
                    CHI_SO: null
                });
                inputElement.setValue("");
                form.submission =  {
                    data: {
                        NUOC_XUAT_ARR: dataGridData
                    }
                };
                thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NUOC_XUAT_ARR = dataGridData;
                idItem++;
            });
            form.on("xoaNuocXuat", function(e) {
                var arrTemp = []
                var dataGridData = thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NUOC_XUAT_ARR;
                var dataGridDataAfter = dataGridData.filter(function(item) {
                    return item.ID_ITEM != e.ID_ITEM;
                });
                if (e.ID_ITEM.toString().indexOf("TEMP") != -1) {
                    arrTemp = dataGridDataAfter;
                    thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NUOC_XUAT_ARR = arrTemp;
                    createFormChiTietNuocXuat();
                } else {
                    confirmToClient("Xoá phần này này sẽ xoá ở tất cả các mốc thời gian, bạn có chắc chắn xoá?", function() {
                        $.ajax({
                            url: "cmu_post",
                            method: "POST",
                            async: false,
                            data: {url:[e.ID_ITEM.toString(), "CMU_LCSC1_ITEM_DEL"].join("```")}
                        }).done(function (countDel) {
                            if(countDel > 0) {
                                luuLogHSBATheoBN({
                                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                    LOAI: LOGHSBALOAI.CHAMSOCCAP1NUOCXUAT.KEY,
                                    NOIDUNGBANDAU: "",
                                    NOIDUNGMOI: "Xoá nước xuất '" + e.TEN_ITEM + "' của tất cả mốc thời gian ở tờ chăm sóc cấp 1 số " + thongtinhsba.thongtinbn.lanChamSocCap1.TO_SO,
                                    USERID: singletonObject.userId,
                                    ACTION: LOGHSBAACTION.DELETE.KEY,
                                });
                                notifiToClient("Green", MESSAGEAJAX.DEL_SUCCESS);
                                arrTemp = dataGridDataAfter;
                                thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NUOC_XUAT_ARR = arrTemp;
                                createFormChiTietNuocXuat();
                            }
                        });
                    }, function() {
                        arrTemp = dataGridData;
                        thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NUOC_XUAT_ARR = arrTemp;
                        createFormChiTietNuocXuat();
                    });
                }
            });
            form.submission =  {
                data: {
                    NUOC_XUAT_ARR: thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet.NUOC_XUAT_ARR
                }
            };
        });
    }

    function stripHtml(html) {
        const doc = new DOMParser().parseFromString(html, 'text/html');
        return doc.body.textContent || '';
    }

    function instanceGridDataListItem() {
        var list = $("#list_dataitem");
        if(!list[0].grid) {
            list.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 450,
                width: null,
                shrinkToFit: false,
                ignoreCase: true,
                colModel: [
                    {label: "ID",name: 'ID', index: 'ID', width: 50},
                    {label: "Tên",name: 'TEN_ITEM', index: 'TEN_ITEM', width: 600},
                    {label: "Mô tả",name: 'MOTA_ITEM', index: 'MOTA_ITEM', width: 400},
                    {label: "KHOA_TAO",name: 'KHOA_TAO', index: 'KHOA_TAO', width: 50, hidden: true},
                    {label: "NGUOI_TAO",name: 'NGUOI_TAO', index: 'NGUOI_TAO', width: 50, hidden: true},
                ],
                rowNum: 1000000,
                caption: "Danh sách",
                onSelectRow: function (id) {
                },
                onRightClickRow: function(id) {
                    if (id) {
                        var ret = getThongtinRowSelected("list_dataitem");
                        var items = {
                            "xoa": {name: '<p class="text-danger"><i class="fa fa-remove text-danger" aria-hidden="true"></i> Xoá</p>'},
                        }
                        $.contextMenu('destroy', '#list_dataitem tr');
                        $.contextMenu({
                            selector: '#list_dataitem tr',
                            reposition : false,
                            callback: function (key, options) {
                                if(key == 'xoa'){
                                    confirmToClient("Xác nhận xóa thông tin này?", function (confirm) {
                                        $.ajax({
                                            url: "cmu_post",
                                            method: "POST",
                                            data: {url:[ret.ID, "CMU_CSC1_DATALIST_ITEM_DEL"].join("```")},
                                            success: function (data) {
                                                if(data > 0){
                                                    notifiToClient("Green",MESSAGEAJAX.DEL_SUCCESS);
                                                    reloadDataListGrid();
                                                } else {
                                                    notifiToClient("Red",MESSAGEAJAX.ERROR);
                                                }
                                            },
                                            error: function (error) {
                                                notifiToClient("Red",MESSAGEAJAX.ERROR);
                                            }
                                        });
                                    });
                                }
                            },
                            items: items
                        });
                    }
                }
            });
            list.jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
        }
        reloadDataListGrid();
    }

    function reloadDataListGrid() {
        var url = "cmu_getlist?url=" + convertArray([singletonObject.dvtt, singletonObject.makhoa, $("#dli_loai").val(), "CMU_CSC1_DATALIST_ITEM_SEL"]);
        $("#list_dataitem").jqGrid('clearGridData');
        $.get(url).done(function (data) {
            if (data && data.length > 0) {
                thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet["DATALIST_" + $("#dli_loai").val()] = data;
                $("#list_dataitem").jqGrid('setGridParam', {
                    datatype: 'local',
                    data: data
                }).trigger('reloadGrid');
            }
        });

        // $.get("cmu_getlist?url=" + convertArray([singletonObject.dvtt, singletonObject.makhoa, "-1", "CMU_CSC1_DATALIST_ITEM_SEL"])).done(function(data) {
        //     if (data && data.length > 0) {
        //         singletonObject.danhSachItemChamSocCap1 = data.map(function(item) {
        //             return {
        //                 label: item.TEN_ITEM,
        //                 value: item.TEN_ITEM,
        //                 ghiChu: item.MOTA_ITEM,
        //                 loai: item.LOAI_ITEM,
        //             }
        //         })
        //     }
        // });
    }

    function kiemTraSoLuongMax() {
        var arrTemp = [];
        singletonObject.configGroupChamSocCap1.forEach(function (itemGroup) {
            arrTemp = [
                ...arrTemp,
                ...thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet[itemGroup.LOAI + "_ARR"]
            ];
        });
        if (arrTemp.length == singletonObject.maxRowCSC1) {
            return false;
        }
        return true
    }

    function configTextfieldDataListFormioDynamic(object) {
        var idInput = object.idInput;
        var idDataList = object.idInput + "_list"

        if($('#' + idInput).attr('instanced') != '0') {
            $('#' + idInput).attr('instanced', '0');
            $("#"+object.idButton + " button").click(function() {
                $("#dli_loai").val(object.inputKey);
                $("#dli_input").val(idDataList);
                instanceGridDataListItem();

                $("#titleDataListItem").html("Mẫu - "+ object.labelModal);
                $("#modalDataListItem").modal("show");
            })
        }

        $.get("cmu_getlist?url=" + convertArray([singletonObject.dvtt, singletonObject.makhoa, object.inputKey, "CMU_CSC1_DATALIST_ITEM_SEL"])).done(function(data) {
            var source = [];
            if (data && data.length > 0) {
                thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet["DATALIST_" + object.inputKey] = data;
                for(var i = 0; i < data.length; i++) {
                    source.push({
                        label: data[i].TEN_ITEM + ' - '+ data[i].MOTA_ITEM,
                        value: data[i].TEN_ITEM,
                    })
                }
            }
            autoCompleteTags(idInput, source, function(itemSelect, value) {
                object.component.setValue(value);
            });
        });
    }

    function createFormChiTietGroup(upd = null) {
        Formio.createForm(document.getElementById('formCSC1ThongTinGroup'), singletonObject.jsonCSC1
        ).then(function (form) {
            formCSC1ThongTinChiTietGroup = form;
            var idItem = 0;
            if (singletonObject.configGroupChamSocCap1.length > 0) {
                singletonObject.configGroupChamSocCap1.forEach(function (itemGroup) {
                    var idItem = 0;
                    form.on('change', function(changed) {
                        var  dataGrid = form.getComponent(itemGroup.LOAI + "_ARR");
                        dataGrid.rows.forEach(function(row, index) {
                            var chiSoComponent =  row.columns.components.find((comp) => comp.key === 'CHI_SO');
                            var mauComponent =  row.columns.components.find((comp) => comp.key === 'MAU_' + itemGroup.LOAI);
                            configTextfieldDataListFormioDynamic({
                                idInput: chiSoComponent.id+"-CHI_SO",
                                inputKey: itemGroup.LOAI + "_" + thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet[itemGroup.LOAI + "_ARR"][index].TEN_ITEM,
                                idButton: mauComponent.id,
                                labelModal: thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet[itemGroup.LOAI + "_ARR"][index].TEN_ITEM,
                                component: chiSoComponent
                            });
                        })
                    });

                    form.on("them" + itemGroup.LOAI, function(e) {
                        var element = form.getComponent('TEN_' + itemGroup.LOAI);
                        var selectedValue = element.getValue();
                        var selectedOption = element.selectOptions?.find(opt => opt.value === selectedValue);
                        var labelText = stripHtml(selectedOption ? selectedOption.label : selectedValue);
                        var dataGridData = thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet[itemGroup.LOAI + "_ARR"];
                        var checkExist = dataGridData.filter(function(itemDataGrid) {
                            return itemDataGrid.TEN_ITEM == labelText;
                        });
                        if (!element.getValue()){
                            return notifiToClient("Red","Vui lòng chọn " + itemGroup.TENHIENTHI);
                        }
                        if (checkExist.length > 0) {
                            return notifiToClient("Red","Đã có dữ liệu: " + labelText);
                        }
                        if (kiemTraSoLuongMax() == false) {
                            return notifiToClient("Red", "Chỉ được nhập tối đa " + singletonObject.maxRowCSC1 + " thông tin");
                        }

                        dataGridData.push({
                            ID_ITEM: "TEMP_" + (idItem + 1),
                            LOAI_ITEM: itemGroup.LOAI,
                            TEN_ITEM: labelText,
                            CHI_SO: null,
                            ID_DATALIST: selectedValue,
                        });
                        element.setValue("");
                        form.submission = {
                            data: thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet
                        }
                        thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet[itemGroup.LOAI + "_ARR"] = dataGridData;
                        idItem++;
                    });

                    form.on("xoa" + itemGroup.LOAI, function(e) {
                        var indexToRemove = thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet[itemGroup.LOAI + "_ARR"].findIndex(function(row) {
                            return row.TEN_ITEM == e.TEN_ITEM;
                        });
                        if (e.ID_ITEM.toString().indexOf("TEMP") != -1) {
                            thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet[itemGroup.LOAI + "_ARR"].splice(indexToRemove, 1);
                            createFormChiTietGroup()
                        } else {
                            confirmToClient("Xoá phần này này sẽ xoá ở tất cả các mốc thời gian, bạn có chắc chắn xoá?", function() {
                                $.ajax({
                                    url: "cmu_post",
                                    method: "POST",
                                    async: false,
                                    data: {
                                        url:[
                                            e.ID_ITEM.toString(),
                                            "CMU_LCSC1_ITEM_DEL"
                                        ].join("```")
                                    }
                                }).done(function (countDel) {
                                    if(countDel > 0) {
                                        luuLogHSBATheoBN({
                                            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                            LOAI: LOGHSBALOAI.CHAMSOCCAP1TUANHOAN.KEY,
                                            NOIDUNGBANDAU: "",
                                            NOIDUNGMOI: "Xoá tuần hoàn '" + e.TEN_ITEM + "' của tất cả mốc thời gian ở tờ chăm sóc cấp 1 số " + thongtinhsba.thongtinbn.lanChamSocCap1.TO_SO,
                                            USERID: singletonObject.userId,
                                            ACTION: LOGHSBAACTION.DELETE.KEY,
                                        });
                                        notifiToClient("Green", MESSAGEAJAX.DEL_SUCCESS);
                                        thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet[itemGroup.LOAI + "_ARR"].splice(indexToRemove, 1);
                                        createFormChiTietGroup();
                                    }
                                });
                            }, function() {});
                        }
                    });
                    if (!upd){
                        form.submission = {
                            data: thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet
                        }
                    }
                });
                if (!upd){
                    form.submission = {
                        data: thongtinhsba.thongtinbn.lanChamSocCap1.chiTiet
                    }
                }
            }
        });
    }

    function instanceGridChamSocCap1ChanDoanDieuDuong() {
        var list = $("#ttcs_list_chamsoccap1_chandoandieuduong");
        if(!list[0].grid) {
            list.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 250,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "ID_CHAM_SOC_CAP_1",name: 'ID_CHAM_SOC_CAP_1', index: 'ID_CHAM_SOC_CAP_1', width: 50, hidden: true},
                    {label: "ID_CHAN_DOAN",name: 'ID_CHAN_DOAN', index: 'ID_CHAN_DOAN', width: 50, hidden: true},
                    {label: "ICD_CHAN_DOAN_DD",name: 'ID_CHAN_DOAN_DIEU_DUONG', index: 'ID_CHAN_DOAN_DIEU_DUONG', width: 10, hidden: true},
                    {label: "TEN_ICD_CHAN_DOAN_DD",name: 'TENICD_DIEUDUONG', index: 'TENICD_DIEUDUONG', width: 10, hidden: true},
                    {label: "ICD_CHAN_DOAN_DD",name: 'ICD_DIEUDUONG', index: 'ICD_DIEUDUONG', width: 10, hidden: true},
                    {label: "Chẩn đoán",name: 'CHAN_DOAN', index: 'CHAN_DOAN', width: 300},
                    {label: "Mục tiêu",name: 'MUC_TIEU', index: 'MUC_TIEU', width: 300},
                    {label: "Người tạo",name: 'TEN_NGUOI_TAO', index: 'TEN_NGUOI_TAO', width: 120},
                ],
                rowNum: 1000000,
                onSelectRow: function (id) {
                },
                onRightClickRow: function(id) {
                    if (id) {
                        var ret = getThongtinRowSelected("ttcs_list_chamsoccap1_chandoandieuduong");
                        var items = {
                            "capnhat": {name: '<p class="text-success"><i class="fa fa-pencil-square-o text-success" aria-hidden="true"></i> Cập nhật</p>'},
                            "xoa": {name: '<p class="text-danger"><i class="fa fa-remove text-danger" aria-hidden="true"></i> Xoá</p>'},
                        }
                        $.contextMenu('destroy', '#ttcs_list_chamsoccap1_chandoandieuduong tr');
                        $.contextMenu({
                            selector: '#ttcs_list_chamsoccap1_chandoandieuduong tr',
                            reposition : false,
                            callback: function (key, options) {
                                if(key == 'capnhat') {
                                    loadThongTinChamSocCap1(ret);
                                }
                                if(key == 'xoa'){
                                    $.ajax({
                                        url: "cmu_post",
                                        method: "POST",
                                        data: {url:[ret.ID_CHAN_DOAN, "CMU_LCSC1_CHANDOAN_DEL"].join("```")},
                                        success: function (data) {
                                            if(data > 0){
                                                luuLogHSBATheoBN({
                                                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                    LOAI: LOGHSBALOAI.CHAMSOCCAP1CHANDOAN.KEY,
                                                    NOIDUNGBANDAU: "",
                                                    NOIDUNGMOI: "Xoá chẩn đoán điều dưỡng/lượng giá mục tiêu: '" + ret.CHAN_DOAN + "' tờ chăm sóc cấp 1 số: " + thongtinhsba.thongtinbn.lanChamSocCap1.TO_SO,
                                                    USERID: singletonObject.userId,
                                                    ACTION: LOGHSBAACTION.DELETE.KEY,
                                                });
                                                releadGridChamSocCap1ChanDoanDieuDuong();
                                                notifiToClient("Green",MESSAGEAJAX.DEL_SUCCESS);
                                            } else {
                                                notifiToClient("Red",MESSAGEAJAX.ERROR);
                                            }
                                        },
                                        error: function (error) {
                                            notifiToClient("Red",MESSAGEAJAX.ERROR);
                                        }
                                    });
                                }
                            },
                            items: items
                        });
                    }
                }
            });
        }
        releadGridChamSocCap1ChanDoanDieuDuong();
    }
    function releadGridChamSocCap1ChanDoanDieuDuong() {
        var data = thongtinhsba.thongtinbn.lanChamSocCap1;
        var url = "cmu_getlist?url=" + convertArray([data.ID_CHAM_SOC_CAP_1, "CMU_LCSC1_CHANDOAN_SEL"]);
        $("#ttcs_list_chamsoccap1_chandoandieuduong").jqGrid('setGridParam', {
            datatype: 'json',
            url: url
        }).trigger('reloadGrid')
    }

    function instanceGridChamSocCap1GhiChuBanGiao() {
        var list = $("#ttcs_list_chamsoccap1_ghichubangiao");
        if(!list[0].grid) {
            list.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 200,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "ID_CHAM_SOC_CAP_1",name: 'ID_CHAM_SOC_CAP_1', index: 'ID_CHAM_SOC_CAP_1', width: 50, hidden: true},
                    {label: "ID_GHI_CHU",name: 'ID_GHI_CHU', index: 'ID_GHI_CHU', width: 10, hidden: true},
                    {label: "Ghi chú/bàn giao",name: 'GHI_CHU_BAN_GIAO', index: 'GHI_CHU_BAN_GIAO', width: 400},
                    {label: "Tên điều dưỡng",name: 'TEN_NGUOI_TAO', index: 'TEN_NHAN_VIEN', width: 120},
                    {label: "MA_NHAN_VIEN",name: 'MA_NGUOI_TAO', index: 'MA_NHAN_VIEN', width: 10, hidden: true},
                    {label: "Tên khoa",name: 'TEN_KHOA_TAO', index: 'TEN_KHOA_TAO', width: 120},
                    {label: "MA_NHAN_VIEN",name: 'MA_KHOA_TAO', index: 'MA_KHOA_TAO', width: 10, hidden: true},
                ],
                rowNum: 1000000,
                onSelectRow: function (id) {
                },
                onRightClickRow: function(id) {
                    if (id) {
                        var ret = getThongtinRowSelected("ttcs_list_chamsoccap1_ghichubangiao");
                        var items = {
                            "xoa": {name: '<p class="text-danger"><i class="fa fa-remove text-danger" aria-hidden="true"></i> Xoá</p>'},
                        }
                        $.contextMenu('destroy', '#ttcs_list_chamsoccap1_ghichubangiao tr');
                        $.contextMenu({
                            selector: '#ttcs_list_chamsoccap1_ghichubangiao tr',
                            reposition : false,
                            callback: function (key, options) {
                                if(key == 'xoa') {
                                    $.ajax({
                                        url: "cmu_post",
                                        method: "POST",
                                        data: {url:[ret.ID_GHI_CHU, "CMU_LCSC1_GHICHU_DEL"].join("```")},
                                        success: function (data) {
                                            if(data > 0){
                                                luuLogHSBATheoBN({
                                                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                    LOAI: LOGHSBALOAI.CHAMSOCCAP1GHICHU.KEY,
                                                    NOIDUNGBANDAU: "",
                                                    NOIDUNGMOI: "Xoá ghi chú/bàn giao: '" + ret.GHI_CHU_BAN_GIAO + "' tờ chăm sóc cấp 1 số: " + thongtinhsba.thongtinbn.lanChamSocCap1.TO_SO,
                                                    USERID: singletonObject.userId,
                                                    ACTION: LOGHSBAACTION.DELETE.KEY,
                                                });
                                                releadGridChamSocCap1GhiChuBanGiao();
                                            } else {
                                                notifiToClient("Red",MESSAGEAJAX.ERROR);
                                            }
                                        },
                                        error: function (error) {
                                            notifiToClient("Red",MESSAGEAJAX.ERROR);
                                        }
                                    });
                                }
                            },
                            items: items
                        });
                    }
                }
            });
        }
        releadGridChamSocCap1GhiChuBanGiao();
    }
    function releadGridChamSocCap1GhiChuBanGiao() {
        var data = thongtinhsba.thongtinbn.lanChamSocCap1;
        var url = "cmu_getlist?url=" + convertArray([data.ID_CHAM_SOC_CAP_1, "CMU_LCSC1_GHICHU_SEL"]);
        $("#ttcs_list_chamsoccap1_ghichubangiao").jqGrid('setGridParam', {
            datatype: 'json',
            url: url
        }).trigger('reloadGrid')
    }

    function instanceGridChamSocCap1QuyUocKyHieu() {
        var list = $("#ttcs_list_chamsoccap1_quyuockyhieu");
        if(!list[0].grid) {
            list.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 200,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "ID_CHAM_SOC_CAP_1",name: 'ID_CHAM_SOC_CAP_1', index: 'ID_CHAM_SOC_CAP_1', width: 50, hidden: true},
                    {label: "ID_QUY_UOC",name: 'ID_QUY_UOC', index: 'ID_QUY_UOC', width: 10, hidden: true},
                    {label: "Quy ước ký hiệu",name: 'QUY_UOC_KY_HIEU', index: 'QUY_UOC_KY_HIEU', width: 400},
                    {label: "Tên điều dưỡng",name: 'TEN_NGUOI_TAO', index: 'TEN_NHAN_VIEN', width: 120},
                    {label: "MA_NHAN_VIEN",name: 'MA_NGUOI_TAO', index: 'MA_NHAN_VIEN', width: 10, hidden: true},
                    {label: "Tên khoa",name: 'TEN_KHOA_TAO', index: 'TEN_KHOA_TAO', width: 120},
                    {label: "MA_NHAN_VIEN",name: 'MA_KHOA_TAO', index: 'MA_KHOA_TAO', width: 10, hidden: true},
                ],
                rowNum: 1000000,
                onSelectRow: function (id) {
                },
                onRightClickRow: function(id) {
                    if (id) {
                        var ret = getThongtinRowSelected("ttcs_list_chamsoccap1_quyuockyhieu");
                        var items = {
                            "xoa": {name: '<p class="text-danger"><i class="fa fa-remove text-danger" aria-hidden="true"></i> Xoá</p>'},
                        }
                        $.contextMenu('destroy', '#ttcs_list_chamsoccap1_quyuockyhieu tr');
                        $.contextMenu({
                            selector: '#ttcs_list_chamsoccap1_quyuockyhieu tr',
                            reposition : false,
                            callback: function (key, options) {
                                if(key == 'xoa') {
                                    $.ajax({
                                        url: "cmu_post",
                                        method: "POST",
                                        data: {url:[ret.ID_QUY_UOC, "CMU_LCSC1_QUYUOC_DEL"].join("```")},
                                        success: function (data) {
                                            if(data > 0){
                                                luuLogHSBATheoBN({
                                                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                    LOAI: LOGHSBALOAI.CHAMSOCCAP1GHICHU.KEY,
                                                    NOIDUNGBANDAU: "",
                                                    NOIDUNGMOI: "Xoá quy ước ký hiệu: '" + ret.QUY_UOC_KY_HIEU + "' tờ chăm sóc cấp 1 số: " + thongtinhsba.thongtinbn.lanChamSocCap1.TO_SO,
                                                    USERID: singletonObject.userId,
                                                    ACTION: LOGHSBAACTION.DELETE.KEY,
                                                });
                                                releadGridChamSocCap1QuyUocKyHieu();
                                            } else {
                                                notifiToClient("Red",MESSAGEAJAX.ERROR);
                                            }
                                        },
                                        error: function (error) {
                                            notifiToClient("Red",MESSAGEAJAX.ERROR);
                                        }
                                    });
                                }
                            },
                            items: items
                        });
                    }
                }
            });
        }
        releadGridChamSocCap1QuyUocKyHieu();
    }
    function releadGridChamSocCap1QuyUocKyHieu() {
        var data = thongtinhsba.thongtinbn.lanChamSocCap1;
        var url = "cmu_getlist?url=" + convertArray([data.ID_CHAM_SOC_CAP_1, "CMU_LCSC1_QUYUOC_SEL"]);
        $("#ttcs_list_chamsoccap1_quyuockyhieu").jqGrid('setGridParam', {
            datatype: 'json',
            url: url
        }).trigger('reloadGrid')
    }

    function createFormChamSocCap1Right() {
        Formio.createForm(document.getElementById('formCSC1ThongTinRight'),
            {
                "display": "form",
                "components": [
                    {
                        label: "",
                        key: "wrap_benhchinh",
                        columns: [
                            {
                                "components": [
                                    {
                                        "tag": "label",
                                        "content": "Chẩn đoán điều dưỡng/lượng giá mục tiêu",
                                        "key": "htmllabel",
                                        "type": "htmlelement",
                                    },
                                ],
                                "width": 12,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "",
                                        "key": "ICD_DIEUDUONG",
                                        "type": "textfield",
                                        customClass: "pr-2",
                                        "placeholder": "ICD",
                                    },
                                ],
                                "width": 2,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "",
                                        "key": "TENICD_DIEUDUONG",
                                        "type": "textfield",
                                        "placeholder": "Tên chẩn đoán",
                                    },
                                ],
                                "width": 10,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "tag": "div",
                                        "content": "<table id=\"ttcs_list_chamsoccap1_chandoandieuduong\"></table>",
                                        "key": "htmllabel",
                                        "type": "htmlelement",
                                    },
                                ],
                                "width": 12,
                                "size": "md",
                            },
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "",
                        key: "wrap_benhchinh",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Ghi chú/bàn giao",
                                        "key": "GHI_CHU_BAN_GIAO",
                                        "type": "textfield",
                                        "placeholder": "Ghi chú/bàn giao",
                                    },
                                ],
                                "width": 12,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "tag": "div",
                                        "content": "<table id=\"ttcs_list_chamsoccap1_ghichubangiao\"></table>",
                                        "key": "htmllabel",
                                        "type": "htmlelement",
                                    },
                                ],
                                "width": 12,
                                "size": "md",
                            },
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "",
                        key: "wrap_benhchinh",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Quy ước ký hiệu",
                                        "key": "QUY_UOC_KY_HIEU",
                                        "type": "textfield",
                                        "placeholder": "Quy ước ký hiệu",
                                    },
                                ],
                                "width": 12,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "tag": "div",
                                        "content": "<table id=\"ttcs_list_chamsoccap1_quyuockyhieu\"></table>",
                                        "key": "htmllabel",
                                        "type": "htmlelement",
                                    },
                                ],
                                "width": 12,
                                "size": "md",
                            },
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                ]
            }
        ).then(function (form) {
            formCSC1ThongTinChiTietNuocNhap = form;
            var icdChanDoanDieuDuongElement = form.getComponent('ICD_DIEUDUONG');
            var tenICDChanDoanDieuDuongElement = form.getComponent('TENICD_DIEUDUONG');
            actionICDBenhChinh("ICD_DIEUDUONG", "TENICD_DIEUDUONG", form);
            combgridTenICD(getIdElmentFormio(form,'TENICD_DIEUDUONG'), function(item) {
                icdChanDoanDieuDuongElement.setValue(item.ICD);
                tenICDChanDoanDieuDuongElement.setValue(item.MO_TA_BENH_LY);
            });

            instanceGridChamSocCap1ChanDoanDieuDuong();
            $("#"+getIdElmentFormio(form,'TENICD_DIEUDUONG')).on('keypress', function(event) {
                if(event.keyCode == 13) {
                    if (form.submission.data.TENICD_DIEUDUONG){
                        var dataSubmit = form.submission.data;
                        $.post("cmu_post", {url:[thongtinhsba.thongtinbn.lanChamSocCap1.ID_CHAM_SOC_CAP_1,
                                dataSubmit.ICD_DIEUDUONG,
                                dataSubmit.TENICD_DIEUDUONG,
                                singletonObject.userId,
                                singletonObject.makhoa,
                                "CMU_LCSC1_CHANDOAN_INS"].join("```")}).done(function (id) {
                            if(id > 0){
                                luuLogHSBATheoBN({
                                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                    LOAI: LOGHSBALOAI.CHAMSOCCAP1CHANDOAN.KEY,
                                    NOIDUNGBANDAU: "Thêm chẩn đoán điều dưỡng/lượng giá mục tiêu '" + dataSubmit.ICD_DIEUDUONG + " - " + dataSubmit.TENICD_DIEUDUONG + "' tờ chăm sóc cấp 1 số: " + thongtinhsba.thongtinbn.lanChamSocCap1.TO_SO,
                                    NOIDUNGMOI: "",
                                    USERID: singletonObject.userId,
                                    ACTION: LOGHSBAACTION.INSERT.KEY,
                                });
                                releadGridChamSocCap1ChanDoanDieuDuong();
                                icdChanDoanDieuDuongElement.setValue("");
                                tenICDChanDoanDieuDuongElement.setValue("");
                                icdChanDoanDieuDuongElement.focus();
                            } else if (id == -1) {
                                notifiToClient("Red", "ICD đã tồn tại");
                            } else if (id == -2) {
                                notifiToClient("Red", "Chỉ được nhập tối đa 4 chẩn đoán");
                            } else {
                                notifiToClient("Red",MESSAGEAJAX.ERROR);
                            }
                        }).fail(function(error) {
                            notifiToClient("Red",MESSAGEAJAX.ERROR);
                        });
                    } else {
                        notifiToClient("Red","Vui lòng chọn ICD trước")
                    }
                }
            });

            instanceGridChamSocCap1GhiChuBanGiao();
            var ghiChuElement = form.getComponent('GHI_CHU_BAN_GIAO');
            $("#"+getIdElmentFormio(form,'GHI_CHU_BAN_GIAO')).on('keypress', function(event) {
                if(event.keyCode == 13) {
                    if (form.submission.data.GHI_CHU_BAN_GIAO){
                        var dataSubmit = form.submission.data;
                        $.post("cmu_post", {url:[thongtinhsba.thongtinbn.lanChamSocCap1.ID_CHAM_SOC_CAP_1,
                                dataSubmit.GHI_CHU_BAN_GIAO,
                                singletonObject.userId,
                                singletonObject.makhoa,
                                "CMU_LCSC1_GHICHU_INS"].join("```")}).done(function (id) {
                            if(id > 0){
                                luuLogHSBATheoBN({
                                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                    LOAI: LOGHSBALOAI.CHAMSOCCAP1GHICHU.KEY,
                                    NOIDUNGBANDAU: "Thêm ghi chú/bàn giao '" + dataSubmit.GHI_CHU_BAN_GIAO + "' tờ chăm sóc cấp 1 số: " + thongtinhsba.thongtinbn.lanChamSocCap1.TO_SO,
                                    NOIDUNGMOI: "",
                                    USERID: singletonObject.userId,
                                    ACTION: LOGHSBAACTION.INSERT.KEY,
                                });
                                releadGridChamSocCap1GhiChuBanGiao();
                                ghiChuElement.setValue("");
                                ghiChuElement.focus();
                            } else {
                                notifiToClient("Red",MESSAGEAJAX.ERROR);
                            }
                        }).fail(function(error) {
                            notifiToClient("Red",MESSAGEAJAX.ERROR);
                        });
                    } else {
                        notifiToClient("Red","Vui lòng chọn ICD trước")
                    }
                }
            });

            instanceGridChamSocCap1QuyUocKyHieu();
            var quyUocElement = form.getComponent('QUY_UOC_KY_HIEU');
            $("#"+getIdElmentFormio(form,'QUY_UOC_KY_HIEU')).on('keypress', function(event) {
                if(event.keyCode == 13) {
                    if (form.submission.data.QUY_UOC_KY_HIEU){
                        var dataSubmit = form.submission.data;
                        $.post("cmu_post", {url:[thongtinhsba.thongtinbn.lanChamSocCap1.ID_CHAM_SOC_CAP_1,
                                dataSubmit.QUY_UOC_KY_HIEU,
                                singletonObject.userId,
                                singletonObject.makhoa,
                                "CMU_LCSC1_QUYUOC_INS"].join("```")}).done(function (id) {
                            if(id > 0){
                                luuLogHSBATheoBN({
                                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                    LOAI: LOGHSBALOAI.CHAMSOCCAP1QUYUOC.KEY,
                                    NOIDUNGBANDAU: "Thêm quy ước ký hiệu '" + dataSubmit.QUY_UOC_KY_HIEU + "' tờ chăm sóc cấp 1 số: " + thongtinhsba.thongtinbn.lanChamSocCap1.TO_SO,
                                    NOIDUNGMOI: "",
                                    USERID: singletonObject.userId,
                                    ACTION: LOGHSBAACTION.INSERT.KEY,
                                });
                                releadGridChamSocCap1QuyUocKyHieu();
                                quyUocElement.setValue("");
                                quyUocElement.focus();
                            } else {
                                notifiToClient("Red",MESSAGEAJAX.ERROR);
                            }
                        }).fail(function(error) {
                            notifiToClient("Red",MESSAGEAJAX.ERROR);
                        });
                    }
                }
            });
        });
    }

    function loadThongTinChamSocCap1(dataID) {
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, dataID.ID_CHAN_DOAN, 'CMU_LCSC1_SEL_MUCTIEU_IDCD'])).done(function(data) {
            if (data.length > 0) {
                thongtinhsba.thongtinbn.lanChamSocCap1.chanDoan = JSON.parse(JSON.stringify(data[0]));
                singletonObject.thongTinCSC1TruocKhiChinhSua.chanDoan = {...JSON.parse(JSON.stringify(data[0]))};
                thongtinhsba.thongtinbn.lanChamSocCap1.chanDoan.MUC_TIEU_ARR = data;
                singletonObject.thongTinCSC1TruocKhiChinhSua.chanDoan.MUC_TIEU_ARR = [...JSON.parse(JSON.stringify(data))];
            }
            createFormMucTieuChanDoan();
        });
    }
    function createFormMucTieuChanDoan() {
        Formio.createForm(document.getElementById('formCSC1ChanDoanMucTieu'),
            {
                "display": "form",
                "components": [
                    {
                        label: "",
                        key: "wrap_benhchinh",
                        columns: [
                            {
                                "components": [
                                    {
                                        "tag": "label",
                                        "content": "Chẩn đoán điều dưỡng/lượng giá mục tiêu",
                                        "key": "htmllabel",
                                        "type": "htmlelement",
                                    },
                                ],
                                "width": 12,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "",
                                        "key": "ICD_DIEUDUONG",
                                        "type": "textfield",
                                        customClass: "pr-2",
                                        "placeholder": "ICD",
                                    },
                                ],
                                "width": 2,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "",
                                        "key": "TENICD_DIEUDUONG",
                                        "type": "textfield",
                                        "placeholder": "Tên chẩn đoán",
                                    },
                                ],
                                "width": 10,
                                "size": "md",
                            },
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        "label": "Danh sách mục tiêu",
                        "addAnother": "Thêm mục tiêu",
                        "initEmpty": true,
                        "key": "MUC_TIEU_ARR",
                        "type": "datagrid",
                        "validate": {
                            "maxLength": "2"
                        },
                        "components": [
                            {
                                "label": "Columns",
                                "columns": [
                                    {
                                        "components": [
                                            {
                                                "label": "",
                                                "key": "IS_MUC_TIEU",
                                                "type": "checkbox",
                                            },
                                        ],
                                        "width": 1,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "",
                                                "customClass": "pr-2",
                                                "validate": {
                                                    "required": true
                                                },
                                                "key": "MUC_TIEU",
                                                "type": "textfield",
                                            }
                                        ],
                                        "width": 11,
                                        "size": "md",
                                    },
                                ],
                                "customClass": "ml-2",
                                "hideLabel": true,
                                "key": "columns",
                                "type": "columns",
                            }
                        ]
                    },
                ]
            }
        ).then(function (form) {
            formCSC1ChanDoanMucTieu = form;
            var icdChanDoanDieuDuongElement = form.getComponent('ICD_DIEUDUONG');
            var tenICDChanDoanDieuDuongElement = form.getComponent('TENICD_DIEUDUONG');
            actionICDBenhChinh("ICD_DIEUDUONG", "TENICD_DIEUDUONG", form);
            combgridTenICD(getIdElmentFormio(form,'TENICD_DIEUDUONG'), function(item) {
                icdChanDoanDieuDuongElement.setValue(item.ICD);
                tenICDChanDoanDieuDuongElement.setValue(item.MO_TA_BENH_LY);
            });
            form.submission =  {
                data: {
                    ...thongtinhsba.thongtinbn.lanChamSocCap1.chanDoan
                }
            };

            $("#modalFormCSC1ChanDoanMucTieu").modal("show");
        });
    }

    function instanceGridChamSocCap1ChiTiet() {
        var list = $("#ttcs_list_chamsoccap1_chitiet");
        if(!list[0].grid) {
            list.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 450,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "ID_CHAM_SOC_CAP_1",name: 'ID_CHAM_SOC_CAP_1', index: 'ID_CHAM_SOC_CAP_1', width: 50, hidden: true},
                    {label: "ID_CHI_TIET",name: 'ID_CHI_TIET', index: 'ID_CHI_TIET', width: 50, hidden: true},
                    {label: "Trạng thái", name: 'TRANG_THAI', index: 'TRANG_THAI', width: 100,
                        formatter: function(cellValue, options, rowObject) {
                            if(rowObject.KEYSIGN) {
                                return "<span class='badge badge-success'>Đã ký</span>";
                            } else {
                                return "<span class='badge badge-danger'>Chưa ký</span>";
                            }
                        },
                        cellattr: function(rowId, tv, rawObject, cm, rdata) {
                            return 'style="text-align: center;"';
                        }
                    },
                    {label: "Ngày",name: 'NGAY', index: 'NGAY', width: 140, align: 'center'},
                    {label: "SPO2",name: 'SPO2', index: 'SPO2', width: 50, align: 'center'},
                    {label: "Mạch",name: 'MACH', index: 'MACH', width: 50, align: 'center'},
                    {label: "H.Áp",name: 'HUYET_AP', index: 'HUYET_AP', width: 100, align: 'center'},
                    {label: "HUYETAP_TREN",name: 'HUYETAP_TREN', index: 'HUYETAP_TREN', width: 50, hidden: true},
                    {label: "HUYETAP_DUOI",name: 'HUYETAP_DUOI', index: 'HUYETAP_DUOI', width: 50, hidden: true},
                    {label: "N.Độ",name: 'NHIET_DO', index: 'NHIET_DO', width: 50},
                    {label: "Toàn thân",name: 'TOANTHAN_SHOW', index: 'TOANTHAN_SHOW', width: 200},
                    {label: "DA_NIEM_MAC",name: 'DA_NIEM_MAC', index: 'DA_NIEM_MAC', width: 50, hidden: true},
                    {label: "TRI_GIAC_GLASSGOW",name: 'TRI_GIAC_GLASSGOW', index: 'TRI_GIAC_GLASSGOW', width: 50, hidden: true},
                    {label: "Hô hấp",name: 'HOHAP_SHOW', index: 'HOHAP_SHOW', width: 200},
                    {label: "Tuần hoàn",name: 'TUANHOAN_SHOW', index: 'TUANHOAN_SHOW', width: 200},
                    {label: "Thông tin thêm",name: 'CHUNG_SHOW', index: 'CHUNG_SHOW', width: 200},
                    {label: "GIAC_NGU_NGHI_NGOI",name: 'GIAC_NGU_NGHI_NGOI', index: 'GIAC_NGU_NGHI_NGOI', width: 50, hidden: true},
                    {label: "VE_SINH_CA_NHAN",name: 'VE_SINH_CA_NHAN', index: 'VE_SINH_CA_NHAN', width: 50, hidden: true},
                    {label: "TINH_THAN",name: 'TINH_THAN', index: 'TINH_THAN', width: 50, hidden: true},
                    {label: "VAN_DONG_PHCN",name: 'VAN_DONG_PHCN', index: 'VAN_DONG_PHCN', width: 50, hidden: true},
                    {label: "GIAO_DUC_SUC_KHOE",name: 'GIAO_DUC_SUC_KHOE', index: 'GIAO_DUC_SUC_KHOE', width: 50, hidden: true},
                    {label: "Nước nhập",name: 'NUOCNHAP_SHOW', index: 'NUOCNHAP_SHOW', width: 200},
                    {label: "Nước xuất",name: 'NUOCXUAT_SHOW', index: 'NUOCXUAT_SHOW', width: 200},
                    {label: "NGUOI_TAO",name: 'NGUOI_TAO', index: 'NGUOI_TAO', width: 50, hidden: true},
                    {label: "TEN_NGUOI_TAO",name: 'TEN_NGUOI_TAO', index: 'TEN_NGUOI_TAO', width: 50, hidden: true},
                    {label: "KEYSIGN",name: 'KEYSIGN', index: 'KEYSIGN', width: 50, hidden: true},
                ],
                rowNum: 1000000,
                caption: "Danh sách đợt chăm sóc cấp 1 chi tiết",
                onSelectRow: function (id) {
                },
                onRightClickRow: function(id) {
                    if (id) {
                        var ret = getThongtinRowSelected("ttcs_list_chamsoccap1_chitiet");
                        var items = {
                            "buocnhay1h": {name: '<p class="text-primary"><i class="fa fa-clock-o text-primary" aria-hidden="true"></i> Bước nhảy 1 giờ</p>'},
                        }
                        if (ret.KEYSIGN == "") {
                            items = {
                                ...items,
                                "capnhat": {name: '<p class="text-success"><i class="fa fa-pencil-square-o text-success" aria-hidden="true"></i> Cập nhật</p>'},
                                "kyso": {name: '<p class="text-primary"><i class="fa fa-key text-primary" aria-hidden="true"></i> Ký số</p>'},
                                "xoa": {name: '<p class="text-danger"><i class="fa fa-remove text-danger" aria-hidden="true"></i> Xoá</p>'},
                            }
                        } else {
                            items = {
                                ...items,
                                "huykyso": {name: '<p class="text-danger"><i class="fa fa-key text-danger" aria-hidden="true"></i> Huỷ ký số</p>'},
                            }
                        }
                        $.contextMenu('destroy', '#ttcs_list_chamsoccap1_chitiet tr');
                        $.contextMenu({
                            selector: '#ttcs_list_chamsoccap1_chitiet tr',
                            reposition : false,
                            callback: function (key, options) {
                                if(key == 'capnhat') {
                                    var ret = getThongtinRowSelected("ttcs_list_chamsoccap1_chitiet");
                                    loadChiTietById(ret);
                                }
                                if(key == 'xoa'){
                                    var ret = getThongtinRowSelected("ttcs_list_chamsoccap1_chitiet");
                                    confirmToClient("Xác nhận xóa đợt chăm sóc cấp 1 chi tiết", function (confirm) {
                                        $.ajax({
                                            url: "cmu_post",
                                            method: "POST",
                                            data: {url:[ret.ID_CHI_TIET, "CMU_LCSC1_CHITIET_DEL"].join("```")},
                                            success: function (data) {
                                                if(data > 0){
                                                    luuLogHSBATheoBN({
                                                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                        LOAI: LOGHSBALOAI.CHAMSOCCAP1CHITIET.KEY,
                                                        NOIDUNGBANDAU: "",
                                                        NOIDUNGMOI: "Xoá chi tiết chăm sóc cấp 1 tờ số " + thongtinhsba.thongtinbn.lanChamSocCap1.TO_SO + ": " + ret.NGAY,
                                                        USERID: singletonObject.userId,
                                                        ACTION: LOGHSBAACTION.DELETE.KEY,
                                                    });
                                                    notifiToClient("Green",MESSAGEAJAX.DEL_SUCCESS);
                                                    $("#csc1_chitiet_lammoi").click();
                                                } else {
                                                    notifiToClient("Red",MESSAGEAJAX.ERROR);
                                                }
                                            },
                                            error: function (error) {
                                                notifiToClient("Red",MESSAGEAJAX.ERROR);
                                            }
                                        });
                                    });
                                }
                                if(key == 'buocnhay1h') {
                                    var ret = getThongtinRowSelected("ttcs_list_chamsoccap1_chitiet");
                                    var ngay = moment(ret.NGAY, ['DD/MM/YYYY HH:mm']).add(1, 'hours').format("DD/MM/YYYY HH:mm");
                                    loadChiTietById({NGAY: ngay});
                                }
                                if (key == 'kyso') {
                                    var ret = getThongtinRowSelected("ttcs_list_chamsoccap1_chitiet");
                                    var allRowData = $("#ttcs_list_chamsoccap1_chitiet").jqGrid('getGridParam', 'data');
                                    var retDateTime = moment(ret.NGAY, "DD/MM/YYYY HH:mm");
                                    var filteredObjects = allRowData.filter(item => {
                                        var itemDateTime = moment(item.NGAY, "DD/MM/YYYY HH:mm");
                                        return itemDateTime.isBefore(retDateTime) && !item.KEYSIGN;
                                    });
                                    if (filteredObjects.length > 0) {
                                        return notifiToClient("Red", "Không thể ký số phiếu này vì đã có phiếu trước ngày " + ret.NGAY + " chưa ký");
                                    }
                                    if (ret.NGUOI_TAO != singletonObject.userId) {
                                        notifiToClient("Red", "Không thể ký số phiếu của người khác: " + ret.TEN_NGUOI_TAO);
                                        return;
                                    }
                                    $("#wrap_canvas").html("<canvas id='myChartCSC1' width='1028' height='584'></canvas>")
                                    ret.ID_DANG_KY = ret.ID_CHI_TIET;
                                    getUrlChamSocCap1(ret, 1).then(objReturn => {
                                        if (objReturn.isError == 0) {
                                            kySoChung({
                                                dvtt: singletonObject.dvtt,
                                                userId: singletonObject.userId,
                                                url: objReturn.url,
                                                loaiGiay: "PHIEU_NOITRU_CHAMSOCCAP1",
                                                maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                                                soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                                                soPhieuDichVu: ret.ID_CHAM_SOC_CAP_1,
                                                maDichVu: ret.ID_CHI_TIET,
                                                soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                                keyword: ";" + ret.ID_CHI_TIET + ";",
                                                userId: singletonObject.userId,
                                                fileName: "Phiếu chăm sóc cấp 1: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - " + thongtinhsba.thongtinbn.SOBENHAN,
                                                visibleType: "100",
                                            }, function(dataKySo) {
                                                luuLogHSBATheoBN({
                                                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                    LOAI: LOGHSBALOAI.CHAMSOCCAP1.KEY,
                                                    NOIDUNGBANDAU: "",
                                                    NOIDUNGMOI: "Ký số phiếu chăm sóc cấp 1 ngày " + ret.NGAY,
                                                    USERID: singletonObject.userId,
                                                    ACTION: LOGHSBAACTION.EDIT.KEY,
                                                })
                                                $("#csc1_chitiet_lammoi").click();
                                            });
                                        } else {
                                            notifiToClient("Red", objReturn.message);
                                        }
                                    }).catch(error => {
                                        notifiToClient("Red", error.message || "Lỗi không xác định");
                                    });
                                }
                                if (key == 'huykyso') {
                                    var ret = getThongtinRowSelected("ttcs_list_chamsoccap1_chitiet");
                                    var allRowData = $("#ttcs_list_chamsoccap1_chitiet").jqGrid('getGridParam', 'data');
                                    var retDateTime = moment(ret.NGAY, "DD/MM/YYYY HH:mm");
                                    var filteredObjects = allRowData.filter(item => {
                                        var itemDateTime = moment(item.NGAY, "DD/MM/YYYY HH:mm");
                                        return itemDateTime.isAfter(retDateTime) && item.KEYSIGN;
                                    });
                                    if (filteredObjects.length > 0) {
                                        return notifiToClient("Red", "Không thể huỷ ký số phiếu này vì đã có phiếu sau ngày " + ret.NGAY + " đã ký");
                                    }
                                    if (ret.NGUOI_TAO != singletonObject.userId) {
                                        notifiToClient("Red", "Không thể ký số phiếu của người khác: " + ret.TEN_NGUOI_TAO);
                                        return;
                                    }
                                    confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                        huykysoFilesign769("PHIEU_NOITRU_CHAMSOCCAP1", ret.ID_CHAM_SOC_CAP_1, singletonObject.userId, singletonObject.dvtt,
                                            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, ret.ID_CHI_TIET, function(data) {
                                                luuLogHSBATheoBN({
                                                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                    LOAI: LOGHSBALOAI.CHAMSOCCAP1.KEY,
                                                    NOIDUNGBANDAU: "",
                                                    NOIDUNGMOI: "Huỷ ký số phiếu chăm sóc cấp 1 ngày " + ret.NGAY,
                                                    USERID: singletonObject.userId,
                                                    ACTION: LOGHSBAACTION.EDIT.KEY,
                                                })
                                                $("#csc1_chitiet_lammoi").click();
                                            })
                                    }, function () {

                                    })
                                }
                            },
                            items: items
                        });
                    }
                }
            });
        }
        reloadDSLanChamSocCap1ChiTiet();
    }

    function reloadDSLanChamSocCap1ChiTiet() {
        var url = "cmu_getlist?url=" + convertArray([thongtinhsba.thongtinbn.lanChamSocCap1.ID_CHAM_SOC_CAP_1, 1, "CMU_LCSC1_CHITIET_SEL"]);
        $("#ttcs_list_chamsoccap1_chitiet").jqGrid('setGridParam', {
            datatype: 'json',
            url: url
        }).trigger('reloadGrid')
    }
})