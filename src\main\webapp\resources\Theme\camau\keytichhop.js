var key = '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'
function checkPlugin()
{
    vnpt_plugin.checkPlugin().then(function (data) {
        if (data === "1") {
            console.log("Plugin đã sẵn sàng")
            clearTimeout(timer); // clear
            vnpt_plugin.setLicenseKey(key).then(function (data) {
                console.log(data);
            }).catch(function (e) {
                console.log(e)
            });
        }
        // else
        // {
        //     timer = setTimeout(checkPlugin, 1500);
        // }
    }).catch(function (e) {
        //alert("VNPT-CA Plugin chưa được cài đặt hoặc chưa được bật");

    });

}
function showMessage(data)
{
    var jsOb = JSON.parse(JSON.parse(data)[0]);
    console.log("data", data);
    switch (jsOb.code)
    {
        case 0:
            err = "Ký thành công";
            jAlert(err);
            $.post('cmu_post', {
                url: [
                    dvtt,
                    Sess_UserID,
                    sovaovien,
                    'ngoaitru_toathuoc',
                    jsOb.data,
                    'CMU_INS_SIGNTOKEN'
                ].join('```')
            })
            $.ajax({type: "POST", url: "cmu_post_kb_kham_benh_upd_stt_htk",
                data:  {url: [dvtt, sovaovien].join('```')}
            });

            var pdf = 'data:application/pdf;base64,' + jsOb.data;
            var link = document.createElement('a');
            link.href = pdf;
            var t = new Date();

            link.download=  t.getTime()+ 'signed.pdf';
            link.click();
            break;
        case 1:
            err = "Dữ liệu đầu vào không đúng định dạng";
            break;
        case 2:
            err = "Không lấy được thông tin chứng thư số";
            break;
        case 3:
            err = "Có lỗi trong quá trình ký số";
            break;
        case 4:
            err = ("Chứng thư số không có khóa bí mật");
            break;
        case 5:
            err = ("Lỗi không xác định");
            break;
        case 6:
            err = ("Ký pdf: không tìm thấy tham số số trang cần ký");
            break;
        case 7:
            err = ("Ký pdf: trang đặt chữ ký không tồn tại");
            break;
        case 8:
            err = ("Ký xml: không tìm thấy thẻ ký số");
            break;
        case 9:
            err = ("Ký pdf: không tìm thấy id của thẻ ký số");
            break;
        case 10:
            err = ("Dữ liệu ký đã chứa một hoặc nhiều chữ ký không hợp lệ");
            break;
        case 11:
            err = ("Người dùng hủy bỏ");
            break;
        case 13:
            err = "Dữ liệu ký rỗng";
            break;
        default:
            err = ("Lỗi không xác định");
            break;
    }
    if (jsOb.code !== 0)
    {
        jAlert(err);
    }
}

function showMessageXN(data)
{
    var jsOb = JSON.parse(JSON.parse(data)[0]);
    console.log("data", data);
    switch (jsOb.code)
    {
        case 0:
            err = "Ký thành công";
            jAlert(err);
            $.post('cmu_post', {
                url: [
                    dvtt,
                    Sess_UserID,
                    sovaovien,
                    CMU_SOPHIEU_XN,
                    jsOb.data,
                    'CMU_INS_SIGNTOKEN'
                ].join('```')
            })

            var pdf = 'data:application/pdf;base64,' + jsOb.data;
            var link = document.createElement('a');
            link.href = pdf;
            var t = new Date();

            link.download=  t.getTime()+ 'signed.pdf';
            link.click();
            break;
        case 1:
            err = "Dữ liệu đầu vào không đúng định dạng";
            break;
        case 2:
            err = "Không lấy được thông tin chứng thư số";
            break;
        case 3:
            err = "Có lỗi trong quá trình ký số";
            break;
        case 4:
            err = ("Chứng thư số không có khóa bí mật");
            break;
        case 5:
            err = ("Lỗi không xác định");
            break;
        case 6:
            err = ("Ký pdf: không tìm thấy tham số số trang cần ký");
            break;
        case 7:
            err = ("Ký pdf: trang đặt chữ ký không tồn tại");
            break;
        case 8:
            err = ("Ký xml: không tìm thấy thẻ ký số");
            break;
        case 9:
            err = ("Ký pdf: không tìm thấy id của thẻ ký số");
            break;
        case 10:
            err = ("Dữ liệu ký đã chứa một hoặc nhiều chữ ký không hợp lệ");
            break;
        case 11:
            err = ("Người dùng hủy bỏ");
            break;
        case 13:
            err = "Dữ liệu ký rỗng";
            break;
        default:
            err = ("Lỗi không xác định");
            break;
    }
    if (jsOb.code !== 0)
    {
        jAlert(err);
    }
}

function showMessageXNKQ(data)
{
    var jsOb = JSON.parse(JSON.parse(data)[0]);
    console.log("data", data);
    switch (jsOb.code)
    {
        case 0:
            err = "Ký thành công";
            jAlert(err);
            $.post('cmu_post', {
                url: [
                    dvtt,
                    $("#nguoidockq").val(),
                    sovaovien,
                    CMU_SOPHIEU_XN+'_KQ',
                    jsOb.data,
                    'CMU_INS_SIGNTOKEN'
                ].join('```')
            })

            var pdf = 'data:application/pdf;base64,' + jsOb.data;
            var link = document.createElement('a');
            link.href = pdf;
            var t = new Date();

            link.download=  t.getTime()+ 'signed.pdf';
            link.click();
            break;
        case 1:
            err = "Dữ liệu đầu vào không đúng định dạng";
            break;
        case 2:
            err = "Không lấy được thông tin chứng thư số";
            break;
        case 3:
            err = "Có lỗi trong quá trình ký số";
            break;
        case 4:
            err = ("Chứng thư số không có khóa bí mật");
            break;
        case 5:
            err = ("Lỗi không xác định");
            break;
        case 6:
            err = ("Ký pdf: không tìm thấy tham số số trang cần ký");
            break;
        case 7:
            err = ("Ký pdf: trang đặt chữ ký không tồn tại");
            break;
        case 8:
            err = ("Ký xml: không tìm thấy thẻ ký số");
            break;
        case 9:
            err = ("Ký pdf: không tìm thấy id của thẻ ký số");
            break;
        case 10:
            err = ("Dữ liệu ký đã chứa một hoặc nhiều chữ ký không hợp lệ");
            break;
        case 11:
            err = ("Người dùng hủy bỏ");
            break;
        case 13:
            err = "Dữ liệu ký rỗng";
            break;
        default:
            err = ("Lỗi không xác định");
            break;
    }
    if (jsOb.code !== 0)
    {
        jAlert(err);
    }
}

function showMessageCDHAKQ(data)
{
    var jsOb = JSON.parse(JSON.parse(data)[0]);
    console.log("data", data);
    switch (jsOb.code)
    {
        case 0:
            err = "Ký thành công";
            jAlert(err);
            $.post('cmu_post', {
                url: [
                    dvtt,
                    Sess_UserID,
                    sovaovien,
                    CMU_SOPHIEU_CDHA+"_"+$("#macdha").val()+"_KQ",
                    jsOb.data,
                    'CMU_INS_SIGNTOKEN'
                ].join('```')
            })

            var pdf = 'data:application/pdf;base64,' + jsOb.data;
            var link = document.createElement('a');
            link.href = pdf;
            var t = new Date();

            link.download=  t.getTime()+ 'signed.pdf';
            link.click();
            break;
        case 1:
            err = "Dữ liệu đầu vào không đúng định dạng";
            break;
        case 2:
            err = "Không lấy được thông tin chứng thư số";
            break;
        case 3:
            err = "Có lỗi trong quá trình ký số";
            break;
        case 4:
            err = ("Chứng thư số không có khóa bí mật");
            break;
        case 5:
            err = ("Lỗi không xác định");
            break;
        case 6:
            err = ("Ký pdf: không tìm thấy tham số số trang cần ký");
            break;
        case 7:
            err = ("Ký pdf: trang đặt chữ ký không tồn tại");
            break;
        case 8:
            err = ("Ký xml: không tìm thấy thẻ ký số");
            break;
        case 9:
            err = ("Ký pdf: không tìm thấy id của thẻ ký số");
            break;
        case 10:
            err = ("Dữ liệu ký đã chứa một hoặc nhiều chữ ký không hợp lệ");
            break;
        case 11:
            err = ("Người dùng hủy bỏ");
            break;
        case 13:
            err = "Dữ liệu ký rỗng";
            break;
        default:
            err = ("Lỗi không xác định");
            break;
    }
    if (jsOb.code !== 0)
    {
        jAlert(err);
    }
}

function showMessageCDHA(data)
{
    var jsOb = JSON.parse(JSON.parse(data)[0]);
    console.log("data", data);
    switch (jsOb.code)
    {
        case 0:
            err = "Ký thành công";
            jAlert(err);
            $.post('cmu_post', {
                url: [
                    dvtt,
                    Sess_UserID,
                    sovaovien,
                    CMU_SOPHIEU_CDHA,
                    jsOb.data,
                    'CMU_INS_SIGNTOKEN'
                ].join('```')
            })

            var pdf = 'data:application/pdf;base64,' + jsOb.data;
            var link = document.createElement('a');
            link.href = pdf;
            var t = new Date();

            link.download=  t.getTime()+ 'signed.pdf';
            link.click();
            break;
        case 1:
            err = "Dữ liệu đầu vào không đúng định dạng";
            break;
        case 2:
            err = "Không lấy được thông tin chứng thư số";
            break;
        case 3:
            err = "Có lỗi trong quá trình ký số";
            break;
        case 4:
            err = ("Chứng thư số không có khóa bí mật");
            break;
        case 5:
            err = ("Lỗi không xác định");
            break;
        case 6:
            err = ("Ký pdf: không tìm thấy tham số số trang cần ký");
            break;
        case 7:
            err = ("Ký pdf: trang đặt chữ ký không tồn tại");
            break;
        case 8:
            err = ("Ký xml: không tìm thấy thẻ ký số");
            break;
        case 9:
            err = ("Ký pdf: không tìm thấy id của thẻ ký số");
            break;
        case 10:
            err = ("Dữ liệu ký đã chứa một hoặc nhiều chữ ký không hợp lệ");
            break;
        case 11:
            err = ("Người dùng hủy bỏ");
            break;
        case 13:
            err = "Dữ liệu ký rỗng";
            break;
        default:
            err = ("Lỗi không xác định");
            break;
    }
    if (jsOb.code !== 0)
    {
        jAlert(err);
    }
}

function showMessageTTPT(data)
{
    var jsOb = JSON.parse(JSON.parse(data)[0]);
    console.log("data", data);
    switch (jsOb.code)
    {
        case 0:
            err = "Ký thành công";
            jAlert(err);
            $.post('cmu_post', {
                url: [
                    dvtt,
                    Sess_UserID,
                    sovaovien,
                    CMU_SOPHIEU_TTPT,
                    jsOb.data,
                    'CMU_INS_SIGNTOKEN'
                ].join('```')
            })

            var pdf = 'data:application/pdf;base64,' + jsOb.data;
            var link = document.createElement('a');
            link.href = pdf;
            var t = new Date();

            link.download=  t.getTime()+ 'signed.pdf';
            link.click();
            break;
        case 1:
            err = "Dữ liệu đầu vào không đúng định dạng";
            break;
        case 2:
            err = "Không lấy được thông tin chứng thư số";
            break;
        case 3:
            err = "Có lỗi trong quá trình ký số";
            break;
        case 4:
            err = ("Chứng thư số không có khóa bí mật");
            break;
        case 5:
            err = ("Lỗi không xác định");
            break;
        case 6:
            err = ("Ký pdf: không tìm thấy tham số số trang cần ký");
            break;
        case 7:
            err = ("Ký pdf: trang đặt chữ ký không tồn tại");
            break;
        case 8:
            err = ("Ký xml: không tìm thấy thẻ ký số");
            break;
        case 9:
            err = ("Ký pdf: không tìm thấy id của thẻ ký số");
            break;
        case 10:
            err = ("Dữ liệu ký đã chứa một hoặc nhiều chữ ký không hợp lệ");
            break;
        case 11:
            err = ("Người dùng hủy bỏ");
            break;
        case 13:
            err = "Dữ liệu ký rỗng";
            break;
        default:
            err = ("Lỗi không xác định");
            break;
    }
    if (jsOb.code !== 0)
    {
        jAlert(err);
    }
}

checkPlugin();

function uuidv4() {
    return ([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g, c =>
        (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
    );
}
function removeAccents(str) {
    var AccentsMap = [
        "aàảãáạăằẳẵắặâầẩẫấậ",
        "AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬ",
        "dđ", "DĐ",
        "eèẻẽéẹêềểễếệ",
        "EÈẺẼÉẸÊỀỂỄẾỆ",
        "iìỉĩíị",
        "IÌỈĨÍỊ",
        "oòỏõóọôồổỗốộơờởỡớợ",
        "OÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢ",
        "uùủũúụưừửữứự",
        "UÙỦŨÚỤƯỪỬỮỨỰ",
        "yỳỷỹýỵ",
        "YỲỶỸÝỴ"
    ];
    for (var i=0; i<AccentsMap.length; i++) {
        var re = new RegExp('[' + AccentsMap[i].substr(1) + ']', 'g');
        var char = AccentsMap[i][0];
        str = str.replace(re, char);
    }
    return str;
}

function doSignPluginSmartca(url) {
    var x = new XMLHttpRequest();
    var mabn = $("#mayte").val();
    x.onload = function() {
        // Create a form
        var reader = new FileReader();
        reader.readAsDataURL(x.response);
        reader.onloadend = function() {

            var base64data = reader.result;
            var refTranId = uuidv4();
            $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien, Sess_UserID,'CMU_SMARTCA_TOATHUOC']))
                .done(function(data){
                    var dataObject = data[0]
                    if(data[0].DAKY > 0) {
                        jAlert("Toa thuốc đã đc gửi.")
                        return false;
                    }
                    var str = [dataObject.USERNAME, dataObject.PASS, "0"];
                    var url = "smartca-kiem-tra-dang-nhap?url=" + convertArray(str);
                    $.ajax({
                        url: url, type: "POST"
                    }).done(function (dt) {
                        if (dt > 0) {
                            $.post('cmu_post', {
                                url: [
                                    dvtt,
                                    mabn,
                                    sovaovien,
                                    0,
                                    '',
                                    'TOATHUOC_BHYT',
                                    Sess_UserID,
                                    0,
                                    refTranId,
                                    refTranId+'-bgd',
                                    maphongkham,
                                    Sess_UserID,
                                    'TBHYT_'+removeAccents($("#hoten").val()),
                                    'CMU_INS_SMARTCA_GRV'
                                ].join('```')
                            })
                            var t = new Date();

                            var fd = new FormData();


                            fd.append("upfile", base64data);
                            fd.append("mabenhnhan", mabn);
                            fd.append("tenfile", t.getTime());
                            fd.append("keyword", "Bác sỹ khám bệnh");

                            // Upload to your server
                            var y = new XMLHttpRequest();
                            y.onreadystatechange = function() {
                                if (y.readyState == XMLHttpRequest.DONE) {
                                    console.log("res", y.responseText);
                                    var pos = y.responseText.replace(';','').split(",")
                                    var x = 140 + Number(pos[0]);
                                    var y2 = -100 + Number(pos[1]);
                                    var x1 = -40 + Number(pos[0]);
                                    var y1 = -15 + Number(pos[1]);
                                    var rect = x+","+y2+","+x1+","+y1
                                    kysosmartca(base64data.replace("data:application/pdf;base64,", ""),
                                        {"signatureText":  "Ngày ký:"+data[0].NGAYGIO+"\nTổ chức xác thực: VNPT SMARTCA RS",
                                            "fontName":"Roboto",
                                            "fontSize":7,
                                            "fontColor":"000000",
                                            "fontStyle":0,
                                            "imageSrc": data[0].CHUKY,
                                            "visibleType":4,"comment":[],
                                            "signatures":[{"rectangle":rect,"page": Number(pos[2])}]},
                                        'BHYT_'+removeAccents($("#hoten").val()),
                                        refTranId,
                                        Sess_UserID
                                    )
                                }
                            }

                            y.open('POST', 'cmu_getposition');
                            y.send(fd);
                        } else if (dt === 0) {
                            jAlert("Không tìm thấy tài khoản trên HIS", 'Thông báo');
                        } else if (dt === -1) {
                            jAlert("Không tìm thấy tài khoản SmartCA", 'Thông báo');
                        } else {
                            jAlert("Đăng nhập thất bại", 'Thông báo');
                        }
                    });
                }).fail(function(){
                jAlert("Lấy dữ liệu thất bại")
            }).always(function(){
            })
        }

    };
    x.responseType = 'blob';    // <-- This is necessary!
    x.open('GET', url, true);
    x.send();
}

function kysosmartca(dataFile, options, scaNameFile, refTranId, userId) {

    var jsonparam = {
        nameFile: scaNameFile,
        dataSigned: dataFile,

        keySign: refTranId,
        vaiTroKySo: 1,
        signedDate: "",
        soChuKyDaKy: 0,
        tongSoChuKy: 1,
        tranId: refTranId,
        userId: Number(userId),
        options: JSON.stringify(options)
    }


    $.ajax({
        type: "POST",
        url: "cmu-sign-smartca",
        dataType: "json",
        contentType: 'application/json',
        data: JSON.stringify(jsonparam),
        success: function (data) {
        },
        error: function () {
        }
    });
    jAlert("Gửi thành công")
    var counter = 0;
    var i = setInterval(function(){
        // do your thing
        $.get("cmu_getlist?url="+convertArray([dvtt, refTranId,'CMU_GET_TRANID_TOATHUOC']))
            .done(function(data){
                if (data[0].TRANID_KHOA != null) {
                    $.ajax({
                        type: "POST",
                        url: "cmu-smartca-get-information",
                        dataType: "json",
                        contentType: 'application/json',
                        data: JSON.stringify({
                            tranId: data[0].TRANID_KHOA,
                            userId: userId,
                        }),
                        success: function (resfile) {
                            console.log("data", resfile)
                            var pdf = 'data:application/pdf;base64,' + resfile.DATA.content.documents[0].dataSigned;
                            var link = document.createElement('a');
                            link.href = pdf;
                            var t = new Date();

                            link.download=   resfile.DATA.content.tranCode+scaNameFile + ".pdf";
                            link.click();
                        },
                        error: function () {
                        }
                    });
                    clearInterval(i);
                }
            })
        counter++;
        if(counter === 40) {
            clearInterval(i);
        }
    }, 10000);
}



function signsmartca(currentUser, nghiepvu, list) {
    var mabs = typeof Sess_UserID == 'undefined'?  currentUser: Sess_UserID;

    var makhambenh = 'kb_'+$("#idtiepnhan").val();
    var idtiepnhan = $("#idtiepnhan").val();
    var sovaovien = $("#sovaovien").val();
    var nghiepvuToathuoc = nghiepvu ? nghiepvu: "ngoaitru_toathuoc";
    var makho = '';
    if(nghiepvuToathuoc == 'ngoaitru_toaquaybanthuocbv') {
        makho = $("#tuthuoctoamuaquay").val();
    }
    var toaThuoc = nghiepvu === 'ngoaitru_toadongy' ? 'toa_y' : 'toa_t';
    var url = "laytrangthaikham_svv_svv?idtiepnhan=" + idtiepnhan + "&dvtt=" + dvtt + "&sovaovien=" + sovaovien;
    if ($("#list_"+list).getGridParam("records") != "0" && $("#icd").val() != "") {
        $.ajax({
            url: url
        }).done(function (data) {
            if($("#icd").val() == "" || $("#cbicd").val() == "") {
                jAlert("Vui lòng thêm chẩn đoán chính và chuẩn đoán phụ");
                return false;
            }
            if(typeof cmuNgoaitruktrathuocvacls == 'function' &&  !cmuNgoaitruktrathuocvacls({
                dvtt: dvtt,
                sovaovien: sovaovien,
                userId: mabs
            })) {
                return false;
            }

            if (intoathuocbacsi == 1) {

                ngoaiTruGuiDonThuocQuocGiaV2(nghiepvuToathuoc, toaThuoc,
                    function() {
                        if(cmusmartca960567 == 1) {
                            doSignSmartca769({
                                url:  (nghiepvuToathuoc == 'ngoaitru_toamuangoai'? "intoathuocmuangoai" : "intoathuoc_theobacsi") +'?makb=kb_' + idtiepnhan + "&nghiepvu="+nghiepvuToathuoc + "&dvtt=" + dvtt + "&mabacsi=" + mabs + "!!!"+makho + "&makho="+makho,
                                kyHieuPhieu: "TOA_THUOC_BHYT_NGOAI",
                                maBenhNhan: $("#mayte").val(),
                                maDichVu: -1,
                                nghiepVu: nghiepvuToathuoc,
                                noiTru: 0,
                                soBe:-1,
                                soBenhAn: "",
                                soPhieuDichVu: nghiepvuToathuoc+mabs+makho,
                                soVaoVien : sovaovien,
                                soVaoVienDT: 0,
                                sttDotDieuTri: "",
                                toaThuoc: idtiepnhan,
                                keyword: nghiepvuToathuoc == 'ngoaitru_toamuangoai'? "Bác sĩ điều trị":  "Bác sỹ khám bệnh",
                                userId: mabs,
                                userName: "",
                                fileName: idtiepnhan+mabs,
                                dvtt: dvtt
                            })
                        } else {
                            doSignPluginSmartca('intoathuoc_theobacsi?makb=kb_' + idtiepnhan + "&nghiepvu="+ nghiepvuToathuoc + "&dvtt=" + dvtt + "&mabacsi=" + mabs)

                        }
                    }

                )
                return false;
            }
            if (data == "3" || data == "6") {
                if (idtiepnhan != "" && $("#icd").val() != "") {

                    ngoaiTruGuiDonThuocQuocGiaV2("ngoaitru_toathuoc", toaThuoc,
                        function() {
                            if(cmusmartca960567 == 1) {
                                var url = 'intoathuoc?makb=' + makhambenh + "&nghiepvu="+nghiepvuToathuoc+"&dvtt="+dvtt+"&makho="+makho;
                                if(dvtt == "96146") {
                                    var sothebhyt = $("#bhyt").val();
                                    var tentoathuoc = 'ĐƠN THUỐC';
                                    if( nghiepvuToathuoc == 'toathuoc_nghien') {
                                        tentoathuoc = 'ĐƠN THUỐC NGHIỆN'
                                    }
                                    var ngay = $("#idtiepnhan").val().split("_")
                                    var arr = [
                                        $("#hoten").val(),
                                        $("#mayte").val(),
                                        dvtt,
                                        $("#icd").val() + "-" +$("#cbicd").val()+";"+$("#benhphu").val(),
                                        sovaovien,
                                        $("#loidan").val(),
                                        $("#gioitinh").val() == "true"? "Nam": "Nữ",
                                        nghiepvuToathuoc,
                                        0,
                                        sothebhyt,
                                        sothebhyt.substring(0, 2),
                                        sothebhyt.substring(2, 3),
                                        sothebhyt.substring(3, 5),
                                        sothebhyt.substring(5, 7),
                                        sothebhyt.substring(7, 10),
                                        sothebhyt.substring(10, 15),
                                        'tt_'+$("#idtiepnhan").val(),
                                        tentoathuoc,
                                        ngay[0],
                                        ngay[1],
                                        ngay[2],
                                        $("#diachi").val()
                                    ];
                                    var param = [
                                        'hovaten',
                                        'masonguoibenh',
                                        "dvtt",
                                        'chandoan',
                                        "sovaovien",
                                        "loidantoathuoc",
                                        "gioitinh",
                                        "nghiepvu",
                                        "mabacsi",
                                        "sothebhyt",
                                        "mathe_2kytudau",
                                        "mathe_kythu3",
                                        "the45",
                                        "the67",
                                        "the8910",
                                        "mathe_5kytucuoi",
                                        "matoathuoc",
                                        "tentoathuoc",
                                        'nam',
                                        'thang',
                                        'ngay',
                                        'diachi'
                                    ];
                                    url = "cmu_injasper?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf&jasper=rp_donthuoc_tt05_96146";

                                }
                                doSignSmartca769({
                                    url: url,
                                    kyHieuPhieu: "TOA_THUOC_BHYT_NGOAI",
                                    maBenhNhan: $("#mayte").val(),
                                    maDichVu: -1,
                                    nghiepVu: nghiepvuToathuoc,
                                    noiTru: 1,
                                    soBe:-1,
                                    soBenhAn: "",
                                    soPhieuDichVu: nghiepvuToathuoc+mabs+makho,
                                    soVaoVien : sovaovien,
                                    soVaoVienDT: 0,
                                    sttDotDieuTri: "",
                                    toaThuoc: idtiepnhan,
                                    keyword: "Bác sỹ khám bệnh",
                                    userId: mabs,
                                    userName: "",
                                    fileName: idtiepnhan+mabs,
                                    dvtt: dvtt
                                })
                            } else {
                                doSignPluginSmartca('intoathuoc?makb=' + makhambenh + "&nghiepvu="+nghiepvuToathuoc+"&dvtt="+dvtt)
                            }

                        });

                    return false;
                }

            } else if (data == "cocls") {
                jAlert("Bệnh nhân chưa thực hiện hết các chỉ định CLS", 'Cảnh báo');
                return false;
            } else {
                jAlert("Bạn chưa hoàn tất khám cho bệnh nhân", 'Cảnh báo');
                return false;
            }
        });
    } else {
        jAlert("Chưa có thuốc trong toa", 'Cảnh báo');
    }
}

function huysignsmartca(userId, nghiepvu) {
    jConfirm('Bạn có muốn hủy ký số?', 'Thông báo', function (r) {
        if (r.toString() == "true") {
            if(cmusmartca960567 == 1) {
                var nghiepvuToathuoc = nghiepvu ? nghiepvu: "ngoaitru_toathuoc";
                var makho = '';
                if(nghiepvuToathuoc == 'ngoaitru_toaquaybanthuocbv') {
                    makho = $("#tuthuoctoamuaquay").val();
                }
                $.get("cmu_getlist?url="+convertArray([
                    dvtt,
                    sovaovien,
                    0,
                    "TOA_THUOC_BHYT_NGOAI",
                    nghiepvuToathuoc+userId+makho,
                    userId,
                    -1,
                    'CMU_SMART769_GET'])).done(function(data) {
                    if(data.length == 0) {
                        return jAlert("Không tìm thấy file ký số")
                    }
                    $.post("smartca-capnhat-huykyso?keysign="+data[0].KEYSIGN).done(function() {
                        jAlert("Hủy thành công")
                    })
                })
                return false;
            }
            $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien, userId,'CMU_SMARTCA_HUYTOATHUOC']))
                .done(function(data){
                    jAlert("Hủy thành công")
                })
        }
    });
}

function insignsmartca(userId, nghiepvu) {
    if(cmusmartca960567 == 1) {
        var nghiepvuToathuoc = nghiepvu ? nghiepvu: "ngoaitru_toathuoc";
        var makho = '';
        if(nghiepvuToathuoc == 'ngoaitru_toaquaybanthuocbv') {
            makho = $("#tuthuoctoamuaquay").val();
        }
        $.get("cmu_getlist?url="+convertArray([
            dvtt,
            sovaovien,
            0,
            "TOA_THUOC_BHYT_NGOAI",
            nghiepvuToathuoc+userId+makho,
            userId,
            -1,
            'CMU_SMART769_GET'])).done(function(data) {
            if(data.length == 0) {
                return jAlert("Không tìm thấy file ký số")
            }
            downloadkyso769(data[0].KEYSIGN, 'PDF')
        })
        return false;
    }
    $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien, userId,'CMU_SMARTCA_INTOATHUOC']))
        .done(function(data){

            if (data.length > 0 && data[0].TRANID_KHOA != null) {
                $.ajax({
                    type: "POST",
                    url: "cmu-smartca-get-information",
                    dataType: "json",
                    contentType: 'application/json',
                    data: JSON.stringify({
                        tranId: data[0].TRANID_KHOA,
                        userId: userId,
                    }),
                    success: function (resfile) {
                        console.log("data", resfile)
                        var pdf = 'data:application/pdf;base64,' + resfile.DATA.content.documents[0].dataSigned;
                        var link = document.createElement('a');
                        link.href = pdf;
                        var t = new Date();

                        link.download=   resfile.DATA.content.tranCode+scaNameFile + ".pdf";
                        link.click();
                    },
                    error: function () {
                    }
                });
                clearInterval(i);
            } else {
                jAlert("Toa thuốc chưa được ký")
            }
        })
}

function kyketquaxetnghiem(userId) {
    if ($("#hoten").val().trim() != "") {
        var makhambenh = $("#makhambenh").val();
        var hoten = $("#hoten").val();
        var namsinh = $("#namsinh").val();
        var gioitinh = $("#gioitinh option:selected").text();
        var diachi = $("#diachi").val();
        var chandoan = $("#ketqua").val();
        var bschidinh = $("#nguoichidinh").val();
        var noitru = $("#noitru").val();
        var sttbenhan = $("#sttbenhan").val();
        var sttdotdieutri = $("#sttdotdieutri").val();
        var sttdieutri = $("#sttdieutri").val();
        var sothebhyt = $("#sothebhyt").val();
        var sophieuxn = $("#sophieuxn").val();
        var capcuu = $("#capcuu").val();
        var ngaytao = convertStr_MysqlDate($("#ngaytao").val());
        var tuoi = $("#tuoi").val();
        var ngaychidinh = convertStr_MysqlDate($("#ngaychidinh").val());
        var khoachidinh = $("#khoachidinh").val();
        var chandoanbenh = $('#chandoanbenh').val();
        if(sophieuxn.indexOf("/") == -1) {
            sttdieutri = '0'
        }
        // var selIds = $("#list_xn_bhyt").jqGrid("getGridParam", "selarrrow"), cellValues = [];
        // for (i = 0, n = selIds.length; i < n; i++) {
        //     cellValues.push($("#list_xn_bhyt").jqGrid("getCell", selIds[i], "MA_XETNGHIEM"));
        // }
        var maxndchon = '';
        $("#list_xn_bhyt input:checked").each(function () {
            maxndchon += $(this).parent().parent().find('td[aria-describedby="list_xn_bhyt_MA_XETNGHIEM"]').html().trim() + ',';
        })
        var arr = [dvtt,makhambenh, hoten, namsinh,
            gioitinh, diachi, chandoan, bschidinh, noitru,
            sttbenhan, sttdotdieutri, sttdieutri+'--,'+maxndchon, sothebhyt,
            sophieuxn, capcuu, ngaytao, tuoi, ngaychidinh,
            khoachidinh, $("#loaixetnghiem").val(),
            sovaovien, sovaovien_noi, sovaovien_dt_noi, chandoanbenh,
            $("#mayte").val(),
            '/WEB-INF/pages/camau/reports/rp_phieuhoasinhmau.jasper',
            '/WEB-INF/pages/camau/reports/rp_phieuxetnghiemhoasinhnuoctieu.jasper',
            '/WEB-INF/pages/camau/reports/rp_phieuxetnghiemhuyethoc_tk.jasper',
            '/WEB-INF/pages/camau/reports/rp_phieuxetnghiembenhpham.jasper',
            '/WEB-INF/pages/camau/reports/rp_phieuxetnghiemhuyethoc_dt.jasper',
            '/WEB-INF/pages/camau/reports/rp_phieuxetnghiemhuyethoc_vs.jasper',
            '/WEB-INF/pages/camau/reports/rp_phieuxetnghiemtong.jasper',
        ];
        var phieuxn = "rp_phieuketquaxetnghiem_theomau";
        if(dvtt == 96161 || dvtt == 96163) {
            phieuxn = "rp_phieuketquaxetnghiem_theomau_96161";
        }

        var param = ['madonvi','makhambenh',  'hoten',"namsinh",
            'gioitinh','diachi_bn','chandoan','bsdieutri','noitru',
            'stt_benhan','stt_dotdieutri','stt_dieutri','sothebaohiem',
            'sophieuxn','capcuu','ngaytao', 'tuoi','ngaydieutri',
            'khoa','loaixetnghiem','sovaovien','sovaovien_noi', 'sovaovien_dt_noi', 'chandoan',
            'mabenhnhan',
            'FILE_1',
            'FILE_2',
            'FILE_3',
            'FILE_4',
            'FILE_5',
            'FILE_6',
            'FILE_7',
        ];
        var url = "cmu_injasper?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf&jasper="+phieuxn;
        if (dvtt == 96014) {
            arr = [makhambenh, hoten, namsinh,
                gioitinh, diachi, chandoan, bschidinh, 0, noitru,
                sttbenhan, sttdotdieutri, sttdieutri, 0, sothebhyt,
                sophieuxn, capcuu, ngaytao, tuoi, ngaychidinh,0,
                khoachidinh, $("#loaixetnghiem").val(),
                sovaovien, sovaovien_noi, sovaovien_dt_noi, chandoanbenh,0,
                $("#mayte").val(),
                0
            ]
            var selIds = $("#list_xn_bhyt").jqGrid("getGridParam", "selarrrow"), cellValues = [];
            for (var i = 0, n = selIds.length; i < n; i++) {
                cellValues.push($("#list_xn_bhyt").jqGrid("getCell", selIds[i], "MA_XETNGHIEM"));
            }
            var maxndchon = "(" + cellValues.join(",") + ")";

            //End VNPT BDG 05/12/2016 -- Huy Linh
            url = "xetnghiem_inketqua_svv?url=" + convertArray(arr) + "&maxetnghiem=" + maxndchon;
        }

        doSignXNSmartca( url, userId);
    }
}

function inketquaxetnghiem(userId, pDvtt) {
    var Sess_UserID = userId
    var sovaovien_t = sovaovien == 0? sovaovien_noi: sovaovien;
    dvtt = pDvtt;
    var mabn = $("#mabenhnhan").val();
    $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien_t, 0,'PHIEUXN', $("#sophieu").val(),'CMU_SMARTCA_INCLS_NGOAI']))
        .done(function(data){

            if (data.length > 0 && data[0].TRANID_KHOA != null) {
                $.ajax({
                    type: "POST",
                    url: "cmu-smartca-get-information",
                    dataType: "json",
                    contentType: 'application/json',
                    data: JSON.stringify({
                        tranId: data[0].TRANID_KHOA,
                        userId: $("#nguoidockq").val(),
                    }),
                    success: function (resfile) {
                        console.log("data", resfile)
                        var pdf = 'data:application/pdf;base64,' + resfile.DATA.content.documents[0].dataSigned;
                        var link = document.createElement('a');
                        link.href = pdf;
                        var t = new Date();

                        link.download=   resfile.DATA.content.tranCode+ ".pdf";
                        link.click();
                    },
                    error: function () {
                    }
                });
                clearInterval(i);
            } else {
                jAlert("Phiếu chưa được ký")
            }
        })
}
function kyketquasieuam(userId, pDvtt) {
    if ($("#hoten_ct").val().trim() != "") {
        var mabenhnhan = $("#mabenhnhan").val();
        var hoten = $("#hoten").val();
        var diachi = $("#diachi").val();
        var tuoi = $("#tuoi").val();
        var phai = $("#gioitinh").val();
        var sothebhyt = $("#sothebhyt").val();
        var namsinh = $("#namsinh").val();
        var tenkhoa = $("#tenkhoa").val();
        //if (phai.toString() == "true") {
        //  phai = "Nam";
        //} else {
        //  phai = "Nữ";
        //}
        dvtt = pDvtt;
        var phai = $("#gioitinh_ct").val();
        var sophieu = $("#sophieu").val();
        var makhambenh = $("#makhambenh").val();
        var macdha = $("#macdha").val();
        var noitru = $("#noitru").val();
        var sttbenhan = $("#sttbenhan").val();
        var sttdotdieutri = $("#sttdotdieutri").val();
        var sttdieutri = $("#sttdieutri").val();
        var bssieuam = $("#bacsisieuam").val() != null && $("#bacsisieuam").val() != '' ? $("#bacsisieuam").val() : " ";
        // VNPTHIS-4697 23/11/2017
        var typein = '2';
        // VNPTHIS-4697 23/11/2017
        if (sophieu != "" && macdha != "") {
            var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha,
                dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, sothebhyt, "0",
                sovaovien,
                sovaovien_noi,
                sovaovien_dt_noi, 0, $("#trieuchungls").val(), bssieuam, maPhongBan ? maPhongBan : 0, namsinh, tenkhoa, 0, typein,
                $("#mausieuam").val(), "0"]; // VNPTHIS-4697 23/11/2017 thêm
            var selected = [];
            $("#list_hinhanhnoisoi").jqGrid('getGridParam', 'selarrrow').forEach(function (data) {
                var stt = $("#list_hinhanhnoisoi").jqGrid('getCell', data, 'STT');
                selected.push(stt);
            });
            var url = "inketquasieuam_svv?thongtin=" + convertArray(arr) + "&anh=" + selected;
            doSignSASmartca( url, userId, dvtt);
        }

    }
}

function inketquasieuam(userId, pDvtt) {
    var Sess_UserID = userId
    var sovaovien_t = sovaovien == 0? sovaovien_noi: sovaovien;
    dvtt = pDvtt;
    var mabn = $("#mabenhnhan").val();
    $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien_t, 0,'PHIEUSA', $("#sophieu").val(),'CMU_SMARTCA_INCLS_NGOAI']))
        .done(function(data){

            if (data.length > 0 && data[0].TRANID_KHOA != null) {
                $.ajax({
                    type: "POST",
                    url: "cmu-smartca-get-information",
                    dataType: "json",
                    contentType: 'application/json',
                    data: JSON.stringify({
                        tranId: data[0].TRANID_KHOA,
                        userId: userId,
                    }),
                    success: function (resfile) {
                        console.log("data", resfile)
                        var pdf = 'data:application/pdf;base64,' + resfile.DATA.content.documents[0].dataSigned;
                        var link = document.createElement('a');
                        link.href = pdf;
                        var t = new Date();

                        link.download=   resfile.DATA.content.tranCode+ ".pdf";
                        link.click();
                    },
                    error: function () {
                    }
                });
                clearInterval(i);
            } else {
                jAlert("Phiếu chưa được ký")
            }
        })
}

function doSignXNSmartca(url, userId) {
    var x = new XMLHttpRequest();
    var mabn = $("#mayte").val();
    var Sess_UserID = $("#nguoidockq").val()
    var sovaovien_t = sovaovien == 0? sovaovien_noi: sovaovien;

    x.onload = function() {
        // Create a form
        var reader = new FileReader();
        reader.readAsDataURL(x.response);
        reader.onloadend = function() {

            var base64data = reader.result;
            var refTranId = uuidv4();
            $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien_t, sovaovien_dt_noi, $("#nguoidockq").val(), $("#sophieuxn").val(), userId,'CMU_SMARTCA_XETNGHIEM']))
                .done(function(data){
                    var dataObject = data[0]
                    if(data[0].DAKY > 0) {
                        jAlert("Phiếu XN đã được gửi.")
                        return false;
                    }
                    var str = [dataObject.USERNAME, dataObject.PASS, "0"];
                    var url = "smartca-kiem-tra-dang-nhap?url=" + convertArray(str);
                    $.ajax({
                        url: url, type: "POST"
                    }).done(function (dt) {
                        if (dt > 0) {
                            $.post('cmu_post', {
                                url: [
                                    dvtt,
                                    mabn,
                                    sovaovien_t,
                                    sovaovien_dt_noi,
                                    $("#sophieuxn").val(),
                                    'PHIEUXN',
                                    Sess_UserID,
                                    0,
                                    refTranId,
                                    refTranId+'-bgd',
                                    0,
                                    userId,
                                    'XN_'+removeAccents($("#hoten").val()),
                                    'CMU_INS_SMARTCA_GRV'
                                ].join('```')
                            })
                            var t = new Date();

                            var fd = new FormData();


                            fd.append("upfile", base64data);
                            fd.append("mabenhnhan", mabn);
                            fd.append("tenfile", t.getTime());
                            if(dvtt == 96014) {
                                fd.append("keyword", "TRƯỞNG KHOA XÉT NGHIỆM");
                            } else {
                                fd.append("keyword", "KÝ DUYỆT KẾT QUẢ");
                            }


                            // Upload to your server
                            var y = new XMLHttpRequest();
                            y.onreadystatechange = function() {
                                if (y.readyState == XMLHttpRequest.DONE) {
                                    console.log("res", y.responseText);
                                    var signatures = [];
                                    y.responseText.split(";").forEach(function(value, id) {
                                        if (id < y.responseText.split(";").length - 1) {
                                            var pos = value.split(",")
                                            var x = 140 + Number(pos[0]);
                                            var y2 = -100 + Number(pos[1]);
                                            var x1 = -40 + Number(pos[0]);
                                            var y1 = -15 + Number(pos[1]);
                                            var rect = x+","+y2+","+x1+","+y1
                                            signatures.push({"rectangle":rect,"page": Number(pos[2])})
                                        }

                                    })
                                    kysosmartca(base64data.replace("data:application/pdf;base64,", ""),
                                        {"signatureText": "Ngày ký:"+data[0].NGAYGIO+"\nTổ chức xác thực: VNPT SMARTCA RS",
                                            "fontName":"Roboto",
                                            "fontSize":7,
                                            "fontColor":"000000",
                                            "fontStyle":0,
                                            "imageSrc": data[0].CHUKY,
                                            "visibleType":4,"comment":[],
                                            "signatures":signatures},
                                        'XN_'+removeAccents($("#hoten").val()),
                                        refTranId,
                                        Sess_UserID
                                    )
                                }
                            }

                            y.open('POST', 'cmu_getposition');
                            y.send(fd);
                        } else if (dt === 0) {
                            jAlert("Không tìm thấy tài khoản trên HIS", 'Thông báo');
                        } else if (dt === -1) {
                            jAlert("Không tìm thấy tài khoản SmartCA", 'Thông báo');
                        } else {
                            jAlert("Đăng nhập thất bại", 'Thông báo');
                        }
                    });
                }).fail(function(){
                jAlert("Lấy dữ liệu thất bại")
            }).always(function(){
            })
        }

    };
    x.responseType = 'blob';    // <-- This is necessary!
    x.open('GET', url, true);
    x.send();
}


function kyketquaxquang(userId, pDvtt) {
    var maPhongBan = $("#phongban").val();
    var mabenhnhan = $("#mabenhnhan").val();
    var hoten = $("#hoten").val();
    var diachi = $("#diachi").val();
    var tuoi = $("#tuoi").val();
    var phai = $("#gioitinh").val() === "true" ? "Nam" : "Nữ";
    var sophieu = $("#sophieu").val();
    var makhambenh = $("#makhambenh").val();
    var macdha = $("#macdha").val();
    dvtt = pDvtt;
    var noitru = $("#noitru").val();
    var sttbenhan = $("#sttbenhan").val();
    var sttdotdieutri = $("#sttdotdieutri").val();
    var sttdieutri = $("#sttdieutri").val();
    var khoa = $("#Khoa").val();
    var giuong = $("#giuong").val();
    var buong = $("#buong").val();
    var sothebaohiem = $("#sothebhyt").val();
    var chandoan = $("#chandoan").val();
    var bacsidieutri = $("#bacsichidinh").val();
    var mabacsichidinh = $("#mabacsichidinh").val();
    var bacsichuyenkhoa = $("#bacsichuyenkhoa").val();
    var ngaychidinh = $("#ngaychidinh").val();
    var solan = $("#solan").val();
    //VNPTHIS-4697 23/11/2017
    var typein = 2;
    //VNPTHIS-4697 23/11/2017
    if (sophieu != "" && macdha != "") {
        var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha,
            dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, khoa, giuong, buong, sothebaohiem, chandoan, bacsidieutri, bacsichuyenkhoa, solan, mabacsichidinh, ngaychidinh, "0",
            sovaovien,
            sovaovien_noi,
            sovaovien_dt_noi, 0, maPhongBan,0, typein]; //VNPTHIS-4697 23/11/2017 thêm loại in
        //jAlert(arr);
        var url = "inketquaxquang_svv?url=" + convertArray(arr) + "&ngaytrakq=" + $("#ngayth_ct").val();
        doSignXQUANGSmartca( url, userId, dvtt);
    }
}

function doSignXQUANGSmartca(url, userId, pDvtt) {
    var x = new XMLHttpRequest();
    var mabn = $("#mabenhnhan").val();
    var Sess_UserID = userId
    var sovaovien_t = sovaovien == 0? sovaovien_noi: sovaovien;
    dvtt = pDvtt
    x.onload = function() {
        // Create a form
        var reader = new FileReader();
        reader.readAsDataURL(x.response);
        reader.onloadend = function() {

            var base64data = reader.result;
            var refTranId = uuidv4();
            $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien_t, sovaovien_dt_noi, userId, $("#sophieu").val(), userId,'CMU_SMARTCA_XQUANG']))
                .done(function(data){
                    var dataObject = data[0]
                    if(data[0].DAKY > 0) {
                        jAlert("Phiếu XQUANG đã được gửi.")
                        return false;
                    }
                    var str = [dataObject.USERNAME, dataObject.PASS, "0"];
                    var url = "smartca-kiem-tra-dang-nhap?url=" + convertArray(str);
                    $.ajax({
                        url: url, type: "POST"
                    }).done(function (dt) {
                        if (dt > 0) {
                            $.post('cmu_post', {
                                url: [
                                    dvtt,
                                    mabn,
                                    sovaovien_t,
                                    sovaovien_dt_noi,
                                    $("#sophieu").val(),
                                    'PHIEUXQUANG',
                                    Sess_UserID,
                                    0,
                                    refTranId,
                                    refTranId+'-bgd',
                                    0,
                                    userId,
                                    'XQUANG_'+removeAccents($("#hoten").val()),
                                    'CMU_INS_SMARTCA_GRV'
                                ].join('```')
                            })
                            var t = new Date();

                            var fd = new FormData();


                            fd.append("upfile", base64data);
                            fd.append("mabenhnhan", mabn);
                            fd.append("tenfile", t.getTime());
                            fd.append("keyword", "BÁC SĨ CHUYÊN KHOA");

                            // Upload to your server
                            var y = new XMLHttpRequest();
                            y.onreadystatechange = function() {
                                if (y.readyState == XMLHttpRequest.DONE) {
                                    console.log("res", y.responseText);
                                    var signatures = [];
                                    y.responseText.split(";").forEach(function(value, id) {
                                        if (id < y.responseText.split(";").length - 1) {
                                            var pos = value.split(",")
                                            var x = 140 + Number(pos[0]);
                                            var y2 = -100 + Number(pos[1]);
                                            var x1 = -40 + Number(pos[0]);
                                            var y1 = -15 + Number(pos[1]);
                                            var rect = x+","+y2+","+x1+","+y1
                                            signatures.push({"rectangle":rect,"page": Number(pos[2])})
                                        }

                                    })
                                    kysosmartca(base64data.replace("data:application/pdf;base64,", ""),
                                        {"signatureText": "Ngày ký:"+data[0].NGAYGIO+"\nTổ chức xác thực: VNPT SMARTCA RS",
                                            "fontName":"Roboto",
                                            "fontSize":7,
                                            "fontColor":"000000",
                                            "fontStyle":0,
                                            "imageSrc": data[0].CHUKY,
                                            "visibleType":4,"comment":[],
                                            "signatures":signatures},
                                        'XQUANG_'+removeAccents($("#hoten").val()),
                                        refTranId,
                                        Sess_UserID
                                    )
                                }
                            }

                            y.open('POST', 'cmu_getposition');
                            y.send(fd);
                        } else if (dt === 0) {
                            jAlert("Không tìm thấy tài khoản trên HIS", 'Thông báo');
                        } else if (dt === -1) {
                            jAlert("Không tìm thấy tài khoản SmartCA", 'Thông báo');
                        } else {
                            jAlert("Đăng nhập thất bại", 'Thông báo');
                        }
                    });
                }).fail(function(){
                jAlert("Lấy dữ liệu thất bại")
            }).always(function(){
            })
        }

    };
    x.responseType = 'blob';    // <-- This is necessary!
    x.open('GET', url, true);
    x.send();
}

function inketquaxquang(userId, pDvtt) {
    var Sess_UserID = userId
    var sovaovien_t = sovaovien == 0? sovaovien_noi: sovaovien;
    dvtt = pDvtt;
    var mabn = $("#mabenhnhan").val();
    $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien_t, 0,'PHIEUXQUANG', $("#sophieu").val(),'CMU_SMARTCA_INCLS_NGOAI']))
        .done(function(data){

            if (data.length > 0 && data[0].TRANID_KHOA != null) {
                $.ajax({
                    type: "POST",
                    url: "cmu-smartca-get-information",
                    dataType: "json",
                    contentType: 'application/json',
                    data: JSON.stringify({
                        tranId: data[0].TRANID_KHOA,
                        userId: userId,
                    }),
                    success: function (resfile) {
                        console.log("data", resfile)
                        var pdf = 'data:application/pdf;base64,' + resfile.DATA.content.documents[0].dataSigned;
                        var link = document.createElement('a');
                        link.href = pdf;
                        var t = new Date();

                        link.download=   resfile.DATA.content.tranCode+ ".pdf";
                        link.click();
                    },
                    error: function () {
                    }
                });
                clearInterval(i);
            } else {
                jAlert("Phiếu chưa được ký")
            }
        })
}


function kyketquadientim(userId, pDvtt) {
    var mabenhnhan = $("#mabenhnhan").val();
    var hoten = $("#hoten").val();
    var diachi = $("#diachi").val();
    var tuoi = $("#tuoi").val();
    var phai = $("#gioitinh").val();
    if (phai == "true") {
        phai = "Nam";
    } else {
        phai = "Nữ";
    }
    var sophieu = $("#sophieu").val();
    var makhambenh = $("#makhambenh").val();
    var macdha = $("#macdha").val();
    dvtt = pDvtt;
    var noitru = $("#noitru").val();
    var sttbenhan = $("#sttbenhan").val();
    var sttdotdieutri = $("#sttdotdieutri").val();
    var sttdieutri = $("#sttdieutri").val();
    var cannang = $("#cannang").val();
    var chieucao = $("#chieucao").val();
    var khoa = $("#Khoa").val();
    var giuong = $("#giuong").val();
    var buong = $("#buong").val();
    var sothebaohiem = $("#sothebhyt").val();
    var chandoan = $("#chandoan").val();
    var yeucaukiemtra = $("#yeucaukiemtra").val();
    var bacsidieutri = $("#bacsichidinh").val();
    var bacsichuyenkhoa = $("#bacsichuyenkhoa").val();
    if (bacsichuyenkhoa == "") {
        bacsichuyenkhoa = nv_with_chucdanh;
    }
    var solan = $("#solan").val();
    var tencdha = $("#tencdha").val();
    var motacdha = $("#loaimauin").val();
    //VNPTHIS-4697 23/11/2017
    var type = 2;
    var ngayth = $("#ngayth_ct").val();
    if(ngayth == ""){
        ngayth = $("#ngaythuchien_cls").val();
    }
    var gioth = $("#gioth_ct").val();
    if(gioth == ""){
        gioth = $("#giothuchien_cls").val();
    }

    let gridDienTim = $("#list_dientim_bhyt");
    let rowId = gridDienTim.jqGrid('getGridParam', 'selrow');
    let rowData = gridDienTim.jqGrid('getRowData', rowId);
    yeucaukiemtra = yeucaukiemtra ? yeucaukiemtra + ". " + rowData.TRUE_TEN_CDHA : rowData.TRUE_TEN_CDHA;

    //VNPTHIS-4697 23/11/2017
    if (sophieu != "" && macdha != "") {
        var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha,
            dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, cannang, chieucao,
            khoa, giuong, buong, sothebaohiem, chandoan, yeucaukiemtra,
            bacsidieutri, bacsichuyenkhoa, solan, tencdha, "0", motacdha, sovaovien
            , sovaovien_noi
            , sovaovien_dt_noi, 0, type, $("#ngayth_ct").val(), $("#gioth_ct").val() =="" ? "00:00:00" : $("#gioth_ct").val(),"0"];  //VNPTHIS-4697 23/11/2017 thêm loại in
        var url = "inketquadientim_svv?url=" + convertArray(arr);
        doSignDientimSmartca( url, userId, dvtt);
    }
}

function doSignDientimSmartca(url, userId, pDvtt) {
    var x = new XMLHttpRequest();
    var mabn = $("#mabenhnhan").val();
    var Sess_UserID = userId
    var sovaovien_t = sovaovien == 0? sovaovien_noi: sovaovien;
    dvtt = pDvtt
    x.onload = function() {
        // Create a form
        var reader = new FileReader();
        reader.readAsDataURL(x.response);
        reader.onloadend = function() {

            var base64data = reader.result;
            var refTranId = uuidv4();
            $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien_t, sovaovien_dt_noi, userId, $("#sophieu").val(), userId,'CMU_SMARTCA_DIENTIM']))
                .done(function(data){
                    var dataObject = data[0]
                    if(data[0].DAKY > 0) {
                        jAlert("Phiếu Điện tim đã được gửi.")
                        return false;
                    }
                    var str = [dataObject.USERNAME, dataObject.PASS, "0"];
                    var url = "smartca-kiem-tra-dang-nhap?url=" + convertArray(str);
                    $.ajax({
                        url: url, type: "POST"
                    }).done(function (dt) {
                        if (dt > 0) {
                            $.post('cmu_post', {
                                url: [
                                    dvtt,
                                    mabn,
                                    sovaovien_t,
                                    sovaovien_dt_noi,
                                    $("#sophieu").val(),
                                    'PHIEUDIENTIM',
                                    Sess_UserID,
                                    0,
                                    refTranId,
                                    refTranId+'-bgd',
                                    0,
                                    userId,
                                    'DIENTIM_'+removeAccents($("#hoten").val()),
                                    'CMU_INS_SMARTCA_GRV'
                                ].join('```')
                            })
                            var t = new Date();

                            var fd = new FormData();


                            fd.append("upfile", base64data);
                            fd.append("mabenhnhan", mabn);
                            fd.append("tenfile", t.getTime());
                            fd.append("keyword", "BÁC SĨ CHUYÊN KHOA");

                            // Upload to your server
                            var y = new XMLHttpRequest();
                            y.onreadystatechange = function() {
                                if (y.readyState == XMLHttpRequest.DONE) {
                                    console.log("res", y.responseText);
                                    var signatures = [];
                                    y.responseText.split(";").forEach(function(value, id) {
                                        if (id < y.responseText.split(";").length - 1) {
                                            var pos = value.split(",")
                                            var x = 140 + Number(pos[0]);
                                            var y2 = -100 + Number(pos[1]);
                                            var x1 = -40 + Number(pos[0]);
                                            var y1 = -15 + Number(pos[1]);
                                            var rect = x+","+y2+","+x1+","+y1
                                            signatures.push({"rectangle":rect,"page": Number(pos[2])})
                                        }

                                    })
                                    kysosmartca(base64data.replace("data:application/pdf;base64,", ""),
                                        {"signatureText": "Ngày ký:"+data[0].NGAYGIO+"\nTổ chức xác thực: VNPT SMARTCA RS",
                                            "fontName":"Roboto",
                                            "fontSize":7,
                                            "fontColor":"000000",
                                            "fontStyle":0,
                                            "imageSrc": data[0].CHUKY,
                                            "visibleType":4,"comment":[],
                                            "signatures":signatures},
                                        'DIENTIM_'+removeAccents($("#hoten").val()),
                                        refTranId,
                                        Sess_UserID
                                    )
                                }
                            }

                            y.open('POST', 'cmu_getposition');
                            y.send(fd);
                        } else if (dt === 0) {
                            jAlert("Không tìm thấy tài khoản trên HIS", 'Thông báo');
                        } else if (dt === -1) {
                            jAlert("Không tìm thấy tài khoản SmartCA", 'Thông báo');
                        } else {
                            jAlert("Đăng nhập thất bại", 'Thông báo');
                        }
                    });
                }).fail(function(){
                jAlert("Lấy dữ liệu thất bại")
            }).always(function(){
            })
        }

    };
    x.responseType = 'blob';    // <-- This is necessary!
    x.open('GET', url, true);
    x.send();
}

function inketquadientim(userId, pDvtt) {
    var Sess_UserID = userId
    var sovaovien_t = sovaovien == 0? sovaovien_noi: sovaovien;
    dvtt = pDvtt;
    var mabn = $("#mabenhnhan").val();
    $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien_t, 0,'PHIEUDIENTIM', $("#sophieu").val(),'CMU_SMARTCA_INCLS_NGOAI']))
        .done(function(data){

            if (data.length > 0 && data[0].TRANID_KHOA != null) {
                $.ajax({
                    type: "POST",
                    url: "cmu-smartca-get-information",
                    dataType: "json",
                    contentType: 'application/json',
                    data: JSON.stringify({
                        tranId: data[0].TRANID_KHOA,
                        userId: userId,
                    }),
                    success: function (resfile) {
                        console.log("data", resfile)
                        var pdf = 'data:application/pdf;base64,' + resfile.DATA.content.documents[0].dataSigned;
                        var link = document.createElement('a');
                        link.href = pdf;
                        var t = new Date();

                        link.download=   resfile.DATA.content.tranCode+ ".pdf";
                        link.click();
                    },
                    error: function () {
                    }
                });
                clearInterval(i);
            } else {
                jAlert("Phiếu chưa được ký")
            }
        })
}

function doSignSASmartca(url, userId, pDvtt) {
    var x = new XMLHttpRequest();
    var mabn = $("#mabenhnhan").val();
    var Sess_UserID = userId
    var sovaovien_t = sovaovien == 0? sovaovien_noi: sovaovien;
    dvtt = pDvtt
    x.onload = function() {
        // Create a form
        var reader = new FileReader();
        reader.readAsDataURL(x.response);
        reader.onloadend = function() {

            var base64data = reader.result;
            var refTranId = uuidv4();
            $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien_t, sovaovien_dt_noi, userId, $("#sophieu").val(), userId,'CMU_SMARTCA_SA']))
                .done(function(data){
                    var dataObject = data[0]
                    if(data[0].DAKY > 0) {
                        jAlert("Phiếu Siêu âm đã được gửi.")
                        return false;
                    }
                    var str = [dataObject.USERNAME, dataObject.PASS, "0"];
                    var url = "smartca-kiem-tra-dang-nhap?url=" + convertArray(str);
                    $.ajax({
                        url: url, type: "POST"
                    }).done(function (dt) {
                        if (dt > 0) {
                            $.post('cmu_post', {
                                url: [
                                    dvtt,
                                    mabn,
                                    sovaovien_t,
                                    sovaovien_dt_noi,
                                    $("#sophieu").val(),
                                    'PHIEUSA',
                                    Sess_UserID,
                                    0,
                                    refTranId,
                                    refTranId+'-bgd',
                                    0,
                                    userId,
                                    'SA_'+removeAccents($("#hoten_ct").val()),
                                    'CMU_INS_SMARTCA_GRV'
                                ].join('```')
                            })
                            var t = new Date();

                            var fd = new FormData();


                            fd.append("upfile", base64data);
                            fd.append("mabenhnhan", mabn);
                            fd.append("tenfile", t.getTime());
                            fd.append("keyword", "Bác sĩ siêu âm");

                            // Upload to your server
                            var y = new XMLHttpRequest();
                            y.onreadystatechange = function() {
                                if (y.readyState == XMLHttpRequest.DONE) {
                                    console.log("res", y.responseText);
                                    var signatures = [];
                                    y.responseText.split(";").forEach(function(value, id) {
                                        if (id < y.responseText.split(";").length - 1) {
                                            var pos = value.split(",")
                                            var x = 140 + Number(pos[0]);
                                            var y2 = -100 + Number(pos[1]);
                                            var x1 = -40 + Number(pos[0]);
                                            var y1 = -15 + Number(pos[1]);
                                            var rect = x+","+y2+","+x1+","+y1
                                            signatures.push({"rectangle":rect,"page": Number(pos[2])})
                                        }

                                    })
                                    kysosmartca(base64data.replace("data:application/pdf;base64,", ""),
                                        {"signatureText": "Ngày ký:"+data[0].NGAYGIO+"\nTổ chức xác thực: VNPT SMARTCA RS",
                                            "fontName":"Roboto",
                                            "fontSize":7,
                                            "fontColor":"000000",
                                            "fontStyle":0,
                                            "imageSrc": data[0].CHUKY,
                                            "visibleType":4,"comment":[],
                                            "signatures":signatures},
                                        'SA_'+removeAccents($("#hoten_ct").val()),
                                        refTranId,
                                        Sess_UserID
                                    )
                                }
                            }

                            y.open('POST', 'cmu_getposition');
                            y.send(fd);
                        } else if (dt === 0) {
                            jAlert("Không tìm thấy tài khoản trên HIS", 'Thông báo');
                        } else if (dt === -1) {
                            jAlert("Không tìm thấy tài khoản SmartCA", 'Thông báo');
                        } else {
                            jAlert("Đăng nhập thất bại", 'Thông báo');
                        }
                    });
                }).fail(function(){
                jAlert("Lấy dữ liệu thất bại")
            }).always(function(){
            })
        }

    };
    x.responseType = 'blob';    // <-- This is necessary!
    x.open('GET', url, true);
    x.send();
}

function kyphieunhapvien(userId, dvtt) {
    var makhambenh = $("#makhambenh_pnv").val();
    if ($("#mabenhly_pnv").val() == "" || $("#icd_pnv").val() == "" || $("#cb_icd_pnv").val() == "") {
        jAlert("Chưa nhập chẩn đoán.", "Thông báo");
    } else {
        if(typeof cmusmartca960567 == 'undefined') {
            cmusmartca960567 = $.ajax({
                url: "cmu_post_cmu_tsdv", type: "POST", data: {  url: [dvtt,  960567, 0].join('```')}, async: false
            }).responseText;
        }
        if(cmusmartca960567 == 1) {
            var sovaovien_t = sovaovien;
            var mabn = $("#mayte").val();
            if(sovaovien == 0) {
                var rowId =$("#list_benhnhan").jqGrid('getGridParam','selrow');
                var rowData = $("#list_benhnhan").getRowData(rowId);
                mabn = mabn? rowData.MABENHNHAN: $("#mabenhnhan").val();
                sovaovien_t = $.ajax({type: "POST", url: "cmu_post", async: false,
                    data: {url: [dvtt, rowData.MAKHAMBENHNGOAITRU_NHAPVIEN,'CMU_GETSVV_NGOAI'].join('```')}
                }).responseText;
            }
            doSignSmartca769({
                url: 'inphieunhapvien?makhambenh=' + makhambenh + "&dvtt=" + dvtt,
                kyHieuPhieu: "PHIEUKHAMBENH_VAOVIEN",
                maBenhNhan: $("#mayte").val(),
                maDichVu: -1,
                nghiepVu: "",
                noiTru: 1,
                soBe: -1,
                soBenhAn: "",
                soPhieuDichVu: sovaovien_t,
                soVaoVien: sovaovien_t,
                soVaoVienDT: 0,
                sttDotDieuTri: "",
                toaThuoc: "",
                keyword: "Bác sĩ điều trị",
                userId: userId,
                userName: "",
                fileName: sovaovien_t,
                dvtt: dvtt
            })
            return false;
        }
        doSignPNVSmartca( 'inphieunhapvien?makhambenh=' + makhambenh + "&dvtt=" + dvtt, userId, dvtt);
    }
}

function doSignPNVSmartca(url, userId, pDvtt) {
    var x = new XMLHttpRequest();

    var Sess_UserID = userId
    var sovaovien_t = sovaovien;
    var mabn = $("#mabenhnhan").val();
    dvtt = pDvtt
    if(sovaovien == 0) {
        var rowId =$("#list_benhnhan").jqGrid('getGridParam','selrow');
        var rowData = $("#list_benhnhan").getRowData(rowId);
        mabn = mabn == undefined? rowData.MABENHNHAN: $("#mabenhnhan").val();
        sovaovien_t = $.ajax({type: "POST", url: "cmu_post", async: false,
            data: {url: [dvtt, rowData.MAKHAMBENHNGOAITRU_NHAPVIEN,'CMU_GETSVV_NGOAI'].join('```')}
        }).responseText;
    }


    x.onload = function() {
        // Create a form
        var reader = new FileReader();
        reader.readAsDataURL(x.response);
        reader.onloadend = function() {

            var base64data = reader.result;
            var refTranId = uuidv4();
            $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien_t, 0, userId, 'PHIEUNV', userId,'CMU_SMARTCA_PHIEUVAOVIEN']))
                .done(function(data){
                    var dataObject = data[0]
                    if(data[0].DAKY > 0) {
                        jAlert("Phiếu nhập viện đã được gửi.")
                        return false;
                    }
                    var str = [dataObject.USERNAME, dataObject.PASS, "0"];
                    var url = "smartca-kiem-tra-dang-nhap?url=" + convertArray(str);
                    $.ajax({
                        url: url, type: "POST"
                    }).done(function (dt) {
                        if (dt > 0) {
                            $.post('cmu_post', {
                                url: [
                                    dvtt,
                                    mabn,
                                    sovaovien_t,
                                    0,
                                    'PHIEUNV',
                                    'PHIEUNV',
                                    Sess_UserID,
                                    0,
                                    refTranId,
                                    refTranId+'-bgd',
                                    0,
                                    userId,
                                    'PNV_'+removeAccents($("#hoten_pnv").val()),
                                    'CMU_INS_SMARTCA_GRV'
                                ].join('```')
                            })
                            var t = new Date();

                            var fd = new FormData();


                            fd.append("upfile", base64data);
                            fd.append("mabenhnhan", mabn);
                            fd.append("tenfile", t.getTime());
                            fd.append("keyword", "Bác sĩ điều trị");

                            // Upload to your server
                            var y = new XMLHttpRequest();
                            y.onreadystatechange = function() {
                                if (y.readyState == XMLHttpRequest.DONE) {
                                    console.log("res", y.responseText);
                                    var signatures = [];
                                    y.responseText.split(";").forEach(function(value, id) {
                                        if (id < y.responseText.split(";").length - 1) {
                                            var pos = value.split(",")
                                            var x = 140 + Number(pos[0]);
                                            var y2 = -100 + Number(pos[1]);
                                            var x1 = -40 + Number(pos[0]);
                                            var y1 = -15 + Number(pos[1]);
                                            var rect = x+","+y2+","+x1+","+y1
                                            signatures.push({"rectangle":rect,"page": Number(pos[2])})
                                        }

                                    })
                                    kysosmartca(base64data.replace("data:application/pdf;base64,", ""),
                                        {"signatureText": "Ngày ký:"+data[0].NGAYGIO+"\nTổ chức xác thực: VNPT SMARTCA RS",
                                            "fontName":"Roboto",
                                            "fontSize":7,
                                            "fontColor":"000000",
                                            "fontStyle":0,
                                            "imageSrc": data[0].CHUKY,
                                            "visibleType":4,"comment":[],
                                            "signatures":signatures},
                                        'PNV_'+removeAccents($("#hoten_pnv").val()),
                                        refTranId,
                                        Sess_UserID
                                    )
                                }
                            }

                            y.open('POST', 'cmu_getposition');
                            y.send(fd);
                        } else if (dt === 0) {
                            jAlert("Không tìm thấy tài khoản trên HIS", 'Thông báo');
                        } else if (dt === -1) {
                            jAlert("Không tìm thấy tài khoản SmartCA", 'Thông báo');
                        } else {
                            jAlert("Đăng nhập thất bại", 'Thông báo');
                        }
                    });
                }).fail(function(){
                jAlert("Lấy dữ liệu thất bại")
            }).always(function(){
            })
        }

    };
    x.responseType = 'blob';    // <-- This is necessary!
    x.open('GET', url, true);
    x.send();
}

function insignpnvsmartca(userId, dvtt) {
    var sovaovien_t = sovaovien;
    var mabn = $("#mabenhnhan").val();
    if(sovaovien == 0) {
        var rowId =$("#list_benhnhan").jqGrid('getGridParam','selrow');
        var rowData = $("#list_benhnhan").getRowData(rowId);
        mabn = mabn == undefined? rowData.MABENHNHAN: $("#mabenhnhan").val();
        sovaovien_t = $.ajax({type: "POST", url: "cmu_post", async: false,
            data: {url: [dvtt, rowData.MAKHAMBENHNGOAITRU_NHAPVIEN,'CMU_GETSVV_NGOAI'].join('```')}
        }).responseText;
    }
    if(typeof cmusmartca960567 == 'undefined') {
        if(typeof cmusmartca960567 == 'undefined') {
            cmusmartca960567 = $.ajax({
                url: "cmu_post_cmu_tsdv", type: "POST", data: {  url: [dvtt,  960567, 0].join('```')}, async: false
            }).responseText;
        }
    }
    if(cmusmartca960567 == 1) {
        $.get("cmu_getlist?url="+convertArray([
            dvtt,
            sovaovien_t,
            0,
            "PHIEUKHAMBENH_VAOVIEN",
            sovaovien_t,
            userId,
            -1,
            'CMU_SMART769_GET'])).done(function(data) {
            if(data.length == 0) {
                return jAlert("Không tìm thấy file ký số")
            }
            downloadkyso769(data[0].KEYSIGN, 'PDF')
        })
        return false;
    }
    $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien_t, userId, 'PHIEUNV','CMU_SMARTCA_INSIGNFILE']))
        .done(function(data){

            if (data.length > 0 && data[0].TRANID_KHOA != null) {
                $.ajax({
                    type: "POST",
                    url: "cmu-smartca-get-information",
                    dataType: "json",
                    contentType: 'application/json',
                    data: JSON.stringify({
                        tranId: data[0].TRANID_KHOA,
                        userId: userId,
                    }),
                    success: function (resfile) {
                        console.log("data", resfile)
                        var pdf = 'data:application/pdf;base64,' + resfile.DATA.content.documents[0].dataSigned;
                        var link = document.createElement('a');
                        link.href = pdf;
                        var t = new Date();

                        link.download=   resfile.DATA.content.tranCode+ ".pdf";
                        link.click();
                    },
                    error: function () {
                    }
                });
                clearInterval(i);
            } else {
                jAlert("Phiếu chưa được ký")
            }
        })
}

function huykyphieunhapvien(userId, dvtt) {
    if(typeof cmusmartca960567 == 'undefined') {
        cmusmartca960567 = $.ajax({
            url: "cmu_post_cmu_tsdv", type: "POST", data: {  url: [dvtt,  960567, 0].join('```')}, async: false
        }).responseText;
    }
    if(cmusmartca960567 == 1) {
        var sovaovien_t = sovaovien;
        var mabn = $("#mabenhnhan").val();
        if(sovaovien == 0) {
            var rowId =$("#list_benhnhan").jqGrid('getGridParam','selrow');
            var rowData = $("#list_benhnhan").getRowData(rowId);
            mabn = mabn == undefined? rowData.MABENHNHAN: $("#mabenhnhan").val();
            sovaovien_t = $.ajax({type: "POST", url: "cmu_post", async: false,
                data: {url: [dvtt, rowData.MAKHAMBENHNGOAITRU_NHAPVIEN,'CMU_GETSVV_NGOAI'].join('```')}
            }).responseText;
        }
        huykysoclsngoaitru("PHIEUKHAMBENH_VAOVIEN", sovaovien_t, userId, dvtt, sovaovien_t);
        return false;
    }
    $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien, 'PHIEUNV','CMU_HUYKYSO_PHIEUNHAPVIEN']))
        .done(function(data){
            jAlert("Hủy thành công")
        })
}
function kyphieuxnngoaitru(userId, dvtt, loaigiay) {
    var makhambenh = "kb_" + $("#idtiepnhan").val();
    var sophieu = $('#sophieuxn').val();
    var bhytkhongchi = $('#bhytkhongchi_xn').val();
    var inbhytchixn = $("#inbhytchixn").val();
    var arr = [makhambenh, bhytkhongchi, sophieu, dvtt, "0", "1"];

    doSignCLSNTSmartca( "inphieuxetnghiem?url=" + convertArray(arr), userId, dvtt, loaigiay, sophieu);
}

function kyphieucdhangoaitru(userId, dvtt, loaigiay) {
    var makhambenh = "kb_" + $("#idtiepnhan").val();
    var sophieu = $('#sophieucdha').val();
    var bhytkhongchi = $('#bhytkhongchi_cdha').val();
    var inbhytchicdha = $("#inbhytchicdha").val();
    var noitru = $('#noitrucdha').prop('checked') == false ? 0 : 1;
    var arr = [makhambenh, bhytkhongchi, sophieu, dvtt, "0", "1", noitru];
    var url = "inphieucdha?url=" + convertArray(arr);
    doSignCLSNTSmartca( url, userId, dvtt, loaigiay, sophieu);
}

function kyphieuttptngoaitru(userId, dvtt, loaigiay) {
    var makhambenh = "kb_" + $("#idtiepnhan").val();
    var id1 = $("#list_ttpt_phieu").jqGrid('getGridParam', 'selrow');
    var ret = $("#list_ttpt_phieu").jqGrid('getRowData', id1);
    var bhytkhongchi = ret.BHYT_CHI.toString() == "No" ? "1" : "0";
    var inbhytchittpt = $("#inbhytchittpt").val();
    var arr = [makhambenh, bhytkhongchi, ret.SO_PHIEU_DICHVU, ret.MA_LOAI_DICHVU, ret.CHUYEN_KHOA, ret.CHI_TIET_CHUYEN_KHOA, dvtt, "0", "2"];
    var url = "inphieuthuthuat?url=" + convertArray(arr);
    doSignCLSNTSmartca( url, userId, dvtt, loaigiay, ret.SO_PHIEU_DICHVU);
}

function insignclsngoaismartca(userId, dvtt, loaigiay) {
    if(cmusmartca960567 == 1) {
        var sophieu = $("#sophieucdha").val()
        if(loaigiay == 'PHIEUCDXN') {
            sophieu = $("#sophieuxn").val()
        }
        if(loaigiay == 'PHIEUCDTTPT') {
            sophieu = $("#sophieuttpt").val()
        }
        $.get("cmu_getlist?url="+convertArray([
            dvtt,
            sovaovien,
            0,
            loaigiay,
            sophieu,
            userId,
            -1,
            'CMU_SMART769_GET'])).done(function(data) {
            if(data.length == 0) {
                return jAlert("Không tìm thấy file ký số")
            }
            downloadkyso769(data[0].KEYSIGN, 'PDF')
        })
        return false;
    }
    var Sess_UserID = userId
    var sovaovien_t = sovaovien;
    var mabn = $("#mabenhnhan").val();
    $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien_t, 0, loaigiay,'CMU_SMARTCA_INSIGNFILE']))
        .done(function(data){

            if (data.length > 0 && data[0].TRANID_KHOA != null) {
                $.ajax({
                    type: "POST",
                    url: "cmu-smartca-get-information",
                    dataType: "json",
                    contentType: 'application/json',
                    data: JSON.stringify({
                        tranId: data[0].TRANID_KHOA,
                        userId: userId,
                    }),
                    success: function (resfile) {
                        console.log("data", resfile)
                        var pdf = 'data:application/pdf;base64,' + resfile.DATA.content.documents[0].dataSigned;
                        var link = document.createElement('a');
                        link.href = pdf;
                        var t = new Date();

                        link.download=   resfile.DATA.content.tranCode+ ".pdf";
                        link.click();
                    },
                    error: function () {
                    }
                });
                clearInterval(i);
            } else {
                jAlert("Phiếu chưa được ký")
            }
        })
}

function doSignCLSNTSmartca(url, userId, pDvtt, loaigiay, sophieu) {
    if(cmusmartca960567 == 1) {
        doSignSmartca769({
            url: url,
            kyHieuPhieu: loaigiay,
            maBenhNhan: $("#mayte").val(),
            maDichVu: -1,
            nghiepVu: "",
            noiTru: 1,
            soBe: -1,
            soBenhAn: "",
            soPhieuDichVu: sophieu,
            soVaoVien: sovaovien,
            soVaoVienDT: 0,
            sttDotDieuTri: "",
            toaThuoc: "",
            keyword: "Bác sĩ điều trị",
            userId: userId,
            userName: "",
            fileName: sophieu,
            dvtt: pDvtt
        })
        return false;
    }
    var x = new XMLHttpRequest();

    var Sess_UserID = userId
    var sovaovien_t = sovaovien;
    var mabn = $("#mabenhnhan").val();
    dvtt = pDvtt


    x.onload = function() {
        // Create a form
        var reader = new FileReader();
        reader.readAsDataURL(x.response);
        reader.onloadend = function() {

            var base64data = reader.result;
            var refTranId = uuidv4();
            $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien_t, 0, userId, sophieu, userId, loaigiay,'CMU_SMARTCA_CLSNGOAITRU']))
                .done(function(data){
                    var dataObject = data[0]
                    if(data[0].DAKY > 0) {
                        jAlert("Phiếu nhập viện đã được gửi.")
                        return false;
                    }
                    var str = [dataObject.USERNAME, dataObject.PASS, "0"];
                    var url = "smartca-kiem-tra-dang-nhap?url=" + convertArray(str);
                    $.ajax({
                        url: url, type: "POST"
                    }).done(function (dt) {
                        if (dt > 0) {
                            $.post('cmu_post', {
                                url: [
                                    dvtt,
                                    mabn,
                                    sovaovien_t,
                                    0,
                                    sophieu,
                                    loaigiay,
                                    Sess_UserID,
                                    0,
                                    refTranId,
                                    refTranId+'-bgd',
                                    0,
                                    userId,
                                    loaigiay+'_'+removeAccents($("#hoten_pnv").val()),
                                    'CMU_INS_SMARTCA_GRV'
                                ].join('```')
                            })
                            var t = new Date();

                            var fd = new FormData();


                            fd.append("upfile", base64data);
                            fd.append("mabenhnhan", mabn);
                            fd.append("tenfile", t.getTime());
                            fd.append("keyword", "Bác sĩ điều trị");

                            // Upload to your server
                            var y = new XMLHttpRequest();
                            y.onreadystatechange = function() {
                                if (y.readyState == XMLHttpRequest.DONE) {
                                    console.log("res", y.responseText);
                                    var signatures = [];
                                    y.responseText.split(";").forEach(function(value, id) {
                                        if (id < y.responseText.split(";").length - 1) {
                                            var pos = value.split(",")
                                            var x = 140 + Number(pos[0]);
                                            var y2 = -100 + Number(pos[1]);
                                            var x1 = -40 + Number(pos[0]);
                                            var y1 = -15 + Number(pos[1]);
                                            var rect = x+","+y2+","+x1+","+y1
                                            signatures.push({"rectangle":rect,"page": Number(pos[2])})
                                        }

                                    })
                                    kysosmartca(base64data.replace("data:application/pdf;base64,", ""),
                                        {"signatureText": "Ngày ký:"+data[0].NGAYGIO+"\nTổ chức xác thực: VNPT SMARTCA RS",
                                            "fontName":"Roboto",
                                            "fontSize":7,
                                            "fontColor":"000000",
                                            "fontStyle":0,
                                            "imageSrc": data[0].CHUKY,
                                            "visibleType":4,"comment":[],
                                            "signatures":signatures},
                                        loaigiay+'_'+removeAccents($("#hoten").val()),
                                        refTranId,
                                        Sess_UserID
                                    )
                                }
                            }

                            y.open('POST', 'cmu_getposition');
                            y.send(fd);
                        } else if (dt === 0) {
                            jAlert("Không tìm thấy tài khoản trên HIS", 'Thông báo');
                        } else if (dt === -1) {
                            jAlert("Không tìm thấy tài khoản SmartCA", 'Thông báo');
                        } else {
                            jAlert("Đăng nhập thất bại", 'Thông báo');
                        }
                    });
                }).fail(function(){
                jAlert("Lấy dữ liệu thất bại")
            }).always(function(){
            })
        }

    };
    x.responseType = 'blob';    // <-- This is necessary!
    x.open('GET', url, true);
    x.send();
}


function cmuhuykysoxn(dvtt, userId, kyhieuphieu) {
    if(cmusmartca960567 == 1) {
        huykysoclsngoaitru(kyhieuphieu, $('#sophieuxn').val(), userId, dvtt, sovaovien, 0, -1, function () {
            CDSBXNGrid();
        });
        return false;
    }
    $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien, $('#sophieuxn').val(),'CMU_HUYKYSO_XETNGHIEM']))
        .done(function(data){
            jAlert("Hủy thành công")
        })
}

function cmuhuykysocdha(dvtt, userId, kyhieuphieu) {
    if(cmusmartca960567 == 1) {
        huykysoclsngoaitru(kyhieuphieu, $('#sophieucdha').val(), userId, dvtt, sovaovien, 0, -1, function () {
            CDSBCDHAGrid();
        });
        return false;
    }
    $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien, $('#sophieucdha').val(),'CMU_HUYKYSO_XETNGHIEM']))
        .done(function(data){
            jAlert("Hủy thành công")
        })
}

function cmuhuykysottpt(dvtt, userId, kyhieuphieu) {
    if(cmusmartca960567 == 1) {
        huykysoclsngoaitru(kyhieuphieu, $('#sophieuttpt').val(), userId, dvtt, sovaovien, 0, -1, function () {
            CDSBTTPTGrid();
        });
        return false;
    }
    $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien, $('#sophieuttpt').val(),'CMU_HUYKYSO_XETNGHIEM']))
        .done(function(data){
            jAlert("Hủy thành công")
        })
}

function cmuinkisotokenxn(dvtt) {
    $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien, $("#sophieuxn").val(),'CMU_DOWNLOAD_CLS_TOKEN']))
        .done(function(data){
            if (data.length > 0 ) {
                var pdf = 'data:application/pdf;base64,' + data[0].FILE_SIGNED;
                var link = document.createElement('a');
                link.href = pdf;
                var t = new Date();

                link.download=   sovaovien+".pdf";
                link.click();
            } else {
                jAlert("Phiếu chỉ định chưa được kí số")
            }
        })
}

function cmuinkisotokencdha(dvtt) {
    $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien, $('#sophieucdha').val(),'CMU_DOWNLOAD_CLS_TOKEN']))
        .done(function(data){
            if (data.length > 0 ) {
                var pdf = 'data:application/pdf;base64,' + data[0].FILE_SIGNED;
                var link = document.createElement('a');
                link.href = pdf;
                var t = new Date();

                link.download=   sovaovien+".pdf";
                link.click();
            } else {
                jAlert("Phiếu chỉ định chưa được kí số")
            }
        })
}

function cmuinkisotokenttpt(dvtt) {
    $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien, $("#sophieuttpt").val(),'CMU_DOWNLOAD_CLS_TOKEN']))
        .done(function(data){
            if (data.length > 0 ) {
                var pdf = 'data:application/pdf;base64,' + data[0].FILE_SIGNED;
                var link = document.createElement('a');
                link.href = pdf;
                var t = new Date();

                link.download=   sovaovien+".pdf";
                link.click();
            } else {
                jAlert("Phiếu chỉ định chưa được kí số")
            }
        })
}

function cmuinkisotokenxnkq(dvtt) {
    $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien, $('#sophieuxn').val()+"_KQ",'CMU_DOWNLOAD_CLS_TOKEN']))
        .done(function(data){
            if (data.length > 0 ) {
                var pdf = 'data:application/pdf;base64,' + data[0].FILE_SIGNED;
                var link = document.createElement('a');
                link.href = pdf;
                var t = new Date();

                link.download=   sovaovien+".pdf";
                link.click();
            } else {
                jAlert("Phiếu chỉ định chưa được kí số")
            }
        })
}

function cmuinkisotokensieuamkq(dvtt) {
    $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien, CMU_SOPHIEU_CDHA+"_"+$("#macdha").val()+"_KQ",'CMU_DOWNLOAD_CLS_TOKEN']))
        .done(function(data){
            if (data.length > 0 ) {
                var pdf = 'data:application/pdf;base64,' + data[0].FILE_SIGNED;
                var link = document.createElement('a');
                link.href = pdf;
                var t = new Date();

                link.download=   sovaovien+".pdf";
                link.click();
            } else {
                jAlert("Phiếu chỉ định chưa được kí số")
            }
        })
}
