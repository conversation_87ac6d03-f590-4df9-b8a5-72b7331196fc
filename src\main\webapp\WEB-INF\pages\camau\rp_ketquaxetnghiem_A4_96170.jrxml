<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ketquaxetnghiem_a4" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="bcca7e92-1948-4661-84f0-b00c4859947a">
	<property name="ireport.zoom" value="3.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="style1">
		<conditionalStyle>
			<conditionExpression><![CDATA[$F{BINHTHUONG}.equals("1")]]></conditionExpression>
			<style isBold="false" isItalic="false" isUnderline="false"/>
		</conditionalStyle>
		<conditionalStyle>
			<conditionExpression><![CDATA[!$F{BINHTHUONG}.equals("1")]]></conditionExpression>
			<style isBold="true" isItalic="false" isUnderline="false"/>
		</conditionalStyle>
	</style>
	<parameter name="madonvi" class="java.lang.String"/>
	<parameter name="makhambenh" class="java.lang.String"/>
	<parameter name="tendonvi" class="java.lang.String"/>
	<parameter name="tenkhoa" class="java.lang.String"/>
	<parameter name="diachi" class="java.lang.String"/>
	<parameter name="sodienthoai" class="java.lang.String"/>
	<parameter name="hoten" class="java.lang.String"/>
	<parameter name="namsinh" class="java.lang.String"/>
	<parameter name="gioitinh" class="java.lang.String"/>
	<parameter name="chandoan" class="java.lang.String"/>
	<parameter name="bschidinh" class="java.lang.String"/>
	<parameter name="ngay" class="java.lang.String"/>
	<parameter name="bsdieutri" class="java.lang.String"/>
	<parameter name="sophieuthanhtoan" class="java.lang.String"/>
	<parameter name="noitru" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_benhan" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_dotdieutri" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_dieutri" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="sophieuxn" class="java.lang.String"/>
	<parameter name="soytetg" class="java.lang.String"/>
	<parameter name="sothebaohiem" class="java.lang.String"/>
	<parameter name="khoachidinh" class="java.lang.String"/>
	<parameter name="mabenhnhan" class="java.lang.String"/>
	<parameter name="thuong" class="java.lang.String"/>
	<parameter name="capcuu" class="java.lang.String"/>
	<parameter name="tuoi" class="java.lang.String"/>
	<parameter name="diachi_bn" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call NGOAITRU_KQXN_SELECT_HUNGYEN($P{madonvi},$P{makhambenh}, $P{noitru}, $P{stt_benhan}, $P{stt_dotdieutri}, $P{stt_dieutri},$P{sophieuxn},$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="ten_loai_xetnghiem" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ten_xetnghiem" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="trisobinhthuong" class="java.lang.String"/>
	<field name="dvnghiepvu_xetnghiem" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="KET_QUA" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="KET_LUAN_TONG" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="NGUOI_LAY_MAU" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ngaylaymau" class="java.lang.String"/>
	<field name="NGAYTRAKETQUA" class="java.lang.String"/>
	<field name="NGAY_CHI_DINH" class="java.lang.String"/>
	<field name="BINHTHUONG" class="java.lang.String"/>
	<field name="NGUOI_THUC_HIEN" class="java.lang.String"/>
	<field name="ngay_lay_mau" class="java.lang.String"/>
	<field name="ngaylaynhanmau" class="java.lang.String"/>
	<field name="nguoilaymau_cmu" class="java.lang.String"/>
	<field name="nguoigiaomau_cmu" class="java.lang.String"/>
	<field name="ket_qua" class="java.lang.String"/>
	<field name="MABENHNHAN" class="java.lang.String"/>
	<field name="MA_XETNGHIEM" class="java.lang.String"/>
	<group name="g_loai">
		<groupExpression><![CDATA[$F{ten_loai_xetnghiem}]]></groupExpression>
		<groupHeader>
			<band height="18">
				<textField>
					<reportElement mode="Transparent" x="0" y="0" width="555" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="62448f4f-1141-4cdd-be60-e20684086d12"/>
					<box leftPadding="0">
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
						<font fontName="Times New Roman" size="13" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ten_loai_xetnghiem}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="251" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement x="0" y="20" width="228" height="20" uuid="2d27a4ca-075a-44cd-8ee4-7ef53e3de9e4"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tendonvi}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="52" width="554" height="24" uuid="d53ed38e-f0d3-415e-8ad4-4e88cfcfbc92"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="16" isBold="true"/>
				</textElement>
				<text><![CDATA[KẾT QUẢ XÉT NGHIỆM]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" mode="Transparent" x="0" y="223" width="201" height="28" forecolor="#000000" backcolor="#FFFFFF" uuid="d5353d67-b00f-4c37-8b04-c3ef85ae7379"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["XÉT NGHIỆM"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" mode="Transparent" x="201" y="223" width="137" height="28" forecolor="#000000" backcolor="#FFFFFF" uuid="31257b3c-3e3e-4935-b5d5-6ac8153a658f"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["KẾT QUẢ"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" mode="Transparent" x="338" y="223" width="130" height="28" forecolor="#000000" backcolor="#FFFFFF" uuid="20afff1c-6b52-4299-a49d-935ea9bba279"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["KHOẢNG THAM CHIẾU"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" mode="Transparent" x="468" y="223" width="87" height="28" forecolor="#000000" backcolor="#FFFFFF" uuid="507f48aa-a157-4de2-883c-016d2005f4b2"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["ĐƠN VỊ"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="228" height="20" uuid="4ee4f198-5763-4e90-a3ef-93a9d3d1282f"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{soytetg}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="269" y="189" width="103" height="17" forecolor="#000000" uuid="c54c88a9-5aeb-46cd-95bb-a7ed0ee0cc23"/>
				<box leftPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày giờ lấy mẫu:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="372" y="189" width="183" height="17" forecolor="#000000" uuid="d0afdc8b-58ae-48ee-b92b-192608e85bf7">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ngaylaynhanmau}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="0" y="206" width="105" height="17" forecolor="#000000" uuid="6806f216-738a-47f2-a611-e8554e525f72"/>
				<box leftPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Người nhận mẫu:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="372" y="206" width="183" height="17" forecolor="#000000" uuid="c0fa4fc4-4fbf-497c-b9b8-24010666dea5">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nguoigiaomau_cmu}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="105" y="189" width="164" height="17" forecolor="#000000" uuid="69d61690-778a-4efb-8fb5-b303b84a6f34">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ngay_lay_mau}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="0" y="189" width="105" height="17" forecolor="#000000" uuid="6b9675cd-b6f1-4fc3-9390-89eed2fdbe37"/>
				<box leftPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Ngày giờ nhận mẫu:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="105" y="206" width="164" height="17" forecolor="#000000" uuid="3e50b60c-5367-4feb-978f-9f1c18d7f4a2">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nguoilaymau_cmu}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="269" y="206" width="103" height="17" forecolor="#000000" uuid="fd08c9e7-b02e-44ad-ae17-9e381c720e48"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Người giao mẫu:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="0" y="172" width="105" height="17" forecolor="#000000" uuid="33bd3cdf-19e5-413e-a907-bd047103918a"/>
				<box leftPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Bác sĩ điều trị:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="105" y="172" width="450" height="17" forecolor="#000000" uuid="1a0b3e41-652a-4f41-ac6b-d798f39bed6f">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{bschidinh}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="46" y="130" width="264" height="21" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="e1e5aadb-50d3-4116-aa7b-a189e95bd036"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{khoachidinh}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="130" width="46" height="21" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="7516c6cb-b68d-44b3-8cd6-e12ee88c7b35"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Khoa:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="310" y="130" width="60" height="21" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="b295fd0f-536a-4683-b220-85cffce40943"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Số thẻ BHYT:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="46" y="151" width="509" height="21" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="973dd447-e5ff-4d1b-a40a-a35348ebfbdc"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{diachi_bn}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="151" width="46" height="21" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="b4343d15-1f06-46f6-b4d8-78989bb54bd5"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Địa chỉ:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="91" y="109" width="262" height="21" forecolor="#000000" uuid="fd7cc1a1-9d3c-4567-810f-9c732a5d226d"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{hoten}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="495" y="109" width="60" height="21" forecolor="#000000" uuid="5d75114f-8e1b-4ead-a29b-5146fd497dd6"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{gioitinh}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="448" y="109" width="47" height="21" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="50185238-8a61-46fe-a31f-1b6a6942dda3"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Giới tính:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="109" width="91" height="21" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="87b05375-2a50-445e-a570-b4215904cb0d"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Họ tên người bệnh:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="404" y="109" width="44" height="21" forecolor="#000000" uuid="fede6879-639f-40ff-b711-fedd78b56c46"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tuoi}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="353" y="109" width="51" height="21" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="31fd7a17-c63b-449d-8972-11cc953df0d2"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Tuổi:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="514" y="130" width="41" height="21" forecolor="#000000" uuid="c0e0f307-db57-481f-8329-9069fa5376b7"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("") ? $P{sothebaohiem}.substring( 10, 15 ) : $P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="397" y="130" width="25" height="21" forecolor="#000000" uuid="aaaf23ae-fcf4-4317-a608-************"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("") ? $P{sothebaohiem}.substring( 02, 03 ) : $P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="478" y="130" width="34" height="21" forecolor="#000000" uuid="41ab23d6-4887-45d4-93af-50ec0aa85919"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("") ? $P{sothebaohiem}.substring( 07, 10 ) : $P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="451" y="130" width="25" height="21" forecolor="#000000" uuid="44022ac7-d37b-4576-a9cb-27aa30aaa62a"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("") ? $P{sothebaohiem}.substring( 05, 07) : $P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="424" y="130" width="25" height="21" forecolor="#000000" uuid="b654cfe6-4a6b-49d4-b512-f4f4c1aad896"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("") ? $P{sothebaohiem}.substring( 03, 05 ) : $P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="370" y="130" width="25" height="21" forecolor="#000000" uuid="6c571251-e8fc-445a-af66-a9322dbc4405"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("") ? $P{sothebaohiem}.substring( 00, 02 ) : $P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="229" y="82" width="29" height="20" forecolor="#000000" uuid="512f98c6-d17a-462a-aed7-623ba50723fa"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{capcuu}.equals("1")? "": "X"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="321" y="82" width="29" height="20" forecolor="#000000" uuid="8603fb70-976a-4175-8ef3-17f5b5d99a2c"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{capcuu}.equals("1")? "X": ""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" mode="Transparent" x="275" y="82" width="46" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF" uuid="c0f59aed-d75b-4621-805b-46c34a36160e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Cấp cứu"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="182" y="82" width="46" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF" uuid="38083743-70c9-44f5-828f-b3f1c2b00c60"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Thường"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="471" y="35" width="85" height="17" forecolor="#000000" uuid="52b3be30-848f-4a23-9dc9-fddfe8a4461d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{mabenhnhan}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="471" y="0" width="85" height="15" forecolor="#000000" uuid="97a95b0f-1b79-412f-9195-59ac76b3856c"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Mã bệnh nhân]]></text>
			</staticText>
			<componentElement>
				<reportElement x="476" y="15" width="78" height="20" uuid="33bd8df7-1eb8-454e-be0f-ea5c97f61f87"/>
				<jr:barbecue xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" type="2of7" drawText="false" checksumRequired="false">
					<jr:codeExpression><![CDATA[$P{mabenhnhan}]]></jr:codeExpression>
				</jr:barbecue>
			</componentElement>
		</band>
	</title>
	<detail>
		<band height="20" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="0" y="0" width="201" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="79a8af00-b190-4665-8068-7cfea9819ea7"/>
				<box leftPadding="10">
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="html">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ten_xetnghiem}.indexOf("K") >= 0 && ($F{MA_XETNGHIEM}.equals("95") || $F{MA_XETNGHIEM}.equals("139")) ? "K<sup>+</sup>"
: $F{ten_xetnghiem}.indexOf("Na") >= 0 && $F{ten_xetnghiem}.indexOf("Máu") == -1 ? "Na<sup>+</sup>" 
: $F{ten_xetnghiem}.indexOf("Cl") >= 0 && $F{ten_xetnghiem}.indexOf("Máu") == -1 ? "Cl<sup>-</sup>"     
: $F{MA_XETNGHIEM}.equals("141") ? "Ca<sup>2+</sup>"
: $F{ten_xetnghiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement style="style1" stretchType="RelativeToTallestObject" mode="Transparent" x="201" y="0" width="137" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="645e44dd-2da8-4aa3-b546-ca0b6f567815"/>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BINHTHUONG}.equals("-1") || $F{BINHTHUONG}.equals("0")? "<style size='11' isBold='true' pdfFontName='Times New Roman'>"+$F{ket_qua}+"</style>":$F{ket_qua}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="338" y="0" width="130" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="62bed760-0aa6-436c-974f-0eedd20a642a"/>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{trisobinhthuong}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="468" y="0" width="87" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="bd5b9f18-6d08-41d7-b905-a058ae759c98"/>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dvnghiepvu_xetnghiem}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band height="103">
			<textField>
				<reportElement x="323" y="0" width="233" height="15" forecolor="#000000" uuid="09a2bb4c-2e7d-49a9-9038-396aa28fb4dd"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isItalic="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{madonvi}.substring(0,2).equals("14")?"......giờ......ngày......tháng......năm......":
$F{NGAYTRAKETQUA}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="323" y="83" width="233" height="20" forecolor="#000000" uuid="a5853e9e-3c4a-4e38-b0a5-fd17a5a56844"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGUOI_THUC_HIEN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="324" y="15" width="231" height="20" uuid="80d40e9b-e0e4-4e6f-a85e-d3db0d4d70eb"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<text><![CDATA[KÝ DUYỆT KẾT QUẢ]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
