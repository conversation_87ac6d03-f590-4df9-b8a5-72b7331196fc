CREATE OR REPLACE FUNCTION "CMU_UPDATE_KETQUA_XML3" (
    p_dvtt        VARCHAR2,
    p_thoigian    VARCHAR2,
    p_sovaovien   VARCHAR2,
    p_sophieu     VARCHAR2,
    p_madv        VARCHAR2,
    p_hinhthuc    VARCHAR2,
    p_loaidv      VARCHAR2,
    p_user        VARCHAR2
) RETURN VARCHAR2 IS

    v_minngay       DATE;
    v_olduser       VARCHAR2(255);
    p_thaotac       VARCHAR2(255);
    cur             SYS_REFCURSOR;
    v_bant          NUMBER(18) := 0;
    v_mabenhnhan    NUMBER;
    v_ngaycu        VARCHAR2(255);
    v_tendichvu     VARCHAR2(1000);
    v_tenbnehnhan   VARCHAR2(255);
BEGIN
    p_thaotac := 'THAYDOINGAYKQ_' || p_loaidv;
    IF p_hinhthuc = 1 THEN
        IF p_loaidv = 'CDHA' THEN
SELECT
    ten_cdha
INTO v_tendichvu
FROM
    cls_cdha
WHERE
    dvtt = p_dvtt
  AND ma_cdha = p_madv;

SELECT
    TO_CHAR(ngay_thuc_hien, 'DD/MM/YYYY HH24:MI'),
    mabenhnhan
INTO
    v_ngaycu,
    v_mabenhnhan
FROM
    kb_cd_cdha_ct
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_madv;

UPDATE kb_cd_cdha_ct
SET
    ngay_thuc_hien = TO_DATE(p_thoigian || ':59', 'DD/MM/YYYY HH24:MI:SS')
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_madv;

ELSIF p_loaidv = 'TTPT' THEN
SELECT
    ten_dv
INTO v_tendichvu
FROM
    dm_dich_vu_kham
WHERE
    dvtt = p_dvtt
  AND ma_dv = p_madv;

SELECT
    TO_CHAR(ngay_gio_pttt, 'DD/MM/YYYY HH24:MI'),
    mabenhnhan
INTO
    v_ngaycu,
    v_mabenhnhan
FROM
    kb_cd_dichvu_ct
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_dichvu = p_sophieu
  AND ma_dv = p_madv;

UPDATE kb_cd_dichvu_ct
SET
    ngay_gio_pttt =
        CASE
            WHEN ngay_gio_pttt_kt IS NOT NULL THEN
                ngay_gio_pttt
            ELSE
                TO_DATE(p_thoigian || ':59', 'DD/MM/YYYY HH24:MI:SS')
            END,
    ngay_gio_pttt_kt =
        CASE
            WHEN ngay_gio_pttt_kt IS NOT NULL THEN
                TO_DATE(p_thoigian || ':59', 'DD/MM/YYYY HH24:MI:SS')
            ELSE
                NULL
            END
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND ma_dv = p_madv
  AND so_phieu_dichvu = p_sophieu;

ELSE
SELECT
    ten_xetnghiem
INTO v_tendichvu
FROM
    cls_xetnghiem
WHERE
    dvtt = p_dvtt
  AND ma_xetnghiem = p_madv;

SELECT
    TO_CHAR(ngay_thuc_hien, 'DD/MM/YYYY HH24:MI'),
    mabenhnhan
INTO
    v_ngaycu,
    v_mabenhnhan
FROM
    kb_cd_xet_nghiem_chi_tiet
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_xn = p_sophieu
  AND ma_xet_nghiem = p_madv;

UPDATE kb_cd_xet_nghiem_chi_tiet
SET
    ngay_thuc_hien = TO_DATE(p_thoigian || ':59', 'DD/MM/YYYY HH24:MI:SS')
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_xn = p_sophieu;
--and MA_XET_NGHIEM = p_madv;

UPDATE kb_cd_xet_nghiem_chi_tiet
SET
    ngay_thuc_hien = TO_DATE(p_thoigian || ':59', 'DD/MM/YYYY HH24:MI:SS')
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_xn = p_sophieu
  AND id_chisocha = p_madv;

UPDATE kb_cd_xet_nghiem
SET
    ngay_tra_ketqua = TO_DATE(p_thoigian || ':59', 'DD/MM/YYYY HH24:MI:SS'),
    ngay_lay_mau = TO_DATE(p_thoigian || ':59', 'DD/MM/YYYY HH24:MI:SS')
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_xn = p_sophieu;

END IF;

ELSE
        IF p_loaidv = 'CDHA' THEN
SELECT
    ten_cdha
INTO v_tendichvu
FROM
    cls_cdha
WHERE
    dvtt = p_dvtt
  AND ma_cdha = p_madv;

SELECT
    TO_CHAR(ngay_thuc_hien, 'DD/MM/YYYY HH24:MI'),
    mabenhnhan
INTO
    v_ngaycu,
    v_mabenhnhan
FROM
    noitru_cd_cdha_chi_tiet
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_madv;

UPDATE noitru_cd_cdha_chi_tiet
SET
    ngay_thuc_hien = TO_DATE(p_thoigian || ':59', 'DD/MM/YYYY HH24:MI:SS')
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_madv;

ELSIF p_loaidv = 'TTPT' THEN
SELECT
    ten_dv
INTO v_tendichvu
FROM
    dm_dich_vu_kham
WHERE
    dvtt = p_dvtt
  AND ma_dv = p_madv;

SELECT
    TO_CHAR(ngay_gio_pttt, 'DD/MM/YYYY HH24:MI'),
    mabenhnhan
INTO
    v_ngaycu,
    v_mabenhnhan
FROM
    noitru_cd_dichvu_ct
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_dichvu = p_sophieu
  AND ma_dv = p_madv;

UPDATE noitru_cd_dichvu_ct
SET
    ngay_gio_pttt =
        CASE
            WHEN ngay_gio_pttt_kt IS NOT NULL THEN
                ngay_gio_pttt
            ELSE
                TO_DATE(p_thoigian || ':59', 'DD/MM/YYYY HH24:MI:SS')
            END,
    ngay_gio_pttt_kt =
        CASE
            WHEN ngay_gio_pttt_kt IS NOT NULL THEN
                TO_DATE(p_thoigian || ':59', 'DD/MM/YYYY HH24:MI:SS')
            ELSE
                NULL
            END
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_dichvu = p_sophieu
  AND ma_dv = p_madv;

ELSE
SELECT
    ten_xetnghiem
INTO v_tendichvu
FROM
    cls_xetnghiem
WHERE
    dvtt = p_dvtt
  AND ma_xetnghiem = p_madv;

SELECT
    TO_CHAR(ngay_thuc_hien, 'DD/MM/YYYY HH24:MI'),
    mabenhnhan
INTO
    v_ngaycu,
    v_mabenhnhan
FROM
    noitru_cd_xet_nghiem_ct
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_xn = p_sophieu
  AND ma_xet_nghiem = p_madv;

UPDATE noitru_cd_xet_nghiem_ct
SET
    ngay_thuc_hien = TO_DATE(p_thoigian || ':59', 'DD/MM/YYYY HH24:MI:SS')
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_xn = p_sophieu
  AND ma_xet_nghiem = p_madv;

UPDATE noitru_cd_xet_nghiem_ct
SET
    ngay_thuc_hien = TO_DATE(p_thoigian || ':59', 'DD/MM/YYYY HH24:MI:SS')
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_xn = p_sophieu
  AND id_chisocha = p_madv;

END IF;
END IF;

SELECT
    ten_benh_nhan
INTO v_tenbnehnhan
FROM
    his_public_list.dm_benh_nhan
WHERE
    ma_benh_nhan = v_mabenhnhan;

UPDATE cmu_thoigian_nhanvien
SET
    thoigian_KT = TO_DATE(p_thoigian , 'DD/MM/YYYY HH24:MI')
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND noitru =
      CASE
          WHEN p_hinhthuc = 1 THEN
              0
          ELSE
              1
          END
  AND sophieu = p_sophieu
  AND ma_dv = p_madv;

cmu_log(p_dvtt, 'Thay đổi ngày  thực hiện : '
                    || v_tenbnehnhan
                    || ' - Mã bệnh nhân: '
                    || v_mabenhnhan
                    || '- Thời gian cũ: '
                    || v_ngaycu
                    || ' - Tên dịch vụ: '
                    || v_tendichvu
                    || ' - Số phiếu: '
                    || p_sophieu
                    || ' - MADV: '
                    || p_madv
                    || ' - Thời gian mới: '
                    || p_thoigian, p_user, p_thaotac);

RETURN '0';
END;