
CREATE TABLE "HIS_MANAGER"."C<PERSON>_THOIG<PERSON>N_NHANVIEN" (
   "DVTT" VARCHAR2(255 BYTE) NULL ,
   "<PERSON><PERSON><PERSON><PERSON>VI<PERSON>" NUMBER ,
   "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" NUMBER ,
   "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" NUMBER ,
   "NOITRU" NUMBER ,
   "THOIGIAN_BD" DATE ,
   "THOIGIAN_KT" DATE ,
   "SOPHIEU" VARCHAR2(255),
   "MA_DV" VARCHAR2(255),
   "LOAI" VARCHAR2(255),
   "NGAYTAO" DATE
);
CREATE INDEX CMUTGNHANVIENID ON CMU_THOIGIAN_NHANVIEN(DVTT, MAN<PERSON><PERSON>VI<PERSON>,THOIGIAN_BD );
CREATE INDEX CMUTGNHANVIENID2 ON CMU_THOIGIAN_NHANVIEN(DVTT,THOIGIAN_BD );
CREATE unique INDEX CMUTGNHANVIENIDUQ ON CMU_THOIGIAN_NHANVIEN(DV<PERSON>, <PERSON>OVAOVIEN,<PERSON>OP<PERSON>IE<PERSON>,MA_DV );



CREATE TABLE "HIS_MANAGER"."CMU_THOIGIAN_BENHNHAN" (
   "DVTT" VARCHAR2(255 BYTE) NULL ,
   "SOVAOVIEN" NUMBER ,
   "MABENHNHAN" NUMBER ,
   "NOITRU" NUMBER ,
   "THOIGIAN_BD" DATE ,
   "THOIGIAN_KT" DATE ,
   "SOPHIEU" VARCHAR2(255),
   "MA_DV" VARCHAR2(255),
   "LOAI" VARCHAR2(255),
   "NGAYTAO" DATE
);
CREATE INDEX CMUTGBENHNHANID ON CMU_THOIGIAN_BENHNHAN(DVTT, SOVAOVIEN,THOIGIAN_BD );
CREATE INDEX CMUTGBENHNHANID2 ON CMU_THOIGIAN_BENHNHAN(DVTT,THOIGIAN_BD );
CREATE INDEX CMUTGBENHNHANIDUQ ON CMU_THOIGIAN_BENHNHAN(DVTT, SOVAOVIEN,SOPHIEU,MA_DV );


create global temporary table HIS_MANAGER.CMU_THOIGIAN_NHANVIEN_TEMP
(
    "DVTT" VARCHAR2(255 BYTE) NULL ,
    "MANHANVIEN" NUMBER ,
    "SOVAOVIEN" NUMBER ,
    "MABENHNHAN" NUMBER ,
    "NOITRU" NUMBER ,
    "THOIGIAN_BD" DATE ,
    "THOIGIAN_KT" DATE ,
    "SOPHIEU" VARCHAR2(255),
    "MA_DV" VARCHAR2(255),
    "LOAI" VARCHAR2(255),
    "NGAYTAO" DATE
) on commit preserve rows;
CREATE INDEX CMUTGNHANVIENTEMPID ON CMU_THOIGIAN_NHANVIEN_TEMP(DVTT, MANHANVIEN,THOIGIAN_BD );
CREATE unique INDEX CMUTGNHANVIENTEMPIDUQ ON CMU_THOIGIAN_NHANVIEN_TEMP(DVTT, SOVAOVIEN,SOPHIEU,MA_DV );