create or replace FUNCTION               "NOITRU_TOATHUOCTONGHOP_SELECT"(p_dvtt           IN VARCHAR2,
                                                           p_<PERSON><PERSON><PERSON><PERSON>     IN VARCHAR2,
                                                           p_stt_benhan     IN VARCHAR2,
                                                           p_stt_dotdieutri IN VARCHAR2)
  RETURN SYS_REFCURSOR IS
  cur SYS_REFCURSOR;
  v_cobhyt       NUMBER(1) DEFAULT 1;
  v_sovaovien    NUMBER(11);
  v_sovaovien_dt NUMBER(11);
	V_ID_DIEUTRI NUMBER(11);
  v_danhthuockhongphantoa varchar2(50);
  v_laythuoctondusouong NUMBER(1); -- BDH thêm tham số chỉ quét những thuốc còn tồn kho
BEGIN
begin
select mota_thamso into v_danhthuockhongphantoa from his_fw.dm_thamso_donvi t
where ma_thamso = 91133 and dvtt = p_dvtt ;
exception when no_data_found then v_danhthuockhongphantoa:= '0';
end;
    -- BDH
BEGIN
SELECT mota_thamso
INTO   v_laythuoctondusouong
FROM   his_fw.dm_thamso_donvi t
WHERE  dvtt = p_dvtt
  AND ma_thamso = 52092 ;
exception
        WHEN no_data_found THEN 
          v_laythuoctondusouong:= 0;
END;
    -- END BDH


SELECT
    t.cobhyt,
    t.sovaovien,
    t.sovaovien_dt
INTO v_cobhyt, v_sovaovien, v_sovaovien_dt
FROM noitru_dotdieutri t
WHERE dvtt = p_dvtt
  AND t.stt_dotdieutri = p_stt_dotdieutri
  AND t.stt_benhan = p_stt_benhan;
select ID_DIEUTRI into V_ID_DIEUTRI from NOITRU_DIEUTRI
where SOVAOVIEN = V_SOVAOVIEN AND SOVAOVIEN_DT = v_sovaovien_dt
  and dvtt = p_Dvtt and STT_DIEUTRI = p_matoathuoc;
IF v_laythuoctondusouong = 0 THEN
    OPEN cur FOR
SELECT
    CASE
        WHEN (v_cobhyt = 0 AND ct.nghiep_vu = 'noitru_toathuoc')
            THEN
            'Toa thuốc nội trú'
        ELSE
            nv.MOTA_NGHIEPVU
        END                                                      AS MOTA_NGHIEPVU,
    ct.STT_TOATHUOC,
    ct.STT_order,
    ct.DVTT,
    ct.MA_TOA_THUOC,
    ct.MAKHOVATTU,
    ct.MAVATTU,
    ct.TEN_VAT_TU,
    ct.HOAT_CHAT,
    ct.NGHIEP_VU,
    ct.SO_LUONG_THUC_LINH AS SO_LUONG,
    ct.SO_LUONG_THUC_LINH,
    ct.DONGIA_BAN_BV,
    ct.DONGIA_BAN_BH,
    ct.THANHTIEN_THUOC,
    ct.SO_NGAY_UONG,
    ct.SANG_UONG,
    ct.TRUA_UONG,
    ct.CHIEU_UONG,
    ct.TOI_UONG,
    ct.XAC_NHAN,
    ct.NGAY_RA_TOA,
    ct.GHI_CHU_CT_TOA_THUOC,
    ct.MA_BAC_SI_THEMTHUOC,
    ct.DATHANHTOAN,
    ct.CACH_SU_DUNG,
    vt.NgoaiDanhMucBHYT,
    vt.TamNgung,
    vt.DVT,
    decode(CT.nhapbandau, 1, 'Nhập đầu kỳ', kho.TenKhoVatTu) AS TenKhoVatTu,
    ct.TU_TU_THUOC,
    ct.NGHIEP_VU,
    ct.NGUOITAO,
    nv.TEN_NHANVIEN_CD TEN_NHANVIEN,
    case when ct.NGHIEP_VU = 'noitru_toathuoc' then 1
         when ct.NGHIEP_VU = 'noitru_toavattu' then 2
         when ct.NGHIEP_VU = 'noitru_toaquaybanthuocbv' then 3
         when ct.NGHIEP_VU = 'noitru_toadichvu' then 4

         else 5 end STT_NGHIEPVU,
    CMU_YLENHTHUOC_THEOGIO_GROUP(p_dvtt, ct.id_dieutri, ct.sovaovien, ct.sovaovien_dt, ct.stt_toathuoc) YLENH,
    ct.SOLOSANXUAT,
    to_char(ct.ngay_gio_KE_thucte, 'DD/MM/YYYY HH24:MI') NGAY_YL,
    to_char(ct.NGAY_TH_YLENH, 'DD/MM/YYYY HH24:MI') NGAY_TH_YL,
    nvth.TEN_NHANVIEN_CD TEN_NHANVIEN_TH,
    vt.dungtich_dichtruyen,
    ct.dungtich_sudung,
    NULL mavattu_muangoai
FROM noitru_ct_toa_thuoc ct
         left join HIS_FW.DM_NHANVIEN_CD nv on ct.NGUOITAO = nv.MA_NHANVIEN
         left join HIS_FW.DM_NHANVIEN_CD nvth on ct.nguoi_thuchienylenh = nvth.MA_NHANVIEN,
     dc_tb_vattu vt,
     dc_tb_nghiepvu nv,
     dc_tb_khovattu kho

WHERE  vt.DVTT = p_dvtt
  AND ct.DVTT = p_dvtt
  AND kho.DVTT = p_dvtt
  AND kho.MaKhoVatTu = ct.MAKHOVATTU
  and NVL(kho.kyhieu, ' ') in ('TUTRUC', 'KHOADUOC')
  AND ct.MAVATTU = vt.MaVatTu
  --and stt_benhan=p_stt_benhan
  --and stt_dotdieutri=p_stt_dotdieutri
  AND ct.sovaovien = v_sovaovien
  AND ct.sovaovien_dt = v_sovaovien_dt
  AND ct.NGHIEP_VU = nv.NGHIEP_VU
  AND ct.MA_TOA_THUOC = p_matoathuoc
  and CT.ID_DIEUTRI = V_ID_DIEUTRI
  AND ct.so_luong_thuc_linh > 0
--and (ct.tu_tu_thuoc = 0 or v_danhthuockhongphantoa = 1)
UNION ALL
SELECT
    nv.MOTA_NGHIEPVU,
    ct.STT_TOATHUOC,
    ct.STT_order,
    ct.DVTT,
    ct.MA_TOA_THUOC,
    0,
    0,
    ct.TEN_VAT_TU,
    ct.HOAT_CHAT,
    ct.NGHIEP_VU,
    ct.SO_LUONG,
    ct.SO_LUONG,
    0,
    0,
    0,
    ct.SO_NGAY_UONG,
    ct.SANG_UONG,
    ct.TRUA_UONG,
    ct.CHIEU_UONG,
    ct.TOI_UONG,
    0,
    ct.NGAY_RA_TOA,
    ct.GHI_CHU_CT_TOA_THUOC,
    ct.MA_BAC_SI_THEMTHUOC,
    0,
    ct.CACH_SU_DUNG,
    1,
    0,
    ct.GHI_CHU_CT_TOA_THUOC,
    'Mua ngoài' AS TenKhoVatTu,
    0 as TU_TU_THUOC,
    ct.NGHIEP_VU,
    ct.NGUOITAO,
    nv.TEN_NHANVIEN_CD TEN_NHANVIEN,
    6 STT_NGHIEPVU,
    CMU_YLENHTHUOC_THEOGIO_GROUP(p_dvtt, ct.id_dieutri, ct.sovaovien, ct.sovaovien_dt, 'n'||ct.stt_toathuoc) YLENH,
    '-' SOLOSANXUAT,
    to_char(ct.NGAY_RA_TOA, 'DD/MM/YYYY HH24:MI') NGAY_YL,
    null NGAY_TH_YL,
    NULL,
    TO_NUMBER(vt.dungtich_dichtruyen),
    ct.dungtich_sudung,
    ct.mavattu           mavattu_muangoai
FROM noitru_ct_toa_thuoc_muangoai ct
         left join HIS_FW.DM_NHANVIEN_CD nv on ct.NGUOITAO = nv.MA_NHANVIEN
         LEFT JOIN dc_tb_vattu_muangoai           vt ON vt.mavattu = ct.mavattu
   , dc_tb_nghiepvu nv
WHERE ct.MA_TOA_THUOC = p_matoathuoc
  AND ct.DVTT = p_dvtt
  -- and stt_benhan=p_stt_benhan
  --and stt_dotdieutri=p_stt_dotdieutri
  AND ct.sovaovien = v_sovaovien
  AND ct.sovaovien_dt = v_sovaovien_dt
  AND ct.NGHIEP_VU = nv.NGHIEP_VU
--ORDER BY MOTA_NGHIEPVU, TEN_VAT_TU;
ORDER BY  STT_NGHIEPVU,STT_order;
ELSE
     OPEN cur FOR 
      -- BDH
SELECT
    CASE
        WHEN (v_cobhyt = 0 AND ct.nghiep_vu = 'noitru_toathuoc')
            THEN
            'Toa thuốc nội trú'
        ELSE
            nv.MOTA_NGHIEPVU
        END                                                      AS MOTA_NGHIEPVU,
    ct.STT_TOATHUOC,
    ct.STT_order,
    ct.DVTT,
    ct.MA_TOA_THUOC,
    ct.MAKHOVATTU,
    ct.MAVATTU,
    ct.TEN_VAT_TU,
    ct.HOAT_CHAT,
    ct.NGHIEP_VU,
    ct.SO_LUONG_THUC_LINH AS SO_LUONG,
    ct.SO_LUONG_THUC_LINH,
    ct.DONGIA_BAN_BV,
    ct.DONGIA_BAN_BH,
    ct.THANHTIEN_THUOC,
    ct.SO_NGAY_UONG,
    ct.SANG_UONG,
    ct.TRUA_UONG,
    ct.CHIEU_UONG,
    ct.TOI_UONG,
    ct.XAC_NHAN,
    ct.NGAY_RA_TOA,
    ct.GHI_CHU_CT_TOA_THUOC,
    ct.MA_BAC_SI_THEMTHUOC,
    ct.DATHANHTOAN,
    ct.CACH_SU_DUNG,
    vt.NgoaiDanhMucBHYT,
    vt.TamNgung,
    vt.DVT,
    decode(CT.nhapbandau, 1, 'Nhập đầu kỳ', kho.TenKhoVatTu) AS TenKhoVatTu,
    ct.TU_TU_THUOC,
    ct.NGHIEP_VU,
    ct.NGUOITAO,
    nv.TEN_NHANVIEN_CD TEN_NHANVIEN,
    case when ct.NGHIEP_VU = 'noitru_toathuoc' then 1
         when ct.NGHIEP_VU = 'noitru_toavattu' then 2
         when ct.NGHIEP_VU = 'noitru_toaquaybanthuocbv' then 3
         when ct.NGHIEP_VU = 'noitru_toadichvu' then 4

         else 5 end STT_NGHIEPVU,
    ct.SOLOSANXUAT,
    to_char(ct.ngay_gio_KE_thucte, 'DD/MM/YYYY HH24:MI') NGAY_YL,
    to_char(ct.NGAY_TH_YLENH, 'DD/MM/YYYY HH24:MI') NGAY_TH_YL,
    nvth.TEN_NHANVIEN_CD TEN_NHANVIEN_TH,
    vt.dungtich_dichtruyen,
    ct.dungtich_sudung,
    NULL mavattu_muangoai
FROM noitru_ct_toa_thuoc ct
         left join HIS_FW.DM_NHANVIEN_CD nv on ct.NGUOITAO = nv.MA_NHANVIEN
         left join HIS_FW.DM_NHANVIEN_CD nvth on ct.nguoi_thuchienylenh = nvth.MA_NHANVIEN,
     dc_tb_vattu vt,
     dc_tb_nghiepvu nv,
     dc_tb_khovattu kho
WHERE ct.MA_TOA_THUOC = p_matoathuoc
  AND vt.DVTT = p_dvtt
  AND ct.DVTT = p_dvtt
  AND kho.DVTT = p_dvtt
  AND kho.MaKhoVatTu = ct.MAKHOVATTU
  AND ct.MAVATTU = vt.MaVatTu
  AND ct.sovaovien = v_sovaovien
  AND ct.sovaovien_dt = v_sovaovien_dt
  AND ct.NGHIEP_VU = nv.NGHIEP_VU
  AND ct.so_luong_thuc_linh > 0
  and (ct.tu_tu_thuoc = 0 or v_danhthuockhongphantoa = 1)
  AND ct.SO_LUONG_THUC_LINH <= BDH_KIEMTRASOLUONG_TONKHO(p_dvtt,ct.makhovattu,ct.mavattu)
UNION ALL
SELECT
    nv.MOTA_NGHIEPVU,
    ct.STT_TOATHUOC,
    ct.STT_order,
    ct.DVTT,
    ct.MA_TOA_THUOC,
    0,
    0,
    ct.TEN_VAT_TU,
    ct.HOAT_CHAT,
    ct.NGHIEP_VU,
    ct.SO_LUONG,
    ct.SO_LUONG,
    0,
    0,
    0,
    ct.SO_NGAY_UONG,
    ct.SANG_UONG,
    ct.TRUA_UONG,
    ct.CHIEU_UONG,
    ct.TOI_UONG,
    0,
    ct.NGAY_RA_TOA,
    ct.GHI_CHU_CT_TOA_THUOC,
    ct.MA_BAC_SI_THEMTHUOC,
    0,
    ct.CACH_SU_DUNG,
    1,
    0,
    ct.GHI_CHU_CT_TOA_THUOC,
    'Mua ngoài' AS TenKhoVatTu,
    0 as TU_TU_THUOC,
    ct.NGHIEP_VU,
    ct.NGUOITAO,
    nv.TEN_NHANVIEN_CD TEN_NHANVIEN,
    6 STT_NGHIEPVU,
    '-' SOLOSANXUAT,
    to_char(ct.NGAY_RA_TOA, 'DD/MM/YYYY HH24:MI') NGAY_YL,
    NULL NGAY_TH_YL,
    NULL TEN_NHANVIEN_TH,
    TO_NUMBER(vt.dungtich_dichtruyen),
    ct.dungtich_sudung,
    ct.mavattu           mavattu_muangoai
FROM noitru_ct_toa_thuoc_muangoai ct
         LEFT JOIN dc_tb_vattu_muangoai           vt ON vt.mavattu = ct.mavattu
         left join HIS_FW.DM_NHANVIEN_CD nv on ct.NGUOITAO = nv.MA_NHANVIEN, dc_tb_nghiepvu nv
WHERE ct.MA_TOA_THUOC = p_matoathuoc
  AND ct.DVTT = p_dvtt
  -- and stt_benhan=p_stt_benhan
  --and stt_dotdieutri=p_stt_dotdieutri
  AND ct.sovaovien = v_sovaovien
  AND ct.sovaovien_dt = v_sovaovien_dt
  AND ct.NGHIEP_VU = nv.NGHIEP_VU
--ORDER BY MOTA_NGHIEPVU, TEN_VAT_TU;
ORDER BY STT_NGHIEPVU,STT_order;
END IF;
RETURN cur;

END;