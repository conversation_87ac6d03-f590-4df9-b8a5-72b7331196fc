<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="TODIEUTRI" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" whenResourceMissingType="Error" uuid="0f5e3019-005e-44fe-bb8f-29ae14f10109">
	<property name="ireport.zoom" value="4.715895382000005"/>
	<property name="ireport.x" value="612"/>
	<property name="ireport.y" value="48"/>
	<parameter name="tenbenhvien" class="java.lang.String"/>
	<parameter name="ten_phongban" class="java.lang.String"/>
	<parameter name="hovaten" class="java.lang.String"/>
	<parameter name="tuoi" class="java.lang.String"/>
	<parameter name="gioitinh" class="java.lang.String"/>
	<parameter name="sogiuong" class="java.lang.String"/>
	<parameter name="buong" class="java.lang.String"/>
	<parameter name="chandoan" class="java.lang.String"/>
	<parameter name="tensoyte" class="java.lang.String"/>
	<parameter name="sovaovien" class="java.lang.String"/>
	<parameter name="sophieu" class="java.lang.String"/>
	<parameter name="dvtt" class="java.lang.String"/>
	<parameter name="stt_benhan" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_dotdieutri" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="id_dieutri" class="java.lang.String"/>
	<parameter name="sovaovien_dt" class="java.lang.String"/>
	<parameter name="sobenhan" class="java.lang.String"/>
	<parameter name="soyte" class="java.lang.String"/>
	<parameter name="makhoa" class="java.lang.String"/>
	<parameter name="userid" class="java.lang.String"/>
	<parameter name="stt_dieutri" class="java.lang.Long"/>
	<parameter name="ten_nhanvien" class="java.lang.String"/>
	<parameter name="TENBS" class="java.lang.String"/>
	<parameter name="NGAY_KY" class="java.lang.String"/>
	<parameter name="KEYSIGN" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{
	call hsba_tdtsub_cmu_selv5(
		$P{dvtt},	
		$P{sovaovien},
		$P{stt_benhan},
		$P{id_dieutri},
		$P{stt_dotdieutri},
		$P{ORACLE_REF_CURSOR}
	)
}]]>
	</queryString>
	<field name="Y_LENH" class="java.lang.String"/>
	<field name="SOLUONG" class="java.lang.String"/>
	<field name="DVT" class="java.lang.String"/>
	<field name="LOAI" class="java.lang.String"/>
	<field name="NGAYKS" class="java.lang.String"/>
	<field name="NGAYCORT" class="java.lang.String"/>
	<field name="NGAYHT" class="java.lang.String"/>
	<field name="ANCHUKY" class="java.lang.String"/>
	<group name="loai">
		<groupExpression><![CDATA[$F{LOAI}]]></groupExpression>
		<groupHeader>
			<band height="14">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" x="0" y="0" width="240" height="14" isPrintWhenDetailOverflows="true" uuid="702c00b6-11ac-4930-acfc-6b2144fed29d"/>
					<box leftPadding="2"/>
					<textElement markup="styled">
						<font fontName="Times New Roman" size="10" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{LOAI}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="14" splitType="Stretch">
			<frame>
				<reportElement x="2" y="1" width="13" height="13" uuid="f91d5623-af48-45b7-a854-4b4d529174d1">
					<printWhenExpression><![CDATA[$F{NGAYKS} != null]]></printWhenExpression>
				</reportElement>
				<rectangle radius="6">
					<reportElement x="0" y="0" width="13" height="13" uuid="52b0328e-3ae7-4459-b14f-c13e0b3a3a64"/>
				</rectangle>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="0" y="0" width="13" height="13" uuid="6b25cb43-379f-429d-91eb-2754c0a305ef">
						<printWhenExpression><![CDATA[$F{NGAYKS} != null]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGAYKS} == null? "-":$F{NGAYKS}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="2" y="1" width="13" height="13" uuid="86144d8c-1f1f-4299-b0ef-1b3a32b05298">
					<printWhenExpression><![CDATA[$F{NGAYHT} != null]]></printWhenExpression>
				</reportElement>
				<rectangle radius="0">
					<reportElement x="0" y="0" width="13" height="13" uuid="a140d9cb-de65-4c64-a934-5f03647ca714"/>
				</rectangle>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="0" y="0" width="13" height="13" uuid="b02bc07a-d136-4ad5-9df0-2e31a4245bd7"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGAYHT}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="2" y="0" width="15" height="14" uuid="0382538d-164e-409e-867c-d39c5567c92a">
					<printWhenExpression><![CDATA[$F{NGAYCORT} != null]]></printWhenExpression>
				</reportElement>
				<line direction="BottomUp">
					<reportElement x="0" y="0" width="7" height="12" uuid="2f477257-9088-4ee7-939d-af1b7bc522f9"/>
				</line>
				<line>
					<reportElement x="0" y="12" width="15" height="1" uuid="655884e7-c2ff-4e1c-899c-8ca2fdcdad4f"/>
				</line>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="1" y="1" width="13" height="13" uuid="e356ee1d-4a71-4e0b-8b60-c641196d6f4c"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGAYCORT}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="7" y="0" width="7" height="12" uuid="56b3b189-1070-4a40-9ef4-a2cebe8d0770"/>
				</line>
			</frame>
			<frame>
				<reportElement x="2" y="1" width="13" height="13" uuid="0a9e98e7-afef-44f2-a3df-8814b5aa7566">
					<printWhenExpression><![CDATA[$F{NGAYKS} == null && $F{NGAYHT} == null && $F{NGAYCORT} == null]]></printWhenExpression>
				</reportElement>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="0" y="0" width="13" height="13" uuid="674123f3-36ae-451b-922c-a1d7ca7ce17c"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["-"]]></textFieldExpression>
				</textField>
			</frame>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="235" y="0" width="43" height="14" uuid="441a974a-2f10-4622-9e46-0732cdd7735f"/>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOLUONG} == null? "": ($F{SOLUONG}+" "+ $F{DVT} )]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="15" y="0" width="220" height="14" isPrintWhenDetailOverflows="true" uuid="42dd3674-fb25-4d8f-ae11-0625dfd3504c"/>
				<box leftPadding="4" rightPadding="2"/>
				<textElement markup="styled">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{Y_LENH}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="83">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="88" y="5" width="190" height="13" isPrintWhenDetailOverflows="true" uuid="ed988197-31fe-4068-ad83-da82f0d1cf48"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Bác sĩ điều trị"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="88" y="62" width="190" height="13" isPrintWhenDetailOverflows="true" uuid="61a4976f-ca2b-4c54-914f-f5671243f2ce">
					<printWhenExpression><![CDATA[$F{ANCHUKY}.equals("0") || $P{KEYSIGN} != null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{ten_nhanvien}]]></textFieldExpression>
			</textField>
			<image scaleImage="FillFrame" hAlign="Center">
				<reportElement x="240" y="18" width="20" height="20" uuid="d8cfe456-68ef-4920-b049-15be89e19820">
					<printWhenExpression><![CDATA[$P{KEYSIGN} != null]]></printWhenExpression>
				</reportElement>
				<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.getInstance(
new SimpleJasperReportsContext()).loadAwtImageFromBytes(Base64.getDecoder().decode("iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAABvElEQVR4nO3U3UrCYBjAcS+gAy0XdjAs925uO+huBGfnnYkmQX7kxwQvoNqJdAmFSEFQptP5RdDRBg26jig6euKxrFdbVjo66oGHuZP/j9cX5vH8z1+OcFv0y0M1hU/X48SoMFK/ZMoDFaRh2ZZvigF3472SJfVVkAYqICIPy5YrJyFGhgl3C5bYK4HUx/1AxIGaXDguGAVL7BZB7OFOIIudgLzGzbBRgDACNNIrfb6Dpari9x0pKXz+JM538ma4k4cRQCPdgi3r0/GDCOPTFNOnxcCrxWxGiwRmxvWcKbTzICBAI4ZTvKr4vUeKhfHx4juiTnHSypq8vg9CG5dG8p/jOPi30PGvEILxZtbkWzngddwPhO/sO8e/OsE0Qi4wnjFJMwsEAQrh2zPiTncwvcva1n2okb4j1xkYARRCWjk7+F18FrKibcH6eQq4RgY4BCiEa/4i7oRgPHi2A6GrNHCN9ATCXWftoL4737cGEe9B1GLrSQhd7kHoam8KSVt4J3PF35FKhGFriccNBChkvZ54XDg+nrXqtsjWEk9jJFhPPK8eRzddib8jh9siexJ/YE/jD/jb1fj/eN7mBQZhd5OoxlU0AAAAAElFTkSuQmCC")
)]]></imageExpression>
			</image>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="110" y="25" width="148" height="13" isPrintWhenDetailOverflows="true" uuid="966416b0-c35b-4aae-9ab7-7a889f08363b">
					<printWhenExpression><![CDATA[$P{KEYSIGN} != null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Ký bởi: " + $P{TENBS}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="110" y="38" width="148" height="13" isPrintWhenDetailOverflows="true" uuid="939d609b-b8e3-494f-9633-86e2d534c8ce">
					<printWhenExpression><![CDATA[$P{KEYSIGN} != null]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="9" isBold="false" isStrikeThrough="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày ký: "+ $P{NGAY_KY}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
