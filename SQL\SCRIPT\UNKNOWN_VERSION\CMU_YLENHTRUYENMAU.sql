CREATE TABLE "HIS_MANAGER"."CMU_YLENHTRUYENMAU"
(	 "ID" NUMBER GENERATED ALWAYS AS IDENTITY,
      "ID_DIEUTRI" NUMBER NOT NULL,
      "DVTT" NUMBER NOT NULL ENABLE,
      "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" NUMBER,
      "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" NUMBER,
      "<PERSON><PERSON><PERSON><PERSON>" NUMBER,
      "N<PERSON><PERSON><PERSON><PERSON>YEN" DATE,
      "SOLUONG" NUMBER,
      "NGAY_TAO" DATE,
      "NGUOI_TAO" NUMBER,
      "TRANGTHAI" NUMBER,
      "LOAI" VARCHAR2(255 BYTE),
      "TOCDO" VARCHAR2(255 BYTE),
      "TENCHEPHAM" VARCHAR2(2000 BYTE)
      PRIMARY KEY ("ID_DIEUTRI", "DVTT", "SOVAOVIEN")
) ;

CREATE INDEX "HIS_MANAGER"."CMU_YLENHTRUYENMAUID" ON "HIS_MANAGER"."CMU_<PERSON><PERSON>NHTRUYENMAU" ("<PERSON><PERSON><PERSON>OVI<PERSON>", "<PERSON>V<PERSON>")
;
CREATE INDEX "HIS_MANAGER"."<PERSON>MU_YLENHTRUYENMAUID2" ON "HIS_MANAGER"."CMU_YLENHTRUYENMAU" ("NGAYTRUYEN", "DVTT")
;