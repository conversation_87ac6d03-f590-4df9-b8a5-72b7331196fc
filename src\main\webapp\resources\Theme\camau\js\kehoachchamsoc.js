$(function () {
    var thongTinPhieuMoiNhat;
    var formKeHoachChamSoc;
    var thongTinPhieuTruocKhiChinhSua;
    $('#modalDSKeHoachChamSoc').on('show.bs.modal', function() {
        initGridKeHoachChamSoc();
        loadDSKeHoachChamSoc();
    });
    $('#modalKeHoachChamSoc').on('hidden.bs.modal', function () {
        thongTinPhieuTruocKhiChinhSua = {};
    })
    $("#ttchamsoc-cacphieukhac").click(function() {
        loadDSKeHoachChamSoc();
    });
    $("#KeHoachChamSocXemDanhsach").click(function () {
        $("#modalDSKeHoachChamSoc").modal("show");
    });

    $(".KeHoachChamSoc_them").click(function () {
        $("#modalKeHoachChamSoc .btn-them").show();
        $("#modalKeHoachChamSoc .btn-sua").hide();
        initFormioKeHoachChamSoc({});
    });

    $("#KeHoachChamSoc_themmoi").click(function () {
        var idButton = this.id;
        themKeHoachChamSoc(idButton,
            function (data) {
                if (data.ID > 0) {
                    notifiToClient("Green", MESSAGEAJAX.ADD_SUCCESS);
                    $("#modalKeHoachChamSoc").modal("hide");
                    loadDSKeHoachChamSoc();
                } else {
                    notifiToClient("Green", MESSAGEAJAX.FAIL);
                }
                hideSelfLoading(idButton);
            },
            function () {
                notifiToClient("Red", MESSAGEAJAX.ERROR);
                hideSelfLoading(idButton);
            });
    });

    $("#KeHoachChamSoc_themkyso").click(function () {
        var idButton = this.id;
        themKeHoachChamSoc(idButton,
            function (data) {
                if (data.ID > 0) {
                    xemKySoKeHoachChamSoc(data);
                    loadDSKeHoachChamSoc();
                } else {
                    notifiToClient("Green", MESSAGEAJAX.FAIL);
                }
                $("#modalKeHoachChamSoc").modal("hide");
                hideSelfLoading(idButton);
            },
            function () {
                notifiToClient("Red", MESSAGEAJAX.ERROR);
                hideSelfLoading(idButton);
            });
    });

    $("#edit_single_KeHoachChamSoc").click(function () {
        $("#modalKeHoachChamSoc .btn-them").hide();
        $("#modalKeHoachChamSoc .btn-sua").show();
        initFormioKeHoachChamSoc(thongTinPhieuMoiNhat);
    });
    $("#KeHoachChamSoc_luu").click(function () {
        var idButton = this.id;
        suaKeHoachChamSoc(idButton,
            function (data) {
                if (data.TT > 0) {
                    notifiToClient("Green", MESSAGEAJAX.ADD_SUCCESS);
                    $("#modalKeHoachChamSoc").modal("hide");
                } else {
                    notifiToClient("Green", MESSAGEAJAX.FAIL);
                }
                hideSelfLoading(idButton);
                loadDSKeHoachChamSoc();
            },
            function () {
                notifiToClient("Red", MESSAGEAJAX.ERROR);
                hideSelfLoading(idButton);
            });
    });

    $("#KeHoachChamSoc_luukyso").click(function () {
        var idButton = this.id;
        suaKeHoachChamSoc(idButton,
            function (data) {
                if (data.TT > 0) {
                    xemKySoKeHoachChamSoc(data);
                    loadDSKeHoachChamSoc();
                } else {
                    notifiToClient("Green", MESSAGEAJAX.FAIL);
                }
                hideSelfLoading(idButton);
            },
            function () {
                notifiToClient("Red", MESSAGEAJAX.ERROR);
                hideSelfLoading(idButton);
            });
    });

    $("#delete_single_KeHoachChamSoc").click(function () {
        var idButton = this.id;
        xoaKeHoachChamSoc(idButton, thongTinPhieuMoiNhat);
    });

    $("#view_single_KeHoachChamSoc").click(function () {
        var idButton = this.id;
        xemKeHoachChamSoc(idButton, thongTinPhieuMoiNhat);
    });

    $("#sign_single_KeHoachChamSoc").click(function () {
        xemKySoKeHoachChamSoc(thongTinPhieuMoiNhat);
    });

    $("#huysign_single_KeHoachChamSoc").click(function () {
        huyKySoKeHoachChamSoc(thongTinPhieuMoiNhat);
    });

    $("#KeHoachChamSoc_xemtong").click(function () {
        var url = 'cmu_in_cmu_kehoachchamsoc?type=pdf&' + $.param({
            dvtt: singletonObject.dvtt,
            id: '-1',
            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
        });
        previewPdfDefaultModal(url, "preview");
    });

    $("#KeHoachChamSoc_mau").click(function() {
        var element = $("#mau_danhsachmaujson_wrap");
        element.attr("function-add", 'insertMauHSBAKEHOACHCHAMSOC');
        element.attr("function-chinhsua", 'editMauHSBAKEHOACHCHAMSOC');
        element.attr("function-select", 'selectMauHSBAKEHOACHCHAMSOC');
        element.attr("function-getdata", 'getdataMauHSBAKEHOACHCHAMSOC');
        element.attr("function-validate", 'formioHSBAKEHOACHCHAMSOCValidate');
        element.attr("data-key", 'MAUKEHOACHCHAMSOC');
        $("#modalMauChungJSON").modal("show");
        $.loadDanhSachMauChungJSON('MAUKEHOACHCHAMSOC')
    })

    // FUNCTION
    function initGridKeHoachChamSoc() {
        var list = $("#list_KeHoachChamSoc");
        if(!list[0].grid) {
            $("#list_KeHoachChamSoc").jqGrid({
                datatype: "local",
                data: [],
                loadonce: true,
                height: 300,
                width: null,
                shrinkToFit: false,
                caption: "Kế hoạch chăm sóc",
                colModel: [
                    {
                        name: "KYSO",
                        label: "Ký số",
                        align: 'left',
                        width: 100,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.KEYSIGN) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: green">Đã ký</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: red">Chưa ký</span>';
                            }
                        }
                    },
                    {name: "ID", label: "ID", align: 'center', width: 50, hidden: true},
                    {name: "SOVAOVIEN", label: "Số vào viện", hidden: true},
                    {name: "SOVAOVIEN_DT", label: "SOVAOVIEN_DT", hidden: true},
                    {name: "STT_BENHAN", label: "STT_BENHAN", hidden: true},
                    {name: "SOBENHAN", label: "SOBENHAN", hidden: true},
                    {name: "SOBENHAN", label: "SOBENHAN", hidden: true},
                    {name: "MABENHNHAN", label: "MABENHNHAN", hidden:true},
                    {name: "NGAY_LAP_PHIEU", label: "NGAY_LAP_PHIEU", hidden:true},
                    {name: "NGUOI_LAP_PHIEU", label: "NGUOI_LAP_PHIEU", hidden:true},
                    {name: "KHOA_LAP_PHIEU", label: "KHOA_LAP_PHIEU", hidden:true},
                    {name: "NGUOI_TAO", label: "NGUOI_TAO", hidden:true},
                    {name: "NGAY_TAO", label: "NGAY_TAO", hidden:true},
                    {name: "NGAY_TAO_TEXT", label: "NGAY_TAO_TEXT", hidden:true},
                    {name: "KEYSIGN", label: "KEYSIGN", hidden:true},

                    {name: "SO_PHIEU", label: "Số phiếu", align: 'center', width: 130},
                    {name: "NGAY_LAP_PHIEU_TEXT", label: "Ngày", align: 'center', width: 130},
                    {name: "TINH_TRANG_NGUOI_BENH", label: "Tình trạng người bệnh", width: 250},
                    {name: "KE_HOACH_CHAM_SOC", label: "Kế hoạch chăm sóc", width: 250},
                    {name: "THUC_HIEN_CHAM_SOC", label: "Thực hiện chăm sóc", width: 250},
                    {name: "LUONG_GIA", label: "Lượng giá", width: 250},
                    {name: "TEN_NGUOI_LAP_PHIEU", label: "Người lập phiếu", align: 'center', width: 150},
                    {name: "TEN_NGUOI_TAO", label: "Người tạo", align: 'center', width: 150},
                ],
                onRightClickRow: function (id1) {
                    if (id1) {
                        $("#list_KeHoachChamSoc").jqGrid('setSelection', id1);
                        $.contextMenu('destroy', '#list_KeHoachChamSoc tr');
                        var rowData = getThongtinRowSelected("list_KeHoachChamSoc");
                        var items = {
                            "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                        }
                        if (rowData.KEYSIGN) {
                            items = {
                                ...items,
                                "huykyso": {name: '<p style="color:red"><i class="fa fa-key text-danger" aria-hidden="true"></i> Huỷ ký số</p>'},
                            }
                        } else {
                            items = {
                                "kyso": {name: '<p><i class="fa fa-key text-primary" aria-hidden="true"></i> Ký số</p>'},
                                ...items,
                                "sua": {name: '<p><i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Sửa</p>'},
                                "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'},
                            }
                        }
                        $.contextMenu({
                            selector: '#list_KeHoachChamSoc tr',
                            callback: function (key, options) {
                                if (key == "xem") {
                                    xemKeHoachChamSoc("", rowData);
                                }
                                if (key == "sua") {
                                    $("#modalKeHoachChamSoc .btn-them").hide();
                                    $("#modalKeHoachChamSoc .btn-sua").show();
                                    initFormioKeHoachChamSoc(rowData);
                                }
                                if (key == "xoa") {
                                    xoaKeHoachChamSoc("", rowData);
                                }
                                if (key == "huykyso") {
                                    huyKySoKeHoachChamSoc(rowData);
                                }
                                if (key == "kyso") {
                                    xemKySoKeHoachChamSoc(rowData);
                                }
                            },
                            items: items
                        })
                    }
                }
            });
        }
        $('#list_KeHoachChamSoc').jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: 'cn'});
    }

    function loadDSKeHoachChamSoc() {
        $("#list_bangkiemtruoctiemchungsosinh").jqGrid('clearGridData');
        var url = "cmu_list_CMU_KEHOACHCHAMSOC_GET?url=" +
            convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT]);
        $.ajax({
            url: url,
            type: 'GET',
            async: false,
            success: function (data) {
                if (data && data.length > 0) {
                    thongTinPhieuMoiNhat = data[0];
                    $("#dataKeHoachChamSoc").html("Người lập: " + data[0].TEN_NGUOI_LAP_PHIEU + " - " + data[0].NGAY_LAP_PHIEU_TEXT);
                    $("#list_KeHoachChamSoc").jqGrid('setGridParam', {
                        datatype: 'local',
                        data: data
                    }).trigger("reloadGrid");
                    $('#handle_icon_KeHoachChamSoc').css('visibility', 'unset');
                    if (data[0].KEYSIGN){
                        $('#handle_icon_KeHoachChamSoc .daky-hidden').hide();
                        $('#handle_icon_KeHoachChamSoc .chuaky-hidden').show();
                    } else {
                        $('#handle_icon_KeHoachChamSoc .daky-hidden').show();
                        $('#handle_icon_KeHoachChamSoc .chuaky-hidden').hide();
                    }
                } else  {
                    thongTinPhieuMoiNhat = {};
                    $("#dataKeHoachChamSoc").html('Không có dữ liệu');
                    $('#handle_icon_KeHoachChamSoc').css('visibility', 'hidden');
                }
            }
        });
    }

    function initFormioKeHoachChamSoc(data) {
        addTextTitleModal('titleKeHoachChamSoc', "Kế hoạch chăm sóc");
        $("#modalKeHoachChamSoc").modal("show");
        var jsonForm = getJSONObjectForm([
            {
                label: "1",
                key: "1",
                columns: [
                    {
                        "components": [
                            JSONDateTime("Ngày giờ lập phiếu", "NGAY_LAP_PHIEU_TEXT_FORMIO", "pr-2", "dd/MM/yyyy HH:mm",
                                true, moment(thongtinhsba.thongtinbn.NGAY_VAO_VIEN, ['DD/MM/YYYY']).format("YYYY-MM-DD"),
                                null, {}, {
                                    others: {
                                        "validate": {
                                            "required": true
                                        },
                                    }
                                }),
                        ],
                        "width": 4,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONSelect("Người lập phiếu", "NGUOI_LAP_PHIEU", "pr-2", {}, {}, {
                                others:
                                    {
                                        "data": {
                                            "values": singletonObject.danhsachtatcanhanvien
                                        },
                                        "defaultValue": String(singletonObject.userId),
                                        "validate": {
                                            "required": true
                                        },
                                    }
                            }),
                        ],
                        "width": 4,
                        "size": "md",
                    },
                    {
                        "components": [
                            JSONNumber("Số phiếu", "SO_PHIEU", "pr-2", {}, {
                                others: {}
                            }),
                        ],
                        "width": 4,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "2",
                key: "2",
                columns: [
                    {
                        "components": [
                            JSONTextarea("Tình trạng người bệnh", "TINH_TRANG_NGUOI_BENH", "", {}, {
                                others:
                                    {
                                        "validate": {
                                            "maxLength": 250
                                        },
                                    }
                            }, 2),
                        ],
                        "width": 12,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "2",
                key: "2",
                columns: [
                    {
                        "components": [
                            JSONTextarea("Kế hoạch chăm sóc", "KE_HOACH_CHAM_SOC", "", {}, {
                                others:
                                    {
                                        "validate": {
                                            "maxLength": 250
                                        },
                                    }
                            }, 2),
                        ],
                        "width": 12,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "3",
                key: "3",
                columns: [
                    {
                        "components": [
                            JSONTextarea("Thực hiện chăm sóc", "THUC_HIEN_CHAM_SOC", "", {}, {
                                others:
                                    {
                                        "validate": {
                                            "maxLength": 250
                                        },
                                    }
                            }, 2),
                        ],
                        "width": 12,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "4",
                key: "4",
                columns: [
                    {
                        "components": [
                            JSONTextarea("Lượng giá", "LUONG_GIA", "", {}, {
                                others:
                                    {
                                        "validate": {
                                            "maxLength": 250
                                        },
                                    }
                            }, 2),
                        ],
                        "width": 12,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
        ])
        Formio.createForm(document.getElementById('KeHoachChamSoc_form'),
            jsonForm
        ).then(function(form) {
            formKeHoachChamSoc = form;
            for (var key in data) {
                if(data[key] != null && data[key] != undefined){
                    data[key] = String(data[key]);
                }
            }
            thongTinPhieuTruocKhiChinhSua = data;
            data.NGAY_LAP_PHIEU_TEXT_FORMIO = moment(data.NGAY_LAP_PHIEU_TEXT, ['DD/MM/YYYY HH:mm']).toISOString();
            formKeHoachChamSoc.submission = {
                data: data
            };
        });
    }

    function themKeHoachChamSoc(idButton, callBackSuccess, callBackFail) {
        showSelfLoading(idButton);
        formKeHoachChamSoc.emit("checkValidity");
        if (!formKeHoachChamSoc.checkValidity(null, false, null, true)) {
            hideSelfLoading(idButton);
            return;
        }
        var dataForm = formKeHoachChamSoc.submission.data;
        dataForm.NGAY_LAP_PHIEU_TEXT = moment(dataForm.NGAY_LAP_PHIEU_TEXT_FORMIO).format("DD/MM/YYYY HH:mm");
        $.post('cmu_post_CMU_KEHOACHCHAMSOC_INS', {
            url: [
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                thongtinhsba.thongtinbn.STT_BENHAN,
                thongtinhsba.thongtinbn.SOBENHAN,
                thongtinhsba.thongtinbn.MA_BENH_NHAN,
                thongtinhsba.thongtinbn.SOGIUONG,
                dataForm.SO_PHIEU,
                dataForm.NGAY_LAP_PHIEU_TEXT,
                dataForm.NGUOI_LAP_PHIEU,
                singletonObject.makhoa,
                singletonObject.userId,
                dataForm.TINH_TRANG_NGUOI_BENH,
                dataForm.KE_HOACH_CHAM_SOC,
                dataForm.THUC_HIEN_CHAM_SOC,
                dataForm.LUONG_GIA,
                singletonObject.dvtt
            ].join('```')
        }).done(function (data) {
            dataForm.ID = data;
            callBackSuccess && callBackSuccess(dataForm);
            if(data > 0){
                luuLogHSBATheoBN({
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: "KEHOACHCHAMSOC",
                    NOIDUNGBANDAU: "",
                    NOIDUNGMOI: '"Ngày giờ lập phiếu: ' + dataForm.NGAY_LAP_PHIEU_TEXT + '" + "Người lập phiếu: ' + dataForm.NGUOI_LAP_PHIEU +
                        '" + "Số phiếu: ' + dataForm.SO_PHIEU + '" + "Tình trạng người bệnh: ' + dataForm.TINH_TRANG_NGUOI_BENH +
                        '" + "Kế hoạch chăm sóc: ' + dataForm.KE_HOACH_CHAM_SOC + '" + "Thực hiện chăm sóc: ' + dataForm.THUC_HIEN_CHAM_SOC +
                        '" + "Lượng giá: ' + dataForm.LUONG_GIA + '"',
                    USERID: singletonObject.userId,
                    ACTION: "INSERT",
                });
            }
        }).fail(function () {
            callBackFail && callBackFail();
        });
    }

    function suaKeHoachChamSoc(idButton, callBackSuccess, callBackFail) {
        showSelfLoading(idButton);
        formKeHoachChamSoc.emit("checkValidity");
        if (!formKeHoachChamSoc.checkValidity(null, false, null, true)) {
            hideSelfLoading(idButton);
            return;
        }
        var dataForm = formKeHoachChamSoc.submission.data;
        dataForm.NGAY_LAP_PHIEU_TEXT = moment(dataForm.NGAY_LAP_PHIEU_TEXT_FORMIO).format("DD/MM/YYYY HH:mm");
        $.post('cmu_post_CMU_KEHOACHCHAMSOC_UPD', {
            url: [
                dataForm.ID,
                dataForm.SO_PHIEU,
                dataForm.NGAY_LAP_PHIEU_TEXT,
                dataForm.NGUOI_LAP_PHIEU,
                dataForm.TINH_TRANG_NGUOI_BENH,
                dataForm.KE_HOACH_CHAM_SOC,
                dataForm.THUC_HIEN_CHAM_SOC,
                dataForm.LUONG_GIA,
                singletonObject.dvtt,
            ].join('```')
        }).done(function (data) {
            dataForm.TT = data;
            callBackSuccess && callBackSuccess(dataForm);
            if(data > 0){
                let NOIDUNGBANDAU = '"Ngày giờ lập phiếu: ' + thongTinPhieuTruocKhiChinhSua.NGAY_LAP_PHIEU_TEXT + '" + "Người lập phiếu: ' + thongTinPhieuTruocKhiChinhSua.NGUOI_LAP_PHIEU +
                    '" + "Số phiếu: ' + thongTinPhieuTruocKhiChinhSua.SO_PHIEU + '" + "Tình trạng người bệnh: ' + thongTinPhieuTruocKhiChinhSua.TINH_TRANG_NGUOI_BENH +
                    '" + "Kế hoạch chăm sóc: ' + thongTinPhieuTruocKhiChinhSua.KE_HOACH_CHAM_SOC + '" + "Thực hiện chăm sóc: ' + thongTinPhieuTruocKhiChinhSua.THUC_HIEN_CHAM_SOC +
                    '" + "Lượng giá: ' + thongTinPhieuTruocKhiChinhSua.LUONG_GIA + '"';
                let NOIDUNGMOI = '"Ngày giờ lập phiếu: ' + dataForm.NGAY_LAP_PHIEU_TEXT + '" + "Người lập phiếu: ' + dataForm.NGUOI_LAP_PHIEU +
                    '" + "Số phiếu: ' + dataForm.SO_PHIEU + '" + "Tình trạng người bệnh: ' + dataForm.TINH_TRANG_NGUOI_BENH +
                    '" + "Kế hoạch chăm sóc: ' + dataForm.KE_HOACH_CHAM_SOC + '" + "Thực hiện chăm sóc: ' + dataForm.THUC_HIEN_CHAM_SOC +
                    '" + "Lượng giá: ' + dataForm.LUONG_GIA + '"';
                if (NOIDUNGBANDAU !== NOIDUNGMOI) {
                    luuLogHSBATheoBN({
                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                        LOAI: "KEHOACHCHAMSOC",
                        NOIDUNGBANDAU: NOIDUNGBANDAU,
                        NOIDUNGMOI: NOIDUNGMOI,
                        USERID: singletonObject.userId,
                        ACTION: "EDIT",
                    });
                }
                //console.log(thongTinPhieuTruocKhiChinhSua);
                //luuLogHSBAChinhSua(thongTinPhieuTruocKhiChinhSua, dataForm, "KEHOACHCHAMSOC");
            }
        }).fail(function () {
            callBackFail && callBackFail();
        });
    }

    function xoaKeHoachChamSoc(idButton, dataForm) {
        idButton && showSelfLoading(idButton);
        if(dataForm.KEYSIGN){
            notifiToClient("Red", "Phiếu đã ký, không thể xóa");
            idButton && hideSelfLoading(idButton);
            return;
        }
        confirmToClient(MESSAGEAJAX.CONFIRM, function(confirm) {
            $.post('cmu_post_CMU_KEHOACHCHAMSOC_DEL', {
                url: [
                    singletonObject.dvtt,
                    dataForm.ID,
                ].join('```')
            }).done(function (data) {
                if(data > 0){
                    luuLogHSBATheoBN({
                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                        LOAI: "KEHOACHCHAMSOC",
                        NOIDUNGBANDAU: '"Ngày giờ lập phiếu: ' + dataForm.NGAY_LAP_PHIEU_TEXT + '" + "Người lập phiếu: ' + dataForm.NGUOI_LAP_PHIEU +
                            '" + "Số phiếu: ' + dataForm.SO_PHIEU + '" + "Tình trạng người bệnh: ' + dataForm.TINH_TRANG_NGUOI_BENH +
                            '" + "Kế hoạch chăm sóc: ' + dataForm.KE_HOACH_CHAM_SOC + '" + "Thực hiện chăm sóc: ' + dataForm.THUC_HIEN_CHAM_SOC +
                            '" + "Lượng giá: ' + dataForm.LUONG_GIA + '"',
                        NOIDUNGMOI: "",
                        USERID: singletonObject.userId,
                        ACTION: "DELETE",
                    });
                    loadDSKeHoachChamSoc();
                    idButton && hideSelfLoading(idButton);
                } else {
                    notifiToClient("Red", MESSAGEAJAX.FAIL);
                    idButton && hideSelfLoading(idButton);
                }
            }).fail(function () {
                idButton && hideSelfLoading(idButton);
            });
        }, function() {
            idButton && hideSelfLoading(idButton);
        });
    }

    function xemKeHoachChamSoc(idButton, dataForm) {
        idButton && showSelfLoading(idButton);
        var params = {
            ID: dataForm.ID,
        }
        getUrlKeHoachChamSoc(params).then(objReturn => {
            if (objReturn.isError == 0) {
                previewPdfDefaultModal(objReturn.url, 'preview');
            } else {
                notifiToClient("Red", objReturn.message);
            }
        }).catch(error => {
            notifiToClient("Red", error.message || "Lỗi không xác định");
        });
        idButton && hideSelfLoading(idButton);
    }

    function xemKySoKeHoachChamSoc(dataForm) {
        var url = 'cmu_in_cmu_kehoachchamsoc?type=pdf&' + $.param({
            dvtt: singletonObject.dvtt,
            id: dataForm.ID,
            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
        });
        previewAndSignPdfDefaultModal({
            url: url,
            idButton: 'kehoachchamsoc_kyso_action',
        }, function(){
            $("#kehoachchamsoc_kyso_action").click(function() {
                if(dataForm.NGUOI_LAP_PHIEU != singletonObject.userId){
                    notifiToClient("Red", "Phiếu này không phải do bạn thực hiện, không thể ký");
                    return;
                }
                kySoChung({
                    dvtt: singletonObject.dvtt,
                    userId: singletonObject.userId,
                    url: url,
                    loaiGiay: "PHIEU_NOITRU_KEHOACHCHAMSOC",
                    maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                    soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                    soPhieuDichVu: dataForm.ID,
                    soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                    keyword: "Điều dưỡng thực hiện",
                    fileName: "Phiếu kế hoạch chăm sóc: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                }, function(dataKySo) {
                    $("#modalPreviewAndSignPDF").modal("hide");
                    loadDSKeHoachChamSoc();
                });
            });
        });
    }

    function huyKySoKeHoachChamSoc(dataForm) {
        if (singletonObject.userId == dataForm.NGUOI_LAP_PHIEU) {
            confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                huykysoFilesign769("PHIEU_NOITRU_KEHOACHCHAMSOC", dataForm.ID, singletonObject.userId, singletonObject.dvtt,
                    thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                        loadDSKeHoachChamSoc();
                    })
            }, function () {

            })
        } else {
            notifiToClient("Red", "Bạn không có quyền hủy ký số phiếu này");
        }
    }

    $.extend({
        insertMauHSBAKEHOACHCHAMSOC: function () {
            generateFormMauKEHOACHCHAMSOC({})
        },
        editMauHSBAKEHOACHCHAMSOC: function (rowSelect) {
            var json = JSON.parse(rowSelect.NOIDUNG);
            var dataMau = {}
            json.forEach(function(item) {
                dataMau[item.key] = item.value
            })

            generateFormMauKEHOACHCHAMSOC({
                ID: rowSelect.ID,
                TENMAU: rowSelect.TENMAU,
                ...dataMau
            })
        },
        selectMauHSBAKEHOACHCHAMSOC: function (rowSelect) {
            var json = JSON.parse(rowSelect.NOIDUNG);
            var dataMau = {
                ...formKeHoachChamSoc.submission.data,
            }
            json.forEach(function(item) {
                dataMau[item.key] = item.value
            })
            formKeHoachChamSoc.submission = {
                data: {
                    ...dataMau
                }
            }
            $("#modalMauChungJSON").modal("hide");
        },
        getdataMauHSBAKEHOACHCHAMSOC: function () {
            var objectNoidung = [];
            getObjectMauKEHOACHCHAMSOC().forEach(function(item) {
                if(item.key != 'ID' && item.key != 'TENMAU') {
                    if (formioMauHSBA.submission.data[item.key]){
                        objectNoidung.push({
                            "label": item.label,
                            "value": formioMauHSBA.submission.data[item.key],
                            "key": item.key,
                        })
                    }
                }
            })
            return {
                ID: formioMauHSBA.submission.data.ID,
                TENMAU: formioMauHSBA.submission.data.TENMAU,
                NOIDUNG: JSON.stringify(objectNoidung),
                KEYMAUCHUNG: 'MAUKEHOACHCHAMSOC'
            };
        },
        formioHSBAKEHOACHCHAMSOCValidate: function() {
            formioMauHSBA.emit("checkValidity");
            if (!formioMauHSBA.checkValidity(null, false, null, true)) {
                return false;
            }
            return true;
        },
    })
    function generateFormMauKEHOACHCHAMSOC(dataForm) {
        var jsonForm = getJSONObjectForm(getObjectMauKEHOACHCHAMSOC());
        Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
            jsonForm,{}
        ).then(function(form) {
            formioMauHSBA = form;
            formioMauHSBA.submission = {
                data: {
                    ...dataForm
                }
            }
        });
    };
    function getObjectMauKEHOACHCHAMSOC() {
        return [
            {
                "label": "ID",
                "key": "ID",
                "type": "textfield",
                others: {
                    hidden: true
                }
            },
            {
                "label": "Tên mẫu",
                "key": "TENMAU",
                "type": "textarea",
                validate: {
                    required: true
                },
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Tình trạng người bệnh",
                "key": "TINH_TRANG_NGUOI_BENH",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Kế hoạch chăm sóc",
                "key": "KE_HOACH_CHAM_SOC",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Thực hiện chăm sóc",
                "key": "THUC_HIEN_CHAM_SOC",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Lượng giá",
                "key": "LUONG_GIA",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
        ];
    };
})



