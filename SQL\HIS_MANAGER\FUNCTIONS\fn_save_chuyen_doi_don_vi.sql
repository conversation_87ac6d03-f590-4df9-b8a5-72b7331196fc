CREATE OR REPLACE
FUNCTION his_manager.fn_save_chuyen_doi_don_vi (
  i_json CLOB
) RETURN NUMBER IS -- return number: 1 is true and 0 is false
  flag NUMBER := 0;
  v_json his_ytcs.JSON;
  v_id NUMBER;
  v_madonvi VARCHAR2(200);
  v_madonvi_moi NUMBER;
  v_ngay_hieu_luc VARCHAR2(12);
  v_ngay_het_hieu_luc VARCHAR2(12);
  v_ghi_chu VARCHAR2(20);
  v_kt NUMBER := 0; -- define variable new cmt type is number to save data
BEGIN
  v_json := his_ytcs.json(i_json);
  v_id := TO_NUMBER(v_json.get('ID').str);
  v_madonvi := v_json.get('MADONVI').str;
  v_madonvi_moi := v_json.get('MADONVI_MOI').str;
  v_ngay_hieu_luc := v_json.get('NGAY_HIEU_LUC').str;
  v_ngay_het_hieu_luc := v_json.get('NGAY_HET_HIEU_LUC').str;
  v_ghi_chu := v_json.get('GHI_CHU').str;



  IF v_id IS NULL OR v_id = '' THEN -- if goi_kham_id does not exist or is 0, then ...
select count(1) into v_kt from his_manager.dm_donvi_chuyen_doi
where (v_ngay_hieu_luc between ngay_hieu_luc and nvl(ngay_het_hieu_luc, '99991231') )
   or (v_ngay_het_hieu_luc is not null and nvl(ngay_het_hieu_luc, '99991231') between ngay_hieu_luc and ngay_het_hieu_luc) ;
if v_kt > 0 then return 2; end if;
INSERT INTO his_manager.dm_donvi_chuyen_doi dm
    (
      dm.madonvi,
      dm.madonvi_moi,
      dm.ngay_hieu_luc,
      dm.ngay_het_hieu_luc,
      dm.ghi_chu
    )
VALUES
    (
    v_madonvi,
    v_madonvi_moi,
    v_ngay_hieu_luc,
    v_ngay_het_hieu_luc,
    v_ghi_chu
    );

IF SQL%ROWCOUNT = 1 THEN
      -- Nếu có 1 hàng bị ảnh hưởng, trả về 1 (true)
      flag := 1;
ELSE
      -- Nếu không có hàng nào bị ảnh hưởng, trả về 0 (false)
      flag := 0;
END IF;
ELSE
select count(1) into v_kt from his_manager.dm_donvi_chuyen_doi
where ((v_ngay_hieu_luc between ngay_hieu_luc and nvl(ngay_het_hieu_luc, '99991231') )
    or (v_ngay_het_hieu_luc is not null and nvl(ngay_het_hieu_luc, '99991231') between ngay_hieu_luc and ngay_het_hieu_luc))
  and id <> v_id;

if v_kt > 0 then return 2; end if;

UPDATE his_manager.dm_donvi_chuyen_doi dm
SET
    dm.madonvi = v_madonvi,
    dm.madonvi_moi = v_madonvi_moi,
    dm.ngay_hieu_luc = v_ngay_hieu_luc,
    dm.ngay_het_hieu_luc = v_ngay_het_hieu_luc,
    dm.ghi_chu = v_ghi_chu
WHERE dm.id = v_id;

IF SQL%ROWCOUNT = 1 THEN
      -- Nếu có 1 hàng bị ảnh hưởng, trả về 1 (true)
      flag := 1;
ELSE
      -- Nếu không có hàng nào bị ảnh hưởng, trả về 0 (false)
      flag := 0;
END IF;
END IF;

RETURN flag;

END;
/