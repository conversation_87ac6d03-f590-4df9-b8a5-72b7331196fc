<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rp_phieuhoasinhmau" pageWidth="595" pageHeight="842" columnWidth="575" leftMargin="0" rightMargin="20" topMargin="0" bottomMargin="0" uuid="d89332f3-0586-4386-a9aa-17cd29b46a69">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="42"/>
	<parameter name="madonvi" class="java.lang.String"/>
	<parameter name="makhambenh" class="java.lang.String"/>
	<parameter name="tenbenhvien" class="java.lang.String"/>
	<parameter name="hoten" class="java.lang.String"/>
	<parameter name="tuoi" class="java.lang.String"/>
	<parameter name="gioitinh" class="java.lang.String"/>
	<parameter name="diachi_bn" class="java.lang.String"/>
	<parameter name="chandoan" class="java.lang.String"/>
	<parameter name="ngayxetnghiem" class="java.lang.String"/>
	<parameter name="bsdieutri" class="java.lang.String"/>
	<parameter name="sophieuthanhtoan" class="java.lang.String"/>
	<parameter name="noitru" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_benhan" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_dotdieutri" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_dieutri" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="sophieuxn" class="java.lang.String"/>
	<parameter name="capcuu" class="java.lang.String"/>
	<parameter name="thuong" class="java.lang.String"/>
	<parameter name="sothebaohiem" class="java.lang.String"/>
	<parameter name="ngaydieutri" class="java.lang.String"/>
	<parameter name="tensoyte" class="java.lang.String"/>
	<parameter name="khoa" class="java.lang.String"/>
	<parameter name="giuong" class="java.lang.String"/>
	<parameter name="buong" class="java.lang.String"/>
	<parameter name="hthi" class="java.lang.String"/>
	<parameter name="mabenhnhan" class="java.lang.String"/>
	<parameter name="loaixetnghiem" class="java.lang.String"/>
	<parameter name="bsphutrach" class="java.lang.String"/>
	<parameter name="maxetnghiem" class="java.lang.String"/>
	<parameter name="soyte" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call CMU_INPHIEUXETNGHIEM_V2($P{madonvi},$P{sophieuxn},$P{noitru},$P{makhambenh},$P{stt_benhan},$P{stt_dotdieutri},$P{stt_dieutri},'TC',$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="dvtt" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="sophieu" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="noitru" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="mota_loai_xetnghiem" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ten_xetnghiem" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="dvnghiepvu_xetnghiem" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="chisobinhthuong" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ket_qua" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ket_luan_tong" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="nguoi_lay_mau" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ngay_lay_mau" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="sapxep" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="nhom_xetnghiem" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="TT" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="HH_ABO" class="java.lang.String"/>
	<field name="HH_Rh" class="java.lang.String"/>
	<field name="nguoi_chi_dinh" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="tuoi" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="kq_ABO" class="java.lang.String"/>
	<field name="kq_Rh" class="java.lang.String"/>
	<field name="NGAYGIOLAYMAU" class="java.lang.String"/>
	<field name="NGAYCHIDINH" class="java.lang.String"/>
	<field name="giolaymau" class="java.lang.String"/>
	<field name="BINHTHUONG" class="java.lang.String"/>
	<field name="NGAYTRAKETQUA" class="java.lang.String"/>
	<field name="thamsocmu" class="java.lang.String"/>
	<field name="NGUOI_THUC_HIEN" class="java.lang.String"/>
	<field name="nguoilaymau_cmu" class="java.lang.String"/>
	<field name="nguoigiaomau_cmu" class="java.lang.String"/>
	<field name="benhpham_cmu" class="java.lang.String"/>
	<field name="solanin" class="java.lang.Integer"/>
	<field name="TEN_MAY_XN" class="java.lang.String"/>
	<field name="CANHBAO_LAMSANG" class="java.lang.String"/>
	<field name="ngthuchien_in" class="java.lang.String"/>
	<field name="ngaylaynhanmau" class="java.lang.String"/>
	<field name="ANCHUKY" class="java.lang.String"/>
	<variable name="count page" class="java.lang.Integer" resetType="Page" calculation="DistinctCount">
		<variableExpression><![CDATA[$V{PAGE_NUMBER}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="201" splitType="Stretch">
			<textField>
				<reportElement x="47" y="85" width="231" height="22" forecolor="#000000" uuid="285042d4-0bcf-48ca-8031-495e8fbb04bc"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{diachi_bn}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="1" y="129" width="58" height="21" forecolor="#000000" uuid="7ac2bc33-3762-4311-8d0c-44f2393f9d05"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Chuẩn đoán:"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="205" y="0" width="244" height="20" forecolor="#000000" uuid="e3810550-53d4-492f-ab3a-de619f5dc853"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="16" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[PHIẾU KẾT QUẢ XÉT NGHIỆM]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="279" y="85" width="66" height="22" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="eaafb2a7-2615-49c8-8844-5ec302a2dadb"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Số thẻ BHYT:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="1" y="86" width="46" height="21" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="da09f868-cb6b-457e-bfe3-9a9681738bf6"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Địa chỉ:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="428" y="84" width="25" height="23" forecolor="#000000" uuid="e9d461de-47df-42a6-8b41-be5a0c91b9fe"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("") ? $P{sothebaohiem}.substring( 05, 07) : $P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="0" y="20" width="205" height="20" forecolor="#000000" uuid="c89cd0dc-1d72-437d-b26e-2ba12ec5495d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenbenhvien}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="429" y="62" width="47" height="21" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="7db83d02-b274-4612-932a-96175789d5a4"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Giới tính:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="385" y="63" width="44" height="21" forecolor="#000000" uuid="a2ccb9cc-724f-474c-a999-42e78edb006b"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tuoi}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="345" y="84" width="25" height="23" forecolor="#000000" uuid="fa82b88a-26fb-4881-b6b0-08414bdf1059"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("") ? $P{sothebaohiem}.substring( 00, 02 ) : $P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="1" y="63" width="91" height="21" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="87edf812-d0d6-47ad-8e04-4e5e0f75f444"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Họ tên người bệnh:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" mode="Transparent" x="223" y="42" width="46" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF" uuid="50b28154-7a38-4469-8c62-7303eaae832f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Thường"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="449" y="108" width="46" height="21" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="9907046e-1021-4292-9573-34bd591885c1"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Giường"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="400" y="84" width="25" height="23" forecolor="#000000" uuid="f78242ff-1f53-4dd3-bc2c-a0364e589aff"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("") ? $P{sothebaohiem}.substring( 03, 05 ) : $P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="356" y="63" width="29" height="21" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="ae5e2aa4-049f-4c31-8257-6239e53e2734"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Tuổi:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="1" y="108" width="46" height="21" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="aefbd0ad-9d78-4b34-bcbd-c524003b7fa3"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Khoa:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" mode="Transparent" x="316" y="42" width="46" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF" uuid="4a0dff4d-e62c-47c0-b72c-11d0ecc62b6e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Cấp cứu"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="476" y="63" width="62" height="20" forecolor="#000000" uuid="aebee0e2-d671-45a9-bcc1-64fb61df2eed"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{gioitinh}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="205" height="20" forecolor="#000000" uuid="bd730e2d-d34d-4e47-9e11-ce18cd04c6bf"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{soyte}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="47" y="108" width="210" height="21" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="307a4833-3b52-4654-856b-0c9c21d499c9"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{khoa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="457" y="84" width="34" height="23" forecolor="#000000" uuid="42ee5d5a-8d6a-40b6-9b37-0901efa0dd2f"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("") ? $P{sothebaohiem}.substring( 07, 10 ) : $P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="92" y="64" width="262" height="20" forecolor="#000000" uuid="b9f1bae0-1e2f-4a35-b117-b0fda19da246"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{hoten}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="372" y="84" width="25" height="23" forecolor="#000000" uuid="f8ad364a-55bc-4a17-86d5-641329a39eca"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("") ? $P{sothebaohiem}.substring( 02, 03 ) : $P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="366" y="108" width="33" height="21" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="cc52bb14-c7d4-48f7-a53e-e384cb5e05ca"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Buồng:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="495" y="84" width="41" height="23" forecolor="#000000" uuid="c20670a9-7b9d-466d-8d77-bdc9acbb0ef2"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("") ? $P{sothebaohiem}.substring( 10, 15 ) : $P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="59" y="129" width="477" height="21" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="336a36f4-4021-41da-abb1-4016b20f6795">
					<printWhenExpression><![CDATA[!$P{madonvi}.equals("96170")]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{chandoan}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="270" y="42" width="29" height="20" forecolor="#000000" uuid="895911bb-c683-4ac5-a8c5-87f2fb3766d2"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{capcuu}.equals("1")? "": "X"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="362" y="42" width="29" height="20" forecolor="#000000" uuid="6338c515-85db-4af1-8d15-eb2be6c3bc6f"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{capcuu}.equals("1")? "X": ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="470" y="29" width="75" height="11" forecolor="#000000" uuid="d1dff7e7-6a78-478b-b726-50c3ff8269ed"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{mabenhnhan}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="473" y="0" width="70" height="11" forecolor="#000000" uuid="e82f5b8e-51ba-4479-b84d-cfa2e864b7d8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Mã bệnh nhân]]></text>
			</staticText>
			<componentElement>
				<reportElement x="478" y="11" width="60" height="18" forecolor="#000000" uuid="526ef2a3-762d-42b0-9340-3a4fa99bb31a"/>
				<jr:barbecue xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" type="Code128" drawText="false" checksumRequired="true" barHeight="45">
					<jr:codeExpression><![CDATA[$P{mabenhnhan}]]></jr:codeExpression>
					<jr:applicationIdentifierExpression><![CDATA[$P{mabenhnhan}]]></jr:applicationIdentifierExpression>
				</jr:barbecue>
			</componentElement>
			<textField isBlankWhenNull="true">
				<reportElement x="495" y="108" width="42" height="20" forecolor="#000000" uuid="abe5ab8a-01c5-46a9-82f3-a6439a882c19"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{giuong}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="399" y="108" width="50" height="20" forecolor="#000000" uuid="8ef3d873-8b19-4433-a20e-54146285840e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{buong}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="1" y="150" width="105" height="17" forecolor="#000000" uuid="052b5468-d351-4154-9d1d-75154b693e6c"/>
				<box leftPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Bác sĩ điều trị:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="372" y="184" width="164" height="17" forecolor="#000000" uuid="556b3a7d-25a2-4dc0-8352-e74e8e80dc1b">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nguoigiaomau_cmu}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="1" y="167" width="105" height="17" forecolor="#000000" uuid="cd5acc7d-50e7-475b-86f4-0bb25e032231"/>
				<box leftPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Ngày giờ nhận mẫu:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="106" y="150" width="431" height="17" forecolor="#000000" uuid="ebfd054d-abc2-4e1d-83fe-4695cc5ca92b">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nguoi_chi_dinh}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="269" y="184" width="103" height="17" forecolor="#000000" uuid="85545f0b-f9d3-4c3e-b2ef-38cc26a6045a"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Người giao mẫu:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="0" y="184" width="106" height="17" forecolor="#000000" uuid="579b4163-21db-4dde-86c6-4154429b4a83"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Người nhận mẫu:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="106" y="184" width="163" height="17" forecolor="#000000" uuid="31668449-6be3-4065-a73e-b7fe1ca2429c">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nguoilaymau_cmu}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="257" y="108" width="59" height="21" forecolor="#000000" uuid="efc8e30e-bda6-4bf7-9646-253dd0003edc"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Bệnh phẩm:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="106" y="167" width="163" height="17" forecolor="#000000" uuid="b2a94143-04e1-4e4c-a279-ff9cccd3c980">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ngay_lay_mau}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="316" y="108" width="49" height="21" forecolor="#000000" uuid="8ade1bd0-4731-436a-a534-2b8f7f8c83d3">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{benhpham_cmu}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="2" y="40" width="90" height="24" isRemoveLineWhenBlank="true" uuid="c7cf1c2e-0b31-4ac1-8da8-18f0a021a611"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Dashed"/>
					<topPen lineWidth="1.0" lineStyle="Dashed"/>
					<leftPen lineWidth="1.0" lineStyle="Dashed"/>
					<bottomPen lineWidth="1.0" lineStyle="Dashed"/>
					<rightPen lineWidth="1.0" lineStyle="Dashed"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{maxetnghiem}.equals( "1" )?"":$F{solanin}<=1 ?"":"BẢN SAO"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="269" y="167" width="103" height="17" forecolor="#000000" uuid="2d1e2865-28e7-4182-ab44-19f9d127e335"/>
				<box leftPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày giờ lấy mẫu:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="372" y="167" width="156" height="17" forecolor="#000000" uuid="f84f6d6f-978d-49c2-997d-cc8cc3d478e0">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ngaylaynhanmau}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="20" splitType="Stretch">
			<staticText>
				<reportElement x="270" y="0" width="106" height="20" forecolor="#000000" uuid="0c73c465-d9d7-44bc-b5f2-1f3d61c15210"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[KHOẢNG THAM CHIẾU]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="175" height="20" forecolor="#000000" uuid="1322994e-3941-45fc-9759-77844e52463c"/>
				<box topPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[XÉT NGHIỆM]]></text>
			</staticText>
			<staticText>
				<reportElement x="376" y="0" width="73" height="20" forecolor="#000000" uuid="d13f5a47-fcf5-4544-a192-a9d623388630"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[ĐƠN VỊ]]></text>
			</staticText>
			<staticText>
				<reportElement x="449" y="0" width="87" height="20" forecolor="#000000" uuid="34d8e127-3501-4234-a153-71fc427c10d6"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[THIẾT BỊ]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="175" y="0" width="95" height="20" forecolor="#000000" uuid="009a2144-9a9d-400a-a020-d00e4e1d0fac"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[KẾT QUẢ	]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="270" y="0" width="106" height="20" forecolor="#000000" uuid="03c971f4-3082-4cb1-9f4e-a1a291040cd9"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{chisobinhthuong}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="376" y="0" width="73" height="20" forecolor="#000000" uuid="d173a7b4-deb0-476a-a36b-053937e159fa"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dvnghiepvu_xetnghiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="175" height="20" forecolor="#000000" uuid="374b4afc-25ad-4f17-959d-427a87882cdc"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ten_xetnghiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="449" y="0" width="87" height="20" uuid="3a52d3c3-0fe4-4c37-b3b7-44dcd7eefeba"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_MAY_XN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="175" y="0" width="95" height="20" forecolor="#000000" uuid="733365fc-ae9f-49e1-98fc-acdec78bdc6a"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="12" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BINHTHUONG}.equals("-1") || $F{BINHTHUONG}.equals("0")? "<style size='11' isBold='true' pdfFontName='Times New Roman'>"+$F{ket_qua}+"</style>":$F{ket_qua}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="240" y="0" width="30" height="20" forecolor="#000000" uuid="0dde6842-4f4a-4361-93c5-f6c4460c5fa7"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
					<rightPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="true" isUnderline="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{BINHTHUONG}.equals("0") && !$P{madonvi}.equals("96177") ? "(H)":"")+
($F{CANHBAO_LAMSANG}.equals("1") ?"*":"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="175" y="0" width="30" height="20" forecolor="#000000" uuid="374d8d07-d62c-4f6c-a347-1d85af7b5529"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.0" lineStyle="Dashed"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="true" isUnderline="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BINHTHUONG}.equals("-1")? "(L)":""]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<lastPageFooter>
		<band height="15">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="1" y="0" width="91" height="15" uuid="2c628054-3b3c-48c9-ba6c-cb32aaa56d89"/>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang "+$V{PAGE_NUMBER}+"/"+$V{count page}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="455" y="0" width="79" height="15" isRemoveLineWhenBlank="true" uuid="d372979c-a62d-4034-9fa2-c739a3811cc1"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{maxetnghiem}.equals( "1" )?"":$F{solanin}<=1 ?"":"In lần "+($F{solanin}-1)]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="162" y="0" width="254" height="15" forecolor="#000000" uuid="94037a59-2793-4a8e-81d9-800ed2b4c950"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{maxetnghiem}.equals( "1" )?"":$F{solanin}<=1 ?"":"Người in bản sao: "+ $P{bsphutrach}]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
	<summary>
		<band height="100" splitType="Stretch">
			<textField>
				<reportElement x="310" y="2" width="224" height="15" forecolor="#000000" uuid="610c8ba6-f897-422f-a29a-d4d496ae7506"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isItalic="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYTRAKETQUA}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="310" y="17" width="226" height="20" forecolor="#000000" uuid="e1be60cc-4c87-4f29-a348-551840480a93"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dvtt}.equals("96014") ? "TRƯỞNG KHOA XÉT NGHIỆM" : "KÝ DUYỆT KẾT QUẢ"]]></textFieldExpression>
			</textField>
			<break>
				<reportElement x="0" y="99" width="575" height="1" uuid="7acdb957-13a2-4236-a686-c0b847b92735"/>
			</break>
			<textField isBlankWhenNull="true">
				<reportElement x="1" y="0" width="258" height="17" forecolor="#000000" uuid="1ded92d0-7f86-4d39-94f9-dc6d57b7ded3"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Người thực hiện: "+ $F{ngthuchien_in}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="75" y="47" width="184" height="15" uuid="df1c93c7-66b1-4e88-93fd-cb4ff43b34cd"/>
				<box>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="75" y="17" width="184" height="15" uuid="0f923446-3e6e-40f1-98e1-0ab1b4a13386"/>
				<box>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="2" y="32" width="74" height="15" forecolor="#000000" uuid="3f0d546d-fe63-4f9a-9da5-e7abc7257869"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Ý kiến bác sĩ:"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="75" y="32" width="184" height="15" uuid="a072f690-5371-495c-a780-01e5bcc8dad4"/>
				<box>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="2" y="17" width="73" height="15" forecolor="#000000" uuid="25be253f-306f-4cf6-9b3e-1c1756445f5a"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Lưu ý: "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="312" y="57" width="226" height="20" forecolor="#000000" uuid="e42be1d7-cb0a-4722-bcaa-6b80a5165f9f">
					<printWhenExpression><![CDATA[$F{ANCHUKY}.equals("0")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGUOI_THUC_HIEN}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement x="24" y="62" width="128" height="20" uuid="28d47b6e-5279-4d74-bd56-2b87067ca187"/>
				<staticText>
					<reportElement x="0" y="0" width="128" height="20" forecolor="#FFFFFF" uuid="fc8f5201-5a93-4cfd-8fb0-5c8e818176e8"/>
					<textElement>
						<font fontName="Times New Roman"/>
					</textElement>
					<text><![CDATA[KYXEMKQ]]></text>
				</staticText>
			</frame>
		</band>
	</summary>
</jasperReport>
