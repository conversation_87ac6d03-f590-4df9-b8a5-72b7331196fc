create or replace PROCEDUR<PERSON> "HIS_MANAGER"."NOITRU_CDDV_HT_LUOI_DACHON" ( p_BHYT_KOCHI IN varchar2 , p_DVTT IN varchar2 ,
 p_SO_PHIEU IN varchar2 , p_<PERSON><PERSON><PERSON><PERSON> IN varchar2 , p_MACHUYENKHOA IN varchar2 , p_MACHITIET IN varchar2, cur OUT SYS_REFCURSOR )IS
 v_CHITIET_CHUYENKHOA NVARCHAR2(250);
 v_TT_KIEMTRA NUMBER(10) DEFAULT 0;
 p_tenht number(1) default 0;
 p_stt_hangngay number default 0;
  V_THAMSO_94301 VARCHAR2(1) DEFAULT '0';  -- Sang STG Them
  -- VNPTHIS-3379 thêm
  V_STG_TUOI VARCHAR(100) DEFAULT ' ';
  -- VNPTHIS-3379 thêm
  -- HGI TGGDEV-28223 10/04/2018 thêm
    V_THAMSO_93130 VARCHAR2(1) DEFAULT '0';
    v_ngay_sinh date default sysdate;
    v_ngay_nhapvien date default sysdate;
  -- <PERSON><PERSON> TGGDEV-28223 10/04/2018 thêm
  v_thamso208 number(1) default '0';
  p_ngaytao date;
     v_thamso_820322 number(10) default 0;
  v_capcuu_cls number(1) default '0';
  v_thamso820900 number(1) default '0';
  v_thamso960616 number(10) := cmu_tsdv(p_DVTT, 960616, 0);
BEGIN
begin
select mota_thamso into v_thamso_820322 FROM HIS_FW.DM_THAMSO_DONVI where dvtt=p_dvtt and ma_thamso=820322;
exception when no_data_found then
        v_thamso_820322:='0';
end;
begin
select mota_thamso into v_thamso208 FROM HIS_FW.DM_THAMSO_DONVI where dvtt=p_dvtt and ma_thamso = 208;
exception when no_data_found then
        v_thamso208:='0';
end;
  -- Sang VNPT STG thêm cho Vĩnh Phúc: Cho phép in phiếu chỉ định của TTPT hoặc loại TTPT đã ngưng hoạt động thông qua tham số 94301

    -- Sang STG Them Begin 1
BEGIN
SELECT NVL(MOTA_THAMSO, '0') INTO V_THAMSO_94301
FROM HIS_FW.DM_THAMSO_DONVI
WHERE MA_THAMSO = 94301
  and dvtt=p_DVTT;
EXCEPTION WHEN NO_DATA_FOUND THEN V_THAMSO_94301 := '0';
END;
  -- Sang STG Them End 1
  -- HGI TGGDEV-28223 10/04/2018 thêm
BEGIN
SELECT NVL(MOTA_THAMSO, '0') INTO V_THAMSO_93130
FROM HIS_FW.DM_THAMSO_DONVI
WHERE MA_THAMSO = 93130 AND DVTT = p_DVTT;
EXCEPTION WHEN NO_DATA_FOUND THEN V_THAMSO_93130 := '0';
END;
    -- HGI TGGDEV-28223 10/04/2018 thêm
-- VNPTHIS-3379 thêm -- HGI TGGDEV-28223 10/04/2018 lấy thêm ngày sinh, ngày nhập viện
SELECT his_manager.stg_tuoi_te_f(to_char(bn.ngay_sinh,'yyyy-mm-dd'), to_char(trunc(ba.ngaynhapvien),'yyyy-mm-dd')),
       bn.ngay_sinh, ba.ngaynhapvien
INTO V_STG_TUOI, v_ngay_sinh, v_ngay_nhapvien
FROM his_public_list.dm_benh_nhan bn,
     his_manager.noitru_benhan ba,
     his_manager.noitru_cd_dichvu_CT cd
WHERE ba.dvtt = p_DVTT
  AND cd.SO_PHIEU_DICHVU = p_SO_PHIEU
  AND ba.dvtt = cd.dvtt
  AND ba.stt_benhan = cd.stt_benhan
  AND ba.mabenhnhan = bn.ma_benh_nhan
  AND ROWNUM = 1;
-- VNPTHIS-3379 thêm
if (p_MACHITIET = 1)
  THEN
         v_CHITIET_CHUYENKHOA := 'Các kỹ thuật về răng miệng';
     elsif (p_MACHITIET = 2)
  THEN
     v_CHITIET_CHUYENKHOA := 'Điều trị răng';
   elsif (p_MACHITIET = 3)
  THEN
     v_CHITIET_CHUYENKHOA := 'Các phẩu thuật thủ thuật hàm mặt';
END IF;
IF p_BHYT_KOCHI = 0 THEN
    v_TT_KIEMTRA := 0;
ELSE
    v_TT_KIEMTRA := 1;
END IF;
  -- AGG le select stt_hangngay
BEGIN
SELECT cd.stt_hangngay, trunc(cd.ngay_tao)
INTO p_stt_hangngay, p_ngaytao
FROM his_manager.noitru_cd_dichvu cd
WHERE cd.dvtt = p_DVTT
  and cd.so_phieu_dichvu = p_SO_PHIEU;
EXCEPTION
    WHEN NO_DATA_FOUND THEN
      p_stt_hangngay := '0';
END;
  -- end AGG Le
begin
select mota_thamso
into p_tenht
from his_fw.dm_thamso_donvi
where dvtt = p_dvtt
  and ma_thamso = 31016;
exception
    when NO_DATA_FOUND THEN
      p_tenht := '0';
end;

begin
select mota_thamso into v_capcuu_cls FROM HIS_FW.DM_THAMSO_DONVI where dvtt=p_dvtt and ma_thamso = 820877;
exception when no_data_found then
        v_capcuu_cls := '0';
end;
   --thêm tham số hiển thị số thứ tự trên phiếu chỉ định
begin
select mota_thamso into v_thamso820900 FROM HIS_FW.DM_THAMSO_DONVI where dvtt=p_dvtt and ma_thamso = 820900;
exception when no_data_found then
        v_thamso820900 := '0';
end;

  if p_tenht = 1 then
    IF p_MALOAI not in ('PT','TT') THEN
      OPEN cur FOR SELECT rownum as STT, DV.MA_DV as MA_DV,
                          DV. LOAI_DV as LOAI_DV,
                          case when dv.ten_hien_thi != ' ' then dv.ten_hien_thi else --DV.TEN_DV
                              case when v_thamso208 in (0,4) and SUDUNG_TT37=0 and trunc(p_ngaytao) >= to_date('2018-07-15','yyyy-mm-dd') then
                                       case when NVL(TEN_TT15,' ')=' ' then DV.TEN_DV else TEN_TT15 end else DV.TEN_DV end
                                   end as TEN_DV,
                          DV. DVT_DV as DVT_DV,
                          LOAI. TEN_LOAI_DICH_VU as TEN_LOAI_DV,
                          --NVL(ct.SO_LUONG,1) AS SO_LUONG,
                          --CT.DON_GIA as GIA_DV,
                          case when v_thamso_820322=0 and ct.TRANG_THAI_CUNG_KIP = 1 then NVL(ct.SO_LUONG,1) else NVL(ct.SO_LUONG,1)*100/ct.TILE_THANHTOAN end AS SO_LUONG,
                          case when v_thamso_820322=0 and ct.TRANG_THAI_CUNG_KIP = 1 then CT.DON_GIA else CT.DON_GIA*(ct.TILE_THANHTOAN/100) end as GIA_DV,
                          NVL(ct.SO_LUONG,1) * NVL(CT.DON_GIA,0)AS THANH_TIEN,
                          (CASE WHEN nvl(ct.BHYTKCHI,1) = 0  THEN 1 ELSE 0 END) as  CHON,
                          DV.TRANGTHAI as hoatdong,
                          p_BHYT_KOCHI AS BHYT_KOCHI,
                          -- VNPTHIS-3379 thêm
                          V_STG_TUOI as tuoi,
                          TO_CHAR(v_ngay_nhapvien, 'dd/MM/yyyy') NGAY_NHAP_VIEN
                          -- VNPTHIS-3379 thêm
                                ,p_stt_hangngay as STT_HANGNGAY
                          -- ghi chu TGGDEV-35203
                                ,ct.ghichu_chidinh as ghichu,
                          to_char(cd.ngay_chi_dinh, 'HH24') || ' ' || ' ' || 'giờ' || ' ' ||
                          to_char(cd.ngay_chi_dinh, 'mi') || ' ' || 'phút' || ' ' || 'ngày' || ' ' ||
                          to_char(cd.ngay_chi_dinh, 'DD') || ' ' || 'tháng' || ' ' ||
                          to_char(cd.ngay_chi_dinh, 'MM') || ' ' || 'năm' || ' ' ||
                          to_char(cd.ngay_chi_dinh, 'YYYY') as NGAY_CHI_DINH,
                          --BDH thêm loại dv đơn vị 82008
                          (CASE WHEN DV.LOAI_DV = 'NS' THEN 'NS' ELSE ' ' END) AS LOAI
                                ,case when v_capcuu_cls = 1 then cd.capcuu else dot.capcuu end as thuong_capcuu  -- TGGDEV-38285 thêm TGGDEV-48254
                                ,(ba.icd_nhapvien || '  ' || ba.tenbenhchinh_nhapvien) as benhchinh
                                , v_thamso820900 as HIENTHI
                                , v_thamso960616 ANCHUKY
                   FROM  DM_DICH_VU_KHAM DV, noitru_cd_dichvu_CT ct , noitru_cd_dichvu cd, dm_loai_dich_vu_kham LOAI ,
                         noitru_dotdieutri dot -- TGGDEV-38285 thêm
                             left join noitru_benhan ba
                                       on dot.stt_benhan = ba.stt_benhan
                                           and dot.dvtt = ba.dvtt
                                           and dot.sovaovien = ba.sovaovien
                   WHERE  DV.dvtt = p_DVTT and ct.DVTT = p_DVTT and ct.SO_PHIEU_DICHVU = p_SO_PHIEU and DV.DVTT = ct.DVTT and DV. MA_DV = ct. MA_DV
                     AND DV.dvtt = p_DVTT
                     AND LOAI.dvtt = p_DVTT
                     and cd.dvtt = p_dvtt
                     and cd.so_phieu_dichvu = ct.so_phieu_dichvu
                     and cd.sovaovien = ct.sovaovien
                     and cd.sovaovien_dt = ct.sovaovien_dt
                     AND (LOAI.hoatdong = 1 OR V_THAMSO_94301 = '1') -- Sang STG Sửa, code gốc (AND LOAI.hoatdong = 1)
                     AND (DV.TRANGTHAI = 1 OR V_THAMSO_94301 = '1')  -- Sang STG Sửa, code gốc (AND DV.TRANGTHAI = 1)
                     AND DV.TT_BHYT_CHI != v_TT_KIEMTRA
      AND DV. LOAI_DV = LOAI. MA_LOAI_DICH_VU
      AND DV.LOAI_DV = p_MALOAI
      -- TGGDEV-38285 thêm
       and ct.dvtt = dot.dvtt
       and ct.stt_benhan = dot.stt_benhan
       and ct.stt_dotdieutri = dot.stt_dotdieutri
       and dot.dvtt = p_dvtt
       -- TGGDEV-38285 end thêm , noitru_dotdieutri dot -- TGGDEV-38285 thêm
                   order by DV.TEN_DV;
ELSE
      IF p_MACHUYENKHOA = '0' AND p_MACHITIET = '0' THEN
        OPEN cur FOR SELECT rownum as STT, DV.MA_DV as MA_DV,
                            DV. LOAI_DV as LOAI_DV,
                            case when dv.ten_hien_thi != ' ' then dv.ten_hien_thi else --DV.TEN_DV
                                case when v_thamso208 in (0,4) and SUDUNG_TT37=0 and trunc(p_ngaytao) >= to_date('2018-07-15','yyyy-mm-dd') then
                                         case when NVL(TEN_TT15,' ')=' ' then DV.TEN_DV else TEN_TT15 end else DV.TEN_DV end
                                end as TEN_DV,
                            DV. DVT_DV as DVT_DV,
                            LOAI. TEN_LOAI_DICH_VU as TEN_LOAI_DV,
                            --NVL(ct.SO_LUONG,1) AS SO_LUONG,
                            --CT.DON_GIA as GIA_DV,
                            case when v_thamso_820322=0 and ct.TRANG_THAI_CUNG_KIP = 1 then NVL(ct.SO_LUONG,1) else NVL(ct.SO_LUONG,1)*100/ct.TILE_THANHTOAN end AS SO_LUONG,
                            case when v_thamso_820322=0 and ct.TRANG_THAI_CUNG_KIP = 1 then CT.DON_GIA else CT.DON_GIA*(ct.TILE_THANHTOAN/100) end as GIA_DV,
                            NVL(ct.SO_LUONG,1) * NVL(CT.DON_GIA,0)AS THANH_TIEN,
                            (CASE WHEN nvl(ct.BHYTKCHI,1) = 0  THEN 1 ELSE 0 END) as  CHON,
                            DV.TRANGTHAI as hoatdong,
                            p_BHYT_KOCHI AS BHYT_KOCHI,
                            -- VNPTHIS-3379 thêm
                            V_STG_TUOI as tuoi,
                            TO_CHAR(v_ngay_nhapvien, 'dd/MM/yyyy') NGAY_NHAP_VIEN
                            -- VNPTHIS-3379 thêm
                             ,p_stt_hangngay as STT_HANGNGAY,
                            -- HGI TGGDEV-28223 10/04/2018 thêm
                            CASE V_THAMSO_93130
                                WHEN '1' THEN his_manager.HGI_HIENTHI_TUOI_BENHNHAN(v_ngay_sinh, v_ngay_nhapvien, p_DVTT)
                                WHEN '0' THEN to_char(v_ngay_sinh,'yyyy')
                                WHEN '2' THEN to_char(v_ngay_sinh,'dd/mm/yyyy')
                                END as ngaytuoihgi,
                            V_THAMSO_93130 tshtngaytuoi
                            -- HGI TGGDEV-28223 10/04/2018 thêm
                            -- ghi chu TGGDEV-35203
                             ,ct.ghichu_chidinh as ghichu,
                            to_char(cd.ngay_chi_dinh, 'HH24') || ' ' || ' ' || 'giờ' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'mi') || ' ' || 'phút' || ' ' || 'ngày' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'DD') || ' ' || 'tháng' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'MM') || ' ' || 'năm' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'YYYY') as NGAY_CHI_DINH,
                            --BDH thêm loại dv đơn vị 82008
                            (CASE WHEN DV.LOAI_DV = 'NS' THEN 'NS' ELSE ' ' END) AS LOAI
                             , case when v_capcuu_cls = 1 then cd.capcuu else dot.capcuu end as thuong_capcuu  -- TGGDEV-38285 thêm TGGDEV-48254
                             ,(ba.icd_nhapvien || '  ' || ba.tenbenhchinh_nhapvien) as benhchinh
                             , v_thamso820900 as HIENTHI
                             , v_thamso960616 ANCHUKY
                     FROM  DM_DICH_VU_KHAM DV, noitru_cd_dichvu_CT ct , noitru_cd_dichvu cd, dm_loai_dich_vu_kham LOAI ,
                           noitru_dotdieutri dot -- TGGDEV-38285 thêm
                               left join noitru_benhan ba
                                         on dot.stt_benhan = ba.stt_benhan
                                             and dot.dvtt = ba.dvtt
                                             and dot.sovaovien = ba.sovaovien
                     WHERE DV.dvtt = p_DVTT and ct.DVTT = p_DVTT and ct.SO_PHIEU_DICHVU = p_SO_PHIEU and DV.DVTT = ct.DVTT and DV. MA_DV = ct. MA_DV
                       AND DV.dvtt = p_DVTT
                       AND LOAI.dvtt = p_DVTT
                       and cd.dvtt = p_dvtt
                       and cd.so_phieu_dichvu = ct.so_phieu_dichvu
                       and cd.sovaovien = ct.sovaovien
                       and cd.sovaovien_dt = ct.sovaovien_dt
                       AND (LOAI.hoatdong = 1 OR V_THAMSO_94301 = '1') -- Sang STG Sửa, code gốc (AND LOAI.hoatdong = 1)
                       AND (DV.TRANGTHAI = 1 OR V_THAMSO_94301 = '1')  -- Sang STG Sửa, code gốc (AND DV.TRANGTHAI = 1)
                       AND DV.TT_BHYT_CHI != v_TT_KIEMTRA
        AND DV. LOAI_DV = LOAI. MA_LOAI_DICH_VU
        AND DV.LOAI_DV = p_MALOAI
      -- TGGDEV-38285 thêm
       and ct.dvtt = dot.dvtt
       and ct.stt_benhan = dot.stt_benhan
       and ct.stt_dotdieutri = dot.stt_dotdieutri
       and dot.dvtt = p_dvtt
       -- TGGDEV-38285 end thêm
                     order by DV.TEN_DV;

ELSIF p_MACHUYENKHOA != '0' AND p_MACHITIET != '0' THEN
        OPEN cur FOR SELECT rownum as STT, DV.MA_DV as MA_DV,
                            DV. LOAI_DV as LOAI_DV,
                            case when dv.ten_hien_thi != ' ' then dv.ten_hien_thi else --DV.TEN_DV
                                case when v_thamso208 in (0,4) and SUDUNG_TT37=0 and trunc(p_ngaytao) >= to_date('2018-07-15','yyyy-mm-dd') then
                                         case when NVL(TEN_TT15,' ')=' ' then DV.TEN_DV else TEN_TT15 end else DV.TEN_DV end
                                      end as TEN_DV,
                            DV. DVT_DV as DVT_DV,
                            LOAI. TEN_LOAI_DICH_VU as TEN_LOAI_DV,
                            --NVL(ct.SO_LUONG,1) AS SO_LUONG,
                            --CT.DON_GIA as GIA_DV,
                            case when v_thamso_820322=0 and ct.TRANG_THAI_CUNG_KIP = 1 then NVL(ct.SO_LUONG,1) else NVL(ct.SO_LUONG,1)*100/ct.TILE_THANHTOAN end AS SO_LUONG,
                            case when v_thamso_820322=0 and ct.TRANG_THAI_CUNG_KIP = 1 then CT.DON_GIA else CT.DON_GIA*(ct.TILE_THANHTOAN/100) end as GIA_DV,
                            NVL(ct.SO_LUONG,1) * NVL(CT.DON_GIA,0)AS THANH_TIEN,
                            (CASE WHEN nvl(ct.BHYTKCHI,1) = 0  THEN 1 ELSE 0 END) as  CHON,
                            DV.TRANGTHAI as hoatdong,
                            p_BHYT_KOCHI AS BHYT_KOCHI,
                            -- VNPTHIS-3379 thêm
                            V_STG_TUOI as tuoi,
                            TO_CHAR(v_ngay_nhapvien, 'dd/MM/yyyy') NGAY_NHAP_VIEN
                            -- VNPTHIS-3379 thêm
                                   ,p_stt_hangngay as STT_HANGNGAY,
                            -- HGI TGGDEV-28223 10/04/2018 thêm
                            CASE V_THAMSO_93130
                                WHEN '1' THEN his_manager.HGI_HIENTHI_TUOI_BENHNHAN(v_ngay_sinh, v_ngay_nhapvien, p_DVTT)
                                WHEN '0' THEN to_char(v_ngay_sinh,'yyyy')
                                WHEN '2' THEN to_char(v_ngay_sinh,'dd/mm/yyyy')
                                      END as ngaytuoihgi,
                            V_THAMSO_93130 tshtngaytuoi
                            -- HGI TGGDEV-28223 10/04/2018 thêm
                            -- ghi chu TGGDEV-35203
                                   ,ct.ghichu_chidinh as ghichu,
                            to_char(cd.ngay_chi_dinh, 'HH24') || ' ' || ' ' || 'giờ' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'mi') || ' ' || 'phút' || ' ' || 'ngày' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'DD') || ' ' || 'tháng' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'MM') || ' ' || 'năm' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'YYYY') as NGAY_CHI_DINH,
                            --BDH thêm loại dv đơn vị 82008
                            (CASE WHEN DV.LOAI_DV = 'NS' THEN 'NS' ELSE ' ' END) AS LOAI
                                   ,case when v_capcuu_cls = 1 then cd.capcuu else dot.capcuu end as thuong_capcuu  -- TGGDEV-38285 thêm TGGDEV-48254
                                   ,(ba.icd_nhapvien || '  ' || ba.tenbenhchinh_nhapvien) as benhchinh
                                   , v_thamso820900 as HIENTHI
                                   , v_thamso960616 ANCHUKY
                     FROM  DM_DICH_VU_KHAM DV ,noitru_cd_dichvu_CT ct , noitru_cd_dichvu cd, dm_loai_dich_vu_kham LOAI ,
                           noitru_dotdieutri dot -- TGGDEV-38285 thêm
                               left join noitru_benhan ba
                                         on dot.stt_benhan = ba.stt_benhan
                                             and dot.dvtt = ba.dvtt
                                             and dot.sovaovien = ba.sovaovien
                     WHERE  DV.dvtt = p_DVTT and ct.DVTT = p_DVTT and ct.SO_PHIEU_DICHVU = p_SO_PHIEU and DV.DVTT = ct.DVTT and DV. MA_DV = ct. MA_DV
                       AND DV.dvtt = p_DVTT
                       AND LOAI.dvtt = p_DVTT
                       and cd.dvtt = p_dvtt
                       and cd.so_phieu_dichvu = ct.so_phieu_dichvu
                       and cd.sovaovien = ct.sovaovien
                       and cd.sovaovien_dt = ct.sovaovien_dt
                       AND (LOAI.hoatdong = 1 OR V_THAMSO_94301 = '1') -- Sang STG Sửa, code gốc (AND LOAI.hoatdong = 1)
                       AND (DV.TRANGTHAI = 1 OR V_THAMSO_94301 = '1')  -- Sang STG Sửa, code gốc (AND DV.TRANGTHAI = 1)
                       AND DV.TT_BHYT_CHI!= v_TT_KIEMTRA
        AND DV.CHUYENKHOA = p_MACHUYENKHOA
        AND DV.CHITIETCHUYENKHOA = v_CHITIET_CHUYENKHOA
        AND DV.LOAI_DV = LOAI.MA_LOAI_DICH_VU
        AND DV.LOAI_DV = p_MALOAI
      -- TGGDEV-38285 thêm
       and ct.dvtt = dot.dvtt
       and ct.stt_benhan = dot.stt_benhan
       and ct.stt_dotdieutri = dot.stt_dotdieutri
       and dot.dvtt = p_dvtt
       -- TGGDEV-38285 end thêm
                     order by DV.TEN_DV;
ELSIF p_MACHUYENKHOA != '0' AND p_MACHITIET = '0' THEN
        OPEN cur FOR SELECT rownum as STT, DV.MA_DV as MA_DV,
                            DV. LOAI_DV as LOAI_DV,
                            case when dv.ten_hien_thi != ' ' then dv.ten_hien_thi else --DV.TEN_DV
                                case when v_thamso208 in (0,4) and SUDUNG_TT37=0 and trunc(p_ngaytao) >= to_date('2018-07-15','yyyy-mm-dd') then
                                         case when NVL(TEN_TT15,' ')=' ' then DV.TEN_DV else TEN_TT15 end else DV.TEN_DV end
                                      end as TEN_DV,
                            DV. DVT_DV as DVT_DV,
                            LOAI. TEN_LOAI_DICH_VU as TEN_LOAI_DV,
                            --NVL(ct.SO_LUONG,1) AS SO_LUONG,
                            --CT.DON_GIA as GIA_DV,
                            case when v_thamso_820322=0 and ct.TRANG_THAI_CUNG_KIP = 1 then NVL(ct.SO_LUONG,1) else NVL(ct.SO_LUONG,1)*100/ct.TILE_THANHTOAN end AS SO_LUONG,
                            case when v_thamso_820322=0 and ct.TRANG_THAI_CUNG_KIP = 1 then CT.DON_GIA else CT.DON_GIA*(ct.TILE_THANHTOAN/100) end as GIA_DV,
                            NVL(ct.SO_LUONG,1) * NVL(CT.DON_GIA,0)AS THANH_TIEN,
                            (CASE WHEN nvl(ct.BHYTKCHI,1) = 0  THEN 1 ELSE 0 END) as  CHON,
                            DV.TRANGTHAI as hoatdong,
                            p_BHYT_KOCHI AS BHYT_KOCHI,
                            -- VNPTHIS-3379 thêm
                            V_STG_TUOI as tuoi,
                            TO_CHAR(v_ngay_nhapvien, 'dd/MM/yyyy') NGAY_NHAP_VIEN
                            -- VNPTHIS-3379 thêm
                                   ,p_stt_hangngay as STT_HANGNGAY,
                            -- HGI TGGDEV-28223 10/04/2018 thêm
                            CASE V_THAMSO_93130
                                WHEN '0' THEN his_manager.HGI_HIENTHI_TUOI_BENHNHAN(v_ngay_sinh, v_ngay_nhapvien, p_DVTT)
                                WHEN '1' THEN to_char(v_ngay_sinh,'yyyy')
                                WHEN '2' THEN to_char(v_ngay_sinh,'dd/mm/yyyy')
                                      END as ngaytuoihgi,
                            V_THAMSO_93130 tshtngaytuoi
                            -- HGI TGGDEV-28223 10/04/2018 thêm
                            -- ghi chu TGGDEV-35203
                                   ,ct.ghichu_chidinh as ghichu,
                            to_char(cd.ngay_chi_dinh, 'HH24') || ' ' || ' ' || 'giờ' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'mi') || ' ' || 'phút' || ' ' || 'ngày' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'DD') || ' ' || 'tháng' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'MM') || ' ' || 'năm' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'YYYY') as NGAY_CHI_DINH,
                            --BDH thêm loại dv đơn vị 82008
                            (CASE WHEN DV.LOAI_DV = 'NS' THEN 'NS' ELSE ' ' END) AS LOAI
                                   , case when v_capcuu_cls = 1 then cd.capcuu else dot.capcuu end as thuong_capcuu  -- TGGDEV-38285 thêm TGGDEV-48254
                                   ,(ba.icd_nhapvien || '  ' || ba.tenbenhchinh_nhapvien) as benhchinh
                                   , v_thamso820900 as HIENTHI
                                   , v_thamso960616 ANCHUKY
                     FROM  DM_DICH_VU_KHAM DV, noitru_cd_dichvu_CT ct , noitru_cd_dichvu cd, dm_loai_dich_vu_kham LOAI ,
                           noitru_dotdieutri dot -- TGGDEV-38285 thêm
                               left join noitru_benhan ba
                                         on dot.stt_benhan = ba.stt_benhan
                                             and dot.dvtt = ba.dvtt
                                             and dot.sovaovien = ba.sovaovien
                     WHERE DV.dvtt = p_DVTT and ct.DVTT = p_DVTT and ct.SO_PHIEU_DICHVU = p_SO_PHIEU and DV.DVTT = ct.DVTT and DV. MA_DV = ct. MA_DV
                       AND DV.dvtt = p_DVTT
                       AND LOAI.dvtt = p_DVTT
                       and cd.dvtt = p_dvtt
                       and cd.so_phieu_dichvu = ct.so_phieu_dichvu
                       and cd.sovaovien = ct.sovaovien
                       and cd.sovaovien_dt = ct.sovaovien_dt
                       AND (LOAI.hoatdong = 1 OR V_THAMSO_94301 = '1') -- Sang STG Sửa, code gốc (AND LOAI.hoatdong = 1)
                       AND (DV.TRANGTHAI = 1 OR V_THAMSO_94301 = '1')  -- Sang STG Sửa, code gốc (AND DV.TRANGTHAI = 1)
                       AND DV.TT_BHYT_CHI != v_TT_KIEMTRA
        AND DV.CHUYENKHOA = p_MACHUYENKHOA
        AND DV.LOAI_DV = LOAI.MA_LOAI_DICH_VU
        AND DV.LOAI_DV = p_MALOAI
      -- TGGDEV-38285 thêm
       and ct.dvtt = dot.dvtt
       and ct.stt_benhan = dot.stt_benhan
       and ct.stt_dotdieutri = dot.stt_dotdieutri
       and dot.dvtt = p_dvtt
       -- TGGDEV-38285 end thêm
                     order by DV.TEN_DV;
END IF;
END IF;

else
      IF p_MALOAI not in ('PT','TT') THEN
      OPEN cur FOR SELECT rownum as STT, DV.MA_DV as MA_DV,
                          DV. LOAI_DV as LOAI_DV,
                          --DV. TEN_DV as TEN_DV,
                          case when v_thamso208 in (0,4) and SUDUNG_TT37=0 and trunc(p_ngaytao) >= to_date('2018-07-15','yyyy-mm-dd') then
                                   case when NVL(TEN_TT15,' ')=' ' then DV.TEN_DV else TEN_TT15 end else DV.TEN_DV end as TEN_DV,
                          DV. DVT_DV as DVT_DV,
                          LOAI. TEN_LOAI_DICH_VU as TEN_LOAI_DV,
                          --NVL(ct.SO_LUONG,1) AS SO_LUONG,
                          --CT.DON_GIA as GIA_DV,
                          case when v_thamso_820322=0 and ct.TRANG_THAI_CUNG_KIP = 1 then NVL(ct.SO_LUONG,1) else NVL(ct.SO_LUONG,1)*100/ct.TILE_THANHTOAN end AS SO_LUONG,
                          case when v_thamso_820322=0 and ct.TRANG_THAI_CUNG_KIP = 1 then CT.DON_GIA else CT.DON_GIA*(ct.TILE_THANHTOAN/100) end as GIA_DV,
                          NVL(ct.SO_LUONG,1) * NVL(CT.DON_GIA,0)AS THANH_TIEN,
                          (CASE WHEN nvl(ct.BHYTKCHI,1) = 0  THEN 1 ELSE 0 END) as  CHON,
                          DV.TRANGTHAI as hoatdong,
                          p_BHYT_KOCHI AS BHYT_KOCHI,
                          -- VNPTHIS-3379 thêm
                          V_STG_TUOI as tuoi,
                          TO_CHAR(v_ngay_nhapvien, 'dd/MM/yyyy') NGAY_NHAP_VIEN
                          -- VNPTHIS-3379 thêm
                           ,p_stt_hangngay as STT_HANGNGAY,
                          -- HGI TGGDEV-28223 10/04/2018 thêm
                          CASE V_THAMSO_93130
                              WHEN '1' THEN his_manager.HGI_HIENTHI_TUOI_BENHNHAN(v_ngay_sinh, v_ngay_nhapvien, p_DVTT)
                              WHEN '0' THEN to_char(v_ngay_sinh,'yyyy')
                              WHEN '2' THEN to_char(v_ngay_sinh,'dd/mm/yyyy')
                              END as ngaytuoihgi,
                          V_THAMSO_93130 tshtngaytuoi
                          -- HGI TGGDEV-28223 10/04/2018 thêm
                          -- ghi chu TGGDEV-35203
                           ,ct.ghichu_chidinh as ghichu,
                          to_char(cd.ngay_chi_dinh, 'HH24') || ' ' || ' ' || 'giờ' || ' ' ||
                          to_char(cd.ngay_chi_dinh, 'mi') || ' ' || 'phút' || ' ' || 'ngày' || ' ' ||
                          to_char(cd.ngay_chi_dinh, 'DD') || ' ' || 'tháng' || ' ' ||
                          to_char(cd.ngay_chi_dinh, 'MM') || ' ' || 'năm' || ' ' ||
                          to_char(cd.ngay_chi_dinh, 'YYYY') as NGAY_CHI_DINH,
                          --BDH thêm loại dv đơn vị 82008
                          (CASE WHEN DV.LOAI_DV = 'NS' THEN 'NS' ELSE ' ' END) AS LOAI
                           ,case when v_capcuu_cls = 1 then cd.capcuu else dot.capcuu end as thuong_capcuu  -- TGGDEV-38285 thêm TGGDEV-48254
                           ,(ba.icd_nhapvien || '  ' || ba.tenbenhchinh_nhapvien) as benhchinh
                           , v_thamso820900 as HIENTHI
                           , v_thamso960616 ANCHUKY
                   FROM  DM_DICH_VU_KHAM DV, noitru_cd_dichvu_CT ct , noitru_cd_dichvu cd, dm_loai_dich_vu_kham LOAI ,
                         noitru_dotdieutri dot -- TGGDEV-38285 thêm
                             left join noitru_benhan ba
                                       on dot.stt_benhan = ba.stt_benhan
                                           and dot.dvtt = ba.dvtt
                                           and dot.sovaovien = ba.sovaovien
                   WHERE  DV.dvtt = p_DVTT and ct.DVTT = p_DVTT and ct.SO_PHIEU_DICHVU = p_SO_PHIEU and DV.DVTT = ct.DVTT and DV. MA_DV = ct. MA_DV
                     AND DV.dvtt = p_DVTT
                     AND LOAI.dvtt = p_DVTT
                     and cd.dvtt = p_dvtt
                     and cd.so_phieu_dichvu = ct.so_phieu_dichvu
                     and cd.sovaovien = ct.sovaovien
                     and cd.sovaovien_dt = ct.sovaovien_dt
                     AND (LOAI.hoatdong = 1 OR V_THAMSO_94301 = '1') -- Sang STG Sửa, code gốc (AND LOAI.hoatdong = 1)
                     AND (DV.TRANGTHAI = 1 OR V_THAMSO_94301 = '1')  -- Sang STG Sửa, code gốc (AND DV.TRANGTHAI = 1)
                     AND DV.TT_BHYT_CHI != v_TT_KIEMTRA
      AND DV. LOAI_DV = LOAI. MA_LOAI_DICH_VU
      AND DV.LOAI_DV = p_MALOAI
      -- TGGDEV-38285 thêm
       and ct.dvtt = dot.dvtt
       and ct.stt_benhan = dot.stt_benhan
       and ct.stt_dotdieutri = dot.stt_dotdieutri
       and dot.dvtt = p_dvtt
       -- TGGDEV-38285 end thêm
                   order by DV.TEN_DV;
ELSE
      IF p_MACHUYENKHOA = '0' AND p_MACHITIET = '0' THEN
        OPEN cur FOR SELECT rownum as STT, DV.MA_DV as MA_DV,
                            DV. LOAI_DV as LOAI_DV,
                            --DV. TEN_DV as TEN_DV,
                            case when v_thamso208 in (0,4) and SUDUNG_TT37=0 and trunc(p_ngaytao) >= to_date('2018-07-15','yyyy-mm-dd') then
                                     case when NVL(TEN_TT15,' ')=' ' then DV.TEN_DV else TEN_TT15 end else DV.TEN_DV end as TEN_DV,
                            DV. DVT_DV as DVT_DV,
                            LOAI. TEN_LOAI_DICH_VU as TEN_LOAI_DV,
                            --NVL(ct.SO_LUONG,1) AS SO_LUONG,
                            --CT.DON_GIA as GIA_DV,
                            case when v_thamso_820322=0 and ct.TRANG_THAI_CUNG_KIP = 1 then NVL(ct.SO_LUONG,1) else NVL(ct.SO_LUONG,1)*100/ct.TILE_THANHTOAN end AS SO_LUONG,
                            case when v_thamso_820322=0 and ct.TRANG_THAI_CUNG_KIP = 1 then CT.DON_GIA else CT.DON_GIA*(ct.TILE_THANHTOAN/100) end as GIA_DV,
                            NVL(ct.SO_LUONG,1) * NVL(CT.DON_GIA,0)AS THANH_TIEN,
                            (CASE WHEN nvl(ct.BHYTKCHI,1) = 0  THEN 1 ELSE 0 END) as  CHON,
                            DV.TRANGTHAI as hoatdong,
                            p_BHYT_KOCHI AS BHYT_KOCHI,
                            -- VNPTHIS-3379 thêm
                            V_STG_TUOI as tuoi,
                            TO_CHAR(v_ngay_nhapvien, 'dd/MM/yyyy') NGAY_NHAP_VIEN
                            -- VNPTHIS-3379 thêm
                             ,p_stt_hangngay as STT_HANGNGAY,
                            -- HGI TGGDEV-28223 10/04/2018 thêm
                            CASE V_THAMSO_93130
                                WHEN '1' THEN his_manager.HGI_HIENTHI_TUOI_BENHNHAN(v_ngay_sinh, v_ngay_nhapvien, p_DVTT)
                                WHEN '0' THEN to_char(v_ngay_sinh,'yyyy')
                                WHEN '2' THEN to_char(v_ngay_sinh,'dd/mm/yyyy')
                                END as ngaytuoihgi,
                            V_THAMSO_93130 tshtngaytuoi
                            -- HGI TGGDEV-28223 10/04/2018 thêm
                            -- ghi chu TGGDEV-35203
                             ,ct.ghichu_chidinh as ghichu,
                            to_char(cd.ngay_chi_dinh, 'HH24') || ' ' || ' ' || 'giờ' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'mi') || ' ' || 'phút' || ' ' || 'ngày' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'DD') || ' ' || 'tháng' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'MM') || ' ' || 'năm' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'YYYY') as NGAY_CHI_DINH,
                            --BDH thêm loại dv đơn vị 82008
                            (CASE WHEN DV.LOAI_DV = 'NS' THEN 'NS' ELSE ' ' END) AS LOAI
                             ,case when v_capcuu_cls = 1 then cd.capcuu else dot.capcuu end as thuong_capcuu  -- TGGDEV-38285 thêm TGGDEV-48254
                             ,(ba.icd_nhapvien || '  ' || ba.tenbenhchinh_nhapvien) as benhchinh
                             , v_thamso820900 as HIENTHI
                             , v_thamso960616 ANCHUKY
                     FROM  DM_DICH_VU_KHAM DV, noitru_cd_dichvu_CT ct , noitru_cd_dichvu cd, dm_loai_dich_vu_kham LOAI ,
                           noitru_dotdieutri dot -- TGGDEV-38285 thêm
                               left join noitru_benhan ba
                                         on dot.stt_benhan = ba.stt_benhan
                                             and dot.dvtt = ba.dvtt
                                             and dot.sovaovien = ba.sovaovien
                     WHERE DV.dvtt = p_DVTT and ct.DVTT = p_DVTT and ct.SO_PHIEU_DICHVU = p_SO_PHIEU and DV.DVTT = ct.DVTT and DV. MA_DV = ct. MA_DV
                       AND DV.dvtt = p_DVTT
                       AND LOAI.dvtt = p_DVTT
                       and cd.dvtt = p_dvtt
                       and cd.so_phieu_dichvu = ct.so_phieu_dichvu
                       and cd.sovaovien = ct.sovaovien
                       and cd.sovaovien_dt = ct.sovaovien_dt
                       AND (LOAI.hoatdong = 1 OR V_THAMSO_94301 = '1') -- Sang STG Sửa, code gốc (AND LOAI.hoatdong = 1)
                       AND (DV.TRANGTHAI = 1 OR V_THAMSO_94301 = '1')  -- Sang STG Sửa, code gốc (AND DV.TRANGTHAI = 1)
                       AND DV.TT_BHYT_CHI != v_TT_KIEMTRA
        AND DV. LOAI_DV = LOAI. MA_LOAI_DICH_VU
        AND DV.LOAI_DV = p_MALOAI
      -- TGGDEV-38285 thêm
       and ct.dvtt = dot.dvtt
       and ct.stt_benhan = dot.stt_benhan
       and ct.stt_dotdieutri = dot.stt_dotdieutri
       and dot.dvtt = p_dvtt
       -- TGGDEV-38285 end thêm
                     order by DV.TEN_DV;

ELSIF p_MACHUYENKHOA != '0' AND p_MACHITIET != '0' THEN
        OPEN cur FOR SELECT rownum as STT, DV.MA_DV as MA_DV,
                            DV. LOAI_DV as LOAI_DV,
                            --DV. TEN_DV as TEN_DV,
                            case when v_thamso208 in (0,4) and SUDUNG_TT37=0 and trunc(p_ngaytao) >= to_date('2018-07-15','yyyy-mm-dd') then
                                     case when NVL(TEN_TT15,' ')=' ' then DV.TEN_DV else TEN_TT15 end else DV.TEN_DV end as TEN_DV,
                            DV. DVT_DV as DVT_DV,
                            LOAI. TEN_LOAI_DICH_VU as TEN_LOAI_DV,
                            --NVL(ct.SO_LUONG,1) AS SO_LUONG,
                            ----ÂN THÊM THAM SỐ 208
                            --CT.DON_GIA as GIA_DV,
                            case when v_thamso_820322=0 and ct.TRANG_THAI_CUNG_KIP = 1 then NVL(ct.SO_LUONG,1) else NVL(ct.SO_LUONG,1)*100/ct.TILE_THANHTOAN end AS SO_LUONG,
                            case when v_thamso_820322=0 and ct.TRANG_THAI_CUNG_KIP = 1 then CT.DON_GIA else CT.DON_GIA*(ct.TILE_THANHTOAN/100) end as GIA_DV,
                            NVL(ct.SO_LUONG,1) * NVL(CT.DON_GIA,0)AS THANH_TIEN,
                            (CASE WHEN nvl(ct.BHYTKCHI,1) = 0  THEN 1 ELSE 0 END) as  CHON,
                            DV.TRANGTHAI as hoatdong,
                            p_BHYT_KOCHI AS BHYT_KOCHI,
                            -- VNPTHIS-3379 thêm
                            V_STG_TUOI as tuoi,
                            TO_CHAR(v_ngay_nhapvien, 'dd/MM/yyyy') NGAY_NHAP_VIEN
                            -- VNPTHIS-3379 thêm
                                   ,p_stt_hangngay as STT_HANGNGAY,
                            -- HGI TGGDEV-28223 10/04/2018 thêm
                            CASE V_THAMSO_93130
                                WHEN '1' THEN his_manager.HGI_HIENTHI_TUOI_BENHNHAN(v_ngay_sinh, v_ngay_nhapvien, p_DVTT)
                                WHEN '0' THEN to_char(v_ngay_sinh,'yyyy')
                                WHEN '2' THEN to_char(v_ngay_sinh,'dd/mm/yyyy')
                                      END as ngaytuoihgi,
                            V_THAMSO_93130 tshtngaytuoi
                            -- HGI TGGDEV-28223 10/04/2018 thêm
                            -- ghi chu TGGDEV-35203
                                   ,ct.ghichu_chidinh as ghichu,
                            to_char(cd.ngay_chi_dinh, 'HH24') || ' ' || ' ' || 'giờ' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'mi') || ' ' || 'phút' || ' ' || 'ngày' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'DD') || ' ' || 'tháng' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'MM') || ' ' || 'năm' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'YYYY') as NGAY_CHI_DINH,
                            --BDH thêm loại dv đơn vị 82008
                            (CASE WHEN DV.LOAI_DV = 'NS' THEN 'NS' ELSE ' ' END) AS LOAI
                                   ,case when v_capcuu_cls = 1 then cd.capcuu else dot.capcuu end as thuong_capcuu  -- TGGDEV-38285 thêm
                                   ,(ba.icd_nhapvien || '  ' || ba.tenbenhchinh_nhapvien) as benhchinh
                                   , v_thamso820900 as HIENTHI
                                   , v_thamso960616 ANCHUKY
                     FROM  DM_DICH_VU_KHAM DV ,noitru_cd_dichvu_CT ct , noitru_cd_dichvu cd, dm_loai_dich_vu_kham LOAI ,
                           noitru_dotdieutri dot -- TGGDEV-38285 thêm
                               left join noitru_benhan ba
                                         on dot.stt_benhan = ba.stt_benhan
                                             and dot.dvtt = ba.dvtt
                                             and dot.sovaovien = ba.sovaovien
                     WHERE  DV.dvtt = p_DVTT and ct.DVTT = p_DVTT and ct.SO_PHIEU_DICHVU = p_SO_PHIEU and DV.DVTT = ct.DVTT and DV. MA_DV = ct. MA_DV
                       AND DV.dvtt = p_DVTT
                       AND LOAI.dvtt = p_DVTT
                       and cd.dvtt = p_dvtt
                       and cd.so_phieu_dichvu = ct.so_phieu_dichvu
                       and cd.sovaovien = ct.sovaovien
                       and cd.sovaovien_dt = ct.sovaovien_dt
                       AND (LOAI.hoatdong = 1 OR V_THAMSO_94301 = '1') -- Sang STG Sửa, code gốc (AND LOAI.hoatdong = 1)
                       AND (DV.TRANGTHAI = 1 OR V_THAMSO_94301 = '1')  -- Sang STG Sửa, code gốc (AND DV.TRANGTHAI = 1)
                       AND DV.TT_BHYT_CHI!= v_TT_KIEMTRA
        AND DV.CHUYENKHOA = p_MACHUYENKHOA
        AND DV.CHITIETCHUYENKHOA = v_CHITIET_CHUYENKHOA
        AND DV.LOAI_DV = LOAI.MA_LOAI_DICH_VU
        AND DV.LOAI_DV = p_MALOAI
      -- TGGDEV-38285 thêm
       and ct.dvtt = dot.dvtt
       and ct.stt_benhan = dot.stt_benhan
       and ct.stt_dotdieutri = dot.stt_dotdieutri
       and dot.dvtt = p_dvtt
       -- TGGDEV-38285 end thêm
                     order by DV.TEN_DV;
ELSIF p_MACHUYENKHOA != '0' AND p_MACHITIET = '0' THEN
        OPEN cur FOR SELECT rownum as STT, DV.MA_DV as MA_DV,
                            DV. LOAI_DV as LOAI_DV,
                            --DV. TEN_DV as TEN_DV,
                            case when v_thamso208 in (0,4) and SUDUNG_TT37=0 and trunc(p_ngaytao) >= to_date('2018-07-15','yyyy-mm-dd') then
                                     case when NVL(TEN_TT15,' ')=' ' then DV.TEN_DV else TEN_TT15 end else DV.TEN_DV end as TEN_DV,
                            DV. DVT_DV as DVT_DV,
                            LOAI. TEN_LOAI_DICH_VU as TEN_LOAI_DV,
                            --NVL(ct.SO_LUONG,1) AS SO_LUONG,
                            --CT.DON_GIA as GIA_DV,
                            case when v_thamso_820322=0 and ct.TRANG_THAI_CUNG_KIP = 1 then NVL(ct.SO_LUONG,1) else NVL(ct.SO_LUONG,1)*100/ct.TILE_THANHTOAN end AS SO_LUONG,
                            case when v_thamso_820322=0 and ct.TRANG_THAI_CUNG_KIP = 1 then CT.DON_GIA else CT.DON_GIA*(ct.TILE_THANHTOAN/100) end as GIA_DV,
                            NVL(ct.SO_LUONG,1) * NVL(CT.DON_GIA,0)AS THANH_TIEN,
                            (CASE WHEN nvl(ct.BHYTKCHI,1) = 0  THEN 1 ELSE 0 END) as  CHON,
                            DV.TRANGTHAI as hoatdong,
                            p_BHYT_KOCHI AS BHYT_KOCHI,
                            -- VNPTHIS-3379 thêm
                            V_STG_TUOI as tuoi,
                            TO_CHAR(v_ngay_nhapvien, 'dd/MM/yyyy') NGAY_NHAP_VIEN
                            -- VNPTHIS-3379 thêm
                                   ,p_stt_hangngay as STT_HANGNGAY,
                            -- HGI TGGDEV-28223 10/04/2018 thêm
                            CASE V_THAMSO_93130
                                WHEN '1' THEN his_manager.HGI_HIENTHI_TUOI_BENHNHAN(v_ngay_sinh, v_ngay_nhapvien, p_DVTT)
                                WHEN '0' THEN to_char(v_ngay_sinh,'yyyy')
                                WHEN '2' THEN to_char(v_ngay_sinh,'dd/mm/yyyy')
                                      END as ngaytuoihgi,
                            V_THAMSO_93130 tshtngaytuoi
                            -- HGI TGGDEV-28223 10/04/2018 thêm
                            -- ghi chu TGGDEV-35203
                                   ,ct.ghichu_chidinh as ghichu,
                            to_char(cd.ngay_chi_dinh, 'HH24') || ' ' || ' ' || 'giờ' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'mi') || ' ' || 'phút' || ' ' || 'ngày' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'DD') || ' ' || 'tháng' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'MM') || ' ' || 'năm' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'YYYY') as NGAY_CHI_DINH,
                            --BDH thêm loại dv đơn vị 82008
                            (CASE WHEN DV.LOAI_DV = 'NS' THEN 'NS' ELSE ' ' END) AS LOAI
                                   ,case when v_capcuu_cls = 1 then cd.capcuu else dot.capcuu end as thuong_capcuu  -- TGGDEV-38285 thêm
                                   ,(ba.icd_nhapvien || '  ' || ba.tenbenhchinh_nhapvien) as benhchinh
                                   , v_thamso820900 as HIENTHI
                                   , v_thamso960616 ANCHUKY
                     FROM  DM_DICH_VU_KHAM DV, noitru_cd_dichvu_CT ct , noitru_cd_dichvu cd, dm_loai_dich_vu_kham LOAI ,
                           noitru_dotdieutri dot -- TGGDEV-38285 thêm
                               left join noitru_benhan ba
                                         on dot.stt_benhan = ba.stt_benhan
                                             and dot.dvtt = ba.dvtt
                                             and dot.sovaovien = ba.sovaovien
                     WHERE DV.dvtt = p_DVTT and ct.DVTT = p_DVTT and ct.SO_PHIEU_DICHVU = p_SO_PHIEU and DV.DVTT = ct.DVTT and DV. MA_DV = ct. MA_DV
                       AND DV.dvtt = p_DVTT
                       AND LOAI.dvtt = p_DVTT
                       and cd.dvtt = p_dvtt
                       and cd.so_phieu_dichvu = ct.so_phieu_dichvu
                       and cd.sovaovien = ct.sovaovien
                       and cd.sovaovien_dt = ct.sovaovien_dt
                       AND (LOAI.hoatdong = 1 OR V_THAMSO_94301 = '1') -- Sang STG Sửa, code gốc (AND LOAI.hoatdong = 1)
                       AND (DV.TRANGTHAI = 1 OR V_THAMSO_94301 = '1')  -- Sang STG Sửa, code gốc (AND DV.TRANGTHAI = 1)
                       AND DV.TT_BHYT_CHI != v_TT_KIEMTRA
        AND DV.CHUYENKHOA = p_MACHUYENKHOA
        AND DV.LOAI_DV = LOAI.MA_LOAI_DICH_VU
        AND DV.LOAI_DV = p_MALOAI
      -- TGGDEV-38285 thêm
       and ct.dvtt = dot.dvtt
       and ct.stt_benhan = dot.stt_benhan
       and ct.stt_dotdieutri = dot.stt_dotdieutri
       and dot.dvtt = p_dvtt
       -- TGGDEV-38285 end thêm
                     order by DV.TEN_DV;
END IF;
END IF;
end if;
END;