create or replace FUNCTION                                       "CMU_KYTHUATPHCN_INSERT" (
    p_dvtt                IN VARCHAR2,
    p_so_vao_vien         IN NUMBER,
    p_ma_benh_nhan        IN NUMBER,
    p_thoigian           IN VARCHAR2,
    p_thoigianphut   IN VARCHAR2,
    p_dien<PERSON>nbenh           IN VARCHAR2,
    p_tendichvu           IN VARCHAR2,
    p_nguoithuchien           IN NUMBER,
    p_bacsichidinh           IN NUMBER,
--    p_ngaytao             IN VARCHAR2,
    p_nguoi_tao           IN NUMBER,
    p_stt_dieutri       IN VARCHAR2
)
return number IS
    v_id CMU_KYTHUATPHCN.id%TYPE;
    v_thoigian   DATE := TO_DATE(p_thoigian, 'dd/mm/yyyy hh24:mi');
--    v_ngay_tao_phieu   DATE := TO_DATE(p_ngaytao, 'dd/mm/yyyy');
BEGIN
INSERT INTO CMU_KYTHUATPHCN
(
    DV<PERSON>,
    SOVAOVIEN,
    MA_BENH_NHAN,
    THOIGIAN,
    THOIGIANPHUT,
    DIENBIENBENH,
    TENDICHVU,
    NGUOITHUCHIEN,
    BACSICHIDINH,
    NGAY_TAO_PHIEU,
    NGUOI_TAO,
    STT_DIEUTRI
)
VALUES
    (
        p_dvtt,
        p_so_vao_vien,
        p_ma_benh_nhan,
        v_thoigian,
        p_thoigianphut,
        p_dienbienbenh,
        p_tendichvu,
        p_nguoithuchien,
        p_bacsichidinh,
        sysdate,
        p_nguoi_tao,
        p_stt_dieutri
    )
    RETURNING id INTO v_id;

RETURN v_id;
END;