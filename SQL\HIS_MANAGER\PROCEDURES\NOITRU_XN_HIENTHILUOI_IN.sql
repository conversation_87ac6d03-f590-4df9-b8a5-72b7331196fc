create or replace PROCEDURE               "HIS_MANAGER"."NOITRU_XN_HIENTHILUOI_IN" ( p_BHYT_KOCHI IN varchar2 ,
 p_DVTT IN varchar2 ,
 p_SO_PHIEU_XN IN varchar2, cur OUT SYS_REFCURSOR )IS
  v_TT_KIEMTRA NUMBER(10) DEFAULT 0;
  v_chandoanicd varchar2(2000) default '';
  p_tenht number(1) default 0;
  p_stt_hangngay number default 0;
  V_THAMSO_94301 VARCHAR2(1) DEFAULT '0';  -- Sang STG Them
   -- VNPTHIS-3379 thêm
   V_STG_TUOI VARCHAR(100) DEFAULT ' ';
   -- VNPTHIS-3379 thêm
   -- HGI TGGDEV-28223 10/04/2018 thêm
    V_THAMSO_93128 VARCHAR2(1) DEFAULT '0';
    v_ngay_sinh date default sysdate;
    v_ngay_nhapvien date default sysdate;
  -- <PERSON><PERSON> TGGDEV-28223 10/04/2018 thêm
  v_thamso208 number(1) default '0';
  p_ngaytao date;
  v_capcuu_cls number(1) default '0';
  v_thamso820900 number(1) default '0';
  v_thamso960616 number(10) := cmu_tsdv(p_DVTT, 960616, 0);
BEGIN
begin
select mota_thamso into v_thamso208 FROM HIS_FW.DM_THAMSO_DONVI where dvtt=p_dvtt and ma_thamso = 208;
exception when no_data_found then
        v_thamso208:='0';
end;
  -- Sang VNPT STG thêm cho Vĩnh Phúc: Cho phép in phiếu chỉ định của XN hoặc loại XN đã ngưng hoạt động thông qua tham số 94301

    -- Sang STG Them Begin 1
BEGIN
SELECT NVL(MOTA_THAMSO, '0') INTO V_THAMSO_94301
FROM HIS_FW.DM_THAMSO_DONVI
WHERE MA_THAMSO = 94301
  and dvtt=p_DVTT;
EXCEPTION WHEN NO_DATA_FOUND THEN V_THAMSO_94301 := '0';
END;
  -- Sang STG Them End 1
  -- AGG le select stt_hangngay
BEGIN
SELECT cd.stt_hangngay, trunc(cd.ngay_tao)
INTO p_stt_hangngay, p_ngaytao
FROM his_manager.noitru_cd_xet_nghiem cd
WHERE cd.dvtt = p_DVTT
  and cd.so_phieu_xn = p_SO_PHIEU_XN;
EXCEPTION
    WHEN NO_DATA_FOUND THEN
      p_stt_hangngay := '0';
END;
  -- end AGG Le
  -- HGI TGGDEV-28223 10/04/2018 thêm
BEGIN
SELECT NVL(MOTA_THAMSO, '0') INTO V_THAMSO_93128
FROM HIS_FW.DM_THAMSO_DONVI
WHERE MA_THAMSO = 93128 AND DVTT = p_DVTT;
EXCEPTION WHEN NO_DATA_FOUND THEN V_THAMSO_93128 := '0';
END;
  -- HGI TGGDEV-28223 10/04/2018 thêm
     -- VNPTHIS-3379 thêm -- HGI TGGDEV-28223 10/04/2018 lấy thêm ngày sinh, ngày nhập viện
SELECT his_manager.stg_tuoi_te_f(to_char(bn.ngay_sinh,'yyyy-mm-dd'), to_char(trunc(ba.ngaynhapvien),'yyyy-mm-dd')),
       bn.ngay_sinh, ba.ngaynhapvien
INTO V_STG_TUOI, v_ngay_sinh, v_ngay_nhapvien
FROM his_public_list.dm_benh_nhan bn,
     his_manager.noitru_benhan ba,
     his_manager.noitru_cd_xet_nghiem cd
WHERE ba.dvtt = p_DVTT
  AND cd.so_phieu_xn = p_SO_PHIEU_XN
  AND ba.dvtt = cd.dvtt
  AND ba.stt_benhan = cd.stt_benhan
  AND ba.mabenhnhan = bn.ma_benh_nhan
  AND ROWNUM = 1;
-- VNPTHIS-3379 thêm
IF p_BHYT_KOCHI = 0 THEN
     v_TT_KIEMTRA := 0;
ELSE
     v_TT_KIEMTRA := 1;
END IF;

select ba.icd_nhapvien ||':' ||ba.tenbenhchinh_nhapvien ||' ' ||ba.tenbenhphu_nhapvien into v_chandoanicd
from noitru_cd_xet_nghiem cd, noitru_benhan ba
where cd.DVTT = p_DVTT and ba.DVTT = p_DVTT
  and cd.SO_PHIEU_XN = p_SO_PHIEU_XN
  and cd.stt_benhan = ba.stt_benhan;

begin
select mota_thamso
into p_tenht
from his_fw.dm_thamso_donvi
where dvtt = p_dvtt
  and ma_thamso = 31016;
exception
    when NO_DATA_FOUND THEN
      p_tenht := '0';
end;

begin
select mota_thamso into v_capcuu_cls FROM HIS_FW.DM_THAMSO_DONVI where dvtt=p_dvtt and ma_thamso = 820877;
exception when no_data_found then
        v_capcuu_cls := '0';
end;
  --tham số hiển thị STT hằng ngày lên phiếu chỉ định
begin
select mota_thamso into v_thamso820900 FROM HIS_FW.DM_THAMSO_DONVI where dvtt=p_dvtt and ma_thamso = 820900;
exception when no_data_found then
        v_thamso820900 := '0';
end;

if p_tenht = 1 then
       OPEN cur FOR SELECT  XN.ma_xetnghiem as MA_XN,
                            XN.ma_loai_xetnghiem as LOAI_XN,
                            case when xn.ten_hien_thi != ' ' then xn.ten_hien_thi else --XN.ten_xetnghiem
                                case when v_thamso208 in (0,4) and SUDUNG_TT37=0 and trunc(p_ngaytao) >= to_date('2018-07-15','yyyy-mm-dd') then
                                         case when NVL(TEN_TT15,' ')=' ' then XN.ten_xetnghiem else TEN_TT15 end else XN.ten_xetnghiem end
                                   end as TEN_XN,
                            XN.dvt_xetnghiem as DVT_XN,
                            LOAI.ten_loai_xetnghiem as TEN_LOAI_XN,
                            NVL(ct.SO_LUONG,1) AS SO_LUONG,
                            ct.DON_GIA as GIA_XN,
                            nvl(XN.TEN_PHIEUXN, 'PHIẾU XÉT NGHIỆM'),
                            NVL(ct.SO_LUONG,1) * NVL(ct.DON_GIA,0) AS THANH_TIEN,
                            case nvl(ct.BHYTKCHI,0) when 0 then '' else 'X' end as  CHON,
                            XN.hoatdong,
                            XN.trangthai_bhyt, XN.sapxep,Concat('Chuẩn đoán: ',v_chandoanicd) as chandoanicd,
                            -- VNPTHIS-3379 thêm
                            V_STG_TUOI as tuoi,
                            TO_CHAR(v_ngay_nhapvien, 'dd/MM/yyyy') NGAY_NHAP_VIEN
                            -- VNPTHIS-3379 thêm
                               ,p_stt_hangngay as STT_HANGNGAY,
                            -- HGI TGGDEV-28223 10/04/2018 thêm
                            CASE V_THAMSO_93128
                                WHEN '1' THEN his_manager.HGI_HIENTHI_TUOI_BENHNHAN(v_ngay_sinh, v_ngay_nhapvien, p_DVTT)
                                WHEN '0' THEN to_char(v_ngay_sinh,'yyyy')
                                WHEN '2' THEN to_char(v_ngay_sinh,'dd/mm/yyyy')
                                   END as ngaytuoihgi,
                            V_THAMSO_93128 tshtngaytuoi
                            -- HGI TGGDEV-28223 10/04/2018 thêm
                            -- vlg Lợi lấy số giường buồng
                               , sogiuong.stt_giuong
                               , sogiuong.stt_buong,
                            to_char(CT.NGAY_CHI_DINH_CT,'HH24') as GIO_CD,
                            to_char(CT.NGAY_CHI_DINH_CT,'mi') as PHUT_CD,
                            to_char(cd.ngay_chi_dinh, 'HH24') || ' ' || ' ' || 'giờ' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'mi') || ' ' || 'phút,' || ' ' || 'ngày' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'DD') || ' ' || 'tháng' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'MM') || ' ' || 'năm' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'YYYY') as NGAY_CHI_DINH
                            --end vlg Lợi lấy số giường buồng
                               , case when v_capcuu_cls = 1 then cd.capcuu else dot.capcuu end as thuong_capcuu -- TGGDEV-38285 thêm --TGGDEV-48254
                               ,(ba.icd_nhapvien || '  ' || ba.tenbenhchinh_nhapvien) as benhchinh
                               , v_thamso820900 as HIENTHI,
                            to_char(cd.NGAY_CHI_DINH, 'dd/mm/yyyy HH24:MI:SS') as NGAY_CHI_DINH_HT,
                            v_thamso960616 ANCHUKY
                    FROM  cls_xetnghiem XN,
                          -- vlg LỢi
                          noitru_cd_xet_nghiem_ct ct
                              left join noitru_cd_xet_nghiem cd
                                        on cd.dvtt = ct.dvtt
                                            and cd.so_phieu_xn = ct.so_phieu_xn
                                            and cd.sovaovien = ct.sovaovien
                                            and cd.sovaovien_dt = ct.sovaovien_dt
                              left join his_manager.CMU_SOBUONGGIUONG sogiuong on ct.dvtt = sogiuong.dvtt
                              and ct.stt_benhan = sogiuong.stt_benhan
                              and ct.stt_dotdieutri = sogiuong.stt_dotdieutri
                          -- end vlg lợi
                               , cls_loaixetnghiem LOAI,
                          noitru_dotdieutri dot -- TGGDEV-38285 thêm
                              left join noitru_benhan ba
                                        on dot.stt_benhan = ba.stt_benhan
                                            and dot.dvtt = ba.dvtt
                                            and dot.sovaovien = ba.sovaovien
                    WHERE
                               ct.DVTT = p_DVTT
                      and ct.SO_PHIEU_XN = p_SO_PHIEU_XN
                      and XN.DVTT = ct.DVTT
                      and XN.ma_xetnghiem = ct.MA_XET_NGHIEM
                      and XN.dvtt = p_DVTT
                      AND LOAI.dvtt = p_DVTT
                      AND (LOAI.hoatdong = 1 OR V_THAMSO_94301 = '1') -- Sang STG Sửa, code gốc (AND LOAI.hoatdong = 1)
                      AND (XN.hoatdong = 1 OR V_THAMSO_94301 = '1')  -- Sang STG Sửa, code gốc (AND XN.hoatdong = 1)
                      and ct.chisocon = 0
                      AND XN.trangthai_bhyt != v_TT_KIEMTRA
       AND XN.ma_loai_xetnghiem = LOAI.ma_loai_xetnghiem
       -- TGGDEV-38285 thêm
       and ct.dvtt = dot.dvtt
       and ct.stt_benhan = dot.stt_benhan
       and ct.stt_dotdieutri = dot.stt_dotdieutri
       and dot.dvtt = p_dvtt
       -- TGGDEV-38285 end thêm
                    order by XN.ma_loai_xetnghiem,XN.sapxep asc;
else
       OPEN cur FOR SELECT  XN.ma_xetnghiem as MA_XN,
                            XN.ma_loai_xetnghiem as LOAI_XN,
                            --XN.ten_xetnghiem as TEN_XN,
                            case when v_thamso208 in (0,4) and SUDUNG_TT37=0 and trunc(p_ngaytao) >= to_date('2018-07-15','yyyy-mm-dd') then
                                     case when NVL(TEN_TT15,' ')=' ' then XN.ten_xetnghiem else TEN_TT15 end else XN.ten_xetnghiem end as TEN_XN,
                            XN.dvt_xetnghiem as DVT_XN,
                            LOAI.ten_loai_xetnghiem as TEN_LOAI_XN,
                            NVL(ct.SO_LUONG,1) AS SO_LUONG,
                            ct.DON_GIA as GIA_XN,
                            nvl(XN.TEN_PHIEUXN, 'PHIẾU XÉT NGHIỆM'),
                            NVL(ct.SO_LUONG,1) * NVL(ct.DON_GIA,0) AS THANH_TIEN,
                            case nvl(ct.BHYTKCHI,0) when 0 then '' else 'X' end as  CHON,
                            XN.hoatdong,
                            XN.trangthai_bhyt, XN.sapxep,Concat('Chuẩn đoán: ',v_chandoanicd) as chandoanicd,
                            -- VNPTHIS-3379 thêm
                            V_STG_TUOI as tuoi,
                            TO_CHAR(v_ngay_nhapvien, 'dd/MM/yyyy') NGAY_NHAP_VIEN
                            -- VNPTHIS-3379 thêm
                            ,p_stt_hangngay as STT_HANGNGAY,
                            -- HGI TGGDEV-28223 10/04/2018 thêm
                            CASE V_THAMSO_93128
                                WHEN '1' THEN his_manager.HGI_HIENTHI_TUOI_BENHNHAN(v_ngay_sinh, v_ngay_nhapvien, p_DVTT)
                                WHEN '0' THEN to_char(v_ngay_sinh,'yyyy')
                                WHEN '2' THEN to_char(v_ngay_sinh,'dd/mm/yyyy')
                                END as ngaytuoihgi,
                            V_THAMSO_93128 tshtngaytuoi
                            -- HGI TGGDEV-28223 10/04/2018 thêm
                            -- vlg Lợi lấy số giường buồng
                            , sogiuong.stt_giuong
                            , sogiuong.stt_buong,
                            to_char(CT.NGAY_CHI_DINH_CT,'HH24') as GIO_CD,
                            to_char(CT.NGAY_CHI_DINH_CT,'mi') as PHUT_CD,
                            to_char(cd.ngay_chi_dinh, 'HH24') || ' ' || ' ' || 'giờ' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'mi') || ' ' || 'phút,' || ' ' || 'ngày' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'DD') || ' ' || 'tháng' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'MM') || ' ' || 'năm' || ' ' ||
                            to_char(cd.ngay_chi_dinh, 'YYYY') as NGAY_CHI_DINH
                            -- vlg Lợi lấy số giường buồng
                            ,case when v_capcuu_cls = 1 then cd.capcuu else dot.capcuu end as thuong_capcuu -- TGGDEV-38285 thêm --TGGDEV-48254
                            ,(ba.icd_nhapvien || '  ' || ba.tenbenhchinh_nhapvien) as benhchinh
                            , v_thamso820900 as HIENTHI,
                            to_char(cd.NGAY_CHI_DINH, 'dd/mm/yyyy HH24:MI:SS') as NGAY_CHI_DINH_HT,
                            v_thamso960616 ANCHUKY
                    FROM  cls_xetnghiem XN,
                          -- vlg LỢi
                          noitru_cd_xet_nghiem_ct ct
                              left join noitru_cd_xet_nghiem cd
                                        on cd.dvtt = ct.dvtt
                                            and cd.so_phieu_xn = ct.so_phieu_xn
                                            and cd.sovaovien = ct.sovaovien
                                            and cd.sovaovien_dt = ct.sovaovien_dt
                              left join his_manager.CMU_SOBUONGGIUONG sogiuong on ct.dvtt = sogiuong.dvtt
                              and ct.stt_benhan = sogiuong.stt_benhan
                              and ct.stt_dotdieutri = sogiuong.stt_dotdieutri
                          -- end vlg lợi
                            , cls_loaixetnghiem LOAI,
                          noitru_dotdieutri dot -- TGGDEV-38285 thêm
                              left join noitru_benhan ba
                                        on dot.stt_benhan = ba.stt_benhan
                                            and dot.dvtt = ba.dvtt
                                            and dot.sovaovien = ba.sovaovien
                    WHERE
                            ct.DVTT = p_DVTT
                      and ct.SO_PHIEU_XN = p_SO_PHIEU_XN
                      and XN.DVTT = ct.DVTT
                      and XN.ma_xetnghiem = ct.MA_XET_NGHIEM
                      and XN.dvtt = p_DVTT
                      AND LOAI.dvtt = p_DVTT
                      AND (LOAI.hoatdong = 1 OR V_THAMSO_94301 = '1') -- Sang STG Sửa, code gốc (AND LOAI.hoatdong = 1)
                      AND (XN.hoatdong = 1 OR V_THAMSO_94301 = '1')  -- Sang STG Sửa, code gốc (AND XN.hoatdong = 1)
                      and ct.chisocon = 0
                      AND XN.trangthai_bhyt != v_TT_KIEMTRA
       AND XN.ma_loai_xetnghiem = LOAI.ma_loai_xetnghiem
       -- TGGDEV-38285 thêm
       and ct.dvtt = dot.dvtt
       and ct.stt_benhan = dot.stt_benhan
       and ct.stt_dotdieutri = dot.stt_dotdieutri
       and dot.dvtt = p_dvtt
       -- TGGDEV-38285 end thêm
                    order by XN.ma_loai_xetnghiem,XN.sapxep asc;
end if;
END;