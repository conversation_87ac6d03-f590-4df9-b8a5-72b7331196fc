package dm_phuongxa;
import dangnhap.SessionFilter;
import dm_tinhthanh.TinhThanhDAO;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Map;
import java.util.List;
import javax.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

@RestController
public class GopXaPhuongController {
    @Autowired
    GopXaPhuongDAO GopXaPhuongDAO;
    @Autowired
    SessionFilter SessionFilter;

    @RequestMapping(value = "/gopxaphuong", method = RequestMethod.GET, produces = "application/json; charset=utf-8")
    ModelAndView xaphuong(ModelMap mm, HttpSession session) {
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }
        String sql = "select * from his_fw.dm_donvi where ma_donvi = '" + session.getAttribute("Sess_DVTT").toString() + "'";
        List<Map<String, Object>> donvi = GopXaPhuongDAO.laydanhmuc(sql);
        mm.put("donvi", donvi);
        return new ModelAndView("Danhmuc/gopxaphuong/GopXaPhuong");
    }

    @RequestMapping(value = "/api-gop-xa-phuong-list", method = RequestMethod.GET, produces={"application/json; charset=utf-8"})
    @ResponseBody
    public List gop_xa_phuong_list(
            @RequestParam("madonvi") String madonvi,
            @RequestParam("madonvi_moi") String madonvi_moi,
            @RequestParam("page") String page,
            @RequestParam("rows") String rows
    ) {


        int p = Integer.parseInt(page);
        int r = Integer.parseInt(rows);
        List result = GopXaPhuongDAO.listGopXaPhuong(madonvi, madonvi_moi,r,p);
        return  result;
    }

    @PostMapping(value="/api-gop-xa-phuong-save")
    @ResponseBody
    public long gop_xa_phuong_save(@RequestParam(value = "data") org.json.JSONObject data) {
        long status = GopXaPhuongDAO.saveGopXaPhuong(data);
        return status;
    }


    @RequestMapping(value = {"/api-gop-xa-phuong-del"}, method = {RequestMethod.POST})
    @ResponseBody
    public long gop_xa_phuong_del(@RequestParam("id") String id) {
        long status = GopXaPhuongDAO.deleteGopXaPhuong(id);
        return status;
    }
}
