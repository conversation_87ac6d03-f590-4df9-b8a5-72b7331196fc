package camau;

import BaocaoBHXH.ApiGuiCong.GuiCongBHXHService;
import BaocaoBHXH.DM_APIDAO;
import BaocaoBHXH.XuatXMLBHYTDAO;
import VSC.jdbc.JdbcTemplate;
import com.fasterxml.jackson.databind.ObjectMapper;
import dangnhap.UserDAO;
import dmthamsodonvi.thamsodonviDAO;
import khambenh.theBHYT_2018;
import net.minidev.json.JSONObject;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.codec.binary.Hex;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import thamsohethong.Thamsohethong;
import tienich.tienich;
import tiepnhan.TiepnhanDAO;
import tiepnhan.TiepnhanObj;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.sql.DataSource;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES;

@Component
public class HDBankService implements HDBankDAO {
    @Autowired
    dmthamsodonvi.thamsodonviDAO thamsodonviDAO;
    @Autowired
    BaocaoBHXH.XuatXMLBHYTDAO XuatXMLBHYTDAO;
    @Autowired
    DM_APIDAO dmApidao;
    @Autowired
    private GuiCongBHXHService guiCongBHXHService;
    @Autowired
    UserDAO userDAO;
    @Resource(name = "dataSourceFW")
    DataSource dataSourceFW;
    @Autowired
    TiepnhanDAO tiepnhanDAO;

    @Autowired
    @Resource(name = "dataSourceMNG")
    DataSource dataSourceMNG;

    @Override
    public List<Map<String, Object>> getDanhSachKhoa(String idLoaiKham, String dvtt) {
        String sql = "call CMU_DM_KHOAKHAM(?,?)#c,s,s";
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        List<Map<String, Object>> list = jdbcTemplate.queryForList(sql, new Object[]{idLoaiKham, dvtt});
        return list;
    }

    @Override
    public List<Map<String, Object>> getDanhSachLoaiKham(String dvtt) {
        String sql = "call CMU_DM_LOAIKHAM_SEL(?)#c,s";
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        List<Map<String, Object>> list = jdbcTemplate.queryForList(sql, new Object[]{dvtt});
        return list;
    }

    @Override
    public List<Map<String, Object>> getDanhSachLichKham(String dvtt, String thoiGianBatDau, String thoiGianKetThuc, String maPhongKham) {
        String sql = "call CMU_GET_LICHKHAM_KIOS(?,?,?,?)#c,s,s,s,s";
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        List<Map<String, Object>> list = jdbcTemplate.queryForList(sql, new Object[]{dvtt, thoiGianBatDau, thoiGianKetThuc, maPhongKham});
        return list;
    }

    @Override
    public Map getBankConfig(String bankName, String merchantId) {
        String sql = "call HIS_MANAGER.GET_BANK_CONFIG_BY_MERCHANTID(?,?)#c,s,s";
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        Map map = jdbcTemplate.queryForMap(sql, new Object[]{bankName, merchantId});
        return map;
    }

    @Override
    public String getBillNumber(String dvtt, String ma_bn, int sovaovien, int sovaovien_dt, String noi_dung, int gia_tien, String bank_name) {
        String sql = "call HIS_MANAGER.cmu_getbillnumber_bank(?,?,?,?,?,?,?)#s,s,s,s,s,s,s,s";
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        return jdbcTemplate.queryForObject(sql, new Object[]{dvtt, ma_bn, sovaovien, sovaovien_dt, noi_dung, gia_tien, bank_name}, String.class);
    }

    @Override
    public String checkTNBenhNhan(String dvtt, String ma_bn, String so_gttt) {
        String sql = "call HIS_MANAGER.CHECK_TN_KIOSK(?,?,?)#s,s,s,s";
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        return jdbcTemplate.queryForObject(sql, new Object[]{dvtt, ma_bn, so_gttt}, String.class);
    }

    @Override
    public String getSttDangGoi(String dvtt, int increase, int uuTien){
        String sql = "call KB_LAYSOTHUTUTIEPNHAN(?,?,?,?)#s,s,l,l,l";
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        return jdbcTemplate.queryForObject(sql, new Object[]{dvtt, increase, uuTien, -1}, String.class);
    }

    @Override
    public String insertTiepNhanKiosDV(String dvtt, String idBN, int sttHangngay, String ngayVao, String ngayVaoNoiTru, String idKhoa, String idPhongKham, String tenPhongKham, int donGiaPhongKham) {
        String sql = "call HIS_MANAGER.CMU_INS_TIEP_NHAN_KIOS_DV(?,?,?,?,?,?,?,?,?)#s,s,s,l,s,s,s,s,s,l";
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        return jdbcTemplate.queryForObject(sql, new Object[]{dvtt, idBN, sttHangngay, ngayVao, ngayVaoNoiTru, idKhoa, idPhongKham, tenPhongKham, donGiaPhongKham}, String.class);
    }

    @Override
    public String insertTiepNhanKios(String dvtt, String idBN, String maBN, String hoTen, String hoBn, String tenBn, String maTinhCutru, String maHuyenCuTru, String maXaCuTru, String maDanToc, String maQuocTich, String maNgheNghiep, String diaChi, String ngaySinh, int gioiTinh, String nhomMau, String soGttt, String ngayCapGttt,
                                     String noiCapGttt, String maTheBhyt, String maDkbd, String gtTheTu, String gtTheDen, int maDoituongKcb, String ngayVao, String ngayVaoNoiTru, String lyDoVnt, String maLyDoVnt, String maLoaiKcb, String maCskcb, String anhBnCccd, String noiLamViec, String diaChiLamViec, String dienThoai, String idKhoa, String idPhongKham, String tenPhongKham, int donGiaPhongKham, String idKhungThoiGian, String idLoaiKham, String duPhong, String soGiayChuyenTuyen, String maBenhChuyenTuyen, String donViChuyenTuyen) {
        String sql = "call HIS_MANAGER.CMU_INS_TIEP_NHAN_KIOS(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)#s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,l,s,s,s,s,s,s,s,s,l,s,s,s,s,s,s,s,s,s,s,s,s,s,l,s,s,s,s,s,s";
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        return jdbcTemplate.queryForObject(sql, new Object[]{dvtt, idBN, maBN, hoTen, hoBn, tenBn, maTinhCutru, maHuyenCuTru, maXaCuTru, maDanToc, maQuocTich, maNgheNghiep, diaChi, ngaySinh, gioiTinh, nhomMau, soGttt, ngayCapGttt,
                noiCapGttt, maTheBhyt, maDkbd, gtTheTu, gtTheDen, maDoituongKcb, ngayVao, ngayVaoNoiTru, lyDoVnt, maLyDoVnt, maLoaiKcb, maCskcb, anhBnCccd, noiLamViec, diaChiLamViec, dienThoai, idKhoa, idPhongKham, tenPhongKham, donGiaPhongKham, idKhungThoiGian, idLoaiKham, duPhong, soGiayChuyenTuyen, maBenhChuyenTuyen, donViChuyenTuyen}, String.class);
    }

    @Override
    public Map<String, Object> danhSachKhoaKios(String idLoaiKham, String merchantId, String timestamp, String sign) {
        Map<String, Object> response = new HashMap<>();
        try {
            Map bankCofig = getBankConfig("HD_BANK_KIOS", merchantId);
            String secretKey = bankCofig.get("SECRET_KEY").toString();
            String dvtt = bankCofig.get("DVTT").toString();
            String dataVerify = merchantId + "|" + timestamp;

            boolean checkSign = HMACSHA256Util.verifyHMACSHA256(dataVerify, secretKey, sign);
            if (!checkSign) {
                response.put("code", "999");
                response.put("message", "Sai chữ ký");
                response.put("data", null);
                return response;
            }
            try {
                List<Map<String, Object>> listKhoa = getDanhSachKhoa(idLoaiKham, dvtt);
                response.put("code", "000");
                response.put("message", "Success");
                response.put("data", listKhoa);
                return response;
            } catch (Exception ex) {
                response.put("code", "999");
                response.put("message", "Lỗi lấy danh sách khoa: " + ex.getMessage());
                response.put("data", new HashMap<>());
                return response;
            }
        } catch (Exception ex) {
            response.put("code", "999");
            response.put("message", "Lỗi lấy thông tin cấu hình ngân hàng: " + ex.getMessage());
            response.put("data", new HashMap<>());
            return response;
        }
    }

    @Override
    public Map<String, Object> danhSachLoaiKhamKios(String merchantId, String timestamp, String sign) {
        Map<String, Object> response = new HashMap<>();
        try {
            Map bankCofig = getBankConfig("HD_BANK_KIOS", merchantId);
            String secretKey = bankCofig.get("SECRET_KEY").toString();
            String dvtt = bankCofig.get("DVTT").toString();
            String dataVerify = merchantId + "|" + timestamp;
            boolean checkSign = HMACSHA256Util.verifyHMACSHA256(dataVerify, secretKey, sign);
            if (!checkSign) {
                response.put("code", "999");
                response.put("message", "Sai chữ ký");
                response.put("data", null);
                return response;
            }
            try {
                List<Map<String, Object>> listLoaiKham = getDanhSachLoaiKham(dvtt);
                response.put("code", "000");
                response.put("message", "Success");
                response.put("data", listLoaiKham);
                return response;
            } catch (Exception ex) {
                response.put("code", "999");
                response.put("message", "Lỗi lấy danh sách loại khám: " + ex.getMessage());
                response.put("data", new HashMap<>());
                return response;
            }
        } catch (Exception ex) {
            response.put("code", "999");
            response.put("message", "Lỗi lấy thông tin cấu hình ngân hàng: " + ex.getMessage());
            response.put("data", new HashMap<>());
            return response;
        }
    }

    @Override
    public Map<String, Object> dangKyKcbKios(String json, String merchantId, String timestamp, String sign) {
        Map<String, Object> response = new HashMap<>();
        try {
            Map<String, Object> data = new HashMap<>();
            ObjectMapper mapper = new ObjectMapper();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            Map bankCofig = getBankConfig("HD_BANK_KIOS", merchantId);
            String secretKey = bankCofig.get("SECRET_KEY").toString();
            String dvtt = bankCofig.get("DVTT").toString();

            try {
                data = mapper.readValue(json, Map.class);
                Map<String, Object> thongTinBenhNhan = (Map<String, Object>) data.get("THONG_TIN_BENH_NHAN");
                Map<String, Object> thongTinDichVu = (Map<String, Object>) data.get("THONG_TIN_DICH_VU");
                String idBN = String.valueOf(thongTinBenhNhan.get("ID_BN"));
                String maBN = String.valueOf(thongTinBenhNhan.get("MA_BN"));

                if (maBN.equals("null")) {
                    maBN = "";
                } else {
                    String BN = checkTNBenhNhan(dvtt, maBN, String.valueOf(thongTinBenhNhan.get("SO_GTTT")));
                    if (BN != null && !BN.isEmpty()) {
                        return createErrorResponse("Bệnh nhân đã đăng ký trong ngày");
                    }
                }

                String hoTen = String.valueOf(thongTinBenhNhan.get("HO_TEN"));
                if (Objects.equals(hoTen, "")) {
                    return createErrorResponse("HO_TEN không được để trống");
                }
                String hoBn = String.valueOf(thongTinBenhNhan.get("HO_BN"));
                if (Objects.equals(hoBn, "")) {
                    return createErrorResponse("HO_BN không được để trống");
                }
                String tenBn = String.valueOf(thongTinBenhNhan.get("TEN_BN"));
                if (Objects.equals(tenBn, "")) {
                    return createErrorResponse("TEN_BN không được để trống");
                }
                String maTinhCutru = String.valueOf(thongTinBenhNhan.get("MATINH_CUTRU"));
                if (Objects.equals(maTinhCutru, "")) {
                    return createErrorResponse("MATINH_CUTRU không được để trống");
                }
                String maHuyenCuTru = String.valueOf(thongTinBenhNhan.get("MAHUYEN_CU_TRU"));
                if (Objects.equals(maHuyenCuTru, "")) {
                    return createErrorResponse("MAHUYEN_CU_TRU không được để trống");
                }
                String maXaCuTru = String.valueOf(thongTinBenhNhan.get("MAXA_CU_TRU"));
                if (Objects.equals(maXaCuTru, "")) {
                    return createErrorResponse("MAXA_CU_TRU không được để trống");
                }
                String maDanToc = String.valueOf(thongTinBenhNhan.get("MA_DANTOC"));
                if (Objects.equals(maDanToc, "")) {
                    return createErrorResponse("MA_DANTOC không được để trống");
                }
                String maQuocTich = String.valueOf(thongTinBenhNhan.get("MA_QUOCTICH"));
                if (Objects.equals(maQuocTich, "")) {
                    return createErrorResponse("MA_QUOCTICH không được để trống");
                }
                String maNgheNghiep = String.valueOf(thongTinBenhNhan.get("MA_NGHE_NGHIEP"));
                if (Objects.equals(maNgheNghiep, "")) {
                    return createErrorResponse("MA_NGHE_NGHIEP không được để trống");
                }
                String diaChi = String.valueOf(thongTinBenhNhan.get("DIA_CHI"));
                if (Objects.equals(diaChi, "")) {
                    return createErrorResponse("DIA_CHI không được để trống");
                }
                String ngaySinh = String.valueOf(thongTinBenhNhan.get("NGAY_SINH"));
                if (Objects.equals(ngaySinh, "")) {
                    return createErrorResponse("NGAY_SINH không được để trống");
                }
                int gioiTinh = Integer.parseInt(String.valueOf(thongTinBenhNhan.get("GIOI_TINH")));
                if (Objects.equals(gioiTinh, "")) {
                    return createErrorResponse("GIOI_TINH không được để trống");
                }
                String nhomMau = String.valueOf(thongTinBenhNhan.get("NHOM_MAU"));
                String soGttt = String.valueOf(thongTinBenhNhan.get("SO_GTTT"));
                String ngayCapGttt = String.valueOf(thongTinBenhNhan.get("NGAY_CAP_GTTT"));
                String noiCapGttt = String.valueOf(thongTinBenhNhan.get("NOI_CAP_GTTT"));
                String maTheBhyt = String.valueOf(thongTinBenhNhan.get("MA_THE_BHYT"));
                String maDkbd = String.valueOf(thongTinBenhNhan.get("MA_DKBD"));
                String gtTheTu = String.valueOf(thongTinBenhNhan.get("GT_THE_TU"));
                String gtTheDen = String.valueOf(thongTinBenhNhan.get("GT_THE_DEN"));
                int maDoituongKcb = Integer.parseInt(thongTinBenhNhan.get("MA_DOITUONG_KCB").toString());
                String ngayVao = String.valueOf(thongTinBenhNhan.get("NGAY_VAO"));
                String ngayVaoNoiTru = String.valueOf(thongTinBenhNhan.get("NGAY_VAO_NOI_TRU"));
                String lyDoVnt = String.valueOf(thongTinBenhNhan.get("LY_DO_VNT"));
                String maLyDoVnt = String.valueOf(thongTinBenhNhan.get("MA_LY_DO_VNT"));
                String maLoaiKcb = String.valueOf(thongTinBenhNhan.get("MA_LOAI_KCB"));
                String maCskcb = String.valueOf(thongTinBenhNhan.get("MA_CSKCB"));
                String anhBnCccd = String.valueOf(thongTinBenhNhan.get("ANH_BN_CCCD"));
                String noiLamViec = String.valueOf(thongTinBenhNhan.get("NOI_LAM_VIEC"));
                String diaChiLamViec = String.valueOf(thongTinBenhNhan.get("DIA_CHI_LAM_VIEC"));
                String dienThoai = String.valueOf(thongTinBenhNhan.get("DIEN_THOAI"));
                String idKhoa = String.valueOf(thongTinDichVu.get("ID_KHOA"));
                if (Objects.equals(idKhoa, "")) {
                    return createErrorResponse("ID_KHOA không được để trống");
                }
                String idPhongKham = String.valueOf(thongTinDichVu.get("ID_PHONG_KHAM"));
                if (Objects.equals(idPhongKham, "")) {
                    return createErrorResponse("ID_PHONG_KHAM không được để trống");
                }
                String tenPhongKham = String.valueOf(thongTinDichVu.get("TEN_PHONG_KHAM"));
                double dbDongia = Double.parseDouble(String.valueOf(thongTinDichVu.get("DON_GIA_PHONG_KHAM")));
                int donGiaPhongKham = (int) dbDongia;
                String idKhungThoiGian = String.valueOf(thongTinDichVu.get("ID_KHUNG_THOI_GIAN"));
                String idLoaiKham = String.valueOf(data.get("ID_LOAI_KHAM"));
                String duPhong = String.valueOf(data.get("DU_PHONG"));
                String soGiayChuyenTuyen = String.valueOf(data.get("SO_GIAY_CHUYEN_TUYEN"));
                String maBenhChuyenTuyen = String.valueOf(data.get("MA_BENH_CHUYEN_TUYEN"));
                String donViChuyenTuyen = String.valueOf(data.get("DON_VI_CHUYEN_TUYEN"));

                String dataVerify = String.join("|", merchantId, timestamp, maBN);
                if (!HMACSHA256Util.verifyHMACSHA256(dataVerify, secretKey, sign)) {
                    return createErrorResponse("Sai chữ ký");
                }

                JdbcTemplate jdbcTemplate = new JdbcTemplate(this.dataSourceMNG);
                TiepnhanObj tiepnhan = null;
                LocalDate currentDate = LocalDate.now();
                String qrCode = "";
                String billNumber = "";
                String tiepNhan = "";
                Map infoTiepNhan = Collections.emptyMap();
                String idTiepNhan = "";
                if (Objects.equals(maTheBhyt, "")) {
                    Thamsohethong tsht = userDAO.thamsohethong(dvtt);
                    tiepnhan = new TiepnhanObj();

                    tiepnhan.dvtt = dvtt;
                    tiepnhan.hoten = hoTen;
                    tiepnhan.socmt = soGttt;
                    tiepnhan.namsinh = ngaySinh;
                    LocalDate dateOfBirth = LocalDate.parse(tiepnhan.namsinh, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    int age = Period.between(dateOfBirth, currentDate).getYears();
                    tiepnhan.tuoi = age;
                    if (age < 6) {
                        int months = Period.between(dateOfBirth, currentDate).getMonths();
                        int totalMonths = Period.between(dateOfBirth, currentDate).getYears() * 12 + months;
                        tiepnhan.thang = totalMonths;
                    } else {
                        tiepnhan.thang = 0;
                    }
                    tiepnhan.gioitinh = Boolean.parseBoolean(String.valueOf(thongTinBenhNhan.get("GIOI_TINH")));
                    tiepnhan.nghenghiep = maNgheNghiep;
                    tiepnhan.sodt = dienThoai;
                    tiepnhan.maTinhTT30 = maTinhCutru;
                    tiepnhan.maHuyenTT30 = maHuyenCuTru;
                    tiepnhan.maXaTT30 = maXaCuTru;
                    tiepnhan.lyDoVaoVien = "0";
                    tiepnhan.khamuutien = Boolean.parseBoolean("0");
                    tiepnhan.diachi = diaChi;
                    tiepnhan.madichvu = Integer.parseInt(idPhongKham);//Integer.parseInt(idLoaiKham); - Mã dịch vụ
                    tiepnhan.phongkham = idKhoa;// idPhongKham; mã phòng khám
                    tiepnhan.manhanvien = 1351105;
                    tiepnhan.sobhyt = "";
                    tiepnhan.tungay = null;
                    tiepnhan.denngay = null;
                    tiepnhan.noidangky = maDkbd;
                    tiepnhan.mabenhnhan = Integer.parseInt(maBN.equals("")? "0": maBN);
                    tiepnhan.cobh = false;
                    tiepnhan.quoc_tich = maQuocTich;

                    infoTiepNhan = themTiepNhanKhongBaoHiem(tiepnhan);
                    String idBn = (String) infoTiepNhan.get("ID_BN");
                    BigDecimal sttHangngay = (BigDecimal) infoTiepNhan.get("STT_HANGNGAY");
                    int sttHangngayInt = sttHangngay.intValue();
                    tiepNhan = insertTiepNhanKiosDV(dvtt, idBn, sttHangngayInt, ngayVao, ngayVaoNoiTru, idKhoa, idKhoa, tenPhongKham, donGiaPhongKham);
                } else {
                    if (Objects.equals(dvtt, "96019") || Objects.equals(dvtt, "96014") || Objects.equals(dvtt, "96011")) {
                        tiepnhan = new TiepnhanObj();
                        tiepnhan.dvtt = dvtt;
                        tiepnhan.hoten = hoTen;
                        tiepnhan.socmt = soGttt;
                        tiepnhan.namsinh = ngaySinh;
                        LocalDate dateOfBirth = LocalDate.parse(tiepnhan.namsinh, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                        int age = Period.between(dateOfBirth, currentDate).getYears();
                        tiepnhan.tuoi = age;
                        if (age < 6) {
                            int months = Period.between(dateOfBirth, currentDate).getMonths();
                            int totalMonths = Period.between(dateOfBirth, currentDate).getYears() * 12 + months;
                            tiepnhan.thang = totalMonths;
                        } else {
                            tiepnhan.thang = 0;
                        }
                        tiepnhan.gioitinh = Boolean.parseBoolean(String.valueOf(thongTinBenhNhan.get("GIOI_TINH")));
                        tiepnhan.nghenghiep = maNgheNghiep;
                        tiepnhan.sodt = dienThoai;
                        tiepnhan.maTinhTT30 = maTinhCutru;
                        tiepnhan.maHuyenTT30 = maHuyenCuTru;
                        tiepnhan.maXaTT30 = maXaCuTru;
                        tiepnhan.lyDoVaoVien = "0";
                        tiepnhan.khamuutien = Boolean.parseBoolean("0");
                        tiepnhan.diachi = diaChi;
                        tiepnhan.madichvu = Integer.parseInt(idPhongKham);//Integer.parseInt(idLoaiKham); - Mã dịch vụ
                        tiepnhan.phongkham = idKhoa;// idPhongKham; mã phòng khám
                        tiepnhan.manhanvien = 1351105;
                        tiepnhan.sobhyt = maTheBhyt;

                        String signCheckBHYT = String.join("|", dvtt, timestamp, soGttt, "CCCD");
                        String sql = "call HIS_MANAGER.GET_BANK_CONFIG(?,?)#c,s,s";
                        Map bankQuery =  jdbcTemplate.queryForMap(sql, new Object[]{dvtt, "HD_BANK_KIOS"});
                        String dataSign = encode(signCheckBHYT, bankQuery.get("SECRET_KEY").toString());

                        Map<String, Object> result = bhytKios(dvtt, timestamp, dataSign, soGttt, "CCCD", hoTen, ngaySinh, gioiTinh);
                        if (result != null && result.containsKey("data")) {
                            Object dataObj = result.get("data");
                            Map<String, Object> dataMap = (Map<String, Object>) dataObj;
                            tiepnhan.tungay = (String) dataMap.get("GT_THE_TU");
                            tiepnhan.denngay = (String) dataMap.get("GT_THE_DEN");
                            tiepnhan.noidangky = (String) dataMap.get("MA_DKBD");
                        }

                        tiepnhan.mabenhnhan = Integer.parseInt(maBN.equals("")? "0": maBN);
                        tiepnhan.cobh = true;
                        tiepnhan.quoc_tich = maQuocTich;
                        infoTiepNhan = themTiepNhanKhongBaoHiem(tiepnhan);
                        idTiepNhan = (String) infoTiepNhan.get("ID_BN");
                        tiepNhan = insertTiepNhanKios(dvtt, idBN, maBN, hoTen, hoBn, tenBn, maTinhCutru, maHuyenCuTru, maXaCuTru, maDanToc, maQuocTich, maNgheNghiep, diaChi, ngaySinh, gioiTinh, nhomMau, soGttt, ngayCapGttt,
                                noiCapGttt, maTheBhyt, maDkbd, gtTheTu, gtTheDen, maDoituongKcb, ngayVao, ngayVaoNoiTru, lyDoVnt, maLyDoVnt,
                                maLoaiKcb, maCskcb, anhBnCccd, noiLamViec, diaChiLamViec, dienThoai, idKhoa, idKhoa,
                                tenPhongKham, donGiaPhongKham, idKhungThoiGian, idPhongKham//idLoaiKham
                                , duPhong, soGiayChuyenTuyen, maBenhChuyenTuyen, donViChuyenTuyen);
                    } else {
                        tiepNhan = insertTiepNhanKios(dvtt, idBN, maBN, hoTen, hoBn, tenBn, maTinhCutru, maHuyenCuTru, maXaCuTru, maDanToc, maQuocTich, maNgheNghiep, diaChi, ngaySinh, gioiTinh, nhomMau, soGttt, ngayCapGttt,
                                noiCapGttt, maTheBhyt, maDkbd, gtTheTu, gtTheDen, maDoituongKcb, ngayVao, ngayVaoNoiTru, lyDoVnt, maLyDoVnt,
                                maLoaiKcb, maCskcb, anhBnCccd, noiLamViec, diaChiLamViec, dienThoai, idKhoa, idKhoa,
                                tenPhongKham, donGiaPhongKham, idKhungThoiGian, idPhongKham//idLoaiKham
                                , duPhong, soGiayChuyenTuyen, maBenhChuyenTuyen, donViChuyenTuyen);
                    }
                }

                if (Objects.equals(maTheBhyt, "")) {
                    billNumber = getBillNumber(dvtt, infoTiepNhan.get("MA_BN").toString(), Integer.parseInt(infoTiepNhan.get("SO_VAO_VIEN").toString()), 0, "MaBN-" + infoTiepNhan.get("MA_BN").toString(),
                            donGiaPhongKham, "HD_BANK_KIOS");
                    if (!billNumber.isEmpty()) {
                        qrCode = HDBankQRGenerate(dvtt, "HD_BANK_KIOS", billNumber, String.valueOf(donGiaPhongKham), "MaBN-" + infoTiepNhan.get("MA_BN").toString()
                                , infoTiepNhan.get("MA_BN").toString(), hoTen);
                    }
                }

                String sql = "call HIS_MANAGER.CMU_GET_TTBN_KIOSK(?,?,?)#c,s,s,s";
                Map thongTinBnQuery = jdbcTemplate.queryForMap(sql, new Object[]{dvtt, maBN, soGttt});

                String sqltttn = "call HIS_MANAGER.CMU_GET_TT_TIEPNHAN_KIOSK(?,?)#c,s,s";
                Map<String, Object> thongTinTnQuery;
                if(Objects.equals(dvtt, "96019") || Objects.equals(dvtt, "96014")){
                    thongTinTnQuery = jdbcTemplate.queryForMap(sqltttn, new Object[]{dvtt, idTiepNhan});
                } else {
                    thongTinTnQuery = jdbcTemplate.queryForMap(sqltttn, new Object[]{dvtt, tiepNhan});
                }

                String sqlttdk = "call HIS_MANAGER.CMU_GET_TT_DANGKY_KIOSK(?,?)#c,s,s";
                Map thongTinDkQuery = jdbcTemplate.queryForMap(sqlttdk, new Object[]{dvtt, tiepNhan});

                String sqlctdk = "call HIS_MANAGER.CMU_GET_CT_DANGKY_KIOSK(?,?)#c,s,s";
                Map chiTietDkQuery = jdbcTemplate.queryForMap(sqlctdk, new Object[]{dvtt, thongTinDkQuery.get("ID_DANG_KY").toString()});

                List<Map<String, Object>> updatedDataList = new ArrayList<>();
                updatedDataList.add(chiTietDkQuery);
                thongTinDkQuery.put("CHI_TIET_DANG_KY", updatedDataList);

                Map<String, Object> thongTinThanhToan = new HashMap<>();
                thongTinThanhToan.put("QR_CODE", qrCode);
                thongTinThanhToan.put("SO_PHIEU", billNumber);

                Map<String, Object> dataResponse = new HashMap<>();
                if(Objects.equals(maTheBhyt, "") || Objects.equals(dvtt, "96019") || Objects.equals(dvtt, "96014")){
                    dataResponse.put("THONG_TIN_BENH_NHAN", infoTiepNhan);
                } else {
                    dataResponse.put("THONG_TIN_BENH_NHAN", thongTinBnQuery);
                }
                dataResponse.put("THONG_TIN_TIEP_NHAN", thongTinTnQuery);
                dataResponse.put("THONG_TIN_DANG_KY", thongTinDkQuery);
                dataResponse.put("THONG_TIN_THANH_TOAN", thongTinThanhToan);
                return createSuccessResponse(dataResponse, "Nội dung thông báo");
            } catch (IOException e) {
                e.printStackTrace();
                throw new IOException("Error parsing JSON", e);
            }

        } catch (Exception ex) {
            return createErrorResponse("Thêm dữ liệu thất bại: " + ex.getMessage());
        }
    }

    @Override
    public
    Map themTiepNhanKhongBaoHiem(TiepnhanObj tiepnhan) throws Exception {
        try {
            String sql = "";
            Thamsohethong tsht = userDAO.thamsohethong(tiepnhan.dvtt);
            tiepnhan.dantoc = "01";
            tiepnhan.capcuu = Boolean.parseBoolean("false");
            tiepnhan.ngaytiepnhan = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            tiepnhan.capdonvi = tsht.tuyenbenhvien;
            tiepnhan.maphuongxa = 0;
            tiepnhan.congty = false;
            tiepnhan.trangthai = 1;
            tiepnhan.ngay = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            tiepnhan.ngaygio = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            tiepnhan.ngayhientai = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            tiepnhan.khamsuckhoe = false;
            tiepnhan.ttcongkham = false;
            tiepnhan.thanhtoan = 0;
            tiepnhan.thanhtoan_yc = 0;
            tiepnhan.douutien = 0;
            tiepnhan.canhbao = false;
            tiepnhan.xacnhansaisot = 0;
            tiepnhan.tnbnnoitru=Boolean.parseBoolean("false");
            tiepnhan.matinh = tsht.matinh;
            if (!tiepnhan.sobhyt.equals("")) {
                tiepnhan.matinhsothe = tiepnhan.sobhyt.substring(3, 5);
                tiepnhan.madoituongbhyt = tiepnhan.sobhyt.substring(0, 3);
                if ((tiepnhan.matinh + "").equals(tiepnhan.matinhsothe)) {
                    tiepnhan.noitinh = true;
                } else {
                    tiepnhan.noitinh = false;
                }
                tiepnhan.bakytudau = tiepnhan.madoituongbhyt;
                tiepnhan.haikytudau = tiepnhan.sobhyt.substring(0, 2);
                tiepnhan.saisot = "0";
                tiepnhan.conthuoc = "0";
                tiepnhan.tiepnhandichvukhac = "0";
            }else {
                tiepnhan.matinh = "0";
                tiepnhan.madoituongbhyt = "";
                tiepnhan.matinhsothe = "0";
                tiepnhan.noitinh = false;
                tiepnhan.bakytudau = "";
                tiepnhan.haikytudau = "";
                tiepnhan.saisot = "0";
                tiepnhan.conthuoc = "0";
                tiepnhan.tiepnhandichvukhac = "0";
            }
            tiepnhan.idtiepnhan = "";
            tiepnhan.sttmaxngay = "0";

            String ngay[] = tiepnhan.ngayhientai.split("-");
            tiepnhan.thang_hientai = ngay[1];
            tiepnhan.namhientai = ngay[0];
            sql = "select ma_donviquanly from his_fw.dm_donvi  where ma_donvi = ?";
            tiepnhan.mabvtuyentren = tiepnhanDAO.queryForString(sql, new Object[]{tiepnhan.dvtt});
            if (tiepnhan.mabenhnhan > 0) {
                tiepnhan.kiemtra_TT_mabenhnhan = "1";
            } else {
                tiepnhan.kiemtra_TT_mabenhnhan = "0";
            }
            tiepnhan.stt_trongphong = "0";
            tiepnhan.phantrambaohiem = "0";
            tiepnhan.kiemtradungtuyen = 0;
            tiepnhan.themthanhcong_tiepnhan = "0";
            tiepnhan.quoc_tich = "0";

            Map map = new HashMap();
            map = this.tiepnhanDAO.themtiepnhan_giamtai2(tiepnhan, tsht.hienthitienchenhlech);

            Map mapResult = new HashMap();
            if (!map.isEmpty()) {
                if (map.get("ID_TIEPNHAN") != null && !map.get("ID_TIEPNHAN").equals("")) {
                    if (!map.get("SOVAOVIEN").toString().equals("0")) {
                        mapResult.put("ID_BN", map.get("ID_TIEPNHAN"));
                        mapResult.put("MA_BN", map.get("MA_BENH_NHAN").toString());
                        mapResult.put("STT_HANGNGAY", map.get("STT_HANGNGAY"));
                        mapResult.put("HO_TEN", tiepnhan.hoten);
                        mapResult.put("MATINH_CUTRU", tiepnhan.maTinhTT30);
                        mapResult.put("MAHUYEN_CU_TRU", tiepnhan.maHuyenTT30);
                        mapResult.put("MAXA_CU_TRU", tiepnhan.maXaTT30);
                        mapResult.put("MA_DANTOC", tiepnhan.dantoc);
                        mapResult.put("MA_QUOCTICH", tiepnhan.quoc_tich);
                        mapResult.put("MA_NGHE_NGHIEP", tiepnhan.nghenghiep);
                        mapResult.put("DIA_CHI", tiepnhan.diachi);
                        mapResult.put("NGAY_SINH", tiepnhan.namsinh);
                        mapResult.put("GIOI_TINH", tiepnhan.gioitinh == true? 1: 2);
                        mapResult.put("NHOM_MAU", "");
                        mapResult.put("EMAIL", "");
                        mapResult.put("SO_GTTT", tiepnhan.socmt);
                        mapResult.put("MA_THE_BHYT", tiepnhan.sobhyt);
                        mapResult.put("SO_VAO_VIEN", map.get("SOVAOVIEN"));
                    }
                }
            }
            return mapResult;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public String HDBankQRGenerate(String dvtt, String bank, String invoiceId, String amount, String description, String mabenhnhan, String tenbenhnhan){
        String qrString = "";
        try {
            String sql = "call HIS_MANAGER.GET_BANK_CONFIG(?,?)#c,s,s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(this.dataSourceMNG);
            Map bankQuery =  jdbcTemplate.queryForMap(sql, new Object[]{dvtt, bank});

            String merchantId = bankQuery.get("MERCHANTID").toString();
            String type = "vietqr";
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            String transactionTime = dateFormat.format(new Date());
            String serviceCode = bankQuery.get("PROVIDERID").toString();
            String ipn = bankQuery.get("URL").toString();

            String jsonString = "{" +
                    "\"merchantId\": \"" + merchantId + "\"," +
                    "\"invoiceId\": \"" + invoiceId + "\"," +
                    "\"type\": \"" + type + "\"," +
                    "\"transactionAmount\": " + amount + "," +
                    "\"additionalData\": {" +
                    "\"serviceCode\": \"" + serviceCode + "\"," +
                    "\"patientCode\": \"" + mabenhnhan + "\"," +
                    "\"patientName\": \"" + tenbenhnhan + "\"," +
                    "\"description\": \"" + description + "\"" +
                    "}," +
                    "\"transactionTime\": " + transactionTime +"," +
                    "\"ipn\": \"" + ipn + "\"" +
                    "}";

            String base64EncodedJson = Base64.getEncoder().encodeToString(jsonString.getBytes(StandardCharsets.UTF_8));
            String dataSign = encode(base64EncodedJson, bankQuery.get("SECRET_KEY").toString());
            OkHttpClient client = new OkHttpClient().newBuilder().build();
            okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json");
            String dataRequest = "{" +
                    "\"data\": \"" + base64EncodedJson + "\"," +
                    "\"sign\": \"" + dataSign + "\"" +
                    "}";
            RequestBody requestBody = RequestBody.create(dataRequest, mediaType);
            Request request = new Request
                    .Builder()
                    .url(bankQuery.get("URL_QR").toString())
                    .method("POST", requestBody)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Accept", "application/json")
                    .build();

            Response response = client.newCall(request).execute();
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.disable(FAIL_ON_UNKNOWN_PROPERTIES);
            String responseStr = Objects.requireNonNull(response.body()).string();
            ObjectMapper mapper = new ObjectMapper();
            Map resultQr = mapper.readValue(responseStr, Map.class);
            qrString = ((Map<String, String>) resultQr.get("data")).get("qrCode");

        } catch (Exception ex) {
            return "Error 1";
        }

        return qrString;
    }

    @Override
    public Map<String, Object> trangThaiPhieu(String merchantId, String timestamp, String sign, String soPhieu) {
        try {
            Map bankCofig = getBankConfig("HD_BANK_KIOS", merchantId);
            String secretKey = bankCofig.get("SECRET_KEY").toString();
            String dvtt = bankCofig.get("DVTT").toString();
            String dataVerify = merchantId + "|" + timestamp + "|" + soPhieu;

            String sql = "call HIS_MANAGER.CMU_TRANG_THAI_PHIEU_KIOS(?,?)#c,s,s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(this.dataSourceMNG);
            Map trangThaiPhieuQuery =  jdbcTemplate.queryForMap(sql, new Object[]{dvtt, soPhieu});
            Map<String, Object> dataResponse = new HashMap<>();
            dataResponse.put("SO_PHIEU", trangThaiPhieuQuery.get("SO_PHIEU").toString());
            dataResponse.put("SO_TIEN", Integer.parseInt(String.valueOf(trangThaiPhieuQuery.get("SO_TIEN"))));
            dataResponse.put("DA_THANH_TOAN", trangThaiPhieuQuery.get("DA_THANH_TOAN").toString().equals("1"));

            if (!HMACSHA256Util.verifyHMACSHA256(dataVerify, secretKey, sign)) {
                return createErrorResponse("Sai chữ ký");
            }

            try {
                return createSuccessResponse(dataResponse, "Trạng thái thanh toán");
            } catch (Exception ex) {
                return createErrorResponse("Lỗi lấy trạng thái thanh toán: " + ex.getMessage());
            }
        } catch (Exception ex) {
            return createErrorResponse("Lỗi lấy trạng thái thanh toán: " + ex.getMessage());
        }
    }

    @Override
    public Map<String, Object> bhytKios(String merchantId, String timestamp, String sign, String soGttt, String loaiGttt, String hoTen, String ngaySinh, Number gioiTinh) {
        Map<String, Object> response = new HashMap<>();
        String dataNgaySinh = (ngaySinh.length() == 4)
                ? ngaySinh
                : String.valueOf(LocalDate.parse(ngaySinh, DateTimeFormatter.ISO_LOCAL_DATE).getYear());
        try {
            Map bankCofig = getBankConfig("HD_BANK_KIOS", merchantId);
            String secretKey = bankCofig.get("SECRET_KEY").toString();
            String dvtt = bankCofig.get("DVTT").toString();
            String dataVerify = merchantId + "|" + timestamp + "|" + soGttt + "|" + loaiGttt;

            String sql = "call HIS_MANAGER.CMU_GET_DEFAULT_USER(?)#c,s,s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(this.dataSourceMNG);
            Map userQuery =  jdbcTemplate.queryForMap(sql, new Object[]{dvtt});

            RestTemplate rest = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            String thamso_taikhoan_xml;
            String thamso_matkhau_xml;
            try {
                thamso_taikhoan_xml = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "123");
            } catch (Exception e) {
                thamso_taikhoan_xml = dvtt + "_BV";
            }
            try {
                String password = thamsodonviDAO.laythamso_donvi_motthamso(dvtt, "124");
                String passwordmd5 = tienich.getMD5(password);
                thamso_matkhau_xml = passwordmd5;
            } catch (Exception e) {
                thamso_matkhau_xml = tienich.getMD5("123456");
            }

            if (!HMACSHA256Util.verifyHMACSHA256(dataVerify, secretKey, sign)) {
                return createErrorResponse("Sai chữ ký");
            }

            try {
                String tenNhanvien = userQuery.get("TEN_NHANVIEN").toString();
                String soCCCD = userQuery.get("SO_CCCD_NV").toString();
                HttpComponentsClientHttpRequestFactory clientHttpRequestFactory
                        = new HttpComponentsClientHttpRequestFactory();
                clientHttpRequestFactory.setConnectTimeout(1000);
                clientHttpRequestFactory.setReadTimeout(2000);
                RestTemplate restoken = new RestTemplate(clientHttpRequestFactory);
                headers.setContentType(MediaType.APPLICATION_JSON);
                Map map = XuatXMLBHYTDAO.getTokenBHXH(dvtt, thamso_taikhoan_xml, thamso_matkhau_xml, restoken);
                theBHYT_2018 the = new theBHYT_2018(soGttt, hoTen, dataNgaySinh);
                String apiLink = "https://egw.baohiemxahoi.gov.vn/api/egw/KQNhanLichSuKCB2024";
                Map apiKetNoi = dmApidao.select_API_LINK(dvtt, "14");
                if (apiKetNoi != null && !apiKetNoi.isEmpty()) {
                    apiLink = String.valueOf(apiKetNoi.get("API_LINK"));
                }
                String urll = apiLink + "?token="
                        + map.get("access_token") + "&id_token=" + map.get("id_token")
                        + "&username=" + thamso_taikhoan_xml + "&password=" + thamso_matkhau_xml;
                HashMap<String, String> dynamicBodyTheBHYT = new HashMap<>();
                dynamicBodyTheBHYT.put("maThe", soGttt);
                dynamicBodyTheBHYT.put("hoTen", hoTen);
                dynamicBodyTheBHYT.put("ngaySinh", dataNgaySinh);
                dynamicBodyTheBHYT.put("hoTenCb", tenNhanvien);
                dynamicBodyTheBHYT.put("cccdCb", soCCCD);
                HttpEntity<Map> entity2 = new HttpEntity<>(dynamicBodyTheBHYT, headers);
                ResponseEntity<Map> responseEntity2 = rest.postForEntity(urll, entity2, Map.class);
                Map<String, Object> responseBody = responseEntity2.getBody();

                String ngaySinhResponse = responseBody.get("ngaySinh").toString();
                String ngaySinhBHYT = (ngaySinhResponse.length() == 4) ?  "01/01/" +ngaySinhResponse : ngaySinhResponse;

                Map<String, Object> dataResponse = new HashMap<>();
                dataResponse.put("HO_TEN", responseBody.get("hoTen").toString());
                dataResponse.put("MA_THE_BHYT", responseBody.get("maThe").toString());
                dataResponse.put("DIA_CHI", responseBody.get("diaChi").toString());
                dataResponse.put("NGAY_SINH", convertDateFormat(ngaySinhBHYT));
                dataResponse.put("GIOI_TINH", responseBody.get("gioiTinh").toString().equalsIgnoreCase("nam") ? 1 : 0);
                dataResponse.put("MA_DKBD", responseBody.get("maDKBD").toString());
                dataResponse.put("GT_THE_TU", convertDateFormat(responseBody.get("gtTheTu").toString()));
                dataResponse.put("GT_THE_DEN", convertDateFormat(responseBody.get("gtTheDen").toString()));
                if (responseBody.get("maDKBD").toString() == dvtt) {
                    dataResponse.put("PHAN_TUYEN", 1);
                } else {
                    dataResponse.put("PHAN_TUYEN", 2);
                }
                dataResponse.put("MA_DKBD", responseBody.get("maDKBD").toString());
                dataResponse.put("MA_KQ", responseBody.get("maKetQua").toString());
                if (responseBody.get("maKetQua").toString().equals("000")) {
                    dataResponse.put("TEN_KQ", "Kết quả kiểm tra hợp lệ");
                } else {
                    dataResponse.put("TEN_KQ", "Kết quả kiểm tra không hợp lệ");
                }
                dataResponse.put("TIEP_NHAN_BHYT", 1);

                return createSuccessResponse(dataResponse, "Thông tin bệnh nhân BHYT");
            } catch (Exception ex) {
                return createErrorResponse("Lỗi lấy thông tin bệnh nhân: " + ex.getMessage());
            }
        } catch (Exception ex) {
            return createErrorResponse("Lỗi lấy thông tin: " + ex.getMessage());
        }
    }

    @Override
    public Map<String, Object> benhNhanKios(String merchantId, String timestamp, String sign, String soGttt, String loaiGttt, String dienThoai) {
        try {
            Map bankCofig = getBankConfig("HD_BANK_KIOS", merchantId);
            String secretKey = bankCofig.get("SECRET_KEY").toString();
            String dvtt = bankCofig.get("DVTT").toString();
            String dataVerify = String.join("|", merchantId, timestamp, soGttt, loaiGttt);

            if (!HMACSHA256Util.verifyHMACSHA256(dataVerify, secretKey, sign)) {
                return createErrorResponse("Sai chữ ký");
            }

            String sql = "call HIS_MANAGER.CMU_GET_BENHNHAN_KIOSK(?,?,?)#c,s,s,s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(this.dataSourceMNG);
            Map benhNhanQuery =  jdbcTemplate.queryForMap(sql, new Object[]{dvtt, soGttt, dienThoai});
            if(benhNhanQuery == null) {
                return createError404Response("Không tìm thấy thông tin bệnh nhân");
            }
            Map<String, Object> dataResponse = new HashMap<>();
            dataResponse.put("ID_BN", benhNhanQuery.get("ID_BN") != null ? benhNhanQuery.get("ID_BN").toString() : "");
            dataResponse.put("MA_BN", benhNhanQuery.get("MA_BN") != null ? benhNhanQuery.get("MA_BN").toString() : "");
            dataResponse.put("HO_TEN", benhNhanQuery.get("HO_TEN") != null ? benhNhanQuery.get("HO_TEN").toString() : "");
            dataResponse.put("HO_BN", benhNhanQuery.get("HO_BN") != null ? benhNhanQuery.get("HO_BN").toString() : "");
            dataResponse.put("MATINH_CUTRU", benhNhanQuery.get("MATINH_CUTRU") != null ? benhNhanQuery.get("MATINH_CUTRU").toString() : "");
            dataResponse.put("MAHUYEN_CU_TRU", benhNhanQuery.get("MAHUYEN_CU_TRU") != null ? benhNhanQuery.get("MAHUYEN_CU_TRU").toString() : "");
            dataResponse.put("MAXA_CU_TRU", benhNhanQuery.get("MAXA_CU_TRU") != null ? benhNhanQuery.get("MAXA_CU_TRU").toString() : "");
            dataResponse.put("MA_DANTOC", benhNhanQuery.get("MA_DANTOC") != null ? benhNhanQuery.get("MA_DANTOC").toString() : "");
            dataResponse.put("MA_QUOCTICH", benhNhanQuery.get("MA_QUOCTICH") != null ? benhNhanQuery.get("MA_QUOCTICH").toString() : "01");
            dataResponse.put("MA_NGHE_NGHIEP", benhNhanQuery.get("MA_NGHE_NGHIEP") != null ? benhNhanQuery.get("MA_NGHE_NGHIEP").toString() : "");
            dataResponse.put("DIA_CHI", benhNhanQuery.get("DIA_CHI") != null ? benhNhanQuery.get("DIA_CHI").toString() : "");
            dataResponse.put("NGAY_SINH", benhNhanQuery.get("NGAY_SINH") != null ? benhNhanQuery.get("NGAY_SINH").toString() : "");
            dataResponse.put("GIOI_TINH", benhNhanQuery.get("GIOI_TINH"));
            dataResponse.put("NHOM_MAU", benhNhanQuery.get("NHOM_MAU") != null ? benhNhanQuery.get("NHOM_MAU").toString() : "");
            dataResponse.put("EMAIL", benhNhanQuery.get("EMAIL") != null ? benhNhanQuery.get("EMAIL").toString() : "");
            dataResponse.put("DIEN_THOAI", benhNhanQuery.get("DIEN_THOAI") != null ? benhNhanQuery.get("DIEN_THOAI").toString() : "");
            dataResponse.put("MA_DINH_DANH", benhNhanQuery.get("MA_DINH_DANH") != null ? benhNhanQuery.get("MA_DINH_DANH").toString() : "");
            dataResponse.put("MA_THE_BHYT", benhNhanQuery.get("MA_THE_BHYT") != null ? benhNhanQuery.get("MA_THE_BHYT").toString() : "");

            try {
                return createSuccessResponse(dataResponse, "Đăng ký khám thành công");
            } catch (Exception ex) {
                return createErrorResponse("Lỗi lấy thông tin bệnh nhân: " + ex.getMessage());
            }
        } catch (Exception ex) {
            return createErrorResponse("Lỗi lấy thông tin: " + ex.getMessage());
        }
    }

    @Override
    public Map<String, Object> dichVuLichKhamKios(String merchantId, String timestamp, String sign, String maPhongKham, String thoiGianBatDau, String thoiGianKetThuc) {
        try {
            Map<String, Object> bankConfig = getBankConfig("HD_BANK_KIOS", merchantId);
            String secretKey = (String) bankConfig.get("SECRET_KEY");
            String dvtt = (String) bankConfig.get("DVTT");

            String dataVerify = String.join("|", merchantId, timestamp, maPhongKham, thoiGianBatDau, thoiGianKetThuc);
            if (!HMACSHA256Util.verifyHMACSHA256(dataVerify, secretKey, sign)) {
                return createErrorResponse("Sai chữ ký");
            }

            List<Map<String, Object>> listLichKham = getDanhSachLichKham(dvtt, thoiGianBatDau, thoiGianKetThuc, maPhongKham);
            Map<String, Object> dataResponse = new HashMap<>();
            dataResponse.put("GHI_CHU", "Đặt tối đa 3 ngày");
            dataResponse.put("KHUNG_THOI_GIAN", listLichKham);

            return createSuccessResponse(dataResponse, "Success");

        } catch (Exception ex) {
            return createErrorResponse("Lỗi lấy thông tin: " + ex.getMessage());
        }
    }

    public Map<String, Object> dichVuCoTonTaiKios(String merchantId, String timestamp, String sign, String maKhoa, String thoiGianBatDau, String thoiGianKetThuc) {
        try {
            Map<String, Object> bankConfig = getBankConfig("HD_BANK_KIOS", merchantId);
            String secretKey = (String) bankConfig.get("SECRET_KEY");
            String dvtt = (String) bankConfig.get("DVTT");

            String dataVerify = String.join("|", merchantId, timestamp, maKhoa, thoiGianBatDau, thoiGianKetThuc);
            if (!HMACSHA256Util.verifyHMACSHA256(dataVerify, secretKey, sign)) {
                return createErrorResponse("Sai chữ ký");
            }

            String sql = "call HIS_MANAGER.CMU_DV_COTONTAI(?,?,?,?)#c,s,s,s,s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(this.dataSourceMNG);
            Map trangThaiPhieuQuery =  jdbcTemplate.queryForMap(sql, new Object[]{dvtt, maKhoa, thoiGianBatDau, thoiGianKetThuc});
            Map<String, Object> dataResponse = new HashMap<>();
            dataResponse.put("ID", trangThaiPhieuQuery.get("ID").toString());
            dataResponse.put("CO_TON_TAI", trangThaiPhieuQuery.get("CO_TON_TAI"));

            return createSuccessResponse(dataResponse, "Success");

        } catch (Exception ex) {
            return createErrorResponse("Lỗi lấy thông tin: " + ex.getMessage());
        }
    }

    public Map<String, Object> danhsachphongkham(String merchantId, String timestamp, String sign, String maKhoa, String idKhoa, String idloaikham) {
        try {
            Map<String, Object> bankConfig = getBankConfig("HD_BANK_KIOS", merchantId);
            String secretKey = (String) bankConfig.get("SECRET_KEY");
            String dvtt = (String) bankConfig.get("DVTT");

            String dataVerify = String.join("|", merchantId, timestamp, maKhoa);
            if (!HMACSHA256Util.verifyHMACSHA256(dataVerify, secretKey, sign)) {
                return createErrorResponse("Sai chữ ký");
            }

            List<Map<String, Object>> listPhongKham = getDanhSachPhongKham(dvtt, maKhoa, idKhoa, idloaikham);
            Map<String, Object> response = new HashMap<>();
            response.put("code", "000");
            response.put("message", "Success");
            response.put("data", listPhongKham);
            return response;

        } catch (Exception ex) {
            return createErrorResponse("Lỗi lấy thông tin: " + ex.getMessage());
        }
    }

    public Map<String, Object> laySoTT(String merchantId, String timestamp, String sign, int uuTien, String idKhoa, String idloaikham) {
        try {
            Map<String, Object> bankConfig = getBankConfig("HD_BANK_KIOS", merchantId);
            String secretKey = (String) bankConfig.get("SECRET_KEY");
            String dvtt = (String) bankConfig.get("DVTT");

            String dataVerify = String.join("|", merchantId, timestamp);
            if (!HMACSHA256Util.verifyHMACSHA256(dataVerify, secretKey, sign)) {
                return createErrorResponse("Sai chữ ký");
            }

            String strResult = getSttDangGoi(dvtt, 1, uuTien);
            String[] stt = strResult.split("_");
            Map<String, Object> response = new HashMap<>();
            Map<String, Object> data = new HashMap<>();
            response.put("code", "000");
            response.put("message", "Success");
            data.put("NGAY_LAYSO", convertDateFormat(stt[0]));
            if(uuTien == 1){
                data.put("STT", "000" + stt[2]);
            } else {
                data.put("STT", "000" + stt[1]);
            }
            data.put("MA_PHONG", "");
            data.put("TEN_PHONG", "");
            data.put("MA_DICH_VU", "");
            data.put("TEN_DICH_VU", "");
            response.put("data", data);
            return response;

        } catch (Exception ex) {
            return createErrorResponse("Lỗi lấy thông tin: " + ex.getMessage());
        }
    }

    public List<Map<String, Object>> getDanhSachPhongKham(String dvtt, String maKhoa, String idKhoa, String idloaikham) {
        String sql = "call CMU_GET_LICHPHONGKHAM_KIOS(?,?,?,?)#c,s,s,s,s";
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        List<Map<String, Object>> list = jdbcTemplate.queryForList(sql, new Object[]{dvtt, maKhoa, idKhoa, idloaikham});
        return list;
    }

    private Map<String, Object> createSuccessResponse(Map<String, Object> data, String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", "000");
        response.put("message", message);
        response.put("data", data);
        return response;
    }

    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", "999");
        response.put("message", message);
        response.put("data", null);
        return response;
    }

    private Map<String, Object> createError404Response(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("code", "404");
        response.put("message", message);
        response.put("data", null);
        return response;
    }

    public static String convertDateFormat(String dateStr) throws ParseException {
        SimpleDateFormat inputFormat = new SimpleDateFormat("dd/MM/yyyy");
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date date = inputFormat.parse(dateStr);
        return outputFormat.format(date);
    }

    public static String encode(String data, String key) throws Exception {
        try {
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");

            SecretKeySpec secret_key = new SecretKeySpec(key.getBytes("UTF-8"), "HmacSHA256");
            sha256_HMAC.init(secret_key);

            return Hex.encodeHexString(sha256_HMAC.doFinal(data.getBytes("UTF-8")));
        } catch (Exception e) {
            throw new Exception("Error encoding data with HMAC-SHA256", e);
        }
    }
}