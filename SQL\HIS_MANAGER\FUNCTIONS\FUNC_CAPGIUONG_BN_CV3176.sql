CREATE OR REPLACE FUNCTION func_capgiuong_bn_cv3176 (
    p_dvtt                           IN                               VARCHAR2,
    p_stt_logkhoaphong               IN                               VARCHAR2,
    p_stt_<PERSON><PERSON>                     IN                               VARCHAR2,
    p_stt_dotdieutri                 IN                               VARCHAR2,
    p_<PERSON><PERSON><PERSON><PERSON>                     IN                               NUMBER,
    p_<PERSON><PERSON><PERSON>                     IN                               VARCHAR2,
    v_ngayvao                        IN                               VARCHAR2,
    p_<PERSON><PERSON><PERSON><PERSON><PERSON>                    IN                               NUMBER,
    p_map<PERSON><PERSON>h                    IN                               VARCHAR2,
    p_magiuong<PERSON>h                   IN                               VARCHAR2,
    p_bhyt_thanhtoan                 IN                               NUMBER,
    p_sovaovien                      IN                               NUMBER,
    p_sovaovien_dt                   IN                               NUMBER,
    p_cg_sostt_giuongbenh            NUMBER DEFAULT 1,
    p_cg_loaigiuongke_giuongbenh     VARCHAR2 DEFAULT 'H',
    p_kcapgiuongkhidathanhtoannt     NUMBER DEFAULT 0,
    p_kcapgiuongkhidathanhtoanbant   NUMBER DEFAULT 0,
    p_tyle_tt                        NUMBER DEFAULT 30,
    p_namcung                        IN                               NUMBER DEFAULT 0,-- 1,0
    p_songuoinam                     IN                               NUMBER DEFAULT 1,-- 1,2,3
    v_ngaynhangiuong                 IN                               VARCHAR2 DEFAULT NULL
) RETURN VARCHAR2 IS

    v_sotienbhkchi            NUMBER(18, 4);
    v_ngoaidanhmuc            NUMBER(1);
    v_sothebhyt               VARCHAR2(20);
    v_cobhyt                  NUMBER(1);
    v_tt_dotdieutri           NUMBER(10);
    v_trang_thai              NUMBER(10); -- tr?ng thái b?nh án
    v_trang_thai_logkhoa      NUMBER(10); -- tr?ng thái b?nh án
    v_maxlog                  NUMBER(10);
    v_giagiuongbenh           NUMBER(18);
    v_return_value            VARCHAR2(5);
    v_kiemtra_cg              NUMBER(11);
    v_kiemtra_tt              NUMBER(11);
    v_ktrbant                 NUMBER(3);
    v_thamso26001             NUMBER(1) DEFAULT 0;
    v_thamso26002             NUMBER(1) DEFAULT 0;
    v_kiemtracunggiuong       NUMBER(10) DEFAULT 0;
    --
    p_sql                     CLOB;
    p_thamso                  VARCHAR2(3);
    v_thamso                  NUMBER(5);
    p_thamso82785             VARCHAR2(2);
    p_ngaytao                 DATE := trunc(SYSDATE);
    p_thamso_ngay             DATE;
    p_thamso_gia_bhyt         VARCHAR2(30);
    p_thamso_gia_kbhyt        VARCHAR2(30);
    v_tyle_tt_giuongbanngay   NUMBER(5) DEFAULT to_number(get_tsdv(p_dvtt, '820835 ', '0'));
    p_ngayvao                 TIMESTAMP;
    p_ngaynhangiuong          TIMESTAMP;
BEGIN
    p_ngayvao := to_timestamp(v_ngayvao, 'yyyy-mm-dd hh24:mi:ss');
    p_ngaynhangiuong := to_timestamp(v_ngaynhangiuong, 'dd/mm/yyyy hh24:mi:ss');
    v_thamso26001 := dm_tsdv_sl_mtso(p_dvtt, '26001');
    v_thamso26002 := dm_tsdv_sl_mtso(p_dvtt, '26002');
    IF v_thamso26001 = 1 THEN
        IF v_thamso26002 = 1 THEN
BEGIN
SELECT
    COUNT(a.so_giuong_tai_khoa_so)
INTO v_kiemtracunggiuong
FROM
    his_manager.noitru_loggiuongbenh a
WHERE
    a.maphongban = p_maphongban
  AND a.so_giuong_tai_khoa_so = p_cg_sostt_giuongbenh
  AND a.trang_thai < 3
  AND ( a.ngayra IS NULL
    OR a.songaydieutri IS NULL
    OR ngayra < p_ngayvao )
  AND a.nam_cung_giuong = 0;

EXCEPTION
                WHEN no_data_found THEN
                    v_kiemtracunggiuong := 0;
END;

ELSE
BEGIN
SELECT
    COUNT(a.magiuongbenh)
INTO v_kiemtracunggiuong
FROM
    his_manager.noitru_loggiuongbenh a
WHERE
    a.maphongban = p_maphongban
  AND a.magiuongbenh = p_cg_sostt_giuongbenh--and a.magiuongbenh = p_magiuongbenh
  AND a.trang_thai < 3
  AND ( a.ngayra IS NULL
    OR a.songaydieutri IS NULL
    OR ngayra < p_ngayvao )
  AND a.nam_cung_giuong = 0;

EXCEPTION
                WHEN no_data_found THEN
                    v_kiemtracunggiuong := 0;
END;

            IF ( v_kiemtracunggiuong > 0 ) THEN
                RETURN '-5';
END IF;
END IF;
END IF;

SELECT
    COUNT(1)
INTO v_kiemtra_cg
FROM
    noitru_loggiuongbenh
WHERE
    dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND ngayra IS NULL;

IF v_kiemtra_cg > 0 THEN
        RETURN '-1';
END IF;
SELECT
    trang_thai,
    bant
INTO
    v_trang_thai,
    v_ktrbant
FROM
    noitru_benhan
WHERE
    dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND sovaovien = p_sovaovien;

IF ( p_kcapgiuongkhidathanhtoannt = 1 AND v_ktrbant = 1 ) THEN
SELECT
    COUNT(1)
INTO v_kiemtra_tt
FROM
    his_manager.vienphinoitru_lantt
WHERE
    sovaovien = p_sovaovien
  AND sovaovien_dt = p_sovaovien_dt
  AND dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND tt_lantt = 1;

IF ( v_kiemtra_tt > 0 ) THEN
            RETURN '-10';
END IF;
    ELSIF ( p_kcapgiuongkhidathanhtoanbant = 1 AND v_ktrbant = 0 ) THEN
SELECT
    COUNT(1)
INTO v_kiemtra_tt
FROM
    his_manager.vienphinoitru_lantt
WHERE
    sovaovien = p_sovaovien
  AND sovaovien_dt = p_sovaovien_dt
  AND dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND tt_lantt = 1;

IF ( v_kiemtra_tt > 0 ) THEN
            RETURN '-10';
END IF;
END IF;

SELECT
    tt_dotdieutri,
    cobhyt,
    sobaohiemyte
INTO
    v_tt_dotdieutri,
    v_cobhyt,
    v_sothebhyt
FROM
    noitru_dotdieutri
WHERE
    dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND sovaovien = p_sovaovien
  AND sovaovien_dt = p_sovaovien_dt;
-- TGGDEV 42897

BEGIN
SELECT
    mota_thamso
INTO p_thamso82785
FROM
    his_fw.dm_thamso_donvi
WHERE
    dvtt = p_dvtt
  AND ma_thamso = 82785;

EXCEPTION
        WHEN no_data_found THEN
            p_thamso82785 := '0';
END;

    IF p_thamso82785 = '1' THEN
SELECT
    trunc(ddt.ngayvao)
INTO p_ngaytao
FROM
    his_manager.noitru_dotdieutri ddt
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND sovaovien_dt = p_sovaovien_dt;

END IF;

BEGIN
SELECT
    mota_thamso
INTO p_thamso
FROM
    his_fw.dm_thamso_donvi
WHERE
    dvtt = p_dvtt
  AND ma_thamso = 208;

EXCEPTION
        WHEN no_data_found THEN
            p_thamso := 0;
            v_thamso := 0;
END;

    IF p_thamso != 0 THEN
        LOOP
BEGIN
SELECT
    g.ngay_ap_dung_truyen,
    g.ma_thamso_truyen_truoc
INTO
    p_thamso_ngay,
    v_thamso
FROM
    his_manager.danhmuc_ngayapdung_dvtt g
WHERE
    g.ma_thamso_truyen = p_thamso
  AND g.dvtt = p_dvtt;

EXCEPTION
                WHEN no_data_found THEN
                    p_thamso := 0;
                    v_thamso := 0;
END;

            IF p_thamso != 0 THEN
BEGIN
SELECT
    g.column_gia_bhyt,
    g.column_gia_kbhyt
INTO
    p_thamso_gia_bhyt,
    p_thamso_gia_kbhyt
FROM
    his_manager.danhmuc_giathaydoi g
WHERE
    g.ma_thamso_truyen = p_thamso;

EXCEPTION
                    WHEN no_data_found THEN
                        p_thamso := 0;
                        v_thamso := 0;
END;

END IF;

            IF p_ngaytao < p_thamso_ngay THEN
                p_thamso := v_thamso;
END IF;
            EXIT WHEN p_ngaytao >= p_thamso_ngay OR p_thamso = 0;
END LOOP;
END IF;
    -- END TGGDEV-42897

    IF p_thamso = 0 THEN
SELECT
    CASE
        WHEN v_cobhyt = 0 THEN
            loai.gia_dich_vu
        ELSE
            CASE loai.ngoai_danh_muc
                WHEN 0 THEN
                    loai.gia_loai_giuong
                ELSE
                    loai.gia_dich_vu
                END
        END AS gia_loai_giuong,
    loai.ngoai_danh_muc,
    loai.tien_bh_khongchi
INTO
    v_giagiuongbenh,
    v_ngoaidanhmuc,
    v_sotienbhkchi
FROM
    dm_loaigiuongbenh   loai,
    dm_giuongbenh       giuong
WHERE
    giuong.magiuongbenh = p_magiuongbenh
  AND giuong.dvtt = p_dvtt
  AND loai.dvtt = p_dvtt
  AND giuong.maloaigiuongbenh = loai.maloaigiuongbenh
  AND to_number(giuong.ma_phong_ban) = to_number(p_maphongban);

ELSE
        p_sql := 'SELECT
          case when '
                 || v_cobhyt
                 || ' = 0 then loai.'
                 || p_thamso_gia_kbhyt
                 || '
            else case loai.ngoai_danh_muc when 0 then loai.'
                 || p_thamso_gia_bhyt
                 || ' else loai.'
                 || p_thamso_gia_kbhyt
                 || ' end end AS gia_loai_giuong,
          loai.ngoai_danh_muc,
          loai.tien_bh_khongchi
        FROM
          dm_loaigiuongbenh loai,
          dm_giuongbenh giuong
        WHERE giuong.magiuongbenh = :p_magiuongbenh
          AND giuong.dvtt = :p_dvtt
          AND loai.dvtt = :p_dvtt
          AND giuong.maloaigiuongbenh = loai.maloaigiuongbenh
          AND to_number(giuong.ma_phong_ban) = to_number('
                 || p_maphongban
                 || ')';

EXECUTE IMMEDIATE p_sql
    INTO
            v_giagiuongbenh,
            v_ngoaidanhmuc,
            v_sotienbhkchi
            USING p_magiuongbenh, p_dvtt, p_dvtt;
END IF;
    -- END TGGDEV

SELECT
    trang_thai
INTO v_trang_thai_logkhoa
FROM
    noitru_logkhoaphong
WHERE
    dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_logkhoaphong = p_stt_logkhoaphong
  AND sovaovien = p_sovaovien
  AND sovaovien_dt = p_sovaovien_dt;

IF v_tt_dotdieutri <= 2 THEN
UPDATE noitru_dotdieutri
SET
    tt_dotdieutri = 1 --
WHERE
    dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND sovaovien = p_sovaovien
  AND sovaovien_dt = p_sovaovien_dt;

END IF;

    IF v_trang_thai < 2 THEN
UPDATE noitru_benhan
SET
    trang_thai = 2 -- dang di?u tr?
WHERE
    dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND sovaovien = p_sovaovien;

END IF;

UPDATE noitru_logkhoaphong
SET
    trang_thai = 1 -- c?p nh?t l?i di?u tr? có giu?ng
WHERE
    dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_logkhoaphong = p_stt_logkhoaphong
  AND sovaovien = p_sovaovien
  AND sovaovien_dt = p_sovaovien_dt
  AND trang_thai < 3;

SELECT
    nvl(MAX(CAST(stt_loggiuongbenh AS NUMBER(18, 0))), 0)
INTO v_maxlog
FROM
    noitru_loggiuongbenh
WHERE
    dvtt = p_dvtt
  AND stt_logkhoaphong = p_stt_logkhoaphong
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND sovaovien = p_sovaovien
  AND sovaovien_dt = p_sovaovien_dt;

INSERT INTO noitru_loggiuongbenh (
    stt_loggiuongbenh,
    dvtt,
    stt_logkhoaphong,
    stt_benhan,
    stt_dotdieutri,
    mabenhnhan,
    maphongban,
    dongia,
    ngayvao,
    nhanvienlap,
    thoigianlap,
    trang_thai,
    maphongbenh,
    magiuongbenh,
    bhyt_thanhtoan,
    sovaovien,
    sovaovien_dt,
    nam_cung_giuong,
    so_nguoi_nam,
    ngoai_danh_muc,
    tien_bh_khongchi,
    so_giuong_tai_khoa_so,
    so_giuong_tai_khoa_chuoi,
    loai_giuong_tai_khoa,
    giuong_banngay,
    giuongbanngay_tyle_tt,
    ngay_th_ylenh
) VALUES (
             ( v_maxlog + 1 ),
             p_dvtt,
             p_stt_logkhoaphong,
             p_stt_benhan,
             p_stt_dotdieutri,
             p_mabenhnhan,
             p_maphongban,
             ---       820835 = 1                   100
             CASE
                 WHEN v_tyle_tt_giuongbanngay = 1 THEN
                     v_giagiuongbenh * 0.3
                 ELSE
                     v_giagiuongbenh
                 END,
             p_ngayvao,
             p_nhanvienlap,
             systimestamp,
             1,
             p_maphongbenh,
             p_magiuongbenh,
             CASE v_cobhyt
                 WHEN 1 THEN
                     CASE
                         WHEN length(v_sothebhyt) = 15 THEN
                             DECODE(v_ngoaidanhmuc, 1, 0, 1)
                         ELSE
                             0
                         END
                 ELSE
                     0
                 END,
             p_sovaovien,
             p_sovaovien_dt,
             p_namcung,
             p_songuoinam,
             CASE v_cobhyt
                 WHEN 1 THEN
                     CASE
                         WHEN length(v_sothebhyt) = 15 THEN
                             v_ngoaidanhmuc
                         ELSE
                             1
                         END
                 ELSE
                     1
                 END,
             v_sotienbhkchi,
             p_cg_sostt_giuongbenh,
             his_manager.chuyen_so_sang_chuoi(p_cg_sostt_giuongbenh, 3),
             p_cg_loaigiuongke_giuongbenh,
             1,
             DECODE(v_tyle_tt_giuongbanngay, 1, p_tyle_tt, 30),
             p_ngaynhangiuong
         );

UPDATE noitru_loggiuongbenh
SET
    trang_thai = 2
WHERE
    dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_logkhoaphong = p_stt_logkhoaphong
  AND stt_loggiuongbenh != ( v_maxlog + 1 )
        AND sovaovien = p_sovaovien
        AND sovaovien_dt = p_sovaovien_dt;

SELECT
    ( v_maxlog + 1 )
INTO v_return_value
FROM
    dual;

RETURN v_return_value;
END;