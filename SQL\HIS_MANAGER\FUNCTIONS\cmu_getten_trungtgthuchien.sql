create or replace FUNCTION cmu_getten_trungtgthuchien (
    p_dvtt      VARCHAR2,
    p_sophieu   VARCHAR2,
    p_madv      VARCHAR2
) RETURN VARCHAR2 AS
    v_ten VARCHAR2(1500) := '';
BEGIN
    IF lower(p_sophieu) LIKE 'xn%' THEN
SELECT
    ten_xetnghiem
INTO v_ten
FROM
    cls_xetnghiem
WHERE
    dvtt = p_dvtt
  AND ma_xetnghiem = p_madv;

ELSIF lower(p_sophieu) LIKE 'cd%' THEN
SELECT
    ten_cdha
INTO v_ten
FROM
    cls_cdha
WHERE
    dvtt = p_dvtt
  AND ma_cdha = p_madv;

ELSE
SELECT
    ten_dv
INTO v_ten
FROM
    dm_dich_vu_kham
WHERE
    dvtt = p_dvtt
  AND ma_dv = p_madv;

END IF;

RETURN v_ten;
END;