create or replace procedure                        "HIS_MANGER"."CMU_IN_PHIEUDANHGIA_DD"(
    p_dvtt              IN  VARCHAR2,
    p_ma_phieu          IN  NUMBER,
    p_ma_ben<PERSON>_<PERSON>han      IN  VARCHAR2,
    cur out SYS_REFCURSOR
)
IS
v_thamso960616 number(10) := cmu_tsdv(p_dvtt, 960616, 0);
BEGIN
OPEN cur FOR SELECT DISTINCT
                    dgdd.MA_PHIEU,
                    dgdd.MA_BENH_NHAN,
                    dgdd.TEN_BENH_NHAN,
                    dgdd.GIOI_TINH,
                    dgdd.CHAN_DOAN,
                    dgdd.CAN_NANG,
                    dgdd.CHIEU_CAO,
                    dgdd.BMI,
                    dgdd.DANH_GIA,
                    dgdd.SANG_LOC_BMI,
                    dgdd.SANG_LOC_SUT_CAN,
                    dgdd.SANG_LOC_LUONG_AN,
                    dgdd.SANG_LOC_BENH_NANG,
                    dgdd.KET_LUAN_SANG_LOC,
                    dgdd.DANH_GIA_BMI,
                    dgdd.DANH_GIA_SUT_CAN,
                    dgdd.DANH_GIA_LUONG_AN,
                    dgdd.DANH_GIA_BENH_LY,
                    dgdd.KET_LUAN,
                    dgdd.DUONG_MIENG,
                    dgdd.THONG_ONG,
                    dgdd.TINH_MACH,
                    dgdd.TAI_DANH_GIA,
                    dgdd.CHI_DINH_DD_KHAC,
                    TO_CHAR(dgdd.NGAY_TAO_PHIEU, 'dd/MM/yyyy') NGAY_TAO_PHIEU,
                    dgdd.BAC_SI_DANH_GIA,
                    bs.TEN_NHANVIEN TEN_BAC_SI,
                    SUM(dgdd.DANH_GIA_BMI + dgdd.DANH_GIA_SUT_CAN + dgdd.DANH_GIA_LUONG_AN + dgdd.DANH_GIA_BENH_LY) AS TONG_DANH_GIA,
                    dgdd.CHE_DO_AN,
                    v_thamso960616 ANCHUKY
             FROM
                    CMU_PHIEU_DANH_GIA_DINH_DUONG     dgdd
                    LEFT JOIN his_public_list.dm_benh_nhan bn ON dgdd.ma_benh_nhan = bn.ma_benh_nhan
                    LEFT JOIN his_fw.dm_nhanvien           bs ON dgdd.bac_si_danh_gia = bs.ma_nhanvien
             WHERE
                   dgdd.DVTT = p_dvtt
               AND dgdd.ma_phieu = p_ma_phieu
               AND dgdd.MA_BENH_NHAN = p_ma_benh_nhan
             GROUP BY
                 dgdd.MA_PHIEU,
                    dgdd.MA_BENH_NHAN,
                    dgdd.TEN_BENH_NHAN,
                    dgdd.GIOI_TINH,
                    dgdd.CHAN_DOAN,
                    dgdd.CAN_NANG,
                    dgdd.CHIEU_CAO,
                    dgdd.BMI,
                    dgdd.DANH_GIA,
                    dgdd.SANG_LOC_BMI,
                    dgdd.SANG_LOC_SUT_CAN,
                    dgdd.SANG_LOC_LUONG_AN,
                    dgdd.SANG_LOC_BENH_NANG,
                    dgdd.KET_LUAN_SANG_LOC,
                    dgdd.DANH_GIA_BMI,
                    dgdd.DANH_GIA_SUT_CAN,
                    dgdd.DANH_GIA_LUONG_AN,
                    dgdd.DANH_GIA_BENH_LY,
                    dgdd.KET_LUAN,
                    dgdd.DUONG_MIENG,
                    dgdd.THONG_ONG,
                    dgdd.TINH_MACH,
                    dgdd.TAI_DANH_GIA,
                    dgdd.CHI_DINH_DD_KHAC,
                    TO_CHAR(dgdd.NGAY_TAO_PHIEU, 'dd/MM/yyyy'),
                    dgdd.BAC_SI_DANH_GIA,
                    bs.TEN_NHANVIEN,
                    dgdd.CHE_DO_AN
             ORDER BY
                    NGAY_TAO_PHIEU ASC;
END;