const phieuKiemTraBenhAnJS = {};
let formPhieuKiemTraBenhAn;
let formPhieuKiemTraBenhAnTruocChinhSua;
let formPhieuKiemTraBenhAnMoiNhat;

$(function () {
    let validatePKTBA = [
        {className: "formio-js-validate-1", cusMsg: "Ngày tạo phiếu là bắt buộc", type: "input"},
    ]

    let kyHieuPKTBA = [
        "PHIEUKTRABA_NGUOI_KIEMTRA"
    ].join(",");

    let thongTinRaVien = {};
    $("#ttchamsoc-phieukhac").click(function () {
        $.get("cmu_list_CMU_NOT_LOAD_TTXUATVIEN_F?url="+convertArray([
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.STT_BENHAN,
            thongtinhsba.thongtinbn.STT_DOTDIEUTRI
        ])).done(function (dataRes) {
            if (dataRes && dataRes.length > 0) {
                thongTinRaVien = dataRes[0];
            }
        });
    });

    $(".themPhieuKiemTraBenhAn").click(function () {
        loadFormPhieuKiemTraBenhAn();
        initModalPhieuKiemTraBenhAn("THEM");
    });

    $("#xemThemDSPhieuKiemTraBenhAn").click(function(){
        initGridPhieuKiemTraBenhAn();
        phieuKiemTraBenhAnJS.reloadDSPhieuKiemTraBenhAn();
    });

    $("#iconXemPhieuKiemTraBenhAn").click(function () {
        getFilesign769V2(
            kyHieuPKTBA, formPhieuKiemTraBenhAnMoiNhat.ID,
            -1, singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1,
            function(dataKySo) {
                viewPhieuKiemTraBenhAn(Number(formPhieuKiemTraBenhAnMoiNhat.ID), false, dataKySo);
            });
    });

    $("#iconSuaPhieuKiemTraBenhAn").click(function () {
        if(formPhieuKiemTraBenhAnMoiNhat.NGUOI_TAO !== singletonObject.userId) {
            return notifiToClient('Red', MESSAGEAJAX.PERMISSION);
        }
        formPhieuKiemTraBenhAnMoiNhat.DATA_PHIEU.ID = formPhieuKiemTraBenhAnMoiNhat.ID
        let trueJson = ungroupBangKtraBa(formPhieuKiemTraBenhAnMoiNhat.DATA_PHIEU);
        loadFormPhieuKiemTraBenhAn(trueJson);
        initModalPhieuKiemTraBenhAn('SUA', formPhieuKiemTraBenhAnMoiNhat);
    });

    $("#iconXoaPhieuKiemTraBenhAn").click(function () {
        if(formPhieuKiemTraBenhAnMoiNhat.NGUOI_TAO !== singletonObject.userId) {
            return notifiToClient('Red', MESSAGEAJAX.PERMISSION);
        }
        delelePhieuKiemTraBenhAn(formPhieuKiemTraBenhAnMoiNhat.ID);
    });

    $("#taoPhieuKiemTraBenhAn").click(function () {
        formPhieuKiemTraBenhAn.emit("checkValidity");
        if (!formPhieuKiemTraBenhAn.checkValidity(null, false, null, true)) {
            xuLyValidateNotWorking(validatePKTBA);
            return;
        }
        let formData = $.extend(true, {}, formPhieuKiemTraBenhAn.submission.data);
        delete formData.ID;
        let formData2 = groupBangKtraBa(formData);
        let thisBtn = this.id;
        showSelfLoading(thisBtn);
        try {
            $.post("cmu_post_CMU_PHIEU_KIEMTRA_BENHAN_INS", {
                url: [
                    singletonObject.dvtt,
                    thongtinhsba.thongtinbn.MABENHNHAN,
                    JSON.stringify(formData2),
                    thongtinhsba.thongtinbn.SOVAOVIEN,
                    thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                    thongtinhsba.thongtinbn.STT_BENHAN,
                    singletonObject.userId,
                    singletonObject.makhoa,
                ].join("```")
            }).fail(function() {
                notifiToClient('Red', MESSAGEAJAX.ERROR);
            }).done(function(data) {
                if (data > 0) {
                    let subData = getSubPKTBA()
                    luuLogHSBAInsertFormioV4(
                        formPhieuKiemTraBenhAn,
                        LOGHSBALOAI.KIEMTRABENHAN.KEY,
                        "Tạo " + LOGHSBALOAI.KIEMTRABENHAN.VALUE + " - ID: " + data + " - ",
                        subData
                    );
                    notifiToClient('Green', MESSAGEAJAX.ADD_SUCCESS);
                    phieuKiemTraBenhAnJS.reloadDSPhieuKiemTraBenhAn();
                    $("#modalPhieuKiemTraBenhAn").modal('hide');
                }
                else {
                    notifiToClient('Red', MESSAGEAJAX.FAIL);
                }
            }).always(function() {
                hideSelfLoading(thisBtn);
            });
        }
        catch (err) {
            console.error(err)
        }
    });

    $("#suaPhieuKiemTraBenhAn").click(function () {
        let formData = $.extend(true, {}, formPhieuKiemTraBenhAn.submission.data);
        formPhieuKiemTraBenhAn.emit("checkValidity");
        if (!formPhieuKiemTraBenhAn.checkValidity(null, false, null, true)) {
            xuLyValidateNotWorking(validatePKTBA);
            return;
        }
        let dataToSave = $.extend(true, {}, formPhieuKiemTraBenhAn.submission.data);
        delete dataToSave.ID;
        let dataToSave2 = groupBangKtraBa(dataToSave);
        let thisBtn = this.id;
        showSelfLoading(thisBtn);
        $.post("cmu_post_CMU_PHIEU_KIEMTRA_BENHAN_UPD", {
            url: [
                formData.ID,
                singletonObject.dvtt,
                JSON.stringify(dataToSave2),
            ].join("```")
        }).fail(function() {
            notifiToClient('Red', MESSAGEAJAX.ERROR);
        }).done(function(data) {
            if (data > 0) {
                let subData = getSubPKTBA()
                luuLogHSBAChinhSuaFormioV4(
                    formPhieuKiemTraBenhAnTruocChinhSua.DATA_PHIEU,
                    formPhieuKiemTraBenhAn,
                    LOGHSBALOAI.KIEMTRABENHAN.KEY,
                    "Cập nhật " + LOGHSBALOAI.KIEMTRABENHAN.VALUE + " - ID: " + formData.ID + " - ",
                    subData
                );
                notifiToClient('Green', MESSAGEAJAX.EDIT_SUCCESS);
                phieuKiemTraBenhAnJS.reloadDSPhieuKiemTraBenhAn();
                $('#modalPhieuKiemTraBenhAn').modal('hide');
            }
            else {
                notifiToClient('Red', MESSAGEAJAX.FAIL);
            }
        }).always(function() {
            hideSelfLoading(thisBtn);
        });
    });

    function delelePhieuKiemTraBenhAn(id) {
        confirmToClient(MESSAGEAJAX.CONFIRM, function() {
            $.post("cmu_post_CMU_PHIEU_KIEMTRA_BENHAN_DEL", {
                url: [id, singletonObject.dvtt].join("```")
            }).fail(function() {
                notifiToClient("Red", MESSAGEAJAX.ERROR);
            }).done(function(data) {
                if (data === '1') {
                    luuLogHSBADeleteFormio(
                        LOGHSBALOAI.KIEMTRABENHAN.KEY,
                        "Xóa " + LOGHSBALOAI.KIEMTRABENHAN.VALUE + " - ID: " + id
                    );
                    notifiToClient('Green', MESSAGEAJAX.DEL_SUCCESS);
                    phieuKiemTraBenhAnJS.reloadDSPhieuKiemTraBenhAn();
                } else {
                    notifiToClient('Red', MESSAGEAJAX.ERROR);
                }
            }).always(function() {});
        }, function() {});
    }

    function autoScaleTextarea(textarea) {
        $(textarea).css('height', 'auto');
        $(textarea).css('height', textarea.scrollHeight + 'px');
    }

    function tinhTongCongDiem(objData, finalKey) {
        let tongDiem = 0.0;
        $.each(objData, function(key, value) {
            if (/^BANG_KTRA_BA_R\d+C0$/.test(key) && key !== finalKey && $.isArray(value)) {
                let diemDat = parseFloat(value[0].DIEM_DAT);
                if (!isNaN(diemDat)) {
                    tongDiem += diemDat;
                }
            }
        });
        return tongDiem.toFixed(2);
    }

    function dinhKemSuKienPhieuKiemTraBenhAn(isInit) {
        let textarea = $('.formio-js-scale-textarea textarea[type="text"]');
        textarea.each(function() {
            $(this).attr('rows', 1);
            $(this).on('input', function() { autoScaleTextarea(this); });
            autoScaleTextarea(this);
        });

        $('#phieuKiemTraBenhAn textarea[name*="BANG_KTRA_BA_R"][name*="NOI_DUNG"]').each(function() {
            $(this).attr('readonly', true).attr('tabindex', '-1').css({
                'pointer-events': 'none',
                'user-select': 'none',
                'background-color': '#ffffff',
            });
        });

        $('.formio-js-diem-dat input[type="text"]').on( "change", function() {
            let nameAttr = $(this).attr("name");
            let matchKey = nameAttr.match(/^data\[([^\]]+)]/);

            if (matchKey) {
                let formioKey = matchKey[1];
                let trueKey = formioKey.replace(/\d+$/, "");

                let total = 0.0;
                for (let i = 1; i <= 10; i++) {
                    let inputKey = $('#phieuKiemTraBenhAn [name="data[' + trueKey + i + '][0][DIEM_DAT]"]');
                    if ($.isNumeric(inputKey.val())) {
                        total += parseFloat(inputKey.val());
                    }
                }

                formPhieuKiemTraBenhAn.data[trueKey + "0"][0].DIEM_DAT = total.toFixed(2);
                $('#phieuKiemTraBenhAn [name="data[' + trueKey + "0" + '][0][DIEM_DAT]"]').val(total.toFixed(2));

                let finalKey = "BANG_KTRA_BA_R4C0";
                let tongDiem = tinhTongCongDiem(formPhieuKiemTraBenhAn.data, finalKey);
                formPhieuKiemTraBenhAn.data[finalKey][0].DIEM_DAT = tongDiem;
                $('#phieuKiemTraBenhAn [name="data[' + finalKey + '][0][DIEM_DAT]"]').val(tongDiem);
            }
        });

        $('#phieuKiemTraBenhAn input[name*="[DIEM_DAT]"]').on('keydown', function (e) {
            if (e.keyCode === 13) {
                e.preventDefault();

                let nameAttr = $(this).attr('name');
                let match = nameAttr.match(/BANG_KTRA_BA_R(\d+)C(\d+)/);

                if (!match) return;

                let row = parseInt(match[1], 10);
                let col = parseInt(match[2], 10);

                let found = false;
                let nextInput;

                while (!found) {
                    col += 1;
                    let nextName = 'data[BANG_KTRA_BA_R' + row + 'C' + col + '][0][DIEM_DAT]';
                    nextInput = $('input[name="' + nextName + '"]');
                    if (nextInput.length) {
                        found = true;
                        break;
                    } else {
                        col = 0;
                        row += 1;
                        let nextRowName = 'data[BANG_KTRA_BA_R' + row + 'C' + '1][0][DIEM_DAT]'
                        let intoNextRow = $('input[name="' + nextRowName + '"]');
                        if (intoNextRow.length) {
                            col = 0;
                        } else {
                            break;
                        }
                    }
                }

                if (found) {
                    nextInput.focus();
                }
            }
        });

        if (isInit) {
            formPhieuKiemTraBenhAn.redraw();
            dinhKemSuKienPhieuKiemTraBenhAn();
        }
    }

    function initGridPhieuKiemTraBenhAn() {
        let listData = $('#phieuKiemTraBenhAnGrid');
        if (!listData[0].grid) {
            listData.jqGrid({
                datatype: 'local',
                data: [],
                loadonce: true,
                height: 400,
                width: null,
                shrinkToFit: false,
                colModel: [
                    // show
                    {
                        name: "KYSO1",
                        label: "Ký số Người kiểm tra",
                        align: 'left',
                        width: 100,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.KEYSIGN_1) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold; color: green">Đã ký</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold; color: red">Chưa ký</span>';
                            }
                        },
                        cellattr: function () {
                            return ' title="Ký số Người kiểm tra"';
                        }
                    },
                    {name: 'ID', label: "ID", width: 100},


                    {name: 'TENNGUOITAO', label: 'Người tạo', width: 250},
                    {name: 'NGAYTAO', label: 'Ngày tạo', width: 150},

                    // hidden
                    {name: 'NGUOI_TAO', hidden: true},
                    {name: 'DATA_PHIEU', hidden: true},
                    {name: "KEYSIGN_1", hidden: true},
                ],
                onRightClickRow: function () {
                    let ret = getThongtinRowSelected("phieuKiemTraBenhAnGrid");
                    let items = {
                        "xem": { name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>' },
                    }
                    $.contextMenu('destroy', '#phieuKiemTraBenhAnGrid tr');
                    if (ret.KEYSIGN_1){
                        items = {
                            "huykyso1": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số Người kiểm tra</p>'},
                            ...items,
                        }
                    }
                    else {
                        items = {
                            "kyso1": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số Người kiểm tra</p>'},
                            ...items,
                            "sua": {
                                name: '<p><i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Sửa</p>'
                            },
                            "xoa": {
                                name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'
                            },
                        }
                    }

                    $.contextMenu({
                        selector: '#phieuKiemTraBenhAnGrid tr',
                        callback: function (key) {
                            let ret = getThongtinRowSelected("phieuKiemTraBenhAnGrid");
                            let dataJson = JSON.parse(ret.DATA_PHIEU);
                            dataJson.ID = Number(ret.ID); ret.DATA_PHIEU = dataJson;
                            if (key === 'kyso1') {
                                formPhieuKiemTraBenhAnTruocChinhSua = $.extend(true, {}, {
                                    ID: ret.ID, DATA_PHIEU: dataJson
                                });
                                getFilesign769V2(
                                    kyHieuPKTBA, ret.ID, -1, singletonObject.dvtt,
                                    thongtinhsba.thongtinbn.SOVAOVIEN,
                                    thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1,
                                    function(dataKySo) {
                                        viewPhieuKiemTraBenhAn(Number(ret.ID), true, dataKySo, "NguoiKiemTra");
                                    });
                            }
                            if (key === 'huykyso1') {
                                confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?",
                                    function() {
                                        huykysoFilesign769V2(
                                            kyHieuPKTBA, ret.ID, singletonObject.userId, singletonObject.dvtt,
                                            thongtinhsba.thongtinbn.SOVAOVIEN,
                                            thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1,
                                            function(_) {
                                                phieuKiemTraBenhAnJS.reloadDSPhieuKiemTraBenhAn();
                                            }
                                        );
                                    }, function () {});
                            }
                            if (key === 'xem') {
                                getFilesign769V2(
                                    kyHieuPKTBA, ret.ID, -1, singletonObject.dvtt,
                                    thongtinhsba.thongtinbn.SOVAOVIEN,
                                    thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1,
                                    function(dataKySo) {
                                        viewPhieuKiemTraBenhAn(Number(ret.ID), false, dataKySo);
                                    });
                            }
                            if (key === 'sua') {
                                if (ret.NGUOI_TAO !== singletonObject.userId) {
                                    return notifiToClient('Red', MESSAGEAJAX.PERMISSION);
                                }
                                let trueJson = ungroupBangKtraBa(dataJson);
                                loadFormPhieuKiemTraBenhAn(trueJson);
                                initModalPhieuKiemTraBenhAn('SUA', ret);
                            }
                            if (key === 'xoa') {
                                if(ret.NGUOI_TAO !== singletonObject.userId) {
                                    return notifiToClient('Red', MESSAGEAJAX.PERMISSION);
                                }
                                delelePhieuKiemTraBenhAn(Number(ret.ID));
                            }
                        },
                        items: items
                    })
                },
            });
            listData.jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: 'cn'});
        }
    }

    function initModalPhieuKiemTraBenhAn(action, data) {
        let titleName = " Phiếu Kiểm tra bệnh án";
        $('#modalPhieuKiemTraBenhAn').modal('show');
        addTextTitleModal('titlePhieuKiemTraBenhAn', titleName);
        if(action === 'THEM') {
            $('#taoPhieuKiemTraBenhAn').show();
            $('#suaPhieuKiemTraBenhAn').hide();
        } else {
            formPhieuKiemTraBenhAnTruocChinhSua = $.extend(true, {}, data);
            $('#taoPhieuKiemTraBenhAn').hide();
            $('#suaPhieuKiemTraBenhAn').show();
        }
    }

    function loadFormPhieuKiemTraBenhAn(data, callback) {
        try {
            $.getJSON(
                "resources/camau/js/formioPhieuKiemTraBenhAn.json?v="+moment().format('YYYYMMDDHHmmss')
            ).done(function(rs){
                Formio.createForm(document.getElementById('phieuKiemTraBenhAn'), rs, {
                    disableAlerts: true,
                }).then(function(instance) {
                    formPhieuKiemTraBenhAn = instance;
                    let nguoiKTra = formPhieuKiemTraBenhAn.getComponent("NGUOI_KIEM_TRA").component;
                    nguoiKTra.data.json = singletonObject.danhsachtatcanhanvien;
                    let bsDieuTri = formPhieuKiemTraBenhAn.getComponent("BS_DIEU_TRI").component;
                    bsDieuTri.data.json = singletonObject.danhsachtatcanhanvien;
                    let khoa = formPhieuKiemTraBenhAn.getComponent("KHOA").component;
                    khoa.data.json = singletonObject.danhsachphongban;

                    let initData = { data: {} };
                    if (data) {
                        initData.data = data;
                    }
                    else {
                        initData.data = {
                            NGUOI_KIEM_TRA: singletonObject.danhsachtatcanhanvien.find(
                                (item) => item.value === singletonObject.userId),
                        }
                    }

                    let defaultObj = {
                        NGAY_VAO_VIEN: moment(
                            thongtinhsba.thongtinbn.NGAYGIO_NHAPVIEN,
                            ['DD/MM/YYYY HH:mm:ss']
                        ).format("YYYY-MM-DDTHH:mm:ssZ"),
                        ...(thongTinRaVien && thongTinRaVien.NGAY_XUATVIEN ? {
                            NGAY_RA: moment(
                                thongTinRaVien.NGAY_XUATVIEN + " " + thongTinRaVien.GIO_XUATVIEN,
                                ['DD/MM/YYYY HH:mm:ss']
                            ).format("YYYY-MM-DDTHH:mm:ssZ"),
                        } : {
                            NGAY_RA: moment().format("YYYY-MM-DDTHH:mm:ssZ"),
                        }),
                        ...(thongtinhsba.thongtinbn.SOBAOHIEMYTE ? {
                            SO_THE_BHYT: thongtinhsba.thongtinbn.SOBAOHIEMYTE,
                        } : {}),
                        SO_BA: thongtinhsba.thongtinbn.SOBENHAN,
                        MA_BENH: thongtinhsba.thongtinbn.MABENHNHAN,
                    }

                    initData.data = $.extend({}, initData.data, defaultObj);
                    formPhieuKiemTraBenhAn.setSubmission(initData).then(function () {
                        dinhKemSuKienPhieuKiemTraBenhAn(true);
                    }).catch(function(err) {
                        notifiToClient('Red', 'Lỗi cài đặt dữ liệu'); console.error(err);
                    });

                    callback && callback();
                });
            });
        } catch (err) {
            console.error(err)
        }
    }

    function getSubPKTBA() {
        return {
            CO: "Có", KHONG: "Không"
        }
    }

    function viewPhieuKiemTraBenhAn(idPhieu, sign, dataKySo, sufKey) {
        let params = {
            ID_PHIEU: idPhieu
        }
        let url = 'cmu_in_rp_phieu_kiemtra_benhan?type=pdf&' + $.param(params);
        if (sign) {
            if (dataKySo && dataKySo.length > 0) {
                let keyMinio = getCMUFileSigned769GetLinkV2(dataKySo[0].KEYMINIO, 'pdf');
                url = keyMinio !== '-1' ? keyMinio : url;
            }
            previewAndSignPdfDefaultModal({url: url, idButton: "kySo" + sufKey + "PKTBA",}, function(){});
        }
        else {
            if (dataKySo && dataKySo.length > 0) { getCMUFileSigned769(dataKySo[0].KEYMINIO,"pdf"); }
            else { previewPdfDefaultModal(url, "framePhieuKiemTraBenhAn"); }
        }
    }

    function xuLyValidateNotWorking(validateList) {
        $.each(validateList, function (_, v) {
            let className = v.className, cusMsg = v.cusMsg, type = v.type;
            let inputTag = $("." + className + " " + type),
                labelTag = $("." + className + " label"),
                errorTag = $("." + className + " .formio-errors");
            if (inputTag.val().length === 0 && errorTag.contents().length === 0
                && errorTag.next('.error-msg-pktba').length === 0) {
                inputTag.css({"border-color": "#dc3545"}); labelTag.css({"color": "#721c24"});
                errorTag.after('<div class="error-msg-pktba" style="display: block; color: #c20000; font-size: 80%; margin-top: .25rem">' + cusMsg + '</div>');
            }
            else if (inputTag.val().length > 0) {
                inputTag.removeAttr("style"); labelTag.css({"color": "#222222"});
                errorTag.next('.error-msg-pktba').remove();
            }
            inputTag.on("change", function () {
                if (inputTag.val().length > 0) {
                    inputTag.removeAttr("style"); labelTag.css({"color": "#222222"});
                    errorTag.next('.error-msg-pktba').remove();
                }
            });
        })
    }

    function groupBangKtraBa(obj) {
        let newObj = $.extend(true, {}, obj);
        let bangKtraList = [];
        Object.keys(newObj).forEach(key => {
            if (key.startsWith("BANG_KTRA_BA_R")) {
                let rowObj = obj[key][0]
                rowObj.KEY = key;
                bangKtraList.push(rowObj);
                delete newObj[key];
            }
        });
        newObj.BANG_KTRA_BA = bangKtraList;
        return newObj;
    }

    function ungroupBangKtraBa(obj) {
        let newObj = $.extend(true, {}, obj);
        if (Array.isArray(newObj.BANG_KTRA_BA)) {
            let bangKtraList = newObj.BANG_KTRA_BA
            bangKtraList.forEach(item => {
                let objTemp = {};
                $.each(item, function(k, v) {
                    if (!k.includes("KEY")) {
                        objTemp[k] = v;
                    }
                });
                newObj[item.KEY] = [objTemp];
            });
            delete newObj.BANG_KTRA_BA;
        }
        return newObj;
    }

    phieuKiemTraBenhAnJS.reloadDSPhieuKiemTraBenhAn = function () {
        $("#phieuKiemTraBenhAnGrid").jqGrid('clearGridData');
        let url = 'cmu_getlist?url=' + convertArray([
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            'CMU_PHIEU_KIEMTRA_BENHAN_LST'
        ]);
        $.get(url).done(function(data){
            if (data && data.length > 0) {
                let dataGanNhat = data[data.length - 1]
                let formData = JSON.parse(dataGanNhat.DATA_PHIEU);
                let khoaTao = singletonObject.danhsachphongban.find(
                    (item) => item.MAKHOA === dataGanNhat.KHOA_TAO);
                $("#dataPhieuKiemTraBenhAn").html(
                    khoaTao.TENKHOA + " - " + moment(dataGanNhat.NGAYTAO, ["DD/MM/YYYY HH:mm:ss"]).format("DD/MM/YYYY")
                );
                formPhieuKiemTraBenhAnMoiNhat = {
                    ...dataGanNhat, DATA_PHIEU: formData
                };
                if (dataGanNhat.KEYSIGN_1){
                    $("#iconSuaPhieuKiemTraBenhAn").hide();
                    $("#iconXoaPhieuKiemTraBenhAn").hide();
                }
                else {
                    $("#xuLyIconPhieuKiemTraBenhAn").css('visibility', 'unset');
                    $("#iconXemPhieuKiemTraBenhAn").show();
                    $("#iconSuaPhieuKiemTraBenhAn").show();
                    $("#iconXoaPhieuKiemTraBenhAn").show();
                }
            } else  {
                $("#dataPhieuKiemTraBenhAn").html("Không có dữ liệu");
                $("#xuLyIconPhieuKiemTraBenhAn").css('visibility', 'hidden');
            }
            $("#phieuKiemTraBenhAnGrid").jqGrid('setGridParam', {
                datatype: 'local',
                data: data.map((item) => {
                    let parseData = JSON.parse(item.DATA_PHIEU);
                    return { ...item, DATA_PARSED: parseData }
                })
            }).trigger("reloadGrid");
        });
    }

    $(document).on('click', '#kySoNguoiKiemTraPKTBA', function() {
        kySoChung({
            dvtt: singletonObject.dvtt,
            userId: singletonObject.userId,
            url: $('#iframePreviewAndSign').attr('src'),
            loaiGiay: "PHIEUKTRABA_NGUOI_KIEMTRA",
            maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
            soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
            soPhieuDichVu: formPhieuKiemTraBenhAnTruocChinhSua.ID,
            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            keyword: "SIGNATURE_1",
            fileName: LOGHSBALOAI.KIEMTRABENHAN.VALUE + ": " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Mã phiếu: " + formPhieuKiemTraBenhAnTruocChinhSua.ID,
        }, function(_) {
            $("#modalPreviewAndSignPDF").modal("hide");
            phieuKiemTraBenhAnJS.reloadDSPhieuKiemTraBenhAn();
        });
    });
});