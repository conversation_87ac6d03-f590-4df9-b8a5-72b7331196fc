create or replace PROCEDURE               "CMU_LCSC1_GHICHU_PRO" (
    p_dvtt                IN                    VARCHAR2,
    p_id_cham_soc_cap_1   IN                    VARCHAR2,
    cur                   OUT                   SYS_REFCURSOR
) IS
BEGIN
OPEN cur FOR SELECT
    ghi_chu
    || ' ('
    || ten_nguoi_tao
    || ')' ghi_chu,
    ngay
FROM
    (
        SELECT
            TO_CHAR(gc.ngay_tao, 'dd/mm/yyyy') AS ngay,
            nt.ten_nhanvien_cd AS ten_nguoi_tao,
            LISTAGG(gc.ghi_chu_ban_giao, '; ') WITHIN GROUP(
                ORDER BY
                    gc.ngay_tao
            ) AS ghi_chu
        FROM
            cmu_lanchamsoccap1_ghichu   gc
            LEFT JOIN his_fw.dm_nhanvien_cd       nt ON gc.ma_nguoi_tao = nt.ma_nhanvien
            LEFT JOIN his_fw.dm_phongban          kt ON gc.khoa_tao = kt.ma_phongban
        WHERE
            gc.id_cham_soc_cap_1 = p_id_cham_soc_cap_1
        GROUP BY
            TO_CHAR(gc.ngay_tao, 'dd/mm/yyyy'),
            nt.ten_nhanvien_cd
        ORDER BY
            ngay ASC
    )
UNION
SELECT
    LISTAGG(ten, ', ') WITHIN GROUP(
                         ORDER BY
                             stt
                     ) AS ghi_chu,
                     '' ngay
FROM
    (
    SELECT DISTINCT
    f.stt,
    nvl(f.prefix, '') || d.ten_item
    || ' - '
    || d.mota_item ten
    FROM
    cmu_lanchamsoccap1           a
    JOIN cmu_lanchamsoccap1_chitiet   b ON a.id_cham_soc_cap_1 = b.id_cham_soc_cap_1
    JOIN cmu_lcsc1_ct_item_chiso      c ON b.id_chi_tiet = c.id_chi_tiet
    JOIN cmu_lcsc1_item               e ON e.id_cham_soc_cap_1 = a.id_cham_soc_cap_1
    AND c.id_item = e.id_item
    JOIN cmu_datalist_item_csc1       d ON d.dvtt = a.dvtt and ',' || c.chi_so || ',' like '%,' || d.ten_item || ',%'
    AND e.loai_item
    || '_'
    || e.ten_item = d.loai_item
    JOIN cmu_chamsoccap1_config       f ON f.dvtt = a.dvtt
--                                                              AND e.loai_item = f.loai
    AND e.ten_item = f.tenhienthi
    JOIN CMU_CHAMSOCCAP1_GROUP_CONFIG g on g.dvtt = f.dvtt and g.id = f.loai and e.loai_item = g.loai
    WHERE
    a.dvtt = p_dvtt
    AND a.id_cham_soc_cap_1 = p_id_cham_soc_cap_1
    AND e.loai_item in ('BAN_GIAO')
    );

END;