<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="cls_xetnghiem" language="groovy" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" whenResourceMissingType="Error" uuid="0f5e3019-005e-44fe-bb8f-29ae14f10109">
	<property name="ireport.zoom" value="1.948717100000002"/>
	<property name="ireport.x" value="338"/>
	<property name="ireport.y" value="0"/>
	<parameter name="tenbenhvien" class="java.lang.String"/>
	<parameter name="ten_phongban" class="java.lang.String"/>
	<parameter name="hovaten" class="java.lang.String"/>
	<parameter name="tuoi" class="java.lang.String"/>
	<parameter name="gioitinh" class="java.lang.String"/>
	<parameter name="sogiuong" class="java.lang.String"/>
	<parameter name="buong" class="java.lang.String"/>
	<parameter name="chandoan" class="java.lang.String"/>
	<parameter name="tensoyte" class="java.lang.String"/>
	<parameter name="sovaovien" class="java.lang.String"/>
	<parameter name="sophieu" class="java.lang.String"/>
	<parameter name="dvtt" class="java.lang.String"/>
	<parameter name="id_dieutri" class="java.lang.Integer"/>
	<parameter name="sobenhan" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_dotdieutri" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="soyte" class="java.lang.String"/>
	<parameter name="idpcs" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call CMU_PHIEUCHAMSOC_P_V2($P{dvtt}, $P{sovaovien}, $P{idpcs}, $P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="PCS_NGAYLAP" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="PCS_GIOLAP" class="java.lang.String"/>
	<field name="PCS_THEODOIDIENBIEN" class="java.lang.String">
		<fieldDescription><![CDATA[theo dõi diễn tiến bệnh]]></fieldDescription>
	</field>
	<field name="PCS_THUCHIENYLENH_CS" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="PCS_NGUOILAP" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="TEN_NHANVIEN" class="java.lang.String"/>
	<field name="KEYSIGN" class="java.lang.String"/>
	<field name="NGAY_KY" class="java.lang.String"/>
	<field name="BUONG" class="java.lang.String"/>
	<field name="GIUONG" class="java.lang.String"/>
	<field name="ANCHUKY" class="java.lang.String"/>
	<field name="ANHCHUKY" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="87" splitType="Stretch">
			<staticText>
				<reportElement mode="Transparent" x="428" y="16" width="64" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="7703de91-898a-47cd-86a0-b3a8a03f2db9"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Số vào viện:]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement mode="Transparent" x="0" y="16" width="248" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="f0c48e80-7d18-49f4-a649-632ef235e480"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenbenhvien}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="0" y="32" width="248" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="a0adb478-141e-4382-ab1b-aaf700cf8766"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{ten_phongban}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement mode="Transparent" x="492" y="16" width="63" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="97aac8a5-3a5a-4553-85d3-5e0421a00b62"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{sobenhan}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="0" y="48" width="383" height="13" uuid="c3568519-b07c-4cbe-98f2-9fe53155c1e2"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["- Họ tên người bệnh: " + $P{hovaten}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="383" y="48" width="103" height="13" uuid="44b144e5-7ce0-44e4-9a8e-fe926682ea79"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["Tuổi: " + $P{tuoi}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="0" y="61" width="59" height="13" uuid="b01bdcb7-40b3-41bf-a8c9-8408ed572b6b"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["- Số giường:"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="59" y="61" width="251" height="13" uuid="eaffd7e6-629c-4caa-a9e1-d46e3d847e80"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIUONG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="59" y="74" width="496" height="13" isPrintWhenDetailOverflows="true" uuid="e54aa044-e8a6-467b-a20c-c2a0efd1949c"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{chandoan}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="248" y="0" width="180" height="32" uuid="ded79c50-5116-4636-b6a9-d4e953b03c52"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="19" isBold="true"/>
				</textElement>
				<text><![CDATA[PHIẾU CHĂM SÓC]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement mode="Transparent" x="0" y="0" width="248" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="1c9d9496-dac9-4148-a093-29e4c4ea8871"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{soyte}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="428" y="0" width="127" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="82acb37e-77b0-4ae7-ad1b-8fc2b72e537f"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[MS: 09/BV-01]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="310" y="61" width="37" height="13" uuid="c1f01125-efcc-4893-a037-de7962ee1042"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["Buồng:"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="347" y="61" width="208" height="13" uuid="9839ca9b-abce-4755-a4b7-8ab043ca7c19"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BUONG}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="486" y="49" width="69" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="e4f92c06-3705-41a1-b55b-0c8103f8f8be"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Giới tính: " + $P{gioitinh}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="0" y="74" width="59" height="13" uuid="c5aa55cc-352a-4e8b-9fb3-6af8cb67359a"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["- Chẩn đoán:"]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="24" splitType="Stretch">
			<staticText>
				<reportElement x="61" y="0" width="51" height="24" uuid="487cc6cd-c743-42b4-a1cd-cd0df6056bb2"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Giờ phút]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="61" height="24" uuid="8196e62c-8f7a-4e83-9be5-363d7aaad10b"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Ngày]]></text>
			</staticText>
			<staticText>
				<reportElement x="112" y="0" width="200" height="24" uuid="4905128b-5a9c-43b3-a9c6-d62b1c18c5f0"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[THEO DÕI DIỄN BIẾN]]></text>
			</staticText>
			<staticText>
				<reportElement x="312" y="0" width="243" height="24" uuid="d656f0d2-e37c-4843-9da9-bbacf9ca5bf0"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[THỰC HIỆN Y LỆNH/CHĂM SÓC]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="86" splitType="Prevent">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="61" height="86" uuid="22db52e3-081a-4b4a-9501-5925b7b914c5"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PCS_NGAYLAP}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="61" y="0" width="51" height="86" uuid="79b0f5c8-df5b-4f39-b9a2-a47e1eacac0f"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PCS_GIOLAP}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="112" y="0" width="200" height="86" uuid="da29f8e6-5140-4c7b-a9c8-2c7004ca018c"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PCS_THEODOIDIENBIEN}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement stretchType="RelativeToTallestObject" x="312" y="0" width="243" height="86" uuid="520042ae-8ec5-4c2d-8e81-ad6c8ac3f0da"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<frame>
					<reportElement positionType="Float" mode="Transparent" x="0" y="0" width="243" height="86" isPrintInFirstWholeBand="true" uuid="3981d8f8-13ec-4eab-9583-b4e5afcf978a"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<image scaleImage="FillFrame" hAlign="Center">
						<reportElement x="223" y="29" width="20" height="20" uuid="6acac598-18cb-40b9-a1df-a93f2ab19849">
							<printWhenExpression><![CDATA[$F{KEYSIGN} != null && $F{ANCHUKY}.equals("0")]]></printWhenExpression>
						</reportElement>
						<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.getInstance(
new SimpleJasperReportsContext()).loadAwtImageFromBytes(Base64.getDecoder().decode("iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAABvElEQVR4nO3U3UrCYBjAcS+gAy0XdjAs925uO+huBGfnnYkmQX7kxwQvoNqJdAmFSEFQptP5RdDRBg26jig6euKxrFdbVjo66oGHuZP/j9cX5vH8z1+OcFv0y0M1hU/X48SoMFK/ZMoDFaRh2ZZvigF3472SJfVVkAYqICIPy5YrJyFGhgl3C5bYK4HUx/1AxIGaXDguGAVL7BZB7OFOIIudgLzGzbBRgDACNNIrfb6Dpari9x0pKXz+JM538ma4k4cRQCPdgi3r0/GDCOPTFNOnxcCrxWxGiwRmxvWcKbTzICBAI4ZTvKr4vUeKhfHx4juiTnHSypq8vg9CG5dG8p/jOPi30PGvEILxZtbkWzngddwPhO/sO8e/OsE0Qi4wnjFJMwsEAQrh2zPiTncwvcva1n2okb4j1xkYARRCWjk7+F18FrKibcH6eQq4RgY4BCiEa/4i7oRgPHi2A6GrNHCN9ATCXWftoL4737cGEe9B1GLrSQhd7kHoam8KSVt4J3PF35FKhGFriccNBChkvZ54XDg+nrXqtsjWEk9jJFhPPK8eRzddib8jh9siexJ/YE/jD/jb1fj/eN7mBQZhd5OoxlU0AAAAAElFTkSuQmCC")
)]]></imageExpression>
					</image>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="243" height="20" isPrintWhenDetailOverflows="true" uuid="7f436248-0b9b-47bd-9c90-2348a4b1037f"/>
						<box leftPadding="2" rightPadding="2"/>
						<textElement verticalAlignment="Top" markup="styled">
							<font fontName="Times New Roman" size="11" isBold="false"/>
							<paragraph lineSpacing="Single"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{PCS_THUCHIENYLENH_CS}]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement positionType="Float" x="104" y="73" width="139" height="13" isPrintWhenDetailOverflows="true" uuid="55562ffc-5aeb-4e0e-a8ae-8da5fc352d32">
							<printWhenExpression><![CDATA[$F{ANCHUKY}.equals("0")]]></printWhenExpression>
						</reportElement>
						<textElement textAlignment="Center" verticalAlignment="Bottom">
							<font fontName="Times New Roman" size="11" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{TEN_NHANVIEN}]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement positionType="Float" x="104" y="20" width="139" height="13" isPrintWhenDetailOverflows="true" uuid="891268e7-0cee-4b4b-97c0-a0309e4fe876"/>
						<textElement textAlignment="Center" verticalAlignment="Bottom">
							<font fontName="Times New Roman" size="11" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Điều dưỡng ký tên"]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement positionType="Float" x="82" y="56" width="148" height="13" isPrintWhenDetailOverflows="true" uuid="f97fb75a-e91f-4f84-98d0-39a6be00baf9">
							<printWhenExpression><![CDATA[$F{KEYSIGN} != null && $F{ANCHUKY}.equals("0")]]></printWhenExpression>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Bottom">
							<font fontName="Times New Roman" size="9" isBold="false" isStrikeThrough="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Ngày ký: "+ $F{NGAY_KY}]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement positionType="Float" x="82" y="43" width="148" height="13" isPrintWhenDetailOverflows="true" uuid="14f50f79-8797-48fd-9ace-552e6c783534">
							<printWhenExpression><![CDATA[$F{KEYSIGN} != null && $F{ANCHUKY}.equals("0")]]></printWhenExpression>
						</reportElement>
						<textElement textAlignment="Left" verticalAlignment="Bottom">
							<font fontName="Times New Roman" size="9" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Ký bởi: " + $F{TEN_NHANVIEN}]]></textFieldExpression>
					</textField>
					<image scaleImage="FillFrame" hAlign="Center">
						<reportElement positionType="Float" x="104" y="36" width="139" height="50" uuid="8553e1f0-3419-4a9f-a217-7709de42412f">
							<printWhenExpression><![CDATA[$F{ANHCHUKY} != null && $F{ANCHUKY}.equals("1")]]></printWhenExpression>
						</reportElement>
						<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.getInstance(
new SimpleJasperReportsContext()).loadAwtImageFromBytes(Base64.getDecoder().decode($F{ANHCHUKY})
)]]></imageExpression>
					</image>
				</frame>
			</frame>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<summary>
		<band/>
	</summary>
</jasperReport>
