CREATE OR REPLACE FUNCTION "F_CMUKTRAXML3_TRUNGGIOTH" (
    p_dvtt       VARCHAR2,
    p_tungay     VARCHAR2,
    p_denngay    VARCHAR2,
    p_khoa       VARCHAR2,
    p_hinhthuc   VARCHAR2
) RETURN SYS_REFCURSOR IS

    v_tungay    DATE := TO_DATE(p_tungay || ' 00:00:00', 'dd/mm/yyyy HH24:MI:SS');
    v_denngay   DATE := TO_DATE(p_denngay || ' 23:59:59', 'dd/mm/yyyy HH24:MI:SS');
    cur         SYS_REFCURSOR;
BEGIN
DELETE FROM his_manager.cmu_thoigian_nhanvien_temp
WHERE
    dvtt = p_dvtt
  AND thoigian_bd BETWEEN v_tungay AND v_denngay;

INSERT INTO "HIS_MANAGER"."CMU_THOIGIAN_NHANVIEN_TEMP" (
    "DVTT",
    "<PERSON>N<PERSON>N<PERSON>EN",
    "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
    "<PERSON>BE<PERSON>H<PERSON>HA<PERSON>",
    "NOITRU",
    "THOIGIAN_BD",
    "THOIGIAN_KT",
    "SOPHIEU",
    "MA_DV",
    "LOAI",
    "NGAYTAO"
)
SELECT
    "DVTT",
    "MANHANVIEN",
    "SOVAOVIEN",
    "MABENHNHAN",
    "NOITRU",
    "THOIGIAN_BD",
    "THOIGIAN_KT",
    "SOPHIEU",
    "MA_DV",
    "LOAI",
    "NGAYTAO"
FROM
    cmu_thoigian_nhanvien
WHERE
    dvtt = p_dvtt
  AND thoigian_bd BETWEEN v_tungay AND v_denngay;

IF p_hinhthuc = 1 THEN
        OPEN cur FOR SELECT DISTINCT
                            a.ngay_ra_vien,
                            nv.ten_nhanvien_cd,
                            a.ngayylenh,
                            a.tenbenhnhan,
                            a.mabenhnhan,
                            a.sovaovien,
                            a.ten_dich_vu,
                            a.ma_bac_si,
                            TO_CHAR(ngay_th_yl, 'DD/MM/YYYY HH24:MI') ngaygio_bd,
                            TO_CHAR(ngay_kq_cv130, 'DD/MM/YYYY HH24:MI') ngaygio_kt,
                            a.loai_kythuat
                     FROM
                            (
                                SELECT
                                    TO_CHAR(ngaygio_ra, 'DD/MM/YYYY HH24:MI:SS') ngay_ra_vien,
                                    0 noitru,
                                    TO_CHAR(kt.ngaygio_ylenh, 'DD/MM/YYYY HH24:MI') ngayylenh,
                                    ho_ten      tenbenhnhan,
                                    kcb.ma_bn   mabenhnhan,
                                    kcb.sovaovien,
                                    kt.ten_dich_vu,
                                    kt.ma_bac_si,
                                    kt.loai_kythuat,
                                    kt.ngay_yl,
                                    TO_DATE(kt.ngay_th_yl, 'YYYYMMDDHH24MI') ngay_th_yl,
                                    TO_DATE(kt.ngay_kq_cv130, 'YYYYMMDDHH24MI') ngay_kq_cv130
                                FROM
                                    his_synchronization.b1_chitieutonghop_kcb            kcb
                                        JOIN his_synchronization.b3_chitieuchitiet_kythuatvattu   kt ON kcb.dvtt = kt.dvtt
                                        AND kcb.sovaovien = kt.sovaovien
                                        AND kt.loai_kythuat = 'CHANDOANHINHANH'
                                        AND kcb.ma_bn = kt.ma_bn
                                WHERE
                                    kcb.dvtt = kt.dvtt
                                  AND ngay_quyet_toan_bh BETWEEN v_tungay AND v_denngay
                                  AND trang_thai_bn_tn != 7
                                 AND tu_thanh_toan = 0
                                 AND ti_le_mien_giam_the > 0
                                 AND kcb.dvtt = p_dvtt
                                 AND ma_loaikcb = 1
                                 AND v_denngay - v_tungay <= 1
                                 AND ( p_khoa = '-1'
                                       OR kcb.b_makhoahoantatkham = p_khoa )
                            ) a
                                JOIN his_fw.dm_nhanvien_cd        nv ON nv.ma_nhanvien = a.ma_bac_si
                                JOIN cmu_thoigian_nhanvien_temp   tgbs ON tgbs.manhanvien = a.ma_bac_si
                                AND tgbs.sovaovien != a.sovaovien
                                                                 AND tgbs.mabenhnhan != a.mabenhnhan
                                                                 AND ( a.ngay_th_yl BETWEEN tgbs.thoigian_bd AND tgbs.thoigian_kt
                                                                       OR a.ngay_kq_cv130 BETWEEN tgbs.thoigian_bd AND tgbs.thoigian_kt
                                                                       )
                     ORDER BY
                            tenbenhnhan;

ELSE
        OPEN cur FOR SELECT DISTINCT
                         a.ngay_ra_vien,
                         nv.ten_nhanvien_cd,
                         a.ngayylenh,
                         a.tenbenhnhan,
                         a.mabenhnhan,
                         a.sovaovien,
                         a.ten_dich_vu,
                         a.ma_bac_si,
                         TO_CHAR(ngay_th_yl, 'DD/MM/YYYY HH24:MI') ngaygio_bd,
                         TO_CHAR(ngay_kq_cv130, 'DD/MM/YYYY HH24:MI') ngaygio_kt,
                         a.loai_kythuat
                     FROM
                         (
                             SELECT
                                 TO_CHAR(ngaygio_ra, 'DD/MM/YYYY HH24:MI:SS') ngay_ra_vien,
                                 0 noitru,
                                 TO_CHAR(kt.ngaygio_ylenh, 'DD/MM/YYYY HH24:MI') ngayylenh,
                                 ho_ten      tenbenhnhan,
                                 kcb.ma_bn   mabenhnhan,
                                 kcb.sovaovien_noi sovaovien,
                                 kt.ten_dich_vu,
                                 kt.ma_bac_si,
                                 kt.loai_kythuat,
                                 kt.ngay_yl,
                                 TO_DATE(kt.ngay_th_yl, 'YYYYMMDDHH24MI') ngay_th_yl,
                                 TO_DATE(kt.ngay_kq_cv130, 'YYYYMMDDHH24MI') ngay_kq_cv130
                             FROM
                                 his_synchronization.b1_chitieutonghop_kcb            kcb
                                     JOIN his_synchronization.b3_chitieuchitiet_kythuatvattu   kt ON kcb.dvtt = kt.dvtt
                                     AND kcb.sovaovien_noi = kt.sovaovien_noitru
                                     AND kt.loai_kythuat = 'cdha'
                                     AND kcb.ma_bn = kt.ma_bn
                             WHERE
                                 kcb.dvtt = kt.dvtt
                               AND ngay_quyet_toan_bh BETWEEN v_tungay AND v_denngay
                               AND ti_le_mien_giam_the > 0
                               AND kcb.dvtt = p_dvtt
                               AND ma_loaikcb != 1
                                 AND trang_thai_ptt < 4
                                 AND v_denngay - v_tungay <= 1
                                 AND ( p_khoa = '-1'
                                       OR kcb.b_makhoahoantatkham = p_khoa )
                         ) a
                             JOIN his_fw.dm_nhanvien_cd        nv ON nv.ma_nhanvien = a.ma_bac_si
                             JOIN cmu_thoigian_nhanvien_temp   tgbs ON tgbs.manhanvien = a.ma_bac_si
                             AND tgbs.sovaovien != a.sovaovien
                                                                 AND tgbs.mabenhnhan != a.mabenhnhan
                                                                 AND ( a.ngay_th_yl BETWEEN tgbs.thoigian_bd AND tgbs.thoigian_kt
                                                                       OR a.ngay_kq_cv130 BETWEEN tgbs.thoigian_bd AND tgbs.thoigian_kt
                                                                       )
                     ORDER BY
                         tenbenhnhan;

END IF;

RETURN cur;
END;