var keyLuuLog = {
    // Thông tin bệnh nhân
    "SO_DIEN_THOAI": "Số điện thoại",
    "SO_NHA": "<PERSON><PERSON> nhà",
    "TEN_BENH_NHAN": "Tên bệnh nhân",
    "CANNANG": "<PERSON><PERSON> nặng",
    "CAPCUU": "<PERSON><PERSON><PERSON> cứu",
    "CHANDOAN_NGUYENNHAN": "Chẩn đoán nguyên nhân",
    "CHANDOAN_NOIGIOITHIEU": "Chẩn đoán nơi giới thiệu",
    "CHIEUCAO": "<PERSON><PERSON><PERSON> cao",
    "CMT_BENHNHAN": "CCCD Bệnh nhân",
    "DIA_CHI": "Địa chỉ",
    "DIA_CHI_EMAIL": "Địa chỉ email bệnh nhân",
    "GIOI_TINH_HT": "Giới tính",
    "GIO_VAO_VIEN": "<PERSON><PERSON><PERSON> vào viện",
    "HINHTHUCKETTHUC": "<PERSON><PERSON><PERSON> thứ<PERSON> kết thúc",
    "HO_TEN_CHA": "<PERSON>ọ tên cha",
    "HO_TEN_ME": "<PERSON>ọ tên mẹ",
    "ICD_HT": "ICD",
    "TENBENHPHU_NHAPVIEN": "Tên bệnh phụ nhập viện",
    "MA_DANTOC": "Mã dân tộc",
    "MA_NGHE_NGHIEP": "Mã nghề nghiệp",
    "MA_CONG_TY": "Mã công ty",
    "NGAYGIO_NHAPVIEN": "Ngày giờ nhập viện",
    "NGAYGIOKHAMNGOAITRU": "Ngày giờ khám ngoại trú",
    "NGUOI_LIEN_HE": "Người liên hệ",
    "NGOAIKIEU": "Mã quốc tịch",
    "NHOMMAU": "Nhóm máu",
    "KHANGTHE": "Kháng thể",
    "NGAY_SINH": "Ngày sinh",
    // BHYT
    "COBHYT": "Có BHYT",
    "SOBAOHIEMYTE": "Số bảo hiểm y tế",
    "TENNOIDANGKYBANDAU": "Tên nơi đăng ký ban đầu",
    "NGAYBATDAU_THEBHYT": "Ngày bắt đầu thẻ BHYT",
    "NGAYHETHAN_THEBHYT": "Ngày hết hạn thẻ BHYT",
    "BAOHIEMYTE5NAM": "Miễn cùng chi trả",
    "NGAY_MIEN_CUNG_CT": "Ngày miễn cùng chi trả",
    "TEN_NOIGIOITHIEU": "Tên nơi giới thiệu",

    // Bảng kiểm trước tiêm chủng trẻ sơ sinh
    "NGAY_GIO_SINH_TEXT": "Ngày giờ sinh",
    "TUOI_THAI_KHI_SINH": "Tuổi thai khi sinh",
    "SDT_CHA": "Số điện thoại cha",
    "SDT_ME": "Số điện thoại mẹ",
    "CAN_NANG": "Cân nặng",
    "THAN_NHIET": "Thân nhiệt",
    "ME_XN_HBSAG": "Mẹ xét nghiệm HBsAg",
    "KET_QUA_ME_XN_HBSAG": "Kết quả mẹ xét nghiệm HBsAg",
    "SUC_KHOE_CHUA_ON_DINH": "Tình trạng sức khỏe chưa ổn định",
    "SOC_HA_THAN_NHIET": "Sốt/Hạ thân nhiệt (Sốt: nhiệt độ ≥ 37,5℃; Hạ thân nhiệt: nhiệt độ ≤ 35,5℃",
    "KHOC_BE_KHONG_KHOC": "Khóc bé hoặc không khóc",
    "DA_MOI_KHONG_HONG": "Da, môi không hồng",
    "BU_KEM_BO_BU": "Bú kém hoặc bỏ bú",
    "TUOI_THAI_28_TUAN": "Tuổi thai < 28 tuần",
    "TRE_34_TUAN_TUOI": "Trẻ < 34 tuần tuổi",
    "TRE_2000_ME_CO_HBSAG": "Trẻ < 2000g, mẹ có HBsAg (-)",
    "IS_CHI_DINH_KHAC": "Các chống chỉ định khác, nếu có ghi rõ",
    "CHI_DINH_KHAC": "Nội dung chống chỉ định khác",
    "IS_SANG_LOC_CHUYEN_KHOA": "Khám sàng lọc theo chuyên khoa",
    "SANG_LOC_CHUYEN_KHOA": "Nội dung khám sàng lọc theo chuyên khoa",
    "LY_DO": "Lý do",
    "KET_QUA": "Kết quả",
    "KET_LUAN": "Kết luận",
    "LOAI_VAC_XIN": "Loại vắc xin",
    "NGUOI_THUC_HIEN": "Người thực hiện",
    "NGAY_LAP_PHIEU_TEXT": "Ngày lập phiếu",

    // Kế hoạch chăm sóc
    "THUC_HIEN_CHAM_SOC": "Thực hiện chăm sóc",
    "TINH_TRANG_NGUOI_BENH": "Tình trạng người bệnh",
    "SO_PHIEU": "Số phiếu",
    "SO_GIUONG": "Số giường",
    "LUONG_GIA": "Lượng giá",
    "KE_HOACH_CHAM_SOC": "Kế hoạch chăm sóc",

    //Thông tin xuất viện
    "TRIEUCHUNG": "Triệu chứng",
    "ICD_BENHCHINH": "ICD bệnh chính",
    "TEN_BENHCHINH": "Tên bệnh chính",
    "BENHKEMTHEO": "Bệnh kèm theo",
    "KETQUADIEUTRI": "Kết quả điều trị",
    "TINHTRANG_RV": "Tình trạng ra viện",
    "PP_DIEUTRI": "Phương pháp điều trị",
    "NGAY_RA_VIEN": "Ngày ra viện",
    "NGAY_HENTAIKHAM": "Ngày hẹn tái khám",
    "HENTAIKHAM": "Số ngày hẹn",
    "SOLUUTRU_TAIKHAM": "Số lưu trữ tái khám",
    "GHICHU": "Ghi chú",
    "MA_BENH_YHCT": "Mã bệnh YHCT",
    "NGOAITRU_TUNGAY": "Ngoại trú từ ngày",
    "NGOAITRU_DENNGAY": "Ngoại trú đến ngày",
    "SO_NGAY_NGHI": "Số ngày nghỉ",
    "MA_BAC_SI_TRUONGKHOA": "Mã BS trưởng khoa",
    "MA_BAC_SI_TRUONGDONVI": "Mã BS trưởng đơn vị",
    "SOLUUTRU_XUATVIEN": "Số lưu trữ xuất viện",
    "SOGIAY_HENTAIKHAM": "Số giấy hẹn tái khám",
    //END thông tin xuất viện

    //Biên bản tử vong
    "ID": "ID",
    "NGAY_TU_VONG": "Tử vong lúc",
    "MAKHOA": "Mã khoa",
    "TENKHOA": "Tại khoa",
    "NGAY_KIEM_DIEM_TU_VONG": "Kiểm điểm tử vong lúc",
    "CHU_TOA": "Mã chủ tọa",
    "TEN_CHU_TOA": "Chủ tọa",
    "THU_KY": "Mã thư ký",
    "TEN_THU_KY": "Thư ký",
    "THANH_VIEN": "Thành viên tham gia",
    "TOM_TAT": "Tóm tắt quá trình diễn biến bệnh, quá trình điều trị và chăm sóc người bệnh",
    //END Biên bản tử vong
    // Phiếu theo dõi Bilan
    "NGAY_GIO_TAO_PHIEU_BILAN": "Ngày giờ tạo phiếu",
    "NGUOI_TAO_PHIEU_BILAN": "Người tạo phiếu",
    "LUONGNUOC_MAT_SINHLY": "Lượng nước mất sinh lý",
    "CANBANG_BILAN_AM": "Cân bằng Bilan âm",
    "CANBANG_BILAN_DUONG": "Cân bằng Bilan dương",
    "SO_PHIEU_BILAN": "Số phiếu Bilan",
    "NGAYGIO_LAN_THEODOI": "Ngày giờ lần theo dõi",
    "DUONG_TRUYEN_DICH": "Đường truyền dịch",
    "BOM_QUA_SONDE": "Bơm qua sonde",
    "THUC_AN_LONG": "Thức ăn lỏng",
    "NUOC_UONG": "Nước uống",
    "DADAY_NUOC_TRANG_ONG": "Dạ dày + nước tráng ống",
    "QUA_DUONG_TIEU": "Qua đường tiểu",
    "QUA_SONDE_DADAY": "Qua sonde dạ dày",
    "NON_OI": "Nôn ói",
    "TIEU_PHAN_LONG": "Tiêu phân lỏng",
    "DICH_KHAC": "Dịch khác",
    //BEGIN Thông tin chuyển viện
    "KHOA": "Khoa",
    "NGAY_VV" : "Ngày vào viện",
    "NGAY_RV": "Ngày ra viện",
    "BENH_VIEN": "Bệnh viện chuyển đến",
    "TUYEN": "Tuyến chuyển",
    "TINH_TIEN_CONG_KHAM": "Tính tiền công khám",
    "ICD_NHAP_VIEN" : "ICD nhập viện",
    "TEN_ICD_NHAP_VIEN" : "Tên ICD nhập viện",
    "ICD_BENH_CHUYEN" : "ICD chuyển viện",
    "TEN_ICD_BENH_CHUYEN" : "Tên ICD chuyển viện",
    "ICD_RV_YHCT": "ICD ra viện theo yhct",
    "TEN_ICD_RV_YHCT" : "Tên ICD ra viện theo yhct",
    "ICD_BENH_PHU": "ICD bệnh phụ",
    "TEN_ICD_BENH_PHU" : "Tên ICD bệnh phụ",
    "DAU_HIEU_CLC" : "Dấu hiệu cận lâm sàn",
    "KQ_XN_CLS" : "Kết quả xét nghiệm cận lâm sàn",
    "PP_TT_KT_T": "Phương pháp, kỹ thuật, kỹ thuật, thuốc",
    "KQ_DT" : "Kết quả điều trị",
    "TT_BN" : "Tình trạng bệnh nhân",
    "HDT" : "Hướng điều trị",
    "LOIDAN_BS": "Lời dặn của bác sĩ",
    "LYDO_CT": "Lý do chuyển tuyến",
    "SO_CHUYEN_TYT" : "Số chuyển tuyến TYT",
    "TG_CT_TYT_TN" : "Chuyển tuyến TYT từ ngày",
    "TG_CT_TYT_DN": "Chuyển tuyến TYT đến ngày",
    "SO_CHUYEN_PKDK": "Số chuyển tuyến PKDK",
    "TG_CT_PKDK_TN":  "Chuyển tuyến PKDK từ ngày",
    "TG_CT_PKDK_DN": "Chuyển tuyến PKDK đến ngày",
    "SO_CT_TTYT":  "Số chuyển tuyến TTYT",
    "TG_CT_TTYT_TN": "Chuyển tuyến TTYT từ ngày",
    "TG_CT_TTYT_DN":  "Chuyển tuyến TTYT đến ngày",
    "SO_CT_DKT": "Số chuyển tuyến DKT",
    "TG_CT_DKT_TN": "Chuyển tuyến DKT từ ngày",
    "TG_CT_DKT_DN": "Chuyển tuyến DKT đến ngày",
    "PHUONG_TIEN": "Phương tiện",
    "LENH_DIEU_XE": "Lệnh điều xe",
    "LOAI_XANG_DAU": "Loại xăng dầu",
    "HO_TEN_NGUOI_DUA": "Họ tên người đưa đi",
    "SO_LUOT_CHUYEN": "Số lượt chuyển",
    "LOAI_HINH": "Loại hình",
    "CHUYEN_TUYEN_CUNG": "Chuyển tuyến cùng người khác",
    "TIEN_VAN_CHUYEN": "Tiền vận chuyển",
    "BS": "Bác sĩ",
    "TK": "Trưởng khoa",
    "GD": "Giám đốc",
    "NOI_LAM_VIEC": "Nơi làm việc",
    "GHI_CHU": "Ghi chú",
    "SO_LUU_TRU": "Số lưu trữ",
    "SO_LUU_TRU_TAM": "Số lưu trữ tạm"
    //END Thông tin chuyển viện
}
function luuLogHSBATheoBN(data){
    if(data.NGOAI == 1) {
        $.post("cmu_post_HSBA_LOG_NGOAI_INS", {
            url: [
                singletonObject.dvtt,
                data.SOVAOVIEN,
                data.LOAI,
                data.NOIDUNGBANDAU,
                data.NOIDUNGMOI,
                data.USERID,
                data.ACTION,
            ].join("```")
        })
    } else {
        $.post("cmu_post_HSBA_LOG_INS", {
            url: [
                singletonObject.dvtt,
                data.SOVAOVIEN,
                data.LOAI,
                data.NOIDUNGBANDAU,
                data.NOIDUNGMOI,
                data.USERID,
                data.ACTION,
            ].join("```")
        })
    }

}

function luuLogHSBAChinhSua(obj_old, obj_new, loai) {
    let keysWithDifferences = findDifferencesBetweenObjects(obj_old, obj_new);
    let logOld = "", logNew = "";
    for (let key in keysWithDifferences) {
        if (keyLuuLog.hasOwnProperty(key)) {
            logOld += keyLuuLog[key] + ": " + keysWithDifferences[key][0] + "; ";
            logNew += keyLuuLog[key] + ": " + keysWithDifferences[key][1] + "; ";
        }
    }
    thongtinhsba.thongTinMoi = {};
    thongtinhsba.thongTinCu = {};
    if(logOld != "" || logNew != ""){
        var dataLog = {
            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
            LOAI: loai,
            NOIDUNGBANDAU: logOld,
            NOIDUNGMOI: logNew,
            USERID: singletonObject.userId,
            ACTION: "UPDATE"
        }
        luuLogHSBATheoBN(dataLog);
    }
    return logNew;
}

function findDifferencesBetweenObjects(obj_old, obj_new) {
    let keysWithDifferences = {};
    for (let key in obj_old) {
        if (obj_new.hasOwnProperty(key)) {
            if (obj_old[key] != obj_new[key]) {
                keysWithDifferences[key] = [obj_old[key], obj_new[key]];
            }
        } else {
            keysWithDifferences[key] = [obj_old[key], obj_new[key]];
        }
    }
    for (let key in obj_new) {
        if (!obj_old.hasOwnProperty(key)) {
            keysWithDifferences[key] = [obj_old[key], obj_new[key]];
        }
    }
    return keysWithDifferences;
}

function getLogHSBAChinhSua(obj_old, obj_new, objectKeyLuuLog) {
    let keysWithDifferences = findDifferencesBetweenObjects(obj_old, obj_new);
    let logOld = [], logNew = [], stringLogOld = "", stringLogNew = "";
    for (let key in keysWithDifferences) {
        if (objectKeyLuuLog.hasOwnProperty(key)) {
            logOld.push(objectKeyLuuLog[key] + ": " + (keysWithDifferences[key][0] ? keysWithDifferences[key][0] : "_"));
            logNew.push(objectKeyLuuLog[key] + ": " + (keysWithDifferences[key][1] ? keysWithDifferences[key][1] : "_"));
        }
    }
    stringLogNew = logNew.join("; ");
    stringLogOld = logOld.join("; ");
    var arrLog = [];
    if(stringLogOld != "" || stringLogNew != ""){
        arrLog.push(stringLogOld);
        arrLog.push(stringLogNew);
    }
    return arrLog;
}

function convertKeyToValueLogHSBA(object) {
    var arrLog = [];
    for (var key in object) {
        if (keyLuuLog.hasOwnProperty(key)) {
            arrLog.push(keyLuuLog[key] + ": " + object[key]);
        }
    }
    var stringLog = arrLog.join("; ");
    return stringLog;
}

function findDifferencesBetweenObjectsFormio(obj_old, obj_new) {
    function checkDate(string) {
        if (typeof string === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:[+-]\d{2}:\d{2}|Z)?$/.test(string)) {
            let date = moment(string);
            if (!date.isValid()) {
                return string;
            } else {
                return date.format('DD/MM/YYYY HH:mm');
            }
        } else {
            return string;
        }
    }

    function isPrimitive(value) {
        return (typeof value !== 'object' || value === null) && !Array.isArray(value);
    }

    let keysWithDifferences = {};
    for (let key in obj_old) {
        var dataOld, dataNew;
        dataOld = checkDate(obj_old[key]);
        if (obj_new.hasOwnProperty(key)) {
            if (isPrimitive(obj_old[key]) && isPrimitive(obj_new[key])) {
                dataNew = checkDate(obj_new[key]);
                if (dataOld != dataNew) {
                    keysWithDifferences[key] = [dataOld, dataNew];
                }
            }

        } else {
            if (isPrimitive(obj_old[key])) {
                keysWithDifferences[key] = [dataOld, "_"];
            }
        }
    }
    for (let key in obj_new) {
        if (isPrimitive(obj_new[key])) {
            var dataNew = checkDate(obj_new[key]);
            if (!obj_old.hasOwnProperty(key)) {
                keysWithDifferences[key] = ["_", dataNew];
            }
        }
    }
    return keysWithDifferences;
}

function luuLogHSBAChinhSuaFormio(obj_old, obj_new, loai, objectKeyLuuLog) {
    let keysWithDifferences = findDifferencesBetweenObjectsFormio(obj_old, obj_new);
    let logOld = [], logNew = [], stringLogOld = "", stringLogNew = "";
    for (let key in keysWithDifferences) {
        if (objectKeyLuuLog.hasOwnProperty(key)) {
            logOld.push(objectKeyLuuLog[key] + ": " + (keysWithDifferences[key][0] != null && keysWithDifferences[key][0] !== "" ? keysWithDifferences[key][0] : "_"));
            logNew.push(objectKeyLuuLog[key] + ": " + (keysWithDifferences[key][1] != null && keysWithDifferences[key][1] !== "" ? keysWithDifferences[key][1] : "_"));
        }
    }
    stringLogNew = logNew.join("; ");
    stringLogOld = logOld.join("; ");
    if(stringLogOld != "" || stringLogNew != ""){
        var dataLog = {
            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
            LOAI: loai,
            NOIDUNGBANDAU: stringLogOld,
            NOIDUNGMOI: stringLogNew,
            USERID: singletonObject.userId,
            ACTION: LOGHSBAACTION.EDIT.KEY
        }
        luuLogHSBATheoBN(dataLog);
    }
    return logNew;
}

function luuLogHSBAChinhSuaFormioV2(obj_old, obj_new, loai, formIO, prefix) {
    let keysWithDifferences = findDifferencesBetweenObjectsFormio(obj_old, obj_new);
    let logOld = [], logNew = [], stringLogOld = "", stringLogNew = "";
    for (let key in keysWithDifferences) {
        var component = formIO.getComponent(key);
        if (component) {
            logOld.push(component.label + ": " + (keysWithDifferences[key][0] != null && keysWithDifferences[key][0] !== "" ? keysWithDifferences[key][0] : "_"));
            logNew.push(component.label + ": " + (keysWithDifferences[key][1] != null && keysWithDifferences[key][1] !== "" ? keysWithDifferences[key][1] : "_"));
        }
    }
    stringLogNew = logNew.join("; ");
    stringLogOld = logOld.join("; ");
    if(stringLogOld != "" || stringLogNew != ""){
        var dataLog = {
            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
            LOAI: loai,
            NOIDUNGBANDAU: prefix+ stringLogOld,
            NOIDUNGMOI: prefix+ stringLogNew,
            USERID: singletonObject.userId,
            ACTION: LOGHSBAACTION.EDIT.KEY
        }
        luuLogHSBATheoBN(dataLog);
    }
    return logNew;
}

function findAllFinalComponents(dataArray) {
    let finalComponents = [];
    function findComponentData(obj) {
        if (obj.hasOwnProperty('components')) {
            for (let item of obj.components) {
                findComponentData(item);
            }
        } else {
            finalComponents.push(obj.component);
        }
    }
    for (let obj of dataArray) {
        findComponentData(obj);
    }
    return finalComponents;
}

// Start Lưu log V3
function getObjectLuuLogFormIO(form) {
    // Lấy những input có custom class "luulog" trả về object chứa 1 object danh sách key và label và 1 object chứa key và value
    var allComponents = findAllFinalComponents(form.components);
    var arrComponentLuuLog = allComponents.filter(function (component) {
        return component.customClass && component.customClass.includes("luulog");
    });
    var objectKeyLabel = {};
    var objectKeyValue = {};
    arrComponentLuuLog.forEach(function (component) {
        objectKeyLabel[component.key] = component.label;
        objectKeyValue[component.key] = getValueOfFormIO(form.getComponent(component.key));
    });
    return {
        objectKeyLabel: objectKeyLabel,
        objectKeyValue: objectKeyValue
    }
}

function luuLogHSBAChinhSuaFormioV3(objOld, formIO, loai, prefix, getObject = 0) {
    var objNew = getObjectLuuLogFormIO(formIO);
    var keysWithDifferences = findDifferencesBetweenObjects(objOld, objNew.objectKeyValue);
    var logOld = [], logNew = [], stringLogOld = "", stringLogNew = "";
    for (let key in keysWithDifferences) {
        if (objNew.objectKeyLabel.hasOwnProperty(key)) {
            logOld.push(objNew.objectKeyLabel[key] + ": " + (keysWithDifferences[key][0] != null && keysWithDifferences[key][0] !== "" ? keysWithDifferences[key][0] : "_"));
            logNew.push(objNew.objectKeyLabel[key] + ": " + (keysWithDifferences[key][1] != null && keysWithDifferences[key][1] !== "" ? keysWithDifferences[key][1] : "_"));
        }
    }
    stringLogNew = logNew.join("; ");
    stringLogOld = logOld.join("; ");
    if(getObject == 0){
        if(stringLogOld != "" || stringLogNew != ""){
            var dataLog = {
                SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                LOAI: loai,
                NOIDUNGBANDAU: prefix + stringLogOld,
                NOIDUNGMOI: prefix + stringLogNew,
                USERID: singletonObject.userId,
                ACTION: LOGHSBAACTION.EDIT.KEY
            }
            luuLogHSBATheoBN(dataLog);
        }
    } else {
        return {
            stringLogNew: stringLogNew,
            stringLogOld: stringLogOld
        }
    }
}

function luuLogHSBAInsertFormioV3(formIO, loai, prefix, getObject = 0) {
    var objNew = getObjectLuuLogFormIO(formIO);
    var stringLogNew = convertKeyToValueLogHSBA(objNew.objectKeyValue, objNew.objectKeyLabel);
    if(getObject == 1){
        return {
            stringLogNew: stringLogNew
        }
    }
    if(stringLogNew != ""){
        var dataLog = {
            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
            LOAI: loai,
            NOIDUNGBANDAU: " ",
            NOIDUNGMOI: prefix + stringLogNew,
            USERID: singletonObject.userId,
            ACTION: LOGHSBAACTION.INSERT.KEY
        }
        luuLogHSBATheoBN(dataLog);
    }
}
// End Lưu log V3

// Start Lưu log V4
function luuLogHSBAInsertFormioV4(formIO, loai, prefix, subData, customLog) {
    let objNew = getObjectLuuLogFormIO(formIO);
    let dataNew = formIO.submission.data;
    let arrLog = [];
    for (let key in objNew.objectKeyValue) {
        if (objNew.objectKeyLabel.hasOwnProperty(key)) {
            arrLog.push(objNew.objectKeyLabel[key] + ": " + (
                dataNew[key] != null && dataNew[key] !== "" && typeof dataNew[key] !== 'undefined'
                    ? getMainValueFromObject(dataNew[key], subData) : "_")
            );
        }
    }

    if (customLog && Array.isArray(customLog) && customLog.length > 0){
        // customLog phải là một danh sách có giá trị.
        // Example: [1, "a"]
        arrLog = $.merge(arrLog, customLog);
    }

    let stringLogNew = arrLog.join("; ");
    if (stringLogNew !== ""){
        let checkByte = new TextEncoder().encode(stringLogNew).length
        if (checkByte <= 2000) {
            let dataLog = {
                SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                LOAI: loai,
                NOIDUNGBANDAU: " ",
                NOIDUNGMOI: prefix + stringLogNew,
                USERID: singletonObject.userId,
                ACTION: LOGHSBAACTION.INSERT.KEY
            }
            luuLogHSBATheoBN(dataLog);
        }
        else {
            let logParts = tachLogStr(stringLogNew, 2000, prefix)
            $.each(logParts, function (_, v) {
                let dataLog = {
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: loai,
                    NOIDUNGBANDAU: " ",
                    NOIDUNGMOI: v,
                    USERID: singletonObject.userId,
                    ACTION: LOGHSBAACTION.INSERT.KEY
                }
                luuLogHSBATheoBN(dataLog);
            })
        }
    }
}

function luuLogHSBAChinhSuaFormioV4(objOld, formIO, loai, prefix, subData, customLog) {
    let objNew = getObjectLuuLogFormIO(formIO);
    let dataNew = formIO.submission.data;
    let keysWithDifferences = findDifferencesBetweenObjects(objOld, dataNew);
    let logOld = [], logNew = [], stringLogOld = "", stringLogNew = "";
    for (let key in keysWithDifferences) {
        if (objNew.objectKeyLabel.hasOwnProperty(key)) {
            logOld.push(objNew.objectKeyLabel[key] + ": " + (
                keysWithDifferences[key][0] != null
                && keysWithDifferences[key][0] !== ""
                && typeof keysWithDifferences[key][0] !== 'undefined'
                    ? getMainValueFromObject(keysWithDifferences[key][0], subData) : "_")
            );
            logNew.push(objNew.objectKeyLabel[key] + ": " + (
                keysWithDifferences[key][1] != null
                && keysWithDifferences[key][1] !== ""
                && typeof keysWithDifferences[key][1] !== 'undefined'
                    ? getMainValueFromObject(keysWithDifferences[key][1], subData) : "_")
            );
        }
    }

    if (customLog && !$.isEmptyObject(customLog)){
        // Khóa trong customLog phải là customLogOld and customLogNew và chúng phải là một danh sách có giá trị.
        // Example: [1, "a"]
        if (Array.isArray(customLog.customLogOld) && customLog.customLogOld.length > 0) {
            logOld = $.merge(logOld, customLog.customLogOld);
        }
        if (Array.isArray(customLog.customLogNew) && customLog.customLogNew.length > 0) {
            logNew = $.merge(logNew, customLog.customLogNew);
        }
    }

    stringLogNew = logNew.join("; ");
    stringLogOld = logOld.join("; ");
    if(stringLogOld !== "" || stringLogNew !== ""){
        let dataLog = {
            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
            LOAI: loai,
            NOIDUNGBANDAU: prefix + stringLogOld,
            NOIDUNGMOI: prefix + stringLogNew,
            USERID: singletonObject.userId,
            ACTION: LOGHSBAACTION.EDIT.KEY
        }
        luuLogHSBATheoBN(dataLog);
    }
}

function getMainValueFromObject(data, subData) {
    if (data && typeof data === "object") {
        if ($.isEmptyObject(data)) { return "_" }
        // Tự định hướng set value những giữ liệu bên trong 1 object cần thấy vào mainKeys
        let mainKeys = ["tennhanvien", "TEN_NGHE_NGHIEP", "TEN_DANTOC", "TEN_TINH", "TEN_HUYEN", "TEN_XA", "TEXT"];
        for (let key of mainKeys) {
            if (data.hasOwnProperty(key)) { return data[key]; }
        }
    }

    if (subData.hasOwnProperty(data)) { return subData[data]; }

    let datetimeData = moment(data, moment.ISO_8601, true);
    if (datetimeData.isValid()) {
        let fVal = datetimeData.hours() === 0 && datetimeData.minutes() === 0 ? "DD-MM-YYYY" : "DD-MM-YYYY HH:mm"
        return datetimeData.format(fVal);
    }
    return data;
}

function tachLogStr(logStr, maxLength, prefix) {
    let logParts = [];
    let start = 0, index = 1;
    let flagAgain = false;

    while (start < logStr.length) {
        let end = Math.min(start + maxLength, logStr.length);
        if (end < logStr.length && logStr[end] !== ";") {
            let chamPhayGanNhat = logStr.lastIndexOf(";", end);
            if (chamPhayGanNhat > start) {
                end = chamPhayGanNhat + 1;
            }
        }
        let endStr = `${prefix}Phần ${index} - ${logStr.substring(start, end).trim()}`
        let checkByte = new TextEncoder().encode(endStr).length
        if (checkByte > 2000) {
            flagAgain = true;
            break;
        }
        logParts.push(endStr);
        start = end;
        index += 1;
    }
    return flagAgain ? tachLogStr(logStr, maxLength - 100, prefix) : logParts;
}
// End Lưu log V4

function luuLogHSBADeleteFormio(loai, prefix) {
    let dataLog = {
        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
        LOAI: loai,
        NOIDUNGBANDAU: prefix,
        NOIDUNGMOI: " ",
        USERID: singletonObject.userId,
        ACTION: LOGHSBAACTION.DELETE.KEY
    }
    luuLogHSBATheoBN(dataLog);
}

function getValueOfFormIO(component) {
    if(!component) {
        return "";
    }
    var value = component.data[component.component.key]
    var componentElemement = component.component;
    if (componentElemement.widget && componentElemement.widget.type == "calendar") {
        if (componentElemement.widget.format.match(/HH:mm/)){
            var phanNgay = componentElemement.widget.format.split(' ')[0];
            var phanGio = componentElemement.widget.format.split(' ')[1];
            var phanNgayInHoa = phanNgay.toUpperCase();
            var ngayFormatMoi = phanNgayInHoa + ' ' + phanGio;
            return moment(value).utcOffset("+07:00").format(ngayFormatMoi)
        }
        return value? moment(value).utcOffset("+07:00").format(componentElemement.widget.format.toUpperCase()): "";
    } else if (componentElemement.type === "selectboxes") {
        var selectedBox = []
        Object.keys(value).forEach(function(key) {
            if (value[key] == true) {
                componentElemement.values.map(function(item) {
                    if(item.value == key) {
                        selectedBox.push(item.label)
                    }
                })
            }
        });
        return selectedBox.join(", ");
    } else if (componentElemement.widget == "choicesjs") {
        const result = componentElemement.template.replace(/{{\s*([a-zA-Z0-9_]+)\.([a-zA-Z0-9_]+)\s*}}/, (match, p1, p2) => value[p2] || '');
        return $(result).text();
    } else if (componentElemement.widget == "radio") {
        const result = componentElemement.values.find(item => item.value == value);
        return result ? result.label : "";
    }
    return value;
}