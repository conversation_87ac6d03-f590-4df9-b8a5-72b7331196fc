CREATE OR REPLACE
FUNCTION f_ma_donvi_moi (
    p_sovaovien      IN VARCHAR2,
    p_sovaovien_dt       IN VARCHAR2,
    p_dvtt            IN VARCHAR2,
    p_loai_kcb           IN VARCHAR2
) RETURN VARCHAR2
IS
    v_madonvi_moi VARCHAR2(10);
    v_ngayttoan_xml varchar2(20);
BEGIN
    if p_loai_kcb = '1' then
select TO_CHAR(ngay_thanh_toan,'yyyyMMdd') into v_ngayttoan_xml from HIS_MANAGER.KB_PHIEUTHANHTOAN where sovaovien = p_sovaovien;
else
select TO_CHAR(ngay_thanh_toan,'yyyyMMdd') into v_ngayttoan_xml from HIS_MANAGER.NOITRU_PHIEUTHANHTOAN where sovaovien = p_sovaovien
                                                                                                         and sovaovien_dt = p_sovaovien_dt and rownum = 1;
end if;

SELECT d.MADONVI_MOI
INTO v_madonvi_moi
FROM DM_DONVI_CHUYEN_DOI d
WHERE d.MADONVI = p_dvtt
  AND v_ngayttoan_xml BETWEEN d.NGAY_HIEU_LUC AND NVL(d.NGAY_HET_HIEU_LUC, '99991231')
    FETCH FIRST 1 ROWS ONLY;


RETURN v_madonvi_moi;

EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN p_dvtt;
WHEN OTHERS THEN
        RETURN NULL;
END;
/



-- End of DDL Script for Function HIS_MANAGER.F_MA_DONVI_MOI

