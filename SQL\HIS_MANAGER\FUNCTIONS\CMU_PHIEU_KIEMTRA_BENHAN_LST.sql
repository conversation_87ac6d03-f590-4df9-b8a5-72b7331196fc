create or replace FUNCTION "HIS_MANAGER"."CMU_PHIEU_KIEMTRA_BENHAN_LST" (
    p_DVTT IN VARCHAR2,
    p_SOVAOVIEN IN VARCHAR2
) RETURN SYS_REFCURSOR IS
    v_cursor SYS_REFCURSOR;
BEGIN
    OPEN v_cursor FOR
    SELECT
        CK.ID,
        CK.DVTT,
        CK.MA_BENHNHAN,
        CK.SOVAOVIEN,
        CK.SOVAOVIEN_DT,
        CK.STT_BENHAN,
        CK.NGUOI_TAO,
        CK.KHOA_TAO,
        CK.DATA_PHIEU,
        NV.TEN_NHANVIEN_CD TENNGUOITAO,
        TO_CHAR(CK.CREATEDDATE, 'DD/MM/YYYY HH24:MI:SS') NGAYTAO,
        SS.KEYSIGN KEYSIGN_1
    FROM "HIS_MANAGER"."CMU_PHIEU_KIEMTRA_BENHAN" CK
        JOIN HIS_FW.DM_NHANVIEN_CD NV
            ON CK.NGUOI_TAO = NV.MA_NHANVIEN
        LEFT JOIN HIS_MANAGER.SMARTCA_SIGNED_KCB SS
            ON CK.DVTT = SS.DVTT
            AND CK.SOVAOVIEN = SS.SOVAOVIEN
            AND SS.SO_PHIEU_DV = CK.ID
            AND SS.STATUS = 0
            AND SS.KY_HIEU_PHIEU IN ('PHIEUKTRABA_NGUOI_KIEMTRA')
    WHERE CK.DVTT = p_DVTT AND CK.SOVAOVIEN = p_SOVAOVIEN
    ORDER BY CK.ID ASC;

    RETURN v_cursor;
END CMU_PHIEU_KIEMTRA_BENHAN_LST;