CREATE OR REPLACE
FUNCTION              "F_CMUKTRAXML4750_THUOC" (
p_dvtt varchar2,
p_tungay varchar2,
p_denngay varchar2,
p_khoa varchar2,
p_hinhthuc varchar2
) return SYS_REFCURSOR
IS
v_tungay date:=to_date(p_tungay, 'dd/mm/yyyy');
v_denngay date:=to_date(p_denngay, 'dd/mm/yyyy');
cur SYS_REFCURSOR;
BEGIN
     if p_hinhthuc = 0 then --<PERSON>ội trú
         open cur for
select TO_CHAR(NGAYGIO_RA,'DD/MM/YYYY HH24:MI') NGAY_RA_VIEN,
       TO_CHAR(NGAYGIO_VAO,'DD/MM/YYYY HH24:MI') NGAY_VAO_VIEN,
       HO_TEN TENBENHNHAN,
       kcb.ma_bn MABENHNHAN,
       KT.TEN_THUOC||'(' ||KT.MA_THUOC|| ')'CONGKHAM,
       KT.MA_BAC_SI,
       nv.ten_nhanvien BACSI,
       CHUNGCHI_HANHNGHE CHUNGCHIHANHNGHE,
       kt.SOVAOVIEN_NOI SOVAOVIEN,
       kt.DON_GIA,
       pb.TEN_PHONGBAN || ' (' || MA_KHOA_CB || ')'  MAKHOA,
       kt.TT_THAU_B2 QUYETDINH,
       kt.MA_GOI_THAU_B2 GOITHAU,
       kt.MA_NHOM_THAU_B2 NHOMTHAU,
       kt.MABAOCAO_THUOC MABAOCAO,
       MADUONGDUNG_DMDC MADUONGDUNG,
       kt.SO_DANGKY_DMDC SODANGKY,
       kt.PHANTRAM_BHXH_TT TYLE_TT,
       TO_CHAR(TO_DATE(kt.NGAY_YL_CV130,'YYYYMMDDHH24MI'),'DD/MM/YYYY HH24:MI') NGAYYLENH,
       CASE WHEN MA_LOAIKCB = 2 THEN TO_CHAR(TO_DATE(kt.NGAY_YL_CV130,'YYYYMMDDHH24MI'),'DD/MM/YYYY HH24:MI')
            ELSE TO_CHAR(TO_DATE(kt.NGAY_TH_YL_XML2,'YYYYMMDDHH24MI'),'DD/MM/YYYY HH24:MI') END NGAYTHYLENH,
       kt.SO_LUONG SOLUONG,
       kt.STT_TOATHUOC_NOI STT_TOATHUOC,
       kt.STT_TOATHUOC_NOI STT_TOATHUOC_NGOAI,
       kcb.SOVAOVIEN_DT_NOI,
       kt.lieu_dung,
       case when TRUNC(NGAYGIO_RA,'MI') < TO_DATE(KT.NGAY_YL_CV130, 'YYYYMMDDHH24MI')
                then 'Y lệnh sau thời gian ra viện'
            when TRUNC(NGAYGIO_VAO,'MI') > TO_DATE(KT.NGAY_YL_CV130, 'YYYYMMDDHH24MI')
                then 'Y lệnh sau thời gian vào viện'
            when kt.NGAY_TH_YL_XML2 is not null and
                 TO_DATE(kt.NGAY_TH_YL_XML2,'YYYYMMDDHH24MI') < TO_DATE(KT.NGAY_YL_CV130, 'YYYYMMDDHH24MI')
                then 'Y thực hiện y lệnh nhỏ hơn thời gian y lệnh'
            when TRUNC(NGAYGIO_RA,'MI') < TO_DATE(KT.NGAY_TH_YL_XML2, 'YYYYMMDDHH24MI') and  kt.NGAY_TH_YL_XML2 is not null
                then 'Thời gian thực hiện lệnh sau thời gian ra viện'
            when CMU_KTRAYLENH_TRUOCCK(KCB.dvtt,kcb.SOVAOVIEN_NOI, kcb.SOVAOVIEN_DT_NOI, KT.NGAY_YL_CV130)
                IS NOT NULL
                then 'Y lệnh thuốc trước thời gian khám: '|| CMU_KTRAYLENH_TRUOCCK(KCB.dvtt,kcb.SOVAOVIEN_NOI, kcb.SOVAOVIEN_DT_NOI, KT.NGAY_YL_CV130)
            when kt.NGAY_TH_YL_XML2 is null then 'Ngày thực hiện y lệnh trống'
            else 'OK'  end LOI
from HIS_SYNCHRONIZATION.B1_CHITIEUTONGHOP_KCB kcb
         join HIS_SYNCHRONIZATION.B2_CHITIEUCHITIETTHUOC KT ON KCB.DVTT = KT.DVTT AND  kcb.ma_bn = kt.ma_bn
    AND KT.SOVAOVIEN_NOI =kcb.SOVAOVIEN_NOI
    and kt.SOVAOVIEN_DT_NOI = kcb.SOVAOVIEN_DT_NOI
    and kt.nghiep_vu in ('noitru_toathuoc', 'ba_ngoaitru_toathuoc') and KT.TU_THANH_TOAN = 0
         left join HIS_FW.DM_PHONGBAN pb on KT.MA_KHOA = pb.MA_PHONGBAN
         join HIS_FW.DM_NHANVIEN nv on kt.ma_bac_si = nv.ma_nhanvien
where kcb.dvtt =   KT.dvtt
  and NGAY_QUYET_TOAN_BH BETWEEN v_tungay AND v_denngay

  AND TI_LE_MIEN_GIAM_THE > 0 AND KCB.DVTT = p_dvtt
  and MA_LOAIKCB != 1
                    and (p_khoa = '-1' or kcb.B_MAKHOAHOANTATKHAM = p_khoa)
order by NGAY_QUYET_TOAN_BH, nv.ten_nhanvien;
elsif p_hinhthuc = 1 then --Ngoại trú
            open cur for
select distinct a.* from (
                             SELECT TO_CHAR(NGAYGIO_RA,'DD/MM/YYYY HH24:MI') NGAY_RA_VIEN,
                                    TO_CHAR(NGAYGIO_VAO,'DD/MM/YYYY HH24:MI') NGAY_VAO_VIEN,
                                    HO_TEN TENBENHNHAN,
                                    kcb.ma_bn MABENHNHAN,
                                    KT.TEN_THUOC||'(' ||KT.MA_THUOC|| ')'CONGKHAM,
                                    KT.MA_BAC_SI,
                                    nv.ten_nhanvien BACSI,
                                    CHUNGCHI_HANHNGHE CHUNGCHIHANHNGHE,
                                    kt.SOVAOVIEN,
                                    kt.DON_GIA,
                                    pb.TEN_PHONG_BENH || ' (' || KT.MA_KHOA || ')'  MAKHOA,
                                    KT.TT_THAU_B2 QUYETDINH,
                                    KT.MA_GOI_THAU_B2 GOITHAU,
                                    KT.MA_NHOM_THAU_B2 NHOMTHAU,
                                    KT.MABAOCAO_THUOC MABAOCAO,
                                    KT.MADUONGDUNG_DMDC MADUONGDUNG,
                                    KT.SO_DANGKY_DMDC SODANGKY,
                                    KT.PHANTRAM_BHXH_TT TYLE_TT,
                                    case when p_dvtt = '96155' then
                                            TO_CHAR(TO_DATE(KT.NGAY_YL_CV130,'YYYYMMDDHH24MI'),'DD/MM/YYYY HH24:MI')|| ':' || TO_CHAR(KT.NGAYGIO_YLENH,'SS')
                                         else
                                             TO_CHAR(TO_DATE(KT.NGAY_YL_CV130,'YYYYMMDDHH24MI'),'DD/MM/YYYY HH24:MI')
                                        end as NGAYYLENH,
                                    TO_CHAR(TO_DATE(kt.NGAY_YL_CV130,'YYYYMMDDHH24MI'),'DD/MM/YYYY HH24:MI') NGAYTHYLENH,
                                    KT.SO_LUONG SOLUONG,
                                    KT.STT_TOATHUOC_NGOAI STT_TOATHUOC,
                                    KT.STT_TOATHUOC_NGOAI,
                                    0 SOVAOVIEN_DT_NOI,
                                    kt.lieu_dung,
                                    case when to_number(to_char(NGAYGIO_RA,'YYYYMMDDHH24MI')) - to_number(KT.NGAY_YL_CV130) < 0
                                             then 'Y lệnh sau thời gian hoàn tất khám'
                                         when  DVKT.MA_BAC_SI = KT.MA_BAC_SI
                                             AND DVKT.LOAI_KYTHUAT IN ('CONGKHAM','CONGKHAMCHUYENDEN')
                                             and to_number(KT.NGAY_YL_CV130) < to_number(dvkt.NGAY_YL_CV130)
                                             then 'Y lệnh thuốc trước thời gian khám: '|| to_char(to_date(dvkt.NGAY_YL_CV130, 'YYYYMMDDHH24MI'),'DD/MM/YYYY HH24:MI:SS')
                                         when DVKT.MA_BAC_SI_CD = KT.MA_BAC_SI and DVKT.LOAI_KYTHUAT NOT IN ('CONGKHAM','CONGKHAMCHUYENDEN')
                                             and DVKT.PHANTRAM_BHXH_TT_BYT > 0 AND TO_DATE(KT.NGAY_YL_CV130,'YYYYMMDDHH24MI') < MAX(TRUNC(DVKT.NGAYGIO_KETQUA, 'MI')) OVER (PARTITION BY kcb.sovaovien,DVKT.MA_BAC_SI_CD) then
                                                 'Y lệnh thuốc trước thời gian kết quả cận lâm sàng:' || to_char(MAX(DVKT.NGAYGIO_KETQUA) OVER (PARTITION BY kcb.sovaovien,DVKT.MA_BAC_SI_CD),'DD/MM/YYYY HH24:MI:SS')
                                         else 'OK'  end LOI
                             from HIS_SYNCHRONIZATION.B1_CHITIEUTONGHOP_KCB kcb
                                      join HIS_SYNCHRONIZATION.B2_CHITIEUCHITIETTHUOC KT ON KCB.DVTT = KT.DVTT AND
                                                                                            KCB.SOVAOVIEN = KT.SOVAOVIEN and kcb.ma_bn = kt.ma_bn
                                      left join HIS_SYNCHRONIZATION.B3_CHITIEUCHITIET_KYTHUATVATTU DVKT ON KCB.DVTT = DVKT.DVTT AND
                                                                                                           KCB.SOVAOVIEN = DVKT.SOVAOVIEN and kcb.ma_bn = DVKT.ma_bn
                                 AND DVKT.TU_THANH_TOAN = 0
                                      left join HIS_MANAGER.DM_PHONG_BENH pb on KT.MA_PHONG_BENH = pb.MA_PHONG_BENH
                                      join HIS_FW.DM_NHANVIEN nv on kt.ma_bac_si = nv.ma_nhanvien
                             where kcb.dvtt =   KT.dvtt
                               and NGAY_QUYET_TOAN_BH BETWEEN v_tungay AND v_denngay
                               AND TRANG_THAI_BN_TN != 7
                    and TRANG_THAI_BN_TN >= 3
                    AND TI_LE_MIEN_GIAM_THE > 0
                    AND KCB.DVTT = p_dvtt
                    and MA_LOAIKCB = 1
                    and (p_khoa = '-1' or kcb.B_MAKHOAHOANTATKHAM = p_khoa)
                             order by LOI, NGAY_QUYET_TOAN_BH, nv.ten_nhanvien
                         ) a
order by LOI, NGAY_RA_VIEN, BACSI;
end if;
return cur;
END
;
