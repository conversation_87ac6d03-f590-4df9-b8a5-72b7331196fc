<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rp_phieu_kiemtra_benhan" language="groovy" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" whenResourceMissingType="Empty" uuid="0c77309d-bb14-4cd7-8eb5-4a8b75b128de">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="s_center" hAlign="Center"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#FFBFBF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_B_L">
		<box>
			<leftPen lineWidth="1.0"/>
			<rightPen lineWidth="0.0"/>
		</box>
	</style>
	<style name="table_B_R">
		<box>
			<leftPen lineWidth="0.0"/>
			<rightPen lineWidth="1.0"/>
		</box>
	</style>
	<style name="table_B_L_R">
		<box>
			<leftPen lineWidth="0.5"/>
			<rightPen lineWidth="0.5"/>
		</box>
	</style>
	<subDataset name="BANG_DANH_GIA" uuid="c5f254b6-7036-4d22-b499-2aa57eb51231">
		<parameter name="ID_PHIEU" class="java.lang.String"/>
		<queryString language="plsql">
			<![CDATA[{call HIS_MANAGER.CMU_KTBA_BANG_DANH_GIA_P($P{ID_PHIEU}, $P{ORACLE_REF_CURSOR})}]]>
		</queryString>
		<field name="STT" class="java.lang.String"/>
		<field name="NOI_DUNG" class="java.lang.String"/>
		<field name="DIEM_CHUAN" class="java.lang.String"/>
		<field name="DIEM_DAT" class="java.lang.String"/>
		<field name="GHI_CHU" class="java.lang.String"/>
		<field name="KEY" class="java.lang.String"/>
	</subDataset>
	<parameter name="tenkhoa" class="java.lang.String"/>
	<parameter name="soyte" class="java.lang.String"/>
	<parameter name="benhvien" class="java.lang.String"/>
	<parameter name="dvtt" class="java.lang.String"/>
	<parameter name="ID_PHIEU" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString language="plsql">
		<![CDATA[{call HIS_MANAGER.CMU_PHIEU_KIEMTRA_BENHAN_P($P{ID_PHIEU}, $P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="DD_TAO_PHIEU" class="java.lang.String"/>
	<field name="MM_TAO_PHIEU" class="java.lang.String"/>
	<field name="YY_TAO_PHIEU" class="java.lang.String"/>
	<field name="HH_TAO_PHIEU" class="java.lang.String"/>
	<field name="MI_TAO_PHIEU" class="java.lang.String"/>
	<field name="NGUOI_KIEM_TRA" class="java.lang.String"/>
	<field name="KHOA" class="java.lang.String"/>
	<field name="HO_TEN_NGUOI_BENH" class="java.lang.String"/>
	<field name="TUOI" class="java.lang.String"/>
	<field name="GIOI_TINH" class="java.lang.String"/>
	<field name="SO_THE_BHYT" class="java.lang.String"/>
	<field name="SO_BA" class="java.lang.String"/>
	<field name="MA_BENH" class="java.lang.String"/>
	<field name="NGAY_VAO_VIEN" class="java.lang.String"/>
	<field name="NGAY_RA" class="java.lang.String"/>
	<field name="TS_NGAY_DIEU_TRI" class="java.lang.String"/>
	<field name="BS_DIEU_TRI" class="java.lang.String"/>
	<group name="rp_phieu_kiemtra_benhan">
		<groupExpression><![CDATA[$P{ID_PHIEU}]]></groupExpression>
		<groupHeader>
			<band height="350" splitType="Immediate">
				<textField>
					<reportElement x="405" y="10" width="150" height="20" uuid="89172837-90e2-4098-bfda-de121221547b"/>
					<textElement verticalAlignment="Middle" markup="styled">
						<font fontName="Times New Roman" size="12" isBold="false" pdfEncoding="Identity-H"/>
					</textElement>
					<textFieldExpression><![CDATA["Phụ lục 01 " + "<b>(BM. 01. ΚΗΤΗ)</b>"]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="0" y="30" width="555" height="55" uuid="18bb9ae9-d959-437d-9e6b-17720fa97551"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="14" isBold="true"/>
					</textElement>
					<text><![CDATA[PHIẾU KIỂM TRA BỆNH ÁN]]></text>
				</staticText>
				<frame>
					<reportElement x="0" y="90" width="555" height="95" uuid="94b25fa3-a150-46fc-b96e-d411a0775aa4"/>
					<textField>
						<reportElement x="0" y="0" width="555" height="15" uuid="8678cd79-13ae-4d83-826c-5ac74db1fcec"/>
						<box leftPadding="25" rightPadding="25"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Khoa: " + (
$F{KHOA} == null ? 
"" : 
$F{KHOA}
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="0" y="20" width="355" height="15" uuid="21052076-627d-4c4b-b367-901ffcd57d0d"/>
						<box leftPadding="25"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Họ và tên người bệnh: " + (
$F{HO_TEN_NGUOI_BENH} == null ? 
"" : 
$F{HO_TEN_NGUOI_BENH}
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="355" y="20" width="80" height="15" uuid="202448d4-f91e-4a53-abd7-1f3efdfa9523"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Tuổi: " + (
$F{TUOI} == null ? 
"" : 
$F{TUOI}
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="435" y="20" width="120" height="15" uuid="f725548f-cbd2-4eee-8037-0dcaf0fb89a0"/>
						<box rightPadding="25"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Giới tính: " + (
$F{GIOI_TINH} == null ? 
"" : 
$F{GIOI_TINH}
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="405" y="40" width="150" height="15" uuid="0de524c2-c333-43c3-b6bc-93c57e9c755e"/>
						<box rightPadding="25"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Mã bệnh: " + (
$F{MA_BENH} == null ? 
"" : 
$F{MA_BENH}
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="255" y="40" width="150" height="15" uuid="7a694391-270e-4ebe-aab3-7b7ff06c86d9"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Số B/A: " + (
$F{SO_BA} == null ? 
"" : 
$F{SO_BA}
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="0" y="40" width="255" height="15" uuid="f1478ca1-4e81-41d6-83f2-93f4fdce2b70"/>
						<box leftPadding="25"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Số thẻ BHYT: " + (
$F{SO_THE_BHYT}== null ? 
"" : 
$F{SO_THE_BHYT}
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="200" y="60" width="175" height="15" uuid="f53aaed9-b801-400a-b91d-43a3a3398c01"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Ngày ra: " + (
$F{NGAY_RA} == null ? 
"" : 
$F{NGAY_RA}
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="0" y="60" width="200" height="15" uuid="de6a42fe-0f90-4585-833c-56452c629dc4"/>
						<box leftPadding="25"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Ngày vào viện: " + (
$F{NGAY_VAO_VIEN} == null ? 
"" : 
$F{NGAY_VAO_VIEN}
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="375" y="60" width="180" height="15" uuid="8a50cdc9-dd27-4d9e-bc47-8c82b69e8096"/>
						<box rightPadding="25"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Tổng số ngày điều trị: " + (
$F{TS_NGAY_DIEU_TRI} == null ? 
"" : 
$F{TS_NGAY_DIEU_TRI}
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="0" y="80" width="555" height="15" uuid="a2f5ed5e-3e51-44c3-9bdf-e788f5e7a70d"/>
						<box leftPadding="25" rightPadding="25"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Bác sĩ điều trị: " + (
$F{BS_DIEU_TRI} == null ? 
"" : 
$F{BS_DIEU_TRI}
)]]></textFieldExpression>
					</textField>
				</frame>
				<componentElement>
					<reportElement key="table" style="table" positionType="Float" x="0" y="190" width="555" height="50" uuid="f63ab573-5f09-4049-ac23-29a520e6ec7d"/>
					<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
						<datasetRun subDataset="BANG_DANH_GIA" uuid="ca2ef91f-7df9-40f0-848a-999abeec77ad">
							<datasetParameter name="REPORT_CONTEXT">
								<datasetParameterExpression><![CDATA[$P{REPORT_CONTEXT}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="REPORT_PARAMETERS_MAP">
								<datasetParameterExpression><![CDATA[$P{REPORT_PARAMETERS_MAP}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ID_PHIEU">
								<datasetParameterExpression><![CDATA[$P{ID_PHIEU}]]></datasetParameterExpression>
							</datasetParameter>
							<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
						</datasetRun>
						<jr:column width="55" uuid="4a22c45e-f841-49ce-9ba1-43a46b8b9075">
							<jr:columnHeader height="45" rowSpan="1">
								<staticText>
									<reportElement x="0" y="0" width="55" height="45" uuid="5286027e-526e-4e20-a783-089bad87373c"/>
									<box topPadding="3"/>
									<textElement textAlignment="Center" verticalAlignment="Top">
										<font fontName="Times New Roman" size="12" isBold="true"/>
									</textElement>
									<text><![CDATA[STT]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table_B_R" height="20" rowSpan="1">
								<frame>
									<reportElement x="0" y="0" width="55" height="20" uuid="f093d5e9-717b-45b3-a457-34a0ba61a260">
										<printWhenExpression><![CDATA[$F{KEY}.startsWith( "BANG_KTRA_BA_R" ) && $F{KEY}.contains( "C0" )]]></printWhenExpression>
									</reportElement>
									<box>
										<topPen lineWidth="1.0"/>
									</box>
								</frame>
								<textField isBlankWhenNull="true">
									<reportElement x="0" y="0" width="55" height="20" uuid="afd44de1-3250-40dd-a52d-ede730549541"/>
									<box topPadding="3" bottomPadding="3"/>
									<textElement textAlignment="Center" verticalAlignment="Top">
										<font fontName="Times New Roman" size="12"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{STT}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="300" uuid="20d8f35e-57cc-4261-b026-85f0437e96f8">
							<jr:columnHeader height="45" rowSpan="1">
								<staticText>
									<reportElement x="0" y="0" width="300" height="45" uuid="4badac9d-ca86-488f-b352-4918fc49a482"/>
									<box topPadding="3">
										<leftPen lineWidth="1.0"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Top">
										<font fontName="Times New Roman" size="12" isBold="true"/>
									</textElement>
									<text><![CDATA[NỘI DUNG]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table_B_L_R" height="20" rowSpan="1">
								<frame>
									<reportElement x="0" y="0" width="300" height="20" uuid="5dc6c0b6-39b3-4713-94c0-3da88b27f261">
										<printWhenExpression><![CDATA[$F{KEY}.startsWith( "BANG_KTRA_BA_R" ) && $F{KEY}.contains( "C0" )]]></printWhenExpression>
									</reportElement>
									<box>
										<topPen lineWidth="1.0"/>
									</box>
								</frame>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement x="0" y="0" width="300" height="20" uuid="874e7724-7975-433b-80c3-a774290cb668"/>
									<box topPadding="3" leftPadding="3" bottomPadding="3" rightPadding="3"/>
									<textElement verticalAlignment="Top">
										<font fontName="Times New Roman" size="12"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{NOI_DUNG}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="60" uuid="5080e33c-e614-43bd-a558-e37161c256b5">
							<jr:columnHeader height="45" rowSpan="1">
								<staticText>
									<reportElement x="0" y="0" width="60" height="45" uuid="d885f719-f564-4624-82e3-36ea39b72a81"/>
									<box topPadding="3">
										<leftPen lineWidth="1.0"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Top">
										<font fontName="Times New Roman" size="12" isBold="true"/>
									</textElement>
									<text><![CDATA[ĐIỂM
CHUẨN]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table_B_L_R" height="20" rowSpan="1">
								<frame>
									<reportElement x="0" y="0" width="60" height="20" uuid="f38043c2-6080-4602-a719-8dde5a70ea75">
										<printWhenExpression><![CDATA[$F{KEY}.startsWith( "BANG_KTRA_BA_R" ) && $F{KEY}.contains( "C0" )]]></printWhenExpression>
									</reportElement>
									<box>
										<topPen lineWidth="1.0"/>
									</box>
								</frame>
								<textField isBlankWhenNull="true">
									<reportElement x="0" y="0" width="60" height="20" uuid="8889fa29-d9cf-4c6d-ae7e-e48cc6ca9eb3"/>
									<box topPadding="3" bottomPadding="3"/>
									<textElement textAlignment="Center" verticalAlignment="Top">
										<font fontName="Times New Roman" size="12"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{DIEM_CHUAN}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="60" uuid="90637028-2aca-4b56-825c-f811434e2e7c">
							<jr:columnHeader height="45" rowSpan="1">
								<staticText>
									<reportElement x="0" y="0" width="60" height="45" uuid="d0a463e0-f9ba-4bf0-846e-ea7eebadf73b"/>
									<box topPadding="3">
										<leftPen lineWidth="1.0"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Top">
										<font fontName="Times New Roman" size="12" isBold="true"/>
									</textElement>
									<text><![CDATA[ĐIỂM
ĐẠT]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table_B_L_R" height="20" rowSpan="1">
								<frame>
									<reportElement x="0" y="0" width="60" height="20" uuid="e5ed22f1-702f-4d70-b618-47f0e5ee554c">
										<printWhenExpression><![CDATA[$F{KEY}.startsWith( "BANG_KTRA_BA_R" ) && $F{KEY}.contains( "C0" )]]></printWhenExpression>
									</reportElement>
									<box>
										<topPen lineWidth="1.0"/>
									</box>
								</frame>
								<textField isBlankWhenNull="true">
									<reportElement x="0" y="0" width="60" height="20" uuid="c462465d-7f7b-4094-b684-16594fcd22e0"/>
									<box topPadding="3" bottomPadding="3"/>
									<textElement textAlignment="Center" verticalAlignment="Top">
										<font fontName="Times New Roman" size="12"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{DIEM_DAT}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="80" uuid="41192a2f-6347-4dc4-a35a-5b9b526c34f8">
							<jr:columnHeader height="45" rowSpan="1">
								<staticText>
									<reportElement x="0" y="0" width="80" height="45" uuid="cffb4dd3-f993-40fd-9d20-a819f6e24cc2"/>
									<box topPadding="3">
										<leftPen lineWidth="1.0"/>
									</box>
									<textElement textAlignment="Center" verticalAlignment="Top">
										<font fontName="Times New Roman" size="12" isBold="true"/>
									</textElement>
									<text><![CDATA[GHI
CHÚ]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table_B_L" height="20" rowSpan="1">
								<frame>
									<reportElement x="0" y="0" width="80" height="20" uuid="d0d72a8b-dcaf-4ca1-a838-b72a1aeed971">
										<printWhenExpression><![CDATA[$F{KEY}.startsWith( "BANG_KTRA_BA_R" ) && $F{KEY}.contains( "C0" )]]></printWhenExpression>
									</reportElement>
									<box>
										<topPen lineWidth="1.0"/>
									</box>
								</frame>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement x="0" y="0" width="80" height="20" uuid="26f38141-4a6d-4c72-9d0c-cbbe26aba5c8"/>
									<box topPadding="3" leftPadding="3" bottomPadding="3" rightPadding="3"/>
									<textElement verticalAlignment="Top">
										<font fontName="Times New Roman" size="12"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{GHI_CHU}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
					</jr:table>
				</componentElement>
				<frame>
					<reportElement positionType="Float" x="0" y="250" width="555" height="100" uuid="d1993f0c-8695-4153-b7ac-8f68648ed8ba"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<frame>
						<reportElement x="0" y="0" width="280" height="100" uuid="ece8d9ac-ccaf-4cda-94ff-ddb3af006988"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
						<staticText>
							<reportElement x="0" y="0" width="280" height="15" uuid="40bbc277-d371-49f3-9b57-aac7081e4122"/>
							<textElement verticalAlignment="Middle" markup="styled">
								<font fontName="Times New Roman" size="12" isBold="true" isItalic="false"/>
							</textElement>
							<text><![CDATA[Nhận xét:]]></text>
						</staticText>
						<staticText>
							<reportElement x="0" y="15" width="280" height="15" uuid="c8d0fd89-a6b0-4e1e-abc7-34dc89b231c3"/>
							<textElement verticalAlignment="Middle" markup="styled">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="false"/>
							</textElement>
							<text><![CDATA[Xếp loại:]]></text>
						</staticText>
						<staticText>
							<reportElement x="0" y="30" width="140" height="15" uuid="f14d2c37-d5a8-44f7-8d24-cf86c0f05382"/>
							<textElement verticalAlignment="Middle" markup="styled">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="false"/>
							</textElement>
							<text><![CDATA[Tốt: 9-10 điểm]]></text>
						</staticText>
						<staticText>
							<reportElement x="140" y="45" width="140" height="15" uuid="fc78cb15-a86d-43c0-afb5-2dc5dff7e31f"/>
							<textElement verticalAlignment="Middle" markup="styled">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="false"/>
							</textElement>
							<text><![CDATA[Kém: < 7 điểm]]></text>
						</staticText>
						<staticText>
							<reportElement x="0" y="45" width="140" height="15" uuid="05616760-6314-4aad-8996-6ef26eadeea1"/>
							<textElement verticalAlignment="Middle" markup="styled">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="false"/>
							</textElement>
							<text><![CDATA[Khá: 8-8.9 điểm]]></text>
						</staticText>
						<staticText>
							<reportElement x="140" y="30" width="140" height="15" uuid="156a8ac2-6aa7-4cbb-b917-973d76293e39"/>
							<textElement verticalAlignment="Middle" markup="styled">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="false"/>
							</textElement>
							<text><![CDATA[TB: 7-7.9 điểm]]></text>
						</staticText>
					</frame>
					<frame>
						<reportElement x="280" y="0" width="275" height="100" uuid="651072f6-7eef-4a71-a346-18aaca78ea51"/>
						<textField isBlankWhenNull="true">
							<reportElement x="0" y="85" width="275" height="15" uuid="fa1396ea-caf0-48d1-9a75-ff4c3d47b604"/>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{NGUOI_KIEM_TRA}]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement x="90" y="45" width="45" height="9" forecolor="#FFFFFF" uuid="04ba19b2-771a-458b-8484-59fbefedb7c6"/>
							<textElement textAlignment="Justified">
								<font fontName="Times New Roman" size="6" isBold="false"/>
							</textElement>
							<text><![CDATA[SIGNATURE_1]]></text>
						</staticText>
						<staticText>
							<reportElement x="0" y="15" width="275" height="15" uuid="447932e2-030a-4a1d-9093-d6d2aefb998b"/>
							<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
								<font fontName="Times New Roman" size="12" isBold="true" isItalic="false"/>
							</textElement>
							<text><![CDATA[Người kiểm tra (Trưởng, phó khoa)]]></text>
						</staticText>
						<textField>
							<reportElement x="0" y="0" width="275" height="15" uuid="8a349de5-8ee1-4446-b9e8-1da307a7ef1f"/>
							<box leftPadding="0"/>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["Ngày " + (
$F{DD_TAO_PHIEU} == null ? 
"......" : 
$F{DD_TAO_PHIEU}) + 
" tháng " + (
$F{MM_TAO_PHIEU} == null ? 
"......" : 
$F{MM_TAO_PHIEU}) + 
" năm " + (
$F{YY_TAO_PHIEU} == null ? 
"20......" : 
$F{YY_TAO_PHIEU})]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement x="0" y="30" width="275" height="15" uuid="63bdcc36-520d-41cd-bdef-a2bba5d6c28c"/>
							<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="false"/>
							</textElement>
							<text><![CDATA[(Ký và ghi rõ họ tên)]]></text>
						</staticText>
					</frame>
				</frame>
			</band>
		</groupHeader>
	</group>
</jasperReport>
