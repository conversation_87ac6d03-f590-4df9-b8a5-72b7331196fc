create or replace FUNCTION    HIS_MANAGER."CMU_PHATHUOC_INS_V4" (
    p_dvtt             IN                 VARCHAR2,
    p_id_dieutri       IN                 VARCHAR2,
    p_stt_benhan       IN                 VARCHAR2,
    p_stt_dotdieutri   IN                 VARCHAR2,
    p_stt_dieutri      IN                 VARCHAR2,
    p_sovaovien        IN                 VARCHAR2,
    p_tenth<PERSON>c<PERSON>      IN                 VARCHAR2,
    p_g<PERSON>hu           IN                 VARCHAR2,
    p_solo             IN                 VARCHAR2,
    p_soluongphatong   IN                 VARCHAR2,
    p_bacsi            IN                 VARCHAR2,
    p_ngaypha          IN                 VARCHAR2,
    p_ngayylenh        IN                 VARCHAR2,
    p_loaitocdo        IN                 VARCHAR2,
    p_tocdo            IN                 VARCHAR2,
    p_soluong<PERSON>uyen    IN                 VARCHAR2,
    p_lieudung         IN                 VARCHAR2,
    p_ylenh            IN                 VARCHAR2,
    p_dvtpha           IN                 VARCHAR2
) RETURN NUMBER IS
    v_id NUMBER := 0;
BEGIN
INSERT INTO cmu_phathuoc (
    dvtt,
    id_dieutri,
    stt_benhan,
    stt_dotdieutri,
    stt_dieutri,
    sovaovien,
    tenth<PERSON><PERSON><PERSON>,
    g<PERSON><PERSON>,
    solo_tong,
    soluongpha_tong,
    ngaygio_ylenh,
    bacsiphathuoc,
    loai_ylenh,
    loai_tocdo,
    tocdo,
    soluongtruyen,
    lieudung,
    dvt_phathuoc
) VALUES (
             p_dvtt,
             p_id_dieutri,
             p_stt_benhan,
             p_stt_dotdieutri,
             p_stt_dieutri,
             p_sovaovien,
             p_tenthuocpha,
             p_ghichu,
             p_solo,
             p_soluongphatong,
             TO_DATE(p_ngayylenh, 'dd/mm/yyyy hh24:mi:ss'),
             p_bacsi,
             p_ylenh,
             p_loaitocdo,
             p_tocdo,
             p_soluongtruyen,
             p_lieudung,
             p_dvtpha
         ) RETURNING id_phathuoc INTO v_id;

RETURN v_id;
END;