<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="phieugaymehoisuc" language="groovy" pageWidth="249" pageHeight="842" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="249" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="5f982810-2f9d-40df-b4a6-deec4a399123">
	<property name="ireport.zoom" value="2.1961500000000016"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="dvtt" class="java.lang.String"/>
	<parameter name="idphieu" class="java.lang.String"/>
	<parameter name="page" class="java.lang.String"/>
	<parameter name="lastpage" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call CMU_LCSC1_KYHIEU_PRO_V2($P{dvtt}, $P{idphieu}, $P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="KY_HIEU" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="43">
			<image scaleImage="FillFrame">
				<reportElement x="0" y="2" width="50" height="11" uuid="d113e4f6-ca49-4437-a800-ebf871ea98db"/>
				<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.getInstance(new SimpleJasperReportsContext()).loadAwtImageFromBytes(Base64.getDecoder().decode("iVBORw0KGgoAAAANSUhEUgAAAEIAAAANCAIAAACSOC8GAAAACXBIWXMAABJ0AAASdAHeZh94AAAA20lEQVRIiWM8c+0Ww9AHTAPtAOoAFjjr2bX3h+fdxFRhm6QupSVIRydhB4fn3nh2/QOaoJSmgG2yBgOyN768/XHr0AtM/Yb+8jR1H5Hg2fUPWJ0HASy4JODgy5uf7x59paqTyAG/vv3BKghxG2FvPDr/5sub79R3F4ng85sfWAWv7HzEMAyzOC4gZyiiYCxKB6fgBw/Pv3374AuaIK8Ih467HAMx3uARYReS46aJ00gBbFxYnMrGxQJxG0KOR5hDzU4CUymPMAftHEc8kNIUwCPIOFqLDyIwTLwBALlbQTBrxPwbAAAAAElFTkSuQmCC"))]]></imageExpression>
			</image>
			<textField isBlankWhenNull="true">
				<reportElement x="50" y="0" width="83" height="15" uuid="3c5b709a-306e-4601-8619-c39feee33556"/>
				<box leftPadding="5">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Nhiệt độ"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="183" y="0" width="66" height="15" uuid="8066955b-8b60-4df9-8239-fd29c2dd213b"/>
				<box leftPadding="5">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Mạch"]]></textFieldExpression>
			</textField>
			<image scaleImage="FillFrame">
				<reportElement x="133" y="2" width="50" height="11" uuid="c60805eb-c66a-4d9c-8244-828b43967ed3"/>
				<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.getInstance(new SimpleJasperReportsContext()).loadAwtImageFromBytes(Base64.getDecoder().decode("iVBORw0KGgoAAAANSUhEUgAAAEgAAAAPCAIAAADI0h7EAAAACXBIWXMAABJ0AAASdAHeZh94AAACgElEQVRIidWWzU8TQRjG53O37cKCFbotEoJI0NoSSOCiF73on+rRP0AvevGEiUYKpmgx2paW1qW73Y/O7HbGQ03tLhSIkQq/4ztPJs+TN/O+A3f2yuAGQjx34eULb2m5u1Zk2dwZgnhBCCj6KAhgGEAJJASSUEGpRBggNAnL5yMEDAIccNK1qNkGS8uSYCDEaW/xYJgz9ah26/271LcK8ZwwNeUtr5xsPWa5O/1EclL2x6J0zKn9Xb30IVX/gbsWAABAZBc2eHoupowEI56rffk89+aV0moS34VhSGyLWJ1EvdZ++txZfdBPaRPLEEcIpWPefvtaL5eoaeKeB6RM1r4Tz6Ht459PnvHZ9GjfIsGU5pG+91GrlKEQgwrkXOGcdi0+nwmmZ7y7qxMNMwIMgqn9Xb1cStSrQMpBEfse7vkAQpbJnWw/kqo61EeCJRo17evBMNWfS4XQKgcskxXa9FUHGAf23ZlPO9Q0h6l+IyU1Tb30wd7YCscFo11LaTfPvFdtNfX9Xcj4FXi+FJgz7bBCmDcSSwIAAQCI+clGNdaPazDo/h54zlmkY6E+y+cMtVk/rWPzhp0v2uvb/9jbpcG+qzaq2mEF+27sSKhJP7sooxM/Esw3FtyVVaXViLVVIuTeW3NW7vcyxhX5vhDImLW+rVgdXPcizwzCIJ22C5t9qozqI8GYkbMLm0rreDjuJSFhUuPzhv1wgxtnLPiJISl18kX1+AgAQE0TMR8AINRkkE7bawUnX5SUjuph7EuFe/61XtBme7Cgk40qAMDPLtqFTSdfPL2g48Fuypdq8FgkQn2qSEov/lIBhCRCfULj9WsCQlJVR/fVWOEEzPwXfgGgmj5H8DHRYwAAAABJRU5ErkJggg=="))]]></imageExpression>
			</image>
			<image scaleImage="FillFrame" hAlign="Right">
				<reportElement x="0" y="15" width="50" height="11" uuid="bf5e1aa3-4b04-445f-b7fc-71b12542fe1a"/>
				<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.getInstance(new SimpleJasperReportsContext()).loadAwtImageFromBytes(Base64.getDecoder().decode("iVBORw0KGgoAAAANSUhEUgAAAEUAAAARCAIAAAAE8PafAAAACXBIWXMAABJ0AAASdAHeZh94AAACtElEQVRIic2W30/TUBTHT7sCG6tjbEtwCooiqBgl8c/2wScT33yRGCHDsC2scxTmBis/+nPltmt7b1sfivvRDpCYdX7e7jknN9/vvfeclirVGjCKiQ3iY4aamZ9Jw38G8YhJEADMMyxDM9ECOhrqoFZdqXZQa+Lq7g/Cel2p1pUqwvrYgjEWa3K5qTeeZTbWFzcnLO/eKD3pm/AFAArJpexcLlow4sf13TPU5rWDQ5VzPXx61SyyTxJUIiaxd6HZSlP/WZcrAPC28D6fKixELI28N+zaFXFPME51R+ugk4q4h107Pr13IaB2XanqjqY7GidXOqgdrRncD/GIYsn74k7XVgBAd9Qfl99XM2sLqXxiXJvFjOM6nFzmVS5YHmn1tezL1cx6aGgN/CCsN9RqUzs0iQEAJjZ4rfb518dienkukYpT+lgUW+Q17sIUguWFKfAa9yK7GWrywcErPWn3fBv7Tj9CPNJB7Z5rxKP4dsTeeddWhyPt7nFNLofKru8naDVe5YiH+znf91RLYmczm/mtxWR+0opvgvhEQCcVqWRiNByXrEteOwgNrWs/QauFhroPvu1aonHh5/2H6eV41EexiFm5LCGsE4/0lQEFtmsFQ6uQWkow80GGBgATGy2d77daiCOt3tJ5E0/n1YWmFAAANcjqjrov7iiW3LdKA0AHtYZbLUTQedP6XRhMqeED/WPJxEZTO2yog5dFA0BNLre7x7dsOrbz4iE6pcAfKcC+s3u+rfSkYEmVao0zdKLYkkWsmzZNMsncXKHIrkxE8q0YBAl6y3CR63lBxHKNT/yH1/mt59mNFMMmaDqdYB9lnqYZFoJ5UGRXpqL1b0gz7HruzXDkyununn19/GD1Ve5dIbUUqp/+h/++0BSdmc06rmURc0w2fkH/CAX0QjJnu7Y5zs9vzE6qpQjsrXoAAAAASUVORK5CYII="))]]></imageExpression>
			</image>
			<textField isBlankWhenNull="true">
				<reportElement x="50" y="13" width="199" height="15" uuid="86fb3663-6e7c-433a-9268-6a1ac52e61a8"/>
				<box leftPadding="5">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["SpO<sub>2</sub>"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="28" width="249" height="15" uuid="f3660fa4-48e4-41ac-adf1-736933d26777"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["(+): Có\n(-) : Không\n(/) : Không ghi nhận\nBT: Bình thường"]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="15" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="249" height="15" isPrintWhenDetailOverflows="true" uuid="11803292-6af9-4ef4-8892-dedb3c3d0321"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{KY_HIEU}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
