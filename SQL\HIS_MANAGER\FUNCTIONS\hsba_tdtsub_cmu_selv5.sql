CREATE OR REPLACE 
PROCEDURE hsba_tdtsub_cmu_selv5 (
    p_dvtt             IN                 VARCHAR2,
    p_sovaovien        IN                 VARCHAR2,
    p_stt_<PERSON>han       IN                 VARCHAR2,
    p_id_dieutri       IN                 VARCHAR2,
    p_stt_dotdieutri   IN                 VARCHAR2,
    cur                OUT                SYS_REFCURSOR
) IS
    v_thamso_94300 VARCHAR2(1) := cmu_tsdv(p_dvtt, 94300, 0);
    v_thamso960615 varchar2(5):= HIS_MANAGER.DM_TSDV_SL_MTSO(p_dvtt, '960615');
		v_thamso960616 					 number(10) := cmu_tsdv(p_dvtt, 960616, 0);
BEGIN
OPEN cur FOR SELECT
                     a.*, v_thamso960616 ANCHUKY
                 FROM
                     (
                         SELECT
                             'Xét nghiệm' loai,
                             nvl(xn.ten_hien_thi, xn.ten_xetnghiem)
                             || DECODE(ct.ghichu_tenxn, NULL, '', '['
                                                                  || ct.ghichu_tenxn
                                                                  || ']')
                             || '['
                             || TO_CHAR(ct.ngay_chi_dinh_ct, 'HH24:MI')
                             || ']' y_lenh,
                             ct.so_luong        soluong,
                             xn.dvt_xetnghiem   dvt,
                             1 stt_order,
                             NULL duong_dung,
                             NULL stt,
                             NULL ngayks,
                             NULL ngaycort,
                             NULL ngayht,
                             0 stt_th
                         FROM
                             noitru_cd_xet_nghiem_ct   ct
                             JOIN cls_xetnghiem             xn ON ct.ma_xet_nghiem = xn.ma_xetnghiem
                                                      AND ct.dvtt = xn.dvtt
                                                      AND xn.cap_xn = 1
                         WHERE
                             ct.dvtt = p_dvtt
                             AND ct.stt_benhan = p_stt_benhan
                             AND ct.stt_dotdieutri = p_stt_dotdieutri
                             AND ct.sovaovien = p_sovaovien
                             AND ct.id_dieutri = p_id_dieutri
                         UNION ALL
                         SELECT
                             'Chẩn đoán hình ảnh' loai,
                             nvl(xn.ten_hien_thi, xn.ten_cdha)
                             || DECODE(ct.ghichu_tencdha, NULL, '', '['
                                                                    || ct.ghichu_tencdha
                                                                    || ']')
                             || '['
                             || TO_CHAR(ct.ngay_chi_dinh_ct, 'HH24:MI')
                             || ']' y_lenh,
                             ct.so_luong,
                             xn.dvt_cdha dvt,
                             2 stt_order,
                             NULL duong_dung,
                             NULL stt,
                             NULL ngayks,
                             NULL ngaycort,
                             NULL ngayht,
                             0 stt_th
                         FROM
                             noitru_cd_cdha_chi_tiet   ct
                             JOIN cls_cdha                  xn ON xn.ma_cdha = ct.ma_cdha
                                                 AND ct.dvtt = xn.dvtt
                         WHERE
                             ct.dvtt = p_dvtt
                             AND ct.stt_benhan = p_stt_benhan
                             AND ct.stt_dotdieutri = p_stt_dotdieutri
                             AND ct.sovaovien = p_sovaovien
                             AND ct.id_dieutri = p_id_dieutri
                         UNION ALL
                         SELECT
                             'Thở Máy' loai,
                             'Thở máy: '
                             || tho_may
                             || '<br/>'
                             || 'Chế độ: '
                             || thomay_chedo
                             || '<br/>'
                             || 'Tần số: '
                             || thomay_tanso
                             || ' l/P'
                             || '<br/>'
                             || 'I/E: '
                             || thomay_ie
                             || '<br/>'
                             || 'FiO2: '
                             || thomay_fio2
                             || ' %'
                             || '<br/>'
                             || 'PC/PS/PEEP: '
                             || thomay_pc_ps_peep
                             || ' cmH20'
                             || '<br/>'
                             || 'MAP: '
                             || thomay_map
                             || ' cmH20'
                             || '<br/>'
                             || 'Trigger: '
                             || thomay_phantram_trigger y_lenh,
                             NULL,
                             NULL,
                             3 stt_order,
                             NULL duong_dung,
                             NULL stt,
                             NULL ngayks,
                             NULL ngaycort,
                             NULL ngayht,
                             0 stt_th
                         FROM
                             noitru_dieutri
                         WHERE
                             dvtt = p_dvtt
                             AND stt_benhan = p_stt_benhan
                             AND stt_dotdieutri = p_stt_dotdieutri
                             AND id_dieutri = p_id_dieutri
                             AND ( tho_may IS NOT NULL
                                   OR thomay_chedo IS NOT NULL
                                   OR thomay_tanso IS NOT NULL
                                   OR thomay_ie IS NOT NULL
                                   OR thomay_fio2 IS NOT NULL
                                   OR thomay_pc_ps_peep IS NOT NULL
                                   OR thomay_map IS NOT NULL
                                   OR thomay_phantram_trigger IS NOT NULL )
                         UNION ALL
                         SELECT
                             'Thở CPAP' loai,
                             'P: '
                             || thocpap_p
                             || ' cmH20'
                             || '<br/>'
                             || 'FiO2: '
                             || thocpap_fio2
                             || ' %' y_lenh,
                             NULL,
                             NULL,
                             4 stt_order,
                             NULL duong_dung,
                             NULL stt,
                             NULL ngayks,
                             NULL ngaycort,
                             NULL ngayht,
                             0 stt_th
                         FROM
                             noitru_dieutri
                         WHERE
                             dvtt = p_dvtt
                             AND stt_benhan = p_stt_benhan
                             AND stt_dotdieutri = p_stt_dotdieutri
                             AND id_dieutri = p_id_dieutri
                             AND ( thocpap_p IS NOT NULL
                                   OR thocpap_fio2 IS NOT NULL )
                         UNION ALL
                         SELECT
                             'Thở OXY' loai,
                             'Số lượng: '
                             || thooxy_soluong
                             || ' '
                             || (
                                 CASE
                                     WHEN thooxy_loaitd = '0' THEN
                                         'lít/phút'
                                     WHEN thooxy_loaitd = '1' THEN
                                         'lít/giờ'
                                     ELSE
                                         thooxy_loaitd
                                 END
                             ) y_lenh,
                             NULL,
                             NULL,
                             5 stt_order,
                             NULL duong_dung,
                             NULL stt,
                             NULL ngayks,
                             NULL ngaycort,
                             NULL ngayht,
                             0 stt_th
                         FROM
                             noitru_dieutri
                         WHERE
                             dvtt = p_dvtt
                             AND stt_benhan = p_stt_benhan
                             AND stt_dotdieutri = p_stt_dotdieutri
                             AND id_dieutri = p_id_dieutri
                             AND ( thooxy_soluong IS NOT NULL
                                   OR thooxy_loaitd IS NOT NULL )
                         UNION ALL
                         SELECT
                             'Thủ thuật/phẫu thuật' loai,
                             nvl(xn.ten_hien_thi, xn.ten_dv)
                             || DECODE(ct.ghichu_tenttpt, NULL, '', '['
                                                                    || ct.ghichu_tenttpt
                                                                    || ']')
                             || '['
                             || TO_CHAR(ct.ngay_chi_dinh_ct, 'HH24:MI')
                             || ']' y_lenh,
                             ct.so_luong,
                             xn.dvt_dv dvt,
                             6 stt_order,
                             NULL duong_dung,
                             NULL stt,
                             NULL ngayks,
                             NULL ngaycort,
                             NULL ngayht,
                             0 stt_th
                         FROM
                             noitru_cd_dichvu_ct   ct
                             JOIN dm_dich_vu_kham       xn ON xn.ma_dv = ct.ma_dv
                                                        AND ct.dvtt = xn.dvtt
                                                        AND xn.loai_dv IN ('TT', 'PT', 'VLTL', 'THO_OXY')
                         WHERE
                             ct.dvtt = p_dvtt
                             AND ct.stt_benhan = p_stt_benhan
                             AND ct.stt_dotdieutri = p_stt_dotdieutri
                             AND ct.sovaovien = p_sovaovien
                             AND ct.id_dieutri = p_id_dieutri
                         UNION ALL
                         SELECT
                             'Pha thuốc' AS loai,
                             ghichu
                             || '<br/>'
                             ||
                                 LISTAGG(
                                     CASE
                                         WHEN y_lenh_thuoc IS NOT NULL THEN
                                             ' Thời gian thực hiện:<br/>'
                                             || y_lenh_thuoc
                                             || '<br/>'
                                         ELSE
                                             ''
                                     END
                                     ||
                                     CASE
                                         WHEN y_lenh_truyendich IS NOT NULL THEN
                                             ' Thời gian thực hiện truyền dịch:<br/>' || y_lenh_truyendich
                                         ELSE
                                             ''
                                     END, '<br/>') WITHIN GROUP(
                                     ORDER BY
                                         ghichu
                                 )
                             AS y_lenh,
                             NULL AS soluong,
                             NULL AS dvt,
                             7 AS stt_order,
                             NULL AS duong_dung,
                             NULL AS stt,
                             NULL AS ngayks,
                             NULL AS ngaycort,
                             NULL AS ngayht,
                             0 stt_th
                         FROM
                             (
                                 SELECT
                                     ghichu,
                                     LISTAGG(
                                         CASE
                                             WHEN y_lenh_thuoc IS NOT NULL THEN
                                                 y_lenh_thuoc
                                             ELSE
                                                 ''
                                         END
                                     ) WITHIN GROUP(
                                         ORDER BY
                                             ghichu
                                     ) AS y_lenh_thuoc,
                                     LISTAGG(
                                         CASE
                                             WHEN y_lenh_truyendich IS NOT NULL THEN
                                                 y_lenh_truyendich
                                             ELSE
                                                 ''
                                         END
                                     ) WITHIN GROUP(
                                         ORDER BY
                                             ghichu
                                     ) AS y_lenh_truyendich
                                 FROM
                                     (
                                         SELECT
                                             ghichu,
                                             cmu_yltg_phathuoc_group(dvtt, id_dieutri, sovaovien, '-1', 'PHA_' || id_phathuoc, 'thuoc'
                                             ) AS y_lenh_thuoc,
                                             cmu_yltg_phathuoc_group(dvtt, id_dieutri, sovaovien, '-1', 'PHA_' || id_phathuoc, 'truyendich'
                                             ) AS y_lenh_truyendich
                                         FROM
                                             cmu_phathuoc
                                         WHERE
                                             dvtt = p_dvtt
                                             AND sovaovien = p_sovaovien
                                             AND id_dieutri = p_id_dieutri
                                     )
                                 GROUP BY
                                     ghichu
                             )
                         GROUP BY
                             ghichu
                         UNION ALL
                         SELECT
                             'Truyền máu' loai,
                             tenchepham
                             ||
                                 CASE
                                     WHEN tenchepham IS NOT NULL THEN
                                         '; '
                                     ELSE
                                         ''
                                 END
                             || soluong
                             || ' ml '
                             ||
                                 CASE
                                     WHEN tocdo IS NOT NULL THEN
                                         '; Tốc độ truyền: '
                                         || tocdo
                                         || ' '
                                         ||
                                             CASE
                                                 WHEN loai = '0' THEN
                                                     ' giọt/phút'
                                                 ELSE
                                                     ' ml/giờ;'
                                             END
                                 END
                             || ' ('
                             || TO_CHAR(ngaytruyen, 'HH24:MI')
                             || ')' y_lenh,
                             NULL soluong,
                             NULL dvt,
                             8 stt_order,
                             NULL duong_dung,
                             NULL stt,
                             NULL ngayks,
                             NULL ngaycort,
                             NULL ngayht,
                             0 stt_th
                         FROM
                             cmu_ylenhtruyenmau
                         WHERE
                             dvtt = p_dvtt
                             AND sovaovien = p_sovaovien
                             AND id_dieutri = p_id_dieutri
                         UNION ALL
                         SELECT
                             'Thuốc/dịch truyền' loai,
                             a.hoat_chat
                             || ' '
                             || a.ham_luong
                             || '( <b>'
                             || a.ten_vat_tu
                             || '</b> )'
                             ||
                                 CASE nvl(ghi_chu_ct_toa_thuoc, ' ')
                                     WHEN ' ' THEN
                                         ''
                                     ELSE
                                         '('
                                         || ghi_chu_ct_toa_thuoc
                                         || ')'
                                 END
                             || DECODE(a.cach_su_dung, 'Viên', ' '
                                                               || a.so_ngay_uong
                                                               || ' ngày uống ', '')
                             ||
                                 CASE
                                     WHEN a.sang_uong = 0 THEN
                                         ''
                                     ELSE
                                         concat(': Sáng: ', chuyen_thapphan_sang_phanso(a.sang_uong))
                                 END
                             ||
                                 CASE
                                     WHEN a.trua_uong = 0 THEN
                                         ''
                                     ELSE
                                         concat(' Trưa: ', chuyen_thapphan_sang_phanso(a.trua_uong))
                                 END
                             ||
                                 CASE
                                     WHEN a.chieu_uong = 0 THEN
                                         ''
                                     ELSE
                                         concat(' Chiều: ', chuyen_thapphan_sang_phanso(a.chieu_uong))
                                 END
                             ||
                                 CASE
                                     WHEN a.toi_uong = 0 THEN
                                         ''
                                     ELSE
                                         concat(' Tối: ', chuyen_thapphan_sang_phanso(a.toi_uong))
                                 END
                             ||
                                 CASE
                                     WHEN a.cach_su_dung IS NOT NULL THEN
                                         DECODE(v_thamso_94300, '2', '', '('
                                                                         || a.cach_su_dung
                                                                         || ')')
                                     ELSE
                                         ''
                                 END
                             || CHR(10)
                             || cmu_ylenhthuoc_theogio_groupv3(p_dvtt, a.id_dieutri, a.sovaovien, a.sovaovien_dt, a.mavattu, a.nghiep_vu
                             ) AS y_lenh,
                             SUM(a.so_luong) soluong,
                             a.dvt,
                             9 stt_order,
                             duong_dung,
                             a.mavattu stt,
                             cmu_ngaysdkhangsinh(p_dvtt, a.mavattu, a.stt_benhan, a.sovaovien, a.id_dieutri) ngayks,
                             cmu_ngaysdcort(p_dvtt, a.mavattu, a.stt_benhan, a.sovaovien, a.id_dieutri) ngaycort,
                             cmu_ngaysdht(p_dvtt, a.mavattu, a.stt_benhan, a.sovaovien, a.id_dieutri) ngayht,
                             cmu_minsttorderthuoc(p_dvtt, a.mavattu, a.stt_benhan, a.sovaovien, a.id_dieutri) stt_th
                         FROM
                             his_manager.noitru_ct_toa_thuoc a
                         WHERE
                             a.stt_benhan = p_stt_benhan
                             AND a.id_dieutri = p_id_dieutri
                             AND a.dvtt = p_dvtt
                             AND ( a.nghiep_vu IN ('noitru_toathuoc', 'ba_ngoaitru_toathuoc', 'noitru_toamienphi', 'noitru_toadongy', 'ba_ngoaitru_toadongy')
                                   --OR a.kyhieunhombc LIKE '%THUOC%' 
                                   OR (v_thamso960615 = '1' and a.nghiep_vu = 'noitru_toadichvu' and a.KYHIEUNHOMBC LIKE 'THUOC%')
                                   OR (a.nghiep_vu = 'noitru_toaquaybanthuocbv' and a.KYHIEUNHOMBC LIKE 'THUOC%')
                                   )
                             AND a.sovaovien = p_sovaovien
                         GROUP BY
                             a.dvt,
                             a.ten_vat_tu,
                             a.hoat_chat,
                             a.ham_luong,
                             a.ghi_chu_ct_toa_thuoc,
                             a.sang_uong,
                             a.trua_uong,
                             a.chieu_uong,
                             a.toi_uong,
                             a.cach_su_dung,
                             a.so_ngay_uong,
                             a.id_dieutri,
                             a.sovaovien,
                             a.sovaovien_dt,
                             a.nghiep_vu,
                             duong_dung,
                             a.mavattu,
                             cmu_ngaysdkhangsinh(p_dvtt, a.mavattu, a.stt_benhan, a.sovaovien, a.id_dieutri),
                             cmu_ngaysdcort(p_dvtt, a.mavattu, a.stt_benhan, a.sovaovien, a.id_dieutri),
                             cmu_ngaysdht(p_dvtt, a.mavattu, a.stt_benhan, a.sovaovien, a.id_dieutri),
                             cmu_minsttorderthuoc(p_dvtt, a.mavattu, a.stt_benhan, a.sovaovien, a.id_dieutri)
                         UNION ALL
                         SELECT
                             'Thuốc/dịch truyền (thực hiện tiếp tục)' loai,
                             a.ten_vat_tu
                             || '<br/>'
                             || cmu_ylenhthuoc_theogio_group(p_dvtt, p_id_dieutri, p_sovaovien, '-1', a.stt_toathuoc) AS y_lenh,
                             NULL soluong,
                             NULL,
                             10 stt_order,
                             NULL duong_dung,
                             NULL stt,
                             NULL ngayks,
                             NULL ngaycort,
                             NULL ngayht,
                             0 stt_th
                         FROM
                             (
                                 SELECT DISTINCT
                                     tt.ten_vat_tu,
                                     ylenh.stt_toathuoc
                                 FROM
                                     cmu_ylenhthuoc_theogio   ylenh
                                     JOIN noitru_dieutri           dt ON ylenh.dvtt = dt.dvtt
                                                               AND ylenh.sovaovien = dt.sovaovien
                                                               AND dt.sovaovien = p_sovaovien
                                                               AND dt.id_dieutri = ylenh.id_dieutri_khac
                                     JOIN noitru_ct_toa_thuoc      tt ON ylenh.dvtt = tt.dvtt
                                                                    AND tt.sovaovien = ylenh.sovaovien
                                                                    AND tt.sovaovien = p_sovaovien
                                                                    AND ylenh.stt_toathuoc = 'tdt' || tt.stt_toathuoc
                                                                    AND ylenh.id_dieutri_khac = tt.id_dieutri
                                 WHERE
                                     ylenh.dvtt = p_dvtt
                                     AND ylenh.id_dieutri = p_id_dieutri
                                     AND ylenh.sovaovien = p_sovaovien
                                     AND ylenh.id_dieutri_khac IS NOT NULL
                                     AND ylenh.loai_ylenh IN (
                                         'truyendich',
                                         'thuoc'
                                     )
                             ) a
                         UNION ALL
                         SELECT
                             'Thuốc mua ngoài' loai,
                             ct.ten_vat_tu
                             ||
                                 CASE
                                     WHEN ct.hoat_chat IS NOT NULL THEN
                                         '('
                                         || ct.hoat_chat
                                         || ')'
                                     ELSE
                                         ''
                                 END
                             ||
                                 CASE
                                     WHEN ct.sang_uong = 0 THEN
                                         ''
                                     ELSE
                                         concat(': Sáng: ', chuyen_thapphan_sang_phanso(ct.sang_uong))
                                 END
                             ||
                                 CASE
                                     WHEN ct.trua_uong = 0 THEN
                                         ''
                                     ELSE
                                         concat(' Trưa: ', chuyen_thapphan_sang_phanso(ct.trua_uong))
                                 END
                             ||
                                 CASE
                                     WHEN ct.chieu_uong = 0 THEN
                                         ''
                                     ELSE
                                         concat(' Chiều: ', chuyen_thapphan_sang_phanso(ct.chieu_uong))
                                 END
                             ||
                                 CASE
                                     WHEN ct.toi_uong = 0 THEN
                                         ''
                                     ELSE
                                         concat(' Tối: ', chuyen_thapphan_sang_phanso(ct.toi_uong))
                                 END
                             || DECODE(v_thamso_94300, '2', '', '('
                                                                || ct.cach_su_dung
                                                                || ')')
                             || CHR(10)
                             || cmu_ylenhthuoc_theogio_group(p_dvtt, ct.id_dieutri, ct.sovaovien, ct.sovaovien_dt, 'n' || ct.stt_toathuoc
                             ) y_lenh,
                             ct.so_luong,
                             nvl(dm.dvt, ct.ghi_chu_ct_toa_thuoc) dvt,
                             11 stt_order,
                             NULL duong_dung,
                             NULL stt,
                             NULL ngayks,
                             NULL ngaycort,
                             NULL ngayht,
                             0 stt_th
                         FROM
                             noitru_ct_toa_thuoc_muangoai   ct
                             JOIN dc_tb_vattu_muangoai           dm ON ct.mavattu = dm.mavattu
                         WHERE
                             ct.dvtt = p_dvtt
                             AND ct.stt_benhan = p_stt_benhan
                             AND ct.stt_dotdieutri = p_stt_dotdieutri
                             AND ct.id_dieutri = p_id_dieutri
                             AND ct.so_luong > 0
                         UNION ALL
                         SELECT
                             'Hoàn trả thuốc' loai,
                             vt.hoatchat
                             || ' '
                             || vt.hamluong
                             || '( '
                             || vt.tenvattu
                             || ' )'
                             || ' - Ngày y lệnh: '
                             || TO_CHAR(dt.ngaygiolap_tdt, 'DD/MM/YYYY HH24:MI') y_lenh,
                             ht.so_luong,
                             vt.dvt,
                             12 stt_order,
                             NULL duong_dung,
                             NULL stt,
                             NULL ngayks,
                             NULL ngaycort,
                             NULL ngayht,
                             0 stt_th
                         FROM
                             agg_nt_cttt_thuocsd_phieusai   ht
                             INNER JOIN noitru_dieutri                 dt ON ht.dvtt = dt.dvtt
                                                             AND ht.stt_benhan = dt.stt_benhan
                                                             AND ht.stt_dotdieutri = dt.stt_dotdieutri
                                                             AND ht.id_dieutri = dt.id_dieutri
                             INNER JOIN dc_tb_vattu                    vt ON ht.dvtt = vt.dvtt
                                                          AND vt.mavattu = ht.mavattu
                         WHERE
                             ht.dvtt = p_dvtt
                             AND ht.stt_benhan = p_stt_benhan
                             AND ht.stt_dotdieutri = p_stt_dotdieutri
                             AND ht.id_dieutri_hoantra = p_id_dieutri
                             AND ht.nghiep_vu = 'noitru_toathuoc'
                         UNION ALL
                         SELECT
                             'Hủy thực hiện y lệnh cận lâm sàng' loai,
                             CASE
                                     WHEN huycls.so_phieu LIKE 'xn%' THEN
                                         nvl(xetnghiem.ten_hien_thi, xetnghiem.ten_xetnghiem)
                                     WHEN huycls.so_phieu LIKE 'CD%' THEN
                                         nvl(cdha.ten_hien_thi, cdha.ten_cdha)
                                     WHEN huycls.so_phieu LIKE 'dv%' THEN
                                         nvl(dv.ten_hien_thi, dv.ten_dv)
                                     ELSE
                                         ''
                                 END
                             || '('
                             || TO_CHAR(huycls.thoigianylenh, 'DD/MM/YYYY HH24:MI')
                             || ') '
                             || ' Bác sĩ chỉ định  '
                             || d.ten_nhanvien_cd
                             || ' ['
                             || 'Số phiếu: '
                             || dthuy.stt_dieutri
                             || ']' y_lenh,
                             huycls.so_luong,
                             CASE
                                 WHEN huycls.so_phieu LIKE 'xn%' THEN
                                     xetnghiem.dvt_xetnghiem
                                 WHEN huycls.so_phieu LIKE 'CD%' THEN
                                     cdha.dvt_cdha
                                 WHEN huycls.so_phieu LIKE 'dv%' THEN
                                     dv.dvt_dv
                                 ELSE
                                     NULL
                             END,
                             13 stt_order,
                             NULL duong_dung,
                             NULL stt,
                             NULL ngayks,
                             NULL ngaycort,
                             NULL ngayht,
                             0 stt_th
                         FROM
                             hsba_huyylcls           huycls
                             JOIN noitru_dieutri          dthuy ON dthuy.dvtt = huycls.dvtt
                                                          AND dthuy.sovaovien = huycls.sovaovien
                                                          AND dthuy.id_dieutri = huycls.id_dieutri_huy
                             LEFT JOIN cls_xetnghiem           xetnghiem ON huycls.dvtt = xetnghiem.dvtt
                                                                  AND huycls.ma_dv = xetnghiem.ma_xetnghiem
                                                                  AND huycls.so_phieu LIKE 'xn%'
                             LEFT JOIN cls_cdha                cdha ON huycls.dvtt = cdha.dvtt
                                                        AND huycls.ma_dv = cdha.ma_cdha
                                                        AND huycls.so_phieu LIKE 'CD%'
                             LEFT JOIN dm_dich_vu_kham         dv ON huycls.dvtt = dv.dvtt
                                                             AND huycls.ma_dv = dv.ma_dv
                                                             AND huycls.so_phieu LIKE 'dv%'
                             LEFT JOIN his_fw.dm_nhanvien_cd   d ON dthuy.tdt_nguoilap = d.ma_nhanvien
                         WHERE
                             huycls.dvtt = p_dvtt
                             AND huycls.sovaovien = p_sovaovien
                             AND huycls.id_dieutri = p_id_dieutri
                         UNION ALL
                         SELECT
                             'Hủy y lệnh thuốc' loai,
                             vt.hoatchat
                             || ' '
                             || vt.hamluong
                             || '( '
                             || vt.tenvattu
                             || ' )'
                             || ' Bác sĩ chỉ định  '
                             || d.ten_nhanvien_cd
                             || ' ['
                             || 'Số phiếu: '
                             || dthuy.stt_dieutri
                             || ']' y_lenh,
                             phathuoc.so_luong,
                             vt.dvt,
                             14 stt_order,
                             NULL duong_dung,
                             NULL stt,
                             NULL ngayks,
                             NULL ngaycort,
                             NULL ngayht,
                             0 stt_th
                         FROM
                             hsba_huyylthuoc         phathuoc
                             JOIN noitru_dieutri          dthuy ON dthuy.dvtt = phathuoc.dvtt
                                                          AND dthuy.sovaovien = phathuoc.sovaovien
                                                          AND dthuy.id_dieutri = phathuoc.id_dieutri_huy
                             JOIN dc_tb_vattu             vt ON phathuoc.dvtt = vt.dvtt
                                                    AND phathuoc.mavattu = vt.mavattu
                             LEFT JOIN his_fw.dm_nhanvien_cd   d ON dthuy.tdt_nguoilap = d.ma_nhanvien
                         WHERE
                             phathuoc.dvtt = p_dvtt
                             AND phathuoc.sovaovien = p_sovaovien
                             AND phathuoc.id_dieutri = p_id_dieutri
                         UNION ALL
                         SELECT
                             'Y lệnh mời khám' loai,
                             pban.ten_phongban
                             || ', thời gian: '
                             || TO_CHAR(ngaymoikham, 'DD/MM/YYYY HH24:MI')
                             ||
                                 CASE
                                     WHEN ghichu IS NOT NULL THEN
                                         '; ' || ghichu
                                     ELSE
                                         ''
                                 END
                             y_lenh,
                             NULL soluong,
                             NULL dvt,
                             15 stt_order,
                             NULL duong_dung,
                             NULL stt,
                             NULL ngayks,
                             NULL ngaycort,
                             NULL ngayht,
                             0 stt_th
                         FROM
                             cmu_hsba_moikham     mk
                             JOIN his_fw.dm_phongban   pban ON pban.ma_donvi = p_dvtt
                                                             AND pban.ma_phongban = mk.makhoabs
                         WHERE
                             dvtt = p_dvtt
                             AND stt_benhan = p_stt_benhan
                             AND id_dieutri_moikham = p_id_dieutri
                         UNION ALL
                         SELECT
                             '' loai,
                             tdt_ylenh y_lenh,
                             NULL,
                             NULL,
                             16 stt_order,
                             NULL duong_dung,
                             NULL stt,
                             NULL ngayks,
                             NULL ngaycort,
                             NULL ngayht,
                             0 stt_th
                         FROM
                             noitru_dieutri
                         WHERE
                             dvtt = p_dvtt
                             AND stt_benhan = p_stt_benhan
                             AND stt_dotdieutri = p_stt_dotdieutri
                             AND id_dieutri = p_id_dieutri
                             AND tdt_ylenh IS NOT NULL
                     ) a
                 ORDER BY
                     stt_order,
                     CASE
                             WHEN duong_dung IN (
                                 '2.01',
                                 '2.02',
                                 '2.03',
                                 '2.04',
                                 '2.05',
                                 '2.06',
                                 '2.07',
                                 '2.08',
                                 '2.09',
                                 '2.10',
                                 '2.11',
                                 '2.14',
                                 '2.15'
                             ) THEN
                                 1 -- Đường tiêm
                             WHEN duong_dung IN (
                                 '1.01',
                                 '1.02',
                                 '1.03'
                             ) THEN
                                 2 -- Đường uống
                             WHEN duong_dung IN (
                                 '1.04',
                                 '1.05',
                                 '4.01',
                                 '4.02',
                                 '4.03',
                                 '4.04',
                                 '4.05',
                                 '5.06',
                                 '9.03',
                                 '9.04',
                                 '9.07'
                             ) THEN
                                 3 -- Đường đặt
                             WHEN duong_dung IN (
                                 '3.01',
                                 '3.02',
                                 '3.03',
                                 '3.04',
                                 '3.05',
                                 '5.02',
                                 '5.03',
                                 '5.04',
                                 '5.05',
                                 '5.07',
                                 '5.08',
                                 '9.01',
                                 '9.02',
                                 '9.08',
                                 '9.09',
                                 '9.10',
                                 '9.11',
                                 '9.12'
                             ) THEN
                                 4 -- Dùng ngoài
                             ELSE
                                 5 -- Các đường dùng khác
END,
                     stt_th,
                     stt,
                     y_lenh ASC;

END;
