create or replace FUNCTION HIS_MANAGER."CMU_YLENHTHUOC_THEOGIO_INS_V5" (
    p_dvtt             VARCHAR2,
    p_id_dieutri       VARCHAR2,
    p_sovaovien        VARCHAR2,
    p_sovaovien_dt     VARCHAR2,
    p_stt_toathuoc     VARCHAR2,
    p_thoigian_ylenh   VARCHAR2,
    p_ghichu           VARCHAR2,
    p_loaiylenh        VARCHAR2,
    p_loaitocdo        VARCHAR2,
    p_tocdo            VARCHAR2,
    p_soml             VARCHAR2,
    p_lieudung         VARCHAR2,
    p_nguoitao         VARCHAR2
) RETURN VARCHAR2 IS
    v_exist NUMBER := 0;
    v_id_ylenh number := 0;
    v_return number := -1;
BEGIN
SELECT
    COUNT(1)
INTO v_exist
FROM
    cmu_ylenhthuoc_theogio
WHERE
        dvtt = p_dvtt
  AND id_dieutri = p_id_dieutri
  AND stt_toathuoc = p_stt_toathuoc
  AND thoigianylenh = TO_DATE(p_thoigian_ylenh, 'DD/MM/YYYY HH24:MI:SS');

IF v_exist = 0 THEN
        INSERT INTO cmu_ylenhthuoc_theogio (
            dvtt,
            id_dieutri,
            sovaovien,
            sovaovien_dt,
            stt_toathuoc,
            thoigianylenh,
            ngaytao,
            ghichu,
            nguoitao,
            loai_ylenh,
            loai_tocdo_ylenh,
            tocdo_truyendich,
            soml_truyendich,
            lieudung
        ) VALUES (
            p_dvtt,
            p_id_dieutri,
            p_sovaovien,
            p_sovaovien_dt,
            p_stt_toathuoc,
            TO_DATE(p_thoigian_ylenh, 'DD/MM/YYYY HH24:MI:SS'),
            SYSDATE,
            p_ghichu,
            p_nguoitao,
            p_loaiylenh,
            p_loaitocdo,
            p_tocdo,
            p_soml,
            p_lieudung
        ) returning id into v_id_ylenh;
        v_return := v_id_ylenh;
ELSE
        v_return := -1;
END IF;

RETURN v_return;
END;