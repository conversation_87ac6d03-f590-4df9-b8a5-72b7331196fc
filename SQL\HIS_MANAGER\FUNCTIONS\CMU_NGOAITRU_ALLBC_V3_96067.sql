create or replace PROCEDURE CMU_NGOAITRU_ALLBC_V3_96067(
p_dvtt varchar2,
p_sovaovien number,
p_sovaovien_dt number default 0
)
IS
v_mabenhnhan number(20);
v_tenbenhnhan varchar2(500):='';
v_diachi varchar2(500):='';
v_mabenhan varchar2(500):='';

v_sothebhyt varchar2(255):='';
v_tylebh varchar2(500):='';
v_ngaybatdau date;
v_ngayketthuc date;

v_ngayvaovien date;
v_ngayravien date;
v_tongchiphi number(18,4):=0;
v_congkham number(18,4):=0;

v_xetnghiem number(18,4):=0;
v_cdha number(18,4):=0;
v_ttpt number(18,4):=0;
v_giuong number(18,4):=0;
v_thuoc number(18,4):=0;


v_xetnghiem_thuphi number(18,4):=0;
v_cdha_thuphi number(18,4):=0;
v_ttpt_thuphi number(18,4):=0;
v_giuong_thuphi number(18,4):=0;
v_thuoc_thuphi number(18,4):=0;
v_thuoc_ktcao number(18,4):=0;
v_thuoc_ktcaobh number(18,4):=0;
v_xetnghiem_xhh number(18,4):=0;
v_cdha_xhh number(18,4):=0;
v_ttpt_xhh number(18,4):=0;
v_sotienchuyenconho number(18,4):=0;

v_maphongban varchar2(255):='';
v_tenphongban varchar2(500):='';
v_ttphieuthanhtoan varchar2(500):='';
v_tongtamung number(18,4):=0;
v_chiphithanhtoanngoaitru number(18,4):=0;
v_ngaysinh date;
v_gioitinh varchar2(5):='';
v_icd clob:='';
v_icd1 clob:='';
v_trangthairavien varchar2(5):='';
v_bant varchar2(5):='0';
v_dathanhtoan varchar2(5):='';
v_dangmokhoathnhatoan number(18,4):=0;
v_tongchiphidathanhtoan number(18,4):=0;
v_tongtien  number(18,4):=0;
v_tongchiphixhh number(18,4):=0;
v_sophieuchinhsua varchar2(255):='';
v_tongchihoantra  number(18,4):=0;
v_loaikcb  varchar2(5):='';
v_noitinh varchar2(5):='';
v_kyhieugroup   varchar2(5):='';
v_tuoi  varchar2(5):='';
v_thang  varchar2(5):='';
v_tongsongaydieutri varchar2(5):='';
v_tongchiphi_phatsinh number(18,4):='';
v_noidangkybandau varchar2(255):='';
v_trangthai number(18,4):=0;
v_tbhyt number(18,4):=0;
BEGIN
    --select 1 into v_bant from dual;



select MABENHNHAN, CHANDOANBENH
into v_mabenhnhan,v_icd1
from KB_KHAM_BENH
where dvtt = p_dvtt and SOVAOVIEN = p_sovaovien;

select TEN_BENH_NHAN,NGAY_SINH,DIA_CHI,GIOI_TINH
into v_tenbenhnhan, v_ngaysinh, v_diachi,v_gioitinh
from HIS_PUBLIC_LIST.DM_BENH_NHAN
where MA_BENH_NHAN = v_mabenhnhan;

select TRUNC(NGAY_TIEP_NHAN),TRUNC(NGAY_TIEP_NHAN)
        , SO_THE_BHYT, NGAY_BATDAU, NGAY_HETHAN,
       TI_LE_MIEN_GIAM,thang,tuoi,NOIDANGKY_KCB, TRANG_THAI
into v_ngayvaovien, v_ngayravien, v_sothebhyt, v_ngaybatdau, v_ngayketthuc, v_tylebh,
    v_thang,v_tuoi,v_noidangkybandau, v_trangthai
from KB_TIEP_NHAN
where dvtt = p_dvtt and SOVAOVIEN = p_sovaovien ;

select
    nvl(sum(decode(GHI_CHU,'TOATHUOC',NGUOI_BENH,0)),0),
    nvl(sum(decode(GHI_CHU,'CHANDOANHINHANH',NGUOI_BENH,0)),0),
    nvl(sum(decode(GHI_CHU,'XETNGHIEM',NGUOI_BENH,0)),0),
    nvl(sum(decode(GHI_CHU,'THUTHUATPHAUTHUAT',NGUOI_BENH,0)),0),
    nvl(sum(CASE WHEN GHI_CHU = 'CONGKHAMCHUYENDEN' OR GHI_CHU = 'CONGKHAM' THEN NGUOI_BENH ELSE 0 END),0)
into v_thuoc, v_cdha,v_xetnghiem,v_ttpt, v_congkham from TEM_BANGKE_IN
where dvtt = p_dvtt and SOVAOVIEN = p_sovaovien
  and NGUOI_BENH > 0 and
    GHI_CHU in (
                'TOATHUOC','CONGKHAMCHUYENDEN','CONGKHAM',
                'CHANDOANHINHANH','XETNGHIEM', 'THUTHUATPHAUTHUAT'
        ) ;

select nvl(sum(THANH_TIEN),0) into v_tongchiphi_phatsinh from TEM_BANGKE_IN
where dvtt = p_dvtt and SOVAOVIEN = p_sovaovien;

select sum(SOTIENBNTRA) INTO v_tongtamung from VIENPHINGOAITRU_LANTAMUNG
where dvtt = p_dvtt and SOVAOVIEN = p_sovaovien
  and  TT_LANTT = 1;
begin
select MA_PHONG_BAN_INT into v_maphongban
from TEM_BANGKE_IN
where dvtt = p_dvtt and SOVAOVIEN = p_sovaovien and ROWNUM = 1
;
EXCEPTION
      when no_data_found
        then v_maphongban:='';
end;

begin
select TEN_PHONGBAN into v_tenphongban from
    HIS_FW.DM_PHONGBAN where MA_DONVI = p_dvtt and MA_PHONGBAN = v_maphongban;
EXCEPTION
      when no_data_found
        then v_tenphongban:='';
end;
select nvl(TT_PHIEUTHANHTOAN,0), THANHTOANBAOHIEM
INTO v_ttphieuthanhtoan,v_dathanhtoan from KB_PHIEUTHANHTOAN
where dvtt = p_dvtt and SOVAOVIEN = p_sovaovien;

begin
select NOITINH,KYHIEUGROUP,MA_LOAIKCB,T_BHTT
into v_noitinh,v_kyhieugroup,v_loaikcb, v_tbhyt
from HIS_SYNCHRONIZATION.B1_CHITIEUTONGHOP_KCB
where dvtt = p_dvtt and SOVAOVIEN = p_sovaovien and SOVAOVIEN_NOI IS NULL;
exception
      when no_data_found
        then v_noitinh:='';
end;
select nvl(sum(round(TONGTIENBNTHANHTOAN,0)),0) into v_tongchiphidathanhtoan  from
    VIENPHINGOAITRU_LANTT
where dvtt = p_dvtt and sovaovien = p_sovaovien
  and TT_LANTT = 1;
delete from CMU_CHIPHINOITRU_CT_PT_96067
WHERE DVTT = to_number(p_dvtt) and SOVAOVIEN = p_sovaovien

  and SOVAOVIEN_DT = p_sovaovien_dt
; commit;
delete from CMU_THEODOICHIPHINOITRU_96067
WHERE DVTT = to_number(p_dvtt) and SOVAOVIEN = p_sovaovien
  and SOVAOVIEN_DT = p_sovaovien_dt

; commit;
INSERT INTO CMU_THEODOICHIPHINOITRU_96067
(DVTT,
 SOVAOVIEN,
 SOVAOVIEN_DT,
 MABENHNHAN,
 TENBENHNHAN,
 DIACHI,
 MABENHAN,
 SOTHEBHYT,
 TYLEBH,
 NGAYBATDAU,
 NGAYHETHAN,
 NGAYVAOVIEN,
 TONGCHIPHI,
 CONGKHAM,
 XETNGHIEM,
 CDHA,
 TTPT,
 GIUONG,
 NGAYRAVIEN,
 MAPHONGBAN,
 TENPHONGBAN,
 TT_PHIEUTHANHTOAN,
 TONGTAMUNG,
 CHIPHI_TTNGOAITRU,
 NGAYSINH,
 THUOC,
 XETNGHIEM_XHH,
 CDHA_XHH,
 TTPT_XHH,SOTIENCHUYENCONHO,GIOITINH,
 HINHTHUCKETTHUC,
 BANT,
 DATHANHTOAN,
 CHIPHIDATHANHTOAN,
 TONGCHIHOANTRA,
 THANG,
 TUOI,
 ICD,
 MALOAI_KCB,
 TONGCHIPHI_PHATSINH,
 TONGSONGAYDIEUTRI,NOIDANGKY_KCB,NOITINH,T_BHYT,TT_HOANTATKHAM,TT_SAISOT) VALUES (
                                                                                     p_dvtt,
                                                                                     p_sovaovien,
                                                                                     p_sovaovien_dt,
                                                                                     v_mabenhnhan,
                                                                                     v_tenbenhnhan,
                                                                                     v_diachi,
                                                                                     v_mabenhan,
                                                                                     v_sothebhyt,
                                                                                     v_tylebh,
                                                                                     v_ngaybatdau,
                                                                                     v_ngayketthuc,
                                                                                     v_ngayvaovien,
                                                                                     round(v_congkham +v_cdha+v_thuoc+v_xetnghiem+v_ttpt,0),
                                                                                     v_congkham,
                                                                                     v_xetnghiem,
                                                                                     v_cdha,
                                                                                     v_ttpt,
                                                                                     v_giuong,
                                                                                     decode(v_ngayravien,null,to_date('2000-01-01','yyyy-mm-dd'),v_ngayravien),
                                                                                     v_maphongban,
                                                                                     v_tenphongban,
                                                                                     v_ttphieuthanhtoan,
                                                                                     nvl(v_tongtamung,0),
                                                                                     nvl(v_chiphithanhtoanngoaitru,0),
                                                                                     v_ngaysinh,
                                                                                     v_thuoc,
                                                                                     0,
                                                                                     0,
                                                                                     0,
                                                                                     0,
                                                                                     v_gioitinh,
                                                                                     v_trangthairavien,
                                                                                     v_bant,
                                                                                     v_dathanhtoan,
                                                                                     v_tongchiphidathanhtoan,
                                                                                     v_tongchihoantra,
                                                                                     v_thang,
                                                                                     v_tuoi,
                                                                                     v_icd1||';'||v_icd,
                                                                                     v_loaikcb,
                                                                                     v_tongchiphi_phatsinh,
                                                                                     v_tongsongaydieutri,v_noidangkybandau,v_noitinh,v_tbhyt,v_trangthai,0
                                                                                 );




INSERT
INTO CMU_CHIPHINOITRU_CT_PT_96067 (
    DVTT,
    SOVAOVIEN,
    SOVAOVIEN_DT,
    MABENHNHAN,
    TENBENHNHAN,
    DIACHI,
    MABENHAN,
    SOTHEBHYT,
    TYLEBH,
    NGAYBATDAU,
    NGAYHETHAN,
    NGAYVAOVIEN,
    NGAYRAVIEN,
    NGAYSINH,
    MAPHONGBAN_CD,
    TENPHONGBAN_CD,
    TT_PHIEUTHANHTOAN,
    TENPHONGBAN_DANGNOITRU,
    MAPHONGBAN_DANGNOITRU,
    NGAYCHIDINH,
    NGAYKETQUA,
    NGUOICHIDINH,
    BACSIDIEUTRI,
    SOPHIEU,
    BACSITHUCHIEN,
    MADV,
    TENDICHVU,
    LOAIDICHVU,
    DONGIA,
    SOLUONG,
    THANHTIEN,
    BHYTCHI,
    GB_NGAYVAO,
    GB_NGAYRA,
    NGHIEPVU_THUOC,
    MA_TTPT,
    SOPHIEUTT_PT,
    KETQUA,
    ICD,GIOITINH,
    GIATHANG3,
    GIATHANG7,
    GIATT15,
    LOAI_KCB,
    NOITINH,
    KYHIEUGROUP,MABAOCAO_BYT,NOIDANGKY_KCB, TT_HOANTATKHAM,
    GIATT39,
    GIATT37_39,
    TT_SAISOT,
    MAKHOA_TH,
    TENKHOA_TH,
    TENFILM,
    SOLUONG_FILM
)
select
    p_dvtt,
    p_sovaovien,
    p_sovaovien_dt,
    ct.MABENHNHAN,
    v_tenbenhnhan,
    v_diachi,
    v_mabenhan,
    v_sothebhyt,
    v_tylebh,
    v_ngaybatdau,
    v_ngayketthuc,
    v_ngayvaovien,
    decode(v_ngayravien,null,to_date('2000-01-01','yyyy-mm-dd'),v_ngayravien),
    v_ngaysinh,
    xncha.PHONG_CHI_DINH,
    '' TENPHONGBAN_CD,
    v_ttphieuthanhtoan,
    v_tenphongban,
    v_maphongban,
    XNCHA.NGAY_CHI_DINH,
    xncha.NGAY_TRA_KETQUA,
    XNCHA.NGUOI_CHI_DINH,
    xncha.BAC_SI_DIEU_TRI,
    XNCHA.SO_PHIEU_XN,
    to_char(xncha.NGUOI_THUC_HIEN),
    fc.MA_XETNGHIEM,
    fc.ten_xetnghiem,
    'XN',

    sum(CASE WHEN CT.BHYTKCHI = 1 THEN CT.DON_GIA_KO_BHYT ELSE CT.DON_GIA_BHYT END)
        ,
    CT.SO_LUONG,
    sum(CASE WHEN CT.BHYTKCHI = 1 THEN CT.THANH_TIEN_KO_BHYT ELSE CT.THANH_TIEN_BHYT END)
        ,
    CT.BHYTKCHI,
    NULL,
    NULL,
    '',
    '',
    '',
    LISTAGG(case when xetnghiem.cap_xn=2
                     then xetnghiem.ten_xetnghiem ||'('||ct.ket_qua ||')' else ct.ket_qua end, '; ') WITHIN GROUP (ORDER BY  ' '),
        v_icd1,
        v_gioitinh,
        fc.GIA_THANG3,
        fc.GIA_THANG7,
        fc.giabaohiem,
        v_loaikcb, v_noitinh,v_kyhieugroup,
        fc.mabaocao_byt,v_noidangkybandau,v_trangthai,
				fc.GIABAOHIEM_TT39,
        fc.GIACOBH_TT37_tt39,0,
				'0' ma_phongban,
				'' ten_phongban,
				'' tenfilm,
				null SOLUONG_FILM
FROM KB_CD_XET_NGHIEM_CHI_TIET ct INNER JOIN
    KB_CD_XET_NGHIEM xncha on ct.dvtt = xncha.dvtt
    and ct.sovaovien = xncha.sovaovien and xncha.so_phieu_xn = ct.so_phieu_xn
    inner join CLS_XETNGHIEM xetnghiem
    on xetnghiem.dvtt = ct.dvtt and xetnghiem.ma_xetnghiem = ct.ma_xet_nghiem --and xetnghiem.co_dulieucon=0
    inner join his_manager.cls_xetnghiem fc on CT.dvtt=fc.dvtt and nvl(ct.id_chisocha, ct.ma_xet_nghiem)=fc.ma_xetnghiem
where ct.dvtt = p_dvtt and ct.sovaovien = p_sovaovien
  AND (CT.DAYDULIEU_VAONOITRU is null or CT.DAYDULIEU_VAONOITRU = 0)
group by ct.MABENHNHAN,CT.BHYTKCHI,
    XNCHA.NGAY_CHI_DINH,
    xncha.NGAY_TRA_KETQUA,
    XNCHA.NGUOI_CHI_DINH,
    xncha.BAC_SI_DIEU_TRI,
    XNCHA.SO_PHIEU_XN,
    to_char(xncha.NGUOI_THUC_HIEN),
    fc.MA_XETNGHIEM,
    fc.ten_xetnghiem,
    xncha.PHONG_CHI_DINH,
    CT.SO_LUONG,
    fc.GIA_THANG3,
    fc.GIA_THANG7,
    fc.giabaohiem,
    fc.mabaocao_byt,
    fc.GIABAOHIEM_TT39,
    fc.GIACOBH_TT37_tt39
UNION ALL
select
    p_dvtt,
    p_sovaovien,
    p_sovaovien_dt,
    ct.MABENHNHAN,
    v_tenbenhnhan,
    v_diachi,
    v_mabenhan,
    v_sothebhyt,
    v_tylebh,
    v_ngaybatdau,
    v_ngayketthuc,
    v_ngayvaovien,
    decode(v_ngayravien,null,to_date('2000-01-01','yyyy-mm-dd'),v_ngayravien),
    v_ngaysinh,
    xncha.PHONG_CHI_DINH,
    '' TENPHONGBAN_CD,
    v_ttphieuthanhtoan,
    v_tenphongban,
    v_maphongban,
    XNCHA.NGAY_CHI_DINH,
    CT.NGAY_THUC_HIEN,
    XNCHA.NGUOI_CHI_DINH,
    xncha.BAC_SI_DIEU_TRI,
    XNCHA.SO_PHIEU_cdha,
    to_char(CT.BACSI_THUCHIEN),
    xetnghiem.MA_CDHA,
    xetnghiem.ten_cdha,
    'CDHA',
    CASE WHEN CT.BHYTKCHI = 1 THEN CT.DON_GIA_KO_BHYT ELSE CT.DON_GIA_BHYT END
        ,
    CT.SO_LUONG,
    CASE WHEN CT.BHYTKCHI = 1 THEN CT.THANH_TIEN_KO_BHYT ELSE CT.THANH_TIEN_BHYT END
        ,
    CT.BHYTKCHI,
    NULL,
    NULL,
    '',
    '',
    '',
    to_char( REGEXP_REPLACE(ct.mo_ta,'&nbsp;', '')),
    v_icd1||';'||v_icd,
    v_gioitinh,
    xetnghiem.GIA_THANG3,
    xetnghiem.GIA_THANG7,
    xetnghiem.giabaohiem,
    v_loaikcb, v_noitinh,v_kyhieugroup,
    xetnghiem.mabaocao_byt,v_noidangkybandau,v_trangthai,
    xetnghiem.GIABAOHIEM_TT39,
    xetnghiem.GIACOBH_TT37_tt39,0,
    '0' ma_phongban,
    '' ten_phongban,
    CMU_GETTENFILMXQUANG(ct.dvtt, ct.sovaovien, ct.so_phieu_CDHA, ct.MA_CDHA, 1),
    CMU_GETSOLUONGFILMXQUANG(ct.dvtt, ct.sovaovien, ct.so_phieu_CDHA, ct.MA_CDHA, 1)
FROM KB_CD_CDHA_CT ct INNER JOIN
     KB_CD_CDHA xncha on ct.dvtt = xncha.dvtt
         and ct.sovaovien = xncha.sovaovien  and xncha.so_phieu_CDHA = ct.so_phieu_CDHA
                      inner join CLS_CDHA xetnghiem
                                 on xetnghiem.dvtt = ct.dvtt and xetnghiem.MA_CDHA = ct.MA_CDHA
where ct.dvtt = p_dvtt and ct.sovaovien = p_sovaovien
  AND (CT.DAYDULIEU_VAONOITRU is null or CT.DAYDULIEU_VAONOITRU = 0)
UNION ALL
select
    p_dvtt,
    p_sovaovien,
    p_sovaovien_dt,
    ct.MABENHNHAN,
    v_tenbenhnhan,
    v_diachi,
    v_mabenhan,
    v_sothebhyt,
    v_tylebh,
    v_ngaybatdau,
    v_ngayketthuc,
    v_ngayvaovien,
    decode(v_ngayravien,null,to_date('2000-01-01','yyyy-mm-dd'),v_ngayravien),
    v_ngaysinh,
    xncha.PHONG_CHI_DINH,
    '' TENPHONGBAN_CD,
    v_ttphieuthanhtoan,
    v_tenphongban,
    v_maphongban,
    XNCHA.NGAY_CHI_DINH,
    ct.NGAY_GIO_PTTT,
    XNCHA.NGUOI_CHI_DINH,
    xncha.BAC_SI_DIEU_TRI,
    XNCHA.SO_PHIEU_DICHVU,
    ct.BACSI_PTTT || '!!!'||ct.BACSI_GAYME,
    xetnghiem.MA_DV,
    xetnghiem.ten_DV,
    case when LOAI_DV = 'E' THEN 'CONGKHAM' ELSE 'TTPT' END,
    CASE WHEN CT.BHYTKCHI = 1 THEN CT.DON_GIA_KO_BHYT ELSE CT.DON_GIA_BHYT END
        ,
    CT.SO_LUONG,
    CASE WHEN CT.BHYTKCHI = 1 THEN CT.THANH_TIEN_KO_BHYT ELSE CT.THANH_TIEN_BHYT END
        ,
    CT.BHYTKCHI,
    NULL,
    NULL,
    CT.PHUONGPHAP_VOCAM||'!!!'||CT.PHUONGPHAP_TT_PT,
    '',
    '',
    to_char(ct.ket_qua),
    v_icd1||';'||v_icd ,
    v_gioitinh,
    xetnghiem.GIA_THANG3,
    xetnghiem.GIA_THANG7,
    xetnghiem.giabaohiem,
    v_loaikcb, v_noitinh,v_kyhieugroup,
    xetnghiem.mabaocao_byt,v_noidangkybandau,v_trangthai,
    xetnghiem.GIABAOHIEM_TT39,
    xetnghiem.GIACOBH_TT37_tt39,0,
    khoa.ma_phongban,
    khoa.ten_phongban,
    '' tenfilm,
    null SOLUONG_FILM
FROM KB_CD_DICHVU_CT ct INNER JOIN
     KB_CD_DICHVU xncha on ct.dvtt = xncha.dvtt
         and ct.sovaovien = xncha.sovaovien
         and xncha.so_phieu_DICHVU = ct.so_phieu_DICHVU
                        inner join DM_DICH_VU_KHAM xetnghiem
                                   on xetnghiem.dvtt = ct.dvtt and xetnghiem.MA_DV = ct.MA_DV
                        left join his_fw.dm_nhanvien             nv on ct.NGUOI_THUC_HIEN = nv.ma_nhanvien
                        left join his_fw.dm_phongban             khoa on nv.ma_phongban = khoa.ma_phongban
where ct.dvtt = p_dvtt and ct.sovaovien = p_sovaovien
  AND (CT.DAYDULIEU_VAONOITRU is null or CT.DAYDULIEU_VAONOITRU = 0)
/* UNION ALL
 select
   p_dvtt,
   p_sovaovien,
   p_sovaovien_dt,
   ct.MABENHNHAN,
   v_tenbenhnhan,
   v_diachi,
   v_mabenhan,
   v_sothebhyt,
   v_tylebh,
   v_ngaybatdau,
   v_ngayketthuc,
   v_ngayvaovien,
   decode(v_ngayravien,null,to_date('2000-01-01','yyyy-mm-dd'),v_ngayravien),
   v_ngaysinh,
   TO_NUMBER(CT.MA_PHONG_CHIDINH),
   '' TENPHONGBAN_CD,
   v_ttphieuthanhtoan,
   v_tenphongban,
   v_maphongban,
   CT.NGAY_RA_TOA,
   NULL,
   CT.MA_BAC_SI_THEMTHUOC,
   NULL,
   ma_toa_thuoc||'--'||stt_toathuoc,
   '',
   ct.mavattu,
   ct.ten_vat_tu,
   CT.KYHIEUNHOMBC,
   DONGIA_BAN_BV
   ,
   SO_LUONG_THUC_LINH,
   SO_LUONG_THUC_LINH*DONGIA_BAN_BV,
   CT.NGOAI_DANH_MUC,
   NULL,
   NULL,
   ct.nghiep_vu,
   TO_CHAR(ct.MA_DV),
   nvl(REGEXP_SUBSTR(CT.GHI_CHU_CT_TOA_THUOC,'[^-]+',1,1),''),
   nvl(REGEXP_SUBSTR(CT.GHI_CHU_CT_TOA_THUOC,'[^-]+',1,2),''),
   v_icd1||';'||v_icd ,
   v_gioitinh,0,0,0,
   v_loaikcb, v_noitinh,v_kyhieugroup,
   SO_DANG_KY,v_noidangkybandau,v_trangthai,
           0,0,0,
           '0' ma_phongban,
           '' ten_phongban
 FROM KB_CHI_TIET_TOA_THUOC CT
 where ct.dvtt = p_dvtt and ct.sovaovien = p_sovaovien
       and CT.GHI_CHU_CT_TOA_THUOC like 'CD%'*/
UNION ALL
select
    p_dvtt,
    p_sovaovien,
    p_sovaovien_dt,
    v_mabenhnhan,
    v_tenbenhnhan,
    v_diachi,
    v_mabenhan,
    v_sothebhyt,
    v_tylebh,
    v_ngaybatdau,
    v_ngayketthuc,
    v_ngayvaovien,
    decode(v_ngayravien,null,to_date('2000-01-01','yyyy-mm-dd'),v_ngayravien),
    v_ngaysinh,
    TO_NUMBER(CT.ma_phong_benh_cd_tbki),
    '' TENPHONGBAN_CD,
    v_ttphieuthanhtoan,
    v_tenphongban,
    v_maphongban,
    null NGAY_RA_TOA,
    NULL,
    NULL MA_BAC_SI_THEMTHUOC,
    NULL,
    null,
    '',
    to_number(ct.ma_dv),
    dv.ten_dv,
    'CONGKHAM',
    ct.don_gia
        ,
    1,
    ct.don_gia,
    CT.NGOAI_DANH_MUC,
    NULL,
    NULL,
    '',
    TO_CHAR(ct.MA_DV),
    '',
    '',
    v_icd1||';'||v_icd ,
    v_gioitinh,
    dv.GIA_THANG3,
    dv.GIA_THANG7,
    dv.giabaohiem,
    v_loaikcb, v_noitinh,v_kyhieugroup,
    dv.mabaocao_byt,v_noidangkybandau,v_trangthai,
    dv.GIABAOHIEM_TT39,
    dv.GIACOBH_TT37_tt39,0,
    '0' ma_phongban,
    '' ten_phongban,
    '' tenfilm,
    null SOLUONG_FILM
FROM TEM_BANGKE_IN CT
         inner join DM_DICH_VU_KHAM dv on ct.dvtt = dv.dvtt and ct.ma_dv = dv.ma_dv
where ct.dvtt = p_dvtt and ct.sovaovien = p_sovaovien
  and lan_chuyen_phong = 0 and ghi_chu = 'CONGKHAM'
  and v_trangthai != 7
union ALL
select
    p_dvtt,
    p_sovaovien,
    p_sovaovien_dt,
    v_mabenhnhan,
    v_tenbenhnhan,
    v_diachi,
    v_mabenhan,
    v_sothebhyt,
    v_tylebh,
    v_ngaybatdau,
    v_ngayketthuc,
    v_ngayvaovien,
    decode(v_ngayravien,null,to_date('2000-01-01','yyyy-mm-dd'),v_ngayravien),
    v_ngaysinh,
    TO_NUMBER(CT.ma_phong_benh),
    '' TENPHONGBAN_CD,
    v_ttphieuthanhtoan,
    v_tenphongban,
    v_maphongban,
    null NGAY_RA_TOA,
    NULL,
    NULL MA_BAC_SI_THEMTHUOC,
    NULL,
    null,
    'CONGKHAM_CHUYENPHONG_'||LANCHUYEN,
    to_number(ct.ma_dv),
    ct.ten_dv,
    'CONGKHAM',
    ct.gia_dv
        ,
    SOLUONG,
    ct.gia_dv*SOLUONG,
    0,
    NULL,
    NULL,
    '',
    TO_CHAR(ct.MA_DV),
    '',
    '',
    v_icd1||';'||v_icd ,
    v_gioitinh,
    dv.GIA_THANG3,
    dv.GIA_THANG7,
    dv.giabaohiem,
    v_loaikcb, v_noitinh,v_kyhieugroup,
    dv.mabaocao_byt,v_noidangkybandau,v_trangthai,
    dv.GIABAOHIEM_TT39,
    dv.GIACOBH_TT37_tt39,0,
    '0' ma_phongban,
    '' ten_phongban,
    '' tenfilm,
    null SOLUONG_FILM
FROM KB_CHUYENPK_THUTIEN_TT37 CT
         inner join DM_DICH_VU_KHAM dv on ct.dvtt = dv.dvtt and ct.ma_dv = dv.ma_dv
where ct.dvtt = p_dvtt and ct.sovaovien = p_sovaovien
  and v_trangthai != 7
;
END;
