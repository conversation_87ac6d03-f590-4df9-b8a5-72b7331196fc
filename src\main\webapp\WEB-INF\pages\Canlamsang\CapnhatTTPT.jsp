<%@ page import="l2.ThamSoManager" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="google-site-verification" content="u6uNEfD4cb3gidezi4r_6aI8Wb1E07-ufBeCQpvmlqQ"/>
    <title>Cập nhật Kết Quả Thủ Thuật - Phẫu Thuật</title>
    <link rel="icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
    <link rel="shortcut icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>

    <!-- jQuery file -->
    <link href="<c:url value="/resources/css/divheader.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/css/style_new.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/css/magiczoomplus.css" />" rel="stylesheet"/>

    <!--Jquery-->
    <link rel="stylesheet" href="<c:url value="/resources/css/jquery-ui-redmond.1.9.1.css" />"/>
    <script src="<c:url value="/resources/js/jquery.min.1.8.3.js" />"></script>
    <script src="<c:url value="/resources/js/jquery-ui.1.9.1.js" />"></script>
    <!--Grid-->
    <link href="<c:url value="/resources/jqgrid/css/ui.jqgrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/jqgrid/js/i18n/grid.locale-en.js" />"></script>
    <script src="<c:url value="/resources/jqgrid/js/jquery.jqGrid.src.js" />"></script>
    <script src="<c:url value="/resources/js/common_function.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <script src="<c:url value="/resources/js/jquery.inputmask.bundle.min.js" />"></script>
    <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
    <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/dialog/jquery.alerts.1.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/dialog/jquery.alerts.js" />"></script>
    <script src="<c:url value="/resources/js/read_file.js" />"></script>
    <link href="<c:url value="/resources/dialog/jBox.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/dialog/jBox.js" />"></script>

    <link rel="stylesheet" href="<c:url value="/resources/camau/css/select2.min.css" />" />
    <script src="<c:url value="/resources/camau/js/select2.min.js" />"></script>

    <link href="<c:url value="/resources/combogrid/css/smoothness/jquery.ui.combogrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/combogrid/plugin/jquery.ui.combogrid-1.6.3.js" />"></script>
    <script src="<c:url value="/resources/ckeditor/ckeditor.js" />"></script>
    <script src="<c:url value="/resources/ckeditor/adapters/jquery.js" />"></script>
    <script src="<c:url value="/resources/webcam/say-cheese.js" />"></script>
    <link href="<c:url value="/resources/webcam/pygments.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/webcam/say-cheese.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
    <script src="<c:url value="/resources/js/magiczoomplus.js" />"></script>
    <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/camau/material/moment.js" />"></script>
    <style>
        .width1 {
            width: 200px;
        }

        .width2 {
            width: 550px;
        }

        .width3 {
            width: 150px;
        }

        .width_100per {
            width: 100%;
        }

        span.cellWithoutBackground {
            display: block;
            background-image: none;
            margin-right: -2px;
            margin-left: -2px;
            height: 14px;
            padding: 4px;
        }

        tr td * {
            font-size: 12px;
        }

        .table {
            border-collapse: collapse;
            text-align: left;
            width: 100%;
        }

        .ui-widget-content .bncapcuuClass {
            color: red;
            weight-font: bold;
            background-image: none;
        }

        .ui-widget-content .treemClass {
            color: #00ff00;
            weight-font: bold;
            background-image: none;
        }

        .ui-widget-content .kobhytClass {
            color: #bf00ff;
            weight-font: bold;
            background-image: none;
        }

        .ui-widget-content .vienphiClass {
            color: #EE7600;
            weight-font: bold;
            background-image: none;
        }
    </style>
    <script>
        var dalBBduyetphauthuat;
        var iddieutri;
        var sovaovien;
        var sovaovien_noi;
        var sovaovien_dt_noi;
        var giothuchien_cls_timer_is_on = false;
        var giopttt_timer_is_on = false;
        var giothuchien_cls_timer;
        var giopttt_timer;
        var showtime_giothuchien_cls_cancel = 0;
        var showtime_giopttt_cancel = 0;
        var dinhmuc_dialog;
        var lichmo_dialog;
        var sophieu;
        var noitru;
        var mabenhnhan;
        var stt_dieutri;
        var stt_benhan;
        var stt_dotdieutri;//VLG chinh textbox gio tra kq chay thoi gian
        var ngaykham_hd = convertStr_MysqlDate("${ngayhientai}");
        var bant;
        var phongchidinh;
        var cmu_nguoi_thuc_hien = 0;
        var loaiTTPT = "";
        var ngay_chi_dinh;
        var ngaychidinh;
        var giochidinh;
        var SESS_PHONG_BAN = "<%= session.getAttribute("Sess_PhongBan").toString()%>";
        var SESS_USER_ID = "<%= session.getAttribute("Sess_UserID").toString()%>";
        var THAMSO_828449 = "<%=ThamSoManager.instance(session).getThamSoString("828449", "0")%>";

        function checkNguoiDocKetQua() {
            var _selector = $('#cboNguoiDocKetQua');
            var countOption =  _selector.find("option[value='"+SESS_USER_ID+"']").length;
            return !!!_selector.val() || countOption==0 //|| _selector.find(':selected').data("khoa") != SESS_PHONG_BAN
                ;
        }

        if("${Sess_DVTT}" == "96029"){
            $.get('cmu_list_CMU_DSNHANVIENTOANBV?url=' + convertArray([${Sess_DVTT}])).done(function(data) {
                var $select = $('#cboNguoiDocKetQua');
                $select.empty();
                data.forEach(function(obj) {
                    $select.append("<option value='" + obj.MA_NHANVIEN + "'>" + obj.TEN_NHANVIEN + "</option>");
                });
                $select.select2();
            });
        } else {

            function setNguoiDocKetQua(maBacSi) {
                var _selector = $('#cboNguoiDocKetQua');
                _selector.val(maBacSi||SESS_USER_ID);
            }

            function loadBacSiTheoKhoa(strListMaKhoa = "-1") {
                // Fetch the preselected item, and add to the control
                var _selector = $('#cboNguoiDocKetQua');
                $.ajax({
                    type: 'GET',
                    url: 'cmu_getlist?url='+convertArray(["${Sess_DVTT}",strListMaKhoa,"SEL_LIST_BAC_SI_THEO_KHOA"]),
                }).then(function (data) {
                    if(jQuery.isArray(data) ) {
                        _selector.empty();
                        // $('#cboDoiTuongTiepNhan').append($('<option>', { value: '0', text : 'Khác', color: '' }));
                        _selector.append($('<option>', {
                            value: SESS_USER_ID,
                            text : "${Sess_User}",
                            "data-khoa": SESS_PHONG_BAN
                        }));
                        $.each(data, function (i, item) {
                            if(!!item && item.MA_NHANVIEN && item.TEN_NHANVIEN && item.MA_PHONGBAN && item.MA_NHANVIEN != SESS_USER_ID) {
                                _selector.append($('<option>', {
                                    value: item.MA_NHANVIEN,
                                    text : item.TEN_NHANVIEN,
                                    "data-khoa": item.MA_PHONGBAN
                                }));
                            }
                        });
                        _selector.val(SESS_USER_ID);
                        if(!!!_selector.val()) {
                            // jAlert("Tài khoản đăng nhập không thuộc khoa đã cấu hình", "Cảnh báo");
                        }
                    }
                }).always(function( data, textStatus, jqXHR ) {

                });
            }
        }

        function luuNguoiDocKetQua(_soPhieu, _soVaoVien, _soVaoVienDt = 0, _noiTru = 0, _listMaCls = "-1") {
            $.ajax({
                type: 'POST',
                url: 'update-thong-tin-theo-benh-nhan',
                async: false,
                data: {
                    P_LOG_USERID: "${Sess_UserID}",
                    p_dvtt: "${Sess_DVTT}",
                    p_soPhieu: _soPhieu,
                    p_soVaoVien: _soVaoVien,
                    p_soVaoVienDt: _soVaoVienDt,
                    p_noiTru: _noiTru,
                    p_sessKhoaId: SESS_PHONG_BAN,
                    p_nguoiDocKetQua: $('#cboNguoiDocKetQua').val(),
                    p_listMaCls: _listMaCls,
                    p_loai_cls: 'TTPT',
                    action: "UPD_CLS_NGUOI_DOC_KET_QUA"
                }
            }).fail(function(data){
                jAlert("Lỗi cập nhật người đọc kết quả", "Cảnh báo");
            }).then(function (data) {
                console.log(data);
            }).always(function( data, textStatus, jqXHR ) {

            });
        }

        function getNguoiDocKetQua(_soPhieu, _soVaoVien, _soVaoVienDt = 0, _noiTru = 0, _listMaCls = "-1") {
            $.ajax({
                type: 'GET',
                url: 'select-thong-tin-theo-benh-nhan',
                data: {
                    P_LOG_USERID: "${Sess_UserID}",
                    p_dvtt: "${Sess_DVTT}",
                    p_soPhieu: _soPhieu,
                    p_soVaoVien: _soVaoVien,
                    p_soVaoVienDt: _soVaoVienDt,
                    p_noiTru: _noiTru,
                    p_listMaCls: _listMaCls,
                    p_loai_cls: 'TTPT',
                    action: "SEL_CLS_NGUOI_DOC_KET_QUA"
                }
            }).then(function (data) {
                if(!!data && !!data[0]){
                    var obj = data[0];
                    setNguoiDocKetQua(obj.MA_NHANVIEN);
                }
            }).always(function( data, textStatus, jqXHR ) {

            });
        }
        function addZero(i) {
            if (i < 10) {
                i = "0" + i;
            }
            return i;
        }

        function createJson() {
            var jsondata = {};
            var alertString = null;
            $("form#form_Ekip").find(".post-data").each(function (index, element) {
                var tag = $(element);
                var type = tag.attr("type");
                var key = tag.attr("key");
                var require = tag.attr("required");
                var value = tag.val();
                if (require == true) {
                    alertString = tag.attr("error-message").val();
                }
                if (alertString != null) {
                    jAlert(alertString, "Cảnh báo");
                    return;
                }
                jsondata[key] = value;
            })
            return jsondata;
        }
        ;


        function showtime_giothuchien_cls() {
            var thoigian = new Date();
            var gio = addZero(thoigian.getHours());
            var phut = addZero(thoigian.getMinutes());
            var giay = addZero(thoigian.getSeconds());
            /*if(showtime_giothuchien_cls_cancel !== 1) {
             $('#giothuchien_cls').val(gio + ":" + phut + ":" + giay);
             }
             t = setTimeout(showtime_giothuchien_cls, 1000);*/
            $('#giothuchien_cls').val(gio + ":" + phut + ":" + giay);
            giothuchien_cls_timer = setTimeout(showtime_giothuchien_cls, 1000);
            giothuchien_cls_timer_is_on = true;
        }

        function showtime_giopttt() {
            var thoigian = new Date();
            var gio = addZero(thoigian.getHours());
            var phut = addZero(thoigian.getMinutes());
            var giay = addZero(thoigian.getSeconds());
            /*if(showtime_giopttt_cancel !== 1) {
             $('#giopttt').val(gio + ":" + phut + ":" + giay);
             }
             t = setTimeout(showtime_giopttt, 1000);*/
            $('#giopttt').val(gio + ":" + phut + ":" + giay);
            giopttt_timer = setTimeout(showtime_giopttt, 1000);
            giopttt_timer_is_on = true;
        }

        function stopCount() {
            clearTimeout(t);
            timer_is_on = 0;
        }

        function stopGioThucHienClsTimer() {
            clearTimeout(giothuchien_cls_timer);
            giothuchien_cls_timer_is_on = false;
        }

        function stopGioPtttTimer() {
            clearTimeout(giopttt_timer);
            giopttt_timer_is_on = false;
        }

        function gioThucHienClsTimerChange() {
            if (giothuchien_cls_timer_is_on || ($("#daxetnghiem").prop('checked') && ${capnhat_cls_timer_off} == 1))
                stopGioThucHienClsTimer();
            else
                showtime_giothuchien_cls();
        }

        function gioPtttTimerChange() {
            if("${Sess_DVTT}"=="14017" || "${Sess_DVTT}".substring(0,2) == "96"){
                stopGioPtttTimer();
            }else
            if (giopttt_timer_is_on || ($("#giopttt").data('da-thuc-hien') && ${capnhat_cls_timer_off} == 1))
                stopGioPtttTimer();
            else
                showtime_giopttt();
        }

        //VLG chinh textbox gio tra kq chay thoi gian

        function showUploadDialog() {
            var madv = $("#madv").val();
            var sophieu = $("#sophieu").val();
            var noitru = $("#noitru").val();
            var makhambenh = $("#makhambenh").val();
            var sttbenhan = $("#sttbenhan").val();
            var sttdotdieutri = $("#sttdotdieutri").val();
            var sttdieutri = $("#sttdieutri").val();

            var postData = {
                maDichVu: madv,
                soPhieu: sophieu,
                noiTru: noitru,
                maKhamBenh: makhambenh,
                sttBenhAn: sttbenhan,
                sttDotDieuTri: sttdotdieutri,
                sttDieuTri: sttdieutri
            };
            if (madv != "" && sophieu != "") {
                var params = {
                    URL: "ttpt_insert_file",
                    DATA: postData
                }
                dialog_upload.data(params).dialog('open');
            }
        }

        // STG

        function chamcong_ekip(dvtt, stt_benhan, mabenhnhan, ma_dv, ten_dv, sophieu) {
            $("#madichvu").val(ma_dv);
            $("#tendichvu").val(ten_dv);
            $("#tiencongthuchien").empty();
            $.getJSON("bdh_maloaitiencong_theoloaipttt", {loai: " ", thamso: ma_dv}, function (data) {
                $("<option value='-1'>--  Chọn vai trò  --</option>").appendTo("#tiencongthuchien");
                if (data && data.length > 0) {
                    $.each(data, function (i) {
                        $("<option value='" + data[i].MA_TIENCONG + "'>" + data[i].TEN_VAITRO + "</option>").appendTo("#tiencongthuchien");
                    });
                }
            });
            $("#ekip_mau").empty();
            $.getJSON("bdh_maloaiekip_theoloaipttt", {ma_dv: ma_dv}, function (data) {
                $("<option value='-1'>--  Chọn ekip  --</option>").appendTo("#ekip_mau");
                if (data && data.length > 0) {
                    $.each(data, function (i) {
                        $("<option value='" + data[i].MA_EKIP + "'>" + data[i].TEN_EKIP + "</option>").appendTo("#ekip_mau");
                    });
                }
            });
            var ekip_thuchien = new jBox('Modal', {
                title: 'Thực hiện chấm công phẫu thuật - thủ thuật',
                overlay: false,
                content: $('#ekip'),
                draggable: 'title'
            });
            if (dvtt != "" && stt_benhan != "" && mabenhnhan != "" && ma_dv != "" && ten_dv != "" && sophieu != "") {
                var arr = [dvtt, stt_benhan, mabenhnhan, ma_dv, sophieu];
                var url = 'bdh_chamcongthuchien_select_theobenhnhan?url=' + convertArray(arr);
                $("#list_chitiettiencongthuchien").jqGrid('setGridParam', {
                    datatype: 'json',
                    url: url
                }).trigger('reloadGrid');
                ekip_thuchien.open();
            }
        }//End VNPT Binh Dinh
        function reload_grid() {
            var ngay = convertStr_MysqlDate($("#ngaythuchien").val());
            var dvtt = "${Sess_DVTT}";
            var phong = "${Sess_Phong}";
            // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT
            var phongban = $("#phongban").val();
            var phongbenh = $("#phongbenh").val();
            var doituong = $("#doituong").val();
            // End ĐắkLắk
            var daxetnghiem = $("#daxetnghiem").prop('checked');
            if (daxetnghiem == true) {
                daxn = 1;
            } else {
                daxn = 0;
            }

            // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT
            var arr = [dvtt, ngay, phong, daxn, phongban, phongbenh, doituong];
            //------HPG -Tim kiem benh nhan CLS tu ngay den ngay
            if ("${timkiem_cls}" == "1") {
                var tungay = convertStr_MysqlDate($("#tungay").val());
                arr = [dvtt, tungay, ngay, phong, daxn, phongban, phongbenh, doituong];
            }
            //--End HPG

            var url = 'ttpt_ds_benhnhan_cothamso?url=' + convertArray(arr);
            $("#list_benhnhan").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');

        }

        function hienthi_them_cls(url) {
            $.ajax({
                url: url
            }).done(function (data) {
                if (data.length > 0) {
                    $('#trieuchungls').val(data[0].TRIEUCHUNGLS);
                    $('#benhtheobs').val(data[0].TEN_BENH_THEOBS);
                    // STG
                    if (data[0].NGAY_TRA_KETQUA == null) {
                        $("#ngaythuchien_cls").val("${ngayhientai}");
                    } else {
                        $("#ngaythuchien_cls").val(data[0].NGAY_TRA_KETQUA);
                    }

                    if (data[0].GIO_TRA_KETQUA == null || !$("#daxetnghiem").prop('checked')) {
                        if (!giothuchien_cls_timer_is_on)
                            showtime_giothuchien_cls();
                    } else {
                        stopGioThucHienClsTimer();
                        $("#giothuchien_cls").val(data[0].GIO_TRA_KETQUA);
                    }
                    /*if(data[0].GIO_TRA_KETQUA == null) {
                     showtime_giothuchien_cls_cancel = 0;
                     } else {
                     showtime_giothuchien_cls_cancel = 1;
                     $("#giothuchien_cls").val(data[0].GIO_TRA_KETQUA);
                     }*/
                    // STG

                    if (data[0].TT_THANHTOAN == "0")
                        $('#tt_thanhtoan').val("Chưa thanh toán");
                    else
                        $('#tt_thanhtoan').val("Đã thanh toán");
                } else {
                    $('#trieuchungls').val("");
                    $('#benhtheobs').val("");
                    //    $('#ngaythuchien_cls').val("");
                    $('#ngaythuchien_cls').val("${ngayhientai}");
                    $('#giothuchien_cls').val("${giohientai}");
                    $('#tt_thanhtoan').val("");
                }
            });
        }

        function kiemtra_tt_cls(url) {
            var x = 0;
            $.ajax({
                url: url,
                async: false
            }).done(function (data) {
                if (data == "2")
                    x = 1;
                else
                    x = 0;
            });
            return x;
        }

        function update_pttt_lenlich() {
            var lenlich = $("#phauthuat_lenlich").prop("checked");
            var sophieu = $("#sophieu").val();
            var sovaovien = $("#sovaovien").val();
            if (sophieu == "" || sovaovien == "")
                return;
            $.ajax({
                type: "POST",
                url: "pttt_update_lenlich",
                data: {sophieu: sophieu, lenlich: lenlich ? 1 : 0}
            }).done(function () {
                jAlert("Cập nhật thành công", "Thông báo");
                $("#lammoi").click();
            }).fail(function () {
                jAlert("Cập nhật không thành công", "Thông báo");
            });
        }

        function showDinhMucDialog(maDichVu) {
            var noiTru = $("#noitru").val() == 1;
            if (dinhmuc_dialog) {
                var params = {
                    MABENHNHAN: $("#mabenhnhan").val(),
                    NOI_TRU: noiTru,
                    BHYT: $("#sothebhyt").val().trim() ? 1 : 0,
                    STT_BENHAN: stt_benhan,
                    STT_DOTDIEUTRI: stt_dotdieutri,
                    SOVAOVIEN: noiTru ? sovaovien_noi : sovaovien,
                    SOVAOVIEN_DT: sovaovien_dt_noi,
                    STT_DIEUTRI: stt_dieutri,
                    MAKHAMBENH: $("#makhambenh").val(),
                    MA_DV: maDichVu,
                    SO_PHIEU_CLS: $("#sophieu").val()
                };
                dinhmuc_dialog.data(params).dialog('open');
            } else {
                jAlert("Hiện tại không thể sử dụng chức năng này", 'Thông báo');
            }
        }

        $(function () {

            if ("${Sess_DVTT}" == "80010") {
                $("#ngaytiepnhan").css("display", "");
            }

            lichmo_dialog = $("#dialog_lichmo").dialog({
                autoOpen: false,
                width: 1000,
                modal: true,
                resizable: false,
                position: {my: "center", at: "center", of: window}
            });

            $("#lichmo").click(function (evt) {
                var id1 = $("#list_ttpt_bhyt").jqGrid('getGridParam', 'selrow');
                var id2 = $("#list_benhnhan").jqGrid('getGridParam', 'selrow');
                if (id1) {
                    var ret1 = $("#list_benhnhan").jqGrid('getRowData', id2);
                    var noitru = $("#noitru").val();
                    if (noitru == "0") {
                        $("#sovaovien_lm").val(sovaovien);
                        nghiepvu = "ngoaitru_toadichvu";
                    } else {
                        $("#sovaovien_lm").val(sovaovien_noi);
                        nghiepvu = "noitru_toadichvu";
                    }
                    ;
                    $("#sttbenhan_lm").val(ret1.STT_BENHAN);
                    $("#sttdieutri_lm").val(ret1.STT_DOTDIEUTRI);
                    $("#sttdotdieutri_lm").val(ret1.STT_DOTDIEUTRI);
                    var ret = $("#list_ttpt_bhyt").jqGrid('getRowData', id1);
                    $("#tendv_lm").val(ret.TEN_DV);
                    $("#madv_lm").val(ret.MA_DV);
                    $("#maphongban").val(ret1.MA_PHONGBAN);
                    lichmo_dialog.dialog("open");


                    var sophieu = $("#sophieu").val();
                    var sttbenhan = $("#sttbenhan").val();
                    var sttdotdieutri = $("#sttdotdieutri").val();
                    var sttdieutri = $("#sttdieutri").val();
                    var makhambenh = $("#makhambenh").val();
                    var madv = $("#madv").val();
                    var dvtt = "${Sess_DVTT}";
                    var arrkt = [$("#sovaovien_lm").val(), sophieu, dvtt, madv]
                    var urlkt = "kiemtra_dalenlich?url=" + convertArray(arrkt);
                    $.ajax({
                        url: urlkt
                    }).done(function (data) {
                        if (data == "1") {

                            toggle("xem");
                        } else toggle("load");
                    });
                    var arr = [sophieu, noitru, sttbenhan, sttdotdieutri, sttdieutri, makhambenh, madv, dvtt, "0", sovaovien, sovaovien_noi, sovaovien_dt_noi];
                    var url3 = "pttt_select_ketqua_svv?url=" + convertArray(arr);
                    $.getJSON(url3, function (result) {
                        $.each(result, function (i, field) {
                            $("#frm_kq_id_ekip").val(field.ID_EKIP);
                            $("#frm_kq_so_phieu_dichvu").val(field.SO_PHIEU_DICHVU);
                            $("#sophieudv_lm").val(field.SO_PHIEU_DICHVU);
                            $("#frm_kq_ma_dv").val(field.MA_DV);
                            $("#frm_kq_mabenhnhan").val(field.MABENHNHAN);
                            $("#frm_kq_sovaovien").val(field.SOVAOVIEN);
                            $("#frm_kq_sovaovien_dt").val(field.SOVAOVIEN_DT);
                            $("#frm_kq_noitru").val(field.NOITRU);
                            $("#frm_kq_id_dieutri").val(field.ID_DIEUTRI);
                            $("#id_dieutri_lm").val(field.ID_DIEUTRI);
                            $("#sophieudv_lm").val(field.SO_PHIEU_DICHVU);
                        })
                    })
                } else jAlert("Chưa chọn dịch vụ chỉ định!", "Cảnh báo");
            });

            $("#loaddinhmuc").click(function (evt) {
                var id = $("#list_ttpt_bhyt").jqGrid('getGridParam', 'selrow');
                var maDichVu = $("#list_ttpt_bhyt").jqGrid('getCell', id, 'MA_DV');
                if (id && maDichVu) {
                    showDinhMucDialog(maDichVu);
                } else {
                    jAlert("Vui lòng chọn một chỉ định để sử dụng định mức vật tư", 'Cảnh báo');
                }
            });

            $("#phongban").val("${Sess_Khoa}");
            $("#phongbenh").val("${Sess_Phong}");
            $("#ngaythuchien_cls").datepicker();
            $("#ngaythuchien_cls").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngaythuchien_cls").val("${ngayhientai}");
            $("#giothuchien_cls").val("${giohientai}");

            $("#ngaychidinh_cls").datepicker();
            $("#ngaychidinh_cls").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngaychidinh_cls").val("${ngayhientai}");
            $("#giochidinh_cls").val("${giohientai}");

            $(":input").inputmask();
            $("#ngaythuchien").datepicker();
            $("#ngaypttt").datepicker();
            $("#ngaypttt").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngaypttt").val("${ngayhientai}");
            $("#thoigiankt").datepicker();
            $("#thoigiankt").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#thoigiankt").val("${ngayhientai}");
            $("#ngaythuchien").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngaythuchien").val("${ngayhientai}");
            // HPG--Timkiem benh nhan CLS tu ngay den ngay
            if ("${timkiem_cls}" == "1") {
                $(".hpg_tmp").show();
                $("#tungay").datepicker();
                $("#tungay").datepicker("option", "dateFormat", "dd/mm/yy");
                $("#tungay").val("${ngayhientai}");
                $("#tungay").change(function (evt) {
                    reload_grid();
                });
            } else $(".hpg_tmp").hide();
            //--
            // ĐắkLắk: tùy chọn hiển thị lọc theo khoa, phòng, đối tượng
            if ("${choloctheokhoaphong_capnhatketquacls}" == "1")
                $(".dlk_tmp").show();
            else $(".dlk_tmp").hide();
            $("#phongban").val("-1");
            $("#phongbenh").val("-1");
            $("#doituong").val("-1");
            // End ĐắkLắk
            if ("${hienthi_mausac_bn}" == "1")
                $(".ghichutrangthai").show();
            else
                $(".ghichutrangthai").hide();
            //-------
            if ("${hienthi_them_cls}" == "1")
                $(".hpg_hienthithem").show();
            else
                $(".hpg_hienthithem").hide();
            //--End HPG
            $('textarea#trinhtupttt').ckeditor();
            $('textarea#luotdottpt').ckeditor();
//            $('textarea#catchisau7ngay').ckeditor();
            $("#list_benhnhan").jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 345,
                width: 300,
                rowattr: function (rd) {
                    if ("${hienthi_mausac_bn}" == "1") {
                        if (rd.CAPCUU === "1") {
                            return {"class": "bncapcuuClass"};
                        } else if (!rd.SOTHEBHYT && rd.DA_THANH_TOAN == 0) {
                            return {"class": "kobhytClass"};
                        } else if (!rd.SOTHEBHYT && rd.DA_THANH_TOAN == 1) {
                            return {"class": "vienphiClass"};
                        } else if (rd.TUOI.indexOf("tháng") != -1) {
                            return {"class": "treemClass"};
                        }
                    }
                },
                colNames: ["STT", "Mã y tế", "Họ tên", "TENBENHNHAN", "Tuổi", "Nội trú", "gioitinh", "diachi", "sobhyt", "SO_PHIEU", "MA_KHAM_BENH", "stt_benhan", "stt_dotdieutri",
                    "stt_dieutri", "NGUOI_CHI_DINH", "PHONG_CHI_DINH", "MA_PHONG_XN", "KET_LUAN_TONG", "SOPHIEUTHANHTOAN", "CAPCUU", "tenkhoa", "SOVAOVIEN", "SOVAOVIEN_NOI",
                    "SOVAOVIEN_DT_NOI", "DA_THANH_TOAN", "CHUANDOANICD", "PHATTHUAT_LENLICH", "CO_BHYT", "NGUOI_THUC_HIEN", "ID_DIEUTRI", 'MA_PHONGBAN', "DACHAMEKIP", 'TG_TIEPNHAN', 'BANT', "NGAYTHUCHIEN", "NGAY_CHI_DINH"], // TGGDEV-36575 thêm NGAGYTHUCHIEN
                colModel: [
                    {name: 'STT_HANGNGAY', index: 'STT_HANGNGAY', width: 50, align: 'center', sorttype: 'int'},
                    {name: 'MABENHNHAN', index: 'MABENHNHAN', hidden: false, width: 100},
                    {
                        name: 'TENBENHNHAN_HT',
                        index: 'TENBENHNHAN_HT',
                        width: 200,
                        formatter: function (cellvalue, options, rowObject) {
                            var color;
                            var color_text;
                            if ("${hienthi_mausac_bn}" == "0") {
                                if (rowObject.DA_THANH_TOAN == "1") {
                                    color = '#009900';
                                    color_text = 'white';
                                }   //END VTU:25/10/2016
                                else {
                                    if ("${hienthi_mausac_bn}" == "0") {
                                        color = 'white';
                                        color_text = 'black';
                                    }
                                }
                            }
                            return '<span class="cellWithoutBackground" style="background-color:' + color + ';font-weight:bold ;color:' + color_text + '">' + cellvalue + '</span>';
                        }
                    },
                    {
                        name: 'TENBENHNHAN',
                        index: 'TENBENHNHAN',
                        width: 200,
                        hidden: true,
                        sorttype: 'string',
                        searchoptions: {
                            dataInit: function (el) {
                                setTimeout(function () {
                                    $(el).focus().trigger({type: 'keypress', charCode: 13});
                                }, 20);
                            }
                        }
                    },
                    {name: 'TUOI', index: 'TUOI', width: 50, align: 'right'},
                    {name: 'NOITRU', index: 'NOITRU', width: 50, align: 'center'},
                    {name: 'GIOITINH', index: 'GIOITINH', hidden: true},
                    {name: 'DIACHI', index: 'DIACHI', hidden: true},
                    {name: 'SOTHEBHYT', index: 'SOTHEBHYT', hidden: true},
                    {name: 'SO_PHIEU', index: 'SO_PHIEU', hidden: true},
                    {name: 'MA_KHAM_BENH', index: 'MA_KHAM_BENH', hidden: true},
                    {name: 'STT_BENHAN', index: 'STT_BENHAN', hidden: true},
                    {name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', hidden: true},
                    {name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', hidden: true},
                    {name: 'NGUOI_CHI_DINH', index: 'NGUOI_CHI_DINH', hidden: true},
                    {name: 'PHONG_CHI_DINH', index: 'PHONG_CHI_DINH', hidden: true},
                    {name: 'MA_PHONG_DICHVU', index: 'MA_PHONG_XN', hidden: true},
                    {name: 'KET_QUA', index: 'KET_LUAN_TONG', hidden: true},
                    {name: 'SOPHIEUTHANHTOAN', index: 'SOPHIEUTHANHTOAN', hidden: true},
                    {name: 'CAPCUU', index: 'CAPCUU', hidden: true},
                    {name: 'TENKHOA', index: 'TENKHOA', hidden: true},
                    {name: 'SOVAOVIEN', index: 'SOVAOVIEN', hidden: true},
                    {name: 'SOVAOVIEN_NOI', index: 'SOVAOVIEN_NOI', hidden: true},
                    {name: 'SOVAOVIEN_DT_NOI', index: 'SOVAOVIEN_DT_NOI', hidden: true},
                    {name: 'DA_THANH_TOAN', index: 'DA_THANH_TOAN', hidden: true},
                    // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: thêm thông tin khoa chỉ định, chẩn đoán icd
                    {name: 'CHUANDOANICD', index: 'CHUANDOANICD', hidden: true},
                    {name: 'PHAUTHUAT_LENLICH', index: 'PHAUTHUAT_LENLICH', hidden: true},
                    {name: 'CO_BHYT', index: 'CO_BHYT', hidden: true},
                    {name: 'NGUOI_THUC_HIEN', index: 'NGUOI_THUC_HIEN', hidden: true},
                    {name: 'ID_DIEUTRI', index: 'ID_DIEUTRI', hidden: true},
                    {name: 'MA_PHONGBAN', index: 'MA_PHONGBAN', hidden: true},
                    // End ĐắkLắk
                    {name: 'DACHAMEKIP', index: 'DACHAMEKIP', hidden: true}, //HGI Ngân 25/4/2018 - TGGDEV-29335
                    {name: 'TG_TIEPNHAN', index: 'TG_TIEPNHAN', hidden: true},
                    {name: 'BANT', index: 'BANT', hidden: true},
                    {name: 'NGAY_THUC_HIEN', index: 'NGAY_THUC_HIEN', hidden: true} // TGGDEV-36575 thêm
                    ,{name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', hidden: true}
                ],
                caption: "Danh sách bệnh nhân",
                ignoreCase: true,
                rowNum: 1000000,
                gridComplete: function () {},
                onSelectRow: function (id) {
                    if (id) {
                        var ret = $("#list_benhnhan").jqGrid('getRowData', id);
                        loaiTTPT = ret.MA_LOAI_DICHVU
                        //CMU Cảnh báo ngoại trú chưa đóng tiền
                        if ("${ttptcanhbao}" > 0 && ret.NOITRU == 0 && !ret.SOTHEBHYT && ret.DA_THANH_TOAN == 0) {
                            jAlert('Bệnh nhân ngoại trú chưa đóng viện phí.', 'Thông báo');
                            if ("${ttptcanhbao}" == 2) {
                                clear_benhnhan();
                                $("#list_ttpt_bhyt").jqGrid('clearGridData');
                                return;
                            }
                        }
                        $("#mabenhnhan").val(ret.MABENHNHAN);
                        $("#sttMayTTPT").val("0");
                        $("#hoten").val(ret.TENBENHNHAN);
                        $("#tuoi").val(ret.TUOI);
                        $("#gioitinh").val(ret.GIOITINH.toString());
                        $("#diachi").val(ret.DIACHI);
                        $("#sothebhyt").val(ret.SOTHEBHYT);
                        $("#sophieu").val(ret.SO_PHIEU);
                        $("#makhambenh").val(ret.MA_KHAM_BENH);
                        $("#noitru").val(ret.NOITRU);
                        $("#sttbenhan").val(ret.STT_BENHAN);
                        $("#sttdotdieutri").val(ret.STT_DOTDIEUTRI);
                        $("#sttdieutri").val(ret.STT_DIEUTRI);
                        $("#ketqua").val(ret.KET_QUA);
                        $("#sophieuthanhtoan").val(ret.SOPHIEUTHANHTOAN);
                        $("#sovaovien").val(ret.SOVAOVIEN);
                        $("#tenkhoa").val(ret.TENKHOA);
                        $("#phauthuat_lenlich").prop("checked", ret.PHAUTHUAT_LENLICH == 1);
                        $("#ngaytiepnhan").val(ret.TG_TIEPNHAN);
                        $("#cmu_ngaychidinh").val(ret.NGAY_CHI_DINH);
                        iddieutri = ret.ID_DIEUTRI;
                        sovaovien = ret.SOVAOVIEN;
                        sovaovien_noi = ret.SOVAOVIEN_NOI;
                        sovaovien_dt_noi = ret.SOVAOVIEN_DT_NOI;
                        noitru = ret.NOITRU;
                        sophieu = ret.SO_PHIEU;
                        makhambenh = ret.MA_KHAM_BENH;
                        stt_benhan = ret.STT_BENHAN;
                        stt_dieutri = ret.STT_DIEUTRI;
                        stt_dotdieutri = ret.STT_DOTDIEUTRI;
                        bant = ret.BANT;
                        phongchidinh = ret.PHONG_CHI_DINH;
                        ngay_chi_dinh = ret.NGAY_CHI_DINH;
                        console.log('ret', ret)
                        if(ngay_chi_dinh !== null){
                            var ngaygio_chichinh_cls = ngay_chi_dinh.split(" ");
                            ngaychidinh = ngaygio_chichinh_cls[0];
                            giochidinh = ngaygio_chichinh_cls[1];
                            if("${Sess_DVTT}" == '96175'){
                                $("#ngaychidinh_kq").val(ngaychidinh);
                                $("#giochidinh_kq").val(giochidinh);
                            }
                            $("#ngaychidinh_cls").val(ngaychidinh);
                            $("#giochidinh_cls").val(giochidinh);
                        }
                        $("#mabenhnhan_lm").val(ret.MABENHNHAN);
                        $("#hoten_lm").val(ret.TENBENHNHAN);
                        $("#tuoi_lm").val(ret.TUOI);
                        $("#gioitinh_lm").val(ret.GIOITINH.toString());
                        $("#sovaovien_dt_lm").val(ret.SOVAOVIEN_DT_NOI);
                        $("#noitru_lm").val(ret.NOITRU);
                        $("#id_dieutri_lm").val(ret.ID_DIEUTRI);
                        // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: thêm thông tin khoa chỉ định, chẩn đoán icd
                        $("#_chandoan").val(ret.CHUANDOANICD);
                        //$("#chandoan").val(ret.CHUANDOANICD);
                        // End ĐắkLắk
                        var dathuchien = $("#daxetnghiem").prop('checked');
                        var idath;
                        if (dathuchien == true) {
                            idath = 1;
                        } else {
                            idath = 0;
                        }
                        var arr = [ret.NOITRU, ret.SO_PHIEU, ret.STT_BENHAN, ret.STT_DOTDIEUTRI, ret.STT_DIEUTRI, "${Sess_DVTT}", sovaovien, sovaovien_noi, sovaovien_dt_noi, idath];
                        var url = "ttpt_hienthi_chitiet_svv?url=" + convertArray(arr);
                        $("#list_ttpt_bhyt").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
                        //CMU Filter theo theo BS thuc hien
                        cmu_nguoi_thuc_hien = ret.NGUOI_THUC_HIEN;

                        //HPG Hiển thị thông tin thêm về bệnh nhân
                        var hpg_STT_BENHAN = "0";
                        var hpg_STT_DIEUTRI = "0";
                        var hpg_STT_DOTDIEUTRI = "0";
                        if (ret.STT_DIEUTRI != "")
                            hpg_STT_BENHAN = ret.STT_BENHAN;
                        if (ret.STT_DIEUTRI != "")
                            hpg_STT_DOTDIEUTRI = ret.STT_DOTDIEUTRI;
                        if (ret.STT_DIEUTRI != "")
                            hpg_STT_DIEUTRI = ret.STT_DIEUTRI;
                        var arr1 = [ret.MA_KHAM_BENH, ret.SO_PHIEU, "${Sess_DVTT}", ret.NOITRU, hpg_STT_BENHAN, hpg_STT_DOTDIEUTRI, hpg_STT_DIEUTRI, 1]
                        var url1 = "hpg_thongtin_mo_rong_bn_cls?url=" + convertArray(arr1);

                        if ("${hienthi_them_cls}" == "1") {
                            hienthi_them_cls(url1);
                        }
                        var url3 = "hpg_pttt_trangthai_thanhtoan?url=" + convertArray(arr1);
                        if ("${suaketqua_cls}" == "1" && kiemtra_tt_cls(url3) > 0)
                            $("#list_ttpt_bhyt").jqGrid().setColProp('KET_QUA', {editable: false});
                        else
                            $("#list_ttpt_bhyt").jqGrid().setColProp('KET_QUA', {editable: true});
                        //--End HPG
                        if (noitru == "0") {
                            var url = "layNgayKhamBenh?sovaovien=" + sovaovien;
                            $.ajax({
                                url: url
                            }).done(function (dt) {
                                ngaykham_hd = dt;
                            });
                        }


                    }
                },
                onRightClickRow: function (id1) {
                    if (id1) {
                        $('#list_benhnhan').jqGrid('setSelection', id1);
                        //alert(id1);

                        $.contextMenu({
                            selector: '#list_benhnhan tr',
                            callback: function (key, options) {
                                if (key == "huyketqua") {
                                    var id = $("#list_benhnhan").jqGrid('getGridParam', 'selrow');
                                    var ret = $("#list_benhnhan").jqGrid('getRowData', id);
                                    if (ret.DACHAMEKIP == 1 && "${dachamekip}" == "1") {
                                        jAlert("Đã chấm công ekip, phải hủy ekip mới có thể hủy kết quả!", 'Thông báo');
                                    } else {
                                        var arr = ["${Sess_DVTT}", ret.NOITRU, ret.SO_PHIEU, ret.MA_KHAM_BENH, ret.STT_BENHAN, ret.STT_DOTDIEUTRI, ret.STT_DIEUTRI, 0];
                                        if ((((((ret.NGUOI_THUC_HIEN.indexOf(";")) < 0 ? (ret.NGUOI_THUC_HIEN + ";") : ret.NGUOI_THUC_HIEN)).indexOf("${Sess_UserID}" + ';')) >= 0 && "${tsktvhuyKQ}" == "0") || "${Sess_Admin}" != "0" || "${ds_nv_huyketqua}".includes("${user_id}") || (((((ret.NGUOI_THUC_HIEN.indexOf(";")) < 0 ? (ret.NGUOI_THUC_HIEN + ";") : ret.NGUOI_THUC_HIEN)).indexOf("${Sess_UserID}" + ';')) >= 0 && ret.NGAY_THUC_HIEN == "${ngayhientai}" && "${tsktvhuyKQ}" == "1")) {
                                            jConfirm('Xác nhận hủy kết quả  ?', 'Thông báo', function (r) {
                                                if (r.toString() == "true") {
                                                    if (ret.SO_PHIEU != "") {
                                                        var url = "huyketquattpt?url=" + convertArray(arr);
                                                        $.ajax({
                                                            url: url
                                                        }).always(function () {
                                                            var arr1 = ["${Sess_DVTT}", "Hủy kết quả TTPT-VLTL cho bệnh nhân " + ret.TEN_BENH_NHAN + " với phiếu TTPT-VLTL " + ret.SO_PHIEU, "${Sess_UserID}" + "-" + "${Sess_User}", "Hủy kết quả TTPT-VLTL"];
                                                            $.post("lichsusudung_insert", {url: convertArray(arr1)});
                                                            $("#lammoi").click();
                                                        });
                                                    } else {
                                                        jAlert("Chọn phiếu để hủy", 'Thông báo');
                                                    }
                                                }
                                            });
                                        } else {
                                            jAlert("Chỉ Admin mới có quyền hủy!", 'Thông báo');
                                        }
                                    }
                                }
                                if (key == "goiso") {
                                    var id = $("#list_benhnhan").jqGrid('getGridParam', 'selrow');
                                    var ret = $("#list_benhnhan").jqGrid('getRowData', id);
                                    var chuoi = ${Sess_PhongDuocSet} +"|" + ret.STT_HANGNGAY.toString().replace('<span class="cellWithoutBackground" style="background-color:white;color:black">', '').replace('</span>', '') + "|" + ret.TENBENHNHAN + "|" + ret.CAPCUU + "|" + "TTPT" + "|" + "0";
                                    <c:choose>
                                    <c:when test="${kgggoiso == '3'}">
                                    chuoihinhanh = goisolayhinhanh($("#list_benhnhan"), id);
                                    saveTextAsFile(chuoihinhanh);
                                    </c:when>
                                    <c:otherwise>
                                    saveTextAsFile(chuoi);
                                    </c:otherwise>
                                    </c:choose>
                                }
                            },
                            items: {
                                "huyketqua": {name: "Hủy kết quả"},
                                "goiso": {
                                    name: "<span style='color:red'>Gọi số bệnh nhân</span>", icon: "goiso"
                                }
                            }
                        });
                    }
                }
                // End ĐắkLắk
            });

            function clear_benhnhan() {
                $("#mabenhnhan").val('');
                $("#tt_thanhtoan").val('');
                $("#hoten").val('');
                $("#tuoi").val('');
                $("#gioitinh").val('');
                $("#diachi").val('');
                $("#sothebhyt").val('');
                $("#sophieu").val('');
                $("#makhambenh").val('');
                $("#noitru").val('');
                $("#sttbenhan").val('');
                $("#sttdotdieutri").val('');
                $("#sttdieutri").val('');
                $("#ketqua").val('');
                $("#sophieuthanhtoan").val('');
                $("#sovaovien").val('');
                $("#tenkhoa").val('');
                $("#phauthuat_lenlich").prop("checked", '');
                sovaovien = '';
                sovaovien_noi = '';
                sovaovien_dt_noi = '';
                $("#_chandoan").val('');
            }

            $("#list_hinhanhttpt").jqGrid({
                datatype: "local",
                loadonce: true,
                //height: 235,
                height: 292, // ĐắkLắk (An Giang yêu cầu) - Ninh 09/12/2016: điều chỉnh độ cao lưới
                width: 290,
                colNames: ["SO_PHIEU_DICHVU", "DVTT", "idauto", "MA_DV", "Hình ảnh phẫu thuật,thủ thuật"],
                colModel: [
                    {name: 'SO_PHIEU_DICHVU', index: 'SO_PHIEU_DICHVU', hidden: true},
                    {name: 'DVTT', index: 'DVTT', hidden: true},
                    {name: 'STT', index: 'STT', hidden: true},
                    {name: 'MA_DV', index: 'MA_DV', hidden: true},
                    {name: 'HINHANH', index: 'HINHANH', width: 80, formatter: imageFormat}
                ],
                rowNum: 200000
            });

            function imageFormat(cellvalue, options, rowObject) {
                return '<img src="' + cellvalue + '" width="100px" height="80px" />';
            }

            $("#list_benhnhan").jqGrid('filterToolbar', {
                stringResult: true,
                searchOnEnter: false,
                defaultSearch: "cn"
            });
            function cmu_laynhanvienttpt(sovaovien,sovaovien_dt,sophieu,madv) {
                $.get("cmu_getlist?url="+convertArray(["${Sess_DVTT}",sovaovien,sovaovien_dt,sophieu,madv,'CMU_DSEKIP_BS'])
                ).done(function(data) {
                    if(data.length > 0) {
                        var vitri = data[0].VITRI.split(";");
                        var tennhanvien = data[0].TENBACSI.split(";");
                        var manhanvien = data[0].MABACSI.split(";");
                        var cchn = data[0].DSBACSI.split(";");
                        vitri.forEach(function(value, index){
                            $("#"+vitri[index]).val(tennhanvien[index])
                            $("#"+vitri[index]+"_cchn").val(cchn[index])
                            $("#ma_"+vitri[index]+"_cchn").val(manhanvien[index])
                        })
                    }

                })
            }
            if ("${hienthinutthemekiptrongttpt}" == 1) {
                $("#list_ttpt_bhyt").jqGrid({
                    datatype: "local",
                    loadonce: true,
                    height: 250,
                    width: 665,
                    colNames: ["DA_THANH_TOAN", "MA_DV","TEN_DV_HT", "Dịch vụ", "Kết quả", "Trangthai", "Ma", "DVT", "EKIP", "ID_DIEUTRI","NGUOI_THUC_HIEN", "TEN_NGUOI_THUC_HIEN"
                        , "MA_BS_PTTP", "MA_BS_GAYME", "MOTA_DIENBENH_BENH", "STT_MAMAY", "MA_BS_DOC_KQ", "MA_TRINHTU_TT_PT"],
                    colModel: [
                        {name: 'DA_THANH_TOAN', index: 'DA_THANH_TOAN', hidden: true},
                        {name: 'MA_DV', index: 'MA_DV', hidden: true},
                        {name: 'TEN_DV_HT', index: 'TEN_DV_HT', hidden: true},
                        {
                            name: 'TEN_DV', index: 'TEN_DV', width: 150,
                            formatter: function (cellvalue, options, rowObject) {
                                var color;
                                var color_text;
                                if (rowObject.DA_THANH_TOAN === "0") {
                                    color = 'yellow';
                                    color_text = 'red';
                                } else {
                                    color = 'white';
                                    color_text = 'black';
                                }
                                return '<span class="cellWithoutBackground" style="background-color:' + color + ';color:' + color_text + '">' + (cellvalue == null ? "" : cellvalue) + '</span>';
                            }
                        },
                        {name: 'KET_QUA', index: 'KET_QUA', width: 60, editable: true, edittype: 'text'},
                        {name: 'TT_BHYT_CHI', index: 'TT_BHYT_CHI', hidden: true},
                        {name: 'MABAOCAO', index: 'MABAOCAO', hidden: true},
                        {name: 'DVT_DV', index: 'DVT_DV', hidden: true},
                        {name: 'EKIP', index: 'ekip', width: 75},
                        {name: 'ID_DIEUTRI', index: 'ID_DIEUTRI', hidden: true},
                        {name: 'NGUOI_THUC_HIEN', index: 'NGUOI_THUC_HIEN', hidden: true},
                        {name: 'TEN_NGUOI_THUC_HIEN', index: 'TEN_NGUOI_THUC_HIEN', hidden: true},
                        {name: 'MA_BS_PTTP', index: 'MA_BS_PTTP', hidden: true},
                        {name: 'MA_BS_GAYME', index: 'MA_BS_GAYME', hidden: true}
                        ,{name: 'MOTA_DIENBENH_BENH', index: 'MOTA_DIENBENH_BENH', hidden: true}
                        ,{name: 'STT_MAMAY', index: 'STT_MAMAY', hidden: true}
                        ,{name: 'MA_BS_DOC_KQ', index: 'MA_BS_DOC_KQ', hidden: true}
                        ,{name: 'MA_TRINHTU_TT_PT', index: 'MA_TRINHTU_TT_PT', hidden: true}

                    ],
                    caption: "Yêu cầu dịch vụ",
                    cellEdit: true,
                    cellsubmit: 'clientArray',
                    loadComplete: function () {
                        var $self = $(this);
                        var ids = $self.jqGrid('getDataIDs');
                        var dvtt = "${Sess_DVTT}";
                        var mabenhnhan = $("#mabenhnhan").val();
                        var stt_benhan = $("#sttbenhan").val();
                        if (stt_benhan === undefined || stt_benhan.length < 1) {
                            stt_benhan = $("#makhambenh").val();
                        }
                        var sophieu = $("#sophieu").val();
                        for (var i = 0; i < ids.length; i++) {
                            var ret = $("#list_ttpt_bhyt").jqGrid('getRowData', i + 1);
                            var ma_dv = ret.MA_DV;
                            var ten_dv = ret.TEN_DV_HT;
                            var rowId = ids[i];
                            var chamcong = "<input style='height:23px;width:100%;' " +
                                "type='button' id='chamcong' name='chamcong' value='Chi tiết' " +
                                "onclick=\"chamcong_ekip('" + dvtt + "','" + stt_benhan + "','" + mabenhnhan + "','" + ma_dv + "','" + ten_dv + "','" + sophieu + "');\" />" +
                                "<input type='text' hidden='true' id='madichvu' name='madichvu' value='" + ma_dv + "' >" +
                                "<input type='text' hidden='true' id='tendichvu' name='tendichvu' value='" + ten_dv + "' >";
                            $self.jqGrid('setRowData', rowId, {EKIP: chamcong});
                        }
                    },
                    ondblClickRow: function (id) {
                        console.log('here')
                        if (id) {
                            var ret = $("#list_ttpt_bhyt").jqGrid('getRowData', id);
                            console.log('here11', ret)
                            $("#madv").val(ret.MA_DV);
                            $("#sttMayTTPT").val(ret.STT_MAMAY);
                            $("#nguoithuchien").val(ret.NGUOI_THUC_HIEN);
                            $("#nhanvienluu").val(ret.TEN_NGUOI_THUC_HIEN);
                            if("${Sess_DVTT}" == '96029') {
                                $("#cboNguoiDocKetQua").val(ret.MA_BS_DOC_KQ).trigger('change');
                                $('#ten_dv_ht').val(ret.TEN_DV_HT)
                            } else {
                                $("#cboNguoiDocKetQua").val(ret.MA_BS_DOC_KQ);
                            }
                            $("#maupttt").val(ret.MA_TRINHTU_TT_PT.toString());
                            var sophieu = $("#sophieu").val();
                            var noitru = $("#noitru").val();
                            var sttbenhan = $("#sttbenhan").val();
                            var sttdotdieutri = $("#sttdotdieutri").val();
                            var sttdieutri = $("#sttdieutri").val();
                            var makhambenh = $("#makhambenh").val();
                            var madv = $("#madv").val();
                            var dvtt = "${Sess_DVTT}";
                            // ĐắkLắk (An Giang yêu cầu) - Ninh 09/12/2016: view thông tin hành chánh của BN lên form nhập kết quả
                            $("#hoten_ct").val($("#hoten").val());
                            $("#tuoi_ct").val($("#tuoi").val());
                            $("#gioitinh_ct").val($("#gioitinh").val() == "true" ? "Nam" : "Nữ");
                            $("#mabenhnhan_ct").val($("#mabenhnhan").val());
                            $("#tenkhoa_ct").val($("#tenkhoa").val());
                            $("#ngaychidinh_kq").val(ngaychidinh);
                            $("#giochidinh_kq").val(giochidinh);
                            $("#sothebhyt_ct").val($("#sothebhyt").val());
                            $("#chandoan").val($("#_chandoan").val());
                            //CMU - 26/04/2017 Bác sĩ mặc định
                            $("#cbbacsittpt").val("${Sess_UserID}").change();
                            $("#cbbacsigayme").val("").change();
                            // End ĐắkLắk
                            var NoiTru = $("#noitru").val() == 1;
                            var obj = {
                                MABENHNHAN: $("#mabenhnhan").val(),
                                NOI_TRU: NoiTru,
                                BHYT: $("#sothebhyt").val().trim() ? 1 : 0,
                                STT_BENHAN: stt_benhan,
                                STT_DOTDIEUTRI: stt_dotdieutri,
                                SOVAOVIEN: NoiTru ? sovaovien_noi : sovaovien,
                                SOVAOVIEN_DT: sovaovien_dt_noi,
                                STT_DIEUTRI: stt_dieutri,
                                MAKHAMBENH: $("#makhambenh").val(),
                                MA_DV: madv,
                                SO_PHIEU: $("#sophieu").val(),
                                BANT: bant,
                                PHONG_CHI_DINH: phongchidinh
                            };
                            dialog_capnhatketquattpt.data(obj).dialog("open");
                            cmu_laynhanvienttpt(noitru==1?sovaovien_noi:sovaovien,noitru==1?sovaovien_dt_noi:0,sophieu,madv);
                            var arr = [sophieu, noitru, sttbenhan, sttdotdieutri, sttdieutri, makhambenh, madv, dvtt, "0", sovaovien, sovaovien_noi, sovaovien_dt_noi];
                            var url3 = "pttt_select_ketqua_svv?url=" + convertArray(arr);
                            $.getJSON(url3, function (result) {
                                $.each(result, function (i, field) {
                                    $("#frm_kq_id_ekip").val(field.ID_EKIP);
                                    $("#frm_kq_so_phieu_dichvu").val(field.SO_PHIEU_DICHVU);
                                    $("#sophieudv_lm").val(field.SO_PHIEU_DICHVU);
                                    $("#frm_kq_ma_dv").val(field.MA_DV);
                                    $("#frm_kq_mabenhnhan").val(field.MABENHNHAN);
                                    $("#frm_kq_sovaovien").val(field.SOVAOVIEN);
                                    $("#frm_kq_sovaovien_dt").val(field.SOVAOVIEN_DT);
                                    $("#frm_kq_noitru").val(field.NOITRU);

                                    $("#frm_kq_id_dieutri").val(field.ID_DIEUTRI);
                                    if (field.CHANDOANSAUPTTT != null) {
                                        $("#chandoan").val(field.CHANDOANSAUPTTT);
                                    }
                                    //$("#chandoan").val(field.CHANDOANSAUPTTT);
                                    if (field.PHUONGPHAP_TT_PT != null) {
                                        $("#phuongphappttt").val(field.PHUONGPHAP_TT_PT);
                                    }

                                    if ("${Sess_DVTT}" == '96029') {
                                        console.log('96029999', field.PPVC)
                                        $("#phuongphapvocam").val(field.PHUONGPHAP_VOCAM || '1');
                                        $("#ma_pp_vo_cam").val(field.PPVC ?? '4');
                                    } else {
                                        $("#phuongphapvocam").val(field.PHUONGPHAP_VOCAM);
                                        $("#ma_pp_vo_cam").val(field.PPVC);
                                    }

                                    $("#bacsipttt").val(field.BACSI_PTTT);
                                    $("#bacsigayme").val(field.BACSI_GAYME);
                                    $("#catchisau7ngay").val(field.CATCHI_SAU7NGAY);
                                    $("#trinhtupttt").val(field.TRINHTU_TT_PT);
                                    $("#luotdottpt").val(field.LUOTDO_TTPT);
                                    $("#icd_truocttpt").val(field.ICD_TRUOCTTPT);
                                    $("#chandoan_truocttpt").val(field.CHANDOAN_TRUOCTTPT);
                                    $("#icd_sauttpt").val(field.ICD_SAUTTPT);
                                    $("#chandoan_sauttpt").val(field.CHANDOAN_SAUTTPT);

									$("#mabenhly_truocttpt").val(field.MABENHLY_TRUOCTTPT);
									$("#mabenhly_sauttpt").val(field.MABENHLY_SAUTTPT);

                                    // STG
                                    if (field.NGAYPTTT_1 == null) {
                                        $("#ngaypttt").val("${ngayhientai}");
                                    } else {
                                        $("#ngaypttt").val(field.NGAYPTTT_1);
                                    }
                                    $("#ma_bacsipttt").val(field.MA_BS_PTTP);
                                    $("#ma_bacsigayme").val(field.MA_BS_GAYME);


                                    if(field.GIOPTTT_1 == null) {
                                     showtime_giopttt_cancel = 0;
                                     } else {
                                     showtime_giopttt_cancel = 1;
                                     $("#giopttt").val(field.GIOPTTT_1);
                                     }
                                    /*$("#giopttt").data('da-thuc-hien', field.DA_CHAN_DOAN == 1)
                                    if (field.GIOPTTT_1 == null || field.DA_CHAN_DOAN != 1) {
                                        showtime_giopttt();
                                    } else {
                                        stopGioPtttTimer();
                                        $("#giopttt").val(field.GIOPTTT_1);
                                    }*/
                                    // STG
                                    if (field.TAIBIEN == "1") {
                                        $("#cb_gaymehoisuc").prop("checked", true);
                                        $("#cb_nhiemkhuan").prop("checked", false);
                                        $("#cb_khac").prop("checked", false);
                                    } else if (field.TAIBIEN == "2") {
                                        $("#cb_nhiemkhuan").prop("checked", true);
                                        $("#cb_gaymehoisuc").prop("checked", false);
                                        $("#cb_khac").prop("checked", false);
                                    } else if (field.TAIBIEN == "3") {
                                        $("#cb_khac").prop("checked", true);
                                        $("#cb_nhiemkhuan").prop("checked", false);
                                        $("#cb_gaymehoisuc").prop("checked", false);
                                    } else {
                                        $("#cb_khac").prop("checked", false);
                                        $("#cb_nhiemkhuan").prop("checked", false);
                                        $("#cb_gaymehoisuc").prop("checked", false);
                                    }

                                    if (field.TUVONG == "1") {
                                        $("#cb_trenban").prop("checked", true);
                                        $("#cb_trong24gio").prop("checked", false);
                                    } else if (field.TUVONG == "2") {
                                        $("#cb_trong24gio").prop("checked", true);
                                        $("#cb_trenban").prop("checked", false);
                                    } else {
                                        $("#cb_trong24gio").prop("checked", false);
                                        $("#cb_trenban").prop("checked", false);
                                    }

                                    var using_ekip = field.ID_EKIP != null;
                                    $("#bacsipttt").prop('disabled', using_ekip);
                                    $("#cbbacsittpt").prop('disabled', using_ekip);
                                    $("#khoabsttpt").prop('disabled', using_ekip);
                                    $("#bacsigayme").prop('disabled', using_ekip);
                                    $("#cbbacsigayme").prop('disabled', using_ekip);
                                    $("#khoabsgayme").prop('disabled', using_ekip);
                                    $("#moTaDienBienBenh").val(field.MOTA_DIENBENH_BENH);
                                    $("#chkVetThuongTaiPhat").setCheckBoxValue(field.VET_THUONG_TP);
                                    if(field.NGAYPTTT_KT1 != null){
                                        $("#thoigiankt").val(field.NGAYPTTT_KT1);
                                    } else {
                                        $("#thoigiankt").val("${ngayhientai}");
                                    }
                                    if(field.GIOPTTT_KT1 != null){
                                        $("#giokt").val(field.GIOPTTT_KT1);
                                    }else{

                                        $('#giokt').val(moment(field.NGAY_CHI_DINH_CT, 'DD/MM/YYYY HH:mm:ss').add(3, 'minutes').format('HH:mm:ss'));
                                    }
                                    $("#use_ekip").val(using_ekip);
                                    if("${Sess_DVTT}".indexOf(96) == 0)  {
                                        $("#bacsigayme").prop("disabled", true);
                                        $("#bacsipttt").prop("disabled", true);
                                    }
                                    if ("${ts3867881}" == "1" && field.DA_CHAN_DOAN != 1) {
                                        $.post('load-mac-dinh-bsttpt-theo-khoa',{maBacSi: "${Sess_UserID}"}).done(function (data) {
                                            if (data) {
                                                $("#ma_bacsipttt").val(data.MA_BS_PTTP);
                                                $("#bacsipttt").val(data.BACSI_PTTT);
                                            }
                                        });
                                    }
                                });
                            });
                            var arr = [sophieu, dvtt, madv, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                            var url = 'pttt_danhsach_hinhanh?url=' + convertArray(arr);
                            $("#list_hinhanhttpt").jqGrid('setGridParam', {
                                datatype: 'json',
                                url: url
                            }).trigger('reloadGrid');

                            getNguoiDocKetQua(sophieu, noitru==1?sovaovien_noi:sovaovien, sovaovien_noi, noitru, madv);
                        }
                    },
                    onSelectRow: function (id) {
                        if (id) {
                            var ret = $("#list_ttpt_bhyt").jqGrid('getRowData', id);
                            $("#sttMayTTPT").val(ret.STT_MAMAY);
                            $("#madv").val(ret.MA_DV);
                        }
                    },
                    onRightClickRow: function (id) {
                        if (id) {

                        }
                    },
                    beforeSaveCell: function (rowid, name, val, iRow, iCol) {
                    }
                    ,
                    afterSaveCell: function (rowid, name, val, iRow, iCol) {
                        var ret = $("#list_ttpt_bhyt").jqGrid('getRowData', rowid);
                        var arr = [$("#sophieu").val(), ret.MA_DV, val.replace(/\+/g, "%2B"), "${Sess_DVTT}", $("#noitru").val(), $("#makhambenh").val(),
                            $("#sttbenhan").val(), $("#sttdotdieutri").val(), $("#sttdieutri").val(), "0"];
                        var url = "ttpt_update_ketqua_chitiet";
                        $.post(url, {
                            url: convertArray(arr)
                        }).done(function () {
                            var sophieu = $("#sophieu").val();
                            var noitru = $("#noitru").val();
                            var sttbenhan = $("#sttbenhan").val();
                            var sttdotdieutri = $("#sttdotdieutri").val();
                            var sttdieutri = $("#sttdieutri").val();
                            var dathuchien = $("#daxetnghiem").prop('checked');
                            var idath;
                            if (dathuchien == true) {
                                idath = 1;
                            } else {
                                idath = 0;
                            }
                            var arr1 = [noitru, sophieu, sttbenhan, sttdotdieutri, sttdieutri, "${Sess_DVTT}", sovaovien, sovaovien_noi, sovaovien_dt_noi, idath];
                            var url1 = "ttpt_hienthi_chitiet_svv?url=" + convertArray(arr1);
                            $("#list_ttpt_bhyt").jqGrid('setGridParam', {
                                datatype: 'json',
                                url: url1
                            }).trigger('reloadGrid');
                        });
                    }
                });
            } else {
                $("#list_ttpt_bhyt").jqGrid({
                    datatype: "local",
                    loadonce: true,
                    height: 250,
                    width: 665,
                    colNames: ["DA_THANH_TOAN", "MA_DV", "Dịch vụ", "Kết quả", "Trangthai", "Ma", "DVT", "ICD_TRUOCTTPT", "CHANDOAN_TRUOCTTPT", "ICD_SAUTTPT", "TEN HIEN THI",
                        "CHANDOAN_SAUTTPT","NGUOI_THUC_HIEN", "TEN_NGUOI_THUC_HIEN", "MA_BS_PTTP", "MA_BS_GAYME", "MOTA_DIENBENH_BENH", "STT_MAMAY"],
                    colModel: [
                        {name: 'DA_THANH_TOAN', index: 'DA_THANH_TOAN', hidden: true},
                        {name: 'MA_DV', index: 'MA_DV', hidden: true},
                        {
                            name: 'TEN_DV', index: 'TEN_DV', width: 150,
                            formatter: function (cellvalue, options, rowObject) {
                                var color;
                                var color_text;
                                if (rowObject.DA_THANH_TOAN === "0") {
                                    color = 'yellow';
                                    color_text = 'red';
                                } else {
                                    color = 'white';
                                    color_text = 'black';
                                }
                                return '<span class="cellWithoutBackground" style="background-color:' + color + ';color:' + color_text + '">' + (cellvalue == null ? "" : cellvalue) + '</span>';
                            }
                        },
                        {name: 'KET_QUA', index: 'KET_QUA', width: 60, editable: true, edittype: 'text'},
                        {name: 'TT_BHYT_CHI', index: 'TT_BHYT_CHI', hidden: true},
                        {name: 'MABAOCAO', index: 'MABAOCAO', hidden: true},
                        {name: 'DVT_DV', index: 'DVT_DV', hidden: true},
                        {name: 'ICD_TRUOCTTPT', index: 'ICD_TRUOCTTPT', hidden: true},
                        {name: 'CHANDOAN_TRUOCTTPT', index: 'CHANDOAN_TRUOCTTPT', hidden: true},
                        {name: 'ICD_SAUTTPT', index: 'ICD_SAUTTPT', hidden: true},
                        {name: 'TEN_DV_HT', index: 'TEN_DV_HT', hidden: true},
                        {name: 'CHANDOAN_SAUTTPT', index: 'CHANDOAN_SAUTTPT', hidden: true},
                        {name: 'TEN_DV_HT', index: 'TEN_DV_HT', hidden: true},
                        {name: 'NGUOI_THUC_HIEN', index: 'NGUOI_THUC_HIEN', hidden: true},
                        {name: 'MA_BS_PTTP', index: 'MA_BS_PTTP', hidden: false},
                        {name: 'MA_BS_GAYME', index: 'MA_BS_GAYME', hidden: false}
                        ,{name: 'MOTA_DIENBENH_BENH', index: 'MOTA_DIENBENH_BENH', hidden: true}
                        ,{name: 'STT_MAMAY', index: 'STT_MAMAY', hidden: false, width: '20%'}
                    ],
                    caption: "Yêu cầu dịch vụ",
                    cellEdit: true,
                    cellsubmit: 'clientArray',
                    loadComplete: function(){
                        if($("#daxetnghiem").prop("checked")){
                            var ids = $("#list_ttpt_bhyt").getDataIDs();
                            var datarow = jQuery('#list_ttpt_bhyt').jqGrid ('getRowData', id);
                            for(var id in ids) {
                                if (!!datarow.NGUOI_THUC_HIEN && datarow.NGUOI_THUC_HIEN > 0 && datarow.NGUOI_THUC_HIEN != cmu_nguoi_thuc_hien && "${ts_82489}" == "0")
                                    $('#list_ttpt_bhyt').jqGrid('delRowData',id);
                            };
                        }
                    },
                    ondblClickRow: function (id) {
                        if (id) {
                            var ret = $("#list_ttpt_bhyt").jqGrid('getRowData', id);
                            $("#madv").val(ret.MA_DV);
                            $("#sttMayTTPT").val(ret.STT_MAMAY);
                            $("#nguoithuchien").val(ret.NGUOI_THUC_HIEN);
                            $("#nhanvienluu").val(ret.TEN_NGUOI_THUC_HIEN);
                            $("#phuongphappttt").val(ret.TEN_DV_HT);
                            debugger;
                            var sophieu = $("#sophieu").val();
                            var noitru = $("#noitru").val();
                            var sttbenhan = $("#sttbenhan").val();
                            var sttdotdieutri = $("#sttdotdieutri").val();
                            var sttdieutri = $("#sttdieutri").val();
                            var makhambenh = $("#makhambenh").val();
                            var madv = $("#madv").val();
                            var dvtt = "${Sess_DVTT}";
                            //CMU - 26/04/2017 Bác sĩ mặc định
                            $("#cbbacsittpt").val("${Sess_UserID}").change();
                            $("#cbbacsigayme").val("").change();
                            // ĐắkLắk (An Giang yêu cầu) - Ninh 09/12/2016: view thông tin hành chánh của BN lên form nhập kết quả
                            $("#hoten_ct").val($("#hoten").val());
                            $("#tuoi_ct").val($("#tuoi").val());
                            $("#gioitinh_ct").val($("#gioitinh").val() == "true" ? "Nam" : "Nữ");
                            $("#mabenhnhan_ct").val($("#mabenhnhan").val());
                            $("#tenkhoa_ct").val($("#tenkhoa").val());
                            $("#sothebhyt_ct").val($("#sothebhyt").val());
                            $("#chandoan").val($("#_chandoan").val());
                            // End ĐắkLắk
                            var NoiTru = $("#noitru").val() == 1;
                            var obj = {
                                MABENHNHAN: $("#mabenhnhan").val(),
                                NOI_TRU: NoiTru,
                                BHYT: $("#sothebhyt").val().trim() ? 1 : 0,
                                STT_BENHAN: stt_benhan,
                                STT_DOTDIEUTRI: stt_dotdieutri,
                                SOVAOVIEN: NoiTru ? sovaovien_noi : sovaovien,
                                SOVAOVIEN_DT: sovaovien_dt_noi,
                                STT_DIEUTRI: stt_dieutri,
                                MAKHAMBENH: $("#makhambenh").val(),
                                MA_DV: madv,
                                SO_PHIEU: $("#sophieu").val(),
                                BANT: bant,
                                PHONG_CHI_DINH: phongchidinh
                            };
                            dialog_capnhatketquattpt.data(obj).dialog("open");
                            dialog_capnhatketquattpt.on('dialogclose', function (event) {
                                stopGioPtttTimer();
                            });
                            cmu_laynhanvienttpt(noitru==1?sovaovien_noi:sovaovien,noitru==1?sovaovien_dt_noi:0,sophieu,madv);
                            var arr = [sophieu, noitru, sttbenhan, sttdotdieutri, sttdieutri, makhambenh, madv, dvtt, "0", sovaovien, sovaovien_noi, sovaovien_dt_noi];
                            var url3 = "pttt_select_ketqua_svv?url=" + convertArray(arr);
                            $.getJSON(url3, function (result) {
                                $.each(result, function (i, field) {
                                    $("#sophieudv_lm").val(field.SO_PHIEU_DICHVU);
                                    $("#frm_kq_id_ekip").val(field.ID_EKIP);
                                    $("#frm_kq_so_phieu_dichvu").val(field.SO_PHIEU_DICHVU);
                                    $("#frm_kq_ma_dv").val(field.MA_DV);
                                    $("#frm_kq_mabenhnhan").val(field.MABENHNHAN);
                                    $("#frm_kq_sovaovien").val(field.SOVAOVIEN);
                                    $("#frm_kq_sovaovien_dt").val(field.SOVAOVIEN_DT);
                                    $("#frm_kq_noitru").val(field.NOITRU);
                                    $("#frm_kq_id_dieutri").val(field.ID_DIEUTRI);

                                    if (field.CHANDOANSAUPTTT != null) {
                                        $("#chandoan").val(field.CHANDOANSAUPTTT);
                                    }
                                    //$("#chandoan").val(field.CHANDOANSAUPTTT);
                                    if (field.PHUONGPHAP_TT_PT != null) {
                                        $("#phuongphappttt").val(field.PHUONGPHAP_TT_PT);
                                    }
                                    $("#phuongphapvocam").val(field.PHUONGPHAP_VOCAM);
                                    $("#bacsipttt").val(field.BACSI_PTTT);
                                    $("#bacsigayme").val(field.BACSI_GAYME);
                                    $("#catchisau7ngay").val(field.CATCHI_SAU7NGAY);
                                    $("#trinhtupttt").val(field.TRINHTU_TT_PT);
                                    $("#luotdottpt").val(field.LUOTDO_TTPT);
                                    $("#icd_truocttpt").val(field.ICD_TRUOCTTPT);
                                    $("#chandoan_truocttpt").val(field.CHANDOAN_TRUOCTTPT);
                                    $("#icd_sauttpt").val(field.ICD_SAUTTPT);
                                    $("#chandoan_sauttpt").val(field.CHANDOAN_SAUTTPT);
									$("#mabenhly_truocttpt").val(field.MABENHLY_TRUOCTTPT);
									$("#mabenhly_sauttpt").val(field.MABENHLY_SAUTTPT);

                                    $("#ma_pp_vo_cam").val(field.PPVC);

                                    // STG
                                    if (field.NGAYPTTT_1 == null) {
                                        $("#ngaypttt").val("${ngayhientai}");
                                    } else {
                                        $("#ngaypttt").val(field.NGAYPTTT_1);
                                    }
                                    $("#ma_bacsipttt").val(field.MA_BS_PTTP);
                                    $("#ma_bacsigayme").val(field.MA_BS_GAYME);

                                    if(field.GIOPTTT_1 == null) {
                                     showtime_giopttt_cancel = 0;
                                     } else {
                                     showtime_giopttt_cancel = 1;
                                     $("#giopttt").val(field.GIOPTTT_1);
                                     }
                                    /*$("#giopttt").data('da-thuc-hien', field.DA_CHAN_DOAN == 1)
                                    if (field.GIOPTTT_1 == null || field.DA_CHAN_DOAN != 1) {
                                        showtime_giopttt();
                                    } else {
                                        stopGioPtttTimer();
                                        $("#giopttt").val(field.GIOPTTT_1);
                                    }*/
                                    // STG
                                    if (field.TAIBIEN == "1") {
                                        $("#cb_gaymehoisuc").prop("checked", true);
                                        $("#cb_nhiemkhuan").prop("checked", false);
                                        $("#cb_khac").prop("checked", false);
                                    } else if (field.TAIBIEN == "2") {
                                        $("#cb_nhiemkhuan").prop("checked", true);
                                        $("#cb_gaymehoisuc").prop("checked", false);
                                        $("#cb_khac").prop("checked", false);
                                    } else if (field.TAIBIEN == "3") {
                                        $("#cb_khac").prop("checked", true);
                                        $("#cb_nhiemkhuan").prop("checked", false);
                                        $("#cb_gaymehoisuc").prop("checked", false);
                                    } else {
                                        $("#cb_khac").prop("checked", false);
                                        $("#cb_nhiemkhuan").prop("checked", false);
                                        $("#cb_gaymehoisuc").prop("checked", false);
                                    }

                                    if (field.TUVONG == "1") {
                                        $("#cb_trenban").prop("checked", true);
                                        $("#cb_trong24gio").prop("checked", false);
                                    } else if (field.TUVONG == "2") {
                                        $("#cb_trong24gio").prop("checked", true);
                                        $("#cb_trenban").prop("checked", false);
                                    } else {
                                        $("#cb_trong24gio").prop("checked", false);
                                        $("#cb_trenban").prop("checked", false);
                                    }

                                    var using_ekip = field.ID_EKIP != null;
                                    $("#bacsipttt").prop('disabled', using_ekip);
                                    $("#cbbacsittpt").prop('disabled', using_ekip);
                                    $("#khoabsttpt").prop('disabled', using_ekip);
                                    $("#bacsigayme").prop('disabled', using_ekip);
                                    $("#cbbacsigayme").prop('disabled', using_ekip);
                                    $("#khoabsgayme").prop('disabled', using_ekip);
                                    $("#use_ekip").val(using_ekip);
                                    $("#moTaDienBienBenh").val(field.MOTA_DIENBENH_BENH);

                                    if ("${ts3867881}" == "1" && field.DA_CHAN_DOAN != 1) {
                                        $.post('load-mac-dinh-bsttpt-theo-khoa',{maBacSi: "${Sess_UserID}"}).done(function (data) {
                                            if (data) {
                                                $("#ma_bacsipttt").val(data.MA_BS_PTTP);
                                                $("#bacsipttt").val(data.BACSI_PTTT);
                                            }
                                        });
                                    }
                                    $("#thoigiankt").val(field.NGAYPTTT_KT1);
                                    $("#giokt").val(field.GIOPTTT_KT1);
                                });
                            });

                            var arr = [sophieu, dvtt, madv, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                            var url = 'pttt_danhsach_hinhanh?url=' + convertArray(arr);
                            $("#list_hinhanhttpt").jqGrid('setGridParam', {
                                datatype: 'json',
                                url: url
                            }).trigger('reloadGrid');
                        }
                    },
                    onSelectRow: function (id) {
                        if (id) {
                            var ret = $("#list_ttpt_bhyt").jqGrid('getRowData', id);
                            $("#sttMayTTPT").val(ret.STT_MAMAY);
                            $("#madv").val(ret.MA_DV);
                        }
                    },
                    onRightClickRow: function (id) {
                        if (id) {
                            $('#list_xn_bhyt').jqGrid('setSelection', id);
                            //alert(id1);
                            $.contextMenu({
                                selector: '#list_xn_bhyt tr',
                                callback: function (key, options) {
                                    if (key == "huyketqua") {
                                        var id = $("#list_xn_bhyt").jqGrid('getGridParam', 'selrow');
                                        var ret = $("#list_xn_bhyt").jqGrid('getRowData', id);
                                        var arr = ["${Sess_DVTT}", noitru, sophieu, makhambenh, ret.MA_DV, stt_benhan, stt_dotdieutri, stt_dieutri, 0];
                                        if ("${Sess_Admin}" != "0" || "${ds_nv_huyketqua}".includes("${user_id}")) {
                                            jConfirm('Xác nhận hủy kết quả  ?', 'Thông báo', function (r) {
                                                if (r.toString() == "true") {
                                                    if (ret.SO_PHIEU != "") {
                                                        var url = "huyketquattpt_tungchidinh?url=" + convertArray(arr);
                                                        $.ajax({
                                                            url: url
                                                        }).always(function () {
                                                            var arr1 = ["${Sess_DVTT}", "Hủy kết quả TTPT-VLTL cho bệnh nhân " + ret.TEN_BENH_NHAN + " với phiếu TTPT-VLTL " + ret.SO_PHIEU, "${Sess_UserID}" + "-" + "${Sess_User}", "Hủy kết quả TTPT-VLTL"];
                                                            $.post("lichsusudung_insert", {url: convertArray(arr1)});
                                                            $("#lammoi").click();
                                                            //reload_grid_dichvu();
                                                        });
                                                    } else {
                                                        jAlert("Chọn phiếu để hủy", 'Thông báo');
                                                    }
                                                }
                                            });
                                        } else {
                                            jAlert("Chỉ Admin mới có quyền hủy!", 'Thông báo');
                                        }
                                    }
                                },
                                items: {
                                    "huyketqua": {name: "Hủy kết quả"}
                                }
                            });
                        }
                    },
                    beforeSaveCell: function (rowid, name, val, iRow, iCol) {
                    }
                    ,
                    afterSaveCell: function (rowid, name, val, iRow, iCol) {
                        var ret = $("#list_ttpt_bhyt").jqGrid('getRowData', rowid);
                        var arr = [$("#sophieu").val(), ret.MA_DV, val.replace(/\+/g, "%2B"), "${Sess_DVTT}", $("#noitru").val(), $("#makhambenh").val(),
                            $("#sttbenhan").val(), $("#sttdotdieutri").val(), $("#sttdieutri").val(), "0"];
                        var url = "ttpt_update_ketqua_chitiet";
                        $.post(url, {
                            url: convertArray(arr)
                        }).done(function () {
                            var sophieu = $("#sophieu").val();
                            var noitru = $("#noitru").val();
                            var sttbenhan = $("#sttbenhan").val();
                            var sttdotdieutri = $("#sttdotdieutri").val();
                            var sttdieutri = $("#sttdieutri").val();
                            var dathuchien = $("#daxetnghiem").prop('checked');
                            var idath;
                            if (dathuchien == true) {
                                idath = 1;
                            } else {
                                idath = 0;
                            }
                            var arr1 = [noitru, sophieu, sttbenhan, sttdotdieutri, sttdieutri, "${Sess_DVTT}", sovaovien, sovaovien_noi, sovaovien_dt_noi, idath];
                            var url1 = "ttpt_hienthi_chitiet_svv?url=" + convertArray(arr1);
                            $("#list_ttpt_bhyt").jqGrid('setGridParam', {
                                datatype: 'json',
                                url: url1
                            }).trigger('reloadGrid');
                        });
                    }
                });
            }
            $("#lammoi").click(function (evt) {
                reload_grid();
            });
            $("#cbbacsittpt").change(function (evt) {
                var bacsipttt = $("#cbbacsittpt option:selected").text();
                var ma_bacsipttt = $("#cbbacsittpt option:selected").val();
                $("#bacsipttt").val(bacsipttt);
                $("#ma_bacsipttt").val(ma_bacsipttt);
                cmu_laycchn_bacsi($(this).val(),"#bacsipttt_cchn")
            });
            $("#cbbacsigayme").change(function (evt) {
                var bacsigayme = $("#cbbacsigayme option:selected").text();
                $("#bacsigayme").val(bacsigayme);
                var ma_bacsigayme = $("#cbbacsigayme option:selected").val();
                $("#ma_bacsigayme").val(ma_bacsigayme);
                cmu_laycchn_bacsi($(this).val(),"#bacsigayme_cchn")
            });
            function cmu_laycchn_bacsi(manhanvien,id) {
                $.get("cmu_getlist?url="+convertArray(["${Sess_DVTT}",manhanvien,'CMU_LAYCCHN'])
                ).done(function(data) {
                    if(data.length > 0) {
                        $(id).val(data[0].CCHN);
                        $("#"+id.replace("#",'ma_')).val(manhanvien);
                    }
                })
            }
            $("#div_list_ttpt").click(function (evt) {
                var id = $("#list_ttpt_bhyt").jqGrid('getGridParam', 'selrow');
                var ret = $("#list_ttpt_bhyt").jqGrid('getRowData', id);

                $("#madv").val(ret.MA_DV);
                $("#sttMayTTPT").val(ret.STT_MAMAY);
            });
            $("#luuthongtin").click(function (evt) {
                $("#luu_tt").click();
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var ketluan = $("#ketqua").val();
                var ngaygioth_cls = "";
                if ($("#ngaythuchien_cls").val() != '' && $("#giothuchien_cls").val() != '') {
                    ngaygioth_cls = convertStr_MysqlDate($("#ngaythuchien_cls").val()) + " " + $("#giothuchien_cls").val();
                }

                //Kiểm tra khóa đã khám - ra viện khóa bảng kê
                var ktraAjax = $.ajax({type: "POST", url: "cmu_post_cmu_kttrangthai_cls", async: false,
                    data:  {url: [dvtt, sovaovien, sovaovien_noi, sovaovien_dt_noi, noitru].join('```')}
                });
                if(ktraAjax.status != 200 || ktraAjax.responseText > 0){
                    var alert = ktraAjax.status != 200 ? ("Lỗi: " + ktraAjax.statusText) : "Bệnh nhân đã khóa dữ liệu, không thể thao tác.";
                    jAlert(alert);
                    return;
                }//

                var arr2 = [dvtt, noitru, sophieu, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, ketluan, "1", sovaovien, sovaovien_noi, sovaovien_dt_noi, ngaygioth_cls, "${Sess_UserID}"];
                var url2 = "ttpt_update_trangthai_svv";
                var arr1 = [makhambenh, sophieu, "${Sess_DVTT}", noitru, sttbenhan, sttdotdieutri, sttdieutri, 0]
                var url1 = "hpg_pttt_trangthai_thanhtoan?url=" + convertArray(arr1);
                if ("${suaketqua_cls}" == "1" && kiemtra_tt_cls(url1) > 0) {
                    jAlert("Bệnh nhân đã thanh toán viện phí. Không được thay đổi kết quả.", 'Cảnh báo');
                    return;
                } else
                    $.post(url2, {
                        url: convertArray(arr2)
                    }).done(function () {
                        reload_grid();
                        jAlert("Cập nhật thành công", 'Thông báo');
                    });
            });

            $("#luufile").click(function (evt) {
                if ($("#madv").val() == "")
                    jAlert("Vui lòng chọn dịch vụ để thêm ảnh!", "Cảnh báo");
                else
                    showUploadDialog();
            });

            $("#luufileanh").click(function (evt) {
                showUploadDialog();
            });

            $("#inphieu").click(function (evt) {
                var madv = $("#madv").val();
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var sovaovien = $("#sovaovien").val();
                var hotenbenhnhan = $("#hoten").val();
                var tuoi = $("#tuoi").val();
                var gioitinh = $("#gioitinh").val();
                if (gioitinh.toString() == "true") {
                    gioitinh = "Nam";
                } else {
                    gioitinh = "Nữ";
                }
                var chandoan = $("#_chandoan").val();
                if (madv != "") {
                    var using_ekip = $("#use_ekip").val() == null ? 0 : $("#use_ekip").val();
                    var arr = [madv, sophieu, makhambenh, dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, sovaovien, hotenbenhnhan, tuoi, gioitinh, using_ekip, chandoan, sovaovien_noi, sovaovien_dt_noi, "0"];
                    var url = "inphieuthuthuatphauthuat?url=" + convertArray(arr) + "&chandoantruocttpt=" + $("#chandoan_truocttpt").val() + "&chandoansauttpt=" + $("#chandoan_sauttpt").val();
                    //HPG--- Xem truc tiep bao cao
                    debugger;
                    if ("${xemtructiep_bc}" == "1") {
                        var redirectWindow = window.open(url, '_blank');
                        redirectWindow.location;
                        return false;

                    } else
                        //-------End
                        $(location).attr('href', url);
                } else {
                    jAlert("Vui lòng chọn dịch vụ để kết quả", 'Cảnh báo');
                }
            });

            function decodeHTMLEntities(str) {
                if (str && typeof str === 'string') {
                    // strip script/html tags
                    str = str.replace(/<script[^>]*>([\S\s]*?)<\/script>/gmi, '');
                    str = str.replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gmi, '');
                    //element.innerHTML = str;
                    //str = element.textContent;
                    //element.textContent = '';
                }

                return str;
            }

            function goisolayhinhanh(mgrid, cur_id) {
                var IDS = mgrid.jqGrid("getDataIDs");
                var count = 5 + parseInt(cur_id - 1);
                var chuoitong = "";
                for (var i = cur_id - 1; i < count; i++) {
                    var id = IDS[i];
                    var ret = mgrid.jqGrid('getRowData', id);
                    if (ret.STT_HANGNGAY == undefined || ret.STT_HANGNGAY == null){
                        break;
                    }
                    var chuoi = "${Sess_TenPhong}" + "|" + ret.STT_HANGNGAY + "|" + ret.TENBENHNHAN + "|" + "0" + "|" + " " + "|" + "${Sess_DVTT}" + "|" + "" + "|" + " ";
                    chuoitong = chuoitong + chuoi + "@";
                }
                return chuoitong;
            }
            
            if("${Sess_DVTT}" != "96029"){
                loadBacSiTheoKhoa(SESS_PHONG_BAN);
            }

            $("#luu_tt").click(function (evt) {
                var ketQuaKiemTra = THAMSO_828449=="1"?kiemTraThoiGianYLenhHopLe():"1";
                if(ketQuaKiemTra!="1"){
                    jAlert(ketQuaKiemTra, 'Thông báo');
                    return false;
                }
                if($("#giokt").val().length != 8) {
                    return jAlert("Giờ kết quả không hợp lệ")
                }
                if($("#thoigiankt").val().length != 10) {
                    return jAlert("Ngày kết quả không hợp lệ")
                }
                if(!$("#ma_pp_vo_cam").val()) {
                    jAlert("Vui lòng chon mã phương pháp vô cảm", "Cảnh báo");
                    return false;
                }
                if (checkNguoiDocKetQua()) {
                    jAlert("Người đọc kết quả không hợp lệ", "Cảnh báo");
                    return false;
                }

                if ($('#ma_bacsipttt').val() == '' || $('#bacsipttt').val() == '') {
                    jAlert("Vui lòng chọn bác sĩ PT/TT", "Cảnh báo");
                    return false;
                }

                if ("${Sess_DVTT}" == "96029" && $('#ten_dv_ht').val() !== $('#phuongphappttt').val()) {
                    jAlert("Phương pháp PT/TT không đúng", "Cảnh báo");
                    return false;
                }

                var ngayketthucpttt = $("#thoigiankt").val() + " " + $("#giokt").val();//tvh thêm ngaykt pttt
                var ngaythuchien = $("#ngaypttt").val() + " " + $("#giopttt").val();
                if ("${thamso_82860467}" == "1" && (ngaythuchien < ngay_chi_dinh)) {
                    jAlert("Thời gian cập nhật kết quả nhỏ hơn thời gian chỉ định", "Cảnh báo")
                } else if ("${Sess_Admin}"  == "0" && "${Sess_DVTT}" != "96025" && $("#nguoithuchien").val() != "0" && (((($("#nguoithuchien").val().indexOf(";")) < 0 ? ($("#nguoithuchien").val() + ";") : $("#nguoithuchien").val())).indexOf("${Sess_UserID}" + ';')) < 0 && $("#nguoithuchien").val().trim() != "" && $("#daxetnghiem").prop("checked") == true) {
                    jAlert("Bạn không thể chỉnh sửa KQ thủ thuật phẫu thuật của nhân viên khác!", 'Thông báo');
                } //  vuthanhvinh.nan HISHTROTGG-30486
                else if (("${nhapthongtin_pttt}" == "1") && ($("#chandoan").val() == ""
                    || $("#chandoan_truocttpt").val() == ""
                    || $("#chandoan_sauttpt").val() == "" || $("#phuongphappttt").val() == ""
                    || $("#phuongphapvocam option:selected").val() == undefined)) {
                    jAlert("Chưa nhập đầy đủ thông tin PTTT!", 'Cảnh báo');
                } else {
					var ngayChiDinh = $('#ngaychidinh_kq').val();
					var gioChiDinh = $('#giochidinh_kq').val();
					var thoiGianChiDinh = ngayChiDinh + ' ' + gioChiDinh
					var ngayTHYL = $('#ngaypttt').val();
					var gioTHYL = $('#giopttt').val();
					var thoiGianTHYL = ngayTHYL + ' ' + gioTHYL;
					var ngayKQ = $('#thoigiankt').val();
					var gioKQ = $('#giokt').val();
					var thoiGianKQ = ngayKQ + ' ' + gioKQ;

					var momentChiDinh = moment(thoiGianChiDinh, ['DD/MM/YYYY HH:mm:ss']);
					var momentThucHienYLenh = moment(thoiGianTHYL, ['DD/MM/YYYY HH:mm:ss']);
					var momentKetQua = moment(thoiGianKQ, ['DD/MM/YYYY HH:mm:ss']);

					var soTheBHYT = $('#sothebhyt_ct').val();

					if (soTheBHYT !== '') {
						if (momentThucHienYLenh.diff(momentChiDinh, 'minutes') < 1) {
							Alert('THỜI GIAN THỰC HIỆN Y LỆNH : ' + thoiGianTHYL + '<br> KHÔNG ĐƯỢC NHỎ HƠN HOẶC BẰNG' + '<br>THỜI GIAN CHỈ ĐỊNH : ' + thoiGianChiDinh, 'Thông báo');
							return;
						}

						if (momentKetQua.diff(momentThucHienYLenh, 'minutes') < 1) {
							jAlert('THỜI GIAN KẾT QUẢ : ' + thoiGianKQ + '<br> KHÔNG ĐƯỢC NHỎ HƠN HOẶC BẰNG' + '<br>THỜI GIAN THỰC HIỆN Y LỆNH : ' + thoiGianTHYL, 'Thông báo');
							return;
						}

						if (momentKetQua.diff(momentThucHienYLenh, 'minutes') < 5) {
							jAlert('THỜI GIAN THỰC HIỆN Y LỆNH: ' + thoiGianTHYL + '<br> ĐẾN GIỜ ' + '<br>THỜI GIAN KẾT QUẢ : ' + thoiGianKQ + " KHÔNG ĐƯỢC NHỎ HƠN 5 PHÚT", 'Thông báo');
							return;
						}
					}

                    var madv = $("#madv").val();
                    var sophieu = $("#sophieu").val();
                    var makhambenh = $("#makhambenh").val();
                    var dvtt = "${Sess_DVTT}";
                    var noitru = $("#noitru").val();
                    var sttbenhan = $("#sttbenhan").val();
                    var sttdotdieutri = $("#sttdotdieutri").val();
                    var sttdieutri = $("#sttdieutri").val();
                    var chandoan = $("#_chandoan").val();
                    var phuongphappttt = $("#phuongphappttt").val();
                    var phuongphapvocam = $("#phuongphapvocam").val();
                    var PPVC = $("#ma_pp_vo_cam").val();
                    //var phuongphapvocam = $("#phuongphapvocam option:selected").text();
                    var bacsipttt = $("#bacsipttt").val();
                    var bacsigayme = $("#bacsigayme").val();
                    var catchisau7ngay = $("#catchisau7ngay").val().trim();
                    var trinhtupttt = $("#trinhtupttt").val().trim();
                    var trinhtupttt_xml5 = $('<textarea />').html(trinhtupttt).text();
                    var trinhtupttt_xml5_luu = decodeHTMLEntities(trinhtupttt_xml5);
                    var ngaypttt = convertStr_MysqlDate($("#ngaypttt").val());
                    var nguoithuchien = "${Sess_UserID}";
                    var giopttt = $("#giopttt").val();
                    var taibien = "0";
                    var tuvong = "0";
                    var check = $("#cb_gaymehoisuc").prop("checked");
                    var mabenhlytruocttpt = $("#mabenhly_truocttpt").val();
                    var mabenhlysauttpt = $("#mabenhly_sauttpt").val();
                    // var ma_bs_ttpt = $("#cbbacsittpt").val();
                    // var ma_bs_gayme = $("#cbbacsigayme").val();
                    var ma_bs_ttpt = $("#ma_bacsipttt").val();
                    var ma_bs_gayme = $("#ma_bacsigayme").val();
                    if (check == true) {
                        taibien = "1";
                    }
                    check = $("#cb_nhiemkhuan").prop("checked");
                    if (check == true) {
                        taibien = "2";
                    }
                    check = $("#cb_khac").prop("checked");
                    if (check == true) {
                        taibien = "3";
                    }
                    check = $("#cb_trenban").prop("checked");
                    if (check == true) {
                        var tuvong = "1";
                    }
                    check = $("#cb_trong24gio").prop("checked");
                    if (check == true) {
                        var tuvong = "2";
                    }
                    var ngaygiopttt = ngaypttt + " " + giopttt;

					if($('#phuongphapvocam').val() == null) {
						jAlert("Vui lòng chọn PP vô cảm");
						return false;
					}


                    var mabenhnhan = $("#mabenhnhan").val();
                    var ngaythangnam =  convertStr_MysqlDate("${ngayhientai}");
                    var thoigian_thaotac = ngaythangnam + " " + "${giohientai}";
                    if("${ts820897}" == "1") {
                        var luotdottpt = $("#luotdottpt").val().trim();
                    } else {
                        var luotdottpt = " ";
                    }

                    //Kiểm tra khóa đã khám - ra viện khóa bảng kê
                    var ktraAjax = $.ajax({type: "POST", url: "cmu_post_cmu_kttrangthai_cls", async: false,
                        data:  {url: [dvtt, sovaovien, sovaovien_noi, sovaovien_dt_noi, noitru].join('```')}
                    });
                    if(ktraAjax.status != 200 || ktraAjax.responseText > 0){
                        var alert = ktraAjax.status != 200 ? ("Lỗi: " + ktraAjax.statusText) : "Bệnh nhân đã khóa dữ liệu, không thể thao tác.";
                        jAlert(alert);
                        return;
                    }//

                    var thoigiankt = convertStr_MysqlDate($("#thoigiankt").val());
                    var giokt = $("#giokt").val();
                    var ngaygiopttt_kt = thoigiankt + " " + giokt;


                    if (madv != "") {
                        luuNguoiDocKetQua(sophieu, noitru==1?sovaovien_noi:sovaovien, sovaovien_dt_noi, noitru, madv);
                        //---------------
                        if ("${suaketqua_cls}" == "1") {
                            var arr1 = [makhambenh, sophieu, "${Sess_DVTT}", noitru, sttbenhan, sttdotdieutri, sttdieutri, 0]
                            var url1 = "hpg_pttt_trangthai_thanhtoan?url=" + convertArray(arr1);
                            $.ajax({
                                url: url1
                            }).done(function (data) {
                                if (data == "2") {
                                    jAlert("Bệnh nhân đã thanh toán viện phí. Không được thay đổi kết quả.", 'Cảnh báo');
                                    return;
                                } else {
                                    $.post("ttpt_capnhat_ketqua_svv", {
                                        madv: madv,
                                        sophieu: sophieu,
                                        makhambenh: makhambenh,
                                        dvtt: dvtt,
                                        noitru: noitru,
                                        sttbenhan: sttbenhan,
                                        sttdotdieutri: sttdotdieutri,
                                        sttdieutri: sttdieutri,
                                        chandoan: chandoan,
                                        phuongphapvocam: $("#phuongphapvocam option:selected").text(),
                                        phuongphappttt: phuongphappttt,
                                        bacsipttt: bacsipttt,
                                        bacsigayme: bacsigayme,
                                        catchisau7ngay: catchisau7ngay,
                                        trinhtupttt: trinhtupttt,
                                        ngaygiopttt: ngaygiopttt,
                                        taibien: taibien,
                                        tuvong: tuvong,
                                        sovaovien: sovaovien,
                                        sovaovien_noi: sovaovien_noi,
                                        sovaovien_dt_noi: sovaovien_dt_noi,
                                        nguoithuchien: nguoithuchien,
                                        mabenhlytruocttpt: mabenhlytruocttpt,
                                        mabenhlysauttpt: mabenhlysauttpt,
                                        trinhtupttt_xml5: trinhtupttt_xml5_luu,
                                        ma_bacsittpt: ma_bs_ttpt,
                                        ma_bacsigayme: ma_bs_gayme,
                                        luotdottpt: luotdottpt
                                        ,moTaDienBienBenh: $("#moTaDienBienBenh").val()
                                        ,PPVC: PPVC
                                        ,ngaygiopttt_kt: ngaygiopttt_kt
                                        ,vetThuongTaiPhat: $("#chkVetThuongTaiPhat").getCheckBoxValue()
                                    })
                                        .done(function () {
                                            if("${Sess_DVTT}" == '96154' || "${Sess_DVTT}" == '96001' || "${Sess_DVTT}" == '96029' || "${Sess_DVTT}" == '96172' || "${Sess_DVTT}" == '96167') {
                                                $.post('cmu_post_CMU_UPT_CHANDOAN_TTPT', {
                                                    url: [
                                                        "${Sess_DVTT}",
                                                        sovaovien,
                                                        sophieu,
                                                        $('#chandoan_truocttpt').val(),
                                                        $('#chandoan_sauttpt').val(),
                                                        $('#maupttt').val()
                                                    ].join('```')
                                                }).done(function (data) {
                                                    if(data === '1') {
                                                        reload_grid();
                                                        jAlert("Cập nhật thành công", 'Thông báo');
                                                    }
                                                })
                                            } else {
                                                reload_grid();
                                                //reload_grid_dichvu();
                                                jAlert("Cập nhật thành công", 'Thông báo');
                                            }

                                        });
                                    var dscchn = '';
                                    var manhanvien = '';
                                    var tennhan = '';
                                    var vitri = '';
                                    if($("#bacsipttt").val().trim() != '') {
                                        dscchn+=$("#bacsipttt_cchn").val()+';'
                                        manhanvien+=$("#ma_bacsipttt_cchn").val()+';'
                                        tennhan+=$("#bacsipttt").val()+';'
                                        vitri += 'bacsipttt;';
                                    }
                                    if($("#bacsigayme").val().trim() != '') {
                                        dscchn+=$("#bacsigayme_cchn").val()+';'
                                        manhanvien+=$("#ma_bacsigayme_cchn").val()+';'
                                        tennhan+=$("#bacsigayme").val()+';'
                                        vitri += 'bacsigayme;';
                                    }
                                    $.post("cmu_post", {
                                        url: ["${Sess_DVTT}",madv,sophieu,noitru==1?sovaovien_noi:sovaovien,noitru==1?sovaovien_dt_noi:0,dscchn
                                            ,manhanvien,tennhan,vitri,'CMU_LUUCCHN_EKIP'].join("```")
                                    })
                                        .done(function () {
                                        });
                                }
                            });
                        } else   //---End
                            $.post("ttpt_capnhat_ketqua_svv", {
                                madv: madv,
                                sophieu: sophieu,
                                makhambenh: makhambenh,
                                dvtt: dvtt,
                                noitru: noitru,
                                sttbenhan: sttbenhan,
                                sttdotdieutri: sttdotdieutri,
                                sttdieutri: sttdieutri,
                                chandoan: chandoan,
                                phuongphapvocam: phuongphapvocam,
                                phuongphappttt: phuongphappttt,
                                bacsipttt: bacsipttt,
                                bacsigayme: bacsigayme,
                                catchisau7ngay: catchisau7ngay,
                                trinhtupttt: trinhtupttt,
                                ngaygiopttt: ngaygiopttt,
                                taibien: taibien,
                                tuvong: tuvong,
                                sovaovien: sovaovien,
                                sovaovien_noi: sovaovien_noi,
                                sovaovien_dt_noi: sovaovien_dt_noi,
                                nguoithuchien: nguoithuchien,
                                mabenhlytruocttpt: mabenhlytruocttpt,
                                mabenhlysauttpt: mabenhlysauttpt,
                                trinhtupttt_xml5: trinhtupttt_xml5_luu,
                                ma_bacsittpt: ma_bs_ttpt,
                                ma_bacsigayme: ma_bs_gayme,
                                luotdottpt: luotdottpt
                                ,moTaDienBienBenh: $("#moTaDienBienBenh").val()
                                ,PPVC: PPVC
                                ,ngaygiopttt_kt: ngaygiopttt_kt
                                ,vetThuongTaiPhat: $("#chkVetThuongTaiPhat").getCheckBoxValue()
                            })
                                .done(function () {
                                    if("${Sess_DVTT}" == '96154' || "${Sess_DVTT}" == '96001' || "${Sess_DVTT}" == '96029' || "${Sess_DVTT}" == '96172' || "${Sess_DVTT}" == '96167') {
                                        $.post('cmu_post_CMU_UPT_CHANDOAN_TTPT', {
                                            url: [
                                                "${Sess_DVTT}",
                                                sovaovien,
                                                sophieu,
                                                $('#chandoan_truocttpt').val(),
                                                $('#chandoan_sauttpt').val(),
                                                $('#maupttt').val()
                                            ].join('```')
                                        }).done(function (data) {
                                            if(data === '1') {
                                                reload_grid();
                                                jAlert("Cập nhật thành công", 'Thông báo');
                                            }
                                        })
                                    } else {
                                        reload_grid();
                                        //reload_grid_dichvu();
                                        jAlert("Cập nhật thành công", 'Thông báo');
                                    }
                                });
                        var dscchn = '';
                        var manhanvien = '';
                        var tennhan = '';
                        var vitri = '';
                        if($("#bacsipttt").val().trim() != '') {
                            dscchn+=$("#bacsipttt_cchn").val()+';'
                            manhanvien+=$("#ma_bacsipttt_cchn").val()+';'
                            tennhan+=$("#bacsipttt").val()+';'
                            vitri += 'bacsipttt;';
                        }
                        if($("#bacsigayme").val().trim() != '') {
                            dscchn+=$("#bacsigayme_cchn").val()+';'
                            manhanvien+=$("#ma_bacsigayme_cchn").val()+';'
                            tennhan+=$("#bacsigayme").val()+';'
                            vitri += 'bacsigayme;';
                        }
                        $.post("cmu_post", {
                            url: ["${Sess_DVTT}",madv,sophieu,noitru==1?sovaovien_noi:sovaovien,noitru==1?sovaovien_dt_noi:0,dscchn
                                ,manhanvien,tennhan,vitri,'CMU_LUUCCHN_EKIP'].join("```")
                        })
                            .done(function () {
                            });

                        var arr2 = [dvtt, nguoithuchien, thoigian_thaotac, sophieu, madv, makhambenh == "" ? "0" : makhambenh, mabenhnhan, noitru, sttbenhan == "" ? "0" : sttbenhan,sttdotdieutri == "" ? "0" : sttdotdieutri, sttdieutri == "" ? "0" : sttdieutri, sovaovien, sovaovien_dt_noi == "" ? "0" : sovaovien_dt_noi, bacsipttt, ma_bs_ttpt, bacsigayme, ma_bs_gayme];
                        var url2 = "luulog_capnhat_ttpt";
                        $.post(url2, {url: convertArray(arr2)}).done(function () {
                            // jAlert('Lưu log thành công', 'Thông báo!')
                        });
                    }

                }
            });
            $("#chkVetThuongTaiPhat").change(function (evt) {
                $(this).val($(this).is(':checked') ? 1 : 0);
            });
            function kiemTraThoiGianYLenhHopLe() {
                var objThoiGianChiDinhChiTiet = {name: "Thời gian chỉ định", value: $("#ngaycdttpt").val()||($("#ngaychidinh_kq").val() + " " + $("#giochidinh_kq").val())};
                var objThoiGianThucHienYLenh = {name: "Thời gian thực hiện Y lệnh", value: $("#ngaypttt").val() + " " + $("#giopttt").val()};
                var objThoiGianKetQua = {name: "Thời gian kết quả", value: $("#thoigiankt").val() + " " + $("#giokt").val()};
                //ThoiGianKetQua > ThoiGianThucHienYLenh > ThoiGianChiDinhChiTiet
                var objCompare = validateAndCompareDatesToMinute(objThoiGianKetQua,objThoiGianThucHienYLenh,objThoiGianChiDinhChiTiet);
                if ((objCompare.errorCode=="-1" || objCompare.errorCode=="-2") && objCompare.objects.length>0) {
                    if(objCompare.errorCode=="-1") {
                        return "Lỗi định dạng " + objCompare.objects[0].name;
                    } else {
                        return "Lỗi " + objCompare.objects[0].name + " phải sau " + objCompare.objects[1].name + " tính đến phút";
                    }
                }
                return "1"; //Hợp lệ
            }
            $("#inphieu_ttpt").click(function (evt) {
                var madv = $("#madv").val();
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var sovaovien = $("#sovaovien").val();
                var hotenbenhnhan = $("#hoten").val();
                var tuoi = $("#tuoi").val();
                var gioitinh = $("#gioitinh").val();
                if (gioitinh.toString() == "true") {
                    gioitinh = "Nam";
                } else {
                    gioitinh = "Nữ";
                }
                var chandoan = $("#chandoan").val();
                if (madv != "") {
                    var using_ekip = $("#use_ekip").val() == null ? 0 : $("#use_ekip").val();
                    var arr = [madv, sophieu, makhambenh, dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, sovaovien, hotenbenhnhan, tuoi, gioitinh, using_ekip, chandoan,sovaovien_noi, sovaovien_dt_noi, "0"];
                    var chandoan_truocttpt = $("#icd_truocttpt").val() + " - " + $("#chandoan_truocttpt").val();
                    var chandoan_sauttpt = $("#icd_sauttpt").val() + " - " + $("#chandoan_sauttpt").val();
                    var url;

					var tenDV = $("#phuongphappttt").val();
					if((tenDV.toLowerCase().indexOf('răng') !== -1) && ("${Sess_DVTT}" === '96154' || "${Sess_DVTT}" === '96155' || "${Sess_DVTT}" === '96176')) {
						var arr1 = [$('#tenkhoa_ct').val(), madv, sophieu, makhambenh, noitru, sttbenhan, sttdotdieutri, sttdieutri, sovaovien, hotenbenhnhan, tuoi, gioitinh];
						var param = ["khoa", "madv", "sophieudichvu","makhambenh","noitru","sttbenhan","sttdotdieutri", "sttdieutri", "sovaovien", "hotennguoibenh", "tuoi", "gioitinh"];
						url = "cmu_injasper?url=" + convertArray(arr1) + "&param=" + convertArray(param) + "&loaifile=rtf&jasper=rp_phieuphauthuat_thuthuat_new_96154";
					} else {
						url = "inphieuthuthuatphauthuat?url=" + convertArray(arr) + "&chandoantruocttpt=" + chandoan_truocttpt + "&chandoansauttpt=" + chandoan_sauttpt + "&phuongphapvocam=" + $("#phuongphapvocam option:selected").text();
					}

					//HPG--- Xem truc tiep bao cao
                    if ("${xemtructiep_bc}" == "1") {
                        var redirectWindow = window.open(url, '_blank');
                        redirectWindow.location;
                        return false;

                    } else
                        //-------End
                        $(location).attr('href', url);
                } else {
                    jAlert("Vui lòng chọn dịch vụ để kết quả", 'Cảnh báo');
                }
            });
            // KGG thêm in phiếu chứng nhận thủ thuật
            $("#inphieuchungnhan").click(function (evt) {
                var madv = $("#madv").val();
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var sovaovien = $("#sovaovien").val();
                var hotenbenhnhan = $("#hoten").val();
                var tuoi = $("#tuoi").val();
                var gioitinh = $("#gioitinh").val();
                if (gioitinh.toString() == "true") {
                    gioitinh = "Nam";
                } else {
                    gioitinh = "Nữ";
                }
                if (madv != "") {
                    var arr = [madv, sophieu, makhambenh, dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, sovaovien, hotenbenhnhan, tuoi, gioitinh];
                    var url = "inphieuchungnhanttpt?url=" + convertArray(arr);
                    $(location).attr('href', url);
                } else {
                    jAlert("Vui lòng chọn dịch vụ để kết quả", 'Cảnh báo');
                }
            });
            // KGG thêm in phiếu chứng nhận thủ thuật
            $("#ingiaybaomo").click(function (evt) {
                if ($("#hoten").val() != "") {
                    var mabenhnhan = $("#mabenhnhan").val();
                    var sophieu = $("#sophieu").val();
                    var sovaovienmoi = sovaovien;
                    if (noitru == "1") {
                        var sovaovienmoisovaovienmoi = sovaovien_noi;
                    }
                    var id1 = $("#list_ttpt_bhyt").jqGrid('getGridParam', 'selrow');
                    var ret = $("#list_ttpt_bhyt").jqGrid('getRowData', id1);
                    var madv = ret.MA_DV;
                    var chandoan = $("#_chandoan").val();
                    var url = "ingiaybaomo?sophieu=" + sophieu + '&madv=' + madv + '&mabenhnhan=' + mabenhnhan + '&sovaovien=' + sovaovienmoi + '&sovaovien_dt_noi=' + sovaovien_dt_noi + '&chandoan=' + chandoan;
                    if (madv) {
                        $(location).attr('href', url);
                    } else jAlert("Chưa chọn dịch vụ", "Cảnh báo");
                }
            });
            // BDH thêm in phiếu gây mê hồi sức
            $("#inphieugaymehoisuc").click(function (evt) {
                if ($("#hoten").val() != "") {
                    var hoten = $("#hoten").val();
                    var tuoi = $("#tuoi").val();
                    var gioitinh = $("#gioitinh").val();
                    var id1 = $("#list_ttpt_bhyt").jqGrid('getGridParam', 'selrow');
                    var ret = $("#list_ttpt_bhyt").jqGrid('getRowData', id1);
                    var ten_dichvu = ret.TEN_DV_HT;
                    var ma_dv      = ret.MA_DV;
                    var chandoan = $("#_chandoan").val();
                    var khoa = $("#tenkhoa").val();
                    var sovaovien = $("#sovaovien").val();
                    if (noitru == "1") {
                        sovaovien = sovaovien_noi;
                    }
                    var phieu_dv = $("#sophieu").val();
                    var url = "inphieugaymehoisuc?hoten=" + hoten + '&tuoi=' + tuoi + '&gioitinh=' + gioitinh + '&ten_dichvu=' + ten_dichvu + '&chandoan=' + chandoan + '&khoa=' + khoa + '&sovaovien=' + sovaovien + '&phieu_dv=' + phieu_dv + '&ma_dv=' + ma_dv;
                    if (ten_dichvu) {
                        $(location).attr('href', url);
                    } else jAlert("Chưa chọn dịch vụ", "Cảnh báo");
                }
            });

            $("#ingiaycamdoan").click(function (evt) {
                if ($("#hoten").val() != "") {
                    var mabenhnhan = $("#mabenhnhan").val();
                    var sophieuthanhtoan = $("#sophieuthanhtoan").val();
                    var arr = [mabenhnhan, sophieuthanhtoan];
                    var url = "ingiaycamdoan?url=" + convertArray(arr);
                    $(location).attr('href', url);
                }
            });
            // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT
            $("#phongban").change(function (evt) {
                var url = "layphongbenh_theokhoaxn?khoa=" + $("#phongban").val() + "&dvtt=${Sess_DVTT}";
                $.ajax({
                    url: url
                }).done(function (data) {
                    if (data) {
                        $("#phongbenh").empty();
                        $.each(data, function (i) {
                            $("<option value='" + data[i].MA_PHONG_BENH + "'>" + data[i].TEN_PHONG_BENH + "</option>").appendTo("#phongbenh");
                        });
                    }
                });
                $("#phongbenh").val(-1);
                reload_grid();
            });
            $("#phongbenh").change(function (evt) {
                reload_grid();
            });
            $("#doituong").change(function (evt) {
                reload_grid();
            });
            // End ĐắkLắk
            $("#daxetnghiem").change(function (evt) {
                var daxetnghiem = $("#daxetnghiem").prop("checked");
                $("#phauthuat_lenlich").prop("disabled", daxetnghiem);
                reload_grid();
            });
            $("#ngaythuchien").change(function (evt) {
                reload_grid();
            });
            reload_grid();
            $("#maupttt").change(function (evt) {
                var id = $("#maupttt").val();
                if (id !== "0") {
                    var url = "select_maupttt_theoma?ma=" + id + "&dvtt=${Sess_DVTT}";
                    $.ajax({
                        url: url
                    }).done(function (data) {
                        $('#trinhtupttt').val(data);
                    });

                }
            });
            $("#list_chitiettiencongthuchien").jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 312,
                width: 600,
                colNames: ["Người thực hiện", "Vai trò", "Số tiền", "Mã nhân viên"],
                colModel: [
                    {name: 'TEN_NHANVIEN', index: 'TEN_NHANVIEN', width: 30},
                    {name: 'TEN_VAITRO', index: 'TEN_VAITRO', width: 30},
                    {name: 'SO_TIEN', index: 'SO_TIEN', width: 30},
                    {name: 'MA_NHANVIEN', index: 'MA_NHANVIEN', width: 30, hidden: true}
                ],
                sortname: 'TEN_VAITRO',
                sortorder: "asc",
                caption: "Ekip thực hiện phẫu thuật - thủ thuật",
                ignoreCase: true,
                onSelectRow: function (id) {
                    if (id) {
                        var ret = $("#list_chitiettiencongthuchien").jqGrid('getRowData', id);
                        $("#manhanvien").val(ret.MA_NHANVIEN);
                    }
                }
            });
            $("#themnguoithuchien").click(function (evt) {
                var dvtt = "${Sess_DVTT}";
                var stt_benhan = $("#sttbenhan").val();//MA_KHAM_BENH
                if (stt_benhan === undefined || stt_benhan.length < 1) {
                    stt_benhan = $("#makhambenh").val();
                }
                var mabenhnhan = $("#mabenhnhan").val();
                var manhanvien = $("#nguoithuchien_ekip").val();
                var maloaidichvu = $("#madichvu").val();
                //var tenloaidichvu = $("#tendichvu").val();
                var phieudichvu = $("#sophieu").val();
                var ma_tiencong = $("#tiencongthuchien").val();
                var ngay_thuchien = convertStr_MysqlDate($("#ngaythuchien").val());
                var nguoi_tao = "${Sess_UserID}";
                var ngaygio_tao = convertStr_MysqlDate("${ngayhientai}");
                var ekip_mau = $("#ekip_mau").val();
                if (ma_tiencong === "-1" && ekip_mau === "-1") {
                    jAlert("Vui lòng chọn Vai trò thực hiện!", 'Thông báo');
                }
                if (manhanvien === "-1" && ekip_mau === "-1") {
                    jAlert("Vui lòng chọn người thực hiện!", 'Thông báo');
                }
                if (ekip_mau == -1 && manhanvien != -1 && ma_tiencong != -1) {
                    var arr = [dvtt, stt_benhan, mabenhnhan, manhanvien, maloaidichvu, phieudichvu, ma_tiencong, ngay_thuchien, nguoi_tao, ngaygio_tao];
                    var url = "bdh_chamcongthuchien_insert?url=" + convertArray(arr);
                    $.ajax({
                        url: url
                    }).done(function () {
                        jAlert("Thêm thành công!", 'Thông báo');
                        $("#list_chitiettiencongthuchien").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                    });
                } else if (ekip_mau != -1) {
                    var arr = [dvtt, stt_benhan, mabenhnhan, ekip_mau, maloaidichvu, phieudichvu, ngay_thuchien, nguoi_tao, ngaygio_tao];
                    var url = "bdh_chamcongthuchien_insert_theoekipmau?url=" + convertArray(arr);
                    $.ajax({
                        url: url
                    }).done(function (data) {
                        if (data === 1) {
                            jAlert("Thêm người thực hiện từ EKIP mẫu thành công!", 'Thông báo');
                            $("#list_chitiettiencongthuchien").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                        } else {
                            jAlert("EKIP mẫu rỗng. Không thể thêm!", 'Thông báo');
                        }
                    });
                }
                ;
            });
            $("#xoanguoithuchien").click(function (evt) {
                var dvtt = "${Sess_DVTT}";
                var stt_benhan = $("#sttbenhan").val();
                if (stt_benhan === undefined || stt_benhan.length < 1) {
                    stt_benhan = $("#makhambenh").val();
                }
                var mabenhnhan = $("#mabenhnhan").val();
                var manhanvien = $("#manhanvien").val();
                var maloaidichvu = $("#madichvu").val();
                var phieudichvu = $("#sophieu").val();
                var str = [dvtt, stt_benhan, mabenhnhan, manhanvien, maloaidichvu, phieudichvu];
                var url = "bdh_chamcongthuchien_delete_nhanvien?url=" + convertArray(str);
                $.ajax({
                    url: url
                }).done(function () {
                    jAlert("Xóa thành công!", 'Thông báo');
                    $("#list_chitiettiencongthuchien").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                });
            });
            $("#xoanguoithuchien_all").click(function (evt) {
                var dvtt = "${Sess_DVTT}";
                var stt_benhan = $("#sttbenhan").val();
                if (stt_benhan === undefined || stt_benhan.length < 1) {
                    stt_benhan = $("#makhambenh").val();
                }
                var mabenhnhan = $("#mabenhnhan").val();
                var maloaidichvu = $("#madichvu").val();
                var phieudichvu = $("#sophieu").val();
                var str = [dvtt, stt_benhan, mabenhnhan, maloaidichvu, phieudichvu];
                var url = "bdh_chamcongthuchien_delete_all?url=" + convertArray(str);
                $.ajax({
                    url: url
                }).done(function () {
                    jAlert("Xóa thành công!", 'Thông báo');
                    $("#list_chitiettiencongthuchien").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                });
            });
            $("#khoaphong_ekip").change(function (evt) {
                $("#nguoithuchien_ekip").empty();
                $.getJSON("bdh_nhanvientheophongban_select", {maphongban: $("#khoaphong_ekip").val()}, function (data) {
                    $("<option value='-1'>--  Chọn nhân viên  --</option>").appendTo("#nguoithuchien_ekip");
                    if (data && data.length > 0) {
                        $.each(data, function (i) {
                            $("<option value='" + data[i].MA_NHANVIEN + "'>" + data[i].TEN_NHANVIEN + "</option>").appendTo("#nguoithuchien_ekip");
                        });
                    }
                });
            });

            // VNPT VLG
            $("#lamdungcu").click(function (evt) {
                dialog_lamdungcu.dialog("open");
                //var ret = $("#list_ttpt_bhyt").jqGrid('getRowData', id);
                //$("#madv").val(ret.MA_DV);
                var sophieu = $("#sophieu").val();
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var makhambenh = $("#makhambenh").val();
                var madv = $("#madv").val();
                var dvtt = "${Sess_DVTT}";
                var arr = [sophieu, noitru, sttbenhan, sttdotdieutri, sttdieutri, makhambenh, madv, dvtt, "0", sovaovien, sovaovien_noi, sovaovien_dt_noi];
                var url3 = "pttt_select_ketqua_svv?url=" + convertArray(arr);
                $.getJSON(url3, function (result) {
                    $.each(result, function (i, field) {
                        $("#frm_kq_id_ekip").val(field.ID_EKIP);
                        $("#frm_kq_so_phieu_dichvu").val(field.SO_PHIEU_DICHVU);
                        $("#frm_kq_ma_dv").val(field.MA_DV);
                        $("#frm_kq_mabenhnhan").val(field.MABENHNHAN);
                        $("#frm_kq_sovaovien").val(field.SOVAOVIEN);
                        $("#frm_kq_sovaovien_dt").val(field.SOVAOVIEN_DT);
                        $("#frm_kq_noitru").val(field.NOITRU);

                        $("#chandoan").val(field.CHANDOANSAUPTTT);
                        if (field.PHUONGPHAP_TT_PT != null) {
                            $("#phuongphappttt").val(field.PHUONGPHAP_TT_PT);
                        }
                        $("#phuongphapvocam").val(field.PHUONGPHAP_VOCAM);
                        //$("#ma_pp_vo_cam").val(field.PPVC);
                        $("#bacsipttt").val(field.BACSI_PTTT);
                        $("#bacsigayme").val(field.BACSI_GAYME);
                        $("#catchisau7ngay").val(field.CATCHI_SAU7NGAY);
                        $("#trinhtupttt").val(field.TRINHTU_TT_PT);
                        $("#ngaypttt").val(field.NGAYPTTT_1);
                        $("#giopttt").val(field.GIOPTTT_1);
                        if (field.TAIBIEN == "1") {
                            $("#cb_gaymehoisuc").prop("checked", true);
                            $("#cb_nhiemkhuan").prop("checked", false);
                            $("#cb_khac").prop("checked", false);
                        } else if (field.TAIBIEN == "2") {
                            $("#cb_nhiemkhuan").prop("checked", true);
                            $("#cb_gaymehoisuc").prop("checked", false);
                            $("#cb_khac").prop("checked", false);
                        } else if (field.TAIBIEN == "3") {
                            $("#cb_khac").prop("checked", true);
                            $("#cb_nhiemkhuan").prop("checked", false);
                            $("#cb_gaymehoisuc").prop("checked", false);
                        } else {
                            $("#cb_khac").prop("checked", false);
                            $("#cb_nhiemkhuan").prop("checked", false);
                            $("#cb_gaymehoisuc").prop("checked", false);
                        }

                        if (field.TUVONG == "1") {
                            $("#cb_trenban").prop("checked", true);
                            $("#cb_trong24gio").prop("checked", false);
                        } else if (field.TUVONG == "2") {
                            $("#cb_trong24gio").prop("checked", true);
                            $("#cb_trenban").prop("checked", false);
                        } else {
                            $("#cb_trong24gio").prop("checked", false);
                            $("#cb_trenban").prop("checked", false);
                        }

                        var using_ekip = field.ID_EKIP != null;
                        $("#bacsipttt").prop('disabled', using_ekip);
                        $("#cbbacsittpt").prop('disabled', using_ekip);
                        $("#khoabsttpt").prop('disabled', using_ekip);
                        $("#bacsigayme").prop('disabled', using_ekip);
                        $("#cbbacsigayme").prop('disabled', using_ekip);
                        $("#khoabsgayme").prop('disabled', using_ekip);
                        $("#use_ekip").val(using_ekip);
                        $("#chkVetThuongTaiPhat").setCheckBoxValue(field.VET_THUONG_TP);
                    });
                });


                var arr = [sophieu, dvtt, madv, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                var url = 'pttt_danhsach_hinhanh?url=' + convertArray(arr);
                $("#list_hinhanhttpt").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');

            });
            // STG
            $("#giopttt").click(function (evt) {
                gioPtttTimerChange();
                //showtime_giopttt_cancel = (showtime_giopttt_cancel == 1 ? 0 : 1);
            });

            $("#giothuchien_cls").click(function (evt) {
                gioThucHienClsTimerChange();
                //showtime_giothuchien_cls_cancel = (showtime_giothuchien_cls_cancel == 1 ? 0 : 1);
            });

            dalBBduyetphauthuat = $("#dalBBduyetphauthuat").dialog({
                autoOpen: false,
                width: 1000,
                modal: true,
                resizable: false,
                position: {my: "center", at: "center", of: window}
            });

            $("#bbduyetphauthuat").click(function (evt) {
                var madichvu = $("#madv").val();
                if (madichvu != "") {
                    var urlget = "layBBDuyetPhauThuat?url=" + convertArray(getObjectBBDuyetPhauThuat());
                    $.getJSON(urlget, function (result) {
                        $.each(result, function (i, field) {
                            $("#thilucmatphai").val(field.THILUCMATPHAI);
                            $("#thilucmattrai").val(field.THILUCMATTRAI);
                            $("#nhanapmatphai").val(field.NHANAPMATPHAI);
                            $("#nhanapmattrai").val(field.NHANAPMATTRAI);
                            $("#cdphauthuatphoihop").val(field.CDPHAUTHUATPHOIHOP);
                            $("#phauthuatphoihop").val(field.PHAUTHUATPHOIHOP);
                            $("#phauthuatvienphu").val(field.PHAUTHUATVIENPHU);
                            $("#gayte_gayme").prop("checked", field.GAYTE_GAYME == "1" ? true : false);
                            $("#ykientruongkhoa").val(field.YKIENTRUONGKHOA);
                        });
                    });
                    dalBBduyetphauthuat.dialog("open");
                } else {
                    jAlert("Chưa chọn dịch vụ", "Thông báo");
                }
            });
            $("#luuMayTTPT").click(function (evt) {
                var sophieu = $("#sophieu").val();
                var noitru = $("#noitru").val();
                var madv = $("#madv").val();
                if (sophieu != "" && madv != "" && (sovaovien != "" || sovaovien_noi != "")) {
                    $.post("luu-may-ttpt", {
                        sophieu: sophieu,
                        madv: madv,
                        noitru: noitru,
                        sovaovien: sovaovien,
                        sovaovien_noi: sovaovien_noi,
                        sovaovien_dt_noi: sovaovien_dt_noi,
                        stt_may_ttpt: ($("#sttMayTTPT").val())
                    }).done(function (data) {
                        if (data == "1") {
                            jAlert("Cập nhật thành công!", "Thông báo");
                        } else {
                            jAlert("Vui lòng kiểm tra lại thông tin!", "Thông báo");
                        }
                    }).fail(function () {
                        jAlert("Vui lòng thử lại!", "Thông báo");
                    });
                }
            });
            function getObjectBBDuyetPhauThuat() {
                var sophieu = $("#sophieu").val();
                var madichvu = $("#madv").val();
                var noitru = $("#noitru").val();
                var thilucmatphai = $("#thilucmatphai").val();
                var thilucmattrai = $("#thilucmattrai").val();
                var nhanapmatphai = $("#nhanapmatphai").val();
                var nhanapmattrai = $("#nhanapmattrai").val();
                var cdphauthuatphoihop = $("#cdphauthuatphoihop").val();
                var phauthuatphoihop = $("#phauthuatphoihop").val();
                var phauthuatvienphu = $("#phauthuatvienphu").val();
                var gayte_gayme = $("#gayte_gayme").prop('checked') == true ? "1" : "0";
                var ykientruongkhoa = $("#ykientruongkhoa").val();
                var arr = [sophieu, madichvu, noitru, thilucmatphai, thilucmattrai, nhanapmatphai, nhanapmattrai, cdphauthuatphoihop, phauthuatphoihop, phauthuatvienphu, gayte_gayme, ykientruongkhoa, "0"];
                return arr;
            }

            $("#luu_bbduyet").click(function (evt) {
                var madichvu = $("#madv").val();
                if (madichvu != "") {
                    var url = "luuBBduyetphauthuat?url=" + convertArray(getObjectBBDuyetPhauThuat());
                    $.ajax({
                        url: url
                    }).done(function (data) {
                        if (data == "1") {
                            jAlert("Lưu thành công", "Thông báo");
                        }
                    });
                } else {
                    jAlert("Chưa chọn dịch vụ", "Thông báo");
                }
            });

            $("#inphieu_bbduyet").click(function (evt) {
                var madichvu = $("#madv").val();
                if (madichvu != "") {
                    var maBHYT = $("#sothebhyt").val();
                    var hoten = $("#hoten").val();
                    var tuoi = $("#tuoi").val();
                    var gioitinh = $("#gioitinh").val();
                    var diachi = $("#diachi").val();
                    // var lydovaovien = $("#_chandoan").val();
                    var chandoan = $("#_chandoan").val();
                    var mabenhnhan = $("#mabenhnhan").val();
                    var arr = [maBHYT, hoten, tuoi, gioitinh, diachi, chandoan, mabenhnhan, "0"];
                    var url = "inphieuBBduyetphauthuat?url=" + convertArray(getObjectBBDuyetPhauThuat()) + "&thongtin=" + convertArray(arr);
                    $(location).attr('href', url);
                } else {
                    jAlert("Chưa chọn dịch vụ", "Thông báo");
                }
            });
            //TGGDEV-53543 BGG_24017 Tạo các phiếu bồi dưỡng thủ thuật, phẫu thuật cho từng bệnh nhân thực hiện.
            $("#inphieuekip").click(function (evt) {
                var madv = $("#madv").val();
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var sovaovien = $("#sovaovien").val();
                var hotenbenhnhan = $("#hoten").val();
                var tuoi = $("#tuoi").val();
                var stt_benhan = $("#sttbenhan").val();//MA_KHAM_BENH
                if (stt_benhan === undefined || stt_benhan.length < 1) {
                    stt_benhan = $("#makhambenh").val();
                }
                var gioitinh = $("#gioitinh").val();
                if (gioitinh.toString() == "true") {
                    gioitinh = "Nam";
                } else {
                    gioitinh = "Nữ";
                }
                var mabenhnhan = $("#mabenhnhan").val();
                var khoa = $("#tenkhoa").val();
                if (madv != "") {
                    var arr = [madv, sophieu, makhambenh, dvtt, noitru, stt_benhan, sttdotdieutri, sttdieutri, sovaovien, hotenbenhnhan, khoa, gioitinh, sovaovien_noi, sovaovien_dt_noi, mabenhnhan];
                    var url = "inphieu_boiduong_pttt?url=" + convertArray(arr) ;
                    $(location).attr('href', url);
                } else {
                    jAlert("Vui lòng chọn dịch vụ để kết quả", 'Cảnh báo');
                }
            });
            //END
        });

    </script>
</head>
<body onload="showtime_giothuchien_cls();">

<%@include file="/WEB-INF/pages/Canlamsang/dialog/dialog-su-dung-dinh-muc.jsp" %>

<div id="panel_all">
    <%@include file="../../../resources/Theme/include_pages/menu.jsp" %>
    <div id="panelwrap">
        <%@include file="UploadfileTTPT.jsp" %>
        <div class="center_content">
            <form id="form1" name="form1" method="post" action="">
                <table width="990">
                    <tr>
                        <td width="302" valign="top">
                            <table width="302">
                                <tr class="hpg_tmp">
                                    <td width="60px"><span style="width:60px; display:inline-block;">Từ Ngày </span>
                                    </td>
                                    <td><input type="text" name="tungay" id="tungay"/>

                                    </td>
                                </tr>
                                <tr>
                                    <td width="60px"><span style="width:60px; display:inline-block;"><span
                                            class="hpg_tmp">Đến </span>Ngày  </span>
                                    </td>
                                    <td>
                                        <input type="text" name="ngaythuchien" id="ngaythuchien"/>

                                        <input type="button" name="lammoi" id="lammoi" value="Làm mới"/>
                                    </td>
                                </tr>
                                <!-- ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT -->
                                <tr class="dlk_tmp">
                                    <td>Khoa</td>
                                    <td><select name="phongban" id="phongban" class="width100">
                                        <c:forEach var="i" items="${phongban}">
                                            <option value="${i.ma_phongban}">${i.ten_phongban}</option>
                                        </c:forEach>
                                    </select>
                                    </td>
                                </tr>
                                <tr class="dlk_tmp">
                                    <td>Phòng</td>
                                    <td><select name="phongbenh" id="phongbenh" class="width100">
                                        <c:forEach var="i" items="${phongbenh}">
                                            <option value="${i.ma_phong_benh}">${i.ten_phong_benh}</option>
                                        </c:forEach>
                                    </select></td>
                                </tr>
                                <tr class="dlk_tmp">
                                    <td>Đối tượng</td>
                                    <td><select name="doituong" id="doituong" class="width100">
                                        <option value="-1">--Tất cả--</option>
                                        <option value="1">Có BHYT</option>
                                        <option value="0">Không BHYT</option>
                                    </select></td>
                                </tr>
                                <!-- End ĐắkLắk -->
                                <tr>
                                    <td colspan="2"><label><input type="checkbox" name="daxetnghiem" id="daxetnghiem">
                                        Đã thực hiện</label></td>
                                </tr>
                                <tr>
                                    <td colspan="2">
                                        <table id="list_benhnhan"></table>
                                    </td>
                                </tr>
                                <tr id="ghichutrangthai">
                                    <td colspan="2" style="padding-top:10px">
                                        <!--CMU: 27/10/2017-->
                                        <label style="color:red;font-weight: normal;">BN cấp cứu</label>
                                        <label style="color:#00ff00;margin-left:20px;font-weight: normal;">BN < 6
                                            tuổi</label><br>
                                        <label style="color:#bf00ff;font-weight: normal;">Bệnh nhân VP, chưa đóng
                                            tiền</label><br>
                                        <label style="color:#EE7600;font-weight: normal;">Bệnh nhân VP, đã đóng
                                            tiền</label>
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td width="676" valign="top">
                            <table width="674">
                                <tr>
                                    <td width="110">Họ tên
                                        <input name="sophieu" type="hidden" id="sophieu"/>
                                        <input name="makhambenh" type="hidden" id="makhambenh"/>
                                        <input name="noitru" type="hidden" id="noitru"/></td>
                                    <input name="use_ekip" type="hidden" id="use_ekip"/></td>
                        <input name="madv" type="hidden" id="madv"/></td>
                        <input name="sovaovien" type="hidden" id="sovaovien"/></td>
                        <td width="552"><input name="hoten" type="text" disabled="disabled" class="width1"
                                               id="hoten" style="color: red; font-weight: bold"/>
                            Tuổi
                            <input name="tuoi" type="text" disabled="disabled" class="width3" id="tuoi"
                                   style="width:50px"/>
                            <select name="gioitinh" id="gioitinh" disabled="disabled">
                                <option value="true">Nam</option>
                                <option value="false">Nữ</option>
                            </select>
                            Mã y tế
                            <input name="mabenhnhan" type="text" disabled="disabled" class="width3"
                                   id="mabenhnhan" style="width:100px"/>
                        </td>
                    </tr>
                    <tr>
                        <td>Khoa</td>
                        <td><input name="tenkhoa" type="text" disabled="disabled" class="width2"
                                   id="tenkhoa"/></td>
                    </tr>
                    <tr>
                        <td>Địa chỉ</td>
                        <td><input name="diachi" type="text" disabled="disabled" class="width2"
                                   id="diachi"/></td>
                    </tr>
                    <tr>
                        <td>Số thẻ BHYT</td>
                        <td><input name="sothebhyt" type="text" disabled="disabled" class="width2"
                                   id="sothebhyt"/></td>
                    </tr>
                    <tr class="hpg_hienthithem">
                        <td>Triệu chứng</td>
                        <td>
                            <input name="trieuchungls" type="text" disabled="disabled" class="width2"
                                   id="trieuchungls"/>
                        </td>
                    </tr>
                    <tr class="hpg_hienthithem">
                        <td>Bệnh tật</td>
                        <td>
                            <input name="benhtheobs" type="text" disabled="disabled" class="width2"
                                   id="benhtheobs"/>
                        </td>
                    </tr>
                    <tr>
                        <td>Ngày chỉ định</td>
                        <td>
                            <input name="ngaychidinh_cls" type="text" disabled="disabled" id="ngaychidinh_cls" size="10"
                                   data-inputmask="'alias': 'date'"/>
                            <input name="giochidinh_cls" type="text" disabled="disabled" id="giochidinh_cls" size="10"
                                   data-inputmask="'alias': 'hh:mm:ss'"/>
                        </td>
                    </tr>
                    <tr class="hpg_hienthithem">
                        <td>Ngày chỉ định</td>
                        <td>
                            <input name="cmu_ngaychidinh" type="text" disabled="disabled" class="width2"
                                   id="cmu_ngaychidinh"/>
                        </td>
                    </tr>
                    <tr class="hpg_hienthithem">
                        <td>Ngày thực hiện</td>
                        <td>
                            <!--      <input name="ngaythuchien_cls" type="text" disabled="disabled" class="width2" id="ngaythuchien_cls" /> -->
                            <input name="ngaythuchien_cls" type="text" id="ngaythuchien_cls" size="10"
                                   data-inputmask="'alias': 'date'"/>
                            <input name="giothuchien_cls" type="text" id="giothuchien_cls" size="10"
                                   data-inputmask="'alias': 'hh:mm:ss'"/>

                            <input id="phauthuat_lenlich" type="checkbox"
                                   onclick="update_pttt_lenlich()"><label for="phauthuat_lenlich"
                                                                          style="margin-left:5px">Lên
                            lịch</label>
                            <span style="margin-left: 5px">Ngày tiếp nhận</span>
                            <input name="ngaytiepnhan" type="text" id="ngaytiepnhan" size="20"
                                   style="display:none"/>
                        </td>
                    </tr>
                    <tr class="hpg_hienthithem">
                        <td>
                            Thanh toán
                        </td>
                        <td>
                            <input name="tt_thanhtoan" type="text" disabled="disabled" class="width2"
                                   id="tt_thanhtoan"/>
                        </td>
                    </tr>
                    <!-- ĐắkLắk (Cà Mau yêu cầu) - Ninh 09/12/2016: thêm thông tin chẩn đoán icd -->
                    <tr>
                        <td>Chẩn đoán</td>
                        <td><input name="_chandoan" type="text" disabled="disabled" class="width2" id="_chandoan"/></td>
                    </tr>
                    <!-- End ĐắkLắk -->
                    <tr>
                        <td>Chọn máy</td>
                        <td>
                            <select name="sttMayTTPT" id="sttMayTTPT" style="width: 70%">
                                <option value="0" selected>--- Chọn máy TPTT ---</option>
                                <c:forEach var="e" items="${dsMayTTPT}">
                                    <option value="${e.STT}">${e.TEN_MAY}</option>
                                </c:forEach>
                            </select>
                            <input type="button" name="luuMayTTPT" id="luuMayTTPT" value="Lưu máy thực hiện" style="width: 150px;" class="button_shadow"/>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2" align="center">
                            <input type="button" name="luufile" id="luufile"
                                   value="Chọn file" class="button_shadow"/>
                            <input type="button" name="luuthongtin" id="luuthongtin" value="Lưu thông tin"
                                   class="button_shadow" style="width: 110px"/>
                            <input type="button" name="lichmo" id="lichmo" value="Lịch phẩu thuật"
                                   class="button_shadow" style="width: auto"/>
                            <input type="button" name="inphieu" id="inphieu" value="In phiếu"
                                   class="button_shadow"/>
                            <input type="button" name="ingiaycamdoan" id="ingiaycamdoan"
                                   value="Giấy cam đoan" class="button_shadow" style="width: 110px"/>
                            <input type="button" name="ingiaybaomo" id="ingiaybaomo"
                                   value="Giấy báo mổ" class="button_shadow" style="width: auto"/>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2" align="center">
                            <input name="sttbenhan" type="hidden" id="sttbenhan"/>
                            <input name="sttdotdieutri" type="hidden" id="sttdotdieutri"/>
                            <input name="sttdieutri" type="hidden" id="sttdieutri"/>
                            <input name="sophieuthanhtoan" type="hidden" id="sophieuthanhtoan"/>
                            <input name="nguoithuchien" type="hidden" id="nguoithuchien"/>
                            <!--VNPT VLG-->
                            <input type="button" name="lamdungcu" id="lamdungcu" value="Làm dụng cụ"
                                   class="button_shadow" style="width: 110px"/>
                            <input type="button" name="loaddinhmuc" id="loaddinhmuc" value="Định mức"
                                   class="button_shadow" style=""/>
                            <input type="button" name="inphieugaymehoisuc" id="inphieugaymehoisuc"
                                   value="Phiếu gây mê hồi sức"
                                   class="button_shadow" style="width: 150px"/>
                            <input type="button" name="bbduyetphauthuat" id="bbduyetphauthuat" value="BB duyệt phẩu thuật" class="button_shadow" style="width: auto"/>
                        </td>
                    </tr>
                    <tr>
                        <td>Kết quả tổng thể</td>
                        <td><textarea name="ketqua" rows="2" class="width2" id="ketqua"></textarea></td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <div id="div_list_ttpt">
                                <table id="list_ttpt_bhyt"></table>
                            </div>
                        </td>
                    </tr>
                </table>
                </td>
                </tr>
                </table>
            </form>
        </div> <!--end of center_content-->
        <%@include file="Capnhatketqua_TTPT.jsp" %>
        <%@include file="lichmo.jsp" %>
        <%-- <%@include file="vlg_duyen_phieulamdungcu.jsp"%> --%>
        <div id="ekip" style="display: none">
            <form id="tiencong" name="tiencong" method="post" action="">
                <table width="600" border="0">
                    <tr>
                        <td colspan="2">
                            <table width="600" border="0">
                                <tr>
                                    <td width="100" align="right" style="font-size: 12px">Phòng ban</td>
                                    <td><select name="khoaphong_ekip" id="khoaphong_ekip" style="width: 200px">
                                        <c:forEach var="i" items="${phongban}">
                                            <option value="${i.MA_PHONGBAN}">${i.TEN_PHONGBAN}</option>
                                        </c:forEach>
                                    </select>
                                    </td>
                                    <td width="100" align="right" style="font-size: 12px">Thực hiện</td>
                                    <td><select name="nguoithuchien_ekip" id="nguoithuchien_ekip" style="width: 200px">
                                        <option value="-1">-- Chọn nhân viên --</option>
                                    </select>
                                    </td>
                                    <td><input name="manhanvien" type="hidden" id="manhanvien"/></td>
                                </tr>
                                <tr>
                                    <td width="100" align="right" style="font-size: 12px">Ekip mẫu</td>
                                    <td><select name="ekip_mau" id="ekip_mau" style="width: 200px">
                                        <option value="-1">-- Chọn ekip --</option>
                                    </select>
                                    </td>
                                    <td width="70" align="right" style="font-size: 12px">Vai trò</td>
                                    <td><select name="tiencongthuchien" id="tiencongthuchien" style="width: 200px">
                                        <option value="-1">-- Chọn vai trò --</option>
                                    </select>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="4" align="center">
                                        <input type="button" class="button_shadow" name="themnguoithuchien"
                                               id="themnguoithuchien" value="Thêm"/>
                                        <input type="button" class="button_shadow" name="xoanguoithuchien"
                                               id="xoanguoithuchien" value="Xóa"/>
                                        <input type="button" class="button_shadow" name="xoanguoithuchien_all"
                                               id="xoanguoithuchien_all" value="Xóa tất cả"/>
                                        <input type="button" class="button_shadow" name="inphieuekip"
                                               id="inphieuekip" value="In phiếu"/>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <table id="list_chitiettiencongthuchien" style="font-size: 12px"></table>
                    </tr>
                </table>
            </form>
        </div>
        <%--BB duyệt phẩu thuật--%>
        <div id="dalBBduyetphauthuat" style="display: none" title="Biên bản duyệt phẩu thuật">
            <form id="frmBBduyetphauthuat" name="frmBBduyetphauthuat" method="post" action="">
                <table width="100%" style="font-size: 13px">
                    <tr>
                        <td width="20%">Thị lực</td>
                        <td width="5%"><b>MP</b></td>
                        <td width="35%"><input name="thilucmatphai" type="text" id="thilucmatphai" style="width: 100%"/></td>
                        <td width="5%"><b>MT</b></td>
                        <td width="35%"><input name="thilucmattrai" type="text" id="thilucmattrai" style="width: 100%"/></td>
                    </tr>
                    <tr>
                        <td width="20%">Nhãn áp</td>
                        <td width="5%"><b>MP</b></td>
                        <td width="35%"><input name="nhanapmatphai" type="text" id="nhanapmatphai" style="width: 100%"/></td>
                        <td width="5%"><b>MT</b></td>
                        <td width="35%"><input name="nhanapmattrai" type="text" id="nhanapmattrai" style="width: 100%"/></td>
                    </tr>
                    <tr>
                        <td width="20%">Chỉ định phẩu thuật phối hợp</td>
                        <td width="80%" colspan="4"><input name="cdphauthuatphoihop" type="text" id="cdphauthuatphoihop" style="width: 100%"/></td>
                    </tr>
                    <tr>
                        <td width="20%">Phẩu thuật phối hợp</td>
                        <td width="80%" colspan="4"><input name="phauthuatphoihop" type="text" id="phauthuatphoihop" style="width: 100%"/></td>
                    </tr>
                    <tr>
                        <td width="20%">Phẩu thuật viên phụ</td>
                        <td width="80%" colspan="4"><input name="phauthuatvienphu" type="text" id="phauthuatvienphu" style="width: 100%"/></td>
                    </tr>
                    <tr>
                        <td width="20%">Theo dõi gây tê/gây mê</td>
                        <td width="80%" colspan="4"><input name="gayte_gayme" type="checkbox" id="gayte_gayme"/></td>
                    </tr>
                    <tr>
                        <td width="20%">Ý kiến trưởng khoa</td>
                        <td width="80%" colspan="4"><input name="ykientruongkhoa" type="text" id="ykientruongkhoa" style="width: 100%"/></td>
                    </tr>
                    <tr>
                        <td width="100%" colspan="5" align="center">
                            <input type="button" style="width: 80px" name="luu_bbduyet" id="luu_bbduyet" value="Lưu" class="button_shadow"/>
                            <%--<input type="button" style="width: 80px" name="sua_bbduyet" id="sua_bbduyet" value="Sửa" class="button_shadow"/>--%>
                            <input type="button" style="width: 80px" name="inphieu_bbduyet" id="inphieu_bbduyet" value="In phiếu" class="button_shadow"/>
                        </td>
                    </tr>
                </table>
            </form>
        </div>
    </div>
</div>
</body>
</html>
