CREATE OR REPLACE
PROCEDURE "RIS_HUY_KETQUA_CDHA_CT"(p_dvtt    VARCHAR2,
                                                     p_sophieu VARCHAR2,
                                                     p_madv    VARCHAR2,
                                                     cur       OUT SYS_REFCURSOR) IS
  v_kiemtrangt NUMBER(11);
  v_kiemtrant  NUMBER(11);
  vcount       NUMBER(11);
	v_sovaovien number(11);
	v_sovaovien_dt number(11);
	v_khoathanhtoan number(11):=0;
	v_return number;
BEGIN

SELECT COUNT(1)
INTO v_kiemtrangt
FROM KB_CD_CDHA
WHERE dvtt = p_dvtt
  AND SO_PHIEU_CDHA = p_sophieu;

SELECT COUNT(1)
INTO v_kiemtrant
FROM NOITRU_CD_CDHA
WHERE dvtt = p_dvtt
  AND SO_PHIEU_CDHA = p_sophieu;
if p_sophieu like '%/%' then
SELECT sovaovien, sovaovien_dt into v_sovaovien, v_sovaovien_dt
FROM NOITRU_CD_CDHA
WHERE dvtt = p_dvtt
  AND SO_PHIEU_CDHA = p_sophieu;

select KHOA_THANHTOAN into v_khoathanhtoan
from noitru_PHIEUTHANHTOAN
where sovaovien = v_sovaovien and sovaovien_dt = v_sovaovien_dt and dvtt = p_dvtt;

else
SELECT sovaovien into v_sovaovien
FROM KB_CD_CDHA
WHERE dvtt = p_dvtt
  AND SO_PHIEU_CDHA = p_sophieu;

select KHOA_THANHTOAN into v_khoathanhtoan
from KB_PHIEUTHANHTOAN
where sovaovien = v_sovaovien and dvtt = p_dvtt;

end if;
	if v_khoathanhtoan = 1 then
		OPEN cur FOR
SELECT 0 AS TT
FROM dual;
return ;
end if;
  IF v_kiemtrangt > 0 THEN
UPDATE KB_CD_CDHA
set TRANG_THAI_CHUAN_DOAN = 0
where SO_PHIEU_CDHA = p_sophieu
  and DVTT = p_DVTT;
UPDATE HIS_MANAGER.KB_CD_CDHA_CT
SET MO_TA           = NULL,
    KET_QUA         = NULL,
    PIC1            = NULL,
    DA_CHAN_DOAN    = 0,
    LOIDANBACSI     = NULL,
    RIS_TT_CACHUP   = 0,
    ma_mausieuam    = 0,
    BACSI_THUCHIEN  = null,
    NGUOI_THUC_HIEN = null
WHERE DVTT = p_dvtt
  AND SO_PHIEU_CDHA = p_sophieu
  AND MA_CDHA = p_madv;
vcount := SQL%ROWCOUNT;

ELSE
    IF v_kiemtrant > 0 THEN
UPDATE NOITRU_CD_CDHA
set TRANG_THAI_CHUAN_DOAN = 0
where SO_PHIEU_CDHA = p_sophieu
  and DVTT = p_DVTT;
UPDATE HIS_MANAGER.NOITRU_CD_CDHA_CHI_TIET
SET MO_TA           = NULL,
    KET_QUA         = NULL,
    PIC1            = NULL,
    DA_CHAN_DOAN    = 0,
    LOIDANBACSI     = NULL,
    RIS_TT_CACHUP   = 0,
    BACSI_THUCHIEN  = null,
    ma_mausieuam    = 0,
    NGUOI_THUC_HIEN = null
WHERE DVTT = p_dvtt
  AND SO_PHIEU_CDHA = p_sophieu
  AND MA_CDHA = p_madv;
vcount := SQL%ROWCOUNT;
END IF;
END IF;
	v_return:=CMU_THOIGIAN_NHANVIEN_DEL(
			P_DVTT ,
			v_sovaovien,
			p_sophieu,
			p_madv
		);
OPEN cur FOR
SELECT case
           when vcount is null then
               0
           else
               vcount
           end AS TT
FROM dual;
END;
