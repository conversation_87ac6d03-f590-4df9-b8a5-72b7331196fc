<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="noitru_toathuoc" pageWidth="421" pageHeight="595" whenNoDataType="AllSectionsNoDetail" columnWidth="401" leftMargin="10" rightMargin="10" topMargin="14" bottomMargin="14" whenResourceMissingType="Empty" uuid="0f5e3019-005e-44fe-bb8f-29ae14f10109">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="tenbenhvien" class="java.lang.String"/>
	<parameter name="tenkhoakham" class="java.lang.String"/>
	<parameter name="tensoyte" class="java.lang.String"/>
	<parameter name="hovaten" class="java.lang.String"/>
	<parameter name="namsinh" class="java.lang.String"/>
	<parameter name="diachi" class="java.lang.String"/>
	<parameter name="mach" class="java.lang.String"/>
	<parameter name="huyetap_tren" class="java.lang.String"/>
	<parameter name="huyetap_duoi" class="java.lang.String"/>
	<parameter name="thannhiet" class="java.lang.String"/>
	<parameter name="dientienbenh" class="java.lang.String"/>
	<parameter name="sophieu" class="java.lang.String"/>
	<parameter name="ngay" class="java.lang.String"/>
	<parameter name="thang" class="java.lang.String"/>
	<parameter name="nam" class="java.lang.String"/>
	<parameter name="bacsidieutri" class="java.lang.String"/>
	<parameter name="masonguoibenh" class="java.lang.String"/>
	<parameter name="mathe_2kytudau" class="java.lang.String"/>
	<parameter name="mathe_kythu3" class="java.lang.String"/>
	<parameter name="the45" class="java.lang.String"/>
	<parameter name="the67" class="java.lang.String"/>
	<parameter name="the8910" class="java.lang.String"/>
	<parameter name="mathe_5kytucuoi" class="java.lang.String"/>
	<parameter name="matoathuoc" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="loidantoathuoc" class="java.lang.String"/>
	<parameter name="ngayhentaikham" class="java.lang.String"/>
	<parameter name="dvtt" class="java.lang.String"/>
	<parameter name="nghiepvu" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="tentoathuoc" class="java.lang.String"/>
	<parameter name="noidangky" class="java.lang.String"/>
	<parameter name="sochuyentuyen" class="java.lang.String"/>
	<parameter name="gioitinh_nam" class="java.lang.String"/>
	<parameter name="gioitinh_nu" class="java.lang.String"/>
	<parameter name="sttdotdieutri" class="java.lang.String"/>
	<parameter name="sttbenhan" class="java.lang.String"/>
	<parameter name="sttdieutri" class="java.lang.String"/>
	<parameter name="thamso" class="java.lang.String">
		<defaultValueExpression><![CDATA['0']]></defaultValueExpression>
	</parameter>
	<parameter name="chandoanbenh" class="java.lang.String"/>
	<parameter name="icdchandoan" class="java.lang.String"/>
	<parameter name="ngayintoa" class="java.lang.String"/>
	<parameter name="toa" class="java.lang.String"/>
	<parameter name="nguoilienhe" class="java.lang.String"/>
	<parameter name="chuky" class="java.lang.String"/>
	<parameter name="cannang" class="java.lang.String"/>
	<parameter name="tuoi" class="java.lang.Integer"/>
	<queryString language="plsql">
		<![CDATA[{call NOITRU_PRINT_TT_TT04($P{matoathuoc},$P{dvtt},$P{nghiepvu},$P{sttbenhan},$P{sttdotdieutri},$P{sttdieutri},$P{toa},$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="STT_order" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ten_nhanvien" class="java.lang.String"/>
	<field name="DVT" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="TenVatTu" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="MOTA_CHITIET" class="java.lang.String"/>
	<field name="SO_LUONG_UONG" class="java.lang.String"/>
	<field name="SANG_UONG" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="TRUA_UONG" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="CHIEU_UONG" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="TOI_UONG" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="GHI_CHU_CT_TOA_THUOC" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="MA_TOA_THUOC" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="SO_LUONG" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="MA_BAC_SI_THEMTHUOC" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="SO_LUONG_THUC_LINH" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="THANHTIEN_THUOC" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="CACH_SU_DUNG" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="GHI_CHU_CT_TOA_THUOC1" class="java.lang.String"/>
	<field name="tt" class="java.lang.Integer"/>
	<field name="MOTA_CHITIET2" class="java.lang.String"/>
	<field name="MOTA_CHITIET1" class="java.lang.String"/>
	<field name="sdt_bacsi" class="java.lang.String"/>
	<field name="BANT" class="java.lang.String"/>
	<field name="diachi" class="java.lang.String"/>
	<field name="ngay_sinh" class="java.lang.String"/>
	<field name="sodienthoai" class="java.lang.String"/>
	<field name="ma_don_thuoc" class="java.lang.String"/>
	<field name="nguoilienhe" class="java.lang.String"/>
	<field name="soCMT" class="java.lang.String"/>
	<field name="sdt_benhnhan" class="java.lang.String"/>
	<field name="ngaythuoc" class="java.lang.String"/>
	<field name="CHANDOAN" class="java.lang.String"/>
	<variable name="count_sl" class="java.lang.Integer" calculation="Count">
		<variableExpression><![CDATA[$F{STT_order}]]></variableExpression>
	</variable>
	<variable name="benhnhankynhan" class="java.lang.String">
		<variableExpression><![CDATA[($P{dvtt}.equals("82019") ? "" : "Bệnh nhân ký nhận")]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="171" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="314" y="0" width="87" height="16" uuid="12a2821b-b13f-43d5-b7a3-e2b4c4cf49bd"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ma_don_thuoc}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="15" width="314" height="15" uuid="1ef0419a-05b6-4522-a430-9f6bd35515c1"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Địa chỉ: " + $F{diachi}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="30" width="222" height="15" uuid="bc48e66b-e138-45be-8994-937bdbf359ec"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Số ĐT:  "  + (
    $F{sodienthoai} != null ? $F{sodienthoai}: ""
)]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="48" width="401" height="18" uuid="aca57878-bfc6-4da4-8dea-5d83304fa400"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="15" isBold="true" pdfFontName="Times-Roman" pdfEncoding="Cp1252" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[ĐƠN THUỐC]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="156" width="401" height="15" uuid="0cb23437-e691-48de-a432-e35b99a0f80d"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Thuốc điều trị:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="142" y="89" width="46" height="15" uuid="adfb0771-5edc-4b8d-9347-db2b885fef82"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["Cân nặng: "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="188" y="89" width="34" height="15" uuid="da7b682c-dd00-459d-8d3f-bd8c41f8de56"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{cannang}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="299" y="89" width="23" height="15" uuid="6f6989f6-2d2e-4ed3-9225-bb4e425e8e1a"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<text><![CDATA[Nam]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="89" width="141" height="15" uuid="4cace9c5-c786-4b8d-bbd2-549c88e644d5"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["- Ngày sinh: " + $F{ngay_sinh}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="74" width="51" height="15" uuid="cdbc1af5-f646-4a45-b5cc-c2cc7b561c11"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA["- Họ tên: "]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="230" y="89" width="51" height="15" uuid="2ff0f1d9-db6c-42a1-bd50-3590cd1fb3a3"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<text><![CDATA[Giới tính:]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="326" y="89" width="15" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="a91f81e4-9937-42eb-b673-6ffa5f421927"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{gioitinh_nu}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="283" y="89" width="15" height="15" uuid="1f19f8a3-8ac3-4bca-a04a-d694d1767991"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{gioitinh_nam}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="119" width="142" height="17" uuid="dce6ecd4-d8b0-4e2d-ac77-2439c2117cb8"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Số thẻ bảo hiểm y tế (nếu có): "]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="342" y="89" width="24" height="15" uuid="856a2c88-28f2-48bc-9d8a-d62783e2e09d"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<text><![CDATA[Nữ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="104" width="401" height="15" uuid="c875f50e-c6d4-4796-b195-e5fd33163a88"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Địa chỉ liên hệ: "+$P{diachi}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="232" y="121" width="40" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="7d007591-904a-4d1d-9f83-fe2174d9bb8b"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{the67}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="202" y="121" width="30" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="c523ba8d-d77d-48e0-9c10-012eccbe5e70"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{the45}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="142" y="121" width="30" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="6064dc6c-9b5b-4149-967f-6c347398be7c"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{mathe_2kytudau}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="312" y="121" width="50" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="ec127353-d077-460b-b4ce-528cf21c3246"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{mathe_5kytucuoi}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="272" y="121" width="40" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="b2f6f3fe-7c73-494c-bce5-887a2fef2407"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{the8910}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="172" y="121" width="30" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="26f05f74-d258-4a08-8e1e-ce06d0908fb6"/>
				<box leftPadding="2">
					<topPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{mathe_kythu3}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="314" y="33" width="87" height="13" uuid="84581f09-a40a-4d52-a058-16115cdee00e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{masonguoibenh}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="222" height="15" uuid="f7fe8b76-3fb1-4fa6-9925-f85d90c1e702"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenbenhvien}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="326" y="18" width="70" height="15" uuid="8537d2f3-6ff5-48cb-9bad-717be67447b4"/>
				<jr:barbecue xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" type="2of7" drawText="false" checksumRequired="false">
					<jr:codeExpression><![CDATA[$P{masonguoibenh}]]></jr:codeExpression>
				</jr:barbecue>
			</componentElement>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement mode="Transparent" x="222" y="0" width="92" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="6dbb5e46-1817-42d0-aa5e-75c027e520dc"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Số phiếu: " + $P{sophieu}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="51" y="74" width="350" height="15" uuid="e4eee047-4f66-4f0a-9ddc-93f17e67c3fc"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{hovaten}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="0" y="136" width="401" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="2fd55990-b5ec-44da-85f5-d2b1b0b6d32f"/>
				<box leftPadding="0">
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Chẩn đoán bệnh của KĐT: " +$F{CHANDOAN}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="31" splitType="Stretch">
			<textField pattern="###0" isBlankWhenNull="false">
				<reportElement mode="Transparent" x="0" y="0" width="17" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="5855b995-cb2b-458c-8ada-1cbbead59edd"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}+" )"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="0" y="15" width="401" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="042e8472-5ca2-40f1-9139-320020e78ad4"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Cách dùng: " + ($F{GHI_CHU_CT_TOA_THUOC1} == null ? "" : $F{GHI_CHU_CT_TOA_THUOC1}) + "  " + ($F{SANG_UONG} == null ? "" : $F{SANG_UONG}) + "  " + ($F{TRUA_UONG} == null ? "" : $F{TRUA_UONG}) + "  " + ($F{CHIEU_UONG} == null ? "" : $F{CHIEU_UONG}) + "  " + ($F{TOI_UONG} == null ? "" : $F{TOI_UONG})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="337" y="0" width="64" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="8c0cf8a3-f794-4f3f-bfee-0ba697930ec8"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SO_LUONG_UONG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" mode="Transparent" x="17" y="0" width="320" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="e2de2a45-6a49-4a27-a091-2172d7440641"/>
				<box bottomPadding="1"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MOTA_CHITIET1}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="46" splitType="Immediate">
			<staticText>
				<reportElement positionType="FixRelativeToBottom" mode="Transparent" x="0" y="0" width="155" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="e7177d1e-f479-4913-bfb4-41faaec518ba"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[- Khám lại xin mang theo đơn này.]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="0" y="15" width="401" height="15" uuid="04376e90-100d-4d60-ad7f-763caa680054"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Số điện thoại liên hệ: " + $F{sdt_benhnhan}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="0" y="30" width="401" height="15" uuid="f87efd93-9102-4ab0-8e6b-6c0e845fdc6e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[(($P{nghiepvu}.equals("noitru_toanghien") || $P{nghiepvu}.equals("ba_ngoaitru_toanghien") || $P{toa}.equals("toa_n") || $P{nghiepvu}.equals("ba_ngoaitru_toadichvu_nghien") || $P{nghiepvu}.equals("noitru_toadichvu_nghien")
|| $P{nghiepvu}.equals("ba_ngoaitru_toahtt") || $P{nghiepvu}.equals("noitru_toahtt") || $P{toa}.equals("toa_h") || $P{nghiepvu}.trim().equals("ba_ngoaitru_toadichvu_htt") || $P{nghiepvu}.equals("noitru_toadichvu_htt"))
?"Căn cước công dân/chứng minh nhân dân của người nhận thuốc: " + $F{soCMT}: " ")]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="117" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="40" y="1" width="161" height="15" isPrintWhenDetailOverflows="true" uuid="1ccc554d-1bfa-4aa7-8b48-5fd5c19fcd4f"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{loidantoathuoc}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="202" y="16" width="199" height="15" uuid="052dd7e4-81b0-4fe8-bd31-68cd147a7233"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
				</textElement>
				<text><![CDATA[Bác sĩ điều trị]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="202" y="91" width="199" height="15" uuid="1902321d-fb59-4661-9873-3af10b613d62"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{bacsidieutri}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="0" y="1" width="40" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="8d50fc16-1d92-4224-946b-e98941b725dc"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Lời dặn:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="202" y="1" width="199" height="15" uuid="38574709-3e5a-48ab-9174-2f54b0752926"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày "+$P{ngay}+" tháng "+$P{thang}+" năm "+$P{nam}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
