CREATE OR REPLACE FUNCTION "HIS_MANAGER"."CMU_VBA_PHATHAI_TONGKET_UPD" (
    p_ID IN NUMBER,
    p_DVTT IN VARCHAR2,
    p_<PERSON>ATA IN CLOB
) RETURN NUMBER IS
    v_count NUMBER;
<PERSON><PERSON><PERSON>
    UPDATE "HIS_MANAGER"."VOBENHAN_TONGKETBA"
    SET BENHAN_PHATHAI = p_DATA
    WHERE ID_HSBA = p_ID AND DVTT = p_DVTT;

    v_count := SQL%ROWCOUNT;

    RETURN v_count;
END CMU_VBA_PHATHAI_TONGKET_UPD;