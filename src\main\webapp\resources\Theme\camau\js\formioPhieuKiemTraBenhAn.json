{"display": "form", "components": [{"label": "ID", "key": "ID", "type": "hidden", "input": true}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON><PERSON> t<PERSON>", "customClass": "mr-2 formio-css-datetime formio-css-suffix formio-js-validate-1 luulog", "key": "NGAY_TAO_PHIEU", "type": "datetime", "format": "dd/MM/yyyy HH:mm", "validate": {"required": true, "customMessage": "<PERSON><PERSON><PERSON> t<PERSON>o phi<PERSON>u là bắt buộc"}, "timePicker": {"showMeridian": false}, "widget": {"enableTime": true, "format": "dd/MM/yyyy HH:mm", "time_24hr": true}}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON><PERSON><PERSON> kiểm tra", "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 luulog", "tableView": true, "dataSrc": "json", "template": "<span>{{ item.tennhanvien }}</span>", "validate": {"required": true, "customMessage": "<PERSON><PERSON><PERSON><PERSON> kiểm tra là bắt buộc"}, "validateWhenHidden": false, "key": "NGUOI_KIEM_TRA", "type": "select", "input": true}], "width": 3, "size": "md", "currentWidth": 3}, {"components": [{"label": "<PERSON><PERSON><PERSON> sĩ điều trị", "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 luulog", "tableView": true, "dataSrc": "json", "template": "<span>{{ item.tennhanvien }}</span>", "validateWhenHidden": false, "key": "BS_DIEU_TRI", "type": "select", "input": true}], "width": 3, "size": "md", "currentWidth": 3}, {"components": [{"label": "<PERSON><PERSON><PERSON>", "widget": "<PERSON><PERSON><PERSON>", "customClass": "lu<PERSON><PERSON>", "tableView": true, "dataSrc": "json", "template": "<span>{{ item.TENKHOA }}</span>", "validateWhenHidden": false, "key": "KHOA", "type": "select", "input": true}], "width": 4, "size": "md", "currentWidth": 4}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Columns", "columns": [{"components": [{"label": "Số thẻ BHYT", "customClass": "mr-2 luulog", "tableView": true, "validateWhenHidden": false, "key": "SO_THE_BHYT", "type": "textfield", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "Số B/A", "customClass": "mr-2 luulog", "tableView": true, "validateWhenHidden": false, "key": "SO_BA", "type": "textfield", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON> b<PERSON><PERSON>", "customClass": "mr-2 luulog", "tableView": true, "validateWhenHidden": false, "key": "MA_BENH", "type": "textfield", "input": true}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON><PERSON> v<PERSON>o viện", "disabled": true, "customClass": "mr-2 formio-css-datetime formio-css-suffix formio-css-disable-datetime luulog", "key": "NGAY_VAO_VIEN", "type": "datetime", "format": "dd/MM/yyyy HH:mm", "timePicker": {"showMeridian": false}, "widget": {"enableTime": true, "format": "dd/MM/yyyy HH:mm", "time_24hr": true}}], "width": 2, "size": "md", "currentWidth": 2}, {"components": [{"label": "<PERSON><PERSON>y ra", "disabled": true, "customClass": "mr-2 formio-css-datetime formio-css-suffix formio-css-disable-datetime luulog", "key": "NGAY_RA", "type": "datetime", "format": "dd/MM/yyyy HH:mm", "timePicker": {"showMeridian": false}, "widget": {"enableTime": true, "format": "dd/MM/yyyy HH:mm", "time_24hr": true}}], "width": 2, "size": "md", "currentWidth": 2}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0 formio-css-not-display"}, {"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b><PERSON><PERSON><PERSON> đ<PERSON>h giá</b>", "input": false}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-no-margin no-bottom header-content resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "1", "NOI_DUNG": "<PERSON><PERSON><PERSON> tục hành ch<PERSON>h", "DIEM_CHUAN": 1.0, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R1C0", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM<br>CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM<br>ĐẠT</b>", "customClass": "formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true, "disabled": true}, {"label": "<b>GHI<br>CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true, "disabled": true}]}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-no-margin formio-css-grid-no-header no-top-bottom resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "", "NOI_DUNG": "- <PERSON><PERSON> tên BN viết in hoa, ghi đủ các mã số", "DIEM_CHUAN": 0.2, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R1C1", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM ĐẠT</b>", "customClass": "formio-css-input-text-center formio-js-diem-dat", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true}, {"label": "<b>GHI CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true}]}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-no-margin formio-css-grid-no-header no-top-bottom resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "", "NOI_DUNG": "- <PERSON><PERSON><PERSON> phần khác ghi đầy đủ các cột mục rõ ràng", "DIEM_CHUAN": 0.2, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R1C2", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM ĐẠT</b>", "customClass": "formio-css-input-text-center formio-js-diem-dat", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true}, {"label": "<b>GHI CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true}]}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-no-margin formio-css-grid-no-header no-top-bottom resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "", "NOI_DUNG": "- Hoàn chỉnh BA trước 24h (NB CC), 36h (NB thường)", "DIEM_CHUAN": 0.3, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R1C3", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM ĐẠT</b>", "customClass": "formio-css-input-text-center formio-js-diem-dat", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true}, {"label": "<b>GHI CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true}]}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-no-margin formio-css-grid-no-header no-top-bottom resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "", "NOI_DUNG": "- <PERSON><PERSON> đầy đủ chữ ký bác sỹ, y tá (ghi r<PERSON> họ tên)", "DIEM_CHUAN": 0.3, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R1C4", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM ĐẠT</b>", "customClass": "formio-css-input-text-center formio-js-diem-dat", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true}, {"label": "<b>GHI CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true}]}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-no-margin formio-css-grid-no-header no-bottom resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "2", "NOI_DUNG": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> chẩn đo<PERSON>", "DIEM_CHUAN": 4.5, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R2C0", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM ĐẠT</b>", "customClass": "formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true, "disabled": true}, {"label": "<b>GHI CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true, "disabled": true}]}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-no-margin formio-css-grid-no-header no-top-bottom resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "", "NOI_DUNG": "- Hỏi bệnh sử và tiền sử chi tiết", "DIEM_CHUAN": 1.0, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R2C1", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM ĐẠT</b>", "customClass": "formio-css-input-text-center formio-js-diem-dat", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true}, {"label": "<b>GHI CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true}]}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-no-margin formio-css-grid-no-header no-top-bottom resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "", "NOI_DUNG": "- <PERSON><PERSON><PERSON><PERSON>, g<PERSON> <PERSON> đ<PERSON> đủ", "DIEM_CHUAN": 1.0, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R2C2", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM ĐẠT</b>", "customClass": "formio-css-input-text-center formio-js-diem-dat", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true}, {"label": "<b>GHI CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true}]}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-no-margin formio-css-grid-no-header no-top-bottom resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "", "NOI_DUNG": "- <PERSON><PERSON><PERSON> đ<PERSON> xét nghiệm cần thiết", "DIEM_CHUAN": 0.8, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R2C3", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM ĐẠT</b>", "customClass": "formio-css-input-text-center formio-js-diem-dat", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true}, {"label": "<b>GHI CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true}]}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-no-margin formio-css-grid-no-header no-top-bottom resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "", "NOI_DUNG": "- <PERSON><PERSON> chẩn đo<PERSON> sơ bộ trong 4 giờ vào viện", "DIEM_CHUAN": 0.5, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R2C4", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM ĐẠT</b>", "customClass": "formio-css-input-text-center formio-js-diem-dat", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true}, {"label": "<b>GHI CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true}]}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-no-margin formio-css-grid-no-header no-top-bottom resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "", "NOI_DUNG": "- <PERSON><PERSON> chẩn đo<PERSON> xác định trong 72 giờ kể từ khi vào viện", "DIEM_CHUAN": 0.5, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R2C5", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM ĐẠT</b>", "customClass": "formio-css-input-text-center formio-js-diem-dat", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true}, {"label": "<b>GHI CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true}]}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-no-margin formio-css-grid-no-header no-top-bottom resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "", "NOI_DUNG": "- <PERSON><PERSON><PERSON> chẩn khi chưa có chẩn đo<PERSON> xác định, ghi đầy đủ vào BA khi NB có diễn biến nặng, thay đổi hướng điều trị", "DIEM_CHUAN": 0.7, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R2C6", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM ĐẠT</b>", "customClass": "formio-css-input-text-center formio-js-diem-dat", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true}, {"label": "<b>GHI CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true}]}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-no-margin formio-css-grid-no-header no-bottom resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "3", "NOI_DUNG": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> điều trị", "DIEM_CHUAN": 4.5, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R3C0", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM ĐẠT</b>", "customClass": "formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true, "disabled": true}, {"label": "<b>GHI CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true, "disabled": true}]}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-no-margin formio-css-grid-no-header no-top-bottom resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "", "NOI_DUNG": "- <PERSON><PERSON> diễn biến hàng ngày (nếu NB cần theo dõi giờ thì ghi rõ giờ, ngày theo dõi)", "DIEM_CHUAN": 1.0, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R3C1", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM ĐẠT</b>", "customClass": "formio-css-input-text-center formio-js-diem-dat", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true}, {"label": "<b>GHI CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true}]}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-no-margin formio-css-grid-no-header no-top-bottom resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "", "NOI_DUNG": "- <PERSON> thuốc hàng <PERSON> (hoặc diễn biến theo bệnh) bám sát vào phác đồ chuẩn", "DIEM_CHUAN": 1.0, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R3C2", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM ĐẠT</b>", "customClass": "formio-css-input-text-center formio-js-diem-dat", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true}, {"label": "<b>GHI CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true}]}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-no-margin formio-css-grid-no-header no-top-bottom resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "", "NOI_DUNG": "- <PERSON> thu<PERSON>, an <PERSON><PERSON><PERSON>, ti<PERSON><PERSON> kiệm và hiệu quả", "DIEM_CHUAN": 1.0, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R3C3", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM ĐẠT</b>", "customClass": "formio-css-input-text-center formio-js-diem-dat", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true}, {"label": "<b>GHI CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true}]}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-no-margin formio-css-grid-no-header no-top-bottom resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "", "NOI_DUNG": "- <PERSON><PERSON> đ<PERSON>nh <PERSON>, đ<PERSON><PERSON> số theo quy định", "DIEM_CHUAN": 0.5, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R3C4", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM ĐẠT</b>", "customClass": "formio-css-input-text-center formio-js-diem-dat", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true}, {"label": "<b>GHI CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true}]}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-no-margin formio-css-grid-no-header no-top-bottom resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "", "NOI_DUNG": "- 15 ng<PERSON>y có tóm tắt BS một lần", "DIEM_CHUAN": 0.5, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R3C5", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM ĐẠT</b>", "customClass": "formio-css-input-text-center formio-js-diem-dat", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true}, {"label": "<b>GHI CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true}]}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-no-margin formio-css-grid-no-header no-top-bottom resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "", "NOI_DUNG": "- Ra viện tổng kết quá trình điều trị và hoàn thiện BA (theo quy chế chuyên môn)", "DIEM_CHUAN": 0.5, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R3C6", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM ĐẠT</b>", "customClass": "formio-css-input-text-center formio-js-diem-dat", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true}, {"label": "<b>GHI CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true}]}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-grid formio-css-grid-no-header resize-col", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"STT": "4", "NOI_DUNG": "<PERSON><PERSON><PERSON> điểm", "DIEM_CHUAN": 10.0, "DIEM_DAT": "", "GHI_CHU": ""}], "validateWhenHidden": false, "key": "BANG_KTRA_BA_R4C0", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b>STT</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "STT", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>NỘI DUNG</b>", "customClass": "formio-css-textarea formio-js-scale-textarea", "tableView": true, "validateWhenHidden": false, "key": "NOI_DUNG", "type": "textarea", "input": true}, {"label": "<b>ĐIỂM CHUẨN</b>", "customClass": "formio-css-disable-none-bottom formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_CHUAN", "type": "number", "input": true, "disabled": true}, {"label": "<b>ĐIỂM ĐẠT</b>", "customClass": "formio-css-input-text-center", "tableView": true, "validateWhenHidden": false, "key": "DIEM_DAT", "type": "number", "input": true, "disabled": true}, {"label": "<b>GHI CHÚ</b>", "tableView": true, "validateWhenHidden": false, "key": "GHI_CHU", "type": "textfield", "input": true, "disabled": true}]}, {"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b><PERSON><PERSON><PERSON><PERSON> xét:</b> <PERSON>ế<PERSON> lo<PERSON>i: </br>Tốt: 9-10 điểm&emsp;&emsp;&emsp;&nbsp;&nbsp;TB: 7-7,9 điểm</br><PERSON>h<PERSON>: 8-8,9 điểm&emsp;&emsp;&emsp;Kém: <7 điểm", "input": false}]}