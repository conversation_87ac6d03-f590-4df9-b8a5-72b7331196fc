CREATE OR REPLACE
PROCEDURE             PRO_IN_CHUYEN_TUYEN_ND_75_NOI(p_dvtt IN varchar2,
                                                                                  p_stt_benhan IN varchar2,
                                                                                  p_stt_dotdieutri IN varchar2,
                                                                                  p_ma<PERSON><PERSON>han IN number,
                                                                                  cur OUT SYS_REFCURSOR) IS
    v_tenbenhnhan          varchar2(500) default ' ';
    v_diachi               varchar2(500) default ' ';
    v_dantoc               varchar2(500) default ' ';
    v_tuoi                 varchar2(500) default ' ';
    v_nghenghiep           varchar2(500) default ' ';
    v_sobenhan             varchar2(500) default ' ';
    v_soluutru             varchar2(1000) default ' ';
    v_gioitinh             number(10);
    v_ngaysinh             date;
    v_ngayvao              date;
    v_ngayra               date;
    v_soluutruchuyenvien   varchar2(500) default ' ';
    v_soluutruchuyenvienht varchar2(1);
    v_bosochuyentuyen      varchar2(1);
    v_bosohoso             varchar2(1);
    v_sovaosochuyentuyen   varchar2(1);
    v_tendonvi             varchar2(250) default ' ';
    v_tuyen_dv             varchar2(250) default ' ';
    v_tennoidk             varchar2(250) default ' ';
    v_chuoikn1             varchar2(250) default ' ';
    v_chuoikn2             varchar2(250) default ' ';
    v_chuoikn3             varchar2(250) default ' ';
    v_chuoikn4             varchar2(250) default ' ';
    v_sochuyen_tyt         clob;
    v_sochuyen_pkdk        clob;
    v_sochuyen_ttyt        clob;
    v_sochuyen_l2          clob;
    v_sochuyen_donvi       clob;
    v_tuyen_tyt            clob;
    v_tuyen_l2             clob;
    v_tuyen_pkdk           clob;
    v_tuyen_ttyt           clob;
    v_tuyen_donvi          clob;
    v_dieutritai           clob;
    v_tuyendieutri         clob;
    v_duockhamdieutritai   clob;
-- Bổ sung
    v_demct                number(10);
    -- Bổ sung
    v_sohoso               varchar2(50) default '';
    -- Begin An Giang - Tâm 0918197999 21/03/2017: bổ sung thông tin để in giấy chuyển tuyến
    v_manoigioithieu       varchar2(500) default '';
    v_tennoigioithieu      varchar2(1000) default '';
    v_chandoannoigioithieu varchar2(1000) default '';
    -- End An Giang - Tâm 0918197999 21/03/2017: bổ sung thông tin để in giấy chuyển tuyến

    v_ma_bsdieutricuoicung varchar2(50) default '';
    v_ngoaikieu            varchar2(50) default '';
    v_93194_ct_so          NUMBER(1);
    v_93195_ct_sohoso      NUMBER(1);
    v_93196_ct_sovaoso     NUMBER(1);
    v_93197_ct_hanthe      NUMBER(1);
		v_thamso960616 number(10) := cmu_tsdv(p_dvtt, 960616, 0);
BEGIN
    v_93194_ct_so := get_tsdv(p_dvtt, 93194, 0);
    v_93195_ct_sohoso := get_tsdv(p_dvtt, 93195, 0);
    v_93196_ct_sovaoso := get_tsdv(p_dvtt, 93196, 0);
    v_93197_ct_hanthe := get_tsdv(p_dvtt, 93197, 0);
begin
select mota_thamso
into v_soluutruchuyenvienht
from HIS_FW.DM_THAMSO_DONVI
where ma_thamso = 820113
  and dvtt = p_dvtt;
exception
        when no_data_found then
            v_soluutruchuyenvienht := '0';
end;

select replace(SOPHIEUTHANHTOAN,
               substr(SOPHIEUTHANHTOAN,
                      greatest(-length(SOPHIEUTHANHTOAN), -5)),
               '')
into v_sohoso
from his_manager.noitru_phieuthanhtoan
where dvtt = p_dvtt
  and stt_benhan = p_stt_benhan
  and rownum <= 1;

begin
select tdt_nguoilap
into v_ma_bsdieutricuoicung
from his_manager.noitru_dieutri
where stt_benhan = p_stt_benhan
  and DVTT = p_dvtt
  and stt_dieutri = (select max(
                                    cast(STT_DIEUTRI as number)
                            )
                     from noitru_dieutri
                     WHERE DVTT = p_dvtt
                       AND STT_BENHAN = p_stt_benhan
                       and stt_dotdieutri = p_stt_dotdieutri)
  and stt_dotdieutri = p_stt_dotdieutri;
exception
            when no_data_found then
                null;
end;

select TEN_BENH_NHAN,
       NGAY_SINH,
       GIOI_TINH,
       SO_NHA || ' ' || DIA_CHI,
       TEN_DANTOC,
       TEN_NGHE_NGHIEP,
       CASE
           WHEN BN.TUOI >= 6
               THEN CAST(BN.TUOI AS VARCHAR2(4))
           ELSE HIENTHI_TUOI_BENHNHAN(BN.NGAY_SINH) END AS TUOI,
       case
           when bn.NGOAIKIEU = 1 then
               'X'
           else
               ' '
           end                                          as ngoaikieu
into v_tenbenhnhan,
    v_ngaysinh,
    v_gioitinh,
    v_diachi,
    v_dantoc,
    v_nghenghiep,
    v_tuoi,
    v_ngoaikieu
from his_public_list.dm_benh_nhan bn,
     his_public_list.dm_dan_toc dt,
     his_public_list.dm_nghenghiep nn
where MA_BENH_NHAN = p_mabenhnhan
  and to_number(bn.ma_dantoc) = to_number(dt.ma_dantoc)
  and bn.MA_NGHE_NGHIEP = nn.MA_NGHE_NGHIEP;

select case
           when nvl(sobenhan, ' ') = ' ' then
               sobenhan_tt
           else
               sobenhan
           end,

       case
           when v_soluutruchuyenvienht = 1 then
               case
                   when nvl(SOCHUYENVIEN_LUUTRU, ' ') = ' ' then
                       'Số lưu trữ:' || SOCHUYENVIEN_TT_LUUTRU
                   else
                       'Số lưu trữ:' || SOCHUYENVIEN_LUUTRU
                   end
           else ' '
           end,


       case
           when nvl(SOXUATVIEN_LUUTRU, ' ') = ' ' then
               SOXUATVIEN_TT_LUUTRU
           else
               SOXUATVIEN_LUUTRU
           end

       -- Begin An Giang - Tâm 0918197999 21/03/2017: bổ sung thông tin để in giấy chuyển tuyến
        ,
       nvl(TEN_NOIGIOITHIEU, ''),
       nvl(MANOIGIOITHIEU, ''),
       nvl(CHANDOAN_NOIGIOITHIEU, '')
-- End An Giang - Tâm 0918197999 21/03/2017: bổ sung thông tin để in giấy chuyển tuyến
into v_sobenhan,v_soluutruchuyenvien,
    v_soluutru
    -- Begin An Giang - Tâm 0918197999 21/03/2017: bổ sung thông tin để in giấy chuyển tuyến
    ,
    v_manoigioithieu,
    v_tennoigioithieu,
    v_chandoannoigioithieu
-- End An Giang - Tâm 0918197999 21/03/2017: bổ sung thông tin để in giấy chuyển tuyến
from his_manager.noitru_benhan
where stt_benhan = p_stt_benhan
  and dvtt = p_dvtt;

select upper(TEN_NOITIEPNHAN), TUYEN_BV
into v_tendonvi, v_tuyen_dv
from his_public_list.dm_noi_tiep_nhan
where MA_NOITIEPNHAN = p_dvtt;
-- Bổ sung
select count(dot.stt_benhan)
into v_demct
from his_manager.noitru_dotdieutri dot
where dot.DVTT = p_dvtt
  and dot.stt_dotdieutri = p_stt_dotdieutri
  and dot.stt_benhan = p_stt_benhan
  and dot.sobaohiemyte != ' ';
-- Bổ sung
if v_demct > 0 then
select case
           when TUYEN_BV = 4 then
               replace(TEN_NOITIEPNHAN, substr(TEN_NOITIEPNHAN, 1, 10), 'TYT ')
           else
               TEN_NOITIEPNHAN
           end
into v_tennoidk
from his_public_list.dm_noi_tiep_nhan noidk,
     his_manager.noitru_dotdieutri dot
where dot.DVTT = p_dvtt
  and dot.stt_dotdieutri = p_stt_dotdieutri
  and dot.stt_benhan = p_stt_benhan
  and dot.noidangkybandau = MA_NOITIEPNHAN;
else
        v_tennoidk := ' ';
end if;
select ngay_vao, ngay_ra
into v_ngayvao, v_ngayra
from his_manager.noitru_dotdieutri
where DVTT = p_dvtt
  and stt_dotdieutri = p_stt_dotdieutri
  and stt_benhan = p_stt_benhan;

select case
           when nvl(SOCHUYEN_L2, ' ') != ' ' then
               '+ Tại: Số GCT: ' || SOCHUYEN_L2 || '/' || TEN_L2
           else
               ' '
           end,
       case
           when nvl(SOCHUYEN_TYT, ' ') != ' ' then
               '+ Tại: Số GCT: ' || SOCHUYEN_TYT || '/' || v_tennoidk
           else
               ' '
           end,
       case
           when nvl(SOCHUYEN_PKDK, ' ') != ' ' then
               '+ Tại: Số GCT: ' || SOCHUYEN_PKDK || '/' || TEN_PKDK
           else
               ' '
           end,
       case
           when nvl(SOCHUYEN_TTYT, ' ') != ' ' then
               '+ Tại: Số GCT: ' || SOCHUYEN_TTYT || '/' || TEN_TTYT
           else
               ' '
           end,
       case
           when nvl(SOCHUYEN_L2, ' ') != ' ' then
               ' ( Tuyến 2' || ' ) - Từ ' ||
               'ngày ' || to_char(TUNGAY_L2, 'DD') || ' tháng ' || to_char(TUNGAY_L2, 'MM') || ' năm ' ||
               to_char(TUNGAY_L2, 'YYYY') || ' đến ' ||
               'ngày ' || to_char(DENNGAY_L2, 'DD') || ' tháng ' || to_char(DENNGAY_L2, 'MM') || ' năm ' ||
               to_char(DENNGAY_L2, 'YYYY')
           else
               ' '
           end,
       case
           when nvl(SOCHUYEN_TYT, ' ') != ' ' then
               ' ( Tuyến 4' || ' ) - Từ ' ||
               'ngày ' || to_char(TUNGAY_TYT, 'DD') || ' tháng ' || to_char(TUNGAY_TYT, 'MM') || ' năm ' ||
               to_char(TUNGAY_TYT, 'YYYY') || ' đến ' ||
               'ngày ' || to_char(DENNGAY_TYT, 'DD') || ' tháng ' || to_char(DENNGAY_TYT, 'MM') || ' năm ' ||
               to_char(DENNGAY_TYT, 'YYYY')
           else
               ' '
           end,
       case
           when nvl(SOCHUYEN_PKDK, ' ') != ' ' then
               ' ( Tuyến 3' || ' ) - Từ ' ||
               'ngày ' || to_char(TUNGAY_PKDK, 'DD') || ' tháng ' || to_char(TUNGAY_PKDK, 'MM') || ' năm ' ||
               to_char(TUNGAY_PKDK, 'YYYY') || ' đến ' ||
               'ngày ' || to_char(DENNGAY_PKDK, 'DD') || ' tháng ' || to_char(DENNGAY_PKDK, 'MM') || ' năm ' ||
               to_char(DENNGAY_PKDK, 'YYYY')
           else
               ' '
           end,
       case
           when nvl(SOCHUYEN_TTYT, ' ') != ' ' then
               ' ( Tuyến 3' || ' ) - Từ ' ||
               'ngày ' || to_char(TUNGAY_TTYT, 'DD') || ' tháng ' || to_char(TUNGAY_TTYT, 'MM') || ' năm ' ||
               to_char(TUNGAY_TTYT, 'YYYY') || ' đến ' ||
               'ngày ' || to_char(DENNGAY_TTYT, 'DD') || ' tháng ' || to_char(DENNGAY_TTYT, 'MM') || ' năm ' ||
               to_char(DENNGAY_TTYT, 'YYYY')
           else
               ' '
           end
into v_sochuyen_l2,
    v_sochuyen_tyt,
    v_sochuyen_pkdk,
    v_sochuyen_ttyt,
    v_tuyen_l2,
    v_tuyen_tyt,
    v_tuyen_pkdk,
    v_tuyen_ttyt
from his_manager.noitru_chuyentuyenbenhnhan
where stt_dotdieutri = p_stt_dotdieutri
  and stt_benhan = p_stt_benhan
  and DVTT = p_dvtt;

v_sochuyen_donvi := Concat('+ Tại: ', v_tendonvi);

    if v_sochuyen_tyt != ' ' then
        v_chuoikn1 := chr(10);
else
        v_chuoikn1 := ' ';
end if;
    if v_sochuyen_pkdk != ' ' then
        v_chuoikn2 := chr(10);
else
        v_chuoikn2 := ' ';
end if;
    if v_sochuyen_ttyt != ' ' then
        v_chuoikn3 := chr(10);
else
        v_chuoikn3 := ' ';
end if;

    IF SUBSTR(p_dvtt, 0, 2) = '80' THEN
        v_dieutritai := v_sochuyen_l2 || v_chuoikn4 || v_sochuyen_tyt || v_chuoikn1 || v_sochuyen_pkdk ||
                        v_chuoikn2 || v_sochuyen_ttyt || v_chuoikn3 ||
                        v_tendonvi || ' ( ' || 'Tuyến ' || v_tuyen_dv || ' )';
ELSE
        v_dieutritai := v_sochuyen_l2 || v_chuoikn4 || v_sochuyen_tyt || v_chuoikn1 || v_sochuyen_pkdk ||
                        v_chuoikn2 || v_sochuyen_ttyt || v_chuoikn3 ||
                        v_sochuyen_donvi;
END IF;
    IF SUBSTR(p_dvtt, 0, 2) = '80' THEN
        v_tuyen_donvi := '- Từ ngày ' ||
                         'ngày ' || to_char(v_ngayvao, 'DD') || ' tháng ' || to_char(v_ngayvao, 'MM') || ' năm ' ||
                         to_char(v_ngayvao, 'YYYY') || ' đến ngày ' ||
                         'ngày ' || to_char(v_ngayra, 'DD') || ' tháng ' || to_char(v_ngayra, 'MM') || ' năm ' ||
                         to_char(v_ngayra, 'YYYY');
ELSE
        v_tuyen_donvi := ' ( Tuyến ' || v_tuyen_dv || ' ) - Từ ' ||
                         'ngày ' || to_char(v_ngayvao, 'DD') || ' tháng ' || to_char(v_ngayvao, 'MM') || ' năm ' ||
                         to_char(v_ngayvao, 'YYYY') || ' đến ' ||
                         'ngày ' || to_char(v_ngayra, 'DD') || ' tháng ' || to_char(v_ngayra, 'MM') || ' năm ' ||
                         to_char(v_ngayra, 'YYYY');
END IF;
    if v_tuyen_tyt != ' ' then
        v_chuoikn1 := chr(10);
else
        v_chuoikn1 := ' ';
end if;
    if v_tuyen_pkdk != ' ' then
        v_chuoikn2 := chr(10);
else
        v_chuoikn2 := ' ';
end if;
    if v_tuyen_ttyt != ' ' then
        v_chuoikn3 := chr(10);
else
        v_chuoikn3 := ' ';
end if;
    if v_tuyen_l2 != ' ' then
        v_chuoikn4 := chr(10);
else
        v_chuoikn4 := ' ';
end if;

    v_tuyendieutri := v_tuyen_l2 || v_chuoikn4 || v_tuyen_tyt || v_chuoikn1 || v_tuyen_pkdk || v_chuoikn2 ||
                      v_tuyen_ttyt || v_chuoikn3 || v_tuyen_donvi;
    v_duockhamdieutritai := trim(v_sochuyen_l2 || v_tuyen_l2 || v_chuoikn4
        || v_sochuyen_tyt || v_tuyen_tyt || v_chuoikn1
        || v_sochuyen_pkdk || v_tuyen_pkdk || v_chuoikn2
        || v_sochuyen_ttyt || v_tuyen_ttyt || v_chuoikn3
        || v_sochuyen_donvi || v_tuyen_donvi);

    /* Neu so tuoi bang 0 hien thi thang */
    --begin
    if v_tuoi = '0' then
        v_tuoi := (extract(year from v_ngayvao) - extract(year from v_ngaysinh)) * 12 +
                  (extract(month from v_ngayvao) -
                   extract(month from v_ngaysinh)) || ' tháng';
end if;
    --end
begin
select mota_thamso
into v_bosochuyentuyen
from HIS_FW.DM_THAMSO_DONVI
where ma_thamso = 96060
  and dvtt = p_dvtt;
exception
        when no_data_found then
            v_bosochuyentuyen := '0';
end;

begin
select mota_thamso
into v_bosohoso
from HIS_FW.DM_THAMSO_DONVI
where ma_thamso = 96034
  and dvtt = p_dvtt;
exception
        when no_data_found then
            v_bosohoso := '0';
end;

begin
select mota_thamso
into v_sovaosochuyentuyen
from HIS_FW.DM_THAMSO_DONVI
where ma_thamso = 96035
  and dvtt = p_dvtt;
exception
        when no_data_found then
            v_sovaosochuyentuyen := '0';
end;

    if p_dvtt = '86131' or p_dvtt = '86132' then
        open cur for
select v_tenbenhnhan                                   as TEN_BENH_NHAN,
       case
           when v_gioitinh = 1 then
               'X'
           else
               ' '
           end                                         as nam,
       case
           when v_gioitinh = 0 then
               'X'
           else
               ' '
           end                                         as nu,
       to_char(v_ngaysinh, 'dd/mm/yyyy')                  ngaysinh,
       v_tuoi                                          as TUOI,
       v_diachi                                        as DIA_CHI,
       v_dantoc                                        as TEN_DANTOC,
       v_nghenghiep                                    as TEN_NGHE_NGHIEP,
       to_char(ngaybatdau_theBHYT, 'DD/MM/YYYY')       as tungay,
       to_char(ngayhethan_theBHYT, 'DD/MM/YYYY')       as denngay,
       nvl(substr(sobaohiemyte, 1, 2), ' ')            as sothe12,
       substr(sobaohiemyte, 3, 1)                      as sothe3,
       substr(sobaohiemyte, 4, 2)                      as sothe45,
       substr(sobaohiemyte, 6, 2)                      as sothe67,
       substr(sobaohiemyte, 8, 3)                      as sothe8910,
       substr(sobaohiemyte, 11, 5)                     as sothecuoi,
       sobaohiemyte as sobaohiemyte_full,
       noidangkybandau                                 as NOIDANGKY_KCB,
       v_dieutritai                                    as dieutri,
       v_tuyendieutri                                  as tuyendieutri,
       v_duockhamdieutritai                            as duockhamdieutritai,
       -- tóm t?t b?nh án
       -- Concat(SOCHUYENVIEN_LUUTRU,'/GCT') as SOCHUYENVIEN_LUUTRU,
       ' '                                             as SOCHUYENVIEN_LUUTRU,
       tenbenhvien_chuyendi,
       Concat('- Dấu hiệu lâm sàng: ', dauhieulamsang) as dauhieulamsang,
       Concat('- Kết quả xét nghiệm, cận lâm sàng: ',
              ketquaxetnghiem_cls)                     as ketquaxetnghiem_cls,
       case
           when BENHKEMTHEO != ' ' then
               '- Chẩn đoán: ' || ICD_BENHCHINH || ': ' || TEN_BENHCHINH || ', ' ||
               BENHKEMTHEO
           else
               '- Chẩn đoán: ' || ICD_BENHCHINH || ': ' || TEN_BENHCHINH
           end                                         as chandoan,
       Concat('- Phương pháp, thủ thuật, kỹ thuật, thuốc đã sử dụng trong điều trị: ',
              PP_TTPT_THUOC_DADUNG)                    as PP_TTPT_THUOC_DADUNG,
       Concat('- Tình trạng người bệnh lúc chuyển tuyến: ',
              TINHTRANGBENHNHAN)                       as TINHTRANGBENHNHAN,
       case
           when ct.LYDOCHUYENTUYEN = 0 OR ct.LYDOCHUYENTUYEN = 2 OR ct.LYDOCHUYENTUYEN = 3 then
               'X'
           else
               ' '
           end                                         as dudieukien,
       case
           when LYDOCHUYENTUYEN = 1 then
               'X'
           else
               ' '
           end                                         as kodudieukien,
       Concat('- Hướng điều trị: ', HUONGDIEUTRI)      as HUONGDIEUTRI,
       to_char(thoigianchuyenvien, 'HH24') || ' giờ ' ||
       to_char(thoigianchuyenvien, 'MI') || ' phút ,' || ' ngày ' ||
       to_char(thoigianchuyenvien, 'DD') || ' tháng ' ||
       to_char(thoigianchuyenvien, 'MM') || ' năm ' ||
       to_char(thoigianchuyenvien, 'YYYY')             as ngaychuyen,
       PHUONGTIENVANCHUYEN,
       'Ngày ' || to_char(thoigianchuyenvien, 'DD') || ' tháng ' ||
       to_char(thoigianchuyenvien, 'MM') || ' năm ' ||
       to_char(thoigianchuyenvien, 'YYYY')             AS ngayky,
       case
           when p_dvtt = '89014' or v_thamso960616 = 1 then
               ' '
           else
               case
                   when nvl(mota_chucdanh, ' ') != ' ' then
                       mota_chucdanh || '. ' || ten_nhanvien
                   else
                       ten_nhanvien
                   end
           end                                         as ten_nhanvien,
       hotennguoiduadi,
       v_sobenhan                                      as sobenhan,
       v_soluutru                                      as soluutru,
       ct.sochuyenvien_luutru                          as SO_VAO_SO_CT,
       v_sohoso                                        as SO_HO_SO,
       ct.LYDOCHUYENTUYEN

from his_manager.noitru_chuyentuyenbenhnhan ct,
     his_manager.noitru_dotdieutri dot,
     his_fw.dm_nhanvien nv,
     his_fw.dm_chucdanh_nhanvien cd
where ct.stt_dotdieutri = p_stt_dotdieutri
  and ct.stt_benhan = p_stt_benhan
  and ct.DVTT = p_dvtt
  and ct.stt_dotdieutri = dot.stt_dotdieutri
  and ct.stt_benhan = dot.stt_benhan
  and ct.DVTT = dot.DVTT
  and ct.nhanvienchuyenvien = nv.ma_nhanvien
  and nv.chucdanh_nhanvien = cd.ma_chucdanh;
else
        open cur for
select v_tenbenhnhan                                       as TEN_BENH_NHAN,
       case
           when v_gioitinh = 1 then
               'X'
           else
               ' '
           end                                             as nam,
       case
           when v_gioitinh = 0 then
               'X'
           else
               ' '
           end                                             as nu,
       to_char(v_ngaysinh, 'dd/mm/yyyy')                      ngaysinh,
       v_tuoi                                              as TUOI,
       v_diachi                                            as DIA_CHI,
       v_dantoc                                            as TEN_DANTOC,
       v_nghenghiep                                        as TEN_NGHE_NGHIEP,
       -- AGG Quí 06/09/2017 điều chỉnh nvl thì trả về rổng để tránh report hiện null
       nvl(to_char(ngaybatdau_theBHYT, 'DD/MM/YYYY'), ' ') as tungay,
       nvl(to_char(ngayhethan_theBHYT, 'DD/MM/YYYY'), ' ') as denngay,
       nvl(substr(sobaohiemyte, 1, 2), ' ')                as sothe12,
       nvl(substr(sobaohiemyte, 3, 1), ' ')                as sothe3,
       nvl(substr(sobaohiemyte, 4, 2), ' ')                as sothe45,
       nvl(substr(sobaohiemyte, 6, 2), ' ')                as sothe67,
       nvl(substr(sobaohiemyte, 8, 3), ' ')                as sothe8910,
       nvl(substr(sobaohiemyte, 11, 5), ' ')               as sothecuoi,
       sobaohiemyte as sobaohiemyte_full,
       nvl(noidangkybandau, ' ')                           as NOIDANGKY_KCB,
       v_dieutritai                                        as dieutri,
       v_tuyendieutri                                      as tuyendieutri,
       v_duockhamdieutritai                                as duockhamdieutritai,
       -- tóm t?t b?nh án
       case
           when v_bosochuyentuyen = '1' then
               ' '
           else
               case
                   when p_dvtt = '82207' then
                       Concat(substr(SOCHUYENVIEN_LUUTRU, length(SOCHUYENVIEN_LUUTRU) - 3, 4), '/GCT')
                   else
                       case
                           when p_dvtt = 24004 then Concat(SOCHUYENVIEN_LUUTRU, '/NT GCT')
                           else case
                                    when p_dvtt = 82002 then '...................../2019/GCT'
                                    else Concat(SOCHUYENVIEN_LUUTRU, '/ GCT')
                               end
                           end
                   end
           end                                             as SOCHUYENVIEN_LUUTRU,
       tenbenhvien_chuyendi,
       Concat('- Dấu hiệu lâm sàng: ', dauhieulamsang)     as dauhieulamsang,
       Concat('- Kết quả xét nghiệm, cận lâm sàng: ',
              ketquaxetnghiem_cls)                         as ketquaxetnghiem_cls,
       case
           when BENHKEMTHEO != ' ' then
               '- Chẩn đoán: ' || ICD_BENHCHINH || ': ' || TEN_BENHCHINH || ', ' ||
               BENHKEMTHEO
           else
               '- Chẩn đoán: ' || ICD_BENHCHINH || ': ' || TEN_BENHCHINH
           end                                             as chandoan,
       Concat('- Phương pháp, thủ thuật, kỹ thuật, thuốc đã sử dụng trong điều trị: ',
              PP_TTPT_THUOC_DADUNG)                        as PP_TTPT_THUOC_DADUNG,
       Concat('- Tình trạng người bệnh lúc chuyển tuyến: ',
              TINHTRANGBENHNHAN)                           as TINHTRANGBENHNHAN,
       case
           when ct.LYDOCHUYENTUYEN = 0 OR ct.LYDOCHUYENTUYEN = 2 OR ct.LYDOCHUYENTUYEN = 3 then
               'X'
           else
               ' '
           end                                             as dudieukien,
       case
           when LYDOCHUYENTUYEN = 1 then
               'X'
           else
               ' '
           end                                             as kodudieukien,
       Concat('- Hướng điều trị: ', HUONGDIEUTRI)          as HUONGDIEUTRI,
       to_char(thoigianchuyenvien, 'HH24') || ' giờ ' ||
       to_char(thoigianchuyenvien, 'MI') || ' phút ,' || ' ngày ' ||
       to_char(thoigianchuyenvien, 'DD') || ' tháng ' ||
       to_char(thoigianchuyenvien, 'MM') || ' năm ' ||
       to_char(thoigianchuyenvien, 'YYYY')                 as ngaychuyen,
       PHUONGTIENVANCHUYEN,
       'Ngày ' || to_char(thoigianchuyenvien, 'DD') || ' tháng ' ||
       to_char(thoigianchuyenvien, 'MM') || ' năm ' ||
       to_char(thoigianchuyenvien, 'YYYY')                 AS ngayky,
       case
           when p_dvtt = '89014' or v_thamso960616 = 1 then
               ' '
           else
               case
                   when nvl(mota_chucdanh, ' ') != ' ' then
                       mota_chucdanh || '. ' || ten_nhanvien
                   else
                       ten_nhanvien
                   end
           end                                             as ten_nhanvien,
       hotennguoiduadi,
       case
           when v_bosohoso = '1' then
               ' '
           else
               v_sobenhan
           end                                             as sobenhan,
       v_soluutruchuyenvien                                as soluutruchuyenvien,
       v_soluutru                                          as soluutru,
       case
           when SUBSTR(p_dvtt, 0, 2) = '96' then
               to_char(ct.thoigianchuyenvien, 'MM') || '/' ||
               to_char(ct.thoigianchuyenvien, 'YYYY')
           else
               case
                   when v_sovaosochuyentuyen = '1' then
                       ' '
                   else
                       ct.sochuyenvien_luutru
                   end
           end                                             as SO_VAO_SO_CT,
       v_sohoso                                            as SO_HO_SO
       -- Begin An Giang - Tâm 0918197999 21/03/2017: bổ sung thông tin để in giấy chuyển tuyến
        ,
       v_manoigioithieu                                       TENNOIGIOITHIEU,
       v_tennoigioithieu                                      NOIGIOITHIEU,
       v_chandoannoigioithieu                                 CHANDOAN_NOIGIOITHIEU,

       (select ten_nhanvien
        from his_fw.dm_nhanvien
        where ma_nhanvien = v_ma_bsdieutricuoicung
       )                                                   as ten_bsdieutricuoicung,
       ct.LYDOCHUYENTUYEN,
       NOILAMVIEC                                             noi_lamviec,
       decode(dot.COBHYT, 1, 'true', 'false')                 cobhyt,
       v_ngoaikieu                                            ngoaikieu,
       v_93194_ct_so                                          hgi_93194_ct_so,
       v_93195_ct_sohoso                                      hgi_93195_ct_sohoso,
       v_93196_ct_sovaoso                                     hgi_93196_ct_sovaoso,
       v_93197_ct_hanthe                                      hgi_93197_ct_hanthe,
       -- End An Giang - Tâm 0918197999 21/03/2017: bổ sung thông tin để in giấy chuyển tuyến
       ptt.sophieuthanhtoan, -- BDH thêm số phiếu thanh toán
       dot.noidangkybandau
from his_manager.noitru_chuyentuyenbenhnhan ct,
     his_manager.noitru_dotdieutri dot,
     his_fw.dm_nhanvien nv,
     his_fw.dm_chucdanh_nhanvien cd,
     his_manager.noitru_phieuthanhtoan ptt -- BDH bổ sung lấy số phiếu thanh toán
where ct.stt_dotdieutri = p_stt_dotdieutri
  and ct.stt_benhan = p_stt_benhan
  and ct.DVTT = p_dvtt
  and ct.stt_dotdieutri = dot.stt_dotdieutri
  and ct.stt_benhan = dot.stt_benhan
  and ct.DVTT = dot.DVTT
  and ct.nhanvienchuyenvien = nv.ma_nhanvien
  and nv.chucdanh_nhanvien = cd.ma_chucdanh
  and ptt.dvtt = p_dvtt
  and ptt.stt_benhan = ct.stt_benhan
  and ptt.stt_dotdieutri = p_stt_dotdieutri
  and ptt.sovaovien = ct.sovaovien
  AND ptt.sovaovien_dt = ct.sovaovien_dt;
end if;
END;
