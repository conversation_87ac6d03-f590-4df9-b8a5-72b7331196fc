CREATE OR <PERSON><PERSON><PERSON>CE
PROCEDURE             "RIS_UPDATE_TRANGTHAI_CACHUP"(P_SOPHIEU   VARCHAR2,
                                                                      P_DVTT      VARCHAR2,
                                                                      P_MADV      VARCHAR2,
                                                                      P_TRANGTHAI VARCHAR2,
                                                                      P_THOIGIAN  VARCHAR2,
                                                                      P_MAMAY     VARCHAR2,
                                                                      C<PERSON>         OUT SYS_REFCURSOR) IS
  V_KIEMTRANGT NUMBER(11);
  V_KIEMTRANT  NUMBER(11);
  V_TIME       DATE;
  VCOUNT       NUMBER(11);
  P_NOITRU     NUMBER(2);
	v_return number;
	v_sovaovien number;
	v_<PERSON><PERSON><PERSON><PERSON> number;
BEGIN
  V_TIME := TO_DATE(P_THOIGIAN, 'dd/mm/yyyy hh24:mi:ss');
BEGIN
SELECT COUNT(1)
INTO P_NOITRU
FROM HIS_MANAGER.NOITRU_CD_CDHA T
WHERE T.SO_PHIEU_CDHA = P_SOPHIEU
  AND T.DVTT = P_DVTT;
EXCEPTION
    WHEN NO_DATA_FOUND THEN
      P_NOITRU := 0;
END;
BEGIN
SELECT COUNT(1)
INTO V_KIEMTRANGT
FROM KB_CD_CDHA
WHERE DVTT = P_DVTT
  AND SO_PHIEU_CDHA = P_SOPHIEU;
EXCEPTION
    WHEN NO_DATA_FOUND THEN
      VCOUNT := 0;
END;
BEGIN
SELECT COUNT(1)
INTO V_KIEMTRANT
FROM NOITRU_CD_CDHA
WHERE DVTT = P_DVTT
  AND SO_PHIEU_CDHA = P_SOPHIEU;
EXCEPTION
    WHEN NO_DATA_FOUND THEN
      VCOUNT := 0;
END;
  IF P_NOITRU = 0 THEN
select sovaovien into v_sovaovien
from HIS_MANAGER.KB_CD_CDHA_CT
WHERE DVTT = P_DVTT
  AND SO_PHIEU_CDHA = P_SOPHIEU
  AND MA_CDHA = P_MADV;
UPDATE HIS_MANAGER.KB_CD_CDHA_CT
SET RIS_TT_CACHUP  = P_TRANGTHAI,
    NGAY_THUC_HIEN = V_TIME,
    STT_MAYCDHA    = P_MAMAY
WHERE DVTT = P_DVTT
  AND SO_PHIEU_CDHA = P_SOPHIEU
  AND MA_CDHA = P_MADV;
VCOUNT := SQL%ROWCOUNT;
ELSE
select sovaovien into v_sovaovien
from HIS_MANAGER.NOITRU_CD_CDHA_CHI_TIET
WHERE DVTT = P_DVTT
  AND SO_PHIEU_CDHA = P_SOPHIEU
  AND MA_CDHA = P_MADV;

UPDATE HIS_MANAGER.NOITRU_CD_CDHA_CHI_TIET
SET RIS_TT_CACHUP  = P_TRANGTHAI,
    NGAY_THUC_HIEN = V_TIME,
    STT_MAYCDHA    = P_MAMAY
WHERE DVTT = P_DVTT
  AND SO_PHIEU_CDHA = P_SOPHIEU
  AND MA_CDHA = P_MADV;
VCOUNT := SQL%ROWCOUNT;
END IF;
update CMU_THOIGIAN_NHANVIEN
set  THOIGIAN_KT = V_TIME
where dvtt =p_dvtt and sovaovien = v_sovaovien
  and SOPHIEU = P_SOPHIEU
  and MA_DV = P_MADV;

OPEN CUR FOR
SELECT VCOUNT AS TT FROM DUAL;
END;
