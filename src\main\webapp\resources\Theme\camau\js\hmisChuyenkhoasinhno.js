$(function() {
    var initDataSinhnoFlag = false;
    var formSinhnoIO;
    var formChungsinhIO;
    var dsQuyenchungsinh;
    var dsTinh;
    $("#ttin_dieutri_showkhamchuyenkhoa-dropdown p").click(function() {
        var dataId = $(this).attr("data-id");
        if(dataId == 'sinhno' ) {
            if(thongtinhsba.thongtinbn.GIOI_TINH == 1) {
                return notifiToClient("Red","Bệnh nhân nam không thể chuyển khoa sinh nở")
            }
            $("#modalHMISKhamchuyenkhoasinhno").modal("show");
            addTextTitleModal("titleModalHMISKhamchuyenkhoasinhno", "Chuyên khoa sinh nở");
            initGridLankhamSinhno();
            loadGridLankhamSinhno();
            showOrHideByClass("HMISKhamchuyenkhoasinhnoWrap", "edit", "add")
            showOrHideByClass("HMISKhamchuyenkhoasinhnoFooterWrap", "add", "edit")
            if(!initDataSinhnoFlag) {
                initDataSinhno();
            }

        }
        if(dataId == 'todieutritong' ) {
            var tenkhoa = "";
            singletonObject.danhsachphongban.forEach(function(obj) {
                if(obj.MAKHOA == singletonObject.makhoa) {
                    tenkhoa = obj.TENKHOA;
                }
            })
            var arr = [singletonObject.dvtt,
                thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                0,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                tenkhoa,
                thongtinhsba.thongtinbn.TUOI > 0 ? thongtinhsba.thongtinbn.TUOI :
                    (thongtinhsba.thongtinbn.THANG > 0?  (thongtinhsba.thongtinbn.THANG+ " Tháng"): (thongtinhsba.thongtinbn.NGAY + " Ngày")),
                thongtinhsba.thongtinbn.GIOI_TINH_HT,
                thongtinhsba.thongtinbn.SOBENHAN,
                $("#ttin_dieutri_bs").val(),
                $("#ttin_dieutri_khoa").val(),
                thongtinhsba.thongtinbn.STT_BENHAN,
                "/WEB-INF/pages/camau/reports/rp_todieutri_tong_v2_sub.jasper",
                singletonObject.user

            ];
            var param = ['dvtt', 'hovaten', 'id_dieutri', 'sovaovien', 'sovaovien_dt',
                "ten_phongban", "tuoi", "gioitinh", "sobenhan", "userid", "makhoa", "stt_benhan", "FILE_1", "nguoiin"];
            var url = "cmu_injasper?url=" + convertArray(arr) + "&param="
                + convertArray(param) + "&loaifile=pdf&jasper=rp_todieutri_tong_v2";
            previewPdfDefaultModal(url,'tdt-tong-iframe-preview');
        }
    })
    $("#hmis_chuyenkhoa_backtolist").click(function() {
        showOrHideByClass("HMISKhamchuyenkhoasinhnoWrap", "edit", "add")
        showOrHideByClass("HMISKhamchuyenkhoasinhnoFooterWrap", "add", "edit")
    })
    $("#hmis_chuyenkhoa_chungsinh").click(function() {
        $("#modalHMISKhamchuyenkhoaGiaychungsinh").modal("show");
        addTextTitleModal("titleModalHMISKhamchuyenkhoaGiaychungsinh", "Giấy chứng sinh");
        hideButtonandShowlistGCS()
        initGridDanhsachTreem()
        loadGridDanhsachTreem()

    })
    $("#hmis_chuyenkhoa_sinhno_luu").click(function() {
        var idButton = this.id
        formSinhnoIO.emit("checkValidity");
        if (!formSinhnoIO.checkValidity(null, false, null, true)) {
            return;
        }
        var dataSubmit = formSinhnoIO.submission.data;
        var NGAY_GIO_DE = moment(dataSubmit.NGAY_GIO_DE).format("DD/MM/YYYY HH:mm")
        $.ajax({
            url: "ktngaychotbaocao27ytcs?ngay=" + NGAY_GIO_DE.split(" ")[0]
        }).done(function (data) {
            if (data != 0) {
                return notifiToClient("Red","Đã chốt báo cáo ytcs");
            }
            dataSubmit.ID_SS_THONG_TIN = dataSubmit.ID_SS_THONG_TIN ? dataSubmit.ID_SS_THONG_TIN:"";
            var NGUOI_DO = "";
            singletonObject.danhsachtatcanhanvien.forEach(function (item) {
                if(item.value == dataSubmit.MA_NGUOI_DO) {
                    NGUOI_DO = item.label.split("(")[0];
                }
            });
            console.log("dataSubmit", dataSubmit)
            var str = [
                dataSubmit.ID_SS_THONG_TIN,
                NGAY_GIO_DE.split(" ")[0],
                dataSubmit.TIEN_THAI,
                dataSubmit.SL_DE_DU_THANG,
                dataSubmit.SL_DE_NON,
                dataSubmit.SL_SAY_PHA_THAI,
                dataSubmit.SL_CON,
                dataSubmit.QUAN_LY_THAI,//8
                dataSubmit.TIEM_UVDD,
                dataSubmit.TINH_TRANG_TRE,
                dataSubmit.HO_TEN_TRE,
                dataSubmit.TUAN_THAI,
                dataSubmit.CAN_NANG,
                dataSubmit.GIOI_TINH,
                dataSubmit.CHI_TIET_TINH_TRANG,
                NGUOI_DO,
                dataSubmit.BU_ME_GIO_DAU,//17
                dataSubmit.TIEM_VITAMINK,
                dataSubmit.DE_NON,
                dataSubmit.BI_NGAT,
                thongtinhsba.thongtinbn.MA_BENH_NHAN,
                thongtinhsba.thongtinbn.STT_BENHAN,
                singletonObject.dvtt,
                singletonObject.dvtt,
                dataSubmit.ID_TRINHDO_NGUOIDO,
                dataSubmit.ID_CACH_THUC_DE,//26
                dataSubmit.ID_SO_LAN_KT,
                dataSubmit.ID_TUVONG_THAINHI,
                dataSubmit.ID_NOI_DE,
                dataSubmit.UVSS,
                dataSubmit.VXUV_ME_TIEM,
                dataSubmit.DUOC_DIEU_TRA,
                dataSubmit.NGAY_TV,
                dataSubmit.TINH_TRANG_ME,
                dataSubmit.CHIEUDAI_LUCDE,//35
                dataSubmit.DITAT_BAMSINH,
                dataSubmit.VANDEKHAC,
                dataSubmit.ID_TT_LUCSINH,
                dataSubmit.SL_COTHAI,
                dataSubmit.CHUAN_DOAN,
                dataSubmit.EENC,
                dataSubmit.SO_TRE,
                dataSubmit.HO_TEN_TRE2,
                dataSubmit.DITAT_BAMSINH2,//DITAT2,
                dataSubmit.TINH_TRANG_TRE2,
                dataSubmit.GIOI_TINH2,//46
                dataSubmit.CAN_NANG2,
                dataSubmit.CHIEUDAI_LUCDE2,
                dataSubmit.UVSS2,
                dataSubmit.ID_TUVONG_THAINHI2,
                dataSubmit.NGAY_TV2,
                dataSubmit.HO_TEN_TRE3,
                dataSubmit.DITAT_BAMSINH3,//DITAT3,
                dataSubmit.TINH_TRANG_TRE3,
                dataSubmit.GIOI_TINH3,
                dataSubmit.CAN_NANG3,
                dataSubmit.CHIEUDAI_LUCDE3,
                dataSubmit.UVSS3,//58
                dataSubmit.ID_TUVONG_THAINHI3,
                dataSubmit.NGAY_TV3,
                1,
                dataSubmit.VACCINB,
                dataSubmit.FXGH,
                dataSubmit.KMC,
                dataSubmit.XNHIV,
                dataSubmit.KQXNHIV,
                dataSubmit.THAINC,
                dataSubmit.NGUYENNHAN_EENC,
                dataSubmit.LAN_KHAM_THAI,
                dataSubmit.DT_DUPHONG_LTHIV_ME,//70
                "",
                dataSubmit.DTDP_LTHIV_TRE,
                dataSubmit.PHAMVI_DE,
                dataSubmit.SANG_LOC_SS,
                dataSubmit.MA_NGUOI_DO,
                dataSubmit.GHI_CHU,
                thongtinhsba.thongtinbn.IDNHANKHAU,
                dataSubmit.TANGSINHMON,
                dataSubmit.ROBSON,
                dataSubmit.CSSS_THIETYEUSAUSINH,
                NGAY_GIO_DE.split(" ")[1].split(":")[0],
                NGAY_GIO_DE.split(" ")[1].split(":")[1],//82
                dataSubmit.CHUANDOANSOSINH_TRE1,
                dataSubmit.DIEUTRI_TRE1,
                dataSubmit.CANTHIEP_TRE1,//85
                dataSubmit.CHUANDOANSOSINH_TRE2,
                dataSubmit.DIEUTRI_TRE2,
                dataSubmit.CANTHIEP_TRE2,//88
                dataSubmit.CHUANDOANSOSINH_TRE3,
                dataSubmit.DIEUTRI_TRE3,
                dataSubmit.CANTHIEP_TRE3,
                0,
                dataSubmit.MA_BAME,
                dataSubmit.HIV_ARV,
                "",//tantat,
                "",//ngaysl,
                "",//kqsl,
                "",//tantat2,
                "",//slss2,
                "",//ngaysl2,
                "",//kqsl2,
                "",//tantat3,
                "",//slss3,
                "",//ngaysl3,
                "",//kqsl3,
                "",//106
                0,
                0];
            if (dataSubmit.ID_SS_THONG_TIN == "") {
                showSelfLoading(idButton)
                $.post("themchitietsinhno", {url : convertArray(str)}).done(function(ID_SS_THONG_TIN) {
                    if (data != "-1") {
                        $.post("cmu_post", {url: [singletonObject.dvtt, ID_SS_THONG_TIN, thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.SOVAOVIEN, 'CMU_UPDATE_TTSS_SVV'].join("```")});
                        notifiToClient("Green", MESSAGEAJAX.ADD_SUCCESS)
                        formSinhnoIO.submission = {
                            data: {
                                ...dataSubmit,
                                ID_SS_THONG_TIN
                            }
                        };
                        console.log("formSinhnoIO.submission.data", formSinhnoIO.submission.data)
                        console.log("formSinhnoIO", {
                            ...dataSubmit,
                            ID_SS_THONG_TIN
                        })
                        $("#HMISKhamchuyenkhoasinhnoFooterWrap .existData").show()
                    } else {
                        notifiToClient("Red", MESSAGEAJAX.FAIL)
                    }
                }).fail(function () {
                    notifiToClient("Red", MESSAGEAJAX.ERROR)
                }).always(function () {
                    hideSelfLoading(idButton)
                })
            } else {
                $.post("suachitietsinhsan", {url : convertArray(str)}).done(function(data) {
                    if (data != "-1") {
                        $.post("cmu_post", {url: [singletonObject.dvtt, dataSubmit.ID_SS_THONG_TIN, thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.SOVAOVIEN, 'CMU_UPDATE_TTSS_SVV'].join("```")});
                        notifiToClient("Green", MESSAGEAJAX.EDIT_SUCCESS)

                    } else {
                        notifiToClient("Red", MESSAGEAJAX.FAIL)
                    }
                }).fail(function () {
                    notifiToClient("Red", MESSAGEAJAX.ERROR)
                }).always(function () {
                    hideSelfLoading(idButton)
                });
            }

        }).fail(function () {
            notifiToClient("Red", MESSAGEAJAX.ERROR)
            hideSelfLoading(idButton);
        })



    })
    $("#hmis_chuyenkhoa_sinhno_xoa").click(function() {
        var idButton = this.id
        confirmToClient(MESSAGEAJAX.CONFIRM, function() {
            var dataSubmit = formSinhnoIO.submission.data;
            var NGAY_GIO_DE = moment(dataSubmit.NGAY_GIO_DE).format("DD/MM/YYYY HH:mm")
            showSelfLoading(idButton)
            $.ajax({
                url: "ktngaychotbaocao27ytcs?ngay=" + NGAY_GIO_DE.split(" ")[0]
            }).done(function (data) {
                if (data != 0) {

                    notifiToClient("Red","Đã chốt báo cáo ytcs");
                } else {
                    var str = [dataSubmit.ID_SS_THONG_TIN];
                    $.post("xoasinhno", {url: convertArray(str)}).done(function (data) {
                        if (data == "1") {
                            notifiToClient("Green", MESSAGEAJAX.DEL_SUCCESS)
                            $("#hmis_chuyenkhoa_backtolist").click();
                            loadGridLankhamSinhno()
                        }
                        else if (data == "-2") {
                            notifiToClient("Red", "Đã thêm giấy chứng sinh, không thể xoá!")
                        }
                        else {
                            notifiToClient("Red", MESSAGEAJAX.FAIL)
                        }
                    }).fail(function() {
                        notifiToClient("Red", MESSAGEAJAX.ERROR)

                    }).always(function () {
                        hideSelfLoading(idButton)
                    });
                }

            }).fail(function() {
                hideSelfLoading(idButton)
                notifiToClient("Red", MESSAGEAJAX.ERROR)

            })

        })
    })
    $("#hmis_chuyenkhoa_giaychungsinh_backtolist").click(function() {
        hideButtonandShowlistGCS()
    })
    $("#hmis_chuyenkhoa_giaychungsinh_luu").click(function() {
        var idButton = this.id
        formChungsinhIO.emit("checkValidity");
        if (!formChungsinhIO.checkValidity(null, false, null, true)) {
            return;
        }
        luuThongtinGCS(idButton)

    })
    $("#hmis_chuyenkhoa_giaychungsinh_xem").click(function() {
        if(formChungsinhIO.submission.data.ID_GCS == "") return notifiToClient("Red", "Chưa có giấy chứng sinh");

        getUrlGiayChungSinh({
            dvtt: singletonObject.dvtt,
            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
            SOVAOVIEN_DT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            ID_CHUNGSINH: formChungsinhIO.submission.data.ID_GCS
        }).then(objReturn => {
            if (objReturn.isError == 0) {
                previewPdfDefaultModal( objReturn.url, "preview_xemgiaychungsinh")
            } else {
                notifiToClient("Red", "Lỗi lấy url giấy báo tử");
            }
        }).catch(error => {
            notifiToClient("Red", error.message || "Lỗi không xác định");
        });
    })

    $("#hmis_chuyenkhoa_giaychungsinh_kyso").click(function() {
        formChungsinhIO.emit("checkValidity");
        if (!formChungsinhIO.checkValidity(null, false, null, true)) {
            return;
        }
        var idButton = this.id
        confirmToClient(MESSAGEAJAX.CONFIRM, function () {
            luuThongtinGCS(idButton, function() {
                var dataSubmit = formChungsinhIO.submission.data
                if(singletonObject.thamSo960601 == 1) {
                    $.get("cmu_getlist?url=" + convertArray([singletonObject.dvtt, dataSubmit.ID_GCS, 'CMU_XUAT_GIAYCS']))
                        .done(function (data) {
                            if (data.length > 0) {
                                data[0].MATINH_CU_TRU = dataSubmit.MATINH_CU_TRU
                                data[0].MAHUYEN_CU_TRU = dataSubmit.MAHUYEN_CU_TRU
                                data[0].MAXA_CU_TRU = dataSubmit.MAXA_CU_TRU

                                data[0].MAKHOA = singletonObject.makhoa;
                                data[0].DVTT = singletonObject.dvtt;
                                data[0].LOAI_GIAYTO_NND = data[0].SO_CCCD_NND.length > 9 ? 1 : 2;
                                data[0].SOVAOVIEN_DT = thongtinhsba.thongtinbn.SOVAOVIEN_DT;
                                data[0].MA_GCS = dataSubmit.MA_CT
                                data[0].NGAYCHUNGTU = data[0].NGAY_CT;
                                data[0].IDCHUNGSINH = dataSubmit.ID_GCS;
                                data[0].NGUOIGUI = singletonObject.userId + "-" + singletonObject.user;
                                var xmlData = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>"
                                    + "<HSDLGCS>" +
                                    "<GIAYCHUNGSINH Id=\"" + dataSubmit.ID_GCS + "\">" +
                                    "<MA_GCS>" + data[0].MA_GCS + "</MA_GCS>" +
                                    "<MA_BN>" + data[0].MA_BN + "</MA_BN>" +
                                    "<MA_CT>" + (data[0].MA_CT == null ? "" : data[0].MA_CT) + "</MA_CT>" +
                                    "<SO_SERI>" + (data[0].SO_SERI == null ? "" : data[0].SO_SERI) + "</SO_SERI>" +
                                    "<MA_BHXH_NND>" + data[0].MA_BHXH_NND + "</MA_BHXH_NND>" +
                                    "<MA_THE_NND>" + data[0].MA_THE_NND + "</MA_THE_NND>" +
                                    "<HOTEN_NND>" + data[0].HOTEN_NND + "</HOTEN_NND>" +
                                    "<NGAYSINH_NND>" + data[0].NGAYSINH_NND + "</NGAYSINH_NND>" +
                                    "<MA_DANTOC_NND>" + data[0].MA_DANTOC_NND + "</MA_DANTOC_NND>" +
                                    "<MA_QUOCTICH_NND>" + data[0].MA_QUOCTICH_NND + "</MA_QUOCTICH_NND>" +
                                    "<LOAI_GIAYTO_NND>" + data[0].LOAI_GIAYTO_NND + "</LOAI_GIAYTO_NND>" +
                                    "<SO_CCCD_NND>" + data[0].SO_CCCD_NND + "</SO_CCCD_NND>" +
                                    "<NGAYCAP_CCCD_NND>" + data[0].NGAYCAP_CCCD_NND + "</NGAYCAP_CCCD_NND>" +
                                    "<NOICAP_CCCD_NND>" + data[0].NOICAP_CCCD_NND + "</NOICAP_CCCD_NND>" +
                                    "<NOI_CU_TRU_NND>" + data[0].NOI_CU_TRU_NND + "</NOI_CU_TRU_NND>" +
                                    "<MATINH_CU_TRU>" + data[0].MATINH_CU_TRU + "</MATINH_CU_TRU>" +
                                    "<MAHUYEN_CU_TRU>" + data[0].MAHUYEN_CU_TRU + "</MAHUYEN_CU_TRU>" +
                                    "<MAXA_CU_TRU>" + data[0].MAXA_CU_TRU + "</MAXA_CU_TRU>" +
                                    "<HO_TEN_CHA>" + (data[0].HO_TEN_CHA == null ? "" : data[0].HO_TEN_CHA) + "</HO_TEN_CHA>" +
                                    "<MA_THE_TAM>" + dataSubmit.MA_THE_TAM + "</MA_THE_TAM>" +
                                    "<TEN_CON>" + data[0].TEN_CON + "</TEN_CON>\n" +
                                    "<GIOI_TINH_CON>" + data[0].GIOI_TINH_CON + "</GIOI_TINH_CON>" +
                                    "<SO_CON>" + data[0].SO_CON + "</SO_CON>" +
                                    "<LAN_SINH>" + data[0].LAN_SINH + "</LAN_SINH>" +
                                    "<SO_CON_SONG>" + data[0].SO_CON_SONG + "</SO_CON_SONG>" +
                                    "<CAN_NANG_CON>" + data[0].CAN_NANG_CON + "</CAN_NANG_CON>" +
                                    "<NGAY_SINH_CON>" + data[0].NGAY_SINH_CON + "</NGAY_SINH_CON>" +
                                    "<NOI_SINH_CON>" + data[0].NOI_SINH_CON + "</NOI_SINH_CON>" +
                                    "<TINH_TRANG_CON>" + data[0].TINH_TRANG_CON + "</TINH_TRANG_CON>" +
                                    "<SINHCON_PHAUTHUAT>" + data[0].SINHCON_PHAUTHUAT + "</SINHCON_PHAUTHUAT>" +
                                    "<SINHCON_DUOI32TUAN>" + data[0].SINHCON_DUOI32TUAN + "</SINHCON_DUOI32TUAN>" +
                                    "<GHI_CHU>" + (data[0].GHI_CHU == null ? "" : data[0].GHI_CHU) + "</GHI_CHU>" +
                                    "<NGUOI_DO_DE>"+(singletonObject.thamSo960599 == 0? dataSubmit.NGUOI_DO_DE: "")+"</NGUOI_DO_DE>" +
                                    "<NGUOI_GHI_PHIEU>" + data[0].NGUOI_GHI_PHIEU + "</NGUOI_GHI_PHIEU>" +
                                    "<MA_TTDV>" + data[0].MA_TTDV + "</MA_TTDV>" +
                                    "<THU_TRUONG_DVI></THU_TRUONG_DVI>" +
                                    "<NGAY_CT>" + data[0].NGAY_CT + "</NGAY_CT>" +
                                    "<SO>" + data[0].SO + "</SO>\n" +
                                    "<QUYEN_SO>" + data[0].QUYEN_SO + "</QUYEN_SO>" +
                                    "</GIAYCHUNGSINH>" +
                                    "</HSDLGCS>";
                                $.post("cmu_post_CMU_GIAY_CHUNG_SINH_INS", {
                                    url: [
                                        singletonObject.dvtt,
                                        thongtinhsba.thongtinbn.SOVAOVIEN,
                                        singletonObject.makhoa,
                                        dataSubmit.ID_GCS,
                                        moment(dataSubmit.NGAY_CT).format("DD/MM/YYYY"),
                                        singletonObject.userId,
                                        xmlData
                                    ].join("```")
                                }).done(function (res) {
                                    if (res == "-1") {
                                        return notifiToClient("Red", "Đã ký số")
                                    }
                                    if (res == "1") {
                                        notifiToClient("Green", "Gửi thành công")
                                        sendMessageTopicFirebase(singletonObject.makhoa+"_TRUONGKHOA", "Giấy chứng sinh", thongtinhsba.thongtinbn.TEN_BENH_NHAN)
                                    } else {
                                        notifiToClient("Red", "Gửi thất bại")
                                    }
                                }).fail(function () {
                                    notifiToClient("Red", "Gửi thất bại")
                                })
                                return false;

                            } else {
                                notifiToClient("Red", "Không tìm thấy dữ liệu");
                                hideSelfLoading(idButton)

                            }
                        }).fail(function () {
                        notifiToClient("Red", MESSAGEAJAX.ERROR)
                        hideSelfLoading(idButton)
                    })
                    return false;
                }


                var x = new XMLHttpRequest();
                x.onload = function() {
                    // Create a form
                    var reader = new FileReader();
                    reader.readAsDataURL(x.response);
                    reader.onloadend = function() {
                        var base64data = reader.result;
                        var fd = new FormData();
                        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, dataSubmit.ID_GCS,'CMU_XUAT_GIAYCS']))
                            .done(function(data){
                                if(data.length > 0) {
                                    data[0].MATINH_CU_TRU = dataSubmit.MATINH_CU_TRU
                                    data[0].MAHUYEN_CU_TRU = dataSubmit.MAHUYEN_CU_TRU
                                    data[0].MAXA_CU_TRU = dataSubmit.MAXA_CU_TRU
                                    data[0].MA_THE_TAM = ""

                                    data[0].FILE = base64data.replace("data:application/pdf;base64,", "");
                                    data[0].MAKHOA = singletonObject.makhoa;
                                    data[0].DVTT = singletonObject.dvtt;
                                    data[0].LOAI_GIAYTO_NND =  data[0].SO_CCCD_NND.length > 9? 1: 2;
                                    data[0].SOVAOVIEN = dataSubmit.ID_GCS;
                                    data[0].SOVAOVIEN_DT = thongtinhsba.thongtinbn.SOVAOVIEN_DT;
                                    data[0].MA_GCS = dataSubmit.MA_CT
                                    data[0].NGAYCHUNGTU = data[0].NGAY_CT;
                                    data[0].IDCHUNGSINH = dataSubmit.ID_GCS;
                                    data[0].NGUOIGUI = singletonObject.userId+"-"+singletonObject.user;
                                    $.ajax({
                                        type: "POST",
                                        url: "https://apikysohis.vnptcamau.vn/gui-giay-chung-sinh",
                                        contentType: 'application/json',
                                        dataType: "json",
                                        data: JSON.stringify(data[0]),
                                        success: function(res) {
                                            hideSelfLoading(idButton)
                                            notifiToClient(res.status == '000'?"Green": 'Red', res.message)
                                        },
                                        error: function() {
                                            hideSelfLoading(idButton)
                                            notifiToClient("Red", MESSAGEAJAX.ERROR)
                                        }
                                    })
                                } else {
                                    notifiToClient("Red", "Không tìm thấy dữ liệu");
                                    hideSelfLoading(idButton)

                                }
                            }).fail(function() {
                            notifiToClient("Red", MESSAGEAJAX.ERROR)
                            hideSelfLoading(idButton)
                        })
                    }
                }
                var str = [formChungsinhIO.submission.data.ID_GCS, 0, "0"];
                var url = "ingiaychungsinhVSC?url=" + convertArray(str);
                x.responseType = 'blob';
                x.open('GET', url, true);
                x.send();

            });
        })



    })

    $("#hmis_chuyenkhoa_giaychungsinh_xoa").click(function() {
        var idButton = this.id
        confirmToClient(MESSAGEAJAX.CONFIRM, function() {
            var rowData = getThongtinRowSelected("hmislistTreemChungsinh");
            var str = [rowData.ID_BIEN_DONG_SINH, '', '', "0"];
            showSelfLoading(idButton)
            $.ajax({
                url: "cmu_getlist?url="+convertArray([
                    singletonObject.dvtt,
                    thongtinhsba.thongtinbn.SOVAOVIEN,
                    thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                    _.get(formChungsinhIO, 'submission.data.ID_GCS', ''),
                    'cmu_smart769_getchungsinh']),
                method: "GET",
            }).done(function (data) {
                var rest = data[0]
                if(!rest['KEYMINIO']) {
                    if (rowData.ID_BIEN_DONG_SINH && rowData.ID_BIEN_DONG_SINH != '-1') {
                        $.post("qlnk_xoabdsinh", {url: convertArray(str)}).done(function (data) {
                            if (data == "-1") {
                                return notifiToClient("Red","Lỗi xóa biến động");
                            } else if (data == "-2") {
                                return notifiToClient("Red","Giấy chứng sinh đã được duyệt, không được xóa");
                            } else {
                                var strnk = [formChungsinhIO.submission.data.ID_TEN_CON, "0"];
                                $.post("xoanhankhau", {url: convertArray(strnk)})
                                loadGridDanhsachTreem();
                                $("#hmis_chuyenkhoa_giaychungsinh_backtolist").click();
                                notifiToClient("Green", MESSAGEAJAX.DEL_SUCCESS)
                            }
                        }).fail(function () {
                            notifiToClient("Red", MESSAGEAJAX.ERROR)
                        }).always(function () {
                            hideSelfLoading(idButton)
                        });
                    } else {
                        str = [rowData.ID_GCS, "0"];
                        $.post("xoagcs", {
                            url: convertArray(str)
                        }).done(function (data) {
                            if (data == "-1") {
                                return notifiToClient("Red","Lỗi xóa giấy chứng sinh");
                            }
                            if (data == "-2") {
                                return notifiToClient("Red","Giấy chứng sinh đã được duyệt, không được xóa");
                            }

                            if (formChungsinhIO.submission.data.ID_TEN_CON && formChungsinhIO.submission.data.ID_TEN_CON != 0) {
                                var strnk = [formChungsinhIO.submission.data.ID_TEN_CON, "0"];
                                url = "xoanhankhau";
                                $.post(url, {url: convertArray(strnk)})
                            }
                            loadGridDanhsachTreem();
                            $.post("cmu_post", {
                                url: [
                                    singletonObject.dvtt,

                                    thongtinhsba.thongtinbn.SOVAOVIEN,
                                    _.get(formChungsinhIO, 'submission.data.ID_GCS', ''),
                                    'CMU_GIAY_CHUNG_SINH_DEL'
                                ].join("```")
                            })
                            luuLogHSBATheoBN({
                                SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                LOAI: LOGHSBALOAI.GIAYCHUNGSINH.KEY,
                                NOIDUNGBANDAU: "Xóa giấy chứng sinh ID: "+ _.get(formChungsinhIO, 'submission.data.ID_GCS', ''),
                                NOIDUNGMOI: "",
                                USERID: singletonObject.userId,
                                ACTION: LOGHSBAACTION.INSERT.KEY
                            });
                            $("#hmis_chuyenkhoa_giaychungsinh_backtolist").click();
                            notifiToClient("Green", MESSAGEAJAX.DEL_SUCCESS)
                        }).fail(function () {
                            notifiToClient("Red", MESSAGEAJAX.ERROR)
                        }).always(function () {
                            hideSelfLoading(idButton)
                        });
                    }
                } else {
                    hideSelfLoading(idButton)
                    notifiToClient("Red", "Đã ký số, không thể xóa")
                }
            })

        })
    });
    $("#hmis_chuyenkhoa_giaychungsinh_huyguikyso").click(function() {
        var idButton = this.id
        confirmToClient(MESSAGEAJAX.CONFIRM, function() {
            showSelfLoading(idButton)
            $.ajax({
                url: "cmu_getlist?url="+convertArray([
                    singletonObject.dvtt,
                    thongtinhsba.thongtinbn.SOVAOVIEN,
                    thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                    _.get(formChungsinhIO, 'submission.data.ID_GCS', ''),
                    'cmu_smart769_getchungsinh']),
                method: "GET",
            }).done(function (data) {
                var rest = data[0]
                if(!rest['KEYMINIO']) {
                    $.post("cmu_post", {
                        url: [
                            singletonObject.dvtt,

                            thongtinhsba.thongtinbn.SOVAOVIEN,
                            _.get(formChungsinhIO, 'submission.data.ID_GCS', ''),
                            'CMU_GIAY_CHUNG_SINH_DEL'
                        ].join("```")
                    }).always(function() {

                        hideSelfLoading(idButton)
                    }).done(function() {
                        notifiToClient("Green", "Hủy thành công")
                        luuLogHSBATheoBN({
                            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                            LOAI: LOGHSBALOAI.GIAYCHUNGSINH.KEY,
                            NOIDUNGBANDAU: "Hủy gửi ký số giấy chứng sinh ID: "+ _.get(formChungsinhIO, 'submission.data.ID_GCS', ''),
                            NOIDUNGMOI: "",
                            USERID: singletonObject.userId,
                            ACTION: LOGHSBAACTION.INSERT.KEY
                        });

                    }).fail(function () {
                        notifiToClient("Red", MESSAGEAJAX.ERROR)
                    })
                } else {
                    hideSelfLoading(idButton)
                    notifiToClient("Red", "Đã ký số, không thể xóa")
                }
            })
        })
    })

    $("#hmis_chuyenkhoa_giaychungsinh_xoakyso").click(function() {
        if(singletonObject.admin != '1') {
            return notifiToClient("Red", MESSAGEAJAX.PERMISSION)
        }
        var idButton = this.id;
        var idGiayCS = _.get(formChungsinhIO, 'submission.data.ID_GCS', '');
        confirmToClient(MESSAGEAJAX.CONFIRM, function () {
            showSelfLoading(idButton);
            huykysoFilesign769("NOITRU_GIAYCHUNGSINH",
                idGiayCS,
                -1, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                    if(_.get(data, 'SUCCESS') == 1) {
                        luuLogHSBATheoBN({
                            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                            LOAI: LOGHSBALOAI.GIAYCHUNGSINH.KEY,
                            NOIDUNGBANDAU: "Hủy ký số giấy chứng sinh ID:" + idGiayCS,
                            NOIDUNGMOI: "",
                            USERID: singletonObject.userId,
                            ACTION: LOGHSBAACTION.EDIT.KEY,
                        })
                        $.post("cmu_post", {
                            url: [
                                singletonObject.dvtt,
                                thongtinhsba.thongtinbn.SOVAOVIEN,
                                idGiayCS,
                                'CMU_GIAY_CHUNG_SINH_DEL'
                            ].join("```")
                        })
                        hideSelfLoading(idButton)
                        checkkysogiaychungsinh();
                        getFilesign769("NOITRU_GIAYCHUNGSINH_BGD",
                            idGiayCS, -1,
                            singletonObject.dvtt,
                            thongtinhsba.thongtinbn.SOVAOVIEN,
                            thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                                if(data.length > 0) {
                                    $.post("smartca-capnhat-huykyso?keysign="+data[0].KEYSIGN).done(function() {

                                    })
                                }
                            })
                    }
                })

        })
    })

    $.extend({
        hmisGetFormSinhno: function() {
            return formSinhnoIO;
        }
    })

    function initGridLankhamSinhno() {
        var list = $("#hmislistBNSinhNo")
        if(!list[0].grid) {
            list.jqGrid({
                url: "",
                datatype: "local",
                loadonce: true,
                height: 300,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "ID", name: 'ID_SS_THONG_TIN', index: 'ID_SS_THONG_TIN', width: 100},
                    {label: "Mã y tế", name: 'MA_BENH_NHAN', index: 'MA_BENH_NHAN', width: 100},
                    {label: "Đơn vị", name: 'TEN_DONVI', index: 'TEN_DONVI', width : 5},
                    {label: "Ngày đẻ", name: 'NGAY_DE_HT', index: 'NGAY_DE_HT', width: 300, align: 'center',
                        formatter: function (cellvalue, options, rowObject) {
                            var color_text;

                            if (rowObject.SOVAOVIEN == thongtinhsba.thongtinbn.SOVAOVIEN) {
                                if(rowObject.DVTT == singletonObject.dvtt) color_text = '#8000ff';
                                else color_text = 'black';
                            } else {
                                color_text = 'black';
                            }
                            return '<span class="cellWithoutBackground" style="font-weight:bold ;color:' + color_text + '">' + cellvalue + '</span>';
                        }
                    },
                    {label: "SOVAOVIEN", name: 'SOVAOVIEN', index: 'SOVAOVIEN',hidden: true},
                    {label: "DVTT", name: 'DVTT', index: 'DVTT',hidden: true},
                    {label: "NGAY_DE", name: 'NGAY_DE', index: 'NGAY_DE',hidden: true}
                ],
                onSelectRow: function(id) {
                    if (id) {
                        // var field = $("#listBNSinhNo").jqGrid('getRowData', id);
                        // $("#hotenlskb_cksn").val($("#hoten").val());
                        // $("#mabenhnhanlskb_cksn").val(field.MA_BENH_NHAN);
                        // v_mabenhnhanme = field.MA_BENH_NHAN;
                        // $("#dtngayde").val(field.NGAY_DE);
                        // if(thongtinhsba.thongtinbn.IDNHANKHAU=='' || thongtinhsba.thongtinbn.IDNHANKHAU=='0'){
                        //     $("#hotenlskb_cksn").css('color', 'black');
                        // }else{
                        //     $("#hotenlskb_cksn").css('color', 'RED');
                        // }
                        // loadDuLieuSinhNo(field.ID_SS_THONG_TIN);
                        // ngayxnss_load = field.NGAY_DE;
                    }
                },
                rownumbers: true,
                autoencode: true,
                gridview: true,
                caption: "Danh sách ngày khám",
                grouping: true,
                groupingView: {
                    groupField: ['TEN_DONVI'],
                    groupColumnShow: [false],
                    groupText: ['<b>{0}</b>'],
                    groupCollapse: false
                },
                gridComplete: function () {
                    var rowIds = $(this).jqGrid('getDataIDs');
                    for (var i = 1; i <= rowIds.length; i++) {
                        var rowData = $(this).jqGrid('getRowData', i);
                        if (rowData.SOVAOVIEN == thongtinhsba.thongtinbn.SOVAOVIEN && rowData.DVTT == singletonObject.dvtt) {
                            $(this).jqGrid('setSelection',i);
                            return;
                        }
                    }
                },
                onRightClickRow: function(id) {
                    if (id) {

                        $.contextMenu({
                            selector: '#hmislistBNSinhNo tr',
                            reposition : false,
                            callback: function (key, options) {
                                if (key == "chitiet") {
                                    var rowData = getThongtinRowSelected("hmislistBNSinhNo");
                                    showOrHideByClass("HMISKhamchuyenkhoasinhnoWrap", "add", "edit")
                                    showOrHideByClass("HMISKhamchuyenkhoasinhnoFooterWrap", "edit", "add")
                                    generateFormSinhno({})
                                }
                            },
                            items: {
                                "chitiet": {name: '<p><i class="fa fa-list-ul text-primary" aria-hidden="true"></i> Xem chi tiết</p>'}
                            }
                        });

                    }
                },
            });
        }
    }
    function loadGridLankhamSinhno() {
        var url = 'dsttsinhno?ma_benh_nhan=' + thongtinhsba.thongtinbn.MA_BENH_NHAN + "&idnk="+ thongtinhsba.thongtinbn.IDNHANKHAU + "&svv="+thongtinhsba.thongtinbn.SOVAOVIEN + "&noitru=1";
        loadDataGridGroupBy($("#hmislistBNSinhNo"), url)
    }

    function initDataSinhno() {
        $.getJSON("loadcombosinhsan", function (result) {
            initDataSinhnoFlag = true;
            singletonObject.hmis = result;
        });
        $.get("cmu_list_DS_BIENLAI_GIAYCHUNGSINH_GET?url="+convertArray([singletonObject.dvtt])).done(function(data) {
          dsQuyenchungsinh = data.map(function (item) {
            return {
              "label": item.TEN_BIEN_LAI,
              "value": item.KY_HIEU
            }
          });
        })
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, 'CMU_GET_MATINH']))
        .done(function(data){
            dsTinh = data.map(function (item) {
                return  {
                    "label": item.TEN_TINH_THANH,
                    "value": item.MA_TINH_THANH
                }
            });
        })
    }

    function generateFormSinhno() {
        var dsKTThaiky = singletonObject.hmis.dm_so_lan_kham_thai_ss.map(function (item) {
            return {
                "label": item.TEN_SO_LAN_KHAM_THAI,
                "value": item.ID_SO_LAN_KHAM_THAI
            }
        })
        var dsTTLucsinh = singletonObject.hmis.dm_tinhtrang_lucsinh.map(function (item) {
            return {
                "label": item.TEN_TT_LUCSINH,
                "value": item.ID_TT_LUCSINH
            }
        })
        var dsTrinhdo = singletonObject.hmis.dm_trinhdo_nguoido_ss.map(function (item) {
            return {
                "label": item.TEN_TRINHDO_NGUOIDO,
                "value": item.ID_TRINHDO_NGUOIDO
            }
        })
        var dsCachthucde = singletonObject.hmis.dm_cach_thuc_de_ss.map(function (item) {
          return {
            "label": item.TEN_CACH_THUC_DE,
            "value": item.ID_CACH_THUC_DE
          }
        })
        var dsNoide = singletonObject.hmis.dm_noi_kham_ss.map(function (item) {
            return {
                "label": item.TEN_NOI_KHAM,
                "value": item.ID_NOI_KHAM
            }
        })
        var dsTuvongthainhi = singletonObject.hmis.dm_tuvong_thainhi_ss.map(function (item) {
            return {
                "label": item.TEN_TUVONG_THAINHI,
                "value": item.ID_TUVONG_THAINHI
            }
        })
        var jsonForm = getJSONObjectForm([
            {
                "collapsible": true,
                "key": "pthongtinme",
                "type": "panel",
                "label": "THÔNG TIN MẸ",
                "title": "THÔNG TIN MẸ",
                "collapsed": false,
                "input": false,
                "tableView": false,
                "customClass": "hsba-tabs-wrap",
                "components": [
                    {
                        label: "ppthongtinmecol1",
                        key: "ppthongtinmecol1",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Mã BMTE",
                                        "key": "MA_BAME",
                                        "customClass": "pr-2",
                                        "type": "textfield"
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Ngày giờ đẻ",
                                        "key": "NGAY_GIO_DE",
                                        "type": "datetime",
                                        format: "dd/MM/yyyy HH:mm",
                                        "customClass": "pr-2",
                                        enableTime: true,
                                        minDate: moment(thongtinhsba.thongtinbn.NGAYGIO_NHAPVIEN, ['DD/MM/YYYY HH:mm:ss']).subtract(30, 'days').toISOString(),
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "SL đẻ đủ tháng",
                                        "customClass": "pr-2",
                                        "key": "SL_DE_DU_THANG",
                                        "type": "number",
                                        validate: {
                                            required: true,
                                            min: 0
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "SL đẻ non",
                                        "customClass": "pr-2",
                                        "key": "SL_DE_NON",
                                        "type": "number",
                                        validate: {
                                            required: true,
                                            min: 0,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinmecol2",
                        key: "ppthongtinmecol2",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "SL sảy phá thai",
                                        "customClass": "pr-2",
                                        "key": "SL_SAY_PHA_THAI",
                                        "type": "number",
                                        validate: {
                                            required: true,
                                            min: 0,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Số con hiện có",
                                        "customClass": "pr-2",
                                        "key": "SL_CON",
                                        "type": "number",
                                        validate: {
                                            required: true,
                                            min: 0,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Số lần có thai",
                                        "customClass": "pr-2",
                                        "key": "SL_COTHAI",
                                        "type": "number",
                                        validate: {
                                            required: true,
                                            min: 0,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "TS lần khám thai",
                                        "customClass": "pr-2",
                                        "key": "LAN_KHAM_THAI",
                                        "type": "number",
                                        validate: {
                                            min: 0,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinmecol3",
                        key: "ppthongtinmecol3",
                        columns: [
                            {
                                "components": [ {
                                    "label": "Số lần KT theo kỳ",
                                    others: {
                                        "data": {
                                            "values": dsKTThaiky
                                        },
                                    },
                                    "customClass": "pr-2",
                                    "key": "ID_SO_LAN_KT",
                                    "type": "select",
                                    validate: {
                                        required: true
                                    }
                                }],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Tuần thai",
                                        "customClass": "pr-2",
                                        "key": "TUAN_THAI",
                                        "type": "number",
                                        validate: {
                                            required: true,
                                            min: 0,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Tình trạng LS",
                                        "customClass": "pr-2",
                                        "key": "ID_TT_LUCSINH",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": dsTTLucsinh
                                            },
                                            defaultValue: 1
                                        },
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Người đỡ đẻ",
                                        "customClass": "pr-2",
                                        "key": "MA_NGUOI_DO",
                                        "type": "select",
                                        validate: {
                                            required: true,
                                        },
                                        others: {
                                            data: {
                                                values: singletonObject.danhsachtatcanhanvien
                                            }
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinmecol4",
                        key: "ppthongtinmecol4",
                        columns: [
                            {
                                "width": 3,
                                "size": "md",
                                "components": [
                                    {
                                        "label": "Trình độ",
                                        others: {
                                            "data": {
                                                "values": dsTrinhdo
                                            },
                                            defaultValue: 2
                                        },
                                        "customClass": "pr-2",
                                        "key": "ID_TRINHDO_NGUOIDO",
                                        "type": "select",
                                        validate: {
                                            required: true
                                        }

                                    }
                                ]

                            },
                            {
                                "width": 3,
                                "size": "md",
                                "components": [{
                                    "label": "Cách thức đẻ",
                                    others: {
                                        "data": {
                                            "values": dsCachthucde
                                        },
                                    },
                                    "customClass": "pr-2",
                                    "key": "ID_CACH_THUC_DE",
                                    "type": "select",
                                    validate: {
                                        required: true
                                    }
                                }]

                            },
                            {
                                "width": 3,
                                "size": "md",
                                "components": [{
                                    "label": "Nơi đẻ",
                                    others: {
                                        "data": {
                                            "values": dsNoide
                                        },
                                    },
                                    "customClass": "pr-2",
                                    "key": "ID_NOI_DE",
                                    "type": "select",
                                    validate: {
                                        required: true
                                    }
                                }]

                            },
                            {
                                "components": [
                                    {
                                        "label": "Số bé sinh ra",
                                        "customClass": "pr-2",
                                        "key": "SO_TRE",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "1",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "2",
                                                        "value": 2
                                                    },
                                                    {
                                                        "label": "3",
                                                        "value": 3
                                                    },
                                                ]
                                            },
                                        },
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinmecol5",
                        key: "ppthongtinmecol5",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Tình trạng mẹ",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Sống",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Chết",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        },
                                        "customClass": "pr-2",
                                        "key": "TINH_TRANG_ME",
                                        "type": "select",
                                    }
                                ],
                                "width": 3,
                                "size": "md"
                            },
                            {
                                "width": 3,
                                "size": "md",
                                "components": [
                                    {
                                        "label": "Chi tiết tình trạng",
                                        "customClass": "pr-2",
                                        "key": "CHI_TIET_TINH_TRANG",
                                        "type": "textfield",
                                    }
                                ]
                            },
                            {
                                "width": 3,
                                "size": "md",
                                "components": [{
                                    "label": "Can thiệp FX/GH",
                                    "customClass": "pr-2",
                                    "key": "CAN_THIEP_FX_GH",
                                    "type": "select",
                                    others: {
                                        "data": {
                                            "values": [
                                                {
                                                    "label": "Có",
                                                    "value": 1
                                                },
                                                {
                                                    "label": "Không",
                                                    "value": 0
                                                }
                                            ]
                                        },
                                    },
                                }]

                            },
                            {
                                "components": [
                                    {
                                        "label": "Được điều tra",
                                        "customClass": "pr-2",
                                        "key": "DUOC_DIEU_TRA",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        },
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinmecol6",
                        key: "ppthongtinmecol6",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Vấn đề khác",
                                        "customClass": "pr-2",
                                        "key": "VANDEKHAC",
                                        "type": "textfield",
                                    }
                                ],
                                "width": 3,
                                "size": "md"
                            },
                            {
                                "width": 3,
                                "size": "md",
                                "components": [
                                    {
                                        "label": "Năm tiền thai",
                                        "customClass": "pr-2",
                                        "key": "TIEN_THAI",
                                        "type": "number",
                                        validate: {
                                            min: 0
                                        }
                                    }
                                ]
                            },
                            {
                                "width": 3,
                                "size": "md",
                                "components": [{
                                    "label": "Quản lý thai",
                                    "customClass": "pr-2",
                                    "key": "QUAN_LY_THAI",
                                    "type": "select",
                                    others: {
                                        "data": {
                                            "values": [
                                                {
                                                    "label": "Có",
                                                    "value": 1
                                                },
                                                {
                                                    "label": "Không",
                                                    "value": 0
                                                }
                                            ]
                                        },
                                    },
                                }]

                            },
                            {
                                "components": [
                                    {
                                        "label": "Thai nguy cơ cao",
                                        "customClass": "pr-2",
                                        "key": "THAI_NGUY_CO",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                            defaultValue: 0,
                                        },
                                        validate: {
                                            required: true
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinmecol7",
                        key: "ppthongtinmecol7",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Bị ngạt",
                                        "customClass": "pr-2",
                                        "key": "BI_NGAT",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        },
                                    }
                                ],
                                "width": 3,
                                "size": "md"
                            },
                            {
                                "width": 3,
                                "size": "md",
                                "components": [
                                    {
                                        "label": "Phạm vi đẻ",
                                        "customClass": "pr-2",
                                        "key": "PHAMVI_DE",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Ngoài tỉnh",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Trong tỉnh",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        },
                                    }
                                ]
                            },
                            {
                                "width": 3,
                                "size": "md",
                                "components": [{
                                    "label": "Chuẩn đoán trước sinh",
                                    "customClass": "pr-2",
                                    "key": "CHUAN_DOAN",
                                    "type": "textfield",

                                }]

                            },
                            {
                                "components": [
                                    {
                                        "label": "Tầng sinh môn",
                                        "customClass": "pr-2",
                                        "key": "TANGSINHMON",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "N (Nguyên)",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "C (Cắt)",
                                                        "value": 2
                                                    },
                                                    {
                                                        "label": "RI (Rách độ I)",
                                                        "value": 3
                                                    },
                                                    {
                                                        "label": "RII (Rách độ II)",
                                                        "value": 4
                                                    },
                                                    {
                                                        "label": "RIII (Rách độ III)",
                                                        "value": 5
                                                    },
                                                    {
                                                        "label": "RIV (Rách độ IV)",
                                                        "value": 6
                                                    }
                                                ]
                                            },
                                        },
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinmecol8",
                        key: "ppthongtinmecol8",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Robson",
                                        "customClass": "pr-2",
                                        "key": "ROBSON",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "RI (Nhóm I)",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "RII (Nhóm II)",
                                                        "value": 2
                                                    },
                                                    {
                                                        "label": "RIII (Nhóm III)",
                                                        "value": 3
                                                    },
                                                    {
                                                        "label": "RIV (Nhóm IV)",
                                                        "value": 4
                                                    },
                                                    {
                                                        "label": "RV (Nhóm V)",
                                                        "value": 5
                                                    },
                                                    {
                                                        "label": "RVI (Nhóm VI)",
                                                        "value": 6
                                                    },
                                                    {
                                                        "label": "RVII (Nhóm VII)",
                                                        "value": 7
                                                    },
                                                    {
                                                        "label": "RVIII (Nhóm VIII)",
                                                        "value": 8
                                                    },
                                                    {
                                                        "label": "RIX (Nhóm IX)",
                                                        "value": 9
                                                    },
                                                    {
                                                        "label": "RX (Nhóm X)",
                                                        "value": 10
                                                    }
                                                ]
                                            },
                                        },
                                    }
                                ],
                                "width": 3,
                                "size": "md"
                            },
                            {
                                "width": 3,
                                "size": "md",
                                "components": [
                                    {
                                        "label": "Sàng lọc sơ sinh",
                                        "customClass": "pr-2",
                                        "key": "SANG_LOC_SS",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        },
                                    }
                                ]
                            },
                            {
                                "width": 3,
                                "size": "md",
                                "components": [{
                                    "label": "Tiêm vitamin K1",
                                    "customClass": "pr-2",
                                    "key": "TIEM_VITAMINK",
                                    "type": "select",
                                    others: {
                                        "data": {
                                            "values": [
                                                {
                                                    "label": "Có",
                                                    "value": 1
                                                },
                                                {
                                                    "label": "Không",
                                                    "value": 0
                                                }
                                            ]
                                        },
                                    },
                                    validate: {
                                        required: true
                                    }

                                }]

                            },
                            {
                                "components": [
                                    {
                                        "label": "Tiêm VX viêm gan B",
                                        "customClass": "pr-2",
                                        "key": "TIEM_VIEMGANB",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    },
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Tiêm 24 giờ đầu",
                                                        "value": 2
                                                    },

                                                ]
                                            },
                                        },
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinmecol9",
                        key: "ppthongtinmecol9",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Chăm sóc KMC",
                                        "customClass": "pr-2",
                                        "key": "KMC",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    },
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },
                                                ]
                                            },
                                        }
                                    }
                                ],
                                "width": 3,
                                "size": "md"
                            },
                            {
                                "width": 3,
                                "size": "md",
                                "components": [
                                    {
                                        "label": "Điều trị DP HIV mẹ",
                                        "customClass": "pr-2",
                                        "key": "DT_DUPHONG_LTHIV_ME",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        },
                                    }
                                ]
                            },
                            {
                                "width": 3,
                                "size": "md",
                                "components": [{
                                    "label": "Điều trị DP HIV trẻ",
                                    "customClass": "pr-2",
                                    "key": "DTDP_LTHIV_TRE",
                                    "type": "select",
                                    others: {
                                        "data": {
                                            "values": [
                                                {
                                                    "label": "Có",
                                                    "value": 1
                                                },
                                                {
                                                    "label": "Không",
                                                    "value": 0
                                                }
                                            ]
                                        },
                                    },

                                }]

                            },
                            {
                                "components": [
                                    {
                                        "label": "Được CSSS thiết yếu sớm",
                                        "customClass": "pr-2",
                                        "key": "CSSS_THIETYEUSAUSINH",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    },
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },

                                                ]
                                            },
                                        },
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinmecol10",
                        key: "ppthongtinmecol10",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Kết quả EENC",
                                        "customClass": "pr-2",
                                        "key": "EENC",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Thất bại",
                                                        "value": 0
                                                    },
                                                    {
                                                        "label": "Thành công",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Không thực hiện",
                                                        "value": 2
                                                    }
                                                ]
                                            },
                                        }
                                    }
                                ],
                                "width": 3,
                                "size": "md"
                            },
                            {
                                "width": 3,
                                "size": "md",
                                "components": [
                                    {
                                        "label": "Số liều VXUV",
                                        "customClass": "pr-2",
                                        "key": "VXUV_ME_TIEM",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Có phiếu",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Không phiếu",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        },
                                    }
                                ]
                            },
                            {
                                "width": 3,
                                "size": "md",
                                "components": [{
                                    "label": "Tiêm UVĐĐ",
                                    "customClass": "pr-2",
                                    "key": "TIEM_UVDD",
                                    "type": "select",
                                    others: {
                                        "data": {
                                            "values": [
                                                {
                                                    "label": "Có",
                                                    "value": 1
                                                },
                                                {
                                                    "label": "Không",
                                                    "value": 0
                                                }
                                            ]
                                        },
                                    },

                                }]

                            },
                            {
                                "components": [
                                    {
                                        "label": "Bú mẹ giờ đầu",
                                        "customClass": "pr-2",
                                        "key": "BU_ME_GIO_DAU",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    },
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },

                                                ]
                                            },
                                        },
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinmecol11",
                        key: "ppthongtinmecol11",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Điều trị ARV",
                                        "customClass": "pr-2",
                                        "key": "HIV_ARV",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    },
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },
                                                ]
                                            },
                                        }
                                    }
                                ],
                                "width": 3,
                                "size": "md"
                            },
                            {
                                "width": 9,
                                "size": "md",
                                "components": [
                                    {
                                        "label": "Ghi chú",
                                        "customClass": "pr-2",
                                        "key": "GHI_CHU",
                                        "type": "textfield",
                                    }
                                ]
                            },
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    }
                ]
            },
            {
                "collapsible": true,
                "key": "pthongtincon",
                "type": "panel",
                "label": "THÔNG TIN CON",
                "title": "THÔNG TIN CON",
                "collapsed": false,
                "input": false,
                "tableView": false,
                "customClass": "hsba-tabs-wrap",
                "components": [
                    {
                        label: "ppthongtinconcol1",
                        key: "ppthongtinconcol1",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Họ tên trẻ",
                                        "key": "HO_TEN_TRE",
                                        "customClass": "pr-2",
                                        validate: {
                                            required: true,
                                        },
                                        "type": "textfield",
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Giới tính",
                                        "key": "GIOI_TINH",
                                        "customClass": "pr-2",
                                        validate: {
                                            required: true,
                                        },
                                        options: [
                                            {
                                                "label": "Nữ",
                                                "value": "0"
                                            },
                                            {
                                                "label": "Nam",
                                                "value": "1"
                                            },
                                            {
                                                "label": "Khác",
                                                "value": "2"
                                            }
                                        ],
                                        "type": "select",
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Cân nặng (gram)",
                                        "customClass": "pr-2",
                                        "key": "CAN_NANG",
                                        "type": "number",
                                        validate: {
                                            required: true,
                                            min: 0
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Tử vong thai nhi",
                                        "customClass": "pr-2",
                                        "key": "ID_TUVONG_THAINHI",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": dsTuvongthainhi
                                            },
                                        },
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinconcol2",
                        key: "ppthongtinconcol2",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Tình trạng trẻ",
                                        "key": "TINH_TRANG_TRE",
                                        "customClass": "pr-2",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Sống",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Chết",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Ngày TV",
                                        "key": "NGAY_TV",
                                        "type": "datetime",
                                        format: "dd/MM/yyyy",
                                        enableTime: false,
                                        customClass: "pr-2",
                                        minDate: moment(thongtinhsba.thongtinbn.NGAY_VAO_VIEN, ['DD/MM/YYYY']).format("YYYY-MM-DD"),
                                        maxDate: moment().format("YYYY-MM-DD"),
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Chiều dài lúc đẻ",
                                        "customClass": "pr-2",
                                        "key": "CHIEUDAI_LUCDE",
                                        "type": "number",
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Mắc UVSS",
                                        "customClass": "pr-2",
                                        "key": "UVSS",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        },
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinconcol3",
                        key: "ppthongtinconcol3",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Dị tật bẩm sinh",
                                        "key": "ID_DITATBAMSINH",
                                        "type": "select",
                                        "customClass": "pr-2",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Nội dung",
                                        "key": "DITAT_BAMSINH",
                                        "type": "textfield",
                                        "customClass": "pr-2",
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Chuẩn đoán sơ sinh",
                                        "customClass": "pr-2",
                                        "key": "CHUANDOANSOSINH_TRE1",
                                        "type": "textfield",
                                    },
                                ],
                                "width": 2,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Điều trị",
                                        "customClass": "pr-2",
                                        "key": "DIEUTRI_TRE1",
                                        "type": "textfield",

                                    },
                                ],
                                "width": 2,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Can thiệp",
                                        "key": "CANTHIEP_TRE1",
                                        "customClass": "pr-2",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        }
                                    },
                                ],
                                "width": 2,
                                "size": "md",
                            },
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },

                ]
            },
            {
                "collapsible": true,
                "key": "pthongtincon2",
                "type": "panel",
                "label": "THÔNG TIN CON",
                "title": "THÔNG TIN CON",
                "collapsed": false,
                "input": false,
                "tableView": false,
                "customClass": "hsba-tabs-wrap",
                "components": [
                    {
                        label: "ppthongtinconcol21",
                        key: "ppthongtinconcol21",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Họ tên trẻ",
                                        "key": "HO_TEN_TRE2",
                                        "customClass": "pr-2",
                                        "type": "textfield",
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Giới tính",
                                        "key": "GIOI_TINH2",
                                        "customClass": "pr-2",
                                        validate: {
                                            required: true,
                                        },
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Nữ",
                                                        "value": 0
                                                    },
                                                    {
                                                        "label": "Nam",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Khác",
                                                        "value": 2
                                                    }
                                                ]
                                            },
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Cân nặng (gram)",
                                        "customClass": "pr-2",
                                        "key": "CAN_NANG2",
                                        "type": "number",
                                        validate: {
                                            required: true,
                                            min: 0
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Tử vong thai nhi",
                                        "customClass": "pr-2",
                                        "key": "ID_TUVONG_THAINHI2",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": dsTuvongthainhi
                                            },
                                        },
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinconcol22",
                        key: "ppthongtinconcol22",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Tình trạng trẻ",
                                        "key": "TINH_TRANG_TRE2",
                                        "customClass": "pr-2",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Sống",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Chết",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Ngày TV",
                                        "key": "NGAY_TV2",
                                        "type": "datetime",
                                        format: "dd/MM/yyyy",
                                        enableTime: false,
                                        customClass: "pr-2",
                                        minDate: moment(thongtinhsba.thongtinbn.NGAY_VAO_VIEN, ['DD/MM/YYYY']).format("YYYY-MM-DD"),
                                        maxDate: moment().format("YYYY-MM-DD"),
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Chiều dài lúc đẻ",
                                        "customClass": "pr-2",
                                        "key": "CHIEUDAI_LUCDE2",
                                        "type": "number",
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Mắc UVSS",
                                        "customClass": "pr-2",
                                        "key": "UVSS2",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        },
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinconcol23",
                        key: "ppthongtinconcol23",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Dị tật bẩm sinh",
                                        "key": "ID_DITATBAMSINH2",
                                        "type": "select",
                                        "customClass": "pr-2",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Nội dung",
                                        "key": "DITAT_BAMSINH2",
                                        "type": "textfield",
                                        "customClass": "pr-2",
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Chuẩn đoán sơ sinh",
                                        "customClass": "pr-2",
                                        "key": "CHUANDOANSOSINH_TRE2",
                                        "type": "textfield",
                                    },
                                ],
                                "width": 2,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Điều trị",
                                        "customClass": "pr-2",
                                        "key": "DIEUTRI_TRE2",
                                        "type": "textfield",

                                    },
                                ],
                                "width": 2,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Can thiệp",
                                        "key": "CANTHIEP_TRE2",
                                        "customClass": "pr-2",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        }
                                    },
                                ],
                                "width": 2,
                                "size": "md",
                            },
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },

                ],
                others: {
                    "customConditional": "show = data.SO_TRE >1;",
                }
            },
            {
                "collapsible": true,
                "key": "pthongtincon3",
                "type": "panel",
                "label": "THÔNG TIN CON",
                "title": "THÔNG TIN CON",
                "collapsed": false,
                "input": false,
                "tableView": false,
                "customClass": "hsba-tabs-wrap",
                "components": [
                    {
                        label: "ppthongtinconcol31",
                        key: "ppthongtinconcol31",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Họ tên trẻ",
                                        "key": "HO_TEN_TRE3",
                                        "customClass": "pr-2",
                                        "type": "textfield",
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Giới tính",
                                        "key": "GIOI_TINH3",
                                        "customClass": "pr-2",
                                        validate: {
                                            required: true,
                                        },
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Nữ",
                                                        "value": 0
                                                    },
                                                    {
                                                        "label": "Nam",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Khác",
                                                        "value": 2
                                                    }
                                                ]
                                            },
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Cân nặng (gram)",
                                        "customClass": "pr-2",
                                        "key": "CAN_NANG3",
                                        "type": "number",
                                        validate: {
                                            required: true,
                                            min: 0
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Tử vong thai nhi",
                                        "customClass": "pr-2",
                                        "key": "ID_TUVONG_THAINHI3",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": dsTuvongthainhi
                                            },
                                        },
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinconcol32",
                        key: "ppthongtinconcol32",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Tình trạng trẻ",
                                        "key": "TINH_TRANG_TRE3",
                                        "customClass": "pr-2",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Sống",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Chết",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Ngày TV",
                                        "key": "NGAY_TV3",
                                        "type": "datetime",
                                        format: "dd/MM/yyyy",
                                        enableTime: false,
                                        customClass: "pr-2",
                                        minDate: moment(thongtinhsba.thongtinbn.NGAY_VAO_VIEN, ['DD/MM/YYYY']).format("YYYY-MM-DD"),
                                        maxDate: moment().format("YYYY-MM-DD"),
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Chiều dài lúc đẻ",
                                        "customClass": "pr-2",
                                        "key": "CHIEUDAI_LUCDE3",
                                        "type": "number",
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Mắc UVSS",
                                        "customClass": "pr-2",
                                        "key": "UVSS3",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        },
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinconcol33",
                        key: "ppthongtinconcol33",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Dị tật bẩm sinh",
                                        "key": "ID_DITATBAMSINH3",
                                        "type": "select",
                                        "customClass": "pr-2",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Nội dung",
                                        "key": "DITAT_BAMSINH3",
                                        "type": "textfield",
                                        "customClass": "pr-2",
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Chuẩn đoán sơ sinh",
                                        "customClass": "pr-2",
                                        "key": "CHUANDOANSOSINH_TRE3",
                                        "type": "textfield",
                                    },
                                ],
                                "width": 2,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Điều trị",
                                        "customClass": "pr-2",
                                        "key": "DIEUTRI_TRE3",
                                        "type": "textfield",

                                    },
                                ],
                                "width": 2,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Can thiệp",
                                        "key": "CANTHIEP_TRE3",
                                        "customClass": "pr-2",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        }
                                    },
                                ],
                                "width": 2,
                                "size": "md",
                            },
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },

                ],
                others: {
                    "customConditional": "show = data.SO_TRE > 2;",
                }
            },
        ])
        Formio.createForm(document.getElementById('hmisBNSinhNoForm'),
            jsonForm,
            {},
        ).then(function(form) {
            formSinhnoIO = form;
            var rowData = getThongtinRowSelected("hmislistBNSinhNo")
            $("#HMISKhamchuyenkhoasinhnoFooterWrap .existData").hide()
            if(rowData.ID_SS_THONG_TIN) {
                $.get("chitietsinhnoID?idSinhNo="+rowData.ID_SS_THONG_TIN+"&mabenhnhan="+thongtinhsba.thongtinbn.MA_BENH_NHAN+"&idnhankhau="+thongtinhsba.thongtinbn.IDNHANKHAU)
                    .done(function(data) {
                        var NGAY_GIO_DE = moment(data[0].NGAY_DE + " " + data[0].GIO_DE + ":" + data[0].PHUT_DE, ["DD/MM/YYYY HH:mm"]).toISOString()
                        console.log("NGAY_GIO_DE", NGAY_GIO_DE)
                        formSinhnoIO.submission = {
                            data: {
                                ...data[0],
                                NGAY_GIO_DE: NGAY_GIO_DE
                            },

                        }
                    })
                $("#HMISKhamchuyenkhoasinhnoFooterWrap .existData").show()
            } else {
                formSinhnoIO.submission = {
                    data: {}
                }
            }

        });
    }

    function generateFormGiayChungsinh(data) {
        var jsonForm = getJSONObjectForm([
            {
                "collapsible": true,
                "key": "pthongtinme",
                "type": "panel",
                "label": "THÔNG TIN CHI TẾT BỐ/MẸ/NND",
                "title": "THÔNG TIN CHI TẾT BỐ/MẸ/NND",
                "collapsed": false,
                "input": false,
                "tableView": false,
                "customClass": "hsba-tabs-wrap",
                "components": [
                    {
                        label: "ppthongtinmecol1",
                        key: "ppthongtinmecol1",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Mã bệnh nhân",
                                        "customClass": "pr-2",
                                        "key": "MA_BENH_NHAN",
                                        "type": "textfield",
                                        others: {
                                            disabled: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Số thẻ BHYT",
                                        "key": "BHYT",
                                        "type": "textfield",
                                        "customClass": "pr-2",
                                        others: {
                                            disabled: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Địa chỉ",
                                        "key": "DIA_CHI",
                                        "type": "textfield",
                                        "customClass": "pr-2",
                                        others: {
                                            disabled: true,
                                        }
                                    },
                                ],
                                "width": 6,
                                "size": "md",
                            }

                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinmecol1",
                        key: "ppthongtinmecol1",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "CMND/CCCD",
                                        "key": "CMND",
                                        "customClass": "pr-2",
                                        "type": "textfield",
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Ngày cấp",
                                        "key": "NGAY_CAP",
                                        "type": "datetime",
                                        "customClass": "pr-2",
                                        format: "dd/MM/yyyy",
                                        enableTime: false,
                                        maxDate: moment().format("YYYY-MM-DD"),
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Nơi cấp",
                                        "customClass": "pr-2",
                                        "key": "NOI_CAP",
                                        "type": "textfield",
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Địa chỉ mẹ/NND tự khai",
                                        "customClass": "pr-2",
                                        "key": "DIACHINND_TUKHAI",
                                        "type": "textfield",
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinmecol2",
                        key: "ppthongtinmecol2",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Họ tên bố",
                                        "key": "HO_TEN_BO",
                                        "customClass": "pr-2",
                                        type: "textfield",
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Địa chỉ  (bố)",
                                        "key": "DIACHI_BO",
                                        "customClass": "pr-2",
                                        type: "textfield",
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "CMND/CCCD  (bố)",
                                        "customClass": "pr-2",
                                        "key": "SO_CMND_BO",
                                        "type": "textfield",
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Ngày cấp (bố)",
                                        "key": "NGAYCAP_CMND_BO",
                                        "type": "datetime",
                                        "customClass": "pr-2",
                                        format: "dd/MM/yyyy",
                                        enableTime: false,
                                        maxDate: moment().format("YYYY-MM-DD"),
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinmecol3",
                        key: "ppthongtinmecol3",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Nơi cấp CMND/CCCD (bố)",
                                        "key": "NOICAP_CMND_BO",
                                        "customClass": "pr-2",
                                        type: "textfield",
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Mã tỉnh NND",
                                        "key": "MATINH_CU_TRU",
                                        "customClass": "pr-2",
                                        type: "select",
                                        others: {
                                            "data": {
                                                "values": dsTinh
                                            },
                                        },
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Mã huyện NND",
                                        "customClass": "pr-2",
                                        "key": "MAHUYEN_CU_TRU",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": []
                                            },
                                        },
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Mã xã NND",
                                        "customClass": "pr-2",
                                        "key": "MAXA_CU_TRU",
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": []
                                            },
                                        },
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    }
                ]
            },
            {
                "collapsible": true,
                "key": "pthongtincon",
                "type": "panel",
                "label": "THÔNG TIN CON",
                "title": "THÔNG TIN CON",
                "collapsed": false,
                "input": false,
                "tableView": false,
                "customClass": "hsba-tabs-wrap",
                "components": [
                    {
                        label: "ppthongtinconcol",
                        key: "ppthongtinconcol",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Tên con",
                                        "key": "HO_TEN_TRE",
                                        "customClass": "pr-2",
                                        "type": "textfield",
                                        others: {
                                            disabled: true
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Cân nặng (gram)",
                                        "key": "CAN_NANG",
                                        "customClass": "pr-2",
                                        "type": "textfield",
                                        others: {
                                            disabled: true
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Giới tính",
                                        "customClass": "pr-2",
                                        "key": "TEN_GIOITINH",
                                        "type": "textfield",
                                        others: {
                                            disabled: true
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Người đỡ đẻ",
                                        "customClass": "pr-2",
                                        "key": "NGUOI_DO_DE",
                                        "type": "textfield",
                                        others: {
                                            disabled: singletonObject.thamSo960599 == 1,
                                            required: singletonObject.thamSo960599 == 0,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinconcol1",
                        key: "ppthongtinconcol1",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Ngày cấp",
                                        "key": "NGAY_CAP_GCS",
                                        "type": "datetime",
                                        "customClass": "pr-2",
                                        format: "dd/MM/yyyy",
                                        enableTime: false,
                                        // maxDate: moment().format("YYYY-MM-DD"),
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Quyển số",
                                        "key": "SO_QUYEN",
                                        "customClass": "pr-2",
                                        validate: {
                                            required: true,
                                        },
                                        "type": "select",
                                        others: {
                                            "data": {
                                                "values": dsQuyenchungsinh
                                            },
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Số",
                                        "customClass": "pr-2",
                                        "key": "SO",
                                        "type": "textfield",
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Hiện trạng sức khỏe",
                                        "customClass": "pr-2",
                                        "key": "TT_HIEN_TAI",
                                        "type": "textfield",
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinconcol2",
                        key: "ppthongtinconcol2",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Ngày giờ sinh",
                                        "key": "NGAY_GIO_SINH",
                                        "customClass": "pr-2",
                                        "type": "datetime",
                                        format: "dd/MM/yyyy HH:mm",
                                        enableTime: true,
                                        // minDate: moment(formSinhnoIO.submission.data.NGAY_GIO_DE).format("YYYY-MM-DD"),
                                        // maxDate: moment(formSinhnoIO.submission.data.NGAY_GIO_DE).format("YYYY-MM-DD"),
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Nơi sinh",
                                        "key": "SINH_TAI",
                                        "customClass": "pr-2",
                                        "type": "textfield",
                                        validate: {
                                            required: true,
                                        },
                                        others: {
                                            defaultValue: singletonObject.tenbenhvien,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Tình trạng",
                                        "customClass": "pr-2",
                                        "key": "TINH_TRANG",
                                        "type": "select",
                                        validate: {
                                            required: true,
                                        },
                                        others: {
                                            "data": {
                                                "values": [
                                                    {
                                                        "label": "Đã duyệt",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Chưa duyệt",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Số lần sinh",
                                        "customClass": "pr-2",
                                        "key": "SO_LAN_SINH",
                                        "type": "number",
                                        validate: {
                                            min: 1,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            }
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinconcol3",
                        key: "ppthongtinconcol3",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Số con sinh",
                                        "key": "SO_CON_SINH",
                                        "customClass": "pr-2",
                                        "type": "number",
                                        validate: {
                                            min: 1,
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Số con hiện sống",
                                        "key": "SO_CON_SONG",
                                        "type": "number",
                                        "customClass": "pr-2",
                                        validate: {
                                            min: 0,
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Mã chứng từ",
                                        "customClass": "pr-2",
                                        "key": "MA_CT",
                                        "type": "textfield",
                                        others: {
                                            disabled: true
                                        }
                                    },
                                ],
                                "width": 2,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Số seri",
                                        "customClass": "pr-2",
                                        "key": "SO_SERI",
                                        "type": "textfield",

                                    },
                                ],
                                "width": 2,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Ngày chứng từ",
                                        "key": "NGAY_CT",
                                        "customClass": "pr-2",
                                        "type": "datetime",
                                        format: "dd/MM/yyyy",
                                        enableTime: false,
                                        // maxDate: moment().format("YYYY-MM-DD"),
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 2,
                                "size": "md",
                            },
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },
                    {
                        label: "ppthongtinconcol4",
                        key: "ppthongtinconcol4",
                        columns: [
                            {
                                "components": [
                                    {
                                        "label": "Sinh con phẩu thuật",
                                        "key": "SINHCON_PHAUTHUAT",
                                        "customClass": "pr-2",
                                        "type": "select",
                                        others: {
                                            data: {
                                                values: [
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                            defaultValue: 0
                                        },
                                        validate: {
                                            required: true
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Sinh con dưới 32 tuần",
                                        "key": "SINHCON_DUOI32TUAN",
                                        "type": "select",
                                        "customClass": "pr-2",
                                        others: {
                                            data: {
                                                values: [
                                                    {
                                                        "label": "Có",
                                                        "value": 1
                                                    },
                                                    {
                                                        "label": "Không",
                                                        "value": 0
                                                    }
                                                ]
                                            },
                                            defaultValue: 0
                                        },
                                        validate: {
                                            required: true
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Người ghi phiếu",
                                        "customClass": "pr-2",
                                        "key": "NGUOI_GHI_PHIEU",
                                        "type": "textfield",
                                        validate: {
                                            required: true,
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                            {
                                "components": [
                                    {
                                        "label": "Thẻ tạm",
                                        "customClass": "pr-2",
                                        "key": "MA_THE_TAM",
                                        "type": "textfield",
                                        validate: {
                                            required: true,
                                            minLength: 15,
                                            maxLength: 15
                                        }
                                    },
                                ],
                                "width": 3,
                                "size": "md",
                            },
                        ],
                        "customClass": "ml-0 mr-0",
                        "type": "columns",
                    },

                ]
            }
        ])
        Formio.createForm(document.getElementById('hmisBNSinhNoGiaychungsinhForm'),
            jsonForm,
            {},
        ).then(function(form) {
            var chieucaoElement = form.getComponent('SO');
            var mahuyenElement = form.getComponent('MAHUYEN_CU_TRU');
            var maxaElement = form.getComponent('MAXA_CU_TRU');
            var nguoighiphieuElement = form.getComponent('NGUOI_GHI_PHIEU');
            formChungsinhIO = form;
            var nhanvien = _.find(singletonObject.danhsachtatcanhanvien, {value: singletonObject.userId+""});
            formChungsinhIO.submission = {
                data: {
                    NGUOI_GHI_PHIEU: nhanvien.tennhanvien,
                    ...data,
                    BHYT: thongtinhsba.thongtinbn.SOBAOHIEMYTE,
                    MA_BENH_NHAN: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                    NGAY_GIO_SINH: data.NGAY_SINH? moment(data.NGAY_SINH + " " + data.GIOIPHUT_DE, ["DD/MM/YYYY HH:mm"]).toISOString(): "",
                    NGAY_CT: data.NGAY_CT? moment(data.NGAY_CT, ["DD/MM/YYYY"]).toISOString(): "",
                    NGAYCAP_CMND_BO: data.NGAYCAP_CMND_BO? moment(data.NGAYCAP_CMND_BO, ["DD/MM/YYYY"]).toISOString(): "",
                }
            }

            $("#"+getIdElmentFormio(form,'SO_QUYEN')).change(function() {
                var url = "soht_GCS";
                var value = $(this).val();
                $.post(url, {
                    dvtt: singletonObject.dvtt,
                    kh: value
                }).done(function (data) {
                    if (data == -1) {
                        notifiToClient("Red","Quyển biên lai này đã sử dụng hết!");
                        chieucaoElement.setValue('');
                    } else {
                        chieucaoElement.setValue(data);
                    }
                }).fail(function() {
                    notifiToClient("Red", MESSAGEAJAX.ERROR);
                });
            })

            $("#"+getIdElmentFormio(form,'MATINH_CU_TRU')).change(function() {
                getDSMaHuyen($(this).val()? $(this).val():0, mahuyenElement)
            })
            $("#"+getIdElmentFormio(form,'MAHUYEN_CU_TRU')).change(function() {
                var mahuyen = $(this).val()? $(this).val():0;
                getDSMaXa(mahuyen, maxaElement)
            })
            $("#"+getIdElmentFormio(form,'NGUOI_GHI_PHIEU')).combogrid({
                url: "noitru_hoichan_timkiem_bacsi?url=" + convertArray([singletonObject.dvtt]),
                debug: true,
                width: "400px",
                colModel: [{'columnName': 'MA_NHANVIEN', 'label': 'Mã nhân viên', 'width': '40'},
                    {'columnName': 'TEN_NHANVIEN', 'width': '40', 'label': 'Tên nhân viên', 'align': 'left'}
                ],
                select: function (event, ui) {
                    nguoighiphieuElement.setValue(ui.item.TEN_NHANVIEN.trim());
                    return false;
                }
            });
            var arrdc = [thongtinhsba.thongtinbn.IDNHANKHAU, thongtinhsba.thongtinbn.MA_BENH_NHAN, "0"];
            $.getJSON("thongtindcbame?url=" + convertArray(arrdc), function(result) {
                $.each(result, function (i, field) {
                    formChungsinhIO.submission = {
                        data: {
                            ...formChungsinhIO.submission.data,
                            CMND: field.CMND,
                            NOI_CAP: field.NOI_CAP,
                            MA_BAME: field.MA_BAME,
                            SO_DIEN_THOAI: field.SO_DIEN_THOAI,
                            NGAY_CAP: field.NGAY_CAP? moment(field.NGAY_CAP, ["DD/MM/YYYY"]).toISOString(): "",
                            MATINH_CU_TRU: Number(field.MATINH_THUONGTRU),
                            MAHUYEN_CU_TRU: Number(field.MAHUYEN_THUONGTRU),
                            MAXA_CU_TRU: field.MAXA_THUONGTRU,
                            DIA_CHI: field.DIA_CHI
                        }
                    }
                    if(field.MATINH_THUONGTRU && field.MATINH_THUONGTRU != 0) {
                        getDSMaHuyen(field.MATINH_THUONGTRU, mahuyenElement, Number(field.MAHUYEN_THUONGTRU))
                    }
                    if(field.MAHUYEN_THUONGTRU && field.MAHUYEN_THUONGTRU != 0) {
                        getDSMaXa(field.MAHUYEN_THUONGTRU, maxaElement, field.MAXA_THUONGTRU)
                    }

                })
                if(data.ID_GCS) {
                    showLoaderIntoWrapId("hmisBNSinhNoGiaychungsinhFormWrap")
                    $.get("chitietgcs?url="+convertArray([data.ID_GCS, 0])).done(function(chitietGCS) {
                        formChungsinhIO.submission = {
                            data: {
                                ...formChungsinhIO.submission.data,
                                ...chitietGCS[0],
                                NGAY_CAP_GCS: chitietGCS[0].NGAY_CAP? moment(chitietGCS[0].NGAY_CAP, ["DD/MM/YYYY"]).toISOString(): "",
                                NGAY_CT: chitietGCS[0].NGAY_CT? moment(chitietGCS[0].NGAY_CT, ["DD/MM/YYYY"]).toISOString(): "",
                                NGAY_CAP: formChungsinhIO.submission.data.NGAY_CAP
                            }
                        }
                        checkkysogiaychungsinh()
                    }).always(function() {
                        hideLoaderIntoWrapId("hmisBNSinhNoGiaychungsinhFormWrap")
                    })
                    $("#hmis_chuyenkhoa_chungsinh_xoa").show()
                } else {
                    $("#hmis_chuyenkhoa_giaychungsinh_kyso").show()
                    $("#hmis_chuyenkhoa_giaychungsinh_huyguikyso").hide()
                    $("#hmis_chuyenkhoa_giaychungsinh_xoakyso").hide()
                }
            });

        });
    }

    function initGridDanhsachTreem() {
        var list = $("#hmislistTreemChungsinh")
        if(!list[0].grid) {
            list.jqGrid({
                url: "",
                datatype: "local",
                loadonce: true,
                height: 300,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "ID",name: 'STT', index: 'STT', width: 50},
                    {label: "Họ tên",name: 'HO_TEN_TRE', index: 'HO_TEN_TRE', width: 200},
                    {label: "Ngày sinh", name: 'NGAY_SINH', index: 'NGAY_SINH', width: 100},
                    {label: "ID_GIOI_TINH", name: 'GIOI_TINH', index: 'GIOI_TINH', width: 0, hidden:true},
                    {label: "Giới tính", name: 'TEN_GIOITINH', index: 'TEN_GIOITINH', width: 150},
                    {label: "ID_NOI_DE",name: 'ID_NOI_DE', index: 'ID_NOI_DE', width: 0, hidden:true},
                    {label: "Cân nặng",name: 'CAN_NANG', index: 'CAN_NANG', width: 150},
                    {label: "SO_LAN_SINH", name: 'SO_LAN_SINH', index: 'SO_LAN_SINH', width: 0, hidden:true},
                    {label: "SO_CON_SINH",name: 'SO_CON_SINH', index: 'SO_CON_SINH', width: 0, hidden:true},
                    {label: "SO_CON_SONG",name: 'SO_CON_SONG', index: 'SO_CON_SONG', width: 0, hidden:true},
                    {label: "SANG_LOC_SS", name: 'SANG_LOC_SS', index: 'SANG_LOC_SS', width: 0, hidden:true},
                    {label: "NGUOI_DO",name: 'NGUOI_DO', index: 'NGUOI_DO', width: 0, hidden:true},
                    {label: "Dị tật bẩm sinh", name: 'HIENTRANG_SK', index: 'HIENTRANG_SK', width: 250},
                    {label: "Tình trạng", name: 'TEN_TINHTRANG', index: 'TEN_TINHTRANG', width: 150},
                    {label: "ID_GCS",name: 'ID_GCS', index: 'ID_GCS', width: 0, hidden:true},
                    {label: "ID_BIEN_DONG_SINH",name: 'ID_BIEN_DONG_SINH', index: 'ID_BIEN_DONG_SINH', width: 0, hidden:true},
                    {label: "GIOIPHUT_DE", name: 'GIOIPHUT_DE', index: 'GIOIPHUT_DE', width: 0, hidden:true},
                    {label: "ID_TAN_TAT",name: 'ID_TAN_TAT', index: 'ID_TAN_TAT', width: 0, hidden:true},
                    {label: "SANG_LOC_SS",name: 'SANG_LOC_SS', index: 'SANG_LOC_SS', width: 0, hidden:true},
                    {label: "NGAY_SANG_LOC",name: 'NGAY_SANG_LOC', index: 'NGAY_SANG_LOC', width: 0, hidden:true},
                    {label: "KET_QUA_SANG_LOC",name: 'KET_QUA_SANG_LOC', index: 'KET_QUA_SANG_LOC', width: 0, hidden:true},
                    {label: "ID_QUAN_HE",name: 'ID_QUAN_HE', index: 'ID_QUAN_HE', width: 0, hidden:true},
                    {label: "TEN_DONVI",name: 'TEN_DONVI', index: 'TEN_DONVI', width: 0, hidden:true}
                ],
                onSelectRow: function(id) {

                },
                caption: "Danh sách trẻ em",
                rownumbers: true,
                autoencode: true,
                gridview: true,
                loadComplete: function(){
                },
                onRightClickRow: function(id) {
                    if (id) {

                        $.contextMenu({
                            selector: '#hmislistTreemChungsinh tr',
                            reposition : false,
                            callback: function (key, options) {
                                if (key == "chitiet") {
                                    var rowData = getThongtinRowSelected("hmislistTreemChungsinh");
                                    showOrHideByClass("HMISKhamchuyenkhoagiaychungsinhWrap", "add", "edit")
                                    showOrHideByClass("HMISKhamchuyenkhoagiaychungsinhFooterWrap", "edit", "add")
                                    generateFormGiayChungsinh(rowData);

                                }
                            },
                            items: {
                                "chitiet": {name: '<p><i class="fa fa-list-ul text-primary" aria-hidden="true"></i> Xem chi tiết</p>'}
                            }
                        });

                    }
                },
            });
        }

    }

    function loadGridDanhsachTreem() {
        console.log("formSinhnoIO.submission.data", formSinhnoIO.submission.data)
        var url = 'laydscon_sinhno?idsinhno=' + formSinhnoIO.submission.data.ID_SS_THONG_TIN;
        loadDataGridGroupBy($("#hmislistTreemChungsinh"), url)
    }

    function hideButtonandShowlistGCS() {
        showOrHideByClass("HMISKhamchuyenkhoagiaychungsinhWrap", "edit", "add")
        showOrHideByClass("HMISKhamchuyenkhoagiaychungsinhFooterWrap", "add", "edit")
    }

    function getDSMaHuyen(matinh, mahuyenElement, defaultVal = "")  {
        $.get("cmu_getlist?url="+convertArray([matinh, 'CMU_GET_MAHUYEN']))
            .done(function(data){
                var items = data.map(function (item) {
                    return {
                        value: item.MA_QUAN_HUYEN,
                        label: item.TEN_QUAN_HUYEN
                    }
                })
                mahuyenElement.component.data.values = items
                mahuyenElement.setItems(items)
                mahuyenElement.setValue(defaultVal)
                // mahuyenElement.redraw()

            })
    }
    function getDSMaXa(mahuyen, maxaElement, defaultVal = "")  {
        $.get("cmu_getlist?url="+convertArray([mahuyen, 'CMU_GET_MAXA']))
            .done(function(data){
                var items = data.map(function (item) {
                    return {
                        value: item.MA_PHUONG_XA,
                        label: item.TEN_PHUONG_XA
                    }
                })
                maxaElement.component.data.values = items
                maxaElement.setItems(items)
                maxaElement.setValue(defaultVal)
                maxaElement.redraw()
            })
    }

    function luuThongtinGCS(idButton, callback) {
        showSelfLoading(idButton);
        var dataSubmit = formChungsinhIO.submission.data;
        var NGAY_CAP = moment(dataSubmit.NGAY_CAP).format("DD/MM/YYYY")
        var NGAYCAP_CMND_BO = dataSubmit.NGAYCAP_CMND_BO? moment(dataSubmit.NGAYCAP_CMND_BO).format("DD/MM/YYYY"): "";
        var NGAY_CAP_GCS = moment(dataSubmit.NGAY_CAP_GCS).format("DD/MM/YYYY");
        var NGAY_CT = moment(dataSubmit.NGAY_CT).format("DD/MM/YYYY");
        var NGAY_GIO_SINH = moment(dataSubmit.NGAY_GIO_SINH).format("DD/MM/YYYY HH:mm");
        var arr = [singletonObject.dvtt, dataSubmit.SO_QUYEN, dataSubmit.SO, dataSubmit.ID_GCS,'0'];
        $.post("soht_trung", {url: convertArray(arr)}).done(function (data) {
            if (parseInt(data) == 0) {
                hideSelfLoading(idButton);
                return notifiToClient("Red","Số biên lai đã được sử dụng!");
            }else if(parseInt(data) == 2){
                hideSelfLoading(idButton);
                return notifiToClient("Red","Quyển biên lai đã hết!");
            }else if(parseInt(data) == 3){
                hideSelfLoading(idButton);
                return notifiToClient("Red","Số biên lai nhỏ hơn số bắt đầu!");
            }

            var str = [
                singletonObject.dvtt,
                NGAY_CAP_GCS,
                thongtinhsba.thongtinbn.IDNHANKHAU,
                NGAY_GIO_SINH.split(" ")[0],
                dataSubmit.SINH_TAI,
                dataSubmit.SO_LAN_SINH,
                dataSubmit.SO_CON_SONG,
                dataSubmit.SO_CON_SINH,
                dataSubmit.GIOI_TINH,
                dataSubmit.CAN_NANG,
                dataSubmit.TT_HIEN_TAI,
                "-1",//idcon,
                dataSubmit.NGUOI_DO,
                dataSubmit.SO_QUYEN,
                dataSubmit.SO,
                dataSubmit.TINH_TRANG,
                dataSubmit.ID_GCS,
                dataSubmit.HO_TEN_TRE,
                thongtinhsba.thongtinbn.MA_BENH_NHAN,
                '',//v_mabenhnhancon,
                dataSubmit.DIA_CHI,
                NGAY_GIO_SINH.split(" ")[1].split(":")[0],
                NGAY_GIO_SINH.split(" ")[1].split(":")[1],
                '',//idme_mth,
                '',//v_mabn_mth,
                0,//v_mth,
                dataSubmit.ID_NOI_DE,
                dataSubmit.HO_TEN_BO,
                formSinhnoIO.submission.data.ID_SS_THONG_TIN,
                '',//idnkbo,
                dataSubmit.CMND,
                NGAY_CAP,
                dataSubmit.NOI_CAP,
                dataSubmit.ID_TAN_TAT,
                dataSubmit.ID_QUAN_HE,
                formSinhnoIO.submission.data.TINH_TRANG_TRE,
                dataSubmit.STT,
                dataSubmit.BHYT,
                dataSubmit.SO_CMND_BO,
                NGAYCAP_CMND_BO,
                dataSubmit.NOICAP_CMND_BO,
                dataSubmit.DIACHI_BO,
                dataSubmit.DIACHINND_TUKHAI?1:0,
                dataSubmit.DIACHINND_TUKHAI,
                dataSubmit.MA_CT,
                dataSubmit.SO_SERI,
                NGAY_CT,
                dataSubmit.SINHCON_PHAUTHUAT?dataSubmit.SINHCON_PHAUTHUAT:0,
                dataSubmit.SINHCON_DUOI32TUAN?dataSubmit.SINHCON_DUOI32TUAN:0,
                dataSubmit.NGUOI_GHI_PHIEU,//nguoighiphieu,
                '0'
            ];
            if (!dataSubmit.ID_GCS) {
                $.post("themgcs", {
                    url: convertArray(str)
                }).done(function (idGCS) {
                    if (idGCS != "-1") {
                        updateMAGCS(dataSubmit, idGCS)
                        updateMATHETAMGCS(dataSubmit, idGCS)
                        if(typeof callback == "function") {
                            callback()
                        } else {
                            notifiToClient("Green", MESSAGEAJAX.ADD_SUCCESS)
                        }
                        thembiendongsinh(dataSubmit, idGCS, NGAY_GIO_SINH)
                    } else {
                        notifiToClient("Green", MESSAGEAJAX.FAILE)
                    }
                }).fail(function() {
                    notifiToClient("Red", MESSAGEAJAX.ERROR)
                }).always(function () {
                    hideSelfLoading(idButton);
                });
            } else {
                $.post("suagcs", {
                    url: convertArray(str)
                }).done(function (data) {
                    if (data != "-1") {
                        updateMAGCS(dataSubmit, dataSubmit.ID_GCS)
                        updateMATHETAMGCS(dataSubmit, dataSubmit.ID_GCS)
                        if(typeof callback == "function") {
                            callback()
                        } else {
                            notifiToClient("Green", MESSAGEAJAX.EDIT_SUCCESS)
                        }
                        thembiendongsinh(dataSubmit, dataSubmit.ID_GCS, NGAY_GIO_SINH)

                    } else {
                        notifiToClient("Red", MESSAGEAJAX.FAIL)
                    }
                }).fail(function() {
                    notifiToClient("Red", MESSAGEAJAX.ERROR)
                }).always(function () {
                    hideSelfLoading(idButton);
                });
            }
        }).fail(function () {
            notifiToClient("Red", MESSAGEAJAX.ERROR)
            hideSelfLoading(idButton);
        });
    }

    function updateMAGCS(dataSubmit, idGCS) {
        var resText =  $.ajax({
            method: "GET",
            url: "cmu_getlist?url="+convertArray([singletonObject.dvtt,
                thongtinhsba.thongtinbn.STT_BENHAN,
                thongtinhsba.thongtinbn.MA_BENH_NHAN,
                idGCS,
                dataSubmit.MATINH_CU_TRU,
                dataSubmit.MAHUYEN_CU_TRU,
                dataSubmit.MAXA_CU_TRU,
                'CMU_THONGTINME_NAMVIEN']),
            async: false
        }).responseText
        resText = JSON.parse(resText);
        formChungsinhIO.submission = {
            data: {
                ...dataSubmit,
                ID_GCS: idGCS,
                MA_CT: resText[0].MA_CT
            }
        }
    }

    function updateMATHETAMGCS(dataSubmit, idGCS) {
        $.post("cmu_post", {
            url: [singletonObject.dvtt,idGCS, dataSubmit.MA_THE_TAM, "CMU_UPDATE_GCS_MATHETAM"].join("```")
        })
    }

    function thembiendongsinh(dataSubmit, idGCS, NGAY_GIO_SINH) {
        $.post("soht_tinh",
            {url: convertArray([singletonObject.dvtt, dataSubmit.SO_QUYEN, dataSubmit.SO, '0'])}).done(function (data) {
        });
        var str2 = [
            !dataSubmit.ID_BIEN_DONG_SINH? "-1" :dataSubmit.ID_BIEN_DONG_SINH,
            NGAY_GIO_SINH.split(" ")[0], "", "",
            dataSubmit.SO_LAN_SINH,
            dataSubmit.SINH_TAI,
            dataSubmit.SANG_LOC_SS? dataSubmit.SANG_LOC_SS: "",
            dataSubmit.NGAY_SANG_LOC? dataSubmit.NGAY_SANG_LOC: "",
            dataSubmit.KET_QUA_SANG_LOC? dataSubmit.KET_QUA_SANG_LOC: "",
            thongtinhsba.thongtinbn.IDNHANKHAU,
            dataSubmit.HO_TEN_TRE,
            dataSubmit.GIOI_TINH,
            dataSubmit.ID_TAN_TAT,
            dataSubmit.ID_QUAN_HE? dataSubmit.ID_QUAN_HE: 0, idGCS, 1, 0, "0"];
        if (thongtinhsba.thongtinbn.GIOI_TINH != 1 && thongtinhsba.thongtinbn.IDNHANKHAU != "" && thongtinhsba.thongtinbn.IDNHANKHAU != 0) {
            if (!dataSubmit.ID_BIEN_DONG_SINH) {
                $.post("qlnk_thembdsinh", {
                    url: convertArray(str2)
                });
            } else {
                $.post("qlnk_suabdsinh", {
                    url: convertArray(str2)
                })
            }
        }
    }

    function checkkysogiaychungsinh() {
        $.ajax({
            url: "cmu_getlist?url="+convertArray([
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                _.get(formChungsinhIO, 'submission.data.ID_GCS', ''),
                'cmu_smart769_getchungsinh']),
            method: "GET",
        }).done(function (data) {
            var rest = data[0]
            if(!rest['KEYMINIO']) {

                $("#hmis_chuyenkhoa_giaychungsinh_xoakyso").hide()
                if(rest['DAGUI'] > 0) {
                    $("#hmis_chuyenkhoa_giaychungsinh_kyso").hide()
                    $("#hmis_chuyenkhoa_giaychungsinh_xoakyso").hide()
                    $("#hmis_chuyenkhoa_giaychungsinh_huyguikyso").show()
                } else {
                    $("#hmis_chuyenkhoa_giaychungsinh_kyso").show()
                    $("#hmis_chuyenkhoa_giaychungsinh_huyguikyso").hide()
                    $("#hmis_chuyenkhoa_giaychungsinh_xoakyso").hide()
                }

            } else {
                $("#hmis_chuyenkhoa_giaychungsinh_xoakyso").show()
                $("#hmis_chuyenkhoa_giaychungsinh_kyso").hide()
                $("#hmis_chuyenkhoa_giaychungsinh_huyguikyso").hide()
            }
        })
    }
})