CREATE OR REPLACE
FUNCTION his_manager.fn_ds_chuyen_doi_don_vi (
    p_madonvi      IN VARCHAR2,
    p_madonvi_moi  IN VARCHAR2,
    i_rows          IN NUMBER,
    i_p             IN NUMBER
) RETURN SYS_REFCURSOR
AS
    l_cursor SYS_REFCURSOR;
    v_count NUMBER := 0;
BEGIN
SELECT COUNT(*) INTO v_count
FROM his_manager.dm_donvi_chuyen_doi
WHERE
    (
            (madonvi = p_madonvi OR p_madonvi IS NULL)
            AND (madonvi_moi = p_madonvi_moi OR p_madonvi_moi IS NULL)
        );

IF v_count = 0 THEN
        OPEN l_cursor FOR
SELECT dm.id,
       dm.madonvi,
       dv.ten_donvi,
       dm.madonvi_moi,
       TO_CHAR(TO_DATE(dm.ngay_hieu_luc, 'YYYYMMDD'), 'DD/MM/YYYY') as ngay_hieu_luc,
       TO_CHAR(TO_DATE(dm.ngay_het_hieu_luc, 'YYYYMMDD'), 'DD/MM/YYYY') as ngay_het_hieu_luc,
       dm.ghi_chu
FROM his_manager.dm_donvi_chuyen_doi dm
         inner join his_fw.dm_donvi dv on dm.madonvi = dv.ma_donvi
WHERE (dm.madonvi = p_madonvi OR p_madonvi IS NULL)
  AND (dm.madonvi_moi = p_madonvi_moi OR p_madonvi_moi IS NULL)
ORDER BY ngay_hieu_luc;
ELSE
        OPEN l_cursor FOR
SELECT k.*
FROM (
         SELECT
             ROW_NUMBER() OVER (ORDER BY dm.id DESC) rn,
                 dm.id,
             dm.madonvi,
             dv.ten_donvi,
             dm.madonvi_moi,
             TO_CHAR(TO_DATE(dm.ngay_hieu_luc, 'YYYYMMDD'), 'DD/MM/YYYY') as ngay_hieu_luc,
             TO_CHAR(TO_DATE(dm.ngay_het_hieu_luc, 'YYYYMMDD'), 'DD/MM/YYYY') as ngay_het_hieu_luc,
             dm.ghi_chu
         FROM his_manager.dm_donvi_chuyen_doi dm
                  inner join his_fw.dm_donvi dv on dm.madonvi = dv.ma_donvi
         WHERE (dm.madonvi = p_madonvi OR p_madonvi IS NULL)
           AND (dm.madonvi_moi = p_madonvi_moi OR p_madonvi_moi IS NULL)
     ) k
WHERE k.rn <= i_p * i_rows AND k.rn > (i_p - 1) * i_rows;
END IF;


RETURN l_cursor;
END;
/