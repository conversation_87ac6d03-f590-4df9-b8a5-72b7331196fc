<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report name" language="groovy" pageWidth="595" pageHeight="842" columnWidth="580" leftMargin="10" rightMargin="5" topMargin="10" bottomMargin="10" uuid="094bb44e-c3a4-4e55-be44-2d9dcaeab41b">
	<property name="ireport.zoom" value="1.3636363636363722"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="12"/>
	<queryString language="plsql">
		<![CDATA[select 1 from dual]]>
	</queryString>
	<field name="ID" class="java.math.BigDecimal"/>
	<field name="LYDOVAOVIEN" class="java.lang.String"/>
	<field name="NGUYENNHAN" class="java.lang.String"/>
	<field name="TUBAOGIO" class="java.lang.String"/>
	<field name="TRIEUCHUNGCHINH" class="java.lang.String"/>
	<field name="TRIEUCHUNGCHINHKHAC" class="java.lang.String"/>
	<field name="DADIEUTRI" class="java.lang.String"/>
	<field name="DADIEUTRITAPNHUOCTHI" class="java.lang.String"/>
	<field name="KETQUATAPNHUOCTHI" class="java.lang.String"/>
	<field name="DADIEUTRIPHAUTHUAT" class="java.lang.String"/>
	<field name="KETQUAPHAUTHUAT" class="java.lang.String"/>
	<field name="DADIEUTRIPHAUTHUATMONON" class="java.lang.String"/>
	<field name="DADIEUTRIPHAUTHUATMOGIA" class="java.lang.String"/>
	<field name="TIENSUBENHBANTHAN" class="java.lang.String"/>
	<field name="TIENSUBENHBANTHANBENHLY" class="java.lang.String"/>
	<field name="TIENSUBENHGIADINH" class="java.lang.String"/>
	<field name="TIENSUBENHGIADINHBENHLY" class="java.lang.String"/>
	<field name="THILUCKHONGKINHMP" class="java.lang.String"/>
	<field name="THILUCKHONGKINHMT" class="java.lang.String"/>
	<field name="THILUCCOKINHMP" class="java.lang.String"/>
	<field name="THILUCCOKINHMT" class="java.lang.String"/>
	<field name="KHUCXAMAYTRUOCMP" class="java.lang.String"/>
	<field name="KHUCXAMAYTRUOCMT" class="java.lang.String"/>
	<field name="KHUCXAMAYSAUMP" class="java.lang.String"/>
	<field name="KHUCXAMAYSAUMT" class="java.lang.String"/>
	<field name="SOIBONGDONGTU" class="java.lang.String"/>
	<field name="VANNHANNGOAILAI" class="java.lang.String"/>
	<field name="VANNHANNOITAIMP" class="java.lang.String"/>
	<field name="VANNHANNOITAIMPBENHLY" class="java.lang.String"/>
	<field name="VANNHANNOITAIMT" class="java.lang.String"/>
	<field name="VANNHANNOITAIMTBENHLY" class="java.lang.String"/>
	<field name="DIEMCANQUITU" class="java.lang.String"/>
	<field name="DIEMCANQUITUBINHTHUONG" class="java.lang.String"/>
	<field name="DIEMCANQUITUBENHLY" class="java.lang.String"/>
	<field name="RUNGGIATNHANCAU" class="java.lang.String"/>
	<field name="RUNGGIATNHANCAUBENHLY" class="java.lang.String"/>
	<field name="RUNGGIATNHANCAUKIEU" class="java.lang.String"/>
	<field name="GOCHAM" class="java.lang.String"/>
	<field name="THUNGHIEMCHEMAT" class="java.lang.String"/>
	<field name="HINHTHAIVATINHCHATLAC" class="java.lang.String"/>
	<field name="DOLACHIRSCHBERGTRUOCATROPINE" class="java.lang.String"/>
	<field name="DOLACHIRSCHBERGSAUATROPINE" class="java.lang.String"/>
	<field name="DOLACLANGKINHTRUOCATROPINE" class="java.lang.String"/>
	<field name="DOLACLANGKINHSAUATROPINE" class="java.lang.String"/>
	<field name="DOLACNHINGAN" class="java.lang.String"/>
	<field name="DOLACNHINXA" class="java.lang.String"/>
	<field name="DOLACNHINLEN" class="java.lang.String"/>
	<field name="DOLACNHINXUONG" class="java.lang.String"/>
	<field name="HOICHUNG" class="java.lang.String"/>
	<field name="SYNOPTOPHOREKHACHQUAN" class="java.lang.String"/>
	<field name="SYNOPTOPHORECHUQUAN" class="java.lang.String"/>
	<field name="TINHTRANGTHIGIACHAIMAT" class="java.lang.String"/>
	<field name="TINHTRANGTHIGIACHAIMATPHUTHI" class="java.lang.String"/>
	<field name="BIENDOHOPTHI" class="java.lang.String"/>
	<field name="TUONGUNGVONGMAC" class="java.lang.String"/>
	<field name="SONGTHI" class="java.lang.String"/>
	<field name="SONGTHICO" class="java.lang.String"/>
	<field name="TUTHEBUTRU" class="java.lang.String"/>
	<field name="TUTHEBUTRUCO" class="java.lang.String"/>
	<field name="MIMATMP" class="java.lang.String"/>
	<field name="MIMATMT" class="java.lang.String"/>
	<field name="SUPMIMP" class="java.lang.String"/>
	<field name="SUPMIMT" class="java.lang.String"/>
	<field name="EPICANTHUSMP" class="java.lang.String"/>
	<field name="EPICANTHUSMT" class="java.lang.String"/>
	<field name="COMIMP" class="java.lang.String"/>
	<field name="COMIMT" class="java.lang.String"/>
	<field name="MARCUSGUNNMP" class="java.lang.String"/>
	<field name="MARCUSGUNNMT" class="java.lang.String"/>
	<field name="DAUHIEUBELLMP" class="java.lang.String"/>
	<field name="DAUHIEUBELLMT" class="java.lang.String"/>
	<field name="MIMATKHACMP" class="java.lang.String"/>
	<field name="MIMATKHACMT" class="java.lang.String"/>
	<field name="KETMACMP" class="java.lang.String"/>
	<field name="KETMACMT" class="java.lang.String"/>
	<field name="KETMACBENHLYMP" class="java.lang.String"/>
	<field name="KETMACBENHLYMT" class="java.lang.String"/>
	<field name="PHANTRUOCNHANCAUMP" class="java.lang.String"/>
	<field name="PHANTRUOCNHANCAUMT" class="java.lang.String"/>
	<field name="PHANTRUOCNHANCAUBENHLYMP" class="java.lang.String"/>
	<field name="PHANTRUOCNHANCAUBENHLYMT" class="java.lang.String"/>
	<field name="PHANSAUNHANCAUMP" class="java.lang.String"/>
	<field name="PHANSAUNHANCAUMT" class="java.lang.String"/>
	<field name="PHANSAUNHANCAUBENHLYMP" class="java.lang.String"/>
	<field name="PHANSAUNHANCAUBENHLYMT" class="java.lang.String"/>
	<field name="DINHTHIMP" class="java.lang.String"/>
	<field name="DINHTHIMT" class="java.lang.String"/>
	<field name="HUYETAPTREN" class="java.lang.String"/>
	<field name="HUYETAPDUOI" class="java.lang.String"/>
	<field name="NHIETDO" class="java.lang.String"/>
	<field name="MACH" class="java.lang.String"/>
	<field name="NOITIET" class="java.lang.String"/>
	<field name="NOITIETBENHLY" class="java.lang.String"/>
	<field name="THANKINH" class="java.lang.String"/>
	<field name="THANKINHBENHLY" class="java.lang.String"/>
	<field name="TUANHOAN" class="java.lang.String"/>
	<field name="TUANHOANBENHLY" class="java.lang.String"/>
	<field name="HOHAP" class="java.lang.String"/>
	<field name="HOHAPBENHLY" class="java.lang.String"/>
	<field name="TIEUHOA" class="java.lang.String"/>
	<field name="TIEUHOABENHLY" class="java.lang.String"/>
	<field name="COXUONGKHOP" class="java.lang.String"/>
	<field name="COXUONGKHOPBENHLY" class="java.lang.String"/>
	<field name="TIETNIEU" class="java.lang.String"/>
	<field name="TIETNIEUBENHLY" class="java.lang.String"/>
	<field name="TOANTHANKHAC" class="java.lang.String"/>
	<field name="CLS" class="java.lang.String"/>
	<field name="BENHCHINH" class="java.lang.String"/>
	<field name="BENHKEMTHEO" class="java.lang.String"/>
	<field name="PHANBIET" class="java.lang.String"/>
	<field name="PHUONGPHAPCHINH" class="java.lang.String"/>
	<field name="CHEDOAN" class="java.lang.String"/>
	<field name="CHEDOCHAMSOC" class="java.lang.String"/>
	<field name="TIENLUONG" class="java.lang.String"/>
	<field name="NV_LAMBENHAN" class="java.lang.String"/>
	<field name="NGAY_LAMBENHAN" class="java.lang.String"/>
	<field name="NGAYKY" class="java.lang.String"/>
	<field name="MABENHCHINH" class="java.lang.String"/>
	<field name="MABENHKEMTHEO" class="java.lang.String"/>
	<field name="TOMTAT_BENHAN" class="java.lang.String"/>
	<field name="ANCHUKY" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="313">
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="89" width="580" height="17" uuid="dfc9fbd9-e1d0-44cb-8af7-99d525307045"/>
				<box leftPadding="8"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Bệnh chính: "+(($F{BENHCHINH} != null && !$F{BENHCHINH}.isEmpty())?$F{BENHCHINH}:"")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="333" y="244" width="247" height="18" uuid="df75430a-1b08-4084-aec9-dfa7f5f47d18"/>
				<box leftPadding="6"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Bác sĩ làm bệnh án]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="140" width="580" height="17" uuid="3b499f23-5803-49cd-a05f-719592583e83"/>
				<box leftPadding="6"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[VII. Điều trị]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="333" y="295" width="247" height="18" uuid="c8f713cd-5d46-4ac8-8516-1348d31795c6">
					<printWhenExpression><![CDATA[$F{ANCHUKY}.equals("0")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Họ và tên: " +($F{NV_LAMBENHAN}!=null?$F{NV_LAMBENHAN}:"............................................................")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="333" y="225" width="247" height="18" uuid="e1dff5a0-2c49-4982-8e84-64efc1f010bc"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY_LAMBENHAN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="72" width="580" height="17" uuid="957c61a6-b3e3-4f3d-85a1-1074b7a33b4f"/>
				<box leftPadding="6"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[VI. Chẩn đoán]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="106" width="580" height="17" uuid="5b80401c-98cd-4228-b8b2-************"/>
				<box leftPadding="8"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Bệnh kèm theo: "+(($F{BENHKEMTHEO} != null && !$F{BENHKEMTHEO}.isEmpty())?$F{BENHKEMTHEO}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="123" width="580" height="17" uuid="030e2e35-e2ab-4d7b-9af1-07bcba1af969"/>
				<box leftPadding="8"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Phân biệt: "+(($F{PHANBIET} != null && !$F{PHANBIET}.isEmpty())?$F{PHANBIET}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="174" width="580" height="17" uuid="42e5e5c9-2607-4568-a876-0459518ae38f"/>
				<box leftPadding="8"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Chế độ ăn uống: "+(($F{CHEDOAN} != null && !$F{CHEDOAN}.isEmpty())?$F{CHEDOAN}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="191" width="580" height="17" uuid="15f0c565-58f0-4c0d-a83d-aabd0dd6524b"/>
				<box leftPadding="8"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Chế độ chăm sóc: "+(($F{CHEDOCHAMSOC} != null && !$F{CHEDOCHAMSOC}.isEmpty())?$F{CHEDOCHAMSOC}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="157" width="580" height="17" uuid="a4fe59dd-11a6-4131-b52f-f0169d05943b"/>
				<box leftPadding="8"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Phương pháp chính: "+(($F{PHUONGPHAPCHINH} != null && !$F{PHUONGPHAPCHINH}.isEmpty())?$F{PHUONGPHAPCHINH}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="208" width="580" height="17" uuid="5985cb2d-106a-4de9-a93a-17ba031730eb"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dotted"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA["<style isBold='true'>VIII. Tiên lượng: </style>"+(($F{TIENLUONG} != null && !$F{TIENLUONG}.isEmpty())?$F{TIENLUONG}:"")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="580" height="17" uuid="e83b1fa5-5696-4844-9c97-8c11a41b9d89"/>
				<box leftPadding="6"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[IV. Các xét nghiệm cần làm]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="35" width="580" height="17" uuid="4fa239ff-4a6a-45b6-a23f-675f2283c2c5"/>
				<box leftPadding="6"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[V. Tóm tắt bệnh án]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="53" width="580" height="17" uuid="f6278a11-43de-41c0-afb7-57bd0e146511"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dotted"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA[(($F{TOMTAT_BENHAN} != null && !$F{TOMTAT_BENHAN}.isEmpty())?$F{TOMTAT_BENHAN}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="17" width="580" height="17" uuid="5b27af5f-44dc-4090-9a7e-a9f0ac681219"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dotted"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA[(($F{CLS} != null && !$F{CLS}.isEmpty())?$F{CLS}:"")]]></textFieldExpression>
			</textField>
		</band>
	</title>
</jasperReport>
