danhSachThuocGMHS = [];
danhSachDichTruyenGMHS = [];

function loadGayMeHoiSuc() {
    showLoaderIntoWrapId("lanphauthuat_gmhs_jqgrid-wrap");
    var gridInstance = $("#lanphauthuat_gmhs_jqgrid");
    if(gridInstance[0].grid) {
        gridInstance.jqGrid('clearGridData');
    }
    $("#lanphauthuat_gmhs_action_themmoi").show();
    $("#lanphauthuat_gmhs_action_tongket").show();
    $("#lanphauthuat_gmhs_action_kyso").show();
    $("#lanphauthuat_gmhs_action_huykyso").hide();
    $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, lanPhauThuatObject.ID, 'CMU_LPT_GMHS_SEL'])).done(function(data){

        if(data.length > 0){
            lanPhauThuatObject.gmhs = data;
            lanPhauThuatObject.ID_GMHS = data[0].ID_GMHS;
            lanPhauThuatObject.gmhs.forEach(function(item) {
                if (item.HOATDONG == '1') {
                    lanPhauThuatObject["BAT_DAU_MO"] = item.THOI_GIAN_BLOCK;
                } else if(item.NGAY_BAT_DAU_ME == item.THOI_GIAN_BLOCK) {
                    lanPhauThuatObject["BAT_DAU_ME"] = item.THOI_GIAN_BLOCK;
                } else if(item.HOATDONG == '2'){
                    lanPhauThuatObject["DAT_NOI_KHIQUAN"] = item.THOI_GIAN_BLOCK;
                } else if(item.NGAY_KET_THUC_MO == item.THOI_GIAN_BLOCK) {
                    lanPhauThuatObject["KET_THUC_MO"] = item.THOI_GIAN_BLOCK;
                }
            })
            if(gridInstance[0].grid) {
                gridInstance.jqGrid('clearGridData');
                gridInstance.jqGrid('setGridParam', {
                    datatype: 'local',
                    data: data
                }).trigger("reloadGrid");
            }
            if (data[0].KEYSIGN){
                $("#lanphauthuat_gmhs_action_themmoi").hide();
                $("#lanphauthuat_gmhs_action_tongket").hide();
                $("#lanphauthuat_gmhs_action_kyso").hide();
                $("#lanphauthuat_gmhs_action_huykyso").show();
            }
        } else {
            lanPhauThuatObject.gmhs = [];
            lanPhauThuatObject.dsthuoc = [];
            lanPhauThuatObject.dsdichtruyen = [];
        }
    }).always(function() {
        hideLoaderIntoWrapId("lanphauthuat_gmhs_jqgrid-wrap");
    });
}

$(function() {
    // ************** EVENT **************
    var ID_GMHS_CT = 0;
    var formGMHSTongket;
    var formThongtintreem;
    var formThongtintruyenmau;
    var dsNhommau = [
        {
            "label": "Nhóm máu A",
            "value": "1"
        },
        {
            "label": "Nhóm máu B",
            "value": "2"
        },
        {
            "label": "Nhóm máu AB",
            "value": "3"
        },
        {
            "label": "Nhóm máu O",
            "value": "4"
        }
    ]

    $("#lanphauthuat-gaymehoisuc").click(function() {
        instanceGridLanPhauThuatGMHS();
        instanceGridLanPhauThuatTreem()
        instanceGridLanPhauThuatTruyenmau();
    });

    $("#lanphauthuat_gmhs_action_themmoi").click(function(){
        openModalGMHSChiTiet("them", null, moment().format('DD/MM/YYYY HH:mm'));
    });

    $("#modalGMHSChiTiet").on('hidden.bs.modal', function (e) {
        $(this).find(".clear-text").val("");
        lanPhauThuatObject.gmhs = [];
        danhSachThuocGMHS = [];
        danhSachDichTruyenGMHS = [];
        ID_GMHS_CT = 0;
        $("#lanphauthuat_gmhs_dichtruyen_table_body").empty();
        $("#lanphauthuat_gmhs_thuoc_table_body").empty();
        loadGayMeHoiSuc();
    });

    $("#modalGMHSChiTiet").on('show.bs.modal', function (e) {
        addTextTitleModal("titleGMHSChiTiet", "Phiếu gây mê hồi sức");
    });

    $("#lanphauthuat_gmhs_nghiepvuthuoc").change(function() {
        var loaiVT = '';
        if($(this).val() == 'noitru_toathuoc') {
            loadKhoThuocVatTucommon(singletonObject.danhsachkhothuocBHYT, 'lanphauthuat_gmhs_khothuoc');
            loaiVT = 'VT_TH'
        }
        if($(this).val() == 'noitru_toavattu') {
            loadKhoThuocVatTucommon(singletonObject.danhsachkhovattu, 'lanphauthuat_gmhs_khothuoc');
            loaiVT = 'TH'
        }
        if($(this).val() == 'noitru_toamienphi') {
            loadKhoThuocVatTucommon(singletonObject.danhsachkhomienphi, 'lanphauthuat_gmhs_khothuoc');
        }
        if($(this).val() == 'noitru_toaquaybanthuocbv') {
            loadKhoThuocVatTucommon(singletonObject.danhsachkhomuataiquay, 'lanphauthuat_gmhs_khothuoc');
        }
        if($(this).val() == 'noitru_toadongy') {
            loadKhoThuocVatTucommon(singletonObject.danhsachkhodongy, 'lanphauthuat_gmhs_khothuoc');
        }
        if($(this).val() == 'noitru_toadichvu') {
            loadKhoThuocVatTucommon(singletonObject.danhsachkhodichvu, 'lanphauthuat_gmhs_khothuoc');
        }
        if($(this).val() == 'noitru_toamuangoai') {
            var url = "layvattu_mn";
            $("#lanphauthuat_gmhs_khothuoc").html("<option selected value='0'>Mua ngoài</option>")
            $("#lanphauthuat_gmhs_thuoc_select").combogrid("option", "url", url);
            return false;
        }
        loadKhothuocmacdinhcommon("lanphauthuat_gmhs_nghiepvuthuoc", "lanphauthuat_gmhs_khothuoc");
        changeSourceComboGridcommon($("#lanphauthuat_gmhs_khothuoc").val(), loaiVT, "lanphauthuat_gmhs_thuoc_select");
    });
    $("#lanphauthuat_gmhs_khothuoc").change(function() {
        localStorage.setItem($("#lanphauthuat_gmhs_nghiepvuthuoc").val(), $(this).val());
        changeSourceComboGridcommon($(this).val(), $("#lanphauthuat_gmhs_nghiepvuthuoc").val(), "lanphauthuat_gmhs_thuoc_select");
    })

    $("#modalGMHSChiTiet").on('keyup', '#lanphauthuat_gmhs_thuoc_select', function(e){
        //Improved with keycode checking to prevent extra typing after select
        var key = e.charCode ? e.charCode : e.keyCode ? e.keyCode : 0;
        var keyCode = $.ui.keyCode;
        if(key != keyCode.ENTER && key != keyCode.LEFT && key != keyCode.RIGHT && key != keyCode.DOWN) {
            $(this).parent().find("#lanphauthuat_gmhs_mathuoc").val("");
            $("#modalGMHSChiTiet").parent().find("#lanphauthuat_gmhs_dungtich").val("");
        }
    });
    $("#lanphauthuat_gmhs_thuoc_select").focusout(function () {
        if(!$(this).parent().find("#lanphauthuat_gmhs_mathuoc").val()){
            $(this).val("");
            $("#lanphauthuat_gmhs_mathuoc").val("");
        }
    });
    $("#lanphauthuat_gmhs_thuoc_select").combogrid({
        url: "layvattu_theoloai?makhovt=" + $("#lanphauthuat_gmhs_khothuoc").val() + "&loaivattu=VT_TH&nhomvattu=",
        debug: true,
        width: "1110",
        colModel: [
            {'columnName': 'MAVATTU', 'label': 'ID', 'width': '5', 'align': 'left'},
            {'columnName': 'TENVATTU', 'width': '27', 'label': 'Tên thương mại', 'align': 'left'},
            {'columnName': 'TEN_HIEN_THI', 'label': 'Tên hiển thị', 'align': 'left', hidden: true},
            {'columnName': 'HOATCHAT', 'width': '12', 'label': 'Hoạt chất', 'align': 'left'},
            {'columnName': 'HAMLUONG', 'width': '8', 'label': 'Hàm lượng', 'align': 'left'}, //an giang chỉnh tỉ lệ từ 20 xuống 10
            {'columnName': 'DVT', 'label': 'ĐVT', 'width': '5'},
            {'columnName': 'SOLUONG', 'width': '6', 'label': 'Số lượng', 'align': 'center'},
            {'columnName': 'DONGIA', 'width': '8', 'label': 'Đơn giá', 'align': 'right'},
            {'columnName': 'CACHSUDUNG', 'label': 'cachsudung', hidden: true},
            {'columnName': 'DANGTHUOC', 'label': 'DANGTHUOC', hidden: true},
            {'columnName': 'GHICHUVATTU', 'width': '5', 'label': 'Ghi chú', 'align': 'right', hidden: true},
            {'columnName': 'MAKHOVATTU', 'label': 'Mã kho', 'align': 'right', hidden: true},
            {'columnName': 'TENKHOVATTU', 'label': 'Tên kho', 'align': 'right', hidden: true},
            {'columnName': 'NGAYHETHAN', 'width': '10', 'label': 'Ngày hết hạn', 'align': 'right'},
            {'columnName': 'NGOAIDANHMUCBHYT', 'width': '10', 'label': 'BHYT', 'align': 'right'},
            {'columnName': 'SOTHAU', 'width': '7', 'label': 'Số thầu', 'align': 'right', hidden: true},
            {'columnName': 'SOLOSANXUAT', 'width': '7', 'label': 'Số lô', 'align': 'right', hidden: true},
            {'columnName': 'TEN_NGUONDUOC', 'width': '5', 'label': 'Nguồn', 'align': 'right', hidden: true},
            {'columnName': 'DUNGTICH_DICHTRUYEN', 'width': '5', 'label': 'DUNGTICH_DICHTRUYEN', 'align': 'right', hidden: true},
            {'columnName': 'DONVI_HAMLUONG', 'width': '5', 'label': 'DONVI_HAMLUONG', 'align': 'right', hidden: true}
        ],
        select: function (event, ui) {
            $("#lanphauthuat_gmhs_thuoc_select").val(ui.item.TENVATTU);
            $("#lanphauthuat_gmhs_mathuoc").val(ui.item.MAVATTU);
            $("#lanphauthuat_gmhs_dungtich").val(ui.item.DUNGTICH_DICHTRUYEN);
            $("#lanphauthuat_gmhs_ghichu").val(ui.item.CACHSUDUNG);
            $("#lanphauthuat_gmhs_dongia").val(ui.item.DONGIA);
            $("#lanphauthuat_gmhs_donvihamluong").val(ui.item.DONVI_HAMLUONG);
            return false;
        }
    });

    $("#lanphauthuat_gmhs_action_lammoi").click(function(){
        loadGayMeHoiSuc();
    });

    $("#lanphauthuat-gaymehoisuc").click(function(){
        loadGayMeHoiSuc();
    });

    $("#lanphauthuat_gmhs_action_themthuoc").click(function() {
        if($("#lanphauthuat_gmhs_thuocdt").valid()) {
            var NGHIEP_VU = $("#lanphauthuat_gmhs_nghiepvuthuoc").val();
            var KHO = $("#lanphauthuat_gmhs_khothuoc").val();
            var MA_VAT_TU = $("#lanphauthuat_gmhs_mathuoc").val();
            var DUNG_TICH = $("#lanphauthuat_gmhs_dungtich").val();
            var DON_GIA = $("#lanphauthuat_gmhs_dongia").val();
            var GHI_CHU = $("#lanphauthuat_gmhs_ghichu").val();
            var DONVI_HAMLUONG = $("#lanphauthuat_gmhs_donvihamluong").val() == ""? "mg": $("#lanphauthuat_gmhs_donvihamluong").val();
            var isExist = kiemTraThuocDichTruyenDaCo(NGHIEP_VU, KHO, MA_VAT_TU);
            if (isExist) {
                return notifiToClient("Red", "Thuốc này đã tồn tại trong danh sách");
            } else {
                danhSachThuocGMHS.push({
                    NGHIEP_VU: NGHIEP_VU,
                    KHO: KHO,
                    MA_VAT_TU: MA_VAT_TU,
                    TEN_THUOC: $("#lanphauthuat_gmhs_thuoc_select").val(),
                    DUNG_TICH: DUNG_TICH,
                    DONVI_HAMLUONG: DONVI_HAMLUONG,
                    DON_GIA: DON_GIA,
                    GHI_CHU: GHI_CHU,
                });
                loadDSThuocGMHS();
                $("#lanphauthuat_gmhs_thuoc_select").val("");
                $("#lanphauthuat_gmhs_mathuoc").val("");
                $("#lanphauthuat_gmhs_dungtich").val("");
            }
        }

    });
    $("#lanphauthuat_gmhs_action_themdichtruyen").click(function() {
        if($("#lanphauthuat_gmhs_thuocdt").valid()) {
            var NGHIEP_VU = $("#lanphauthuat_gmhs_nghiepvuthuoc").val();
            var KHO = $("#lanphauthuat_gmhs_khothuoc").val();
            var MA_VAT_TU = $("#lanphauthuat_gmhs_mathuoc").val();
            var DUNG_TICH = $("#lanphauthuat_gmhs_dungtich").val();
            var DON_GIA = $("#lanphauthuat_gmhs_dongia").val();
            var GHI_CHU = $("#lanphauthuat_gmhs_ghichu").val();
            var DONVI_HAMLUONG = $("#lanphauthuat_gmhs_donvihamluong").val() == ""? "ml": $("#lanphauthuat_gmhs_donvihamluong").val();
            var isExist = kiemTraThuocDichTruyenDaCo(NGHIEP_VU, KHO, MA_VAT_TU);
            if (isExist) {
                return notifiToClient("Red", "Dịch truyền hoặc thuốc này đã tồn tại trong danh sách");
            } else {
                danhSachDichTruyenGMHS.push({
                    NGHIEP_VU: NGHIEP_VU,
                    KHO: KHO,
                    MA_VAT_TU: MA_VAT_TU,
                    TEN_THUOC: $("#lanphauthuat_gmhs_thuoc_select").val(),
                    BOMTIEMDIEN: $("#lanphauthuat_gmhs_bomtiemdien").val(),
                    TOCDOTRUYEN: "",
                    DONVI_HAMLUONG: DONVI_HAMLUONG,
                    ID_PHATHUOC: 0,
                    DUNG_TICH: DUNG_TICH,
                    DON_GIA: DON_GIA,
                    GHI_CHU: GHI_CHU,
                });
                loadDSDichTruyenGMHS();
                $("#lanphauthuat_gmhs_thuoc_select").val("");
                $("#lanphauthuat_gmhs_mathuoc").val("");
                $("#lanphauthuat_gmhs_dungtich").val("");
                $("#lanphauthuat_gmhs_bomtiemdien").val(0);
            }
        }
    });
    $('#lanphauthuat_gmhs_thuoc_table').on('click', '.deleteButton', function() {
        var row = $(this).closest('tr');
        var dataThuoc = {
            MA_VAT_TU: row.data('MA_VAT_TU'),
            NGHIEP_VU: row.data('NGHIEP_VU'),
            KHO: row.data('KHO'),
            DUNG_TICH: row.data('DUNG_TICH'),
            CHOT_DUOC: row.data('CHOT_DUOC'),
        }
        xoaThuocDichTruyen(dataThuoc, row, 'thuoc');
    });
    $('#lanphauthuat_gmhs_thuoc_table').on('change', 'input[type="number"], input[type="text"]', function() {
        var row = $(this).closest('tr');
        var key = $(this).attr("data-name")
        var index = row.data('index');
        danhSachThuocGMHS[index][key] = $(this).val();
    });
    $('#lanphauthuat_gmhs_dichtruyen_table').on('click', '.deleteButton', function() {
        var row = $(this).closest('tr');
        var dataThuoc = {
            MA_VAT_TU: row.data('MA_VAT_TU'),
            NGHIEP_VU: row.data('NGHIEP_VU'),
            KHO: row.data('KHO'),
            DUNG_TICH: row.data('DUNG_TICH'),
            CHOT_DUOC: row.data('CHOT_DUOC'),
        }
        xoaThuocDichTruyen(dataThuoc, row, 'dichtruyen');
    });
    $('#lanphauthuat_gmhs_dichtruyen_table').on('change', 'input[type="number"], input[type="text"]', function() {
        var row = $(this).closest('tr');
        var key = $(this).attr("data-name")
        var index = row.data('index');
        danhSachDichTruyenGMHS[index][key] = $(this).val();
    });
    $('#lanphauthuat_gmhs_dichtruyen_table').on('click', '.ghichuButton', function() {
        var row = $(this).closest('tr');
        $("#lanphauthuat_gmhs_dt_ghichuluu").attr("data-id",row.data('index'))
        $("#titleModalGMHSDichtruyenGhichu").html("Ghi chú dịch truyền: " + row.data('TEN_THUOC'))
        $("#lanphauthuat_gmhs_dt_inputghichu").val(danhSachDichTruyenGMHS[row.data('index')]['GHI_CHU_TRUYENDICH'])
        $("#lanphauthuat_gmhs_dt_inputlinetruyen").val(danhSachDichTruyenGMHS[row.data('index')]['LINETRUYEN'])
        $("#lanphauthuat_gmhs_dt_inputtocdotruyenbdt").val(danhSachDichTruyenGMHS[row.data('index')]['TOCDOTRUYEN_BTD'])
        $("#modalGMHSDichtruyenGhichu").modal("show")
    });
    $("#lanphauthuat_gmhs_dt_ghichuluu").click(function() {
        var id = $("#lanphauthuat_gmhs_dt_ghichuluu").attr("data-id");
        danhSachDichTruyenGMHS[id]['GHI_CHU_TRUYENDICH'] = $("#lanphauthuat_gmhs_dt_inputghichu").val()
        danhSachDichTruyenGMHS[id]['LINETRUYEN'] = $("#lanphauthuat_gmhs_dt_inputlinetruyen").val()
        danhSachDichTruyenGMHS[id]['TOCDOTRUYEN_BTD'] = $("#lanphauthuat_gmhs_dt_inputtocdotruyenbdt").val()
        $("#modalGMHSDichtruyenGhichu").modal("hide")
        $("#lanphauthuat_gmhs_dt_ghichuluu").attr("data-id","")
        loadDSDichTruyenGMHS()
    })
    $("#lanphauthuat_gmhs_action_them").click(function() {
        var idButton = this.id
        if($("#lanphauthuat-gmhs_formtheodoi").valid()) {
            var thoigian = $("#lanphauthuat_gmhs_thoigian").val();
            var data = {
                thoigian: thoigian,
                mach: $("#lanphauthuat_gmhs_mach").val(),
                huyetap_tren: $("#lanphauthuat_gmhs_tamthu").val(),
                huyetap_duoi: $("#lanphauthuat_gmhs_tamtruong").val(),
                nhietdo: $("#lanphauthuat_gmhs_nhietdo").val(),
                matmau: $("#lanphauthuat_gmhs_matmau").val(),
                nuoctieu: $("#lanphauthuat_gmhs_nuoctieu").val(),
                nhiptho: $("#lanphauthuat_gmhs_nhiptho").val(),
                ttlt: $("#lanphauthuat_gmhs_ttlt").val(),
                feco2: $("#lanphauthuat_gmhs_feco2").val(),
                apluc: $("#lanphauthuat_gmhs_apluc").val(),
                spo2: $("#lanphauthuat_gmhs_spo2").val(),
                fio2: $("#lanphauthuat_gmhs_fio2").val(),
                loaicustom: $("#lanphauthuat_gmhs_loaicustom").val(),
                aldmp: $("#lanphauthuat_gmhs_aldmp").val(),
                quansat: $("#lanphauthuat_gmhs_quansat").val(),
                batdaumo: $("#lanphauthuat_gmhs_hoatdong").val() == 1? 1: 0,
                datnoikhiquan: $("#lanphauthuat_gmhs_hoatdong").val() == 2? 1: 0,
                batdaugayte: $("#lanphauthuat_gmhs_hoatdong").val() == 3? 1: 0,
                tetaicho: $("#lanphauthuat_gmhs_hoatdong").val() == 4? 1: 0,
                tengoaimangcung: $("#lanphauthuat_gmhs_hoatdong").val() == 5? 1: 0,
                tienme: $("#lanphauthuat_gmhs_hoatdong").val() == 6? 1: 0,
                hoatdong: $("#lanphauthuat_gmhs_hoatdong").val()
            }
            var arrThoiGianTrung = lanPhauThuatObject.gmhs.filter(function(item) {
                return item.THOI_GIAN_BLOCK == thoigian;
            });
            showSelfLoading(idButton)
            var responseText = $.ajax({
                url: "cmu_post_CMU_LANPHAUTHUAT_KTRTUONGTRINH",
                type: "POST",
                data: {
                    url: [singletonObject.dvtt, lanPhauThuatObject.ID].join("```")
                },
                async: false
            }).responseText;
            if(responseText == "-2") {
                hideSelfLoading(idButton)
                return notifiToClient("Red", "Đã thực hiện tường trình không thể chỉnh sửa");
            }
            if(arrThoiGianTrung.length > 0) {
                hideSelfLoading(idButton)
                return notifiToClient("Red", "Thời gian này đã tồn tại");
            } else {

                if(lanPhauThuatObject.gmhs.length == 0){
                    themLanPhauThuatGMHS(lanPhauThuatObject.ID)
                        .then(function(id_gmhs) {
                            hideSelfLoading(idButton)
                            themLanPhauThuatGMHSChiTiet(data)
                                .then(function(id_gmhsct) {
                                    if(id_gmhsct > 0){
                                        capNhatThuocVaDichTruyen(id_gmhsct);
                                        capnhatDanhsachTreem(id_gmhsct);
                                        capnhatDanhsachTruyenmau(id_gmhsct);
                                    } else {
                                        notifiToClient("Red", "Có lỗi xảy ra");
                                    }
                                })
                                .catch(function(error) {
                                    console.error(error);
                                });
                        })
                        .catch(function(error) {
                            hideSelfLoading(idButton)
                        });
                } else {
                    themLanPhauThuatGMHSChiTiet(data)
                        .then(function(id_gmhsct) {
                            hideSelfLoading(idButton)
                            if(id_gmhsct > 0){
                                capNhatThuocVaDichTruyen(id_gmhsct);
                                capnhatDanhsachTreem(id_gmhsct);
                                capnhatDanhsachTruyenmau(id_gmhsct);
                            } else {
                                notifiToClient("Red", "Có lỗi xảy ra");
                            }
                        })
                        .catch(function(error) {
                            hideSelfLoading(idButton)
                        });
                }
            }
        }

    })

    $("#lanphauthuat_gmhs_action_luu").click(function() {
        var idButton = this.id
        if($("#lanphauthuat-gmhs_formtheodoi").valid()) {
            var thoigian = $("#lanphauthuat_gmhs_thoigian").val();
            var data = {
                thoigian: thoigian,
                mach: $("#lanphauthuat_gmhs_mach").val(),
                huyetap_tren: $("#lanphauthuat_gmhs_tamthu").val(),
                huyetap_duoi: $("#lanphauthuat_gmhs_tamtruong").val(),
                nhietdo: $("#lanphauthuat_gmhs_nhietdo").val(),
                matmau: $("#lanphauthuat_gmhs_matmau").val(),
                nuoctieu: $("#lanphauthuat_gmhs_nuoctieu").val(),
                nhiptho: $("#lanphauthuat_gmhs_nhiptho").val(),
                ttlt: $("#lanphauthuat_gmhs_ttlt").val(),
                feco2: $("#lanphauthuat_gmhs_feco2").val(),
                apluc: $("#lanphauthuat_gmhs_apluc").val(),
                spo2: $("#lanphauthuat_gmhs_spo2").val(),
                fio2: $("#lanphauthuat_gmhs_fio2").val(),
                loaicustom: $("#lanphauthuat_gmhs_loaicustom").val(),
                aldmp: $("#lanphauthuat_gmhs_aldmp").val(),
                quansat: $("#lanphauthuat_gmhs_quansat").val(),
                batdaumo: $("#lanphauthuat_gmhs_hoatdong").val() == 1? 1: 0,
                datnoikhiquan: $("#lanphauthuat_gmhs_hoatdong").val() == 2? 1: 0,
                batdaugayte: $("#lanphauthuat_gmhs_hoatdong").val() == 3? 1: 0,
                tetaicho: $("#lanphauthuat_gmhs_hoatdong").val() == 4? 1: 0,
                tengoaimangcung: $("#lanphauthuat_gmhs_hoatdong").val() == 5? 1: 0,
                tienme: $("#lanphauthuat_gmhs_hoatdong").val() == 6? 1: 0,
                hoatdong: $("#lanphauthuat_gmhs_hoatdong").val()
            }
            var arrThoiGianTrung = lanPhauThuatObject.gmhs.filter(function(item) {
                return item.THOI_GIAN_BLOCK == thoigian && item.ID_GMHS_CT != ID_GMHS_CT;
            });
            showSelfLoading(idButton)
            var responseText = $.ajax({
                url: "cmu_post_CMU_LANPHAUTHUAT_KTRTUONGTRINH",
                type: "POST",
                data: {
                    url: [singletonObject.dvtt, lanPhauThuatObject.ID].join("```")
                },
                async: false
            }).responseText;
            if(responseText == "-2") {
                hideSelfLoading(idButton)
                return notifiToClient("Red", "Đã thực hiện tường trình không thể chỉnh sửa");
            }
            if(arrThoiGianTrung.length > 0) {
                hideSelfLoading(idButton)
                return notifiToClient("Red", "Thời gian này đã tồn tại");
            } else {
                capNhatLanPhauThuatGMHSChiTiet(data)
                    .then(function(dt) {
                        hideSelfLoading(idButton)
                        if(dt > 0){
                            capNhatThuocVaDichTruyen(ID_GMHS_CT);
                            capnhatDanhsachTreem(ID_GMHS_CT);
                            capnhatDanhsachTruyenmau(ID_GMHS_CT);
                        } else {
                            notifiToClient("Red", "Có lỗi xảy ra");
                        }
                    })
                    .catch(function(error) {
                        hideSelfLoading(idButton)
                        notifiToClient("Red", "Có lỗi xảy ra");
                    });
            }
        }

    });

    $("#lanphauthuat_gmhs_action_xem").click(function() {
        getFilesign769(
            "PHIEU_NOITRU_GAYMEHOISUC",
            lanPhauThuatObject.ID,
            -1,//singletonObject.userId,
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            -1,
            function(data) {
                if(data.length > 0) {
                    getCMUFileSigned769(data[0].KEYMINIO,"pdf")
                } else {
                    $("#wrap_canvas_doc").html("<canvas id='myChart' width='420' height='300'></canvas>")
                    $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, lanPhauThuatObject.ID, 'CMU_LPT_GMHS_SEL'])).done(function(data){
                        if(data.length > 0){
                            var charts = [];
                            var ngaykethuc = "";
                            var thoigianbatdaume = "";
                            var thoigianbatdaumo = "";
                            var batdaume = [];
                            for(var p = 0; p < Math.ceil(data.length/13); p++){
                                var dataMach = [];
                                var dataTamtruong = [];
                                var dataTamthu = [];
                                var indexDatnoikq = -1;
                                var indexMo = -1;
                                var indexKetThuc = -1;
                                var others = {
                                    batdaugayte: -1,
                                    tetaicho: -1,
                                    tengoaimangcung: -1,
                                    tienme: -1,
                                    metinhmach: -1,
                                    metinhmachtetaicho: -1,
                                }
                                for(var i = 0; i < 13; i++){
                                    var item = data[i+p*13];
                                    if(item){
                                        dataMach.push(item.MACH);
                                        dataTamtruong.push(item.HUYETAP_DUOI) ;
                                        dataTamthu.push(item.HUYENAP_TREN);
                                        if (item.HOATDONG == '1') {
                                            indexMo = i;
                                            thoigianbatdaumo = item.THOI_GIAN_BLOCK;
                                            batdaume.push(moment(item.THOI_GIAN_BLOCK, ['DD/MM/YYYY HH:mm']))
                                        } else if(item.NGAY_BAT_DAU_ME == item.THOI_GIAN_BLOCK) {
                                            thoigianbatdaume = item.THOI_GIAN_BLOCK;
                                        }else if(item.HOATDONG == '2'){
                                            indexDatnoikq = i;
                                            batdaume.push(moment(item.THOI_GIAN_BLOCK, ['DD/MM/YYYY HH:mm']))
                                        } else if(item.HOATDONG == '3'){
                                            others.batdaugayte = i;
                                            batdaume.push(moment(item.THOI_GIAN_BLOCK, ['DD/MM/YYYY HH:mm']))
                                        } else if(item.HOATDONG == '4'){
                                            others.tetaicho = i;
                                            batdaume.push(moment(item.THOI_GIAN_BLOCK, ['DD/MM/YYYY HH:mm']))
                                        } else if(item.HOATDONG == '5'){
                                            others.tengoaimangcung = i;
                                            batdaume.push(moment(item.THOI_GIAN_BLOCK, ['DD/MM/YYYY HH:mm']))
                                        } else if(item.HOATDONG == '6'){
                                            others.tienme = i;
                                            batdaume.push(moment(item.THOI_GIAN_BLOCK, ['DD/MM/YYYY HH:mm']))
                                        } else if(item.HOATDONG == '7'){
                                            others.metinhmach = i;
                                            batdaume.push(moment(item.THOI_GIAN_BLOCK, ['DD/MM/YYYY HH:mm']))
                                        } else if(item.HOATDONG == '8'){
                                            others.metinhmachtetaicho = i;
                                            batdaume.push(moment(item.THOI_GIAN_BLOCK, ['DD/MM/YYYY HH:mm']))
                                        } else if(item.NGAY_KET_THUC_MO == item.THOI_GIAN_BLOCK) {
                                            indexKetThuc = i;
                                            ngaykethuc = item.NGAY_KET_THUC_MO;
                                        }
                                    }
                                }
                                vebieudomachhuyetap(dataMach, dataTamtruong, dataTamthu, indexMo, indexDatnoikq, indexKetThuc, others);
                                var image = $("#myChart").get(0).toDataURL("image/png").replace("data:image/png;base64,", "")
                                charts.push("");
                                $.ajax({
                                    url: "cmu_post_CMU_LANPHAUTHUAT_CHART_INS",
                                    type: "POST",
                                    data: {
                                        url: [singletonObject.dvtt, lanPhauThuatObject.ID, p, image].join("```"),
                                    },
                                    async: false
                                })
                            }
                            var tenkhoa = '';
                            singletonObject.danhsachphongban.map(function(item) {
                                if(item.MAKHOA == lanPhauThuatObject.KHOA){
                                    tenkhoa = item.TENKHOA;
                                }
                            })
                            var tenkhoathuchien = '';
                            singletonObject.danhsachphongban.map(function(item) {
                                if(item.MAKHOA == lanPhauThuatObject.KHOA_THUC_HIEN){
                                    tenkhoathuchien = item.TENKHOA;
                                }
                            })
                            var loaiPhauthuat = ['Dạ dày đầy/Cấp cứu: <b>không</b>', '<b><u>Dạ dày đầy</u></b>/Cấp cứu',
                                'Dạ dày đầy/<b><u>Cấp cứu </u></b>', '<b>Dạ dày đầy/Cấp cứu</b>']
                            var arrayTacdung = [' ', 'Tốt', 'Trung bình', 'Xấu']
                            var stringNgaykettuc = ngaykethuc.split(" ")[0].split("/")
                            batdaume =     batdaume.sort(function(a,b){ return a -b;} )
                            var dateMo = moment(batdaume[0], ['DD/MM/YYYY HH:mm']);
                            var dateKetthuc = moment(ngaykethuc, ['DD/MM/YYYY HH:mm']);

                            var diffInMinutes = dateKetthuc.diff(dateMo, 'minutes');
                            var textTongthoigianme = "";
                            if(diffInMinutes < 60) {
                                textTongthoigianme = diffInMinutes + " phút";
                            } else {
                                var hours = Math.floor(diffInMinutes / 60);
                                var minutes = diffInMinutes % 60;
                                textTongthoigianme = ('0'+hours).slice('-2') + " giờ " +  ('0'+minutes).slice('-2') + " phút";
                            }
                            var url = "inphieugaymehoisuc_v2?url="
                                + convertArray([
                                    singletonObject.dvtt,
                                    thongtinhsba.thongtinbn.MA_BENH_NHAN,
                                    thongtinhsba.thongtinbn.STT_BENHAN,
                                    thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                                    thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                    thongtinhsba.thongtinbn.TUOI == 0? thongtinhsba.thongtinbn.TUOI_HT: thongtinhsba.thongtinbn.TUOI,
                                    thongtinhsba.thongtinbn.GIOI_TINH_HT
                                ])+
                                "&idphieu="+ lanPhauThuatObject.ID
                                +"&sovaovien="+thongtinhsba.thongtinbn.SOVAOVIEN
                                +"&sobenhan="+thongtinhsba.thongtinbn.SOBENHAN
                                +"&chandoan="//lanPhauThuatObject.CHAN_DOAN
                                +"&ngayphauthuat="+ (lanPhauThuatObject.NGAY_THUC_HIEN.split(" ")[0])
                                +"&tongsotrang="+ (Math.ceil(data.length/13))
                                +"&thongtinphauthuat="+ encodeURIComponent(JSON.stringify({
                                    "mallampati": lanPhauThuatObject.MALLAMPATI+"",
                                    "diung": lanPhauThuatObject.DI_UNG_THUOC == 1 ? "Có" : lanPhauThuatObject.DI_UNG_THUOC == 2 ? "Không" : "",
                                    "tiensu": lanPhauThuatObject.TIEN_SU_THUOC,
                                    "batthuong": lanPhauThuatObject.BAT_THUONG_CLS,
                                    "asa": lanPhauThuatObject.ASA+"",
                                    "tenkhoa": tenkhoa+"",
                                    "khoathuchien": (tenkhoathuchien+""),
                                    "loaiphauthuat": loaiPhauthuat[lanPhauThuatObject.LOAI_PHAU_THUAT],
                                    "ketluan": data[0].NHANXET,
                                    "tongthoigianme": textTongthoigianme,
                                    "ngaythangnam": "Ngày " + stringNgaykettuc[0] + " tháng " + stringNgaykettuc[1] + " năm " + stringNgaykettuc[2],
                                    "bacsigaymehs": lanPhauThuatObject.TEN_BAC_SI_GAY_ME,
                                    charts: charts,
                                    phuongphappt: lanPhauThuatObject.PHUONG_PHAP_PHAU_THUAT?lanPhauThuatObject.PHUONG_PHAP_PHAU_THUAT:"",
                                    phuongphapvocam: lanPhauThuatObject.PHUONG_PHAP_VO_CAM,
                                    tuthe: lanPhauThuatObject.TU_THE,
                                    cannang: thongtinhsba.thongtinbn.CANNANG ? thongtinhsba.thongtinbn.CANNANG : "",
                                    chieucao: thongtinhsba.thongtinbn.CHIEUCAO? thongtinhsba.thongtinbn.CHIEUCAO : "",
                                    bsphauthuat:  lanPhauThuatObject.EKIPPHAUTHUAT,
                                    bsgayme: lanPhauThuatObject.EKIPGAYME,
                                    tienme: lanPhauThuatObject.TIEN_ME,
                                    tacdung: lanPhauThuatObject.TAC_DUNG == null? " ": arrayTacdung[lanPhauThuatObject.TAC_DUNG],
                                    mayphauthuat: data[0].MAYGAYME,
                                    chandoan: lanPhauThuatObject.CHAN_DOAN,
                                    nhommau: lanPhauThuatObject.NHOMMAU,
                                    tongmatmau: data[0].TONGMATMAU,
                                    tongnuoctieu: data[0].TONGNUOCTIEU,
                                    tongnhiptho: data[0].TONGNHIPTHO,
                                    tongme: data[0].TONGME,
                                    phuongphapme: data[0].PHUONGPHAPME,
                                    tenthuocme: data[0].TENTHUOCME,
                                    khangthe: lanPhauThuatObject.KHANGTHE
                                }))
                            ;
                            previewPdfDefaultModal(url, 'frame-inbaocao')
                        } else {
                            notifiToClient("Red", "Chưa có dữ liệu !");
                        }
                    })
                }
            }
        )
    });
    $("#lanphauthuat_gmhs_action_tongket").click(function() {
        $("#modalGMHSTongketphieu").modal("show")
        var jsonForm = getJSONObjectForm([
            {
                label: "ghmstongket1",
                key: "ghmstongket1",
                columns: [
                    {
                        "components": [
                            {
                                "label": "Mất máu",
                                "customClass": "pr-2",
                                "key": "TONGMATMAU",
                                "type": "textfield",
                                validate: {
                                    required: true,
                                    minlength: 1,
                                    maxlength: 255,
                                }
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Nước tiểu",
                                "customClass": "pr-2",
                                "key": "TONGNUOCTIEU",
                                "type": "textfield",
                                validate: {
                                    required: true,
                                    minlength: 1,
                                    maxlength: 255,
                                }
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Nhịp thở",
                                "customClass": "pr-2",
                                "key": "TONGNHIPTHO",
                                "type": "textfield",
                                validate: {
                                    minlength: 1,
                                    maxlength: 255,
                                }
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Tổng mê",
                                "customClass": "pr-2",
                                "key": "TONGME",
                                "type": "textfield",
                                validate: {
                                    minlength: 1,
                                    maxlength: 255,
                                }
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "ghmstongket2",
                key: "ghmstongket2",
                columns: [
                    {
                        "components": [
                            {
                                "label": "Phương pháp mê",
                                "customClass": "pr-2",
                                "key": "PHUONGPHAPME",
                                "type": "textfield",
                                validate: {
                                    required: true
                                }
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Tên máy",
                                "customClass": "pr-2",
                                "key": "MAYGAYME",
                                "type": "textfield",
                                validate: {
                                    minlength: 1,
                                    maxlength: 255,
                                },
                                others: {
                                    "autocomplete": "on"
                                }
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Tên thuốc mê",
                                "customClass": "pr-2",
                                "key": "TENTHUOCME",
                                "type": "textfield",
                                validate: {
                                    minlength: 1,
                                    maxlength: 255,
                                }

                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Nhận xét",
                                "customClass": "pr-2",
                                "key": "NHANXET",
                                "type": "textfield",
                                validate: {
                                    maxlength: 500,
                                }
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Quan sát",
                                "customClass": "mt-2 pr-2",
                                "key": "QUANSAT",
                                "type": "textfield",
                                validate: {
                                    maxlength: 2000,
                                },
                                others: {
                                    "autocomplete": "on"
                                }
                            },
                        ],
                        "width": 12,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            }
        ])
        Formio.createForm(document.getElementById('formPhauthuatGMHSTongketphieu'),
            jsonForm,{}
        ).then(function(form) {
            formGMHSTongket = form;
            showLoaderIntoWrapId("formPhauthuatGMHSTongketphieuWrap")
            var idTenthuocme = getIdElmentFormio(form,'TENTHUOCME');
            var idQuansat= getIdElmentFormio(form,'QUANSAT');
            var idNhanxet= getIdElmentFormio(form,'NHANXET');
            var idTongme= getIdElmentFormio(form,'TONGME');
            var idTongmatmau= getIdElmentFormio(form,'TONGMATMAU');
            var idTongnuoctieu= getIdElmentFormio(form,'TONGNUOCTIEU');
            var idPhuongphapme= getIdElmentFormio(form,'PHUONGPHAPME');

            $("#"+idTenthuocme).attr("list", idTenthuocme+"_sg");
            $("#"+idQuansat).attr("list", idQuansat+"_sg");
            $("#"+idNhanxet).attr("list", idNhanxet+"_sg");
            $("#"+idTongme).attr("list", idTongme+"_sg");
            $("#"+idTongmatmau).attr("list", idTongmatmau+"_sg");
            $("#"+idTongnuoctieu).attr("list", idTongnuoctieu+"_sg");
            $("#"+idPhuongphapme).attr("list", idPhuongphapme+"_sg");
            $("#"+idTenthuocme).parent().append(
                "<datalist id='"+idTenthuocme+"_sg'>" +
                "<option>Sevoflurane</option>"+
                "</datalist>"
            )
            $("#"+idQuansat).parent().append(
                "<datalist id='"+idQuansat+"_sg'>" +
                "<option>Đâm kim 1 lần, dịch nảo tủy trắng trong bơm từ từ sau 2 phút 2 chân liệt hoàn toàn, mất cảm giác nông sâu ngang T6</option>"+
                "<option>Nghe 2 phổi 4 vị trí thông khí tốt. Kiểm tra dạ dày không thông khí </option>"+
                "</datalist>"
            )
            $("#"+idNhanxet).parent().append(
                "<datalist id='"+idNhanxet+"_sg'>" +
                "<option>Trong quá trình phẫu thuật bệnh nhân ổn</option>"+
                "</datalist>"
            )
            $("#"+idTongme).parent().append(
                "<datalist id='"+idTongme+"_sg'>" +
                "<option>30 ml</option>"+
                "<option>50 ml</option>"+
                "<option>300 ml</option>"+
                "</datalist>"
            )
            $("#"+idTongmatmau).parent().append(
                "<datalist id='"+idTongmatmau+"_sg'>" +
                "<option>30 ml</option>"+
                "<option>50 ml</option>"+
                "<option>300 ml</option>"+
                "</datalist>"
            )
            $("#"+idTongnuoctieu).parent().append(
                "<datalist id='"+idTongnuoctieu+"_sg'>" +
                "<option>30 ml</option>"+
                "<option>50 ml</option>"+
                "<option>300 ml</option>"+
                "</datalist>"
            )
            $("#"+idPhuongphapme).parent().append(
                "<datalist id='"+idPhuongphapme+"_sg'>" +
                "<option>Gây mê tĩnh mạch</option>"+
                "<option>Gây mê nội khí quản</option>"+
                "<option>Gây mê nội khí quản mũi</option>"+
                "<option>Gây mê tại chỗ</option>"+
                "<option>Gây tê tủy sống</option>"+
                "<option>Gây tê ngoài màng cứng</option>"+
                "<option>Gây mê mask</option>"+
                "<option>Gây tê cơ bậc thang</option>"+
                "<option>Tiền mê + gây tê tại chỗ</option>"+
                "<option>Tê tại chỗ</option>"+
                "<option>Tê tùng nách</option>"+
                "<option>Tê tùng đoàn</option>"+
                "<option>Bình thường</option>"+
                "</datalist>"
            )
            $.get('cmu_getlist?url='+convertArray([
                singletonObject.dvtt,
                lanPhauThuatObject.ID,
                'CMU_LPT_GAYMEHOISUC_SEL'
            ])).done(function(data) {
                if(data.length > 0) {
                    formGMHSTongket.submission =  {
                        data: {
                            ...data[0]
                        }
                    };
                }

            }).fail(function() {
                notifiToClient("Red", "Lỗi lấy dữ liệu phiếu gây mê hô sức");
            }).always(function() {
                hideLoaderIntoWrapId("formPhauthuatGMHSTongketphieuWrap")
            })
        });
    })
    $("#lanphauthuat_gmhs_luutttong").click(function() {
        var idButton = this.id
        showSelfLoading(idButton);
        formGMHSTongket.emit("checkValidity");
        if (!formGMHSTongket.checkValidity(null, false, null, true)) {
            hideSelfLoading(idButton);
            return;
        }
        var actionUrl = 'cmu_post';
        var dataSubmit = formGMHSTongket.submission.data;
        var url = [
            singletonObject.dvtt,
            lanPhauThuatObject.ID,
            dataSubmit.TONGMATMAU,
            dataSubmit.TONGNUOCTIEU,
            dataSubmit.TONGNHIPTHO,
            dataSubmit.TONGME,
            dataSubmit.PHUONGPHAPME,
            dataSubmit.TENTHUOCME,
            dataSubmit.NHANXET,
            dataSubmit.MAYGAYME,
            dataSubmit.QUANSAT,
            'CMU_LPT_GAYMEHOISUC_UPDATE'
        ];
        $.post(actionUrl, {
            url: url.join('```')
        }).done(function (data) {
            if (data > 0){
                notifiToClient("Green", "Cập nhật thành công");
            } else {
                notifiToClient("Red", "Cập nhật thất bại");
            }
        }).fail(function() {
            notifiToClient('Red', 'Lỗi cập nhật');
        }).always(function () {
            hideSelfLoading(idButton);
        });
    })


    $("#lanphauthuat_icdchandoan").keypress(function(e) {
        var icd = $("#lanphauthuat_icdchandoan").val();
        if(e.keyCode == 13 && icd.trim() != "") {
            getMotabenhly(icd.toUpperCase(), function(data) {
                var splitIcd = data.split("!!!");
                $("#lanphauthuat_chandoan").val(splitIcd[1]);
                $("#lanphauthuat_icdchandoan").val(icd.toUpperCase())
            })
        }
    })
    $("#lanphauthuat_gmhs_action_phathuoc").click(function() {
        var thuoc = $("#lanphauthuat_gmhs_dichtruyen_table").find("input[type=checkbox]:checked")
        var dichtruyen = $("#lanphauthuat_gmhs_thuoc_table").find("input[type=checkbox]:checked")
        if(thuoc.length == 0 || dichtruyen.length == 0) {
            return notifiToClient("Red", "Chưa chọn thuốc/dịch truyền");
        }
        var idButton = this.id
        showSelfLoading(idButton);
        var dataThuoc = [];
        var tenphathuoc = [];
        thuoc.each(function() {
            var row = $(this).closest('tr');
            dataThuoc.push({
                MAVATTU: row.data('MA_VAT_TU'),
            })
            tenphathuoc.push(row.data('TEN_THUOC'));
        })
        dichtruyen.each(function() {
            var row = $(this).closest('tr');
            dataThuoc.push({
                MAVATTU: row.data('MA_VAT_TU'),
            })
            tenphathuoc.push(row.data('TEN_THUOC'));
        })
        $.post("cmu_post_CMU_LANPHAUTHUAT_PHATHUOC_INS", {
            url: [singletonObject.dvtt, lanPhauThuatObject.ID, JSON.stringify(dataThuoc), singletonObject.userId].join("```")
        }).done(function(data) {
            if(data > 0) {
                notifiToClient("Green", "Thêm thành công");
                danhSachDichTruyenGMHS.push({
                    NGHIEP_VU: "phathuoc",
                    KHO: 0,
                    MA_VAT_TU: 0,
                    TEN_THUOC: tenphathuoc.join("+"),
                    BOMTIEMDIEN: $("#lanphauthuat_gmhs_bomtiemdien").val(),
                    TOCDOTRUYEN: "",
                    ID_PHATHUOC: data,
                    DUNG_TICH: 0,
                    DON_GIA: 0,
                    GHI_CHU: '',
                });
                loadDSDichTruyenGMHS();
                $("#lanphauthuat_gmhs_bomtiemdien").val(0)
            } else {
                notifiToClient("Red", "Có lỗi xảy ra");
            }
        }).always(function() {
            hideSelfLoading(idButton);
        }).fail(function() {
            notifiToClient("Red", "Có lỗi xảy ra");
        })
    })
    $("#lanphauthuat_gmhs_themtreem").click(function() {
        initFormioTreem({})
        showOrHideByClass('modalPhauthuatTreemFooter', 'edit', 'add')
    })
    $("#phauthuatTreemLuu").click(function() {
        var allData = getAllRowDataJqgrid("lanphauthuat_gmhs_dstreem");
        var list = $("#lanphauthuat_gmhs_dstreem");
        var id = list.jqGrid('getGridParam', 'selrow')
        if(allData.length > 0 && !id) {
            return notifiToClient("Red", "Chỉ thêm được 1 trẻ em");
        }
        formThongtintreem.emit("checkValidity");
        if (!formThongtintreem.checkValidity(null, false, null, true)) {
            return;
        }
        var dataSubmit = formThongtintreem.submission.data;
        if(id) {
            list.jqGrid('setRowData', id, dataSubmit);
        } else {
            list.jqGrid('addRowData', 1, dataSubmit);
        }
        list.trigger("reloadGrid");
        $("#modalPhauthuatTreem").modal("hide");
    })
    $("#phauthuatTreemXoa").click(function() {
        confirmToClient(MESSAGEAJAX.CONFIRM, function() {
            $("#lanphauthuat_gmhs_dstreem").jqGrid('delRowData', $("#lanphauthuat_gmhs_dstreem").jqGrid('getGridParam', 'selrow'));
        })
    })

    $("#lanphauthuat_gmhs_themtruyenmau").click(function() {
        initFormioTruyenmau({})
        showOrHideByClass('modalPhauthuatTruyenmauFooter', 'edit', 'add')
    })
    $("#phauthuatTruyenmauThem").click(function() {
        var allData = getAllRowDataJqgrid("lanphauthuat_gmhs_dstruyenmau");
        var dataSubmit =  formThongtintruyenmau.submission.data;
        var list = $("#lanphauthuat_gmhs_dstruyenmau");
        var id = list.jqGrid('getGridParam', 'selrow')
        var exist = allData.filter(function(item) {
            return item.MANHOM == dataSubmit.MANHOM
        })
        if(exist.length > 0) {
            return notifiToClient("Red", "Nhóm máu đã được thêm");
        }
        formThongtintruyenmau.emit("checkValidity");
        if (!formThongtintruyenmau.checkValidity(null, false, null, true)) {
            return;
        }

        dataSubmit = {
            ...dataSubmit,
            TENNHOMMAU: getTennhommau(dataSubmit.MANHOM)
        }
        list.jqGrid('addRowData', allData.length + 1, dataSubmit);
        list.trigger("reloadGrid");
        $("#modalPhauthuatTruyenmau").modal("hide");
    })
    $("#phauthuatTruyenmauLuu").click(function() {
        var allData = getAllRowDataJqgrid("lanphauthuat_gmhs_dstruyenmau");
        var dataSubmit =  formThongtintruyenmau.submission.data;
        var list = $("#lanphauthuat_gmhs_dstruyenmau");
        var id = list.jqGrid('getGridParam', 'selrow')
        var rowData = getThongtinRowSelected("lanphauthuat_gmhs_dstruyenmau")
        var exist = allData.filter(function(item) {
            return item.MANHOM == dataSubmit.MANHOM
        })
        if(exist.length > 0 && exist[0].ID_TRUYENMAU != rowData.ID_TRUYENMAU) {
            return notifiToClient("Red", "Nhóm máu đã được thêm");
        }
        formThongtintruyenmau.emit("checkValidity");
        if (!formThongtintruyenmau.checkValidity(null, false, null, true)) {
            return;
        }

        dataSubmit = {
            ...dataSubmit,
            TENNHOMMAU: getTennhommau(dataSubmit.MANHOM)
        }
        list.jqGrid('setRowData', id,dataSubmit);
        list.trigger("reloadGrid");
        $("#modalPhauthuatTruyenmau").modal("hide");
    })
    $("#phauthuatTruyenmauXoa").click(function() {
        confirmToClient(MESSAGEAJAX.CONFIRM, function() {
            $("#lanphauthuat_gmhs_dstruyenmau").jqGrid('delRowData', $("#lanphauthuat_gmhs_dstruyenmau").jqGrid('getGridParam', 'selrow'));
        })
    })
    $("#lanphauthuat_gmhs_action_kyso").click(function() {
        $("#wrap_canvas_doc").html("<canvas id='myChart' width='420' height='300'></canvas>")
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, lanPhauThuatObject.ID, 'CMU_LPT_GMHS_SEL'])).done(function(data){
            if(data.length > 0){
                var charts = [];
                var ngaykethuc = "";
                var thoigianbatdaume = "";
                var thoigianbatdaumo = "";
                var batdaume = [];
                for(var p = 0; p < Math.ceil(data.length/13); p++){
                    var dataMach = [];
                    var dataTamtruong = [];
                    var dataTamthu = [];
                    var indexDatnoikq = -1;
                    var indexMo = -1;
                    var indexKetThuc = -1;
                    var others = {
                        batdaugayte: -1,
                        tetaicho: -1,
                        tengoaimangcung: -1,
                        tienme: -1,
                        metinhmach: -1,
                        metinhmachtetaicho: -1,
                    }
                    for(var i = 0; i < 13; i++){
                        var item = data[i+p*13];
                        if(item){
                            dataMach.push(item.MACH);
                            dataTamtruong.push(item.HUYETAP_DUOI) ;
                            dataTamthu.push(item.HUYENAP_TREN);
                            if (item.HOATDONG == '1') {
                                indexMo = i;
                                thoigianbatdaumo = item.THOI_GIAN_BLOCK;
                                batdaume.push(moment(item.THOI_GIAN_BLOCK, ['DD/MM/YYYY HH:mm']))
                            } else if(item.NGAY_BAT_DAU_ME == item.THOI_GIAN_BLOCK) {
                                thoigianbatdaume = item.THOI_GIAN_BLOCK;
                            }else if(item.HOATDONG == '2'){
                                indexDatnoikq = i;
                                batdaume.push(moment(item.THOI_GIAN_BLOCK, ['DD/MM/YYYY HH:mm']))
                            } else if(item.HOATDONG == '3'){
                                others.batdaugayte = i;
                                batdaume.push(moment(item.THOI_GIAN_BLOCK, ['DD/MM/YYYY HH:mm']))
                            } else if(item.HOATDONG == '4'){
                                others.tetaicho = i;
                                batdaume.push(moment(item.THOI_GIAN_BLOCK, ['DD/MM/YYYY HH:mm']))
                            } else if(item.HOATDONG == '5'){
                                others.tengoaimangcung = i;
                                batdaume.push(moment(item.THOI_GIAN_BLOCK, ['DD/MM/YYYY HH:mm']))
                            } else if(item.HOATDONG == '6'){
                                others.tienme = i;
                                batdaume.push(moment(item.THOI_GIAN_BLOCK, ['DD/MM/YYYY HH:mm']))
                            } else if(item.HOATDONG == '7'){
                                others.metinhmach = i;
                                batdaume.push(moment(item.THOI_GIAN_BLOCK, ['DD/MM/YYYY HH:mm']))
                            } else if(item.HOATDONG == '8'){
                                others.metinhmachtetaicho = i;
                                batdaume.push(moment(item.THOI_GIAN_BLOCK, ['DD/MM/YYYY HH:mm']))
                            } else if(item.NGAY_KET_THUC_MO == item.THOI_GIAN_BLOCK) {
                                indexKetThuc = i;
                                ngaykethuc = item.NGAY_KET_THUC_MO;
                            }
                        }
                    }
                    vebieudomachhuyetap(dataMach, dataTamtruong, dataTamthu, indexMo, indexDatnoikq, indexKetThuc, others);
                    var image = $("#myChart").get(0).toDataURL("image/png").replace("data:image/png;base64,", "")
                    charts.push("");
                    $.ajax({
                        url: "cmu_post_CMU_LANPHAUTHUAT_CHART_INS",
                        type: "POST",
                        data: {
                            url: [singletonObject.dvtt, lanPhauThuatObject.ID, p, image].join("```"),
                        },
                        async: false
                    })
                }
                var tenkhoa = '';
                singletonObject.danhsachphongban.map(function(item) {
                    if(item.MAKHOA == lanPhauThuatObject.KHOA){
                        tenkhoa = item.TENKHOA;
                    }
                })
                var tenkhoathuchien = '';
                singletonObject.danhsachphongban.map(function(item) {
                    if(item.MAKHOA == lanPhauThuatObject.KHOA_THUC_HIEN){
                        tenkhoathuchien = item.TENKHOA;
                    }
                })
                var loaiPhauthuat = ['Dạ dày đầy/Cấp cứu: <b>không</b>', '<b><u>Dạ dày đầy</u></b>/Cấp cứu',
                    'Dạ dày đầy/<b><u>Cấp cứu </u></b>', '<b>Dạ dày đầy/Cấp cứu</b>']
                var arrayTacdung = [' ', 'Tốt', 'Trung bình', 'Xấu']
                var stringNgaykettuc = ngaykethuc.split(" ")[0].split("/")
                batdaume =     batdaume.sort(function(a,b){ return a -b;} )
                var dateMo = moment(batdaume[0], ['DD/MM/YYYY HH:mm']);
                var dateKetthuc = moment(ngaykethuc, ['DD/MM/YYYY HH:mm']);

                var diffInMinutes = dateKetthuc.diff(dateMo, 'minutes');
                var textTongthoigianme = "";
                if(diffInMinutes < 60) {
                    textTongthoigianme = diffInMinutes + " phút";
                } else {
                    var hours = Math.floor(diffInMinutes / 60);
                    var minutes = diffInMinutes % 60;
                    textTongthoigianme = ('0'+hours).slice('-2') + " giờ " +  ('0'+minutes).slice('-2') + " phút";
                }
                console.log("tsettt")
                var url = "inphieugaymehoisuc_v2?url="
                    + convertArray([
                        singletonObject.dvtt,
                        thongtinhsba.thongtinbn.MA_BENH_NHAN,
                        thongtinhsba.thongtinbn.STT_BENHAN,
                        thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                        thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                        thongtinhsba.thongtinbn.TUOI == 0? thongtinhsba.thongtinbn.TUOI_HT: thongtinhsba.thongtinbn.TUOI,
                        thongtinhsba.thongtinbn.GIOI_TINH_HT
                    ])+
                    "&idphieu="+ lanPhauThuatObject.ID
                    +"&sovaovien="+thongtinhsba.thongtinbn.SOVAOVIEN
                    +"&sobenhan="+thongtinhsba.thongtinbn.SOBENHAN
                    +"&chandoan="//lanPhauThuatObject.CHAN_DOAN
                    +"&ngayphauthuat="+ (lanPhauThuatObject.NGAY_THUC_HIEN.split(" ")[0])
                    +"&tongsotrang="+ (Math.ceil(data.length/13))
                    +"&thongtinphauthuat="+ encodeURIComponent(JSON.stringify({
                        "mallampati": lanPhauThuatObject.MALLAMPATI+"",
                        "diung": lanPhauThuatObject.DI_UNG_THUOC == 1 ? "Có" : lanPhauThuatObject.DI_UNG_THUOC == 2 ? "Không" : "",
                        "tiensu": lanPhauThuatObject.TIEN_SU_THUOC,
                        "batthuong": lanPhauThuatObject.BAT_THUONG_CLS,
                        "asa": lanPhauThuatObject.ASA+"",
                        "tenkhoa": tenkhoa+"",
                        "khoathuchien": (tenkhoathuchien+""),
                        "loaiphauthuat": loaiPhauthuat[lanPhauThuatObject.LOAI_PHAU_THUAT],
                        "ketluan": data[0].NHANXET,
                        "tongthoigianme": textTongthoigianme,
                        "ngaythangnam": "Ngày " + stringNgaykettuc[0] + " tháng " + stringNgaykettuc[1] + " năm " + stringNgaykettuc[2],
                        "bacsigaymehs": lanPhauThuatObject.TEN_BAC_SI_GAY_ME,
                        charts: charts,
                        phuongphappt: lanPhauThuatObject.PHUONG_PHAP_PHAU_THUAT?lanPhauThuatObject.PHUONG_PHAP_PHAU_THUAT:"",
                        phuongphapvocam: lanPhauThuatObject.PHUONG_PHAP_VO_CAM,
                        tuthe: lanPhauThuatObject.TU_THE,
                        cannang: thongtinhsba.thongtinbn.CANNANG ? thongtinhsba.thongtinbn.CANNANG : "",
                        chieucao: thongtinhsba.thongtinbn.CHIEUCAO? thongtinhsba.thongtinbn.CHIEUCAO : "",
                        bsphauthuat:  lanPhauThuatObject.EKIPPHAUTHUAT,
                        bsgayme: lanPhauThuatObject.EKIPGAYME,
                        tienme: lanPhauThuatObject.TIEN_ME,
                        tacdung: lanPhauThuatObject.TAC_DUNG == null? " ": arrayTacdung[lanPhauThuatObject.TAC_DUNG],
                        mayphauthuat: data[0].MAYGAYME,
                        chandoan: lanPhauThuatObject.CHAN_DOAN,
                        nhommau: lanPhauThuatObject.NHOMMAU,
                        tongmatmau: data[0].TONGMATMAU,
                        tongnuoctieu: data[0].TONGNUOCTIEU,
                        tongnhiptho: data[0].TONGNHIPTHO,
                        tongme: data[0].TONGME,
                        phuongphapme: data[0].PHUONGPHAPME,
                        tenthuocme: data[0].TENTHUOCME,
                        khangthe: lanPhauThuatObject.KHANGTHE
                    }));

                previewAndSignPdfDefaultModal({
                    url: url,
                    idButton: 'gaymehoisuc_kyso_action',
                }, function(){
                    $("#gaymehoisuc_kyso_action").click(function() {
                        // getXMLHSBAChung({
                        //     fileName: "XML_PHIEU_GAY_ME_HOI_SUC",
                        //     method: "sendXMLNoiTruChungCmu",
                        //     maBenhNhan: thongtinhsba.thongtinbn.MABENHNHAN,
                        //     soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                        //     soBenhAn: thongtinhsba.thongtinbn.SOBENHAN,
                        //     bant: singletonObject.bant == "" ? 0 : singletonObject.bant,
                        //     sttDotDieuTri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                        //     soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                        //     objectGet: {
                        //         SOGIUONG: thongtinhsba.thongtinbn.SOGIUONG,
                        //         CHANDOAN: thongtinhsba.thongtinbn.ICD_HT,
                        //         TENKHOA: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                        //         MALLAMPATI: lanPhauThuatObject.MALLAMPATI+"",
                        //         DIUNG: lanPhauThuatObject.DI_UNG_THUOC == 1 ? "Có" : lanPhauThuatObject.DI_UNG_THUOC == 2 ? "Không" : "",
                        //         TIENSU: lanPhauThuatObject.TIEN_SU_THUOC,
                        //         BATTHUONG: lanPhauThuatObject.BAT_THUONG_CLS,
                        //         ASA: lanPhauThuatObject.ASA+"",
                        //         KHOATHUCHIEN: tenkhoathuchien+"",
                        //         LOAIPHAUTHUAT: loaiPhauthuat[lanPhauThuatObject.LOAI_PHAU_THUAT],
                        //         KETLUAN: data[0].NHANXET,
                        //         TONGTHOIGIANME: textTongthoigianme,
                        //         NGAYTHANGNAM: "Ngày " + stringNgaykettuc[0] + " tháng " + stringNgaykettuc[1] + " năm " + stringNgaykettuc[2],
                        //         BACSIGAYME: lanPhauThuatObject.TEN_BAC_SI_GAY_ME,
                        //         CHARTS: charts,
                        //         PHUONGPHAPPT: lanPhauThuatObject.PHUONG_PHAP_PHAU_THUAT?lanPhauThuatObject.PHUONG_PHAP_PHAU_THUAT:"",
                        //         PHUONGPHAPVOCAM: lanPhauThuatObject.PHUONG_PHAP_VO_CAM,
                        //         TUTHE: lanPhauThuatObject.TU_THE,
                        //         CANNANG: thongtinhsba.thongtinbn.CANNANG ? thongtinhsba.thongtinbn.CANNANG : "",
                        //         CHIEUCAO: thongtinhsba.thongtinbn.CHIEUCAO? thongtinhsba.thongtinbn.CHIEUCAO : "",
                        //         BACSIPHAUTHUAT:  lanPhauThuatObject.EKIPPHAUTHUAT,
                        //         BACSIGAYME: lanPhauThuatObject.EKIPGAYME,
                        //         TIENME: lanPhauThuatObject.TIEN_ME,
                        //         TACDUNG: lanPhauThuatObject.TAC_DUNG == 0? "Không": ("+"+lanPhauThuatObject.TAC_DUNG),
                        //         MAYPHAUTHUAT: data[0].MAYGAYME,
                        //         NHOMMAU: lanPhauThuatObject.NHOMMAU,
                        //         TONGMATMAU: data[0].TONGMATMAU,
                        //         TONGNUOCTIEU: data[0].TONGNUOCTIEU,
                        //         TONGNHIPTHO: data[0].TONGNHIPTHO,
                        //         TONGME: data[0].TONGME,
                        //         PHUONGPHAPME: data[0].PHUONGPHAPME,
                        //         TENTHUOCME: data[0].TENTHUOCME,
                        //         KHANGTHE: lanPhauThuatObject.KHANGTHE
                        //     },
                        //     functionGet: "CMU_LPT_GMHS_SEL",
                        //     arrayGet: convertArray([singletonObject.dvtt, lanPhauThuatObject.ID])
                        // }, function(response) {
                        //     console.log(response)
                        //     console.log("1234")
                        //     if ("error" in response) {
                        //         notifiToClient("Red", "Có lỗi ký số XML: " + response.message);
                        //         return;
                        //     }
                        //     kySoChung({
                        //         dvtt: singletonObject.dvtt,
                        //         userId: singletonObject.userId,
                        //         url: url,
                        //         loaiGiay: "PHIEU_NOITRU_GAYMEHOISUC",
                        //         maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                        //         soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                        //         soPhieuDichVu: lanPhauThuatObject.ID,
                        //         // nghiepVu: "",
                        //         soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                        //         soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                        //         keyword: "BÁC SĨ GÂY MÊ HỒI SỨC",
                        //         fileName: "Phiếu gây mê hồi sức: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                        //         signXML: response.contentFile.buffer
                        //     }, function(dataKySo) {
                        //         $("#modalPreviewAndSignPDF").modal("hide");
                        //         loadGayMeHoiSuc();
                        //     });
                        // });
                        kySoChung({
                            dvtt: singletonObject.dvtt,
                            userId: singletonObject.userId,
                            url: $("#iframePreviewAndSign").attr('src'),
                            loaiGiay: "PHIEU_NOITRU_GAYMEHOISUC",
                            maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                            soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                            soPhieuDichVu: lanPhauThuatObject.ID,
                            // nghiepVu: "",
                            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                            keyword: "BÁC SĨ GÂY MÊ HỒI SỨC",
                            fileName: "Phiếu gây mê hồi sức: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                        }, function(dataKySo) {
                            $("#modalPreviewAndSignPDF").modal("hide");
                            loadGayMeHoiSuc();
                        });
                    });
                });
            } else {
                notifiToClient("Red", "Chưa có dữ liệu !");
            }
        })
    })
    $("#lanphauthuat_gmhs_action_huykyso").click(function() {
        var idButton = this.id
        showSelfLoading(idButton);
        console.log("tetsttt")
        confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
            huykysoFilesign769("PHIEU_NOITRU_GAYMEHOISUC",
                lanPhauThuatObject.ID, singletonObject.userId, singletonObject.dvtt,  thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                    hideSelfLoading(idButton);
                    if(data && data.ERROR) {
                        notifiToClient("Red", "Hủy ký số thất bại");
                    } else {
                        loadGayMeHoiSuc();
                    }

                })
        }, function () {

        })
    })
    $("#lanphauthuat-gmhs_formtheodoi").validate({
        rules: {
            lanphauthuat_gmhs_thoigian: {
                required: true,
                validDateTime: true,
                validDateNgayhientai: true,
                validDateNgaynhapvien: true,
            }
        }
    })
    $("#lanphauthuat_gmhs_thuocdt").validate({
        rules: {
            lanphauthuat_gmhs_thuoc_select: {
                required: true,
                validThuocTontai: true
            },
        }
    })

    $.validator.addMethod("validThuocTontai", function(value, element) {
        if(!value || !$("#lanphauthuat_gmhs_mathuoc").val()) {
            return true
        }
        var response = $.ajax({
            url: 'cmu_post_CMU_LPT_GMHS_CT_THUOCCHECK',
            type: 'POST',
            data: {
                url: [singletonObject.dvtt, lanPhauThuatObject.ID,
                    $("#lanphauthuat_gmhs_mathuoc").val(),
                    $("#lanphauthuat_gmhs_nghiepvuthuoc").val()
                ].join("```")
            },
            async: false
        }).responseText
        return response == 0;
    }, "Thuốc/dịch truyền đã tồn tại trong tab vật tư");
    $.extend({
        getFormioTongketGMHS: function() {
            return formGMHSTongket;
        },
        themThuocdichtruyentutoamauGMHS: function(list) {
            showLoaderIntoWrapId("tableMauDuocDichtruyenPTWrap")
            var error = []
            for(var i = 0; i < list.length; i++) {
                var item = list[i];
                var isExist = kiemTraThuocDichTruyenDaCo(item.NGHIEPVU, item.MAKHOVATTU, item.MAVATTU);
                if (isExist) {
                    error.push({
                        ...item,
                        ERROR: "Thuốc/dịch truyền đã tồn tại"
                    });
                    continue;
                }
                var response = $.ajax({
                    url: 'cmu_getlist?url='+convertArray([
                        singletonObject.dvtt,
                        item.MAKHOVATTU,
                        item.MAVATTU,
                        "CMU_KTRAVATTU_TONKHO"
                    ]),
                    method: "GET",
                    async: false
                }).responseText;
                response = JSON.parse(response);
                if(response.length == 0) {
                    error.push({
                        ...item,
                        ERROR: "Thuốc/dịch truyền không còn tồn kho"
                    });
                    continue;
                }
                if(item.LOAI == 'THUOC') {
                    danhSachThuocGMHS.push({
                        NGHIEP_VU: item.NGHIEPVU,
                        KHO: item.MAKHOVATTU,
                        MA_VAT_TU: item.MAVATTU,
                        TEN_THUOC: item.TENVATTU,
                        DUNG_TICH: item.HAMLUONG,
                        DONVI_HAMLUONG: item.DVT,
                        DON_GIA: response[0].DONGIA,
                        GHI_CHU: item.CACHSUDUNG,
                    });

                } else {
                    danhSachDichTruyenGMHS.push({
                        NGHIEP_VU: item.NGHIEPVU,
                        KHO: item.MAKHOVATTU,
                        MA_VAT_TU: item.MAVATTU,
                        TEN_THUOC: item.TENVATTU,
                        BOMTIEMDIEN: 0,
                        TOCDOTRUYEN: "",
                        DONVI_HAMLUONG: item.DVT,
                        ID_PHATHUOC: 0,
                        DUNG_TICH: item.HAMLUONG,
                        DON_GIA: response[0].DONGIA,
                        GHI_CHU: item.CACHSUDUNG,
                        LINETRUYEN: item.LINETRUYEN,
                        TOCDOTRUYEN_BTD: item.TOCDOTRUYEN_BTD,
                        GHI_CHU_TRUYENDICH: item.GHI_CHU_TRUYENDICH,
                    });
                }


            }
            if(error.length > 0) {
                $.alert({
                    title: 'Dữ liệu không hợp lệ!',
                    content: '<table id="cmu_ngoaitru_kt_dulieu_tg" class="jqx-grid-cell-wrap"></table>',
                    type: 'red',
                    boxWidth: '900px',
                    useBootstrap: false,
                    escapeKey: true,
                    closeIcon: true,
                    typeAnimated: true,
                    onContentReady: function () {
                        $("#cmu_ngoaitru_kt_dulieu_tg").jqGrid({
                            datatype: 'local',
                            //regional: 'en', // this is default
                            data: error,
                            rownumbers: true,
                            height: 400,
                            colModel: [
                                {name: 'MAVATTU', label: 'Mã vật tư', width: 150},
                                {name: 'TENVATTU', label: 'Tên vật tư', width: 250,},
                                {name: 'ERROR', label: 'Lỗi', width: 400, align: 'center'},
                            ],
                        });
                    }
                });
            } else {
                notifiToClient("Green", "Thêm thành công");
            }
            $("#modalMauthuocdichtruyenPTJSON").modal("hide")
            hideLoaderIntoWrapId("tableMauDuocDichtruyenPTWrap")
            loadDSThuocGMHS();
            loadDSDichTruyenGMHS();
        }
    })

    // ************** FUNCTION **************
    combgridTenICD("lanphauthuat_chandoan", function(item) {
        $("#lanphauthuat_icdchandoan").val(item.ICD.toUpperCase());
        $("#lanphauthuat_chandoan").val(item.MO_TA_BENH_LY);
    });
    function instanceGridLanPhauThuatGMHS() {
        if (!$("#lanphauthuat_gmhs_jqgrid")[0].grid) {
            $("#lanphauthuat_gmhs_jqgrid").jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 450,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {
                        name: "KYSO",
                        label: "Ký số",
                        align: 'left',
                        width: 100,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.KEYSIGN) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: green">Đã ký</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: red">Chưa ký</span>';
                            }
                        }
                    },
                    {label: "ID_GMHS", name: 'ID_GMHS', index: 'ID_GMHS', width: 10, hidden: true},
                    {label: "ID_GMHS_CT", name: 'ID_GMHS_CT', index: 'ID_GMHS_CT', width: 10, hidden: true},
                    {label: "THOI_GIAN_BLOCK", name: 'THOI_GIAN_BLOCK', index: 'THOI_GIAN_BLOCK', width: 10, hidden: true},
                    {label: "NGAY_BAT_DAU_ME", name: 'NGAY_BAT_DAU_ME', index: 'NGAY_BAT_DAU_ME', width: 10, hidden: true},
                    {label: "NGAY_KET_THUC_MO", name: 'NGAY_KET_THUC_MO', index: 'NGAY_KET_THUC_MO', width: 10, hidden: true},
                    {label: "ALDMP", name: 'ALDMP', index: 'ALDMP', width: 300, hidden: true},
                    {label: "LOAI_CUSTOM", name: 'LOAI_CUSTOM', index: 'LOAI_CUSTOM', width: 300, hidden: true},
                    {label: "Mạch", name: 'MACH', index: 'MACH', width: 100, hidden: true},
                    {label: "T.Thu", name: 'HUYENAP_TREN', index: 'HUYENAP_TREN', width: 100, hidden: true},
                    {label: "T.Trương", name: 'HUYETAP_DUOI', index: 'HUYETAP_DUOI', width: 100, hidden: true},
                    {label: "N.Độ", name: 'NHIET_DO', index: 'NHIET_DO', width: 100, hidden: true},
                    {label: "M.Máu", name: 'MAT_MAU', index: 'MAT_MAU', width: 100, hidden: true},
                    {label: "N.Tiểu", name: 'NUOC_TIEU', index: 'NUOC_TIEU', width: 100, hidden: true},
                    {label: "N.Thở", name: 'NHIP_THO', index: 'NHIP_THO', width: 100, hidden: true},
                    {label: "TTLT", name: 'TTLT', index: 'TTLT', width: 100, hidden: true},
                    {label: "FeCO2", name: 'FECO2', index: 'FECO2', width: 100, hidden: true},
                    {label: "Áp lực", name: 'AP_LUC', index: 'AP_LUC', width: 100, hidden: true},
                    {label: "SpO2", name: 'SPO2', index: 'SPO2', width: 100, hidden: true},
                    {label: "FiO2", name: 'FIO2', index: 'FIO2', width: 100, hidden: true},
                    {label: "Quan sát", name: 'QUAN_SAT', index: 'QUAN_SAT', width: 100, hidden: true},
                    {label: "BAT_DAU_MO", name: 'BAT_DAU_MO', index: 'BAT_DAU_MO', width: 100, hidden: true},
                    {label: "DAT_NOI_KHIQUAN", name: 'DAT_NOI_KHIQUAN', index: 'DAT_NOI_KHIQUAN', width: 100, hidden: true},
                    {label: "BATDAUGAYTE", name: 'BATDAUGAYTE', index: 'BATDAUGAYTE', width: 100, hidden: true},
                    {label: "TETAICHO", name: 'TETAICHO', index: 'TETAICHO', width: 100, hidden: true},
                    {label: "TENGOAIMANGCUNG", name: 'TENGOAIMANGCUNG', index: 'TENGOAIMANGCUNG', width: 100, hidden: true},
                    {label: "TIENME", name: 'TIENME', index: 'TIENME', width: 100, hidden: true},
                    {label: "HOATDONG", name: 'HOATDONG', index: 'HOATDONG', width: 100, hidden: true},
                    {label: "LOCK_PHIEU", name: 'LOCK_PHIEU', index: 'LOCK_PHIEU', width: 100, hidden: true},
                    {label: "NGUOI_TAO", name: 'NGUOI_TAO', index: 'NGUOI_TAO', width: 100, hidden: true},
                    {label: "NGAY_TAO", name: 'NGAY_TAO', index: 'NGAY_TAO', width: 100, hidden: true},
                    {label: "TEN_NGUOI_TAO", name: 'TEN_NGUOI_TAO', index: 'TEN_NGUOI_TAO', width: 100, hidden: true},
                    {label: "Thời gian", name: 'THOI_GIAN_BLOCK_HT', index: 'THOI_GIAN_BLOCK_HT', width: 200,
                        formatter: function (cellvalue, options, rowObject) {
                            if (rowObject.NGAY_BAT_DAU_ME == rowObject.THOI_GIAN_BLOCK) {
                                return '<i class="fa fa-low-vision text-primary"></i> ' + rowObject.THOI_GIAN_BLOCK + " (Mê)";
                            } else if(rowObject.HOATDONG == '1'){
                                return '<i class="fa fa-bolt text-success"></i> ' + rowObject.THOI_GIAN_BLOCK + " (Mổ)";
                            } else if(rowObject.HOATDONG == '2'){
                                return '<i class="fa fa-hashtag text-info"></i> ' + rowObject.THOI_GIAN_BLOCK + " (Đặt nội khí quản)";
                            } else if(rowObject.HOATDONG == '3'){
                                return '<i class="fa fa-arrow-right text-primary"></i> ' + rowObject.THOI_GIAN_BLOCK + " (Bắt đầu gây tê)";
                            } else if(rowObject.HOATDONG == '4'){
                                return '<i class="fa fa-arrow-circle-o-right text-primary"></i> ' + rowObject.THOI_GIAN_BLOCK + " (Tê tại chỗ)";
                            } else if(rowObject.HOATDONG == '5'){
                                return '<i class="fa fa-arrows text-primary"></i> ' + rowObject.THOI_GIAN_BLOCK + " (Tê ngoài màng cứng)";
                            } else if(rowObject.HOATDONG == '6'){
                                return '<i class="fa fa-arrows-h text-primary"></i> ' + rowObject.THOI_GIAN_BLOCK + " (Tiền mê)";
                            } else if(rowObject.HOATDONG == '7'){
                                return '<i class="fa fa-arrows-h text-primary"></i> ' + rowObject.THOI_GIAN_BLOCK + " (Mê tĩnh mạch)";
                            } else if(rowObject.HOATDONG == '8'){
                                return '<i class="fa fa-arrows-h text-primary"></i> ' + rowObject.THOI_GIAN_BLOCK + " (Mê tĩnh mạch + Tê tại chỗ)";
                            } else if(rowObject.NGAY_KET_THUC_MO == rowObject.THOI_GIAN_BLOCK) {
                                return '<i class="fa fa-stop-circle text-purple"></i> ' + rowObject.THOI_GIAN_BLOCK + " (Kết thúc)";
                            } else {
                                return rowObject.THOI_GIAN_BLOCK;
                            }
                        },
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            if (rawObject.NGAY_BAT_DAU_ME == rawObject.THOI_GIAN_BLOCK) {
                                return 'style="color:#007bff;font-weight:bold;white-space: normal;"';
                            } else if (rawObject.HOATDONG == '1') {
                                return 'style="color:#28a745; font-weight:bold;white-space: normal;"';
                            } else if (rawObject.HOATDONG == '2') {
                                return 'style="color:#17a2b8;font-weight:bold;white-space: normal;"';
                            } else if (rawObject.HOATDONG > 3 && rawObject.HOATDONG < 9) {
                                return 'style="color:#17a2b8;font-weight:bold;white-space: normal;"';
                            } else if(rawObject.NGAY_KET_THUC_MO == rawObject.THOI_GIAN_BLOCK) {
                                return 'style="color:#222222;font-weight:bold;white-space: normal;"';
                            } else {
                                return 'style="white-space: normal;"';
                            }
                        },
                    },
                    {label: "Chỉ số 1", name: 'CHISO1', index: 'CHISO1', width: 150,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "Chỉ số 2", name: 'CHISO2', index: 'CHISO2', width: 150,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "Chỉ số 3", name: 'CHISO3', index: 'CHISO3', width: 200,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "Thuốc", name: 'DANH_SACH_THUOC', index: 'DANH_SACH_THUOC', width: 250,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "Dịch truyền", name: 'DANH_SACH_DICH_TRUYEN', index: 'DANH_SACH_DICH_TRUYEN', width: 250,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "Người tạo", name: 'NGUOI_TAO_HT', index: 'NGUOI_TAO_HT', width: 150,
                        formatter: function (cellvalue, options, rowObject) {
                            return rowObject.TEN_NGUOI_TAO + " (" + rowObject.NGAY_TAO + ")";
                        },
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    {name: "KEYSIGN", label: "KEYSIGN", align: 'center', width: 150, hidden: true},
                ],
                rowNum: 1000,
                caption: "",
                gridComplete: function() {

                },
                onRightClickRow: function(id) {
                    if (id) {
                        $.contextMenu('destroy', '#lanphauthuat_gmhs_jqgrid tr');
                        var rowData = getThongtinRowSelected("lanphauthuat_gmhs_jqgrid");
                        var allData = getAllRowDataJqgrid("lanphauthuat_gmhs_jqgrid");
                        var timeLast = allData[allData.length - 1].THOI_GIAN_BLOCK;
                        var items = {
                        }
                        if (rowData.KEYSIGN) {
                            items = {
                                ...items
                            }
                        } else {
                            items = {
                                "chinhsua": {name: '<p><i class="fa fa-pencil-square-o text-success" aria-hidden="true"></i> Chỉnh sửa</p>'},
                                "buocnhay10": {name: '<p><i class="fa fa-clock-o text-primary" aria-hidden="true"></i> Bước nhảy 10 phút</p>'},
                                "buocnhay5": {name: '<p><i class="fa fa-clock-o text-primary" aria-hidden="true"></i> Bước nhảy 5 phút</p>'},
                                "buocnhay1": {name: '<p><i class="fa fa-clock-o text-primary" aria-hidden="true"></i> Bước nhảy 1 phút</p>'},
                                "xoa": {name: '<p><i class="fa fa-trash-o red-text" aria-hidden="true"></i> Xóa</p>'}
                            }
                            if(!(rowData.NGAY_BAT_DAU_ME == rowData.THOI_GIAN_BLOCK) &&
                                !(rowData.NGAY_KET_THUC_MO == rowData.THOI_GIAN_BLOCK) &&
                                !(rowData.BAT_DAU_MO == '1')) {
                                items = {
                                    "batdaumo": {name: '<p><i class="fa fa-bolt text-success" aria-hidden="true"></i> Bắt đầu mổ</p>'},
                                    "chinhsua": {name: '<p><i class="fa fa-pencil-square-o text-success" aria-hidden="true"></i> Chỉnh sửa</p>'},
                                    "buocnhay10": {name: '<p><i class="fa fa-clock-o text-primary" aria-hidden="true"></i> Bước nhảy 10 phút</p>'},
                                    "buocnhay5": {name: '<p><i class="fa fa-clock-o text-primary" aria-hidden="true"></i> Bước nhảy 5 phút</p>'},
                                    "buocnhay1": {name: '<p><i class="fa fa-clock-o text-primary" aria-hidden="true"></i> Bước nhảy 1 phút</p>'},
                                    "xoa": {name: '<p><i class="fa fa-trash-o red-text" aria-hidden="true"></i> Xóa</p>'}
                                }
                            }
                            if(!(rowData.NGAY_BAT_DAU_ME == rowData.THOI_GIAN_BLOCK) &&
                                !(rowData.NGAY_KET_THUC_MO == rowData.THOI_GIAN_BLOCK) &&
                                !(rowData.DAT_NOI_KHIQUAN == '1')) {
                                items = {
                                    "datnoikhiquan": {name: '<p><i class="fa fa-hashtag text-info" aria-hidden="true"></i> Đặt nội khí quản</p>'},
                                    "chinhsua": {name: '<p><i class="fa fa-pencil-square-o text-success" aria-hidden="true"></i> Chỉnh sửa</p>'},
                                    "buocnhay10": {name: '<p><i class="fa fa-clock-o text-primary" aria-hidden="true"></i> Bước nhảy 10 phút</p>'},
                                    "buocnhay5": {name: '<p><i class="fa fa-clock-o text-primary" aria-hidden="true"></i> Bước nhảy 5 phút</p>'},
                                    "buocnhay1": {name: '<p><i class="fa fa-clock-o text-primary" aria-hidden="true"></i> Bước nhảy 1 phút</p>'},
                                    "xoa": {name: '<p><i class="fa fa-trash-o red-text" aria-hidden="true"></i> Xóa</p>'}
                                }
                            }
                        }
                        $.contextMenu({
                            selector: '#lanphauthuat_gmhs_jqgrid tr',
                            reposition : false,
                            callback: function (key, options) {
                                if (key == "chinhsua") {
                                    if(rowData.NGUOI_TAO != singletonObject.userId) {
                                        return notifiToClient("Red", MESSAGEAJAX.PERMISSION);
                                    }
                                    openModalGMHSChiTiet('sua', rowData, null);
                                }
                                if (key == "xoa") {
                                    if(rowData.NGUOI_TAO != singletonObject.userId) {
                                        return notifiToClient("Red", MESSAGEAJAX.PERMISSION);
                                    }
                                    if(rowData.LOCK_PHIEU == 1) {
                                        return notifiToClient("Red", "Phiếu chứa dữ liệu dược đã chốt, không thể xóa");
                                    } else {
                                        xoaLanPhauThuatGMHSChiTiet(rowData.ID_GMHS_CT);
                                    }
                                }
                                if (key == 'buocnhay10') {
                                    var momentObject = moment(timeLast, "DD/MM/YYYY HH:mm").add(10, 'minutes');
                                    openModalGMHSChiTiet('them', rowData, momentObject.format("DD/MM/YYYY HH:mm"));
                                    loadDulieumacdinh(rowData);
                                }
                                if (key == 'buocnhay5') {
                                    var momentObject = moment(timeLast, "DD/MM/YYYY HH:mm").add(5, 'minutes');
                                    openModalGMHSChiTiet('them', rowData, momentObject.format("DD/MM/YYYY HH:mm"));
                                    loadDulieumacdinh(rowData);
                                }
                                if (key == 'buocnhay1') {
                                    var momentObject = moment(timeLast, "DD/MM/YYYY HH:mm").add(1, 'minutes');
                                    openModalGMHSChiTiet('them', rowData, momentObject.format("DD/MM/YYYY HH:mm"));
                                    loadDulieumacdinh(rowData);
                                }
                                if (key == 'batdaumo') {
                                    $.post("cmu_post_CMU_LPT_GMHS_CT_BATDAUMO", {
                                        url: [
                                            singletonObject.dvtt, lanPhauThuatObject.ID, lanPhauThuatObject.ID_GMHS, rowData.ID_GMHS_CT
                                        ].join("```")
                                    }).always(function() {
                                        loadGayMeHoiSuc();
                                    }).done(function(data) {
                                        if(data > 0) {
                                            notifiToClient("Green", MESSAGEAJAX.EDIT_SUCCESS);
                                        } else {
                                            notifiToClient("Red", MESSAGEAJAX.FAIL);
                                        }
                                    }).fail(function() {
                                        notifiToClient("Red", MESSAGEAJAX.ERROR);
                                    })
                                }
                                if (key == 'datnoikhiquan') {
                                    $.post("cmu_post_CMU_LPT_GMHS_CT_DATNOIKQ", {
                                        url: [
                                            singletonObject.dvtt, lanPhauThuatObject.ID, lanPhauThuatObject.ID_GMHS, rowData.ID_GMHS_CT
                                        ].join("```")
                                    }).always(function() {
                                        loadGayMeHoiSuc();
                                    }).done(function(data) {
                                        if(data > 0) {
                                            notifiToClient("Green", MESSAGEAJAX.EDIT_SUCCESS);
                                        } else {
                                            notifiToClient("Red", MESSAGEAJAX.FAIL);
                                        }
                                    }).fail(function() {
                                        notifiToClient("Red", MESSAGEAJAX.ERROR);
                                    })
                                }
                            },
                            items: items
                        });

                    }
                },
                ondblClickRow: function (id) {
                    var ret = getThongtinRowSelected("lanphauthuat_gmhs_jqgrid")
                    openModalGMHSChiTiet('sua', ret, null);
                }
            });
            $("#lanphauthuat_gmhs_jqgrid").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
            loadGayMeHoiSuc();
        }
    }

    function openModalGMHSChiTiet(action, data, ngay) {
        $("#lanphauthuat_gmhs_nghiepvuthuoc").change();
        if(action == "them") {
            $("#lanphauthuat_gmhs_action_them").show();
            $("#lanphauthuat_gmhs_action_luu").hide();
            $("#modalGMHSChiTiet").modal('show');
            $("#lanphauthuat_gmhs_thoigian").val(ngay);
            $("#lanphauthuat_gmhs_nhietdo").val(37);
            $("#lanphauthuat_gmhs_hoatdong").val(0);
            $("#lanphauthuat_gmhs_bomtiemdien").val(0);
            getDanhSachThuocDichTruyen(null);
            loadDSGridTreem({})
            loadDSGridTruyenmau({})
        } else {
            $("#lanphauthuat_gmhs_action_them").hide();
            $("#lanphauthuat_gmhs_action_luu").show();
            $("#lanphauthuat_gmhs_thoigian").val(data.THOI_GIAN_BLOCK);
            $("#lanphauthuat_gmhs_mach").val(data.MACH);
            $("#lanphauthuat_gmhs_tamthu").val(data.HUYENAP_TREN);
            $("#lanphauthuat_gmhs_tamtruong").val(data.HUYETAP_DUOI);
            $("#lanphauthuat_gmhs_nhietdo").val(data.NHIET_DO);
            $("#lanphauthuat_gmhs_matmau").val(data.MAT_MAU);
            $("#lanphauthuat_gmhs_nuoctieu").val(data.NUOC_TIEU);
            $("#lanphauthuat_gmhs_nhiptho").val(data.NHIP_THO);
            $("#lanphauthuat_gmhs_ttlt").val(data.TTLT);
            $("#lanphauthuat_gmhs_feco2").val(data.FECO2);
            $("#lanphauthuat_gmhs_apluc").val(data.AP_LUC);
            $("#lanphauthuat_gmhs_spo2").val(data.SPO2);
            $("#lanphauthuat_gmhs_fio2").val(data.FIO2);
            $("#lanphauthuat_gmhs_loaicustom").val(data.LOAI_CUSTOM);
            $("#lanphauthuat_gmhs_aldmp").val(data.ALDMP);
            $("#lanphauthuat_gmhs_quansat").val(data.QUAN_SAT);
            $("#lanphauthuat_gmhs_hoatdong").val(data.HOATDONG);

            $("#modalGMHSChiTiet").modal('show');
            ID_GMHS_CT = data.ID_GMHS_CT;
            getDanhSachThuocDichTruyen(data.ID_GMHS_CT);
            loadDSGridTreem(data);
            loadDSGridTruyenmau(data);
        }
    }

    function capNhatThuocVaDichTruyen(id_ct) {
        return new Promise(function(resolve, reject) {
            var danhSachThuocAll = mergeThuocDichTruyen();
            var danhSachThuocAllPromise = danhSachThuocAll.map(function(item) {
                return new Promise(function(resolve, reject) {
                    if(!item.ID_GMHS) {
                        // Thuốc mới trong lần phẫu thuật - Thêm mới thuốc vào bảng cmu_lpt_gaymehoisuc_ct_thuoc và cmu_lpt_gaymehoisuc_ct_thuoc_ct
                        $.post("cmu_post", {url:[singletonObject.dvtt, lanPhauThuatObject.ID,
                                lanPhauThuatObject.ID_GMHS,
                                id_ct, item.NGHIEP_VU, item.KHO,
                                item.MA_VAT_TU, item.loai_vat_tu, item.GIA_TRI,
                                item.DUNG_TICH, item.DON_GIA, item.GHI_CHU,
                                singletonObject.userId,
                                item.ID_PHATHUOC? item.ID_PHATHUOC: 0,
                                item.BOMTIEMDIEN? item.BOMTIEMDIEN: 0,
                                item.DONVI_HAMLUONG,
                                item.GHI_CHU_TRUYENDICH,
                                item.LINETRUYEN,
                                item.TOCDOTRUYEN_BTD,
                                item.TOCDOTRUYEN? item.TOCDOTRUYEN: "",
                                "CMU_LPT_GMHS_CT_THUOC_INS"].join("```")}).done(function (data) {
                            if (data > 0) {
                                resolve(data);
                            } else {
                                reject("Invalid data received");
                            }
                        }).fail(function(error) {
                            reject(error);
                        });
                    } else {
                        // Thuốc đã có sẵn trong lần phẫu thuật
                        if(!item.ID_GMHS_CT_THUOC_CT) {
                            // Thêm mới chi tiết thuốc vào bảng cmu_lpt_gaymehoisuc_ct_thuoc_ct - Thêm mới chi tiết GMHS
                            $.post("cmu_post", {url:[singletonObject.dvtt, lanPhauThuatObject.ID, lanPhauThuatObject.ID_GMHS, id_ct, item.ID_GMHS_CT_THUOC
                                    , item.GIA_TRI, item.DUNG_TICH,
                                    item.TOCDOTRUYEN? item.TOCDOTRUYEN: "",
                                    item.DONVI_HAMLUONG,
                                    item.GHI_CHU_TRUYENDICH,
                                    item.LINETRUYEN,
                                    item.TOCDOTRUYEN_BTD,
                                    "CMU_LPT_GMHS_CT_THUOC_CT_INS"].join("```")}).done(function (data) {
                                if (data > 0) {
                                    resolve(data);
                                } else {
                                    reject("Invalid data received");
                                }
                            }).fail(function(error) {
                                reject(error);
                            });
                        } else {
                            // Cập nhật chi tiết thuốc vào bảng cmu_lpt_gaymehoisuc_ct_thuoc_ct - Cập nhật chi tiết GMHS
                            $.post("cmu_post", {url:[singletonObject.dvtt, lanPhauThuatObject.ID, lanPhauThuatObject.ID_GMHS, id_ct, item.ID_GMHS_CT_THUOC
                                    ,item.ID_GMHS_CT_THUOC_CT, item.GIA_TRI, item.DUNG_TICH,
                                    item.TOCDOTRUYEN? item.TOCDOTRUYEN: "",
                                    item.DONVI_HAMLUONG,
                                    item.GHI_CHU_TRUYENDICH,
                                    item.LINETRUYEN,
                                    item.TOCDOTRUYEN_BTD,
                                    "CMU_LPT_GMHS_CT_THUOC_CT_UPD"].join("```")}).done(function (data) {
                                if (data > 0) {
                                    resolve(data);
                                } else {
                                    reject("Invalid data received");
                                }
                            }).fail(function(error) {
                                reject(error);
                            });
                        }
                    }
                });
            });
            Promise.all(danhSachThuocAllPromise)
                .then(function(data) {
                    resolve(data);
                })
                .catch(function(error) {
                    reject(error);
                });
        });
    }

    function themLanPhauThuatGMHS(id_lanphauthuat) {
        //Khi cần tạo nhiều phiều GMHS thì sử dụng hàm này, hiện tại 96001 chỉ tạo 1 phiếu GMHS
        return new Promise(function(resolve, reject) {
            $.post("cmu_post", { url: [singletonObject.dvtt, id_lanphauthuat, "CMU_LPT_GAYMEHOISUC_INS"].join("```") })
                .done(function(data) {
                    if (data > 0) {
                        lanPhauThuatObject.ID_GMHS = data;
                        resolve(data);
                    } else {
                        reject("Invalid data received");
                    }
                })
                .fail(function(error) {
                    reject(error);
                });
        });
    }

    function kiemTraThuocDichTruyenDaCo(NGHIEP_VU, KHO, MA_VAT_TU) {
        //Kiểm tra xem thuốc hoặc dịch truyền đã tồn tại trong danh sách chưa
        // var isExist1 = danhSachDichTruyenGMHS.some(function(item) {
        //     return item.NGHIEP_VU == NGHIEP_VU && item.KHO == KHO && item.MA_VAT_TU == MA_VAT_TU;
        // });F
        var isExist2 = danhSachThuocGMHS.some(function(item) {
            return item.NGHIEP_VU == NGHIEP_VU && item.KHO == KHO && item.MA_VAT_TU == MA_VAT_TU;
        });
        return isExist2;
    }

    function loadDSThuocGMHS() {
        // Load danh sách thuốc và gen ra HTML
        $('#lanphauthuat_gmhs_thuoc_table_body').empty();
        danhSachThuocGMHS.forEach(function(item, index) {
            var newRow = $('<tr>');
            newRow.append($('<td>').html('<input class="gmhs-thuoc-dt-checkbox" type="checkbox">'));
            newRow.append($('<td>').html('<button class="btn btn-danger btn-loading form-control-sm line-height-1 deleteButton" type="button">' +
                '<i class="fa fa-trash-o"></i>' +
                '</button>'));
            if(item.CHOT_DUOC == '0' || item.CHOT_DUOC == null){
                newRow.append($('<td style="text-align: left; vertical-align: middle;">').text(item.TEN_THUOC));
            } else {
                newRow.append($('<td style="text-align: left; vertical-align: middle; color: red">').text(item.TEN_THUOC));
            }
            var inputDVT = $('<input>').attr('data-name', 'DONVI_HAMLUONG').attr('type', 'text').addClass('form-control form-control-sm').val(item.DONVI_HAMLUONG).prop('disabled', item.CHOT_DUOC === 1);
            newRow.append($('<td>').append(inputDVT));
            var inputGiaTri = $('<input>').attr('data-name', 'GIA_TRI').attr('type', 'number').addClass('form-control form-control-sm').val(item.GIA_TRI).prop('disabled', item.CHOT_DUOC === 1);
            newRow.append($('<td>').append(inputGiaTri));

            var inputDungtich = $('<input>').attr('data-name', 'DUNG_TICH').attr('type', 'number').addClass('form-control form-control-sm').val(item.DUNG_TICH).prop('disabled', item.CHOT_DUOC === 1);
            newRow.append($('<td>').append(inputDungtich));

            newRow.data('MA_VAT_TU', item.MA_VAT_TU);
            newRow.data('NGHIEP_VU', item.NGHIEP_VU);
            newRow.data('TEN_THUOC', item.TEN_THUOC);
            newRow.data('KHO', item.KHO);
            newRow.data('DUNG_TICH', item.DUNG_TICH);
            newRow.data('DON_GIA', item.DON_GIA);
            newRow.data('GHI_CHU', item.GHI_CHU);
            newRow.data('CHOT_DUOC', item.CHOT_DUOC);
            newRow.data('TOCDOTRUYEN', "");
            newRow.data('DONVI_HAMLUONG',item.DONVI_HAMLUONG);
            newRow.data('index', index); // Lưu index của item trong mảng

            $('#lanphauthuat_gmhs_thuoc_table_body').append(newRow);
        });
    }

    function loadDSDichTruyenGMHS() {
        // Load danh sách dịch truyền và gen ra HTML
        $('#lanphauthuat_gmhs_dichtruyen_table_body').empty();
        danhSachDichTruyenGMHS.forEach(function(item, index) {
            var newRow = $('<tr>');
            newRow.append($('<td style="vertical-align: middle;">').html(
                item.MA_VAT_TU == 0? "" : '<input class="gmhs-thuoc-dt-checkbox" type="checkbox">'
            ));
            newRow.append($('<td>').html('<button class="btn btn-danger btn-loading form-control-sm line-height-1 deleteButton" type="button">' +
                '<i class="fa fa-trash-o"></i>' +
                '</button>' +
                '<button class="btn btn-primary mt-2 btn-loading form-control-sm line-height-1 ghichuButton" type="button">' +
                '<i class="fa fa-pencil"></i>' +
                '</button>'
            ));
            var ghichudt = item.GHI_CHU_TRUYENDICH? " (" + item.GHI_CHU_TRUYENDICH + ")": "";
            var line = item.LINETRUYEN? " (Line: " + item.LINETRUYEN + ")": "";
            var tocdotruyen = item.TOCDOTRUYEN_BTD? " ( Tốc độ: " + item.TOCDOTRUYEN_BTD + ")": "";
            if((item.CHOT_DUOC == '0' || item.CHOT_DUOC == null) && item.BOMTIEMDIEN != '1') {
                newRow.append($('<td style="text-align: left; vertical-align: middle;">').text(item.TEN_THUOC + ghichudt + line + tocdotruyen));
            } else if(item.BOMTIEMDIEN == '1' ) {
                newRow.append($('<td style="text-align: left; vertical-align: middle; color: brown">').text(item.TEN_THUOC + "(btd)" + ghichudt + line + tocdotruyen));
            }  else {
                newRow.append($('<td style="text-align: left; vertical-align: middle; color: red">').text(item.TEN_THUOC + (item.BOMTIEMDIEN == '1'? "(btd)": "") +  ghichudt + line + tocdotruyen));
            }
            var inputDVT = $('<input>').attr('data-name', 'DONVI_HAMLUONG').attr('type', 'text').addClass('form-control form-control-sm').val(item.DONVI_HAMLUONG).prop('disabled', item.CHOT_DUOC === 1);
            newRow.append($('<td>').append(inputDVT));
            var inputGiaTri = $('<input>').attr('data-name', 'GIA_TRI').attr('type', 'number').addClass('form-control form-control-sm').val(item.GIA_TRI).prop('disabled', item.CHOT_DUOC === 1);
            newRow.append($('<td>').append(inputGiaTri));

            var inputTocdo = $('<input>').attr('data-name', 'TOCDOTRUYEN').attr('type', 'text').addClass('form-control form-control-sm').val(item.TOCDOTRUYEN).prop('disabled', item.CHOT_DUOC === 1);
            newRow.append($('<td>').append(inputTocdo));

            var inputDungtich = $('<input>').attr('data-name', 'DUNG_TICH').attr('type', 'number').addClass('form-control form-control-sm').val(item.DUNG_TICH).prop('disabled', item.CHOT_DUOC === 1);
            newRow.append($('<td>').append(inputDungtich));

            newRow.data('MA_VAT_TU', item.MA_VAT_TU);
            newRow.data('NGHIEP_VU', item.NGHIEP_VU);
            newRow.data('KHO', item.KHO);
            newRow.data('DUNG_TICH', item.DUNG_TICH);
            newRow.data('TEN_THUOC', item.TEN_THUOC);
            newRow.data('BOMTIEMDIEN', item.BOMTIEMDIEN);
            newRow.data('TOCDOTRUYEN', item.TOCDOTRUYEN);
            newRow.data('DON_GIA', item.DON_GIA);
            newRow.data('GHI_CHU', item.GHI_CHU);
            newRow.data('CHOT_DUOC', item.CHOT_DUOC);
            newRow.data('DONVI_HAMLUONG', item.DONVI_HAMLUONG);
            newRow.data('LINETRUYEN', item.LINETRUYEN);
            newRow.data('TOCDOTRUYEN_BTD', item.TOCDOTRUYEN_BTD);
            newRow.data('GHI_CHU_TRUYENDICH', item.GHI_CHU_TRUYENDICH);
            newRow.data('index', index); // Lưu index của item trong mảng

            $('#lanphauthuat_gmhs_dichtruyen_table_body').append(newRow);
        });
    }

    function xoaThuocDichTruyen(dataThuoc, row, loai) {
        if (loai == "thuoc") {
            var danhSachCanXoa = danhSachThuocGMHS.filter(function(item) {
                return (item.MA_VAT_TU == dataThuoc.MA_VAT_TU && item.NGHIEP_VU == dataThuoc.NGHIEP_VU && item.KHO == dataThuoc.KHO && item.DUNG_TICH == dataThuoc.DUNG_TICH);
            });
        } else {
            var danhSachCanXoa = danhSachDichTruyenGMHS.filter(function(item) {
                return (item.MA_VAT_TU == dataThuoc.MA_VAT_TU && item.NGHIEP_VU == dataThuoc.NGHIEP_VU && item.KHO == dataThuoc.KHO && item.DUNG_TICH == dataThuoc.DUNG_TICH);
            });
        }
        if(dataThuoc.CHOT_DUOC == '1') {
            return notifiToClient("Red", "Thuốc/dịch truyền này đã chốt, không thể xóa");
        } else {
            confirmToClient("Thuốc sẽ bị xóa ở tất cả mốc thời gian. Bạn có chắc muốn xóa dịch truyền này?", function() {
                if(!danhSachCanXoa[0].ID_GMHS) {
                    if (loai == "thuoc") {
                        danhSachThuocGMHS = danhSachThuocGMHS.filter(function(item) {
                            return !(item.MA_VAT_TU == dataThuoc.MA_VAT_TU && item.NGHIEP_VU == dataThuoc.NGHIEP_VU && item.KHO == dataThuoc.KHO && item.DUNG_TICH == dataThuoc.DUNG_TICH);
                        });
                    } else {
                        danhSachDichTruyenGMHS = danhSachDichTruyenGMHS.filter(function(item) {
                            return !(item.MA_VAT_TU == dataThuoc.MA_VAT_TU && item.NGHIEP_VU == dataThuoc.NGHIEP_VU && item.KHO == dataThuoc.KHO && item.DUNG_TICH == dataThuoc.DUNG_TICH);
                        });
                    }
                    row ? row.remove() : reloadGridChotDuoc();
                } else {
                    $.post("cmu_post_CMU_LPT_GMHS_CT_THUOC_DEL", {
                        url: [
                            singletonObject.dvtt, lanPhauThuatObject.ID, lanPhauThuatObject.ID_GMHS, danhSachCanXoa[0].ID_GMHS_CT_THUOC
                        ].join("```")
                    }).always(function() {
                    }).done(function(data) {
                        if(data > 0) {
                            notifiToClient("Green", "Xóa thành công");
                            if (loai == "thuoc") {
                                danhSachThuocGMHS = danhSachThuocGMHS.filter(function(item) {
                                    return !(item.MA_VAT_TU == dataThuoc.MA_VAT_TU && item.NGHIEP_VU == dataThuoc.NGHIEP_VU && item.KHO == dataThuoc.KHO && item.DUNG_TICH == dataThuoc.DUNG_TICH);
                                });
                            } else {
                                danhSachDichTruyenGMHS = danhSachDichTruyenGMHS.filter(function(item) {
                                    return !(item.MA_VAT_TU == dataThuoc.MA_VAT_TU && item.NGHIEP_VU == dataThuoc.NGHIEP_VU && item.KHO == dataThuoc.KHO && item.DUNG_TICH == dataThuoc.DUNG_TICH);
                                });
                            }
                            row ? row.remove() : reloadGridChotDuoc();
                            return;
                        } else {
                            notifiToClient("Red", "Xóa thất bại");
                        }
                    }).fail(function() {
                        notifiToClient("Red", "Lỗi API, vui lòng liên hệ IT");
                    })
                }
            }, function() {
            });
        }
    }

    function themLanPhauThuatGMHSChiTiet(data) {
        // Thêm lần phẫu thuật gây mê hồi sức chi tiết vào bảng cmu_lpt_gaymehoisuc_ct
        return new Promise(function(resolve, reject) {
            $.post("cmu_post", {url:[singletonObject.dvtt, lanPhauThuatObject.ID, lanPhauThuatObject.ID_GMHS, data.thoigian, data.mach, data.huyetap_tren
                    , data.huyetap_duoi, data.nhietdo, data.matmau, data.nuoctieu, data.nhiptho, data.ttlt, data.feco2, data.apluc, data.spo2, data.fio2,
                    data.loaicustom, data.aldmp, data.quansat,
                    data.batdaumo,
                    data.datnoikhiquan,
                    data.batdaugayte,
                    data.tetaicho,
                    data.tengoaimangcung,
                    data.tienme,
                    data.hoatdong,
                    singletonObject.userId, "CMU_LPT_GAYMEHOISUC_CT_INS"].join("```")}).done(function (data) {
                if (data > 0) {
                    notifiToClient("Green","Thêm thành công");
                    $("#modalGMHSChiTiet").modal('hide');
                    resolve(data);
                } else {
                    reject("Invalid data received");
                }
            })
                .fail(function(error) {
                    notifiToClient("Red", "Đã có lỗi API CMU_LPT_GAYMEHOISUC_CT_INS, vui lòng báo IT");
                    reject(error);
                });
        });
    }

    function capNhatLanPhauThuatGMHSChiTiet(data) {
        // Cập nhật lần phẫu thuật gây mê hồi sức chi tiết vào bảng cmu_lpt_gaymehoisuc_ct
        return new Promise(function(resolve, reject) {
            $.post("cmu_post", {url:[singletonObject.dvtt, lanPhauThuatObject.ID, lanPhauThuatObject.ID_GMHS, data.thoigian, data.mach, data.huyetap_tren
                    , data.huyetap_duoi, data.nhietdo, data.matmau, data.nuoctieu, data.nhiptho, data.ttlt, data.feco2, data.apluc, data.spo2, data.fio2,
                    data.loaicustom, data.aldmp, data.quansat, ID_GMHS_CT,
                    data.batdaumo,data.datnoikhiquan,
                    data.batdaugayte,
                    data.tetaicho,
                    data.tengoaimangcung,
                    data.tienme,
                    data.hoatdong,
                    "CMU_LPT_GAYMEHOISUC_CT_UPD"].join("```")}).done(function (data) {
                if (data > 0) {
                    notifiToClient("Green","Cập nhật thành công");
                    $("#modalGMHSChiTiet").modal('hide');
                    loadGayMeHoiSuc();
                    resolve(data);
                } else {
                    reject("Invalid data received");
                }
            })
                .fail(function(error) {
                    notifiToClient("Red", "Đã có lỗi API, vui lòng báo IT");
                    reject(error);
                });
        });
    }

    function xoaLanPhauThuatGMHSChiTiet(id_gmhs_ct) {
        // Xóa lần phẫu thuật gây mê hồi sức chi tiết trong bảng cmu_lpt_gaymehoisuc_ct
        return new Promise(function(resolve, reject) {
            confirmToClient("Bạn có chắc muốn xóa mốc thời gian này?", function() {
                $.post("cmu_post", {url:[singletonObject.dvtt, lanPhauThuatObject.ID, lanPhauThuatObject.ID_GMHS, id_gmhs_ct, "CMU_LPT_GAYMEHOISUC_CT_DEL"].join("```")}).done(function (data) {
                    if (data > 0) {
                        notifiToClient("Green","Xóa thành công");
                        $("#modalGMHSChiTiet").modal('hide');
                        loadGayMeHoiSuc();
                        resolve(data);
                    } else {
                        reject("Invalid data received");
                    }
                }).fail(function(error) {
                    notifiToClient("Red", "Đã có lỗi API CMU_LPT_GAYMEHOISUC_CT_DEL, vui lòng báo IT");
                    reject(error);
                });
            }, function() {
            });
        });
    }

    function getDanhSachThuocDichTruyen(id_gmhs_ct) {
        // Lấy danh sách thuốc và dịch truyền của lần phẫu thuật gây mê hồi sức chi tiết, nếu id_gmhs_ct = null thì lấy danh sách thuốc và dịch truyền của lần phẫu thuật gây mê hồi sức
        showLoaderIntoWrapId("wrap-chitietgmhs");
        return new Promise(function(resolve, reject) {
            $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, lanPhauThuatObject.ID, lanPhauThuatObject.ID_GMHS, id_gmhs_ct, 'CMU_LPT_GMHS_CT_THUOC_SEL'])).done(function(data){
                resolve(data);
                danhSachThuocGMHS = $.grep(data, function(item) {
                    return item.LOAI_VAT_TU === 1;
                });
                loadDSThuocGMHS();

                danhSachDichTruyenGMHS = $.grep(data, function(item) {
                    return item.LOAI_VAT_TU === 2;
                });
                loadDSDichTruyenGMHS()
            }).always(function(){
                hideLoaderIntoWrapId("wrap-chitietgmhs");
            });
        });
    }

    function mergeThuocDichTruyen() {
        // Merge 2 mảng thuốc và dịch truyền thành 1 mảng
        var mergedArray = [];
        $.each(danhSachDichTruyenGMHS, function(index, item) {
            if ( (item['GIA_TRI'] &&  item['GIA_TRI'] > 0) ||
                (item["TOCDOTRUYEN"] && (!item['GIA_TRI'] || item['GIA_TRI'] >0) )) {
                item["loai_vat_tu"] = 2;
                mergedArray.push(item);
            }
        });
        $.each(danhSachThuocGMHS, function(index, item) {
            if ("GIA_TRI" in item && item["GIA_TRI"] && (item["GIA_TRI"] !== "" || item["GIA_TRI"] !== "0")) {
                item["loai_vat_tu"] = 1;
                mergedArray.push(item);
            }
        });
        return mergedArray;
    }


    function vebieudomachhuyetap(dataMach, dataTamtruong , dataTamthu, indexMe, indexMo, indexKethuc, others) {
        var canvas = document.getElementById('myChart');
        var ctx = canvas.getContext('2d');
        var canvasWidth = canvas.width;
        var canvasHeight = canvas.height;
        ctx.clearRect(0, 0, canvasWidth, canvasHeight)
        var margin = 10;
        var Hmargin = 0;
        var chartWidth = canvasWidth - 2 * margin;
        var chartHeight = canvasHeight - 2 * Hmargin;
        var barWidth = chartWidth / 12;

        // Function to draw a line chart with dots and grid
        function drawMachChart(data) {

            // Set the line chart color and width
            ctx.lineWidth = 1; // Line chart width

            // Set the dot color
            ctx.strokeStyle = '#000000';
            ctx.fillStyle = '#000000'; // Dot color (red in this example)

            // Begin drawing the line chart
            ctx.beginPath();
            // Draw the grid lines
            for (let i = 0; i <= 16; i++) {
                var yPos = Hmargin + i * (chartHeight / 16);
                ctx.beginPath();
                ctx.moveTo(margin, yPos);
                ctx.lineTo(canvasWidth - margin, yPos);
                ctx.stroke();

            }
            // Draw the y-axis
            for (let i = 0; i <= 12; i++) {
                var xPos = margin + i * barWidth;
                ctx.beginPath();
                ctx.moveTo(xPos, Hmargin);
                ctx.lineTo(xPos, canvasHeight - Hmargin);
                ctx.setLineDash([5, 5]);
                ctx.stroke();

            }
            ctx.setLineDash([]);
            ctx.fillStyle = '#ff4136';
            ctx.strokeStyle = '#ff4136';
            var x = 0;
            var y = 0;
            var stepH = (chartHeight / 16);
            for (let i = 0; i < data.length; i++) {
                x = margin + i * barWidth;
                y = canvasHeight - Hmargin - ((data[i]-30)/10) * stepH;

                // Draw a dot (circle) at each data point
                ctx.beginPath();
                ctx.arc(x, y, 4, 0, 2 * Math.PI);
                if(data[i] < 30) {
                    ctx.font = "bold 12px Arial";
                    ctx.fillText("M:"+data[i], x - 5, canvasHeight - 2*stepH );
                }
                ctx.fill();
            }

            for (let i = 0; i < data.length; i++) {
                x = margin + i * barWidth;
                y = canvasHeight - Hmargin - ((data[i]-30)/10) * stepH;
                if (i === 0  ) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);

                }
            }
            ctx.stroke();
        }

        function drawDownArrow(x, y, size, text) {
            ctx.beginPath();
            ctx.moveTo(x, y-10);
            ctx.lineTo(x + size, y + size-10);
            ctx.lineTo(x + size * 2, y-10);
            ctx.font = "bold 12px Arial";
            ctx.fillStyle = "#09329a";
            ctx.fillText(text, x+2, y - 10);
            ctx.stroke();
        }

        function drawUpArrow(x, y, size, text) {
            ctx.beginPath();
            ctx.moveTo(x, y + size);
            ctx.lineTo(x + size, y);
            ctx.lineTo(x + size * 2, y + size);
            ctx.font = "bold 12px Arial";
            ctx.fillStyle = "#09329a";
            ctx.fillText(text, x+2, y + 20);
            ctx.stroke();
        }

        function drawDownTextArrow(x, y, text) {
            ctx.beginPath();
            ctx.font = "bold 12px Arial";
            ctx.fillStyle = "#09329a";
            ctx.fillText(text, x+2, y );
            ctx.stroke();
        }

        function drawHuyetap(dataTamtruong, dataTamthu) {
            var stepH = (canvasHeight / 16);
            ctx.strokeStyle = '#0074d9';
            ctx.lineWidth = 2;
            for (let i = 0; i < dataTamtruong.length; i++) {
                var xPos = margin + i * barWidth;
                if(dataTamtruong[i] < 30 || dataTamthu[i] < 30) {
                    ctx.beginPath();
                    ctx.fillText(dataTamtruong[i]+"/"+dataTamthu[i], xPos - 10+2, canvasHeight - stepH);
                    ctx.stroke();
                }else {
                    if(dataTamthu[i] > 190) {
                        drawDownTextArrow(xPos - 10, stepH-5, dataTamthu[i])
                    } else {
                        drawDownArrow(xPos - 10, canvasHeight - ((dataTamthu[i]-30)/10) * stepH,10, dataTamthu[i]);
                    }

                    drawUpArrow(xPos - 10, canvasHeight - ((dataTamtruong[i]-30)/10) * stepH, 10, dataTamtruong[i]);
                    ctx.beginPath();
                    ctx.moveTo(xPos, canvasHeight - ((dataTamthu[i]-30)/10) * stepH);
                    ctx.lineTo(xPos, canvasHeight - ((dataTamtruong[i]-30)/10) * stepH);
                    ctx.stroke();
                }

            }

        }

        function drawGayme(indexMe) {
            ctx.strokeStyle = '#ba143b';
            ctx.lineWidth = 2;
            drawDownArrow(indexMe*barWidth, 44, 10, "");
            ctx.beginPath();
            ctx.moveTo(margin +  indexMe*barWidth, 16);
            ctx.lineTo(margin + indexMe*barWidth, 44);
            ctx.stroke();
        }
        function drawGaybatdaumo(indexMo) {
            ctx.strokeStyle = '#ba143b';
            ctx.lineWidth = 2;
            var xPos = margin + barWidth*indexMo
            drawDownArrow(barWidth*indexMo, 44, 10, "");
            ctx.beginPath();
            ctx.moveTo(xPos, 16);
            ctx.lineTo(xPos, 44);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(xPos - 5, 20);
            ctx.lineTo(xPos + 5, 20);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(xPos - 5, 24);
            ctx.lineTo(xPos + 5, 24);
            ctx.stroke();
        }
        function drawGayketthuc(indexKethuc) {
            ctx.strokeStyle = '#ba143b';
            ctx.lineWidth = 2;
            var xPos = margin + indexKethuc*barWidth
            drawUpArrow(xPos - margin, 10, 10, "");
            ctx.beginPath();
            ctx.moveTo(xPos, 10);
            ctx.lineTo(xPos, 44);
            ctx.stroke();
        }
        function drawRightArrow(fromX, fromY, toX, toY, headLength = 8, headWidth = 8) {
            ctx.beginPath();
            ctx.strokeStyle = '#081cc6';
            ctx.lineWidth = 1.5;
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, fromY);
            ctx.lineTo(toX - headLength, fromY - headWidth);
            ctx.moveTo(toX, fromY);
            ctx.lineTo(toX - headLength, fromY + headWidth);
            ctx.stroke();
        }
        function drawTextandArrow(index, text, drawDot) {
            var xPos = margin + index*barWidth
            drawRightArrow(xPos, 20, xPos+17, 20);
            ctx.beginPath();
            ctx.font = "bold 13px Arial";
            ctx.fillStyle = "#081cc6";
            if(drawDot) {
                ctx.arc(xPos-5, 20, 4, 0, 2 * Math.PI);
            }
            ctx.fillText(text, xPos-5, 25);
            ctx.fill();
        }

        function drawTextandCircle(index, text, drawDot) {
            var xPos = margin + index*barWidth
            ctx.beginPath();
            ctx.strokeStyle = '#ba143b';
            ctx.font = "bold 13px Arial";
            ctx.fillStyle = "#081cc6";
            if(drawDot) {
                ctx.arc(xPos, 20, 10, 0, 2 * Math.PI);
            }
            ctx.fillText(text, xPos-5, 25);
            ctx.stroke();
        }
        drawMachChart(dataMach);
        drawHuyetap(dataTamtruong, dataTamthu);
        if(indexMe > -1) {
            drawGayme(indexMe)
        }
        if(indexMo > -1) {
            drawGaybatdaumo(indexMo)
        }
        if(indexKethuc > -1) {
            drawGayketthuc(indexKethuc)
        }
        if(others.batdaugayte > -1) {
            drawTextandArrow(others.batdaugayte, 'X')
        }
        if(others.tetaicho > -1) {
            drawTextandArrow(others.tetaicho, 'O')
        }
        if(others.tengoaimangcung > -1) {
            drawTextandArrow(others.tengoaimangcung, 'X')
        }
        if(others.tienme > -1) {
            drawTextandArrow(others.tienme, '', true)
        }

        if(others.metinhmach > -1) {
            drawTextandCircle(others.metinhmach, 'X', true)
        }

        if(others.metinhmachtetaicho > -1) {
            drawTextandCircle(others.metinhmachtetaicho, 'X', true)
        }
    }

    function instanceGridLanPhauThuatTreem() {
        var list = $("#lanphauthuat_gmhs_dstreem");
        if (!list[0].grid) {
            list.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 100,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "ID", name: 'ID', index: 'ID', width: 50,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "Giới tính", name: 'GIOI_TINH_HT', index: 'GIOI_TINH_HT', width: 100,
                        formatter: function (cellvalue, options, rowObject) {
                            return rowObject.GIOI_TINH == 1 ? "Nam" : "Nữ";
                        },
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "Cân nặng", name: 'CANNANG', index: 'CANNANG', width: 150,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "Vòng đầu", name: 'VONGDAU', index: 'VONGDAU', width: 100},
                    {label: "Apgar", name: 'APGAR', index: 'APGAR', width: 200,
                        formatter: function (cellvalue, options, rowObject) {
                            var html = "";
                            if(rowObject.APGAR1) {
                                html += "<p class='m-0'> Apgar 1': " + rowObject.APGAR1 + "</p>";
                            }
                            if(rowObject.APGAR5) {
                                html += "<p class='m-0'> Apgar 5': " + rowObject.APGAR5+ "</p>";
                            }
                            if(rowObject.APGAR10) {
                                html += "<p class='m-0'> Apgar 10': " + rowObject.APGAR10+ "</p>";
                            }
                            return html;
                        },
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "GIOI_TINH", name: 'GIOI_TINH', index: 'GIOI_TINH',  hidden: true},
                    {label: "APGAR1", name: 'APGAR1', index: 'APGAR1', hidden: true},
                    {label: "APGAR5", name: 'APGAR5', index: 'APGAR5', hidden: true},
                    {label: "APGAR10", name: 'APGAR10', index: 'APGAR10', hidden: true},

                ],
                rowNum: 1000,
                caption: "",
                gridComplete: function() {

                },
                onRightClickRow: function(id) {
                    if (id) {

                        $.contextMenu({
                            selector: '#lanphauthuat_gmhs_dstreem tr',
                            reposition : false,
                            callback: function (key, options) {
                                var rowData = getThongtinRowSelected("lanphauthuat_gmhs_dstreem");
                                if (key == "chinhsua") {
                                    initFormioTreem(rowData)
                                    showOrHideByClass('modalPhauthuatTreemFooter', 'T', 'edit')
                                }
                                if (key == "xoa") {
                                    confirmToClient(MESSAGEAJAX.CONFIRM, function() {
                                        list.jqGrid('delRowData', list.jqGrid('getGridParam', 'selrow'));
                                    })
                                }
                            },
                            items:  {
                                "chinhsua": {name: '<p><i class="fa fa-pencil-square-o text-success" aria-hidden="true"></i> Chỉnh sửa</p>'},
                                "xoa": {name: '<p><i class="fa fa-trash-o red-text" aria-hidden="true"></i> Xóa</p>'}
                            }
                        });

                    }
                },
                ondblClickRow: function (id) {
                    initFormioTreem(getThongtinRowSelected("lanphauthuat_gmhs_dstreem"))
                    showOrHideByClass('modalPhauthuatTreemFooter', 'T', 'edit')
                }
            });
        }
    }

    function initFormioTreem(data) {
        $("#modalPhauthuatTreem").modal("show")
        var jsonForm = getJSONObjectForm([
            {
                label: "ghmstongket1",
                key: "ghmstongket1",
                columns: [
                    {
                        "components": [
                            {
                                "label": "Cân nặng (gram)",
                                "customClass": "pr-2",
                                "key": "CANNANG",
                                "type": "number",
                                validate: {
                                    required: true,
                                    min: 0,
                                    max: 10000,
                                }
                            },
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Giới tính",
                                "customClass": "pr-2",
                                "key": "GIOI_TINH",
                                "type": "select",
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Nam",
                                                "value": "1"
                                            },
                                            {
                                                "label": "Nữ",
                                                "value": "0"
                                            }
                                        ]
                                    },
                                },
                                validate: {
                                    required: true
                                },
                            },
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Vòng đầu (cm)",
                                "customClass": "pr-2",
                                "key": "VONGDAU",
                                "type": "number",
                                validate: {
                                    required: true,
                                    min: 0,
                                    max: 200,
                                }
                            },
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Apgar 1'",
                                "customClass": "pr-2",
                                "key": "APGAR1",
                                "type": "number",
                                validate: {
                                    min: 0,
                                    max: 10,
                                }
                            },
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Apgar 5'",
                                "customClass": "pr-2",
                                "key": "APGAR5",
                                "type": "number",
                                validate: {
                                    min: 0,
                                    max: 10,
                                }
                            },
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Apgar 10'",
                                "customClass": "pr-2",
                                "key": "APGAR10",
                                "type": "number",
                                validate: {
                                    min: 0,
                                    max: 10,
                                }
                            },
                        ],
                        "width": 2,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            }
        ])
        Formio.createForm(document.getElementById('modalPhauthuatTreemForm'),
            jsonForm,{}
        ).then(function(form) {
            formThongtintreem = form;
            formThongtintreem.submission = {
                data: data
            };
        });
    }

    function loadDSGridTreem(data) {
        var url = "cmu_list_CMU_LANPHAUTHUAT_TREEM_GET?url="+convertArray([singletonObject.dvtt, data.ID_GMHS_CT , lanPhauThuatObject.ID]);
        loadDataGridGroupBy($("#lanphauthuat_gmhs_dstreem"), url);
    }

    function capnhatDanhsachTreem(ID_GMHS_CT) {
        return $.post("cmu_post_CMU_LANPHAUTHUAT_TREEM_INS", {
            url: [
                singletonObject.dvtt,
                JSON.stringify(getAllRowDataJqgrid("lanphauthuat_gmhs_dstreem")),
                ID_GMHS_CT,
                lanPhauThuatObject.ID,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                singletonObject.userId
            ].join("```")
        })
    }

    function instanceGridLanPhauThuatTruyenmau() {
        var list = $("#lanphauthuat_gmhs_dstruyenmau");
        if (!list[0].grid) {
            list.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 100,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "ID", name: 'ID', index: 'ID', width: 50,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "ID_TRUYENMAU", name: 'ID_TRUYENMAU', index: 'ID_TRUYENMAU', hidden: true},
                    {label: "ID_GMHS_CT", name: 'ID_GMHS_CT', index: 'ID_GMHS_CT', hidden: true},
                    {label: "MANHOM", name: 'MANHOM', index: 'MANHOM', hidden: true},
                    {label: "Nhóm máu", name: 'TENNHOMMAU', index: 'TENNHOMMAU', width: 250,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "Mã túi máu", name: 'MATUIMAU', index: 'MATUIMAU', width: 100,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "Số lượng", name: 'GIATRI', index: 'GIATRI', width: 80,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "Tổng", name: 'TONGTRUYEN', index: 'TONGTRUYEN', width: 80,},
                ],
                rowNum: 1000,
                caption: "",
                gridComplete: function() {

                },
                onRightClickRow: function(id) {
                    if (id) {

                        $.contextMenu({
                            selector: '#lanphauthuat_gmhs_dstruyenmau tr',
                            reposition : false,
                            callback: function (key, options) {
                                var rowData = getThongtinRowSelected("lanphauthuat_gmhs_dstruyenmau");
                                if (key == "chinhsua") {
                                    initFormioTruyenmau(rowData)
                                    showOrHideByClass('modalPhauthuatTruyenmauFooter', 'add', 'edit')
                                }
                                if (key == "xoa") {
                                    confirmToClient(MESSAGEAJAX.CONFIRM, function() {
                                        if(rowData.ID) {
                                            $.post("cmu_post_CMU_LANPHAUTHUAT_LTMAU_DEL", {
                                                url: [
                                                    singletonObject.dvtt,
                                                    rowData.ID_TRUYENMAU,
                                                    lanPhauThuatObject.ID
                                                ].join("```")
                                            }).done(function(data) {
                                                list.jqGrid('delRowData', list.jqGrid('getGridParam', 'selrow'));
                                            }).fail(function(error) {
                                                notifiToClient("Red", MESSAGEAJAX.ERROR)
                                            })
                                        } else {
                                            list.jqGrid('delRowData', list.jqGrid('getGridParam', 'selrow'));
                                        }
                                    })
                                }
                            },
                            items:  {
                                "chinhsua": {name: '<p><i class="fa fa-pencil-square-o text-success" aria-hidden="true"></i> Chỉnh sửa</p>'},
                                "xoa": {name: '<p><i class="fa fa-trash-o red-text" aria-hidden="true"></i> Xóa</p>'}
                            }
                        });

                    }
                },
                ondblClickRow: function (id) {
                    initFormioTruyenmau(getThongtinRowSelected("lanphauthuat_gmhs_dstruyenmau"))
                    showOrHideByClass('modalPhauthuatTreemFooter', 't', 'edit')
                }
            });
        }
    }
    function initFormioTruyenmau(data) {
        $("#modalPhauthuatTruyenmau").modal("show")
        var jsonForm = getJSONObjectForm([
            {
                label: "ghmstruyenmau",
                key: "ghmstruyenmau",
                columns: [
                    {
                        "components": [
                            {
                                "label": "Nhóm máu",
                                "customClass": "pr-2",
                                "key": "MANHOM",
                                "type": "select",
                                others: {
                                    "data": {
                                        "values": dsNhommau
                                    },
                                },
                                validate: {
                                    required: true
                                },
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Mã túi máu",
                                "customClass": "pr-2",
                                "key": "MATUIMAU",
                                "type": "textfield",
                                validate: {
                                    required: true,
                                    maxlenght: 255,
                                },
                            }
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Số lượng",
                                "customClass": "pr-2",
                                "key": "GIATRI",
                                "type": "number",
                                validate: {
                                    min: 0,
                                    max: 1000,
                                }
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Tổng ",
                                "customClass": "pr-2",
                                "key": "TONGTRUYEN",
                                "type": "number",
                                validate: {
                                    required: true,
                                    min: 0,
                                    max: 20000,
                                }
                            },
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            }
        ])
        Formio.createForm(document.getElementById('modalPhauthuatTruyenmauForm'),
            jsonForm,{}
        ).then(function(form) {
            formThongtintruyenmau = form;
            formThongtintruyenmau.submission = {
                data: data
            };
        });
    }
    function loadDSGridTruyenmau(data) {
        var url = "cmu_list_CMU_LANPHAUTHUAT_TRUYENMAU_GET?url="+convertArray([singletonObject.dvtt, data.ID_GMHS_CT , lanPhauThuatObject.ID]);
        loadDataGridGroupBy($("#lanphauthuat_gmhs_dstruyenmau"), url);
    }

    function capnhatDanhsachTruyenmau(ID_GMHS_CT) {
        var allData = getAllRowDataJqgrid("lanphauthuat_gmhs_dstruyenmau")
        if (allData.length == 0) {
            return $.post("cmu_post_CMU_LANPHAUTHUAT_TRUYENMAU_DEL", {
                url: [
                    singletonObject.dvtt,
                    lanPhauThuatObject.ID
                ].join("```")
            });
        }
        allData.forEach(function(data) {
            $.post("cmu_post_CMU_LANPHAUTHUAT_TRUYENMAU_INS", {
                url: [
                    singletonObject.dvtt,
                    data.ID? data.ID : 0,
                    data.ID_TRUYENMAU? data.ID_TRUYENMAU : 0,
                    ID_GMHS_CT,
                    lanPhauThuatObject.ID,
                    thongtinhsba.thongtinbn.SOVAOVIEN,
                    data.MANHOM,
                    getTennhommau(data.MANHOM),
                    data.GIATRI,
                    data.MATUIMAU,
                    data.TONGTRUYEN,
                    singletonObject.userId
                ].join("```")
            })
        })
    }

    function getTennhommau(manhom) {
        var tennhommau = '';
        dsNhommau.forEach(function(item) {
            if(item.value == manhom) {
                tennhommau = item.label;
            }
        })
        return tennhommau
    }

    function loadDulieumacdinh(object) {
        $("#lanphauthuat_gmhs_mach").val(object.MACH);
        $("#lanphauthuat_gmhs_tamthu").val(object.HUYENAP_TREN);
        $("#lanphauthuat_gmhs_tamtruong").val(object.HUYETAP_DUOI);
        $("#lanphauthuat_gmhs_spo2").val(object.SPO2);
        $("#lanphauthuat_gmhs_nhietdo").val(object.NHIET_DO);
        $("#lanphauthuat_gmhs_nhiptho").val(object.NHIP_THO);
        $("#lanphauthuat_gmhs_ttlt").val(object.TTLT);
        $("#lanphauthuat_gmhs_feco2").val(object.FECO2);
        $("#lanphauthuat_gmhs_apluc").val(object.AP_LUC);
        $("#lanphauthuat_gmhs_fio2").val(object.FIO2);
        $("#lanphauthuat_gmhs_loaicustom").val(object.LOAI_CUSTOM);
        $("#lanphauthuat_gmhs_aldmp").val(object.ALDMP);
    }

});