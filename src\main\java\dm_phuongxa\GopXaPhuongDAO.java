package dm_phuongxa;
import VSC.jdbc.JdbcTemplate;
import org.json.JSONObject;

import java.util.List;

public interface GopXaPhuongDAO {
    public List laydanhmuc(String sql);
    public List listGopXaPhuong(String madonvi, String madonvi_moi, int rows, int page);
    public Long saveGopXaPhuong(JSONObject data);
    public Long deleteGopXaPhuong(String idPakages);
    public long convertToInt(String sql,Object[] parameters);

}
