create or replace PROCEDURE      HIS_MANAGER.CMU_KHAMBENHTHEOYC_PROCED(
    p_dvtt              IN     VARCHAR2,
    p_id               IN     NUMBER,
    p_sovaovien      IN     NUMBER,
    p_stt_benhan      IN     VARCHAR2,
    p_stt_dotdieutri      IN     VARCHAR2,
    cur OUT SYS_REFCURSOR
) IS
v_stt_buong varchar2(255):='';
v_stt_giuong varchar2(255):='';
BEGIN
begin
select stt_buong, stt_giuong into v_stt_buong, v_stt_giuong from cmu_sobuonggiuong
where dvtt = p_dvtt and stt_benhan = p_stt_benhan and stt_dotdieutri = p_stt_dotdieutri and rownum <= 1;
exception
	when no_data_found then v_stt_buong:=null;v_stt_giuong:=null;
end;
open cur for
select
    phieu.*,
--    TO_CHAR(phieu.NGAY_TAO_PHIEU, 'dd/MM/yyyy') NGAY_TAO_PHIEU,
    TO_CHAR(phieu.NGAY_TAO_PHIEU, ' "Ngày" DD "tháng" MM "năm" YYYY') NGAY_TAO_PHIEU_TEXT,
    dt.TEN_DANTOC,
    nn.TEN_NGHE_NGHIEP,
    nv.TEN_NHANVIEN NGUOITHUCHIEN,
    nv2.TEN_NHANVIEN TEN_GIAM_DOC,
    nv1.TEN_NHANVIEN TEN_BAC_SI,
    v_stt_buong PHONG,
    v_stt_giuong GIUONG

FROM CMU_KHAMBENHTHEOYC phieu
         LEFT JOIN HIS_FW.DM_NHANVIEN nv ON phieu.NGUOI_TAO = nv.MA_NHANVIEN
         LEFT JOIN HIS_FW.DM_NHANVIEN nv1 ON phieu.BACSICHUABENH = nv1.MA_NHANVIEN
         LEFT JOIN HIS_FW.DM_NHANVIEN nv2 ON phieu.GIAMDOCBV = nv2.MA_NHANVIEN
         LEFT JOIN his_public_list.dm_dan_toc dt ON phieu.DANTOC = dt.ma_dantoc
         LEFT JOIN his_public_list.dm_nghenghiep nn ON phieu.NGHENGHIEP = nn.ma_nghe_nghiep

WHERE phieu.ID = p_id and phieu.SOVAOVIEN = p_sovaovien
order by phieu.NGAY_TAO_PHIEU;
END;