<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rp_vobenhan_trang_3_methadone_full" language="groovy" pageWidth="595" pageHeight="842" columnWidth="565" leftMargin="10" rightMargin="20" topMargin="10" bottomMargin="10" uuid="4fc17260-5264-4f4b-863d-7ae27fb81761">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="116"/>
	<property name="ireport.y" value="649"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="BACSIDIEUTRI" class="java.lang.String"/>
	<field name="NGUOIGIAO_HOSO" class="java.lang.String"/>
	<field name="NGUOINHAN_HOSO" class="java.lang.String"/>
	<field name="SOTO_CTSCANNER" class="java.math.BigDecimal"/>
	<field name="SOTO_KHAC" class="java.math.BigDecimal"/>
	<field name="SOTO_SIEUAM" class="java.math.BigDecimal"/>
	<field name="SOTO_TOANBOHS" class="java.math.BigDecimal"/>
	<field name="SOTO_XETNGHIEM" class="java.math.BigDecimal"/>
	<field name="SOTO_XQUANG" class="java.math.BigDecimal"/>
	<field name="NGUOI_BENH_MOI" class="java.lang.String"/>
	<field name="CSK_CHUYEN_DEN" class="java.lang.String"/>
	<field name="DIEU_TRI_LAI" class="java.lang.String"/>
	<field name="NGAY_CHUYEN_DEN" class="java.lang.String"/>
	<field name="NOI_CHUYEN_DEN" class="java.lang.String"/>
	<field name="NGAY_BD_DIEUTRI" class="java.lang.String"/>
	<field name="NGAY_KT_DIEUTRI" class="java.lang.String"/>
	<field name="THOI_GIAN_DIEUTRI" class="java.lang.String"/>
	<field name="LIEU_DIEU_TRI" class="java.lang.String"/>
	<field name="LIEU_TRUOC_NGUNG_DT" class="java.lang.String"/>
	<field name="CAC_TACDUNG_PHU" class="java.lang.String"/>
	<field name="BO_LIEU_LY_DO" class="java.lang.String"/>
	<field name="TIEP_TUC_DUNG_MATUY" class="java.lang.String"/>
	<field name="DT_BENH_KEM_THEO" class="java.lang.String"/>
	<field name="CHECK_CHUYEN_NOI_KHAC" class="java.lang.String"/>
	<field name="NGAY_CHUYEN_NOI_KHAC" class="java.lang.String"/>
	<field name="QKDT_NOI_CHUYEN_DEN" class="java.lang.String"/>
	<field name="CHECK_NGUNG_DT_TU_NGUYEN" class="java.lang.String"/>
	<field name="NGAY_NGUNG_DT_TU_NGUYEN" class="java.lang.String"/>
	<field name="CHECK_NGUNG_DT_BAT_BUOC" class="java.lang.String"/>
	<field name="NGAY_NGUNG_DT_BAT_BUOC" class="java.lang.String"/>
	<field name="CHECK_DA_CAI_NGHIEN" class="java.lang.String"/>
	<field name="DA_CAI_NGHIEN_METHADONE" class="java.lang.String"/>
	<field name="CHECK_TU_VONG" class="java.lang.String"/>
	<field name="NGAY_TU_VONG" class="java.lang.String"/>
	<field name="LY_DO_TU_VONG" class="java.lang.String"/>
	<field name="NGAY_KY_BACSI" class="java.lang.String"/>
	<field name="HOTEN_BACSI" class="java.lang.String"/>
	<field name="NGAY_KY_THU_TRUONG" class="java.lang.String"/>
	<field name="HOTEN_THU_TRUONG" class="java.lang.String"/>
	<field name="METHADONE_LYDO_TKBA" class="java.lang.String"/>
	<field name="NGAY_TONGKET" class="java.lang.String"/>
	<field name="ANCHUKY" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="706" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="565" height="18" uuid="0c7f2f79-2b8a-450a-b8c5-34c2a36e7c24">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[TỔNG KẾT BỆNH ÁN]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="18" width="565" height="45" uuid="bf3d3d84-3e99-4fcb-bdcc-a44cae7b712a">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["<style isBold='true'>1.1. Lý do tổng kết bệnh án </style>"+"<style isItalic='true'>(chuyển đi, bỏ điều trị, ngừng điều trị, thay đổi phương pháp điều trị, thay bệnh án mới, tử vong...)</style> " + ($F{METHADONE_LYDO_TKBA} == null ? "" : $F{METHADONE_LYDO_TKBA})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="63" width="565" height="18" uuid="1355771a-51a1-468e-ba18-eb9760f4069a">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[2. Diễn biến quá trình điều trị]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="81" width="80" height="18" uuid="1aed03a0-b2b4-4065-8b18-aff143b5f66b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Người bệnh mới]]></text>
			</staticText>
			<staticText>
				<reportElement x="100" y="81" width="233" height="18" uuid="d978eb56-b036-403a-97ad-7a509726da96">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph rightIndent="5"/>
				</textElement>
				<text><![CDATA[Đang điều trị tại cơ sở khác chuyển đến]]></text>
			</staticText>
			<staticText>
				<reportElement x="351" y="81" width="142" height="18" uuid="4377efa4-d0ef-4011-b822-2fda27d58037">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph rightIndent="5"/>
				</textElement>
				<text><![CDATA[Điều trị lại]]></text>
			</staticText>
			<textField>
				<reportElement x="80" y="81" width="18" height="18" uuid="9cbba71f-6398-40f1-96ab-6ccf25970eae">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="0">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGUOI_BENH_MOI}.equals( "true" ) ? "X" : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="333" y="81" width="18" height="18" uuid="92f7770a-1e87-4062-92ac-3b171b23bebe">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="0">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CSK_CHUYEN_DEN}.equals( "true" ) ? "X" : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="493" y="81" width="18" height="18" uuid="ab70d3d5-656e-4162-b05d-1b540be23e0d">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="0">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{DIEU_TRI_LAI}.equals( "true" ) ? "X" : ""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="99" width="201" height="18" uuid="5e7410b5-a1df-4c5e-b977-e154995288f9"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày chuyển đến: " + ($F{NGAY_CHUYEN_DEN} == null ? "" : ($F{NGAY_CHUYEN_DEN}.split("-")[2] + "/" + $F{NGAY_CHUYEN_DEN}.split("-")[1] + "/" + $F{NGAY_CHUYEN_DEN}.split("-")[0]))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="201" y="99" width="364" height="18" uuid="94704ab0-956b-4d40-911d-be16ceb5199c"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Nơi chuyển đến: " + ($F{NOI_CHUYEN_DEN} == null ? "" : $F{NOI_CHUYEN_DEN})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="117" width="200" height="18" uuid="b320a6c5-6ba2-40d6-9be0-0ff81776b3b0"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày bắt đầu điều trị: " + ($F{NGAY_BD_DIEUTRI} == null ? "" : $F{NGAY_BD_DIEUTRI})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="201" y="117" width="150" height="18" uuid="2d0237ae-acd5-46fb-9d31-142551563dd6"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày kết thúc: " + ($F{NGAY_KT_DIEUTRI} == null ? "" : $F{NGAY_KT_DIEUTRI})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="351" y="117" width="214" height="18" uuid="3d43c6bf-f3a3-401a-9e88-79447a7c609c"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Thời gian điều trị: " + ($F{THOI_GIAN_DIEUTRI} == null ? "" : $F{THOI_GIAN_DIEUTRI}) + " tháng"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="135" width="201" height="18" uuid="03015f34-9e01-4274-be80-95be3f492855"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Liều duy trì: " + ($F{LIEU_DIEU_TRI} == null ? "" : $F{LIEU_DIEU_TRI}) + " mg/ngày"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="201" y="135" width="364" height="18" uuid="bccb4583-56f2-4310-937d-6330cbfdb4aa"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Liều trước khi ngừng điều trị: " + ($F{LIEU_TRUOC_NGUNG_DT} == null ? "" : $F{LIEU_TRUOC_NGUNG_DT}) + "mg/ngày"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="153" width="564" height="18" uuid="5cfc4eba-915e-453a-8fe6-e40b79d61dbb"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Các tác dụng phụ: " + ($F{CAC_TACDUNG_PHU} == null ? "" : $F{CAC_TACDUNG_PHU})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="171" width="564" height="18" uuid="68f2b949-2d8d-46b6-8af2-1b4ecb9a5f75"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Số lần bỏ liều và lý do: " + ($F{BO_LIEU_LY_DO} == null ? "" : $F{BO_LIEU_LY_DO})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="189" width="565" height="35" uuid="6c30d7f2-9d52-439a-8ae4-25dc705cbf33"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Trong quá trình điều trị có tiếp tục sử dụng ma túy (loại ma túy, thời gian, cách sử dụng, liều lượng...): " + ($F{TIEP_TUC_DUNG_MATUY} == null ? "" : $F{TIEP_TUC_DUNG_MATUY})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="224" width="564" height="25" uuid="8ede06ee-3c56-4cea-be51-8ee7e0f3905c"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Điều trị các bệnh kèm theo: " + ($F{DT_BENH_KEM_THEO} == null ? "" : $F{DT_BENH_KEM_THEO})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="249" width="564" height="18" uuid="43014ec1-c189-49e5-8487-58964428d7e6">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[3. Kết quả điều trị]]></text>
			</staticText>
			<textField>
				<reportElement x="1" y="267" width="18" height="18" uuid="cea10af3-0e4b-4980-b8b1-504e96483c09">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="0">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CHECK_CHUYEN_NOI_KHAC}.equals( "true" ) ? "X" : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1" y="287" width="18" height="18" uuid="21bb0b4d-2e98-4723-a035-644c1077ab8f">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="0">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CHECK_NGUNG_DT_TU_NGUYEN}.equals( "true" ) ? "X" : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1" y="327" width="18" height="18" uuid="ea832ef0-25c2-443d-9f64-4e654c84c878">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="0">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CHECK_DA_CAI_NGHIEN}.equals( "true" ) ? "X" : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1" y="307" width="18" height="18" uuid="d0029e0d-0944-40f1-ae3b-975d667d013a">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="0">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CHECK_NGUNG_DT_BAT_BUOC}.equals( "true" ) ? "X" : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1" y="347" width="18" height="18" uuid="80de209b-1c41-4dc6-b67f-67fbb267633e">
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box leftPadding="0">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CHECK_TU_VONG}.equals( "true" ) ? "X" : ""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="19" y="267" width="235" height="18" uuid="071ac179-8166-4c99-a67e-0892c32125af"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Chuyển đi nới khác, ngày " + ($F{NGAY_CHUYEN_NOI_KHAC} == null ? "" : ($F{NGAY_CHUYEN_NOI_KHAC}.split("-")[2] + "/" + $F{NGAY_CHUYEN_NOI_KHAC}.split("-")[1] + "/" + $F{NGAY_CHUYEN_NOI_KHAC}.split("-")[0]))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="254" y="267" width="310" height="18" uuid="75ec1ae0-4c88-4daa-a746-6aaf3b7b9018"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Nơi chuyển đến: " + ($F{QKDT_NOI_CHUYEN_DEN} == null ? "" : $F{QKDT_NOI_CHUYEN_DEN})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="19" y="287" width="545" height="18" uuid="0d464de6-d174-4695-a738-e1e3ab7b6a11"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngưng điều trị tự nguyện, ngày " + ($F{NGAY_NGUNG_DT_TU_NGUYEN} == null ? "" : ($F{NGAY_NGUNG_DT_TU_NGUYEN}.split("-")[2] + "/" + $F{NGAY_NGUNG_DT_TU_NGUYEN}.split("-")[1] + "/" + $F{NGAY_NGUNG_DT_TU_NGUYEN}.split("-")[0]))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="19" y="308" width="545" height="18" uuid="5693f3fd-210b-469d-b63a-a997fc37b2bd"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngưng điều trị bắt buộc, ngày " + ($F{NGAY_NGUNG_DT_BAT_BUOC} == null ? "" : ($F{NGAY_NGUNG_DT_BAT_BUOC}.split("-")[2] + "/" + $F{NGAY_NGUNG_DT_BAT_BUOC}.split("-")[1] + "/" + $F{NGAY_NGUNG_DT_BAT_BUOC}.split("-")[0]))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="19" y="327" width="545" height="18" uuid="60a7c277-c3af-49ba-8675-479d2d5dd191"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Đã cai nghiện methadone " + ($F{DA_CAI_NGHIEN_METHADONE} == null ? "" : $F{DA_CAI_NGHIEN_METHADONE})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="19" y="347" width="545" height="18" uuid="2e5261cf-bc0d-4ea0-90fe-f1055d8beb29"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Tử vong, ngày " + ($F{NGAY_TU_VONG} == null ? "" : ($F{NGAY_TU_VONG}.split("-")[2] + "/" + $F{NGAY_TU_VONG}.split("-")[1] + "/" + $F{NGAY_TU_VONG}.split("-")[0]))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="365" width="564" height="25" uuid="279ba846-a751-489d-8d35-63dea9bd8edc"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Lý do: " + ($F{LY_DO_TU_VONG} == null ? "" : $F{LY_DO_TU_VONG})]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="140" y="489" width="60" height="20" uuid="fe5e22f9-4a9a-4a06-8823-009b189d282f"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTO_SIEUAM} == null ? "" : $F{SOTO_SIEUAM}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="393" y="448" width="172" height="121" uuid="dc8c8cc8-9c0d-4702-8d78-db368a639ec6"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="20"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="0" y="469" width="140" height="20" uuid="807381f2-c375-4126-94e0-c3f5e4825f7a"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<text><![CDATA[- CT Scanner]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="140" y="449" width="60" height="20" uuid="6fe66a60-47c1-4ea9-8920-1f49b92132ab"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTO_XQUANG} == null ? "" : $F{SOTO_XQUANG}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="0" y="509" width="140" height="20" uuid="c150b25a-40a8-46c6-b7ba-c6583ed3e7ca"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<text><![CDATA[- Xét nghiệm]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="140" y="429" width="60" height="20" uuid="439ed51c-c98c-46cf-96de-d4d79587d47e"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Số tờ]]></text>
			</staticText>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="201" y="449" width="192" height="40" uuid="7bdae0ef-950e-4a8b-b52a-7d1a2a5c017e">
					<printWhenExpression><![CDATA[$F{ANCHUKY}.equals("0")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Bottom" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Họ tên: " + ($F{NGUOIGIAO_HOSO} == null ? "" : $F{NGUOIGIAO_HOSO})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="0" y="549" width="140" height="20" uuid="25f65ce3-265f-490f-ba25-593f2053b7ed"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<text><![CDATA[- Toàn bộ hồ sơ]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="201" y="489" width="192" height="20" uuid="546e76e3-533c-47eb-b965-4c877bf5205a"/>
				<box>
					<topPen lineWidth="0.3"/>
					<leftPen lineWidth="0.3"/>
					<bottomPen lineWidth="0.3"/>
					<rightPen lineWidth="0.3"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="20"/>
				</textElement>
				<text><![CDATA[Người nhận hồ sơ:]]></text>
			</staticText>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="393" y="528" width="171" height="41" uuid="4643dc17-cc65-437e-89f8-0712c4ae404c">
					<printWhenExpression><![CDATA[$F{ANCHUKY}.equals("0")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Bottom" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Họ tên: " + ($F{BACSIDIEUTRI} == null ? "" : $F{BACSIDIEUTRI})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="0" y="489" width="140" height="20" uuid="2a9667ae-dc0e-4c30-a791-8fa15a361852"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<text><![CDATA[- Siêu âm]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="140" y="509" width="60" height="20" uuid="c1b5083f-e9c8-4939-80e3-a8458bf2fe18"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTO_XETNGHIEM} == null ? "" : $F{SOTO_XETNGHIEM}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="140" y="549" width="60" height="20" uuid="06047307-07d7-4dd1-ab5a-f916da44faa9"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTO_TOANBOHS} == null ? "" : $F{SOTO_TOANBOHS}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="0" y="449" width="140" height="20" uuid="fecb9a70-dbd1-4989-865e-8ed8bb4f2819"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<text><![CDATA[- X - quang]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="201" y="409" width="192" height="20" uuid="0e4458af-e3d2-45d9-aa5d-484f5c6f600b"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.3"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="20"/>
				</textElement>
				<text><![CDATA[Người giao hồ sơ:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="393" y="429" width="172" height="20" uuid="de6e8588-623e-42ae-88a9-75f2a899fd09"/>
				<box>
					<topPen lineWidth="0.3"/>
					<leftPen lineWidth="0.3"/>
					<bottomPen lineWidth="0.3"/>
					<rightPen lineWidth="0.3"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="20"/>
				</textElement>
				<text><![CDATA[Bác sĩ điều trị]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="201" y="509" width="192" height="60" uuid="29e7f369-7115-425f-98cb-b27f4a849d8d"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="20"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="0" y="409" width="200" height="20" uuid="d89812a9-b6fc-4fc2-898d-d809110fb3b6"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="20"/>
				</textElement>
				<text><![CDATA[Hồ sơ, phim, ảnh]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="140" y="469" width="60" height="20" uuid="feb5c7e8-104c-4741-8b59-803e0226fb3c"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTO_CTSCANNER} == null ? "" : $F{SOTO_CTSCANNER}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="0" y="529" width="140" height="20" uuid="2a8c25f1-592b-46e6-94e1-87f24f81d737"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<text><![CDATA[- Khác:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="140" y="529" width="60" height="20" uuid="37fd5877-98ff-44d3-a252-1091efcb566f"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOTO_KHAC} == null ? "" : $F{SOTO_KHAC}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="0" y="429" width="140" height="20" uuid="2ca6fb8b-ee79-4c57-bb5a-16ff86bb8e68"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph leftIndent="20"/>
				</textElement>
				<text><![CDATA[Loại]]></text>
			</staticText>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="201" y="529" width="192" height="40" uuid="517859d9-9deb-4b72-9e70-afbb8429039f">
					<printWhenExpression><![CDATA[$F{ANCHUKY}.equals("0")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Bottom" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Họ tên: " + ($F{NGUOINHAN_HOSO} == null ? "" : $F{NGUOINHAN_HOSO})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="393" y="409" width="172" height="20" uuid="8e7388cf-e9f1-4ebe-bb0f-0db6e4aa893b"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.25"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY_TONGKET} == null ? "Ngày..... tháng..... năm ........" : $F{NGAY_TONGKET}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="280" y="688" width="284" height="18" uuid="5f1c8330-337a-4485-8cfc-8d1b37897baf"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOTEN_BACSI} == null ? "" : $F{HOTEN_BACSI}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="280" y="625" width="285" height="18" uuid="d82c5f8a-6f0b-4cac-be8a-818c2d688838"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<text><![CDATA[(Ký tên, đóng dấu)]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="0" y="625" width="280" height="18" uuid="43ac9e9d-5dd9-40c1-899b-5119e921e822"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
				</textElement>
				<text><![CDATA[(Ký tên, đóng dấu)]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="0" y="688" width="280" height="18" uuid="591ad193-60cc-4e4a-a8a8-1ade74cc74bf"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOTEN_THU_TRUONG} == null ? "" : $F{HOTEN_THU_TRUONG}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="280" y="607" width="285" height="18" uuid="33e54037-858b-40bf-89d6-1b2bf2ee2e80"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Bác sĩ làm bệnh án]]></text>
			</staticText>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="280" y="589" width="285" height="18" uuid="86853a59-3dff-4a0e-975a-2124523f6dda"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY_KY_BACSI} == null ? "Ngày......tháng.......năm........" : $F{NGAY_KY_BACSI}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="0" y="607" width="280" height="18" uuid="78f2911d-2832-4ab6-bcb9-090ea5dc44e6"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Thủ trưởng cơ sở điều trị methadone]]></text>
			</staticText>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="0" y="589" width="280" height="18" uuid="a65edf55-ae3e-425f-bfb5-945d81c6308e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY_KY_THU_TRUONG} == null ? "Ngày ....tháng ...... năm ......." : $F{NGAY_KY_THU_TRUONG}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="0" y="390" width="565" height="18" uuid="3fe3d3e1-6c19-4f06-ac9e-2ad9b371d5da">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Bàn giao hồ sơ:]]></text>
			</staticText>
		</band>
	</title>
</jasperReport>
