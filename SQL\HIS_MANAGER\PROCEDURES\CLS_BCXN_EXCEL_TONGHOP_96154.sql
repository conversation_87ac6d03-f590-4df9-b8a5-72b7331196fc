create or replace PROCEDURE         "HIS_MANAGER"."CLS_BCXN_EXCEL_TONGHOP_96154" (
    p_tungay IN VARCHAR2,
    p_denngay IN VARCHAR2,
    p_dvtt IN VARCHAR2,
    p_trangthai IN NUMBER,
    p_cobhyt IN NUMBER,
    cur OUT SYS_REFCURSOR
)
IS
    v_tungay DATE;
    v_denngay DATE;
BEGIN
    v_tungay := TO_DATE(p_tungay, 'yyyy-mm-dd');
    v_denngay := TO_DATE(p_denngay, 'yyyy-mm-dd');
    HIS_MANAGER.cmu_instemp_ctdata(p_dvtt, v_tungay, v_denngay, 2);
OPEN cur FOR
SELECT
    pb.TEN_PHONG_BENH AS ten_phong_benh,
    ct.tendichvu AS ten_dich_vu,
    ct.dongia AS dongia,
    SUM(ct.soluong) AS soluong,
    ct.maphongban_cd AS maphongban_cd,
    SUM(ct.dongia * ct.soluong) AS thanh_tien
FROM CMU_CHIPHINOITRU_CT_PT_TMP ct
         LEFT JOIN his_manager.dm_phong_benh pb ON pb.MA_PHONG_BENH = ct.maphongban_cd
WHERE ct.dvtt = p_dvtt
  AND ct.NGAYRAVIEN BETWEEN v_tungay AND v_denngay
GROUP BY pb.TEN_PHONG_BENH, ct.tendichvu, ct.dongia, ct.maphongban_cd
ORDER BY ct.maphongban_cd;
END;