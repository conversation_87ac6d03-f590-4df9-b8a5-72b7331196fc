<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="TODIEUTRI" pageWidth="591" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="551" leftMargin="20" rightMargin="20" topMargin="10" bottomMargin="3" whenResourceMissingType="Error" uuid="0f5e3019-005e-44fe-bb8f-29ae14f10109">
	<property name="ireport.zoom" value="1.6105100000000045"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 2_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 3_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table 4_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<subDataset name="Table Dataset 1" uuid="c25a51bd-5c1a-4447-9f03-09a72ac99524">
		<parameter name="parameter1" class="java.lang.String"/>
		<parameter name="parameter2" class="java.lang.String"/>
		<parameter name="dvtt1" class="java.lang.String">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="mabenhnhan1" class="java.lang.String">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<queryString>
			<![CDATA[call YBI_KHUONG_TODIEUTRU(17013,779943)]]>
		</queryString>
		<field name="ngaygio" class="java.sql.Timestamp"/>
		<field name="mabenhnhan" class="java.lang.String">
			<fieldDescription><![CDATA[]]></fieldDescription>
		</field>
		<field name="stt_dotdieutri" class="java.lang.String">
			<fieldDescription><![CDATA[]]></fieldDescription>
		</field>
		<field name="dvtt" class="java.lang.String">
			<fieldDescription><![CDATA[]]></fieldDescription>
		</field>
		<field name="DIEN_BIEN_BENH" class="java.lang.String"/>
		<field name="tdt_ylenh" class="java.lang.String">
			<fieldDescription><![CDATA[]]></fieldDescription>
		</field>
		<field name="tdt_ghichu" class="java.lang.String">
			<fieldDescription><![CDATA[]]></fieldDescription>
		</field>
		<field name="Y_LENH" class="java.lang.String">
			<fieldDescription><![CDATA[]]></fieldDescription>
		</field>
	</subDataset>
	<parameter name="tenbenhvien" class="java.lang.String"/>
	<parameter name="ten_phongban" class="java.lang.String"/>
	<parameter name="hovaten" class="java.lang.String"/>
	<parameter name="tuoi" class="java.lang.String"/>
	<parameter name="gioitinh" class="java.lang.String"/>
	<parameter name="sogiuong" class="java.lang.String"/>
	<parameter name="buong" class="java.lang.String"/>
	<parameter name="chandoan" class="java.lang.String"/>
	<parameter name="tensoyte" class="java.lang.String"/>
	<parameter name="sophieu" class="java.lang.String"/>
	<parameter name="dvtt" class="java.lang.String"/>
	<parameter name="sovaovien" class="java.lang.String"/>
	<parameter name="soyte" class="java.lang.String"/>
	<parameter name="tenkhoa" class="java.lang.String"/>
	<parameter name="stt_benhan" class="java.lang.String"/>
	<parameter name="id_truyendich" class="java.lang.String"/>
	<parameter name="id_dottruyen" class="java.lang.String"/>
	<parameter name="loaingay" class="java.lang.String"/>
	<parameter name="tenkhoakham" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{
      call  CMU_NOI_PHIEUTRUYENDICH_P_V4(
	$P{dvtt},
	$P{sovaovien},
	$P{stt_benhan},
	2,
	$P{id_truyendich},
	$P{id_dottruyen},
	$P{loaingay},
	$P{ORACLE_REF_CURSOR}
      )
}]]>
	</queryString>
	<field name="icd" class="java.lang.String"/>
	<field name="giuong" class="java.lang.String"/>
	<field name="buong" class="java.lang.String"/>
	<field name="ngay" class="java.lang.String"/>
	<field name="tenthuoc" class="java.lang.String"/>
	<field name="sogiot" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="solo" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="giobd" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="giott" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="giokt" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="bacsi" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="mach" class="java.lang.String"/>
	<field name="huyetap" class="java.lang.String"/>
	<field name="nhietdo" class="java.lang.String"/>
	<field name="nhiptho" class="java.lang.String"/>
	<field name="yta" class="java.lang.String"/>
	<field name="hovaten" class="java.lang.String"/>
	<field name="tuoi" class="java.lang.String"/>
	<field name="gioitinh" class="java.lang.String"/>
	<field name="SOLUONGTRUYEN_DOT" class="java.lang.String"/>
	<field name="TRANGTHAI" class="java.lang.String"/>
	<field name="ID_TRUYENDICH" class="java.lang.String"/>
	<field name="KEYSIGN" class="java.lang.String"/>
	<field name="NGAY_KY" class="java.lang.String"/>
	<field name="id_truyendich_chitiet" class="java.lang.String"/>
	<field name="dang_ky" class="java.lang.String"/>
	<field name="ngay_dang_ky" class="java.lang.String"/>
	<group name="g-nhom">
		<groupExpression><![CDATA[$F{ngay}]]></groupExpression>
		<groupHeader>
			<band height="23">
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="551" height="23" uuid="d36b7ce8-ffad-44ea-8331-de2ea5e5ce65"/>
					<box leftPadding="3">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ngay}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="id_truyendich_group">
		<groupExpression><![CDATA[$F{ID_TRUYENDICH}]]></groupExpression>
		<groupHeader>
			<band/>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="120">
			<printWhenExpression><![CDATA[$V{PAGE_NUMBER}%2 == 1]]></printWhenExpression>
			<textField>
				<reportElement x="397" y="15" width="154" height="32" uuid="bd9d1cd4-e227-45d2-9de6-11dc7679da11"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["Số vào viện: " + $P{sovaovien}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="397" y="0" width="154" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="82acb37e-77b0-4ae7-ad1b-8fc2b72e537f"/>
				<textElement verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[MS: 07/BV-02]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="262" y="68" width="289" height="20" uuid="9839ca9b-abce-4755-a4b7-8ab043ca7c19"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{buong}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="95" y="48" width="276" height="20" uuid="d924e2d2-1cd5-4681-8a62-dca3e5f1aa1e">
					<printWhenExpression><![CDATA[$F{TRANGTHAI}.equals("0")]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{hovaten}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="48" width="95" height="20" uuid="b168425a-7d90-40d6-9637-b31cba329986"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[- Họ tên người bệnh:]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="68" width="65" height="20" uuid="f4ba5bbe-551e-46e7-9da3-78428906d3bf"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[- Số giường:]]></text>
			</staticText>
			<textField>
				<reportElement x="399" y="48" width="52" height="20" uuid="942d4194-5b23-48d3-bc7c-fb8fb4319415"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tuoi}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="247" y="0" width="150" height="48" uuid="ded79c50-5116-4636-b6a9-d4e953b03c52"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="16" isBold="true"/>
				</textElement>
				<text><![CDATA[PHIẾU THEO DÕI TRUYỀN DỊCH]]></text>
			</staticText>
			<staticText>
				<reportElement x="222" y="68" width="40" height="20" uuid="ba96b0e5-7075-4c64-add5-fdd1862dedbb"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Buồng:]]></text>
			</staticText>
			<staticText>
				<reportElement x="371" y="48" width="28" height="20" uuid="d5bbc1cc-f9ee-42a8-8a44-c2b83398af4b"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Tuổi:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="65" y="68" width="157" height="20" uuid="eaffd7e6-629c-4caa-a9e1-d46e3d847e80"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{giuong}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement mode="Transparent" x="451" y="48" width="100" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="fb4a594a-ece5-4fc0-8e16-625f1297ee13"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Giới tính: "+ $F{gioitinh}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="65" y="88" width="486" height="32" uuid="cbc6d7b0-4049-403f-81e1-17106e4a3603"/>
				<box topPadding="4"/>
				<textElement>
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{icd}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="88" width="65" height="20" uuid="a66f819c-6c2f-436f-9406-876168d24430"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[- Chẩn đoán:]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement mode="Transparent" x="0" y="30" width="243" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="eb4220dc-9d57-4a70-ac18-caae2183ab9d"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenkhoakham}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="15" width="243" height="15" uuid="eecb940b-0ade-4d58-b639-9098041b5f83"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenbenhvien}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="243" height="15" uuid="76ce2917-**************-7ba8419ed650"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{soyte}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="40" splitType="Stretch">
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="128" height="40" uuid="9211d7df-d845-4de7-9401-de1249d3c5a4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[DỊCH TRUYỀN/
HÀM LƯỢNG]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="128" y="0" width="30" height="40" uuid="033219bc-f8d4-439b-95c2-e69cb1ae1272"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Số lượng]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="158" y="0" width="50" height="40" uuid="7d5422df-76b9-4a77-8e3d-9914c43b9e56"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Lô/ số SX]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="208" y="0" width="38" height="40" uuid="a09bc604-2d99-4ed5-8275-d4c5ad9f3491"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Tốc độ giọt/ph]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="246" y="0" width="60" height="16" uuid="01c0318b-83db-4fe5-a8bc-270d25db0c3a"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Thời gian]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="246" y="16" width="30" height="24" uuid="81e0a976-19d7-4159-9230-592051efa879"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Bắt đầu]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="276" y="16" width="30" height="24" uuid="25c6539f-8ae1-4eb7-a7db-b64c47b88855"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Kết thúc]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="306" y="0" width="55" height="40" uuid="8f6ca979-ed43-40d3-ba3e-1feeb9d4238f"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[BS chỉ định]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="466" y="0" width="85" height="40" uuid="767e74ca-e5f3-4855-aa22-50c9d9c6bed5"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Điều dưỡng]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="361" y="0" width="105" height="16" uuid="f6fefd51-3978-433a-b04f-6e6125a7917b"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Dấu sinh hiệu]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="361" y="16" width="24" height="24" uuid="088720c7-14cb-4dc4-b9a0-8c22412aae75"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[M]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="385" y="16" width="32" height="24" uuid="702c679a-a3a4-4479-9709-b0514d59c745"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[HA]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="417" y="16" width="24" height="24" uuid="a15f1f9e-d31d-4bf3-b9ef-2bf7bff01925"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[N/độ]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="441" y="16" width="25" height="24" uuid="16efb94d-4dae-4a75-90ee-8a7fa06f30ec"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Nhịp thở]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="28" splitType="Prevent">
			<elementGroup/>
			<elementGroup/>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="128" height="28" uuid="e4d5815e-96b8-4471-8af4-479f7337d13e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{id_dottruyen}.equals("-1") ? ($F{TRANGTHAI}.equals("0") || $F{TRANGTHAI}.equals("4") ? $F{tenthuoc} : "") : $F{tenthuoc}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="128" y="0" width="30" height="28" uuid="6e5d9bd4-0e59-4b5f-9986-2fb437663a5f"/>
				<box leftPadding="2" rightPadding="2">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{id_dottruyen}.equals("-1") ? ($F{TRANGTHAI}.equals("0") || $F{TRANGTHAI}.equals("4") ? $F{SOLUONGTRUYEN_DOT} : "") : $F{SOLUONGTRUYEN_DOT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="158" y="0" width="50" height="28" uuid="1ec95874-ebe6-4984-b603-2569ba562bc1"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{id_dottruyen}.equals("-1") ? ($F{TRANGTHAI}.equals("0") || $F{TRANGTHAI}.equals("4") ? $F{solo} : "") : $F{solo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="208" y="0" width="38" height="28" uuid="40709a55-bf7b-4493-8ab3-0e8c2c662344"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{sogiot}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="246" y="0" width="30" height="28" uuid="5f03ff91-ca23-4d5c-a476-4d3c36c1c228"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[!$F{TRANGTHAI}.equals("4") ? $F{giobd} : ""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="306" y="0" width="55" height="28" uuid="873ba3e3-aeed-452a-be89-1d0e9d65fa87"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{bacsi}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="361" y="0" width="24" height="28" uuid="a645fcfb-5f7b-4dc3-8ecb-9503d97f2bad"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mach}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="385" y="0" width="32" height="28" uuid="0460898f-e508-495f-91fd-099821e2e278"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{huyetap}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="417" y="0" width="24" height="28" uuid="4096454c-06d1-4630-b89d-8be91647cd0e"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nhietdo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="441" y="0" width="25" height="28" uuid="503c6408-4928-40be-9c12-4a20efee60e4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nhiptho}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="276" y="0" width="30" height="28" uuid="c0c3487d-4970-4a78-81e5-31d3245457db"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[!$F{TRANGTHAI}.equals("3") ? $F{giokt} : ""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="246" y="0" width="30" height="28" uuid="9d35d18d-0ef6-4cc4-a69c-7744d4af2faa"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TRANGTHAI}.equals("4") ? $F{giobd} : ""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="276" y="0" width="30" height="28" uuid="ccc2e887-f9ea-4130-bde4-2e06576172f7"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TRANGTHAI}.equals("3") ? $F{giokt} : ""]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement stretchType="RelativeToTallestObject" x="466" y="0" width="85" height="28" uuid="418488ec-e77e-4147-a1b6-a3ba56f3b615"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="0" y="14" width="85" height="14" isPrintWhenDetailOverflows="true" uuid="4de2d37e-df2f-444c-a062-a86e72bd3594"/>
					<box leftPadding="5">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{dang_ky}.equals("1") ? $F{ngay_dang_ky} : $F{NGAY_KY}]]></textFieldExpression>
				</textField>
				<image>
					<reportElement x="61" y="4" width="20" height="20" uuid="65916ed3-6e02-4158-b4a2-4bd77d8c49b7">
						<printWhenExpression><![CDATA[$F{KEYSIGN} != null || $F{dang_ky}.equals("1")]]></printWhenExpression>
					</reportElement>
					<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.getInstance(
new SimpleJasperReportsContext()).loadAwtImageFromBytes(Base64.getDecoder().decode("iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAABvElEQVR4nO3U3UrCYBjAcS+gAy0XdjAs925uO+huBGfnnYkmQX7kxwQvoNqJdAmFSEFQptP5RdDRBg26jig6euKxrFdbVjo66oGHuZP/j9cX5vH8z1+OcFv0y0M1hU/X48SoMFK/ZMoDFaRh2ZZvigF3472SJfVVkAYqICIPy5YrJyFGhgl3C5bYK4HUx/1AxIGaXDguGAVL7BZB7OFOIIudgLzGzbBRgDACNNIrfb6Dpari9x0pKXz+JM538ma4k4cRQCPdgi3r0/GDCOPTFNOnxcCrxWxGiwRmxvWcKbTzICBAI4ZTvKr4vUeKhfHx4juiTnHSypq8vg9CG5dG8p/jOPi30PGvEILxZtbkWzngddwPhO/sO8e/OsE0Qi4wnjFJMwsEAQrh2zPiTncwvcva1n2okb4j1xkYARRCWjk7+F18FrKibcH6eQq4RgY4BCiEa/4i7oRgPHi2A6GrNHCN9ATCXWftoL4737cGEe9B1GLrSQhd7kHoam8KSVt4J3PF35FKhGFriccNBChkvZ54XDg+nrXqtsjWEk9jJFhPPK8eRzddib8jh9siexJ/YE/jD/jb1fj/eN7mBQZhd5OoxlU0AAAAAElFTkSuQmCC")
)]]></imageExpression>
				</image>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="0" y="0" width="85" height="14" isPrintWhenDetailOverflows="true" uuid="e137a467-9a79-4315-9c62-056c3fc9480a"/>
					<box leftPadding="5">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" markup="html">
						<font fontName="Times New Roman" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{dang_ky}.equals("1") ? $F{yta} + "<i style='color:white'>.</i>" : $F{yta}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="15">
			<textField pattern="" isBlankWhenNull="false">
				<reportElement mode="Transparent" x="0" y="0" width="551" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="15aa39e7-6770-4a50-a48b-b1c136e267d7"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Bottom" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang  "+ $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band/>
	</summary>
</jasperReport>
