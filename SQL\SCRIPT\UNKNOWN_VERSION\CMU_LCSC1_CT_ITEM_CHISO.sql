
CREATE TABLE "HIS_MANAGER"."CMU_LCSC1_CT_ITEM_CHISO"
(	"ID" NUMBER GENERATED ALWAYS AS IDENTITY MINVALUE 1 MAXVALUE 9999999999999999999999999999 INCREMENT BY 1 START WITH 1 CACHE 20 NOORDER  NOCYCLE  NOT NULL ENABLE,
     "ID_ITEM" NUMBER,
     "ID_CHI_TIET" NUMBER,
     "CHI_SO" VARCHAR2(255 BYTE),
     CONSTRAINT "UNIQ_ITEM_CHITIET" UNIQUE ("ID_ITEM", "ID_CHI_TIET")
         USING INDEX PCTFREE 10 IN<PERSON><PERSON>NS 2 MAXTRANS 255 COMPUTE STATISTICS
         STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
         PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
         BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
         T<PERSON><PERSON><PERSON>AC<PERSON> "DBHIS"  ENABLE
)
