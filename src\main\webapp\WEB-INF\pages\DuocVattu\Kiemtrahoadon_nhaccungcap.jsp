<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@page contentType="text/html" pageEncoding="UTF-8"  %>
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>         
        <meta name="google-site-verification" content="u6uNEfD4cb3gidezi4r_6aI8Wb1E07-ufBeCQpvmlqQ" />
        <title>Hệ thống chăm sóc sức khỏe</title>
        <link rel="icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/> 
        <link rel="shortcut icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/> 

        <!-- jQuery file -->

        <link href="<c:url value="/resources/css/divheader.css" />" rel="stylesheet"/>         
        <link href="<c:url value="/resources/css/style_new.css" />" rel="stylesheet"/>


        <!--Jquery-->
        <link rel="stylesheet" href="<c:url value="/resources/css/jquery-ui-redmond.1.9.1.css" />" />
        <script src="<c:url value="/resources/js/jquery.min.1.8.3.js" />"></script>    
        <script src="<c:url value="/resources/js/jquery-ui.1.9.1.js" />"></script>
        <!--Grid-->
        <link href="<c:url value="/resources/jqgrid/css/ui.jqgrid.css" />" rel="stylesheet"/>           
        <script src="<c:url value="/resources/jqgrid/js/i18n/grid.locale-en.js" />"></script>            
        <script src="<c:url value="/resources/jqgrid/js/jquery.jqGrid.src.js" />"></script>
        <script src="<c:url value="/resources/js/common_function.js" />"></script>
        <script src="<c:url value="/resources/js/jquery.inputmask.bundle.min.js" />"></script>
        <!-- Êm 2016-09-12-->
        <link href="<c:url value="/resources/dialog/jquery.alerts.1.css" />" rel="stylesheet"/>           
        <script src="<c:url value="/resources/dialog/jquery.alerts.js" />"></script>
        <link href="<c:url value="/resources/dialog/jBox.css" />" rel="stylesheet"/>           
        <script src="<c:url value="/resources/dialog/jBox.js" />"></script>
        <link href="<c:url value="/resources/jqueryui/themes/redmond/jquery-ui.css" />" rel="stylesheet"/>
        <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
        <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>
        <!-- End Êm 2016-09-12-->
        <script>
            $(function() {
                $(":input").inputmask();
                $("#tungay").datepicker();
                $("#tungay").datepicker("option", "dateFormat", "dd/mm/yy");
                $("#denngay").datepicker();
                $("#denngay").datepicker("option", "dateFormat", "dd/mm/yy");
                $("#tungay").val("${ngayhientai}");
                $("#denngay").val("${ngayhientai}");
                // Êm ngày 2016-09-12
                var dialog_phieu = new jBox('Modal', {
                    title: "Phiếu Nhập",
                    overlay: false,
                    content: $('#dialogphieu'),
                    draggable: 'title'
                });
                // End ngày 2016-09-12
                $("#list_danhsach").jqGrid({
                    url: '',
                    datatype: "local",
                    loadonce: true,
                    height: 400,
                    width: 988,
                    colNames: ["Mã đơn vị","Kho vật tư", "Số phiếu nhập", "Sô hóa đơn", "Ngày HĐ","Ngày nhập", "Ghi chú", "Thành tiền","Nhà cung cấp", 
                        "Người tạo", "Số hợp đồng"],  //CMU: 07/11/2017
                    colModel: [
                        {name: 'MA_DONVI', index: 'MA_DONVI', width: 100, hidden: true},
                        {name: 'TENKHOVATTU', index: 'TENKHOVATTU', width: 100},
                        {name: 'SOPHIEUNHAP', index: 'SOPHIEUNHAP', width: 100},
                        {name: 'SOHOADON', index: 'SOHOADON', width: 100},
                        {name: 'NGAYHOADON', index: 'NGAYHOADON', width: 80, align: "center"},
                        {name: 'NGAYNHAP', index: 'NGAYNHAP', width: 80, align: "center"},
                        {name: 'GHICHU', index: 'GHICHU', width: 60},
                        {name: 'TONGTIEN', index: 'TONGTIEN',
                            width: 100,
                            align: "right",
                            summaryType: 'sum',
                            formatter: 'integer',
                            formatoptions:{decimalSeparator: ',', decimalPlaces: 3, thousandsSeparator:'.'},
                            fixed: true
                        },
                        {name: 'TENNHACC', index: 'TENNHACC', width: 250},
                        {name: 'TEN_NHANVIEN', index: 'TEN_NHANVIEN', width: 100},    //CMU: 07/11/2017
                        {name: 'SOHOPDONG', index: 'SOHOPDONG', width: 60}
                    ],
                    rowNum: 1000000,
                    caption: "Danh sách hóa đơn nhập từ nhà cung cấp",
                    ignoreCase: true,
                    footerrow: true,
                    loadComplete: function() {
                        var $self = $(this);
                        var sum_sl = $self.jqGrid("getCol", "TONGTIEN", false, "sum");
                        $self.jqGrid("footerData", "set", {TONGTIEN: sum_sl});
                        var count = $("#list_danhsach").getGridParam("records");
                        count = count + " phiếu";
                        $self.jqGrid("footerData", "set", {TENKHOVATTU: count});
                    }
                // Êm 2016-09-12
                    ,onRightClickRow: function (id1) {
                            //$('#list_phieu').jqGrid('setSelection', id1);
                            stt = id1;
                            $.contextMenu({
                                selector: '#list_danhsach tr',
                                callback: function (key, options) {
                                    if (key == "xemchitiet") {
                                        $("#list_thongke_phieu_theo_dc_vt").jqGrid("clearGridData");
                                        $('#list_danhsach').jqGrid('setSelection', id1);
                                        var ret = $("#list_danhsach").jqGrid('getRowData', stt);
                                        var ar_duoc = [ret.SOPHIEUNHAP, ret.MA_DONVI];
                                        var duoc = 'AGG_danhsachchitietphieunhapkho?url=' + convertArray(ar_duoc);
                                        $("#list_chitietphieunhap").jqGrid('setGridParam', {datatype: 'json', url: duoc}).trigger('reloadGrid');
                                        dialog_phieu.open();
                                    }
                                },
                                items: {
                                    "xemchitiet": {name: "<span style=' color: green; font-weight: bold'>Chi tiết phiếu nhập</span>"},
                                    "sep1": "---------"
                                }

                            });
                    }
                });
                $("#list_chitietphieunhap").jqGrid({
                    url: '',
                    datatype: "local",
                    loadonce: true,
                    height: 250,
                    rowNum: 1000000,
                    width: 800,
                    ignoreCase: true,
                    colNames: ["Tên dược/vật tư", "Hoạt chất", "Số lượng", "Đơn giá", "Thành tiền", "Đơn vị tính", "mavattu", "solosanxuat"],
                    colModel: [
                        {name: 'TENVATTU', index: 'TENVATTU', width: 160, align: 'left'},
                        {name: 'HOATCHAT', index: 'HOATCHAT', width: 140, align: 'left'},
                        {name: 'SOLUONG', index: 'SOLUONG', width: 60, align: 'right', formatter: 'integer', formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 0}},
                        {name: 'DONGIA', index: 'DONGIA', width: 80, align: 'right', formatter: 'integer', formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}},
                        {name: 'THANHTIEN', index: 'THANHTIEN', width: 80, align: 'right', formatter: 'integer', formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}},
                        {name: 'DVT', index: 'DVT', width: 80, align: 'right'},
                        {name: 'MAVATTU', index: 'MAVATTU', width: 80, align: 'right', hidden: true},
                        {name: 'SOLOSANXUAT', index: 'SOLOSANXUAT', width: 80, align: 'right', hidden: true}
                    ],
                    caption: "Danh sách dược/vật tư trong phiếu nhập",
                    onSelectRow: function (id) {
                        if (id) {
                            var rowCount = $("#list_danhsach").getGridParam("reccount");
                            var ds_sophieu = [];
                            for (var i=1; i<=rowCount; i++){
                                ds_sophieu[i] = $("#list_danhsach").jqGrid ('getCell', i, 'SOPHIEUNHAP');
                            }
                            var row = $("#list_chitietphieunhap").jqGrid('getRowData', id);

                            var params = {
                                pDsSoPhieu      : ds_sophieu.join(),
                                pMaVatTu        : row.MAVATTU,
                                pDonGia         : row.DONGIA,
                                pSoLoSanXuat    : row.SOLOSANXUAT
                            }
                            var url = String.format("kiemtrahoadon_ncc_dsphieu_theo_dc?params={0}", encodeURI(JSON.stringify(params)));
                            $("#list_thongke_phieu_theo_dc_vt").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
                        }
                    },
                    footerrow: true,
                    loadComplete: function () {
                        var $self = $(this);
                        var sl = $("#list_chitietphieunhap").getGridParam("records");
                        sl = sl + " dược/vật tư";
                        $self.jqGrid("footerData", "set", {TenVatTu: sl});
                        var sum_sld = $self.jqGrid("getCol", "ThanhTien", false, "sum");
                        $self.jqGrid("footerData", "set", {ThanhTien: sum_sld});
                    }
                });                
        //End Êm ngày 2016-09-12
                $("#list_danhsach").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
                $("#hienthi").click(function(evt) {
                    // vinh long them dieu kien loc kho
                    var makhovattu = $("#khovattu").val();
                    //end vinh long
                    //vinh long them ds nha cung cap
                     var mancc = $("#nhacc").val();
                     var ma_nguonduoc =$("#ma_nguonduoc").val();
                    //end vinh long
                    var tungay = $("#tungay").val();
                    var denngay = $("#denngay").val();
                    /// vinh long co them gia tri makhovattu - mancc vao mang arr
                    /// BDH thêm đơn vị xuất
                    var donvixuat = $("#donvixuat").val();
                    var check = $("#theongaynhap").prop("checked");
                    if ("${theo_donvi}" == 1 ) {
                        if (check == false) {
                            check = 0;
                        } else {
                            check = 1;
                        }
                        var arr = ["${Sess_DVTT}", tungay, denngay, makhovattu, mancc, ma_nguonduoc, donvixuat, check];
                        var url = "danhsach_hoadon_nhacungcap_donvi?url=" + convertArray(arr);
                        $("#list_danhsach").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
                    } else {
                        var arr = ["${Sess_DVTT}", tungay, denngay, makhovattu, mancc, ma_nguonduoc];
                        if (check == false) {
                            check = 0;
                        } else {
                            check = 1;
                        }
                        var arr = ["${Sess_DVTT}", tungay, denngay, makhovattu, mancc, ma_nguonduoc, check];
                        var url = "danhsach_hoadon_nhacungcap?url=" + convertArray(arr);
                        $("#list_danhsach").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
                    }
                });
                 $("#inhd2").click(function(evt) {
                    var tungay = convertStr_MysqlDate($("#tungay").val());
                    var denngay = convertStr_MysqlDate($("#denngay").val());
                    var check = $("#theongaynhap").prop("checked")==true?"1":"0";
                    //vinh long bo sung them mancc va ma nguon duoc
                    var makho = $("#khovattu").val();
                    var manhacc = $("#nhacc").val();
                    var ma_nguonduoc =$("#ma_nguonduoc").val();
                    //end vinh long
                    var arr = ["${Sess_DVTT}", tungay, denngay, check, makho, manhacc,ma_nguonduoc,$("#typeexport").val()];
                    var url = "insokiemke_hoadon_nhacungcap?url=" + convertArray(arr);
                    $(location).attr('href', url);
                });
                //VLG THÊM IN SỔ KIỂM NHẬP THEO NCC
                $("#inhd_ncc").click(function(evt) {
                    var tungay = convertStr_MysqlDate($("#tungay").val());
                    var denngay = convertStr_MysqlDate($("#denngay").val());
                    var check = $("#theongaynhap").prop("checked")==true?"1":"0";
                    //vinh long bo sung them mancc va ma nguon duoc
                    var makho = $("#khovattu").val();
                    var manhacc = $("#nhacc").val();
                    var ma_nguonduoc =$("#ma_nguonduoc").val();
                    //end vinh long
                    var arr = ["${Sess_DVTT}", tungay, denngay, check, makho, manhacc,ma_nguonduoc,$("#typeexport").val()];
                    var url = "insokiemke_theonhacc?url=" + convertArray(arr);
                    $(location).attr('href', url);
                });
                //VLG THÊM IN SỔ KIỂM NHẬP THEO NCC
                //vlg them so kiem nhap vi thuoc
                $("#insokiemnhapvithuoc").click(function(evt) {
                    var tungay = convertStr_MysqlDate($("#tungay").val());
                    var denngay = convertStr_MysqlDate($("#denngay").val());
                    var check = $("#theongaynhap").prop("checked")==true?"1":"0";
                    //vinh long bo sung them mancc va ma nguon duoc
                    var makho = $("#khovattu").val();
                    var manhacc = $("#nhacc").val();
                    var ma_nguonduoc =$("#ma_nguonduoc").val();
                    //end vinh long
                    var arr = ["${Sess_DVTT}", tungay, denngay, check, makho, manhacc,ma_nguonduoc,$("#typeexport").val()];
                    var url = "insokiemke_hoadon_nhacungcap_vithuoc?url=" + convertArray(arr);
                    $(location).attr('href', url);
                });
                $("#inbienbankiemnhapth").click(function (evt) {
                    var dvtt = "${Sess_DVTT}";
                    var check = $("#theongaynhap").prop("checked");
                    if (check == false) {
                        check = 0;
                    } else {
                        check = 1;
                    }
                    $(location).attr('href', 'inbienbankiemnhap_th?dvtt=' + dvtt + '&tungay=' + convertStr_MysqlDate($("#tungay").val()) + '&denngay=' + convertStr_MysqlDate($("#denngay").val())+'&type='+$("#typeexport").val() + '&check=' + check);
                });
                //end vlg
                $("#inhd").click(function(evt) {
                    var tungay = convertStr_MysqlDate($("#tungay").val());
                    var denngay = convertStr_MysqlDate($("#denngay").val());
                    var check = $("#theongaynhap").prop("checked")==true?"1":"0";
                    //vinh long bo sung them mancc va ma nguon duoc
                    var manhacc = $("#nhacc").val();
                    var ma_nguonduoc =$("#ma_nguonduoc").val();
                    //end vinh long
                    var arr = ["${Sess_DVTT}", tungay, denngay, check,$("#khovattu").val(),manhacc,ma_nguonduoc,$("#typeexport").val()];
                    var url = "indanhsach_hoadon_nhacungcap?url=" + convertArray(arr);
                    $(location).attr('href', url);
                });
                //vinh long them dieu su kien chang tu ngay den nay
                $("#tungay").change(function (evt) {
                    var tungay = $("#tungay").val();
                    var denngay = $("#denngay").val();
                    var ngaynhap = $("#theongaynhap").prop("checked")==true?"1":"0";
                    var arr2 = [${Sess_DVTT}, tungay, denngay, ngaynhap, "0"];
                    var url2 = 'vlg_lay_ncc_hoadon?url=' + convertArray(arr2);
                    $.ajax({
                        url: url2
                    }).done(function (data2) {
                        if (data2) {
                            $("#nhacc").empty();
                            $("<option value='-1'>Tất cả</option>").appendTo("#nhacc");
                            $.each(data2, function (i) {
                                $("<option value='" + data2[i].MANHACC + "'>" + data2[i].TENNHACC + "</option>").appendTo("#nhacc");
                            });
                        }
                    });
                    
                });
                $("#denngay").change(function (evt) {
                    var tungay = $("#tungay").val();
                    var denngay = $("#denngay").val();
                    var ngaynhap = $("#theongaynhap").prop("checked")==true?"1":"0";
                    var arr2 = [${Sess_DVTT}, tungay, denngay, ngaynhap, "0"];
                    var url2 = 'vlg_lay_ncc_hoadon?url=' + convertArray(arr2);
                    $.ajax({
                        url: url2
                    }).done(function (data2) {
                        if (data2) {
                            $("#nhacc").empty();
                            $("<option value='-1'>Tất cả</option>").appendTo("#nhacc");
                            $.each(data2, function (i) {
                                $("<option value='" + data2[i].MANHACC + "'>" + data2[i].TENNHACC + "</option>").appendTo("#nhacc");
                            });
                        }
                    });
                    
                });
                //end vinh long
                
                // vinh long in bien bang kiem nhap theo nha cung cap
                $("#inbbkkncc").click(function(evt) {
                    var tungay = convertStr_MysqlDate($("#tungay").val());
                    var denngay = convertStr_MysqlDate($("#denngay").val());
                    var makhovattu = $("#khovattu").val();
                    var check = $("#theongaynhap").prop("checked")==true?"1":"0";
                    //vinh long bo sung them mancc va ma nguon duoc
                    var manhacc = $("#nhacc").val();
                    var ma_nguonduoc =$("#ma_nguonduoc").val();
                    var arr = ["${Sess_DVTT}", tungay, denngay, check, makhovattu,manhacc,ma_nguonduoc,$("#typeexport").val()];
                    var url;
                    var tieuDe = "Từ ngày " + tungay + " đến ngày " + denngay;
                    if("${Sess_DVTT}" == "96176") {
                        var typeIn = $('#typeexport').val() == '0' ? 'pdf' : 'xls'
                        var params = {
                            tungay: $("#tungay").val(),
                            denngay: $("#denngay").val(),
                            tieude: "Từ ngày " + $("#tungay").val() + " đến ngày " + $("#denngay").val(),
                            khovattu: makhovattu,
                            manhacc: manhacc,
                            manguonduoc: ma_nguonduoc
                        }
                        url = "cmu_in_rp_bangkehoadontheonhacungcap_theongaynhap_96176?" + $.param(params)+"&type=" + typeIn;
                    } else{
                        url = "insokiemke_hoadon_theo_nhacungcap?url=" + convertArray(arr);
                    }
                    $(location).attr('href', url);
                });
                // end vinh long in bieng bang kiem ke
                $("#inbbkkncc_chitiet").click(function(evt) {
                    var tungay = convertStr_MysqlDate($("#tungay").val());
                    var denngay = convertStr_MysqlDate($("#denngay").val());
                    var makhovattu = $("#khovattu").val();
                    var check = $("#theongaynhap").prop("checked")==true?"1":"0";
                    //vinh long bo sung them mancc va ma nguon duoc
                    var manhacc = $("#nhacc").val();
                    var ma_nguonduoc =$("#ma_nguonduoc").val();
                    var tenkho = $("#khovattu option:selected").text();
                    var arr = ["${Sess_DVTT}", tungay, denngay, check, makhovattu,manhacc,ma_nguonduoc,$("#typeexport").val(),tenkho];
                    var url = "insokiemke_hoadon_theo_nhacungcap_chitiet?url=" + convertArray(arr);
                    $(location).attr('href', url);
                });
                $("#inchitiet").click(function(evt) {
                    var tungay = convertStr_MysqlDate($("#tungay").val());
                    var denngay = convertStr_MysqlDate($("#denngay").val());
                    var makhovattu = $("#khovattu").val();
                    var check = $("#theongaynhap").prop("checked")==true?"1":"0";
                    //vinh long bo sung them mancc va ma nguon duoc
                    var manhacc = $("#nhacc").val();
                    var ma_nguonduoc =$("#ma_nguonduoc").val();
                    var arr = ["${Sess_DVTT}", tungay, denngay, check, makhovattu,manhacc,ma_nguonduoc,$("#typeexport").val()];
                    var url = "insokiemke_chitiet?url=" + convertArray(arr);
                    $(location).attr('href', url);
                });
                
                $("#xuatexcel").click(function(evt){
                    var tungay = $("#tungay").val();
                    var denngay = $("#denngay").val();
                    var check = $("#theongaynhap").prop("checked") == true ? "1" : "0";
                    var manhacc = $("#nhacc").val();
                    var ma_nguonduoc =$("#ma_nguonduoc").val();
                    var arr = ["${Sess_DVTT}", tungay, denngay, check,$("#khovattu").val(),manhacc,ma_nguonduoc];
                    var url = "indanhsach_hoadon_nhacungcap_excel?url=" + convertArray(arr);
                    $(location).attr('href', url);
                });

                $("#list_thongke_phieu_theo_dc_vt").jqGrid({
                    url: '',
                    datatype: "local",
                    loadonce: true,
                    height: 250,
                    rowNum: 1000000,
                    width: 800,
                    ignoreCase: true,
                    colNames: ["Kho vật tư", "Số phiếu nhập", "Số hoá đơn", "Ngày", "Tên VT", "Số lượng", "Đơn giá", "Thành tiền"],
                    colModel: [
                        {name: 'TENKHOVATTU', index: 'TENKHOVATTU', width: 160, align: 'left'},
                        {name: 'SOPHIEUNHAP', index: 'SOPHIEUNHAP', width: 120, align: 'left'},
                        {name: 'SOHOADON', index: 'SOHOADON', width: 120, align: 'left'},
                        {name: 'NGAYHOADON', index: 'NGAYHOADON', width: 100, align: 'left'},
                        {name: 'TENVATTU', index: 'TENVATTU', width: 160, align: 'left', hidden : true},
                        {name: 'SOLUONG', index: 'SOLUONG', width: 60, align: 'right', formatter: 'integer', formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 0}},
                        {name: 'DONGIA', index: 'DONGIA', width: 80, align: 'right', formatter: 'integer', formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}, hidden : false},
                        {name: 'THANHTIEN', index: 'THANHTIEN', width: 80, align: 'right', formatter: 'integer', formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}}
                    ],
                    caption: "Danh sách phiếu nhập theo Dược/VT",
                    footerrow: true,
                    loadComplete: function () {
                        var $self = $(this);
                        var sum_sl = $self.jqGrid("getCol", "SOLUONG", false, "sum");
                        var sum_thanhtien = $self.jqGrid("getCol", "THANHTIEN", false, "sum");
                        $self.jqGrid("footerData", "set", {SOLUONG: sum_sl});
                        $self.jqGrid("footerData", "set", {THANHTIEN: sum_thanhtien});
                    }
                });
                $("#list_thongke_phieu_theo_dc_vt").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});

                $("#theongaynhap").change(function (evt) {
                    var tungay = $("#tungay").val();
                    var denngay = $("#denngay").val();
                    var ngaynhap = $("#theongaynhap").prop("checked")==true?"1":"0";
                    var arr2 = [${Sess_DVTT}, tungay, denngay, ngaynhap, "0"];
                    var url2 = 'vlg_lay_ncc_hoadon?url=' + convertArray(arr2);
                    $.ajax({
                        url: url2
                    }).done(function (data2) {
                        if (data2) {
                            $("#nhacc").empty();
                            $("<option value='-1'>Tất cả</option>").appendTo("#nhacc");
                            $.each(data2, function (i) {
                                $("<option value='" + data2[i].MANHACC + "'>" + data2[i].TENNHACC + "</option>").appendTo("#nhacc");
                            });
                        }
                    });

                });
                $("#donvixuat").change(function (evt) {
                    var value = $("#donvixuat").val();
                    if (value == "1") {
                        $("#khovattu").attr("disabled", false);
                        $("#nhacc").attr("disabled", false);
                        $("#ma_nguonduoc").attr("disabled", false);
                        $("#inhd").attr("disabled", false);
                        $("#inhd").removeClass("button_disabled");
                        $("#inhd").addClass("button_shadow");
                        $("#inhd2").attr("disabled", false);
                        $("#inhd2").removeClass("button_disabled");
                        $("#inhd2").addClass("button_shadow");
                        $("#inbbkkncc").attr("disabled", false);
                        $("#inbbkkncc").removeClass("button_disabled");
                        $("#inbbkkncc").addClass("button_shadow");
                        $("#xuatexcel").attr("disabled", false);
                        $("#xuatexcel").removeClass("button_disabled");
                        $("#xuatexcel").addClass("button_shadow");
                        $("#inchitiet").attr("disabled", false);
                        $("#inchitiet").removeClass("button_disabled");
                        $("#inchitiet").addClass("button_shadow");
                        $("#insokiemnhapvithuoc").attr("disabled", false);
                        $("#insokiemnhapvithuoc").removeClass("button_disabled");
                        $("#insokiemnhapvithuoc").addClass("button_shadow");
                        $("#inbbkkncc_chitiet").attr("disabled", false);
                        $("#inbbkkncc_chitiet").removeClass("button_disabled");
                        $("#inbbkkncc_chitiet").addClass("button_shadow");
                        $("#inbienbankiemnhapth").attr("disabled", false);
                        $("#inbienbankiemnhapth").removeClass("button_disabled");
                        $("#inbienbankiemnhapth").addClass("button_shadow");
                    } else {
                        $("#khovattu").attr("disabled", true);
                        $("#nhacc").attr("disabled", true);
                        $("#ma_nguonduoc").attr("disabled", true);
                        $("#inhd").attr("disabled", true);
                        $("#inhd").addClass("button_disabled");
                        $("#inhd").removeClass("button_shadow");
                        $("#inhd2").attr("disabled", true);
                        $("#inhd2").addClass("button_disabled");
                        $("#inhd2").removeClass("button_shadow");
                        $("#inbbkkncc").attr("disabled", true);
                        $("#inbbkkncc").addClass("button_disabled");
                        $("#inbbkkncc").removeClass("button_shadow");
                        $("#xuatexcel").attr("disabled", true);
                        $("#xuatexcel").addClass("button_disabled");
                        $("#xuatexcel").removeClass("button_shadow");
                        $("#inchitiet").attr("disabled", true);
                        $("#inchitiet").addClass("button_disabled");
                        $("#inchitiet").removeClass("button_shadow");
                        $("#insokiemnhapvithuoc").attr("disabled", true);
                        $("#insokiemnhapvithuoc").addClass("button_disabled");
                        $("#insokiemnhapvithuoc").removeClass("button_shadow");
                        $("#inbbkkncc_chitiet").attr("disabled", true);
                        $("#inbbkkncc_chitiet").addClass("button_disabled");
                        $("#inbbkkncc_chitiet").removeClass("button_shadow");
                        $("#inbienbankiemnhapth").attr("disabled", true);
                        $("#inbienbankiemnhapth").addClass("button_disabled");
                        $("#inbienbankiemnhapth").removeClass("button_shadow");
                    }
                });
            });
        </script>
        <style>
            .width1{
                width:150px;	
            }
        </style>
    </head>
    <body> 
        <div id="panel_all">
            <%@include file="../../../resources/Theme/include_pages/menu.jsp"%>
            <div id="panelwrap">
                <div class="center_content">
                    <div class="panel_with_title"> 
                        <div class="panel_title">Kiểm tra hóa đơn nhập từ nhà cung cấp</div>
                        <div class="panel_body">
                            <form action="" method="get"><table width="1041" border="0" align="center">
                                    <tr>
                                        <td style="position:relative;left:10px;"  >Từ ngày</td>
                                        <td width="159"><input name="tungay" type="text" class="width1" id="tungay" data-inputmask="'alias': 'date'"/></td>
                                        <td width="66" align="left">Đến ngày</td>
                                        <td width="155" align="left"><input name="denngay" type="text" class="width1" id="denngay" data-inputmask="'alias': 'date'"/></td>
                                        <td width="571" align="left"><input type="button" name="hienthi" id="hienthi" value="Hiển thị" class="button_shadow"/>                                    
                                            <input type="button" name="inhd" id="inhd" value="In bản kiểm kê" class="button_shadow" style="width:150px"/>
                                            <input type="button" name="inhd2" id="inhd2" value="In sổ kiểm nhập" class="button_shadow" style="width:150px"/></td>
                                    </tr>
                                    <tr
                                        <!--VNPT vinh long them kho vat tu nha cung cap -->
                                        <td width="68" align ="right">Chọn kho</td>
                                        <td width="159" >
                                            <select name="khovattu"  id="khovattu" style="width:150px;" width = "150">
                                            <option value="-1">Tất cả...............</option>
                                            <c:forEach var="i" items="${khovattu}">
                                                <option value="${i.MaKhoVatTu}">${i.TenKhoVatTu}</option>
                                            </c:forEach>
                                            </select></td>
                                        <td >Nhà CC:</td>
                                        <td width="155" >
                                            <select name="nhacc"  id="nhacc" style="width:150px;" width = "150">
                                            <option value="-1">Tất cả...............</option>
                                            <c:forEach var="i" items="${nhacc}">
                                                <option value="${i.MANHACC}">${i.TENNHACC}</option>
                                            </c:forEach>
                                            </select></td>
                                        <td>
                                           <input type="button" name="inbbkkncc" id="inbbkkncc" value="Biên Bản kiểm nhập theo NCC" class="button_shadow" style="width:210px"/>
                                           <input type="button" name="xuatexcel" id="xuatexcel" value="In bản kiểm kê excel" class="button_shadow" style="width:150px"/>
                                           <input type="button" name="inchitiet" id="inchitiet" value="In hóa đơn chi tiết" class="button_shadow" style="width:150px"/></td>
                                           
                                    </tr>
                                    <tr>
                                        <td style="position:relative;left:10px;">Nguồn</td>
                                        <td width="155" ><select name="ma_nguonduoc" id="ma_nguonduoc" style="width:150px;" width = "150">
                                            <option value="-1">Tất cả...............</option>
                                            <c:forEach var="i" items="${danhsachnguonduoc}">
                                            <option value="${i.MA_NGUONDUOC}">${i.TEN_NGUONDUOC}</option>
                                            </c:forEach>
                                            </select>
                                        </td>
                                        <td align="right">
                                            <input type="checkbox" name="theongaynhap" id="theongaynhap">
                                        </td>
                                        <td >
                                            <label>Theo ngày nhập</label>
                                        </td>
                                        
                                        <td >
                                            <select id="typeexport" style="position:relative;left:1px;">                                                
                                                <option value="1">EXCEL</option>
                                                <option value="0">PDF</option>
                                            </select>
                                            <input type="button" name="insokiemnhapvithuoc" id="insokiemnhapvithuoc" value="In sổ kiểm nhập (Vị thuốc)" class="button_shadow" style="width:200px"/>
                                            <input type="button" name="inhd_ncc" id="inhd_ncc" value="In sổ kiểm nhập(NCC)" class="button_shadow" style="width:170px"/>
                                            <input type="button" name="inbbkkncc_chitiet" id="inbbkkncc_chitiet" value="BB kiểm nhập theo NCC chi tiết" class="button_shadow" style="width:240px"/>
                                            <input type="button" name="inbienbankiemnhapth" id="inbienbankiemnhapth" value="In biên bản kiểm nhập TH" class="button_shadow" style="width:200px"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="position:relative;left:10px;">
                                            <c:if test="${theo_donvi == 1}">
                                            <label >Xuất theo</label>
                                        </td>
                                        <td>
                                            <select id="donvixuat" style="width:150px;" type="hidden">                                                
                                                <option value="1">Riêng đơn vị</option>
                                                <option value="2">Đơn vị + TYT xã</option>
                                                <option value="3">Riêng TYT xã</option>
                                            </select>


                                            </c:if>
                                        </td>
                                    </tr>
                                    <!--VNPT VLG end  -->

                                </table>
                            </form>
                        </div>
                    </div>
                    <div style="padding-bottom: 5px">
                        <table id="list_danhsach" style="font-size: 12px; font-family:Verdana"></table>
                    </div>
                    <!--Êm ngày 2016-09-12 -->
                    <div id="dialogphieu" style="display: none">
                        <table id="list_chitietphieunhap"></table>
                        <table id="list_thongke_phieu_theo_dc_vt"></table>
                    </div>
                    <!-- End ngày 2016-09-12 -->
                </div> <!--end of center_content-->
				<%@include file="../../../resources/Theme/include_pages/footer.jsp"%>
            </div>
        </div>
    </body>
</html>