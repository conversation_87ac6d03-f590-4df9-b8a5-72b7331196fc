function loadDSGheptheBHYT() {
    var url = 'laydanhsachthexml_ddt_sudung?sovaovien=' +
        thongtinhsba.thongtinbn.SOVAOVIEN
        + '&sovaovien_dt=' + thongtinhsba.thongtinbn.SOVAOVIEN_DT;
    $("#vp_list_danhsachthe_sudung").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
}
$(function() {
    $("#vp-ghepthebhyt").click(function() {
        initGridTheBHYT()
        initGridTodieutriGhepthe();
        initGridTodieutriDaGhepthe();
        loadDSGheptheBHYT();
        loadDSTodieutriDaGhepthe();
        loadDSTodieutriChuaGhepthe();
    })
    $("#vp_ghepthe_noidangkykcb").keypress(function(e) {
        if(e.keyCode == 13) {
            layTennoidangkykcb($(this).val(), function(data) {
                $("#vp_ghepthe_tennoidangkykcb").val(data);
            })
        }
    })
    $("#vp-btn-themthebhyt").click(function() {
        $("#modalFormthemghepthebhyt").modal("show");
        $("#formThemmoiGheptheBHYT .clear-text").val('')
        addTextTitleModal("titleModalFormthemghepthebhyt", "Thêm mới thẻ BHYT")
        hideSelfLoading("vp_luu_ghepthebhyt");
    })
    $("#vp_ghepthe_kiemtrathebhyt").click(function() {
        var buttonId = this.id;
        showSelfLoading(buttonId)
        var jsonForm = convertDataFormToJson("formThemmoiGheptheBHYT");
        kiemtratheBHYT(
            {
                makcbbd: jsonForm.vp_ghepthe_noidangkykcb,
                dvtt: singletonObject.dvtt,
                magioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                ngaysinh_nhap: thongtinhsba.thongtinbn.NGAY_SINH,
                hoten_nhap: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                sothebhyt: jsonForm.vp_ghepthe_sothebhyt.trim(),
                ngaybatdau: jsonForm.vp_ghepthe_ngaybatdau,
                ngayketthuc: jsonForm.vp_ghepthe_ngayhethan,
                manoidoituong: thongtinhsba.thongtinbn.MA_KHUVUC
            }, function() {
                hideSelfLoading(buttonId);
            }
        )
    })
    $("#vp_luu_ghepthebhytv2").click(function() {
        var idButton = this.id
        if($("#formThemmoiGheptheBHYT").valid()) {
            var jsonForm = convertDataFormToJson("formThemmoiGheptheBHYT");
            showSelfLoading(idButton);
            $.post("them_danhsachthebaohiem_theoddt_sudung", {
                sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                sovaovien_dt: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                sothebhyt: jsonForm.vp_ghepthe_sothebhyt.trim(),
                hantungay: jsonForm.vp_ghepthe_ngaybatdau,
                handenngay: jsonForm.vp_ghepthe_ngayhethan,
                manoikcbbd: jsonForm.vp_ghepthe_noidangkykcb.trim(),
                themtructiep_hkquakiemtra: 1
            }).done(function (datacheck) {
                notifiToClient("Green", "Thêm thẻ thành công.")
                $("#modalFormthemghepthebhyt").modal("hide");
                loadDSGheptheBHYT()
            }).fail(function () {
                notifiToClient("Red", "Đã có lỗi trong quá trình kiểm tra! Xin vui lòng thử lại sau.")
            }).always(function() {
                hideSelfLoading(idButton);
            });
        }
    })
    $("#vp-btn-ghepthebhyt").click(function() {
        var idButton = this.id

        var url = "thuchienketnoithongtin_ddt_sudung";
        var rowSelected = getThongtinRowSelected("vp_list_danhsachthe_sudung");
        var listIdDieutri = $("#vp_list_tdt_chuaghepthe").jqGrid('getGridParam', 'selarrrow');

        if (rowSelected.ID && listIdDieutri.length > 0) {
            showSelfLoading(idButton)
            var count = listIdDieutri.length;
            var totalRequest = 0;
            for (var i = 0; i < count; i++) {
                var ret = $("#vp_list_tdt_chuaghepthe").jqGrid('getRowData', listIdDieutri[i]);
                $.post(url, {
                    SOTHEBH: rowSelected.SOTHEBH,
                    SOVAOVIEN: rowSelected.SOVAOVIEN,
                    SOVAOVIEN_DT: rowSelected.SOVAOVIEN_DT,
                    MABENHNHAN: rowSelected.MABENHNHAN,
                    STT_DIEUTRI: ret.STT_DIEUTRI,
                    ID_DIEUTRI: ret.ID_DIEUTRI,
                    ID: rowSelected.ID
                }).done(function () {
                    if (totalRequest == count - 1) {
                        loadDSTodieutriDaGhepthe()
                        notifiToClient("Green","Thực hiện kết nối thông tin thành công thành công.");
                        hideSelfLoading(idButton)
                    }
                }).fail(function () {
                    if (count-1 == totalRequest) {
                        notifiToClient("Red","Thực hiện kết nối thông tin thành công không thành công.");
                        hideSelfLoading(idButton)
                    }
                }).always(function() {
                    totalRequest++;
                })
            }
        } else {
            notifiToClient("Red","Vui lòng chọn thẻ và phiếu điều trị cần ghép.");
        }
    })
    $("#formThemmoiGheptheBHYT").validate({
        rules: {
            vp_ghepthe_sothebhyt: {
                maxlength: 15,
                minlength: 15
            },
            vp_ghepthe_ngaybatdau: {
                validDate: true
            },
            vp_ghepthe_ngayhethan: {
                validDate: true
            },
        }
    })

    function initGridTheBHYT() {
        var list = $("#vp_list_danhsachthe_sudung");
        if(!list[0].grid) {
            list.jqGrid({
                url: "",
                datatype: "local",
                loadonce: false,
                height: 250,
                width: null,
                shrinkToFit: false,
                ignoreCase: true,
                rowNum: 6000,
                colModel: [
                    {label: "STT",name: 'STT', index: 'STT', width: 60, align: "center", formatter: 'integer'},
                    {label: "Số thẻ BH",name: 'SOTHEBH', index: 'SOTHEBH', width: 200, align: "right"},
                    {label: "Thời hạn bắt đầu",name: 'THOIHANBATDAU', index: 'THOIHANBATDAU', width: 200, align: "right"},
                    {label: "Thời hạn kết thúc",name: 'THOIHANKETTHUC', index: 'THOIHANKETTHUC', width: 200, align: "right"},
                    {label: "Mức hưởng",name: 'MUC_HUONG_THE', index: 'MUC_HUONG_THE', width: 100, align: "right"},
                    {label: "Nới đăng ký",name: 'MANOIKCBBD', index: 'MANOIKCBBD', width: 100, align: "right"},
                    {label: "SOVAOVIEN",name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 20, align: "right", hidden: true},
                    {label: "SOVAOVIEN_DT",name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', width: 20, align: "right", hidden: true},
                    {label: "ID",name: 'ID', index: 'ID', width: 20, align: "right", hidden: true},
                    {label: "MABENHNHAN",name: 'MABENHNHAN', index: 'MABENHNHAN', width: 20, align: "right", hidden: true}
                ],
                footerrow: true,
                onSelectRow: function (id) {},
                onRightClickRow: function (id) {
                    if(id) {
                        $.contextMenu({
                            selector: '#vp_list_danhsachthe_sudung tr',
                            reposition : false,
                            callback: function (key, options) {
                                var idWrap = "vp_list_danhsachthe_sudung_wrap"
                                var rowData = getThongtinRowSelected("vp_list_danhsachthe_sudung");

                                if (key == "kiemtradulieu") {
                                    showLoaderIntoWrapId(idWrap)
                                    kiemtratheBHYT(
                                        {
                                            makcbbd: rowData.MANOIKCBBD,
                                            dvtt: singletonObject.dvtt,
                                            magioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                                            ngaysinh_nhap: thongtinhsba.thongtinbn.NGAY_SINH,
                                            hoten_nhap: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                            sothebhyt: rowData.SOTHEBH.trim(),
                                            ngaybatdau: rowData.THOIHANBATDAU,
                                            ngayketthuc: rowData.THOIHANKETTHUC,
                                            manoidoituong: thongtinhsba.thongtinbn.MA_KHUVUC
                                        }, function() {
                                            hideLoaderIntoWrapId(idWrap)
                                        }
                                    )
                                }
                                if (key == "xoa") {
                                    confirmToClient("Bạn có chắc chắn muốn xóa thẻ BHYT này không?", function() {
                                        var url = "xoa_thekhoidanhsach_ddt_sudung";
                                        showLoaderIntoWrapId(idWrap)
                                        $.post(url, {
                                            SOTHEBH: rowData.SOTHEBH,
                                            SOVAOVIEN: rowData.SOVAOVIEN,
                                            SOVAOVIEN_DT: rowData.SOVAOVIEN_DT,
                                            MABENHNHAN: rowData.MABENHNHAN,
                                            HANKETTHUC: rowData.THOIHANKETTHUC,
                                            HANBATDAU: rowData.THOIHANBATDAU,
                                            NOIDKBD: rowData.MANOIKCBBD
                                        }).done(function () {
                                            loadDSGheptheBHYT();
                                            notifiToClient("Green","Xoá thẻ thành công.", );
                                        }).fail(function () {
                                            notifiToClient("Red","Xoá thẻ BH khỏi danh sách thẻ sử dụng không thành công.");
                                        }).always(function() {
                                            hideLoaderIntoWrapId(idWrap)
                                        });
                                    }, function () {

                                    })
                                }
                            },
                            items: {
                                "kiemtradulieu": {name: '<p><i class="fa fa-braille text-primary" aria-hidden="true"></i> Kiểm tra thông tin</p>'},
                                // "capnhat": {name: '<p><i class="fa fa-list text-primary" aria-hidden="true"></i> Chi tiết phiếu điều trị</p>'},
                                "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'},
                            }
                        });
                    }
                },
                loadComplete: function () {
                    var $self = $(this);
                    var sl = list.getGridParam("records");
                    sl = "Tổng: " + sl + " Thẻ BH";
                    $self.jqGrid("footerData", "set", {SOTHEBH: sl});
                }
            });
        }
    }

    function kiemtratheBHYT(object, callback) {
        $.get(
            singletonObject.urlCheckBHXH+
            $.param(object)
        ).done(function(data) {
            var div = data.maKetQua == '000'? "<div style='color:green'>": "<div style='color:red'>";
            notifiToClient(data.maKetQua == '000'? "Green": "Red", div+data.ghiChu+"</div>")
        }).fail(function() {
            notifiToClient("Red", "Đã có lỗi trong quá trình kiểm tra! Xin vui lòng thử lại sau.")
        }).always(function() {
            callback()
        })
    }

    function initGridTodieutriGhepthe() {
        var list= $("#vp_list_tdt_chuaghepthe");
        if(!list[0].grid) {
            list.jqGrid({
                url: "",
                datatype: "local",
                loadonce: false,
                height: 250,
                width: null,
                shrinkToFit: false,
                ignoreCase: true,
                rowNum: 6000,
                colModel: [
                    {label: "STT", name: 'STT', index: 'STT', width: 30, align: "center", formatter: 'integer'},
                    {label: "Số tờ điều trị",name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 80, align: "right"},
                    {label: "Thời gian tạo",name: 'THOIGIANTAO', index: 'THOIGIANTAO', width: 150, align: "right"},
                    {label: "ID_DIEUTRI",name: 'ID_DIEUTRI', index: 'ID_DIEUTRI', width: 100, align: "right", hidden: true},
                    {label: "SOVAOVIEN",name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 20, align: "right", hidden: true},
                    {label: "SOVAOVIEN_DT",name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', width: 20, align: "right", hidden: true},
                    {label: "MABENHNHAN",name: 'MABENHNHAN', index: 'MABENHNHAN', width: 20, align: "right", hidden: true}
                ],
                footerrow: true,
                multiselect: true,
                onSelectRow: function (id) {
                },
                loadComplete: function () {
                    var $self = $(this);
                    var sl = list.getGridParam("records");
                    sl = "Tổng: " + sl + " phiếu";
                    $self.jqGrid("footerData", "set", {THOIGIANTAO: sl});
                }
            });
        }
    }
    function initGridTodieutriDaGhepthe() {
        var list= $("#vp_list_tdt_daghepthe");
        if(!list[0].grid) {
            list.jqGrid({
                url: "",
                datatype: "local",
                loadonce: false,
                height: 350,
                width: null,
                shrinkToFit: false,
                ignoreCase: true,
                rowNum: 6000,
                colModel: [
                    {label: "STT",name: 'STT', index: 'STT', width: 30, align: "center", formatter: 'integer'},
                    {label: "Số tờ điều trị",name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 80, align: "right"},
                    {label: "Thời gian tạo",name: 'THOIGIANTAO', index: 'THOIGIANTAO', width: 150, align: "right"},
                    {label: "ID_DIEUTRI",name: 'ID_DIEUTRI', index: 'ID_DIEUTRI', width: 100, align: "right", hidden: true},
                    {label: "SOVAOVIEN",name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 20, align: "right", hidden: true},
                    {label: "SOVAOVIEN_DT",name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', width: 20, align: "right", hidden: true},
                    {label: "MABENHNHAN",name: 'MABENHNHAN', index: 'MABENHNHAN', width: 20, align: "right", hidden: true},
                    {label: "Số thẻ BH",name: 'SOTHEBH', index: 'SOTHEBH', width: 200, align: "right"},
                    {label: "Thời hạn bắt đầu",name: 'THOIHANBATDAU', index: 'THOIHANBATDAU', width: 150, align: "right"},
                    {label: "Thời hạn kết thúc",name: 'THOIHANKETTHUC', index: 'THOIHANKETTHUC', width: 150, align: "right"},
                    {label: "Nới đăng ký",name: 'MANOIKCBBD', index: 'MANOIKCBBD', width: 100, align: "right"}
                ],
                footerrow: true,
                onSelectRow: function (id) {
                    if (id) {
                    }
                },
                loadComplete: function () {
                    var $self = $(this);
                    var sl = list.getGridParam("records");
                    sl = "Tổng: " + sl + " phiếu";
                    $self.jqGrid("footerData", "set", {THOIGIANTAO: sl});
                }
            });
        }
    }

    function loadDSTodieutriDaGhepthe() {
        var url = 'laydanhsachtheketnoithongtin_ddt_sudung?sovaovien='
            + thongtinhsba.thongtinbn.SOVAOVIEN + '&sovaovien_dt=' + thongtinhsba.thongtinbn.SOVAOVIEN_DT;
        $("#vp_list_tdt_daghepthe").jqGrid('setGridParam', {
            datatype: 'json',
            url: url
        }).trigger('reloadGrid');
    }

    function loadDSTodieutriChuaGhepthe() {
        var url = 'laydanhsachphieudtxml_ddt_sudung?sovaovien='
            + thongtinhsba.thongtinbn.SOVAOVIEN + '&sovaovien_dt=' + thongtinhsba.thongtinbn.SOVAOVIEN_DT;
        $("#vp_list_tdt_chuaghepthe").jqGrid('setGridParam', {
            datatype: 'json',
            url: url
        }).trigger('reloadGrid');
    };

})