package VoBenhAnNoiTru;

import VSC.jdbc.JdbcTemplate;
import VSC.jreport.report.util.CommonFunctions;
import VSC.jreport.report.util.JasperHelper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.*;

import khambenhnoitru.KhambenhnoitruDAO;
import l2.L2Utils;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.export.HtmlExporter;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.export.JRRtfExporter;
import net.sf.jasperreports.engine.util.JRLoader;
import net.sf.jasperreports.export.*;
import org.json.JSONObject;
import org.json.simple.JSONArray;
import org.json.simple.parser.JSONParser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.CollectionUtils;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import thamsohethong.Thamsohethong;
import tienich.tienich;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.sql.DataSource;
import java.io.File;
import java.io.FileInputStream;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Controller
public class VoBenAnNoiTruController {
    private static final jspVBAPathClass jspName = new jspVBAPathClass();

    @Autowired
    VoBenhAnNoiTruDAO voBenhAnNoiTruDAO;

    @Autowired
    @Resource(name = "dataSourceMNG")
    DataSource dataSourceMNG;

    @Autowired
    dangnhap.SessionFilter SessionFilter;

    @Autowired
    KhambenhnoitruDAO KhambenhnoitruDAO;

    @RequestMapping(value = "/vo-benh-an-noi-tru", method = RequestMethod.GET)
    public ModelAndView voBenhAnTest(ModelMap mm, HttpSession session) {
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }
        Enumeration attributeNames = session.getAttributeNames();
        while (attributeNames.hasMoreElements()) {
            String att = (String) attributeNames.nextElement();
            if (session.getAttribute(att) instanceof String)
                mm.put(att, session.getAttribute(att));
        }
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        String userId = session.getAttribute("Sess_UserID") != null ? session.getAttribute("Sess_UserID").toString() : "";
        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSourceMNG);
        MapSqlParameterSource params = new MapSqlParameterSource().addValue("maPhongBan", L2Utils.getMaPhongBan(session)).addValue("dvtt", L2Utils.getDvtt(session));
        mm.put("dsPhongBan", namedParameterJdbcTemplate.queryForList("SELECT MA_PHONGBAN, TEN_PHONGBAN FROM HIS_FW.DM_PHONGBAN WHERE MA_DONVI = :dvtt AND CHUCNANGKHAMCHUABENH BETWEEN 1 AND 4", params));
        mm.put("dsNhanVien", namedParameterJdbcTemplate.queryForList("SELECT NV.MA_NHANVIEN, CD.MOTA_CHUCDANH ||'. '|| NV.TEN_NHANVIEN AS TEN_NHANVIEN, CD.TEN_CHUCDANH " +
                " FROM HIS_FW.DM_NHANVIEN NV " +
                "   JOIN HIS_FW.DM_CHUCDANH_NHANVIEN CD ON CD.MA_CHUCDANH = NV.CHUCDANH_NHANVIEN " +
                " WHERE NV.MA_PHONGBAN = :maPhongBan", params));
        mm.put("dsmautrang2", namedParameterJdbcTemplate.queryForList("SELECT MA_PHONGBAN, TEN_PHONGBAN FROM HIS_FW.DM_PHONGBAN WHERE MA_DONVI = :dvtt AND CHUCNANGKHAMCHUABENH BETWEEN 1 AND 4", params));
        Thamsohethong tsht = (Thamsohethong) session.getAttribute("Sess_Thamso");
        mm.put("smartCA_950351", tsht.smartCA_950351);
        mm.put("smartCA_950360_xml", tsht.smartCA_950360_xml);
        mm.put("Sess_User", session.getAttribute("Sess_User").toString());
        mm.put("thamSoVBA_6", voBenhAnNoiTruDAO.getThamSoVoBenhAnString(dvtt, "6"));
        mm.put("thamsoVBA_10", voBenhAnNoiTruDAO.getThamSoVoBenhAnString(dvtt, "10"));
        mm.put("thamsoVBA_11", voBenhAnNoiTruDAO.getThamSoVoBenhAnString(dvtt, "11"));
        mm.addAttribute("modelMap", new Gson().toJson(mm));
        return new ModelAndView("VoBenhAnNoiTru/view/main/vobenhan_main");
    }

    @RequestMapping(value = "/cauhinh-vobenhan", method = RequestMethod.GET)
    public ModelAndView voBenhAnSetting(ModelMap mm, HttpSession session) {
        if (!SessionFilter.checkSession(session)) {
            return SessionFilter.redirectLogin2();
        }
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        List listVBA = voBenhAnNoiTruDAO.dmVoBenhAn(dvtt);
        mm.put("list_vobenhan", listVBA);
        return new ModelAndView("VoBenhAnNoiTru/cau_hinh_vba/setting");
    }

    //    load-danh-sach-report-mau-by-loai-benhan
    public @ResponseBody
    @RequestMapping(value = "/load-danh-sach-report-mau-by-loai-benhan", method = RequestMethod.GET)
    List dsReportMau(HttpSession session, HttpServletRequest request) {
        List ret = voBenhAnNoiTruDAO.dsReportMauByLoaiBenhAn(
                L2Utils.getDvtt(session),
                request.getParameter("loaiBenhAn")
        );
        return ret;
    }

    public @ResponseBody
    @RequestMapping(value = "/add-report-mau-vba", method = RequestMethod.POST)
    int addReport(HttpSession session, HttpServletRequest request) {
        return voBenhAnNoiTruDAO.themReportMau(
                L2Utils.getDvtt(session),
                request.getParameter("loaiBenhAn"),
                Integer.parseInt(request.getParameter("trang")),
                Integer.parseInt(request.getParameter("sapXep")),
                request.getParameter("path"),
                request.getParameter("ghiChu")
        );
    }

    public @ResponseBody
    @RequestMapping(value = "/update-report-mau-vba", method = RequestMethod.POST)
    int updateReport(HttpSession session, HttpServletRequest request) {
        return voBenhAnNoiTruDAO.updateReportMau(
                Integer.parseInt(request.getParameter("id")),
                Integer.parseInt(request.getParameter("trang")),
                Integer.parseInt(request.getParameter("sapXep")),
                request.getParameter("path"),
                request.getParameter("ghiChu")
        );
    }

    public @ResponseBody
    @RequestMapping(value = "/delete-report-mau-vba", method = RequestMethod.POST)
    int deleteReport(HttpSession session, HttpServletRequest request) {
        return voBenhAnNoiTruDAO.deleteReportMau(
                Integer.parseInt(request.getParameter("iD"))
        );
    }

    public @ResponseBody
    @RequestMapping(value = "/thong-tin-benh-nhan-rut-gon", method = RequestMethod.GET)
    Map findBenhNhanBySoBenhAn(HttpSession session, HttpServletRequest request) {
        Map map = new HashMap();
        try {
            String dvtt = L2Utils.getDvtt(session);
            String maBenhNhan = request.getParameter("maBenhNhan");
            String soVaoVien = request.getParameter("soVaoVien");
            String sttBenhAn = request.getParameter("sttBenhAn");
            map = voBenhAnNoiTruDAO.loadThongTinBenhNhan(dvtt, maBenhNhan, soVaoVien, sttBenhAn);
            map.put("STATUS", "1");
            map.put("MESSAGE", "");
        } catch (Exception e) {
            map.put("STATUS", "0");
            map.put("MESSAGE", e.getMessage());
        }
        return map;
    }


    @RequestMapping(value = "/call-jsp-path-page-vba", method = RequestMethod.POST, produces = "application/json; charset=utf-8")
    public ModelAndView jspPageV2(ModelMap mm, HttpSession session, @RequestBody String data
    ) throws ParseException {
        try {
            if (!SessionFilter.checkSession(session)) {
                return SessionFilter.redirectLogin2();
            }
            String jspPathFromDialog;
            JSONObject dataDialogApiJson = new JSONObject(data);
            jspPathFromDialog = jspName.getVbaJspPath("VIEW_VO_BENH_AN");
            String loaiBa = dataDialogApiJson.optString("loaiBa");
            mm.put("tieuDe", jspName.getVbaJspPath("TIEUDE"));
            mm.put("hanhChinh", jspName.getVbaJspPath("HANHCHINH"));
            mm.put("quanLyNguoiBenh", jspName.getVbaJspPath("QL_NGUOIBENH"));
            mm.put("chanDoan", jspName.getVbaJspPath("CHANDOAN"));
            mm.put("tinhTrangRaVien", jspName.getVbaJspPath("TINHTRANG_RAVIEN"));
            mm.put("trang3", jspName.getVbaJspPath("TRANG_3"));

            if (loaiBa.equals("BONG")) {
                mm.put("quanLyNguoiBenh", jspName.getVbaJspPath("QL_NGUOIBENH_BONG"));
                mm.put("chanDoan", jspName.getVbaJspPath("CHANDOAN_BONG"));
                mm.put("tinhTrangRaVien", jspName.getVbaJspPath("TINHTRANG_RAVIEN_BONG"));
            } else if (loaiBa.equals("TAYCHANMIENG")){
                mm.put("hanhChinh", jspName.getVbaJspPath("HANHCHINH_TAYCHANMIENG"));
                mm.put("quanLyNguoiBenh", jspName.getVbaJspPath("QL_NGUOIBENH_TCM"));
                mm.put("chanDoan", jspName.getVbaJspPath("CHANDOAN_TAYCHANMIENG"));
                mm.put("tinhTrangRaVien", jspName.getVbaJspPath("TINHTRANG_RAVIEN_TCM"));
            } else if (loaiBa.equals("NHI")) {
                mm.put("hanhChinh", jspName.getVbaJspPath("HANHCHINH_NHIKHOA"));
                mm.put("quanLyNguoiBenh", jspName.getVbaJspPath("QL_NGUOIBENH_NHIKHOA"));
                mm.put("chanDoan", jspName.getVbaJspPath("CHANDOAN_NHIKHOA"));
                mm.put("tinhTrangRaVien", jspName.getVbaJspPath("TINHTRANG_RAVIEN_NHIKHOA"));
            } else if (loaiBa.equals("TAMTHAN")) {
                mm.put("hanhChinh", jspName.getVbaJspPath("HANHCHINH_TAMTHAN"));
                mm.put("quanLyNguoiBenh", jspName.getVbaJspPath("QL_NGUOIBENH_TAMTHAN"));
                mm.put("chanDoan", jspName.getVbaJspPath("CHANDOAN_TAMTHAN"));
                mm.put("tinhTrangRaVien", jspName.getVbaJspPath("TINHTRANG_RAVIEN_TAMTHAN"));
            } else if (loaiBa.equals("NOITRUYHCT")) {
                mm.put("hanhChinh", jspName.getVbaJspPath("HANHCHINH_YHCTNOI"));
                mm.put("chanDoan", jspName.getVbaJspPath("CHANDOAN_YHCTNOI"));
                mm.put("tinhTrangRaVien", jspName.getVbaJspPath("TINHTRANG_RAVIEN_YHCTNOI"));
            } else if (loaiBa.equals("NHIYHCT1941")) {
                mm.put("hanhChinh", jspName.getVbaJspPath("HANHCHINH_YHCTNOI"));
                mm.put("chanDoan", jspName.getVbaJspPath("CHANDOAN_YHCTNHI1941"));
                mm.put("tinhTrangRaVien", jspName.getVbaJspPath("TINHTRANG_RAVIEN_YHCTNHI1941"));
            } else if (loaiBa.equals("NGOAITRUYHCT4069")) {
                mm.put("hanhChinh", jspName.getVbaJspPath("HANHCHINH_YHCTNGOAI4069"));
                mm.put("chanDoan", jspName.getVbaJspPath("CHANDOAN_YHCTNGOAI4069"));
                mm.put("tinhTrangRaVien", jspName.getVbaJspPath("TINHTRANG_RAVIEN_YHCTNGOAI4069"));
            } else if (loaiBa.equals("MATTREEM")) {
                mm.put("chanDoan", jspName.getVbaJspPath("CHANDOAN_MATTREEM"));
                mm.put("tinhTrangRaVien", jspName.getVbaJspPath("TINHTRANG_RAVIEN_MATTREEM"));
            } else if (loaiBa.equals("PHUKHOA")) {
                mm.put("hanhChinh", jspName.getVbaJspPath("HANHCHINH_PHUKHOA"));
                mm.put("quanLyNguoiBenh", jspName.getVbaJspPath("QL_NGUOIBENH_NHIKHOA"));
                mm.put("chanDoan", jspName.getVbaJspPath("CHANDOAN_PHUKHOA"));
                mm.put("tinhTrangRaVien", jspName.getVbaJspPath("TINHTRANG_RAVIEN_PHUKHOA"));
            } else if (loaiBa.equals("PHCN")) {
                mm.put("tieuDe", jspName.getVbaJspPath("TIEUDE_PHCN"));
                mm.put("hanhChinh", jspName.getVbaJspPath("HANHCHINH_PHCN"));
            } else if (loaiBa.equals("SANKHOA")) {
                mm.put("hanhChinh", jspName.getVbaJspPath("HANHCHINH_SANKHOA"));
                mm.put("quanLyNguoiBenh", jspName.getVbaJspPath("QL_NGUOIBENH_NHIKHOA"));
                mm.put("chanDoan", jspName.getVbaJspPath("CHANDOAN_SANKHOA"));
                mm.put("tinhTrangRaVien", jspName.getVbaJspPath("TINHTRANG_RAVIEN_SANKHOA"));
            } else if (loaiBa.equals("SOSINH")) {
                mm.put("hanhChinh", jspName.getVbaJspPath("HANHCHINH_SOSINH"));
            } else if (loaiBa.equals("NGOAITRUTMH") || loaiBa.equals("NGOAITRURHM")) {
                mm.put("hanhChinh", jspName.getVbaJspPath("CKTMHRHM"));
            }else if (loaiBa.equals("NGOAITRUYHCT")) {
                mm.put("hanhChinh", jspName.getVbaJspPath("HANHCHINH_YHCTNGOAI"));
                mm.put("chanDoan", jspName.getVbaJspPath("CHANDOAN_YHCTNGOAI"));
                mm.put("tinhTrangRaVien", jspName.getVbaJspPath("TINHTRANG_RAVIEN_YHCTNGOAI"));
                mm.put("quanLyNguoiBenh", jspName.getVbaJspPath("QL_NGUOIBENH_YHCTNGOAI"));
            }else if (loaiBa.equals("MATGLOCOM")){
                mm.put("chanDoan", jspName.getVbaJspPath("CHANDOAN_BONG"));
                mm.put("tinhTrangRaVien", jspName.getVbaJspPath("TINHTRANG_RAVIEN_BONG"));
            }else if (loaiBa.equals("XAPHUONG")){
                mm.put("tieuDe", jspName.getVbaJspPath("TIEUDE_XAPHUONG"));
                mm.put("hanhChinh", jspName.getVbaJspPath("HANHCHINH_XAPHUONG"));
            }
            else if (loaiBa.equals("DAYMAT")||loaiBa.equals("MATLAC")||loaiBa.equals("MATCHANTHUONG")||loaiBa.equals("BANPHANTRUOC")) {
                mm.put("chanDoan", jspName.getVbaJspPath("CHANDOAN_DAYMAT"));
                mm.put("tinhTrangRaVien", jspName.getVbaJspPath("TINHTRANG_RAVIEN_DAYMAT"));
            } else if (loaiBa.equals("NGOAITRUYHCT1941")) {
                mm.put("hanhChinh", jspName.getVbaJspPath("HANHCHINH_YHCTNOI"));
                mm.put("chanDoan", jspName.getVbaJspPath("CHANDOAN_NGOAITRUYHCT1941"));
                mm.put("tinhTrangRaVien", jspName.getVbaJspPath("TINHTRANG_RAVIEN_NGOAITRUYHCT1941"));
                mm.put("quanLyNguoiBenh", jspName.getVbaJspPath("QL_NGUOIBENH_NGOAITRUYHCT1941"));
            }else if(loaiBa.equals("PHCN_BANT")){
                mm.put("hanhChinh", jspName.getVbaJspPath("HANHCHINH_PHCN_BANT"));
            }else if (loaiBa.equals("MATGLOCOM")||loaiBa.equals("MATDUCTHUYTINHTHE")) {
                mm.put("chanDoan", jspName.getVbaJspPath("CHANDOAN_BAMAT"));
                mm.put("tinhTrangRaVien", jspName.getVbaJspPath("TINHTRANG_RAVIEN_BAMAT"));
            }else if(loaiBa.equals("PHCN_NHI")){
                mm.put("tieuDe", jspName.getVbaJspPath("TIEUDE_PHCN_NHI"));
                mm.put("hanhChinh", jspName.getVbaJspPath("HANHCHINH_NHI"));
                mm.put("quanLyNguoiBenh", jspName.getVbaJspPath("QL_NGUOIBENH_TAMTHAN"));
                mm.put("chanDoan", jspName.getVbaJspPath("CHANDOAN_TAMTHAN"));
                mm.put("tinhTrangRaVien", jspName.getVbaJspPath("TINHTRANG_RAVIEN_TAMTHAN"));
            }else if (loaiBa.equals("RANGHAMMAT") || loaiBa.equals("TAIMUIHONG")){
                mm.put("quanLyNguoiBenh", jspName.getVbaJspPath("QL_NGUOIBENH_RHM"));
                mm.put("chanDoan", jspName.getVbaJspPath("CHANDOAN_BONG"));
                mm.put("tinhTrangRaVien", jspName.getVbaJspPath("TINHTRANG_RAVIEN_MATTREEM"));
            }else if (loaiBa.equals("NGOAIKHOA") ){
                mm.put("quanLyNguoiBenh", jspName.getVbaJspPath("QL_NGUOIBENH_RHM"));
                mm.put("chanDoan", jspName.getVbaJspPath("CHANDOAN_BONG"));
                mm.put("tinhTrangRaVien", jspName.getVbaJspPath("TINHTRANG_RAVIEN_MATTREEM"));
            }else if (loaiBa.equals("NGOAITRU_CHUNG")){
                mm.put("tieuDe", jspName.getVbaJspPath("TIEUDE_NGOAITRU_CHUNG"));
                mm.put("hanhChinh", jspName.getVbaJspPath("CKTMHRHM"));
            }else if (loaiBa.equals("METHADONE")){
                mm.put("tieuDe", jspName.getVbaJspPath("TITLE_METHADONE"));
                mm.put("hanhChinh", jspName.getVbaJspPath("HANHCHINH_METHADONE"));
                mm.put("quanLyNguoiBenh", jspName.getVbaJspPath("CHATGAYNGHIEN_METHADONE"));
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_METHADONE"));
            }

            if (loaiBa.equals("NGOAITRUTMH")) {
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_NGOAITRUTMH"));
            } else if (loaiBa.equals("TAIMUIHONG") || loaiBa.equals("RANGHAMMAT") || loaiBa.equals("PHUKHOA") || loaiBa.equals("BONG") || loaiBa.equals("NGOAIKHOA") || loaiBa.equals("TAYCHANMIENG")) {
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_TAIMUIHONG"));
            } else if (loaiBa.equals("TAMTHAN")) {
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_TAMTHAN"));
            } else if (loaiBa.equals("UNGBUOU")) {
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_UNGBUOU"));
            } else if (loaiBa.equals("NOITRUYHCT")||loaiBa.equals("NGOAITRUYHCT1941")) {
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_YHCTNOI"));
            } else if (loaiBa.equals("NHIYHCT1941")) {
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_YHCTNHI1941"));
            } else if (loaiBa.equals("NGOAITRUYHCT4069")) {
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_YHCTNGOAI4069"));
            } else if (loaiBa.equals("PHCN")) {
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_PHCN"));
            } else if (loaiBa.equals("PHCN_NHI")) {
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_PHCN_NHI"));
            } else if (loaiBa.equals("SANKHOA")) {
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_SANKHOA"));
            } else if (loaiBa.equals("NGOAITRUYHCT")) {
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_YHCTNGOAI"));
            } else if (loaiBa.equals("DAYMAT")) {
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_DAYMAT"));
            } else if (loaiBa.equals("MATLAC")) {
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_MATLAC"));
            } else if (loaiBa.equals("BANPHANTRUOC") || loaiBa.equals("MATCHANTHUONG")) {
//                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_BANPHANTRUOC"));
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_MATLAC"));
            } else if (loaiBa.equals("MATGLOCOM")){
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_MAT_GLOCOM"));
            }else if(loaiBa.equals("NGOAITRURHM") || loaiBa.equals("PHCN_BANT")|| loaiBa.equals("NGOAITRU_CHUNG")){
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_NGOAITRURHM"));
            } else if(loaiBa.equals("MATTREEM")){
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_MATLAC"));
            } else if (loaiBa.equals("NGOAITRUYHCT1941")) {
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_YHCTNGOAI1941"));
            }
            else if(loaiBa.equals("MATDUCTHUYTINHTHE")) {
                mm.put("trang3", jspName.getVbaJspPath("TRANG_3_MATDUCTHUYTINHTHE"));
            }
            mm.put("dsQuocGia", voBenhAnNoiTruDAO.dmQuocGia());
            mm.put("noiDungBenhAn", jspName.getVbaJspPath("" + dataDialogApiJson.optString("loaiBa")));
            return new ModelAndView(jspPathFromDialog);
        } catch (Exception e) {
            mm.put("error", "Lỗi: " + e.getMessage());
            return new ModelAndView("VoBenhAnNoiTru/view/main/startPage");
        }
    }

    @RequestMapping(value = "/reset-page-begin", method = RequestMethod.POST, produces = "application/json; charset=utf-8")
    public ModelAndView beginPage(ModelMap mm, HttpSession session
    ) throws ParseException {
        try {
            if (!SessionFilter.checkSession(session)) {
                return SessionFilter.redirectLogin2();
            }
            String jspPathFromDialog = jspName.getVbaJspPath("BEGIN_PAGE");
            return new ModelAndView(jspPathFromDialog);
        } catch (Exception e) {
            mm.put("RESULT", "" + e.getMessage());
            return null;
        }
    }

    @RequestMapping(value = "/danh-sach-vo-benh-an-da-khoi-tao", method = RequestMethod.GET)
    public @ResponseBody
    List loadDanhSachVoBenhAnDaTaoV2(HttpSession session, HttpServletRequest request) {
        try {
            return voBenhAnNoiTruDAO.dsBenhAnDaTao(L2Utils.getDvtt(session), request.getParameter("soVaoVien"), request.getParameter("soVaoVienDt"));
        } catch (Exception e) {
            return null;
        }
    }

    @RequestMapping(value = "/danh-sach-vo-benh-an-day-du", method = RequestMethod.GET)
    public @ResponseBody
    List danhSachVoBenhAnFull(HttpSession session, HttpServletRequest request) {
        try {
            List listVBA = voBenhAnNoiTruDAO.listVoBenhAn(
                    L2Utils.getDvtt(session),
                    request.getParameter("soVaoVien"),
                    request.getParameter("soVaoVienDt"));
            String levelHis = L2Utils.getTuyenbenhvien(session);
            if (Integer.parseInt(levelHis) < 4){
                List _lstVBA = new ArrayList();
                for (Map<String, Object> item: (List<Map<String, Object>>) listVBA){
                    if (!item.get("ID_VBA").toString().equals("XAPHUONG")){
                        _lstVBA.add(item);
                    }
                }
                return _lstVBA;
            }
            return listVBA;
        } catch (Exception e) {
            return null;
        }
    }

    public @ResponseBody
    @RequestMapping(value = "/load-thong-tin-vba", produces = "application/json; charset=utf-8", method = RequestMethod.POST)
    Map loadThongTinVbaFull(HttpSession session, HttpServletRequest request, HttpServletResponse response,
                            @RequestBody String data) throws ParseException {
        Map ret = new HashMap();
        String tenKhoaPhong, phongBenh, giuongBenh;
        JSONObject dataDialogApiJson = new JSONObject(data);
        Map info = new HashMap();
        try {
            Map vbaInfo = voBenhAnNoiTruDAO.thongTinVoBenhAn(L2Utils.getDvtt(session),
                    dataDialogApiJson.optString("loaiBa"),
                    dataDialogApiJson.optString("iD")
            );
            String sqlThongTinKhoaGiuong = "call THONGTIN_KHOA_GIUONG_VBA(?,?,?)#c,s,s,s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
            Map ttkg = jdbcTemplate.queryForMap(sqlThongTinKhoaGiuong,
                    new Object[]{L2Utils.getDvtt(session),
                            dataDialogApiJson.optString("soVaoVien"),
                            dataDialogApiJson.optString("soVaoVienDt")});
            ret.put("VBA_INF", vbaInfo);

            tenKhoaPhong = ttkg.get("TENKHOA").toString().trim().equals("") ? "....." : ttkg.get("TENKHOA").toString().trim();
            giuongBenh = ttkg.get("GIUONG_BENH").toString().trim().equals("") ? "....." : ttkg.get("GIUONG_BENH").toString().trim();
            phongBenh = ttkg.get("PHONGBENH").toString().trim().equals("") ? "....." : ttkg.get("PHONGBENH").toString().trim();

            Thamsohethong tsht = (Thamsohethong) request.getSession().getAttribute("Sess_Thamso");
            String capDonVi = L2Utils.getTuyenbenhvien(session);
            ret.put("TENKHOA", tenKhoaPhong);
            ret.put("GIUONG_BENH", giuongBenh);
            ret.put("PHONGBENH", phongBenh);
            ret.put("SOYTE", "SỞ Y TẾ " + tsht.tinh.toUpperCase());
            if (Integer.parseInt(capDonVi) > 3){
                ret.put("DONVI_QUANLY", tsht.benhvientuyentren.toUpperCase());
                ret.put("TENBENHVIEN", tsht.tenbenhvien.toUpperCase());
            }else {
                ret.put("DONVI_QUANLY", "");
                ret.put("TENBENHVIEN", tsht.tenbenhvien.toUpperCase());
            }
            String iD = dataDialogApiJson.optString("iD");
            List<Map<String, Object>> thongTin = voBenhAnNoiTruDAO.getThongTinBenhNhan(L2Utils.getDvtt(session),
                    dataDialogApiJson.optString("soVaoVien"),
                    dataDialogApiJson.optString("soVaoVienDt"),
                    Integer.parseInt(iD)
            );
            info = thongTin.get(0);
            List<Map<String, Object>> listChuyenKhoa = voBenhAnNoiTruDAO.dsChuyenKhoa(L2Utils.getDvtt(session),
                    dataDialogApiJson.optString("soVaoVien"), dataDialogApiJson.optString("soVaoVienDt"));
            for (int i = 0; i < listChuyenKhoa.size(); i++) {
                ret.put("maKhoa" + i, listChuyenKhoa.get(i).get("MAPHONGBAN").toString());
                ret.put("tenKhoa" + i, listChuyenKhoa.get(i).get("TEN_PHONGBAN").toString());
                ret.put("chuyenKhoaThoiGianText" + i, listChuyenKhoa.get(i).get("THOI_GIAN_CD_TEXT").toString());
                ret.put("chuyenKhoaThoiGian" + i, listChuyenKhoa.get(i).get("THOI_GIAN_CD").toString());
                ret.put("chuyenKhoaSoNgay" + i, listChuyenKhoa.get(i).get("SONGAY_DT").toString());
                if (i == 4){
                    break;
                }
            }
            Map<String, Object> canLamSang = voBenhAnNoiTruDAO.getListCanLamSang(L2Utils.getDvtt(session),
                    dataDialogApiJson.optString("sttBenhAn"),
                    Integer.parseInt(dataDialogApiJson.optString("soVaoVien")),
                    Integer.parseInt(dataDialogApiJson.optString("soVaoVienDt")));
            info.put("CAN_LAM_SANG", canLamSang);
            ret.put("INFO", info);
        } catch (Exception e) {
            e.printStackTrace();
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            ret.put("STATUS", "0");
            ret.put("MES", e.getMessage());
        }
        return ret;
    }

    public @ResponseBody
    @RequestMapping(value = "/khoi-tao-benh-an")
    Map khoiTaoBenhAn(HttpSession session, HttpServletRequest request, HttpServletResponse response,
                      @RequestBody trang1Obj trang1
    ) throws ParseException {
        Map ret = new HashMap();
        String maBenhNhan = request.getParameter("maBenhNhan");
        String soVaoVien = request.getParameter("soVaoVien");
        String soVaoVienDt = request.getParameter("soVaoVienDt");
        String soBenhAn = request.getParameter("soBenhAn");
        String loaiBA = request.getParameter("loaiBA");
        String nvLamBenhAn = request.getParameter("nvLamBenhAn");

        try {
            int createId = voBenhAnNoiTruDAO.khoiTaoVoBenhAn(
                    L2Utils.getDvtt(session), soVaoVien, soVaoVienDt, maBenhNhan,
                    soBenhAn, loaiBA, L2Utils.getMaPhongBan(session), L2Utils.getMaUser(session), nvLamBenhAn
            );
            if (createId > 0) {
                try {
                    String maYteTitle = voBenhAnNoiTruDAO.generalMaYTe(L2Utils.getDvtt(session), soBenhAn);
                    trang1.maYteVba = maYteTitle;
                    int tthc = voBenhAnNoiTruDAO.luuThongTinHanhChinh(createId, trang1);
                } catch (Exception e) {
                    ret.put("STATUS", "0");
                    ret.put("MAYTE_TITLE", "");
                    ret.put("MESSAGE", "Lỗi trong quá trình lưu thông tin hành chính");
                    int del = voBenhAnNoiTruDAO.xoaVoBenhAn(createId, L2Utils.getMaUser(session),
                            "/khoi-tao-benh-an", "Lỗi trong quá trình lưu thông tin hành chính");
                    return ret;
                }
                try {
                    List<Map<String, Object>> listchuyenKhoa = voBenhAnNoiTruDAO.dsChuyenKhoa(L2Utils.getDvtt(session), soVaoVien, soVaoVienDt);
                    /*Lọc bỏ lần vào khoa đầu tiên*/
                    trang1.dsChuyenKhoa = listchuyenKhoa.stream().filter((Map<String, Object> x) -> !x.get("STT_LOGKHOAPHONG").toString().equals("1")).collect(Collectors.toList());
                    int qlnb = voBenhAnNoiTruDAO.luuThongTinQuanLyNguoiBenh(createId, trang1);
                } catch (Exception e) {
                    ret.put("STATUS", "0");
                    ret.put("MAYTE_TITLE", "");
                    ret.put("MESSAGE", "Lỗi trong quá trình lưu thông tin quản lý người bệnh");
                    int del = voBenhAnNoiTruDAO.xoaVoBenhAn(createId, L2Utils.getMaUser(session),
                            "/khoi-tao-benh-an", "Lỗi trong quá trình lưu thông tin quản lý người bệnh");
                    return ret;
                }
                try {
                    int ttChanDoan = 0;
                    if (loaiBA.equals("NOITRUYHCT")||loaiBA.equals("NHIYHCT1941")){
                        ttChanDoan = voBenhAnNoiTruDAO.luuThongTinChanDoanNoiTruYHCT(createId, trang1);
                    } else if (loaiBA.equals("NGOAITRUYHCT")){
                        ttChanDoan = voBenhAnNoiTruDAO.luuThongTinChanDoanNgoaiTruYHCT(createId, trang1);
                    } else if (loaiBA.equals("NGOAITRUYHCT1941")){
                        ttChanDoan = voBenhAnNoiTruDAO.luuThongTinChanDoanNgoaiTruYHCT1941(createId, trang1);
                    } else {
                        ttChanDoan = voBenhAnNoiTruDAO.luuThongTinChanDoan(createId, trang1);
                    }
                } catch (Exception e) {
                    ret.put("STATUS", "0");
                    ret.put("MAYTE_TITLE", "");
                    ret.put("MESSAGE", "Lỗi trong quá trình lưu thông tin chẩn đoán");
                    int del = voBenhAnNoiTruDAO.xoaVoBenhAn(createId, L2Utils.getMaUser(session),
                            "/khoi-tao-benh-an", "Lỗi trong quá trình lưu thông tin chẩn đoán");
                    return ret;
                }
                try {
                    int ttRaVien = voBenhAnNoiTruDAO.luuThongTinRaVien(createId, trang1);
                } catch (Exception e) {
                    ret.put("STATUS", "0");
                    ret.put("MAYTE_TITLE", "");
                    ret.put("MESSAGE", "Lỗi trong quá trình lưu thông tin ra viện");
                    int del = voBenhAnNoiTruDAO.xoaVoBenhAn(createId, L2Utils.getMaUser(session),
                            "/khoi-tao-benh-an", "Lỗi trong quá trình lưu thông tin ra viện");
                    return ret;
                }
                ret.put("STATUS", "1");
                ret.put("MESSAGE", "");
                ret.put("MAYTE_TITLE", trang1.maYteVba);
                ret.put("ID_HSBA", createId);
                return ret;
            } else {
                ret.put("STATUS", "0");
                ret.put("MAYTE_TITLE", "");
                ret.put("MESSAGE", "Lỗi trong quá trình khởi tạo ID");
                int del = voBenhAnNoiTruDAO.xoaVoBenhAn(createId, L2Utils.getMaUser(session),
                        "/khoi-tao-benh-an", "Lỗi trong quá trình khởi tạo ID");
                return ret;
            }

        } catch (Exception e) {
            ret.put("STATUS", "0");
            ret.put("MAYTE_TITLE", "");
            ret.put("MESSAGE", "NOT FOUND TABLE! Vui lòng liên hệ ADMIN để được hổ trợ");
            return ret;
        }
    }

    public @ResponseBody
    @RequestMapping(value = "/luu-thong-tin-page-1")
    Map luuThongTinPage1(HttpSession session, HttpServletRequest request, HttpServletResponse response,
                         @RequestBody trang1Obj trang1
    ) throws ParseException {
        Map ret = new HashMap();
        int iD = Integer.parseInt(request.getParameter("iD"));
        String loaiBA = voBenhAnNoiTruDAO.VBA_getloaibenhan(L2Utils.getDvtt(session), iD);
        try {
            int tthc = voBenhAnNoiTruDAO.luuThongTinHanhChinh(iD, trang1);
            voBenhAnNoiTruDAO.log_update(iD, L2Utils.getDvtt(session), L2Utils.getMaUser(session));
        } catch (Exception e) {
            ret.put("STATUS", "0");
            ret.put("MESSAGE", "Lỗi trong quá trình lưu thông tin hành chính");
            return ret;
        }
        try {
            int qlnb = voBenhAnNoiTruDAO.luuThongTinQuanLyNguoiBenh(iD, trang1);
            voBenhAnNoiTruDAO.log_update(iD, L2Utils.getDvtt(session), L2Utils.getMaUser(session));
        } catch (Exception e) {
            ret.put("STATUS", "0");
            ret.put("MESSAGE", "Lỗi trong quá trình lưu thông tin quản lý người bệnh");
            return ret;
        }
        try {
            int ttChanDoan = 0;
            if (loaiBA.equals("NOITRUYHCT")||loaiBA.equals("NHIYHCT1941")) {
                ttChanDoan = voBenhAnNoiTruDAO.luuThongTinChanDoanNoiTruYHCT(iD, trang1);
            } else if (loaiBA.equals("SANKHOA")) {
                ttChanDoan = voBenhAnNoiTruDAO.luuThongTinChanDoanBASanKhoa(iD, trang1);
            } else if (loaiBA.equals("NGOAITRUYHCT")){
                ttChanDoan = voBenhAnNoiTruDAO.luuThongTinChanDoanNgoaiTruYHCT(iD, trang1);
            } else if (loaiBA.equals("NGOAITRUYHCT1941")){
                ttChanDoan = voBenhAnNoiTruDAO.luuThongTinChanDoanNgoaiTruYHCT1941(iD, trang1);
            } else {
                ttChanDoan = voBenhAnNoiTruDAO.luuThongTinChanDoan(iD, trang1);
            }
            voBenhAnNoiTruDAO.log_update(iD, L2Utils.getDvtt(session), L2Utils.getMaUser(session));
        } catch (Exception e) {
            ret.put("STATUS", "0");
            ret.put("MESSAGE", "Lỗi trong quá trình lưu thông tin chẩn đoán");
            return ret;
        }
        try {
            int ttRaVien = voBenhAnNoiTruDAO.luuThongTinRaVien(iD, trang1);
            voBenhAnNoiTruDAO.log_update(iD, L2Utils.getDvtt(session), L2Utils.getMaUser(session));
        } catch (Exception e) {
            ret.put("STATUS", "0");
            ret.put("MESSAGE", "Lỗi trong quá trình lưu thông tin ra viện");
            return ret;
        }
        try{
            String maPhuongXa = trang1.xaPhuong;
            voBenhAnNoiTruDAO.updMaBenhNhanOnDanhMucBenhNhan(iD, Integer.parseInt(trang1.xaPhuong));
        }catch(Exception e){

        }
        ret.put("STATUS", "1");
        ret.put("MESSAGE", "");
        return ret;

    }

    public @ResponseBody
    @RequestMapping(value = "/xoa-vo-benh-an")
    Map xoaVba(HttpSession session, HttpServletRequest request, HttpServletResponse response
    ) throws ParseException {
        Map ret = new HashMap();
        int iD = Integer.parseInt(request.getParameter("iD"));
        try {
            int del = voBenhAnNoiTruDAO.xoaVoBenhAn(iD,
                    L2Utils.getMaUser(session),
                    "/xoa-vo-benh-an", request.getParameter("lyDo"));
            ret.put("STATUS", "1");
            ret.put("MESSAGE", del);
        } catch (Exception e) {
            ret.put("STATUS", "0");
            ret.put("MESSAGE", "" + e.getMessage());
        }
        return ret;
    }

    @RequestMapping(value = "/searchBy{c}", method = RequestMethod.GET, produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map searchBy(@PathVariable("c") String type, @RequestParam(value = "sidx") String sidx,
                 @RequestParam(value = "page") String page, @RequestParam(value = "sord") String sord,
                 @RequestParam(value = "rows") String rows, @RequestParam(value = "searchTerm") String searchTerm,
                 HttpSession session,
                 @RequestParam(value = "maTinh", required = false, defaultValue = "-1") String maTinh,
                 @RequestParam(value = "maQuanHuyen", required = false, defaultValue = "-1") String maQuanHuyen
    ) {
        try {
            int page_Int = CommonFunctions.parseInt(page);
            int limit = CommonFunctions.parseInt(rows);
            if (sidx.equals("")) {
                sidx = "1";
            }
            if (searchTerm.equals("")) {
                searchTerm = "%";
            } else {
                searchTerm = "%" + searchTerm + "%";
            }
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
            String sql = "";
            int c;
            switch (type) {
                case "TenTinh":
                    sql = "SELECT COUNT(MA_TINH_THANH) " +
                            "FROM HIS_PUBLIC_LIST.DM_TINH_THANH " +
                            "WHERE UPPER(TEN_TINH_THANH) LIKE ?";
                    c = jdbcTemplate.queryForInt(sql, new Object[]{searchTerm.toUpperCase()});
                    break;
                case "TenQuanHuyen":
                    sql = "SELECT COUNT(TEN_QUAN_HUYEN) " +
                            "FROM HIS_PUBLIC_LIST.DM_QUAN_HUYEN " +
                            "WHERE UPPER(TEN_QUAN_HUYEN) LIKE ? AND MA_TINH_THANH = decode(?, '-1',MA_TINH_THANH, ? )";
                    c = jdbcTemplate.queryForInt(sql, new Object[]{searchTerm.toUpperCase(), Integer.parseInt(maTinh), Integer.parseInt(maTinh)});
                    break;
                case "TenPhuongXa":
                    sql = "SELECT COUNT(TEN_PHUONG_XA) " +
                            "FROM HIS_PUBLIC_LIST.DM_PHUONG_XA " +
                            "WHERE UPPER(TEN_PHUONG_XA) LIKE ? AND MA_TINH_THANH = decode(?, '-1',MA_TINH_THANH, ? ) AND MA_QUAN_HUYEN = decode(?,'-1',MA_QUAN_HUYEN, ?)";
                    c = jdbcTemplate.queryForInt(sql, new Object[]{searchTerm.toUpperCase(), Integer.parseInt(maTinh), Integer.parseInt(maTinh), Integer.parseInt(maQuanHuyen), Integer.parseInt(maQuanHuyen)});
                    break;
                case "TenNgheNghiep":
                    sql = "SELECT COUNT(MA_NGHE_NGHIEP) " +
                            "FROM HIS_PUBLIC_LIST.DM_NGHENGHIEP " +
                            "WHERE UPPER(TEN_NGHE_NGHIEP) LIKE ?";
                    c = jdbcTemplate.queryForInt(sql, new Object[]{searchTerm.toUpperCase()});
                    break;
                case "TenDanToc":
                    sql = "SELECT COUNT(MA_DANTOC) " +
                            "FROM HIS_PUBLIC_LIST.DM_DAN_TOC " +
                            "WHERE UPPER(TEN_DANTOC) LIKE ? ";
                    c = jdbcTemplate.queryForInt(sql, new Object[]{searchTerm.toUpperCase()});
                    break;
                case "TenKhoaPhong":
                    sql = "SELECT COUNT(MA_PHONGBAN) " +
                            "FROM HIS_FW.DM_PHONGBAN " +
                            "WHERE MA_DONVI = ? AND UPPER(TEN_PHONGBAN) LIKE ? ";
                    c = jdbcTemplate.queryForInt(sql, new Object[]{L2Utils.getDvtt(session), searchTerm.toUpperCase()});
                    break;
                case "QuocGia":
                    sql = "SELECT COUNT(KYHIEU) " +
                            "FROM HIS_MANAGER.DM_QUOCGIA " +
                            "WHERE UPPER(TEN_QUOCTE) LIKE ? ";
                    c = jdbcTemplate.queryForInt(sql, new Object[]{searchTerm.toUpperCase()});
                    break;
                default:
                    c = 0;
            }

            int total_pages;
            if (c > 0) {
                total_pages = (c / limit) + 1;
            } else {
                total_pages = 0;
            }
            if (page_Int > total_pages) {
                page_Int = total_pages;
            }
            int start = limit * page_Int - limit;
            List<Map<String, Object>> recore;
            if (total_pages != 0) {
                switch (type) {
                    case "TenTinh":
                        sql = "SELECT MA_TINH_THANH, TEN_TINH_THANH " +
                                "FROM HIS_PUBLIC_LIST.DM_TINH_THANH " +
                                "WHERE UPPER(TEN_TINH_THANH) like ? order by TEN_TINH_THANH ASC";
                        recore = jdbcTemplate.queryForList(sql, new Object[]{searchTerm.toUpperCase()});
                        break;
                    case "TenQuanHuyen":
                        sql = "SELECT MA_QUAN_HUYEN, TEN_QUAN_HUYEN " +
                                "FROM HIS_PUBLIC_LIST.DM_QUAN_HUYEN " +
                                "WHERE UPPER(TEN_QUAN_HUYEN) like ? AND MA_TINH_THANH = decode(?, '-1',MA_TINH_THANH, ?) " +
                                "order by TEN_QUAN_HUYEN ASC";
                        recore = jdbcTemplate.queryForList(sql, new Object[]{searchTerm.toUpperCase(), Integer.parseInt(maTinh), Integer.parseInt(maTinh)});
                        break;
                    case "TenPhuongXa":
                        sql = "SELECT MA_PHUONG_XA, TEN_PHUONG_XA " +
                                "FROM HIS_PUBLIC_LIST.DM_PHUONG_XA " +
                                "WHERE UPPER(TEN_PHUONG_XA) like ? AND MA_TINH_THANH = decode(?, '-1',MA_TINH_THANH, ?) AND MA_QUAN_HUYEN = decode(?, '-1',MA_QUAN_HUYEN, ?)" +
                                "order by TEN_PHUONG_XA ASC";
                        recore = jdbcTemplate.queryForList(sql, new Object[]{searchTerm.toUpperCase(), Integer.parseInt(maTinh), Integer.parseInt(maTinh), Integer.parseInt(maQuanHuyen), Integer.parseInt(maQuanHuyen)});
                        break;
                    case "TenNgheNghiep":
                        sql = "SELECT MA_NGHE_NGHIEP, TEN_NGHE_NGHIEP, MA_NGHE_NGHIEP_4069, TEN_NGHE_NGHIEP_4069 " +
                                "FROM HIS_PUBLIC_LIST.DM_NGHENGHIEP " +
                                "WHERE UPPER(TEN_NGHE_NGHIEP) like ? order by TEN_NGHE_NGHIEP ASC";
                        recore = jdbcTemplate.queryForList(sql, new Object[]{searchTerm.toUpperCase()});

                        break;
                    case "TenDanToc":
                        sql = "SELECT MA_DANTOC, MA_DANTOC_4069, TEN_DANTOC " +
                                "FROM HIS_PUBLIC_LIST.DM_DAN_TOC " +
                                "WHERE UPPER(TEN_DANTOC) like ? order by TEN_DANTOC ASC";
                        recore = jdbcTemplate.queryForList(sql, new Object[]{searchTerm.toUpperCase()});
                        break;
                    case "TenKhoaPhong":
                        sql = "SELECT MA_PHONGBAN, TEN_PHONGBAN " +
                                "FROM HIS_FW.DM_PHONGBAN " +
                                "WHERE MA_DONVI = ? AND UPPER(TEN_PHONGBAN) like ? order by TEN_PHONGBAN ASC";
                        recore = jdbcTemplate.queryForList(sql, new Object[]{L2Utils.getDvtt(session), searchTerm.toUpperCase()});
                        break;
                    case "QuocGia":
                        sql = "SELECT KYHIEU, TEN_VIETNAM AS TEN_QUOCGIA " +
                                "FROM  HIS_MANAGER.DM_QUOCGIA " +
                                "WHERE UPPER(TEN_QUOCTE) like ? order by TEN_QUOCTE ASC";
                        recore = jdbcTemplate.queryForList(sql, new Object[]{searchTerm.toUpperCase()});
                        break;
                    default:
                        recore = null;
                }
            } else {
                switch (type) {
                    case "TenTinh":
                        sql = "(SELECT MA_TINH_THANH, TEN_TINH_THANH " +
                                "FROM HIS_PUBLIC_LIST.DM_TINH_THANH " +
                                "WHERE UPPER(TEN_TINH_THANH) like ? order by TEN_TINH_THANH ASC) a where a.rn>= ?";
                        recore = jdbcTemplate.queryForList(sql, new Object[]{searchTerm.toUpperCase(), limit, start});
                        break;
                    case "TenQuanHuyen":
                        sql = "(SELECT MA_QUAN_HUYEN, TEN_QUAN_HUYEN " +
                                "FROM HIS_PUBLIC_LIST.DM_QUAN_HUYEN " +
                                "WHERE UPPER(TEN_QUAN_HUYEN) like ? AND MA_TINH_THANH = decode(?, '-1',MA_TINH_THANH, ?) " +
                                "order by TEN_QUAN_HUYEN ASC) a where a.rn>= ?";
                        recore = jdbcTemplate.queryForList(sql, new Object[]{searchTerm.toUpperCase(), Integer.parseInt(maTinh), Integer.parseInt(maTinh), limit, start});
                        break;
                    case "TenPhuongXa":
                        sql = "(SELECT MA_PHUONG_XA, TEN_PHUONG_XA " +
                                "FROM HIS_PUBLIC_LIST.DM_PHUONG_XA " +
                                "WHERE UPPER(TEN_PHUONG_XA) like ? AND MA_TINH_THANH = decode(?, '-1',MA_TINH_THANH, ?) AND MA_QUAN_HUYEN = decode(?, '-1',MA_QUAN_HUYEN, ?) " +
                                "order by TEN_PHUONG_XA ASC) a where a.rn>= ?";
                        recore = jdbcTemplate.queryForList(sql, new Object[]{searchTerm.toUpperCase(), Integer.parseInt(maTinh), Integer.parseInt(maTinh), Integer.parseInt(maQuanHuyen), Integer.parseInt(maQuanHuyen), limit, start});
                        break;
                    case "TenNgheNghiep":
                        sql = "(SELECT MA_NGHE_NGHIEP, TEN_NGHE_NGHIEP, MA_NGHE_NGHIEP_4069, TEN_NGHE_NGHIEP_4069 " +
                                "FROM HIS_PUBLIC_LIST.DM_NGHENGHIEP " +
                                "WHERE UPPER(TEN_NGHE_NGHIEP) like ? order by TEN_NGHE_NGHIEP ASC) a where a.rn>= ?";
                        recore = jdbcTemplate.queryForList(sql, new Object[]{searchTerm.toUpperCase(), limit, start});
                        break;
                    case "TenDanToc":
                        sql = "(SELECT MA_DANTOC, MA_DANTOC_4069, TEN_DANTOC " +
                                "FROM HIS_PUBLIC_LIST.DM_DAN_TOC " +
                                "WHERE UPPER(TEN_DANTOC) like ? order by TEN_DANTOC ASC) a where a.rn>= ?";
                        recore = jdbcTemplate.queryForList(sql, new Object[]{searchTerm.toUpperCase(), limit, start});
                        break;
                    case "TenKhoaPhong":
                        sql = "(SELECT MA_PHONGBAN, TEN_PHONGBAN " +
                                "FROM HIS_FW.DM_PHONGBAN " +
                                "WHERE MA_DONVI = ? AND UPPER(TEN_PHONGBAN) like ? order by TEN_PHONGBAN ASC) a where a.rn>= ?";
                        recore = jdbcTemplate.queryForList(sql, new Object[]{L2Utils.getDvtt(session), searchTerm.toUpperCase(), limit, start});
                        break;
                    case "QuocGia":
                        sql = "SELECT KYHIEU, TEN_VIETNAM AS TEN_QUOCGIA " +
                                "FROM HIS_MANAGER.DM_QUOCGIA " +
                                "WHERE  UPPER(TEN_QUOCTE) like ? order by TEN_QUOCTE ASC) a where a.rn>= ?";
                        recore = jdbcTemplate.queryForList(sql, new Object[]{searchTerm.toUpperCase(), limit, start});
                        break;
                    default:
                        recore = null;
                }
            }
            if (!recore.isEmpty()) {
                Map m = new HashMap();
                m.put("records", c);
                m.put("total", total_pages);
                m.put("rows", recore);
                return m;
            }
            return new HashMap();
        } catch (Exception e) {
            String a = e.getMessage();
            return new HashMap();
        }
    }

    public @ResponseBody
    @RequestMapping(value = "/update-benhan-ngoaikhoa")
    Map updateBenhAnNgoaiKhoa(HttpSession session,
                              HttpServletRequest request, HttpServletResponse response,
                              @RequestBody BenhAnNgoaiTruYHCT benhAnNgoaiTruYHCT
    ) throws ParseException {
        Map ret = new HashMap();
        try {
            int kq = voBenhAnNoiTruDAO.updateBenhAnNgoaiTruYHCT(benhAnNgoaiTruYHCT);
            ret.put("SUCCESS", kq);
            ret.put("MESSAGE", ' ');
        } catch (Exception e) {
            ret.put("SUCCESS", 0);
            ret.put("MESSAGE", "" + e.getMessage());
        }
        return ret;
    }

    public @ResponseBody
    @RequestMapping(value = "/load-thong-tin-chi-tiet-page2", method = RequestMethod.GET)
    Map loadThongTinBenhAnPage2(HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        try {
            String iD = request.getParameter("iD");
            String loaiBa = request.getParameter("loaiBa");
            List<Map<String, Object>> ls = voBenhAnNoiTruDAO.getThongTinPage2byID(Integer.parseInt(iD), loaiBa);
            Map map = ls.get(0);
            return map;
        } catch (Exception e) {
            return null;
        }
    }

    public @ResponseBody
    @RequestMapping(value = "/load-thong-tin-chi-tiet-page3", method = RequestMethod.GET)
    Map loadThongTinBenhAnPage3(HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        try {
            String iD = request.getParameter("iD");
            String loaiBa = request.getParameter("loaiBa");
            List<Map<String, Object>> ls;

            if (loaiBa.equals("NOITRUYHCT")||loaiBa.equals("NGOAITRUYHCT")||loaiBa.equals("NGOAITRUYHCT1941")||loaiBa.equals("NHIYHCT1941")){
                ls = voBenhAnNoiTruDAO.getThongTinPage3byID_YHCTNoiTru(Integer.parseInt(iD));
            } else {
                ls = voBenhAnNoiTruDAO.getThongTinPage3byID(Integer.parseInt(iD));
            }
            Map map = ls.get(0);
            return map;
        } catch (Exception e) {
            return null;
        }
    }

    @RequestMapping(value = "/update-benhan-ngoaitruyhct4069", produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map BenhAn_YHCTNgoaiTru_4069_Insert(HttpServletRequest request) {
        Map map = new HashMap();
        String ID = request.getParameter("ID");
        String LYDOVAOVIEN = request.getParameter("LYDOVAOVIEN");
        String VAONGAYTHU = request.getParameter("VAONGAYTHU");
        String BENHSU = request.getParameter("BENHSU");
        String TIENSUBANTHAN = request.getParameter("TIENSUBANTHAN");
        String BOXDDDIUNG = request.getParameter("BOXDDDIUNG");
        String DDDIUNG = request.getParameter("DDDIUNG");
        String BOXDDTHUOCLA = request.getParameter("BOXDDTHUOCLA");
        String DDTHUOCLA = request.getParameter("DDTHUOCLA");
        String BOXDDMATUY = request.getParameter("BOXDDMATUY");
        String DDMATUY = request.getParameter("DDMATUY");
        String BOXDDTHUOCLAO = request.getParameter("BOXDDTHUOCLAO");
        String DDTHUOCLAO = request.getParameter("DDTHUOCLAO");
        String BOXDDRUOUBIA = request.getParameter("BOXDDRUOUBIA");
        String DDRUOUBIA = request.getParameter("DDRUOUBIA");
        String BOXDDKHAC = request.getParameter("BOXDDKHAC");
        String DDKHAC = request.getParameter("DDKHAC");
        String TIENSUGIADINH = request.getParameter("TIENSUGIADINH");
        String DGANUONG = request.getParameter("DGANUONG");
        String DGMACQUANAO = request.getParameter("DGMACQUANAO");
        String DGDUNGNGOI = request.getParameter("DGDUNGNGOI");
        String DGCHAITOC = request.getParameter("DGCHAITOC");
        String DGDIVESINH = request.getParameter("DGDIVESINH");
        String DGTUSANDUNGLEN = request.getParameter("DGTUSANDUNGLEN");
        String DGDANHRANG = request.getParameter("DGDANHRANG");
        String DGNAMNGUASAP = request.getParameter("DGNAMNGUASAP");
        String DGKHANANGDICHUYEN = request.getParameter("DGKHANANGDICHUYEN");
        String DGTAM = request.getParameter("DGTAM");
        String DGNAMNGUANGOI = request.getParameter("DGNAMNGUANGOI");
        String DGDUNGCUTROGIUP = request.getParameter("DGDUNGCUTROGIUP");
        String DGKHAC = request.getParameter("DGKHAC");
        String KHAMTOANTHAN = request.getParameter("KHAMTOANTHAN");
        String MACH = request.getParameter("MACH");
        String NHIETDO = request.getParameter("NHIETDO");
        String HUYETAPTREN = request.getParameter("HUYETAPTREN");
        String HUYETAPDUOI = request.getParameter("HUYETAPDUOI");
        String NHIPTHO = request.getParameter("NHIPTHO");
        String CANNANG = request.getParameter("CANNANG");
        String CHIEUCAO = request.getParameter("CHIEUCAO");
        String BMI = request.getParameter("BMI");
        String CACBOPHAN = request.getParameter("CACBOPHAN");
        String KHUYETTATDACBIET = request.getParameter("KHUYETTATDACBIET");
        String THANKINHVANDONG = request.getParameter("THANKINHVANDONG");
        String THANKINHCAMGIAC = request.getParameter("THANKINHCAMGIAC");
        String THANKINHPHANXA = request.getParameter("THANKINHPHANXA");
        String THANKINHCOTRON = request.getParameter("THANKINHCOTRON");
        String THANKINHSONAO = request.getParameter("THANKINHSONAO");
        String THANKINHRLCN = request.getParameter("THANKINHRLCN");
        String TUANHOANNHIPTIM = request.getParameter("TUANHOANNHIPTIM");
        String TUANHOANTIENGTIM = request.getParameter("TUANHOANTIENGTIM");
        String TUANHOANRLCNTM = request.getParameter("TUANHOANRLCNTM");
        String HOHAPLONGNGUC = request.getParameter("HOHAPLONGNGUC");
        String HOHAPTHETICHKHI = request.getParameter("HOHAPTHETICHKHI");
        String HOHAPTINHTRANG = request.getParameter("HOHAPTINHTRANG");
        String HOHAPRLCN = request.getParameter("HOHAPRLCN");
        String TIEUHOATINHTRANG = request.getParameter("TIEUHOATINHTRANG");
        String TIEUHOARLCN = request.getParameter("TIEUHOARLCN");
        String DAVAMOI = request.getParameter("DAVAMOI");
        String HINHTHEKHOP = request.getParameter("HINHTHEKHOP");
        String THDVVKHOP = request.getParameter("THDVVKHOP");
        String THDRVKHOP = request.getParameter("THDRVKHOP");
        String TINHTRANGCO = request.getParameter("TINHTRANGCO");
        String ROILOANCO = request.getParameter("ROILOANCO");
        String DUOCTHUCO = request.getParameter("DUOCTHUCO");
        String COBOXV = request.getParameter("COBOXV");
        String COBOXIV = request.getParameter("COBOXIV");
        String COBOXIII = request.getParameter("COBOXIII");
        String COBOXII = request.getParameter("COBOXII");
        String COBOXI = request.getParameter("COBOXI");
        String TINHTRANGCOTSONG = request.getParameter("TINHTRANGCOTSONG");
        String SCHOBERCOTSONG = request.getParameter("SCHOBERCOTSONG");
        String STIBORCOTSONG = request.getParameter("STIBORCOTSONG");
        String TIETNIEUSINHDUC = request.getParameter("TIETNIEUSINHDUC");
        String CACCOQUANKHAC = request.getParameter("CACCOQUANKHAC");
        String HINHANHVV = request.getParameter("HINHANHVV");
        String XETNGHIEMCANLAM = request.getParameter("XETNGHIEMCANLAM");
        String TOMTATBENHAN = request.getParameter("TOMTATBENHAN");
        String BENHCHINH = request.getParameter("BENHCHINH");
        String BENHPHU = request.getParameter("BENHPHU");
        String PHANBIET = request.getParameter("PHANBIET");
        String VANDEKHIEMKHUYET = request.getParameter("VANDEKHIEMKHUYET");
        String DIEUTRIPHCN = request.getParameter("DIEUTRIPHCN");
        double canNang = (CANNANG == null || CANNANG.trim().equals("")) ? 0 : Float.parseFloat(CANNANG);
        double chieuCao = (CHIEUCAO == null || CHIEUCAO.trim().equals("") || CHIEUCAO.trim().equals("0")) ? 1 : (Float.parseFloat(CHIEUCAO) / 100);
        double bmi = canNang / (chieuCao * chieuCao);
        double bmilamtron = Math.round(bmi * 100.0) / 100.0;
        try {
            String sql = "call HIS_MANAGER.UPDATE_BAYHCT_NGOAITRU_4069(" +
                    "?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?)" +
                    "#l,s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
            jdbcTemplate.update(sql, new Object[]{
                    ID, LYDOVAOVIEN, BENHSU, TIENSUBANTHAN, BOXDDDIUNG
                    , DDDIUNG, BOXDDTHUOCLA, DDTHUOCLA, BOXDDMATUY, DDMATUY
                    , BOXDDTHUOCLAO, DDTHUOCLAO, BOXDDRUOUBIA, DDRUOUBIA, BOXDDKHAC
                    , DDKHAC, TIENSUGIADINH, DGANUONG, DGMACQUANAO, DGDUNGNGOI
                    , DGCHAITOC, DGDIVESINH, DGTUSANDUNGLEN, DGDANHRANG, DGNAMNGUASAP
                    , DGKHANANGDICHUYEN, DGTAM, DGNAMNGUANGOI, DGDUNGCUTROGIUP, DGKHAC
                    , KHAMTOANTHAN, MACH, NHIETDO, HUYETAPTREN, HUYETAPDUOI
                    , NHIPTHO, CANNANG, CHIEUCAO, bmilamtron, CACBOPHAN
                    , KHUYETTATDACBIET, THANKINHVANDONG, THANKINHCAMGIAC, THANKINHPHANXA, THANKINHCOTRON
                    , THANKINHSONAO, THANKINHRLCN, TIEUHOATINHTRANG, TIEUHOARLCN
                    , TUANHOANNHIPTIM, TUANHOANTIENGTIM, TUANHOANRLCNTM, HOHAPLONGNGUC, HOHAPTHETICHKHI
                    , HOHAPTINHTRANG, HOHAPRLCN, DAVAMOI, HINHTHEKHOP, THDVVKHOP
                    , THDRVKHOP, TINHTRANGCO, ROILOANCO, DUOCTHUCO, COBOXV
                    , COBOXIV, COBOXIII, COBOXII, COBOXI, TINHTRANGCOTSONG
                    , SCHOBERCOTSONG, STIBORCOTSONG, TIETNIEUSINHDUC, CACCOQUANKHAC, HINHANHVV
                    , XETNGHIEMCANLAM, TOMTATBENHAN, BENHCHINH, BENHPHU, PHANBIET
                    , VANDEKHIEMKHUYET, DIEUTRIPHCN, VAONGAYTHU
            });
            map.put("SUCCESS", "1");
            map.put("MESSAGE", " ");
        } catch (Exception e) {
            map.put("SUCCESS", "0");
            map.put("MESSAGE", e.getMessage());

        }
        return map;
    }

    @RequestMapping(value = "/print-vobenhan-by-id", method = {RequestMethod.POST, RequestMethod.GET})
    public @ResponseBody
    String printVoBenhAn(@RequestParam(value = "kyso", required = false, defaultValue = "0") String kyso,
                         @RequestParam(value = "smartcafiletype", required = false, defaultValue = "pdf") String smartcaFileType,
                         @RequestParam(value = "pageToPrint", required = false, defaultValue = "0") String pageToPrint,
                         HttpServletRequest request, HttpSession session, HttpServletResponse response) {
        try {
            Boolean shouldBeAddedPage1 = pageToPrint.equals("0") || pageToPrint.equals("1");
            Boolean shouldBeAddedPage2 = pageToPrint.equals("0") || pageToPrint.equals("2");
            Boolean shouldBeAddedPage3 = pageToPrint.equals("0") || pageToPrint.equals("3");
            String sql = "SELECT UPPER (TITLE_REPORT) AS TITLE_REPORT FROM HIS_MANAGER.DM_LOAI_VOBENHAN WHERE DVTT = :dvtt AND ID_VBA = :id ";
            NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSourceMNG);
            MapSqlParameterSource params = new MapSqlParameterSource().addValue("dvtt", L2Utils.getDvtt(session)).addValue("id", request.getParameter("loaiBenhAn"));
            Map title = namedParameterJdbcTemplate.queryForMap(sql, params);
            List<Map<String, Object>> page1 = voBenhAnNoiTruDAO.getThongTinPage1byID(Integer.parseInt(request.getParameter("iD")));
            List<Map<String, Object>> page2 = voBenhAnNoiTruDAO.getThongTinPage2byID(Integer.parseInt(request.getParameter("iD")), request.getParameter("loaiBenhAn"));
            List<Map<String, Object>> page3 = voBenhAnNoiTruDAO.getThongTinPage3byID(Integer.parseInt(request.getParameter("iD")));
            List<Map<String, Object>> page3yhct_noitru = voBenhAnNoiTruDAO.getThongTinPage3byID_YHCTNoiTru(Integer.parseInt(request.getParameter("iD")));
            String dvtt = session.getAttribute("Sess_DVTT").toString();
            Map parameters = new HashMap();
            Thamsohethong tsht = (Thamsohethong) request.getSession().getAttribute("Sess_Thamso");
            String capDonVi = L2Utils.getTuyenbenhvien(session);

            parameters.put("SOYTE", "SỞ Y TẾ " + tsht.tinh.toUpperCase());
            if (Integer.parseInt(capDonVi) > 3){
                parameters.put("DONVI_QUANLY", tsht.benhvientuyentren.toUpperCase());
                parameters.put("TENBENHVIEN", tsht.tenbenhvien.toUpperCase());
            }else {
                parameters.put("DONVI_QUANLY", "");
                parameters.put("TENBENHVIEN", tsht.tenbenhvien.toUpperCase());
            }
            parameters.put("TITLE", title.get("TITLE_REPORT").toString());
            parameters.put("SOLUUTRU", "Số lưu trữ: " + page1.get(0).get("SOLUUTRU_VBA").toString());
//            parameters.put("MAYTE", "Mã YT: " + page1.get(0).get("MAYTE_TITLE"));
            parameters.put("MAYTE", "Mã YT: " + page1.get(0).get("MABENHNHAN"));
            parameters.put("MABENHNHAN4GT", "Mã người bệnh: " + page1.get(0).get("MABENHNHAN").toString());
//            parameters.put("SOVAOVIEN4GT", "Số vào viện: " + page1.get(0).get("SOVAOVIEN_VBA").toString());
            parameters.put("SOVAOVIEN4GT", "Số vào viện: " + page1.get(0).get("SOBENHAN").toString());
            parameters.put("SOBENHAN4GT", page1.get(0).get("SOBENHAN").toString());
            parameters.put("SONHAPVIEN", "Số nhập Viện: " + page1.get(0).get("SOBENHAN").toString());
            parameters.put("DVTT", dvtt);
            String sqlThongTinKhoaGiuong = "call THONGTIN_KHOA_GIUONG_VBA(?,?,?)#c,s,s,s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
            Map ttkg = jdbcTemplate.queryForMap(sqlThongTinKhoaGiuong, new Object[]{L2Utils.getDvtt(session), page1.get(0).get("SOVAOVIEN").toString(), page1.get(0).get("SOVAOVIEN_DT").toString()});
            parameters.put("PHONGBENH", ttkg.get("PHONGBENH").toString());
            parameters.put("TENKHOA", ttkg.get("TENKHOA").toString());
            parameters.put("GIUONG_BENH", ttkg.get("GIUONG_BENH").toString());
            parameters.put("KHOA_GIUONG", "<p><b>Khoa: </b>" + ttkg.get("TENKHOA").toString() + "<br/>" +
                    "<b>Giường: </b>" + ttkg.get("GIUONG_BENH").toString() + "</p>");
            parameters.put("KHOA_GIUONG_PHCN", "<p><b>Phòng: </b>" + ttkg.get("TENKHOA").toString() + "&emsp;&emsp;" +
                    "<b>Giường: </b>" + ttkg.get("GIUONG_BENH").toString() + "</p>");

            parameters.put("BUONG_GIUONG", "<p>Buồng: " + ttkg.get("PHONGBENH").toString() + "&emsp;&emsp;" +
                    "Giường: " + ttkg.get("GIUONG_BENH").toString() + "</p>");

            parameters.put("SUBREPORT_DIR", request.getSession().getServletContext().getRealPath("/WEB-INF/pages/VoBenhAnNoiTru/report"));
            String HINHANHVV = "";
            try {
                HINHANHVV = page2.get(0).get("HINHANHVV").toString().replaceFirst("data:image/png;base64", "").replaceFirst("data:image/jpeg;base64", "");
            } catch (Exception e) {

            }

            parameters.put("HINHANHVV", HINHANHVV);
            List<JasperPrint> jasperPrintList = new ArrayList<>();
            String loaiBA = request.getParameter("loaiBenhAn");

            File path_ngoaitru = new File("");
            List<Map<String, Object>> map_duongdan_1 = voBenhAnNoiTruDAO.select_duongdanvobant(dvtt, "1", loaiBA);
            List<Map<String, Object>> map_duongdan_2 = voBenhAnNoiTruDAO.select_duongdanvobant(dvtt, "2", loaiBA);
            List<Map<String, Object>> map_duongdan_3 = voBenhAnNoiTruDAO.select_duongdanvobant(dvtt, "3", loaiBA);


            Map<String, Object> page_sub_1_temp = page1.get(0);
            Map<String, Object> page_sub_3_temp;

            updatePage1Data(page_sub_1_temp, page3yhct_noitru);

            if (loaiBA.equals("NOITRUYHCT") || loaiBA.equals("NGOAITRUYHCT") || loaiBA.equals("NGOAITRUYHCT1941") || loaiBA.equals("NHIYHCT1941")) {
                page_sub_3_temp = page3yhct_noitru.get(0);
            }else{
                page_sub_3_temp = page3.get(0);
            }
            List<Map<String, Object>> page_sub_1 = new ArrayList<Map<String, Object>>();
            List<Map<String, Object>> page_sub_3 = new ArrayList<Map<String, Object>>();

            if (page_sub_1_temp.get("DS_CHUYEN_KHOA") == null) {
                JSONArray emptyList = new JSONArray();
                page_sub_1_temp.put("DS_CHUYEN_KHOA", emptyList);
            }

            JSONArray jsonListChuyenKhoa = (JSONArray) new JSONParser().parse(page_sub_1_temp.get("DS_CHUYEN_KHOA").toString());
            if (jsonListChuyenKhoa.isEmpty()){
                org.json.simple.JSONObject chuyenKhoa = new org.json.simple.JSONObject();
                chuyenKhoa.put("MAPHONGBAN", "");
                chuyenKhoa.put("TEN_PHONGBAN", "");
                chuyenKhoa.put("SONGAY_DT", "");
                for (int i=0; i < 3; i++){
                    jsonListChuyenKhoa.add(chuyenKhoa);
                }
            }
            page_sub_1_temp.put("DANHSACH_CHUYENKHOA", jsonListChuyenKhoa);

            JSONArray jsonListTreSoSinhT1 = (JSONArray) new JSONParser().parse(page_sub_1_temp.get("DS_TRE_SO_SINH").toString());
            if (jsonListTreSoSinhT1.isEmpty()){
                org.json.simple.JSONObject item = new org.json.simple.JSONObject();
                item.put("MAPHONGBAN", "");
                item.put("TEN_PHONGBAN", "");
                item.put("SONGAY_DT", "");
                for (int i=0; i < 3; i++){
                    jsonListTreSoSinhT1.add(item);
                }
            }
            page_sub_1_temp.put("DANHSACH_TRESOSINH", jsonListTreSoSinhT1);

            if (loaiBA.equals("METHADONE")){
                JSONArray chatGaynghien = (JSONArray) new JSONParser().parse(page_sub_1_temp.get("METHADONE_CHAT_GAYNGHIEN").toString());
                JSONArray tienSuCaiNghien = (JSONArray) new JSONParser().parse(page_sub_1_temp.get("METHADONE_TS_CAINGHIEN").toString());
                page_sub_1_temp.replace("METHADONE_CHAT_GAYNGHIEN", chatGaynghien);
                page_sub_1_temp.replace("METHADONE_TS_CAINGHIEN", tienSuCaiNghien);
            }

            if (loaiBA.equals("SOSINH") && page2.get(0).get("SILVERMAN_SCORE") != null){
                ObjectMapper mapper = new ObjectMapper();
                Map<String, Object> mapSilverman = mapper.readValue(page2.get(0).get("SILVERMAN_SCORE").toString(), HashMap.class);
                page2.get(0).putAll(mapSilverman);
            }

            if (page_sub_3_temp.get("CHITIET_THUTHUAT_PHAUTHUAT") != null) {
                try {
                    JSONArray ttpt = (JSONArray) new JSONParser().parse(page3.get(0).get("CHITIET_THUTHUAT_PHAUTHUAT").toString());
                    page_sub_3_temp.replace("CHITIET_THUTHUAT_PHAUTHUAT", ttpt);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (loaiBA.equals("SANKHOA")){
                try {
                    List listDacDiemTreSoSinh = voBenhAnNoiTruDAO.getListDacDiemTreSoSinh(dvtt, page1.get(0).get("SOBENHAN").toString(), Integer.parseInt(page1.get(0).get("SOVAOVIEN").toString()));
                    List listDacDiemSoRau = voBenhAnNoiTruDAO.getListDacDiemSoRau(dvtt, page1.get(0).get("SOBENHAN").toString(), Integer.parseInt(page1.get(0).get("SOVAOVIEN").toString()));
                    page_sub_3_temp.put("DACDIEM_TRE_SOSINH", listDacDiemTreSoSinh);
                    page_sub_3_temp.put("DACDIEM_TRE_SORAU", listDacDiemSoRau);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            if (loaiBA.equals("NGOAITRUTMH") || loaiBA.equals("NGOAITRURHM") || loaiBA.equals("UNGBUOU") || loaiBA.equals("XAPHUONG") || loaiBA.equals("PHCN_BANT") || loaiBA.equals("NGOAITRU_CHUNG") || loaiBA.equals("PHATHAI")) {
                page_sub_1_temp.putAll(page2.get(0));
                page_sub_3_temp.putAll(page2.get(0));
            }
            if (loaiBA.equals("NGOAITRUYHCT")) {
                page_sub_3_temp.put("P3_NGAYNHAPVIEN_VARCHAR", page1.get(0).get("NGAYNHAPVIEN_VARCHAR").toString());
                page_sub_3_temp.put("P3_THOIGIAN_RAVIEN_VAR", page1.get(0).get("THOIGIAN_RAVIEN_VAR").toString());
                page_sub_3_temp.put("P3_SO_NGAY_DIEU_TRI", page1.get(0).get("SO_NGAY_DIEU_TRI").toString());
            }

            if (map_duongdan_1 != null && !map_duongdan_1.isEmpty() && shouldBeAddedPage1) {
                page_sub_1.add(page_sub_1_temp);
                for (Map<String, Object> row : map_duongdan_1) {
                    String duongdan_report = row.get("DUONG_DAN").toString();
                    path_ngoaitru = new File(request.getSession().getServletContext().getRealPath("" + duongdan_report + ""));
                    JasperPrint jasperPrint_page1 = JasperFillManager.fillReport((JasperReport) JRLoader.loadObject(path_ngoaitru),
                            parameters,
                            new JRBeanCollectionDataSource(page_sub_1)
                    );
                    jasperPrintList.add(jasperPrint_page1);
                }
            } else if(shouldBeAddedPage1) {
                File path_page1 = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/VoBenhAnNoiTru/report/trang1/rp_vobenhan_trang_1.jasper"));
                JasperPrint jasperPrint_page1 = JasperFillManager.fillReport((JasperReport) JRLoader.loadObject(path_page1),
                        parameters,
                        new JRBeanCollectionDataSource(page1)
                );
                jasperPrintList.add(jasperPrint_page1);
            }

            if (map_duongdan_2 != null && !map_duongdan_2.isEmpty() && shouldBeAddedPage2) {
                for (Map<String, Object> row : map_duongdan_2) {
                    String duongdan_report = row.get("DUONG_DAN").toString();
                    path_ngoaitru = new File(request.getSession().getServletContext().getRealPath("" + duongdan_report + ""));
                    JasperPrint jasperPrint_page2 = JasperFillManager.fillReport((JasperReport) JRLoader.loadObject(path_ngoaitru),
                            parameters,
                            new JRBeanCollectionDataSource(page2)
                    );
                    jasperPrintList.add(jasperPrint_page2);
                }
            }
            if(!loaiBA.equals("XAPHUONG")){
                if (map_duongdan_3 != null && !map_duongdan_3.isEmpty() && shouldBeAddedPage3) {
                    page_sub_3.add(page_sub_3_temp);
                    for (Map<String, Object> row : map_duongdan_3) {
                        String duongdan_report = row.get("DUONG_DAN").toString();
                        path_ngoaitru = new File(request.getSession().getServletContext().getRealPath("" + duongdan_report + ""));
                        JasperPrint jasperPrint_page3 = JasperFillManager.fillReport((JasperReport) JRLoader.loadObject(path_ngoaitru),
                                parameters,
                                new JRBeanCollectionDataSource(page_sub_3)
                        );
                        jasperPrintList.add(jasperPrint_page3);
                    }
                } else if(shouldBeAddedPage3) {
                    File path_page3 = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/VoBenhAnNoiTru/report/trang3/rp_vobenhan_trang_3.jasper"));
                    JasperPrint jasperPrint_page3 = JasperFillManager.fillReport((JasperReport) JRLoader.loadObject(path_page3),
                            parameters,
                            new JRBeanCollectionDataSource(page3)
                    );
                    jasperPrintList.add(jasperPrint_page3);
                }
            }
            File file;
            file = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/VoBenhAnNoiTru/") + request.getParameter("loaiBenhAn") + "_" + request.getParameter("iD") + "_" + new Date().getTime() + "." + smartcaFileType);
            if (smartcaFileType.equals("html")) {
                response.setContentType("text/html");
                response.setContentLength(new Long(file.length()).intValue());
                response.setHeader("Content-Disposition", "attachment; filename=" + request.getParameter("loaiBenhAn") + "_" + new Date().getTime() + ".html");
                HtmlExporter export = new HtmlExporter();
                export.setExporterInput(SimpleExporterInput.getInstance(jasperPrintList));
                export.setExporterOutput(new SimpleHtmlExporterOutput(response.getOutputStream()));
                export.exportReport();
            }else if (smartcaFileType.equals("rtf")){
                JRRtfExporter exporter = new JRRtfExporter();
                exporter.setExporterInput(SimpleExporterInput.getInstance(jasperPrintList));
                exporter.setExporterOutput(new SimpleWriterExporterOutput(file));
                exporter.exportReport();
                if (file.exists()) {
                    response.setContentType("application/rtf");
                    response.setContentLength(new Long(file.length()).intValue());
                    response.setHeader("Content-Disposition", "attachment; filename=" + file.getName());
                    FileCopyUtils.copy(new FileInputStream(file), response.getOutputStream());
                }
            } else {
                JRPdfExporter exporter = new JRPdfExporter();
                exporter.setExporterInput(SimpleExporterInput.getInstance(jasperPrintList));
                exporter.setExporterOutput(new SimpleOutputStreamExporterOutput(file));
                SimplePdfExporterConfiguration configuration = new SimplePdfExporterConfiguration();
                configuration.setCreatingBatchModeBookmarks(true);
                exporter.setConfiguration(configuration);
                exporter.exportReport();
                if (file.exists()) {
                    response.setContentType("application/pdf");
                    response.setContentLength(new Long(file.length()).intValue());
                    response.setHeader("Content-Disposition", "attachment; filename=" + file.getName());
                    FileCopyUtils.copy(new FileInputStream(file), response.getOutputStream());
                }
            }
            return null;
        } catch (Exception ex) {
            Logger.getLogger(VoBenAnNoiTruController.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    private void updatePage1Data(Map<String, Object> page1Data, List<Map<String, Object>> page3YHCTNoiTru) {
        try {
            updateDateFormat(page1Data, "NGAYSINHCUAME");
            updateDateFormat(page1Data, "NGAYSINH_CUACHA");

            String thoiGianChuyenKhoa0 = Optional.ofNullable(page1Data.get("CHUYENKHOATHOIGIAN0")).map(Object::toString).orElse(null);
            String thoiGianChuyenKhoa1 = Optional.ofNullable(page1Data.get("CHUYENKHOATHOIGIAN1")).map(Object::toString).orElse(null);
            if (thoiGianChuyenKhoa0 != null && thoiGianChuyenKhoa1 != null) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
                LocalDateTime ngayVaoKhoa0 = LocalDateTime.parse(thoiGianChuyenKhoa0, formatter);
                LocalDateTime ngayVaoKhoa1 = LocalDateTime.parse(thoiGianChuyenKhoa1, formatter);
                if (ngayVaoKhoa0.isAfter(ngayVaoKhoa1)) {
                    sapXepTheoNgayVaoKhoa(page1Data);
                }
            }

            if (!CollectionUtils.isEmpty(page3YHCTNoiTru)) {
                updateChanDoanYHCT(page1Data, page3YHCTNoiTru.get(0));
            }
        } catch (Exception ex) {
            Logger.getLogger(VoBenAnNoiTruController.class.getName()).log(Level.SEVERE, "Error update page1 data", ex);
        }
    }

    private void updateChanDoanYHCT(Map<String, Object> page1Data, Map<String, Object> page3Data) {
        // Update YHCT
        page1Data.put("ICDYHCT_KHOADT_TEN", page3Data.get("YHCTICDBENHCHINH_TEN"));
        page1Data.put("ICDYHCT_KHOADT", page3Data.get("YHCTICDBENHCHINH"));
        page1Data.put("ICDYHCTPHU1_KHOADT_TEN", page3Data.get("YHCTBENHKEMTHEO"));
        page1Data.put("ICDYHCTPHU1_KHOADT", page3Data.get("ICD1YHCTBENHKEMTHEO_VAOVIEN"));

        page1Data.put("ICDYHCT_BENHCHINH_RV_TEN", page3Data.get("YHCTRAVIENICDBENHCHINH_TEN"));
        page1Data.put("ICDYHCT_BENHCHINH_RV", page3Data.get("YHCTRAVIENICDBENHCHINH"));
        page1Data.put("ICDYHCT_BENHPHU_RV_TEN", page3Data.get("YHCTRAVIENBENHKEMTHEO_TEN"));
        page1Data.put("ICDYHCT_BENHPHU_RV", getICDBenhPhuList(page3Data, "ICD1YHCTBENHKEMTHEO_RAVIEN", "ICD2YHCTBENHKEMTHEO_RAVIEN"));

        // Update YHHD
        page1Data.put("TENICD_KHOADIEUTRI", page3Data.get("YHHDICDBENHCHINH_TEN"));
        page1Data.put("ICD_KHOADIEUTRI", page3Data.get("YHHDICDBENHCHINH"));
        page1Data.put("ICDPHU1_KHOADT_TEN", page3Data.get("YHHDBENHKEMTHEO"));
        page1Data.put("ICDPHU1_KHOADT", page3Data.get("ICD1BENHKEMTHEO_VAOVIEN"));

        page1Data.put("TENICD_BENHRAVIEN", page3Data.get("YHHDRAVIENICDBENHCHINH_TEN"));
        page1Data.put("ICD_BENHRAVIEN", page3Data.get("YHHDRAVIENICDBENHCHINH"));
        page1Data.put("ICD_BENHPHU_RV_TEN", page3Data.get("YHHDRAVIENBENHKEMTHEO_TEN"));
        page1Data.put("ICD_BENHPHU_RV", getICDBenhPhuList(page3Data, "ICD1BENHKEMTHEO_RAVIEN", "ICD2BENHKEMTHEO_RAVIEN"));
    }

    private String getICDBenhPhuList(Map<String, Object> page3Data, String idcBenhPhu1, String idcBenhPhu2) {
        List<String> idcBenhPhuRaVienList = new ArrayList<>();
        Optional.ofNullable(page3Data.get(idcBenhPhu1))
                .map(Object::toString)
                .map(String::trim)
                .filter(icd1 -> !icd1.isEmpty())
                .ifPresent(idcBenhPhuRaVienList::add);

        Optional.ofNullable(page3Data.get(idcBenhPhu2))
                .map(Object::toString)
                .map(String::trim)
                .filter(icd2 -> !icd2.isEmpty())
                .ifPresent(idcBenhPhuRaVienList::add);
        return String.join("; ", idcBenhPhuRaVienList);
    }

    private void updateDateFormat(Map<String, Object> page1Data, String dateKey) {
        Optional.ofNullable(page1Data.get(dateKey))
                .map(Object::toString)
                .ifPresent(ngay -> {
                    // Convert date format 'yyyy-MM-dd' to 'dd/MM/yyyy'
                    if (ngay.matches("\\d{4}-\\d{2}-\\d{2}")) {
                        String formattedDate = LocalDate.parse(ngay).format(DateTimeFormatter.ofPattern("dd/MM/yyyy"));
                        page1Data.put(dateKey, formattedDate);
                    }
                });
    }

    private void sapXepTheoNgayVaoKhoa(Map<String, Object> page1Data) {
        String maKhoaKey = "MAKHOA%s";
        String tenKhoaKey = "TENKHOA%s";
        String thoiGianChuyenKhoaTextKey = "CHUYENKHOATHOIGIANTEXT%s";
        String thoiGianChuyenKhoaKey = "CHUYENKHOATHOIGIAN%s";
        String soNgayChuyenKhoaKey = "CHUYENKHOASONGAY%s";
        String soNgayChuyenKhoa0Key = "CHUYENKHOASONGAY%s_0";
        String soNgayChuyenKhoa1Key = "CHUYENKHOASONGAY%s_1";
        String soNgayChuyenKhoa2Key = "CHUYENKHOASONGAY%s_2";

       List<Map<String, String>> khoaList = new ArrayList<>();

        for (int i = 0; i < 2; i++) {
            String maKhoa = getStrValue(page1Data.get(getKeyValue(maKhoaKey, i)));
            String tenKhoa = getStrValue(page1Data.get(getKeyValue(tenKhoaKey, i)));
            String thoiGianChuyenKhoaText = getStrValue(page1Data.get(getKeyValue(thoiGianChuyenKhoaTextKey, i)));
            String thoiGianChuyenKhoa = getStrValue(page1Data.get(getKeyValue(thoiGianChuyenKhoaKey, i)));
            String soNgayChuyenKhoa = getStrValue(page1Data.get(getKeyValue(soNgayChuyenKhoaKey, i)));
            String soNgayChuyenKhoa0 = getStrValue(page1Data.get(getKeyValue(soNgayChuyenKhoa0Key, i)));
            String soNgayChuyenKhoa1 = getStrValue(page1Data.get(getKeyValue(soNgayChuyenKhoa1Key, i)));
            String soNgayChuyenKhoa2 = getStrValue(page1Data.get(getKeyValue(soNgayChuyenKhoa2Key, i)));

            Map<String, String> thongTinKhoa = new HashMap<>();
            thongTinKhoa.put(maKhoaKey, maKhoa);
            thongTinKhoa.put(tenKhoaKey, tenKhoa);
            thongTinKhoa.put(thoiGianChuyenKhoaTextKey, thoiGianChuyenKhoaText);
            thongTinKhoa.put(thoiGianChuyenKhoaKey, thoiGianChuyenKhoa);
            thongTinKhoa.put(soNgayChuyenKhoaKey, soNgayChuyenKhoa);
            thongTinKhoa.put(soNgayChuyenKhoa0Key, soNgayChuyenKhoa0);
            thongTinKhoa.put(soNgayChuyenKhoa1Key, soNgayChuyenKhoa1);
            thongTinKhoa.put(soNgayChuyenKhoa2Key, soNgayChuyenKhoa2);

            khoaList.add(thongTinKhoa);
        }

        Collections.reverse(khoaList);

        for (int i = 0; i < 2; i++) {
            Map<String, String> khoaMap = khoaList.get(i);
            for (Map.Entry<String, String> entry : khoaMap.entrySet()) {
                page1Data.put(getKeyValue(entry.getKey(), i), entry.getValue());
            }
        }
    }

    private String getKeyValue(String keyStr, int stt) {
        return String.format(keyStr, stt);
    }

    private String getStrValue(Object obj) {
        return Optional.ofNullable(obj).map(Object::toString).orElse("");
    }

    public @ResponseBody
    @RequestMapping(value = "/BenhAn_YHCTNgoaiTru_4069_SelectHinhAnh", method = RequestMethod.GET)
    Map BenhAn_YHCTNgoaiTru_4069_SelectHinhAnh(HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        Map map = new HashMap();
        String ID = request.getParameter("ID");
        String sqlHinh = "call HIS_MANAGER.SELECT_BAYHCT_NGOAITRU_4069_IM(?)#c,s";
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        map = jdbcTemplate.queryForMap(sqlHinh, new Object[]{ID});
        return map;
    }

    public @ResponseBody
    @RequestMapping(value = "/BenhAn_VBA_SelectHinhAnh", method = RequestMethod.GET)
    Map BenhAn_VBA_SelectHinhAnh(HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        Map map = new HashMap();
        String ID = request.getParameter("ID");
        String LOAIBENHAN = request.getParameter("LOAIBENHAN");
        String sqlHinh = "call HIS_MANAGER.SELECT_HINHANHVV_VBA_IM(?,?)#c,s,s";
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        map = jdbcTemplate.queryForMap(sqlHinh, new Object[]{ID, LOAIBENHAN});
        return map;
    }

    public @ResponseBody
    @RequestMapping(value = "/update-benhan-noitruyhct", method = RequestMethod.POST)
    Map BenhAn_YHCTNoiTruInsert(HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        int vreturn = -1;
        String dvtt = L2Utils.getDvtt(session);
        String maKhoa = L2Utils.getMaPhongBan(session);
        String idUser = L2Utils.getMaUser(session);
        String ID = request.getParameter("ID");
        String LYDOVAOVIEN = request.getParameter("LYDOVAOVIEN");
        String VAONGAYTHU = request.getParameter("VAONGAYTHU");
        String BENHSU = request.getParameter("BENHSU");
        String TIENSUBANTHAN = request.getParameter("TIENSUBANTHAN");
        String TIENSU1 = request.getParameter("TIENSU1");
        String TIENSU2 = request.getParameter("TIENSU2");
        String TIENSU3 = request.getParameter("TIENSU3");
        String TIENSU4 = request.getParameter("TIENSU4");
        String MOTABANTHAN = request.getParameter("MOTABANTHAN");
        String TIENSUBENHTAT = request.getParameter("TIENSUBENHTAT");
        String TIENSUGIADINH = request.getParameter("TIENSUGIADINH");
        String KHAMTOANTHAN = request.getParameter("KHAMTOANTHAN");
        String MACH = request.getParameter("MACH");
        String NHIETDO = request.getParameter("NHIETDO");
        String HUYETAPTREN = request.getParameter("HUYETAPTREN");
        String HUYETAPDUOI = request.getParameter("HUYETAPDUOI");
        String NHIPTHO = request.getParameter("NHIPTHO");
        String CANNANG = request.getParameter("CANNANG");
        String CHIEUCAO = request.getParameter("CHIEUCAO");
        String BMI = request.getParameter("BMI");
        String TUANHOAN = request.getParameter("TUANHOAN");
        String HOHAP = request.getParameter("HOHAP");
        String TIEUHOA = request.getParameter("TIEUHOA");
        String TIETNIEUSINHDUC = request.getParameter("TIETNIEUSINHDUC");
        String THANKINH = request.getParameter("THANKINH");
        String XUONGKHOP = request.getParameter("XUONGKHOP");
        String TMH = request.getParameter("TMH");
        String RHM = request.getParameter("RHM");
        String MAT = request.getParameter("MAT");
        String NOITIET = request.getParameter("NOITIET");
        String CLS = request.getParameter("CLS");
        String TOMTATBA = request.getParameter("TOMTATBA");
        String BENHCHINH = request.getParameter("BENHCHINH");
        String ICDCHINH1 = request.getParameter("ICDCHINH1");
        String ICDCHINH2 = request.getParameter("ICDCHINH2");
        String ICDCHINH3 = request.getParameter("ICDCHINH3");
        String ICDCHINH4 = request.getParameter("ICDCHINH4");
        String ICDCHINH5 = request.getParameter("ICDCHINH5");
        String BENHPHU = request.getParameter("BENHPHU");
        String ICDPHU1 = request.getParameter("ICDPHU1");
        String ICDPHU2 = request.getParameter("ICDPHU2");
        String ICDPHU3 = request.getParameter("ICDPHU3");
        String ICDPHU4 = request.getParameter("ICDPHU4");
        String ICDPHU5 = request.getParameter("ICDPHU5");
        String ICDPHU6 = request.getParameter("ICDPHU6");
        String ICDPHU7 = request.getParameter("ICDPHU7");
        String ICDPHU8 = request.getParameter("ICDPHU8");
        String ICDPHU9 = request.getParameter("ICDPHU9");
        String ICDPHU10 = request.getParameter("ICDPHU10");
        String PHANBIET = request.getParameter("PHANBIET");
        String HINHTHAI1 = request.getParameter("HINHTHAI1");
        String HINHTHAI2 = request.getParameter("HINHTHAI2");
        String HINHTHAI3 = request.getParameter("HINHTHAI3");
        String THAN1 = request.getParameter("THAN1");
        String THAN2 = request.getParameter("THAN2");
        String SAC1 = request.getParameter("SAC1");
        String SAC2 = request.getParameter("SAC2");
        String SAC3 = request.getParameter("SAC3");
        String TRACH1 = request.getParameter("TRACH1");
        String TRACH2 = request.getParameter("TRACH2");
        String HINHLUOI1 = request.getParameter("HINHLUOI1");
        String HINHLUOI2 = request.getParameter("HINHLUOI2");
        String CHATLUOI1 = request.getParameter("CHATLUOI1");
        String CHATLUOI2 = request.getParameter("CHATLUOI2");
        String CHATLUOI3 = request.getParameter("CHATLUOI3");
        String REULUOI1 = request.getParameter("REULUOI1");
        String REULUOI2 = request.getParameter("REULUOI2");
        String REULUOI3 = request.getParameter("REULUOI3");
        String MOTAVONGCHAN = request.getParameter("MOTAVONGCHAN");
        String TIENGNOI1 = request.getParameter("TIENGNOI1");
        String TIENGNOI2 = request.getParameter("TIENGNOI2");
        String TIENGNOI3 = request.getParameter("TIENGNOI3");
        String TIENGNOI4 = request.getParameter("TIENGNOI4");
        String TIENGNOI5 = request.getParameter("TIENGNOI5");
        String HOITHO1 = request.getParameter("HOITHO1");
        String HOITHO2 = request.getParameter("HOITHO2");
        String HOITHO3 = request.getParameter("HOITHO3");
        String HOITHO4 = request.getParameter("HOITHO4");
        String HOITHO5 = request.getParameter("HOITHO5");
        String COHO = request.getParameter("COHO");
        String HO1 = request.getParameter("HO1");
        String HO2 = request.getParameter("HO2");
        String HO3 = request.getParameter("HO3");
        String COO = request.getParameter("COO");
        String CONAC = request.getParameter("CONAC");
        String COMUI = request.getParameter("COMUI");
        String MUICOTHE1 = request.getParameter("MUICOTHE1");
        String MUICOTHE2 = request.getParameter("MUICOTHE2");
        String MUICOTHE3 = request.getParameter("MUICOTHE3");
        String COCHATTHAI = request.getParameter("COCHATTHAI");
        String CHATTHAI1 = request.getParameter("CHATTHAI1");
        String CHATTHAI2 = request.getParameter("CHATTHAI2");
        String CHATTHAI3 = request.getParameter("CHATTHAI3");
        String MOTAVANCHAN = request.getParameter("MOTAVANCHAN");
        String COHANNHIET = request.getParameter("COHANNHIET");
        String HANNHIET1 = request.getParameter("HANNHIET1");
        String HANNHIET2 = request.getParameter("HANNHIET2");
        String HANNHIET3 = request.getParameter("HANNHIET3");
        String MOHOI1 = request.getParameter("MOHOI1");
        String MOHOI2 = request.getParameter("MOHOI2");
        String MOHOI3 = request.getParameter("MOHOI3");
        String CODAUMATCO = request.getParameter("CODAUMATCO");
        String DAUDAU1 = request.getParameter("DAUDAU1");
        String DAUDAU2 = request.getParameter("DAUDAU2");
        String DAUDAU3 = request.getParameter("DAUDAU3");
        String COHMCM = request.getParameter("COHMCM");
        String DOIMAT1 = request.getParameter("DOIMAT1");
        String DOIMAT2 = request.getParameter("DOIMAT2");
        String DOIMAT3 = request.getParameter("DOIMAT3");
        String TAI1 = request.getParameter("TAI1");
        String TAI2 = request.getParameter("TAI2");
        String TAI3 = request.getParameter("TAI3");
        String MUI1 = request.getParameter("MUI1");
        String MUI2 = request.getParameter("MUI2");
        String MUI3 = request.getParameter("MUI3");
        String HONG1 = request.getParameter("HONG1");
        String HONG2 = request.getParameter("HONG2");
        String HONG3 = request.getParameter("HONG3");
        String COVAI1 = request.getParameter("COVAI1");
        String COVAI2 = request.getParameter("COVAI2");
        String COVAI3 = request.getParameter("COVAI3");
        String COLUNG = request.getParameter("COLUNG");
        String LUNG = request.getParameter("LUNG");
        String CONGUC = request.getParameter("CONGUC");
        String NGUC1 = request.getParameter("NGUC1");
        String NGUC2 = request.getParameter("NGUC2");
        String NGUC3 = request.getParameter("NGUC3");
        String NGUC4 = request.getParameter("NGUC4");
        String NGUC5 = request.getParameter("NGUC5");
        String NGUC6 = request.getParameter("NGUC6");
        String COBUNG = request.getParameter("COBUNG");
        String BUNG1 = request.getParameter("BUNG1");
        String BUNG2 = request.getParameter("BUNG2");
        String BUNG3 = request.getParameter("BUNG3");
        String BUNG4 = request.getParameter("BUNG4");
        String BUNG5 = request.getParameter("BUNG5");
        String BUNG6 = request.getParameter("BUNG6");
        String COCHANTAY = request.getParameter("COCHANTAY");
        String CHANTAY1 = request.getParameter("CHANTAY1");
        String CHANTAY2 = request.getParameter("CHANTAY2");
        String CHANTAY3 = request.getParameter("CHANTAY3");
        String CHANTAY4 = request.getParameter("CHANTAY4");
        String CHANTAY5 = request.getParameter("CHANTAY5");
        String CHANTAY6 = request.getParameter("CHANTAY6");
        String COAN = request.getParameter("COAN");
        String AN1 = request.getParameter("AN1");
        String AN2 = request.getParameter("AN2");
        String AN3 = request.getParameter("AN3");
        String COUONG = request.getParameter("COUONG");
        String UONG1 = request.getParameter("UONG1");
        String UONG2 = request.getParameter("UONG2");
        String UONG3 = request.getParameter("UONG3");
        String CODAITIEUTIEN = request.getParameter("CODAITIEUTIEN");
        String TIEUTIEN1 = request.getParameter("TIEUTIEN1");
        String TIEUTIEN2 = request.getParameter("TIEUTIEN2");
        String TIEUTIEN3 = request.getParameter("TIEUTIEN3");
        String DAITIEN1 = request.getParameter("DAITIEN1");
        String DAITIEN2 = request.getParameter("DAITIEN2");
        String DAITIEN3 = request.getParameter("DAITIEN3");
        String CONGU = request.getParameter("CONGU");
        String NGU1 = request.getParameter("NGU1");
        String NGU2 = request.getParameter("NGU2");
        String NGU3 = request.getParameter("NGU3");
        String COSDSS = request.getParameter("COSDSS");
        String BENHNAM1 = request.getParameter("BENHNAM1");
        String BENHNAM2 = request.getParameter("BENHNAM2");
        String BENHNAM3 = request.getParameter("BENHNAM3");
        String BENHNU1 = request.getParameter("BENHNU1");
        String BENHNU2 = request.getParameter("BENHNU2");
        String BENHNU3 = request.getParameter("BENHNU3");
        //<%--Bổ sung ngày 04/05/2021 phần thiết chẩn,...--%>
        String COKINHNGUYET = request.getParameter("COKINHNGUYET");
        String RLKINHNGUYET1 = request.getParameter("RLKINHNGUYET1");
        String RLKINHNGUYET2 = request.getParameter("RLKINHNGUYET2");
        String THONGKINH1 = request.getParameter("THONGKINH1");
        String THONGKINH2 = request.getParameter("THONGKINH2");
        String CODOIHA = request.getParameter("CODOIHA");
        String DOIHA1 = request.getParameter("DOIHA1");
        String DOIHA2 = request.getParameter("DOIHA2");
        String COYEUTOLIENQUAN = request.getParameter("COYEUTOLIENQUAN");
        String MOTAKHACVANCHAN = request.getParameter("MOTAKHACVANCHAN");
        String COXUCCHAN = request.getParameter("COXUCCHAN");
        String DA1 = request.getParameter("DA1");
        String DA2 = request.getParameter("DA2");
        String DA3 = request.getParameter("DA3");
        String COCOXUONGKHOP = request.getParameter("COCOXUONGKHOP");
        String COXUONGKHOP1 = request.getParameter("COXUONGKHOP1");
        String COXUONGKHOP2 = request.getParameter("COXUONGKHOP2");
        String COXUONGKHOP3 = request.getParameter("COXUONGKHOP3");
        String COBUNGTHIETCHAN = request.getParameter("COBUNGTHIETCHAN");
        String BUNGTHIETCHAN1 = request.getParameter("BUNGTHIETCHAN1");
        String BUNGTHIETCHAN2 = request.getParameter("BUNGTHIETCHAN2");
        String BUNGTHIETCHAN3 = request.getParameter("BUNGTHIETCHAN3");
        String COMOHOITHIETCHAN = request.getParameter("COMOHOITHIETCHAN");
        String MOHOITHIETCHAN1 = request.getParameter("MOHOITHIETCHAN1");
        String MOHOITHIETCHAN2 = request.getParameter("MOHOITHIETCHAN2");
        String MOHOITHIETCHAN3 = request.getParameter("MOHOITHIETCHAN3");
        String MACHCHAN1 = request.getParameter("MACHCHAN1");
        String MACHCHAN2 = request.getParameter("MACHCHAN2");
        String MACHCHAN3 = request.getParameter("MACHCHAN3");
        String TONGKHANPHAI1 = request.getParameter("TONGKHANPHAI1");
        String TONGKHANPHAI2 = request.getParameter("TONGKHANPHAI2");
        String TONGKHANPHAI3 = request.getParameter("TONGKHANPHAI3");
        String TONGKHANTRAI1 = request.getParameter("TONGKHANTRAI1");
        String TONGKHANTRAI2 = request.getParameter("TONGKHANTRAI2");
        String TONGKHANTRAI3 = request.getParameter("TONGKHANTRAI3");
        String TAITRAITHON1 = request.getParameter("TAITRAITHON1");
        String TAITRAITHON2 = request.getParameter("TAITRAITHON2");
        String TAITRAITHON3 = request.getParameter("TAITRAITHON3");
        String TAITRAIQUAN1 = request.getParameter("TAITRAIQUAN1");
        String TAITRAIQUAN2 = request.getParameter("TAITRAIQUAN2");
        String TAITRAIQUAN3 = request.getParameter("TAITRAIQUAN3");
        String TAITRAIXICH1 = request.getParameter("TAITRAIXICH1");
        String TAITRAIXICH2 = request.getParameter("TAITRAIXICH2");
        String TAITRAIXICH3 = request.getParameter("TAITRAIXICH3");

        String TAIPHAITHON1 = request.getParameter("TAIPHAITHON1");
        String TAIPHAITHON2 = request.getParameter("TAIPHAITHON2");
        String TAIPHAITHON3 = request.getParameter("TAIPHAITHON3");
        String TAIPHAIQUAN1 = request.getParameter("TAIPHAIQUAN1");
        String TAIPHAIQUAN2 = request.getParameter("TAIPHAIQUAN2");
        String TAIPHAIQUAN3 = request.getParameter("TAIPHAIQUAN3");
        String TAIPHAIXICH1 = request.getParameter("TAIPHAIXICH1");
        String TAIPHAIXICH2 = request.getParameter("TAIPHAIXICH2");
        String TAIPHAIXICH3 = request.getParameter("TAIPHAIXICH3");

        String MOTATHIETCHAN = request.getParameter("MOTATHIETCHAN");
        String TOMTATTUCHAN = request.getParameter("TOMTATTUCHAN");
        String BIENCHUNGLUANTRI = request.getParameter("BIENCHUNGLUANTRI");
        String CHUANDOANBENHDANH = request.getParameter("CHUANDOANBENHDANH");
        String BENHDANH1 = request.getParameter("BENHDANH1");
        String BENHDANH2 = request.getParameter("BENHDANH2");
        String BENHDANH3 = request.getParameter("BENHDANH3");
        String BENHDANH4 = request.getParameter("BENHDANH4");
        String BENHDANH5 = request.getParameter("BENHDANH5");
        String BENHDANH6 = request.getParameter("BENHDANH6");
        String CHUANDOANBATCUONG = request.getParameter("CHUANDOANBATCUONG");
        String BATCUONG1 = request.getParameter("BATCUONG1");
        String BATCUONG2 = request.getParameter("BATCUONG2");
        String BATCUONG3 = request.getParameter("BATCUONG3");
        String BATCUONG4 = request.getParameter("BATCUONG4");
        String BATCUONG5 = request.getParameter("BATCUONG5");
        String NGUYENNHANCHANDOAN = request.getParameter("NGUYENNHANCHANDOAN");
        String TANGPHU1 = request.getParameter("TANGPHU1");
        String TANGPHU2 = request.getParameter("TANGPHU2");
        String TANGPHU3 = request.getParameter("TANGPHU3");
        String TANGPHU4 = request.getParameter("TANGPHU4");
        String KINHMACH1 = request.getParameter("KINHMACH1");
        String KINHMACH2 = request.getParameter("KINHMACH2");
        String KINHMACH3 = request.getParameter("KINHMACH3");
        String KINHMACH4 = request.getParameter("KINHMACH4");
        String DINHVIBENH1 = request.getParameter("DINHVIBENH1");
        String DINHVIBENH2 = request.getParameter("DINHVIBENH2");
        String DINHVIBENH3 = request.getParameter("DINHVIBENH3");
        String DINHVIBENH4 = request.getParameter("DINHVIBENH4");
        String PHAPDIEUTRI = request.getParameter("PHAPDIEUTRI");
        String PHUONGDUOC = request.getParameter("PHUONGDUOC");
        String PPKHONGDUNGTHUOC = request.getParameter("PPKHONGDUNGTHUOC");
        String PHUONGPHAPKHAC = request.getParameter("PHUONGPHAPKHAC");
        String YHOCHIENDAI = request.getParameter("YHOCHIENDAI");
        String DUHAU = request.getParameter("DUHAU");
        double canNang = (CANNANG == null || CANNANG.trim().equals("")) ? 0 : Float.parseFloat(CANNANG);
        double chieuCao = (CHIEUCAO == null || CHIEUCAO.trim().equals("") || CHIEUCAO.trim().equals("0")) ? 1 : (Float.parseFloat(CHIEUCAO) / 100);
        double bmi = canNang / (chieuCao * chieuCao);
        double bmilamtron = Math.round(bmi * 100.0) / 100.0;
        Map map = new HashMap();
        String ngayLamBenhAn = request.getParameter("NGAYLAMBENHAN");
        String bacSiLamBenhAn = request.getParameter("BACSILAMBENHAN");
        try {
            String sql = "call HIS_MANAGER.UPDATE_BENHAN_YHCT_NOITRU(" +
                    "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)" +
                    "#l,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
            vreturn = jdbcTemplate.update(sql, new Object[]{
                    ID, LYDOVAOVIEN, BENHSU, TIENSUBANTHAN, TIENSU1, TIENSU2, TIENSU3, TIENSU4
                    , MOTABANTHAN, TIENSUBENHTAT, TIENSUGIADINH, KHAMTOANTHAN, MACH, NHIETDO
                    , HUYETAPTREN, HUYETAPDUOI, NHIPTHO, CANNANG, CHIEUCAO, bmilamtron, TUANHOAN, HOHAP
                    , TIEUHOA, TIETNIEUSINHDUC, THANKINH, XUONGKHOP, TMH, RHM, MAT, NOITIET, CLS
                    , TOMTATBA, BENHCHINH, ICDCHINH1, ICDCHINH2, ICDCHINH3, ICDCHINH4, ICDCHINH5
                    , BENHPHU, ICDPHU1, ICDPHU2, ICDPHU3, ICDPHU4, ICDPHU5, ICDPHU6, ICDPHU7, ICDPHU8
                    , ICDPHU9, ICDPHU10, PHANBIET, HINHTHAI1, HINHTHAI2, HINHTHAI3, THAN1, THAN2
                    , SAC1, SAC2, SAC3, TRACH1, TRACH2, HINHLUOI1, HINHLUOI2, CHATLUOI1, CHATLUOI2, CHATLUOI3,
                    REULUOI1, REULUOI2, REULUOI3, MOTAVONGCHAN, TIENGNOI1, TIENGNOI2, TIENGNOI3, TIENGNOI4, TIENGNOI5
                    , HOITHO1, HOITHO2, HOITHO3, HOITHO4, HOITHO5, COHO, HO1, HO2, HO3, COO, CONAC, COMUI, MUICOTHE1, MUICOTHE2, MUICOTHE3
                    , COCHATTHAI, CHATTHAI1, CHATTHAI2, CHATTHAI3, MOTAVANCHAN, COHANNHIET, HANNHIET1, HANNHIET2, HANNHIET3
                    , MOHOI1, MOHOI2, MOHOI3, CODAUMATCO, DAUDAU1, DAUDAU2, DAUDAU3, COHMCM, DOIMAT1, DOIMAT2, DOIMAT3
                    , TAI1, TAI2, TAI3, MUI1, MUI2, MUI3, HONG1, HONG2, HONG3, COVAI1, COVAI2, COVAI3, COLUNG, LUNG, CONGUC
                    , NGUC1, NGUC2, NGUC3, NGUC4, NGUC5, NGUC6, COBUNG, BUNG1, BUNG2, BUNG3, BUNG4, BUNG5, BUNG6, COCHANTAY
                    , CHANTAY1, CHANTAY2, CHANTAY3, CHANTAY4, CHANTAY5, CHANTAY6, COAN, AN1, AN2, AN3, COUONG, UONG1, UONG2, UONG3
                    , CODAITIEUTIEN, TIEUTIEN1, TIEUTIEN2, TIEUTIEN3, DAITIEN1, DAITIEN2, DAITIEN3, CONGU, NGU1, NGU2, NGU3, COSDSS
                    , BENHNAM1, BENHNAM2, BENHNAM3, BENHNU1, BENHNU2, BENHNU3, COKINHNGUYET, RLKINHNGUYET1, RLKINHNGUYET2, THONGKINH1
                    , THONGKINH2, CODOIHA, DOIHA1, DOIHA2, COYEUTOLIENQUAN, MOTAKHACVANCHAN, COXUCCHAN, DA1, DA2, DA3, COCOXUONGKHOP, COXUONGKHOP1
                    , COXUONGKHOP2, COXUONGKHOP3, COBUNGTHIETCHAN, BUNGTHIETCHAN1, BUNGTHIETCHAN2, BUNGTHIETCHAN3, COMOHOITHIETCHAN
                    , MOHOITHIETCHAN1, MOHOITHIETCHAN2, MOHOITHIETCHAN3, MACHCHAN1, MACHCHAN2, MACHCHAN3, TONGKHANPHAI1, TONGKHANPHAI2
                    , TONGKHANPHAI3, TONGKHANTRAI1, TONGKHANTRAI2, TONGKHANTRAI3, TAITRAITHON1, TAITRAITHON2, TAITRAITHON3, TAITRAIQUAN1
                    , TAITRAIQUAN2, TAITRAIQUAN3, TAITRAIXICH1, TAITRAIXICH2, TAITRAIXICH3, TAIPHAITHON1, TAIPHAITHON2, TAIPHAITHON3, TAIPHAIQUAN1
                    , TAIPHAIQUAN2, TAIPHAIQUAN3, TAIPHAIXICH1, TAIPHAIXICH2, TAIPHAIXICH3, MOTATHIETCHAN, TOMTATTUCHAN, BIENCHUNGLUANTRI
                    , CHUANDOANBENHDANH, BENHDANH1, BENHDANH2, BENHDANH3, BENHDANH4, BENHDANH5, BENHDANH6, CHUANDOANBATCUONG, BATCUONG1
                    , BATCUONG2, BATCUONG3, BATCUONG4, BATCUONG5, NGUYENNHANCHANDOAN, TANGPHU1, TANGPHU2, TANGPHU3, TANGPHU4, KINHMACH1
                    , KINHMACH2, KINHMACH3, KINHMACH4, DINHVIBENH1, DINHVIBENH2, DINHVIBENH3, DINHVIBENH4, PHAPDIEUTRI, PHUONGDUOC
                    , PPKHONGDUNGTHUOC, PHUONGPHAPKHAC, YHOCHIENDAI, DUHAU, VAONGAYTHU, ngayLamBenhAn, bacSiLamBenhAn
            });
            map.put("SUCCESS", vreturn);
            map.put("ERRO", "0");
        } catch (Exception e) {
            map.put("ERRO", e.getMessage());

        }
        return map;
    }

    public @ResponseBody
    @RequestMapping(value = "/update-benhan-phukhoa")
    Map updateBenhAnPhuKhoa(HttpSession session, HttpServletRequest request, HttpServletResponse response,
                            @RequestBody benhAnPhuKhoa ba
    ) throws ParseException {
        Map ret = new HashMap();
        try {
            int kq = voBenhAnNoiTruDAO.updateBenhAnPhuKhoa(ba);
            ret.put("SUCCESS", kq);
            ret.put("MESSAGE", ' ');
        } catch (Exception e) {
            ret.put("SUCCESS", 0);
            ret.put("MESSAGE", "" + e.getMessage());
        }
        return ret;
    }

    public @ResponseBody
    @RequestMapping(value = "/update-benhan-nhi", produces = "application/json; charset=utf-8")
    Map updateBenhAnNhiKhoa(@RequestBody BenhAnNhiKhoaObj benhAnNhiKhoaObj, HttpSession session) {
        Map response = new HashMap();
        try {
            String userId = session.getAttribute("Sess_UserID") != null ? session.getAttribute("Sess_UserID").toString() : "";
            int result = voBenhAnNoiTruDAO.updateBenhAnNhiKhoa(benhAnNhiKhoaObj, userId);
            voBenhAnNoiTruDAO.log_update(Integer.parseInt(benhAnNhiKhoaObj.getId()), L2Utils.getDvtt(session), L2Utils.getMaUser(session));
            response.put("SUCCESS", result);
            response.put("MESSAGE", ' ');
        } catch (Exception e) {
            response.put("SUCCESS", 0);
            response.put("MESSAGE", "" + e.getMessage());
        }
        return response;
    }

    public @ResponseBody
    @RequestMapping(value = "/update-benhan-huyethoc", produces = "application/json; charset=utf-8")
    Map updateBenhAnHuyetHocTruyenMau(@RequestBody BenhAnHHTMObj benhAnHHTMObj, HttpSession session) {
        Map response = new HashMap();
        try {
            String userId = session.getAttribute("Sess_UserID") != null ? session.getAttribute("Sess_UserID").toString() : "";
            int result = voBenhAnNoiTruDAO.updateBenhAnHHTM(benhAnHHTMObj, userId);
            voBenhAnNoiTruDAO.log_update(Integer.parseInt(benhAnHHTMObj.getId()), L2Utils.getDvtt(session), L2Utils.getMaUser(session));
            response.put("SUCCESS", result);
            response.put("MESSAGE", ' ');
        } catch (Exception e) {
            response.put("SUCCESS", 0);
            response.put("MESSAGE", "" + e.getMessage());
        }
        return response;
    }

    @RequestMapping(value = "/BenhAn_SANKHOA_Insert", produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map BenhAn_SANKHOA_Insert(HttpServletRequest request) {
        int vreturn = -1;
        String ID = request.getParameter("ID");
        String LYDOVAOVIEN = request.getParameter("LYDOVAOVIEN");
        String VAONGAYTHU = request.getParameter("VAONGAYTHU");
        String KINHCUOI_TUNGAY = request.getParameter("KINHCUOI_TUNGAY");
        String KINHCUOI_DENNGAY = request.getParameter("KINHCUOI_DENNGAY");
        String TUOITHAI = request.getParameter("TUOITHAI");
        String KHAMTHAITAI = request.getParameter("KHAMTHAITAI");
        String BOX_UONVAN = request.getParameter("BOX_UONVAN");
        String DUOCTIEM = request.getParameter("DUOCTIEM");
        String NGAYGIO_CHUYENDA = request.getParameter("NGAYGIO_CHUYENDA");
        String DAUHIEULUCDAU = request.getParameter("DAUHIEULUCDAU");
        String BIENCHUYEN = request.getParameter("BIENCHUYEN");
        String BANTHAN = request.getParameter("BANTHAN");
        String GIADINH = request.getParameter("GIADINH");
        String NAMCOKINH = request.getParameter("NAMCOKINH");
        String TUOICOKINH = request.getParameter("TUOICOKINH");
        String TINHCHATKINH = request.getParameter("TINHCHATKINH");
        String CHUKY = request.getParameter("CHUKY");
        String LUONGKINH = request.getParameter("LUONGKINH");
        String NAMLAYCHONG = request.getParameter("NAMLAYCHONG");
        String TUOILAYCHONG = request.getParameter("TUOILAYCHONG");
        String BENHPHUKHOA = request.getParameter("BENHPHUKHOA");
        String MACH = request.getParameter("MACH");
        String NHIETDO = request.getParameter("NHIETDO");
        String HUYETAPTREN = request.getParameter("HUYETAPTREN");
        String HUYETAPDUOI = request.getParameter("HUYETAPDUOI");
        String NHIPTHO = request.getParameter("NHIPTHO");
        String CANNANG = request.getParameter("CANNANG");
        String CHIEUCAO = request.getParameter("CHIEUCAO");
        String TOANTRANG = request.getParameter("TOANTRANG");
        String BOXDDDIUNG = request.getParameter("BOXDDDIUNG");
        String TUANHOAN = request.getParameter("TUANHOAN");
        String HOHAP = request.getParameter("HOHAP");
        String TIEUHOA = request.getParameter("TIEUHOA");
        String TIETNIEU = request.getParameter("TIETNIEU");
        String CACBOPHANKHAC = request.getParameter("CACBOPHANKHAC");
        String BOX_SEOBUNG = request.getParameter("BOX_SEOBUNG");
        String HINHDANG = request.getParameter("HINHDANG");
        String TUTHE = request.getParameter("TUTHE");
        String CHIEUCAOTUCUNG = request.getParameter("CHIEUCAOTUCUNG");
        String VONGBUNG = request.getParameter("VONGBUNG");
        String CONCO = request.getParameter("CONCO");
        String TIMTHAI = request.getParameter("TIMTHAI");
        String VU = request.getParameter("VU");
        String CHISHOBISHOP = request.getParameter("CHISHOBISHOP");
        String AMHO = request.getParameter("AMHO");
        String AMDAO = request.getParameter("AMDAO");
        String TANGSINHMON = request.getParameter("TANGSINHMON");
        String COTUCUNG = request.getParameter("COTUCUNG");
        String PHAPHU = request.getParameter("PHAPHU");
        String BOXOIPHONG = request.getParameter("BOXOIPHONG");
        String BOXOIDET = request.getParameter("BOXOIDET");
        String BOXOIQUALE = request.getParameter("BOXOIQUALE");
        String NGAYGIO_OIVO = request.getParameter("NGAYGIO_OIVO");
        String BOXTUNHIEN = request.getParameter("BOXTUNHIEN");
        String BOXBAMOI = request.getParameter("BOXBAMOI");
        String MAUSACNUOCOI = request.getParameter("MAUSACNUOCOI");
        String TINHTRANGNUOCOI = request.getParameter("TINHTRANGNUOCOI");
        String NGOI = request.getParameter("NGOI");
        String THE = request.getParameter("THE");
        String KIEUTHE = request.getParameter("KIEUTHE");
        String BOXCAO = request.getParameter("BOXCAO");
        String BOXCHU = request.getParameter("BOXCHU");
        String BOXCHAT = request.getParameter("BOXCHAT");
        String BOXLOT = request.getParameter("BOXLOT");
        String HAVE = request.getParameter("HAVE");
        String XETNGHIEMCANLAM = request.getParameter("XETNGHIEMCANLAM");
        String KHIVAOKHOA = request.getParameter("KHIVAOKHOA");
        String PHANBIET = request.getParameter("PHANBIET");
        String TIENLUONG = request.getParameter("TIENLUONG");
        String HUONGDIEUTRI = request.getParameter("HUONGDIEUTRI");
        String PHUONGPHAP = request.getParameter("PHUONGPHAP");
        String ID_TIENSU_SANKHOA = request.getParameter("ID_TIENSU_SANKHOA");
        String ngayLamBenhAn = request.getParameter("NGAYLAMBENHAN");
        String bacSiLamBenhAn = request.getParameter("BACSILAMBENHAN");
        String PARA = request.getParameter("PARA");
        String NGAY_DU_SINH = request.getParameter("NGAY_DU_SINH");
        Map map = new HashMap();
        try {
            String sql = "call HIS_MANAGER.UPDATE_BENHAN_SANKHOA(" +
                    "?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ", ?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?)" +
                    "#l,s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
            vreturn = jdbcTemplate.update(sql, new Object[]{
                    ID, LYDOVAOVIEN, VAONGAYTHU, KINHCUOI_TUNGAY, KINHCUOI_DENNGAY,
                    TUOITHAI, KHAMTHAITAI, BOX_UONVAN, DUOCTIEM, NGAYGIO_CHUYENDA,
                    DAUHIEULUCDAU, BIENCHUYEN, BANTHAN, GIADINH, NAMCOKINH,
                    TUOICOKINH, TINHCHATKINH, CHUKY, LUONGKINH, NAMLAYCHONG,
                    TUOILAYCHONG, BENHPHUKHOA, MACH, NHIETDO, HUYETAPTREN,
                    HUYETAPDUOI, NHIPTHO, CANNANG, CHIEUCAO, TOANTRANG,
                    BOXDDDIUNG, TUANHOAN, HOHAP, TIEUHOA, TIETNIEU,
                    CACBOPHANKHAC, BOX_SEOBUNG, HINHDANG, TUTHE, CHIEUCAOTUCUNG,
                    VONGBUNG, CONCO, TIMTHAI, VU, CHISHOBISHOP,
                    AMHO, AMDAO, TANGSINHMON, COTUCUNG, PHAPHU,
                    BOXOIPHONG, BOXOIDET, BOXOIQUALE, NGAYGIO_OIVO, BOXTUNHIEN,
                    BOXBAMOI, MAUSACNUOCOI, TINHTRANGNUOCOI, NGOI, THE,
                    KIEUTHE, BOXCAO, BOXCHU, BOXCHAT, BOXLOT,
                    HAVE, XETNGHIEMCANLAM, KHIVAOKHOA, PHANBIET, TIENLUONG,
                    HUONGDIEUTRI, PHUONGPHAP, ID_TIENSU_SANKHOA, ngayLamBenhAn, bacSiLamBenhAn, NGAY_DU_SINH, PARA
            });
            map.put("SUCCESS", vreturn);
            map.put("ERRO", "0");
        } catch (Exception e) {
            map.put("ERRO", e.getMessage());

        }
        return map;
    }

    @RequestMapping(value = "/lis_ttiensusankhoa", method = RequestMethod.GET)
    public @ResponseBody
    List lis_ttiensusankhoa(@RequestParam(value = "id_benan") String id_benan, HttpSession session) {
        try {
            String sql = "select *  \n" +
                    "from HIS_MANAGER.BA_SANKHOA_TIEN_SU_SAN_KHOA \n" +
                    "WHERE DVTT = :dvtt AND ID_BENHAN = :id_benan \n" +
                    "ORDER BY SO_LAN_CO_THAI";
            NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSourceMNG);
            MapSqlParameterSource params = new MapSqlParameterSource()
                    .addValue("id_benan", id_benan)
                    .addValue("dvtt", L2Utils.getDvtt(session));
            List ls = namedParameterJdbcTemplate.queryForList(sql, params);
            return ls;
        } catch (Exception e) {
            return null;
        }
    }

    @RequestMapping(value = "/xoatiensusankhoa", method = RequestMethod.GET)
    public @ResponseBody
    int xoatiensusankhoa(@RequestParam(value = "SO_LAN_CO_THAI") int SO_LAN_CO_THAI, HttpSession session) {
        Thamsohethong tsht = (Thamsohethong) session.getAttribute("Sess_Thamso");

        String sql = "Delete    \n" +
                "from HIS_MANAGER.BA_SANKHOA_TIEN_SU_SAN_KHOA \n" +
                "WHERE DVTT = :dvtt AND SO_LAN_CO_THAI = :SO_LAN_CO_THAI";
        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSourceMNG);
        MapSqlParameterSource params = new MapSqlParameterSource()
                .addValue("SO_LAN_CO_THAI", SO_LAN_CO_THAI)
                .addValue("dvtt", L2Utils.getDvtt(session));
        int ls = namedParameterJdbcTemplate.update(sql, params);
        return ls;
    }

    @RequestMapping(value = "/TienSu_SanKhoa_Update", produces = "application/json; charset=utf-8")
    public @ResponseBody
    void TienSu_SanKhoa_Update(HttpServletRequest request, HttpSession session) {
        int vnreturn = -1;
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        String TUTHE = request.getParameter("TUTHE");
        String ID_BA = request.getParameter("ID_BA");
        String SO_LAN_CO_THAI = request.getParameter("SO_LAN_CO_THAI");
        String NAM = request.getParameter("NAM");
        String DE_DU_THANG = request.getParameter("DE_DU_THANG");
        String SAY = request.getParameter("SAY");
        String DE_THIEU_THANG = request.getParameter("DE_THIEU_THANG");
        String HUT = request.getParameter("HUT");
        String NAO = request.getParameter("NAO");
        String CO_VAC = request.getParameter("CO_VAC");
        String CHUA_NGOAI_TC = request.getParameter("CHUA_NGOAI_TC");
        String CHUA_TRUNG = request.getParameter("CHUA_TRUNG");
        String THAI_CHET_LUU = request.getParameter("THAI_CHET_LUU");
        String CON_HIEN_DONG = request.getParameter("CON_HIEN_DONG");
        String CAN_NANG = request.getParameter("CAN_NANG");
        String PHUONG_PHAP_DE = request.getParameter("PHUONG_PHAP_DE");
        String TAI_BIEN = request.getParameter("TAI_BIEN");
        Map map = new HashMap();
        try {
            String sql = "call HIS_MANAGER.CAPNHAT_TIENSU_SANKHOA(" +
                    "?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?)" +
                    "#l,s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
            vnreturn = jdbcTemplate.update(sql, new Object[]{
                    SO_LAN_CO_THAI, NAM, SAY, DE_THIEU_THANG, HUT,
                    NAO, CO_VAC, CHUA_NGOAI_TC, CHUA_TRUNG, CON_HIEN_DONG,
                    CAN_NANG, PHUONG_PHAP_DE, TAI_BIEN, ID_BA, dvtt,
                    DE_DU_THANG, THAI_CHET_LUU
            });
            map.put("SUCCESS", vnreturn);
            map.put("ERRO", "0");
        } catch (Exception e) {
            map.put("ERRO", e.getMessage());

        }
    }


    @RequestMapping(value = "/TienSu_SanKhoa_Insert", produces = "application/json; charset=utf-8")
    public @ResponseBody
    void TienSu_SanKhoa_Insert(HttpServletRequest request, HttpSession session) {
        int vnreturn = -1;
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        String TUTHE = request.getParameter("TUTHE");
        String ID_BA = request.getParameter("ID_BA");
        String SO_LAN_CO_THAI = request.getParameter("SO_LAN_CO_THAI");
        String NAM = request.getParameter("NAM");
        String DE_DU_THANG = request.getParameter("DE_DU_THANG");
        String SAY = request.getParameter("SAY");
        String DE_THIEU_THANG = request.getParameter("DE_THIEU_THANG");
        String HUT = request.getParameter("HUT");
        String NAO = request.getParameter("NAO");
        String CO_VAC = request.getParameter("CO_VAC");
        String CHUA_NGOAI_TC = request.getParameter("CHUA_NGOAI_TC");
        String CHUA_TRUNG = request.getParameter("CHUA_TRUNG");
        String THAI_CHET_LUU = request.getParameter("THAI_CHET_LUU");
        String CON_HIEN_DONG = request.getParameter("CON_HIEN_DONG");
        String CAN_NANG = request.getParameter("CAN_NANG");
        String PHUONG_PHAP_DE = request.getParameter("PHUONG_PHAP_DE");
        String TAI_BIEN = request.getParameter("TAI_BIEN");
        Map map = new HashMap();
        try {
            String sql = "call HIS_MANAGER.ADD_TIENSU_SANKHOA(" +
                    "?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?)" +
                    "#l,s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
            vnreturn = jdbcTemplate.update(sql, new Object[]{
                    SO_LAN_CO_THAI, NAM, SAY, DE_THIEU_THANG, HUT,
                    NAO, CO_VAC, CHUA_NGOAI_TC, CHUA_TRUNG, CON_HIEN_DONG,
                    CAN_NANG, PHUONG_PHAP_DE, TAI_BIEN, ID_BA, dvtt,
                    DE_DU_THANG, THAI_CHET_LUU
            });
            map.put("SUCCESS", vnreturn);
            map.put("ERRO", "0");
        } catch (Exception e) {
            map.put("ERRO", e.getMessage());

        }
    }

    @RequestMapping(value = "/BenhAn_TruyenNhiem_Insert", produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map BenhAn_TruyenNhiem_Insert(HttpServletRequest request) {
        int vreturn = -1;
        String ID = request.getParameter("ID");
        String LYDOVAOVIEN = request.getParameter("LYDOVAOVIEN");
        String VAONGAYTHU = request.getParameter("VAONGAYTHU");
        String BENHSU = request.getParameter("BENHSU");
        String TIENSUBANTHAN = request.getParameter("TIENSUBANTHAN");
        String BOXDDDIUNG = request.getParameter("BOXDDDIUNG");
        String DDDIUNG = request.getParameter("DDDIUNG");
        String BOXDDTHUOCLA = request.getParameter("BOXDDTHUOCLA");
        String DDTHUOCLA = request.getParameter("DDTHUOCLA");
        String BOXDDMATUY = request.getParameter("BOXDDMATUY");
        String DDMATUY = request.getParameter("DDMATUY");
        String BOXDDTHUOCLAO = request.getParameter("BOXDDTHUOCLAO");
        String DDTHUOCLAO = request.getParameter("DDTHUOCLAO");
        String BOXDDRUOUBIA = request.getParameter("BOXDDRUOUBIA");
        String DDRUOUBIA = request.getParameter("DDRUOUBIA");
        String BOXDDKHAC = request.getParameter("BOXDDKHAC");
        String DDKHAC = request.getParameter("DDKHAC");
        String TIENSUGIADINH = request.getParameter("TIENSUGIADINH");
        String KHAMTOANTHAN = request.getParameter("KHAMTOANTHAN");
        String MACH = request.getParameter("MACH");
        String NHIETDO = request.getParameter("NHIETDO");
        String HUYETAPTREN = request.getParameter("HUYETAPTREN");
        String HUYETAPDUOI = request.getParameter("HUYETAPDUOI");
        String NHIPTHO = request.getParameter("NHIPTHO");
        String CANNANG = request.getParameter("CANNANG");
        String CHIEUCAO = request.getParameter("CHIEUCAO");
        String BMI = request.getParameter("BMI");
        String TUANHOAN = request.getParameter("TUANHOAN");
        String HOHAP = request.getParameter("HOHAP");
        String TIEUHOA = request.getParameter("TIEUHOA");
        String TIETNIEUSINHDUC = request.getParameter("TIETNIEUSINHDUC");
        String THANKINH = request.getParameter("THANKINH");
        String XUONGKHOP = request.getParameter("XUONGKHOP");
        String TMH = request.getParameter("TMH");
        String RHM = request.getParameter("RHM");
        String MAT = request.getParameter("MAT");
        String NOITIET = request.getParameter("NOITIET");
        String CLS = request.getParameter("CLS");
        String TOMTATBA = request.getParameter("TOMTATBA");
        String BENHCHINH = request.getParameter("BENHCHINH");
        String BENHPHU = request.getParameter("BENHPHU");
        String PHANBIET = request.getParameter("PHANBIET");
        String TIENLUONG = request.getParameter("TIENLUONG");
        String HUONGDIEUTRI = request.getParameter("HUONGDIEUTRI");
        String DICHTE = request.getParameter("DICHTE");
        String BENHCAPTINH = request.getParameter("BENHCAPTINH");
        String NOISONG = request.getParameter("NOISONG");
        String THOIGIANSONG = request.getParameter("THOIGIANSONG");
        String MOISINH = request.getParameter("MOISINH");

        double canNang = (CANNANG == null || CANNANG.trim().equals("")) ? 0 : Float.parseFloat(CANNANG);
        double chieuCao = (CHIEUCAO == null || CHIEUCAO.trim().equals("") || CHIEUCAO.trim().equals("0")) ? 1 : (Float.parseFloat(CHIEUCAO) / 100);
        double bmi = canNang / (chieuCao * chieuCao);
        double bmilamtron = Math.round(bmi * 100.0) / 100.0;
        Map map = new HashMap();
        try {
            String sql = "call HIS_MANAGER.UPDATE_BANHIEM(" +
                    "?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?)" +
                    "#l,s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
            vreturn = jdbcTemplate.update(sql, new Object[]{
                    ID, LYDOVAOVIEN, BENHSU, TIENSUBANTHAN, BOXDDDIUNG
                    , DDDIUNG, BOXDDTHUOCLA, DDTHUOCLA, BOXDDMATUY, DDMATUY
                    , BOXDDTHUOCLAO, DDTHUOCLAO, BOXDDRUOUBIA, DDRUOUBIA, BOXDDKHAC
                    , DDKHAC, TIENSUGIADINH, KHAMTOANTHAN, MACH, NHIETDO
                    , HUYETAPTREN, HUYETAPDUOI, NHIPTHO, CANNANG, CHIEUCAO
                    , bmilamtron, TUANHOAN, HOHAP, TIEUHOA, TIETNIEUSINHDUC
                    , THANKINH, XUONGKHOP, TMH, RHM, MAT
                    , NOITIET, CLS, TOMTATBA, BENHCHINH, BENHPHU
                    , PHANBIET, TIENLUONG, HUONGDIEUTRI, DICHTE, BENHCAPTINH
                    , NOISONG, THOIGIANSONG, MOISINH, VAONGAYTHU
            });
            map.put("SUCCESS", vreturn);
            map.put("MESSAGE", " ");
        } catch (Exception e) {
            map.put("SUCCESS", 0);
            map.put("MESSAGE", e.getMessage());

        }
        return map;
    }

    @RequestMapping(value = "/BenhAn_PHCN_Insert", produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map BenhAn_PHCN_Insert(HttpServletRequest request) {
        int vreturn = -1;
        String ID = request.getParameter("ID");
        String LYDOVAOVIEN = request.getParameter("LYDOVAOVIEN");
        String BENHSU = request.getParameter("BENHSU");
        String DIUNG = request.getParameter("DIUNG");
        String BANTHAN = request.getParameter("BANTHAN");
        String GIADINH = request.getParameter("GIADINH");
        String KHAMTOANTHAN = request.getParameter("KHAMTOANTHAN");
        String MACH = request.getParameter("MACH");
        String NHIETDO = request.getParameter("NHIETDO");
        String HUYETAPTREN = request.getParameter("HUYETAPTREN");
        String HUYETAPDUOI = request.getParameter("HUYETAPDUOI");
        String NHIPTHO = request.getParameter("NHIPTHO");
        String CANNANG = request.getParameter("CANNANG");
        String CHIEUCAO = request.getParameter("CHIEUCAO");
        String BMI = request.getParameter("BMI");
        String TINHTRANGDAU = request.getParameter("TINHTRANGDAU");
        String THANKINH = request.getParameter("THANKINH");
        String COXUONGKHOP = request.getParameter("COXUONGKHOP");
        String CHUYENKHOAKHAC = request.getParameter("CHUYENKHOAKHAC");
        String CLS = request.getParameter("CLS");
        String TOMTATBA = request.getParameter("TOMTATBA");
        String BENHCHINH = request.getParameter("BENHCHINH");
        String BENHPHU = request.getParameter("BENHPHU");
        String PHANBIET = request.getParameter("PHANBIET");
        String KHOKHAN = request.getParameter("KHOKHAN");
        String MUCTIEU = request.getParameter("MUCTIEU");
        String CHUONGTRINH = request.getParameter("CHUONGTRINH");
        String DIEUTRI = request.getParameter("DIEUTRI");
        double canNang = (CANNANG == null || CANNANG.trim().equals("")) ? 0 : Float.parseFloat(CANNANG);
        double chieuCao = (CHIEUCAO == null || CHIEUCAO.trim().equals("") || CHIEUCAO.trim().equals("0")) ? 1 : (Float.parseFloat(CHIEUCAO) / 100);
        double bmi = canNang / (chieuCao * chieuCao);
        double bmilamtron = Math.round(bmi * 100.0) / 100.0;
        Map map = new HashMap();
        try {
            String sql = "call HIS_MANAGER.UPDATE_BAPHCN(" +
                    "?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?)" +
                    "#l,s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
            vreturn = jdbcTemplate.update(sql, new Object[]{
                    ID, LYDOVAOVIEN, BENHSU, DIUNG, BANTHAN
                    , GIADINH, KHAMTOANTHAN, MACH, NHIETDO, HUYETAPTREN
                    , HUYETAPDUOI, NHIPTHO, CANNANG, CHIEUCAO, bmilamtron
                    , TINHTRANGDAU, THANKINH, COXUONGKHOP, CHUYENKHOAKHAC, CLS
                    , TOMTATBA, BENHCHINH, BENHPHU, PHANBIET, KHOKHAN
                    , MUCTIEU, CHUONGTRINH, DIEUTRI
            });
            map.put("SUCCESS", vreturn);
            map.put("ERRO", "0");
        } catch (Exception e) {
            map.put("ERRO", e.getMessage());

        }
        return map;
    }

    @RequestMapping(value = "/BenhAn_TayChanMieng_Insert", produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map BenhAn_TayChanMieng_Insert(HttpServletRequest request) {
        int vreturn = -1;
        String ID = request.getParameter("ID");
        String LYDOVAOVIEN = request.getParameter("LYDOVAOVIEN");
        String VAONGAYTHU = request.getParameter("VAONGAYTHU");
        String COSOT = request.getParameter("COSOT");
        String COPHATBAN = request.getParameter("COPHATBAN");
        String COBOANLOETMIENG = request.getParameter("COBOANLOETMIENG");
        String COGIATMINH = request.getParameter("COGIATMINH");
        String SOLAN = request.getParameter("SOLAN");
        String CONONOI = request.getParameter("CONONOI");
        String COCOGIAT = request.getParameter("COCOGIAT");
        String CORUNCHI = request.getParameter("CORUNCHI");
        String DAUHIEUKHAC = request.getParameter("DAUHIEUKHAC");
        String DICHTE = request.getParameter("DICHTE");
        String CODIHOC = request.getParameter("CODIHOC");
        String DIACHITRUONG = request.getParameter("DIACHITRUONG");
        String COCHUNGNHA = request.getParameter("COCHUNGNHA");
        String COGANNHA = request.getParameter("COGANNHA");
        String COCHUNGTRUONG = request.getParameter("COCHUNGTRUONG");
        String DIEUTRITUYENTRUOC = request.getParameter("DIEUTRITUYENTRUOC");
        String DIEUTRIKHAC = request.getParameter("DIEUTRIKHAC");
        String BANTHAN = request.getParameter("BANTHAN");
        String GIADINH = request.getParameter("GIADINH");
        String CONTHUMAY = request.getParameter("CONTHUMAY");
        String COSINH = request.getParameter("COSINH");
        String COSOM = request.getParameter("COSOM");
        String COSAY = request.getParameter("COSAY");
        String COSONG = request.getParameter("COSONG");
        String CODETHUONG = request.getParameter("CODETHUONG");
        String COFORCEPS = request.getParameter("COFORCEPS");
        String COGIACHUT = request.getParameter("COGIACHUT");
        String CODEPHAUTHUAT = request.getParameter("CODEPHAUTHUAT");
        String CODECHIHUY = request.getParameter("CODECHIHUY");
        String CODEKHAC = request.getParameter("CODEKHAC");
        String CANNANGLUCSINH = request.getParameter("CANNANGLUCSINH");
        String CODITATBAMSINH = request.getParameter("CODITATBAMSINH");
        String CUTHETATBAMSINH = request.getParameter("CUTHETATBAMSINH");
        String PHATTRIENVETINHTHAN = request.getParameter("PHATTRIENVETINHTHAN");
        String PHATTRIENVEVANDONG = request.getParameter("PHATTRIENVEVANDONG");
        String CACBENHLYKHAC = request.getParameter("CACBENHLYKHAC");
        String COSUAME = request.getParameter("COSUAME");
        String CONUOINHANTAO = request.getParameter("CONUOINHANTAO");
        String COHONHOP = request.getParameter("COHONHOP");
        String CAISUATHANGTHU = request.getParameter("CAISUATHANGTHU");
        String COCSTAIVUONTRE = request.getParameter("COCSTAIVUONTRE");
        String COCSTAINHA = request.getParameter("COCSTAINHA");
        String CODATIEMLAO = request.getParameter("CODATIEMLAO");
        String CODATIEMBAILIET = request.getParameter("CODATIEMBAILIET");
        String CODATIEMSOI = request.getParameter("CODATIEMSOI");
        String CODATIEMHOGA = request.getParameter("CODATIEMHOGA");
        String CODATIEMUONVAN = request.getParameter("CODATIEMUONVAN");
        String CODATIEMBACHHAU = request.getParameter("CODATIEMBACHHAU");
        String CODATIEMKHAC = request.getParameter("CODATIEMKHAC");
        String MACH = request.getParameter("MACH");
        String NHIETDO = request.getParameter("NHIETDO");
        String HUYETAPTREN = request.getParameter("HUYETAPTREN");
        String HUYETAPDUOI = request.getParameter("HUYETAPDUOI");
        String NHIPTHO = request.getParameter("NHIPTHO");
        String CANNANG = request.getParameter("CANNANG");
        String CHIEUCAO = request.getParameter("CHIEUCAO");
        String BMI = request.getParameter("BMI");
        String CHIEUCAOTOANTHAN = request.getParameter("CHIEUCAOTOANTHAN");
        String VONGNGUCTOANTHAN = request.getParameter("VONGNGUCTOANTHAN");
        String VONGDAUTOANTHAN = request.getParameter("VONGDAUTOANTHAN");
        String COTOANTHANTIM = request.getParameter("COTOANTHANTIM");
        String SPO2 = request.getParameter("SPO2");
        String COTINH = request.getParameter("COTINH");
        String COLIBI = request.getParameter("COLIBI");
        String COHONME = request.getParameter("COHONME");
        String COLOETMIENG = request.getParameter("COLOETMIENG");
        String COTOANTHANPHATBAN = request.getParameter("COTOANTHANPHATBAN");
        String TOANTHANPHATBAN = request.getParameter("TOANTHANPHATBAN");
        String COTIENGTIMRO = request.getParameter("COTIENGTIMRO");
        String COTIENGTIMMO = request.getParameter("COTIENGTIMMO");
        String COTIENGTIMGALLOP = request.getParameter("COTIENGTIMGALLOP");
        String COTIENGTIMAMTHOI = request.getParameter("COTIENGTIMAMTHOI");
        String AMTHOI = request.getParameter("AMTHOI");
        String COTINHMACHCONOI = request.getParameter("COTINHMACHCONOI");
        String THOIGIANDODAYMAOMACH = request.getParameter("THOIGIANDODAYMAOMACH");
        String COVAMOHOI = request.getParameter("COVAMOHOI");
        String CODANOIBONG = request.getParameter("CODANOIBONG");
        String TUANHOANDAUHIEUKHAC = request.getParameter("TUANHOANDAUHIEUKHAC");
        String COCONNGUNGTHO = request.getParameter("COCONNGUNGTHO");
        String COTHOBUNG = request.getParameter("COTHOBUNG");
        String COTHOONG = request.getParameter("COTHOONG");
        String HINHANHVV = request.getParameter("HINHANHVV");
        String COKHOKHE = request.getParameter("COKHOKHE");
        String COTHORITTHANHQUAN = request.getParameter("COTHORITTHANHQUAN");
        String CORUTLOMNGUC = request.getParameter("CORUTLOMNGUC");
        String CORANPHOI = request.getParameter("CORANPHOI");
        String RANPHOI = request.getParameter("RANPHOI");
        String HOHAPDAUHIEUKHAC = request.getParameter("HOHAPDAUHIEUKHAC");
        String COGANTO = request.getParameter("COGANTO");
        String GANTO = request.getParameter("GANTO");
        String TIEUHOADACDIEM = request.getParameter("TIEUHOADACDIEM");
        String TIEUHOADAUHIEUKHAC = request.getParameter("TIEUHOADAUHIEUKHAC");
        String THANTIETNIEUSINHDUC = request.getParameter("THANTIETNIEUSINHDUC");
        String DONGTU = request.getParameter("DONGTU");
        String PXAS = request.getParameter("PXAS");
        String COCOGUONG = request.getParameter("COCOGUONG");
        String COGIATMINHLUCKHAM = request.getParameter("COGIATMINHLUCKHAM");
        String COTHATDIEU = request.getParameter("COTHATDIEU");
        String CORUNGIATNHANCAU = request.getParameter("CORUNGIATNHANCAU");
        String COLE = request.getParameter("COLE");
        String COYEUCHILIETMEMCAP = request.getParameter("COYEUCHILIETMEMCAP");
        String COLIETTKSO = request.getParameter("COLIETTKSO");
        String CONGUGA = request.getParameter("CONGUGA");
        String THANKINHDAUHIEUKHAC = request.getParameter("THANKINHDAUHIEUKHAC");
        String COXUONGKHOP = request.getParameter("COXUONGKHOP");
        String CACCOQUANKHAC = request.getParameter("CACCOQUANKHAC");
        String CLS = request.getParameter("CLS");
        String NGAYBENH = request.getParameter("NGAYBENH");
        String COSUYHOHAP = request.getParameter("COSUYHOHAP");
        String COSOC = request.getParameter("COSOC");
        String COPHUPHOICAP = request.getParameter("COPHUPHOICAP");
        String COROILOANHOHAP = request.getParameter("COROILOANHOHAP");
        String COMACHNHANH = request.getParameter("COMACHNHANH");
        String COTANGHUYETAP = request.getParameter("COTANGHUYETAP");
        String COGONGCHIHONME = request.getParameter("COGONGCHIHONME");
        String COVAMOHOITOANTHAN = request.getParameter("COVAMOHOITOANTHAN");
        String COTTBATHATDIEU = request.getParameter("COTTBATHATDIEU");
        String CORUNGGIATNHANCAU = request.getParameter("CORUNGGIATNHANCAU");
        String COYEUCHILIETMEM = request.getParameter("COYEUCHILIETMEM");
        String COLIETTHANKINHSO = request.getParameter("COLIETTHANKINHSO");
        String COTTBAGIATMINHLUCKHAM = request.getParameter("COTTBAGIATMINHLUCKHAM");
        String COBENHSUGIATMINH = request.getParameter("COBENHSUGIATMINH");
        String TTBABIEUHIENKHAC = request.getParameter("TTBABIEUHIENKHAC");
        String BENHCHINH = request.getParameter("BENHCHINH");
        String MABENHCHINH = request.getParameter("MABENHCHINH");
        String BENHPHU = request.getParameter("BENHPHU");
        String MABENHPHU = request.getParameter("MABENHPHU");
        String PHANBIET = request.getParameter("PHANBIET");
        String TIENLUONG = request.getParameter("TIENLUONG");
        String COOXY = request.getParameter("COOXY");
        String COCHONGSOC = request.getParameter("COCHONGSOC");
        String CODIEUTRICAOHA = request.getParameter("CODIEUTRICAOHA");
        String COANTHAN = request.getParameter("COANTHAN");
        String COYGLOBULIN = request.getParameter("COYGLOBULIN");
        String CONHAPICU = request.getParameter("CONHAPICU");
        String HUONGDIEUTRI = request.getParameter("HUONGDIEUTRI");
        String BENHKHACTIEMCHUNG = request.getParameter("BENHKHACTIEMCHUNG");
        double canNang = (CANNANG == null || CANNANG.trim().equals("")) ? 0 : Float.parseFloat(CANNANG);
        double chieuCao = (CHIEUCAO == null || CHIEUCAO.trim().equals("") || CHIEUCAO.trim().equals("0")) ? 1 : (Float.parseFloat(CHIEUCAO) / 100);
        double bmi = canNang / (chieuCao * chieuCao);
        double bmilamtron = Math.round(bmi * 100.0) / 100.0;
        Map map = new HashMap();
        try {
            String sql = "call HIS_MANAGER.UPDATE_BATAYCHANMIENG(" +
                    "?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?)" +
                    "#l,s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
            vreturn = jdbcTemplate.update(sql, new Object[]{
                    ID, LYDOVAOVIEN, COSOT, COPHATBAN, COBOANLOETMIENG
                    , COGIATMINH, SOLAN, CONONOI, COCOGIAT, CORUNCHI
                    , DAUHIEUKHAC, DICHTE, CODIHOC, DIACHITRUONG, COCHUNGNHA
                    , COGANNHA, COCHUNGTRUONG, DIEUTRITUYENTRUOC, DIEUTRIKHAC, BANTHAN
                    , GIADINH, CONTHUMAY, COSINH, COSOM, COSAY
                    , COSONG, CODETHUONG, COFORCEPS, COGIACHUT, CODEPHAUTHUAT
                    , CODECHIHUY, CODEKHAC, CANNANGLUCSINH, CODITATBAMSINH, CUTHETATBAMSINH
                    , PHATTRIENVETINHTHAN, PHATTRIENVEVANDONG, CACBENHLYKHAC, COSUAME, CONUOINHANTAO
                    , COHONHOP, CAISUATHANGTHU, COCSTAIVUONTRE, COCSTAINHA, CODATIEMLAO
                    , CODATIEMBAILIET, CODATIEMSOI, CODATIEMHOGA, CODATIEMUONVAN, CODATIEMBACHHAU
                    , CODATIEMKHAC, MACH, NHIETDO, HUYETAPTREN, HUYETAPDUOI
                    , NHIPTHO, CANNANG, CHIEUCAO, bmilamtron, CHIEUCAOTOANTHAN
                    , VONGNGUCTOANTHAN, VONGDAUTOANTHAN, COTOANTHANTIM, SPO2, COTINH
                    , COLIBI, COHONME, COLOETMIENG, COTOANTHANPHATBAN, TOANTHANPHATBAN
                    , COTIENGTIMRO, COTIENGTIMMO, COTIENGTIMGALLOP, COTIENGTIMAMTHOI, AMTHOI
                    , COTINHMACHCONOI, THOIGIANDODAYMAOMACH, COVAMOHOI, CODANOIBONG, TUANHOANDAUHIEUKHAC
                    , COCONNGUNGTHO, COTHOBUNG, COTHOONG, HINHANHVV, COKHOKHE
                    , COTHORITTHANHQUAN, CORUTLOMNGUC, CORANPHOI, RANPHOI, HOHAPDAUHIEUKHAC
                    , COGANTO, GANTO, TIEUHOADACDIEM, TIEUHOADAUHIEUKHAC, THANTIETNIEUSINHDUC
                    , DONGTU, PXAS, COCOGUONG, COGIATMINHLUCKHAM, COTHATDIEU
                    , CORUNGIATNHANCAU, COLE, COYEUCHILIETMEMCAP, COLIETTKSO, CONGUGA
                    , THANKINHDAUHIEUKHAC, COXUONGKHOP, CACCOQUANKHAC, CLS, NGAYBENH
                    , COSUYHOHAP, COSOC, COPHUPHOICAP, COROILOANHOHAP, COMACHNHANH
                    , COTANGHUYETAP, COGONGCHIHONME, COVAMOHOITOANTHAN, COTTBATHATDIEU, CORUNGGIATNHANCAU
                    , COYEUCHILIETMEM, COLIETTHANKINHSO, COTTBAGIATMINHLUCKHAM, COBENHSUGIATMINH, TTBABIEUHIENKHAC
                    , BENHCHINH, MABENHCHINH, BENHPHU, MABENHPHU, PHANBIET
                    , TIENLUONG, COOXY, COCHONGSOC, CODIEUTRICAOHA, COANTHAN
                    , COYGLOBULIN, CONHAPICU, HUONGDIEUTRI, BENHKHACTIEMCHUNG, VAONGAYTHU
            });
            map.put("SUCCESS", vreturn);
            map.put("ERRO", "0");
        } catch (Exception e) {
            map.put("ERRO", e.getMessage());

        }
        return map;
    }

    @RequestMapping(value = "/BenhAn_Bong_Insert", produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map BenhAn_Bong_Insert(HttpServletRequest request, HttpSession session) {
        int vreturn = -1;
        String ID = request.getParameter("ID");
        String LYDOVAOVIEN = request.getParameter("LYDOVAOVIEN");
        String VAONGAYTHU = request.getParameter("VAONGAYTHU");
        String BENHSU = request.getParameter("BENHSU");
        String TIENSUBANTHAN = request.getParameter("TIENSUBANTHAN");
        String BOXDDDIUNG = request.getParameter("BOXDDDIUNG");
        String DDDIUNG = request.getParameter("DDDIUNG");
        String BOXDDTHUOCLA = request.getParameter("BOXDDTHUOCLA");
        String DDTHUOCLA = request.getParameter("DDTHUOCLA");
        String BOXDDMATUY = request.getParameter("BOXDDMATUY");
        String DDMATUY = request.getParameter("DDMATUY");
        String BOXDDTHUOCLAO = request.getParameter("BOXDDTHUOCLAO");
        String DDTHUOCLAO = request.getParameter("DDTHUOCLAO");
        String BOXDDRUOUBIA = request.getParameter("BOXDDRUOUBIA");
        String DDRUOUBIA = request.getParameter("DDRUOUBIA");
        String BOXDDKHAC = request.getParameter("BOXDDKHAC");
        String DDKHAC = request.getParameter("DDKHAC");
        String TIENSUGIADINH = request.getParameter("TIENSUGIADINH");
        String KHAMTOANTHAN = request.getParameter("KHAMTOANTHAN");
        String MACH = request.getParameter("MACH");
        String NHIETDO = request.getParameter("NHIETDO");
        String HUYETAPTREN = request.getParameter("HUYETAPTREN");
        String HUYETAPDUOI = request.getParameter("HUYETAPDUOI");
        String NHIPTHO = request.getParameter("NHIPTHO");
        String CANNANG = request.getParameter("CANNANG");
        String CHIEUCAO = request.getParameter("CHIEUCAO");
        String BMI = request.getParameter("BMI");
        String TUANHOAN = request.getParameter("TUANHOAN");
        String HOHAP = request.getParameter("HOHAP");
        String TIEUHOA = request.getParameter("TIEUHOA");
        String TIETNIEUSINHDUC = request.getParameter("TIETNIEUSINHDUC");
        String THANKINH = request.getParameter("THANKINH");
        String XUONGKHOP = request.getParameter("XUONGKHOP");
        String TMH = request.getParameter("TMH");
        String RHM = request.getParameter("RHM");
        String MAT = request.getParameter("MAT");
        String NOITIET = request.getParameter("NOITIET");
        String CLS = request.getParameter("CLS");
        String TOMTATBA = request.getParameter("TOMTATBA");
        String BENHCHINH = request.getParameter("BENHCHINH");
        String BENHPHU = request.getParameter("BENHPHU");
        String PHANBIET = request.getParameter("PHANBIET");
        String TIENLUONG = request.getParameter("TIENLUONG");
        String HUONGDIEUTRI = request.getParameter("HUONGDIEUTRI");
        String HINHANHVV = request.getParameter("HINHANHVV");
        double canNang = (CANNANG == null || CANNANG.trim().equals("")) ? 0 : Float.parseFloat(CANNANG);
        double chieuCao = (CHIEUCAO == null || CHIEUCAO.trim().equals("") || CHIEUCAO.trim().equals("0")) ? 1 : (Float.parseFloat(CHIEUCAO) / 100);
        double bmi = canNang / (chieuCao * chieuCao);
        double bmilamtron = Math.round(bmi * 100.0) / 100.0;
        Map map = new HashMap();
        try {
            String sql = "call HIS_MANAGER.UPDATE_BABONG(" +
                    "?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?)" +
                    "#l,s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
            vreturn = jdbcTemplate.update(sql, new Object[]{
                    ID, LYDOVAOVIEN, BENHSU, TIENSUBANTHAN, BOXDDDIUNG
                    , DDDIUNG, BOXDDTHUOCLA, DDTHUOCLA, BOXDDMATUY, DDMATUY
                    , BOXDDTHUOCLAO, DDTHUOCLAO, BOXDDRUOUBIA, DDRUOUBIA, BOXDDKHAC
                    , DDKHAC, TIENSUGIADINH, KHAMTOANTHAN, MACH, NHIETDO
                    , HUYETAPTREN, HUYETAPDUOI, NHIPTHO, CANNANG, CHIEUCAO
                    , bmilamtron, TUANHOAN, HOHAP, TIEUHOA, TIETNIEUSINHDUC
                    , THANKINH, XUONGKHOP, TMH, RHM, MAT
                    , NOITIET, CLS, TOMTATBA, BENHCHINH, BENHPHU
                    , PHANBIET, TIENLUONG, HUONGDIEUTRI, HINHANHVV, VAONGAYTHU
            });
            voBenhAnNoiTruDAO.log_update(Integer.parseInt(ID), L2Utils.getDvtt(session), L2Utils.getMaUser(session));
            map.put("SUCCESS", vreturn);
            map.put("ERRO", "0");
        } catch (Exception e) {
            map.put("ERRO", e.getMessage());

        }
        return map;
    }

    public @ResponseBody
    @RequestMapping(value = "/BenhAn_TaiMuiHong_Insert", produces = "application/json; charset=utf-8")
    Map BenhAn_TaiMuiHong_Insert(@RequestBody BenhAnNoiTruTaiMuiHongObj benhAnNoiTruTaiMuiHongObj, HttpSession session) {
        Map response = new HashMap();
        try {
            int result = voBenhAnNoiTruDAO.updateBenhAnNoiTruTaiMuiHong(benhAnNoiTruTaiMuiHongObj);
            voBenhAnNoiTruDAO.log_update(Integer.parseInt(benhAnNoiTruTaiMuiHongObj.getId()), L2Utils.getDvtt(session), L2Utils.getMaUser(session));
            response.put("SUCCESS", result);
            response.put("MESSAGE", ' ');
        } catch (Exception e) {
            response.put("SUCCESS", 0);
            response.put("MESSAGE", "" + e.getMessage());
        }
        return response;
    }

    public @ResponseBody
    @RequestMapping(value = "/BenhAn_NgoaiTruTMH_Insert", produces = "application/json; charset=utf-8")
    Map BenhAn_NgoaiTruTMH_Insert(@RequestBody BenhAnNgoaiTruTaiMuiHongObj benhAnNgoaiTruTaiMuiHongObj, HttpSession session) {
        Map response = new HashMap();
        try {
            int result = voBenhAnNoiTruDAO.updateBenhAnNgoaiTruTaiMuiHong(benhAnNgoaiTruTaiMuiHongObj);
            voBenhAnNoiTruDAO.log_update(Integer.parseInt(benhAnNgoaiTruTaiMuiHongObj.getId()), L2Utils.getDvtt(session), L2Utils.getMaUser(session));
            response.put("SUCCESS", result);
            response.put("MESSAGE", ' ');
        } catch (Exception e) {
            response.put("SUCCESS", 0);
            response.put("MESSAGE", "" + e.getMessage());
        }
        return response;
    }

    public @ResponseBody
    @RequestMapping(value = "/BenhAn_SoSinh_Insert", produces = "application/json; charset=utf-8")
    Map BenhAn_SoSinh_Insert(@RequestBody BenhAnSoSinhObj benhAnSoSinhObj, HttpSession session) {
        Map response = new HashMap();
        try {
            int result = voBenhAnNoiTruDAO.updateBenhAnSoSinh(benhAnSoSinhObj);
            voBenhAnNoiTruDAO.log_update(Integer.parseInt(benhAnSoSinhObj.getId()), L2Utils.getDvtt(session), L2Utils.getMaUser(session));
            response.put("SUCCESS", result);
            response.put("MESSAGE", ' ');
        } catch (Exception e) {
            response.put("SUCCESS", 0);
            response.put("MESSAGE", "" + e.getMessage());
        }
        return response;
    }

    public @ResponseBody
    @RequestMapping(value = "/BenhAn_DaLieu_Insert", produces = "application/json; charset=utf-8")
    Map BenhAn_DaLieu_Insert(@RequestBody BenhAnDaLieuObj benhAnDaLieuObj, HttpSession session) {
        Map response = new HashMap();
        try {
            int result = voBenhAnNoiTruDAO.updateBenhAnDaLieu(benhAnDaLieuObj);
            voBenhAnNoiTruDAO.log_update(Integer.parseInt(benhAnDaLieuObj.getId()), L2Utils.getDvtt(session), L2Utils.getMaUser(session));
            response.put("SUCCESS", result);
            response.put("MESSAGE", ' ');
        } catch (Exception e) {
            response.put("SUCCESS", 0);
            response.put("MESSAGE", "" + e.getMessage());
        }
        return response;
    }

    public @ResponseBody
    @RequestMapping(value = "/BenhAn_TamThan_Insert", produces = "application/json; charset=utf-8")
    Map BenhAn_TamThan_Insert(@RequestBody BenhAnTamThanObj benhAnTamThanObj, HttpSession session) {
        Map response = new HashMap();
        try {
            int result = voBenhAnNoiTruDAO.updateBenhAnTamThan(benhAnTamThanObj);
            voBenhAnNoiTruDAO.log_update(Integer.parseInt(benhAnTamThanObj.getId()), L2Utils.getDvtt(session), L2Utils.getMaUser(session));
            response.put("SUCCESS", result);
            response.put("MESSAGE", ' ');
        } catch (Exception e) {
            response.put("SUCCESS", 0);
            response.put("MESSAGE", "" + e.getMessage());
        }
        return response;
    }

    public @ResponseBody
    @RequestMapping(value = "/BenhAn_UngBuou_Insert", produces = "application/json; charset=utf-8")
    Map BenhAn_UngBuou_Insert(@RequestBody BenhAnUngBuouObj benhAnUngBuouObj, HttpSession session) {
        Map response = new HashMap();
        try {
            int result = voBenhAnNoiTruDAO.updateBenhAnUngBuou(benhAnUngBuouObj);
            voBenhAnNoiTruDAO.log_update(Integer.parseInt(benhAnUngBuouObj.getId()), L2Utils.getDvtt(session), L2Utils.getMaUser(session));
            response.put("SUCCESS", result);
            response.put("MESSAGE", ' ');
        } catch (Exception e) {
            response.put("SUCCESS", 0);
            response.put("MESSAGE", "" + e.getMessage());
        }
        return response;
    }

    public @ResponseBody
    @RequestMapping(value = "/BenhAn_UngBuou_Page3_Insert", produces = "application/json; charset=utf-8")
    Map BenhAn_UngBuou_Page3_Insert(@RequestBody BenhAnUngBuouObj benhAnUngBuouObj, HttpSession session) {
        Map response = new HashMap();
        try {
            int result = voBenhAnNoiTruDAO.updateBenhAnUngBuouPage3(benhAnUngBuouObj);
            voBenhAnNoiTruDAO.log_update(Integer.parseInt(benhAnUngBuouObj.getId()), L2Utils.getDvtt(session), L2Utils.getMaUser(session));
            response.put("SUCCESS", result);
            response.put("MESSAGE", ' ');
        } catch (Exception e) {
            response.put("SUCCESS", 0);
            response.put("MESSAGE", "" + e.getMessage());
        }
        return response;
    }

    public @ResponseBody
    @RequestMapping(value = "/BenhAn_NgoaiKhoa_Insert", produces = "application/json; charset=utf-8")
    Map BenhAn_NgoaiKhoa_Insert(@RequestBody BenhAnNgoaiKhoaObj benhAnNgoaiKhoaObj, HttpSession session) {
        Map response = new HashMap();
        try {
            int result = voBenhAnNoiTruDAO.updateBenhAnNgoaiKhoa(benhAnNgoaiKhoaObj);
            voBenhAnNoiTruDAO.log_update(Integer.parseInt(benhAnNgoaiKhoaObj.getId()), L2Utils.getDvtt(session), L2Utils.getMaUser(session));
            response.put("SUCCESS", result);
            response.put("MESSAGE", ' ');
        } catch (Exception e) {
            response.put("SUCCESS", 0);
            response.put("MESSAGE", "" + e.getMessage());
        }
        return response;
    }

    public @ResponseBody
    @RequestMapping(value = "/LoadThongTinNhapVienVBA", method = RequestMethod.GET, produces = "application/json; charset=utf-8")
    Map LoadThongTinNhapVienVBA(HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        try {
            String dvtt = session.getAttribute("Sess_DVTT").toString();
            String stt_benhan = request.getParameter("stt_benhan");//ls_sttbenhan
            List<Map<String, Object>> ls = voBenhAnNoiTruDAO.LoadThongTinNhapVienVBA(dvtt, stt_benhan);
            Map map = ls.get(0);
            return map;
        } catch (Exception e) {
            return null;
        }
    }

    @RequestMapping(value = "/BenhAn_RangHamMat_Update", produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map BenhAn_RangHamMat_Update(HttpServletRequest request, HttpSession session) {
        long vreturn = -1;
        String DVTT = L2Utils.getDvtt(session);
        BenhAnNoiTruRangHamMatObj rangHamMatObj = new BenhAnNoiTruRangHamMatObj(request);
        Map map = new HashMap();
        try {
            vreturn = voBenhAnNoiTruDAO.upd_benhannoitru_ranghammat(rangHamMatObj.getId(), DVTT, rangHamMatObj.getLyDoVaoVien(), rangHamMatObj.getNgayCuaBenh(), rangHamMatObj.getQuaTrinhBenhLy(),
                    rangHamMatObj.getTienSuBanThan(), rangHamMatObj.getKyHieuDiUng(), rangHamMatObj.getKyHieuMaTuy(), rangHamMatObj.getThoiGianMaTuy(), rangHamMatObj.getKyHieuRuouBia(),
                    rangHamMatObj.getThoiGianRuouBia(), rangHamMatObj.getKyHieuThuocLa(), rangHamMatObj.getThoiGianThuocLa(), rangHamMatObj.getKyHieuThuocLao(), rangHamMatObj.getThoiGianThuocLao(),
                    rangHamMatObj.getKyHieuKhac(), rangHamMatObj.getThoiGianKhac(), rangHamMatObj.getTienSuBanThan(), rangHamMatObj.getKhamToanThan(), rangHamMatObj.getMach(),
                    rangHamMatObj.getNhietDo(), rangHamMatObj.getHuyeApTren(), rangHamMatObj.getHuyetApDuoi(), rangHamMatObj.getNhipTho(), rangHamMatObj.getCanNang(),
                    rangHamMatObj.getChieuCao(), rangHamMatObj.getBMI(), rangHamMatObj.getBenhChuyenKhoa(), rangHamMatObj.getTamThanThanKinh(), rangHamMatObj.getTuanHoan(),
                    rangHamMatObj.getHoHap(), rangHamMatObj.getTieuHoa(), rangHamMatObj.getDaMoDuoiDa(), rangHamMatObj.getTietNieuSinhDuc(), rangHamMatObj.getCoXuongKhop(),
                    rangHamMatObj.getKhac(), rangHamMatObj.getCanLamSang(), rangHamMatObj.getTomTatBenhAn(), rangHamMatObj.getBenhChinh(), rangHamMatObj.getBenhPhu(),
                    rangHamMatObj.getPhanBiet(), rangHamMatObj.getTienLuong(), rangHamMatObj.getHuongDieuTri(), rangHamMatObj.getHinhAnhMoTa(), rangHamMatObj.getThoiGiangDiUng());

            map.put("SUCCESS", vreturn);
            map.put("ERRO", "0");
        } catch (Exception e) {
            map.put("ERRO", e.getMessage());

        }
        return map;
    }

    @RequestMapping(value = "/BenhAn_NgoaiTru_RangHamMat_Update", produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map BenhAn_NgoaiTru_RangHamMat_Update(HttpServletRequest request, HttpSession session) {
        long vreturn = -1;
        String DVTT = L2Utils.getDvtt(session);
        BenhAnNgoaiTruRangHamMatObj rangHamMatObj = new BenhAnNgoaiTruRangHamMatObj(request);
        Map map = new HashMap();
        try {
            vreturn = voBenhAnNoiTruDAO.upd_benhanngoaitru_ranghammat(rangHamMatObj.getId(), DVTT, rangHamMatObj.getLyDoVaoVien(), rangHamMatObj.getQuaTrinhBenhLy(), rangHamMatObj.getTienSuBanThan(),
                    rangHamMatObj.getTienSuGiaDinh(), rangHamMatObj.getKhamToanThan(), rangHamMatObj.getMach(), rangHamMatObj.getNhietDo(), rangHamMatObj.getHuyetApTren(),
                    rangHamMatObj.getHuyetApDuoi(), rangHamMatObj.getNhipTho(), rangHamMatObj.getCanNang(), rangHamMatObj.getBenhChuyenKhoa(), rangHamMatObj.getTomTatBenhAn(),
                    rangHamMatObj.getChanDoanKhoaKhamBenh(), rangHamMatObj.getXuLyTuyenDuoi(), rangHamMatObj.getHinhAnhMota(), rangHamMatObj.getDieuTriTuNgay(), rangHamMatObj.getDieuTriDenNgay(),
                    rangHamMatObj.getGiamDoc());
            voBenhAnNoiTruDAO.log_update(Integer.parseInt(request.getParameter("ID")), L2Utils.getDvtt(session), L2Utils.getMaUser(session));
            map.put("SUCCESS", vreturn);
            map.put("ERRO", "0");
        } catch (Exception e) {
            map.put("ERRO", e.getMessage());

        }
        return map;
    }

    @RequestMapping(value = "/BenhAn_TuyenXaPhuong_Update", produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map BenhAn_TuyenXaPhuong_Update(HttpServletRequest request, HttpSession session) {
        long vreturn = -1;
        String DVTT = L2Utils.getDvtt(session);
        BenhAnTuyenXaPhuongObj tuyenXaPhuongObj = new BenhAnTuyenXaPhuongObj(request);
        Map map = new HashMap();
        try {
            vreturn = voBenhAnNoiTruDAO.upd_benhan_tuyen_xaphuong(
                    tuyenXaPhuongObj.getId(), DVTT, tuyenXaPhuongObj.getLyDoVaoTram(), tuyenXaPhuongObj.getNgayCuaBenh(), tuyenXaPhuongObj.getQuaTrinhBenhLy(),
                    tuyenXaPhuongObj.getTienSuBanThan(), tuyenXaPhuongObj.getTienSuGiaDinh(), tuyenXaPhuongObj.getKhamToanThan(), tuyenXaPhuongObj.getMach(), tuyenXaPhuongObj.getNhietDo(),
                    tuyenXaPhuongObj.getHuyetApTren(), tuyenXaPhuongObj.getHuyetApDuoi(), tuyenXaPhuongObj.getNhipTho(), tuyenXaPhuongObj.getCanNang(), tuyenXaPhuongObj.getCacCoQuan(),
                    tuyenXaPhuongObj.getTomTatBenhAn(), tuyenXaPhuongObj.getICDBenhChinh(), tuyenXaPhuongObj.getBenhChinh(), tuyenXaPhuongObj.getICDBenhPhu(), tuyenXaPhuongObj.getBenhPhu(),
                    tuyenXaPhuongObj.getTienLuong(), tuyenXaPhuongObj.getHuongDieuTri()
            );

            map.put("SUCCESS", vreturn);
            map.put("ERRO", "0");
        } catch (Exception e) {
            map.put("ERRO", e.getMessage());

        }
        return map;
    }

    @RequestMapping(value = "/BenhAn_NoiKhoa_Insert", produces = "application/json; charset=utf-8")
    public @ResponseBody
    void BenhAn_NoiKhoa_Insert(HttpServletRequest request) {
        int vreturn = -1;
        String ID = request.getParameter("ID");
        String LYDOVAOVIEN = request.getParameter("LYDOVAOVIEN");
        String VAONGAYTHU = request.getParameter("VAONGAYTHU");
        String BENHSU = request.getParameter("BENHSU");
        String TIENSUBANTHAN = request.getParameter("TIENSUBANTHAN");
        String BOXDDDIUNG = request.getParameter("BOXDDDIUNG");
        String DDDIUNG = request.getParameter("DDDIUNG");
        String BOXDDTHUOCLA = request.getParameter("BOXDDTHUOCLA");
        String DDTHUOCLA = request.getParameter("DDTHUOCLA");
        String BOXDDMATUY = request.getParameter("BOXDDMATUY");
        String DDMATUY = request.getParameter("DDMATUY");
        String BOXDDTHUOCLAO = request.getParameter("BOXDDTHUOCLAO");
        String DDTHUOCLAO = request.getParameter("DDTHUOCLAO");
        String BOXDDRUOUBIA = request.getParameter("BOXDDRUOUBIA");
        String DDRUOUBIA = request.getParameter("DDRUOUBIA");
        String BOXDDKHAC = request.getParameter("BOXDDKHAC");
        String DDKHAC = request.getParameter("DDKHAC");
        String TIENSUGIADINH = request.getParameter("TIENSUGIADINH");
        String KHAMTOANTHAN = request.getParameter("KHAMTOANTHAN");
        String MACH = request.getParameter("MACH");
        String NHIETDO = request.getParameter("NHIETDO");
        String HUYETAPTREN = request.getParameter("HUYETAPTREN");
        String HUYETAPDUOI = request.getParameter("HUYETAPDUOI");
        String NHIPTHO = request.getParameter("NHIPTHO");
        String CANNANG = request.getParameter("CANNANG");
        String CHIEUCAO = request.getParameter("CHIEUCAO");
        String BMI = request.getParameter("BMI");
        String TUANHOAN = request.getParameter("TUANHOAN");
        String HOHAP = request.getParameter("HOHAP");
        String TIEUHOA = request.getParameter("TIEUHOA");
        String TIETNIEUSINHDUC = request.getParameter("TIETNIEUSINHDUC");
        String THANKINH = request.getParameter("THANKINH");
        String XUONGKHOP = request.getParameter("XUONGKHOP");
        String TMH = request.getParameter("TMH");
        String RHM = request.getParameter("RHM");
        String MAT = request.getParameter("MAT");
        String NOITIET = request.getParameter("NOITIET");
        String CLS = request.getParameter("CLS");
        String TOMTATBA = request.getParameter("TOMTATBA");
        String BENHCHINH = request.getParameter("BENHCHINH");
        String BENHPHU = request.getParameter("BENHPHU");
        String PHANBIET = request.getParameter("PHANBIET");
        String TIENLUONG = request.getParameter("TIENLUONG");
        String HUONGDIEUTRI = request.getParameter("HUONGDIEUTRI");
        double canNang = (CANNANG == null || CANNANG.trim().equals("")) ? 0 : Float.parseFloat(CANNANG);
        double chieuCao = (CHIEUCAO == null || CHIEUCAO.trim().equals("") || CHIEUCAO.trim().equals("0")) ? 1 : (Float.parseFloat(CHIEUCAO) / 100);
        double bmi = canNang / (chieuCao * chieuCao);
        double bmilamtron = Math.round(bmi * 100.0) / 100.0;
        String ngayLamBenhAn = request.getParameter("NGAYLAMBENHAN");
        String bacSiLamBenhAn = request.getParameter("BACSILAMBENHAN");
        Map map = new HashMap();
        try {
            String sql = "call HIS_MANAGER.UPDATE_BANOITRU(" +
                    "?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?)" +
                    "#l,s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
            vreturn = jdbcTemplate.update(sql, new Object[]{
                    ID, LYDOVAOVIEN, BENHSU, TIENSUBANTHAN, BOXDDDIUNG
                    , DDDIUNG, BOXDDTHUOCLA, DDTHUOCLA, BOXDDMATUY, DDMATUY
                    , BOXDDTHUOCLAO, DDTHUOCLAO, BOXDDRUOUBIA, DDRUOUBIA, BOXDDKHAC
                    , DDKHAC, TIENSUGIADINH, KHAMTOANTHAN, MACH, NHIETDO
                    , HUYETAPTREN, HUYETAPDUOI, NHIPTHO, CANNANG, CHIEUCAO
                    , bmilamtron, TUANHOAN, HOHAP, TIEUHOA, TIETNIEUSINHDUC
                    , THANKINH, XUONGKHOP, TMH, RHM, MAT
                    , NOITIET, CLS, TOMTATBA, BENHCHINH, BENHPHU
                    , PHANBIET, TIENLUONG, HUONGDIEUTRI, VAONGAYTHU
                    ,ngayLamBenhAn, bacSiLamBenhAn
            });
            map.put("SUCCESS", vreturn);
            map.put("ERRO", "0");
        } catch (Exception e) {
            map.put("ERROR", e.getMessage());

        }
    }

    public @ResponseBody
    @RequestMapping(value = "/update-benhan-mattreem", produces = "application/json; charset=utf-8")
    Map updateBenhAnMatTreEm(@RequestBody BenhAnMatTreEmObj obj, HttpSession session) {
        Map response = new HashMap();
        try {
            int result = voBenhAnNoiTruDAO.updateBenhAnMatTreEm(obj);
            response.put("SUCCESS", result);
            response.put("MESSAGE", ' ');
        } catch (Exception e) {
            response.put("SUCCESS", 0);
            response.put("MESSAGE", "" + e.getMessage());
        }
        return response;
    }

    public @ResponseBody
    @RequestMapping(value = "/update-benhan-daymat", produces = "application/json; charset=utf-8")
    Map updateBenhAnDayMat(@RequestBody BenhAnDayMatObj obj, HttpSession session) {
        Map response = new HashMap();
        try {
            int result = voBenhAnNoiTruDAO.updateBenhAnDayMat(obj);
            response.put("SUCCESS", result);
            response.put("MESSAGE", ' ');
        } catch (Exception e) {
            response.put("SUCCESS", 0);
            response.put("MESSAGE", "" + e.getMessage());
        }
        return response;
    }

    public @ResponseBody
    @RequestMapping(value = "/TongKetBenhAn_Update", produces = "application/json; charset=utf-8")
    Map TongKetBenhAn_Update(@RequestBody TongKetBenhAnObj tongKetBenhAnObj, HttpSession session) {
        Map response = new HashMap();
        try {
            int result = voBenhAnNoiTruDAO.updateTongKetBenhAn(tongKetBenhAnObj);
            voBenhAnNoiTruDAO.log_update(Integer.parseInt(tongKetBenhAnObj.getId()), L2Utils.getDvtt(session), L2Utils.getMaUser(session));
            response.put("SUCCESS", result);
            response.put("MESSAGE", ' ');
        } catch (Exception e) {
            response.put("SUCCESS", 0);
            response.put("MESSAGE", "" + e.getMessage());
        }
        return response;
    }

    @RequestMapping(value = "/load-danh-sach-thamso-vba-by-dvtt")
    public @ResponseBody
    List loadDanhMucThamSoVBA(HttpSession session) {
        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSourceMNG);
        MapSqlParameterSource params = new MapSqlParameterSource().addValue("dvtt", L2Utils.getDvtt(session));
        return namedParameterJdbcTemplate.queryForList("SELECT MA_THAMSO, TEN_THAMSO, MOTA_THAMSO, GHICHU_THAMSO FROM DM_THAM_SO_DON_VI_VOBENHAN WHERE DVTT = :dvtt", params);
    }

    @RequestMapping(value = "/them-tham-so-vba")
    public @ResponseBody
    int insertThamSoVba(HttpSession session, HttpServletRequest request) {
        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSourceMNG);
        MapSqlParameterSource params = new MapSqlParameterSource().addValue("dvtt", L2Utils.getDvtt(session));
        params.addValue("maThamSo", request.getParameter("maThamSo"));
        params.addValue("tenThamSo", request.getParameter("tenThamSo"));
        params.addValue("giaTriThamSo", request.getParameter("giaTriThamSo"));
        params.addValue("ghiChuThamSo", request.getParameter("ghiChuThamSo"));
        params.addValue("curUser", L2Utils.getMaUser(session));
        return namedParameterJdbcTemplate.update("INSERT INTO DM_THAM_SO_DON_VI_VOBENHAN(DVTT, MA_THAMSO, TEN_THAMSO, MOTA_THAMSO, GHICHU_THAMSO, DATE_INS, USER_INS) VALUES\n" +
                "(:dvtt, :maThamSo, :tenThamSo, :giaTriThamSo, :ghiChuThamSo, SYSTIMESTAMP, :curUser)", params);
    }

    @RequestMapping(value = "/cap-nhat-tham-so-vba")
    public @ResponseBody
    int updateThamSoVba(HttpSession session, HttpServletRequest request) {
        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSourceMNG);
        MapSqlParameterSource params = new MapSqlParameterSource().addValue("dvtt", L2Utils.getDvtt(session));
        params.addValue("maThamSo", request.getParameter("maThamSo"));
        params.addValue("tenThamSo", request.getParameter("tenThamSo"));
        params.addValue("giaTriThamSo", request.getParameter("giaTriThamSo"));
        params.addValue("ghiChuThamSo", request.getParameter("ghiChuThamSo"));
        params.addValue("curUser", L2Utils.getMaUser(session));
        return namedParameterJdbcTemplate.update("UPDATE DM_THAM_SO_DON_VI_VOBENHAN SET TEN_THAMSO = :tenThamSo, MOTA_THAMSO = :giaTriThamSo, GHICHU_THAMSO = :ghiChuThamSo,\n" +
                "                                      DATE_UPD_LAST = SYSTIMESTAMP, USER_UP_LAST = :curUser\n" +
                "WHERE DVTT = :dvtt AND MA_THAMSO = :maThamSo", params);
    }

    @RequestMapping(value = "/xoa-tham-so-vba")
    public @ResponseBody
    int xoaDanhMucThamSoVBA(HttpSession session, HttpServletRequest request) {
        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSourceMNG);
        MapSqlParameterSource params = new MapSqlParameterSource().addValue("dvtt", L2Utils.getDvtt(session));
        params.addValue("maThamSo", request.getParameter("maThamSo"));
        return namedParameterJdbcTemplate.update("DELETE FROM DM_THAM_SO_DON_VI_VOBENHAN WHERE DVTT = :dvtt AND MA_THAMSO = :maThamSo", params);
    }

    @RequestMapping(value = "/load_danhsach_nhanvien_by_maphongban", produces = "application/json; charset=utf-8", method = RequestMethod.GET)
    public @ResponseBody
    List dsNhanVien(HttpServletRequest request, HttpSession session) {
        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSourceMNG);
        MapSqlParameterSource params = new MapSqlParameterSource().addValue("maPhongBan", request.getParameter("maPhongBan"));
        String sql = " SELECT NV.MA_NHANVIEN, CD.MOTA_CHUCDANH ||'. '|| NV.TEN_NHANVIEN AS TEN_NHANVIEN, CD.TEN_CHUCDANH " +
                "        FROM HIS_FW.DM_NHANVIEN NV " +
                "        JOIN HIS_FW.DM_CHUCDANH_NHANVIEN CD ON CD.MA_CHUCDANH = NV.CHUCDANH_NHANVIEN " +
                "       WHERE DECODE(:maPhongBan, '-1', NV.MA_PHONGBAN, :maPhongBan) = NV.MA_PHONGBAN ORDER BY NV.TEN_NHANVIEN ASC";
        return namedParameterJdbcTemplate.queryForList(sql, params);
    }

    @RequestMapping(value = "/reload-thong-tin-vo-benh-an", produces = "application/json; charset=utf-8", method = RequestMethod.GET)
    public @ResponseBody
    Map thongTinBenhAn(HttpServletRequest request, HttpSession session) {
        return voBenhAnNoiTruDAO.thongTinVoBenhAn(L2Utils.getDvtt(session), request.getParameter("loaiBenhAn"), request.getParameter("iD"));
    }

    public @ResponseBody
    @RequestMapping(value = "/TongKetBenhAn_UpdateYHCTNoiTru", produces = "application/json; charset=utf-8")
    Map TongKetBenhAn_UpdateYHCTNoiTru(@RequestBody TongKetBenhAn_YHCTNoiTruObj TongKetBenhAn_YHCTNoiTruObj, HttpSession session) {
        Map response = new HashMap();
        try {
            int result = voBenhAnNoiTruDAO.updateTongKetBenhAn_YHCTNoiTru(TongKetBenhAn_YHCTNoiTruObj);
            voBenhAnNoiTruDAO.log_update(Integer.parseInt(TongKetBenhAn_YHCTNoiTruObj.getId()), L2Utils.getDvtt(session), L2Utils.getMaUser(session));
            response.put("SUCCESS", result);
            response.put("MESSAGE", ' ');
        } catch (Exception e) {
            response.put("SUCCESS", 0);
            response.put("MESSAGE", "" + e.getMessage());
        }
        return response;
    }

    public @ResponseBody
    @RequestMapping(value = "/update-benhan-nhitruyhct1941", method = RequestMethod.POST)
    Map BenhAn_YHCTNhi1941Insert(HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        int vreturn = -1;
        String dvtt = L2Utils.getDvtt(session);
        String maKhoa = L2Utils.getMaPhongBan(session);
        String idUser = L2Utils.getMaUser(session);
        String ID = request.getParameter("ID");
        String LYDOVAOVIEN = request.getParameter("LYDOVAOVIEN");
        String BENHSU = request.getParameter("BENHSU");
        String KHAMTOANTHAN = request.getParameter("KHAMTOANTHAN");
        String MACH = request.getParameter("MACH");
        String NHIETDO = request.getParameter("NHIETDO");
        String HUYETAPTREN = request.getParameter("HUYETAPTREN");
        String HUYETAPDUOI = request.getParameter("HUYETAPDUOI");
        String NHIPTHO = request.getParameter("NHIPTHO");
        String CANNANG = request.getParameter("CANNANG");
        String CHIEUCAO = request.getParameter("CHIEUCAO");
        String BMI = request.getParameter("BMI");
        String TUANHOAN = request.getParameter("TUANHOAN");
        String HOHAP = request.getParameter("HOHAP");
        String TIEUHOA = request.getParameter("TIEUHOA");
        String TIETNIEUSINHDUC = request.getParameter("TIETNIEUSINHDUC");
        String THANKINH = request.getParameter("THANKINH");
        String XUONGKHOP = request.getParameter("XUONGKHOP");
        String TMH = request.getParameter("TMH");
        String RHM = request.getParameter("RHM");
        String MAT = request.getParameter("MAT");
        String NOITIET = request.getParameter("NOITIET");
        String CLS = request.getParameter("CLS");
        String BENHCHINH = request.getParameter("BENHCHINH");
        String BENHPHU = request.getParameter("BENHPHU");
        String PHANBIET = request.getParameter("PHANBIET");
        String CONTHU = request.getParameter("CONTHU");
        String DUTHANG = request.getParameter("DUTHANG");
        String CANNANGMOISINH = request.getParameter("CANNANGMOISINH");
        String DEDE = request.getParameter("DEDE");
        String NGATTHOKHISINH = request.getParameter("NGATTHOKHISINH");
        String THOIGIANRUNGRON = request.getParameter("THOIGIANRUNGRON");
        String CHEDOANDUOI1TUOI = request.getParameter("CHEDOANDUOI1TUOI");
        String THANGCAISUA = request.getParameter("THANGCAISUA");
        String CHEDOANTREN1TUOI = request.getParameter("CHEDOANTREN1TUOI");
        String THANGBIETLAY = request.getParameter("THANGBIETLAY");
        String THANGBIETBO = request.getParameter("THANGBIETBO");
        String THANGBIETDI = request.getParameter("THANGBIETDI");
        String THANGBIETNOI = request.getParameter("THANGBIETNOI");
        String THANGMOCRANG = request.getParameter("THANGMOCRANG");
        String TUOICOKINH = request.getParameter("TUOICOKINH");
        String DATIEMCHUNG = request.getParameter("DATIEMCHUNG");
        String BENHDAMAC = request.getParameter("BENHDAMAC");
        String MOTABENHDAMAC = request.getParameter("MOTABENHDAMAC");
        String BENHDAMACGIADINH = request.getParameter("BENHDAMACGIADINH");
        String BENHDAMACCANHAN = request.getParameter("BENHDAMACCANHAN");
        String DACDIEMSINHHOAT = request.getParameter("DACDIEMSINHHOAT");
        String TIENSUDACBIET = request.getParameter("TIENSUDACBIET");
        String TOMTATBA = request.getParameter("TOMTATBA");
        String HINHTHAI1 = request.getParameter("HINHTHAI1");
        String HINHTHAI2 = request.getParameter("HINHTHAI2");
        String HINHTHAI3 = request.getParameter("HINHTHAI3");
        String THAN1 = request.getParameter("THAN1");
        String THAN2 = request.getParameter("THAN2");
        String SAC1 = request.getParameter("SAC1");
        String SAC2 = request.getParameter("SAC2");
        String SAC3 = request.getParameter("SAC3");
        String TRACH1 = request.getParameter("TRACH1");
        String TRACH2 = request.getParameter("TRACH2");
        String HINHLUOI1 = request.getParameter("HINHLUOI1");
        String HINHLUOI2 = request.getParameter("HINHLUOI2");
        String CHATLUOI1 = request.getParameter("CHATLUOI1");
        String CHATLUOI2 = request.getParameter("CHATLUOI2");
        String CHATLUOI3 = request.getParameter("CHATLUOI3");
        String REULUOI1 = request.getParameter("REULUOI1");
        String REULUOI2 = request.getParameter("REULUOI2");
        String REULUOI3 = request.getParameter("REULUOI3");
        String DUONGDI = request.getParameter("DUONGDI");
        String TINHCHAT = request.getParameter("TINHCHAT");
        String HINHDANG = request.getParameter("HINHDANG");
        String MAUSAC = request.getParameter("MAUSAC");
        String MOTAVONGCHAN = request.getParameter("MOTAVONGCHAN");
        String TIENGNOI1 = request.getParameter("TIENGNOI1");
        String TIENGNOI2 = request.getParameter("TIENGNOI2");
        String TIENGNOI3 = request.getParameter("TIENGNOI3");
        String TIENGNOI4 = request.getParameter("TIENGNOI4");
        String TIENGNOI5 = request.getParameter("TIENGNOI5");
        String HOITHO1 = request.getParameter("HOITHO1");
        String HOITHO2 = request.getParameter("HOITHO2");
        String HOITHO3 = request.getParameter("HOITHO3");
        String COHO = request.getParameter("COHO");
        String HO1 = request.getParameter("HO1");
        String HO2 = request.getParameter("HO2");
        String HO3 = request.getParameter("HO3");
        String COO = request.getParameter("COO");
        String CONAC = request.getParameter("CONAC");
        String COMUI = request.getParameter("COMUI");
        String MUICOTHE1 = request.getParameter("MUICOTHE1");
        String MUICOTHE2 = request.getParameter("MUICOTHE2");
        String MUICOTHE3 = request.getParameter("MUICOTHE3");
        String COCHATTHAI = request.getParameter("COCHATTHAI");
        String CHATTHAI1 = request.getParameter("CHATTHAI1");
        String CHATTHAI2 = request.getParameter("CHATTHAI2");
        String CHATTHAI3 = request.getParameter("CHATTHAI3");
        String MOTAVANCHAN = request.getParameter("MOTAVANCHAN");
        String COHANNHIET = request.getParameter("COHANNHIET");
        String HANNHIET1 = request.getParameter("HANNHIET1");
        String HANNHIET2 = request.getParameter("HANNHIET2");
        String HANNHIET3 = request.getParameter("HANNHIET3");
        String COMOHOI = request.getParameter("COMOHOI");
        String MOHOI1 = request.getParameter("MOHOI1");
        String MOHOI2 = request.getParameter("MOHOI2");
        String MOHOI3 = request.getParameter("MOHOI3");
        String CODAUMATCO = request.getParameter("CODAUMATCO");
        String DAUDAU1 = request.getParameter("DAUDAU1");
        String DAUDAU2 = request.getParameter("DAUDAU2");
        String DAUDAU3 = request.getParameter("DAUDAU3");
        String COHMCM = request.getParameter("COHMCM");
        String DOIMAT1 = request.getParameter("DOIMAT1");
        String DOIMAT2 = request.getParameter("DOIMAT2");
        String DOIMAT3 = request.getParameter("DOIMAT3");
        String TAI1 = request.getParameter("TAI1");
        String TAI2 = request.getParameter("TAI2");
        String TAI3 = request.getParameter("TAI3");
        String MUI1 = request.getParameter("MUI1");
        String MUI2 = request.getParameter("MUI2");
        String MUI3 = request.getParameter("MUI3");
        String HONG1 = request.getParameter("HONG1");
        String HONG2 = request.getParameter("HONG2");
        String HONG3 = request.getParameter("HONG3");
        String COVAI1 = request.getParameter("COVAI1");
        String COVAI2 = request.getParameter("COVAI2");
        String COVAI3 = request.getParameter("COVAI3");
        String COLUNG = request.getParameter("COLUNG");
        String LUNG = request.getParameter("LUNG");
        String CONGUC = request.getParameter("CONGUC");
        String NGUC1 = request.getParameter("NGUC1");
        String NGUC2 = request.getParameter("NGUC2");
        String NGUC3 = request.getParameter("NGUC3");
        String NGUC4 = request.getParameter("NGUC4");
        String NGUC5 = request.getParameter("NGUC5");
        String NGUC6 = request.getParameter("NGUC6");
        String COBUNG = request.getParameter("COBUNG");
        String BUNG1 = request.getParameter("BUNG1");
        String BUNG2 = request.getParameter("BUNG2");
        String BUNG3 = request.getParameter("BUNG3");
        String BUNG4 = request.getParameter("BUNG4");
        String BUNG5 = request.getParameter("BUNG5");
        String BUNG6 = request.getParameter("BUNG6");
        String COCHANTAY = request.getParameter("COCHANTAY");
        String CHANTAY1 = request.getParameter("CHANTAY1");
        String CHANTAY2 = request.getParameter("CHANTAY2");
        String CHANTAY3 = request.getParameter("CHANTAY3");
        String CHANTAY4 = request.getParameter("CHANTAY4");
        String CHANTAY5 = request.getParameter("CHANTAY5");
        String CHANTAY6 = request.getParameter("CHANTAY6");
        String COAN = request.getParameter("COAN");
        String AN1 = request.getParameter("AN1");
        String AN2 = request.getParameter("AN2");
        String AN3 = request.getParameter("AN3");
        String COUONG = request.getParameter("COUONG");
        String UONG1 = request.getParameter("UONG1");
        String UONG2 = request.getParameter("UONG2");
        String UONG3 = request.getParameter("UONG3");
        String CODAITIEUTIEN = request.getParameter("CODAITIEUTIEN");
        String TIEUTIEN1 = request.getParameter("TIEUTIEN1");
        String TIEUTIEN2 = request.getParameter("TIEUTIEN2");
        String TIEUTIEN3 = request.getParameter("TIEUTIEN3");
        String DAITIEN1 = request.getParameter("DAITIEN1");
        String DAITIEN2 = request.getParameter("DAITIEN2");
        String DAITIEN3 = request.getParameter("DAITIEN3");
        String CONGU = request.getParameter("CONGU");
        String NGU1 = request.getParameter("NGU1");
        String NGU2 = request.getParameter("NGU2");
        String NGU3 = request.getParameter("NGU3");
        String COSDSS = request.getParameter("COSDSS");
        String BENHNAM1 = request.getParameter("BENHNAM1");
        String BENHNAM2 = request.getParameter("BENHNAM2");
        String BENHNAM3 = request.getParameter("BENHNAM3");
        String COKINHNGUYET = request.getParameter("COKINHNGUYET");
        String RLKINHNGUYET1 = request.getParameter("RLKINHNGUYET1");
        String RLKINHNGUYET2 = request.getParameter("RLKINHNGUYET2");
        String THONGKINH1 = request.getParameter("THONGKINH1");
        String THONGKINH2 = request.getParameter("THONGKINH2");
        String CODOIHA = request.getParameter("CODOIHA");
        String DOIHA1 = request.getParameter("DOIHA1");
        String DOIHA2 = request.getParameter("DOIHA2");
        String DIEUKIENXUATHIENBENH = request.getParameter("DIEUKIENXUATHIENBENH");
        String MOTAKHACVANCHAN = request.getParameter("MOTAKHACVANCHAN");
        String COXUCCHAN = request.getParameter("COXUCCHAN");
        String DA1 = request.getParameter("DA1");
        String DA2 = request.getParameter("DA2");
        String DA3 = request.getParameter("DA3");
        String COCOXUONGKHOP = request.getParameter("COCOXUONGKHOP");
        String COXUONGKHOP1 = request.getParameter("COXUONGKHOP1");
        String COXUONGKHOP2 = request.getParameter("COXUONGKHOP2");
        String COXUONGKHOP3 = request.getParameter("COXUONGKHOP3");
        String COBUNGTHIETCHAN = request.getParameter("COBUNGTHIETCHAN");
        String BUNGTHIETCHAN1 = request.getParameter("BUNGTHIETCHAN1");
        String BUNGTHIETCHAN2 = request.getParameter("BUNGTHIETCHAN2");
        String BUNGTHIETCHAN3 = request.getParameter("BUNGTHIETCHAN3");
        String COMOHOITHIETCHAN = request.getParameter("COMOHOITHIETCHAN");
        String MOHOITHIETCHAN1 = request.getParameter("MOHOITHIETCHAN1");
        String MOHOITHIETCHAN2 = request.getParameter("MOHOITHIETCHAN2");
        String MOHOITHIETCHAN3 = request.getParameter("MOHOITHIETCHAN3");
        String THOP = request.getParameter("THOP");
        String MACHCHAN1 = request.getParameter("MACHCHAN1");
        String MACHCHAN2 = request.getParameter("MACHCHAN2");
        String MACHCHAN3 = request.getParameter("MACHCHAN3");
        String TONGKHANPHAI1 = request.getParameter("TONGKHANPHAI1");
        String TONGKHANPHAI2 = request.getParameter("TONGKHANPHAI2");
        String TONGKHANPHAI3 = request.getParameter("TONGKHANPHAI3");
        String TONGKHANTRAI1 = request.getParameter("TONGKHANTRAI1");
        String TONGKHANTRAI2 = request.getParameter("TONGKHANTRAI2");
        String TONGKHANTRAI3 = request.getParameter("TONGKHANTRAI3");
        String TAITRAITHON1 = request.getParameter("TAITRAITHON1");
        String TAITRAITHON2 = request.getParameter("TAITRAITHON2");
        String TAITRAITHON3 = request.getParameter("TAITRAITHON3");
        String TAITRAIQUAN1 = request.getParameter("TAITRAIQUAN1");
        String TAITRAIQUAN2 = request.getParameter("TAITRAIQUAN2");
        String TAITRAIQUAN3 = request.getParameter("TAITRAIQUAN3");
        String TAITRAIXICH1 = request.getParameter("TAITRAIXICH1");
        String TAITRAIXICH2 = request.getParameter("TAITRAIXICH2");
        String TAITRAIXICH3 = request.getParameter("TAITRAIXICH3");
        String TAIPHAITHON1 = request.getParameter("TAIPHAITHON1");
        String TAIPHAITHON2 = request.getParameter("TAIPHAITHON2");
        String TAIPHAITHON3 = request.getParameter("TAIPHAITHON3");
        String TAIPHAIQUAN1 = request.getParameter("TAIPHAIQUAN1");
        String TAIPHAIQUAN2 = request.getParameter("TAIPHAIQUAN2");
        String TAIPHAIQUAN3 = request.getParameter("TAIPHAIQUAN3");
        String TAIPHAIXICH1 = request.getParameter("TAIPHAIXICH1");
        String TAIPHAIXICH2 = request.getParameter("TAIPHAIXICH2");
        String TAIPHAIXICH3 = request.getParameter("TAIPHAIXICH3");
        String MOTATHIETCHAN = request.getParameter("MOTATHIETCHAN");
        String TOMTATTUCHAN = request.getParameter("TOMTATTUCHAN");
        String BIENCHUNGLUANTRI = request.getParameter("BIENCHUNGLUANTRI");
        String CHUANDOANBENHDANH = request.getParameter("CHUANDOANBENHDANH");
        String BENHDANH1 = request.getParameter("BENHDANH1");
        String BENHDANH2 = request.getParameter("BENHDANH2");
        String BENHDANH3 = request.getParameter("BENHDANH3");
        String BENHDANH4 = request.getParameter("BENHDANH4");
        String BENHDANH5 = request.getParameter("BENHDANH5");
        String BENHDANH6 = request.getParameter("BENHDANH6");
        String CHUANDOANBATCUONG = request.getParameter("CHUANDOANBATCUONG");
        String BATCUONG1 = request.getParameter("BATCUONG1");
        String BATCUONG2 = request.getParameter("BATCUONG2");
        String BATCUONG3 = request.getParameter("BATCUONG3");
        String BATCUONG4 = request.getParameter("BATCUONG4");
        String BATCUONG5 = request.getParameter("BATCUONG5");
        String TANGPHU1 = request.getParameter("TANGPHU1");
        String TANGPHU2 = request.getParameter("TANGPHU2");
        String TANGPHU3 = request.getParameter("TANGPHU3");
        String TANGPHU4 = request.getParameter("TANGPHU4");
        String KINHMACH1 = request.getParameter("KINHMACH1");
        String KINHMACH2 = request.getParameter("KINHMACH2");
        String KINHMACH3 = request.getParameter("KINHMACH3");
        String KINHMACH4 = request.getParameter("KINHMACH4");
        String DINHVIBENH1 = request.getParameter("DINHVIBENH1");
        String DINHVIBENH2 = request.getParameter("DINHVIBENH2");
        String PHAPDIEUTRI = request.getParameter("PHAPDIEUTRI");
        String PHUONGDUOC = request.getParameter("PHUONGDUOC");
        String PPKHONGDUNGTHUOC = request.getParameter("PPKHONGDUNGTHUOC");
        String PHUONGPHAPKHAC = request.getParameter("PHUONGPHAPKHAC");
        String YHOCHIENDAI = request.getParameter("YHOCHIENDAI");
        String DUHAU = request.getParameter("DUHAU");
        String TENBENHCHINH = request.getParameter("TENBENHCHINH");
        String ICDBENHDANH = request.getParameter("ICDBENHDANH");
        String ICDBENHPHU1 = request.getParameter("ICDBENHPHU1");
        String ICDBENHPHU2 = request.getParameter("ICDBENHPHU2");
        String TENBENHPHU1 = request.getParameter("TENBENHPHU1");
        String TENBENHPHU2 = request.getParameter("TENBENHPHU2");
        String NGUYENNHANCHANDOAN = request.getParameter("NGUYENNHANCHANDOAN");
        double canNang = (CANNANG == null || CANNANG.trim().equals("")) ? 0 : Float.parseFloat(CANNANG);
        double chieuCao = (CHIEUCAO == null || CHIEUCAO.trim().equals("") || CHIEUCAO.trim().equals("0")) ? 1 : (Float.parseFloat(CHIEUCAO) / 100);
        double bmi = canNang / (chieuCao * chieuCao);
        double bmilamtron = Math.round(bmi * 100.0) / 100.0;
        Map map = new HashMap();
        try {
            String sql = "call HIS_MANAGER.UPDATE_BENHAN_YHCT_NHI1941(" +
                    "?,?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?" +
                    ",?)" +
                    "#l,s,s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s" +
                    ",s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
            vreturn = jdbcTemplate.update(sql, new Object[]{
                    ID,LYDOVAOVIEN,BENHSU,KHAMTOANTHAN,MACH,NHIETDO,HUYETAPTREN,HUYETAPDUOI,NHIPTHO,CANNANG,CHIEUCAO
                    ,bmilamtron,TUANHOAN,HOHAP,TIEUHOA,TIETNIEUSINHDUC,THANKINH,XUONGKHOP,TMH,RHM,MAT
                    ,NOITIET,CLS,BENHCHINH,BENHPHU,PHANBIET,CONTHU,DUTHANG,CANNANGMOISINH,DEDE,NGATTHOKHISINH,THOIGIANRUNGRON
                    ,CHEDOANDUOI1TUOI,THANGCAISUA,CHEDOANTREN1TUOI,THANGBIETLAY,THANGBIETBO,THANGBIETDI,THANGBIETNOI,THANGMOCRANG,TUOICOKINH,DATIEMCHUNG
                    ,BENHDAMAC,MOTABENHDAMAC,BENHDAMACGIADINH,BENHDAMACCANHAN,DACDIEMSINHHOAT,TIENSUDACBIET,TOMTATBA,HINHTHAI1,HINHTHAI2,HINHTHAI3
                    ,THAN1,THAN2,SAC1,SAC2,SAC3,TRACH1,TRACH2,HINHLUOI1,HINHLUOI2,CHATLUOI1
                    ,CHATLUOI2,CHATLUOI3,REULUOI1,REULUOI2,REULUOI3,DUONGDI,TINHCHAT,HINHDANG,MAUSAC,MOTAVONGCHAN
                    ,TIENGNOI1,TIENGNOI2,TIENGNOI3,TIENGNOI4,TIENGNOI5,HOITHO1,HOITHO2,HOITHO3,COHO,HO1
                    ,HO2,HO3,COO,CONAC,COMUI,MUICOTHE1,MUICOTHE2,MUICOTHE3,COCHATTHAI,CHATTHAI1
                    ,CHATTHAI2,CHATTHAI3,MOTAVANCHAN,COHANNHIET,HANNHIET1,HANNHIET2,HANNHIET3,COMOHOI,MOHOI1,MOHOI2
                    ,MOHOI3,CODAUMATCO,DAUDAU1,DAUDAU2,DAUDAU3,COHMCM,DOIMAT1,DOIMAT2,DOIMAT3,TAI1
                    ,TAI2,TAI3,MUI1,MUI2,MUI3,HONG1,HONG2,HONG3,COVAI1,COVAI2
                    ,COVAI3,COLUNG,LUNG,CONGUC,NGUC1,NGUC2,NGUC3,NGUC4,NGUC5,NGUC6
                    ,COBUNG,BUNG1,BUNG2,BUNG3,BUNG4,BUNG5,BUNG6,COCHANTAY,CHANTAY1,CHANTAY2
                    ,CHANTAY3,CHANTAY4,CHANTAY5,CHANTAY6,COAN,AN1,AN2,AN3,COUONG,UONG1
                    ,UONG2,UONG3,CODAITIEUTIEN,TIEUTIEN1,TIEUTIEN2,TIEUTIEN3,DAITIEN1,DAITIEN2,DAITIEN3,CONGU
                    ,NGU1,NGU2,NGU3,COSDSS,BENHNAM1,BENHNAM2,BENHNAM3,COKINHNGUYET,RLKINHNGUYET1,RLKINHNGUYET2
                    ,THONGKINH1,THONGKINH2,CODOIHA,DOIHA1,DOIHA2,DIEUKIENXUATHIENBENH,MOTAKHACVANCHAN,COXUCCHAN,DA1,DA2
                    ,DA3,COCOXUONGKHOP,COXUONGKHOP1,COXUONGKHOP2,COXUONGKHOP3,COBUNGTHIETCHAN,BUNGTHIETCHAN1,BUNGTHIETCHAN2,BUNGTHIETCHAN3,COMOHOITHIETCHAN
                    ,MOHOITHIETCHAN1,MOHOITHIETCHAN2,MOHOITHIETCHAN3,THOP,MACHCHAN1,MACHCHAN2,MACHCHAN3,TONGKHANPHAI1,TONGKHANPHAI2,TONGKHANPHAI3
                    ,TONGKHANTRAI1,TONGKHANTRAI2,TONGKHANTRAI3,TAITRAITHON1,TAITRAITHON2,TAITRAITHON3,TAITRAIQUAN1,TAITRAIQUAN2,TAITRAIQUAN3,TAITRAIXICH1
                    ,TAITRAIXICH2,TAITRAIXICH3,TAIPHAITHON1,TAIPHAITHON2,TAIPHAITHON3,TAIPHAIQUAN1,TAIPHAIQUAN2,TAIPHAIQUAN3,TAIPHAIXICH1,TAIPHAIXICH2
                    ,TAIPHAIXICH3,MOTATHIETCHAN,TOMTATTUCHAN,BIENCHUNGLUANTRI,CHUANDOANBENHDANH,BENHDANH1,BENHDANH2,BENHDANH3,BENHDANH4,BENHDANH5
                    ,BENHDANH6,CHUANDOANBATCUONG,BATCUONG1,BATCUONG2,BATCUONG3,BATCUONG4,BATCUONG5,TANGPHU1,TANGPHU2,TANGPHU3
                    ,TANGPHU4,KINHMACH1,KINHMACH2,KINHMACH3,KINHMACH4,DINHVIBENH1,DINHVIBENH2,PHAPDIEUTRI,PHUONGDUOC,PPKHONGDUNGTHUOC
                    ,PHUONGPHAPKHAC,YHOCHIENDAI,DUHAU,TENBENHCHINH,ICDBENHDANH,ICDBENHPHU1,ICDBENHPHU2,TENBENHPHU1,TENBENHPHU2
                    ,NGUYENNHANCHANDOAN

            });
            map.put("SUCCESS", vreturn);
            map.put("ERRO", "0");
        } catch (Exception e) {
            map.put("ERRO", e.getMessage());

        }
        return map;
    }

    @RequestMapping(value = "/BenhAn_MatGlocom_Update", produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map BenhAn_MatGlocom_Update(HttpServletRequest request, HttpSession session) {
        long vreturn = -1;
        String DVTT = L2Utils.getDvtt(session);
        BenhAnNoiTruMatGlocomObj matGlocomObj = new BenhAnNoiTruMatGlocomObj(request);
        Map map = new HashMap();
        try {
            vreturn = voBenhAnNoiTruDAO.upd_benhannoitru_matglocom(Integer.parseInt(matGlocomObj.getId()), DVTT,
                    matGlocomObj.getLydonhucmat(), matGlocomObj.getLydonhin(), matGlocomObj.getLydosoanhsang(), matGlocomObj.getLydodomat(), matGlocomObj.getLydotoanthan(),
                    matGlocomObj.getLydocactrieuchungkhac(), matGlocomObj.getThoigianxuathienbenh(), matGlocomObj.getCosodakham(), matGlocomObj.getPhuongphapdieutri(), matGlocomObj.getLoaiptmplan1(),
                    matGlocomObj.getLoaiptmplan2(), matGlocomObj.getLoaiptmplan3(), matGlocomObj.getLoaiptmplan4(), matGlocomObj.getLoaiptmtlan1(), matGlocomObj.getLoaiptmtlan2(),
                    matGlocomObj.getLoaiptmtlan3(), matGlocomObj.getLoaiptmtlan4(), matGlocomObj.getPtmplan1(), matGlocomObj.getPtmplan2(), matGlocomObj.getPtmplan3(), matGlocomObj.getPtmplan4(),
                    matGlocomObj.getPtmtlan1(), matGlocomObj.getPtmtlan2(), matGlocomObj.getPtmtlan3(), matGlocomObj.getPtmtlan4(), matGlocomObj.getNoiptmplan1(), matGlocomObj.getNoiptmplan2(),
                    matGlocomObj.getNoiptmplan3(), matGlocomObj.getNoiptmplan4(), matGlocomObj.getNoiptmtlan1(), matGlocomObj.getNoiptmtlan2(), matGlocomObj.getNoiptmtlan3(), matGlocomObj.getNoiptmtlan4(),
                    matGlocomObj.getThuochanhan(), matGlocomObj.getThuochanhansl(),
                    matGlocomObj.getStt1(), matGlocomObj.getTenthuoc1(), matGlocomObj.getDuongdung1(), matGlocomObj.getLieudung1(), matGlocomObj.getThoigiandung1(), matGlocomObj.getGhichu1(),
                    matGlocomObj.getStt2(), matGlocomObj.getTenthuoc2(), matGlocomObj.getDuongdung2(), matGlocomObj.getLieudung2(), matGlocomObj.getThoigiandung2(), matGlocomObj.getGhichu2(),
                    matGlocomObj.getStt3(), matGlocomObj.getTenthuoc3(), matGlocomObj.getDuongdung3(), matGlocomObj.getLieudung3(), matGlocomObj.getThoigiandung3(), matGlocomObj.getGhichu3(),
                    matGlocomObj.getStt4(), matGlocomObj.getTenthuoc4(), matGlocomObj.getDuongdung4(), matGlocomObj.getLieudung4(), matGlocomObj.getThoigiandung4(), matGlocomObj.getGhichu4(),
                    matGlocomObj.getThuockhac(), matGlocomObj.getTientrinhdieutri(), matGlocomObj.getTs_benhmatkhac(), matGlocomObj.getTs_benhmatkhac_khac(), matGlocomObj.getCorticosteroid_thuoc(), matGlocomObj.getCorticosteroid_thoigian(), matGlocomObj.getCorticosteroid_duongdung(),
                    matGlocomObj.getCorticosteroid_chidinh(), matGlocomObj.getTs_toanthan(), matGlocomObj.getTs_ttbenhkhac(), matGlocomObj.getNguoithan_glocom(),
                    matGlocomObj.getThiluc_mp(), matGlocomObj.getThiluc_mt(), matGlocomObj.getNhanap_mp(), matGlocomObj.getNhanap_mt(), matGlocomObj.getMimat_sungne_mp(), matGlocomObj.getMimat_sungne_mt(),
                    matGlocomObj.getMimat_khac_mp(), matGlocomObj.getMimat_khac_mt(), matGlocomObj.getKetmac_cuongtu_mp(), matGlocomObj.getKetmac_cuongtu_mt(), matGlocomObj.getKetmac_seocu_mp(),
                    matGlocomObj.getKetmac_seocu_mp_vitri(), matGlocomObj.getKetmac_seocu_mt(), matGlocomObj.getKetmac_seocu_mt_vitri(), matGlocomObj.getKetmac_bongdo_mp(), matGlocomObj.getKetmac_bongdo_mt(),
                    matGlocomObj.getGiacmac_trongsuot_mp(), matGlocomObj.getGiacmac_trongsuot_mt(), matGlocomObj.getGiacmac_phune_mp(), matGlocomObj.getGiacmac_phunet_mp_mucdo(),
                    matGlocomObj.getGiacmac_phune_mt(), matGlocomObj.getGiacmac_phunet_mt_mucdo(), matGlocomObj.getGiacmac_dodaygiacmac_mp(), matGlocomObj.getGiacmac_dodaygiacmac_mt(),
                    matGlocomObj.getGiacmac_tua_mp(), matGlocomObj.getGiacmac_tua_mt(), matGlocomObj.getGiacmac_duongkinh_mp(), matGlocomObj.getGiacmac_duongkinh_mt(), matGlocomObj.getCungmac_danloi_mp(), matGlocomObj.getCungmac_danloi_mt(),
                    matGlocomObj.getCungmac_seomo_mp(), matGlocomObj.getCungmac_seomo_mt(), matGlocomObj.getCungmac_seomo_mp_vitri(), matGlocomObj.getCungmac_seomo_mt_vitri(),
                    matGlocomObj.getTienphong_dosau_mp(), matGlocomObj.getTienphong_dosau_mt(), matGlocomObj.getTienphong_herick_mp(), matGlocomObj.getTienphong_herick_mt(),
                    matGlocomObj.getHinhanh_goctienphong_mp(), matGlocomObj.getHinhanh_goctienphong_mt(), matGlocomObj.getGoctienphong_mp(), matGlocomObj.getGoctienphong_mt(), matGlocomObj.getGoctienphong_khac_mp(), matGlocomObj.getGoctienphong_khac_mt(), matGlocomObj.getMongmat_mausac_mp(),
                    matGlocomObj.getMongmat_mausac_mt(), matGlocomObj.getMongmat_tinhtrang_mp(), matGlocomObj.getMongmat_tinhtrang_mucdo_mp(), matGlocomObj.getMongmat_tinhtrang_mt(),
                    matGlocomObj.getMongmat_tinhtrang_mucdo_mt(), matGlocomObj.getMongmat_tanmach_mp(), matGlocomObj.getMongmat_tanmach_mt(), matGlocomObj.getDongtu_mp(), matGlocomObj.getDongtu_mt(),
                    matGlocomObj.getDongtu_duongkinh_mp(), matGlocomObj.getDongtu_duongkinh_mt(), matGlocomObj.getDongtu_sacto_mp(), matGlocomObj.getDongtu_sacto_mt(), matGlocomObj.getDongtu_phanxa_mp(),
                    matGlocomObj.getDongtu_phanxa_mt(), matGlocomObj.getThethuytinh_mp(), matGlocomObj.getThethuytinh_mt(), matGlocomObj.getDichkinh_mp(), matGlocomObj.getDichkinh_mt(),
                    matGlocomObj.getDaymat_vongmac_mp(), matGlocomObj.getDaymat_vongmac_mt(), matGlocomObj.getDaymat_hoangdiem_mp(),
                    matGlocomObj.getDaymat_hoangdiem_mt(), matGlocomObj.getDaymat_tanmach_mp(), matGlocomObj.getDaymat_tanmach_mt(), matGlocomObj.getDaymat_xuathuyet_mp(), matGlocomObj.getDaymat_xuathuyet_mt(),
                    matGlocomObj.getDiathigiac_vientk_mp(), matGlocomObj.getDiathigiac_vientk_vt_mp(), matGlocomObj.getDiathigiac_vientk_mt(), matGlocomObj.getDiathigiac_vientk_vt_mt(), matGlocomObj.getDiathigiac_mausac_mp(), matGlocomObj.getDiathigiac_mausac_mt(),
                    matGlocomObj.getDiathigiac_cd_mp(), matGlocomObj.getDiathigiac_cd_mt(), matGlocomObj.getDiathigiac_machmau_mp(), matGlocomObj.getDiathigiac_machmau_mt(),
                    matGlocomObj.getDiathigiac_xuathuyet_mp(), matGlocomObj.getDiathigiac_xuathuyet_mt(), matGlocomObj.getDiathigiac_teocanhgai_mp(), matGlocomObj.getDiathigiac_teocanhgai_mt(),
                    matGlocomObj.getVannhanlac_mp(), matGlocomObj.getVannhanlac_mt(), matGlocomObj.getNhancau_mp(), matGlocomObj.getNhancau_mt(), matGlocomObj.getHocmat_mp(), matGlocomObj.getHocmat_mt(),
                    matGlocomObj.getHuyetap(), matGlocomObj.getNhietdo(), matGlocomObj.getMach(), matGlocomObj.getNoitiet(), matGlocomObj.getNoitiet_tenbenh(), matGlocomObj.getTttk(),
                    matGlocomObj.getTttk_tenbenh(), matGlocomObj.getTuanhoan(), matGlocomObj.getTuanhoan_tenbenh(), matGlocomObj.getHohap(), matGlocomObj.getHohap_tenbenh(), matGlocomObj.getTieuhoa(),
                    matGlocomObj.getTieuhoa_tenbenh(), matGlocomObj.getCxkhop(), matGlocomObj.getCxkhop_tenbenh(), matGlocomObj.getTietnieu(), matGlocomObj.getTietnieu_tenbenh(), matGlocomObj.getToanthankhac(),
                    matGlocomObj.getCls(), matGlocomObj.getBenhchinh_mp(), matGlocomObj.getBenhchinh_mt(), matGlocomObj.getBenhphu_mp(), matGlocomObj.getBenhphu_mt(),
                    matGlocomObj.getBenhtoanthan(), matGlocomObj.getChidinhdieutri(), matGlocomObj.getTienluong(), matGlocomObj.getNgay(), matGlocomObj.getThang(), matGlocomObj.getNam(), matGlocomObj.getBacsilambenhan());

            map.put("SUCCESS", vreturn);
            map.put("ERRO", "0");
        }catch (Exception e){
            map.put("ERRO", e.getMessage());

        }
        return map;
    }
    @RequestMapping(value = "/BenhAn_MatChanThuong_Update", produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map BenhAn_MatChanThuong_Update(HttpServletRequest request, HttpSession session) {
        long vreturn = -1;
        String DVTT = L2Utils.getDvtt(session);
        BenhAnNoiTruMatChanThuongObj matChanThuongObj = new BenhAnNoiTruMatChanThuongObj(request);
        Map map = new HashMap();
        try {
            vreturn = voBenhAnNoiTruDAO.upd_benhannoitru_matchanthuong(Integer.parseInt(matChanThuongObj.getId()),DVTT,
                    matChanThuongObj.getLydovaovien(),matChanThuongObj.getNgaycuabenh(),matChanThuongObj.getQuatrinhbenhly(),matChanThuongObj.getTiensubanthanmat(),matChanThuongObj.getTiensubanthantoanthan(),matChanThuongObj.getTiensugiadinh(),
                    matChanThuongObj.getThiluckhongkinhmp(),matChanThuongObj.getThiluckhongkinhmt(),matChanThuongObj.getThiluccokinhmp(),matChanThuongObj.getThiluccokinhmt(),matChanThuongObj.getThitruongmp(),matChanThuongObj.getThitruongmt(),
                    matChanThuongObj.getNhanapmp(),matChanThuongObj.getNhanapmt(),matChanThuongObj.getMimatmp(),matChanThuongObj.getMimatdomp(),matChanThuongObj.getMimatvitrimp(),matChanThuongObj.getMimatseomimp(),matChanThuongObj.getMimatkhacmp(),matChanThuongObj.getMimatmt(),
                    matChanThuongObj.getMimatdomt(),matChanThuongObj.getMimatvitrimt(),matChanThuongObj.getMimatseomimt(),matChanThuongObj.getMimatkhacmt(),matChanThuongObj.getKmmp(),matChanThuongObj.getKmxuathuyetmp(),matChanThuongObj.getKmrachkmmp(),matChanThuongObj.getKmthieumaump(),
                    matChanThuongObj.getKmtonthuongmp(),matChanThuongObj.getKmmt(),matChanThuongObj.getKmxuathuyetmt(),matChanThuongObj.getKmrachkmmt(),matChanThuongObj.getKmthieumaumt(),matChanThuongObj.getKmtonthuongmt(),
                    matChanThuongObj.getHinhanhtonthuongmp(),matChanThuongObj.getHinhanhtonthuongmt(),matChanThuongObj.getGiacmacmp(),matChanThuongObj.getGiacmacseomp(),matChanThuongObj.getGiacmacdivatmp(),matChanThuongObj.getGiacmackichthuocmp(),
                    matChanThuongObj.getGiacmacvitrimp(),matChanThuongObj.getGiacmackhacmp(),matChanThuongObj.getGiacmacmt(),matChanThuongObj.getGiacmacseomt(),matChanThuongObj.getGiacmacdivatmt(),matChanThuongObj.getGiacmackichthuocmt(),
                    matChanThuongObj.getGiacmacvitrimt(),matChanThuongObj.getGiacmackhacmt(),
                    matChanThuongObj.getCungmacmp(),matChanThuongObj.getCungmackichthuocmp(),matChanThuongObj.getCungmacvitrimp(),matChanThuongObj.getCungmackhacmp(),matChanThuongObj.getCungmacmt(),matChanThuongObj.getCungmackichthuocmt(),
                    matChanThuongObj.getCungmacvitrimt(),matChanThuongObj.getCungmackhacmt(),
                    matChanThuongObj.getTienphongmp(),matChanThuongObj.getTienphongsaump(),matChanThuongObj.getTienphongmump(),matChanThuongObj.getTienphongxuattietmp(),matChanThuongObj.getTienphongtyndallmp(),matChanThuongObj.getTienphongmucdomp(),matChanThuongObj.getTienphongdivatmp(),
                    matChanThuongObj.getTienphongkhacmp(),matChanThuongObj.getTienphongmt(),matChanThuongObj.getTienphongsaumt(),matChanThuongObj.getTienphongmumt(),matChanThuongObj.getTienphongxuattietmt(),matChanThuongObj.getTienphongtyndallmt(),matChanThuongObj.getTienphongmucdomt(),
                    matChanThuongObj.getTienphongdivatmt(),matChanThuongObj.getTienphongkhacmt(),
                    matChanThuongObj.getMongmatmp(),matChanThuongObj.getMongmatdutchanmp(),matChanThuongObj.getMongmatmatmp(),matChanThuongObj.getMongmatthungmp(),matChanThuongObj.getMongmatmt(),matChanThuongObj.getMongmatdutchanmt(),
                    matChanThuongObj.getMongmatmatmt(),matChanThuongObj.getMongmatthungmt(),
                    matChanThuongObj.getDongtukichthuocmp(),matChanThuongObj.getDongtump(),matChanThuongObj.getDongtuvtmp(),matChanThuongObj.getDongtuanhmp(),matChanThuongObj.getDongtukichthuocmt(),matChanThuongObj.getDongtumt(),
                    matChanThuongObj.getDongtuvtmt(),matChanThuongObj.getDongtuanhmt(),matChanThuongObj.getTttmp(),matChanThuongObj.getTttiolmp(),matChanThuongObj.getTttkhacmp(),matChanThuongObj.getTttmt(),
                    matChanThuongObj.getTttiolmt(),matChanThuongObj.getTttkhacmt(),matChanThuongObj.getDichkinhmp(),matChanThuongObj.getDichkinhducmp(),matChanThuongObj.getDichkinhxuathuyetmp(),matChanThuongObj.getDichkinhkhacmp(),matChanThuongObj.getDichkinhmt(),matChanThuongObj.getDichkinhducmt(),matChanThuongObj.getDichkinhxuathuyetmp(),matChanThuongObj.getDichkinhkhacmt(),
                    matChanThuongObj.getVongmacmp(),matChanThuongObj.getVongmacmachmp(),matChanThuongObj.getVongmacdiathimp(),matChanThuongObj.getVongmacphump(),matChanThuongObj.getVongmacxuathuyetmp(),matChanThuongObj.getVongmacmucdomp(),
                    matChanThuongObj.getVongmacslmp(),matChanThuongObj.getVongmacvtrachmp(),matChanThuongObj.getVongmachinhthaimp(),matChanThuongObj.getVongmackichthuocmp(),matChanThuongObj.getVongmacvtmp(),matChanThuongObj.getVongmackhacmp(),
                    matChanThuongObj.getVongmachinhanhmp(),matChanThuongObj.getVongmacmt(),matChanThuongObj.getVongmacmachmt(),matChanThuongObj.getVongmacdiathimt(),matChanThuongObj.getVongmacphumt(),matChanThuongObj.getVongmacmucdomt(),
                    matChanThuongObj.getVongmacxuathuyetmt(),matChanThuongObj.getVongmacslmt(),matChanThuongObj.getVongmacvtrachmt(),matChanThuongObj.getVongmachinhthaimt(),matChanThuongObj.getVongmackichthuocmt(),matChanThuongObj.getVongmacvtmt(),
                    matChanThuongObj.getVongmackhacmt(),matChanThuongObj.getVongmachinhanhmt(),matChanThuongObj.getHocmatmp(),matChanThuongObj.getHocmatbenhlymp(),matChanThuongObj.getHocmatdivatmp(),matChanThuongObj.getHocmatvnbenhlymp(),
                    matChanThuongObj.getHocmatdoloimp(),matChanThuongObj.getHocmatmt(),matChanThuongObj.getHocmatbenhlymt(),matChanThuongObj.getHocmatdivatmt(),matChanThuongObj.getHocmatvnbenhlymt(),matChanThuongObj.getHocmatdoloimt(),
                    matChanThuongObj.getToanthanchuabenhly(),matChanThuongObj.getMach(),matChanThuongObj.getToanthanbenhly(),matChanThuongObj.getNhietdo(),matChanThuongObj.getHuyetap(),matChanThuongObj.getNhiptho(),matChanThuongObj.getCannang(),
                    matChanThuongObj.getCls(),matChanThuongObj.getTomtat(),matChanThuongObj.getBenhchinh(),matChanThuongObj.getBenhkemtheo(),matChanThuongObj.getPhanbiet(),matChanThuongObj.getTienluong(),matChanThuongObj.getPpdieutri()
                    ,matChanThuongObj.getHocMatVoXuongMp(), matChanThuongObj.getHocMatVoXuongMt());

            map.put("SUCCESS", vreturn);
            map.put("ERRO", "0");
        } catch (Exception e) {
            map.put("ERRO", e.getMessage());

        }
        return map;
    }
    @RequestMapping(value = "/BenhAn_MatDucThuyTinhThe_Update", produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map BenhAn_MatDucThuyTinhThe_Update(HttpServletRequest request, HttpSession session) {
        long vreturn = -1;
        String DVTT = L2Utils.getDvtt(session);
        BenhAnNoiTruMatDucThuyTinhTheObj matDucThuyTinhTheObj = new BenhAnNoiTruMatDucThuyTinhTheObj(request);
        Map map = new HashMap();
        try {
            vreturn = voBenhAnNoiTruDAO.upd_benhannoitru_matducthuytinhthe(Integer.parseInt(matDucThuyTinhTheObj.getId()),DVTT,
                    matDucThuyTinhTheObj.getLydovaovien(),matDucThuyTinhTheObj.getLydovaovienngay(),
                    matDucThuyTinhTheObj.getHoibenh(),matDucThuyTinhTheObj.getQuatrinhbenhly(),
                    matDucThuyTinhTheObj.getTiensubenhbanthan(),matDucThuyTinhTheObj.getTsdiung(),
                    matDucThuyTinhTheObj.getTsthuocla(),matDucThuyTinhTheObj.getTsthuoclathoigian(),
                    matDucThuyTinhTheObj.getTsmatuy(),matDucThuyTinhTheObj.getTsmatuythoigian(),
                    matDucThuyTinhTheObj.getTsthuoclao(),matDucThuyTinhTheObj.getTsthuoclaothoigian(),
                    matDucThuyTinhTheObj.getTsruoubia(),matDucThuyTinhTheObj.getTsruoubiathoigian(),
                    matDucThuyTinhTheObj.getTskhac(),matDucThuyTinhTheObj.getTskhacthoigian(),
                    matDucThuyTinhTheObj.getTsgiadinh(),
                    matDucThuyTinhTheObj.getThiluckhongkinhmp(),matDucThuyTinhTheObj.getThiluckhongkinhmt(),
                    matDucThuyTinhTheObj.getNhanapmp(),matDucThuyTinhTheObj.getNhanapmt(),
                    matDucThuyTinhTheObj.getThiluccokinhmp(),matDucThuyTinhTheObj.getThiluccokinhmt(),
                    matDucThuyTinhTheObj.getThitruongmp(),matDucThuyTinhTheObj.getThitruongmt(),
                    matDucThuyTinhTheObj.getLedaomp(),matDucThuyTinhTheObj.getLedaomt(),
                    matDucThuyTinhTheObj.getMimatmp(),matDucThuyTinhTheObj.getMimatmt(),
                    matDucThuyTinhTheObj.getKetmacmp(),matDucThuyTinhTheObj.getKetmacmt(),
                    matDucThuyTinhTheObj.getTinhhinhmathotmp(),matDucThuyTinhTheObj.getTinhhinhmathotmt(),
                    matDucThuyTinhTheObj.getGiacmacmp(),matDucThuyTinhTheObj.getGiacmacmt(),
                    matDucThuyTinhTheObj.getCungmacmp(),matDucThuyTinhTheObj.getCungmacmt(),
                    matDucThuyTinhTheObj.getHinhanhtonthuongmp(),matDucThuyTinhTheObj.getHinhanhtonthuongmt(),
                    matDucThuyTinhTheObj.getTienphongmp(),matDucThuyTinhTheObj.getTienphongmt(),
                    matDucThuyTinhTheObj.getMongmatmp(),matDucThuyTinhTheObj.getMongmatmt(),
                    matDucThuyTinhTheObj.getDongtuphanxamp(),matDucThuyTinhTheObj.getDongtuphanxamt(),
                    matDucThuyTinhTheObj.getThuytinhthemp(),matDucThuyTinhTheObj.getThuytinhthemt(),
                    matDucThuyTinhTheObj.getThuytinhdichmp(),matDucThuyTinhTheObj.getThuytinhdichmt(),
                    matDucThuyTinhTheObj.getSoianhdongtump(),matDucThuyTinhTheObj.getSoianhdongtumt(),
                    matDucThuyTinhTheObj.getTinhhinhnhancaump(),matDucThuyTinhTheObj.getTinhhinhnhancaumt(),
                    matDucThuyTinhTheObj.getHocmatmp(),matDucThuyTinhTheObj.getHocmatmt(),
                    matDucThuyTinhTheObj.getDaymatmp(),matDucThuyTinhTheObj.getDaymatmt(),
                    matDucThuyTinhTheObj.getHinhanhdaymatmp(),matDucThuyTinhTheObj.getHinhanhdaymatmt(),
                    matDucThuyTinhTheObj.getKhambenhtoanthan(),
                    matDucThuyTinhTheObj.getMach(),matDucThuyTinhTheObj.getNhietdo(),matDucThuyTinhTheObj.getHuyetap(),matDucThuyTinhTheObj.getNhiptho(),matDucThuyTinhTheObj.getCannang(),
                    matDucThuyTinhTheObj.getNoitiet(),matDucThuyTinhTheObj.getTamthanthankinh(),
                    matDucThuyTinhTheObj.getTuanhoan(),matDucThuyTinhTheObj.getHohap(),
                    matDucThuyTinhTheObj.getTieuhoa(),matDucThuyTinhTheObj.getCoxuongkhop(),
                    matDucThuyTinhTheObj.getTietnieusinhduc(),matDucThuyTinhTheObj.getCoquankhac(),
                    matDucThuyTinhTheObj.getXetnghiemcls(),matDucThuyTinhTheObj.getTomtatbenhan(),
                    matDucThuyTinhTheObj.getBenhchinh(),matDucThuyTinhTheObj.getBenhkemtheo(),
                    matDucThuyTinhTheObj.getPhanbiet(),matDucThuyTinhTheObj.getTienluong(),matDucThuyTinhTheObj.getHuongdieutri()
                    );

            map.put("SUCCESS", vreturn);
            map.put("ERRO", "0");
        } catch (Exception e) {
            map.put("ERRO", e.getMessage());

        }
        return map;
    }

    public @ResponseBody
    @RequestMapping(value = "/VBA_LAYTENBACSI", method = RequestMethod.GET, produces = "text/html; charset=utf-8")
    String VBA_LAYTENBACSI(HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        int ID = Integer.parseInt(request.getParameter("ID"));
        return this.voBenhAnNoiTruDAO.VBA_LAYTENBACSI(dvtt, ID);

    }

    @RequestMapping(value = "/LuuThongTin_Trang2_BenhAnMau")
    public @ResponseBody
    String LuuThongTin_Trang2_BenhAnMau(HttpSession session, HttpServletRequest request){
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        String userId = session.getAttribute("Sess_UserID") != null ? session.getAttribute("Sess_UserID").toString() : "";
        String Phongsd = session.getAttribute("Sess_PhongBan") != null ? session.getAttribute("Sess_PhongBan").toString() : "";
        String ID = request.getParameter("ID");
        String LOAIBA   = request.getParameter("LOAIBA");
        String MAIDHSMAU = request.getParameter("MAIDHSMAU");
        String TEN   = request.getParameter("TEN");
        String CACHLUU = request.getParameter("CACHLUU");

        return voBenhAnNoiTruDAO.LuuThongTin_Trang2_BenhAnMau(dvtt,userId,Phongsd,ID,LOAIBA,MAIDHSMAU,TEN,CACHLUU);
    }

    @RequestMapping(value = "/LuuThongTin_Trang3_BenhAnMau")
    public @ResponseBody
    String LuuThongTinTrang3BenhAnMau(HttpSession session, HttpServletRequest request){
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        String userId = session.getAttribute("Sess_UserID") != null ? session.getAttribute("Sess_UserID").toString() : "";
        String Phongsd = session.getAttribute("Sess_PhongBan") != null ? session.getAttribute("Sess_PhongBan").toString() : "";
        String ID = request.getParameter("ID");
        String LOAIBA   = request.getParameter("LOAIBA");
        String MAIDHSMAU = request.getParameter("MAIDHSMAU");
        String TEN   = request.getParameter("TEN");
        String CACHLUU = request.getParameter("CACHLUU");

        return voBenhAnNoiTruDAO.LuuThongTin_Trang3_BenhAnMau(dvtt,userId,Phongsd,ID,LOAIBA,MAIDHSMAU,TEN,CACHLUU);
    }

    @RequestMapping(value = "/LoadDanhSach_Trang2_BenhAnMau", produces = "application/json; charset=utf-8")
    public @ResponseBody
    List LoadDanhSach_Trang2_BenhAnMau(@RequestParam(value = "loaibenhan") String loaibenhan, HttpSession session) {
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        String userId = session.getAttribute("Sess_UserID") != null ? session.getAttribute("Sess_UserID").toString() : "";
        String Phongsd = session.getAttribute("Sess_PhongBan") != null ? session.getAttribute("Sess_PhongBan").toString() : "";
        List list = voBenhAnNoiTruDAO.LoadDanhSach_Trang2_BenhAnMau(dvtt, userId, Phongsd, loaibenhan);
        return list;
    }

    @RequestMapping(value = "/LoadDanhSach_Trang3_BenhAnMau", produces = "application/json; charset=utf-8")
    public @ResponseBody
    List LoadDanhSach_Trang3_BenhAnMau(@RequestParam(value = "loaibenhan") String loaibenhan, HttpSession session) {
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        String userId = session.getAttribute("Sess_UserID") != null ? session.getAttribute("Sess_UserID").toString() : "";
        String Phongsd = session.getAttribute("Sess_PhongBan") != null ? session.getAttribute("Sess_PhongBan").toString() : "";
        List list = voBenhAnNoiTruDAO.LoadDanhSach_Trang3_BenhAnMau(dvtt, userId, Phongsd, loaibenhan);
        return list;
    }

    public @ResponseBody
    @RequestMapping(value = "/update-benhan-ngoaitruyhct1941", method = RequestMethod.POST)
    Map BenhAn_YHCTNgoaiTru1941Insert(HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        int vreturn = -1;
        String dvtt = L2Utils.getDvtt(session);
        String maKhoa = L2Utils.getMaPhongBan(session);
        String idUser = L2Utils.getMaUser(session);
        String ID = request.getParameter("ID");
        String LYDOVAOVIEN = request.getParameter("LYDOVAOVIEN");
        String BENHSU = request.getParameter("BENHSU");
        String TIENSUBANTHAN = request.getParameter("TIENSUBANTHAN");
        String TIENSU1 = request.getParameter("TIENSU1");
        String TIENSU2 = request.getParameter("TIENSU2");
        String TIENSU3 = request.getParameter("TIENSU3");
        String TIENSU4 = request.getParameter("TIENSU4");
        String MOTABANTHAN = request.getParameter("MOTABANTHAN");
        String TIENSUBENHTAT = request.getParameter("TIENSUBENHTAT");
        String TIENSUGIADINH = request.getParameter("TIENSUGIADINH");
        String KHAMTOANTHAN = request.getParameter("KHAMTOANTHAN");
        String MACH = request.getParameter("MACH");
        String NHIETDO = request.getParameter("NHIETDO");
        String HUYETAPTREN = request.getParameter("HUYETAPTREN");
        String HUYETAPDUOI = request.getParameter("HUYETAPDUOI");
        String NHIPTHO = request.getParameter("NHIPTHO");
        String CANNANG = request.getParameter("CANNANG");
        String CHIEUCAO = request.getParameter("CHIEUCAO");
        String BMI = request.getParameter("BMI");
        String TUANHOAN = request.getParameter("TUANHOAN");
        String HOHAP = request.getParameter("HOHAP");
        String TIEUHOA = request.getParameter("TIEUHOA");
        String TIETNIEUSINHDUC = request.getParameter("TIETNIEUSINHDUC");
        String THANKINH = request.getParameter("THANKINH");
        String XUONGKHOP = request.getParameter("XUONGKHOP");
        String TMH = request.getParameter("TMH");
        String RHM = request.getParameter("RHM");
        String MAT = request.getParameter("MAT");
        String NOITIET = request.getParameter("NOITIET");
        String CLS = request.getParameter("CLS");
        String BENHCHINH = request.getParameter("BENHCHINH");
        String BENHPHU = request.getParameter("BENHPHU");
        String PHANBIET = request.getParameter("PHANBIET");
        String MOTAVONGCHAN = request.getParameter("MOTAVONGCHAN");
        String MOTAVANCHAN = request.getParameter("MOTAVANCHAN");
        String MOTAKHACVANCHAN = request.getParameter("MOTAKHACVANCHAN");
        String XUCCHAN = request.getParameter("XUCCHAN");
        String MACHTAYTRAI = request.getParameter("MACHTAYTRAI");
        String MACHTAYPHAI = request.getParameter("MACHTAYPHAI");
        String TOMTATTUCHAN = request.getParameter("TOMTATTUCHAN");
        String BIENCHUNGLUANTRI = request.getParameter("BIENCHUNGLUANTRI");
        String BENHDANH = request.getParameter("BENHDANH");
        String BATCUONG = request.getParameter("BATCUONG");
        String NGUYENNHAN = request.getParameter("NGUYENNHAN");
        String TANGPHU = request.getParameter("TANGPHU");
        String KINHMACH = request.getParameter("KINHMACH");
        String DINHVIBENH = request.getParameter("DINHVIBENH");
        String PHAPDIEUTRI = request.getParameter("PHAPDIEUTRI");
        String PHUONGDUOC = request.getParameter("PHUONGDUOC");
        String PPKHONGDUNGTHUOC = request.getParameter("PPKHONGDUNGTHUOC");
        String YHOCHIENDAI = request.getParameter("YHOCHIENDAI");
        String DUHAU = request.getParameter("DUHAU");
        String MABENHPHU1 = request.getParameter("MABENHPHU1");
        String TENBENHPHU1 = request.getParameter("TENBENHPHU1");
        String MABENHPHU2 = request.getParameter("MABENHPHU2");
        String TENBENHPHU2 = request.getParameter("TENBENHPHU2");
        String TENBENHCHINH = request.getParameter("TENBENHCHINH");

        double canNang = (CANNANG == null || CANNANG.trim().equals("")) ? 0 : Float.parseFloat(CANNANG);
        double chieuCao = (CHIEUCAO == null || CHIEUCAO.trim().equals("") || CHIEUCAO.trim().equals("0")) ? 1 : (Float.parseFloat(CHIEUCAO) / 100);
        double bmi = canNang / (chieuCao * chieuCao);
        double bmilamtron = Math.round(bmi * 100.0) / 100.0;
        Map map = new HashMap();
        try {
            String sql = "call HIS_MANAGER.UPDATE_BENHAN_YHCT_NGTR1941(" +
                    "?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?)" +
                    "#l,s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
            vreturn = jdbcTemplate.update(sql, new Object[]{
                    ID,LYDOVAOVIEN,BENHSU,TIENSUBANTHAN,TIENSU1
                    ,TIENSU2,TIENSU3,TIENSU4,MOTABANTHAN,TIENSUBENHTAT
                    ,TIENSUGIADINH,KHAMTOANTHAN,MACH,NHIETDO,HUYETAPTREN
                    ,HUYETAPDUOI,NHIPTHO,CANNANG,CHIEUCAO,bmilamtron
                    ,TUANHOAN,HOHAP,TIEUHOA,TIETNIEUSINHDUC,THANKINH
                    ,XUONGKHOP,TMH,RHM,MAT,NOITIET
                    ,CLS,BENHCHINH,BENHPHU,PHANBIET,MOTAVONGCHAN
                    ,MOTAVANCHAN,MOTAKHACVANCHAN,XUCCHAN,MACHTAYTRAI,MACHTAYPHAI
                    ,TOMTATTUCHAN,BIENCHUNGLUANTRI,BENHDANH,BATCUONG,NGUYENNHAN
                    ,TANGPHU,KINHMACH,DINHVIBENH,PHAPDIEUTRI,PHUONGDUOC
                    ,PPKHONGDUNGTHUOC,YHOCHIENDAI,DUHAU,MABENHPHU1,TENBENHPHU1
                    ,MABENHPHU2,TENBENHPHU2,TENBENHCHINH
            });
            map.put("SUCCESS", vreturn);
            map.put("ERRO", "0");
        } catch (Exception e) {
            map.put("ERRO", e.getMessage());

        }
        return map;
    }
    @RequestMapping(value = "/update-benhan-phcn-nhi", produces = "application/json; charset=utf-8", method = RequestMethod.POST)
    public @ResponseBody
    Map updatePHCNnhi(HttpServletRequest request, HttpSession session) {
        Map map = new HashMap();
        try {
            NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSourceMNG);
            MapSqlParameterSource params = new MapSqlParameterSource()
                    .addValue("ttPHCNnhi", request.getParameter("benhAnPhucHoiChucNangNhi"))
                    .addValue("iD", request.getParameter("ID"))
                    ;
            int update = namedParameterJdbcTemplate.update("UPDATE HIS_MANAGER.VBA_PHCN_NHI SET THONGTIN_PHCN_NHI_CLOB = :ttPHCNnhi WHERE ID = :iD", params);
            map.put("SUCCESS", update);
        }catch (Exception e){
            map.put("SUCCESS", 0);
            map.put("MESSAGE", e.getMessage());
        }
        return map;
    }
    @RequestMapping(value = "/update-benhan-phcn-bant", produces = "application/json; charset=utf-8", method = RequestMethod.POST)
    public @ResponseBody
    Map updatePHCNbant(HttpServletRequest request, HttpSession session) {
        Map map = new HashMap();
        try {
            NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSourceMNG);
            MapSqlParameterSource params = new MapSqlParameterSource()
                    .addValue("iD", request.getParameter("ID"))
                    .addValue("benhAnPhucHoiChucNangBant", request.getParameter("benhAnPhucHoiChucNangBant"))
                    .addValue("ngayKhamBenh", request.getParameter("ngayKhamBenh"))
                    ;
            int update = namedParameterJdbcTemplate.update("UPDATE HIS_MANAGER.VBA_PHCN_BANT " +
                    "SET THONGTIN_PHCN_BANT_CLOB = :benhAnPhucHoiChucNangBant,  NGAY_KHAMBENH = TO_DATE(:ngayKhamBenh, 'dd/MM/yyyy')" +
                    "WHERE ID = :iD", params);
            map.put("SUCCESS", update);
        }catch (Exception e){
            map.put("SUCCESS", 0);
            map.put("MESSAGE", e.getMessage());
        }
        return map;
    }

    @RequestMapping(value = "/BenhAn_MatLac_Update", produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map BenhAn_MatLac_Update(HttpServletRequest request, HttpSession session) {
        int vreturn = -1;
        String ID = request.getParameter("ID");
        String LYDOVAOVIEN = request.getParameter("LYDOVAOVIEN");
        String NGUYENNHAN = request.getParameter("NGUYENNHAN");
        String TUBAOGIO = request.getParameter("TUBAOGIO");
        String TRIEUCHUNGCHINH = request.getParameter("TRIEUCHUNGCHINH");
        String TRIEUCHUNGCHINHKHAC = request.getParameter("TRIEUCHUNGCHINHKHAC");
        String DADIEUTRI = request.getParameter("DADIEUTRI");
        String DADIEUTRITAPNHUOCTHI = request.getParameter("DADIEUTRITAPNHUOCTHI");
        String KETQUATAPNHUOCTHI = request.getParameter("KETQUATAPNHUOCTHI");
        String DADIEUTRIPHAUTHUAT = request.getParameter("DADIEUTRIPHAUTHUAT");
        String KETQUAPHAUTHUAT = request.getParameter("KETQUAPHAUTHUAT");
        String DADIEUTRIPHAUTHUATMONON = request.getParameter("DADIEUTRIPHAUTHUATMONON");
        String DADIEUTRIPHAUTHUATMOGIA = request.getParameter("DADIEUTRIPHAUTHUATMOGIA");
        String TIENSUBENHBANTHAN = request.getParameter("TIENSUBENHBANTHAN");
        String TIENSUBENHBANTHANBENHLY = request.getParameter("TIENSUBENHBANTHANBENHLY");
        String TIENSUBENHGIADINH = request.getParameter("TIENSUBENHGIADINH");
        String TIENSUBENHGIADINHBENHLY = request.getParameter("TIENSUBENHGIADINHBENHLY");
        String THILUCKHONGKINHMP = request.getParameter("THILUCKHONGKINHMP");
        String THILUCKHONGKINHMT = request.getParameter("THILUCKHONGKINHMT");
        String THILUCCOKINHMP = request.getParameter("THILUCCOKINHMP");
        String THILUCCOKINHMT = request.getParameter("THILUCCOKINHMT");
        String KHUCXAMAYTRUOCMP = request.getParameter("KHUCXAMAYTRUOCMP");
        String KHUCXAMAYTRUOCMT = request.getParameter("KHUCXAMAYTRUOCMT");
        String KHUCXAMAYSAUMP = request.getParameter("KHUCXAMAYSAUMP");
        String KHUCXAMAYSAUMT = request.getParameter("KHUCXAMAYSAUMT");
        String SOIBONGDONGTU = request.getParameter("SOIBONGDONGTU");
        String VANNHANNGOAILAI = request.getParameter("VANNHANNGOAILAI");
        String VANNHANNOITAIMP = request.getParameter("VANNHANNOITAIMP");
        String VANNHANNOITAIMPBENHLY = request.getParameter("VANNHANNOITAIMPBENHLY");
        String VANNHANNOITAIMT = request.getParameter("VANNHANNOITAIMT");
        String VANNHANNOITAIMTBENHLY = request.getParameter("VANNHANNOITAIMTBENHLY");
        String DIEMCANQUITU = request.getParameter("DIEMCANQUITU");
        String DIEMCANQUITUBINHTHUONG = request.getParameter("DIEMCANQUITUBINHTHUONG");
        String DIEMCANQUITUBENHLY = request.getParameter("DIEMCANQUITUBENHLY");
        String RUNGGIATNHANCAU = request.getParameter("RUNGGIATNHANCAU");
        String RUNGGIATNHANCAUBENHLY = request.getParameter("RUNGGIATNHANCAUBENHLY");
        String RUNGGIATNHANCAUKIEU = request.getParameter("RUNGGIATNHANCAUKIEU");
        String GOCHAM = request.getParameter("GOCHAM");
        String THUNGHIEMCHEMAT = request.getParameter("THUNGHIEMCHEMAT");
        String HINHTHAIVATINHCHATLAC = request.getParameter("HINHTHAIVATINHCHATLAC");
        String DOLACHIRSCHBERGTRUOCATROPINE = request.getParameter("DOLACHIRSCHBERGTRUOCATROPINE");
        String DOLACHIRSCHBERGSAUATROPINE = request.getParameter("DOLACHIRSCHBERGSAUATROPINE");
        String DOLACLANGKINHTRUOCATROPINE = request.getParameter("DOLACLANGKINHTRUOCATROPINE");
        String DOLACLANGKINHSAUATROPINE = request.getParameter("DOLACLANGKINHSAUATROPINE");
        String DOLACNHINGAN = request.getParameter("DOLACNHINGAN");
        String DOLACNHINXA = request.getParameter("DOLACNHINXA");
        String DOLACNHINLEN = request.getParameter("DOLACNHINLEN");
        String DOLACNHINXUONG = request.getParameter("DOLACNHINXUONG");
        String HOICHUNG = request.getParameter("HOICHUNG");
        String SYNOPTOPHOREKHACHQUAN = request.getParameter("SYNOPTOPHOREKHACHQUAN");
        String SYNOPTOPHORECHUQUAN = request.getParameter("SYNOPTOPHORECHUQUAN");
        String TINHTRANGTHIGIACHAIMAT = request.getParameter("TINHTRANGTHIGIACHAIMAT");
        String TINHTRANGTHIGIACHAIMATPHUTHI = request.getParameter("TINHTRANGTHIGIACHAIMATPHUTHI");
        String BIENDOHOPTHI = request.getParameter("BIENDOHOPTHI");
        String TUONGUNGVONGMAC = request.getParameter("TUONGUNGVONGMAC");
        String SONGTHI = request.getParameter("SONGTHI");
        String SONGTHICO = request.getParameter("SONGTHICO");
        String TUTHEBUTRU = request.getParameter("TUTHEBUTRU");
        String TUTHEBUTRUCO = request.getParameter("TUTHEBUTRUCO");
        String MIMATMP = request.getParameter("MIMATMP");
        String MIMATMT = request.getParameter("MIMATMT");
        String SUPMIMP = request.getParameter("SUPMIMP");
        String SUPMIMT = request.getParameter("SUPMIMT");
        String EPICANTHUSMP = request.getParameter("EPICANTHUSMP");
        String EPICANTHUSMT = request.getParameter("EPICANTHUSMT");
        String COMIMP = request.getParameter("COMIMP");
        String COMIMT = request.getParameter("COMIMT");
        String MARCUSGUNNMP = request.getParameter("MARCUSGUNNMP");
        String MARCUSGUNNMT = request.getParameter("MARCUSGUNNMT");
        String DAUHIEUBELLMP = request.getParameter("DAUHIEUBELLMP");
        String DAUHIEUBELLMT = request.getParameter("DAUHIEUBELLMT");
        String MIMATKHACMP = request.getParameter("MIMATKHACMP");
        String MIMATKHACMT = request.getParameter("MIMATKHACMT");
        String KETMACMP = request.getParameter("KETMACMP");
        String KETMACMT = request.getParameter("KETMACMT");
        String KETMACBENHLYMP = request.getParameter("KETMACBENHLYMP");
        String KETMACBENHLYMT = request.getParameter("KETMACBENHLYMT");
        String PHANTRUOCNHANCAUMP = request.getParameter("PHANTRUOCNHANCAUMP");
        String PHANTRUOCNHANCAUMT = request.getParameter("PHANTRUOCNHANCAUMT");
        String PHANTRUOCNHANCAUBENHLYMP = request.getParameter("PHANTRUOCNHANCAUBENHLYMP");
        String PHANTRUOCNHANCAUBENHLYMT = request.getParameter("PHANTRUOCNHANCAUBENHLYMT");
        String PHANSAUNHANCAUMP = request.getParameter("PHANSAUNHANCAUMP");
        String PHANSAUNHANCAUMT = request.getParameter("PHANSAUNHANCAUMT");
        String PHANSAUNHANCAUBENHLYMP = request.getParameter("PHANSAUNHANCAUBENHLYMP");
        String PHANSAUNHANCAUBENHLYMT = request.getParameter("PHANSAUNHANCAUBENHLYMT");
        String DINHTHIMP = request.getParameter("DINHTHIMP");
        String DINHTHIMT = request.getParameter("DINHTHIMT");
        String HUYETAPTREN = request.getParameter("HUYETAPTREN");
        String HUYETAPDUOI = request.getParameter("HUYETAPDUOI");
        String NHIETDO = request.getParameter("NHIETDO");
        String MACH = request.getParameter("MACH");
        String NOITIET = request.getParameter("NOITIET");
        String NOITIETBENHLY = request.getParameter("NOITIETBENHLY");
        String THANKINH = request.getParameter("THANKINH");
        String THANKINHBENHLY = request.getParameter("THANKINHBENHLY");
        String TUANHOAN = request.getParameter("TUANHOAN");
        String TUANHOANBENHLY = request.getParameter("TUANHOANBENHLY");
        String HOHAP = request.getParameter("HOHAP");
        String HOHAPBENHLY = request.getParameter("HOHAPBENHLY");
        String TIEUHOA = request.getParameter("TIEUHOA");
        String TIEUHOABENHLY = request.getParameter("TIEUHOABENHLY");
        String COXUONGKHOP = request.getParameter("COXUONGKHOP");
        String COXUONGKHOPBENHLY = request.getParameter("COXUONGKHOPBENHLY");
        String TIETNIEU = request.getParameter("TIETNIEU");
        String TIETNIEUBENHLY = request.getParameter("TIETNIEUBENHLY");
        String TOANTHANKHAC = request.getParameter("TOANTHANKHAC");
        String CLS = request.getParameter("CLS");
        String BENHCHINH = request.getParameter("BENHCHINH");
        String BENHKEMTHEO = request.getParameter("BENHKEMTHEO");
        String PHANBIET = request.getParameter("PHANBIET");
        String PHUONGPHAPCHINH = request.getParameter("PHUONGPHAPCHINH");
        String CHEDOAN = request.getParameter("CHEDOAN");
        String CHEDOCHAMSOC = request.getParameter("CHEDOCHAMSOC");
        String TIENLUONG = request.getParameter("TIENLUONG");
        String MABENHCHINH = request.getParameter("MABENHCHINH");
        String MABENHKEMTHEO = request.getParameter("MABENHKEMTHEO");
        String TOMTAT_BENHAN = request.getParameter("TOMTAT_BENHAN");

        String BIEUHIEN_BENHLY = request.getParameter("BIEUHIEN_BENHLY");
        String BENHLY = request.getParameter("BENHLY");
        String BENHLY_TEXT = request.getParameter("BENHLY_TEXT");
        String NHIPTHO = request.getParameter("NHIPTHO");
        String CANNANG = request.getParameter("CANNANG");
        String CHIEUCAO = request.getParameter("CHIEUCAO");
        Map map = new HashMap();
        try {
            String sql = "call HIS_MANAGER.UPDATE_BAMATLAC(" +
                    "?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?)" +
                    "#l,s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
            vreturn = jdbcTemplate.update(sql, new Object[]{
                    ID, LYDOVAOVIEN, NGUYENNHAN, TUBAOGIO, TRIEUCHUNGCHINH,
                    TRIEUCHUNGCHINHKHAC, DADIEUTRI, DADIEUTRITAPNHUOCTHI, KETQUATAPNHUOCTHI, DADIEUTRIPHAUTHUAT,
                    KETQUAPHAUTHUAT, DADIEUTRIPHAUTHUATMONON, DADIEUTRIPHAUTHUATMOGIA, TIENSUBENHBANTHAN, TIENSUBENHBANTHANBENHLY,
                    TIENSUBENHGIADINH, TIENSUBENHGIADINHBENHLY, THILUCKHONGKINHMP, THILUCKHONGKINHMT, THILUCCOKINHMP,
                    THILUCCOKINHMT, KHUCXAMAYTRUOCMP, KHUCXAMAYTRUOCMT, KHUCXAMAYSAUMP, KHUCXAMAYSAUMT,
                    SOIBONGDONGTU, VANNHANNGOAILAI, VANNHANNOITAIMP, VANNHANNOITAIMPBENHLY, VANNHANNOITAIMT,
                    VANNHANNOITAIMTBENHLY, DIEMCANQUITU, DIEMCANQUITUBINHTHUONG, DIEMCANQUITUBENHLY, RUNGGIATNHANCAU,
                    RUNGGIATNHANCAUBENHLY, RUNGGIATNHANCAUKIEU, GOCHAM, THUNGHIEMCHEMAT, HINHTHAIVATINHCHATLAC,
                    DOLACHIRSCHBERGTRUOCATROPINE, DOLACHIRSCHBERGSAUATROPINE, DOLACLANGKINHTRUOCATROPINE, DOLACLANGKINHSAUATROPINE, DOLACNHINGAN,
                    DOLACNHINXA, DOLACNHINLEN, DOLACNHINXUONG, HOICHUNG, SYNOPTOPHOREKHACHQUAN,
                    SYNOPTOPHORECHUQUAN, TINHTRANGTHIGIACHAIMAT, TINHTRANGTHIGIACHAIMATPHUTHI, BIENDOHOPTHI, TUONGUNGVONGMAC,
                    SONGTHI, SONGTHICO, TUTHEBUTRU, TUTHEBUTRUCO, MIMATMP,
                    MIMATMT, SUPMIMP, SUPMIMT, EPICANTHUSMP, EPICANTHUSMT,
                    COMIMP, COMIMT, MARCUSGUNNMP, MARCUSGUNNMT, DAUHIEUBELLMP,
                    DAUHIEUBELLMT, MIMATKHACMP, MIMATKHACMT, KETMACMP, KETMACMT,
                    KETMACBENHLYMP, KETMACBENHLYMT, PHANTRUOCNHANCAUMP, PHANTRUOCNHANCAUMT, PHANTRUOCNHANCAUBENHLYMP,
                    PHANTRUOCNHANCAUBENHLYMT, PHANSAUNHANCAUMP, PHANSAUNHANCAUMT, PHANSAUNHANCAUBENHLYMP, PHANSAUNHANCAUBENHLYMT,
                    DINHTHIMP, DINHTHIMT, HUYETAPTREN, HUYETAPDUOI, NHIETDO,
                    MACH, NOITIET, NOITIETBENHLY, THANKINH, THANKINHBENHLY,
                    TUANHOAN, TUANHOANBENHLY, HOHAP, HOHAPBENHLY, TIEUHOA,
                    TIEUHOABENHLY, COXUONGKHOP, COXUONGKHOPBENHLY, TIETNIEU, TIETNIEUBENHLY,
                    TOANTHANKHAC, CLS, BENHCHINH, BENHKEMTHEO, PHANBIET,
                    PHUONGPHAPCHINH, CHEDOAN, CHEDOCHAMSOC, TIENLUONG,MABENHCHINH,
                    MABENHKEMTHEO, TOMTAT_BENHAN, BIEUHIEN_BENHLY,  BENHLY, BENHLY_TEXT, NHIPTHO, CANNANG, CHIEUCAO
            });
            voBenhAnNoiTruDAO.log_update(Integer.parseInt(ID), L2Utils.getDvtt(session), L2Utils.getMaUser(session));
            map.put("SUCCESS", vreturn);
            map.put("ERRO", "0");
        } catch (Exception e) {
            map.put("ERRO", e.getMessage());

        }
        return map;
    }

    @RequestMapping(value = "/BenhAn_BanPhanTruoc_Update", produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map BenhAn_BanPhanTruoc_Update(HttpServletRequest request, HttpSession session) {
        int vreturn = -1;
        String ID = request.getParameter("ID");
        String LYDOVAOVIEN = request.getParameter("LYDOVAOVIEN");
        String THOIGIANXUATHIENBENH = request.getParameter("THOIGIANXUATHIENBENH");
        String NGUYENNHAN = request.getParameter("NGUYENNHAN");
        String CACPHUONGPHAPDADIEUTRI = request.getParameter("CACPHUONGPHAPDADIEUTRI");
        String TIENSUBANTHANTAIMAT = request.getParameter("TIENSUBANTHANTAIMAT");
        String TIENSUBANTHANTOANTHAN = request.getParameter("TIENSUBANTHANTOANTHAN");
        String TIENSUGIADINHBENHMAT = request.getParameter("TIENSUGIADINHBENHMAT");
        String TIENSUGIADINHBENHTOANTHAN = request.getParameter("TIENSUGIADINHBENHTOANTHAN");
        String THILUCKHONGKINHMP = request.getParameter("THILUCKHONGKINHMP");
        String THILUCQUALOMP = request.getParameter("THILUCQUALOMP");
        String THILUCKHONGKINHMT = request.getParameter("THILUCKHONGKINHMT");
        String THILUCQUALOMT = request.getParameter("THILUCQUALOMT");
        String THILUCCOCHINHKINHMP = request.getParameter("THILUCCOCHINHKINHMP");
        String THILUCCOCHINHKINHMT = request.getParameter("THILUCCOCHINHKINHMT");
        String THILUCNHINGANMP = request.getParameter("THILUCNHINGANMP");
        String THILUCNHINGANMT = request.getParameter("THILUCNHINGANMT");
        String NHANAPMP = request.getParameter("NHANAPMP");
        String NHANAPMT = request.getParameter("NHANAPMT");
        String LACVANNHANMP = request.getParameter("LACVANNHANMP");
        String LACVANNHANMT = request.getParameter("LACVANNHANMT");
        String LEDAONUOCTHOATTOTMP = request.getParameter("LEDAONUOCTHOATTOTMP");
        String LEDAONUOCTHOATTOTMT = request.getParameter("LEDAONUOCTHOATTOTMT");
        String LEDAOTRAOLEQUANMP = request.getParameter("LEDAOTRAOLEQUANMP");
        String LEDAOTRAOLEQUANMT = request.getParameter("LEDAOTRAOLEQUANMT");
        String LEDAOTRAOTAICHOMP = request.getParameter("LEDAOTRAOTAICHOMP");
        String LEDAOTRAOTAICHOMT = request.getParameter("LEDAOTRAOTAICHOMT");
        String MIMATTINHTRANGMP = request.getParameter("MIMATTINHTRANGMP");
        String MIMATTINHTRANGMT = request.getParameter("MIMATTINHTRANGMT");
        String MIMATTINHTRANGKHACMP = request.getParameter("MIMATTINHTRANGKHACMP");
        String MIMATTINHTRANGKHACMT = request.getParameter("MIMATTINHTRANGKHACMT");
        String MIMATUMIMP = request.getParameter("MIMATUMIMP");
        String MIMATTINHCHATUMP = request.getParameter("MIMATTINHCHATUMP");
        String MIMATUMIMT = request.getParameter("MIMATUMIMT");
        String MIMATTINHCHATUMT = request.getParameter("MIMATTINHCHATUMT");
        String MIMATVITRIMP = request.getParameter("MIMATVITRIMP");
        String MIMATKICHTHUOCMP = request.getParameter("MIMATKICHTHUOCMP");
        String MIMATVITRIMT = request.getParameter("MIMATVITRIMT");
        String MIMATKICHTHUOCMT = request.getParameter("MIMATKICHTHUOCMT");
        String MIMATQUAMMP = request.getParameter("MIMATQUAMMP");
        String MIMATQUAMMT = request.getParameter("MIMATQUAMMT");
        String MIMATQUAMMITRENMP = request.getParameter("MIMATQUAMMITRENMP");
        String MIMATQUAMMITRENMT = request.getParameter("MIMATQUAMMITRENMT");
        String MIMATQUAMMIDUOIMP = request.getParameter("MIMATQUAMMIDUOIMP");
        String MIMATQUAMMIDUOIMT = request.getParameter("MIMATQUAMMIDUOIMT");
        String HOMIMP = request.getParameter("HOMIMP");
        String HOMIMT = request.getParameter("HOMIMT");
        String TREMIMP = request.getParameter("TREMIMP");
        String TREMIMT = request.getParameter("TREMIMT");
        String MIMATKHUYETMIMP = request.getParameter("MIMATKHUYETMIMP");
        String MIMATKHUYETMIMT = request.getParameter("MIMATKHUYETMIMT");
        String MIMATTUYENBOMIMP = request.getParameter("MIMATTUYENBOMIMP");
        String MIMATTUYENBOMIMT = request.getParameter("MIMATTUYENBOMIMT");
        String MIMATVIEMBOMIMP = request.getParameter("MIMATVIEMBOMIMP");
        String MIMATVIEMBOMIMT = request.getParameter("MIMATVIEMBOMIMT");
        String MIMATTONTHUONGKHACMP = request.getParameter("MIMATTONTHUONGKHACMP");
        String MIMATTONTHUONGKHACMT = request.getParameter("MIMATTONTHUONGKHACMT");
        String KETMACCUONGTUMP = request.getParameter("KETMACCUONGTUMP");
        String KETMACCUONGTUMT = request.getParameter("KETMACCUONGTUMT");
        String KETMACPHUNEMP = request.getParameter("KETMACPHUNEMP");
        String KETMACPHUNEMT = request.getParameter("KETMACPHUNEMT");
        String KETMACNHUMP = request.getParameter("KETMACNHUMP");
        String KETMACNHUMT = request.getParameter("KETMACNHUMT");
        String KETMACTIETTOMUMP = request.getParameter("KETMACTIETTOMUMP");
        String KETMACTIETTOMUMT = request.getParameter("KETMACTIETTOMUMT");
        String KETMACFLUORMP = request.getParameter("KETMACFLUORMP");
        String KETMACFLUORMT = request.getParameter("KETMACFLUORMT");
        String KETMACUKETMACTINHCHATMP = request.getParameter("KETMACUKETMACTINHCHATMP");
        String KETMACUKETMACTINHCHATMT = request.getParameter("KETMACUKETMACTINHCHATMT");
        String KETMACUKETMACVITRIMP = request.getParameter("KETMACUKETMACVITRIMP");
        String KETMACUKETMACVITRIMT = request.getParameter("KETMACUKETMACVITRIMT");
        String KETMACUKETMACKICHTHUOCMP = request.getParameter("KETMACUKETMACKICHTHUOCMP");
        String KETMACUKETMACKICHTHUOCMT = request.getParameter("KETMACUKETMACKICHTHUOCMT");
        String KETMACCUNGDOMP = request.getParameter("KETMACCUNGDOMP");
        String KETMACCUNGDOMT = request.getParameter("KETMACCUNGDOMT");
        String KETMACCHIEUCAOCUACAUDINHMP = request.getParameter("KETMACCHIEUCAOCUACAUDINHMP");
        String KETMACCHIEUCAOCUACAUDINHMT = request.getParameter("KETMACCHIEUCAOCUACAUDINHMT");
        String KETMACDORONGCUACAUDINHMP = request.getParameter("KETMACDORONGCUACAUDINHMP");
        String KETMACDORONGCUACAUDINHMT = request.getParameter("KETMACDORONGCUACAUDINHMT");
        String KETMACTONTHUONGKHACMP = request.getParameter("KETMACTONTHUONGKHACMP");
        String KETMACTONTHUONGKHACMT = request.getParameter("KETMACTONTHUONGKHACMT");
        String GIACMACMP = request.getParameter("GIACMACMP");
        String GIACMACMT = request.getParameter("GIACMACMT");
        String GIACMACKICHTHUOCMP = request.getParameter("GIACMACKICHTHUOCMP");
        String GIACMACKICHTHUOCMT = request.getParameter("GIACMACKICHTHUOCMT");
        String GIACMACHINHDANGMP = request.getParameter("GIACMACHINHDANGMP");
        String GIACMACHINHDANGMT = request.getParameter("GIACMACHINHDANGMT");
        String GIACMACTONTHUONGDANCHAMMP = request.getParameter("GIACMACTONTHUONGDANCHAMMP");
        String GIACMACTONTHUONGDANCHAMMT = request.getParameter("GIACMACTONTHUONGDANCHAMMT");
        String GIACMACPHUBONGBIEUMOMP = request.getParameter("GIACMACPHUBONGBIEUMOMP");
        String GIACMACPHUBONGBIEUMOMT = request.getParameter("GIACMACPHUBONGBIEUMOMT");
        String GIACMACMATBIEUMOMP = request.getParameter("GIACMACMATBIEUMOMP");
        String GIACMACMATBIEUMOMT = request.getParameter("GIACMACMATBIEUMOMT");
        String GIACMACVITRIMATBIEUMOMP = request.getParameter("GIACMACVITRIMATBIEUMOMP");
        String GIACMACVITRIMATBIEUMOMT = request.getParameter("GIACMACVITRIMATBIEUMOMT");
        String GIACMACBOTONTHUONGMP = request.getParameter("GIACMACBOTONTHUONGMP");
        String GIACMACBOTONTHUONGMT = request.getParameter("GIACMACBOTONTHUONGMT");
        String GIACMACTHOAIHOADAIBANGMP = request.getParameter("GIACMACTHOAIHOADAIBANGMP");
        String GIACMACTHOAIHOADAIBANGMT = request.getParameter("GIACMACTHOAIHOADAIBANGMT");
        String GIACMACLANGDONGTHUOCMP = request.getParameter("GIACMACLANGDONGTHUOCMP");
        String GIACMACLANGDONGTHUOCMT = request.getParameter("GIACMACLANGDONGTHUOCMT");
        String GIACMACBIEUMOTONTHUONGKHACMP = request.getParameter("GIACMACBIEUMOTONTHUONGKHACMP");
        String GIACMACBIEUMOTONTHUONGKHACMT = request.getParameter("GIACMACBIEUMOTONTHUONGKHACMT");
        String GIACMACNHUMOPHUMP = request.getParameter("GIACMACNHUMOPHUMP");
        String GIACMACNHUMOPHUMT = request.getParameter("GIACMACNHUMOPHUMT");
        String GIACMACNHUMOTHAMLAUMP = request.getParameter("GIACMACNHUMOTHAMLAUMP");
        String GIACMACNHUMOTHAMLAUMT = request.getParameter("GIACMACNHUMOTHAMLAUMT");
        String GIACMACNHUMOVITRITHAMLAUMP = request.getParameter("GIACMACNHUMOVITRITHAMLAUMP");
        String GIACMACNHUMOVITRITHAMLAUMT = request.getParameter("GIACMACNHUMOVITRITHAMLAUMT");
        String GIACMACNHUMOTIEUMONGMP = request.getParameter("GIACMACNHUMOTIEUMONGMP");
        String GIACMACNHUMOTIEUMONGMT = request.getParameter("GIACMACNHUMOTIEUMONGMT");
        String GIACMACNHUMOVITRITIEUMONGMP = request.getParameter("GIACMACNHUMOVITRITIEUMONGMP");
        String GIACMACNHUMOVITRITIEUMONGMT = request.getParameter("GIACMACNHUMOVITRITIEUMONGMT");
        String GIACMACNHUMOTONTHUONGKHACMP = request.getParameter("GIACMACNHUMOTONTHUONGKHACMP");
        String GIACMACNHUMOTONTHUONGKHACMT = request.getParameter("GIACMACNHUMOTONTHUONGKHACMT");
        String GIACMACNOIMOMP = request.getParameter("GIACMACNOIMOMP");
        String GIACMACNOIMOMT = request.getParameter("GIACMACNOIMOMT");
        String GIACMACNOIMOTUASACTOMP = request.getParameter("GIACMACNOIMOTUASACTOMP");
        String GIACMACNOIMOTUASACTOMT = request.getParameter("GIACMACNOIMOTUASACTOMT");
        String GIACMACNOIMOXUATTIETMP = request.getParameter("GIACMACNOIMOXUATTIETMP");
        String GIACMACNOIMOXUATTIETMT = request.getParameter("GIACMACNOIMOXUATTIETMT");
        String GIACMACNOIMODESCEMETMP = request.getParameter("GIACMACNOIMODESCEMETMP");
        String GIACMACNOIMODESCEMETMT = request.getParameter("GIACMACNOIMODESCEMETMT");
        String GIACMACNOIMOTONTHUONGKHACMP = request.getParameter("GIACMACNOIMOTONTHUONGKHACMP");
        String GIACMACNOIMOTONTHUONGKHACMT = request.getParameter("GIACMACNOIMOTONTHUONGKHACMT");
        String GIACMACDOATHUNGMP = request.getParameter("GIACMACDOATHUNGMP");
        String GIACMACDOATHUNGMT = request.getParameter("GIACMACDOATHUNGMT");
        String GIACMACTHUNGGIACMACMP = request.getParameter("GIACMACTHUNGGIACMACMP");
        String GIACMACTHUNGGIACMACMT = request.getParameter("GIACMACTHUNGGIACMACMT");
        String GIACMACSEIDELMP = request.getParameter("GIACMACSEIDELMP");
        String GIACMACDUONGKINHTHUNGMP = request.getParameter("GIACMACDUONGKINHTHUNGMP");
        String GIACMACSEIDELMT = request.getParameter("GIACMACSEIDELMT");
        String GIACMACDUONGKINHTHUNGMT = request.getParameter("GIACMACDUONGKINHTHUNGMT");
        String GIACMACTHUNGBITMP = request.getParameter("GIACMACTHUNGBITMP");
        String GIACMACTHUNGBITMT = request.getParameter("GIACMACTHUNGBITMT");
        String GIACMACCAMGIACGIACMACMP = request.getParameter("GIACMACCAMGIACGIACMACMP");
        String GIACMACCAMGIACGIACMACMT = request.getParameter("GIACMACCAMGIACGIACMACMT");
        String GIACMACTANMACHMP = request.getParameter("GIACMACTANMACHMP");
        String GIACMACTANMACHMT = request.getParameter("GIACMACTANMACHMT");
        String GIACMACMUCDOMP = request.getParameter("GIACMACMUCDOMP");
        String GIACMACMUCDOMT = request.getParameter("GIACMACMUCDOMT");
        String GIACMACSUYTEBAONGUONMP = request.getParameter("GIACMACSUYTEBAONGUONMP");
        String GIACMACSUYTEBAONGUONMT = request.getParameter("GIACMACSUYTEBAONGUONMT");
        String GIACMACTHOAIHOAGIAMP = request.getParameter("GIACMACTHOAIHOAGIAMP");
        String GIACMACTHOAIHOAGIAMT = request.getParameter("GIACMACTHOAIHOAGIAMT");
        String GIACMACLANGDONGCANXIMP = request.getParameter("GIACMACLANGDONGCANXIMP");
        String GIACMACLANGDONGCANXIMT = request.getParameter("GIACMACLANGDONGCANXIMT");
        String GIACMACBATTHUONGKHACMP = request.getParameter("GIACMACBATTHUONGKHACMP");
        String GIACMACBATTHUONGKHACMT = request.getParameter("GIACMACBATTHUONGKHACMT");
        String CUNGMACVIEMMP = request.getParameter("CUNGMACVIEMMP");
        String CUNGMACVIEMMT = request.getParameter("CUNGMACVIEMMT");
        String CUNGMACDOVIEMMP = request.getParameter("CUNGMACDOVIEMMP");
        String CUNGMACDOVIEMMT = request.getParameter("CUNGMACDOVIEMMT");
        String CUNGMACVIEMTHUONGCUNGMACMP = request.getParameter("CUNGMACVIEMTHUONGCUNGMACMP");
        String CUNGMACVIEMTHUONGCUNGMACMT = request.getParameter("CUNGMACVIEMTHUONGCUNGMACMT");
        String CUNGMACGIANLOIMP = request.getParameter("CUNGMACGIANLOIMP");
        String CUNGMACGIANLOIMT = request.getParameter("CUNGMACGIANLOIMT");
        String CUNGMACCHITIETKHACMP = request.getParameter("CUNGMACCHITIETKHACMP");
        String CUNGMACCHITIETKHACMT = request.getParameter("CUNGMACCHITIETKHACMT");
        String TIENPHONGBINHTHUONGMP = request.getParameter("TIENPHONGBINHTHUONGMP");
        String TIENPHONGBINHTHUONGMT = request.getParameter("TIENPHONGBINHTHUONGMT");
        String TIENPHONGMUMP = request.getParameter("TIENPHONGMUMP");
        String TIENPHONGTYNDALMP = request.getParameter("TIENPHONGTYNDALMP");
        String TIENPHONGMUMT = request.getParameter("TIENPHONGMUMT");
        String TIENPHONGTYNDALMT = request.getParameter("TIENPHONGTYNDALMT");
        String TIENPHONGMANGXUATTIETMP = request.getParameter("TIENPHONGMANGXUATTIETMP");
        String TIENPHONGMANGXUATTIETMT = request.getParameter("TIENPHONGMANGXUATTIETMT");
        String TIENPHONGMAUMP = request.getParameter("TIENPHONGMAUMP");
        String TIENPHONGMAUMT = request.getParameter("TIENPHONGMAUMT");
        String TIENPHONGTONTHUONGKHACMP = request.getParameter("TIENPHONGTONTHUONGKHACMP");
        String TIENPHONGTONTHUONGKHACMT = request.getParameter("TIENPHONGTONTHUONGKHACMT");
        String MONGMATNAUXOPMP = request.getParameter("MONGMATNAUXOPMP");
        String MONGMATNAUXOPMT = request.getParameter("MONGMATNAUXOPMT");
        String MONGMATCUONGTUMP = request.getParameter("MONGMATCUONGTUMP");
        String MONGMATCUONGTUMT = request.getParameter("MONGMATCUONGTUMT");
        String MONGMATPHOIMP = request.getParameter("MONGMATPHOIMP");
        String MONGMATPHOIMT = request.getParameter("MONGMATPHOIMT");
        String DONGTUDUONGKINHMP = request.getParameter("DONGTUDUONGKINHMP");
        String DONGTUDUONGKINHMT = request.getParameter("DONGTUDUONGKINHMT");
        String DONGTUTRONMP = request.getParameter("DONGTUTRONMP");
        String DONGTUVITRIMP = request.getParameter("DONGTUVITRIMP");
        String DONGTUTRONMT = request.getParameter("DONGTUTRONMT");
        String DONGTUVITRIMT = request.getParameter("DONGTUVITRIMT");
        String DONGTUPHANXAMP = request.getParameter("DONGTUPHANXAMP");
        String DONGTUPHANXAMT = request.getParameter("DONGTUPHANXAMT");
        String DONGTUTONTHUONGKHACMP = request.getParameter("DONGTUTONTHUONGKHACMP");
        String DONGTUTONTHUONGKHACMT = request.getParameter("DONGTUTONTHUONGKHACMT");
        String THUYTINHTHEBINHTHUONGMP = request.getParameter("THUYTINHTHEBINHTHUONGMP");
        String THUYTINHTHEBINHTHUONGMT = request.getParameter("THUYTINHTHEBINHTHUONGMT");
        String THUYTINHTHEHINHTHAIDUCMP = request.getParameter("THUYTINHTHEHINHTHAIDUCMP");
        String THUYTINHTHEHINHTHAIDUCMT = request.getParameter("THUYTINHTHEHINHTHAIDUCMT");
        String THUYTINHTHEIOLMP = request.getParameter("THUYTINHTHEIOLMP");
        String THUYTINHTHEIOLMT = request.getParameter("THUYTINHTHEIOLMT");
        String THUYTINHTHETONTHUONGKHACMP = request.getParameter("THUYTINHTHETONTHUONGKHACMP");
        String THUYTINHTHETONTHUONGKHACMT = request.getParameter("THUYTINHTHETONTHUONGKHACMT");
        String ANHDONGTUHONGMP = request.getParameter("ANHDONGTUHONGMP");
        String ANHDONGTUHONGMT = request.getParameter("ANHDONGTUHONGMT");
        String DICHKINHSACHMP = request.getParameter("DICHKINHSACHMP");
        String DICHKINHSACHMT = request.getParameter("DICHKINHSACHMT");
        String DICHKINHTONTHUONGKHACMP = request.getParameter("DICHKINHTONTHUONGKHACMP");
        String DICHKINHTONTHUONGKHACMT = request.getParameter("DICHKINHTONTHUONGKHACMT");
        String DAYMATGAITHIMP = request.getParameter("DAYMATGAITHIMP");
        String DAYMATGAITHIMT = request.getParameter("DAYMATGAITHIMT");
        String DAYMATCDMP = request.getParameter("DAYMATCDMP");
        String DAYMATCDMT = request.getParameter("DAYMATCDMT");
        String DAYMATVONGMACMP = request.getParameter("DAYMATVONGMACMP");
        String DAYMATVONGMACMT = request.getParameter("DAYMATVONGMACMT");
        String DAYMATHEMACHMAUMP = request.getParameter("DAYMATHEMACHMAUMP");
        String DAYMATHEMACHMAUMT = request.getParameter("DAYMATHEMACHMAUMT");
        String DAYMATTONTHUONGKHACMP = request.getParameter("DAYMATTONTHUONGKHACMP");
        String DAYMATTONTHUONGKHACMT = request.getParameter("DAYMATTONTHUONGKHACMT");
        String HUYETAP = request.getParameter("HUYETAP");
        String NHIETDO = request.getParameter("NHIETDO");
        String MACH = request.getParameter("MACH");
        String NOITIET = request.getParameter("NOITIET");
        String NOITIETBENHLY = request.getParameter("NOITIETBENHLY");
        String THANKINH = request.getParameter("THANKINH");
        String THANKINHBENHLY = request.getParameter("THANKINHBENHLY");
        String TUANHOAN = request.getParameter("TUANHOAN");
        String TUANHOANBENHLY = request.getParameter("TUANHOANBENHLY");
        String HOHAP = request.getParameter("HOHAP");
        String HOHAPBENHLY = request.getParameter("HOHAPBENHLY");
        String TIEUHOA = request.getParameter("TIEUHOA");
        String TIEUHOABENHLY = request.getParameter("TIEUHOABENHLY");
        String COXUONGKHOP = request.getParameter("COXUONGKHOP");
        String COXUONGKHOPBENHLY = request.getParameter("COXUONGKHOPBENHLY");
        String TIETNIEU = request.getParameter("TIETNIEU");
        String TIETNIEUBENHLY = request.getParameter("TIETNIEUBENHLY");
        String TOANTHANKHAC = request.getParameter("TOANTHANKHAC");
        String CLS = request.getParameter("CLS");
        String BENHCHINH = request.getParameter("BENHCHINH");
        String BENHKEMTHEO = request.getParameter("BENHKEMTHEO");
        String PHANBIET = request.getParameter("PHANBIET");
        String TIENLUONG = request.getParameter("TIENLUONG");
        String HUONGDIEUTRI = request.getParameter("HUONGDIEUTRI");
        String KETMAC_MATPHAI = request.getParameter("KETMAC_MATPHAI");
        String KETMAC_MATTRAI = request.getParameter("KETMAC_MATTRAI");

        String BIEUHIEN_BENHLY = request.getParameter("BIEUHIEN_BENHLY");
        String BENHLY = request.getParameter("BENHLY");
        String BENHLY_TEXT = request.getParameter("BENHLY_TEXT");
        String NHIPTHO = request.getParameter("NHIPTHO");
        String CANNANG = request.getParameter("CANNANG");
        String CHIEUCAO = request.getParameter("CHIEUCAO");

        String GHEPGIACMAC_MP = request.getParameter("GHEPGIACMAC_MP");
        String GHEPGIACMAC_MT = request.getParameter("GHEPGIACMAC_MT");

        String NHUMO_BTH_MP = request.getParameter("NHUMO_BTH_MP");
        String NHUMO_BTH_MT = request.getParameter("NHUMO_BTH_MT");

        String NOIMO_DESCEMET_BTH_MP = request.getParameter("NOIMO_DESCEMET_BTH_MP");
        String NOIMO_DESCEMET_BTH_MT = request.getParameter("NOIMO_DESCEMET_BTH_MT");

        String CUNGMAC_BTH_MP = request.getParameter("CUNGMAC_BTH_MP");
        String CUNGMAC_BTH_MT = request.getParameter("CUNGMAC_BTH_MT");

        String DUC_BAO_SAU_MP = request.getParameter("DUC_BAO_SAU_MP");
        String DUC_BAO_SAU_MT = request.getParameter("DUC_BAO_SAU_MT");
        String TOMTAT_BENHAN = request.getParameter("TOMTAT_BENHAN");

        String BOMLEQUAN_MP = request.getParameter("BOMLEQUAN_MP");
        String BOMLEQUAN_MT = request.getParameter("BOMLEQUAN_MT");
        String TRAOLEQUANDOIDIEN_MP = request.getParameter("TRAOLEQUANDOIDIEN_MP");
        String TRAOLEQUANDOIDIEN_MT = request.getParameter("TRAOLEQUANDOIDIEN_MT");
        String TRAOTAICHO_MP = request.getParameter("TRAOTAICHO_MP");
        String TRAOTAICHO_MT = request.getParameter("TRAOTAICHO_MT");

        String MONGMAT_KHUYET_MP = request.getParameter("MONGMAT_KHUYET_MP");
        String MONGMAT_KHUYET_MT = request.getParameter("MONGMAT_KHUYET_MT");
        String MONGMATKHAC_MP = request.getParameter("MONGMATKHAC_MP");
        String MONGMATKHAC_MT = request.getParameter("MONGMATKHAC_MT");
        Map map = new HashMap();
        try {
            String sql = "call HIS_MANAGER.UPDATE_BABANPHANTRUOC(" +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?," +
                    "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?" +
                    ",?,?,?,?,?,?,?,?,?,?)" +
                    "#l,s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,s," +
                    "s,s,s,s,S,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s,s" +
                    ",s,s,s,s,s,s,s,s,s,s";
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
            vreturn = jdbcTemplate.update(sql, new Object[]{
                    ID, LYDOVAOVIEN,THOIGIANXUATHIENBENH,NGUYENNHAN,CACPHUONGPHAPDADIEUTRI,
                    TIENSUBANTHANTAIMAT,TIENSUBANTHANTOANTHAN,TIENSUGIADINHBENHMAT,TIENSUGIADINHBENHTOANTHAN,THILUCKHONGKINHMP,
                    THILUCQUALOMP,THILUCKHONGKINHMT,THILUCQUALOMT,THILUCCOCHINHKINHMP,THILUCCOCHINHKINHMT,
                    THILUCNHINGANMP,THILUCNHINGANMT,NHANAPMP,NHANAPMT,LACVANNHANMP,
                    LACVANNHANMT,LEDAONUOCTHOATTOTMP,LEDAONUOCTHOATTOTMT,LEDAOTRAOLEQUANMP,LEDAOTRAOLEQUANMT,
                    LEDAOTRAOTAICHOMP,LEDAOTRAOTAICHOMT,MIMATTINHTRANGMP,MIMATTINHTRANGMT,MIMATTINHTRANGKHACMP,
                    MIMATTINHTRANGKHACMT,MIMATUMIMP,MIMATTINHCHATUMP,MIMATUMIMT,MIMATTINHCHATUMT,
                    MIMATVITRIMP,MIMATKICHTHUOCMP,MIMATVITRIMT,MIMATKICHTHUOCMT,MIMATQUAMMP,
                    MIMATQUAMMT,MIMATQUAMMITRENMP,MIMATQUAMMITRENMT,MIMATQUAMMIDUOIMP,MIMATQUAMMIDUOIMT,
                    HOMIMP,HOMIMT,TREMIMP,TREMIMT,MIMATKHUYETMIMP,
                    MIMATKHUYETMIMT,MIMATTUYENBOMIMP,MIMATTUYENBOMIMT,MIMATVIEMBOMIMP,MIMATVIEMBOMIMT,
                    MIMATTONTHUONGKHACMP,MIMATTONTHUONGKHACMT,KETMACCUONGTUMP,KETMACCUONGTUMT,KETMACPHUNEMP,
                    KETMACPHUNEMT,KETMACNHUMP,KETMACNHUMT,KETMACTIETTOMUMP,KETMACTIETTOMUMT,
                    KETMACFLUORMP,KETMACFLUORMT,KETMACUKETMACTINHCHATMP,KETMACUKETMACTINHCHATMT,KETMACUKETMACVITRIMP,
                    KETMACUKETMACVITRIMT,KETMACUKETMACKICHTHUOCMP,KETMACUKETMACKICHTHUOCMT,KETMACCUNGDOMP,KETMACCUNGDOMT,
                    KETMACCHIEUCAOCUACAUDINHMP,KETMACCHIEUCAOCUACAUDINHMT,KETMACDORONGCUACAUDINHMP,KETMACDORONGCUACAUDINHMT,KETMACTONTHUONGKHACMP,
                    KETMACTONTHUONGKHACMT,GIACMACMP,GIACMACMT,GIACMACKICHTHUOCMP,GIACMACKICHTHUOCMT,
                    GIACMACHINHDANGMP,GIACMACHINHDANGMT,GIACMACTONTHUONGDANCHAMMP,GIACMACTONTHUONGDANCHAMMT,GIACMACPHUBONGBIEUMOMP,
                    GIACMACPHUBONGBIEUMOMT,GIACMACMATBIEUMOMP,GIACMACMATBIEUMOMT,GIACMACVITRIMATBIEUMOMP,GIACMACVITRIMATBIEUMOMT,
                    GIACMACBOTONTHUONGMP,GIACMACBOTONTHUONGMT,GIACMACTHOAIHOADAIBANGMP,GIACMACTHOAIHOADAIBANGMT,GIACMACLANGDONGTHUOCMP,
                    GIACMACLANGDONGTHUOCMT,GIACMACBIEUMOTONTHUONGKHACMP,GIACMACBIEUMOTONTHUONGKHACMT,GIACMACNHUMOPHUMP,GIACMACNHUMOPHUMT,
                    GIACMACNHUMOTHAMLAUMP,GIACMACNHUMOTHAMLAUMT,GIACMACNHUMOVITRITHAMLAUMP,GIACMACNHUMOVITRITHAMLAUMT,GIACMACNHUMOTIEUMONGMP,
                    GIACMACNHUMOTIEUMONGMT,GIACMACNHUMOVITRITIEUMONGMP,GIACMACNHUMOVITRITIEUMONGMT,GIACMACNHUMOTONTHUONGKHACMP,GIACMACNHUMOTONTHUONGKHACMT,
                    GIACMACNOIMOMP,GIACMACNOIMOMT,GIACMACNOIMOTUASACTOMP,GIACMACNOIMOTUASACTOMT,GIACMACNOIMOXUATTIETMP,
                    GIACMACNOIMOXUATTIETMT,GIACMACNOIMODESCEMETMP,GIACMACNOIMODESCEMETMT,GIACMACNOIMOTONTHUONGKHACMP,GIACMACNOIMOTONTHUONGKHACMT,
                    GIACMACDOATHUNGMP,GIACMACDOATHUNGMT,GIACMACTHUNGGIACMACMP,GIACMACTHUNGGIACMACMT,GIACMACSEIDELMP,
                    GIACMACDUONGKINHTHUNGMP,GIACMACSEIDELMT,GIACMACDUONGKINHTHUNGMT,GIACMACTHUNGBITMP,GIACMACTHUNGBITMT,
                    GIACMACCAMGIACGIACMACMP,GIACMACCAMGIACGIACMACMT,GIACMACTANMACHMP,GIACMACTANMACHMT,GIACMACMUCDOMP,
                    GIACMACMUCDOMT,GIACMACSUYTEBAONGUONMP,GIACMACSUYTEBAONGUONMT,GIACMACTHOAIHOAGIAMP,GIACMACTHOAIHOAGIAMT,
                    GIACMACLANGDONGCANXIMP,GIACMACLANGDONGCANXIMT,GIACMACBATTHUONGKHACMP,GIACMACBATTHUONGKHACMT,CUNGMACVIEMMP,
                    CUNGMACVIEMMT,CUNGMACDOVIEMMP,CUNGMACDOVIEMMT,CUNGMACVIEMTHUONGCUNGMACMP,CUNGMACVIEMTHUONGCUNGMACMT,
                    CUNGMACGIANLOIMP,CUNGMACGIANLOIMT,CUNGMACCHITIETKHACMP,CUNGMACCHITIETKHACMT,TIENPHONGBINHTHUONGMP,
                    TIENPHONGBINHTHUONGMT,TIENPHONGMUMP,TIENPHONGTYNDALMP,TIENPHONGMUMT,TIENPHONGTYNDALMT,
                    TIENPHONGMANGXUATTIETMP,TIENPHONGMANGXUATTIETMT,TIENPHONGMAUMP,TIENPHONGMAUMT,TIENPHONGTONTHUONGKHACMP,
                    TIENPHONGTONTHUONGKHACMT,MONGMATNAUXOPMP,MONGMATNAUXOPMT,MONGMATCUONGTUMP,MONGMATCUONGTUMT,
                    MONGMATPHOIMP,MONGMATPHOIMT,DONGTUDUONGKINHMP,DONGTUDUONGKINHMT,DONGTUTRONMP,
                    DONGTUVITRIMP,DONGTUTRONMT,DONGTUVITRIMT,DONGTUPHANXAMP,DONGTUPHANXAMT,
                    DONGTUTONTHUONGKHACMP,DONGTUTONTHUONGKHACMT,THUYTINHTHEBINHTHUONGMP,THUYTINHTHEBINHTHUONGMT,THUYTINHTHEHINHTHAIDUCMP,
                    THUYTINHTHEHINHTHAIDUCMT,THUYTINHTHEIOLMP,THUYTINHTHEIOLMT,THUYTINHTHETONTHUONGKHACMP,THUYTINHTHETONTHUONGKHACMT,
                    ANHDONGTUHONGMP,ANHDONGTUHONGMT,DICHKINHSACHMP,DICHKINHSACHMT,DICHKINHTONTHUONGKHACMP,
                    DICHKINHTONTHUONGKHACMT,DAYMATGAITHIMP,DAYMATGAITHIMT,DAYMATCDMP,DAYMATCDMT,
                    DAYMATVONGMACMP,DAYMATVONGMACMT,DAYMATHEMACHMAUMP,DAYMATHEMACHMAUMT,DAYMATTONTHUONGKHACMP,
                    DAYMATTONTHUONGKHACMT,HUYETAP,NHIETDO,MACH,NOITIET,
                    NOITIETBENHLY,THANKINH,THANKINHBENHLY,TUANHOAN,TUANHOANBENHLY,
                    HOHAP,HOHAPBENHLY,TIEUHOA,TIEUHOABENHLY,COXUONGKHOP,
                    COXUONGKHOPBENHLY,TIETNIEU,TIETNIEUBENHLY,TOANTHANKHAC,CLS,
                    BENHCHINH,BENHKEMTHEO,PHANBIET,TIENLUONG,HUONGDIEUTRI, KETMAC_MATPHAI, KETMAC_MATTRAI,
                    BIEUHIEN_BENHLY, BENHLY, BENHLY_TEXT, NHIPTHO, CANNANG, CHIEUCAO
                    ,GHEPGIACMAC_MP, GHEPGIACMAC_MT
                    ,NHUMO_BTH_MP, NHUMO_BTH_MT
                    ,NOIMO_DESCEMET_BTH_MP, NOIMO_DESCEMET_BTH_MT
                    ,CUNGMAC_BTH_MP, CUNGMAC_BTH_MT
                    ,DUC_BAO_SAU_MP, DUC_BAO_SAU_MT
                    ,TOMTAT_BENHAN
                    ,BOMLEQUAN_MP, BOMLEQUAN_MT, TRAOLEQUANDOIDIEN_MP, TRAOLEQUANDOIDIEN_MT, TRAOTAICHO_MP, TRAOTAICHO_MT, MONGMAT_KHUYET_MP, MONGMAT_KHUYET_MT, MONGMATKHAC_MP, MONGMATKHAC_MT
            });
            voBenhAnNoiTruDAO.log_update(Integer.parseInt(ID), L2Utils.getDvtt(session), L2Utils.getMaUser(session));
            map.put("SUCCESS", vreturn);
            map.put("ERRO", "0");
        } catch (Exception e) {
            map.put("ERRO", e.getMessage());

        }
        return map;
    }
    @RequestMapping(value = "/load-danh-sach-phieu-di-kem")
    public @ResponseBody
    List loadDsPhieuDiKem(HttpSession session, HttpServletRequest request) {
        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSourceMNG);
        MapSqlParameterSource params = new MapSqlParameterSource().addValue("dvtt", L2Utils.getDvtt(session))
                .addValue("loaiBenhAn", request.getParameter("loaiBenhAn"));
        return namedParameterJdbcTemplate.queryForList("SELECT ID, TEN_PHIEU_DI_KEM, MOTA, CLASS_VIEW FROM HIS_MANAGER.DM_PHIEU_DI_KEM_VOBENHAN WHERE DVTT = :dvtt AND LOAI_BENH_AN = :loaiBenhAn AND HOATDONG = 1 ORDER BY STT_ORDER ASC", params);
    }

    String getUrlViewPhieuDiKem(String dvtt, String idPhieu, String defaultUrl) {
        String maThamSo = "-1";
        switch (idPhieu) {
            case "MAU1_PHCN": {
                maThamSo = "7";
                break;
            }
            case "MAU2_PHCN": {
                maThamSo = "8";
                break;
            }
            case "MAU3_PHCN": {
                maThamSo = "9";
                break;
            }
            default:
                break;
        }

        String thamSoVBA = voBenhAnNoiTruDAO.getThamSoVoBenhAnString(dvtt, maThamSo);
        if (thamSoVBA.equals("0")) {
            return defaultUrl;
        }
        return thamSoVBA;
    }

    @RequestMapping(value = "/call-html-phieu-di-kem", method = RequestMethod.POST, produces = "application/json; charset=utf-8")
    public ModelAndView htmlPDK(ModelMap mm, HttpSession session, @RequestBody String data) throws ParseException {
        try {
            if (!SessionFilter.checkSession(session)) {
                return SessionFilter.redirectLogin2();
            }
            JSONObject dataJson = new JSONObject(data);
            String idPhieu = dataJson.optString("iD");
            String dvtt = L2Utils.getDvtt(session);
            String url = jspName.getVbaJspPath(""+idPhieu);
            return new ModelAndView(getUrlViewPhieuDiKem(dvtt, idPhieu, url));
        }catch (Exception e){
            e.getMessage();
            return new ModelAndView("VoBenhAnNoiTru/view/main/startPage");
        }
    }
    @RequestMapping (value = "/print-view-phieu-di-kem")
    public @ResponseBody
    void printTrangBiaVoBenhAnPHCN(HttpSession session, HttpServletRequest request, HttpServletResponse response){
        Thamsohethong tsht = (Thamsohethong) request.getSession().getAttribute("Sess_Thamso");

        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        String lsSoVaoVien = request.getParameter("soVaoVien");
        String lsSoVaoVienDt = request.getParameter("soVaoVienDT");
        String loaiBA = request.getParameter("loaiBa");
        Map ttkg = jdbcTemplate.queryForMap("call THONGTIN_KHOA_GIUONG_VBA(?,?,?)#c,s,s,s", new Object[]{L2Utils.getDvtt(session), lsSoVaoVien, lsSoVaoVienDt});
        List<Map<String, Object>> page1 = voBenhAnNoiTruDAO.getThongTinPage1byID(Integer.parseInt(request.getParameter("iD")));


        String tensoyte = "SỞ Y TẾ " + tsht.tinh.toUpperCase();
        String tenbenhvien = tsht.tenbenhvien.toUpperCase();

        String path = voBenhAnNoiTruDAO.getPathReportPDK(loaiBA, request.getParameter("idPhieuDiKem"), L2Utils.getDvtt(session));
        Map ttphieu = new HashMap();
        try {
            ttphieu = voBenhAnNoiTruDAO.ttPhieuDiKemPhucHoiChucNang(
                    request.getParameter("iD"), request.getParameter("soVaoVien"), request.getParameter("soVaoVienDT")
            );
        }catch (Exception e){
            ttphieu.put("MAU_1", " ");
            ttphieu.put("MAU_2", " ");
            ttphieu.put("MASOMAU3", " ");
            ttphieu.put("TOSO", " ");
        }

        Map parameters = new HashMap();
        parameters.put("tensoyte", tensoyte);
        parameters.put("tenbenhvien", tenbenhvien);

        parameters.put("SOYTE", tsht.tinh);
        parameters.put("TENKHOA", ttkg.get("TENKHOA").toString());
        parameters.put("PHONGBENH", ttkg.get("PHONGBENH").toString());
        parameters.put("GIUONG_BENH", ttkg.get("GIUONG_BENH").toString());
        parameters.put("TENBENHVIEN", tsht.tenbenhvien);
        parameters.put("TEN_BENH_NHAN", page1.get(0).get("TEN_BENH_NHAN"));
        parameters.put("TUOI_NUM", page1.get(0).get("TUOI_NUM"));
        parameters.put("GIOI_TINH_TEXT", page1.get(0).get("GIOI_TINH_TEXT"));
        parameters.put("MASOMAU3", ttphieu.get("MASOMAU3").toString());
        parameters.put("TOSO", ttphieu.get("TOSO").toString());
        parameters.put("CHANDOAN", page1.get(0).get("ICD_KHOADIEUTRI")+"-"+page1.get(0).get("TENICD_KHOADIEUTRI"));


        parameters.put("sovaovien", request.getParameter("soVaoVien"));
        parameters.put("sovaovien_dt", request.getParameter("soVaoVienDT"));
        parameters.put("dvtt", L2Utils.getDvtt(session));
        parameters.put("id_hsba", request.getParameter("iD"));
        parameters.putAll(page1.get(0));


        File reportFile = new File(request.getSession().getServletContext().getRealPath(""+path));
        try {
            JasperHelper.printReportView("pdf", reportFile, parameters, dataSourceMNG.getConnection(), response);
            return;
        }catch (Exception e){
            return;
        }
    }
    @RequestMapping (value = "/print-view-phieu-di-kem-mau3-phcn-chitiet")
    public @ResponseBody
    void printPhieuDiKemMau3PHCNChiTiet(HttpSession session, HttpServletRequest request, HttpServletResponse response){
        Thamsohethong tsht = (Thamsohethong) request.getSession().getAttribute("Sess_Thamso");

        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        String lsSoVaoVien = request.getParameter("soVaoVien");
        String lsSoVaoVienDt = request.getParameter("soVaoVienDT");
        String loaiBA = request.getParameter("loaiBa");
        String maDichVu = request.getParameter("maDichVu");
        String soPhieu = request.getParameter("soPhieu");
        String ngayChiDinh = request.getParameter("ngayChiDinh");
        Map ttkg = jdbcTemplate.queryForMap("call THONGTIN_KHOA_GIUONG_VBA(?,?,?)#c,s,s,s", new Object[]{L2Utils.getDvtt(session), lsSoVaoVien, lsSoVaoVienDt});
        List<Map<String, Object>> page1 = voBenhAnNoiTruDAO.getThongTinPage1byID(Integer.parseInt(request.getParameter("iD")));


        String tensoyte = "SỞ Y TẾ " + tsht.tinh.toUpperCase();
        String tenbenhvien = tsht.tenbenhvien.toUpperCase();

        String path = voBenhAnNoiTruDAO.getPathReportPDK(loaiBA, request.getParameter("idPhieuDiKem"), L2Utils.getDvtt(session));
        Map ttphieu = new HashMap();
        try {
            ttphieu = voBenhAnNoiTruDAO.ttPhieuDiKemPhucHoiChucNang(
                    request.getParameter("iD"), request.getParameter("soVaoVien"), request.getParameter("soVaoVienDT")
            );
        }catch (Exception e){
            ttphieu.put("MAU_1", " ");
            ttphieu.put("MAU_2", " ");
            ttphieu.put("MASOMAU3", " ");
            ttphieu.put("TOSO", " ");
        }

        Map parameters = new HashMap();
        parameters.put("tensoyte", tensoyte);
        parameters.put("tenbenhvien", tenbenhvien);

        parameters.put("SOYTE", tsht.tinh);
        parameters.put("TENKHOA", ttkg.get("TENKHOA").toString());
        parameters.put("PHONGBENH", ttkg.get("PHONGBENH").toString());
        parameters.put("GIUONG_BENH", ttkg.get("GIUONG_BENH").toString());
        parameters.put("TENBENHVIEN", tsht.tenbenhvien);
        parameters.put("TEN_BENH_NHAN", page1.get(0).get("TEN_BENH_NHAN"));
        parameters.put("TUOI_NUM", page1.get(0).get("TUOI_NUM"));
        parameters.put("GIOI_TINH_TEXT", page1.get(0).get("GIOI_TINH_TEXT"));
        parameters.put("MASOMAU3", ttphieu.get("MASOMAU3").toString());
        parameters.put("TOSO", ttphieu.get("TOSO").toString());
        parameters.put("CHANDOAN", page1.get(0).get("ICD_KHOADIEUTRI")+"-"+page1.get(0).get("TENICD_KHOADIEUTRI"));


        parameters.put("sovaovien", request.getParameter("soVaoVien"));
        parameters.put("sovaovien_dt", request.getParameter("soVaoVienDT"));
        parameters.put("dvtt", L2Utils.getDvtt(session));
        parameters.put("id_hsba", request.getParameter("iD"));
        parameters.put("MA_DICH_VU", maDichVu);
        parameters.put("SO_PHIEU", soPhieu);
        parameters.put("NGAY_CHI_DINH", ngayChiDinh);

        File reportFile = new File(request.getSession().getServletContext().getRealPath("/WEB-INF/pages/VoBenhAnNoiTru/report/phieudikem/phieudikem_phcn_mau3_chitiet.jasper"));
        try {
            JasperHelper.printReportView("pdf", reportFile, parameters, dataSourceMNG.getConnection(), response);
            return;
        }catch (Exception e){
            return;
        }
    }
    @RequestMapping (value = "load-thong-tin-mau-di-kem-benh-an-phcn")
    public @ResponseBody
    Map pdkPHCN(HttpServletRequest request, HttpSession session){
        Map map = new HashMap<>();
        Thamsohethong tsht = (Thamsohethong) request.getSession().getAttribute("Sess_Thamso");
        int iD = Integer.parseInt(request.getParameter("idHsba"));
        String sqlThongTinKhoaGiuong = "call THONGTIN_KHOA_GIUONG_VBA(?,?,?)#c,s,s,s";
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSourceMNG);
        Map ttkg = jdbcTemplate.queryForMap(sqlThongTinKhoaGiuong, new Object[]{L2Utils.getDvtt(session),
                request.getParameter("soVaoVien"),
                request.getParameter("soVaoVienDt")});
        List<Map<String, Object>> page1 = voBenhAnNoiTruDAO.getThongTinPage1byID(Integer.parseInt(request.getParameter("idHsba")));
        Map ttphieu = new HashMap<>();
        try {
            ttphieu = voBenhAnNoiTruDAO.ttPhieuDiKemPhucHoiChucNang(
                    request.getParameter("idHsba"), request.getParameter("soVaoVien"), request.getParameter("soVaoVienDt")
            );
        }catch (Exception e){
            ttphieu.put("MAU_1", " ");
            ttphieu.put("MAU_2", " ");
            ttphieu.put("MASOMAU3", " ");
            ttphieu.put("TOSO", " ");
        }
        map.put("SOYTE", tsht.tinh);
        map.put("TENBENHVIEN", tsht.tenbenhvien);
        map.put("TENKHOA", ttkg.get("TENKHOA").toString());
        map.put("PHONGBENH", ttkg.get("PHONGBENH").toString());
        map.put("GIUONG_BENH", ttkg.get("GIUONG_BENH").toString());
        map.put("SO_GIUONG_TAI_KHOA_SO", ttkg.getOrDefault("SO_GIUONG_TAI_KHOA_SO", "").toString());
        map.put("SOVAOVIEN", page1.get(0).get("SOVAOVIEN"));
        map.put("TEN_BENH_NHAN", page1.get(0).get("TEN_BENH_NHAN"));
        map.put("TUOI_NUM", page1.get(0).get("TUOI_NUM"));
        map.put("GIOI_TINH_TEXT", page1.get(0).get("GIOI_TINH_TEXT"));
        map.put("CHANDOAN", page1.get(0).get("ICD_KHOADIEUTRI")+"-"+page1.get(0).get("TENICD_KHOADIEUTRI"));

        map.put("TT_PHIEU_1", ttphieu.get("MAU_1").toString());
        map.put("TT_PHIEU_2", ttphieu.get("MAU_2").toString());
        map.put("MASOMAU3", ttphieu.get("MASOMAU3").toString());
        map.put("TOSO", ttphieu.get("TOSO").toString());
        return map;
    }
    @RequestMapping(value = "/update-phieu-di-kem-phcn")
    public @ResponseBody
    int updatePdkPHCN (HttpServletRequest request, HttpSession session){
        int retValue = 0;
        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSourceMNG);
        MapSqlParameterSource params = new MapSqlParameterSource()
                .addValue("idBenhAn", request.getParameter("idBenhAn"))
                .addValue("soVaoVien", request.getParameter("soVaoVien"))
                .addValue("soVaoVienDt", request.getParameter("soVaoVienDt"))
                .addValue("maSo", request.getParameter("maSo"))
                .addValue("jsDataMauSo1", request.getParameter("jsDataMauSo1"))
                .addValue("jsDataMauSo2", request.getParameter("jsDataMauSo2"))
                .addValue("jsDataMauSo3", request.getParameter("jsDataMauSo3"))
                ;
        boolean isIns = namedParameterJdbcTemplate.queryForObject("SELECT COUNT(1) " +
                "FROM HIS_MANAGER.PHIEUDIKEM_PHCN " +
                "WHERE ID_HSBA = :idBenhAn AND SOVAOVIEN = :soVaoVien AND SOVAOVIEN_DT = :soVaoVienDt ", params, Integer.class)>0?false:true;
        if(isIns){
            retValue = namedParameterJdbcTemplate.update("INSERT INTO HIS_MANAGER.PHIEUDIKEM_PHCN (ID_HSBA, SOVAOVIEN, SOVAOVIEN_DT, MASO, MAU_PHIEU_SO_1, MAU_PHIEU_SO_2) VALUES " +
                    "(:idBenhAn, :soVaoVien, :soVaoVienDt, :maSo, :jsDataMauSo1, :jsDataMauSo2)", params);
        }else {
            if(request.getParameter("mauPhieu").equals("1")) {
                retValue = namedParameterJdbcTemplate.update("UPDATE HIS_MANAGER.PHIEUDIKEM_PHCN SET MAU_PHIEU_SO_1 = :jsDataMauSo1 " +
                        "WHERE ID_HSBA = :idBenhAn AND SOVAOVIEN = :soVaoVien AND SOVAOVIEN_DT = :soVaoVienDt", params);
            }else if(request.getParameter("mauPhieu").equals("3")){
                retValue = namedParameterJdbcTemplate.update("UPDATE HIS_MANAGER.PHIEUDIKEM_PHCN SET MAU_PHIEU_SO_3 = :jsDataMauSo3 " +
                        "WHERE ID_HSBA = :idBenhAn AND SOVAOVIEN = :soVaoVien AND SOVAOVIEN_DT = :soVaoVienDt", params);
            }else{
                retValue = namedParameterJdbcTemplate.update("UPDATE HIS_MANAGER.PHIEUDIKEM_PHCN SET MAU_PHIEU_SO_2 = :jsDataMauSo2 " +
                        "WHERE ID_HSBA = :idBenhAn AND SOVAOVIEN = :soVaoVien AND SOVAOVIEN_DT = :soVaoVienDt", params);
            }

        }

        return retValue;
    }
    public @ResponseBody
    @RequestMapping(value = "/get-danh-sach-vltl")
    List luuThongTinPage1(HttpSession session, HttpServletRequest request, HttpServletResponse response
    )throws ParseException {
        List ret;
        String soVaoVien = request.getParameter("soVaoVien");
        String soVaoVienDt = request.getParameter("soVaoVienDt");
        ret = voBenhAnNoiTruDAO.getDanhSachVLTL(
                L2Utils.getDvtt(session),
                soVaoVien,
                soVaoVienDt
        );
        return ret;
    }

    public @ResponseBody
    @RequestMapping(value = "/LayDanhSachTTPTMat")
    List LayDanhSachTTPTMat(HttpSession session, HttpServletRequest request, HttpServletResponse response
    )throws ParseException {
        List ret;
        String soVaoVien = request.getParameter("soVaoVien");
        String soVaoVienDt = request.getParameter("soVaoVienDt");
        ret = voBenhAnNoiTruDAO.LayDanhSachTTPTMat(
                L2Utils.getDvtt(session),
                soVaoVien,
                soVaoVienDt
        );
        return ret;
    }

    @RequestMapping (value = "load-thong-tin-mau-di-kem-benh-an-mat")
    public @ResponseBody
    Map pdkMat(HttpServletRequest request, HttpSession session){
        Map map = new HashMap<>();
        Thamsohethong tsht = (Thamsohethong) request.getSession().getAttribute("Sess_Thamso");
        int iD = Integer.parseInt(request.getParameter("idHsba"));
        String maphieu = request.getParameter("maphieu");
        String loaiphieu = request.getParameter("loaiphieu");
        String madv = request.getParameter("madv");
        String sqlThongTinKhoaGiuong = "call THONGTIN_KHOA_GIUONG_VBA(?,?,?)#c,s,s,s";
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSourceMNG);
        Map ttkg = jdbcTemplate.queryForMap(sqlThongTinKhoaGiuong, new Object[]{L2Utils.getDvtt(session),
                request.getParameter("soVaoVien"),
                request.getParameter("soVaoVienDt")});
        List<Map<String, Object>> page1 = voBenhAnNoiTruDAO.getThongTinPage1byID(Integer.parseInt(request.getParameter("idHsba")));
        Map ttphieu = new HashMap<>();
        try {
            ttphieu = voBenhAnNoiTruDAO.ttPhieuDiKemMat(
                    L2Utils.getDvtt(session),request.getParameter("idHsba"), request.getParameter("soVaoVien"), request.getParameter("soVaoVienDt"),maphieu,loaiphieu,madv
            );
            map.put("CHANDOAN_TRUOCPT", ttphieu.get("CHANDOAN_TRUOCPT").toString());
            map.put("MAT", ttphieu.get("MAT").toString());
            map.put("CHIDINHPHAUTHUAT", ttphieu.get("CHIDINHPHAUTHUAT").toString());
            map.put("PTVCHINH", ttphieu.get("PTVCHINH").toString());
            map.put("PTVPHU", ttphieu.get("PTVPHU").toString());
            map.put("BSGAYME", ttphieu.get("BSGAYME").toString());
            map.put("PPVOCAM", ttphieu.get("PPVOCAM").toString());
            map.put("LOAITHUOCTE", ttphieu.get("LOAITHUOCTE").toString());
            map.put("NGAYGIAYTO_FORMAT", ttphieu.get("NGAYGIAYTO_FORMAT").toString());
            map.put("NGAYGIAYTO_REPORT", ttphieu.get("NGAYGIAYTO_REPORT").toString());
            map.put("PTVDUNGPHIEU", ttphieu.get("PTVDUNGPHIEU").toString());
            map.put("GLOCOM", ttphieu.get("GLOCOM").toString());
            map.put("LANPT", ttphieu.get("LANPT").toString());
            map.put("NOIDUNGTTPT", ttphieu.get("NOIDUNGTTPT").toString());
            map.put("NGAYPHAUTHUAT_TEXT", ttphieu.get("NGAYPHAUTHUAT_TEXT").toString());
            map.put("NGAYPHAUTHUAT", ttphieu.get("NGAYPHAUTHUAT").toString());
            map.put("MOTATECNC", ttphieu.get("MOTATECNC").toString());
            map.put("MOTATEBEMAT", ttphieu.get("MOTATEBEMAT").toString());
            map.put("PPPHAUTHUAT", ttphieu.get("PPPHAUTHUAT").toString());
            map.put("NHOMMAU", ttphieu.get("NHOMMAU").toString());
        }catch (Exception e){
        }
        map.put("SOYTE", tsht.tinh);
        map.put("TENBENHVIEN", tsht.tenbenhvien);
        map.put("TENKHOA", ttkg.get("TENKHOA").toString());
        map.put("PHONGBENH", ttkg.get("PHONGBENH").toString());
        map.put("GIUONG_BENH", ttkg.get("GIUONG_BENH").toString());
        map.put("SOBENHAN", page1.get(0).get("SOBENHAN"));
        map.put("SOVAOVIEN", page1.get(0).get("SOVAOVIEN"));
        map.put("TEN_BENH_NHAN", page1.get(0).get("TEN_BENH_NHAN"));
        map.put("TUOI_NUM", page1.get(0).get("TUOI_NUM"));
        map.put("GIOI_TINH_TEXT", page1.get(0).get("GIOI_TINH_TEXT"));
        map.put("GIOI_TINH_NUM", page1.get(0).get("GIOI_TINH_NUM"));
        map.put("CHANDOAN", page1.get(0).get("ICD_KHOADIEUTRI")+"-"+page1.get(0).get("TENICD_KHOADIEUTRI"));
        map.put("NGAYNHAPVIEN_DATE", page1.get(0).get("NGAYNHAPVIEN_DATE"));
        map.put("NGAYNHAPVIEN_VARCHAR", page1.get(0).get("NGAYNHAPVIEN_VARCHAR"));


        return map;
    }

    public @ResponseBody
    @RequestMapping(value = "/update-phieu-di-kem-mat")
    int updatePhieudikemMat(HttpSession session, HttpServletRequest request) {
        String dvtt = L2Utils.getDvtt(session);
        String ID_HSBA = request.getParameter("ID_HSBA");
        String SOVAOVIEN = request.getParameter("SOVAOVIEN");
        String SOVAOVIEN_DT = request.getParameter("SOVAOVIEN_DT");
        String MABENHNHAN = request.getParameter("MABENHNHAN");
        String MAT = request.getParameter("MAT");
        String CHIDINHPHAUTHUAT = request.getParameter("CHIDINHPHAUTHUAT");
        String PTVCHINH = request.getParameter("PTVCHINH");
        String PTVPHU = request.getParameter("PTVPHU");
        String BSGAYME = request.getParameter("BSGAYME");
        String PPVOCAM = request.getParameter("PPVOCAM");
        String LOAITHUOCTE = request.getParameter("LOAITHUOCTE");
        String NGAYGIAYTO = request.getParameter("NGAYGIAYTO");
        String PTVDUNGPHIEU = request.getParameter("PTVDUNGPHIEU");
        String GLOCOM = request.getParameter("GLOCOM");
        String PPPHAUTHUAT = request.getParameter("PPPHAUTHUAT");
        String LANPT = request.getParameter("LANPT");
        String NOIDUNGTTPT = request.getParameter("NOIDUNGTTPT");
        String NGAYPHAUTHUAT = request.getParameter("NGAYPHAUTHUAT");
        String MAPHIEUTTPT = request.getParameter("MAPHIEUTTPT");
        String LOAIPHIEUTTPT = request.getParameter("LOAIPHIEUTTPT");
        String MADV = request.getParameter("MADV");
        String TECNC = request.getParameter("TECNC");
        String TEBEMAT = request.getParameter("TEBEMAT");

        return voBenhAnNoiTruDAO.updatePhieudikemMat(
                dvtt,Integer.parseInt(ID_HSBA),Integer.parseInt(SOVAOVIEN),Integer.parseInt(SOVAOVIEN_DT),Integer.parseInt(MABENHNHAN)
                ,Integer.parseInt(MAT),CHIDINHPHAUTHUAT,PTVCHINH,PTVPHU,BSGAYME
                ,Integer.parseInt(PPVOCAM),LOAITHUOCTE,NGAYGIAYTO,PTVDUNGPHIEU,Integer.parseInt(GLOCOM)
                ,Integer.parseInt(PPPHAUTHUAT),Integer.parseInt(LANPT),NOIDUNGTTPT,NGAYPHAUTHUAT,MAPHIEUTTPT
                ,LOAIPHIEUTTPT,Integer.parseInt(MADV),TECNC,TEBEMAT
        );
    }

    @RequestMapping (value = "/print-view-phieu-di-kem-mat")
    public @ResponseBody
    void printTrangBiaVoBenhAnMat(HttpSession session, HttpServletRequest request, HttpServletResponse response){
        Thamsohethong tsht = (Thamsohethong) request.getSession().getAttribute("Sess_Thamso");

        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        String maphieu = request.getParameter("maphieu");
        String loaiphieu = request.getParameter("loaiphieu");
        String lsSoVaoVien = request.getParameter("soVaoVien");
        String lsSoVaoVienDt = request.getParameter("soVaoVienDT");
        String madv = request.getParameter("madv");
        Map ttkg = jdbcTemplate.queryForMap("call THONGTIN_KHOA_GIUONG_VBA(?,?,?)#c,s,s,s", new Object[]{L2Utils.getDvtt(session), lsSoVaoVien, lsSoVaoVienDt});
        List<Map<String, Object>> page1 = voBenhAnNoiTruDAO.getThongTinPage1byID(Integer.parseInt(request.getParameter("iD")));


        String tensoyte = "SỞ Y TẾ " + tsht.tinh.toUpperCase();
        String tenbenhvien = tsht.tenbenhvien.toUpperCase();

        String path = voBenhAnNoiTruDAO.getPathPDKVLG(loaiphieu, L2Utils.getDvtt(session));
        Map parameters = new HashMap();
        Map ttphieu = new HashMap();
        try {
            ttphieu = voBenhAnNoiTruDAO.ttPhieuDiKemMat(
                    L2Utils.getDvtt(session),request.getParameter("iD"), request.getParameter("soVaoVien"), request.getParameter("soVaoVienDT"),maphieu,loaiphieu,madv
            );
            parameters.put("CHANDOAN_TRUOCPT", ttphieu.get("CHANDOAN_TRUOCPT").toString());
            parameters.put("MAT", ttphieu.get("MAT").toString());
            parameters.put("CHIDINHPHAUTHUAT", ttphieu.get("CHIDINHPHAUTHUAT").toString());
            parameters.put("PTVCHINH", ttphieu.get("PTVCHINH").toString());
            parameters.put("PTVPHU", ttphieu.get("PTVPHU").toString());
            parameters.put("BSGAYME", ttphieu.get("BSGAYME").toString());
            parameters.put("PPVOCAM", ttphieu.get("PPVOCAM").toString());
            parameters.put("LOAITHUOCTE", ttphieu.get("LOAITHUOCTE").toString());
            parameters.put("NGAYGIAYTO_FORMAT", ttphieu.get("NGAYGIAYTO_FORMAT").toString());
            parameters.put("NGAYGIAYTO_REPORT", ttphieu.get("NGAYGIAYTO_REPORT").toString());
            parameters.put("PTVDUNGPHIEU", ttphieu.get("PTVDUNGPHIEU").toString());
            parameters.put("GLOCOM", ttphieu.get("GLOCOM").toString());
            parameters.put("LANPT", ttphieu.get("LANPT").toString());
            parameters.put("NOIDUNGTTPT", ttphieu.get("NOIDUNGTTPT").toString());
            parameters.put("NGAYPHAUTHUAT_TEXT", ttphieu.get("NGAYPHAUTHUAT_TEXT").toString());
            parameters.put("NGAYPHAUTHUAT", ttphieu.get("NGAYPHAUTHUAT").toString());
            parameters.put("MOTATECNC", ttphieu.get("MOTATECNC").toString());
            parameters.put("MOTATEBEMAT", ttphieu.get("MOTATEBEMAT").toString());
            parameters.put("PPPHAUTHUAT", ttphieu.get("PPPHAUTHUAT").toString());
            parameters.put("NHOMMAU", ttphieu.get("NHOMMAU").toString());
        }catch (Exception e){
        }

        parameters.put("tensoyte", tensoyte);
        parameters.put("tenbenhvien", tenbenhvien);

        parameters.put("SOYTE", tsht.tinh);
        parameters.put("TENKHOA", ttkg.get("TENKHOA").toString());
        parameters.put("PHONGBENH", ttkg.get("PHONGBENH").toString());
        parameters.put("GIUONG_BENH", ttkg.get("GIUONG_BENH").toString());
        parameters.put("TENBENHVIEN", tsht.tenbenhvien);
        parameters.put("TEN_BENH_NHAN", page1.get(0).get("TEN_BENH_NHAN"));
        parameters.put("TUOI_NUM", page1.get(0).get("TUOI_NUM"));
        parameters.put("GIOI_TINH_TEXT", page1.get(0).get("GIOI_TINH_TEXT"));
        parameters.put("GIOI_TINH_NUM", page1.get(0).get("GIOI_TINH_NUM"));
        parameters.put("GIOI_TINH_CHAR", page1.get(0).get("GIOI_TINH_CHAR"));
        parameters.put("CHANDOAN", page1.get(0).get("ICD_KHOADIEUTRI")+"-"+page1.get(0).get("TENICD_KHOADIEUTRI"));


        parameters.put("sovaovien", lsSoVaoVien);
        parameters.put("sovaovien_dt", lsSoVaoVienDt);
        parameters.put("dvtt", L2Utils.getDvtt(session));
        parameters.put("id_hsba", request.getParameter("iD"));
        parameters.put("SOBENHAN", page1.get(0).get("SOBENHAN"));
        parameters.put("NGAYNHAPVIEN_DATE", page1.get(0).get("NGAYNHAPVIEN_DATE"));
        parameters.put("NGAYNHAPVIEN_VARCHAR", page1.get(0).get("NGAYNHAPVIEN_VARCHAR"));
        parameters.put("maphieu", maphieu);
        parameters.put("loaiphieu", loaiphieu);
        parameters.put("madv", madv);


        File reportFile = new File(request.getSession().getServletContext().getRealPath(""+path));
        try {
            JasperHelper.printReportView("pdf", reportFile, parameters, dataSourceMNG.getConnection(), response);
            return;
        }catch (Exception e){
            return;
        }
    }
    @RequestMapping(value = "/update-benhan-ngoai-tru-chung", produces = "application/json; charset=utf-8", method = RequestMethod.POST)
    public @ResponseBody
    Map updateBANTchung(HttpServletRequest request, HttpSession session) {
        Map map = new HashMap();
        try {
            NamedParameterJdbcTemplate namedParameterJdbcTemplate = new NamedParameterJdbcTemplate(dataSourceMNG);
            MapSqlParameterSource params = new MapSqlParameterSource()
                    .addValue("iD", request.getParameter("ID"))
                    .addValue("benhAnNgoaiTruChung", request.getParameter("benhAnNgoaiTruChung"))
                    .addValue("ngayKhamBenh", request.getParameter("ngayKhamBenh"))
                    ;
            int update = namedParameterJdbcTemplate.update("UPDATE HIS_MANAGER.VBA_NGOAITRU_CHUNG " +
                    "SET THONGTIN_NGTRU_CHUNG_CLOB = :benhAnNgoaiTruChung,  NGAY_KHAMBENH = TO_DATE(:ngayKhamBenh, 'dd/MM/yyyy')" +
                    "WHERE ID = :iD", params);
            map.put("SUCCESS", update);
        }catch (Exception e){
            map.put("SUCCESS", 0);
            map.put("MESSAGE", e.getMessage());
        }
        return map;
    }

    public @ResponseBody
    @RequestMapping(value = "/update-benhan-methadone", produces = "application/json; charset=utf-8")
    Map updateBenhAnMethadone(@RequestBody BenhAnMethadoneObj methadoneObj, HttpSession session) {
        Map response = new HashMap();
        try {
            String userId = session.getAttribute("Sess_UserID") != null ? session.getAttribute("Sess_UserID").toString() : "";
            int result = voBenhAnNoiTruDAO.updateBenhAnMethadone(methadoneObj, userId);
            voBenhAnNoiTruDAO.log_update(Integer.parseInt(methadoneObj.getId()), L2Utils.getDvtt(session), L2Utils.getMaUser(session));
            response.put("SUCCESS", result);
            response.put("MESSAGE", ' ');
        } catch (Exception e) {
            response.put("SUCCESS", 0);
            response.put("MESSAGE", "" + e.getMessage());
        }
        return response;
    }

    @RequestMapping(value = "/vba-add-mau-phieu-di-kem", produces = "application/json; charset=utf-8")
    public @ResponseBody
    int vbaAddMauPhieuDiKem(@RequestParam(value = "idHsba") long idHsba,
                            @RequestParam(value = "idPhieuDiKem") String idPhieuDiKem,
                            @RequestParam(value = "tenMau") String tenMau,
                            @RequestParam(value = "loaiBenhAn") String loaiBenhAn,
                            HttpSession session) {
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        String userId = session.getAttribute("Sess_UserID") != null ? session.getAttribute("Sess_UserID").toString() : "";
        return voBenhAnNoiTruDAO.addMauPhieuDiKem(dvtt, idHsba, idPhieuDiKem, tenMau, userId, loaiBenhAn, "");
    }

    @RequestMapping(value = "/vba-get-list-phieu-di-kem", produces = "application/json; charset=utf-8")
    public @ResponseBody
    List vbaGetListPhieuDiKem(@RequestParam(value = "idPhieuDiKem") String idPhieuDiKem,
                              @RequestParam(value = "loaiBenhAn") String loaiBenhAn,
                              HttpSession session) {
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        String userId = session.getAttribute("Sess_UserID") != null ? session.getAttribute("Sess_UserID").toString() : "";
        return voBenhAnNoiTruDAO.getListMauPhieuDiKem(dvtt, idPhieuDiKem, userId, loaiBenhAn);
    }

    @RequestMapping(value = "/vba-get-info-by-id-loai-ba", produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map vbaGetInfoByIdAndLoaiBenhAn(@RequestParam(value = "idHsba") long idHsba,
                                    @RequestParam(value = "loaiBenhAn") String loaiBenhAn,
                                    HttpSession session) {
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        String userId = session.getAttribute("Sess_UserID") != null ? session.getAttribute("Sess_UserID").toString() : "";
        return voBenhAnNoiTruDAO.getInfoByIdAndLoaiBenhAn(dvtt, idHsba, loaiBenhAn);
    }

    @GetMapping(value = "/get-benhly-yhct-by-icd")
    public @ResponseBody
    Map getBenhLyByICD(HttpServletRequest request){
        Map mapResult = new HashMap();
        String icd = request.getParameter("icd").toString().toUpperCase();
        mapResult = voBenhAnNoiTruDAO.getBenhLyByICD(icd);
        if (mapResult.isEmpty()){
            mapResult.put("MA_YHCT", "");
            mapResult.put("TEN_BENH_LY", "");
            return mapResult;
        }
        return mapResult;
    }

    @RequestMapping(value = "/get-category-icd-yhct", method = RequestMethod.GET, produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map getDanhMucBenhYHCT(@RequestParam(value = "sidx") String sidx, @RequestParam(value = "page") String page, @RequestParam(value = "sord") String sord,
                           @RequestParam(value = "rows") String rows, @RequestParam(value = "searchTerm") String searchTerm, HttpSession session) {
        int page_Int = Integer.parseInt(page);
        int limit = Integer.parseInt(rows);
        if (sidx.equals("")) {
            sidx = "1";
        }

        searchTerm = tienich.removeAccent(searchTerm).toUpperCase();
        if (searchTerm.equals("")) {
            searchTerm = "%";
        } else {
            searchTerm = "%" + searchTerm + "%";
        }

        int c = voBenhAnNoiTruDAO.countSizeBenhLyYHCT(searchTerm);
        int total_pages;
        if (c > 0) {
            total_pages = (c / limit) + 1;
        } else {
            total_pages = 0;
        }

        if (page_Int > total_pages) {
            page_Int = total_pages;
        }
        int start = limit * page_Int - limit;
        List<Map<String, Object>> listBenhLy = voBenhAnNoiTruDAO.getCategoryBenhYHCT(searchTerm, sidx, sord, start, limit);
        if (!listBenhLy.isEmpty()) {
            Map m = new HashMap();
            m.put("records", c);
            m.put("total", total_pages);
            m.put("rows", listBenhLy);
            return m;
        }
        return new HashMap();
    }

    @RequestMapping(value = "/vba-update-thoi-gian-thuc-hien-ttpt", produces = "application/json; charset=utf-8")
    public @ResponseBody
    int updateThoiGianThucHienTTPTVBA(@RequestParam(value = "soVaoVien") long soVaoVien,
                                      @RequestParam(value = "soVaoVienDieuTri") long soVaoVienDieuTri,
                                      @RequestParam(value = "soPhieu") String soPhieu,
                                      @RequestParam(value = "maDichVu") long maDichVu,
                                      @RequestParam(value = "thoiGianThucHien") int thoiGianThucHien,
                                      HttpSession session) {
        String dvtt = session.getAttribute("Sess_DVTT").toString();
        return voBenhAnNoiTruDAO.updateThoiGianThucHienTTPTVBA(dvtt, soVaoVien, soVaoVienDieuTri, soPhieu, maDichVu, thoiGianThucHien);
    }

    @GetMapping(value = "/get-benhly-yhhd-by-icd")
    public @ResponseBody
    Map getBenhLyYHHDByICD(HttpServletRequest request){
        Map mapResult = new HashMap();
        String icd = request.getParameter("icd").toString().toUpperCase();
        mapResult = voBenhAnNoiTruDAO.getBenhLyYHHDByICD(icd);
        if (mapResult.isEmpty()){
            mapResult.put("MA_YHCT", "");
            mapResult.put("TENBENH_YHCT", "");
            mapResult.put("ICD10", "");
            mapResult.put("TENBENH_YHHD", "");
            return mapResult;
        }
        return mapResult;
    }

    @GetMapping(value = "/list-dich-vu-ttpt")
    public @ResponseBody
    List listDichVuTTPT(HttpServletRequest request,
                        HttpSession session){
        String dvtt = L2Utils.getDvtt(session);
        String maBenhNhan = request.getParameter("maBenhNhan").toString();
        String sttBenhAn = request.getParameter("sttBenhAn").toString();
        String sttDotDieuTri =  request.getParameter("sttDotDieuTri").toString();
        String idVoBenhAn =  request.getParameter("idVoBenhAn").toString();
        String codePhieuDiKem =  request.getParameter("codePhieuDiKem").toString();
        List listDichVu = voBenhAnNoiTruDAO.getListDichVuTTPT(dvtt, Integer.parseInt(maBenhNhan), sttBenhAn, sttDotDieuTri, Integer.parseInt(idVoBenhAn), codePhieuDiKem);
        return listDichVu;
    }

    @PostMapping(value = "/do-phieu-di-kem")
    @ResponseBody
    public ResponseEntity<Map> PhieuDiKemTTPT(@RequestBody String jsonBody,
                                              HttpServletRequest request,
                                              HttpSession session){
        Map<String, Object> mapResult = new HashMap<>();
        String dvtt = L2Utils.getDvtt(session);
        Thamsohethong tsht = (Thamsohethong) request.getSession().getAttribute("Sess_Thamso");

        JsonObject jsonParam = new JsonParser().parse(jsonBody).getAsJsonObject();
        String action = jsonParam.get("action").getAsString();
        jsonParam.addProperty("userId", L2Utils.getMaUser(session));

        if (action.equals("viewInit")){
            PhieuDiKemTTPTObj ttptObj = new PhieuDiKemTTPTObj(jsonParam);
            int init = voBenhAnNoiTruDAO.initPhieuDiKemTTPT(dvtt, ttptObj);
            if (init >= 0){
                try {
                    String sqlThongTinKhoaGiuong = "call THONGTIN_KHOA_GIUONG_VBA(?,?,?)#c,s,s,s";
                    JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
                    Map ttkg = jdbcTemplate.queryForMap(sqlThongTinKhoaGiuong, new Object[]{L2Utils.getDvtt(session), ttptObj.getSovaovien(), ttptObj.getSttDotDieuTri()});
                    List<Map<String, Object>> page1 = voBenhAnNoiTruDAO.getThongTinPage1byID((int) ttptObj.getIdVobenhan());
                    Map mapPatient = page1.get(0);
                    mapPatient.put("SOYTE", tsht.tinh);
                    mapPatient.put("TENBENHVIEN", tsht.tenbenhvien);
                    mapPatient.put("TENKHOA", ttkg.get("TENKHOA"));
                    mapPatient.put("PHONGBENH", ttkg.get("PHONGBENH"));
                    mapPatient.put("GIUONG_BENH", ttkg.get("GIUONG_BENH"));

                    mapResult.put("dataInfo",mapPatient);
                    mapResult.put("dataPhieu", voBenhAnNoiTruDAO.loadPhieuDiKemTTPT(dvtt, ttptObj.getMaBenhNhan(), ttptObj.getSttBenhAn(), ttptObj.getSttDotDieuTri(), ttptObj.getCodeMauPhieu(), ttptObj.getMaDV()));
                    mapResult.put("errorCode", HttpServletResponse.SC_OK);
                    mapResult.put("errorMessage", "Khởi tạo phiếu thành công!");
                    return ResponseEntity.ok(mapResult);
                }catch (Exception e){
                    mapResult.put("infoPatient", null);
                    mapResult.put("dataPhieu", null);
                    mapResult.put("errorCode", HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    mapResult.put("errorMessage", "Lỗi khởi tạo phiếu!");
                    return ResponseEntity.ok(mapResult);
                }
            }
        }

        if (action.equals("update")){
            PhieuDiKemTTPTObj ttptObj = new PhieuDiKemTTPTObj(jsonParam);
            int rsUpdate = voBenhAnNoiTruDAO.updPhieuDiKemTTPT(ttptObj);
            if (rsUpdate > 0){
                mapResult.put("errorCode", HttpServletResponse.SC_OK);
                mapResult.put("errorMessage", "Cập nhật thông tin phiếu thành công!");
                return ResponseEntity.ok(mapResult);
            }
        }

        if (action.equals("delete")){
            long idPhieu = jsonParam.get("idPhieuDiKem").getAsLong();
            int resDel = voBenhAnNoiTruDAO.delPhieuDiKemTTPT(idPhieu);
            if (resDel > 0){
                mapResult.put("errorCode", HttpServletResponse.SC_OK);
                mapResult.put("errorMessage", "Xóa phiếu thành công!");
                return ResponseEntity.ok(mapResult);
            }
        }
        mapResult.put("errorCode", HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        mapResult.put("errorMessage", "Thao tác thực hiện có lỗi xảy ra!");
        return ResponseEntity.ok(mapResult);
    }

    @RequestMapping (value = "/print-phieu-di-kem-ttpt")
    public @ResponseBody
    void printPhieuDiKemTTPT(HttpSession session,
                             HttpServletRequest request,
                             HttpServletResponse response){

        Thamsohethong tsht = (Thamsohethong) request.getSession().getAttribute("Sess_Thamso");
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        String idVoBenhAn = request.getParameter("idVoBenhAn");
        String loaiVoBenhAn = request.getParameter("loaiVoBenhAn");
        String idPhieuDiKem = request.getParameter("idPhieuDiKem");
        String maPhieuDiKem = request.getParameter("maPhieuDiKem");
        String soVaoVien = request.getParameter("soVaoVien");
        String soVaoVienDT = request.getParameter("soVaoVienDT");

        Map ttkg = jdbcTemplate.queryForMap("call THONGTIN_KHOA_GIUONG_VBA(?,?,?)#c,s,s,s", new Object[]{L2Utils.getDvtt(session), soVaoVien, soVaoVienDT});
        List<Map<String, Object>> page1 = voBenhAnNoiTruDAO.getThongTinPage1byID(Integer.parseInt(idVoBenhAn));

        String tensoyte = "SỞ Y TẾ " + tsht.tinh.toUpperCase();
        String tenbenhvien = tsht.tenbenhvien.toUpperCase();

        String path = voBenhAnNoiTruDAO.getPathReportPDK(loaiVoBenhAn, maPhieuDiKem, L2Utils.getDvtt(session));

        Map parameters = new HashMap();
        parameters.put("TENSOYTE", tensoyte);
        parameters.put("TENBENHVIEN", tenbenhvien);

        parameters.put("SOYTE", tsht.tinh);
        parameters.put("TENKHOA", ttkg.get("TENKHOA"));
        parameters.put("PHONGBENH", ttkg.get("PHONGBENH"));
        parameters.put("GIUONG_BENH", ttkg.get("GIUONG_BENH"));
        parameters.put("TENBENHVIEN", tsht.tenbenhvien);
        parameters.put("TEN_BENH_NHAN", page1.get(0).get("TEN_BENH_NHAN"));
        parameters.put("TUOI_NUM", page1.get(0).get("TUOI_NUM"));
        parameters.put("GIOI_TINH_CHAR", page1.get(0).get("GIOI_TINH_CHAR"));
        parameters.put("NGAYNHAPVIEN_DATE", page1.get(0).get("NGAYNHAPVIEN_DATE"));
        parameters.put("SOBENHAN",  page1.get(0).get("SOBENHAN"));
        parameters.put("IDPHIEUDIKEM", idPhieuDiKem);
        parameters.put("USER_LOGIN", L2Utils.getTenNV(session));

        File reportFile = new File(request.getSession().getServletContext().getRealPath(""+path));
        try {
            JasperHelper.printReport("pdf", reportFile, parameters, dataSourceMNG.getConnection(), response);
            return;
        }catch (Exception e){
            return;
        }
    }

    @RequestMapping(value = "/get-list-nhan-vien", method = RequestMethod.GET, produces = "application/json; charset=utf-8")
    public @ResponseBody
    Map getCategoryNhanVien(@RequestParam(value = "sidx") String sidx,
                           @RequestParam(value = "page") String page,
                           @RequestParam(value = "sord") String sord,
                           @RequestParam(value = "rows") String rows,
                           @RequestParam(value = "searchTerm") String searchTerm, HttpSession session) {
        int page_Int = Integer.parseInt(page);
        int limit = Integer.parseInt(rows);
        if (sidx.equals("")) {
            sidx = "1";
        }

        searchTerm = tienich.removeAccent(searchTerm).toUpperCase();
        if (searchTerm.equals("")) {
            searchTerm = "%";
        } else {
            searchTerm = "%" + searchTerm + "%";
        }

        int c = voBenhAnNoiTruDAO.countSizeListNhanVien(L2Utils.getDvtt(session), searchTerm);
        int total_pages;
        if (c > 0) {
            total_pages = (c / limit) + 1;
        } else {
            total_pages = 0;
        }

        if (page_Int > total_pages) {
            page_Int = total_pages;
        }
        int start = limit * page_Int - limit;
        List<Map<String, Object>> listNhanVien = voBenhAnNoiTruDAO.getCategoryNhanVien(L2Utils.getDvtt(session), searchTerm, sidx, sord, start, limit);
        if (!listNhanVien.isEmpty()) {
            Map m = new HashMap();
            m.put("records", c);
            m.put("total", total_pages);
            m.put("rows", listNhanVien);
            return m;
        }
        return new HashMap();
    }

    @GetMapping(value = "/list-lan-chuyen-khoa")
    public @ResponseBody
    List danhSachLanChuyenKhoa(HttpServletRequest request,
                        HttpSession session){
        String dvtt = L2Utils.getDvtt(session);
        int idVoBenhAn =  Integer.parseInt(request.getParameter("idVoBenhAn").toString()) ;
        return voBenhAnNoiTruDAO.getListChuyenKhoaDaKhoiTaoVBA(idVoBenhAn);
    }

    @PostMapping(value = "/do-lan-chuyen-khoa")
    @ResponseBody
    public ResponseEntity<Map> updateLanChuyenKhoa(@RequestBody String jsonBody,
                                                   HttpSession session){
        Map<String, Object> mapResult = new HashMap<>();
        String dvtt = L2Utils.getDvtt(session);
        JsonObject jsonParam = new JsonParser().parse(jsonBody).getAsJsonObject();
        String action = jsonParam.get("action").getAsString();
        int idVoBenhAn = jsonParam.get("idVobenhan").getAsInt();
        String rowData = "";
        if (action.equals("delete")){
            JsonArray jsonArrRowData =  jsonParam.get("rowData").getAsJsonArray();
            rowData = String.valueOf(jsonArrRowData);
        }
        if (action.equals("init")){
            List<Map<String, Object>> listchuyenKhoa = voBenhAnNoiTruDAO.dsChuyenKhoa(L2Utils.getDvtt(session), jsonParam.get("sovaovien").getAsString(), jsonParam.get("soVaoVienDT").getAsString());
            /*Lọc bỏ lần vào khoa đầu tiên*/
            listchuyenKhoa = listchuyenKhoa.stream().filter((Map<String, Object> x) -> !x.get("STT_LOGKHOAPHONG").toString().equals("1")).collect(Collectors.toList());
            rowData = JSONArray.toJSONString(listchuyenKhoa);
        }
        int result = voBenhAnNoiTruDAO.updateChuyenKhoaVBA(idVoBenhAn, rowData);
        if (result < 0){
            mapResult.put("errorCode", HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            mapResult.put("errorMessage", "Thao tác thực hiện có lỗi xảy ra!");
            return ResponseEntity.ok(mapResult);
        }
        mapResult.put("errorCode", HttpServletResponse.SC_OK);
        mapResult.put("errorMessage", "Thao tác thực hiện thành công!");
        return ResponseEntity.ok(mapResult);
    }

    public @ResponseBody
    @RequestMapping(value = "/get-pdt-dau-tien", method = RequestMethod.GET, produces = "application/json; charset=utf-8")
    Map loadThongTinPhieuDieuTriDauTien(HttpSession session,
                                        HttpServletRequest request,
                                        HttpServletResponse response) {
        try {
            String dvtt = session.getAttribute("Sess_DVTT").toString();
            String stt_benhan = request.getParameter("sttBenhAn");
            Map<String, Object> mapPhieuDiTri = voBenhAnNoiTruDAO.getThongTinPhieuDieuTriDauTien(dvtt, stt_benhan);
            return mapPhieuDiTri;
        } catch (Exception e) {
            return new HashMap();
        }
    }

    public @ResponseBody
    @RequestMapping(value = "/get-dacdiem-tre-sosinh", method = RequestMethod.GET, produces = "application/json; charset=utf-8")
    List getListDacDiemTreSoSinh(HttpSession session,
                                        HttpServletRequest request,
                                        HttpServletResponse response) {
        try {
            String dvtt = session.getAttribute("Sess_DVTT").toString();
            String soBenhAn = request.getParameter("soBenhAn");
            int soVaoVien = Integer.parseInt(request.getParameter("soVaoVien"));
            return voBenhAnNoiTruDAO.getListDacDiemTreSoSinh(dvtt, soBenhAn, soVaoVien);
        } catch (Exception e) {
            return new ArrayList();
        }
    }

    @PostMapping(value = "/do-dacdiem-tre-sosinh")
    @ResponseBody
    public ResponseEntity<Map> doDacDiemTreSoSinh(@RequestBody String jsonBody,
                                                   HttpSession session){
        Map<String, Object> mapResult = new HashMap<>();
        String dvtt = L2Utils.getDvtt(session);
        JsonObject jsonParam = new JsonParser().parse(jsonBody).getAsJsonObject();
        String action = jsonParam.get("action").getAsString();
        int idVoBenhAn = jsonParam.get("idVobenhan").getAsInt();
        int result = 0;
        if (action.equals("delete")){
            int treSoSinhId = jsonParam.get("idTreSoSinh").getAsInt();
           result = voBenhAnNoiTruDAO.delDacDiemTreSoSinh(treSoSinhId, dvtt);
        }

        if (action.equals("ghi")){
            long idTreSoSinh = jsonParam.get("idTreSoSinh").isJsonNull() ? 0 : jsonParam.get("idTreSoSinh").getAsLong();
            if (idTreSoSinh > 0){
                result= voBenhAnNoiTruDAO.updDacDiemTreSoSinh(jsonParam, dvtt);
            }else {
                result= voBenhAnNoiTruDAO.addDacDiemTreSoSinh(jsonParam, dvtt);
            }
        }
        mapResult.put("errorCode", result > 0 ? HttpServletResponse.SC_OK : -1);
        mapResult.put("errorMessage", result > 0 ? "Thao tác thực hiện thành công!" : "Thao tác thực hiện lỗi!");
        return ResponseEntity.ok(mapResult);
    }

    public @ResponseBody
    @RequestMapping(value = "/get-dacdiem-sorau", method = RequestMethod.GET, produces = "application/json; charset=utf-8")
    List getListDacDiemSoRau(HttpSession session,
                                 HttpServletRequest request,
                                 HttpServletResponse response) {
        try {
            String dvtt = session.getAttribute("Sess_DVTT").toString();
            String soBenhAn = request.getParameter("soBenhAn");
            int soVaoVien = Integer.parseInt(request.getParameter("soVaoVien"));
            return voBenhAnNoiTruDAO.getListDacDiemSoRau(dvtt, soBenhAn, soVaoVien);
        } catch (Exception e) {
            return new ArrayList();
        }
    }

    @PostMapping(value = "/do-dacdiem-sorau")
    @ResponseBody
    public ResponseEntity<Map> doDacDiemSoRau(@RequestBody String jsonBody,
                                                  HttpSession session){
        Map<String, Object> mapResult = new HashMap<>();
        String dvtt = L2Utils.getDvtt(session);
        JsonObject jsonParam = new JsonParser().parse(jsonBody).getAsJsonObject();
        String action = jsonParam.get("action").getAsString();
        int idVoBenhAn = jsonParam.get("idVoBenhAn").getAsInt();
        int result = 0;
        if (action.equals("delete")){
            int idSoRau = jsonParam.get("idSoRau").getAsInt();
            result = voBenhAnNoiTruDAO.delDacDiemSoRau(idSoRau, dvtt);
        }

        if (action.equals("ghi")){
            long idSoRau = jsonParam.get("idSoRau").isJsonNull() ? 0 : jsonParam.get("idSoRau").getAsLong();
            if (idSoRau > 0){
                result= voBenhAnNoiTruDAO.updDacDiemSoRau(jsonParam, dvtt);
            }else {
                result= voBenhAnNoiTruDAO.addDacDiemSoRau(jsonParam, dvtt);
            }
        }
        mapResult.put("errorCode", result > 0 ? HttpServletResponse.SC_OK : -1);
        mapResult.put("errorMessage", result > 0 ? "Thao tác thực hiện thành công!" : "Thao tác thực hiện lỗi!");
        return ResponseEntity.ok(mapResult);
    }

    public @ResponseBody
    @RequestMapping(value = "/get-list-dv-sinh-thuong", method = RequestMethod.GET, produces = "application/json; charset=utf-8")
    List getListDichVuThucHienSinhThuong(HttpSession session,
                             HttpServletRequest request,
                             HttpServletResponse response) {
        try {
            String dvtt = session.getAttribute("Sess_DVTT").toString();
            String soBenhAn = request.getParameter("soBenhAn");
            int soVaoVien = Integer.parseInt(request.getParameter("soVaoVien"));
            return voBenhAnNoiTruDAO.getListDichVuTTPTThucHienSinhThuong(dvtt, soBenhAn, soVaoVien);
        } catch (Exception e) {
            return new ArrayList();
        }
    }

    public @ResponseBody
    @RequestMapping(value = "/get-info-sinh-thuong", method = RequestMethod.GET, produces = "application/json; charset=utf-8")
    Map getThongTinSinhThuongByMaDichVu(HttpSession session,
                                         HttpServletRequest request,
                                         HttpServletResponse response) {
        try {
            String dvtt = session.getAttribute("Sess_DVTT").toString();
            String soBenhAn = request.getParameter("soBenhAn");
            int soVaoVien = Integer.parseInt(request.getParameter("soVaoVien"));
            String soPhieu = request.getParameter("soPhieu");
            int maDichVu = Integer.parseInt(request.getParameter("maDichVu"));
            return voBenhAnNoiTruDAO.getThongTinSinhThuongByMaDichVu(dvtt, soPhieu, soVaoVien, maDichVu);
        } catch (Exception e) {
            return new HashMap();
        }
    }

    public @ResponseBody
    @RequestMapping(value = "/upd-benhan-hscc-noi", produces = "application/json; charset=utf-8")
    Map updateBenhAnHSCCNoi(@RequestBody BenhAnHSCCNoiObj hsccNoiObj,
                            HttpSession session) {
        Map response = new HashMap();
        try {
            int result = voBenhAnNoiTruDAO.updBenhAnHoiSucCapCuuNoi(hsccNoiObj);
            response.put("SUCCESS", result);
            response.put("MESSAGE", ' ');
        } catch (Exception e) {
            response.put("SUCCESS", -1);
            response.put("MESSAGE", "" + e.getMessage());
        }
        return response;
    }
}