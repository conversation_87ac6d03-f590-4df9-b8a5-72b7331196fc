CREATE OR REPLACE FUNCTION cmu_update_ktv_xquang (
    p_dvtt            VARCHAR2,
    p_sophieu         VARCHAR2,
    p_ma_cdha         VARCHAR2,
    p_sovaovien       VARCHAR2,
    p_sovaovien_dt    VARCHAR2,
    p_nguoithuchien   VARCHAR2,
    p_mabsdockq       VARCHAR2
) RETURN VARCHAR2 IS

    v_ngaychidinh         DATE;
    v_thoigianketqua      DATE;
    v_return              NUMBER;
    v_thoigianbatdaucls   VARCHAR2(255) := '';
    p_ngaythuchien        VARCHAR2(255) := '';
    p_maben<PERSON>han number;
BEGIN
    IF p_sovaovien_dt = 0 THEN
SELECT
    TO_CHAR(ngay_th_yl, 'DD/MM/YYYY HH24:MI'),
    TO_CHAR(ngay_thuc_hien, 'dd/mm/yyyy hh24:mi'),
    ma<PERSON><PERSON><PERSON>
INTO
    v_thoigianbatdaucls,
    p_ngaythuchien,
    p_ma<PERSON><PERSON>han
FROM
    kb_cd_cdha_ct
WHERE
    sovaovien = p_sovaovien
  AND p_dvtt = dvtt
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_ma_cdha;

UPDATE kb_cd_cdha_ct
SET
    kythuatvien = p_nguoithuchien,
    ma_bs_doc_kq = p_mabsdockq
WHERE
    sovaovien = p_sovaovien
  AND p_dvtt = dvtt
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_ma_cdha;

ELSE
UPDATE noitru_cd_cdha_chi_tiet
SET
    kythuatvien = p_nguoithuchien,
    ma_bs_doc_kq = p_mabsdockq
WHERE
    sovaovien = p_sovaovien
  AND p_dvtt = dvtt
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_ma_cdha
  AND sovaovien_dt = p_sovaovien_dt;

SELECT
    TO_CHAR(ngay_th_yl, 'DD/MM/YYYY HH24:MI'),
    TO_CHAR(ngay_thuc_hien, 'dd/mm/yyyy hh24:mi'),
    mabenhnhan
INTO
    v_thoigianbatdaucls,
    p_ngaythuchien,
    p_mabenhnhan
FROM
    noitru_cd_cdha_chi_tiet
WHERE
    sovaovien = p_sovaovien
  AND sovaovien_dt = p_sovaovien_dt
  AND p_dvtt = dvtt
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_ma_cdha;

END IF;

    v_return := cmu_thoigian_nhanvien_ins
    (p_dvtt, p_nguoithuchien, p_sovaovien, p_mabenhnhan, case when p_sovaovien_dt = 0 then 0 else 1 end
    , v_thoigianbatdaucls, p_ngaythuchien, p_sophieu, p_ma_cdha, 'DT');

RETURN '0';
END;