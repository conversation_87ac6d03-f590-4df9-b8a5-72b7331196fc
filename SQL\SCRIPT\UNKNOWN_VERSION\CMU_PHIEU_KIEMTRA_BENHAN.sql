CREATE TABLE "HIS_MANAGER"."CMU_PHIEU_KIEMTRA_BENHAN"
(
    ID NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY
    , DVTT VARCHAR2(255 BYTE)
    , MA_BENHNHAN VARCHAR2(255 BYTE)
    , DATA_PHIEU CLOB
    , CREATEDDATE TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
    , SOVAOVIEN number
    , SO<PERSON><PERSON><PERSON><PERSON>_DT NUMBER
    , STT_BENHAN VARCHAR2(255 BYTE)
    , NGUOI_TAO VARCHAR2(255 BYTE)
    , KHOA_TAO VARCHAR2(255 BYTE)
);
create index CMU<PERSON>H<PERSON><PERSON><PERSON><PERSON><PERSON>RA<PERSON>NHANINDEX1 ON "HIS_MANAGER"."CMU_PHIEU_KIEMTRA_BENHAN"(DVTT, SOVAOVI<PERSON>);