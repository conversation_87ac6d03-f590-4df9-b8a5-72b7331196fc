create or replace FUNCTION cmu_qt_dsbn_tt (
    p_tungay      IN            VARCHAR2,
    p_denngay     IN            VARCHAR2,
    p_trangthai   IN            VARCHAR2,
    p_nhanvien    IN            VARCHAR2,
    p_dvtt        IN            VARCHAR2
) RETURN SYS_REFCURSOR IS

    v_tungay    DATE := TO_DATE(p_tungay || ':00', 'dd/mm/yyyy hh24:mi:ss');
    v_denngay   DATE := TO_DATE(p_denngay || ':59', 'dd/mm/yyyy hh24:mi:ss');
    cur         SYS_REFCURSOR;
BEGIN
    IF p_trangthai = 1 THEN
        OPEN cur FOR SELECT
                                don.dvtt,
                                don.ma<PERSON><PERSON><PERSON>,
                                don.mad<PERSON><PERSON><PERSON>,
                                don.<PERSON><PERSON><PERSON><PERSON>,
                                don.nguoitao,
                                don.ngaytao,
                                don.trangthai,
                                don.sovaovien,
                                don.diachi,
                                don.hinhth<PERSON>,
                                don.id_tie<PERSON><PERSON><PERSON>,
                                don.sophieuthanhtoan,
                                round(SUM(ct.dongia * ct.soluong)) thanhtien,
                                case when bank.TT_THANHTOAN_QRCODE = 1 then 1 else 0 end trangthaichuyenkhoan
                     FROM
                                cmu_qt_bn_banle      don
                                    INNER JOIN cmu_qt_ct_donthuoc   ct ON don.dvtt = ct.dvtt
                                    AND don.madonthuoc = ct.madonthuoc
                                    LEFT JOIN BANK_PAYMENT bank on don.dvtt = bank.dvtt and 'NHATHUOC_' || don.MADONTHUOC = bank.NOIDUNG
                                    and bank.ngay_tao BETWEEN v_tungay AND v_denngay
                     WHERE
                                don.dvtt = p_dvtt
                       AND don.ngaytao BETWEEN v_tungay AND v_denngay
                       AND don.trangthai = 1
                       AND ( don.nguoitao = p_nhanvien
                         OR p_nhanvien = 0 )
                       AND ct.mathanhtoan IS NOT NULL
                       and nvl(bank.TT_THANHTOAN_QRCODE, 0) = 0
                     GROUP BY
                         don.dvtt,
                         don.mabenhnhan,
                         don.madonthuoc,
                         don.tenbenhnhan,
                         don.nguoitao,
                         don.ngaytao,
                         don.trangthai,
                         don.sovaovien,
                         don.diachi,
                         don.hinhthuc,
                         don.id_tiepnhan,
                         don.sophieuthanhtoan,
                         bank.TT_THANHTOAN_QRCODE
                     ORDER BY
                                don.ngaytao DESC;

ELSIF p_trangthai = 0 THEN
        OPEN cur FOR SELECT
                               don.dvtt,
                               don.mabenhnhan,
                               don.madonthuoc,
                               don.tenbenhnhan,
                               don.nguoitao,
                               don.ngaytao,
                               don.trangthai,
                               don.sovaovien,
                               don.diachi,
                               don.hinhthuc,
                               don.id_tiepnhan,
                               don.sophieuthanhtoan,
                               round(SUM(ct.dongia * ct.soluong)) thanhtien,
                               case when bank.TT_THANHTOAN_QRCODE = 1 then 1 else 0 end trangthaichuyenkhoan
                     FROM
                               cmu_qt_bn_banle      don
                                   INNER JOIN cmu_qt_ct_donthuoc   ct ON don.dvtt = ct.dvtt
                                   AND don.madonthuoc = ct.madonthuoc
                                   LEFT JOIN BANK_PAYMENT bank on don.dvtt = bank.dvtt and 'NHATHUOC_' || don.MADONTHUOC = bank.NOIDUNG
                                   and bank.ngay_tao BETWEEN v_tungay AND v_denngay
                     WHERE
                               don.dvtt = p_dvtt
                       AND don.ngaytao BETWEEN v_tungay AND v_denngay
                       AND don.trangthai = 1
                       AND ( don.nguoitao = p_nhanvien
                         OR p_nhanvien = 0 )
                       AND ct.mathanhtoan IS NULL
                     GROUP BY
                         don.dvtt,
                         don.mabenhnhan,
                         don.madonthuoc,
                         don.tenbenhnhan,
                         don.nguoitao,
                         don.ngaytao,
                         don.trangthai,
                         don.sovaovien,
                         don.diachi,
                         don.hinhthuc,
                         don.id_tiepnhan,
                         don.sophieuthanhtoan,
                         bank.TT_THANHTOAN_QRCODE
                     ORDER BY
                               don.ngaytao DESC;
ELSE
        OPEN cur FOR SELECT
                         don.dvtt,
                         don.mabenhnhan,
                         don.madonthuoc,
                         don.tenbenhnhan,
                         don.nguoitao,
                         don.ngaytao,
                         don.trangthai,
                         don.sovaovien,
                         don.diachi,
                         don.hinhthuc,
                         don.id_tiepnhan,
                         don.sophieuthanhtoan,
                         round(SUM(ct.dongia * ct.soluong)) thanhtien,
                         case when bank.TT_THANHTOAN_QRCODE = 1 then 1 else 0 end trangthaichuyenkhoan
                     FROM
                         cmu_qt_bn_banle      don
                             INNER JOIN cmu_qt_ct_donthuoc   ct ON don.dvtt = ct.dvtt
                             AND don.madonthuoc = ct.madonthuoc
                             LEFT JOIN BANK_PAYMENT bank on don.dvtt = bank.dvtt and 'NHATHUOC_' || don.MADONTHUOC = bank.NOIDUNG
                             and bank.ngay_tao BETWEEN v_tungay AND v_denngay
                     WHERE
                         don.dvtt = p_dvtt
                       AND don.ngaytao BETWEEN v_tungay AND v_denngay
                       AND don.trangthai = 1
                       AND ( don.nguoitao = p_nhanvien
                         OR p_nhanvien = 0 )
                       AND ct.mathanhtoan IS NOT NULL
                       and bank.TT_THANHTOAN_QRCODE = 1
                     GROUP BY
                         don.dvtt,
                         don.mabenhnhan,
                         don.madonthuoc,
                         don.tenbenhnhan,
                         don.nguoitao,
                         don.ngaytao,
                         don.trangthai,
                         don.sovaovien,
                         don.diachi,
                         don.hinhthuc,
                         don.id_tiepnhan,
                         don.sophieuthanhtoan,
                         bank.TT_THANHTOAN_QRCODE
                     ORDER BY
                         don.ngaytao DESC;
END IF;

RETURN cur;
END;