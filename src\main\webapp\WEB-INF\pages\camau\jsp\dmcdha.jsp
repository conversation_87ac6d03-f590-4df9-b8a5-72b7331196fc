<%@ page import="l2.ThamSoManager" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="google-site-verification" content="u6uNEfD4cb3gidezi4r_6aI8Wb1E07-ufBeCQpvmlqQ"/>
    <title>Hệ thống chăm sóc sức khỏe</title>
    <link rel="icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
    <link rel="shortcut icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
    <link href="<c:url value="/resources/css/divheader.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/css/style_new.css" />" rel="stylesheet"/>

    <link rel="stylesheet" href="<c:url value="/resources/css/jquery-ui-redmond.1.9.1.css" />" />
    <script src="<c:url value="/resources/js/jquery.min.1.8.3.js" />"></script>
    <script src="<c:url value="/resources/js/jquery-ui.1.9.1.js" />"></script>
    <link href="<c:url value="/resources/bootstrap-4.1.3/dist/css/bootstrap.min.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/jqgrid/css/ui.jqgrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/jqgrid/js/i18n/grid.locale-en.js" />"></script>
    <script src="<c:url value="/resources/jqgrid/js/jquery.jqGrid.src.js" />"></script>
    <script src="<c:url value="/resources/js/common_function.js" />"></script>
    <script src="<c:url value="/resources/js/jquery.inputmask.bundle.min.js" />"></script>
    <script src="<c:url value="/resources/blockUI/jquery.blockUI.js" />"></script>
    <script src="<c:url value="/resources/dialog/jquery.alerts.js" />"></script>
    <link href="<c:url value="/resources/dialog/jquery.alerts.1.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/camau/popper.1.11.0.js" />"></script>
    <script src="<c:url value="/resources/camau/js/lodash.min.js" />"></script>
    <script src="<c:url value="/resources/camau/js/common.js" />"></script>
    <link rel="stylesheet" href="<c:url value="/resources/font-awesome-4.7.0/css/font-awesome.min.css"/>">
    <script src="<c:url value="/resources/bootstrap-4.4.1-dist/js/bootstrap.min.js"/>" ></script>
    <link href="<c:url value="/resources/camau/css/khambenhnoitru.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/camau/css/formio.full.min.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/camau/css/loader.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/camau/css/custom.css" />" rel="stylesheet"/>
    <link rel="stylesheet" href="<c:url value="/resources/camau/css/select2.min.css" />" />
    <script src="<c:url value="/resources/camau/js/select2.min.js" />"></script>
    <script src="<c:url value="/resources/js/datetimepicker.js" />"></script>
    <link rel="stylesheet" href="<c:url value="/resources/css/datetimepicker.css" />" />
    <script src="<c:url value="/resources/js/jquery-confirm.min.js" />"></script>
    <link href="<c:url value="/resources/css/jquery-confirm.min.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/combogrid/css/smoothness/jquery.ui.combogrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/combogrid/plugin/jquery.ui.combogrid-1.6.3.js" />"></script>
    <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
    <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/camau/smartca769.js" />"></script>
    <script src="<c:url value="/resources/camau/js/jquery.validate.min.js" />"></script>
    <script src="<c:url value="/resources/camau/js/formio.full.min.js" />"></script>
    <script src="<c:url value="/resources/camau/material/moment.js" />"></script>
    <script src="<c:url value="/resources/camau/js/jszip.js" />"></script>
    <script src="<c:url value="/resources/camau/js/xlsx.js" />"></script>

    <style>
        .row {
            margin-left: 0px;
            margin-right: 0px;
        }
        /* CSS Toggle Checkbox */
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 31px;
            float:left;
        }
        .switch input {display:none;}
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            -webkit-transition: .4s;
            transition: .4s;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 22.5px;
            width: 22.5px;
            left: 4px;
            bottom: 4.5px;
            background-color: white;
            -webkit-transition: .4s;
            transition: .4s;
        }
        input.primary:checked + .slider {
            background-color: #2196F3;
        }
        input:focus + .slider {
            box-shadow: 0 0 1px #2196F3;
        }
        input:checked + .slider:before {
            -webkit-transform: translateX(30px);
            -ms-transform: translateX(30px);
            transform: translateX(30px);
        }
        .slider.round {
            border-radius: 34px;
        }
        .slider.round:before {
            border-radius: 50%;
        }
        label .labelnobottom {
            margin-bottom: 0;
        }

        .was-validated .form-control:valid {
            border-color: #ced4da !important;
        }

        fieldset.scheduler-border {
            border: 1px groove #ddd !important;
            padding: 0 1.4em 1.4em 1.4em !important;
            margin: 0 0 1.5em 0 !important;
            -webkit-box-shadow:  0px 0px 0px 0px #000;
            box-shadow:  0px 0px 0px 0px #000;
        }

        legend.scheduler-border {
            font-size: 1.2em !important;
            font-weight: bold !important;
            text-align: left !important;
            width:auto;
            padding:0 10px;
            border-bottom:none;
        }
        .ui-jqgrid .ui-jqgrid-htable th div
        {
            height: auto;
            overflow: hidden;
            padding-right: 4px;
            padding-top: 2px;
            position: relative;
            vertical-align: text-top;
            white-space: normal !important;
        }
        .modal {
            text-align: center;
        }
        body .modal-dialog {
            max-width: 100%;
            width: auto !important;
            display: inline-block;
            text-align: left;
        }
    </style>
    <script>
        function loadDSCDHA(){
            var hoatdong = $("#loadhoatdong").prop('checked') ? 1 : 0;
            var maloai = $("#loaicdha").val();
            var ttbhyt = $("#loadbhyt").prop('checked') ? 4 : 0;
            var url = "cmu_getlist?url="+convertArray(["${Sess_DVTT}", maloai, ttbhyt, hoatdong,'DM_CDHA_SELECT_THEOLOAI_CMU_V2']);
            $("#list_cdha").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
        }

        function loadLichSu(id){
            var url = "cmu_getlist?url="+convertArray(["${Sess_DVTT}", "CĐHA", id,'LICHSU_CLS_SELECT']);
            $("#list_lichsu").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
        }

        function loadDSLoaiCDHA(){
            $("#list_loaicdha").jqGrid('setGridParam', {datatype: 'json', url: "danhsachloaicdha"}).trigger('reloadGrid');
        }

        function fillOptions() {
            var ddl2 = $(".loaicdha");
            $.getJSON('danhsachloaicdha', function (data) {
                //$('option', ddl2).remove();
                if (data) {
                    $.each(data, function (i) {
                        ddl2.append($('<option/>').val(data[i].MA_LOAI_CDHA).text(data[i].MA_LOAI_CDHA + " - " + data[i].TEN_LOAI_CDHA));
                    });
                } else {
                    $('option', ddl2).remove();
                    ddl2.append($('<option/>').val('0').text("Chọn loại chẩn đoán hình ảnh"));
                }
            }).done(function(){
                $(".loaicdha").select2({dropdownAutoWidth : true, width: '100%'});
                loadDSCDHA();

            });
        }

        function loadAllSelect(){
            fillOptions();
            $.ajax({
                url: "cmu_list_GET_DM_MA_NHOM_XML_BHXH?url=" + convertArray([""])
            }).done(function (data) {
                if (data) {
                    $("#manhomCV9324").empty();
                    $("<option value=''></option>").appendTo("#manhomCV9324");
                    $.each(data, function (i) {
                        $("<option value='" + data[i].MA_NHOM + "'>" + data[i].MA_NHOM  + ' - ' + data[i].TEN_NHOM + "</option>").appendTo("#manhomCV9324");
                    });
                    $("#manhomCV9324").select2({dropdownAutoWidth : true, width: '100%'});
                }
            });
            $.ajax({
                url: "cmu_list_GET_DM_LOAIHINH_DICHVU?url=" + convertArray([""])
            }).done(function (data) {
                if (data) {
                    $("#loaidichvucdha").empty();
                    $("<option value=''>Chọn loại</option>").appendTo("#loaidichvucdha");
                    $.each(data, function (i) {
                        $("<option value='" + data[i].MA_LOAIHINH + "'>" + data[i].TEN_LOAIHINH + "</option>").appendTo("#loaidichvucdha");
                    });
                    $("#loaidichvucdha").select2({dropdownAutoWidth : true, width: '100%'});
                }
            });
            $.ajax({
                url: "cmu_list_DANHSACH_MAYCDHA_DMCDHA?url=" + convertArray(["${Sess_DVTT}"])
            }).done(function (data) {
                if (data) {
                    $("#maycdha").empty();
                    $("<option value='0'>Không sử dụng máy</option>").appendTo("#maycdha");
                    $.each(data, function (i) {
                        $("<option value='" + data[i].STT + "'>" + data[i].STT + ' - ' + data[i].TEN_MAY + "</option>").appendTo("#maycdha");
                    });
                    $("#maycdha").select2({dropdownAutoWidth : true, width: '100%'});
                }
            });
        }

        function loadDataCDHA(data){
            $("#tenloaicdha").val(data.TEN_LOAI_CDHA);
            $("#maloaicdha").val(data.MA_LOAI_CDHA);
            $("#tencdha").val(data.TEN_CDHA);
            $("#macdha").val(data.MA_CDHA);
            $("#tenhienthi").val(data.TEN_HIEN_THI);
            $("#gioihanchidinh").val(data.GIOI_HAN_CHI_DINH);
            $("#motacdha").val(data.MOTA_CDHA);
            $("#giakhongbaohiem").val(data.GIA_KHONGBAOHIEM_CMU);
            $("#giabaohiem").val(data.GIA_BAOHIEM_CMU);
            $("#giachenhlech").val(data.GIA_CHENHLECH_BHYT);
            $("#mabaocaobhyt").val(data.MA_DICHVU_KYTHUAT_DMDC);
            $("#manhomCV9324").val(data.MA_NHOM_9324).change();
            $("#ngayapdungCV9324").val(data.NGAY_HL_NHOM9234);
            $("#maycdha").val(data.STT_MA_MAYCDHA).change();
            $("#soquyetdinh").val(data.SOQUYETDINH);
            $("#machisodmdc").val(data.MA_CHI_SO_DMDC);
            $("#tenchisodmdc").val(data.TEN_CHI_SO_DMDC);
            $("#mabaocaobhyt_tt37").val(data.MA_THONGTU_37_DMDC);
            data.THUOC_TTPT == "1" ? $("#thuocttpt").attr("checked", true).change() : $("#thuocttpt").attr("checked", false).change();
            $("#ngayapdungttpt").val(data.NGAY_DUOC_XET_TTPT);
            data.HOATDONG == "Yes" ? $("#checkhoatdong").attr("checked", true).change() : $("#checkhoatdong").attr("checked", false).change();
            data.DOLOPER == "No" ? $("#doloper").attr("checked", false).change() : $("#doloper").attr("checked", true).change();
            Number(data.GIACOBH_TT13_TT39) == 0 ? $("#bhyt").attr("checked", false).change() : $("#bhyt").attr("checked", true).change();
            data.VET_THUONG_TP == "0" ? $("#vetthuongtaiphat").attr("checked", false).change() : $("#vetthuongtaiphat").attr("checked", true).change();
        }

        function init() {
            $(":input").inputmask();
            $(".input-date").inputmask({
                mask: "1/2/y",
                placeholder: "dd/mm/yyyy",
                alias: "date",
            });
            $(".input-date").datepicker();

            $(window).bind('resize', function () {
                var width = $('#div_cdha').width() - 5;
                $('#list_cdha').setGridWidth(width);
            });

            $(".select2input").select2({dropdownAutoWidth : true, width: '100%'});

            $("input[data-type='currency']").on({
                keyup: function() {
                    formatCurrency($(this));
                },
                blur: function() {
                    formatCurrency($(this), "blur");
                }
            });

            function formatNumber(n) {
                return n.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",")
            }

            function formatCurrency(input, blur) {
                var input_val = input.val();
                if (input_val === "") { return; }
                var original_len = input_val.length;
                var caret_pos = input.prop("selectionStart");
                if (input_val.indexOf(".") >= 0) {
                    var decimal_pos = input_val.indexOf(".");
                    var left_side = input_val.substring(0, decimal_pos);
                    var right_side = input_val.substring(decimal_pos);
                    left_side = formatNumber(left_side);
                    right_side = formatNumber(right_side);
                    if (blur === "blur") {
                        right_side += "00";
                    }
                    right_side = right_side.substring(0, 2);
                    input_val = left_side + "." + right_side;
                } else {
                    input_val = formatNumber(input_val);
                    input_val = input_val;
                    if (blur === "blur") {
                        input_val += ".00";
                    }
                }
                input.val(input_val);
                var updated_len = input_val.length;
                caret_pos = updated_len - original_len + caret_pos;
                input[0].setSelectionRange(caret_pos, caret_pos);
            }

            loadAllSelect();
        }

        function uploadFile(event) {
            this.file = event.target;
            var reader = new FileReader();
            reader.onload = function(){
                var fileData = reader.result;
                var wb = XLSX.read(fileData, {type : 'binary'});
                var gridData = {};
                var regexnumber = new RegExp('^[0-9]+$');
                wb.SheetNames.forEach(function(sheetName) {
                    var rowObj = XLSX.utils.sheet_to_row_object_array(wb.Sheets[sheetName]);
                    if (rowObj.length > 0) {
                        gridData = rowObj
                    }
                    rowObj.forEach(function(_object){
                        var flag = true;
                        var loi = "";
                        if(_object.TEN_CDHA != null){
                            if(isEmpty(_object.MA_LOAI_CDHA)) {
                                flag = false;
                                loi += '; MA_LOAI_CDHA không được trống';
                            } else {
                                if(!regexnumber.test((parseInt(_object.MA_LOAI_CDHA)))) {
                                    flag = false;
                                    loi += '; MA_LOAI_CDHA phải là dạng số';
                                }
                            }
                            if(isEmpty(_object.GIA_KHONGBAOHIEM)) {
                                flag = false
                                loi += '; GIA_KHONGBAOHIEM không được trống'
                                if(!regexnumber.test((parseInt(_object.GIA_BAOHIEM) || 0)) || !regexnumber.test((parseInt(_object.GIA_KHONGBAOHIEM) || 0)) ||
                                    !regexnumber.test((parseInt(_object.GIA_CHENHLECH) || 0))) {
                                    flag = false
                                    loi += '; Giá dịch vụ (bảo hiểm, không bảo hiểm, chênh lệch) phải là dạng số'
                                }
                            }
                            if(parseInt(_object.GIA_BAOHIEM) > 0){
                                var flagbhyt = true;
                                var arrloibh = [];
                                if(isEmpty(_object.MABAOCAO_BHYT)) {
                                    flagbhyt = false;
                                    arrloibh.push('MABAOCAO_BHYT');
                                }
                                if (isEmpty(_object.SOQUYETDINH)){
                                    flagbhyt = false;
                                    arrloibh.push('SOQUYETDINH');
                                }
                                if (isEmpty(_object.MAY_CDHA)){
                                    flagbhyt = false;
                                    arrloibh.push('MAY_CDHA');
                                }
                                if (isEmpty(_object.MA_CHI_SO_DMDC)){
                                    flagbhyt = false;
                                    arrloibh.push('MA_CHI_SO_DMDC');
                                }
                                if(flagbhyt == false){
                                    flag = false;
                                    loi += '; Khi GIA_BAOHIEM > 0 thì ' + arrloibh.join(', ') + ' không được trống';
                                }
                                if(!(isEmpty(_object.MAY_CDHA)) && !regexnumber.test(_object.MAY_CDHA.trim())) {
                                    flag = false
                                    loi += '; MAY_CDHA phải là dạng số'
                                }
                            }
                            if(isEmpty(_object.MA_NHOM_9324)) {
                                flag = false
                                loi += '; MA_NHOM_9324 không được trống'
                            }
                            if(!(isEmpty(_object.MA_NHOM_9324)) && !regexnumber.test(_object.MA_NHOM_9324.trim())) {
                                flag = false
                                loi += '; MA_NHOM_9324 phải là dạng số'
                            }
                            _object['LOI'] = loi.replace("; ", "");
                            if(flag == true) {
                                _object['LOI'] = 'OK'
                            }
                        }

                    })
                })
                var list = $("#ds_cdha_import_ls");
                list[0].grid.beginReq();
                list.jqGrid("clearGridData");
                list.jqGrid('setGridParam', { data: gridData});
                list[0].grid.endReq();
                list.trigger('reloadGrid');
            }
            reader.readAsBinaryString(this.file.files[0]);
        }

        function isEmpty(value) {
            return typeof value == 'string' && !value.trim() || typeof value == 'undefined' || value === null;
        }

        $(function () {
            // Start trang chủ
            var width = $('#div_cdha').width() - 10;
            init();
            $("#lammoi").click(function(){
                loadDSCDHA();
            });
            $("#loadbhyt").change(function(){
                loadDSCDHA();
            });
            $("#loadhoatdong").change(function(){
                loadDSCDHA();
            });
            $("#loaicdha").change(function(){
                loadDSCDHA();
            });

            $("#list_cdha").jqGrid({
                url: "",
                datatype: "local",
                loadonce: true,
                height: 500,
                width: width,
                cellEdit: true,
                cellsubmit: 'clientArray',
                colModel: [
                    {label:'GIOI_HAN_CHI_DINH', name: 'GIOI_HAN_CHI_DINH', index: 'GIOI_HAN_CHI_DINH', width: 10, hidden: true},

                    {label:'STT_MA_MAYCDHA', name: 'STT_MA_MAYCDHA', index: 'STT_MA_MAYCDHA', width: 10, hidden: true},
                    {label:'Mã CDHA', name: 'MA_CDHA', index: 'MA_CDHA', width: 50},
                    {label:'MA_LOAI_CDHA', name: 'MA_LOAI_CDHA', index: 'MA_LOAI_CDHA', width: 10, hidden: true},
                    {label:'dvtt', name: 'DVTT', index: 'DVTT', width: 10, hidden: true},
                    {label:'Tên CĐHA', name: 'TEN_CDHA', index: 'TEN_CDHA', width: 350, sorttype: 'string', searchoptions: {
                            dataInit: function (el) {
                                setTimeout(function () {
                                    $(el).focus().trigger({type: 'keypress', charCode: 13});
                                }, 20);
                            }
                        }
                    },
                    {label:'Tên hiển thị', name: 'TEN_HIEN_THI', index: 'TEN_HIEN_THI', width: 350, sorttype: 'string', searchoptions: {
                            dataInit: function (el) {
                                setTimeout(function () {
                                    $(el).focus().trigger({type: 'keypress', charCode: 13});
                                }, 20);
                            }
                        }
                    },
                    {label:'Mã DMDC', name: 'MA_CHI_SO_DMDC', index: 'TEN_CHI_SO_DMDC', width: 100},
                    {label:'Tên DMDC', name: 'TEN_CHI_SO_DMDC', index: 'TEN_CHI_SO_DMDC', width: 100},
                    {label:'Tên TT15', name: 'TEN_TT15', index: 'TEN_TT15', width: 300, sorttype: 'string', hidden: true},
                    {label:'Mô tả CĐHA', name: 'MOTA_CDHA', index: 'MOTA_CDHA', hidden: true},
                    {label:'Giá không bảo hiểm', name: 'GIA_KHONGBAOHIEM_CMU',
                        index: 'GIA_KHONGBAOHIEM_CMU',
                        width: 150,
                        align: "right",
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        summaryType: 'sum'
                    },
                    {label:'Đơn giá', name: 'GIA_CDHA',
                        index: 'GIA_CDHA',
                        width: 300,
                        align: "right",
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        summaryType: 'sum',
                        hidden: true
                    },
                    {label:'Tháng 3', name: 'GIA_THANG3_KOBAOHIEM',
                        index: 'GIA_THANG3_KOBAOHIEM',
                        editable: true,
                        width: 400,
                        align: "right",
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        summaryType: 'sum',
                        hidden: true
                    },
                    {label:'Tháng 7', name: 'GIA_THANG7_KOBAOHIEM',
                        index: 'GIA_THANG7_KOBAOHIEM',
                        editable: true,
                        width: 400,
                        align: "right",
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        summaryType: 'sum',
                        hidden: true
                    },
                    {label:'Giá 208=3 KBH', name: 'GIA_THANG10_KOBAOHIEM',
                        index: 'GIA_THANG10_KOBAOHIEM',
                        width: 350,
                        align: "right",
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        summaryType: 'sum',
                        hidden: true
                    },
                    {label:'Giá không bảo hiểm', name: 'GIAKHONGBH',
                        index: 'GIAKHONGBH',
                        width: 350,
                        align: "right",
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        summaryType: 'sum',
                        hidden: true
                    },
                    {label:'Giá không BH TT39', name: 'GIAKHONGBH_TT39',
                        index: 'GIAKHONGBH_TT39',
                        width: 350,
                        align: "right",
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        summaryType: 'sum',
                        hidden: true
                    },
                    {label:'Giá KBH TT37 ngày 15/01/2019', name: 'GIAKBH_TT37_TT39',
                        index: 'GIAKBH_TT37_TT39',
                        width: 350,
                        align: "right",
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        summaryType: 'sum',
                        hidden: true
                    },
                    {label:'Giá không bảo hiểm', name: 'GIAKOBH_TT14_TT39',
                        index: 'GIAKOBH_TT14_TT39',
                        width: 150,
                        align: "right",
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        summaryType: 'sum',
                        hidden: true
                    },
                    {label:'Giá bảo hiểm', name: 'GIA_BAOHIEM_CMU',
                        index: 'GIA_BAOHIEM_CMU',
                        width: 150,
                        align: "right",
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        summaryType: 'sum'
                    },
                    {label:'Tháng 3 có BH', name: 'GIA_THANG3',
                        index: 'GIA_THANG3',
                        editable: true,
                        width: 350,
                        align: "right",
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        summaryType: 'sum',
                        hidden: true
                    },
                    {label:'Tháng 7 có BH', name: 'GIA_THANG7',
                        index: 'GIA_THANG7',
                        editable: true,
                        width: 350,
                        align: "right",
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        summaryType: 'sum',
                        hidden: true
                    },
                    {label:'Giá 208=3 BH', name: 'GIA_THANG10',
                        index: 'GIA_THANG10',
                        width: 350,
                        align: "right",
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        summaryType: 'sum',
                        hidden: true
                    },
                    {label:'Giá có bảo hiểm', name: 'GIABAOHIEM',
                        index: 'GIABAOHIEM',
                        width: 350,
                        align: "right",
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        summaryType: 'sum',
                        hidden: true
                    },
                    {label:'Giá BH TT39', name: 'GIABAOHIEM_TT39',
                        index: 'GIABAOHIEM_TT39',
                        width: 350,
                        align: "right",
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        summaryType: 'sum',
                        hidden: true
                    },
                    {label:'Giá BH TT37 ngày 15/01/2019', name: 'GIACOBH_TT37_TT39',
                        index: 'GIACOBH_TT37_TT39',
                        width: 200,
                        align: "right",
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        summaryType: 'sum',
                        hidden: true
                    },
                    {label:'Giá bảo hiểm', name: 'GIACOBH_TT13_TT39',
                        index: 'GIACOBH_TT13_TT39',
                        width: 150,
                        align: "right",
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        summaryType: 'sum',
                        hidden: true
                    },
                    {label:'Tiền ngoài BHYT', name: 'TIEN_NGOAI_BHYT', index: 'TIEN_NGOAI_BHYT',
                        width: 350,
                        align: "right",
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        hidden: true
                    },
                    {label:'Giá chênh lệch', name: 'GIA_CHENHLECH_BHYT',
                        index: 'GIA_CHENHLECH_BHYT',
                        width: 150,
                        align: "right",
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        summaryType: 'sum'
                    },
                    {label:'Mã báo cáo', name: 'MABAOCAO', index: 'MABAOCAO', width: 150 , hidden: true},
                    {label:'trangthaibhyt', name: 'TRANGTHAI_BHYT', index: 'TRANGTHAI_BHYT', hidden: true},
                    {label:'STT Báo cáo', name: 'MABAOCAO_BYT', index: 'MABAOCAO_BYT', hidden: true},
                    {label:'Mã BHYT', name: 'MA_DICHVU_KYTHUAT_DMDC', index: 'MA_DICHVU_KYTHUAT_DMDC'},
                    {label:'Mã TT 43 50 DMDC', name: 'MA_THONGTU_43_50_DMDC', index: 'MA_THONGTU_43_50_DMDC', hidden: true},
                    {label:'Mã thông tư 37 ', name: 'MA_THONGTU_37_DMDC', index: 'MA_THONGTU_37_DMDC'},
                    {label:'Mã nhóm 9324/5917', name: 'MA_NHOM_9324', index: 'MA_NHOM_9324', width: 150},
                    {label:'Ngày áp dụng 9423/5917', name: 'NGAY_HL_NHOM9234', index: 'NGAY_HL_NHOM9234', width: 150},
                    {label:'THUOC_TTPT', name: 'THUOC_TTPT', index: 'THUOC_TTPT', hidden: true},
                    {label:'Là TTPT', name: 'THUOC_TTPT_CHU', index: 'THUOC_TTPT_CHU', width: 100},
                    {label:'Ngày được xét TTPT', name: 'NGAY_DUOC_XET_TTPT', index: 'NGAY_DUOC_XET_TTPT', width: 150},
                    {label:'Hoạt động', name: 'HOATDONG',
                        index: 'HOATDONG',
                        width: 100,
                        align: "center",
                        formatter: 'checkbox',
                        formatoptions: {value: 'true:false'}
                    },
                    {label:'Doloper', name: 'DOLOPER',
                        index: 'DOLOPER',
                        width: 100,
                        align: "center",
                        formatter: 'checkbox',
                        formatoptions: {value: 'true:false'}
                    },
                    {label:'LOAIHINH', name: 'LOAIHINH', index: 'LOAIHINH', hidden: true},
                    {label:'SOLANTHUCHIEN', name: 'SOLANTHUCHIEN', index: 'SOLANTHUCHIEN', hidden: true},
                    {label:'TRANGTHAI_BHYT_PVI', name: 'TRANGTHAI_BHYT_PVI', index: 'TRANGTHAI_BHYT_PVI', hidden: true},
                    {label:'Mã TT15', name: 'MA_TT15', index: 'MA_TT15', hidden: true},
                    {label:'DICHVU_TT37', name: 'DICHVU_TT37', index: 'DICHVU_TT37', hidden: true},
                    {label:'SUDUNG_TT37', name: 'SUDUNG_TT37', index: 'SUDUNG_TT37', hidden: true},
                    {label:'PHAN_THEO_GIOI_TINH', name: 'PHAN_THEO_GIOI_TINH', index: 'PHAN_THEO_GIOI_TINH', hidden: true},
                    {label:'TEN_NOIDUNG_6556', name: 'TEN_NOIDUNG_6556', index: 'TEN_NOIDUNG_6556', hidden: true},
                    {label:'soluongfilm', name: 'SOLUONGFILM', index: 'SOLUONGFILM', hidden: true},
                    {label:'VET_THUONG_TP', name: 'VET_THUONG_TP', index: 'VET_THUONG_TP', hidden: true},
                    {label:'DON_VI_DO', name: 'DON_VI_DO', index: 'DON_VI_DO', hidden: true},
                    {label:'TEN_LOAI_CDHA', name: 'TEN_LOAI_CDHA', index: 'TEN_LOAI_CDHA', hidden: true},
                    {label:'SOQUYETDINH', name: 'SOQUYETDINH', index: 'SOQUYETDINH', hidden: true},
                    {label:'MA_CHI_SO_DMDC', name: 'MA_CHI_SO_DMDC', index: 'MA_CHI_SO_DMDC', hidden: true},
                ],
                sortname: 'TEN_CDHA',
                sortorder: "asc",
                caption: "Chẩn đoán hình ảnh",
                ignoreCase: true,
                rowNum: 50,
                rowList: [50, 100, 150, 200],
                pager: "#jqGridPager",
                ondblClickRow : function(id) {
                    if (id) {
                        var ret = $("#list_cdha").jqGrid('getRowData', id);
                        $("#loaicdha").val(ret.MA_LOAI_CDHA).change();
                        $('#formchitiet')[0].reset();
                        $("#chuyenloaicdha").attr("disabled", false);
                        $("#checkhoatdong").attr("disabled", false);
                        $("#themcdha").attr("hidden", true);
                        $("#luucdha").attr("hidden", false);
                        $("#titleChiTiet").html("Cập nhật CĐHA " + ret.TEN_CDHA + " - " + $("#loaicdha option:selected" ).text());
                        $("#modalChiTiet").modal("show");
                        loadDataCDHA(ret);
                    }
                },
                onRightClickRow: function (id1) {
                    if (id1) {
                        $("#list_cdha").jqGrid('setSelection', id1);
                        $.contextMenu({
                            selector: '#list_cdha tr',
                            callback: function (key, options) {
                                if (key == "chuyenloai") {
                                    var id = $("#list_cdha").jqGrid('getGridParam', 'selrow');
                                    var ret = $("#list_cdha").jqGrid('getRowData', id);
                                    $.confirm({
                                        title: 'Xác nhận!',
                                        type: 'orange',
                                        content: 'Bạn có chắc chắn muốn chuyển loại chẩn đoán hình ảnh này?',
                                        buttons: {
                                            warning: {
                                                btnClass: 'btn-warning',
                                                text: "Tiếp tục",
                                                action: function(){
                                                    $("#loaicdhacu").val(ret.TEN_LOAI_CDHA);
                                                    $("#macdha_chuyen").val(ret.MA_CDHA);
                                                    $("#modalChuyenLoaiCDHA").modal("show");
                                                }
                                            },
                                            cancel: function () {
                                                hideSelfLoading(idButton)
                                            }
                                        }
                                    });
                                }
                                if (key == "xemlichsu") {
                                    var id = $("#list_cdha").jqGrid('getGridParam', 'selrow');
                                    var ret = $("#list_cdha").jqGrid('getRowData', id);
                                    loadLichSu(ret.MA_CDHA);
                                    $("#modalXemLichSu").modal("show");
                                }
                            },
                            items: {
                                "chuyenloai": {name: '<p style="color:red"><i class="fa fa-refresh" aria-hidden="true"></i> Chuyển loại</p>'},
                                "xemlichsu": {name: '<p style="color:green"><i class="fa fa-history" aria-hidden="true"></i> Xem lịch sử</p>'},
                            }
                        });
                    }
                },
            });
            $("#list_cdha").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});

            $("#themmoi").click(function(){
                $('#formchitiet')[0].reset();
                if($("#loaicdha").val() == "-1" || $("#loaicdha").val() == null){
                    notifiToClient("Red", "Vui lòng chọn loại chẩn đoán hình ảnh");
                } else {
                    $("#titleChiTiet").html("Thêm CĐHA - " + $("#loaicdha option:selected" ).text());
                    $("#modalChiTiet").modal("show");
                    $("#chuyenloaicdha").attr("disabled", true);
                    $("#checkhoatdong").attr("disabled", true);
                    $("#themcdha").attr("hidden", false);
                    $("#luucdha").attr("hidden", true);
                    $("#tenloaicdha").val($("#loaicdha option:selected" ).text());
                    $("#maloaicdha").val($("#loaicdha").val());
                    $("#manhomCV9324").val(2).trigger("change");
                    $(".input-date").val("${ngayhientai}");
                    $("#ngayapdungttpt").val("");
                }
            });
            // End trang chủ

            // Start chi tiết cđha
            $("#bhyt").change(function(){
                if($(this).prop("checked") == 1){
                    $(".bhyt").attr("hidden", false);
                    $(".bhyt-input").attr("required", true);
                } else {
                    $(".bhyt").attr("hidden", true);
                    $(".bhyt-input").attr("required", false);
                    $("#maycdha").val("");
                    $("#loaibhyt").val(1).change();
                    $("#thuocttpt").attr("checked", false).change();
                    $("#mabaocaobhyt").val("");
                }
            });

            $("#thuocttpt").change(function(){
                if($(this).prop("checked") == 1){
                    $("#ngayapdungttpt").attr("hidden", false);
                    $("#ngayapdungttpt").attr("required", true);
                    $("#ngayapdungttpt").val("${ngayhientai}")
                } else {
                    $("#ngayapdungttpt").attr("hidden", true);
                    $("#ngayapdungttpt").attr("required", false);
                    $("#ngayapdungttpt").val("");
                }
            });

            $("#themcdha").click(function(){
                if($("#ngayapdungCV9324").val() != "" && isValidDate($("#ngayapdungCV9324").val()) == false){
                    notifiToClient("Red", "Ngày áp dụng nhóm 9324 không hợp lệ");
                    return;
                }
                if($("#ngayapdungCV9324").val() != "" && !$("#manhomCV9324").val()){
                    notifiToClient("Red", "Vui lòng chọn nhóm 9324");
                    return;
                }
                if(!$("#ngayapdungCV9324").val() && $("#manhomCV9324").val()){
                    notifiToClient("Red", "Vui lòng nhập ngày áp dụng nhóm 9324");
                    return;
                }
                if($('#formchitiet').valid()){
                    var vetThuongTaiPhat = !!$("#vetthuongtaiphat").prop("checked") ? 1 : 0;
                    var loaicdha = $("#maloaicdha").val();
                    var ten = $("#tencdha").val().trim();
                    var tenht = $("#tenhienthi").val().trim();
                    var motacdha = $("#motacdha").val();
                    var giakhongbaohiem = Number($("#giakhongbaohiem").val().replaceAll(",",""));
                    var tien_bhyt_khongchi = 0;
                    var mathongtu37 = "0"
                    if($("#bhyt").prop("checked") == 1){
                        var mabaocaobhyt = $("#mabaocaobhyt").val();
                        var giachenhlech = Number($("#giachenhlech").val().replaceAll(",",""));
                        var giabaohiem = Number($("#giabaohiem").val().replaceAll(",",""));
                        var machisodmdc = $("#machisodmdc").val();
                        var tenchisodmdc = $("#tenchisodmdc").val();
                        mathongtu37 = $("#mabaocaobhyt_tt37").val();
                    } else {
                        var mabaocaobhyt = "";
                        var giachenhlech = "0";
                        var giabaohiem = "0";
                        var machisodmdc = "";
                        var tenchisodmdc = "";
                    }
                    var ttdoloper = $("#doloper").prop('checked') == true ? 1 : 0;
                    var mabaocao5084 = "";
                    var thongtu37 = "2016-03-01";
                    var ma_dvkt_dmdc = mabaocaobhyt == "" ? "0" : mabaocaobhyt;
                    var ma_tt4350_dmdc = mabaocaobhyt == "" ? "0" : mabaocaobhyt;
                    var ma_tt37_dmdc = mathongtu37;
                    var nhom9324 = $("#manhomCV9324").val();
                    var ngaycapnhat_nhom9324 = $("#ngayapdungCV9324").val();
                    var soluongfilm = 0;
                    var duplicate = "";
                    var thuoc_ttpt = $("#thuocttpt").prop('checked') == true ? 1 : 0;
                    var ngayapdung_ttpt = $("#ngayapdungttpt").val() == "" ? "-1000000000" : $("#ngayapdungttpt").val();
                    var phan_theo_gioi_tinh = $("#phanloaigioitinh").val();
                    var maycdha = $("#maycdha").val() == "" || $("#maycdha").val() == null ? -10000 : $("#maycdha").val();
                    var loaibhyt = $("#loaibhyt").val();
                    var matt15 = "";
                    var tentt15 = "";
                    var dichvutt37 = 0;
                    var sudungtt37 = 0;
                    var ngayapdungtt15 = "2018-07-15";
                    var gioihanchidinh = 0;
                    var ngayapdungtt39 = "2018-12-15";
                    var TEN_NOIDUNG_6556 = "";
                    var ngayapdungtt13 = "2019-08-20";
                    var donvido = "";
                    var soquyetdinh = $("#soquyetdinh").val();
                    var loaihinh = $("#loaidichvucdha").val();
                    if($("#bhyt").prop("checked") == 1) {
                        if (Number(giabaohiem) > 0) {
                            $.post("cmu_post", {
                                url: ["${Sess_DVTT}",
                                    ten,
                                    motacdha,
                                    loaicdha,
                                    giakhongbaohiem,
                                    "1", // trangthai_bhyt
                                    mabaocaobhyt,
                                    mabaocaobhyt,
                                    giachenhlech,
                                    mabaocao5084,
                                    thongtu37,
                                    ma_dvkt_dmdc,
                                    ma_tt4350_dmdc,
                                    ma_tt37_dmdc,
                                    tenht,
                                    duplicate,
                                    tien_bhyt_khongchi,
                                    thuoc_ttpt,
                                    ngayapdung_ttpt,
                                    giabaohiem, //giathang10
                                    giakhongbaohiem, //giathang10kbh
                                    ttdoloper,
                                    maycdha,
                                    loaibhyt,
                                    giabaohiem,
                                    giakhongbaohiem,
                                    matt15,
                                    tentt15,
                                    dichvutt37,
                                    sudungtt37,
                                    ngayapdungtt15,
                                    gioihanchidinh,
                                    phan_theo_gioi_tinh,
                                    giabaohiem, // giabaohiem_tt39
                                    giakhongbaohiem, // giakbh_tt39
                                    ngayapdungtt39,
                                    TEN_NOIDUNG_6556,
                                    giabaohiem, // p_giabaohiem_tt37_tt39
                                    giakhongbaohiem, // p_giakhongbh_tt37_tt39
                                    soluongfilm,
                                    giabaohiem, // p_giabaohiem_tt13
                                    giakhongbaohiem, // p_giakhongbh_tt13
                                    ngayapdungtt13,
                                    nhom9324,
                                    ngaycapnhat_nhom9324,
                                    vetThuongTaiPhat,
                                    donvido,
                                    soquyetdinh,
                                    machisodmdc,
                                    tenchisodmdc,
                                    loaihinh,
                                    "DM_CDHA_THEM_HT_F_CMU"].join("```")
                            }).done(function(data) {
                                var r = data.split(';;;');
                                if (r[0] == '0') {
                                    notifiToClient("Red", "Chẩn đoán hình ảnh này đã tồn tại");
                                } else {
                                    var jsonluu = {
                                        hoatdong: 1,
                                        macls: r[1],
                                        ten: ten,
                                        motacdha: motacdha,
                                        loaicdha: loaicdha,
                                        giakhongbaohiem: giakhongbaohiem,
                                        mabaocaobhyt: mabaocaobhyt,
                                        giachenhlech: giachenhlech,
                                        tenht: tenht,
                                        thuoc_ttpt: thuoc_ttpt,
                                        ngayapdung_ttpt: ngayapdung_ttpt,
                                        giabaohiem: giabaohiem,
                                        ttdoloper: ttdoloper,
                                        maycdha: maycdha,
                                        loaibhyt: loaibhyt,
                                        phan_theo_gioi_tinh: phan_theo_gioi_tinh,
                                        nhom9324: nhom9324,
                                        ngaycapnhat_nhom9324: ngaycapnhat_nhom9324,
                                        vetThuongTaiPhat: vetThuongTaiPhat,
                                        soquyetdinh: soquyetdinh,
                                        loaihinh: loaihinh,
                                        machisodmdc: machisodmdc,
                                        tenchisodmdc: tenchisodmdc,
                                    }
                                    var arr = ['INSERT', 'Thêm CĐHA vào danh mục ' + loaicdha, ${Sess_DVTT}, JSON.stringify(jsonluu), "CĐHA", r[1], "${Sess_UserID}", 'LICHSU_CLS_INSERT'];
                                    $.post("cmu_post", {
                                        url: arr.join("```")
                                    })
                                    setTimeout(function () {
                                        $("#list_cdha").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                                        $("#modalChiTiet").modal("hide");
                                    }, 700);
                                    notifiToClient("Green", "Thêm chẩn đoán hình ảnh thành công");
                                }
                            });
                        } else {
                            notifiToClient("Red", "Giá bảo hiểm phải lớn hơn 0");
                        }
                    } else {
                        $.post("cmu_post", {
                            url: ["${Sess_DVTT}",
                                ten,
                                motacdha,
                                loaicdha,
                                giakhongbaohiem,
                                "1", // trangthai_bhyt
                                mabaocaobhyt,
                                mabaocaobhyt,
                                giachenhlech,
                                mabaocao5084,
                                thongtu37,
                                ma_dvkt_dmdc,
                                ma_tt4350_dmdc,
                                ma_tt37_dmdc,
                                tenht,
                                duplicate,
                                tien_bhyt_khongchi,
                                thuoc_ttpt,
                                ngayapdung_ttpt,
                                giabaohiem, //giathang10
                                giakhongbaohiem, //giathang10kbh
                                ttdoloper,
                                maycdha,
                                loaibhyt,
                                giabaohiem,
                                giakhongbaohiem,
                                matt15,
                                tentt15,
                                dichvutt37,
                                sudungtt37,
                                ngayapdungtt15,
                                gioihanchidinh,
                                phan_theo_gioi_tinh,
                                giabaohiem, // giabaohiem_tt39
                                giakhongbaohiem, // giakbh_tt39
                                ngayapdungtt39,
                                TEN_NOIDUNG_6556,
                                giabaohiem, // p_giabaohiem_tt37_tt39
                                giakhongbaohiem, // p_giakhongbh_tt37_tt39
                                soluongfilm,
                                giabaohiem, // p_giabaohiem_tt13
                                giakhongbaohiem, // p_giakhongbh_tt13
                                ngayapdungtt13,
                                nhom9324,
                                ngaycapnhat_nhom9324,
                                vetThuongTaiPhat,
                                donvido,
                                soquyetdinh,
                                machisodmdc,
                                tenchisodmdc,
                                loaihinh,
                                "DM_CDHA_THEM_HT_F_CMU"].join("```")
                        }).done(function(data) {
                            var r = data.split(';;;');
                            if (r[0] == '0') {
                                notifiToClient("Red", "Chẩn đoán hình ảnh này đã tồn tại");
                            } else {
                                var jsonluu = {
                                    hoatdong: 1,
                                    macls: r[1],
                                    ten: ten,
                                    motacdha: motacdha,
                                    loaicdha: loaicdha,
                                    giakhongbaohiem: giakhongbaohiem,
                                    mabaocaobhyt: mabaocaobhyt,
                                    giachenhlech: giachenhlech,
                                    tenht: tenht,
                                    thuoc_ttpt: thuoc_ttpt,
                                    ngayapdung_ttpt: ngayapdung_ttpt,
                                    giabaohiem: giabaohiem,
                                    ttdoloper: ttdoloper,
                                    maycdha: maycdha,
                                    loaibhyt: loaibhyt,
                                    phan_theo_gioi_tinh: phan_theo_gioi_tinh,
                                    nhom9324: nhom9324,
                                    ngaycapnhat_nhom9324: ngaycapnhat_nhom9324,
                                    vetThuongTaiPhat: vetThuongTaiPhat,
                                    soquyetdinh: soquyetdinh,
                                    loaihinh: loaihinh,
                                    machisodmdc: machisodmdc,
                                    tenchisodmdc: tenchisodmdc,
                                }
                                var arr = ['INSERT', 'Thêm CĐHA vào danh mục ' + loaicdha, ${Sess_DVTT}, JSON.stringify(jsonluu), "CĐHA", r[1], "${Sess_UserID}", 'LICHSU_CLS_INSERT'];
                                $.post("cmu_post", {
                                    url: arr.join("```")
                                })
                                setTimeout(function () {
                                    $("#list_cdha").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                                    $("#modalChiTiet").modal("hide");
                                }, 700);
                                notifiToClient("Green", "Thêm chẩn đoán hình ảnh thành công");
                            }
                        });
                    }
                } else {
                    notifiToClient("Red", "Vui lòng nhập đầy đủ thông tin yêu cầu!");
                }
            });

            $("#luucdha").click(function(){
                if($("#ngayapdungCV9324").val() != "" && isValidDate($("#ngayapdungCV9324").val()) == false){
                    notifiToClient("Red", "Ngày áp dụng nhóm 9324 không hợp lệ");
                    return;
                }
                if($("#ngayapdungCV9324").val() != "" && !$("#manhomCV9324").val()){
                    notifiToClient("Red", "Vui lòng chọn nhóm 9324");
                    return;
                }
                if(!$("#ngayapdungCV9324").val() && $("#manhomCV9324").val()){
                    notifiToClient("Red", "Vui lòng nhập ngày áp dụng nhóm 9324");
                    return;
                }
                if($('#formchitiet').valid()){
                    var ma_cdha = $("#macdha").val();
                    var hoatdong = $("#checkhoatdong").prop('checked') == true ? 1 : 0;
                    var vetThuongTaiPhat = !!$("#vetthuongtaiphat").prop("checked") ? 1 : 0;
                    var loaicdha = $("#maloaicdha").val();
                    var ten = $("#tencdha").val().trim();
                    var tenht = $("#tenhienthi").val().trim();
                    var motacdha = $("#motacdha").val();
                    var giakhongbaohiem = Number($("#giakhongbaohiem").val().replaceAll(",",""));
                    var tien_bhyt_khongchi = 0;
                    var mathongtu37 = "0"
                    if($("#bhyt").prop("checked") == 1){
                        var mabaocaobhyt = $("#mabaocaobhyt").val();
                        var giachenhlech = Number($("#giachenhlech").val().replaceAll(",",""));
                        var giabaohiem = Number($("#giabaohiem").val().replaceAll(",",""));
                        var machisodmdc = $("#machisodmdc").val();
                        var tenchisodmdc = $("#tenchisodmdc").val();
                        mathongtu37 = $("#mabaocaobhyt_tt37").val();
                    } else {
                        var mabaocaobhyt = "";
                        var giachenhlech = "0";
                        var giabaohiem = "0";
                        var machisodmdc = "";
                        var tenchisodmdc = "";
                    }
                    var ttdoloper = $("#doloper").prop('checked') == true ? 1 : 0;
                    var mabaocao5084 = "";
                    var thongtu37 = "2016-03-01";
                    var ma_dvkt_dmdc = mabaocaobhyt == "" ? "0" : mabaocaobhyt;
                    var ma_tt4350_dmdc = mabaocaobhyt == "" ? "0" : mabaocaobhyt;
                    var ma_tt37_dmdc = mathongtu37;
                    var nhom9324 = $("#manhomCV9324").val();
                    var ngaycapnhat_nhom9324 = $("#ngayapdungCV9324").val();
                    var soluongfilm = 0;
                    var thuoc_ttpt = $("#thuocttpt").prop('checked') == true ? 1 : 0;
                    var ngayapdung_ttpt = $("#ngayapdungttpt").val() == "" ? "-1000000000" : $("#ngayapdungttpt").val();
                    var phan_theo_gioi_tinh = $("#phanloaigioitinh").val();
                    var maycdha = $("#maycdha").val() == "" || $("#maycdha").val() == null ? -10000 : $("#maycdha").val();
                    var loaibhyt = $("#loaibhyt").val();
                    var matt15 = "";
                    var tentt15 = "";
                    var dichvutt37 = 0;
                    var sudungtt37 = 0;
                    var ngayapdungtt15 = "2018-07-15";
                    var gioihanchidinh = 0;
                    var ngayapdungtt39 = "2018-12-15";
                    var TEN_NOIDUNG_6556 = "";
                    var ngayapdungtt13 = "2019-08-20";
                    var donvido = "";
                    var soquyetdinh = $("#soquyetdinh").val();
                    var loaihinh = $("#loaidichvucdha").val();
                    var solanthuchien = "1";
                    if($("#bhyt").prop("checked") == 1) {
                        if (Number(giabaohiem) > 0) {
                            $.post("cmu_post", {
                                url: [ma_cdha,
                                    "${Sess_DVTT}",
                                    ten,
                                    hoatdong,
                                    giakhongbaohiem,
                                    "1", // trangthai_bhyt
                                    mabaocaobhyt,
                                    mabaocaobhyt,
                                    giachenhlech,
                                    mabaocao5084,
                                    motacdha,
                                    thongtu37,
                                    ma_dvkt_dmdc,
                                    ma_tt4350_dmdc,
                                    ma_tt37_dmdc,
                                    tenht,
                                    tien_bhyt_khongchi,
                                    thuoc_ttpt,
                                    ngayapdung_ttpt,
                                    giabaohiem, //giathang10
                                    giakhongbaohiem, //giathang10kbh
                                    ttdoloper,
                                    "<%= ThamSoManager.instance(session).getThamSoString("208","0")%>",
                                    loaihinh,
                                    solanthuchien,
                                    maycdha,
                                    loaibhyt,
                                    giabaohiem,
                                    giakhongbaohiem,
                                    matt15,
                                    tentt15,
                                    dichvutt37,
                                    sudungtt37,
                                    ngayapdungtt15,
                                    gioihanchidinh,
                                    phan_theo_gioi_tinh,
                                    giabaohiem, // giabaohiem_tt39
                                    giakhongbaohiem, // giakbh_tt39
                                    ngayapdungtt39,
                                    TEN_NOIDUNG_6556,
                                    giabaohiem, // p_giabaohiem_tt37_tt39
                                    giakhongbaohiem, // p_giakhongbh_tt37_tt39
                                    soluongfilm,
                                    giabaohiem, // p_giabaohiem_tt13
                                    giakhongbaohiem, // p_giakhongbh_tt13
                                    nhom9324,
                                    ngaycapnhat_nhom9324,
                                    vetThuongTaiPhat,
                                    donvido,
                                    soquyetdinh,
                                    machisodmdc,
                                    tenchisodmdc,
                                    "DM_CDHA_SUA_HT_F_CMU"].join("```")
                            }).done(function(data) {
                                if (data == '0') {
                                    notifiToClient("Red", "Tên hiển thị trùng với CĐHA khác");
                                } else if (data == '1') {
                                    var jsonluu = {
                                        hoatdong: hoatdong,
                                        ma_cdha: ma_cdha,
                                        ten: ten,
                                        tenht: tenht,
                                        motacdha: motacdha,
                                        loaicdha: loaicdha,
                                        giakhongbaohiem: giakhongbaohiem,
                                        mabaocaobhyt: mabaocaobhyt,
                                        giachenhlech: giachenhlech,
                                        giabaohiem: giabaohiem,
                                        ttdoloper: ttdoloper,
                                        maycdha: maycdha,
                                        loaibhyt: loaibhyt,
                                        nhom9324: nhom9324,
                                        ngaycapnhat_nhom9324: ngaycapnhat_nhom9324,
                                        thuoc_ttpt: thuoc_ttpt,
                                        ngayapdung_ttpt: ngayapdung_ttpt,
                                        phan_theo_gioi_tinh: phan_theo_gioi_tinh,
                                        vetThuongTaiPhat: vetThuongTaiPhat,
                                        soquyetdinh: soquyetdinh,
                                        loaihinh: loaihinh,
                                        machisodmdc: machisodmdc,
                                        tenchisodmdc: tenchisodmdc,
                                    }
                                    var arr = ['UPDATE', 'Cập nhật CĐHA vào danh mục ' + loaicdha, ${Sess_DVTT}, JSON.stringify(jsonluu), "CĐHA", ma_cdha, "${Sess_UserID}", 'LICHSU_CLS_INSERT'];
                                    $.post("cmu_post", {
                                        url: arr.join("```")
                                    })
                                    notifiToClient("Green", "Cập nhật chẩn đoán hình ảnh thành công");
                                    setTimeout(function () {
                                        $("#list_cdha").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                                        $("#modalChiTiet").modal("hide");
                                    }, 700);
                                } else notifiToClient("Red", "Cập nhật chẩn đoán hình ảnh thất bại");
                            });
                        } else {
                            notifiToClient("Red", "Giá bảo hiểm phải lớn hơn 0");
                        }
                    } else {
                        $.post("cmu_post", {
                            url: [ma_cdha,
                                "${Sess_DVTT}",
                                ten,
                                hoatdong,
                                giakhongbaohiem,
                                "1", // trangthai_bhyt
                                mabaocaobhyt,
                                mabaocaobhyt,
                                giachenhlech,
                                mabaocao5084,
                                motacdha,
                                thongtu37,
                                ma_dvkt_dmdc,
                                ma_tt4350_dmdc,
                                ma_tt37_dmdc,
                                tenht,
                                tien_bhyt_khongchi,
                                thuoc_ttpt,
                                ngayapdung_ttpt,
                                giabaohiem, //giathang10
                                giakhongbaohiem, //giathang10kbh
                                ttdoloper,
                                "<%= ThamSoManager.instance(session).getThamSoString("208","0")%>",
                                loaihinh,
                                solanthuchien,
                                maycdha,
                                loaibhyt,
                                giabaohiem,
                                giakhongbaohiem,
                                matt15,
                                tentt15,
                                dichvutt37,
                                sudungtt37,
                                ngayapdungtt15,
                                gioihanchidinh,
                                phan_theo_gioi_tinh,
                                giabaohiem, // giabaohiem_tt39
                                giakhongbaohiem, // giakbh_tt39
                                ngayapdungtt39,
                                TEN_NOIDUNG_6556,
                                giabaohiem, // p_giabaohiem_tt37_tt39
                                giakhongbaohiem, // p_giakhongbh_tt37_tt39
                                soluongfilm,
                                giabaohiem, // p_giabaohiem_tt13
                                giakhongbaohiem, // p_giakhongbh_tt13
                                nhom9324,
                                ngaycapnhat_nhom9324,
                                vetThuongTaiPhat,
                                donvido,
                                soquyetdinh,
                                machisodmdc,
                                tenchisodmdc,
                                "DM_CDHA_SUA_HT_F_CMU"].join("```")
                        }).done(function(data) {
                            if (data == '0') {
                                notifiToClient("Red", "Tên hiển thị trùng với CĐHA khác");
                            } else if (data == '1') {
                                var jsonluu = {
                                    hoatdong: hoatdong,
                                    ma_cdha: ma_cdha,
                                    ten: ten,
                                    tenht: tenht,
                                    motacdha: motacdha,
                                    loaicdha: loaicdha,
                                    giakhongbaohiem: giakhongbaohiem,
                                    mabaocaobhyt: mabaocaobhyt,
                                    giachenhlech: giachenhlech,
                                    giabaohiem: giabaohiem,
                                    ttdoloper: ttdoloper,
                                    maycdha: maycdha,
                                    loaibhyt: loaibhyt,
                                    nhom9324: nhom9324,
                                    ngaycapnhat_nhom9324: ngaycapnhat_nhom9324,
                                    thuoc_ttpt: thuoc_ttpt,
                                    ngayapdung_ttpt: ngayapdung_ttpt,
                                    phan_theo_gioi_tinh: phan_theo_gioi_tinh,
                                    vetThuongTaiPhat: vetThuongTaiPhat,
                                    soquyetdinh: soquyetdinh,
                                    loaihinh: loaihinh,
                                    machisodmdc: machisodmdc,
                                    tenchisodmdc: tenchisodmdc,
                                }
                                var arr = ['UPDATE', 'Cập nhật CĐHA vào danh mục ' + loaicdha, ${Sess_DVTT}, JSON.stringify(jsonluu), "CĐHA", ma_cdha, "${Sess_UserID}", 'LICHSU_CLS_INSERT'];
                                $.post("cmu_post", {
                                    url: arr.join("```")
                                })
                                notifiToClient("Green", "Cập nhật chẩn đoán hình ảnh thành công");
                                setTimeout(function () {
                                    $("#list_cdha").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                                    $("#modalChiTiet").modal("hide");
                                }, 700);
                            } else notifiToClient("Red", "Cập nhật chẩn đoán hình ảnh thất bại");
                        });
                    }
                } else {
                    notifiToClient("Red", "Vui lòng nhập đầy đủ thông tin yêu cầu!");
                }
            });
            // End chi tiết cđha

            // Start loại cdha
            $("#list_loaicdha").jqGrid({
                url: '',
                datatype: "json",
                loadonce: true,
                height: 312,
                width: null,
                shrinkToFit: false,
                colNames: ['Mã loại', "Loại CĐHA", "Mô tả", "hoatdong", "motacap2"],
                colModel: [
                    {name: 'MA_LOAI_CDHA', index: 'MA_LOAI_CDHA', width: 100},
                    {
                        name: 'TEN_LOAI_CDHA', index: 'TEN_LOAI_CDHA', width: 400, sorttype: 'string', searchoptions: {
                            dataInit: function (el) {
                                setTimeout(function () {
                                    $(el).focus().trigger({type: 'keypress', charCode: 13});
                                }, 20);
                            }
                        }
                    },
                    {name: 'MOTA_LOAI_CDHA', index: 'MOTA_LOAI_CDHA', width: 150, hidden: true},
                    {name: 'HOATDONG', index: 'HOATDONG', width: 60, hidden: true},
                    {name: 'MOTA_TEXT', index: 'MOTA_TEXT', width: 60, hidden: true}
                ],
                sortname: 'TEN_LOAI_CDHA',
                sortorder: "asc",
                caption: "Loại chẩn đoán hình ảnh",
                ignoreCase: true,
                rowNum: 100000,
                ondblClickRow: function (id) {
                    if (id) {
                        var ret = $("#list_loaicdha").jqGrid('getRowData', id);
                        $("#maloaicdha_loai").val(ret.MA_LOAI_CDHA);
                        $("#tenloaicdha_loai").val(ret.TEN_LOAI_CDHA);
                        $("#motacap1_loai").val(ret.MOTA_LOAI_CDHA);
                        $("#motacap2_loai").val(ret.MOTA_TEXT);
                    }
                },
                onRightClickRow: function (id1) {
                    if (id1) {
                        $("#list_loaicdha").jqGrid('setSelection', id1);
                        $.contextMenu({
                            selector: '#list_loaicdha tr',
                            callback: function (key, options) {
                                if (key == "xoa") {
                                    var id = $("#list_loaicdha").jqGrid('getGridParam', 'selrow');
                                    var ret = $("#list_loaicdha").jqGrid('getRowData', id);
                                    $.confirm({
                                        title: 'Xác nhận!',
                                        type: 'orange',
                                        content: 'Bạn có chắc chắn muốn xóa loại chẩn đoán hình ảnh này?',
                                        buttons: {
                                            warning: {
                                                btnClass: 'btn-warning',
                                                text: "Tiếp tục",
                                                action: function(){
                                                    $.post("cmu_post", {
                                                        url: [ret.MA_LOAI_CDHA,
                                                            "${Sess_DVTT}",
                                                            "DM_LOAICDHA_DELETE_F"
                                                        ].join("```")
                                                    }).done(function (dt) {
                                                        if(dt>0){
                                                            loadDSLoaiCDHA();
                                                            $('#formloaicdha')[0].reset();
                                                            fillOptions();
                                                            notifiToClient("Green", "Xóa loại chẩn đoán hình ảnh thành công");
                                                        } else {
                                                            notifiToClient("Red", "Loại chẩn đoán hình ảnh đã có dữ liệu, không thể xóa");
                                                        }
                                                    }).fail(function() {
                                                        notifiToClient("Red", "Xóa thất bại")
                                                    });
                                                }
                                            },
                                            cancel: function () {
                                                hideSelfLoading(idButton)
                                            }
                                        }
                                    });
                                }
                            },
                            items: {
                                "xoa": {name: '<p style="color:red"><i class="fa fa-trash-o" aria-hidden="true"></i> Xóa</p>'},
                            }
                        });
                    }
                },
            });
            $("#list_loaicdha").jqGrid('filterToolbar', {
                stringResult: true,
                searchOnEnter: false,
                defaultSearch: "cn"
            });

            $("#editloaicdha").click(function(){
                loadDSLoaiCDHA();
                $('#formloaicdha')[0].reset();
                $("#modalLoaiCDHA").modal("show");
            });

            $("#huy_loai").click(function(){
                loadDSLoaiCDHA();
                $('#formloaicdha')[0].reset();
            });

            $("#capnhat_loai").click(function (evt) {
                var maloai = $("#maloaicdha_loai").val();
                if(maloai == ""){
                    notifiToClient("Red", "Vui lòng chọn loại chẩn đoán hình ảnh để chỉnh sửa");
                } else {
                    if($('#formloaicdha').valid()){
                        var loaixn = $("#tenloaicdha_loai").val().trim();
                        var motacap1_loai = $("#motacap1_loai").val().trim();
                        var motacap2_loai = $("#motacap2_loai").val().trim();
                        var str = [maloai, loaixn, motacap1_loai, motacap2_loai, 0];
                        var url = "sua_loai_cdha?url=" + convertArray(str);
                        $.ajax({
                            url: url
                        }).done(function (data) {
                            if (data == '1') {
                                $('#formloaicdha')[0].reset();
                                fillOptions();
                                setTimeout(function () {
                                    $("#list_loaicdha").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                                }, 700);
                                notifiToClient("Green", "Cập nhật loại chẩn đoán hình ảnh thành công");
                            } else
                                notifiToClient("Red", "Cập nhật loại chẩn đoán hình ảnh thất bại");
                        });
                    }
                }
            });

            $("#themmoi_loai").click(function (evt) {
                var loaixn = $("#tenloaicdha_loai").val().trim();
                var mota = $("#motacap1_loai").val().trim();
                var motacap2 = $("#motacap2_loai").val().trim();
                if($('#formloaicdha').valid()){
                    var str = [loaixn, mota, motacap2, 0];
                    var url = "them_loai_cdha?url=" + convertArray(str);
                    $.ajax({
                        url: url
                    }).done(function (data) {
                        if (data == '1') {
                            $('#formloaicdha')[0].reset();
                            fillOptions();
                            setTimeout(function () {
                                $("#list_loaicdha").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                            }, 700);
                            notifiToClient("Green", "Thêm loại chẩn đoán hình ảnh thành công");
                        } else
                            notifiToClient("Red", "Loại chẩn đoán hình ảnh này đã tồn tại");
                    });
                }
            });
            // End loại cđha

            // Start chuyển loại cđha
            $("#chuyen_loai").click(function () {
                var ma_cdha = $("#macdha_chuyen").val();
                var ma_loai = $("#loaicdhamoi").val();
                var arr = [ma_cdha, ma_loai];
                var url = "chuyen_loai_cdha?url=" + convertArray(arr);
                $.ajax({
                    url: url
                }).done(function () {
                    $("#list_cdha").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                }).fail(function () {
                    $("#list_cdha").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                });
                $("#modalChuyenLoaiCDHA").modal("hide");
            });
            // End chuyển loại cđha

            // Start in danh sách
            $("#indanhsach").click(function(evt){
                $('#formxuatexcel')[0].reset();
                $("#modalXuatExcel").modal("show");
            });
            $("#xuatdanhsach").click(function (evt) {
                var loaibaocao = $("#loaibaocao").val();
                var trangthai = $("#trangthai").val();
                var url = "xuatfiledanhmuc_cdha?loai=" + loaibaocao + "&tt=" + trangthai;
                $(location).attr('href', url);
            });
            $("#xuatchitiet").click(function (evt) {
                var loaibaocao = $("#loaibaocao").val();
                var trangthai = $("#trangthai").val();
                var url = "xuatfiledanhmuc_cdha_cmu?loai=" + loaibaocao + "&tt=" + trangthai;
                $(location).attr('href', url);
            });
            // End in danh sách

            // Start import
            $("#import").click(function(evt){
                $('#ds_cdha_import_ls').jqGrid('clearGridData');
                $("#import_file").val("");
                $("#modalImport").modal("show");
            });

            $("#ds_cdha_import_ls").jqGrid({
                url: '',
                datatype: "local",
                // loadonce: true,
                height: 600,
                autowidth: true,
                shrinkToFit: true,
                colModel: [
                    {name:"LOI", label:"Lỗi", width: 100,
                        formatter: function (cellvalue, options, rowObject) {
                            var color ='green';
                            var color_text = 'white';
                            if (rowObject.LOI == 'OK') {
                                color = 'green';
                                color_text = 'white';
                            } else if (rowObject.LOI == 'IMPORT THÀNH CÔNG'){
                                color = 'yellow';
                                color_text = 'black';
                            } else {
                                color = 'red';
                                color_text = 'white';
                            }
                            return '<span class="cellWithoutBackground" style="background-color:' + color + ';font-weight:bold ;color:' + color_text + '">' + cellvalue + '</span>';
                        }},
                    {name:"TEN_CDHA", label:"Tên", width: 200},
                    {name:"TEN_HIEN_THI", label:"Tên hiển thị", width: 200},
                    {name:"MA_LOAI_CDHA", label:"Mã loại", width: 60},
                    {name:"GIA_BAOHIEM", label:"Giá BH", width: 100},
                    {name:"GIA_KHONGBAOHIEM", label:"Giá KBH", width: 100},
                    {name:"GIA_CHENHLECH", label:"Giá CL", width: 100},
                    {name:"MABAOCAO_BHYT", label:"Mã BHYT", width: 150},
                    {name:"SOQUYETDINH", label:"Số QĐ", align: 'center', width: 150},
                    {name:"MOTA_CDHA", label:"Mô tả", align: 'center', width: 80},
                    {name:"MA_NHOM_9324", label:"Mã nhóm 9324", width: 150},
                    {name:"MAY_CDHA", label:"Mã máy", width: 60},
                    {name:"MA_CHI_SO_DMDC", label:"Chỉ số DMDC", width: 100},
                ],
                rowNum: 10000000,
                viewrecords: true,
                rownumbers: true,
            });

            $("#btn_import").click(function(){
                $("#import_file").val("");
                var list = $('#ds_cdha_import_ls')
                var allRowsInGrid = list.jqGrid('getGridParam','data');
                var flag = true;
                var rows = list.getDataIDs();
                var dem = 0;
                for(var i=0;i<rows.length;i++)
                {
                    var row = list.getRowData(rows[i]);
                    var loi = $(row.LOI).html();
                    if(loi != 'OK'){
                        flag = false;
                    }
                    allRowsInGrid[i]['ID'] = rows[i];
                }
                if(!flag) {
                    notifiToClient("Red", "Dữ liệu có dòng bị lỗi, vui lòng chỉnh sửa lại trước khi import")
                } else {
                    var t = [];
                    var ajax = function(data) {
                        var vetThuongTaiPhat = 0;
                        var loaicdha = data.MA_LOAI_CDHA;
                        var ten = data.TEN_CDHA.trim();
                        var tenht = data.TEN_HIEN_THI.trim();
                        var motacdha = data.MOTA_CDHA.trim();
                        var giakhongbaohiem = data.GIA_KHONGBAOHIEM == undefined ? data.GIA_KHONGBAOHIEM : data.GIA_KHONGBAOHIEM.replace(/,/g, '');
                        var tien_bhyt_khongchi = 0;
                        if(Number(data.GIA_BAOHIEM.replaceAll(",","")) > 0){
                            var mabaocaobhyt = data.MABAOCAO_BHYT.trim();
                            var giachenhlech = data.GIA_CHENHLECH == undefined ? data.GIA_CHENHLECH : data.GIA_CHENHLECH.replace(/,/g, '');
                            var giabaohiem = data.GIA_BAOHIEM == undefined ? data.GIA_BAOHIEM : data.GIA_BAOHIEM.replace(/,/g, '');
                            var machisodmdc = isEmpty(data.MA_CHI_SO_DMDC) ? "" : data.MA_CHI_SO_DMDC.trim(); //abc
                            var tenchisodmdc = isEmpty(data.MA_CHI_SO_DMDC) ? "" : data.TEN_CDHA.trim();
                        } else {
                            var mabaocaobhyt = "";
                            var giachenhlech = "0";
                            var giabaohiem = "0";
                            var machisodmdc = "";
                            var tenchisodmdc = "";
                        }
                        var ttdoloper = 0;
                        var mabaocao5084 = "";
                        var thongtu37 = "2016-03-01";
                        var ma_dvkt_dmdc = mabaocaobhyt == "" ? "0" : mabaocaobhyt;
                        var ma_tt4350_dmdc = mabaocaobhyt == "" ? "0" : mabaocaobhyt;
                        var ma_tt37_dmdc = mabaocaobhyt == "" ? "0" : mabaocaobhyt;
                        var nhom9324 = data.MA_NHOM_9324.trim();
                        var ngaycapnhat_nhom9324 = "${ngayhientai}";
                        var soluongfilm = 0;
                        var duplicate = "";
                        var thuoc_ttpt = 0;
                        var ngayapdung_ttpt = "-1000000000";
                        var phan_theo_gioi_tinh = "-1";
                        var maycdha = data.MAY_CDHA;
                        var loaibhyt = "0";
                        var matt15 = "";
                        var tentt15 = "";
                        var dichvutt37 = 0;
                        var sudungtt37 = 0;
                        var ngayapdungtt15 = "2018-07-15";
                        var gioihanchidinh = 0;
                        var ngayapdungtt39 = "2018-12-15";
                        var TEN_NOIDUNG_6556 = "";
                        var ngayapdungtt13 = "2019-08-20";
                        var donvido = "";
                        var soquyetdinh = data.SOQUYETDINH;
                        var loaihinh = "";
                        $.post("cmu_post", {
                            url: ["${Sess_DVTT}",
                                ten,
                                motacdha,
                                loaicdha,
                                giakhongbaohiem,
                                "1", // trangthai_bhyt
                                mabaocaobhyt,
                                mabaocaobhyt,
                                giachenhlech,
                                mabaocao5084,
                                thongtu37,
                                ma_dvkt_dmdc,
                                ma_tt4350_dmdc,
                                ma_tt37_dmdc,
                                tenht,
                                duplicate,
                                tien_bhyt_khongchi,
                                thuoc_ttpt,
                                ngayapdung_ttpt,
                                giabaohiem, //giathang10
                                giakhongbaohiem, //giathang10kbh
                                ttdoloper,
                                maycdha,
                                loaibhyt,
                                giabaohiem,
                                giakhongbaohiem,
                                matt15,
                                tentt15,
                                dichvutt37,
                                sudungtt37,
                                ngayapdungtt15,
                                gioihanchidinh,
                                phan_theo_gioi_tinh,
                                giabaohiem, // giabaohiem_tt39
                                giakhongbaohiem, // giakbh_tt39
                                ngayapdungtt39,
                                TEN_NOIDUNG_6556,
                                giabaohiem, // p_giabaohiem_tt37_tt39
                                giakhongbaohiem, // p_giakhongbh_tt37_tt39
                                soluongfilm,
                                giabaohiem, // p_giabaohiem_tt13
                                giakhongbaohiem, // p_giakhongbh_tt13
                                ngayapdungtt13,
                                nhom9324,
                                ngaycapnhat_nhom9324,
                                vetThuongTaiPhat,
                                donvido,
                                soquyetdinh,
                                machisodmdc,
                                tenchisodmdc,
                                loaihinh,
                                "DM_CDHA_THEM_HT_F_CMU"].join("```")
                        }).done(function(r) {
                            var result = r.split(';;;');
                            var rowData = list.getRowData(data.ID);
                            if (result[0] == '0') {
                                rowData.LOI = 'Đã có CĐHA trùng tên hiển thị';
                                list.jqGrid('setRowData', data.ID, rowData);
                                $('#ds_cdha_import_ls').jqGrid('setSelection', data.ID);
                                $("#" + $('#ds_cdha_import_ls').jqGrid('getGridParam', 'selrow')).focus();
                            } else if (result[0] == '-1') {
                                rowData.LOI = 'Không tìm thấy mã loại CĐHA tương ứng';
                                list.jqGrid('setRowData', data.ID, rowData);
                                $('#ds_cdha_import_ls').jqGrid('setSelection', data.ID);
                                $("#" + $('#ds_cdha_import_ls').jqGrid('getGridParam', 'selrow')).focus();
                            } else if (result[0] == '1') {
                                var jsonluu = {
                                    hoatdong: 1,
                                    macls: result[1],
                                    ten: ten,
                                    motacdha: motacdha,
                                    loaicdha: loaicdha,
                                    giakhongbaohiem: giakhongbaohiem,
                                    mabaocaobhyt: mabaocaobhyt,
                                    giachenhlech: giachenhlech,
                                    tenht: tenht,
                                    thuoc_ttpt: thuoc_ttpt,
                                    ngayapdung_ttpt: ngayapdung_ttpt,
                                    giabaohiem: giabaohiem,
                                    ttdoloper: ttdoloper,
                                    maycdha: maycdha,
                                    loaibhyt: loaibhyt,
                                    phan_theo_gioi_tinh: phan_theo_gioi_tinh,
                                    nhom9324: nhom9324,
                                    ngaycapnhat_nhom9324: ngaycapnhat_nhom9324,
                                    vetThuongTaiPhat: vetThuongTaiPhat,
                                    soquyetdinh: soquyetdinh,
                                    loaihinh: loaihinh,
                                    machisodmdc: machisodmdc,
                                    tenchisodmdc: tenchisodmdc,
                                }
                                var arr = ['INSERT', 'Thêm CĐHA vào danh mục ' + loaicdha, ${Sess_DVTT}, JSON.stringify(jsonluu), "CĐHA", result[1], "${Sess_UserID}", 'LICHSU_CLS_INSERT'];
                                $.post("cmu_post", {
                                    url: arr.join("```")
                                })
                                setTimeout(function () {
                                    rowData.LOI = 'IMPORT THÀNH CÔNG';
                                    list.jqGrid('setRowData', data.ID, rowData);
                                    $('#ds_cdha_import_ls').jqGrid('setSelection', data.ID);
                                    $("#" + $('#ds_cdha_import_ls').jqGrid('getGridParam', 'selrow')).focus();
                                }, 300);
                            } else {
                                rowData.LOI = 'IMPORT LỖI';
                                list.jqGrid('setRowData', data.ID, rowData);
                                $('#ds_cdha_import_ls').jqGrid('setSelection', data.ID);
                                $("#" + $('#ds_cdha_import_ls').jqGrid('getGridParam', 'selrow')).focus();
                            }
                            dem += 1;
                            if (dem == allRowsInGrid.length){
                                notifiToClient("Green", "Import thành công");
                            }
                        });
                    }
                    allRowsInGrid.forEach(function(data, index){
                        setTimeout(ajax.bind(null, data),index*3000)
                    })
                }
            })
            // End Import

            // Start Xem lịch sử
            $("#list_lichsu").jqGrid({
                url: '',
                datatype: "local",
                // loadonce: true,
                height: 600,
                autowidth: true,
                shrinkToFit: true,
                colModel: [
                    {name:"THAOTAC", label:"Thao tác", width: 80},
                    {name:"NOIDUNG", label:"Nội dung", width: 230},
                    {name:"COLUMN_JSON", label:"Dữ liệu", width: 500},
                    {name:"THOIGIAN", label:"Thời gian", width: 150},
                    {name:"TENNHANVIEN", label:"Nhân viên", width: 150},
                ],
                rowNum: 10000000,
                viewrecords: true,
                rownumbers: true,
            });
            // End Xem lịch sử
        });
    </script>
</head>
<body>
<div id="panel_all">
    <%@include file="../../../../resources/Theme/include_pages/menu.jsp" %>
    <div class="p-2 pb-5" style="background: white">
        <div style="display: none">
        </div>
        <div class="">
            <div class="row" style="padding-top: 5px; background: white">
                <div class="container">
                    <form>
                        <div class="form-group row">
                            <label for="loaicdha" class="col-sm-2 col-form-label">Loại CĐHA</label>
                            <div class="col-sm-9">
                                <select class="form-control loaicdha" id="loaicdha">
                                    <option selected value="-1">Tất cả</option>
                                </select>
                            </div>
                            <div class="col-sm-1 pl-2">
                                <button type="button" class="btn btn-sm btn-success btn-block h-100" id="editloaicdha" style="max-height:31px"><i class="fa fa-edit"></i></button>
                            </div>
                        </div>
                        <div class="d-flex justify-content-center">
                            <div class="form-check form-check-inline pr-5">
                                <label class="pr-2" for="loadbhyt">BHYT</label>
                                <label class="switch">
                                    <input type="checkbox" class="primary" id="loadbhyt" checked>
                                    <span class="slider round"></span>
                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <label class="pr-2" for="loadhoatdong">Hoạt động</label>
                                <label class="switch">
                                    <input type="checkbox" class="primary" id="loadhoatdong" checked>
                                    <span class="slider round"></span>
                                </label>

                            </div>
                        </div>
                    </form>
                    <div class="d-flex justify-content-center mb-3">
                        <button type="button" class="btn btn-primary mr-2" id="themmoi"><i class="fa fa-plus" aria-hidden="true"> Thêm</i></button>
                        <button type="button" class="btn btn-primary mr-2" id="lammoi"><i class="fa fa-refresh" aria-hidden="true"> Làm mới</i></button>
                        <button type="button" class="btn btn-primary mr-2" id="indanhsach"><i class="fa fa-print" aria-hidden="true"> In</i></button>
                        <button type="button" class="btn btn-primary mr-2" id="import"><i class="fa fa-folder-open"> Import</i></button>
                    </div>
                </div>
                <div id="div_cdha" class="col-12">
                    <table id="list_cdha" style="font-size: 12px"></table>
                    <div style="height: 45px" id="jqGridPager"></div>
                </div>
            </div>
        </div> <!--end of center_content-->
    </div>
</div>
<jsp:include page="../../../../resources/Theme/include_pages/footerBT.jsp"/>

<div class="modal" id="modalChiTiet"  role="dialog"
     aria-labelledby="modalChiTiet" aria-hidden="true"
     data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog" style="max-width: 98%;width:98%" role="document">
        <div class="modal-content wrap-full">
            <div class="modal-header modal-header-sticky">
                <h6 class="modal-title text-primary font-weight-bold" id="titleChiTiet"></h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row ml-0 mr-0  mt-2">
                    <div class="col-md-12">
                        <form class="was-validated row g-2" id="formchitiet">
                            <div class="col-3 pr-2 pb-2">
                                <label for="loaicdha" class="form-label">Loại CĐHA</label>
                                <div class="form-group row">
                                    <div class="col-sm-12">
                                        <input type="text" name="tenloaicdha" class="form-control form-control-sm" id="tenloaicdha" required readonly />
                                        <input type="hidden" class="form-control" id="maloaicdha" required />
                                    </div>
                                </div>
                            </div>
                            <div class="col-7 pr-2 pb-2">
                                <label for="tencdha" class="form-label">Tên CĐHA</label>
                                <input type="hidden" class="form-control" id="macdha" required />
                                <input type="text" name="tencdha" class="form-control form-control-sm" id="tencdha" required />
                            </div>
                            <div class="col-1 pr-2 pb-2">
                                <label for="bhyt" class="form-label">BHYT</label>
                                <div>
                                    <label class="switch">
                                        <input type="checkbox" class="primary" id="bhyt" checked>
                                        <span class="slider round"></span>
                                    </label>
                                </div>
                            </div>
                            <div class="col-1 pr-2 pb-2">
                                <label for="checkhoatdong" class="form-label">Hoạt động</label>
                                <div>
                                    <label class="switch">
                                        <input type="checkbox" class="primary" id="checkhoatdong" checked disabled>
                                        <span class="slider round"></span>
                                    </label>
                                </div>
                            </div>

                            <div class="col-6 pr-2 pb-2">
                                <label for="tenhienthi" class="form-label">Tên hiển thị</label>
                                <input type="text" name="tenhienthi" class="form-control form-control-sm" id="tenhienthi" />
                            </div>
                            <div class="col-2 pr-2 pb-2">
                                <label for="loaidichvucdha" class="form-label">Loại</label>
                                <select id="loaidichvucdha" class="form-control select2input"></select>
                            </div>
                            <%--                            <div class="col-2 pr-2 pb-2">--%>
                            <%--                                <label for="gioihanchidinh" class="form-label">Giới hạn chỉ định</label>--%>
                            <%--                                <input type="number" name="gioihanchidinh" class="form-control form-control-sm" id="gioihanchidinh" />--%>
                            <%--                            </div>--%>
                            <div class="col-2 pr-2 pb-2">
                                <label for="phanloaigioitinh" class="form-label">Phân loại giới tính</label>
                                <select id="phanloaigioitinh" class="form-control select2input" required>
                                    <option value="-1" selected="">Không phân theo giới tính</option>
                                    <option value="1">Nam</option>
                                    <option value="0">Nữ</option>
                                </select>
                            </div>

                            <div class="col-2 pr-2 pb-2">
                                <label for="motacdha" class="form-label">Mô tả CĐHA</label>
                                <input type="text" name="motacdha" class="form-control form-control-sm" id="motacdha" required/>
                            </div>
                            <%--                            <div class="col-2 pr-2 pb-2">--%>
                            <%--                                <label for="giakhongbaohiem" class="form-label font-weight-bold">Giá không bảo hiểm</label>--%>
                            <%--                                <input type="text" name="giakhongbaohiem" class="form-control form-control-sm" value="0" data-type="currency" id="giakhongbaohiem" required/>--%>
                            <%--                            </div>--%>
                            <%--                            <div class="col-2 pr-2 pb-2 bhyt">--%>
                            <%--                                <label for="giabaohiem" class="form-label font-weight-bold">Giá bảo hiểm</label>--%>
                            <%--                                <input type="text" name="giabaohiem" class="form-control form-control-sm bhyt-input" value="0" data-type="currency" id="giabaohiem" required/>--%>
                            <%--                            </div>--%>
                            <%--                            <div class="col-2 pr-2 pb-2 bhyt">--%>
                            <%--                                <label for="giachenhlech" class="form-label font-weight-bold">Giá chênh lệch</label>--%>
                            <%--                                <input type="text" name="giachenhlech" class="form-control form-control-sm" value="0" data-type="currency" id="giachenhlech" required/>--%>
                            <%--                            </div>--%>
                            <div class="col-2 pr-2 pb-2">
                                <label for="manhomCV9324" class="form-label">Chọn mã nhóm CV9324</label>
                                <select id="manhomCV9324" class="form-control select2input" required></select>
                            </div>
                            <div class="col-2 pr-2 pb-2">
                                <label for="ngayapdungCV9324" class="form-label">Ngày áp dụng CV9324</label>
                                <input type="text" name="ngayapdungCV9324" class="form-control form-control-sm input-date" id="ngayapdungCV9324" />
                            </div>

                            <div class="col-12">
                                <fieldset class="scheduler-border">
                                    <legend class="scheduler-border">Giá</legend>
                                    <div class="row">
                                        <div class="col-4 pr-2 pb-2">
                                            <label for="giakhongbaohiem" class="form-label font-weight-bold">Giá không bảo hiểm</label>
                                            <input type="text" name="giakhongbaohiem" class="form-control form-control-sm" value="0" data-type="currency" id="giakhongbaohiem" required/>
                                        </div>
                                        <div class="col-4 pr-2 pb-2 bhyt">
                                            <label for="giabaohiem" class="form-label font-weight-bold">Giá bảo hiểm</label>
                                            <input type="text" name="giabaohiem" class="form-control form-control-sm bhyt-input" value="0" data-type="currency" id="giabaohiem" required/>
                                        </div>
                                        <div class="col-4 pr-2 pb-2 bhyt">
                                            <label for="giachenhlech" class="form-label font-weight-bold">Giá chênh lệch</label>
                                            <input type="text" name="giachenhlech" class="form-control form-control-sm" value="0" data-type="currency" id="giachenhlech" required/>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>

                            <div class="col-12">
                                <fieldset class="scheduler-border bhyt">
                                    <legend class="scheduler-border">BHYT</legend>
                                    <div class="row">
                                        <div class="col-2 pr-2 pb-2 bhyt">
                                            <label for="machisodmdc" class="form-label">Mã chỉ số DMDC</label>
                                            <input type="text" name="machisodmdc" class="form-control form-control-sm bhyt-input" required id="machisodmdc"/>
                                        </div>
                                        <div class="col-6 pr-2 pb-2 bhyt">
                                            <label for="tenchisodmdc" class="form-label">Tên DMDC</label>
                                            <input type="text" name="tenchisodmdc" class="form-control form-control-sm bhyt-input" required id="tenchisodmdc"/>
                                        </div>
                                        <div class="col-4 pr-2 pb-2 bhyt">
                                            <label for="maycdha" class="form-label">Máy CĐHA</label>
                                            <select id="maycdha" class="form-control select2input bhyt-input" required></select>
                                        </div>
                                        <div class="col-2 pr-2 pb-2 bhyt">
                                            <label for="mabaocaobhyt" class="form-label">Mã báo cáo BHYT</label>
                                            <input type="text" name="mabaocaobhyt" class="form-control form-control-sm bhyt-input" id="mabaocaobhyt" required/>
                                        </div>
                                        <div class="col-2 pr-2 pb-2 bhyt">
                                            <label for="mabaocaobhyt" class="form-label">Mã thông tư 37</label>
                                            <input type="text" name="mabaocaobhyt_tt37" class="form-control form-control-sm bhyt-input" id="mabaocaobhyt_tt37" required/>
                                        </div>
                                        <div class="col-3 pr-2 pb-2 bhyt">
                                            <label for="loaibhyt" class="form-label">Loại bảo hiểm</label>
                                            <select id="loaibhyt" class="form-control select2input bhyt-input" required>
                                                <option value="0" selected="">Sử dụng cho tất cả</option>
                                                <option value="1">Sử dụng cho BHYT</option>
                                                <option value="2">Sử dụng cho BHPVI</option>
                                            </select>
                                        </div>

                                        <div class="col-2 pr-2 pb-2 bhyt">
                                            <label for="soquyetdinh" class="form-label">Số quyết định</label>
                                            <input type="text" name="soquyetdinh" class="form-control form-control-sm bhyt-input" id="soquyetdinh" required/>
                                        </div>
                                        <div class="col-3 pr-2 pb-2 bhyt">
                                            <label for="thuocttpt" class="form-label">Thuộc TTPT</label>
                                            <div class="form-inline">
                                                <div class="col-3">
                                                    <input type="checkbox" class="primary" id="thuocttpt" style="width:31px; height:31px">
                                                </div>
                                                <div class="col-9">
                                                    <input type="text" name="ngayapdungttpt" class="form-control form-control-sm input-date" id="ngayapdungttpt" hidden />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>

                            <div class="col-12">
                                <div class="row">
                                    <div class="col-2 pr-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="" id="doloper">
                                            <label class="form-check-label pt-1" for="doloper">
                                                SA màu/Doloper
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-2 pr-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="" id="vetthuongtaiphat">
                                            <label class="form-check-label pt-1" for="vetthuongtaiphat">
                                                Vết thương tái phát
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer modal-footer-sticky">
                <div class="col-md-5 text-right">
                    <button class="btn btn-success ml-2" type="button" id="themcdha">
                        Thêm
                    </button>
                    <button class="btn btn-success ml-2" type="button" id="luucdha">
                        Lưu
                    </button>
                    <button class="btn btn-default ml-2" type="button"  data-dismiss="modal">
                        Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="modalLoaiCDHA"  role="dialog"
     aria-labelledby="modalLoaiCDHA" aria-hidden="true"
     data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog" style="max-width: 80%;width:80%" role="document">
        <div class="modal-content wrap-full">
            <div class="modal-header modal-header-sticky">
                <h6 class="modal-title text-primary font-weight-bold" id="titleLoaiCDHA">Chi tiết loại chẩn đoán hình ảnh</h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row ml-0 mr-0  mt-2">
                    <div class="col-md-7 pr-3">
                        <form class="was-validated row g-2" id="formloaicdha">
                            <div class="col-12 pb-2">
                                <label for="tenloaicdha_loai" class="form-label">Mã Loại CĐHA</label>
                                <input type="number" class="form-control" id="maloaicdha_loai" readonly />
                            </div>
                            <div class="col-12 pb-2">
                                <label for="tenloaicdha_loai" class="form-label">Loại CĐHA</label>
                                <input type="text" name="tenloaicdha_loai" class="form-control form-control-sm" id="tenloaicdha_loai" required />
                            </div>
                            <div class="col-12 pb-2">
                                <label for="motacap1_loai" class="form-label">Mô tả cấp 1</label>
                                <input type="text" name="motacap1_loai" class="form-control form-control-sm" id="motacap1_loai" required />
                            </div>
                            <div class="col-12 pb-2">
                                <label for="motacap2_loai" class="form-label">Mô tả cấp 2</label>
                                <input type="text" name="motacap2_loai" class="form-control form-control-sm" id="motacap2_loai" />
                            </div>
                        </form>
                        <div class="d-flex justify-content-center mb-3">
                            <button type="button" class="btn btn-primary mr-2" id="themmoi_loai">Thêm</button>
                            <button type="button" class="btn btn-primary mr-2" id="capnhat_loai">Cập nhật</button>
                            <button type="button" class="btn btn-primary" id="huy_loai">Hủy</button>
                        </div>
                    </div>
                    <div class="col-md-5" id="div_loaicdha">
                        <table id="list_loaicdha" style="font-size: 12px"></table>
                    </div>
                </div>
            </div>
            <div class="modal-footer modal-footer-sticky">
                <div class="col-md-5 text-right">
                    <button class="btn btn-default ml-2" type="button"  data-dismiss="modal">
                        Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="modalChuyenLoaiCDHA"  role="dialog"
     aria-labelledby="modalChuyenLoaiCDHA" aria-hidden="true"
     data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog" style="max-width: 50%;width:50%" role="document">
        <div class="modal-content wrap-full">
            <div class="modal-header modal-header-sticky">
                <h6 class="modal-title text-primary font-weight-bold" id="titleChuyenLoaiCDHA">Chuyển loại chẩn đoán hình ảnh</h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row ml-0 mr-0  mt-2">
                    <div class="col-md-12 pr-3">
                        <form class="was-validated row g-2" id="formchuyenloaicdha">
                            <div class="col-12 pb-2">
                                <label for="loaicdhacu" class="form-label">Loại CĐHA hiện tại</label>
                                <input type="text" class="form-control" id="macdha_chuyen" readonly hidden />
                                <input type="text" class="form-control" id="loaicdhacu" readonly />
                            </div>
                            <div class="col-12 pb-2">
                                <label for="loaicdhamoi" class="form-label">Loại CĐHA cần chuyển</label>
                                <select class="loaicdha" id="loaicdhamoi"></select>
                            </div>
                        </form>
                        <div class="d-flex justify-content-center mb-3">
                            <button type="button" class="btn btn-primary mr-2" id="chuyen_loai">Chuyển loại</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer modal-footer-sticky">
                <div class="col-md-5 text-right">
                    <button class="btn btn-default ml-2" type="button"  data-dismiss="modal">
                        Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="modalXuatExcel"  role="dialog"
     aria-labelledby="modalXuatExcel" aria-hidden="true"
     data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog" style="max-width: 50%;width:50%" role="document">
        <div class="modal-content wrap-full">
            <div class="modal-header modal-header-sticky">
                <h6 class="modal-title text-primary font-weight-bold" id="titleXuatExcel">In danh sách</h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row ml-0 mr-0  mt-2">
                    <div class="col-md-12">
                        <form class="was-validated row g-2" id="formxuatexcel">
                            <div class="col-12 pb-2">
                                <label for="loaibaocao" class="form-label">Loại báo cáo</label>
                                <select class="form-control" id="loaibaocao">
                                    <option value="-1">Tất cả</option>
                                    <option value="1">BHYT</option>
                                    <option value="2">Không BHYT</option>
                                </select>
                            </div>
                            <div class="col-12 pb-2">
                                <label for="trangthai" class="form-label">Trạng thái</label>
                                <select class="form-control" id="trangthai">
                                    <option value="-1">Tất cả</option>
                                    <option value="1">Hoạt động</option>
                                    <option value="2">Ngừng hoạt động</option>
                                </select>
                            </div>
                        </form>
                        <div class="d-flex justify-content-center mb-3">
                            <%--                            <button type="button" class="btn btn-primary mr-2" id="xuatdanhsach">In danh sách</button>--%>
                            <button type="button" class="btn btn-primary mr-2" id="xuatchitiet">In chi tiết</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer modal-footer-sticky">
                <div class="col-md-5 text-right">
                    <button class="btn btn-default ml-2" type="button"  data-dismiss="modal">
                        Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="modalImport"  role="dialog"
     aria-labelledby="modalImport" aria-hidden="true"
     data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog" style="max-width: 98%;width:98%" role="document">
        <div class="modal-content wrap-full">
            <div class="modal-header modal-header-sticky">
                <h6 class="modal-title text-primary font-weight-bold" id="titleImport">Import chẩn đoán hình ảnh</h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row ml-0 mr-0  mt-2">
                    <div class="col-md-12">
                        <form class="was-validated row g-2" id="formimport">
                            <div class="col-12 pb-2">
                                <table id="ds_cdha_import_ls"></table>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer modal-footer-sticky">
                <div class="col-md-12 text-right">
                    <input type="file" onchange="uploadFile(event)" name="import_file" id="import_file" value="DM XN"/>
                    <button class="btn btn-success ml-2" type="button" id="btn_import">
                        Import
                    </button>
                    <button class="btn btn-default ml-2" type="button"  data-dismiss="modal">
                        Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="modalXemLichSu"  role="dialog"
     aria-labelledby="modalXemLichSu" aria-hidden="true"
     data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog" style="max-width: 98%;width:98%" role="document">
        <div class="modal-content wrap-full">
            <div class="modal-header modal-header-sticky">
                <h6 class="modal-title text-primary font-weight-bold" id="titleXemLichSu">Xem lịch sử</h6>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row ml-0 mr-0  mt-2">
                    <div class="col-md-12">
                        <form class="was-validated row g-2" id="formxemlichsu">
                            <div class="col-12 pb-2">
                                <table id="list_lichsu"></table>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal-footer modal-footer-sticky">
                <div class="col-md-12 text-right">
                    <button class="btn btn-default ml-2" type="button"  data-dismiss="modal">
                        Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>