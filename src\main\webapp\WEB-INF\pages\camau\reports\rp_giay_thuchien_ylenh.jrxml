<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rp_giay_thuchien_ylenh" language="groovy" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" whenResourceMissingType="Empty" uuid="0c77309d-bb14-4cd7-8eb5-4a8b75b128de">
	<property name="ireport.zoom" value="1.3310000000000004"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="s_center" hAlign="Center"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#FFBFBF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<subDataset name="Bang_Y_Lenh" whenResourceMissingType="Empty" uuid="1fe62b3e-ec10-465c-9028-296f7d4113e5">
		<parameter name="STT_DOTDIEUTRI" class="java.lang.String"/>
		<parameter name="STT_BENHAN" class="java.lang.String"/>
		<parameter name="ID_DIEUTRI" class="java.lang.String"/>
		<parameter name="TRANGTHAI" class="java.lang.String"/>
		<parameter name="SOVAOVIEN" class="java.lang.String">
			<defaultValueExpression><![CDATA[]]></defaultValueExpression>
		</parameter>
		<parameter name="dvtt" class="java.lang.String"/>
		<queryString language="plsql">
			<![CDATA[{call HIS_MANAGER.CMU_BANG_THUCHIEN_YLENH_J(
$P{dvtt},
$P{STT_DOTDIEUTRI}, 
$P{STT_BENHAN}, 
$P{ID_DIEUTRI}, 
$P{TRANGTHAI},
$P{SOVAOVIEN},
$P{ORACLE_REF_CURSOR})}]]>
		</queryString>
		<field name="TEN_VAT_TU" class="java.lang.String"/>
		<field name="STT_TOATHUOC" class="java.lang.String"/>
		<field name="NGAY_YL" class="java.lang.String"/>
		<field name="THOIGIAN_SORT" class="java.lang.String"/>
		<field name="SO_LUONG" class="java.lang.String"/>
		<field name="NGAY_TH_YL" class="java.lang.String"/>
		<field name="KEYSIGN" class="java.lang.String"/>
		<field name="NGAY_KY" class="java.lang.String"/>
		<field name="NGUOI_KY" class="java.lang.String"/>
		<field name="THOIGIAN" class="java.lang.String"/>
		<field name="NGUOITH" class="java.lang.String"/>
	</subDataset>
	<parameter name="tenkhoa" class="java.lang.String"/>
	<parameter name="soyte" class="java.lang.String"/>
	<parameter name="benhvien" class="java.lang.String"/>
	<parameter name="dvtt" class="java.lang.String"/>
	<parameter name="ID_PHIEU" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="STT_DOTDIEUTRI" class="java.lang.String"/>
	<parameter name="STT_BENHAN" class="java.lang.String"/>
	<parameter name="ID_DIEUTRI" class="java.lang.String"/>
	<parameter name="TRANGTHAI" class="java.lang.String"/>
	<parameter name="SOVAOVIEN" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call HIS_MANAGER.CMU_PHIEU_THUCHIEN_YLENH_J(
$P{SOVAOVIEN}, 
$P{dvtt},
$P{STT_DOTDIEUTRI},
$P{ORACLE_REF_CURSOR})
}]]>
	</queryString>
	<field name="TEN_BENH_NHAN" class="java.lang.String"/>
	<field name="TUOI" class="java.lang.String"/>
	<field name="GIOI_TINH" class="java.lang.String"/>
	<field name="CHUAN_DOAN" class="java.lang.String"/>
	<field name="SO_HS" class="java.lang.String"/>
	<field name="SO_GIUONG" class="java.lang.String"/>
	<group name="rp_giay_thuchien_ylenh">
		<groupExpression><![CDATA[$P{ID_PHIEU}]]></groupExpression>
		<groupHeader>
			<band height="170" splitType="Immediate">
				<textField>
					<reportElement x="0" y="35" width="200" height="15" uuid="6c7f6a2f-18c6-42a4-ad45-90186cb0f481"/>
					<box leftPadding="0"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true" pdfEncoding="Identity-H"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{benhvien}.toUpperCase()]]></textFieldExpression>
				</textField>
				<frame>
					<reportElement x="0" y="70" width="555" height="50" uuid="94b25fa3-a150-46fc-b96e-d411a0775aa4"/>
					<textField>
						<reportElement x="0" y="0" width="360" height="15" uuid="020185ad-dff4-4941-8d2c-71dd028d49b8"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Họ và tên bệnh nhân: " + (
$F{TEN_BENH_NHAN} == null ? 
"" : 
$F{TEN_BENH_NHAN}
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="360" y="0" width="89" height="15" uuid="202448d4-f91e-4a53-abd7-1f3efdfa9523"/>
						<box leftPadding="0"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Tuổi: " + (
$F{TUOI} == null ? 
"" : 
$F{TUOI}
)]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true">
						<reportElement x="0" y="20" width="360" height="15" uuid="b62f1f22-21bd-487d-bd56-ed871c3c2ce9"/>
						<box rightPadding="9"/>
						<textElement textAlignment="Justified" verticalAlignment="Middle" markup="html">
							<font fontName="Times New Roman" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Chuẩn đoán: " + (
$F{CHUAN_DOAN} == null ? 
"" : 
$F{CHUAN_DOAN}
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="360" y="20" width="195" height="15" uuid="1356cdca-d407-43f5-a842-a12f7a37fa13"/>
						<box leftPadding="0"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Số giường: " + (
$F{SO_GIUONG} == null ? 
"" : 
$F{SO_GIUONG}
)]]></textFieldExpression>
					</textField>
					<textField>
						<reportElement x="449" y="0" width="106" height="15" uuid="54393aa0-2984-4380-a59a-cac32ab97fd0"/>
						<box leftPadding="0"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Giới tính: " + 
($F{GIOI_TINH}.equals("0")? "Nữ": "Nam")]]></textFieldExpression>
					</textField>
				</frame>
				<textField>
					<reportElement x="0" y="15" width="200" height="20" uuid="2846e8d3-7076-4c6e-90d2-cf19371d1dab"/>
					<box leftPadding="28"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="false" pdfEncoding="Identity-H"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{soyte}.toUpperCase()]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="200" y="15" width="220" height="35" uuid="ac720f51-f89a-4ac2-a0e0-8ff098a3867f"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="true"/>
					</textElement>
					<text><![CDATA[PHIẾU THỰC HIỆN Y LỆNH]]></text>
				</staticText>
				<componentElement>
					<reportElement key="table" style="table" positionType="Float" x="0" y="120" width="555" height="50" uuid="c0dba393-c0f5-497d-b713-b424d10db7c8"/>
					<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
						<datasetRun subDataset="Bang_Y_Lenh" uuid="c77b9661-**************-9482b4a4d16e">
							<datasetParameter name="REPORT_CONTEXT">
								<datasetParameterExpression><![CDATA[$P{REPORT_CONTEXT}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="REPORT_PARAMETERS_MAP">
								<datasetParameterExpression><![CDATA[$P{REPORT_PARAMETERS_MAP}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="dvtt">
								<datasetParameterExpression><![CDATA[$P{dvtt}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="STT_DOTDIEUTRI">
								<datasetParameterExpression><![CDATA[$P{STT_DOTDIEUTRI}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="STT_BENHAN">
								<datasetParameterExpression><![CDATA[$P{STT_BENHAN}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="ID_DIEUTRI">
								<datasetParameterExpression><![CDATA[$P{ID_DIEUTRI}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="TRANGTHAI">
								<datasetParameterExpression><![CDATA[$P{TRANGTHAI}]]></datasetParameterExpression>
							</datasetParameter>
							<datasetParameter name="SOVAOVIEN">
								<datasetParameterExpression><![CDATA[$P{SOVAOVIEN}]]></datasetParameterExpression>
							</datasetParameter>
							<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
						</datasetRun>
						<jr:column width="35" uuid="841172e1-17da-49ea-8132-74aef17dec26">
							<jr:columnHeader style="table_TH" height="30" rowSpan="1">
								<staticText>
									<reportElement x="0" y="0" width="35" height="30" uuid="1dfa250a-5b5d-4f72-97c9-dc4862748ce7"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font fontName="Times New Roman"/>
									</textElement>
									<text><![CDATA[TT]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="40" rowSpan="1">
								<textField isBlankWhenNull="true">
									<reportElement x="0" y="0" width="35" height="40" uuid="8e20ca20-0894-4a5a-8a2b-fa6f65020783"/>
									<textElement textAlignment="Center">
										<font fontName="Times New Roman"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{REPORT_COUNT}.toString()]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="135" uuid="d0bc7fcb-7f9f-49d3-a567-c25e6658c1a1">
							<jr:columnHeader style="table_TH" height="30" rowSpan="1">
								<staticText>
									<reportElement x="0" y="0" width="135" height="30" uuid="764bc01d-4e99-4e03-bf52-a47355184265"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font fontName="Times New Roman"/>
									</textElement>
									<text><![CDATA[TÊN THUỐC]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="40" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement x="0" y="0" width="135" height="40" uuid="c9f412f3-b53b-492f-81d3-36cb79632d1f"/>
									<box leftPadding="3" rightPadding="3"/>
									<textElement>
										<font fontName="Times New Roman"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{TEN_VAT_TU}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="65" uuid="736bbd68-a6a1-48fd-b75c-13c87fb90483">
							<jr:columnHeader style="table_TH" height="30" rowSpan="1">
								<staticText>
									<reportElement x="0" y="0" width="65" height="30" uuid="530cb069-4bd9-4d5d-b1d5-00555e7ff88c"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font fontName="Times New Roman"/>
									</textElement>
									<text><![CDATA[SỐ LƯỢNG]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="40" rowSpan="1">
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement x="0" y="0" width="65" height="40" uuid="9d815cf2-e545-4369-9527-e8c8cd564c73"/>
									<textElement textAlignment="Center">
										<font fontName="Times New Roman"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{SO_LUONG}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90" uuid="6c4fabb3-a402-43f6-99bd-a722cdad78e4">
							<jr:columnHeader style="table_TH" height="30" rowSpan="1">
								<staticText>
									<reportElement x="0" y="0" width="90" height="30" uuid="9f649781-18e4-432f-a119-1a35dbcf6257"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font fontName="Times New Roman"/>
									</textElement>
									<text><![CDATA[THỜI GIAN YC THỰC HIỆN]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="40" rowSpan="1">
								<textField isBlankWhenNull="true">
									<reportElement x="0" y="0" width="90" height="40" uuid="c656d041-47f3-412d-8914-72a030316f6f"/>
									<textElement textAlignment="Center">
										<font fontName="Times New Roman"/>
									</textElement>
									<textFieldExpression><![CDATA[($F{THOIGIAN} == null || $F{THOIGIAN}.equals( " " ) ? 
    "" :
    (
        $F{THOIGIAN}.equals( "Sáng" ) || 
        $F{THOIGIAN}.equals( "Trưa" ) || 
        $F{THOIGIAN}.equals( "Chiều" ) || 
        $F{THOIGIAN}.equals( "Tối" ) ?
            $F{NGAY_YL}.split(" ")[0] + " - " + $F{THOIGIAN} :
            $F{THOIGIAN}
    )
)]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="90" uuid="3fa27ba0-b861-4551-9b34-578d964b5c0f">
							<jr:columnHeader style="table_TH" height="30" rowSpan="1">
								<staticText>
									<reportElement x="0" y="0" width="90" height="30" uuid="eb815c2c-021b-4f97-9737-f0556aff0367"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font fontName="Times New Roman"/>
									</textElement>
									<text><![CDATA[NGÀY THỰC HIỆN]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="40" rowSpan="1">
								<textField isBlankWhenNull="true">
									<reportElement x="0" y="0" width="90" height="40" uuid="9a73131b-b856-4856-8be6-03675340715c"/>
									<textElement textAlignment="Center">
										<font fontName="Times New Roman"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{NGAY_TH_YL}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
						<jr:column width="140" uuid="6e6dd4a7-33e1-4710-b3ac-cb31647a83d5">
							<jr:columnHeader style="table_TH" height="30" rowSpan="1">
								<staticText>
									<reportElement x="0" y="0" width="140" height="30" uuid="bf98a96a-749f-48af-9822-86bce5f1a5e6"/>
									<textElement textAlignment="Center" verticalAlignment="Middle">
										<font fontName="Times New Roman"/>
									</textElement>
									<text><![CDATA[NGƯỜI THỰC HIỆN]]></text>
								</staticText>
							</jr:columnHeader>
							<jr:detailCell style="table_TD" height="40" rowSpan="1">
								<textField>
									<reportElement x="0" y="0" width="140" height="7" forecolor="#FFFFFF" uuid="85624adb-3252-450e-80f7-00a0ee1bcebb"/>
									<textElement>
										<font fontName="Times New Roman" size="6"/>
									</textElement>
									<textFieldExpression><![CDATA["kyso." + 
$F{STT_TOATHUOC} + 
"." + 
$F{NGAY_YL}.replace( " ", "." ) + 
"." + 
$F{THOIGIAN}.replace( " ", "." ) + 
"." + 
$F{THOIGIAN_SORT}]]></textFieldExpression>
								</textField>
								<image scaleImage="FillFrame" hAlign="Center">
									<reportElement x="122" y="7" width="18" height="7" uuid="a212f419-a37b-4de2-b7e3-ff4b99c31a91">
										<printWhenExpression><![CDATA[$F{KEYSIGN} != null]]></printWhenExpression>
									</reportElement>
									<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.getInstance(
new SimpleJasperReportsContext()).loadAwtImageFromBytes(Base64.getDecoder().decode("iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAABvElEQVR4nO3U3UrCYBjAcS+gAy0XdjAs925uO+huBGfnnYkmQX7kxwQvoNqJdAmFSEFQptP5RdDRBg26jig6euKxrFdbVjo66oGHuZP/j9cX5vH8z1+OcFv0y0M1hU/X48SoMFK/ZMoDFaRh2ZZvigF3472SJfVVkAYqICIPy5YrJyFGhgl3C5bYK4HUx/1AxIGaXDguGAVL7BZB7OFOIIudgLzGzbBRgDACNNIrfb6Dpari9x0pKXz+JM538ma4k4cRQCPdgi3r0/GDCOPTFNOnxcCrxWxGiwRmxvWcKbTzICBAI4ZTvKr4vUeKhfHx4juiTnHSypq8vg9CG5dG8p/jOPi30PGvEILxZtbkWzngddwPhO/sO8e/OsE0Qi4wnjFJMwsEAQrh2zPiTncwvcva1n2okb4j1xkYARRCWjk7+F18FrKibcH6eQq4RgY4BCiEa/4i7oRgPHi2A6GrNHCN9ATCXWftoL4737cGEe9B1GLrSQhd7kHoam8KSVt4J3PF35FKhGFriccNBChkvZ54XDg+nrXqtsjWEk9jJFhPPK8eRzddib8jh9siexJ/YE/jD/jb1fj/eN7mBQZhd5OoxlU0AAAAAElFTkSuQmCC")
)]]></imageExpression>
								</image>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement positionType="Float" x="0" y="14" width="140" height="7" uuid="ea19aaa4-a8ac-4a96-9843-18a23074de81">
										<printWhenExpression><![CDATA[$F{KEYSIGN} != null]]></printWhenExpression>
									</reportElement>
									<textElement textAlignment="Left" verticalAlignment="Bottom">
										<font fontName="Times New Roman" size="6" isBold="false" isStrikeThrough="false"/>
									</textElement>
									<textFieldExpression><![CDATA["Ngày ký: " + $F{NGAY_KY}]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement positionType="Float" x="0" y="21" width="140" height="7" uuid="b34df79c-fd2f-4ed9-8c61-13e66fdb2f84">
										<printWhenExpression><![CDATA[$F{KEYSIGN} != null]]></printWhenExpression>
									</reportElement>
									<textElement textAlignment="Left" verticalAlignment="Bottom">
										<font fontName="Times New Roman" size="6" isBold="false"/>
									</textElement>
									<textFieldExpression><![CDATA["Ký bởi: " + $F{NGUOI_KY}.toUpperCase()]]></textFieldExpression>
								</textField>
								<textField isStretchWithOverflow="true" isBlankWhenNull="true">
									<reportElement positionType="Float" x="0" y="28" width="140" height="12" isPrintWhenDetailOverflows="true" uuid="c9b46285-11ae-4d7f-ab35-f9bd22558fdf"/>
									<textElement textAlignment="Center">
										<font fontName="Times New Roman" size="8"/>
									</textElement>
									<textFieldExpression><![CDATA[$F{NGUOITH}]]></textFieldExpression>
								</textField>
							</jr:detailCell>
						</jr:column>
					</jr:table>
				</componentElement>
				<textField>
					<reportElement x="420" y="15" width="135" height="35" uuid="ae5a5fc9-9290-4c7c-b30e-aaefa426e7d9"/>
					<box leftPadding="28"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Times New Roman" isBold="false" pdfEncoding="Identity-H"/>
					</textElement>
					<textFieldExpression><![CDATA["Số BA: " + (
$F{SO_HS} == null ? 
"........." : 
$F{SO_HS}
)]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<pageFooter>
		<band height="15">
			<textField>
				<reportElement x="0" y="0" width="555" height="15" uuid="4c130b8a-d8f2-4d5f-96e5-81cd30eedf9a"/>
				<box leftPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
