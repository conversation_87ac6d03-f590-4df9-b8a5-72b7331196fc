<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="phieugaymehoisuc" language="groovy" pageWidth="249" pageHeight="842" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="249" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="5f982810-2f9d-40df-b4a6-deec4a399123">
	<property name="ireport.zoom" value="2.1961500000000016"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="dvtt" class="java.lang.String"/>
	<parameter name="idphieu" class="java.lang.String"/>
	<parameter name="page" class="java.lang.String"/>
	<parameter name="lastpage" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call CMU_LCSC1_CHANDOAN_PRO($P{dvtt}, $P{idphieu}, $P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="ICD_DIEUDUONG" class="java.lang.String"/>
	<field name="TENICD_DIEUDUONG" class="java.lang.String"/>
	<field name="IS_MUC_TIEU" class="java.lang.String"/>
	<field name="MUC_TIEU" class="java.lang.String"/>
	<field name="STT_CHANDOAN" class="java.lang.String"/>
	<group name="group_icd">
		<groupExpression><![CDATA[$F{ICD_DIEUDUONG}]]></groupExpression>
		<groupHeader>
			<band height="15">
				<textField isBlankWhenNull="true">
					<reportElement x="0" y="0" width="249" height="15" isRemoveLineWhenBlank="true" uuid="410b8d94-7253-470b-ae45-9c3810ff0991"/>
					<box leftPadding="2">
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
						<font fontName="Times New Roman" size="11" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[!$F{ICD_DIEUDUONG}.isEmpty() ? ($P{dvtt}.equals("96001") || $P{dvtt}.equals("96029") ? "Chẩn đoán " + $F{STT_CHANDOAN} + ": " : "- ") + $F{ICD_DIEUDUONG} : null]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="15" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="16" y="0" width="233" height="15" uuid="11803292-6af9-4ef4-8892-dedb3c3d0321"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[!$F{MUC_TIEU}.isEmpty() ? ($P{dvtt}.equals("96001") || $P{dvtt}.equals("96029") ? "Mục tiêu " + ($P{dvtt}.equals("96029") ? "" : $V{group_icd_COUNT}) + ": " : "") + $F{MUC_TIEU} : null]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="3" y="1" width="13" height="13" uuid="93c52ed5-5d7a-44ee-8e32-f90776a1b7fd"/>
				<box leftPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{IS_MUC_TIEU}.equals("1") ? "x" : ""]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
