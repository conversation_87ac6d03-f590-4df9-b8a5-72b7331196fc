function kiemtradulieuXuatvien(data) {
	try {
		var arr_params = [singletonObject.dvtt, data.SOVAOVIEN, data.SOVAOVIEN_DT, data.NGAYRAVIEN];
		const sttDieuTris = getSttToDieuTriWithEmptyYLenhHoacDienBien(data);
        if (sttDieuTris !== '') {
            arr_params.push(sttDieuTris);
        }
		var resCLS = $.ajax({
			url: "cmu_list_noitru_kt_cls?url=" + convertArray(arr_params),
			async: false
		}).responseText;
		var resCLSJSON = JSON.parse(resCLS);
		if (resCLSJSON && resCLSJSON.length > 0) {
			$.alert({
				title: 'Dữ liệu không hợp lệ!',
				content: '<table id="noitru_kt_cls_list" class="jqx-grid-cell-wrap"></table>',
				type: 'red',
				boxWidth: '900px',
				useBootstrap: false,
				escapeKey: true,
				closeIcon: true,
				typeAnimated: true,
				onContentReady: function () {
					$("#noitru_kt_cls_list").jqGrid({
						datatype: 'local',
						//regional: 'en', // this is default
						data: resCLSJSON,
						rownumbers: true,
						height: 400,
						colModel: [
							{name: 'LOAI', label: 'Loại'},
							{name: 'STT_DIEUTRI', label: 'Phiếu', width: '40px', align: 'center'},
							{name: 'NGAY_CHI_DINH_CT', label: 'Ngày chỉ định', width: '85px', align: 'center'},
							{name: 'TEN_CLS', label: 'Tên CLS', width: '180px'},
							{name: 'NGAY_THUC_HIEN_CT', label: 'Ngày thực hiện', width: '90px', align: 'center'},
							{name: 'MO_TA', label: 'Kết quả', width: '120px'},
							{name: 'TEN_NGUOI_THUC_HIEN', label: 'Người thực hiện', width: '150px'},
							{name: 'GHI_CHU', label: 'Nội dung', width: '250px', cellattr: function (rowId, val, rawObject, cm, rdata) {
									return 'style="white-space: normal;"';

								},}
						],
						grouping: true,
						groupingView: {
							groupField: ["LOAI"],
							groupColumnShow: [false],
							groupText: ['<b>{0}</b>'],
							groupCollapse: false
						}
					});
				}
			});
			return false;
		} else {
			notifiToClient("Green", "Dữ liệu hợp lệ")
		}
	} catch (e) {
		notifiToClient("Red", "Lỗi kiểm tra dữ liệu")
	}

}
$(function() {
	var oldData = {};
	var oldDataSLT = {};
	var oldDataGiayBaoTu = {};
	var oldDataGiayChuyenTuyen = {};
	var oldDataSLTChuyenTuyen = {};
	var keyLuuLogTTXV = {
		"TRIEUCHUNG": "Triệu chứng",
		"ICD_BENHCHINH": "ICD bệnh chính",
		"TEN_BENHCHINH": "Tên bệnh chính",
		"BENHKEMTHEO": "Bệnh kèm theo",
		"KETQUADIEUTRI": "Kết quả điều trị",
		"TENKETQUADIEUTRI": "Tên kết quả điều trị",
		"TINHTRANG_RV": "Tình trạng ra viện",
		"TENTINHTRANG_RV": "Tên tình trạng ra viện",
		"PP_DIEUTRI": "Phương pháp điều trị",
		"LOIDAN_BS": "Lời dặn",
		"NGAY_RA_VIEN": "Ngày ra viện",
		"NGAY_HENTAIKHAM": "Ngày hẹn tái khám",
		"HENTAIKHAM": "Số ngày hẹn",
		"SOLUUTRU_TAIKHAM": "Số lưu trữ tái khám",
		"GHICHU": "Ghi chú",
		"MA_BENH_YHCT": "Mã bệnh YHCT",
		"NGOAITRU_TUNGAY": "Ngoại trú từ ngày",
		"NGOAITRU_DENNGAY": "Ngoại trú đến ngày",
		"SO_NGAY_NGHI": "Số ngày nghỉ",
		"MA_BAC_SI_TRUONGKHOA": "Mã BS trưởng khoa",
		"TEN_BAC_SI_TRUONGKHOA": "Tên BS trưởng khoa",
		"MA_BAC_SI_TRUONGDONVI": "Mã BS trưởng đơn vị",
		"TEN_BAC_SI_TRUONGDONVI": "Tên BS trưởng đơn vị",
		"SOLUUTRU_XUATVIEN": "Số lưu trữ xuất viện",
		"SOGIAY_HENTAIKHAM": "Số giấy hẹn tái khám",
	}
	var keyLuuLogGiayChuyenTuyen = {
		"KHOA_CHUYENTUYEN": "Khoa",
		"BS": "Bác sĩ",
		"CHUYEN_CUNG_NGUOI_KHAC": "Chuyển tuyến cùng người khác (Số lượng = 1, đơn giá = giá bình thường * 0.5, chỉ có thể áp dụng với số lượng bằng 1)",
		"DAUHIEULAMSANG" : "Dấu hiệu cận lâm sàn",
		"DENNGAY_L2": "Chuyển tuyến DKT đến ngày",
		"DENNGAY_PKDK": "Chuyển tuyến PKDK đến ngày",
		"DENNGAY_TTYT":  "Chuyển tuyến TTYT đến ngày",
		"DENNGAY_TYT": "Chuyển tuyến TYT đến ngày",
		"GHI_CHU": "Ghi chú",
		"HOTENNGUOIDUADI": "Họ tên người đưa đi",
		"HUONGDIEUTRI" : "Hướng điều trị",
		"ICD_BENHCHINH" : "ICD chuyển viện",
		"ICD_BENH_PHU": "ICD bệnh phụ",
		"ICD_NHAPVIEN" : "ICD nhập viện",
		"KETQUAXETNGHIEM_CLS" : "Kết quả xét nghiệm cận lâm sàn",
		"KETQUA_DIEUTRI" : "Kết quả điều trị",
		"LENH_DIEU_XE": "Lệnh điều xe",
		"LOAI_HINH": "Loại hình",
		"LOIDAN_BS": "Lời dặn của bác sĩ",
		"LYDOCHUYENTUYEN": "Lý do chuyển tuyến",
		"MA_BENH_YHCT": "ICD ra viện theo yhct",
		"MA_XANG_DAU": "Loại xăng dầu",
		"NGAY_CHUYENVIEN": "Ngày ra viện",
		"NGAY_VV" : "Ngày vào viện",
		"NOILAMVIEC": "Nơi làm việc",
		"PHI_CHUYENVIEN": "Tiền vận chuyển",
		"PHUONGTIENVANCHUYEN": "Phương tiện",
		"PP_TTPT_THUOC_DADUNG": "Phương pháp, kỹ thuật, kỹ thuật, thuốc",
		"SOCHUYEN_L2": "Số chuyển tuyến DKT",
		"SOCHUYEN_PKDK": "Số chuyển tuyến PKDK",
		"SOCHUYEN_TTYT":  "Số chuyển tuyến TTYT",
		"SOCHUYEN_TYT" : "Số chuyển tuyến TYT",
		"SO_LUOT_CHUYEN": "Số lượt chuyển",
		"TENBENHCHINH_NHAPVIEN" : "Tên ICD nhập viện",
		"TENBENHPHU_NHAPVIEN" : "Tên ICD bệnh phụ",
		"TENBENHVIEN_CHUYENDI": "Bệnh viện chuyển đến",
		"TEN_BENHCHINH" : "Tên ICD chuyển viện",
		"TEN_BENH_YHCT" : "Tên ICD ra viện theo yhct",
		"TINHTRANGBENHNHAN" : "Tình trạng bệnh nhân",
		"TINH_TIEN_CONG_KHAM": "Tính tiền công khám",
		"TRUONG_DON_VI": "Giám đốc",
		"TRUONG_KHOA": "Trưởng khoa",
		"TUNGAY_L2": "Chuyển tuyến DKT từ ngày",
		"TUNGAY_PKDK":  "Chuyển tuyến PKDK từ ngày",
		"TUNGAY_TTYT": "Chuyển tuyến TTYT từ ngày",
		"TUNGAY_TYT" : "Chuyển tuyến TYT từ ngày",
		"TUYEN": "Tuyến chuyển",
		"SOXUATVIEN_LUUTRU": "Số lưu trữ chuyển tuyến",
		"SOXUATVIEN_TT_LUUTRU": "Số lưu trữ tạm chuyển tuyến"
	}

	var keyLuuLogSLTChuyenTuyen ={
		"SOXUATVIEN_LUUTRU": "Số lưu trữ chuyển tuyến",
		"SOXUATVIEN_TT_LUUTRU": "Số lưu trữ tạm chuyển tuyến"
	}

	var keyLuuLogGiayBaoTu = {
		"ID_TV_THONG_TIN": "ID",
		"NGAY_TV": "Thời gian tử vong",
		"NGAY_GHI_NHAN": "Ngày ghi nhạn",
		"NGUOI_THU_THAP": "Người thu thập",
		"CHAM_SOC": "Chăm sóc",
		"TEN_CHAM_SOC": "Tên chăm sóc",
		"NGUOI_THANTHICH": "Người báo tử",
		"QUANHE_NGUOIBAOTU": "Quan hệ với người chết",
		"TEN_QUANHE_NGUOIBAOTU": "Tên quan hệ với người chết",
		"NAMSINH_NGUOIBAOTU": "Năm sinh NBT",
		"ID_NGUYEN_NHAN_TV": "Nguyên nhân",
		"TEN_ID_NGUYEN_NHAN_TV": "Tên nguyên nhân",
		"CHI_TIET_NGUYEN_NHAN": "Chi tiết nguyên nhân",
		"ID_NOI_TV": "Nơi tử vong",
		"TEN_ID_NOI_TV": "Tên nơi tử vong",
		"CHI_TIET_NOI_TU_VONG": "Chi tiết nơi tử vong",
		"ID_BENH": "Mã bệnh",
		"ICD": "Chẩn đoán ICD",
		"MOTA_BENHLY": "Tên bệnh",
		"CO_CHONG": "Có chồng",
		"TEN_CO_CHONG": "Tên có chồng",
		"TUOI_THAI": "Tuổi thai",
		"TUOITHAI_KHICHET": "Tuổi thai KC",
		"TUVONG_ME": "Tử vong mẹ",
		"TEN_TUVONG_ME": "Tên tử vong mẹ",
		"GHI_CHU": "Ghi chú",
		"TT_CHUATRI": "TT chữa trị trước TV",
		"KHAMDIEUTRI_TRUOCTV": "Đã khám/điều trị tại CSYT trong vòng 30 ngày trước khi tử vong",
		"NGOAIVIEN": "Tử vong ngoại viện",
		"GIAYBAOTU": "Được cấp giấy báo tử",
		"MA_GIAY_BAO_TU": "Mã giấy báo tử",
		"NOI_DK_GBT": "Nơi cấp GBT",
		"NGAY_CAP_GBT": "Ngày cấp GBT",
		"DCHI_HIENTAI": "Địa chỉ hiện tại",
		"DCHI_THUONGTRU": "Địa chỉ thường trú",
		"MATINH_THUONGTRU": "Mã tỉnh thường trú",
		"TEN_MATINH_THUONGTRU": "Tên tỉnh thường trú",
		"MAHUYEN_THUONGTRU": "Mã huyện thường trú",
		"TEN_MAHUYEN_THUONGTRU": "Tên huyện thường trú",
		"MAXA_THUONGTRU": "Mã xã thường trú",
		"TEN_MAXA_THUONGTRU": "Tên xã thường trú",
		"MATINH_HIENTAI": "Mã tỉnh hiện tại",
		"TEN_MATINH_HIENTAI": "Tên tỉnh hiện tại",
		"MAHUYEN_HIENTAI": "Mã huyện hiện tại",
		"TEN_MAHUYEN_HIENTAI": "Tên huyện hiện tại",
		"MAXA_HIENTAI": "Mã xã hiện tại",
		"TEN_MAXA_HIENTAI": "Tên xã hiện tại",
		"LOAI_GIAYTO": "Loại giấy tờ",
		"TEN_LOAI_GIAYTO": "Tên loại giấy tờ",
		"SO_GIAYTO": "Số giấy tờ",
		"NGAY_CAP": "Ngày cấp",
		"NOI_CAP": "Nơi cấp",
		"SO_BAOTU": "Sổ",
		"MAQUYEN": "Quyển",
		"TEN_MAQUYEN": "Tên quyển",
	}

	if (singletonObject.tsMaBenhYHCT === "1") {
		$("#tthc_xv_div_icdravien").show();
		$("#tthc_ct_div_icdravien").show();
	} else {
		$("#tthc_xv_div_icdravien").hide();
		$("#tthc_ct_div_icdravien").hide();
	}

	$("#hsba-xulyravien").click(function() {
		addTextTitleModal("titleFormthongtinxuatvien");
		var dataBN = thongtinhsba.thongtinbn
		$("#tthc_xv_khoadieutri").val(dataBN.TEN_PHONGBAN)
		$("#tthc_xv_ngayvaokhoa").val(dataBN.NGAYGIO_NHAPVIEN)
		$("#tthc_xv_icdnhapvien").val(dataBN.ICD_NHAPVIEN)
		$("#tthc_xv_tenicdnhapvien").val(dataBN.TENBENHCHINH_NHAPVIEN)
		loadLoidanbacsi()
		initGridTrieuChung();
		initGridPPDieuTri();
		loadTrieuchung()
		loadPhuongphapdieutri()
		$("#tthc_xv_tinhtiencongkham").prop("checked", false);
		$("#tthc_xv_icdravien").val("")
		$("#tthc_xv_tenicdravien").val("")
		$("#tthc_xv_icdphuravien").val("")
		$("#tthc_xv_tenicdphuravien").val("")
		$("#tthc_xv_ketquadt").val("1")
		$("#tthc_xv_hinhthucxv").val("1")
		$("#tthc_xv_trieuchung").val("")
		$("#tthc_xv_ppdieutri").val("")
		$("#tthc_xv_loidanbacsi").val("")
		$("#tthc_xv_songayhentaikham").val("")
		$("#tthc_xv_ngayhentaikham").val("")
		$("#tthc_xv_truongkhoa").val("-1").trigger("change")
		$("#tthc_xv_giamdoc").val("-1").trigger("change")
		$("#tthc_xv_ghichu").val("")
		$("#tthc_xv_soluutru").val("")
		$("#tthc_xv_noilamviec").val("")
		$("#tthc_xv_soluutru_tam").val("")
		$("#contentFormxuatvien .clear-text").val("")
		$("#tthc_xv_showtttuvong").hide()
		var url = "noitru_xuatvien_loadttBA";
		$.post(url, {dvtt: singletonObject.dvtt, stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN}).done(function (data) {
			if (data) {
				$("#tthc_xv_soluutru").val(data.SOXUATVIEN_LUUTRU);
				$("#tthc_xv_soluutru_tam").val(data.SOXUATVIEN_TT_LUUTRU);
				$("#tthc_xv_soluutru_hentaikham").val(data.SOLUUTRU_TAIKHAM);
				assignNonNullValues(oldDataSLT,data);
			}
		});
		$.get("cmu_list_CMU_NOT_LOAD_TTXUATVIEN_F?url="+convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.STT_DOTDIEUTRI]))
			.done(function (dataRes) {
			var ngayravien = moment().format("DD/MM/YYYY HH:mm")
			if(dataRes.length > 0) {
				var data = dataRes[0];
				$("#tthc_xv_trieuchung").val(data.TRIEUCHUNG);
				$("#tthc_xv_icdravien").val(data.ICD_BENHCHINH);
				$("#tthc_xv_tenicdravien").val(data.TEN_BENHCHINH);
				$("#tthc_xv_tenicdphuravien").val(data.BENHKEMTHEO);
				$("#tthc_xv_ketquadt").val(data.KETQUADIEUTRI);

				if (data.TINHTRANG_RV !== "") {
					$("#tthc_xv_hinhthucxv").val(data.TINHTRANG_RV)
				}
				$("#tthc_xv_ppdieutri").val(data.PP_DIEUTRI);
				$("#tthc_xv_loidanbacsi").val(data.LOIDAN_BS);
				var gioxuatvien = data.GIO_XUATVIEN.split(":")
				$("#tthc_xv_ngayravien").val(data.NGAY_XUATVIEN + " "+ gioxuatvien[0] + ":" + gioxuatvien[1]);
				$("#tthc_xv_ngayhentaikham").val(data.NGAY_HENTAIKHAM);
				$("#tthc_xv_songayhentaikham").val(data.HENTAIKHAM);
				$("#luutru_henkham").val(data.SOLUUTRU_TAIKHAM);
				$("#tthc_xv_ghichu").val(data.GHICHU);
				$("#tthc_xv_ngaybatnghi_ngoaitru").val(data.NGOAITRU_TUNGAY);
				$("#tthc_xv_ngayktnghi_ngoaitru").val(data.NGOAITRU_DENNGAY);
				$("#tthc_xv_songaydt_ngoaitru").val(data.SO_NGAY_NGHI);
				$("#tthc_xv_truongkhoa").val(data.MA_BAC_SI_TRUONGKHOA).trigger("change");
				$("#tthc_xv_giamdoc").val(data.MA_BAC_SI_TRUONGDONVI).trigger("change");
				$("#tthc_xv_soluutru_hentaikham").val(data.SOLUUTRU_TAIKHAM);
				ngayravien = $("#tthc_xv_ngayravien").val();
				assignNonNullValues(oldData,data);
			} else {
				loadChanDoans();
			}
			kiemtradulieuXuatvien({
				...thongtinhsba.thongtinbn,
				NGAYRAVIEN: ngayravien
			})
		}).fail(function() {
			kiemtradulieuXuatvien({
				...thongtinhsba.thongtinbn,
				NGAYRAVIEN: moment().format("DD/MM/YYYY HH:mm")
			})
		});
		$("#modalFormthongtinxuatvien").modal("show");
		initGridYHCT("tthc_xv_icdravien_yhct_list", "tthc_xv_icdravien_yhct_list_wrap");
		loadGridYHCT("tthc_xv_icdravien_yhct_list");
		checkkysogiayhen();
		checkkysogiayravien();


	})

	$("#hsba-xulychuyentuyen").click(function() {
		addTextTitleModal("titleFormthongtinchuyentuyen");
		initLoadChuyentuyen(thongtinhsba.thongtinbn);
		initGridYHCT('tthc_ct_icdravien_yhct_list', "tthc_ct_icdravien_yhct_list_wrap");
		loadGridYHCT("tthc_ct_icdravien_yhct_list");
		$("#modalFormthongtinchuyentuyen").modal("show");
	})

	$("#tthc_xv_icdravien").keypress(function(e) {
		if(e.keyCode == 13) {
			var mabenhICD = $(this).val();
			getMotabenhly(mabenhICD, function(data) {
				var splitIcd = data.split("!!!")
				$("#tthc_xv_tenicdravien").val(splitIcd[1])
				$("#tthc_tv_mabenhly").val(splitIcd[0])
				$("#tthc_xv_icdravien").val(mabenhICD.toUpperCase())
			})
		}
	})
	$("#tthc_xv_icdphuravien").keypress(function(e) {
		if(e.keyCode == 13) {
			var mabenhICD = $(this).val();
			getMotabenhly(mabenhICD, function(data) {
				var splitIcd = data.split("!!!")
				var stringIcd = $("#tthc_xv_tenicdphuravien").val();
				if(!stringIcd.includes(splitIcd[1])) {
					$("#tthc_xv_tenicdphuravien").val( stringIcd + "; (" +mabenhICD.toUpperCase() + ") " + splitIcd[1]);
				}
				$("#tthc_xv_icdphuravien").val("")
			})
		}
	})
	$("#tthc_xv_gensoluutru").click(function() {
		if($("#tthc_xv_soluutru").val() != "") {
			return notifiToClient("Red", "Đã có số lưu trữ")
		}
		if ($("#tthc_xv_soluutru").val() == "") {
			var idButton = this.id;
			$.confirm({
				title: 'Xác nhận!',
				type: 'orange',
				content: 'Bạn Có Muốn tạo số lưu trữ xuất viện cho bệnh nhân ' + thongtinhsba.thongtinbn.TEN_BENH_NHAN + '?',
				buttons: {
					warning: {
						btnClass: 'btn-warning',
						text: "Tiếp tục",
						action: function(){
							showSelfLoading(idButton);
							var url = "noitru_xuatvien_taosoluutru";
							$.post(url, {dvtt: singletonObject.dvtt, stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN}).done(function (data) {
								$("#tthc_xv_soluutru").val(data);
								luuLogHSBATheoBN({
									SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
									LOAI: LOGHSBALOAI.SOLUUTRUNOITRU.KEY,
									NOIDUNGBANDAU: "",
									NOIDUNGMOI: "Thêm mới số lưu trữ " + data,
									USERID: singletonObject.userId,
									ACTION: LOGHSBAACTION.INSERT.KEY
								});
							}).fail(function() {
								notifiToClient("Red", "Tạo số lưu trữ thất bại");
							}).always(function() {
								hideSelfLoading(idButton);
							})
						}
					},
					cancel: function () {
					}
				}
			});

		}
	})
	$("#tthc_xv_capnhatsoluutru").click(function() {
		var idButton = this.id;
		var soluutru = $("#tthc_xv_soluutru").val().trim();
		showSelfLoading(idButton);
		if (soluutru == "") {
			notifiToClient('Red','Số lưu trữ không được để trống',);
		} else {
			var url = "noitru_xv_capnhat_soluutru";
			$.post(url, {
				dvtt: singletonObject.dvtt,
				stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
				soluutru: soluutru
			}).done(function (data) {
				if (data == "1") {
					notifiToClient("Green", "Cập nhật số lưu trữ thành công");

					var dataJSON = convertDataFormToJson("contentFormxuatvien")
					var combinedJson = { ...oldData, ...oldDataSLT };
					var Logbandau = []
					var Logmoi = []
					var diffObject = findDifferencesBetweenObjects(combinedJson, dataJSON);
					for (let key in diffObject) {
						if (keyLuuLogTTXV.hasOwnProperty(key)) {
							Logbandau.push(getLabelValueTTXV(key, combinedJson))
							Logmoi.push(getLabelValueTTXV(key, dataJSON))
						}
					}
					if (Logbandau.length != 0 || Logmoi.length != 0) {
						luuLogHSBATheoBN({
							SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
							LOAI: LOGHSBALOAI.SOLUUTRUNOITRU.KEY,
							NOIDUNGBANDAU: Logbandau.join(";"),
							NOIDUNGMOI: Logmoi.join(";"),
							USERID: singletonObject.userId,
							ACTION: LOGHSBAACTION.EDIT.KEY,
						})
					}
				} else {
					notifiToClient("Red", "Cập nhật số lưu trữ thất bại");
				}
			}).fail(function() {
				notifiToClient("Red", "Cập nhật số lưu trữ thất bại");
			}).always(function() {
				hideSelfLoading(idButton);
			})
		}
	})

	$("#tthc_xv_luuthongtin").click(function() {
		console.log("click")
		var idButton = this.id;
		showSelfLoading(idButton)
		if ($("#tthc_xv_ketquadt").val() == '5') {
			$.confirm({
				title: 'Xác nhận!',
				type: 'orange',
				content: "Chắc chắn bệnh nhân có tử vong?",
				buttons: {
					warning: {
						btnClass: 'btn-warning',
						text: "Tiếp tục",
						action: function(){
							xuatvien();
						}
					},
					cancel: function () {
						hideSelfLoading(idButton);
					}
				}
			});
		} else {
			xuatvien();
		}
	})

	$('#modalFormthongtinxuatvien').on('hidden.bs.modal', function (e) {
		loadDulieuSauravien();
	})

	$('#modalFormthongtinchuyentuyen').on('hidden.bs.modal', function (e) {
		loadDulieuSauravien()
	})

	$("#tthc_xv_gensoluutrutam").click(function() {
		if ($("#tthc_xv_soluutru_tam").val() == "") {
			var idButton = this.id;
			showSelfLoading(idButton)
			$.confirm({
				title: 'Xác nhận!',
				type: 'orange',
				content: "Bạn Có Muốn tạo số lưu trữ xuất viện tạm thời cho bệnh nhân?",
				buttons: {
					warning: {
						btnClass: 'btn-warning',
						text: "Tiếp tục",
						action: function(){

							var url = "noitru_xuatvien_taosoluutru_TT";
							$.post(url, {dvtt: singletonObject.dvtt, stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN})
								.done(function (data) {
									$("#tthc_xv_soluutru_tam").val(data);
									luuLogHSBATheoBN({
										SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
										LOAI: LOGHSBALOAI.SOLUUTRUTAMNOITRU.KEY,
										NOIDUNGBANDAU: "",
										NOIDUNGMOI: "Thêm mới số lưu trữ tạm " + data,
										USERID: singletonObject.userId,
										ACTION: LOGHSBAACTION.INSERT.KEY
									});
								}).fail(function() {
								notifiToClient("Red", "Tạo số lưu trữ thất bại")
							}).always(function() {
								hideSelfLoading(idButton);
							})
						}
					},
					cancel: function () {
						hideSelfLoading(idButton);
					}
				}
			});
		}
	})

	$('#xuativen-btn-khac-dropdown p').click(function () {
		var attrId = $(this).attr('data-id');
		var idButton = "action_xuatvien_btn_khac";
		if(attrId == 'xemgiayhen') {
			var arrTemp = [];
			getFilesign769("PHIEU_NOITRU_GIAYHEN_BACSI", thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt,
				thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
					if (dataKySo.length > 0) {
						arrTemp.push(dataKySo[0])
					}
				});
			getFilesign769("PHIEU_NOITRU_GIAYHEN_DAIDIEN", thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt,
				thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
					if (dataKySo.length > 0) {
						arrTemp.push(dataKySo[0])
					}
				});
			var maxCreateDate = null;
			var maxCreateDataObject = null;
			if(arrTemp.length > 0) {
				$.each(arrTemp, function(index, dataObject) {
					var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
					if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
						maxCreateDate = createDate;
						maxCreateDataObject = dataObject;
					}
				});
				var url = "smartca-get-signed-file-minio?keyminio=" + maxCreateDataObject.KEYMINIO + "&type=pdf";
				$.ajax({
					method: "POST", url: url, contentType: "charset=utf-8"
				}).done(function (data) {
					var pdf = 'data:application/pdf;base64,' +data.FILE;
					previewAndSignPdfDefaultModal({
						url: pdf,
						idButton: 'giayhen_kyso_action',
					}, function(){

					});
				}).fail(function() {
					showMessageSignSmartca769("Không tìm thấy file đã ký số", "red");
				});
			} else {
				var url = 'ingiayhen?dvtt=' + singletonObject.dvtt + "&stt_benhan=" + thongtinhsba.thongtinbn.STT_BENHAN
					+ "&stt_dotdieutri=" + thongtinhsba.thongtinbn.STT_DOTDIEUTRI + "&mabenhnhan=" + thongtinhsba.thongtinbn.MA_BENH_NHAN
					+ "&tenphongban=" + thongtinhsba.thongtinbn.TEN_PHONGBAN
				previewAndSignPdfDefaultModal({
					url: url,
					idButton: 'giayhen_kyso_action',
				}, function(){

				});
			}
			checkkysogiayhen();
		}
		if(attrId == 'xemgiayraiven') {

			showSelfLoading(idButton)
			$.post("noitru_laytenphongban", {maphongban: singletonObject.makhoa}).done(function (data) {
				getUrlGiayRaVien({
					dvtt: singletonObject.dvtt,
					SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
					SOVAOVIEN_DT: thongtinhsba.thongtinbn.SOVAOVIEN_DT
				}).then(objReturn => {
					var url = !objReturn.url? getUrlGiayravienTemp(data): objReturn.url;
					previewPdfDefaultModal(url,
						"preview_tthc_xv_xemgiayravien")
				}).catch(function () {
					var url = getUrlGiayravienTemp(data);
					previewPdfDefaultModal(url,
						"preview_tthc_xv_xemgiayravien")
				})

			}).fail(function() {
				notifiToClient("Red", "Lỗi xem giấy ra viện, vui lòng thử lại sau")
			}).always(function() {
				hideSelfLoading(idButton);
			})
		}
		if(attrId == 'xembangke') {
			showSelfLoading(idButton)
			noitruTaoBangke(thongtinhsba.thongtinbn, function() {
				xembangke(idButton);
			})
		}
		if(attrId == 'xembiabenhan') {
			showSelfLoading(idButton)
			var url = String.format('print-vobenhan-by-id?iD={0}&loaiBenhAn={1}&soVaoVien={2}&soVaoVienDt={3}&smartcafiletype={4}&pageToPrint=1',
				thongtinhsba.thongtinbn.VOBENHAN[0].ID, thongtinhsba.thongtinbn.VOBENHAN[0].ID_VBA,
				thongtinhsba.thongtinbn.SOVAOVIEN,
				thongtinhsba.thongtinbn.SOVAOVIEN_DT, 'pdf');
			previewPdfDefaultModal(url, 'previewPage1')
			hideSelfLoading(idButton);
		}
	});

	$("#tthc_xv_songayhentaikham").keypress(function(e) {
		if(e.keyCode == 13) {
			var songayhen = $("#tthc_xv_songayhentaikham").val();
			if (songayhen == "")
				songayhen = 0;
			var ngay = getDate(singletonObject.ngayhientai);
			ngay = addDays(ngay, parseInt(songayhen));
			var month = parseInt(ngay.getMonth()) + 1;
			var str_date = ngay.getDate() + "/" + month + "/" + ngay.getFullYear();
			$("#tthc_xv_ngayhentaikham").val(str_date);
		}

	})

	$("#tthc_xv_guikyso").click(function() {
		var idButton = this.id;
		showSelfLoading(idButton)
		$.post("noitru_laytenphongban", {maphongban: singletonObject.makhoa}).done(function (khoaxuatvien) {
			if(singletonObject.thamSo960601 == 1) {
				$.post("cmu_post_cmu_grvct_check", {
					url: [
						singletonObject.dvtt,
						thongtinhsba.thongtinbn.SOVAOVIEN,
						'GRV'
					].join("```")
				}).done(function(rescheck) {
					if(rescheck == 2) {
						hideSelfLoading(idButton)
						return notifiToClient("Red", "Giấy ra viện đã được ký số")
					}
					if(rescheck == 1) {
						hideSelfLoading(idButton)
						return notifiToClient("Red", "Bệnh nhân đã được ký số giấy chuyển tuyến")
					}
					guitruongkhoabgdkyso(idButton, 'GRV', $("#tthc_xv_ngayravien").val());
				}).fail(function() {
					notifiToClient("Red", MESSAGEAJAX.ERROR)
					hideSelfLoading(idButton)
				})

				return false;
			}

			var x = new XMLHttpRequest();
			x.onload = function() {
				// Create a form
				var reader = new FileReader();
				reader.readAsDataURL(x.response);
				reader.onloadend = function() {
					var base64data = reader.result;
					var fd = new FormData();


					fd.append("upfile", base64data);
					// If you want to add an extra field for the FormData
					fd.append("dvtt", singletonObject.dvtt);
					fd.append("khoaxv", singletonObject.makhoa);
					fd.append("mabenhnhan", thongtinhsba.thongtinbn.MA_BENH_NHAN);
					fd.append("tenbenhnhan", thongtinhsba.thongtinbn.TEN_BENH_NHAN);
					fd.append("gioitinh", thongtinhsba.thongtinbn.GIOI_TINH);
					fd.append("diachi", thongtinhsba.thongtinbn.DIA_CHI);
					fd.append("ngaygioxuatvien", $("#tthc_xv_ngayravien").val()+":00");
					fd.append("sovaovien", thongtinhsba.thongtinbn.SOVAOVIEN);
					fd.append("sovaoviendt",thongtinhsba.thongtinbn.SOVAOVIEN_DT);
					fd.append("loaigiay",0);

					// Upload to your server
					var y = new XMLHttpRequest();

					y.onload = function(data) {
						var res = JSON.parse(data.currentTarget.response);
						if(res.status == '000') {
							notifiToClient("Green", "Gửi ký số thành công");
							luuLogHSBATheoBN({
								SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
								LOAI: LOGHSBALOAI.XUATVIEN.KEY,
								NOIDUNGBANDAU: "",
								NOIDUNGMOI: singletonObject.user+" ký số xuất viện "+singletonObject.ngayhientai,
								USERID: singletonObject.userId,
								ACTION: LOGHSBAACTION.INSERT.KEY
							});
						} else {
							notifiToClient("Red", "Giấy ra viện đã được ký")
						}
					};
					y.onloadend = function() {
						hideSelfLoading(idButton)
					};


					y.open('POST', 'cmu_uploadfile_giayravien');
					y.send(fd);
				}

			};
			var url = getUrlGiayravienTemp(khoaxuatvien);
			x.responseType = 'blob';    // <-- This is necessary!
			x.open('GET', url, true);
			x.send();
		}).fail(function() {
			notifiToClient("Red", "Lỗi gửi ký số, vui lòng thử lại sau")
			hideSelfLoading(idButton)
		})

	});

	$("#tthc_ct_kyso").click(function() {
		var idButton = this.id;
		showSelfLoading(idButton)
		var result = validateDulieuChuyenTuyen(true);
		if(result === false) {
			hideSelfLoading(idButton)
			return false;
		}
		$.post("cmu_post_cmu_grvct_check", {
			url: [
				singletonObject.dvtt,
				thongtinhsba.thongtinbn.SOVAOVIEN,
				'GCT'
			].join("```")
		}).done(function(rescheck) {
			if(rescheck == 1) {
				hideSelfLoading(idButton)
				return notifiToClient("Red", "Giấy chuyển tuyến đã được ký số")
			}
			if(rescheck == 2) {
				hideSelfLoading(idButton)
				return notifiToClient("Red", "Bệnh nhân đã được ký giấy ra viện")
			}
			doSignSmartca769({
				url:  "noitru_ingiaychuyentuyen?mabenhnhan=" +
					thongtinhsba.thongtinbn.MA_BENH_NHAN
					+"&dvtt=" + singletonObject.dvtt
					+"&stt_benhan=" + thongtinhsba.thongtinbn.STT_BENHAN
					+"&stt_dotdieutri=" + thongtinhsba.thongtinbn.STT_DOTDIEUTRI
				,
				kyHieuPhieu: "NOITRU_GIAYCHUYENTUYEN",
				maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
				maDichVu: -1,
				nghiepVu: "",
				noiTru: 1,
				soBe: -1,
				soBenhAn: "",
				soPhieuDichVu: thongtinhsba.thongtinbn.SOVAOVIEN,
				soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
				soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
				sttDotDieuTri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
				toaThuoc: "",
				keyword: singletonObject.thamSo960602 == 1? "(Ký tên, đóng dấu)" : "(Ký và ghi rõ họ tên)",
				userId: singletonObject.userId,
				userName: singletonObject.user,
				fileName: "Giấy chuyên tuyến nội trú: " + thongtinhsba.thongtinbn.MA_BENH_NHAN + " - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
				dvtt: singletonObject.dvtt
			}, function(json, data) {
				hideSelfLoading(idButton)
				if(_.get(data, 'SUCCESS') > 0) {
					if(singletonObject.thamSo960602 == 0) {
						guitruongkhoabgdkyso(idButton, 'GCT', $("#tthc_ct_ngayravien").val())
					}

				}

			})

		}).fail(function() {
			notifiToClient("Red", MESSAGEAJAX.ERROR)
			hideSelfLoading(idButton)
		})
	});

	$(document).on('click', '#giayhen_kyso_action', function () {
		var url = $('#iframePreviewAndSign').attr('src');
		var idButton = 'giayhen_kyso_action';
		showSelfLoading(idButton)
		var result = validateDulieu(true);
		if(result === false) {
			hideSelfLoading(idButton)
			return false;
		}
		$.post("cmu_post_CMU_GIAYHENTK_KTR", {
			url: [
				singletonObject.dvtt,
				thongtinhsba.thongtinbn.SOVAOVIEN
			].join("```")
		}).done(function(data) {
			if(data == 0) {
				hideSelfLoading(idButton)
				return notifiToClient("Red", "Chưa tạo giấy hẹn ")
			}
			getFilesign769("PHIEU_NOITRU_GIAYHEN_DAIDIEN", thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt,
				thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
					if (dataKySo.length > 0) {
						hideSelfLoading(idButton)
						return notifiToClient("Red", "Giấy hẹn đã được ký số")
					}

					kySoChung({
						dvtt: singletonObject.dvtt,
						userId: singletonObject.userId,
						url: url,
						loaiGiay: "PHIEU_NOITRU_GIAYHEN_BACSI",
						maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
						soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
						soPhieuDichVu: thongtinhsba.thongtinbn.SOVAOVIEN,
						soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
						soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
						keyword: singletonObject.thamSo960602 == 1? "(Ký tên)":"Bác sĩ, y sĩ",
						fileName: "Giấy hẹn khám lại: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
					}, function(dataKySo, data) {
						hideSelfLoading(idButton)
						$("#modalPreviewAndSignPDF").modal("hide");
						checkkysogiayhen();
						if(_.get(data, 'SUCCESS') > 0) {
							$.post("cmu_post_CMU_GIAYHENTK_INS", {
								url: [
									singletonObject.dvtt,
									thongtinhsba.thongtinbn.SOVAOVIEN,
									singletonObject.makhoa,
									$("#tthc_xv_ngayravien").val(),
									singletonObject.userId
								].join("```")
							})
						}
					});
				});
		}).fail(function() {
			hideSelfLoading(idButton)
			notifiToClient("Red", MESSAGEAJAX.ERROR)
		})

	})

	$("#tthc_xv_icdravien_yhct").keypress(function(e) {
		if(e.keyCode == 13) {
			var mabenhICD = $(this).val();
			mabenhICD = mabenhICD.toUpperCase();
			getMotabenhlyYHCT(mabenhICD, function(data) {
				var splitIcd = data.split("!!!")
				$("#tthc_xv_icdravien_yhct").val(mabenhICD);
				$("#tthc_xv_tenicdravien_yhct").val(splitIcd[0]);
				$("#tthc_xv_tenicdravien_yhct").focus();
			})
		}
	})
	$("#tthc_xv_tenicdravien_yhct").keypress(function(e) {
		if(!$("#tthc_xv_icdravien_yhct").val() || !$("#tthc_xv_tenicdravien_yhct").val()) {
			return notifiToClient("Red", "Vui lòng nhập ICD và Tên chẩn đoán")
		}
		if(e.keyCode == 13) {
			var mabenhICD = $("#tthc_xv_icdravien_yhct").val();
			mabenhICD = mabenhICD.toUpperCase();
			$.post("cmu_post", {
				url: [
					singletonObject.dvtt,
					thongtinhsba.thongtinbn.SOVAOVIEN,
					mabenhICD,
					$("#tthc_xv_tenicdravien_yhct").val(),
					singletonObject.userId,
					"NOITRU_XV_ICDYHCT"
				].join("```")
			}).done(function(data) {

				notifiToClient("Green", MESSAGEAJAX.SUCCESS)
				luuLogHSBATheoBN({
					SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
					LOAI: LOGHSBALOAI.XUATVIEN.KEY,
					NOIDUNGMOI: "Thêm ICD YHCT: "+ mabenhICD + "-"+ $("#tthc_xv_tenicdravien_yhct").val(),
					NOIDUNGBANDAU:  "",
					USERID: singletonObject.userId,
					ACTION: LOGHSBAACTION.INSERT.KEY,
				})
				$("#tthc_xv_icdravien_yhct").val("");
				$("#tthc_xv_tenicdravien_yhct").val("");
				$("#tthc_xv_icdravien_yhct").focus();
				loadGridYHCT("tthc_xv_icdravien_yhct_list");
			}).fail(function() {
				notifiToClient("Red", MESSAGEAJAX.FAIL)
			})
		}
	})

	$("#tthc_ct_icdravien_yhct").keypress(function(e) {
		if(e.keyCode == 13) {
			var mabenhICD = $(this).val();
			mabenhICD = mabenhICD.toUpperCase();
			getMotabenhlyYHCT(mabenhICD, function(data) {
				var splitIcd = data.split("!!!")
				$("#tthc_ct_icdravien_yhct").val(mabenhICD);
				$("#tthc_ct_tenicdravien_yhct").val(splitIcd[0]);
				$("#tthc_ct_tenicdravien_yhct").focus();
			})
		}
	})
	$("#tthc_ct_tenicdravien_yhct").keypress(function(e) {
		if(!$("#tthc_ct_icdravien_yhct").val() || !$("#tthc_ct_tenicdravien_yhct").val()) {
			return notifiToClient("Red", "Vui lòng nhập ICD và Tên chẩn đoán")
		}
		if(e.keyCode == 13) {
			var mabenhICD = $("#tthc_ct_icdravien_yhct").val();
			mabenhICD = mabenhICD.toUpperCase();
			$.post("cmu_post", {
				url: [
					singletonObject.dvtt,
					thongtinhsba.thongtinbn.SOVAOVIEN,
					mabenhICD,
					$("#tthc_ct_tenicdravien_yhct").val(),
					singletonObject.userId,
					"NOITRU_XV_ICDYHCT"
				].join("```")
			}).done(function(data) {

				notifiToClient("Green", MESSAGEAJAX.SUCCESS)
				luuLogHSBATheoBN({
					SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
					LOAI: LOGHSBALOAI.CHUYENTUYEN.KEY,
					NOIDUNGMOI: "Thêm ICD YHCT: "+ mabenhICD + "-"+ $("#tthc_ct_tenicdravien_yhct").val(),
					NOIDUNGBANDAU:  "",
					USERID: singletonObject.userId,
					ACTION: LOGHSBAACTION.INSERT.KEY,
				})
				$("#tthc_ct_icdravien_yhct").val("");
				$("#tthc_ct_tenicdravien_yhct").val("");
				$("#tthc_ct_icdravien_yhct").focus();
				loadGridYHCT("tthc_ct_icdravien_yhct_list")
			}).fail(function() {
				notifiToClient("Red", MESSAGEAJAX.FAIL)
			})
		}
	})

	$("#tthc_ct_icdravien").keypress(function(e) {
		if(e.keyCode == 13) {
			var mabenhICD = $(this).val();
			getMotabenhly(mabenhICD, function(data) {
				var splitIcd = data.split("!!!")
				$("#tthc_ct_tenicdravien").val(splitIcd[1])
				$("#tthc_ct_mabenhly").val(splitIcd[0])
				$("#tthc_ct_icdravien").val(mabenhICD.toUpperCase())
			})
		}
	})
	
	$("#tthc_ct_icdphuravien").keypress(function(e) {
		if(e.keyCode == 13) {
			var mabenhICD = $(this).val();
			getMotabenhly(mabenhICD, function(data) {
				var splitIcd = data.split("!!!")
				var stringIcd = $("#tthc_ct_tenicdphuravien").val();
				if(!stringIcd.includes(splitIcd[1])) {
					$("#tthc_ct_tenicdphuravien").val( stringIcd + "; (" +mabenhICD.toUpperCase() + ") " + splitIcd[1]);
				}
				$("#tthc_ct_icdphuravien").val("")
			})
		}
	})
	$("#tthc_ct_phuontienvc").change(function() {
		var val =  $(this).val();
		if(val == 'tự túc') {
			$("#tthc_ct_lenhdieuxe").val("")
			$("#tthc_ct_lenhdieuxe").attr("disabled", true)
			$("#tthc_ct_loaihinhvanchuyen").attr("disabled", true)
		} else {
			$("#tthc_ct_lenhdieuxe").attr("disabled", false)
			$("#tthc_ct_loaihinhvanchuyen").attr("disabled", false)
		}
		changePhivanchuyen();
	})
	$("#tthc_ct_chuyencungnguoikhac").change(function() {
		if($(this).prop("checked")) {
			$("#tthc_ct_manguoidicung").attr("disabled", false)
			$("#tthc_ct_tennguoidicung").attr("disabled", false)
			$("#tthc_ct_soluotchuyen").val(1)
			$("#tthc_ct_soluotchuyen").attr("disabled", true)
		} else {
			$("#tthc_ct_manguoidicung").attr("disabled", true)
			$("#tthc_ct_tennguoidicung").attr("disabled", true)
			$("#tthc_ct_soluotchuyen").attr("disabled", false)
		}
	})

	$("#tthc_ct_gensoluutru").click(function() {
		if($("#tthc_ct_gensoluutru").val() != "") {
			return notifiToClient("Red", "Đã có số lưu trữ")
		}
		if ($("#tthc_ct_gensoluutru").val() == "") {
			var idButton = this.id;
			$.confirm({
				title: 'Xác nhận!',
				type: 'orange',
				content: 'Bạn Có Muốn tạo số lưu trữ chuyển tuyến cho bệnh nhân ' + thongtinhsba.thongtinbn.TEN_BENH_NHAN + '?',
				buttons: {
					warning: {
						btnClass: 'btn-warning',
						text: "Tiếp tục",
						action: function(){
							showSelfLoading(idButton);
							var url = "noitru_xuatvien_taosoluutru";
							$.post(url, {dvtt: singletonObject.dvtt, stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN}).done(function (data) {
								$("#tthc_ct_soluutru").val(data);

								luuLogHSBATheoBN({
									SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
									LOAI: LOGHSBALOAI.SOLUUTRUCHUYENTUYEN.KEY,
									NOIDUNGBANDAU: "",
									NOIDUNGMOI: "Thêm mới số lưu trữ " + data,
									USERID: singletonObject.userId,
									ACTION: LOGHSBAACTION.EDIT.KEY,
								})

							}).fail(function() {
								notifiToClient("Red", "Tạo số lưu trữ thất bại");
							}).always(function() {
								hideSelfLoading(idButton);
							})
						}
					},
					cancel: function () {
					}
				}
			});

		}
	})

	$("#tthc_ct_capnhatsoluutru").click(function() {
		var idButton = this.id;
		var soluutru = $("#tthc_ct_soluutru").val().trim();
		showSelfLoading(idButton);
		if (soluutru == "") {
			notifiToClient('Red','Số lưu trữ không được để trống',);
		} else {
			var url = "noitru_xv_capnhat_soluutru";
			$.post(url, {
				dvtt: singletonObject.dvtt,
				stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
				soluutru: soluutru
			}).done(function (data) {
				if (data == "1") {
					notifiToClient("Green", "Cập nhật số lưu trữ thành công");
					var dataJSON = convertDataFormToJson("contentFormchuyentuyen")
					var combinedJson = { ...oldDataSLTChuyenTuyen };
					var Logbandau = []
					var Logmoi = []
					var diffObject = findDifferencesBetweenObjects(combinedJson, dataJSON);
					for (let key in diffObject) {
						if (keyLuuLogSLTChuyenTuyen.hasOwnProperty(key)) {
							Logbandau.push(getLabelValueGiayChuyenTuyen(key, combinedJson))
							Logmoi.push(getLabelValueGiayChuyenTuyen(key, dataJSON))
						}
					}

					if (Logbandau.length != 0 || Logmoi.length != 0) {
						luuLogHSBATheoBN({
							SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
							LOAI: LOGHSBALOAI.SOLUUTRUCHUYENTUYEN.KEY,
							NOIDUNGBANDAU: Logbandau.join(";"),
							NOIDUNGMOI: Logmoi.join(";"),
							USERID: singletonObject.userId,
							ACTION: LOGHSBAACTION.EDIT.KEY,
						})
					}



				} else {
					notifiToClient("Red", "Cập nhật số lưu trữ thất bại");
				}
			}).fail(function() {
				notifiToClient("Red", "Cập nhật số lưu trữ thất bại");
			}).always(function() {
				hideSelfLoading(idButton);
			})
		}
	})

	$("#tthc_ct_gensoluutrutam").click(function() {
		if ($("#tthc_ct_soluutru_tam").val() == "") {
			var idButton = this.id;
			showSelfLoading(idButton)
			$.confirm({
				title: 'Xác nhận!',
				type: 'orange',
				content: "Bạn Có Muốn tạo số lưu trữ xuất viện tạm thời cho bệnh nhân?",
				buttons: {
					warning: {
						btnClass: 'btn-warning',
						text: "Tiếp tục",
						action: function(){
							var url = "noitru_xuatvien_taosoluutru_TT";
							$.post(url, {dvtt: singletonObject.dvtt, stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN})
								.done(function (data) {
									$("#tthc_ct_soluutru_tam").val(data);

									luuLogHSBATheoBN({
										SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
										LOAI: LOGHSBALOAI.SOLUUTRUTAMCHUYENTUYEN.KEY,
										NOIDUNGBANDAU: "",
										NOIDUNGMOI: "Thêm mới số lưu trữ " + data,
										USERID: singletonObject.userId,
										ACTION: LOGHSBAACTION.EDIT.KEY,
									})

								}).fail(function() {
								notifiToClient("Red", "Tạo số lưu trữ thất bại")
							}).always(function() {
								hideSelfLoading(idButton);
							})
						}
					},
					cancel: function () {
						hideSelfLoading(idButton);
					}
				}
			});
		}
	})

	$("#tthc_ct_benhvien").change(function() {
		loadTuyenchuyendi($(this).val())
		changePhivanchuyen();
	})

	$("#tthc_ct_loaihinhvanchuyen").change(function () {
		changePhivanchuyen();
	})

	$("#tthc_ct_luuthongtin").click(function () {
		var idButton = this.id;
		showSelfLoading(idButton)
		chuyentuyen(idButton);
	})

	$("#tthc_ct_luuxemtruocthongtin").click(function () {
		var idButton = this.id;
		showSelfLoading(idButton)
		luuTTChuyentuyen(idButton);
	})

	$('#chuyentuyen-btn-khac-dropdown p').click(function () {
		var attrId = $(this).attr('data-id');
		var idButton = "action_chuyentuyen_btn_khac";
		if(attrId == 'xemgiaychuyentuyen') {
			getUrlGiayChuyenTuyen({
				dvtt: singletonObject.dvtt,
				SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
				SOVAOVIEN_DT: thongtinhsba.thongtinbn.SOVAOVIEN_DT
			}).then(objReturn => {
				if (objReturn.isError == 0) {
					previewPdfDefaultModal( objReturn.url, "preview_tthc_ct_xemgiaychuyentuyen")
				} else {
					notifiToClient("Red", "Lỗi lấy url giấy báo tử");
				}
			}).catch(error => {
				notifiToClient("Red", error.message || "Lỗi không xác định");
			});
		}
		if(attrId == 'xembangke') {
			showSelfLoading(idButton)
			noitruTaoBangke(thongtinhsba.thongtinbn, function() {
				xembangke(idButton);
			})
		}
	});

	$("#tthc_xv_ketquadt").change(function() {
		console.log("tthc_ct_ketquadt", $("#tthc_xv_ketquadt").val())
		$("#tthc_xv_showtttuvong").hide();
		if($("#tthc_xv_ketquadt").val() == 5) {
			$("#tthc_xv_showtttuvong").show();
			$.confirm({
				title: 'Xác nhận!',
				type: 'orange',
				content: 'Bạn vừa chọn kết quả điều trị là "Tử vong", bạn có chắc chắn lựa chọn thao tác này',
				buttons: {
					warning: {
						btnClass: 'btn-warning',
						text: "Tiếp tục",
						action: function(){
							showModalTuvong()
						}
					},
					cancel: function () {
						$("#tthc_xv_ketquadt").val(1)
					}
				}
			});
		}
	})
	$("#tthc_tv_duoccapgiaybaotu").change(function() {
		if($(this).prop("checked")) {
			$("#contentFormtuvong .giaybaotu").show()
		} else {
			$("#contentFormtuvong .giaybaotu").hide()
		}
	})
	$("#tthc_xv_showtttuvong").click(function() {
		showModalTuvong()
	})
	$("#tthc_tv_luuthongtin").click(function() {
		var idButton = this.id;
		showSelfLoading(idButton)

		if(!$("#xuatvienFormtuvong").valid()){
			hideSelfLoading(idButton)
			return;
		}
		$.ajax({url: "ktngaychotbaocao27ytcs?ngay=" + $("#tthc_tv_ngayghinhan").val()}).done(function (data) {
			if (data != 0){
				hideSelfLoading(idButton)
				notifiToClient("Red","Đã chốt báo cáo ytcs");
			} else {
				luuThongtinTuVong(idButton);
			}
		}).fail(function() {
			hideSelfLoading(idButton)
			notifiToClient("Red","Lỗi không thể lưu thông tin");
		})
	})
	$("#tthc_tv_guikyso").click(function() {
		var idButton = this.id;
		showSelfLoading(idButton)
		if(!$("#tthc_tv_duoccapgiaybaotu").prop("checked")) {
			hideSelfLoading(idButton)
			notifiToClient("Red","Vui lòng chọn cấp giấy báo tử");
			return;
		}
		if(!$("#xuatvienFormtuvong").valid()){
			hideSelfLoading(idButton)
			return;
		}
		luuThongtinTuVong(idButton, guikysochungtu);
	})
	$("#tthc_tv_icdtuvong").keypress(function(e) {
		if(e.keyCode == 13) {
			var mabenhICD = $(this).val();
			getMotabenhly(mabenhICD, function(data) {
				var splitIcd = data.split("!!!")
				$("#tthc_tv_tenicdtuvong").val(splitIcd[1])
				$("#tthc_tv_mabenhly").val(splitIcd[0])
				$("#tthc_tv_icdtuvong").val(mabenhICD.toUpperCase())
			})
		}
	})
	$("#tthc_tv_xoagiaybaotu").click(function() {
		var idtvthongtin = $("#tthc_tv_idthongtin").val();
		if (idtvthongtin == null || idtvthongtin == '') {
			notifiToClient("Red", "Chưa có thông tin để xóa")
		} else {
			showSelfLoading("tthc_tv_xoagiaybaotu")
			$.confirm({
				title: 'Xác nhận!',
				type: 'orange',
				content: 'Bạn chắc chắn xóa thông tin này?',
				buttons: {
					warning: {
						btnClass: 'btn-warning',
						text: "Tiếp tục",
						action: function(){
							var str = [idtvthongtin];
							var url = "xoatv";
							$.post(url, {url : convertArray(str)}).done(function(data) {
								if (data != "-1") {
									notifiToClient("Green","Xóa thành công");
									$("#modalFormthongtintuvong").modal("hide")
									var dataJSON = convertDataFormToJson("xuatvienFormtuvong")
									var stringLog = []
									Object.keys(keyLuuLogGiayBaoTu).forEach(function(key) {
										stringLog.push(getLabelValueGiayBaoTu(key, dataJSON))
									})
									luuLogHSBATheoBN({
										SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
										LOAI: LOGHSBALOAI.GIAYBAOTU.KEY,
										NOIDUNGBANDAU: stringLog.join(";"),
										NOIDUNGMOI: "",
										USERID: singletonObject.userId,
										ACTION: LOGHSBAACTION.DELETE.KEY,
									})
								} else {
									notifiToClient("Red","Lỗi khi thực hiện xóa");
								}
							}).fail(function() {
								notifiToClient("Red","Lỗi khi thực hiện xóa");
							}).always(function() {
								hideSelfLoading("tthc_tv_xoagiaybaotu")
							})
						}
					},
					cancel: function () {
						hideSelfLoading("tthc_tv_xoagiaybaotu")
					}
				}
			});
		}
	})

	$("#tthc_tv_xemgiaybaotu").click(function() {
		var arr = [thongtinhsba.thongtinbn.IDNHANKHAU, thongtinhsba.thongtinbn.MA_BENH_NHAN, 1, "0"];
		var url = "ingiaybaotu_nk?url=" + convertArray(arr);
		previewPdfDefaultModal(url, "tthc_tv_giaybaotu_preview")
	})
	$("#tthc_tv_matinhhientai").change(function() {
		getDSHuyenIDTinh($(this).val(), "tthc_tv_mahuyenhientai", )
	})
	$("#tthc_tv_mahuyenhientai").change(function() {
		getDSXaIDHuyen($(this).val(), "tthc_tv_maxahientai", )
	})

	$("#tthc_tv_matinhthuongtru").change(function() {
		getDSHuyenIDTinh($(this).val(), "tthc_tv_mahuyenthuongtru", )
	})
	$("#tthc_tv_mahuyenthuongtru").change(function() {
		getDSXaIDHuyen($(this).val(), "tthc_tv_maxathuongtru", )
	})
	$("#tthc_tv_quyengiaychungtu").change(function() {
		var maquyen = $(this).val();
		laySogiaychungtu(maquyen)
	})

	$("#tthc_xv_luutttruockhixv").click(function() {
		var idButton = this.id;
		showSelfLoading(idButton)
		if ($("#tthc_xv_ketquadt").val() == '5') {
			$.confirm({
				title: 'Xác nhận!',
				type: 'orange',
				content: "Chắc chắn bệnh nhân có tử vong?",
				buttons: {
					warning: {
						btnClass: 'btn-warning',
						text: "Tiếp tục",
						action: function(){
							luuthongtinxuatvien(idButton);
						}
					},
					cancel: function () {
						hideSelfLoading(idButton);
					}
				}
			});
		} else {
			luuthongtinxuatvien(idButton);
		}
	})

	$("#tthc_xv_gensoluutruhentaikham").click(function() {
		if (!$("#tthc_xv_songayhentaikham").val()) {
			return notifiToClient("Red", "Vui lòng nhập số ngày hẹn tái khám")
		}
		if (!$("#tthc_xv_ngayhentaikham").val()) {
			return notifiToClient("Red", "Vui lòng nhập số ngày hẹn tái khám")
		}
		if (!$("#tthc_xv_soluutru_hentaikham").val()) {
			var idButton = this.id;
			confirmToClient(MESSAGEAJAX.CONFIRM, function () {
				showSelfLoading(idButton)
				var url = "noitru_xuatvien_luusohenkham?url=" + convertArray([singletonObject.dvtt]);
				$.post(url).done(function (data) {
					$("#tthc_xv_soluutru_hentaikham").val(data);
					luuLogHSBATheoBN({
						SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
						LOAI: LOGHSBALOAI.SOGIAYHENNOITRU.KEY,
						NOIDUNGBANDAU: "",
						NOIDUNGMOI: "Thêm mới số số giấy " + data,
						USERID: singletonObject.userId,
						ACTION: LOGHSBAACTION.INSERT.KEY
					});
					luusoHentaikham(idButton)
				}).fail(function() {
					notifiToClient("Red",MESSAGEAJAX.FAIL);
				});
			})

		} else {
			notifiToClient("Red", "Đã có số lưu trữ")
		}
	})

	$("#tthc_xv_capnhatsoluutruhentaikham").click(function() {
		if (!$("#tthc_xv_songayhentaikham").val()) {
			return notifiToClient("Red", "Vui lòng nhập số ngày hẹn tái khám")
		}
		if (!$("#tthc_xv_ngayhentaikham").val()) {
			return notifiToClient("Red", "Vui lòng nhập số ngày hẹn tái khám")
		}
		if (!$("#tthc_xv_soluutru_hentaikham").val()) {
			return notifiToClient("Red", "Vui lòng nhập số lưu trữ")
		}
		if ($("#tthc_xv_soluutru_hentaikham").val()) {
			var idButton = this.id;
			confirmToClient(MESSAGEAJAX.CONFIRM, function () {
				showSelfLoading(idButton)
				luusoHentaikham(idButton)
			})
			var dataJSON = convertDataFormToJson("contentFormxuatvien")
			var combinedJson = { ...oldData, ...oldDataSLT };
			var Logbandau = []
			var Logmoi = []
			var diffObject = findDifferencesBetweenObjects(combinedJson, dataJSON);
			for (let key in diffObject) {
				if (keyLuuLogTTXV.hasOwnProperty(key)) {
					Logbandau.push(getLabelValueTTXV(key, combinedJson))
					Logmoi.push(getLabelValueTTXV(key, dataJSON))
				}
			}
			if (Logbandau.length != 0 || Logmoi.length != 0) {
				luuLogHSBATheoBN({
					SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
					LOAI: LOGHSBALOAI.SOGIAYHENNOITRU.KEY,
					NOIDUNGBANDAU: Logbandau.join(";"),
					NOIDUNGMOI: Logmoi.join(";"),
					USERID: singletonObject.userId,
					ACTION: LOGHSBAACTION.EDIT.KEY,
				})
			}
		} else {
			notifiToClient("Red", "Vui lòng nhập số lưu trữ")
		}
	})

	$("#tthc_xv_xoagiayhen").click(function() {
		var idButton = this.id;
		confirmToClient(MESSAGEAJAX.CONFIRM, function () {
			showSelfLoading(idButton)
			$.post("cmu_post_CMU_DELNGAYHENTAIKHAM", {
				url: [
					singletonObject.dvtt,
					thongtinhsba.thongtinbn.SOVAOVIEN,
					thongtinhsba.thongtinbn.SOVAOVIEN_DT
				].join("```")
			}).done(function (data) {
				$("#tthc_xv_ngayhentaikham").val("");
				$("#tthc_xv_songayhentaikham").val("");
				$("#tthc_xv_soluutru_hentaikham").val("");
				notifiToClient("Green", MESSAGEAJAX.DEL_SUCCESS)
			}).fail(function() {
				notifiToClient("Red",MESSAGEAJAX.FAIL);

			}).always(function() {
				hideSelfLoading(idButton)
			})
		})
	})

	$("#tthc_xv_huykysogiayhen").click(function() {
		var idButton = this.id;
		showSelfLoading(idButton);
		confirmToClient(MESSAGEAJAX.CONFIRM, function () {
			huykysoFilesign769("PHIEU_NOITRU_GIAYHEN_BACSI",
				thongtinhsba.thongtinbn.SOVAOVIEN,
				singletonObject.userId, singletonObject.dvtt,
				thongtinhsba.thongtinbn.SOVAOVIEN,
				thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
					if(_.get(data, 'SUCCESS') == 1) {
						luuLogHSBATheoBN({
							SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
							LOAI: LOGHSBALOAI.HENTAIKHAM.KEY,
							NOIDUNGBANDAU: "Hủy ký số",
							NOIDUNGMOI: "",
							USERID: singletonObject.userId,
							ACTION: LOGHSBAACTION.EDIT.KEY,
						})
						hideSelfLoading(idButton)
						checkkysogiayhen();
						getFilesign769("PHIEU_NOITRU_GIAYHEN_BGD",
							thongtinhsba.thongtinbn.SOVAOVIEN, -1,
							singletonObject.dvtt,
							thongtinhsba.thongtinbn.SOVAOVIEN,
							thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
								if(data.length > 0) {
									$.post("smartca-capnhat-huykyso?keysign="+data[0].KEYSIGN).done(function() {

									})
								}
							})
					}

				})
		})

	})
	$("#tthc_tv_huykysogiaybaotu").click(function() {
		var idButton = this.id;

		confirmToClient(MESSAGEAJAX.CONFIRM, function () {
			showSelfLoading(idButton);
			huykysoFilesign769("NOITRU_GIAYCHUNGTU",
				thongtinhsba.thongtinbn.SOVAOVIEN,
				-1, singletonObject.dvtt,
				thongtinhsba.thongtinbn.SOVAOVIEN,
				thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
					luuLogHSBATheoBN({
						SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
						LOAI: LOGHSBALOAI.GIAYBAOTU.KEY,
						NOIDUNGBANDAU: "Hủy ký số",
						NOIDUNGMOI: "",
						USERID: singletonObject.userId,
						ACTION: LOGHSBAACTION.EDIT.KEY,
					})
					hideSelfLoading(idButton)
					checkkysogiaybaotu();
				});
			huykysoFilesign769("NOITRU_GIAYCHUNGTU_BGD",
				thongtinhsba.thongtinbn.SOVAOVIEN,
				singletonObject.userId, singletonObject.dvtt,
				thongtinhsba.thongtinbn.SOVAOVIEN,
				thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
					hideSelfLoading(idButton)
					checkkysogiaybaotu();
				})
		})
	})

	$("#tthc_xv_huykyso").click(function() {
		var idButton = this.id;
		confirmToClient(MESSAGEAJAX.CONFIRM, function () {
			showSelfLoading(idButton);
			huykysoFilesign769("NOITRU_GIAYRAVIEN",
				thongtinhsba.thongtinbn.SOVAOVIEN,
				-1, singletonObject.dvtt,
				thongtinhsba.thongtinbn.SOVAOVIEN,
				thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
					if(_.get(data, 'SUCCESS') == 1) {
						luuLogHSBATheoBN({
							SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
							LOAI: LOGHSBALOAI.XUATVIEN.KEY,
							NOIDUNGBANDAU: "Hủy ký số",
							NOIDUNGMOI: "",
							USERID: singletonObject.userId,
							ACTION: LOGHSBAACTION.EDIT.KEY,
						})
						$.post("cmu_post_cmu_grvct_del", {
							url: [
								singletonObject.dvtt,
								thongtinhsba.thongtinbn.SOVAOVIEN,
								'GRV'
							].join("```")
						})
						hideSelfLoading(idButton)
						checkkysogiayravien();
						getFilesign769("NOITRU_GIAYRAVIEN_BGD",
							thongtinhsba.thongtinbn.SOVAOVIEN, -1,
							singletonObject.dvtt,
							thongtinhsba.thongtinbn.SOVAOVIEN,
							thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataGD) {
								if(dataGD.length > 0) {
									$.post("smartca-capnhat-huykyso?keysign="+dataGD[0].KEYSIGN).done(function() {

									})
								}
							})
					}
				})

		})
	})
	$("#tthc_xv_huyguikyso").click(function() {
		var idButton = this.id;
		confirmToClient(MESSAGEAJAX.CONFIRM, function () {
			showSelfLoading(idButton);
			$.post("cmu_post_cmu_grvct_del", {
				url: [
					singletonObject.dvtt,
					thongtinhsba.thongtinbn.SOVAOVIEN,
					'GRV'
				].join("```")
			}).done(function(data) {
				if(data > 0) {
					notifiToClient("Green", "Hủy gửi thành công")
					$("#tthc_xv_guikyso").show()
					$("#tthc_xv_huyguikyso").hide()
					luuLogHSBATheoBN({
						SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
						LOAI: LOGHSBALOAI.XUATVIEN.KEY,
						NOIDUNGBANDAU: "",
						NOIDUNGMOI: "Hủy gửi ký số",
						USERID: singletonObject.userId,
						ACTION: LOGHSBAACTION.DELETE.KEY,
					})
				} else {
					notifiToClient("Red", MESSAGEAJAX.ERROR)
				}
			}).fail(function() {
				notifiToClient("Red", MESSAGEAJAX.FAIL)
			}).always(function() {
				hideSelfLoading(idButton);
			})

		})
	})

	$("#tthc_tv_huyguikyso").click(function() {
		var idButton = this.id;
		confirmToClient(MESSAGEAJAX.CONFIRM, function () {
			showSelfLoading(idButton);
			$.post("cmu_post_cmu_giaychungtu_del", {
				url: [
					singletonObject.dvtt,
					thongtinhsba.thongtinbn.SOVAOVIEN
				].join("```")
			}).done(function(data) {
				if(data > 0) {
					notifiToClient("Green", "Hủy gửi thành công")
					$("#tthc_tv_guikyso").show()
					$("#tthc_tv_luuthongtin").show();
					$("#tthc_tv_huyguikyso").hide()
					luuLogHSBATheoBN({
						SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
						LOAI: LOGHSBALOAI.GIAYCHUNGTU.KEY,
						NOIDUNGBANDAU: "",
						NOIDUNGMOI: "Hủy gửi ký số",
						USERID: singletonObject.userId,
						ACTION: LOGHSBAACTION.DELETE.KEY,
					})
				} else {
					notifiToClient("Red", MESSAGEAJAX.ERROR)
				}
			}).fail(function() {
				notifiToClient("Red", MESSAGEAJAX.FAIL)
			}).always(function() {
				hideSelfLoading(idButton);
			})

		})
	})

	$("#tthc_ct_huykyso").click(function() {
		var idButton = this.id;
		confirmToClient(MESSAGEAJAX.CONFIRM, function () {
			showSelfLoading(idButton);
			huykysoFilesign769("NOITRU_GIAYCHUYENTUYEN",
				thongtinhsba.thongtinbn.SOVAOVIEN,
				singletonObject.userId, singletonObject.dvtt,
				thongtinhsba.thongtinbn.SOVAOVIEN,
				thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
					if(_.get(data, 'SUCCESS') == 1) {
						luuLogHSBATheoBN({
							SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
							LOAI: LOGHSBALOAI.CHUYENTUYEN.KEY,
							NOIDUNGBANDAU: "Hủy ký số",
							NOIDUNGMOI: "",
							USERID: singletonObject.userId,
							ACTION: LOGHSBAACTION.EDIT.KEY,
						})
						$.post("cmu_post_cmu_grvct_del", {
							url: [
								singletonObject.dvtt,
								thongtinhsba.thongtinbn.SOVAOVIEN,
								'GCT'
							].join("```")
						})
						hideSelfLoading(idButton)
						checkkysogiaychuyentuyen();
						getFilesign769("NOITRU_GIAYCHUYENTUYEN_BGD",
							thongtinhsba.thongtinbn.SOVAOVIEN, -1,
							singletonObject.dvtt,
							thongtinhsba.thongtinbn.SOVAOVIEN,
							thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataBGD) {
								if(dataBGD.length > 0) {
									$.post("smartca-capnhat-huykyso?keysign="+dataBGD[0].KEYSIGN).done(function() {

									})
								}
							})
					}
				})

		})
	})

	$("#xuatvienFormtuvong").validate({
		rules: {
			tthc_tv_thoigiantuvong: {
				validDateTime: true,
				validDateNgayhientai: true,
				validDateNamsinh: true
			},
			tthc_tv_ngayghinhan: {
				validDate: true,
				validDateNgayhientai: true,
				validDateNamsinh: true
			},
			tthc_tv_ngaycapgiaybaotu: {
				validDate: {
					depends: function(element) {
						return $("#tthc_tv_duoccapgiaybaotu").prop("checked")
					}
				},
				validDateNgayhientai: {
					depends: function(element) {
						return $("#tthc_tv_duoccapgiaybaotu").prop("checked")
					}
				},
				validDateNamsinh: {
					depends: function(element) {
						return $("#tthc_tv_duoccapgiaybaotu").prop("checked")
					}
				},
				required: {
					depends: function(element) {
						return $("#tthc_tv_duoccapgiaybaotu").prop("checked")
					}
				},

			},
			tthc_tv_diachithuongtru: {
				required: function(element) {
					return $("#tthc_tv_duoccapgiaybaotu").prop("checked")
				},
			},
			tthc_tv_diachihientai: {
				required: function(element) {
					return $("#tthc_tv_duoccapgiaybaotu").prop("checked")
				},
			},
			tthc_tv_matinhthuongtru: {
				required: function(element) {
					return $("#tthc_tv_duoccapgiaybaotu").prop("checked")
				},
			},
			tthc_tv_mahuyenthuongtru: {
				required: function(element) {
					return $("#tthc_tv_duoccapgiaybaotu").prop("checked")
				},
			},
			tthc_tv_maxathuongtru: {
				required: function(element) {
					return $("#tthc_tv_duoccapgiaybaotu").prop("checked")
				},
			},
			tthc_tv_matinhhientai: {
				required: function(element) {
					return $("#tthc_tv_duoccapgiaybaotu").prop("checked")
				},
			},
			tthc_tv_mahuyenhientai: {
				required: function(element) {
					return $("#tthc_tv_duoccapgiaybaotu").prop("checked")
				},
			},
			tthc_tv_maxahientai: {
				required: function(element) {
					return $("#tthc_tv_duoccapgiaybaotu").prop("checked")
				},
			},
			tthc_tv_sogiayto: {
				required: function(element) {
					return $("#tthc_tv_duoccapgiaybaotu").prop("checked")
				},
			},
			tthc_tv_ngaycap: {
				validDate: {
					depends: function(element) {
						return $("#tthc_tv_duoccapgiaybaotu").prop("checked")
					}
				},
				required: function(element) {
					return $("#tthc_tv_duoccapgiaybaotu").prop("checked")
				},
			},
			tthc_tv_noicap: {
				required: function(element) {
					return $("#tthc_tv_duoccapgiaybaotu").prop("checked")
				},
			},
			tthc_tv_quyengiaychungtu: {
				required: function(element) {
					return $("#tthc_tv_duoccapgiaybaotu").prop("checked")
				},
			},
			tthc_tv_sgiaychungtu: {
				required: function(element) {
					return $("#tthc_tv_duoccapgiaybaotu").prop("checked")
				},
			},
			tthc_tv_chitietnguyennhantv: {
				maxlength: 200
			}

		},
		messages: {

		}
	});

	$("#themTrieuChung").click(function () {
		let thisBtn = this.id;
		showSelfLoading(thisBtn);
		try {
			$.post("cmu_post_CMU_TRIEU_CHUNG_INS", {
				url: [
					$("#tc_stt").val(),
					$("#tc_trieuchung").val(),
				].join("```")
			}).fail(function() {
				notifiToClient('Red', MESSAGEAJAX.ERROR);
			}).done(function() {
				notifiToClient('Green', MESSAGEAJAX.SUCCESS);
				loadTrieuchung();
				$("#tc_stt").val("");
				$("#tc_trieuchung").val("");
			}).always(function() {
				hideSelfLoading(thisBtn);
			});
		}
		catch (err) {
			console.error(err)
		}
	});

    $("#themPPDieuTri").click(function () {
		let thisBtn = this.id;
		showSelfLoading(thisBtn);
        try {
            $.post("cmu_post_CMU_PP_DIEUTRI_INS", {
                url: [
                    $("#ppdt_stt").val(),
                    $("#ppdt_ppdieutri").val(),
                ].join("```")
            }).fail(function() {
                notifiToClient('Red', MESSAGEAJAX.ERROR);
            }).done(function() {
                notifiToClient('Green', MESSAGEAJAX.SUCCESS);
                loadPhuongphapdieutri();
				$("#ppdt_stt").val("");
				$("#ppdt_ppdieutri").val("");
            }).always(function() {
				hideSelfLoading(thisBtn);
			});
        }
        catch (err) {
            console.error(err)
        }
    });

	$.validator.addMethod("validDateNamsinh", function(value, element) {
		var tem = value.split(" ")
		return convertDateFromString(tem[0], "/") >= convertDateFromString(thongtinhsba.thongtinbn.NGAY_SINH, "/");
	}, "Ngày tử vong không được nhỏ hơn ngày sinh");

	combgridTenICD("tthc_xv_tenicdravien", function(item) {
		$("#tthc_xv_icdravien").val(item.ICD.toUpperCase());
		$("#tthc_xv_tenicdravien").val(item.MO_TA_BENH_LY);
		$("#tthc_tv_mabenhly").val(item.MA_BENH_LY)
	})
	combgridTenICD("tthc_ct_tenicdravien", function(item) {
		$("#tthc_ct_icdravien").val(item.ICD.toUpperCase());
		$("#tthc_ct_tenicdravien").val(item.MO_TA_BENH_LY);
		$("#tthc_ct_mabenhly").val(item.MA_BENH_LY)
	})
	combgridTenICD("tthc_xv_tenicdphuravien", function(item) {
		$("#tthc_xv_tenicdphuravien").val("; ("+item.ICD.toUpperCase()+") "+item.MO_TA_BENH_LY)
	})
	combgridTenICD("tthc_ct_icdphuravien", function(item) {
		$("#tthc_ct_icdphuravien").val("; ("+item.ICD.toUpperCase()+") "+item.MO_TA_BENH_LY)
	})
	combgridTenICDYHCT("tthc_xv_tenicdravien_yhct", function(item) {
		$("#tthc_xv_icdravien_yhct").val(item.MA_YHCT)
		$("#tthc_xv_tenicdravien_yhct").val(item.TENBENH_YHHD)
	})
	combgridTenICDYHCT("tthc_ct_tenicdravien_yhct", function(item) {
		$("#tthc_ct_icdravien_yhct").val(item.MA_YHCT)
		$("#tthc_ct_tenicdravien_yhct").val(item.TENBENH_YHHD)
	})
	function getUrlGiayravienTemp(data) {
		var tuoi = thongtinhsba.thongtinbn.TUOI;
		var thang = thongtinhsba.thongtinbn.THANG;
		var thoigianravien = $("#tthc_xv_ngayravien").val();
		var ngayravien = thoigianravien.split(" ")[0];
		var gioravien = thoigianravien.split(" ")[1]+":00";
		var icd_xv = $("#tthc_xv_icdravien").val();
		var tenbenhchinh = $("#tthc_xv_tenicdravien").val();
		var benhphu = $("#tthc_xv_tenicdphuravien").val();
		var ketquadieutri = $("#tthc_xv_ketquadt").val();
		var ttravien = $('#tthc_xv_hinhthucxv').val();
		var ppdieutri = $("#tthc_xv_ppdieutri").val();
		var loidanbacsi = $("#tthc_xv_loidanbacsi").val();
		var ngayhen = $("#tthc_xv_ngayhentaikham").val();
		var trieuchung = $("#tthc_xv_trieuchung").val();
		var soluutru = $("#tthc_xv_soluutru").val();
		var soluutrutam = $("#tthc_xv_soluutru_tam").val();
		var dvtt = singletonObject.dvtt;
		var url ='ingiayraviennoitru_tmp?dvtt=' + dvtt + "&stt_benhan=" + thongtinhsba.thongtinbn.STT_BENHAN
			+ "&stt_dotdieutri=" + thongtinhsba.thongtinbn.STT_DOTDIEUTRI + "&mabenhnhan=" + thongtinhsba.thongtinbn.MA_BENH_NHAN
			+ "&tenphongban=" + data + "&tuoi=" + tuoi + "&thang=" + thang
			+ "&ngayravien=" + ngayravien + "&gioravien=" + gioravien + "&icd_xv=" + icd_xv + "&tenbenhchinh=" + encodeURIComponent(tenbenhchinh)
			+ "&benhphu=" + encodeURIComponent(benhphu)
			+ "&ketquadieutri=" + ketquadieutri + "&ttravien=" + ttravien + "&ppdieutri=" + ppdieutri + "&loidanbacsi=" + loidanbacsi
			+ "&ngayhen=" + ngayhen + "&trieuchung=" + trieuchung + "&soluutru=" + soluutru + "&soluutrutam=" + soluutrutam + "&noilamviec=''"
		return url;
	}

	function xembangke(idButton) {
		var P_MA_BENH_NHAN = thongtinhsba.thongtinbn.MA_BENH_NHAN;
		var P_SOVAOVIEN = thongtinhsba.thongtinbn.SOVAOVIEN;
		var P_SOVAOVIEN_DT = thongtinhsba.thongtinbn.SOVAOVIEN_DT;
		$.post("noitru_trangthaiketthuc_svv", {
			sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
			sovaovien_dt: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
			dvtt: singletonObject.dvtt,
			stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
			stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI
		}).done(function (dt) {
			if (dt == "0" || dt == "1")
				dt = "1";
			if (dt == "6")
				dt = "3";
			var getTongtien = $.ajax({
				url: 'cmu_getlist?url=' + convertArray([singletonObject.dvtt, P_SOVAOVIEN, P_SOVAOVIEN_DT, 'CMU_TONGTIEN_BANGKE_NOITRU']),
				method: 'get',
				dataType: 'json',
				async: false
			}).responseText;
			var tongtienJSON = JSON.parse(getTongtien);
			if (tongtienJSON[0].SOTIENTHIEU > 0) {
				var dataqr = getQrCode(singletonObject.dvtt, P_MA_BENH_NHAN, P_SOVAOVIEN, P_SOVAOVIEN_DT, 'BANGKE_NOITRU', tongtienJSON[0].SOTIENTHIEU);
				getQrCodeBank(singletonObject.dvtt, P_MA_BENH_NHAN, P_SOVAOVIEN, P_SOVAOVIEN_DT, 'BANGKE_NOITRU', tongtienJSON[0].SOTIENTHIEU, 'BIDV');
				getQrCodeBank(singletonObject.dvtt, P_MA_BENH_NHAN, P_SOVAOVIEN, P_SOVAOVIEN_DT, 'BANGKE_NOITRU', tongtienJSON[0].SOTIENTHIEU, 'VIETINBANK');
			}
			var arr_bk = [singletonObject.dvtt,
				thongtinhsba.thongtinbn.STT_DOTDIEUTRI, thongtinhsba.thongtinbn.STT_BENHAN,
				thongtinhsba.thongtinbn.SOPHIEUTHANHTOAN, P_MA_BENH_NHAN, dt, P_SOVAOVIEN, P_SOVAOVIEN_DT];
			var url_bangke = 'noitru_inbangke?url=' + convertArray(arr_bk);
			hideSelfLoading(idButton)
			setTimeout(function () {
				previewPdfDefaultModal(url_bangke, "tthc_ravien_bangke_preview")
			})

			var P_ID_NHAN_KHAU = 0;
			$.ajax({
				url: "check_out_byt_noi_tru?sovaovien_dt=" + P_SOVAOVIEN_DT + "&sovaovien=" + P_SOVAOVIEN + "&dvtt=" + singletonObject.dvtt,
				type: "post"
			});
			$.ajax({
				url: "lay_id_nhankhau?mabenhnhan=" + thongtinhsba.thongtinbn.MA_BENH_NHAN
			}).done(function (dt) {
				P_ID_NHAN_KHAU = parseInt(dt);
				$.post("ls_hssk_insert",
					{
						mabenhnhan: P_MA_BENH_NHAN,
						idnhankhau: P_ID_NHAN_KHAU,
						sovaovien: P_SOVAOVIEN,
						sovaovien_dt: P_SOVAOVIEN_DT,
						tt_hssk: 3
					}).done(function (trang_thai) {
				});

				$.post("hssk_get_pid",
					{
						mabenhnhan: P_MA_BENH_NHAN,
						idnhankhau: P_ID_NHAN_KHAU,
						sovaovien: P_SOVAOVIEN,
						sovaovien_dt: P_SOVAOVIEN_DT,
						tt_hssk: 3
					}).done(function (trang_thai) {

				}).fail(function (trang_thai) {

				});

			});
		}).fail(function (dt) {
			hideSelfLoading(idButton)
			notifiToClient("Red", "Lỗi xem bảng kê, vui lòng thử lại sau");
		})
	}

	function validateDulieu(ignore) {
		if($("#tthc_xv_icdravien").val().trim() == "") {
			notifiToClient('Red','Chưa nhập ICD ra viện');
			return false;
		}
		if($("#tthc_xv_tenicdravien").val().trim() == "") {
			notifiToClient('Red','Chưa nhập Tên ICD ra viện');
			return false;
		}
		if($("#tthc_xv_soluutru").val().trim() == "" && $("#tthc_xv_soluutru_tam").val().trim() == "") {
			notifiToClient('Red','Chưa tạo số lưu trữ');
			return false;
		}
		if($("#tthc_xv_songaydt_ngoaitru").val().trim() != "" && $("#tthc_xv_songaydt_ngoaitru").val().trim() < 0) {
			notifiToClient('Red','Số ngày điều trị ngoại trú không hợp lệ');
			return false;
		}
		if(!isValidDateTime($("#tthc_xv_ngayravien").val())) {
			notifiToClient('Red','Ngày ra viện không hợp lệ');
			return false;
		}
		if($("#tthc_xv_ngaybatnghi_ngoaitru").val().trim() != "" && !isValidDate($("#tthc_xv_ngaybatnghi_ngoaitru").val())) {
			notifiToClient('Red','Ngày bắt đầu nghỉ ngoại trú không hợp lệ');
			return false;
		}
		if($("#tthc_xv_ngayktnghi_ngoaitru").val().trim() != "" && !isValidDate($("#tthc_xv_ngayktnghi_ngoaitru").val())) {
			notifiToClient('Red','Ngày kết thúc nghỉ ngoại trú không hợp lệ');
			return false;
		}
		if($("#tthc_xv_ppdieutri").val().trim() == "" ) {
			notifiToClient('Red','Vui lòng nhập phương pháp điều trị');
			return false;
		}
		if($("#tthc_xv_truongkhoa").val() == '-1' || !$("#tthc_xv_truongkhoa").val()) {
			notifiToClient('Red','Vui lòng nhập trưởng khoa');
			return false;
		}
		if($("#tthc_xv_ngayhentaikham").val() != '' && !isValidDateTime($("#tthc_xv_ngayhentaikham").val())){
			notifiToClient('Red','Ngày hẹn tái khám không hợp lệ');
			return false;
		}
		if($("#tthc_xv_ngayhentaikham").val() != '' && $("#tthc_xv_soluutru_hentaikham").val() == ''){
			notifiToClient('Red','Vui lòng tạo số lưu trữ hẹn tái khám');
			return false;
		}
		if($("#tthc_xv_soluutru_hentaikham").val() != '' && $("#tthc_xv_ngayhentaikham").val() == ''){
			notifiToClient('Red','Vui lòng nhập ngày hẹn tái khám');
			return false;
		}

		if($("#tthc_xv_ngayhentaikham").val() != '' &&
			Number(moment($("#tthc_xv_ngayravien").val(), ['DD/MM/YYYY HH:mm']).format("YYYYMMDD")) >=
			Number(moment($("#tthc_xv_ngayhentaikham").val(), ['DD/MM/YYYY HH:mm']).format("YYYYMMDD"))){
			notifiToClient('Red','Ngày hẹn tái khám phải lớn hơn ngày ra viện');
			return false;
		}
		return kiemtraCLS($("#tthc_xv_ngayravien").val(), $("#tthc_xv_ketquadt").val(), $("#tthc_xv_hinhthucxv").val(), ignore);
	}

	function kiemtraCLS(ngayra, kqdieutri, tinhtrangravien, ignore) {
		try {
			var url = "noitru_kt_controngkhoa";
			var textWarning = "";
			var ngayravien = ngayra.split(" ");
			var resText = $.ajax({
				url: url,
				type: 'POST',
				data: {
					stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
					dvtt: singletonObject.dvtt,
					maphongban: singletonObject.makhoa,
					stt_logkhoaphong: thongtinhsba.thongtinbn.STT_LOGKHOAPHONG,
					ngayravien: convertStr_MysqlDate(ngayravien[0])
				},
				async: false,
			}).responseText
			if (resText == "-1") {
				notifiToClient("Red", "Bệnh nhân đã được xuất viện. Vui lòng kiểm tra lại");
				return false;
			}
			if (resText == "4") {
				notifiToClient("Red", "Ngày phiếu điều trị lớn hơn ngày ra viện. Vui lòng kiểm tra lại");
				return false;
			}
			if (resText == "-10") {
				notifiToClient("Red", "Phải nhập CÂN NẶNG cho trẻ dưới 1 tuổi!");
				return false;
			}
			if (resText == "-2") {
				notifiToClient("Red", "Bệnh nhân đã được chuyển khoa . Vui lòng kiểm tra lại");
				return false;
			}
			var url_cg = "noitru_kiemtra_capgiuong";

			var resGiuong = $.ajax({
				url: url_cg,
				type: 'POST',
				data: {
					stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
					dvtt: singletonObject.dvtt,
					stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
					ketquadieutri: kqdieutri,
					tinhtrangravien: tinhtrangravien,
					ngayravien: convertStr_MysqlDate(ngayravien[0]),
					thamso82816: singletonObject.thamso82816
				},
				async: false,
			}).responseText
			if (resGiuong == "1") {
				notifiToClient("Red", "Bệnh nhân đã cấp giường nhưng chưa cập nhật ngày rời giường");
				return false;
			}
			if (resGiuong == "3") {
				notifiToClient("Red", "Ngày cấp giường lớn hơn ngày ra viện. Vui lòng kiểm tra lại");
				return false;
			}
			if (resGiuong == "2") {
				textWarning = 'Số ngày giường lớn hơn số ngày điều trị;';
			}
			var arr_params = [singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT];
			arr_params.push(ngayravien+":00");
			var resCLS = $.ajax({
				url: "cmu_list_noitru_kt_cls?url=" + convertArray(arr_params),
				async: false
			}).responseText;
			var resCLSJSON = JSON.parse(resCLS);
			if (resCLSJSON && resCLSJSON.length > 0) {
				$.alert({
					title: 'Dữ liệu không hợp lệ!',
					content: '<table id="noitru_kt_cls_list" class="jqx-grid-cell-wrap"></table>',
					type: 'red',
					boxWidth: '900px',
					useBootstrap: false,
					escapeKey: true,
					closeIcon: true,
					typeAnimated: true,
					onContentReady: function () {
						$("#noitru_kt_cls_list").jqGrid({
							datatype: 'local',
							//regional: 'en', // this is default
							data: resCLSJSON,
							rownumbers: true,
							height: 400,
							colModel: [
								{name: 'LOAI', label: 'Loại'},
								{name: 'STT_DIEUTRI', label: 'Phiếu', width: '40px', align: 'center'},
								{name: 'NGAY_CHI_DINH_CT', label: 'Ngày chỉ định', width: '85px', align: 'center'},
								{name: 'TEN_CLS', label: 'Tên CLS', width: '180px'},
								{name: 'NGAY_THUC_HIEN_CT', label: 'Ngày thực hiện', width: '90px', align: 'center'},
								{name: 'MO_TA', label: 'Kết quả', width: '120px'},
								{name: 'TEN_NGUOI_THUC_HIEN', label: 'Người thực hiện', width: '150px'},
								{name: 'GHI_CHU', label: 'Nội dung', width: '250px', cellattr: function (rowId, val, rawObject, cm, rdata) {
										return 'style="white-space: normal;"';

									},}
							],
							grouping: true,
							groupingView: {
								groupField: ["LOAI"],
								groupColumnShow: [false],
								groupText: ['<b>{0}</b>'],
								groupCollapse: false
							}
						});
					}
				});
				return ignore === true;
			}

			var url_dt = "xv_canhbao_chuadutru?dvtt="+singletonObject.dvtt+"&sovaovien=" +
				thongtinhsba.thongtinbn.SOVAOVIEN+ "&sovaovien_dt=" + thongtinhsba.thongtinbn.SOVAOVIEN_DT;
			var resDutru = $.ajax({
				url: url_dt,
				async: false
			}).responseText;
			if (resDutru == "1") {
				textWarning += 'Bệnh nhân chưa tổng hợp hoặc chưa duyệt dự trù;';
			}
			if (resDutru == "2") {
				textWarning += 'Bệnh nhân chưa tổng hợp hoặc chưa duyệt hoàn trả;';
			}
			if (resDutru == "3") {
				textWarning += 'Bệnh nhân còn thuốc đang dự trù, vui lòng hoàn trả;';
			}
			if (resDutru == "4") {
				textWarning += 'Bệnh nhân còn nợ tiền, bạn có muốn cho xuất viện;';
			}
			if (textWarning != "") {
				if(singletonObject.thamso42001 != '0') {
					notifiToClient("Red", textWarning);
					return false;
				} else {
					return textWarning
				}
			}
			return true;
		} catch (e) {
			notifiToClient("Red", "Lỗi kiểm tra dữ liệu");
			hideSelfLoading("tthc_xv_luuthongtin")
			return false;

		}

	}

	function luuthongtinxuatvien(idButton) {
		var result = validateDulieu(true);
		if(result === false) {
			hideSelfLoading(idButton)
			return false;
		}
		if(result !== true) {
			return $.confirm({
				title: 'Xác nhận!',
				type: 'orange',
				content: result,
				buttons: {
					warning: {
						btnClass: 'btn-warning',
						text: "Tiếp tục",
						action: function(){
							luuXuatvien(idButton);
						}
					},
					cancel: function () {
						hideSelfLoading(idButton)
					}
				}
			});
		}
		luuXuatvien(idButton);
	}

	function luuXuatvien(idButton) {
		var dataBN = thongtinhsba.thongtinbn;
		var phongxuatvien = singletonObject.maphongbenh;
		var splitNgayravien = $("#tthc_xv_ngayravien").val().split(" ")
		var ngayxuatvien = convertStr_MysqlDate(splitNgayravien[0]) + " " + splitNgayravien[1]+":00";
		var gioxuatvien = convertStr_MysqlDate(splitNgayravien[0]) + " " + splitNgayravien[1]+":00";
		var trieuchung = $("#tthc_xv_trieuchung").val();
		var url = "laymotabenhly?icd=" + $("#tthc_xv_icdravien").val();
		var resMabenhly = $.ajax({
			url: url,
			async: false
		}).responseText;
		if(resMabenhly == "") {
			notifiToClient("Red", "Không tìm thấy mã bệnh lý");
			hideSelfLoading("tthc_xv_luuthongtin")
			return false;
		}
		var mabenhchinh = resMabenhly.split("!!!")[0];
		var icdbenhchinh = $("#tthc_xv_icdravien").val();
		var tenbenhchinh = $("#tthc_xv_tenicdravien").val();
		var benhkemtheo = $("#tthc_xv_tenicdphuravien").val();
		var ppdieutri = $("#tthc_xv_ppdieutri").val();
		var loidanbs = $("#tthc_xv_loidanbacsi").val();
		var songayhentaikham = $("#tthc_xv_songayhentaikham").val();
		if (songayhentaikham == "")
			songayhentaikham = "0";
		var ngayhentaikham = convertStr_MysqlDate($("#tthc_xv_ngayhentaikham").val().split(" ")[0]);
		if (ngayhentaikham === undefined)
			ngayhentaikham = '';
		var ketquadieutri = $("#tthc_xv_ketquadt").val();
		var tinhtrang_rv = $("#tthc_xv_hinhthucxv").val();
		var tttinhtiengiuong = $("#tthc_xv_tinhtiencongkham").prop("checked") == true ? "1" : "0";
		var dongia = "0";
		var songay = "0";
		var thanhtien = parseInt(songay) * parseInt(dongia);
		var arr = [
			dataBN.STT_DOTDIEUTRI,
			dataBN.STT_BENHAN,
			singletonObject.dvtt,
			phongxuatvien,
			ngayxuatvien,
			gioxuatvien,
			trieuchung,
			mabenhchinh,
			icdbenhchinh,
			benhkemtheo,
			ketquadieutri,
			tinhtrang_rv,
			ppdieutri,
			loidanbs,
			songayhentaikham,
			ngayhentaikham,
			tenbenhchinh,
			tttinhtiengiuong,
			songay,
			dongia,
			thanhtien,
			singletonObject.makhoa,
			thongtinhsba.thongtinbn.STT_LOGKHOAPHONG,
			dataBN.STT_LOGGIUONGBENH,
			dataBN.MA_BENH_NHAN,
			$("#tthc_xv_ghichu").val(),
			$("#tthc_xv_truongkhoa").val(),
			$("#tthc_xv_giamdoc").val(),
			$("#tthc_xv_ngaybatnghi_ngoaitru").val(),
			$("#tthc_xv_ngayktnghi_ngoaitru").val(),
			$("#tthc_xv_songaydt_ngoaitru").val(),
			getIcdYHCTList("tthc_xv_icdravien_yhct_list"),
			"NOITRU_LUUTHONGTIN_XV"
		];
		$.post("cmu_post", {
			url: arr.join("```")
		}).done(function (data) {
			if (data == "1") {
				notifiToClient("Green", MESSAGEAJAX.ADD_SUCCESS);

				$.luuSongaysauphauthuatPage1($("#tthc_xv_ngayravien").val()).always(function() {
					loadThongTinVBATrang1();
					luuThongTinVBATrang1();
				})
				var dataJSON = convertDataFormToJson("contentFormxuatvien")
				var combinedJson = { ...oldData, ...oldDataSLT };
				var Logbandau = []
				var Logmoi = []
				var diffObject = findDifferencesBetweenObjects(combinedJson, dataJSON);
				for (let key in diffObject) {
					if (keyLuuLogTTXV.hasOwnProperty(key)) {
						Logbandau.push(getLabelValueTTXV(key, combinedJson))
						Logmoi.push(getLabelValueTTXV(key, dataJSON))
					}
				}
				if (Logbandau.length != 0 || Logmoi.length != 0) {
					luuLogHSBATheoBN({
						SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
						LOAI: LOGHSBALOAI.TTXUATVIEN.KEY,
						NOIDUNGBANDAU: Logbandau.join(";"),
						NOIDUNGMOI: Logmoi.join(";"),
						USERID: singletonObject.userId,
						ACTION: LOGHSBAACTION.EDIT.KEY,
					})
				}
			} else {
				notifiToClient("Red", MESSAGEAJAX.FAIL);
			}
		}).fail(function() {
			notifiToClient("Red", MESSAGEAJAX.ERROR);
		}).always(function() {
			hideSelfLoading(idButton)
		});
	}
	function xuatvien() {
		var result = validateDulieu();
		if(result === false) {
			hideSelfLoading("tthc_xv_luuthongtin")
			return false;
		}
		if(result !== true) {
			return $.confirm({
				title: 'Xác nhận!',
				type: 'orange',
				content: result,
				buttons: {
					warning: {
						btnClass: 'btn-warning',
						text: "Tiếp tục",
						action: function(){
							capnhatXuatvien();
						}
					},
					cancel: function () {
						hideSelfLoading("tthc_xv_luuthongtin")
					}
				}
			});
		}
		capnhatXuatvien();
	}

	function capnhatXuatvien() {
		var dataBN = thongtinhsba.thongtinbn;
		var phongxuatvien = singletonObject.maphongbenh;
		var splitNgayravien = $("#tthc_xv_ngayravien").val().split(" ")
		var ngayxuatvien = convertStr_MysqlDate(splitNgayravien[0]) + " " + splitNgayravien[1]+":00";
		var gioxuatvien = convertStr_MysqlDate(splitNgayravien[0]) + " " + splitNgayravien[1]+":00";
		var trieuchung = $("#tthc_xv_trieuchung").val();
		var url = "laymotabenhly?icd=" + $("#tthc_xv_icdravien").val();
		var resMabenhly = $.ajax({
			url: url,
			async: false
		}).responseText;
		if(resMabenhly == "") {
			notifiToClient("Red", "Không tìm thấy mã bệnh lý");
			hideSelfLoading("tthc_xv_luuthongtin")
			return false;
		}
		var mabenhchinh = resMabenhly.split("!!!")[0];
		var icdbenhchinh = $("#tthc_xv_icdravien").val();
		var tenbenhchinh = $("#tthc_xv_tenicdravien").val();
		var benhkemtheo = $("#tthc_xv_tenicdphuravien").val();
		var ppdieutri = $("#tthc_xv_ppdieutri").val();
		var loidanbs = $("#tthc_xv_loidanbacsi").val();
		var songayhentaikham = $("#tthc_xv_songayhentaikham").val();
		if (songayhentaikham == "")
			songayhentaikham = "0";
		var ngayhentaikham = convertStr_MysqlDate($("#tthc_xv_ngayhentaikham").val().split(" ")[0]);
		if (ngayhentaikham === undefined)
			ngayhentaikham = convertStr_MysqlDate(singletonObject.ngayhientai);
		var ketquadieutri = $("#tthc_xv_ketquadt").val();
		var tinhtrang_rv = $("#tthc_xv_hinhthucxv").val();
		var tttinhtiengiuong = $("#tthc_xv_tinhtiencongkham").prop("checked") == true ? "1" : "0";
		var dongia = "0";
		var songay = "0";
		var thanhtien = parseInt(songay) * parseInt(dongia);
		var noilamviec = $("#tthc_xv_noilamviec").val();
		if (noilamviec == "")
			noilamviec = " ";

		var ykiendenghi = " ";
		var nhanxet = " ";
		var ma_bac_si_truongkhoa = $("#tthc_xv_truongkhoa").val();
		var ma_bac_si_truongdonvi = $("#tthc_xv_giamdoc").val();
		var ghichu = $("#tthc_xv_ghichu").val();
		var songaynghi = $("#so_ngay_nghi").val();
		var ngoaitrutungay = convertStr_MysqlDate($("#tthc_xv_ngaybatnghi_ngoaitru").val());
		var ngoaitrudenngay = convertStr_MysqlDate($("#tthc_xv_ngayktnghi_ngoaitru").val());
		var songaynghi = $("#tthc_xv_songaydt_ngoaitru").val();
		var url = "noitru_xuatvien_capnhat?ykiendenghi=" + ykiendenghi + "&nhanxet=" + nhanxet +
			"&sovaovien=" + dataBN.SOVAOVIEN + "&sovaovien_dt=" + dataBN.SOVAOVIEN_DT  +
			"&mabacsi_truongkhoa=" + ma_bac_si_truongkhoa + "&mabacsi_truongdonvi=" +
			ma_bac_si_truongdonvi + "&dieuTriPhongKhamDK="+0+ "&ghichu=" + ghichu
			+ "&soNgayNghi=" + songaynghi
			+ (ngoaitrutungay?"&ngoaiTruTuNgay=" + ngoaitrutungay:"")
			+ (ngoaitrudenngay?"&ngoaiTruDenNgay=" + ngoaitrudenngay:"")
			+ "&maBenhYhct=" + getIcdYHCTList("tthc_xv_icdravien_yhct_list");
		var arr = [dataBN.STT_DOTDIEUTRI,
			dataBN.STT_BENHAN, singletonObject.dvtt, phongxuatvien, ngayxuatvien, gioxuatvien, trieuchung, mabenhchinh,
			icdbenhchinh, benhkemtheo, ketquadieutri, tinhtrang_rv, ppdieutri,
			loidanbs, songayhentaikham, ngayhentaikham, tenbenhchinh,
			tttinhtiengiuong, songay, dongia, thanhtien,
			singletonObject.makhoa, thongtinhsba.thongtinbn.STT_LOGKHOAPHONG,
			dataBN.STT_LOGGIUONGBENH, dataBN.MA_BENH_NHAN, noilamviec,ykiendenghi,nhanxet];
		$.post(url, {url: convertArray(arr)}).done(function (data) {

			noitruTaoBangke(dataBN, function() {
				if(singletonObject.thamso960518 == 1) {
					var arr = [singletonObject.dvtt, dataBN.SOVAOVIEN, dataBN.SOVAOVIEN_DT,'CMU_KIEMTRA_CHIPHICAO'];
					$.post('cmu_post', {
						url: arr.join("```")
					}).done(function (data) {
						if(data == '1') {
							notifiToClient("Red","Bệnh nhân có chi phí cao");
						}
					});
				}
				$.ajax({
					url: "check_out_byt_noi_tru?sovaovien_dt=" + dataBN.SOVAOVIEN_DT + "&sovaovien=" +
						dataBN.SOVAOVIEN + "&dvtt=" + singletonObject.dvtt,
					type: "post"
				});
				if(singletonObject.thamso960484 == 1) {
					cmu_noitru_dongboxml()
				}
			})
			$.luuSongaysauphauthuatPage1($("#tthc_xv_ngayravien").val()).always(function() {
				loadThongTinVBATrang1();
				luuThongTinVBATrang1();
			})
			$.post("cmu_post", {
				url: [singletonObject.dvtt,
					thongtinhsba.thongtinbn.SOVAOVIEN,
					thongtinhsba.thongtinbn.SOVAOVIEN_DT,
					$("#tthc_xv_truongkhoa").val(),
					$("#tthc_xv_ngayhentaikham").val(),
					"NOITRU_LUUTHONGTIN_XVTK"
				].join("```")
			})
			thongtinhsba.thongtinbn['XUATVIEN'] = 1;
			notifiToClient("Green","Xuất viện thành công");
			var dataJSON = convertDataFormToJson("contentFormxuatvien")
			var combinedJson = { ...oldData, ...oldDataSLT };
			var Logbandau = []
			var Logmoi = []
			var diffObject = findDifferencesBetweenObjects(combinedJson, dataJSON);
			for (let key in diffObject) {
				if (keyLuuLogTTXV.hasOwnProperty(key)) {
					Logbandau.push(getLabelValueTTXV(key, combinedJson))
					Logmoi.push(getLabelValueTTXV(key, dataJSON))
				}
			}
			if (Logbandau.length != 0 || Logmoi.length != 0) {
				luuLogHSBATheoBN({
					SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
					LOAI: LOGHSBALOAI.XUATVIEN.KEY,
					NOIDUNGBANDAU: Logbandau.join(";"),
					NOIDUNGMOI: Logmoi.join(";"),
					USERID: singletonObject.userId,
					ACTION: LOGHSBAACTION.EDIT.KEY,
				})
			}
		}).fail(function () {
			notifiToClient("Red", "Lỗi xuất viện, vui lòng thử lại sau")
		}).always(function () {
			hideSelfLoading("tthc_xv_luuthongtin")
		})
	}

	function cmu_noitru_dongboxml(){
		var sovaovien = thongtinhsba.thongtinbn.SOVAOVIEN;
		var sovaovien_dt = thongtinhsba.thongtinbn.SOVAOVIEN_DT;
		var dvtt = singletonObject.dvtt;
		var url = "capnhat_trangthai_dotdieutri_svv?sovaovien=" + sovaovien
			+ "&sovaovien_dt=" + sovaovien_dt
			+ "&dvtt=" + dvtt + "&stt_benhan=" + thongtinhsba.thongtinbn.STT_BENHAN
			+ "&stt_dotdieutri=" + thongtinhsba.thongtinbn.STT_DOTDIEUTRI+ "&tt=3";
		$.ajax({
			type: "POST",
			url: url
		}).done(function (data) {
			$.ajax({
				url: "laytrangthaiphieuthanhtoan?sovaovien_dt=" + sovaovien_dt + "&sovaovien="
					+ sovaovien + "&dvtt=" + dvtt
			}).always(function (data) {

				if (data == "3") {
					var urlxml = "laysoluong_xml_noitru?sovaovien_dt=" + sovaovien_dt + "&sovaovien=" + sovaovien + "&dvtt=" + dvtt;
					$.ajax({
						url: urlxml
					}).always(function (data) {
						$.ajax({
							url: "dongbodulieu_noitru?sovaovien_dt=" + sovaovien_dt + "&sovaovien=" + sovaovien  + "&hinhthuc=0",
							type: "post"
						});
					});
				}
			});
		});
	}

	function loadLoidanbacsi() {
		var url = "noitru_loidanbacsi";
		$.post(url).done(function (data) {
			if (data) {
				$("#tthc_xv_loidanbacsi_sg").empty();
				$.each(data, function (i) {
					$("<option>" + data[i].LOIDAN_BACSI + "</option>").appendTo("#tthc_xv_loidanbacsi_sg");
				});
			}
		});
	}

	function initLoadChuyentuyen(dataBN) {

		$("#contentFormchuyentuyen .clear-text").val("")
		$("#tthc_ct_tuyenbenhvien").val("1")
		$("#tthc_ct_ketquadt").val("1")
		$("#tthc_ct_lydo").val("0")
		$("#tthc_ct_tinhtiencongkham").prop("checked", false)
		$("#tthc_ct_phuontienvc").val("tự túc")
		$("#tthc_ct_lenhdieuxe").attr("disabled", true)
		$("#tthc_ct_loaihinhvanchuyen").attr("disabled", true)
		$("#tthc_ct_manguoidicung").attr("disabled", true)
		$("#tthc_ct_tennguoidicung").attr("disabled", true)
		$("#tthc_ct_soluotchuyen").val("1")
		$("#tthc_ct_loaihinhvanchuyen").val("1")
		$("#tthc_ct_chuyencungnguoikhac").prop("checked", false)
		$("#tthc_ct_bacsi").val("").trigger("change")
		$("#tthc_ct_truongkhoa").val("").trigger("change")
		$("#tthc_ct_giamdoc").val("").trigger("change")
		kiemTraDuoctinhphichuyentuyen();
		$("#tthc_ct_khoadieutri").val(dataBN.TEN_PHONGBAN)
		$("#tthc_ct_ngayvaokhoa").val(dataBN.NGAYGIO_NHAPVIEN)
		$("#tthc_ct_icdnhapvien").val(dataBN.ICD_NHAPVIEN)
		$("#tthc_ct_tenicdnhapvien").val(dataBN.TENBENHCHINH_NHAPVIEN)
		var url = "noitru_xuatvien_loadttBA";
		$.post(url, {dvtt: singletonObject.dvtt, stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN}).done(function (data) {
			if (data) {
				$("#tthc_ct_soluutru").val(data.SOXUATVIEN_LUUTRU);
				$("#tthc_ct_soluutru_tam").val(data.SOXUATVIEN_TT_LUUTRU);
				assignNonNullValues(oldDataSLTChuyenTuyen,data);
			}
		});
		initSelect2IfnotIntance("tthc_ct_bacsi",singletonObject.danhsachnhanvien, "MA_NHANVIEN", "TEN_NHANVIEN", true, true )
		initSelect2IfnotIntance("tthc_ct_truongkhoa",singletonObject.danhsachtruongkhoa, "MA_BAC_SI", "CHUCDANH_HOTEN", false, true )
		initSelect2IfnotIntance("tthc_ct_giamdoc",singletonObject.danhsachnhanvien, "MA_NHANVIEN", "TEN_NHANVIEN", false, true )
		if (thongtinhsba.thongtinbn.SOBAOHIEMYTE != "") {
			$.ajax({
				url: "chuyentuyen_kt_cangiaychuyen?madt=" + thongtinhsba.thongtinbn.MADOITUONG
			}).done(function (data) {
				thongtinhsba.thongtinbn['KHONGCANGIAYCHUYEN'] = data;
			});


			$.ajax({
				url: "chuyentuyen_kiemtratytcangiaychuyen?ma=32&dvtt=" + thongtinhsba.thongtinbn.NOIDANGKYBANDAU,
			}).done(function (data) {
				thongtinhsba.thongtinbn['TYTKHONGCANGIAYCHUYEN'] = data;
			});
		}
		$.post("noitru_giaychuyentuyenselect", {
			dvtt: singletonObject.dvtt,
			sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
			sovaovien_dt: thongtinhsba.thongtinbn.SOVAOVIEN_DT
		}).done(function (data) {
			if(data) {
				$("#tthc_ct_benhvien").val(data.MABENHVIEN_CHUYENDI).trigger("change")
				var giocv = data.GIO_CHUYENVIEN.split(":");
				$("#tthc_ct_ngayravien").val(data.NGAY_CHUYENVIEN + " " + giocv[0] + ":" + giocv[1])
				$("#tthc_ct_icdravien").val(data.ICD_BENHCHINH)
				$("#tthc_ct_tenicdravien").val(data.TEN_BENHCHINH)
				$("#tthc_ct_tenicdphuravien").val(data.BENHKEMTHEO)

				$("#tthc_ct_dauhieulamsang").val(data.DAUHIEULAMSANG)
				$("#tthc_ct_kqxetnghiemcls").val(data.KETQUAXETNGHIEM_CLS)
				$("#tthc_ct_ppktdadt").val(data.PP_TTPT_THUOC_DADUNG)
				$("#tthc_ct_tinhtrangbn").val(data.TINHTRANGBENHNHAN)
				$("#tthc_ct_huongdt").val(data.HUONGDIEUTRI)
				$("#tthc_ct_phuontienvc").val(data.PHUONGTIENVANCHUYEN)
				$("#tthc_ct_ketquadt").val(data.KETQUA_DIEUTRI)
				$("#tthc_ct_loidanbs").val(data.LOIDAN_BS)
				$("#tthc_ct_noilamviec").val(data.NOILAMVIEC)
				$("#tthc_ct_quatrinhbenhly").val(data.QT_BENHLY)
				$("#tthc_ct_bacsi").val(data.NHANVIENCHUYENVIEN).trigger("change")
				$("#tthc_ct_truongkhoa").val(data.MA_BAC_SI_TRUONGKHOA).trigger("change")
				$("#tthc_ct_giamdoc").val(data.TRUONG_DON_VI).trigger("change")
				$("#tthc_ct_ghichu").val(data.GHICHU)
				$("#tthc_ct_lydo").val(data.LYDOCHUYENTUYEN).trigger("change")
				$("#tthc_ct_loaixangdau").val(data.MA_XANG_DAU).trigger("change")
				$("#tthc_ct_soluotchuyen").val(data.SOLUOT_CHUYEN).trigger("change")
				assignNonNullValues(oldDataGiayChuyenTuyen,data);
			}

		})
		checkkysogiaychuyentuyen()
	}

	function kiemTraDuoctinhphichuyentuyen() {
		if(!thongtinhsba.thongtinbn.SOBAOHIEMYTE){
			thongtinhsba.thongtinbn['TINHVANCHUYEN'] = 0;
			return;
		}
		var madt = thongtinhsba.thongtinbn.SOBAOHIEMYTE.substring(0, 3);
		var url1 = "chuyentuyen_duocvanchuyen?madt=" + madt;
		$.ajax({
			url: url1
		}).done(function (data) {
			thongtinhsba.thongtinbn['TINHVANCHUYEN'] = data;
		});
	}

	function loadTuyenchuyendi(bv) {
		var url = "laytuyenchuyendi_bv?mabv=" + bv + "&dvtt="+singletonObject.dvtt;
		$.ajax({
			url: url
		}).done(function (data) {
			$("#tthc_ct_tuyenbenhvien").val(data);
		});
	}
	function changePhivanchuyen() {

		var tt = $("#tthc_ct_loaihinhvanchuyen").val();
		if (tt == "3") {
			$("#tthc_ct_tienvanchuyen").val("0");
		} else {
			var duoc_vanchuyen = (thongtinhsba.thongtinbn.TINHVANCHUYEN == true) ? "1" : "0";
			var url = "layphivanchuyen_theott_list_nt?sovaovien=" +
				thongtinhsba.thongtinbn.SOVAOVIEN + "&sovaovien_dt=" + thongtinhsba.thongtinbn.SOVAOVIEN_DT +
				"&mabv=" + $("#tthc_ct_benhvien").val() + "&dvtt="+singletonObject.dvtt+"&tt=" + tt + "&duocvc=" + duoc_vanchuyen;
			$.ajax({
				url: url
			}).done(function (data) {
				$("#tthc_ct_tienvanchuyen").val(data.TIEN_VAN_CHUYEN);
				$("#tthc_ct_so_km_donvichuyen").val(data.SO_KM_DONVICHUYEN);
				$("#tthc_ct_giaxang").val(data.GIA_XANG);
			});

		}
	}

	function chuyentuyen(idButton) {
		var result = validateDulieuChuyenTuyen();
		if(result === false) {
			hideSelfLoading(idButton)
			return false;
		}
		if(result !== true) {
			return $.confirm({
				title: 'Xác nhận!',
				type: 'orange',
				content: result,
				buttons: {
					warning: {
						btnClass: 'btn-warning',
						text: "Tiếp tục",
						action: function(){
							capnhatChuyentuyen();
						}
					},
					cancel: function () {
						hideSelfLoading(idButton)
					}
				}
			});
		}
		capnhatChuyentuyen();

	}

	function validateDulieuChuyenTuyen(ignore) {
		if($("#tthc_ct_benhvien").val() == "") {
			notifiToClient('Red','Chưa chọn bệnh viện');
			return false;
		}
		if($("#tthc_ct_bacsi").val() == "") {
			notifiToClient('Red','Chưa chọn bác sĩ');
			return false;
		}
		if($("#tthc_ct_icdravien").val().trim() == "") {
			notifiToClient('Red','Chưa nhập ICD ra viện');
			return false;
		}
		if($("#tthc_ct_tenicdravien").val().trim() == "") {
			notifiToClient('Red','Chưa nhập Tên ICD ra viện');
			return false;
		}
		if($("#tthc_ct_ppktdadt").val().trim() == "") {
			notifiToClient('Red','Vui lòng nhập phương pháp, thủ thuật, kỹ thuật thuốc đã dùng điều trị');
			$("#tthc_ct_ppktdadt").focus();
			return false;
		}
		if($("#tthc_ct_quatrinhbenhly").val().trim() == "") {
			notifiToClient('Red','Vui lòng nhập quá trình bệnh lý');
			$("#tthc_ct_ppktdadt").focus();
			return false;
		}
		if($("#tthc_ct_huongdt").val().trim() == "") {
			notifiToClient('Red','Vui lòng nhập hướng điều trị');
			$("#tthc_ct_ppktdadt").focus();
			return false;
		}
		if(!$("#tthc_ct_truongkhoa").val()) {
			notifiToClient('Red','Vui lòng nhập trưởng khoa');
			return false;
		}
		if($("#tthc_ct_soluutru").val().trim() == "" && $("#tthc_ct_soluutru_tam").val() == "") {
			notifiToClient('Red','Chưa tạo số lưu trữ');
			return false;
		}
		if(!isValidDateTime($("#tthc_ct_ngayravien").val())) {
			notifiToClient('Red','Ngày chuyển tuyến không hợp lệ');
			return false;
		}
		if(kiemTrathanhtoanChuyentuyen() == "1") {
			notifiToClient("Red","Lần chuyển tuyến này đã được thanh toán, vui lòng hủy thanh toán trước khi chỉnh sửa thông tin!");
			return false;
		}
		return kiemtraCLS($("#tthc_ct_ngayravien").val(),$("#tthc_ct_ketquadt").val(),1, ignore);
	}

	function kiemTrathanhtoanChuyentuyen() {
		var url_kt_tt_ct = "kiemtra_thanh_toan_chuyentuyen_noitru";
		var result = $.ajax({
			url: url_kt_tt_ct + "?sovaovien=" + thongtinhsba.thongtinbn.SOVAOVIEN
				+ "&sovaovien_dt=" + thongtinhsba.thongtinbn.SOVAOVIEN_DT+"&dvtt="+singletonObject.dvtt,
			type:"GET",
			async: false

		}).responseText
		return result;
	}

	function capnhatChuyentuyen() {
		var dvtt = singletonObject.dvtt;
		var phongchuyenvien = singletonObject.maphongbenh;
		var nhanvienchuyenvien = $("#tthc_ct_bacsi").val();
		var ngaycv = $("#tthc_ct_ngayravien").val().split(" ")
		var ngayxuly = convertStr_MysqlDate(ngaycv[0]) + " " + ngaycv[1]+":00";
		var ngaychuyenvien = convertStr_MysqlDate(ngaycv[0]) + " " +  ngaycv[1]+":00";
		var mabenhvienchuyendi = $("#tthc_ct_benhvien").val();
		var tenbenhvienchuyendi = $("#tthc_ct_benhvien option:selected").text();
		var tuyenbenhvien = $("#tthc_ct_tuyenbenhvien").val();
		var noilamviec = $("#tthc_ct_noilamviec").val();
		var dauhieulamsang = $("#tthc_ct_dauhieulamsang").val();
		var ketquaxetnghiem_cls = $("#tthc_ct_kqxetnghiemcls").val();
		var mabenhchinh = $("#tthc_ct_mabenhly").val();
		var icdbenhchinh = $("#tthc_ct_icdravien").val();
		var tenbenhchinh = $("#tthc_ct_tenicdravien").val();
		var benhkemtheo = $("#tthc_ct_tenicdphuravien").val();
		benhkemtheo = benhkemtheo.replaceAll("+", "=");
		var pp_ttpt_thuocdadung = $("#tthc_ct_ppktdadt").val();
		var tinhtrangbenhnhan = $("#tthc_ct_tinhtrangbn").val();
		var lydochuyentuyen = $("#tthc_ct_lydo").val();
		var sochuyentyt = $("#tthc_ct_sochuyentyt").val();
		var tentyt = $("#tthc_ct_tentyt").val().trim();
		var tuyentyt = 4;
		var tungaytyt = $("#tthc_ct_tyt_tungay").val();
		var denngaytyt = $("#tthc_ct_tyt_denngay").val();
		var sochuyenpkdk = $("#tthc_ct_sochuyenpkdk").val();
		var tenpkdk = $("#tthc_ct_tenpkdk").val();
		var tuyenpkdk = 3;
		var tungaypkdk = $("#tthc_ct_pkdk_tungay").val();
		var denngaypkdk = $("#tthc_ct_pkdk_denngay").val();
		var sochuyenttyt = $("#tthc_ct_sochuyenttyt").val();
		var tenttyt = $("#tthc_ct_tenttyt").val();
		var tuyenttyt = 3;
		var tungayttyt = $("#tthc_ct_ttyt_tungay").val();
		var denngayttyt = $("#tthc_ct_ttyt_denngay").val();
		var huongdieutri = $("#tthc_ct_huongdt").val();
		var loidan_bs = $("#tthc_ct_loidanbs").val();
		var hotennguoiduadi = $("#tthc_ct_hotennguoiduadi").val();
		var benhnhanduoc_vc = "false";
		var mauchuyenvien = "1";
		var bhyt = thongtinhsba.thongtinbn.SOBAOHIEMYTE;
		var ketquadieutri = $("#tthc_ct_ketquadt").val();
		var soluotchuyen = $("#tthc_ct_soluotchuyen").val();

		var sochuyenl2 = $("#tthc_ct_sochuyendkt").val();
		var tenl2 = $("#tthc_ct_tendkt").val();
		var tuyenl2 = 2;
		//var tuyenl2=2;
		var tungayl2 = $("#tthc_ct_dkt_tungay").val();
		var denngayl2 = $("#tthc_ct_dkt_denngay").val();
		var ghichu = $("#tthc_ct_ghichu").val();
		var madt = "";
		if (!bhyt) {
			madt = "";
		} else {
			madt = bhyt.substring(0, 3);
		}

		var phuongtien = $("#tthc_ct_phuontienvc").val().trim();
		var phi_chuyenvien = "0";
		var tt_phichuyenvien = "1";
		var lenhdieuxe = "";
		var so_km_chuyen = "0";
		var gia_xang = "0";
		if (phuongtien == "tự túc") {
			phi_chuyenvien = "0";
			tt_phichuyenvien = "1";
			lenhdieuxe = "";
			so_km_chuyen = "0";
			gia_xang = "0";
		}  else {
			phi_chuyenvien = $("#tthc_ct_tienvanchuyen").val();
			tt_phichuyenvien = $("#tthc_ct_loaihinhvanchuyen").val();
			lenhdieuxe = $("#tthc_ct_lenhdieuxe").val();
			so_km_chuyen = $("#tthc_ct_so_km_donvichuyen").val();
			gia_xang = $("#tthc_ct_giaxang").val();
		}
		var tttinhtiengiuong = $("#tthc_ct_tinhtiencongkham").prop("checked") == true ? "1" : "0";
		var chuyendoi_ck = $("#tthc_ct_chuyencungnguoikhac").prop('checked');
		if (chuyendoi_ck == true)
			chuyendoi_ck = 1;
		else
			chuyendoi_ck = 0;
		var dataBN = thongtinhsba.thongtinbn;
		var arr = [
			dataBN.STT_DOTDIEUTRI,
			dataBN.STT_BENHAN, dvtt, phongchuyenvien,
			nhanvienchuyenvien, ngayxuly, ngaychuyenvien, mabenhvienchuyendi, tenbenhvienchuyendi,
			tuyenbenhvien, dauhieulamsang, ketquaxetnghiem_cls, mabenhchinh, icdbenhchinh, tenbenhchinh, benhkemtheo, pp_ttpt_thuocdadung, tinhtrangbenhnhan, lydochuyentuyen,
			sochuyentyt, tuyentyt, tungaytyt, denngaytyt, sochuyenpkdk, tuyenpkdk, tungaypkdk, denngaypkdk, sochuyenttyt, tuyenttyt, tungayttyt,
			denngayttyt, huongdieutri, phuongtien, hotennguoiduadi, benhnhanduoc_vc, phi_chuyenvien, tt_phichuyenvien, lenhdieuxe, tentyt, tenpkdk,
			tenttyt, mauchuyenvien, dataBN.STT_LOGKHOAPHONG,
			dataBN.STT_LOGGIUONGBENH, singletonObject.makhoa,
			madt, dataBN.MA_BENH_NHAN, tttinhtiengiuong, ketquadieutri,
			dataBN.SOVAOVIEN, dataBN.SOVAOVIEN_DT,
			soluotchuyen, chuyendoi_ck, $("#tthc_ct_manguoidicung").val(), $("#tthc_ct_tennguoidicung").val(),
			so_km_chuyen, gia_xang, noilamviec, sochuyenl2, tuyenl2, tungayl2, denngayl2, tenl2, loidan_bs, 0];
		var url = "noitru_capnhapchuyentuyen_new?sovaovien=" + dataBN.SOVAOVIEN + "&sovaovien_dt=" +
			dataBN.SOVAOVIEN_DT + "&dieuTriPhongKhamDK="+0
			+"&quaTrinhBenhLy="+$("#tthc_ct_quatrinhbenhly").val().trim();
		$.post(url, {
			url: convertArray(arr),
			maXangDau: $("#tthc_ct_loaixangdau").val(),
			ghiChu: ghichu
			, nguoiKyTruongDonVi: $("#tthc_ct_giamdoc").val()
			, nguoiKyTruongKhoa: $("#tthc_ct_truongkhoa").val()
			, maBenhYhct: getIcdYHCTList("tthc_ct_icdravien_yhct_list")
		}).done(function (data) {

			noitruTaoBangke(dataBN, function() {
				if(singletonObject.thamso960518 == 1) {
					var arr = [singletonObject.dvtt, dataBN.SOVAOVIEN, dataBN.SOVAOVIEN_DT,'CMU_KIEMTRA_CHIPHICAO'];
					$.post('cmu_post', {
						url: arr.join("```")
					}).done(function (data) {
						if(data == '1') {
							notifiToClient("Red","Bệnh nhân có chi phí cao");
						}
					});
				}
				$.ajax({
					url: "check_out_byt_noi_tru?sovaovien_dt=" + dataBN.SOVAOVIEN_DT + "&sovaovien=" +
						dataBN.SOVAOVIEN + "&dvtt=" + singletonObject.dvtt,
					type: "post"
				});
				if(singletonObject.thamso960484 == 1) {
					cmu_noitru_dongboxml()
				}
			})
			$.luuSongaysauphauthuatPage1($("#tthc_ct_ngayravien").val()).always(function() {
				loadThongTinVBATrang1();
				luuThongTinVBATrang1();
			})
			thongtinhsba.thongtinbn['XUATVIEN'] = 1;
			notifiToClient("Green","Chuyển tuyến thành công");

			var dataJSON = convertDataFormToJson("contentFormchuyentuyen")
			var combinedJson = { ...oldDataGiayChuyenTuyen, ...oldData };
			var Logbandau = []
			var Logmoi = []
			var diffObject = findDifferencesBetweenObjects(combinedJson, dataJSON);
			for (let key in diffObject) {
				if (keyLuuLogGiayChuyenTuyen.hasOwnProperty(key)) {
					Logbandau.push(getLabelValueGiayChuyenTuyen(key, combinedJson))
					Logmoi.push(getLabelValueGiayChuyenTuyen(key, dataJSON))
				}
			}
			luuLogHSBATheoBN({
				SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
				LOAI: LOGHSBALOAI.CHUYENTUYEN.KEY,
				NOIDUNGBANDAU: Logbandau.join(";"),
				NOIDUNGMOI: Logmoi.join(";"),
				USERID: singletonObject.userId,
				ACTION: LOGHSBAACTION.EDIT.KEY,
			})

		}).fail(function () {
			notifiToClient("Red", "Lỗi chuyển tuyến, vui lòng thử lại sau")
		}).always(function () {
			hideSelfLoading("tthc_ct_luuthongtin")
		});
	}

	function loadDulieuSauravien() {
		if (thongtinhsba.thongtinbn['XUATVIEN'] == 1) {
			load_dsbenhnhan()
			$("#hsba_tabs").tabs("option", "active", 0);
			$("#hsba_tabs").tabs("option", "disabled", [1]);
		}
	}

	function showModalTuvong() {
		addTextTitleModal("titleFormthongtintuvong");

		$("#contentFormtuvong .clear-text").val("")
		$("#tthc_tv_chamsoc").val(1)
		$("#tthc_tv_cochong").val(0)
		$("#tthc_tv_tuvongme").val(0)
		$("#tthc_tv_tuvongngoaivien").prop("checked", false)
		$("#tthc_tv_duoccapgiaybaotu").prop("checked", false)
		$("#tthc_tv_dakhamdieutri").prop("checked", false)
		$("#contentFormtuvong .giaybaotu").hide()
		$("#tthc_tv_xoagiaybaotu").hide()
		$("#tthc_tv_icdtuvong").val($("#tthc_xv_icdravien").val())
		$("#tthc_tv_tenicdtuvong").val($("#tthc_xv_tenicdravien").val())
		initSelect2IfnotIntance("tthc_tv_matinhthuongtru", singletonObject.danhsachtinh,
			"MA_TINH_THANH", "TEN_TINH_THANH", true, true)
		initSelect2IfnotIntance("tthc_tv_matinhhientai", singletonObject.danhsachtinh,
			"MA_TINH_THANH", "TEN_TINH_THANH", true, true)
		showLoaderIntoWrapId("contentFormtuvong")
		laySogiaychungtu($("#tthc_tv_quyengiaychungtu").val())
		$.get("cmu_list_CMU_GETTHONGTIN_TUVONG?url="+convertArray(
			[
				singletonObject.dvtt,
				thongtinhsba.thongtinbn.MA_BENH_NHAN,
				thongtinhsba.thongtinbn.SOVAOVIEN
			]), function (data) {
			if(data.length > 0) {
				$("#tthc_tv_idthongtin").val(data[0].ID_TV_THONG_TIN)
				$("#tthc_tv_thoigiantuvong").val(data[0].NGAY_TV)
				$("#tthc_tv_ngayghinhan").val(data[0].NGAY_GHI_NHAN)
				$("#tthc_tv_nguoithuthap").val(data[0].NGUOI_THU_THAP)
				$("#tthc_tv_chamsoc").val(data[0].CHAM_SOC)
				$("#tthc_tv_nguoibaotu").val(data[0].NGUOI_THANTHICH)
				$("#tthc_tv_quanhenguoichet").val(data[0].QUANHE_NGUOIBAOTU)
				$("#tthc_tv_namsinhnbt").val(data[0].NAMSINH_NGUOIBAOTU)
				$("#tthc_tv_nguyennhantv").val(data[0].ID_NGUYEN_NHAN_TV)
				$("#tthc_tv_chitietnguyennhantv").val(data[0].CHI_TIET_NGUYEN_NHAN)
				$("#tthc_tv_noituvong").val(data[0].ID_NOI_TV)
				$("#tthc_tv_chitietnoitv").val(data[0].CHI_TIET_NOI_TU_VONG)
				$("#tthc_tv_tenbenh").val(data[0].ID_BENH)
				$("#tthc_tv_mabenhly").val(data[0].ID_BENH)
				$("#tthc_tv_icdtuvong").val(data[0].ICD)
				$("#tthc_tv_tenicdtuvong").val(data[0].MOTA_BENHLY)
				$("#tthc_tv_cochong").val(data[0].CO_CHONG)
				$("#tthc_tv_tuoithai").val(data[0].TUOI_THAI)
				$("#tthc_tv_tuoithaikc").val(data[0].TUOITHAI_KHICHET)
				$("#tthc_tv_tuvongme").val(data[0].TUVONG_ME)
				$("#tthc_tv_ghichu").val(data[0].GHI_CHU)
				$("#tthc_tv_ttchuatritruoctv").val(data[0].TT_CHUATRI)
				$("#tthc_tv_dakhamdieutri").prop("checked",data[0].KHAMDIEUTRI_TRUOCTV == 1)
				$("#tthc_tv_tuvongngoaivien").prop("checked",data[0].NGOAIVIEN == 1)
				$("#tthc_tv_duoccapgiaybaotu").prop("checked",data[0].GIAYBAOTU == 1)
				if(data[0].GIAYBAOTU == 1) {
					$("#tthc_tv_magiaybaotu").val(data[0].MA_GIAY_BAO_TU)
					$("#tthc_tv_noigiaybaotu").val(data[0].NOI_DK_GBT)
					$("#tthc_tv_ngaycapgiaybaotu").val(data[0].NGAY_CAP_GBT)
					$("#tthc_tv_diachihientai").val(data[0].DCHI_HIENTAI);
					$("#tthc_tv_diachithuongtru").val(data[0].DCHI_THUONGTRU);
					$("#tthc_tv_matinhthuongtru").val(data[0].MATINH_THUONGTRU).trigger("change");
					getDSHuyenIDTinh(data[0].MATINH_THUONGTRU, "tthc_tv_mahuyenthuongtru", data[0].MAHUYEN_THUONGTRU);
					getDSXaIDHuyen(data[0].MAHUYEN_THUONGTRU, "tthc_tv_maxathuongtru", data[0].MAXA_THUONGTRU);
					$("#tthc_tv_matinhhientai").val(data[0].MATINH_HIENTAI).trigger("change");
					getDSHuyenIDTinh(data[0].MATINH_HIENTAI, "tthc_tv_mahuyenhientai", data[0].MAHUYEN_HIENTAI);
					getDSXaIDHuyen(data[0].MAHUYEN_HIENTAI, "tthc_tv_maxahientai", data[0].MAXA_HIENTAI);

					$("#tthc_tv_loaigiayto").val(data[0].LOAI_GIAYTO);
					$("#tthc_tv_sogiayto").val(data[0].SO_GIAYTO);
					$("#tthc_tv_ngaycap").val(data[0].NGAY_CAP);
					$("#tthc_tv_noicap").val(data[0].NOI_CAP);
					$("#tthc_tv_sgiaychungtu").val(data[0].SO_BAOTU);
					$("#tthc_tv_quyengiaychungtu").val(data[0].MAQUYEN);
				}
				$("#tthc_tv_xoagiaybaotu").show();
				$("#contentFormtuvong .giaybaotu").show();
				assignNonNullValues(oldDataGiayBaoTu,data[0]);
			}
			$("#modalFormthongtintuvong").modal("show");
		}).fail(function(){
			notifiToClient("Red", "Lỗi load thông tin tử vong")
		}).always(function () {
			hideLoaderIntoWrapId("contentFormtuvong")
		})
		checkkysogiaybaotu()
	}

	function luuThongtinTuVong(idButton, callback) {
		var dataBN = thongtinhsba.thongtinbn
		var idnhankhau = dataBN.IDNHANKHAU;
		var mabenhnhan = dataBN.MA_BENH_NHAN;
		var sovaovien = dataBN.SOVAOVIEN;
		var ngayghinhan_tv = $("#tthc_tv_ngayghinhan").val();
		var chitietnoituvong_tv = $("#tthc_tv_chitietnoitv").val();
		var chitietnguyennhan_tv = $("#tthc_tv_chitietnguyennhantv").val();
		var ngaytuvong_tv = $("#tthc_tv_thoigiantuvong").val().split(" ")[0];
		var nguyennhan_tv = $("#tthc_tv_nguyennhantv").val();
		var chamsoc_tv = $("#tthc_tv_chamsoc").val();
		var noituvong_tv = $("#tthc_tv_noituvong").val();
		var nguoithuthap_tv = $("#tthc_tv_nguoithuthap").val();
		var ma_benh_ly = $("#tthc_tv_mabenhly").val();
		var cochong_tv = $("#tthc_tv_cochong").val();
		var tuoithai_tv = $("#tthc_tv_tuoithai").val();
		var tuoithaikhichet_tv = $("#tthc_tv_tuoithaikc").val();
		var tuvongme_tv = $("#tthc_tv_tuvongme").val();
		var ghichu_tv = $("#tthc_tv_ghichu").val();
		var icd = $("#tthc_tv_icdtuvong").val();
		var giaybaotu = $("#tthc_tv_duoccapgiaybaotu").prop('checked')==true?"1":"0";
		var khamdieutri_truoctv = $("#tthc_tv_dakhamdieutri").prop('checked')==true?"1":"0";
		var arr_giophut = $("#tthc_tv_thoigiantuvong").val().split(" ")[1].split(":");
		var gio_tv = arr_giophut[0];
		var phut_tv = arr_giophut[1];
		var nguoibaotu_tv = $("#tthc_tv_nguoibaotu").val();
		var namsinhnbt_tv = $("#tthc_tv_namsinhnbt").val();
		var quanhenbt_tv = $("#tthc_tv_quanhenguoichet").val();
		var magiaybaotu = giaybaotu == 1? "": $("#tthc_tv_magiaybaotu").val();
		var noicapgiaybaotu = giaybaotu == 1? "": $("#tthc_tv_noigiaybaotu").val();
		var ngaycapgiaybaotu = giaybaotu == 1? "": $("#tthc_tv_ngaycapgiaybaotu").val();
		var ttchuatri_tv = $("#tthc_tv_ttchuatritruoctv").val();

		var str = [idnhankhau, ngaytuvong_tv, ngayghinhan_tv, chitietnoituvong_tv, chitietnguyennhan_tv, chamsoc_tv, nguoithuthap_tv,
			nguyennhan_tv, noituvong_tv, ma_benh_ly, cochong_tv, tuoithai_tv, tuoithaikhichet_tv, ghichu_tv, tuvongme_tv, "0", "0", "0",
			mabenhnhan, icd, sovaovien, giaybaotu, khamdieutri_truoctv,gio_tv,phut_tv,nguoibaotu_tv,namsinhnbt_tv,quanhenbt_tv,"","0",
			magiaybaotu,noicapgiaybaotu,ngaycapgiaybaotu,ttchuatri_tv,0];
		var url = "suatv";
		$.post(url, {url : convertArray(str)}).done(function(data) {
			if (data != "-1") {
				$.ajax({
					url: 'cmu_post_cmu_uptttv_ngoaivien',
					method: 'post',
					dataType: 'json',
					data: {
						url: [singletonObject.dvtt, mabenhnhan, sovaovien,
							$("#tthc_tv_tuvongngoaivien").prop("checked")? 1: 0,
							singletonObject.makhoa
						].join('```')
					},
				})
				if(giaybaotu == 1) {
					luuThongtinkyso(callback)
				}
				notifiToClient("Green", "Cập nhật thành công");
				if (data != "1") {
					var dataJSON = convertDataFormToJson("xuatvienFormtuvong")
					var stringLog = []
					Object.keys(keyLuuLogGiayBaoTu).forEach(function(key) {
						stringLog.push(getLabelValueGiayBaoTu(key, dataJSON))
					})
					luuLogHSBATheoBN({
						SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
						LOAI: LOGHSBALOAI.GIAYBAOTU.KEY,
						NOIDUNGBANDAU: "",
						NOIDUNGMOI: stringLog.join(";"),
						USERID: singletonObject.userId,
						ACTION: LOGHSBAACTION.INSERT.KEY,
					})
				}
				else {
					notifiToClient("Green", "Thêm thành công");
					var dataJSON = convertDataFormToJson("xuatvienFormtuvong")
					var Logbandau = []
					var Logmoi = []
					var diffObject = findDifferencesBetweenObjects(oldDataGiayBaoTu, dataJSON);
					for (let key in diffObject) {
						if (keyLuuLogGiayBaoTu.hasOwnProperty(key)) {
							Logbandau.push(getLabelValueGiayBaoTu(key, oldDataGiayBaoTu))
							Logmoi.push(getLabelValueGiayBaoTu(key, dataJSON))
						}
					}
					if (Logbandau.length != 0 || Logmoi.length != 0) {
						luuLogHSBATheoBN({
							SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
							LOAI: LOGHSBALOAI.GIAYBAOTU.KEY,
							NOIDUNGBANDAU: Logbandau.join(";"),
							NOIDUNGMOI: Logmoi.join(";"),
							USERID: singletonObject.userId,
							ACTION: LOGHSBAACTION.EDIT.KEY,
						})
					}
				}
			} else {
				notifiToClient("Red", "Lỗi cập nhật tử vong");
			}
		}).fail(function() {
			notifiToClient("Red", "Lỗi cập nhật tử vong");
		}).always(function(){
			hideSelfLoading(idButton)
		});
	}

	function luuThongtinkyso(callback) {
		$.ajax({
			url: 'cmu_post_cmu_uptttv_gbt',
			method: 'post',
			dataType: 'json',
			data: {
				url: [
					singletonObject.dvtt,
					thongtinhsba.thongtinbn.MA_BENH_NHAN,
					thongtinhsba.thongtinbn.SOVAOVIEN,
					$("#tthc_tv_diachihientai").val(),
					$("#tthc_tv_diachithuongtru").val(),
					$("#tthc_tv_matinhthuongtru").val(),
					$("#tthc_tv_mahuyenthuongtru").val(),
					$("#tthc_tv_maxathuongtru").val(),
					$("#tthc_tv_matinhhientai").val(),
					$("#tthc_tv_mahuyenhientai").val(),
					$("#tthc_tv_maxahientai").val(),
					$("#tthc_tv_loaigiayto").val(),
					$("#tthc_tv_sogiayto").val(),
					$("#tthc_tv_ngaycap").val(),
					$("#tthc_tv_noicap").val(),
					$("#tthc_tv_sgiaychungtu").val(),
					$("#tthc_tv_quyengiaychungtu").val(),
					singletonObject.makhoa,
					$("#cmu_tuvongngoaivien").prop("checked")? 1: 0

				].join('```')
			},
			success: function() {
				if(typeof callback == "function") {
					callback()

				}
			},
			error: function() {
			}
		})
	}
	function guikysochungtu() {
		if(singletonObject.thamSo960601 == 1) {
			showSelfLoading("tthc_tv_guikyso");
			$.get("cmu_getlist?url=" + convertArray([singletonObject.dvtt,
				thongtinhsba.thongtinbn.MA_BENH_NHAN
				, thongtinhsba.thongtinbn.SOVAOVIEN, 'CMU_GETTHONGTIN_GBT']))
				.done(function (data) {
					if (data.length > 0) {
						data[0].DVTT = singletonObject.dvtt;
						data[0].NGUOIGUI = singletonObject.userId + "-" + singletonObject.user;
						var giaycs = "00000" + data[0].MA_GBT;
						giaycs = giaycs.substring(giaycs.length - 5, giaycs.length);
						data[0].MA_GBT = giaycs + ".GBT." + singletonObject.dvtt + "." + data[0].NGAY_CAPGIAYBT.substring(2, 4);
						var xmlData = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>"
							+ "<HSDLGBT>" +
							"<GIAYBAOTU Id=\"" + data[0].SOVAOVIEN + "\">" +
							"<MA_GBT>" + data[0].MA_GBT + "</MA_GBT>" +
							"<MA_BN>" + data[0].MA_BN + "</MA_BN>" +
							"<MA_HSBA>" + data[0].MA_HSBA + "</MA_HSBA>" +
							"<HO_TEN>" + data[0].HO_TEN + "</HO_TEN>" +
							"<NGAY_SINH>" + data[0].NGAY_SINH + "</NGAY_SINH>" +
							"<GIOI_TINH>" + data[0].GIOI_TINH + "</GIOI_TINH>" +
							"<MA_THE>" + data[0].MA_THE + "</MA_THE>" +
							"<MA_DANTOC>" + data[0].MA_DANTOC + "</MA_DANTOC>" +
							"<MA_QUOCTICH>" + data[0].MA_QUOCTICH + "</MA_QUOCTICH>" +
							"<DCHI_THUONGTRU>" + data[0].DCHI_THUONGTRU + "</DCHI_THUONGTRU>" +
							"<MATINH_THUONGTRU>" + data[0].MATINH_THUONGTRU + "</MATINH_THUONGTRU>" +
							"<MAHUYEN_THUONGTRU>" + data[0].MAHUYEN_THUONGTRU + "</MAHUYEN_THUONGTRU>" +
							"<MAXA_THUONGTRU>" + data[0].MAXA_THUONGTRU + "</MAXA_THUONGTRU>" +
							"<DCHI_HIENTAI>" + data[0].DCHI_HIENTAI + "</DCHI_HIENTAI>" +
							"<MATINH_HIENTAI>" + data[0].MATINH_HIENTAI + "</MATINH_HIENTAI>" +
							"<MAHUYEN_HIENTAI>" + data[0].MAHUYEN_HIENTAI + "</MAHUYEN_HIENTAI>" +
							"<MAXA_HIENTAI>" + data[0].MAXA_HIENTAI + "</MAXA_HIENTAI>" +
							"<LOAI_GIAYTO>" + data[0].LOAI_GIAYTO + "</LOAI_GIAYTO>" +
							"<SO_GIAYTO>" + data[0].SO_GIAYTO + "</SO_GIAYTO>" +
							"<NGAY_CAP>" + data[0].NGAY_CAP + "</NGAY_CAP>" +
							"<NOI_CAP>" + data[0].NOI_CAP + "</NOI_CAP>" +
							"<NGAYGIO_VV>" + data[0].NGAYGIO_VV + "</NGAYGIO_VV>" +
							"<NGAY_TV>" + data[0].NGAY_TV + "</NGAY_TV>" +
							"<TINH_TRANG_TV>" + data[0].TINH_TRANG_TV + "</TINH_TRANG_TV>" +
							"<NGUYENNHAN_TV>" + data[0].NGUYENNHAN_TV + "</NGUYENNHAN_TV>" +
							"<NGUOI_GHIGIAY></NGUOI_GHIGIAY>" +
							"<NGUOI_THANTHICH>" + data[0].NGUOI_THANTHICH + "</NGUOI_THANTHICH>" +
							"<TTRUONG_DVI></TTRUONG_DVI>" +
							"<SO_BAOTU>" + data[0].SO_BAOTU + "</SO_BAOTU>" +
							"<QUYEN_SO>" + data[0].QUYEN_SO + "</QUYEN_SO>" +
							"<NGAY_CAPGIAYBT>" + data[0].NGAY_CAPGIAYBT + "</NGAY_CAPGIAYBT>" +
							"<SO_BAOTU_BD>" + (data[0].SO_BAOTU_BD == null ? "" : data[0].SO_BAOTU_BD) + "</SO_BAOTU_BD>" +
							"<QUYEN_SO_BD>" + (data[0].QUYEN_SO_BD == null ? "" : data[0].QUYEN_SO_BD) + "</QUYEN_SO_BD>" +
							"<MACSKCB>" + data[0].MACSKCB + "</MACSKCB>" +
							"<DIACHI_CSKCB>" + data[0].DIACHI_CSKCB + "</DIACHI_CSKCB>" +
							"</GIAYBAOTU>" +
							"</HSDLGBT>"
						$.post("cmu_post_CMU_GIAY_CHUNG_TU_INS", {
							url: [
								singletonObject.dvtt,
								thongtinhsba.thongtinbn.SOVAOVIEN,
								singletonObject.makhoa,
								data[0].ID_TV_THONG_TIN,
								$("#tthc_tv_thoigiantuvong").val(),
								xmlData
							].join("```")
						}).done(function (res) {
							if (res == "-1") {
								return notifiToClient("Red", "Đã ký số")
							}
							if (res == "1") {
								notifiToClient("Green", "Gửi thành công")
							} else {
								notifiToClient("Red", "Gửi thất bại")
							}
						}).fail(function () {
							notifiToClient("Red", "Gửi thất bại")
						})
					} else {
						notifiToClient("Red", "Không tìm thấy dữ liệu")
					}
				}).always(function () {
				hideSelfLoading("tthc_tv_guikyso");
			})
			return false;
		}
		var x = new XMLHttpRequest();
		showSelfLoading("tthc_tv_guikyso");
		x.onload = function() {
			// Create a form
			var reader = new FileReader();
			reader.readAsDataURL(x.response);
			reader.onloadend = function() {
				var base64data = reader.result;
				var fd = new FormData();
				$.get("cmu_getlist?url="+convertArray([singletonObject.dvtt,
					thongtinhsba.thongtinbn.MA_BENH_NHAN
					, thongtinhsba.thongtinbn.SOVAOVIEN,'CMU_GETTHONGTIN_GBT']))
					.done(function(data){
						if(data.length > 0) {

							data[0].FILE = base64data.replace("data:application/pdf;base64,", "");
							data[0].DVTT = singletonObject.dvtt;
							data[0].NGUOIGUI = singletonObject.userId + "-"+singletonObject.user;
							var giaycs = "00000"+data[0].MA_GBT;
							giaycs = giaycs.substring(giaycs.length - 5, giaycs.length);
							data[0].MA_GBT = giaycs+".GBT."+singletonObject.dvtt+"."+data[0].NGAY_CAPGIAYBT.substring(2,4);
							$.ajax({
								type: "POST",
								url: "https://apikysohis.vnptcamau.vn/gui-giay-chung-tu",
								contentType: 'application/json',
								dataType: "json",
								data: JSON.stringify(data[0]),
								success: function(res) {
									notifiToClient("Green",res.message)
								},
								error: function() {
									notifiToClient("Red","Gửi thất bại")
								}
							})
						} else {
							notifiToClient("Red","Không tìm thấy dữ liệu")
						}
					}).always(function() {
					hideSelfLoading("tthc_tv_guikyso");
				})

			}

		};
		var arr = [thongtinhsba.thongtinbn.IDNHANKHAU, thongtinhsba.thongtinbn.MA_BENH_NHAN, 1, "0"];
		var url = "ingiaybaotu_nk?url=" + convertArray(arr);
		x.responseType = 'blob';    // <-- This is necessary!
		x.open('GET', url, true);
		x.send();
	}

	function laySogiaychungtu(maquyen) {
		$.ajax({
			url: 'cmu_post_QCT_GET_SO_BAOTU_BY_QUYEN',
			method: 'post',
			dataType: 'json',
			data: {
				url: [singletonObject.dvtt, maquyen].join('```')
			},
			success: function(data) {
				$("#tthc_tv_sgiaychungtu").val(data);
			},
			error: function() {
			}
		})
	}

	function luuTTChuyentuyen(idButton) {
		var result = validateDulieuChuyenTuyen(true);
		if(result === false) {
			hideSelfLoading(idButton)
			return false;
		}
		if(result !== true) {
			return $.confirm({
				title: 'Xác nhận!',
				type: 'orange',
				content: result,
				buttons: {
					warning: {
						btnClass: 'btn-warning',
						text: "Tiếp tục",
						action: function(){
							luuXemtruocChuyentuyen(idButton);
						}
					},
					cancel: function () {
						hideSelfLoading(idButton)
					}
				}
			});
		}
		luuXemtruocChuyentuyen(idButton);
	}
	function loadTrieuchung() {
		$.ajax({
			url: "cmu_list_NOT_TRIEUCHUNG_F?url=" + convertArray([singletonObject.dvtt]),
			type:"GET",
		}).done(function (data) {
			if (data) {
				$("#tthc_xv_trieuchung_sg").empty();
				$.each(data, function (i) {
					$("<option>" + data[i].TRIEUCHUNG + "</option>").appendTo("#tthc_xv_trieuchung_sg");
				});
				$("#trieuChungGrid").jqGrid('setGridParam', { datatype: 'local', data: data }).trigger("reloadGrid");
			}
		});
	}
	function loadPhuongphapdieutri() {
		var url = "noitru_phuongphapdieutri";
		$.post(url).done(function (data) {
			if (data) {
				$("#tthc_xv_ppdieutri_sg").empty();
				$.each(data, function (i) {
					$("<option>" + data[i].PHUONGPHAP_DIEUTRI + "</option>").appendTo("#tthc_xv_ppdieutri_sg");
				});
				$("#ppDieuTriGrid").jqGrid('setGridParam', { datatype: 'local', data: data }).trigger("reloadGrid");
			}
		});
	}

	function luuXemtruocChuyentuyen(idButton) {
		var dvtt = singletonObject.dvtt;
		var phongchuyenvien = singletonObject.maphongbenh;
		var nhanvienchuyenvien = $("#tthc_ct_bacsi").val();
		var ngaycv = $("#tthc_ct_ngayravien").val().split(" ")
		var ngayxuly = convertStr_MysqlDate(ngaycv[0]) + " " + ngaycv[1]+":00";
		var ngaychuyenvien = convertStr_MysqlDate(ngaycv[0]) + " " +  ngaycv[1]+":00";
		var mabenhvienchuyendi = $("#tthc_ct_benhvien").val();
		var tenbenhvienchuyendi = $("#tthc_ct_benhvien option:selected").text();
		var tuyenbenhvien = $("#tthc_ct_tuyenbenhvien").val();
		var noilamviec = $("#tthc_ct_noilamviec").val();
		var dauhieulamsang = $("#tthc_ct_dauhieulamsang").val();
		var ketquaxetnghiem_cls = $("#tthc_ct_kqxetnghiemcls").val();
		var mabenhchinh = $("#tthc_ct_mabenhly").val();
		var icdbenhchinh = $("#tthc_ct_icdravien").val();
		var tenbenhchinh = $("#tthc_ct_tenicdravien").val();
		var benhkemtheo = $("#tthc_ct_tenicdphuravien").val();
		benhkemtheo = benhkemtheo.replaceAll("+", "=");
		var pp_ttpt_thuocdadung = $("#tthc_ct_ppktdadt").val();
		var tinhtrangbenhnhan = $("#tthc_ct_tinhtrangbn").val();
		var lydochuyentuyen = $("#tthc_ct_lydo").val();
		var sochuyentyt = $("#tthc_ct_sochuyentyt").val();
		var tentyt = $("#tthc_ct_tentyt").val().trim();
		var tuyentyt = 4;
		var tungaytyt = $("#tthc_ct_tyt_tungay").val();
		var denngaytyt = $("#tthc_ct_tyt_denngay").val();
		var sochuyenpkdk = $("#tthc_ct_sochuyenpkdk").val();
		var tenpkdk = $("#tthc_ct_tenpkdk").val();
		var tuyenpkdk = 3;
		var tungaypkdk = $("#tthc_ct_pkdk_tungay").val();
		var denngaypkdk = $("#tthc_ct_pkdk_denngay").val();
		var sochuyenttyt = $("#tthc_ct_sochuyenttyt").val();
		var tenttyt = $("#tthc_ct_tenttyt").val();
		var tuyenttyt = 3;
		var tungayttyt = $("#tthc_ct_ttyt_tungay").val();
		var denngayttyt = $("#tthc_ct_ttyt_denngay").val();
		var huongdieutri = $("#tthc_ct_huongdt").val();
		var loidan_bs = $("#tthc_ct_loidanbs").val();
		var hotennguoiduadi = $("#tthc_ct_hotennguoiduadi").val();
		var benhnhanduoc_vc = "false";
		var mauchuyenvien = "1";
		var bhyt = thongtinhsba.thongtinbn.SOBAOHIEMYTE;
		var ketquadieutri = $("#tthc_ct_ketquadt").val();
		var soluotchuyen = $("#tthc_ct_soluotchuyen").val();

		var sochuyenl2 = $("#tthc_ct_sochuyendkt").val();
		var tenl2 = $("#tthc_ct_tendkt").val();
		var tuyenl2 = 2;
		//var tuyenl2=2;
		var tungayl2 = $("#tthc_ct_dkt_tungay").val();
		var denngayl2 = $("#tthc_ct_dkt_denngay").val();
		var ghichu = $("#tthc_ct_ghichu").val();
		var madt = "";
		if (!bhyt) {
			madt = "";
		} else {
			madt = bhyt.substring(0, 3);
		}

		var phuongtien = $("#tthc_ct_phuontienvc").val().trim();
		var phi_chuyenvien = "0";
		var tt_phichuyenvien = "1";
		var lenhdieuxe = "";
		var so_km_chuyen = "0";
		var gia_xang = "0";
		if (phuongtien == "tự túc") {
			phi_chuyenvien = "0";
			tt_phichuyenvien = "1";
			lenhdieuxe = "";
			so_km_chuyen = "0";
			gia_xang = "0";
		}  else {
			phi_chuyenvien = $("#tthc_ct_tienvanchuyen").val();
			tt_phichuyenvien = $("#tthc_ct_loaihinhvanchuyen").val();
			lenhdieuxe = $("#tthc_ct_lenhdieuxe").val();
			so_km_chuyen = $("#tthc_ct_so_km_donvichuyen").val();
			gia_xang = $("#tthc_ct_giaxang").val();
		}
		var tttinhtiengiuong = $("#tthc_ct_tinhtiencongkham").prop("checked") == true ? "1" : "0";
		var chuyendoi_ck = $("#tthc_ct_chuyencungnguoikhac").prop('checked');
		if (chuyendoi_ck == true)
			chuyendoi_ck = 1;
		else
			chuyendoi_ck = 0;
		var dataBN = thongtinhsba.thongtinbn;
		var arr = [
			dataBN.STT_DOTDIEUTRI,
			dataBN.STT_BENHAN, dvtt, phongchuyenvien,
			nhanvienchuyenvien, ngayxuly, ngaychuyenvien, mabenhvienchuyendi, tenbenhvienchuyendi,
			tuyenbenhvien, dauhieulamsang, ketquaxetnghiem_cls, mabenhchinh, icdbenhchinh, tenbenhchinh, benhkemtheo, pp_ttpt_thuocdadung, tinhtrangbenhnhan, lydochuyentuyen,
			sochuyentyt, tuyentyt, tungaytyt, denngaytyt, sochuyenpkdk, tuyenpkdk, tungaypkdk, denngaypkdk, sochuyenttyt, tuyenttyt, tungayttyt,
			denngayttyt, huongdieutri, phuongtien, hotennguoiduadi, benhnhanduoc_vc, phi_chuyenvien, tt_phichuyenvien, mauchuyenvien, lenhdieuxe, tentyt, tenpkdk,
			tenttyt,  dataBN.STT_LOGKHOAPHONG,
			dataBN.STT_LOGGIUONGBENH, singletonObject.makhoa,
			madt, dataBN.MA_BENH_NHAN, tttinhtiengiuong, ketquadieutri,
			dataBN.SOVAOVIEN, dataBN.SOVAOVIEN_DT,
			soluotchuyen, chuyendoi_ck, $("#tthc_ct_manguoidicung").val(), $("#tthc_ct_tennguoidicung").val(),
			so_km_chuyen, gia_xang, noilamviec, sochuyenl2, tuyenl2, tungayl2, denngayl2, tenl2, loidan_bs,
			$("#tthc_ct_quatrinhbenhly").val().trim(),
			'noitru_luu_chuyentuyen'];

		var url = "cmu_post";
		$.post(url, {url: arr.join('```')}).done(function (data) {
			noitruTaoBangke(dataBN, function() {
			})
			$.luuSongaysauphauthuatPage1($("#tthc_ct_ngayravien").val()).always(function() {
				loadThongTinVBATrang1();
				luuThongTinVBATrang1();
			})
			notifiToClient("Green","Lưu huyển tuyến thành công");

			var dataJSON = convertDataFormToJson("contentFormchuyentuyen")
			var combinedJson = { ...oldDataGiayChuyenTuyen, ...oldData };
			var Logbandau = []
			var Logmoi = []
			var diffObject = findDifferencesBetweenObjects(combinedJson, dataJSON);
			for (let key in diffObject) {
				if (keyLuuLogGiayChuyenTuyen.hasOwnProperty(key)) {
					Logbandau.push(getLabelValueGiayChuyenTuyen(key, combinedJson))
					Logmoi.push(getLabelValueGiayChuyenTuyen(key, dataJSON))
				}
			}
			luuLogHSBATheoBN({
				SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
				LOAI: LOGHSBALOAI.TTCHUYENTUYEN.KEY,
				NOIDUNGBANDAU: Logbandau.join(";"),
				NOIDUNGMOI: Logmoi.join(";"),
				USERID: singletonObject.userId,
				ACTION: LOGHSBAACTION.EDIT.KEY,
			})

		}).fail(function () {
			notifiToClient("Red", "Lỗi lưu chuyển tuyến, vui lòng thử lại sau")
		}).always(function () {
			hideSelfLoading(idButton)
		});

	}

	function luusoHentaikham(idButton) {
		var songayhen = $("#tthc_xv_songayhentaikham").val();
		var ngayhen = $("#tthc_xv_ngayhentaikham").val();
		var soluutru = $("#tthc_xv_soluutru_hentaikham").val();
		var arr = [thongtinhsba.thongtinbn.MA_BENH_NHAN, thongtinhsba.thongtinbn.SOVAOVIEN,
			thongtinhsba.thongtinbn.SOVAOVIEN_DT, singletonObject.dvtt, songayhen, ngayhen, soluutru, 0];
		var url = "noitru_xuatvien_luuthongtinsohenkham?url=" + convertArray(arr);
		$.post(url).done(function (data) {
			if (data == "1") {
				notifiToClient("Green", MESSAGEAJAX.EDIT_SUCCESS)
			} else {
				notifiToClient("Red", "Số lưu trữ đã tồn tại")
			}
		}).fail(function() {
			notifiToClient("Red",MESSAGEAJAX.FAIL);
		}).always(function() {
			hideSelfLoading(idButton);

		});
	}

	function getLabelValueTTXV(key, data) {
		var value = "";
		switch (key) {
			case "TEN_BAC_SI_TRUONGKHOA":
				value = $("#tthc_xv_truongkhoa").find(':selected').text()
				break;
			case "TEN_BAC_SI_TRUONGDONVI":
				value = $("#tthc_xv_giamdoc").find(':selected').text()
				break;
			case "TENKETQUADIEUTRI":
				value = $("#tthc_xv_ketquadt").find(':selected').text()
				break;
			case "TENTINHTRANG_RV":
				value = $("#tthc_xv_hinhthucxv").find(':selected').text()
				break;
			default:
				value = _.get(data, key, "");
				break;
		}
		return keyLuuLogTTXV[key] + ": " +value;
	}

	function getLabelValueGiayChuyenTuyen(key, data){
		var value = "";
		switch (key){
			case "TENBENHVIEN_CHUYENDI":
				value = $("#tthc_ct_benhvien").find(':selected').text()
				break;
			case "TUYEN":
				value = $("#tthc_ct_tuyenbenhvien").find(':selected').text()
				break;
			case "KETQUA_DIEUTRI":
				value = $("#tthc_ct_ketquadt").find(':selected').text()
				break;
			case "LYDOCHUYENTUYEN":
				value = $("#tthc_ct_lydo").find(':selected').text()
				break;
			case "PHUONGTIENVANCHUYEN":
				value = $("#tthc_ct_phuontienvc").find(':selected').text()
				break;
			case "SO_LUOT_CHUYEN":
				value = $("#tthc_ct_soluotchuyen").find(':selected').text()
				break;
			case "MA_XANG_DAU":
				value = $("#tthc_ct_loaixangdau").find(':selected').text()
				break;
			case "LOAI_HINH":
				value = $("#tthc_ct_loaihinhvanchuyen").find(':selected').text()
				break;
			case "BS":
				value = $("#tthc_ct_bacsi").find(':selected').text()
				break;
			case "TRUONG_KHOA":
				value = $("#tthc_ct_truongkhoa").find(':selected').text()
				break;
			case "TRUONG_DON_VI":
				value = $("#tthc_ct_giamdoc").find(':selected').text()
				break;
			default:
				value = _.get(data, key, "");
				break;
		}
		return keyLuuLogGiayChuyenTuyen[key] + ": " +value;
	}


	function getLabelValueGiayBaoTu(key, data) {
		var value = "";
		switch (key) {
			case "TEN_CHAM_SOC":
				value = $("#tthc_tv_chamsoc").find(':selected').text()
				break;
			case "TEN_QUANHE_NGUOIBAOTU":
				value = $("#tthc_tv_quanhenguoichet").find(':selected').text()
				break;
			case "TEN_ID_NGUYEN_NHAN_TV":
				value = $("#tthc_tv_nguyennhantv").find(':selected').text()
				break;
			case "TEN_ID_NOI_TV":
				value = $("#tthc_tv_noituvong").find(':selected').text()
				break;
			case "TEN_CO_CHONG":
				value = $("#tthc_tv_cochong").find(':selected').text()
				break;
			case "TEN_TUVONG_ME":
				value = $("#tthc_tv_tuvongme").find(':selected').text()
				break;
			case "TEN_MATINH_THUONGTRU":
				value = $("#tthc_tv_matinhthuongtru").find(':selected').text()
				break;
			case "TEN_MAHUYEN_THUONGTRU":
				value = $("#tthc_tv_mahuyenthuongtru").find(':selected').text()
				break;
			case "TEN_MAXA_THUONGTRU":
				value = $("#tthc_tv_maxathuongtru").find(':selected').text()
				break;
			case "TEN_MATINH_HIENTAI":
				value = $("#tthc_tv_matinhhientai").find(':selected').text()
				break;
			case "TEN_MAHUYEN_HIENTAI":
				value = $("#tthc_tv_mahuyenhientai").find(':selected').text()
				break;
			case "TEN_MAXA_HIENTAI":
				value = $("#tthc_tv_maxahientai").find(':selected').text()
				break;
			case "TEN_LOAI_GIAYTO":
				value = $("#tthc_tv_loaigiayto").find(':selected').text()
				break;
			case "TEN_MAQUYEN":
				value = $("#tthc_tv_quyengiaychungtu").find(':selected').text()
				break;
			default:
				value = _.get(data, key, "");
				break;
		}
		return keyLuuLogGiayBaoTu[key] + ": " +value;
	}

	function assignNonNullValues(target, source) {
		for (const key in source) {
			if (source[key]) {
				target[key] = source[key];
			} else {
				target[key] = "";
			}
		}
	}

	function guitruongkhoabgdkyso(idButton, loaigiay, ngayravien) {
		$.post("cmu_post_CMU_GRVCT_INS", {
			url: [
				singletonObject.dvtt,
				thongtinhsba.thongtinbn.SOVAOVIEN,
				singletonObject.makhoa,
				ngayravien,
				loaigiay
			].join("```")
		}).done(function(data) {
			if(data == -1) {
				return notifiToClient("Red", "Giấy ra viện được ký")
			}
			if(data > 0) {
				luuLogHSBATheoBN({
					SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
					LOAI: loaigiay =='GRV' ?LOGHSBALOAI.XUATVIEN.KEY : LOGHSBALOAI.CHUYENTUYEN.KEY,
					NOIDUNGBANDAU: "Gửi ký số",
					NOIDUNGMOI: "",
					USERID: singletonObject.userId,
					ACTION: LOGHSBAACTION.INSERT.KEY,
				})
				if(loaigiay =='GRV') {
					$("#tthc_xv_guikyso").hide()
					$("#tthc_xv_huyguikyso").show()
				}

				var topic = singletonObject.makhoa + (loaigiay =='GRV' ? "_TRUONGKHOA" : "_BGD");
				sendMessageTopicFirebase(topic, loaigiay =='GRV' ? "Giấy ra viện" : "Giấy chuyển tuyến", thongtinhsba.thongtinbn.TEN_BENH_NHAN)
				return notifiToClient("Green", "Gửi ký số thành công")
			}
			notifiToClient("Red", MESSAGEAJAX.ERROR)
		}).fail(function() {
			notifiToClient("Red", "Lỗi gửi ký số, vui lòng thử lại sau")
		}).always(function() {
			hideSelfLoading(idButton)
		})
	}

	function checkkysogiayhen(){
		var bien = false
		getFilesign769("PHIEU_NOITRU_GIAYHEN_BACSI", thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt,
			thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
				if (dataKySo.length > 0) {
					$("#giayhen_kysobs").hide()
					$("#giayhen_huykysobs").show()
					bien = true
				} else {
					$("#giayhen_kysobs").show()
					$("#giayhen_huykysobs").hide()
				}
			});
		getFilesign769("PHIEU_NOITRU_GIAYHEN_DAIDIEN", thongtinhsba.thongtinbn.SOVAOVIEN, -1, singletonObject.dvtt,
			thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
				if (dataKySo.length > 0) {
					$("#giayhen_kysodd").hide()
					$("#giayhen_huykysodd").show()
					bien = true
				} else {
					$("#giayhen_kysodd").show()
					$("#giayhen_huykysodd").hide()
				}
			});
		if (bien){
			$("#tthc_xv_gensoluutruhentaikham").hide();
			$("#tthc_xv_capnhatsoluutruhentaikham").hide();
			$("#tthc_xv_xoagiayhen").hide();
			$("#tthc_xv_songayhentaikham").prop("disabled", true);
			$("#tthc_xv_ngayhentaikham").prop("disabled", true);
			$("#tthc_xv_huykysogiayhen").show();
		} else {
			$("#tthc_xv_gensoluutruhentaikham").show();
			$("#tthc_xv_capnhatsoluutruhentaikham").show();
			$("#tthc_xv_xoagiayhen").show();
			$("#tthc_xv_huykysogiayhen").hide();
			$("#tthc_xv_songayhentaikham").prop("disabled", false);
			$("#tthc_xv_ngayhentaikham").prop("disabled", false);
		}
	}

	function checkkysogiayravien() {
		$.ajax({
			url: "cmu_getlist?url="+convertArray([
				singletonObject.dvtt,
				thongtinhsba.thongtinbn.SOVAOVIEN,
				thongtinhsba.thongtinbn.SOVAOVIEN_DT,
				thongtinhsba.thongtinbn.SOVAOVIEN,
				'cmu_smart769_getgrv']),
			method: "GET",
		}).done(function (data) {
			var rest = data[0]
			if(!rest['KEYMINIO']) {

				$("#tthc_xv_huykyso").hide()
				if(rest['DAGUI'] > 0) {
					$("#tthc_xv_guikyso").hide()
					$("#tthc_xv_huyguikyso").show()
				} else {
					$("#tthc_xv_guikyso").show()
					$("#tthc_xv_huyguikyso").hide()
				}

			} else {
				$("#tthc_xv_huykyso").show()
				$("#tthc_xv_guikyso").hide()
				$("#tthc_xv_huyguikyso").hide()
			}
		})
	}

	function checkkysogiaybaotu() {
		$.ajax({
			url: "cmu_getlist?url="+convertArray([
				singletonObject.dvtt,
				thongtinhsba.thongtinbn.SOVAOVIEN,
				thongtinhsba.thongtinbn.SOVAOVIEN_DT,
				thongtinhsba.thongtinbn.SOVAOVIEN,
				'cmu_smart769_getchungtu']),
			method: "GET",
		}).done(function (data) {
			var rest = data[0]
			if(!rest['KEYMINIO']) {

				$("#tthc_tv_huykyso").hide()
				$("#tthc_tv_luuthongtin").show();
				if(rest['DAGUI'] > 0) {
					$("#tthc_tv_guikyso").hide()
					$("#tthc_tv_huyguikyso").show()
				} else {
					$("#tthc_tv_guikyso").show()
					$("#tthc_tv_huyguikyso").hide()
				}

			} else {
				$("#tthc_tv_huykyso").show();
				$("#tthc_tv_luuthongtin").hide();
				$("#tthc_tv_guikyso").hide();
				$("#tthc_tv_huyguikyso").hide();
			}
		})

	}
	function checkkysogiaychuyentuyen() {
		$.ajax({
			url: "cmu_getlist?url="+convertArray([
				singletonObject.dvtt,
				thongtinhsba.thongtinbn.SOVAOVIEN,
				thongtinhsba.thongtinbn.SOVAOVIEN_DT,
				thongtinhsba.thongtinbn.SOVAOVIEN,
				'cmu_smart769_getchuyentuyen']),
			method: "GET",
		}).done(function (data) {
			if(data.length > 0) {
				$("#tthc_ct_huykyso").show()
				$("#tthc_ct_kyso").hide()
			} else {
				$("#tthc_ct_huykyso").hide()
				$("#tthc_ct_kyso").show()
			}
		})
	}

	async function layChanDoan(){
		try {
			var url = 'cmu_getlist?url=' + convertArray([
				thongtinhsba.thongtinbn.SOVAOVIEN,
				thongtinhsba.thongtinbn.STT_BENHAN,
				singletonObject.dvtt,
				"HSBA_CHANDOAN_CMU_SEL"]);
			var chanDoans = await $.ajax({
				url: url,
				method: "GET",
			})
			if(chanDoans==null || chanDoans.length ===0) return null;

			return chanDoans[0];
		} catch (e) {
			return null;
		}
	}

    async function loadChanDoans() {

		const chanDoan = await layChanDoan();
		if(chanDoan == null) return;

		getMotabenhly(chanDoan['ICD_DIEUTRI'], function (data) {
			const splitIcd = data.split("!!!")
		    $("#tthc_xv_tenicdravien").val(splitIcd[1])
		    $("#tthc_tv_mabenhly").val(splitIcd[0])
		    $("#tthc_xv_icdravien").val(chanDoan['ICD_DIEUTRI'].toUpperCase())
		});

		const benhPhus = chanDoan['TEN_BENHPHU'].split(';');
		benhPhus.forEach((benhPhu) => {
			const icd = benhPhu.split('-')[0].trim().toUpperCase();
		    getMotabenhly(icd, function (data) {
		        var splitIcd = data.split("!!!")
		        var stringIcd = $("#tthc_xv_tenicdphuravien").val();
		        if (!stringIcd.includes(splitIcd[1])) {
		            $("#tthc_xv_tenicdphuravien").val(stringIcd + "; (" + icd.toUpperCase() + ") " + splitIcd[1]);
		        }
		        $("#tthc_xv_icdphuravien").val("")
		    })
		});
    }
	function initGridYHCT(idGrid, idGridWrap) {
		var list = $("#"+idGrid)
		if(!list[0].grid) {
			list.jqGrid({
				url: '',
				datatype: "local",
				loadonce: true,
				height: 120,
				gridview: true,
				width: null,
				shrinkToFit: false,
				colModel: [
					{label: "ICD", name: 'ICD', index: 'ICD', width: 150, align: 'center'},
					{label: "TÊN ICD", name: 'TENICD', index: 'TENICD', width: 400},
					{label: "Ngày tạo", name: 'NGAY_TAO', index: 'NGAY_TAO',  width: 150},
					{label: "Người tạo", name: 'TEN_NHANVIEN_CD', index: 'TEN_NHANVIEN_CD',  width: 150},
					{label: "ID_DIEUTRI", name: 'ID_DIEUTRI', index: 'ID_DIEUTRI', width: 100, hidden: true},
					{name: 'NGUOITAO', index: 'NGUOITAO', width: 100, hidden: true},

				],
				caption: "Chẩn đoán YHCT",
				ignoreCase: true,
				rowNum: 1000000,
				onRightClickRow: function(id) {
					if (id) {
						$.contextMenu({
							selector: '#'+idGrid+' tr',
							reposition : false,
							callback: function (key, options) {
								var rowSelected = getThongtinRowSelected(idGrid)
								if(key == 'xoa') {
									confirmToClient('Bạn có chắc chắn muốn xóa ICD này?', function () {
										showLoaderIntoWrapId(idGridWrap);
										$.post("cmu_post", {
											url: [singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, rowSelected.ICD , "NOITRU_XV_ICDYHCT_DEL"].join("```")
										}).done(function () {
											luuLogHSBATheoBN({
												SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
												LOAI:  LOGHSBALOAI[idGrid == "tthc_xv_icdravien_yhct_list"? "XUATVIEN": "CHUYENTUYEN"].KEY,
												NOIDUNGMOI: "",
												NOIDUNGBANDAU:  "Xoá Chẩn đoán YHCT: "+ rowSelected.ICD + "-"+ rowSelected.TENICD,
												USERID: singletonObject.userId,
												ACTION: LOGHSBAACTION.DELETE.KEY,
											})
											loadGridYHCT(idGrid);
										}).always(function () {
											hideLoaderIntoWrapId(idGridWrap);
										}).fail(function () {
											notifiToClient("Red", "Xóa ICD thất bại.");
										})
									}, function () {

									})
								}
							},
							items: {
								"xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'}
							}
						});

					}

				},
			});
		}
	}
	function loadGridYHCT(idGrid) {
		var url = "cmu_getlist?url=" + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, "NOITRU_XV_ICDYHCT_SEL"]);
		$("#"+idGrid).jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
	}


	function getIcdYHCTList(idGrid) {
		var icdYHCT = [];
		getAllRowDataJqgrid(idGrid).forEach(function(item) {
			icdYHCT.push(item.ICD)
		})
		return icdYHCT.join(";");
	}

	function initGridTrieuChung() {
		let listData = $('#trieuChungGrid');
		if (!listData[0].grid) {
			listData.jqGrid({
				datatype: 'local',
				data: [],
				loadonce: true,
				height: 380,
				width: null,
				shrinkToFit: false,
				colModel: [
					{name: 'STT', label: "STT", width: 100},
					{name: 'TRIEUCHUNG', label: "Triệu chứng", width: 800},
				],
			});
			listData.jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: 'cn'});
		}
	}

	function initGridPPDieuTri() {
		let listData = $('#ppDieuTriGrid');
		if (!listData[0].grid) {
			listData.jqGrid({
				datatype: 'local',
				data: [],
				loadonce: true,
				height: 380,
				width: null,
				shrinkToFit: false,
				colModel: [
					{name: 'STT', label: "STT", width: 100},
					{name: 'PHUONGPHAP_DIEUTRI', label: "Triệu chứng", width: 800},
				],
			});
			listData.jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: 'cn'});
		}
	}

	// Bổ sung Mẫu chuẩn bị
	$("#tthc_xv_mauchuanbi").click(function() {
		let element = $("#mau_danhsachmaujson_wrap");
		element.attr("function-add", 'insertMauCBRaVien');
		element.attr("function-chinhsua", 'editMauCBRaVien');
		element.attr("function-select", 'selectMauCBRaVien');
		element.attr("function-getdata", 'getdataMauCBRaVien');
		element.attr("function-validate", 'formioCBRaVienValidate');
		element.attr("data-key", 'MAUCBRAVIEN');
		$("#modalMauChungJSON").modal("show");
		$.loadDanhSachMauChungJSON('MAUCBRAVIEN')
	}); $.extend({
		insertMauCBRaVien: function () {
			generateFormMauCBRaVien({});
		},
		editMauCBRaVien: function (rowSelect) {
			let json = JSON.parse(rowSelect.NOIDUNG);
			let dataMau = {}
			json.forEach(function(item) {
				dataMau[item.key] = item.value
			});
			generateFormMauCBRaVien({
				ID: rowSelect.ID,
				TENMAU: rowSelect.TENMAU,
				...dataMau
			});
		},
		selectMauCBRaVien: function (rowSelect) {
			let json = JSON.parse(rowSelect.NOIDUNG);
			json.forEach(function(item) {
				$('#contentFormxuatvien [name="' + item.key + '"]').val(item.value);
			});
			$("#modalMauChungJSON").modal("hide");
		},
		getdataMauCBRaVien: function () {
			let objectNoidung = [];
			getObjectMauCBRaVien().forEach(function(item) {
				console.log(item)
				if (item.key !== 'ID' && item.key !== 'TENMAU' &&
					item.label !== 'Columns' && item.label !== 'Titles'
				) {
					objectNoidung.push({
						"label": item.label,
						"value": formioMauHSBA.submission.data[item.key],
						"key": item.key,
					});
				}
				else if (item.label === 'Columns') {
					item.columns.forEach(function (v) {
						objectNoidung.push({
							"label": v.components[0].label,
							"value": formioMauHSBA.submission.data[v.components[0].key],
							"key": v.components[0].key,
						});
					});
				}
			})
			return {
				ID: formioMauHSBA.submission.data.ID,
				TENMAU: formioMauHSBA.submission.data.TENMAU,
				NOIDUNG: JSON.stringify(objectNoidung),
				KEYMAUCHUNG: 'MAUCBRAVIEN'
			};
		},
		formioCBRaVienValidate: function() {
			formioMauHSBA.emit("checkValidity");
			return formioMauHSBA.checkValidity(null, false, null, true);
		},
	});

	function generateFormMauCBRaVien(dataForm) {
		let jsonForm = getJSONObjectForm(getObjectMauCBRaVien());
		Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
			jsonForm, {}
		).then(function(form) {
			formioMauHSBA = form;
			formioMauHSBA.submission = { data: { ...dataForm }}

			$('#formChiTietMauChungJSON [name="data[ICD_BENHCHINH]"]').keypress(function(e) {
				if (e.keyCode === 13) {
					let mabenhICD = $(this).val();
					getMotabenhly(mabenhICD, function(data) {
						let splitIcd = data.split("!!!")
						$('#formChiTietMauChungJSON [name="data[TEN_BENHCHINH]"]').val(splitIcd[1]);
						$('#formChiTietMauChungJSON [name="data[ID_BENH]"]').val(splitIcd[0]);
						$('#formChiTietMauChungJSON [name="data[ICD_BENHCHINH]"]').val(mabenhICD.toUpperCase());
						formioMauHSBA.data.TEN_BENHCHINH = splitIcd[1];
						formioMauHSBA.data.ID_BENH = splitIcd[0];
						formioMauHSBA.data.ICD_BENHCHINH = mabenhICD.toUpperCase();
					});
				}
			});

			combgridTenICD('formChiTietMauChungJSON [name="data[TEN_BENHCHINH]"]', function(item) {
				$('#formChiTietMauChungJSON [name="data[TEN_BENHCHINH]"]').val(item.MO_TA_BENH_LY);
				$('#formChiTietMauChungJSON [name="data[ID_BENH]"]').val(item.MA_BENH_LY);
				$('#formChiTietMauChungJSON [name="data[ICD_BENHCHINH]"]').val(item.ICD.toUpperCase());
				formioMauHSBA.data.TEN_BENHCHINH = item.MO_TA_BENH_LY;
				formioMauHSBA.data.ID_BENH = item.MA_BENH_LY;
				formioMauHSBA.data.ICD_BENHCHINH = item.ICD.toUpperCase();
			});

			$('#formChiTietMauChungJSON [name="data[ICD_BENHKEMTHEO]"]').keypress(function(e) {
				if (e.keyCode === 13) {
					let mabenhICD = $(this).val();
					getMotabenhly(mabenhICD, function(data) {
						let splitIcd = data.split("!!!")
						let idBenhKT = $('#formChiTietMauChungJSON [name="data[BENHKEMTHEO]"]');
						let stringIcd = idBenhKT.val();
						if(!stringIcd.includes(splitIcd[1])) {
							let inData = stringIcd + "; (" +mabenhICD.toUpperCase() + ") " + splitIcd[1];
							idBenhKT.val(inData);
							formioMauHSBA.data.BENHKEMTHEO = inData;
						}
						$('#formChiTietMauChungJSON [name="data[ICD_BENHKEMTHEO]"]').val("");
						formioMauHSBA.data.ICD_BENHKEMTHEO = "";
					});
				}
			});
		});
	}

	function getObjectMauCBRaVien() {
		return [
			{
				"label": "ID",
				"key": "ID",
				"type": "textfield",
				others: {
					hidden: true
				}
			},
			{
				"label": "ID_BENH",
				"key": "ID_BENH",
				"type": "textfield",
				others: {
					hidden: true
				}
			},
			{
				"label": "Tên mẫu",
				"key": "TENMAU",
				"type": "textarea",
				validate: {
					required: true
				},
				others: {
					"labelPosition": "left-left",
					"labelWidth": 10
				}
			},
			{
				"label": "Titles",
				"type": "htmlelement",
				"tag": "p",
				"content": "<b>THÔNG TIN MẪU</b>",
				"input": false
			},
			{
				"label": "Columns",
				"columns": [
					{
						"components": [
							{
								"label": "ICD Bệnh ra viện",
								"customClass": "mr-2",
								"key": "ICD_BENHCHINH",
								"type": "textfield",
							}
						],
						"width": 2, "size": "md", "currentWidth": 2
					},
					{
						"components": [
							{
								"label": "Tên Bệnh ra viện",
								"customClass": "mr-2",
								"key": "TEN_BENHCHINH",
								"type": "textfield",
							}
						],
						"width": 4, "size": "md", "currentWidth": 4
					},
					{
						"components": [
							{
								"label": "ICD Bệnh phụ ra viện",
								"customClass": "mr-2",
								"key": "ICD_BENHKEMTHEO",
								"type": "textfield",
							}
						],
						"width": 2, "size": "md", "currentWidth": 2
					},
					{
						"components": [
							{
								"label": "Tên Bệnh phụ ra viện",
								"customClass": "",
								"key": "BENHKEMTHEO",
								"type": "textfield",
							}
						],
						"width": 4, "size": "md", "currentWidth": 4
					},
				],
				"key": "columns",
				"type": "columns",
				"customClass": "ml-0 mr-0"
			},
			{
				"label": "Columns",
				"columns": [
					{
						"components": [
							{
								"label": "Triệu chứng",
								"customClass": "mr-2",
								"key": "TRIEUCHUNG",
								"type": "textfield",
							}
						],
						"width": 6, "size": "md", "currentWidth": 6
					},
					{
						"components": [
							{
								"label": "Phương pháp điều trị",
								"customClass": "",
								"key": "PP_DIEUTRI",
								"type": "textfield",
							}
						],
						"width": 6, "size": "md", "currentWidth": 6
					},
				],
				"key": "columns",
				"type": "columns",
				"customClass": "ml-0 mr-0"
			},
		];
	}
})