var todieutriObject = {}
function themtodieutri() {
	var data = thongtinhsba.thongtinbn;
	var url = "noitru_kt_controngkhoa";
	if (!data || !data.MA_BENH_NHAN) {
		return notifiToClient("Red", "<PERSON>ui lòng chọn bệnh nhân cần khám bệnh");
	}
	if($("#bacsidieutri").val() == "" || $("#idbacsidieutri").val() == "") {
		return notifiToClient("Red", "Vui lòng chọn bác sĩ khám bệnh nội trú");
	}
	if (data.SOBENHAN == "" && data.SOBENHAN_TT == "") {
		return notifiToClient("Red", "Vui lòng tạo bệnh án hoặc bệnh án tạm thời trước khám bệnh");
	}
	if(!isValidDateTime($("#tdt_ngaygiopdt_date").val())) {
		return notifiToClient("Red", "Th<PERSON><PERSON> gian không đúng, vui chọn lại");
	}
	resetObjectTodieutri();
	$("#tdt_dienbien").val("");
	$("#tdt_ylenh").val("");
	var idButton = "action_khambenh";
	showSelfLoading(idButton)
	$.post(url, {
		stt_benhan: data.STT_BENHAN,
		dvtt: singletonObject.dvtt,
		maphongban: singletonObject.makhoa,
		stt_logkhoaphong: data.STT_LOGKHOAPHONG
	}).done(function (dt) {
		if (dt == "-1") {
			hideSelfLoading(idButton)
			return notifiToClient("Red","Bệnh nhân đã được xuất viện. Vui lòng kiểm tra lại");
		}
		if (dt == "-2") {
			hideSelfLoading(idButton)
			return notifiToClient("Red", "Bệnh nhân đã được chuyển khoa . Vui lòng kiểm tra lại");
		}
		if(moment($("#tdt_ngaygiopdt_date").val(), ['DD/MM/YYYY HH:mm']).isBefore(moment(thongtinhsba.thongtinbn.THOIGIANKHAMBENH, ['DD/MM/YYYY HH:mm']))) {
			notifiToClient("Red", `Ngày giờ lập phải  phải sau giờ khám ${thongtinhsba.thongtinbn.THOIGIANKHAMBENH}, vui lòng kiểm tra lại`);
			hideSelfLoading(idButton)
			return false;
		}

		var sosanhngaynv = $.ajax({
			url: "cmu_post",
			method: "POST",
			data: {
				url: [singletonObject.dvtt, data.SOVAOVIEN, data.SOVAOVIEN_DT, $("#tdt_ngaygiopdt_date").val(), 'CMU_CHECK_NGAYDTNHAPVIEN'].join("```")
			},
			async: false,
		}).responseText;
		if(sosanhngaynv == 1) {
			hideSelfLoading(idButton)
			return notifiToClient("Red", "Ngày điều trị không được nhỏ hơn ngày nhập viện.");
		}
		var unqtdtngaylap = $.ajax({
			url: "cmu_post",
			method: "POST",
			data: {
				url: [singletonObject.dvtt, data.SOVAOVIEN, $("#tdt_ngaygiopdt_date").val(), 'CMU_CHECK_UNQNGAYLAPTDT'].join("```")
			},
			async: false,
		}).responseText;
		if(unqtdtngaylap > 0) {
			hideSelfLoading(idButton)
			return notifiToClient("Red", "Ngày lập đã tồn tại");
		}
		var url = "cmu_post";
		$.post(url, {url:
				[
					singletonObject.dvtt,
					data.SOVAOVIEN,
					data.SOVAOVIEN_DT,
					data.MA_BENH_NHAN,
					$("#tdt_ngaygiopdt_date").val(),
					"CMU_NOITRU_THEMDT_KTDOTDT"
				].join("```")
		}).done(function (resphieu) {
			if (resphieu != "0") {
				var confirmModal = $.confirm({
					title: 'Xác nhận!',
					type: 'orange',
					content: "Bệnh nhân đã được lập " + resphieu + " phiếu trong ngày, bạn muốn tiếp tục lập phiếu",
					buttons: {
						warning: {
							btnClass: 'btn-warning',
							text: "Tiếp tục",
							action: function(){
								confirmModal.close();
								kham_taophieu(data);
							}
						},
						cancel: function () {
							hideSelfLoading(idButton)
						}
					}
				});
			} else {
				kham_taophieu(data);
			}
		});
	});
}

function kham_taophieu(dataBN) {

	var url = "noitru_themdieutribandau_new";
	var arr = ["", dataBN.STT_BENHAN, dataBN.STT_DOTDIEUTRI, singletonObject.dvtt, dataBN.MA_BENH_NHAN, "0",
		$("#bacsidieutri").val(),
		singletonObject.makhoa, $("#tenphongbenh").val(), " ", dataBN.STT_LOGKHOAPHONG,
		dataBN.SOVAOVIEN, dataBN.SOVAOVIEN_DT, singletonObject.bant? singletonObject.bant : 0];
	todieutriObject = {};
	$.post(url, {url: convertArray(arr)})
		.done(function (data) {
			$("#modalFormKhambenh").modal("hide");
			if(data.ERROR) {

				return notifiToClient("Red", "Lỗi tạo phiếu vui lòng thử lại sau");
			}
			todieutriObject = data;
			$("#footer_todieutri .add").show();
			$("#action_huykysotodieutri").hide();
			$("#ttdtFormthongtinsinhhieu .clear-text").val("")
			todieutriObject['NGAYGIO'] = $("#tdt_ngaygiopdt_date").val();
			todieutriObject['TENBSDIEUTRI'] = $("#bacsidieutri option:selected").text();

			showModalTodieutri(dataBN);
			todieutriObject['mode'] = 'insert';
			$.post("cmu_post", {url:
					[
						singletonObject.dvtt,
						dataBN.SOVAOVIEN,
						dataBN.SOVAOVIEN_DT,
						data.ID_DIEUTRI,
						$("#tdt_ngaygiopdt_date").val(),
						"CMU_UPDATE_TDT_NGAYLAP"
					].join("```")
			})
			luuLogHSBATheoBN({
				SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
				LOAI: LOGHSBALOAI.TODIEUTRI.KEY,
				NOIDUNGBANDAU: "",
				NOIDUNGMOI: "Số phiếu: " + todieutriObject.STT_DIEUTRI + "; Ngày giờ lập: " + todieutriObject.NGAYGIOLAPTDT + "; Bác sĩ điều trị: "+ todieutriObject['TENBSDIEUTRI'],
				USERID: singletonObject.userId,
				ACTION: LOGHSBAACTION.INSERT.KEY,
			})
			$.post("cmu_post", {url:
					[
						singletonObject.dvtt,
						data.ID_DIEUTRI,
						singletonObject.userId,
						"HSBA_UP_TDT_NGUOITAO"
					].join("```")
			})
			$.get("cmu_list_CMU_HSBA_GETDEFTDT?url="+convertArray([singletonObject.dvtt, todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT,
				todieutriObject.ID_DIEUTRI, singletonObject.userId])).done(function(dataDef) {
				if(dataDef.length > 0) {
					$("#tdt_inputcannang").val(dataDef[0].CANNANG)
					$("#tdt_inputchieucao").val(dataDef[0].CHIEUCAO)
					if(todieutriObject.STT_DIEUTRI == 1) {
						$("#tdt_inputmach").val(dataDef[0].MACH)
						$("#tdt_inputnhietdo").val(dataDef[0].NHIETDO)
						$("#tdt_inputnhiptho").val(dataDef[0].NHIPTHO)
						$("#tdt_inputhuyetap_tren").val(dataDef[0].HUYETAPTREN)
						$("#tdt_inputhuyetap_duoi").val(dataDef[0].HUYETAPDUOI)
					}
					tdtLoadIcd();

				}
			})
			$.post("cmu_post", {
				url: [
					dataBN.SOVAOVIEN,
					dataBN.SOVAOVIEN_DT,
					singletonObject.bant == 1? 2: 3,
					singletonObject.dvtt,
					"KIEM_TRA_GUI_XML0_CV130"
				].join("```")
			}).done(function(datacheck) {
				if(datacheck == 1) {
					noitruTaoBangke(thongtinhsba.thongtinbn, function() {
						const param = {
							loaiKcb: singletonObject.bant == 1? 2: 3,
							coBaoHiem: dataBN.COBHYT,
							soVaoVien: dataBN.SOVAOVIEN,
							soVaoVienDt: dataBN.SOVAOVIEN_DT
						};
						$.ajax({
							type: "POST",
							contentType: "application/json",
							dataType: "json",
							url: "api/dong-bo-check-in-130?guiTuDong=" + true,
							data: JSON.stringify(param),
						});
					})
				}

			})

		}).always(function() {
		hideSelfLoading("action_khambenh")
	});

}

function showModalTodieutri(dataBN) {
	$("#modalTodieutri").modal("show");
	hideSelfLoadingByClass("btn-loading");
	$("#titleTodieutri").html(dataBN.TEN_BENH_NHAN + " - Số bệnh án: " + dataBN.SOBENHAN
		+ " - Tờ điều trị số: "+ todieutriObject.STT_DIEUTRI + " - " + todieutriObject.NGAYGIO);
	$("#tdt_ngaygiopdt").val(todieutriObject.NGAYGIO);
	initGridThuocTdt();
	initGridXetnghiem();
	initGridCDHA();
	initGridTTPT();
	initGridICD();
	initToathuocnoitru();
	tdtloaddstoathuoc();
	tdtLoadIcd();
	initButton();
	tdtClearInputIcd();
	clearInputToathuoc();


	$("#tdt_cdha_hinhthuc").html("")
	$("#tdt_xn_hinhthuc").html("")
	$("#tdt_ttpt_hinhthuc").html("")
	if(dataBN.TYLEBAOHIEM > 0) {
		$("#tdt_cdha_hinhthuc").html("<option value='0'>BHYT</option>")
		$("#tdt_xn_hinhthuc").html("<option value='0'>BHYT</option>")
		$("#tdt_ttpt_hinhthuc").html("<option value='0'>BHYT</option>")
	}
	$("#tdt_cdha_hinhthuc").append("<option value='1'>Thu Phí</option>")
	$("#tdt_xn_hinhthuc").append("<option value='1'>Thu Phí</option>")
	$("#tdt_ttpt_hinhthuc").append("<option value='1'>Thu Phí</option>")
	loadKhothuocmacdinh()
	if(!thongtinhsba.thongtinbn.MAKHAMBENHNGOAITRU_NHAPVIEN || thongtinhsba.thongtinbn.MAKHAMBENHNGOAITRU_NHAPVIEN.length < 5) {
		$(".btn-dl-ngoaitru").hide()
	} else {
		$(".btn-dl-ngoaitru").show()
	}
}

function loadDSXetnghiem(sophieuXN, dataBN) {
	var arr = [$("#tdt_xn_hinhthuc").val() != 0, singletonObject.dvtt, sophieuXN, 0, $("#tdt_xn_phongxetnghiem").val(),
		todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT];
	var gioitinh = dataBN.GIOI_TINH == 0? "Nam": "Nữ"
	var url = "noitru_xetnghiem_hienthiluoi_svv?url=" + convertArray(arr) + "&phan_loai_gioi_tinh=" + gioitinh + "&nguonngoai=" + $("#tdt_xn_hinhthuc").val();
	var list = $("#tdt_list_xetnghiem");
	loadDataGridGroupBy(list, url);
}

function loadDSPhieuXetnghiem(dataBN) {
	var arr = [ singletonObject.dvtt, todieutriObject.ID_DIEUTRI,
		thongtinhsba.thongtinbn.STT_BENHAN, todieutriObject.STT_DOTDIEUTRI
	];
	var url = "cmu_list_CMU_HSBA_DSXN_TDT_F?url=" + convertArray(arr);
	var list = $("#tdt_list_phieuxetnghiem");
	loadDataGridGroupBy(list, url);
}

function loadDSCDHA(sophieuCDHA, dataBN) {
	var arr = [$("#tdt_cdha_hinhthuc").val() != 0, singletonObject.dvtt, sophieuCDHA,  $("#tdt_cdha_phongcdha").val(), 0,
		todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT];
	var gioitinh = dataBN.GIOI_TINH == 0? "Nam": "Nữ"
	var url = "noitru_cdha_hienthiluoi_svv?url=" + convertArray(arr) + "&phan_loai_gioi_tinh=" + gioitinh + "&nguonngoai=" + $("#tdt_xn_hinhthuc").val();
	var list = $("#tdt_list_cdha");
	loadDataGridGroupBy(list, url);
}

function loadDSPhieuCDHA(dataBN) {
	var arr = [ singletonObject.dvtt, todieutriObject.ID_DIEUTRI,
		thongtinhsba.thongtinbn.STT_BENHAN, todieutriObject.STT_DOTDIEUTRI];
	var url = "cmu_list_CMU_HSBA_DSCDHA_TDT_F?url=" + convertArray(arr);
	var list = $("#tdt_list_phieucdha");
	loadDataGridGroupBy(list, url);
}

function loadDSTTPT(sophieuTTPT, dataBN) {
	var arr = [$("#tdt_ttpt_hinhthuc").val() != 0, singletonObject.dvtt, sophieuTTPT,
		$("#tdt_ttpt_loaittpt").val(), 0,0,
		0,
		todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT];
	var gioitinh = dataBN.GIOI_TINH == 0? "Nam": "Nữ"
	var url = "noitru_ttpt_hienthiluoi_svv?url=" + convertArray(arr) + "&phan_loai_gioi_tinh=" + gioitinh + "&nguonngoai=" + $("#tdt_ttpt_hinhthuc").val();
	var list = $("#tdt_list_ttpt");
	loadDataGridGroupBy(list, url);
}

function loadDSPhieuTTPT(dataBN) {
	var arr = [ singletonObject.dvtt, todieutriObject.ID_DIEUTRI,
		thongtinhsba.thongtinbn.STT_BENHAN, todieutriObject.STT_DOTDIEUTRI];
	var url = "cmu_list_CMU_HSBA_DSTTPT_TDT_F?url=" + convertArray(arr);
	var list = $("#tdt_list_phieuttpt");
	loadDataGridGroupBy(list, url);
}

function resetObjectTodieutri () {
	todieutriObject = {};
}

function initButton() {
	hideSelfLoading("action_tdt_themoixn");
	hideSelfLoading("action_tdt_luuxn");
	hideSelfLoading("action_tdt_xoaxn");
	hideSelfLoading("action_tdt_kysoxn");
	hideSelfLoading("action_tdt_themoicdha");
	hideSelfLoading("action_tdt_luucdha");
	hideSelfLoading("action_tdt_xoacdha");
	hideSelfLoading("action_tdt_kysocdha");
	hideSelfLoading("action_tdt_themoittpt");
	hideSelfLoading("action_tdt_luuttpt");
	hideSelfLoading("action_tdt_xoattpt");
	hideSelfLoading("action_tdt_kysottpt");
	hideSelfLoading("action_thuoc_xoatoathuoc");
	enableModeButtonTTPT('add')
	enableModeButtonXN('add')
	enableModeButtonCDHA('add')
	$("#tdt_cdha_phongcdha").prop("disabled", false);
	$("#tdt_cdha_hinhthuc").prop("disabled", false);
	$("#tdt_xn_hinhthuc").prop("disabled", false);
	$("#tdt_ttpt_hinhthuc").prop("disabled", false);

}

function initGridXetnghiem() {
	var listXN = $("#tdt_list_xetnghiem");
	if(!listXN[0].grid) {
		listXN.jqGrid({
			url: '',
			datatype: "local",
			loadonce: true,
			height: 300,
			width: null,
			shrinkToFit: false,
			ignoreCase: true,
			colModel: [
				{label: "Ten loai", name: 'TEN_LOAI_XN', width: 10},
				{label: "Mã XN", name: 'MA_XN', width: 80},
				{label: "MABAOCAO", name: 'MABAOCAO', width: 30, hidden: true},
				{label: "MA_DICHVU_KYTHUAT_DMDC", name: 'MA_DICHVU_KYTHUAT_DMDC', width: 30, hidden: true},
				{label: "Tên xét nghiệm", name: 'TEN_XN', width: 230, cellattr: function (rowId, tv, rawObject, cm, rdata) {
						return 'style="white-space: normal;"';
					}
				},
				{label:  "SL", name: 'SO_LUONG', width: 60, editable: true, edittype: 'text', align: "center"},
				{label: "Đơn giá",name: 'GIA_XN', width: 100, align: "right"},
				{label: "Thành tiền", name: 'THANH_TIEN', width: 100, align: "right"},
				{label: "CHON", name: 'CHON', width: 60, hidden: true},
				{label: "GIA_BHYT",name: 'GIA_BHYT', width: 50, hidden: true},
				{label: "THANHTIEN_BHYT",name: 'THANHTIEN_BHYT', width: 50, hidden: true},
				{label: "GIA_KBHYT",name: 'GIA_KBHYT', width: 50, hidden: true},
				{label: "THANHTIEN_KBHYT",name: 'THANHTIEN_KBHYT', width: 50, hidden: true},
				{label: "SOLUONG_AN",name: 'SOLUONG_AN', width: 50, hidden: true},
				{label: "MANOIBO",name: 'MANOIBO', width: 50, hidden: true},
				{label: "MANOIBO",name: 'MANOIBO', width: 50, hidden: true},
				{label: "MANOIBO",name: 'MANOIBO', width: 50, hidden: true},
				{label: "Bệnh phẩm",name: 'MAUBENHPHAM', index: 'MAUBENHPHAM', width: 50, align: "right", formatter: function (cellvalue, options, rowObject) {

						return cellvalue == null || cellvalue == 'null'? "": cellvalue;
					}
				},
				{label: "Dịch vụ", name: 'DICHVU', index: 'DICHVU', width: 50, cellattr: function (cellvalue, options, rowObject) {
						var color;
						var color_text;
						if (rowObject.DICHVU !=undefined && rowObject.DICHVU.toString() == "1") {
							color = 'red';
							color_text = 'black;font-weight:bold';
						} else {
							color = 'white';
							color_text = 'black';
						}
						return 'style="background-color:' + color + ' ;color:' + color_text + '"';
					}
				}
			],
			rowNum: 1000000,
			multiselect: true,
			caption: "",
			gridComplete: function () {
				var str = listXN.jqGrid('getDataIDs');
				if (str != "") {
					for (var i = 0; i < str.length; i++) {
						var ret1 = listXN.jqGrid('getRowData', str[i]);
						if (ret1.CHON.toString() == "1")
							listXN.jqGrid('setSelection', str[i]);
					}
				}
			},
			onSelectRow: function(id, selected){
				if(id){
					var rowData = listXN.jqGrid("getRowData", id);
					var allData = listXN.jqGrid('getGridParam','data');
					allData.map(function(item){
						if(item.MA_XN == rowData.MA_XN){
							item.CHON = selected? 1: 0;
						}
						return item;
					})
					listXN.jqGrid('setGridParam', { data: allData});
				}
			},
			cellEdit: false,
			cellsubmit: 'clientArray',
			afterSaveCell: function (rowid, name, val, iRow, iCol) {
				var list = $("#tdt_list_xetnghiem");
				var sl_an = list.jqGrid('getCell', rowid, iCol + 8);
				var gia_val_an = list.jqGrid('getCell', rowid, iCol + 1);
				var gia_val1_an = list.jqGrid('getCell', rowid, iCol + 4);
				var gia_val2_an = list.jqGrid('getCell', rowid, iCol + 6);
				list.jqGrid('setRowData', rowid, {THANH_TIEN: gia_val_an / sl_an});
				list.jqGrid('setRowData', rowid, {THANHTIEN_BHYT: gia_val1_an / sl_an});
				list.jqGrid('setRowData', rowid, {THANHTIEN_KBHYT: gia_val2_an / sl_an});
				var gia_val = list.jqGrid('getCell', rowid, iCol + 1);
				list.jqGrid('setRowData', rowid, {THANH_TIEN: gia_val * val});
				var gia_val1 = list.jqGrid('getCell', rowid, iCol + 4);
				var gia_val2 = list.jqGrid('getCell', rowid, iCol + 6);
				list.jqGrid('setRowData', rowid, {THANHTIEN_BHYT: gia_val1 * val});
				list.jqGrid('setRowData', rowid, {THANHTIEN_KBHYT: gia_val2 * val});
				list.jqGrid('setRowData', rowid, {SOLUONG_AN: val});
			},
			grouping: true,
			groupingView: {
				groupField: ["TEN_LOAI_XN"],
				groupColumnShow: [false],
				groupText: ['<b>{0}</b>'],
				groupCollapse: true
			},
			afterSearch: function () {
				console.log("text")
			},
			jqGridToolbarAfterSearch: function () {
				console.log("jqGridToolbarAfterSearch")
			}
		});
		listXN.jqGrid('filterToolbar', {
			stringResult: true,
			searchOnEnter: false,
			defaultSearch: "cn",
			afterSearch: function () {
				listXN.find(".tdt_list_xetnghiemghead_0").each(function() {
					listXN.jqGrid('groupingToggle', $(this).attr('id'))
				})
			}
		});
	}

	if(!$("#tdt_list_phieuxetnghiem")[0].grid) {
		$("#tdt_list_phieuxetnghiem").jqGrid({
			url: '',
			datatype: "local",
			loadonce: true,
			height: 396,
			width: null,
			shrinkToFit: false,
			colModel: [
				{label: "SO_PHIEU_XN", name: 'SO_PHIEU_XN', index: 'SO_PHIEU_XN', width: 150},

				{label: "MA_PHONG_XN",name: 'MA_PHONG_XN', index: 'MA_PHONG_XN', width: 60, hidden: true},
				{label: "STT_DIEUTRI",name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 60, hidden: true},
				{label: "SID",name: 'SID', index: 'SID', width: 60},
				{label: "Khoa",name: 'KHOA_CHI_DINH', index: 'KHOA_CHI_DINH', width: 120},
				{label: "Tên loại", name: 'TEN_LOAI_XN', index: 'TEN_LOAI_XN', width: 10, hidden: true},
				{label: "MA_XN", name: 'MA_XN', index: 'MA_XN', width: 60, hidden: true},
				{label: "TEN_XN", name: 'TEN_XN', index: 'TEN_XN', width: 215, hidden: true },
				{label: "Tên xét nghiệm", name: 'TEN_XN_HT', index: 'TEN_XN_HT', width: 215,
					formatter: function (cellvalue, options, rowObject) {
						if(rowObject.DA_XET_NGHIEM == 1) {
							return rowObject.TEN_XN + " <p class='text-primary font-weight-bold m-0 mt-1'> <i class='fa fa-check-circle'></i> Đã thực hiện</p>";
						}
						return rowObject.TEN_XN;
					},
					cellattr: function (rowId, tv, rawObject, cm, rdata) {
						return 'style="white-space: normal;"';
					}
				},
				{label: "Cấp cứu",name: 'CAPCUU', index: 'CAPCUU', width: 60},
				{label: "BHYT", name: 'CO_BHYT', index: 'CO_BHYT', width: 60},
				{label: "Số lượng", name: 'SO_LUONG', index: 'SO_LUONG', width: 50, editable: true, edittype: 'text', align: "center"},
				{label: "Đơn giá", name: 'GIA_XN', index: 'GIA_XN', width: 50, align: "right"},
				{label: "Thành tiền", name: 'THANH_TIEN', index: 'THANH_TIEN', width: 50, align: "right"},
				{label: "Bác sĩ điều trị",name: 'TEN_NHANVIEN', index: 'TEN_NHANVIEN', width: 150},
				{label: "Ngày chỉ định",name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', width: 100},
				{label: "Giờ chỉ định",name: 'GIO_CHI_DINH', index: 'GIO_CHI_DINH', width: 60},
				{label: "CHON", name: 'CHON', index: 'CHON', width: 60, hidden: true},
				{label: "DA_XET_NGHIEM", name: 'DA_XET_NGHIEM', index: 'DA_XET_NGHIEM', width: 60, hidden: true},
				{label: "SOVAOVIEN", name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 60, hidden: true},
				{label: "SOVAOVIEN_DT", name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', width: 60, hidden: true},
				{label: "GHICHU", name: 'GHICHU', index: 'GHICHU', width: 60, hidden: true},

			],
			rowNum: 1000000,
			caption: "Danh sách phiếu chỉ định",
			grouping: true,
			groupingView: {
				groupField: ["SO_PHIEU_XN","TEN_LOAI_XN"],
				groupColumnShow: [false, false],
				groupText: ['<b>{0}</b>', '<b>{0}</b>'],
				groupCollapse: false
			},
			footerrow: true,
			loadComplete: function () {
				var $self = $(this);
				var sum_tt = $self.jqGrid("getCol", "THANH_TIEN", false, "sum");
				var sum_sl = $self.jqGrid("getCol", "SO_LUONG", false, "sum");
				$self.jqGrid("footerData", "set", {THANH_TIEN: sum_tt});
				$self.jqGrid("footerData", "set", {SO_LUONG: sum_sl});
				$self.jqGrid("footerData", "set", {TEN_XN: "Tổng:"});
			},
			onSelectRow: function(id) {
				if (id) {
					var ret = $("#tdt_list_phieuxetnghiem").jqGrid('getRowData', id);
					$("#tdt_xn_giochidinh").val(ret.GIO_CHI_DINH);
					$("#tdt_xn_hinhthuc").val(ret.CO_BHYT == 1? 0: 1);
					$("#tdt_xn_hinhthuc").prop("disabled", true);
					$("#tdt_xn_trangthai").val(ret.CAPCUU);
					$("#tdt_xn_phongxetnghiem").val(ret.MA_PHONG_XN);
					loadDSXetnghiem(ret.SO_PHIEU_XN, thongtinhsba.thongtinbn)
					enableModeButtonXN('edit');
					checkKyso769({
						dvtt: singletonObject.dvtt,
						soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
						soVaoVienDT: todieutriObject.SOVAOVIEN_DT,
						kyHieuPhieu: "PHIEUCD_NOITRU_XN",
						soPhieuDichVu: ret.SO_PHIEU_XN,
						userId: singletonObject.userId,
					}, function(data) {
						if(data[0].DAKY > 0) {
							$("#action_tdt_kysoxn").hide()
							$("#action_tdt_luuxn").hide()
							$("#tdt-xn-dropdown .item-kyso").show()
						} else {
							$("#action_tdt_kysoxn").show()
							$("#action_tdt_luuxn").show()
							$("#tdt-xn-dropdown .item-kyso").hide()
						}
					}, function() {

					})
				}

			},
			onRightClickRow: function (id1) {
				if (id1) {
					$('#tdt_list_phieuxetnghiem').jqGrid('setSelection', id1);
					$.contextMenu({
						selector: '#tdt_list_phieuxetnghiem tr',
						callback: function (key, options) {
							var id = $("#tdt_list_phieuxetnghiem").jqGrid('getGridParam', 'selrow');
							var ret = $("#tdt_list_phieuxetnghiem").jqGrid('getRowData', id);
							if (key == "giaiphaubenh") {
								$(".clear-input").val("");

								var data = {
									DVTT: thongtinhsba.thongtinbn.DVTT,
									MA_BENH_NHAN: thongtinhsba.thongtinbn.MA_BENH_NHAN,
									TEN_BENH_NHAN : thongtinhsba.thongtinbn.TEN_BENH_NHAN,
									TUOI: thongtinhsba.thongtinbn.TUOI_HT,
									GIOI_TINH: thongtinhsba.thongtinbn.GIOI_TINH_HT,
									DIA_CHI: thongtinhsba.thongtinbn.DIA_CHI,
									KHOA: thongtinhsba.thongtinbn.TEN_PHONGBAN,
									CHAN_DOAN: thongtinhsba.thongtinbn.ICD_HT,
									STT_BENHAN: thongtinhsba.thongtinbn.STT_BENHAN,
									STT_DOTDIEUTRI: todieutriObject.STT_DOTDIEUTRI,
									SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
									SOVAOVIEN_DT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
									SO_PHIEU_XN: ret.SO_PHIEU_XN,
									ID_DIEUTRI: todieutriObject.ID_DIEUTRI,
									MA_XET_NGHIEM: ret.MA_XN,
									TEN_XET_NGHIEM: ret.TEN_XN,
									CAP_CUU: ret.CAPCUU,
									USER_ID: singletonObject.userId,
									BAC_SI_DIEU_TRI: todieutriObject.BACSIDIEUTRI
								}
								loadModalGiaiPhauBenhSinhThiet(data, 'them');
							}
							var rowSelected = getThongtinRowSelected("tdt_list_phieuxetnghiem")
							if(key == "xoa") {
								if(rowSelected.DA_XET_NGHIEM == 1) {
									return notifiToClient("Red", "Xét nghiệm đã thực hiện không được xóa");
								}
								confirmToClient("Bạn có chắc chắn muốn chỉ định này?", function() {
									showLoaderIntoWrapId("tdt_list_phieucdha_wrap")
									var arr = [rowSelected.SO_PHIEU_XN,
										singletonObject.dvtt, rowSelected.MA_XN,
										rowSelected.STT_DIEUTRI,
										thongtinhsba.thongtinbn.STT_BENHAN,
										todieutriObject.STT_DOTDIEUTRI,
										todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT];
									$.post("noitru_xetnghiem_delete_cacchitiet_svv", {
										url: convertArray(arr)
									}).done(function(data) {
										notifiToClient("Green", "Xóa thành công!")
										var logXetNghiem = [
											"Thời gian y lệnh: " + rowSelected.NGAY_CHI_DINH + " " + rowSelected.GIO_CHI_DINH,
											"Tờ điều trị số: " + todieutriObject.STT_DIEUTRI,
											"Bác sĩ điều trị: "+ todieutriObject['TENBSDIEUTRI'],
											"Hình thức: "+ ($("#tdt_xn_hinhthuc").val() == 0? "BHYT": "Thu phí"),
											"Số phiếu xét nghiệm: "+ rowSelected.SO_PHIEU_XN,
											"Xét nghiệm: "+ rowSelected.MA_XN + " - " + rowSelected.TEN_XN + " - số lượng: " + rowSelected.SO_LUONG +" lần",
										]
										luuLogHSBATheoBN({
											SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
											LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
											NOIDUNGBANDAU: logXetNghiem.join("; "),
											NOIDUNGMOI: "",
											USERID: singletonObject.userId,
											ACTION: LOGHSBAACTION.DELETE.KEY,
										})

										var urlXoarong = "xetnghiem_xoaphieurong_noitru";
										var arrXoarong = [singletonObject.dvtt, rowSelected.SOVAOVIEN, rowSelected.SOVAOVIEN_DT];
										$.post(urlXoarong, {
											url: convertArray(arrXoarong)
										});
										$("#action_tdt_huyxn").click();
									}).fail(function() {
										notifiToClient("Red", "Xóa thất bại!")
									}).always(function() {
										hideLoaderIntoWrapId("tdt_list_phieucdha_wrap")
									})


								}, function () {

								})
							}
						},
						items: {
							// "giaiphaubenh": {name: '<p><i class="fa fa-medkit text-primary" aria-hidden="true"></i> Giải phẫu bệnh sinh thiết</p>'},
							"xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'}
						}
					});
				}
			},
		});
		$("#tdt_list_phieuxetnghiem").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
	}
}

function initGridCDHA() {
	var listCDHA = $("#tdt_list_cdha")
	if(!listCDHA[0].grid) {
		listCDHA.jqGrid({
			url: '',
			datatype: "local",
			loadonce: true,
			height: 300,
			width: null,
			shrinkToFit: false,
			ignoreCase: true,
			colModel: [
				{label: "TEN_LOAI_CDHA", name: 'TEN_LOAI_CDHA', index: 'TEN_LOAI_CDHA', width: 10},
				{label: "Mã",name: 'MA_CDHA', index: 'MA_CDHA', width: 80},
				{label: "MABAOCAO", label: "MABAOCAO",name: 'MABAOCAO', index: 'MABAOCAO', width: 30, hidden: true},
				{label: "Mã DVKT",name: 'MA_DICHVU_KYTHUAT_DMDC', index: 'MA_DICHVU_KYTHUAT_DMDC', width: 30, hidden: true},
				{label:  "Tên CDHA", name: 'TEN_CDHA', index: 'TEN_CDHA', width: 230, cellattr: function (rowId, tv, rawObject, cm, rdata) {
						return 'style="white-space: normal;"';
					}},
				{label: "SL", name: 'SO_LUONG', index: 'SO_LUONG', width: 60, editable: true, edittype: 'text', align: "center"},
				{label: "Đơn giá", name: 'GIA_CDHA', index: 'GIA_CDHA', width: 100, align: "right"},
				{label: "Thành tiền", name: 'THANH_TIEN', index: 'THANH_TIEN', width: 100, align: "right"},
				{label: 'CHON', name: 'CHON', index: 'CHON', width: 60, hidden: true},
				{label: 'GIA_BHYT',name: 'GIA_BHYT', index: 'GIA_BHYT', width: 50, hidden: true},
				{label: "THANHTIEN_BHYT", name: 'THANHTIEN_BHYT', index: 'THANHTIEN_BHYT', width: 50, hidden: true},
				{label: 'GIA_KBHYT', name: 'GIA_KBHYT', index: 'GIA_KBHYT', width: 50, hidden: true},
				{label: "THANHTIEN_KBHYT", name: 'THANHTIEN_KBHYT', index: 'THANHTIEN_KBHYT', width: 50, hidden: true},
				{label: 'SOLUONG_AN',name: 'SOLUONG_AN', index: 'SOLUONG_AN', width: 50, hidden: true},
				{label: 'MANOIBO', name: 'MANOIBO', index: 'MANOIBO', width: 50, hidden: true},
				{label: "Dịch vụ", name: 'DICHVU', index: 'DICHVU', width: 50, cellattr: function (cellvalue, options, rowObject) {
						var color;
						var color_text;
						if (rowObject.DICHVU !=undefined && rowObject.DICHVU.toString() == "1") {
							color = 'red';
							color_text = 'black;font-weight:bold';
						} else {
							color = 'white';
							color_text = 'black';
						}
						return 'style="background-color:' + color + ' ;color:' + color_text + '"';
					}
				}
			],
			rowNum: 1000000,
			multiselect: true,
			caption: "",
			gridComplete: function () {
				var str = listCDHA.jqGrid('getDataIDs');
				if (str != "") {
					for (var i = 0; i < str.length; i++) {
						var ret1 = listCDHA.jqGrid('getRowData', str[i]);
						if (ret1.CHON.toString() == "1")
							listCDHA.jqGrid('setSelection', str[i]);
					}
				}
			},
			onSelectRow: function(id, selected){
				if(id){
					var rowData = listCDHA.jqGrid("getRowData", id);
					var allData = listCDHA.jqGrid('getGridParam','data');
					allData.map(function(item){
						if(item.MA_CDHA == rowData.MA_CDHA){
							item.CHON = selected? 1: 0;
						}
						return item;
					})
					listCDHA.jqGrid('setGridParam', { data: allData});
				}
			},
			cellEdit: false,
			cellsubmit: 'clientArray',
			afterSaveCell: function (rowid, name, val, iRow, iCol) {
				var list = $("#tdt_list_cdha");
				var sl_an = list.jqGrid('getCell', rowid, iCol + 8);
				var gia_val_an = list.jqGrid('getCell', rowid, iCol + 1);
				var gia_val1_an = list.jqGrid('getCell', rowid, iCol + 4);
				var gia_val2_an = list.jqGrid('getCell', rowid, iCol + 6);
				list.jqGrid('setRowData', rowid, {THANH_TIEN: gia_val_an / sl_an});
				list.jqGrid('setRowData', rowid, {THANHTIEN_BHYT: gia_val1_an / sl_an});
				list.jqGrid('setRowData', rowid, {THANHTIEN_KBHYT: gia_val2_an / sl_an});
				var gia_val = list.jqGrid('getCell', rowid, iCol + 1);
				list.jqGrid('setRowData', rowid, {THANH_TIEN: gia_val * val});
				var gia_val1 = list.jqGrid('getCell', rowid, iCol + 4);
				var gia_val2 = list.jqGrid('getCell', rowid, iCol + 6);
				list.jqGrid('setRowData', rowid, {THANHTIEN_BHYT: gia_val1 * val});
				list.jqGrid('setRowData', rowid, {THANHTIEN_KBHYT: gia_val2 * val});
				list.jqGrid('setRowData', rowid, {SOLUONG_AN: val});
			},
			grouping: true,
			groupingView: {
				groupField: ["TEN_LOAI_CDHA"],
				groupColumnShow: [false],
				groupText: ['<b>{0}</b>'],
				groupCollapse: true
			}
		});
		listCDHA.jqGrid('filterToolbar', {
			stringResult: true,
			searchOnEnter: false,
			defaultSearch: "cn",
			afterSearch: function () {
				listCDHA.find(".tdt_list_cdhaghead_0").each(function() {
					listCDHA.jqGrid('groupingToggle', $(this).attr('id'))
				})
			}
		});
	}
	if(!$("#tdt_list_phieucdha")[0].grid) {
		$("#tdt_list_phieucdha").jqGrid({
			url: '',
			datatype: "local",
			loadonce: true,
			height: 340,
			width: null,
			shrinkToFit: false,
			colModel: [
				{label: "SO_PHIEU_CDHA", name: 'SO_PHIEU_CDHA', index: 'SO_PHIEU_CDHA', width: 150},
				{label: "MA_PHONG_CDHA",name: 'MA_PHONG_CDHA', index: 'MA_PHONG_CDHA', width: 60, hidden: true},
				{label: "STT_DIEUTRI",name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 60, hidden: true},
				{label: "Khoa",name: 'KHOA_CHI_DINH', index: 'KHOA_CHI_DINH', width: 120},
				{label: "Tên loại", name: 'TEN_LOAI_CDHA', index: 'TEN_LOAI_CDHA', width: 10, hidden: true},
				{label: "MA_CDHA", name: 'MA_CDHA', index: 'MA_CDHA', width: 60, hidden: true},
				{label: "TEN_CDHA", name: 'TEN_CDHA', index: 'TEN_CDHA', width: 215, hidden: true},
				{label: "Tên CDHA", name: 'TEN_CDHA_HT', index: 'TEN_CDHA_HT', width: 215,
					formatter: function (cellvalue, options, rowObject) {
						let ghichu = rowObject.GHICHU_TENCDHA.trim() ? ("[" + rowObject.GHICHU_TENCDHA + "]") : ""
						if(rowObject.DA_CHAN_DOAN == 1) {
							return rowObject.TEN_CDHA + ghichu + " <p class='text-primary font-weight-bold m-0 mt-1'> <i class='fa fa-check-circle'></i> Đã thực hiện</p>";
						}
						return rowObject.TEN_CDHA + ghichu;
					},
					cellattr: function (rowId, tv, rawObject, cm, rdata) {
						return 'style="white-space: normal;"';
					}
				},
				{label: "Cấp cứu",name: 'CAPCUU', index: 'CAPCUU', width: 60},
				{label: "BHYT", name: 'CO_BHYT', index: 'CO_BHYT', width: 60},
				{label: "Số lượng", name: 'SO_LUONG', index: 'SO_LUONG', width: 50, editable: true, edittype: 'text', align: "center"},
				{label: "Đơn giá", name: 'GIA_CDHA', index: 'GIA_CDHA', width: 50, align: "right"},
				{label: "Thành tiền", name: 'THANH_TIEN', index: 'THANH_TIEN', width: 50, align: "right"},
				{label: "Bác sĩ điều trị",name: 'TEN_NHANVIEN', index: 'TEN_NHANVIEN', width: 150},
				{label: "Ngày chỉ định",name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', width: 100},
				{label: "Giờ chỉ định",name: 'GIO_CHI_DINH', index: 'GIO_CHI_DINH', width: 60},
				{label: "CHON", name: 'CHON', index: 'CHON', width: 60, hidden: true},
				{label: "DA_CHAN_DOAN", name: 'DA_CHAN_DOAN', index: 'DA_CHAN_DOAN', width: 60, hidden: true},
				{label: "SOVAOVIEN", name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 60, hidden: true},
				{label: "SOVAOVIEN_DT", name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', width: 60, hidden: true},
				{label: "GHICHU", name: 'GHICHU', index: 'GHICHU', width: 60, hidden: true},
				{label: "GHICHU_TENCDHA", name: 'GHICHU_TENCDHA', index: 'GHICHU_TENCDHA', width: 60, hidden: true},

			],
			rowNum: 1000000,
			caption: "Danh sách phiếu chỉ định",
			grouping: true,
			groupingView: {
				groupField: ["SO_PHIEU_CDHA","TEN_LOAI_CDHA"],
				groupColumnShow: [false, false],
				groupText: ['<b>{0}</b>', '<b>{0}</b>'],
				groupCollapse: false
			},
			footerrow: true,
			loadComplete: function () {
				var $self = $(this);
				var sum_tt = $self.jqGrid("getCol", "THANH_TIEN", false, "sum");
				var sum_sl = $self.jqGrid("getCol", "SO_LUONG", false, "sum");
				$self.jqGrid("footerData", "set", {THANH_TIEN: sum_tt});
				$self.jqGrid("footerData", "set", {SO_LUONG: sum_sl});
				$self.jqGrid("footerData", "set", {TEN_CDHA: "Tổng:"});
			},
			onSelectRow: function(id) {
				if (id) {
					var ret = $("#tdt_list_phieucdha").jqGrid('getRowData', id);
					$("#tdt_cdha_giochidinh").val(ret.GIO_CHI_DINH);
					$("#tdt_cdha_hinhthuc").val(ret.CO_BHYT == 1? 0: 1);
					$("#tdt_cdha_hinhthuc").prop("disabled", true);
					$("#tdt_cdha_trangthai").val(ret.CAPCUU);
					$("#tdt_cdha_phongcdha").val(ret.MA_PHONG_CDHA);
					$("#tdt_cdha_phongcdha").prop("disabled", true);
					loadDSCDHA(ret.SO_PHIEU_CDHA, thongtinhsba.thongtinbn)
					enableModeButtonCDHA('edit');
					checkKyso769({
						dvtt: singletonObject.dvtt,
						soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
						soVaoVienDT: todieutriObject.SOVAOVIEN_DT,
						kyHieuPhieu: "PHIEUCD_NOITRU_CDHA",
						soPhieuDichVu: ret.SO_PHIEU_CDHA,
						userId: singletonObject.userId,
					}, function(data) {
						if(data[0].DAKY > 0) {
							$("#action_tdt_kysocdha").hide()
							$("#action_tdt_luucdha").hide()
							$("#tdt-cdha-dropdown .item-kyso").show()
						} else {
							$("#action_tdt_kysocdha").show()
							$("#action_tdt_luucdha").show()
							$("#tdt-cdha-dropdown .item-kyso").hide()
						}
					}, function() {

					})
				}

			},
			onRightClickRow: function(id) {
				if(id) {
					$.contextMenu({
						selector: '#tdt_list_phieucdha tr',
						reposition : false,
						callback: function (key, options) {
							var rowSelected = getThongtinRowSelected("tdt_list_phieucdha")
							var idWrap = "tdt_list_phieucdha_wrap"
							if (key == "ghichu"){
								$("#modalFormGhiChuCDHA").modal('show')
								$("#ghichucdha").val(rowSelected.GHICHU_TENCDHA)
							}
							if(key == "xoa") {
								if (rowSelected.DA_CHAN_DOAN == 1) {
									return notifiToClient("Red", "Chẩn đoán hình ảnh đã thực hiện không được xóa");
								}
								confirmToClient("Bạn có chắc chắn muốn chỉ định này?", function () {
									showLoaderIntoWrapId(idWrap)
									var arr = [rowSelected.SO_PHIEU_CDHA,
										singletonObject.dvtt, rowSelected.MA_CDHA,
										rowSelected.STT_DIEUTRI,
										thongtinhsba.thongtinbn.STT_BENHAN,
										todieutriObject.STT_DOTDIEUTRI,
										todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT];
									$.post("noitru_cdha_delete_cacchitiet_svv?arr_ris="+rowSelected.MA_CDHA, {
										url: convertArray(arr)
									}).done(function(data) {
										if(data == "NOTUPDATE") {
											$.post("cmu_post_not_cdha_delete_chitiet_svv", {
												url: [rowSelected.SO_PHIEU_CDHA,
													singletonObject.dvtt, rowSelected.MA_CDHA,
													rowSelected.STT_DIEUTRI,
													thongtinhsba.thongtinbn.STT_BENHAN,
													todieutriObject.STT_DOTDIEUTRI,
													todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT
												].join("```")
											}).done(function(res) {
												var arrMessage = [
													"Xóa thành công!",
													"Dịch vụ đã được thanh toán",
													"Dịch vụ đã được thực hiện",
													"Dịch vụ đã được tính tiền ekip",
												]
												notifiToClient(res == 0? "Green": "Red", arrMessage[res])
												if(res == 0) {
													$.logXoaCDHATodieutri(rowSelected)
													var urlXoarong = "cdha_xoaphieurong_noitru";
													var arrXoarong = [singletonObject.dvtt, rowSelected.SOVAOVIEN, rowSelected.SOVAOVIEN_DT];
													$.post(urlXoarong, {
														url: convertArray(arrXoarong)
													});
												}
											}).fail(function() {
												notifiToClient("Red","Xóa thất bại!")
											}).always(function() {
												hideLoaderIntoWrapId(idWrap)
											})
										} else {
											var urlXoarong = "cdha_xoaphieurong_noitru";
											var arrXoarong = [singletonObject.dvtt, rowSelected.SOVAOVIEN, rowSelected.SOVAOVIEN_DT];
											$.post(urlXoarong, {
												url: convertArray(arrXoarong)
											});
											$.logXoaCDHATodieutri(rowSelected)
											notifiToClient("Green", "Xóa thành công")
											$("#action_tdt_huycdha").click();
											hideLoaderIntoWrapId(idWrap)
										}

									}).fail(function() {
										hideLoaderIntoWrapId(idWrap)
										notifiToClient("Red", "Xóa thất bại!")
									})


								}, function () {

								})
							}
						},
						items: {
							"ghichu": {name: '<p><i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Ghi chú</p>'},
							"xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'}
						}
					});
				}
			},

		});
		$("#tdt_list_phieucdha").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
	}
}

function initGridTTPT() {
	var listTTPT = $("#tdt_list_ttpt");
	if(!listTTPT[0].grid) {
		listTTPT.jqGrid({
			url: '',
			datatype: "local",
			loadonce: true,
			height: 300,
			width: null,
			shrinkToFit: false,
			ignoreCase: true,
			colModel: [
				{label: "Mã DV",name: 'MA_DV', index: 'MA_DV', width: 120},
				{label: "MABAOCAO",name: 'MABAOCAO', index: 'MABAOCAO', width: 30, hidden: true},
				{label: "MA_DICHVU_KYTHUAT_DMDC",name: 'MA_DICHVU_KYTHUAT_DMDC', index: 'MA_DICHVU_KYTHUAT_DMDC', width: 30, hidden: true},
				{label: "Tên dịch vụ",name: 'TEN_DV', index: 'TEN_DV', width: 230, cellattr: function (rowId, tv, rawObject, cm, rdata) {
						return 'style="white-space: normal;"';
					}
				},
				{label: "SL",name: 'SO_LUONG', index: 'SO_LUONG', width: 60, editable: true, edittype: 'text', align: "center"},
				{label: "Loại",name: 'TEN_LOAIHINH', index: 'TEN_LOAIHINH', width: 50},
				{label: "Đơn giá",name: 'GIA_DV', index: 'GIA_DV', width: 100, align: "right"},
				{label: "Thành tiền",name: 'THANH_TIEN', index: 'THANH_TIEN', width: 100, align: "right"},
				{label: "Madv",name: 'CHON', index: 'CHON', width: 60, hidden: true},
				{label: "GIA_BHYT",name: 'GIA_BHYT', index: 'GIA_BHYT', width: 50, hidden: true},
				{label: "THANHTIEN_BHYT",name: 'THANHTIEN_BHYT', index: 'THANHTIEN_BHYT', width: 50, hidden: true},
				{label: "GIA_KBHYT",name: 'GIA_KBHYT', index: 'GIA_KBHYT', width: 50, hidden: true},
				{label: "THANHTIEN_KBHYT",name: 'THANHTIEN_KBHYT', index: 'THANHTIEN_KBHYT', width: 50, hidden: true},
				{label: "SOLUONG_AN",name: 'SOLUONG_AN', index: 'SOLUONG_AN', width: 50, hidden: true},
				{label: "MANOIBO",name: 'MANOIBO', index: 'MANOIBO', width: 50, hidden: true},
				{label: "TILETT",name: 'TILETT', index: 'TILETT', width: 50, hidden: true},
				{label: "IDVITRITTPT",name: 'VITRITTPT', index: 'VITRITTPT', width: 50, hidden: true},
				{label: "IDVITRITTPT",name: 'IDVITRITTPT', index: 'IDVITRITTPT', width: 50, hidden: true},
				{label: "THOIGIANTTPT",name: 'THOIGIANTTPT', index: 'THOIGIANTTPT', width: 50, hidden: true},
				{label: "Dịch vụ", name: 'DICHVU', index: 'DICHVU', width: 50, cellattr: function (cellvalue, options, rowObject) {
						var color;
						var color_text;
						if (rowObject.DICHVU !=undefined && rowObject.DICHVU.toString() == "1") {
							color = 'red';
							color_text = 'black;font-weight:bold';
						} else {
							color = 'white';
							color_text = 'black';
						}
						return 'style="background-color:' + color + ' ;color:' + color_text + '"';
					}
				}

			],
			sortname: 'TEN_DV',
			rowNum: 1000000,
			sortorder: "asc",
			multiselect: true,
			caption: "",
			gridComplete: function () {
				var str = listTTPT.jqGrid('getDataIDs');
				if (str != "") {
					for (var i = 0; i < str.length; i++) {
						var ret1 = listTTPT.jqGrid('getRowData', str[i]);
						if (ret1.CHON.toString() == "1")
							listTTPT.jqGrid('setSelection', str[i]);
					}
				}
			},
			onSelectRow: function(id, selected){
				if(id){
					var rowData = listTTPT.jqGrid("getRowData", id);
					var allData = listTTPT.jqGrid('getGridParam','data');
					allData.map(function(item){
						if(item.MA_DV == rowData.MA_DV){
							item.CHON = selected? 1: 0;
						}
						return item;
					})
					listTTPT.jqGrid('setGridParam', { data: allData});
				}
			},
			cellEdit: true,
			cellsubmit: 'clientArray',
			afterSaveCell: function (rowid, name, val, iRow, iCol) {
				var sl_an = listTTPT.jqGrid('getCell', rowid, iCol + 8);
				var gia_val_an = listTTPT.jqGrid('getCell', rowid, iCol + 2);
				var gia_val1_an = listTTPT.jqGrid('getCell', rowid, iCol + 5);
				var gia_val2_an = listTTPT.jqGrid('getCell', rowid, iCol + 7);
				listTTPT.jqGrid('setRowData', rowid, {THANH_TIEN: gia_val_an / sl_an});
				listTTPT.jqGrid('setRowData', rowid, {THANHTIEN_BHYT: gia_val1_an / sl_an});
				listTTPT.jqGrid('setRowData', rowid, {THANHTIEN_KBHYT: gia_val2_an / sl_an});
				var gia_val = listTTPT.jqGrid('getCell', rowid, iCol + 2);
				listTTPT.jqGrid('setRowData', rowid, {THANH_TIEN: gia_val * val});
				var gia_val1 = listTTPT.jqGrid('getCell', rowid, iCol + 4);
				var gia_val2 = listTTPT.jqGrid('getCell', rowid, iCol + 7);
				listTTPT.jqGrid('setRowData', rowid, {THANHTIEN_BHYT: gia_val1 * val});
				listTTPT.jqGrid('setRowData', rowid, {THANHTIEN_KBHYT: gia_val2 * val});
				listTTPT.jqGrid('setRowData', rowid, {SOLUONG_AN: val});

				var rowData = listTTPT.jqGrid("getRowData", rowid);
				var allData = listTTPT.jqGrid('getGridParam','data');
				allData.map(function(item){
					if(item.MA_DV == rowData.MA_DV){
						item.SOLUONG_AN = rowData.SOLUONG_AN;
						item.SO_LUONG = val;
						item.THANHTIEN_BHYT = rowData.THANHTIEN_BHYT;
						item.THANHTIEN_KBHYT = rowData.THANHTIEN_KBHYT;
					}
					return item;
				})
				listTTPT.jqGrid('setGridParam', { data: allData});
			}
		});
		listTTPT.jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
	}
	var listPhieuTTPT = $("#tdt_list_phieuttpt");
	if(!listPhieuTTPT[0].grid) {
		listPhieuTTPT.jqGrid({
			url: '',
			datatype: "local",
			loadonce: true,
			height: 396,
			width: null,
			shrinkToFit: false,
			colModel: [
				{label: "SO_PHIEU_DICHVU", name: 'SO_PHIEU_DICHVU', index: 'SO_PHIEU_DICHVU', width: 150},
				{label: "MA_PHONG_DICHVU",name: 'MA_PHONG_DICHVU', index: 'MA_PHONG_DICHVU', width: 60, hidden: true},
				{label: "STT_DIEUTRI",name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 60, hidden: true},
				{label: "Khoa",name: 'KHOA_CHI_DINH', index: 'KHOA_CHI_DINH', width: 120},
				{label: "Tên loại", name: 'TEN_LOAI_DV', index: 'TEN_LOAI_DV', width: 10, hidden: true},
				{label: "MA_LOAI", name: 'MA_LOAI_DICHVU', index: 'MA_LOAI_DICHVU', width: 10, hidden: true},
				{label: "Mã DV", name: 'MA_DV', index: 'MA_DV', width: 60, hidden: true},
				{label: "Tên DV", name: 'TEN_DV_HT', index: 'TEN_DV_HT', width: 215,
					formatter: function (cellvalue, options, rowObject) {
						if(rowObject.DA_CHAN_DOAN == 1) {
							return rowObject.TEN_DV + " <p class='text-primary font-weight-bold m-0 mt-1'> <i class='fa fa-check-circle'></i> Đã thực hiện</p>";
						}
						return rowObject.TEN_DV;
					},
					cellattr: function (rowId, tv, rawObject, cm, rdata) {
						return 'style="white-space: normal;"';
					}},
				{label: "TEN_DV", name: 'TEN_DV', index: 'TEN_DV', width: 215, hidden: true},
				{label: "Tỉ lệ TT", name: 'TILE_THANHTOAN', index: 'TILE_THANHTOAN', width: 60},
				{label: "Cấp cứu",name: 'CAPCUU', index: 'CAPCUU', width: 60},
				{label: "BHYT", name: 'CO_BHYT', index: 'CO_BHYT', width: 60},
				{label: "Số lượng", name: 'SO_LUONG', index: 'SO_LUONG', width: 50, editable: true, edittype: 'text', align: "center"},
				{label: "Đơn giá", name: 'GIA_CDHA', index: 'GIA_CDHA', width: 50, align: "right"},
				{label: "Thành tiền", name: 'THANH_TIEN', index: 'THANH_TIEN', width: 50, align: "right"},
				{label: "Bác sĩ điều trị",name: 'TEN_NHANVIEN', index: 'TEN_NHANVIEN', width: 150},
				{label: "Ngày chỉ định",name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', width: 100},
				{label: "Giờ chỉ định",name: 'GIO_CHI_DINH', index: 'GIO_CHI_DINH', width: 60},
				{label: "SOVAOVIEN",name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 60, hidden: true},
				{label: "SOVAOVIEN_DT",name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', width: 60, hidden: true},
				{label: "CHON", name: 'CHON', index: 'CHON', width: 60, hidden: true},
				{label: "DA_CHAN_DOAN", name: 'DA_CHAN_DOAN', index: 'DA_CHAN_DOAN', width: 60, hidden: true},
				{label: "GHICHU", name: 'GHICHU', index: 'GHICHU', width: 60, hidden: true},

			],
			rowNum: 1000000,
			caption: "Danh sách phiếu chỉ định",
			grouping: true,
			groupingView: {
				groupField: ["SO_PHIEU_DICHVU","TEN_LOAI_DV"],
				groupColumnShow: [false, false],
				groupText: ['<b>{0}</b>', '<b>{0}</b>'],
				groupCollapse: false
			},
			footerrow: true,
			loadComplete: function () {
				var $self = $(this);
				var sum_tt = $self.jqGrid("getCol", "THANH_TIEN", false, "sum");
				$self.jqGrid("footerData", "set", {THANH_TIEN: sum_tt});
				$self.jqGrid("footerData", "set", {TEN_CDHA: "Tổng tiền:"});
			},
			onSelectRow: function(id) {
				if (id) {
					var ret = $("#tdt_list_phieuttpt").jqGrid('getRowData', id);
					$("#tdt_ttpt_giochidinh").val(ret.GIO_CHI_DINH);
					$("#tdt_ttpt_hinhthuc").val(ret.CO_BHYT == 1? 0: 1);
					$("#tdt_ttpt_hinhthuc").prop("disabled", true);
					$("#tdt_ttpt_trangthai").val(ret.CAPCUU);
					$("#tdt_ttpt_phongttpt").val(ret.MA_PHONG_DICHVU);
					$("#tdt_ttpt_phongttpt").prop("disabled", true);
					$("#tdt_ttpt_loaittpt").val(ret.MA_LOAI_DICHVU);
					$("#tdt_ttpt_loaittpt").prop("disabled", true);
					loadDSTTPT(ret.SO_PHIEU_DICHVU, thongtinhsba.thongtinbn)
					enableModeButtonTTPT('edit')
					checkKyso769({
						dvtt: singletonObject.dvtt,
						soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
						soVaoVienDT: todieutriObject.SOVAOVIEN_DT,
						kyHieuPhieu: "PHIEUCD_NOITRU_TTPT",
						soPhieuDichVu: ret.SO_PHIEU_DICHVU,
						userId: singletonObject.userId,
					}, function(data) {
						if(data[0].DAKY > 0) {
							$("#action_tdt_kysottpt").hide()
							$("#action_tdt_luuttpt").hide()
							$("#tdt-ttpt-dropdown .item-kyso").show()
						} else {
							$("#action_tdt_kysottpt").show()
							$("#action_tdt_luuttpt").show()
							$("#tdt-ttpt-dropdown .item-kyso").hide()
						}
					}, function() {

					})
				}

			},
			onRightClickRow: function(id) {
				if(id) {
					$.contextMenu({
						selector: '#tdt_list_phieuttpt tr',
						reposition : false,
						callback: function (key, options) {
							var rowSelected = getThongtinRowSelected("tdt_list_phieuttpt")
							if(key == "tile50" || key == "tile80" || key == "tile100") {
								var tile = 50;
								if(key == "tile80") {
									tile = 80;
								}
								if(key == "tile100") {
									tile = 100;
								}
								showLoaderIntoWrapId("tdt_list_phieuttpt_wrap");
								$.post("capnhattielethanhtoan", {
									sovaovien: rowSelected.SOVAOVIEN,
									sovaovien_dt: rowSelected.SOVAOVIEN_DT,
									madv: rowSelected.MA_DV,
									tile: tile,
									sophieu: rowSelected.SO_PHIEU_DICHVU,
									stt_dieutri: rowSelected.STT_DIEUTRI
								}).done(function() {
									notifiToClient("Green", "Cập nhật thành công!")
									$("#action_tdt_huyttpt").click()
								}).fail(function() {
									notifiToClient("Red", "Cập nhật thất bại!")
								}).always(function() {
									hideLoaderIntoWrapId("tdt_list_phieuttpt_wrap");
								})
							}
							if(key == "lieudung") {
								var lieudungload = rowSelected.GHICHU;
								jPrompt('Liều dùng:', ''+lieudungload, 'Thông tin', function (lieudung)
								{
									var sophieu = rowSelected.SO_PHIEU_DICHVU;
									var ghichu = lieudung;
									if (ghichu == '') {
										ghichu = ' '
									}
									var ma_dv = rowSelected.MA_DV;
									var arr = [sophieu, singletonObject.dvtt, ma_dv, ghichu];
									var url = "vlg_update_ghichu_ttpt_noitru";
									$.post(url, {
										url: convertArray(arr)
									}).done(function() {
										notifiToClient("Green", "Cập nhật thành công!");
									}).fail(function() {
										notifiToClient("Red", "Cập nhật thất bại!");
									}).always(function() {
										hideLoaderIntoWrapId("tdt_list_phieuttpt_wrap");
									})
								});
							}
							if(key == "xoa") {
								if (rowSelected.DA_CHAN_DOAN == 1) {
									return notifiToClient("Red", "Dịch vụ đã thực hiện không được xóa");
								}
								confirmToClient("Bạn có chắc chắn muốn chỉ định này?", function() {
									showLoaderIntoWrapId("tdt_list_phieuttpt_wrap")
									var arr = [rowSelected.SO_PHIEU_DICHVU,
										singletonObject.dvtt, rowSelected.MA_DV,
										rowSelected.STT_DIEUTRI,
										thongtinhsba.thongtinbn.STT_BENHAN,
										todieutriObject.STT_DOTDIEUTRI,
										rowSelected.SOVAOVIEN, rowSelected.SOVAOVIEN_DT];
									$.post("noitru_ttpt_delete_cacchitiet_svv", {
										url: convertArray(arr)
									}).done(function(data) {
										var arrMessage = [
											"Xóa thành công!",
											"Dịch vụ đã được thanh toán",
											"Dịch vụ đã được thực hiện",
											"Dịch vụ đã được tính tiền ekip",
										]
										if(data == 0) {
											var logTTPT = [
												"Thời gian y lệnh: " + rowSelected.NGAY_CHI_DINH + " " + rowSelected.GIO_CHI_DINH,
												"Tờ điều trị số: " + todieutriObject.STT_DIEUTRI,
												"Bác sĩ điều trị: "+ todieutriObject['TENBSDIEUTRI'],
												"Hình thức: "+ ($("#tdt_ttpt_hinhthuc").val() == 1? "BHYT": "Thu phí"),
												"Số phiếu thủ thuật/ phẩu thuật: "+ rowSelected.SO_PHIEU_DICHVU,
												"Danh sách CDHA: "+ rowSelected.MA_DV + " - " + rowSelected.TEN_DV + "; Số lượng: "+ rowSelected.SO_LUONG + " lần",
											]
											luuLogHSBATheoBN({
												SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
												LOAI: LOGHSBALOAI.TTPT.KEY,
												NOIDUNGBANDAU: logTTPT.join("; "),
												NOIDUNGMOI: "",
												USERID: singletonObject.userId,
												ACTION: LOGHSBAACTION.DELETE.KEY,
											})
											var urlXoarong = "ttpt_xoaphieurong_noitru";
											var arrXoarong = [singletonObject.dvtt, rowSelected.SOVAOVIEN, rowSelected.SOVAOVIEN_DT];
											$.post(urlXoarong, {
												url: convertArray(arrXoarong)
											});
										}
										notifiToClient(data == 0? "Green": "Red", arrMessage[data])
										enableModeButtonTTPT('add')
										$("#action_tdt_huyttpt").click();
									}).fail(function() {
										notifiToClient("Red", "Xóa thất bại!")
									}).always(function() {
										hideLoaderIntoWrapId("tdt_list_phieuttpt_wrap")
									})


								}, function () {

								})
							}
						},
						items: {
							"tile50": {name: '<p><i class="fa fa-money text-primary" aria-hidden="true"></i> Tỉ lệ thanh toán 50%</p>'},
							"tile80": {name: '<p><i class="fa fa-money text-primary" aria-hidden="true"></i> Tỉ lệ thanh toán 80%</p>'},
							"tile100": {name: '<p><i class="fa fa-money text-primary" aria-hidden="true"></i> Tỉ lệ thanh toán 100%</p>'},
							"lieudung": {name: '<p><i class="fa fa-edit text-primary" aria-hidden="true"></i> Liều dùng</p>'},
							"xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'}
						}
					});
				}
			},
		});
		listPhieuTTPT.jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
	}
}

function initGridICD() {
	var listICD = $("#tdt_list_icd")
	if(!listICD[0].grid) {
		listICD.jqGrid({
			url: '',
			datatype: "local",
			loadonce: true,
			height: 100,
			gridview: true,
			width: null,
			shrinkToFit: false,
			colModel: [
				{label: "ICD", name: 'ICD', index: 'ICD', width: 150, align: 'center'},
				{label: "TÊN ICD", name: 'TENBENH', index: 'TENBENH', width: 400},
				{
					label: "Bệnh chính",
					name: 'BENHCHINH',
					index: 'BENHCHINH_HT',
					width: 100,
					formatter: function (cellvalue, options, rowObject) {
						return cellvalue == 1? "X": "";
					},
					align: 'center'
				},
				{label: "BENHCHINH", name: 'BENHCHINH', index: 'BENHCHINH', width: 100, hidden: true},
				{label: "Ngày tạo", name: 'NGAYTAO', index: 'NGAYTAO',  width: 150},
				{label: "Người tạo", name: 'TEN_NHANVIEN', index: 'TEN_NHANVIEN',  width: 150},
				{label: "ID_DIEUTRI", name: 'ID_DIEUTRI', index: 'ID_DIEUTRI', width: 100, hidden: true},
				{name: 'NGUOITAO', index: 'NGUOITAO', width: 100, hidden: true},
				{label: "", name: 'ACT', index: 'ACT', width: 120,
					cellattr: function (rowId, val, rawObject, cm, rdata) {
						return 'style="text-align:center"';
					}
				},

			],
			caption: "Chẩn đoán",
			ignoreCase: true,
			rowNum: 1000000,
			ondblClickRow: function (id) {
			},
			onRightClickRow: function(id) {
				if (id) {
					$.contextMenu({
						selector: '#tdt_list_icd tr',
						reposition : false,
						callback: function (key, options) {
							var rowSelected = getThongtinRowSelected("tdt_list_icd")
							if(key == 'xoa') {
								if (rowSelected.NGUOITAO !== singletonObject.userId && singletonObject.userId != todieutriObject.TDT_NGUOILAP) {
									return notifiToClient("Red", "Bạn không quyền xóa chỉ định của người khác.");
								}
								confirmToClient('Bạn có chắc chắn muốn xóa ICD này?', function () {
									showLoaderIntoWrapId("tdt_list_icd_wrap");
									$.post("cmu_post", {
										url: [singletonObject.dvtt, rowSelected.ICD, rowSelected.ID_DIEUTRI , "CMU_TDT_ICD_DEL"].join("```")
									}).done(function () {
										luuLogHSBATheoBN({
											SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
											LOAI: LOGHSBALOAI.TODIEUTRI.KEY,
											NOIDUNGMOI: "",
											NOIDUNGBANDAU:  "Ngày giờ lập: " +todieutriObject.NGAYGIOLAPTDT
												+"; Số phiếu: "+ todieutriObject.STT_DIEUTRI + "; Chẩn đoán: "+ rowSelected.ICD + "-"+ rowSelected.TENBENH,
											USERID: singletonObject.userId,
											ACTION: LOGHSBAACTION.DELETE.KEY,
										})
										tdtLoadIcd();
									}).always(function () {
										hideLoaderIntoWrapId("tdt_list_icd_wrap");
									}).fail(function () {
										notifiToClient("Red", "Xóa ICD thất bại.");
									})
								}, function () {

								})
							}
							if(key == 'chinhsua') {
								if (rowSelected.NGUOITAO !== singletonObject.userId && singletonObject.userId != todieutriObject.TDT_NGUOILAP) {
									return notifiToClient("Red", "Bạn không quyền xóa chỉ định của người khác.");
								}
								showLoaderIntoWrapId("tdt_list_icd_wrap");
								$.post("cmu_post", {
									url: [singletonObject.dvtt, rowSelected.ICD, rowSelected.ID_DIEUTRI , "CMU_TDT_ICD_UPDPRI"].join("```")
								}).done(function () {
									luuLogHSBATheoBN({
										SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
										LOAI: LOGHSBALOAI.TODIEUTRI.KEY,
										NOIDUNGBANDAU: "",
										NOIDUNGMOI:  "Ngày giờ lập: " +todieutriObject.NGAYGIOLAPTDT
											+"; Số phiếu: "+ todieutriObject.STT_DIEUTRI + "; Chẩn đoán: "+ rowSelected.ICD + "-"+ rowSelected.TENBENH + " làm bệnh chính",
										USERID: singletonObject.userId,
										ACTION: LOGHSBAACTION.EDIT.KEY,
									})
									tdtLoadIcd();
								}).always(function () {
									hideLoaderIntoWrapId("tdt_list_icd_wrap");
								}).fail(function () {
									notifiToClient("Red", "Cập nhật thất bại.");
								})
							}
						},
						items: {
							"chinhsua": {name: '<p><i class="fa fa-key text-primary" aria-hidden="true"></i> Đặt làm bệnh chính</p>'},
							"xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'}
						}
					});

				}

			},
			loadComplete: function () {
				var ids = listICD.jqGrid('getDataIDs');
				for (var i = 0; i < ids.length; i++) {
					var cl = ids[i];
					listICD.jqGrid('setRowData', cl, {id: cl});
					listICD.jqGrid('setRowData', ids[i], {ACT: getButtonTDTICD(cl)});
				}
			}
		});
	}

}

function enableModeButtonTTPT(mode) {
	$("#tdt_ttpt_solan").val("1");
	$("#tdt_ttpt_cachphut").val("0");
	if(mode == 'add') {
		$("#tdt-ttpt-tab .add-ttpt").show()
		$("#tdt-ttpt-tab .edit-ttpt").hide()
	} else {
		$("#tdt-ttpt-tab .add-ttpt").hide()
		$("#tdt-ttpt-tab .edit-ttpt").show()
	}
}

function enableModeButtonXN(mode) {
	$("#tdt_xetnghiem_solan").val("1");
	$("#tdt_xetnghiem_cachphut").val("0");
	if(mode == 'add') {
		$("#tdt-xetnghiem-tab .add-xn").show()
		$("#tdt-xetnghiem-tab .edit-xn").hide()
	} else {
		$("#tdt-xetnghiem-tab .add-xn").hide()
		$("#tdt-xetnghiem-tab .edit-xn").show()
	}
}

function enableModeButtonCDHA(mode) {
	if(mode == 'add') {
		$("#tdt-chandoanhinhanh-tab .add-cdha").show()
		$("#tdt-chandoanhinhanh-tab .edit-cdha").hide()
	} else {
		$("#tdt-chandoanhinhanh-tab .add-cdha").hide()
		$("#tdt-chandoanhinhanh-tab .edit-cdha").show()
	}
}

function kiemtraicdtuongtacthuoc(maicd) {
	try {
		var res = $.ajax({
			url: 'cmu_getlist?url='+convertArray([maicd, todieutriObject.SOVAOVIEN,todieutriObject.ID_DIEUTRI, singletonObject.dvtt, 'CMU_KIEMTRA_ICDTHUOC_NOI']),
			typ: 'GET',
			async: false,
		}).responseText;
		var resJSON = JSON.parse(res);
		var stringTuongtac = '';
		var flag = false;
		resJSON.forEach(function(icd) {
			stringTuongtac += icd.HOATCHAT + ' Chống chỉ định với:  ' + icd.MA_ICD + (icd.TACDUNG? (' - '+icd.TACDUNG): '') + '; ';
			flag = true;
		})
		if(flag) {
			return notifiToClient("Red",stringTuongtac);
		}
	} catch(ex) {

	}
	return true;
}
function tdtLoadIcd() {
	var url = 'cmu_getlist?url=' + convertArray([singletonObject.dvtt,
		todieutriObject.SOVAOVIEN,
		todieutriObject.ID_DIEUTRI,
		"CMU_TDT_ICD_LIST"]);
	$("#tdt_list_icd").jqGrid('setGridParam', {
		datatype: 'json',
		url: url
	}).trigger('reloadGrid');
}

function tdtClearInputIcd() {
	$("#tdt_inputicd").val("");
	$("#tdt_inputten_icd").val("");
}

function xoaphieudieutri() {
	try {
		$.post("cmu_post_HSBA_KIEMTRA_TDT", {
			url: [singletonObject.dvtt,
				todieutriObject.SOVAOVIEN,
				todieutriObject.SOVAOVIEN_DT,
				todieutriObject.ID_DIEUTRI,
				thongtinhsba.thongtinbn.STT_BENHAN,
				todieutriObject.STT_DOTDIEUTRI,
				singletonObject.userId].join("```"),
		}).done(function(resTdt) {
			if(resTdt == -1) {
				var res = $.ajax({
					url: "cmu_list_HSBA_TONGYLENH_TDT?url="+convertArray([singletonObject.dvtt,
						todieutriObject.SOVAOVIEN,
						todieutriObject.SOVAOVIEN_DT,
						todieutriObject.ID_DIEUTRI]),
					method: "GET",
					async: false
				}).responseText
				var resJSON = JSON.parse(res);
				for(var k = 0; k < resJSON.length; k++) {
					var item = resJSON[k];
					if(item.LOAI == 'CDHA') {
						var url = "noitru_cdha_delete_bangcha";
						var arr = [item.SO_PHIEU_CDHA,
							singletonObject.dvtt, todieutriObject.STT_DIEUTRI,
							thongtinhsba.thongtinbn.STT_BENHAN, todieutriObject.STT_DOTDIEUTRI];
						var resCdha = $.ajax({
							url: url,
							method: "POST",
							async: false,
							data: {
								url: convertArray(arr)
							}
						}).responseText;
						if (resCdha == "RISFAIL") {
							$.post("noitru_cdha_delete_bangcha_his", {
								url: convertArray(arr)
							})
						}

					}
					if(item.LOAI == 'XN') {
						var urlxn = "noitru_xetnghiem_delete_bangcha";
						var arrxn = [todieutriObject.STT_DIEUTRI, thongtinhsba.thongtinbn.STT_BENHAN,
							todieutriObject.STT_DOTDIEUTRI, item.SO_PHIEU_CDHA, singletonObject.dvtt];
						$.ajax({
							url: urlxn,
							method: "POST",
							async: false,
							data: {
								url: convertArray(arrxn)
							}
						})

					}
					if(item.LOAI == 'TTPT') {
						var urlTtpt = "noitru_ttpt_delete_bangcha_svv";
						var arrTtpt = [ item.SO_PHIEU_CDHA, singletonObject.dvtt, todieutriObject.STT_DIEUTRI,
							thongtinhsba.thongtinbn.STT_BENHAN, todieutriObject.STT_DOTDIEUTRI, todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT];
						$.ajax({
							url: urlTtpt,
							method: "POST",
							async: false,
							data: {
								url: convertArray(arrTtpt)
							}
						})
					}
				}
				var urlToathuoc  = 'noitru_load_chitiet_toathuoctonghop?matt=' + todieutriObject.STT_DIEUTRI + "&dvtt="+singletonObject.dvtt+"&stt_benhan=" +
					thongtinhsba.thongtinbn.STT_BENHAN + "&stt_dotdieutri=" + todieutriObject.STT_DOTDIEUTRI
				var resToathuoc = $.ajax({
					url: urlToathuoc,
					method: "GET",
					async: false
				}).responseText;
				var resToathuocJSON = JSON.parse(resToathuoc);
				for(var i = 0; i< resToathuocJSON.lenth; i++) {
					var urlDel = "noitru_toathuoc_delete";
					var ret = resToathuocJSON[i];
					var nghiepvu = ret.NGHIEP_VU;

					var arrDelThuoc = [ret.STT_TOATHUOC, ret.MA_TOA_THUOC, singletonObject.dvtt,
						ret.MA_TOA_THUOC, thongtinhsba.thongtinbn.STT_BENHAN,
						todieutriObject.STT_DOTDIEUTRI, todieutriObject.SOPHIEUTHANHTOAN,
						ret.THANHTIEN_THUOC, ret.MAKHOVATTU, ret.DONGIA_BAN_BV, nghiepvu,
						ret.MAVATTU, ret.TEN_VAT_TU, ret.SO_LUONG];
					var resToathuoc = $.ajax({
						url: urlToathuoc,
						method: "POST",
						async: false,
						data: {
							url: convertArray(arrDelThuoc)
						}
					})
				}
				$.post("cmu_post_HSBA_XOA_TDT_BYUSER",{
					url: [
						singletonObject.dvtt,
						thongtinhsba.thongtinbn.STT_BENHAN,
						todieutriObject.STT_DOTDIEUTRI,
						todieutriObject.STT_DIEUTRI,
						todieutriObject.SOVAOVIEN,
						todieutriObject.SOVAOVIEN_DT,
						todieutriObject.ID_DIEUTRI,
						singletonObject.userId
					].join("```")
				}).done(function(data) {
					if(data == 1) {
						notifiToClient("Green", "Xóa phiếu điều trị thành công");
						todieutriObject = {};
						$("#modalTodieutri").modal("hide");
					} else {
						notifiToClient("Red", "Xóa thất bại, vui lòng kiểm tra thuốc, cận lâm sàng trên phiếu điều trị");
					}
					loadDsToDieutri();

				}).always(function() {
					hideLoaderIntoWrapId("hsba_list_todieutri");
					hideSelfLoading("action_xoatodieutri");
				})
			} else {
				hideLoaderIntoWrapId("hsba_list_todieutri");
				hideSelfLoading("action_xoatodieutri");
				var arrErrors = [
					"Không được xóa phiếu điều trị của người khác",
					"Thuốc đã được dự trù, duyệt hoặc hoàn trả, không được xóa",
					"Thuốc đã được thanh toán, không được xóa",
					"Xét nghiêm đã có kết quả, không được xóa",
					"Xét nghiêm đã thành toán, không được xóa",
					"Chẩn đoán đã có kết quả, không được xóa",
					"Chẩn đoán đã thanh toán, không được xóa",
					"Thủ thuật/phẫu thuật đã có kết quả, không được xóa",
					"Thủ thuật/phẫu thuật đã thanh toán, không được xóa",
					"Có y lệnh hủy cận lâm sàng/thuốc, không được xóa",
					"Phiếu điều trị có chỉ định suất ăn, vui lòng xóa suất ăn",
					"Phiếu điều trị có thực hiện phẫu thuật",
					"Có y lệnh truyền máu đã thưc hiện, không thể xóa",
				];
				notifiToClient("Red", arrErrors[resTdt]);
			}
		}).fail(function(err) {
			hideLoaderIntoWrapId("hsba_list_todieutri");
			hideSelfLoading("action_xoatodieutri");
		})
	} catch(e) {
		notifiToClient("Red", "Lỗi xóa phiếu điều trị");
		hideLoaderIntoWrapId("hsba_list_todieutri");
		hideSelfLoading("action_xoatodieutri");
	}

}

function getUrlToDieutriById(dataTDT) {

	var tenkhoa = "";
	singletonObject.danhsachphongban.forEach(function(obj) {
		if(obj.MAKHOA == dataTDT.KHOALAP) {
			tenkhoa = obj.TENKHOA;
		}
	})
	var arr = [singletonObject.dvtt,
		thongtinhsba.thongtinbn.TEN_BENH_NHAN,
		dataTDT.ID_DIEUTRI,
		dataTDT.SOVAOVIEN,
		dataTDT.SOVAOVIEN_DT,
		tenkhoa,
		thongtinhsba.thongtinbn.TUOI > 0 ? thongtinhsba.thongtinbn.TUOI :
			(thongtinhsba.thongtinbn.THANG > 0?  (thongtinhsba.thongtinbn.THANG+ " Tháng"): (thongtinhsba.thongtinbn.NGAY + " Ngày")),
		thongtinhsba.thongtinbn.GIOI_TINH_HT,
		thongtinhsba.thongtinbn.SOBENHAN,
		"/WEB-INF/pages/camau/reports/rp_todieutri_tong_v2_sub.jasper"

	];
	var param = ['dvtt', 'hovaten', 'id_dieutri', 'sovaovien', 'sovaovien_dt', "ten_phongban",
		"tuoi", "gioitinh", "sobenhan", "FILE_1"];
	var url = "cmu_injasper?url=" + convertArray(arr) + "&param=" + convertArray(param)
		+ "&loaifile=pdf&jasper=rp_todieutri_v2";
	return url;
}

function checkThanhtoanXetnghiem(SO_PHIEU_XN, dataBN) {
	var res = $.ajax({
		url: 'cmu_post',
		method: "POST",
		async: false,
		data: {
			url: [
				singletonObject.dvtt,
				SO_PHIEU_XN,
				'XN',
				todieutriObject.SOVAOVIEN,
				'CMU_KIEMTRACLSTHANHTOAN'
			].join('```')
		}
	}).responseText;
	return res;
}

function checkThanhtoanCDHA(SO_PHIEU_CDHA, dataBN) {
	var res = $.ajax({
		url: 'cmu_post',
		method: "POST",
		async: false,
		data: {
			url: [
				singletonObject.dvtt,
				SO_PHIEU_CDHA,
				'CDHA',
				todieutriObject.SOVAOVIEN,
				'CMU_KIEMTRACLSTHANHTOAN'
			].join('```')
		}
	}).responseText;
	return res;
}

function checkThanhtoanTTPT(SO_PHIEU_DICHVU, dataBN) {
	var res = $.ajax({
		url: 'cmu_post',
		method: "POST",
		async: false,
		data: {
			url: [
				singletonObject.dvtt,
				SO_PHIEU_DICHVU,
				'TTPT',
				todieutriObject.SOVAOVIEN,
				'CMU_KIEMTRACLSTHANHTOAN'
			].join('```')
		}
	}).responseText;
	return res;
}

function loadKhothuocmacdinh() {
	if(localStorage.getItem($("#tdt_nghiepvutoathuoc").val())) {
		$("#tdt_khothuoc").val(localStorage.getItem($("#tdt_nghiepvutoathuoc").val()));
		$("#tdt_khothuoc").trigger("change");
	}
}
$(function() {
	var keyLogTodieutri = {
		"NGAYGIOLAPTDT": "Ngày giờ lập",
		"TDT_DIENBIENBENH": "Diễn biến",
		"TDT_YLENH": "Y lệnh",
		"MACH": "Mạch",
		"NHIPTHO": "Nhịp thở",
		"CHIEUCAO": "Chiều cao",
		"NHIETDO": "Nhiệt độ",
		"CANNANG": "Cân nặng",
		"HUYETAPTREN": "Huyết áp tâm trương",
		"HUYETAPDUOI": "Huyết áp tâm thu",
	}
	if (singletonObject.tsMaBenhYHCT === "1") {
		//$("#tdt_input_icd_yhct").show();
	} else {
		//$("#tdt_input_icd_yhct").hide();
	}

	$("#ghichucdha_luu").click(function() {
		var ret = getThongtinRowSelected("tdt_list_phieucdha");
		$.post("cmu_post", {url:[ret.SO_PHIEU_CDHA, ret.MA_CDHA, $('#ghichucdha').val(),
				"CMU_GHICHUCDHA_INS"].join("```")}).done(function (data) {
			if(data > 0){
				$("#modalFormGhiChuCDHA").modal("hide");
				notifiToClient("Green",MESSAGEAJAX.SUCCESS);
				loadDSPhieuCDHA();
			} else {
				notifiToClient("Red",MESSAGEAJAX.ERROR);
			}
		}).fail(function(error) {
			notifiToClient("Red",MESSAGEAJAX.ERROR);
		}).always(function() {

		});
	});

	$("#ttin_dieutri_showkhamchuyenkhoa-dropdown p").click(function() {
		var dataId = $(this).attr("data-id");
		if(dataId == 'todieutritong' ) {
			var tenkhoa = "";
			singletonObject.danhsachphongban.forEach(function(obj) {
				if(obj.MAKHOA == singletonObject.makhoa) {
					tenkhoa = obj.TENKHOA;
				}
			})
			var arr = [singletonObject.dvtt,
				thongtinhsba.thongtinbn.TEN_BENH_NHAN,
				0,
				thongtinhsba.thongtinbn.SOVAOVIEN,
				thongtinhsba.thongtinbn.SOVAOVIEN_DT,
				tenkhoa,
				thongtinhsba.thongtinbn.TUOI > 0 ? thongtinhsba.thongtinbn.TUOI :
					(thongtinhsba.thongtinbn.THANG > 0?  (thongtinhsba.thongtinbn.THANG+ " Tháng"): (thongtinhsba.thongtinbn.NGAY + " Ngày")),
				thongtinhsba.thongtinbn.GIOI_TINH_HT,
				thongtinhsba.thongtinbn.SOBENHAN,
				$("#ttin_dieutri_bs").val(),
				$("#ttin_dieutri_khoa").val(),
				thongtinhsba.thongtinbn.STT_BENHAN,
				"/WEB-INF/pages/camau/reports/rp_todieutri_tong_v2_sub.jasper",
				singletonObject.user

			];
			var param = ['dvtt', 'hovaten', 'id_dieutri', 'sovaovien', 'sovaovien_dt',
				"ten_phongban", "tuoi", "gioitinh", "sobenhan", "userid", "makhoa", "stt_benhan", "FILE_1", "nguoiin"];
			var url = "cmu_injasper?url=" + convertArray(arr) + "&param="
				+ convertArray(param) + "&loaifile=pdf&jasper=rp_todieutri_tong_v2";
			previewPdfDefaultModal(url,'tdt-tong-iframe-preview');
		}
		if(dataId == 'moikham') {
			$("#modalDSMoikham").modal("show")
		}
	})
	$("#hsba-thongtindieutri-tab").click(function() {
		$("#ttin_dieutri_btnlammoi").click()
		$("#todieutri").click()
	})
	$("#action_tdt_themoixn").click(function() {
		try {
			clearToolbarJqgrid("tdt_list_xetnghiem");
			var dataBN = thongtinhsba.thongtinbn;
			var idButton = "action_tdt_themoixn"
			var ngaychidinh = convertStr_MysqlDate($("#tdt_ngaygiopdt").val().split(" ")[0]);

			var arr = [todieutriObject.STT_DIEUTRI, dataBN.STT_BENHAN, dataBN.STT_DOTDIEUTRI,
				singletonObject.dvtt,
				$("#tdt_xn_phongxetnghiem").val(),
				todieutriObject.BACSIDIEUTRI,
				singletonObject.userId, singletonObject.maphongbenh,
				$("#tdt_xn_hinhthuc").val() == 0, todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT,
				ngaychidinh,
				dataBN.MA_BENH_NHAN, $("#tdt_xn_trangthai").val()];
			if(!isValidTime($("#tdt_xn_giochidinh").val())) {
				return notifiToClient("Red", "Thời gian không đúng, vui chọn lại");
			}
			showSelfLoading(idButton)
			var resCheck = checkXetnghiem("action_tdt_themoixn", " ");
			if(resCheck === false) {
				hideSelfLoading(idButton)
				return resCheck;
			}
			if(resCheck != "") {
				return $.confirm({
					title: 'Xác nhận!',
					type: 'orange',
					content: resCheck  + '.Nhấn tiếp tục để chỉ định - Nhấn hủy để hủy bỏ chỉ định',
					buttons: {
						warning: {
							btnClass: 'btn-warning',
							text: "Tiếp tục",
							action: function(){
								insertXetnghiem(idButton, arr, dataBN);
							}
						},
						cancel: function () {
							hideSelfLoading(idButton)
						}
					}
				});
			}
			insertXetnghiem(idButton, arr, dataBN);
		} catch (e) {
			console.log("ex", e)
			notifiToClient("Red", "Có lỗi xảy ra vui lòng kiểm tra lại.")
			hideSelfLoading("action_tdt_themoixn")
		}

	})
	$("#action_tdt_luuxn").click(function() {
		var ret = getThongtinRowSelected("tdt_list_phieuxetnghiem")
		try {
			var idButton = "action_tdt_luuxn";
			clearToolbarJqgrid("tdt_list_xetnghiem");
			if(!ret.SO_PHIEU_XN) {
				return notifiToClient("Red", "Vui lòng chọn phiếu để chỉnh sửa.")
			}
			if(!isValidTime($("#tdt_xn_giochidinh").val())) {
				return notifiToClient("Red", "Thời gian không đúng, vui chọn lại");
			}
			showSelfLoading(idButton);
			var dataBN = thongtinhsba.thongtinbn;
			if(checkThanhtoanXetnghiem(ret.SO_PHIEU_XN, dataBN) == 1) {
				hideSelfLoading(idButton);
				return notifiToClient("Red", "Phiếu xét nghiệm đã được thanh toán, không thể cập nhật.")
			}

			var resCheck = checkXetnghiem("action_tdt_luuxn", ret.SO_PHIEU_XN);
			if(resCheck === false) {
				hideSelfLoading(idButton);
				return resCheck;
			}
			if(resCheck != "") {
				return $.confirm({
					title: 'Xác nhận!',
					type: 'orange',
					content: resCheck  + '.Nhấn tiếp tục để chỉ định - Nhấn hủy để hủy bỏ chỉ định',
					buttons: {
						warning: {
							btnClass: 'btn-warning',
							text: "Tiếp tục",
							action: function(){
								doUpateXetnghiem(ret, dataBN)
							}
						},
						cancel: function () {
							hideSelfLoading(idButton)
						}
					}
				});
			}
			doUpateXetnghiem(ret, dataBN);

		} catch (e) {
			notifiToClient("Red", "Có lỗi xảy ra vui lòng kiểm tra lại.")
			hideSelfLoading(idButton)
		}
	})

	$("#action_tdt_huyxn").click(function() {
		loadDSXetnghiem("", thongtinhsba.thongtinbn);
		loadDSPhieuXetnghiem(thongtinhsba.thongtinbn)
		hideSelfLoading("action_tdt_themoixn")
		hideSelfLoading("action_tdt_luuxn")
		hideSelfLoading("action_tdt_xoaxn")
		hideSelfLoading("action_tdt_kysoxn")
		$("#tdt_xn_hinhthuc").prop("disabled", false);
		enableModeButtonXN('add')
	})

	$("#tdt_xn_hinhthuc").change(function() {
		loadDSXetnghiem("", thongtinhsba.thongtinbn);
	})

	$("#tdt_cdha_hinhthuc").change(function() {
		loadDSCDHA("", thongtinhsba.thongtinbn);
	})

	$("#tdt_cdha_phongcdha").change(function() {
		loadDSCDHA("", thongtinhsba.thongtinbn);
	})

	$("#action_tdt_themoicdha").click(function() {
		try {
			if(!isValidTime($("#tdt_cdha_giochidinh").val())) {
				return notifiToClient("Red", "Thời gian không đúng, vui chọn lại");
			}
			showSelfLoading("action_tdt_themoicdha")
			clearToolbarJqgrid("tdt_list_cdha");
			var resCheck = checkCDHA("action_tdt_themoicdha", " ");
			if(resCheck === false) {
				hideSelfLoading("action_tdt_themoicdha")
				return resCheck;
			}
			if(resCheck != "") {
				return $.confirm({
					title: 'Xác nhận!',
					type: 'orange',
					content: resCheck  + '.Nhấn tiếp tục để chỉ định - Nhấn hủy để hủy bỏ chỉ định',
					buttons: {
						warning: {
							btnClass: 'btn-warning',
							text: "Tiếp tục",
							action: function(){
								insertCDHA();
							}
						},
						cancel: function () {
							hideSelfLoading("action_tdt_themoicdha")
						}
					}
				});
			}
			insertCDHA();
		} catch (e) {
			notifiToClient("Red", "Có lỗi xảy ra vui lòng kiểm tra lại.")
			hideSelfLoading("action_tdt_themoicdha")
		}
	});

	$("#action_tdt_huycdha").click(function() {
		loadDSCDHA("", thongtinhsba.thongtinbn);
		loadDSPhieuCDHA(thongtinhsba.thongtinbn)
		hideSelfLoading("action_tdt_themoicdha")
		hideSelfLoading("action_tdt_luucdha")
		hideSelfLoading("action_tdt_xoacdha")
		hideSelfLoading("action_tdt_kysocdha")
		$("#tdt_cdha_hinhthuc").prop("disabled", false);
		$("#tdt_cdha_phongcdha").prop("disabled", false);
		enableModeButtonCDHA('add')
	})

	$("#action_tdt_luucdha").click(function() {
		var ret = getThongtinRowSelected("tdt_list_phieucdha")

		try {
			if(!ret.SO_PHIEU_CDHA) {
				return notifiToClient("Red", "Vui lòng chọn phiếu để chỉnh sửa.")
			}
			if(!isValidTime($("#tdt_cdha_giochidinh").val())) {
				return notifiToClient("Red", "Thời gian không đúng, vui chọn lại");
			}
			var dataBN = thongtinhsba.thongtinbn;
			showSelfLoading("action_tdt_luucdha")
			clearToolbarJqgrid("tdt_list_cdha");
			if(checkThanhtoanCDHA(ret.SO_PHIEU_CDHA, dataBN) == 1) {
				hideSelfLoading("action_tdt_luucdha")
				return notifiToClient("Red", "Phiếu CDHA đã được thanh toán, không thể cập nhật.")
			}

			var resCheck = checkCDHA("action_tdt_luucdha", ret.SO_PHIEU_CDHA);
			if(resCheck === false) {
				hideSelfLoading("action_tdt_luucdha")
				return resCheck;
			}
			if(resCheck != "") {
				return $.confirm({
					title: 'Xác nhận!',
					type: 'orange',
					content: resCheck  + '.Nhấn tiếp tục để chỉ định - Nhấn hủy để hủy bỏ chỉ định',
					buttons: {
						warning: {
							btnClass: 'btn-warning',
							text: "Tiếp tục",
							action: function(){
								doUpateCDHA(ret, dataBN)
							}
						},
						cancel: function () {
							hideSelfLoading("action_tdt_luucdha")
						}
					}
				});
			}
			doUpateCDHA(ret, dataBN);
		} catch (e) {
			notifiToClient("Red", "Có lỗi xảy ra vui lòng kiểm tra lại.")
			hideSelfLoading("action_tdt_luucdha")
		}
	})

	$("#action_tdt_themoittpt").click(function() {
		try {
			var idButton = "action_tdt_themoittpt";
			if(!isValidTime($("#tdt_ttpt_giochidinh").val())) {
				return notifiToClient("Red", "Thời gian không đúng, vui chọn lại");
			}
			showSelfLoading(idButton)
			clearToolbarJqgrid("tdt_list_ttpt");
			var resCheck = checkTTPT(idButton, " ");
			if(resCheck === false) {
				hideSelfLoading(idButton)
				return resCheck;
			}
			if(resCheck != "") {
				return $.confirm({
					title: 'Xác nhận!',
					type: 'orange',
					content: resCheck  + '.Nhấn tiếp tục để chỉ định - Nhấn hủy để hủy bỏ chỉ định',
					buttons: {
						warning: {
							btnClass: 'btn-warning',
							text: "Tiếp tục",
							action: function(){
								insertTTPT(idButton);
							}
						},
						cancel: function () {
							hideSelfLoading(idButton)
						}
					}
				});
			}
			insertTTPT(idButton);
		} catch (e) {
			console.log("ẽ", e);
			notifiToClient("Red", "Có lỗi xảy ra vui lòng kiểm tra lại.")
			hideSelfLoading(idButton)
		}
	})

	$("#action_tdt_luuttpt").click(function() {
		var ret = getThongtinRowSelected("tdt_list_phieuttpt")

		try {

			if(!ret.SO_PHIEU_DICHVU) {
				return notifiToClient("Red", "Vui lòng chọn phiếu để chỉnh sửa.")
			}
			if(!isValidTime($("#tdt_ttpt_giochidinh").val())) {
				return notifiToClient("Red", "Thời gian không đúng, vui chọn lại");
			}
			var idButton = "action_tdt_luuttpt";
			showSelfLoading(idButton);
			clearToolbarJqgrid("tdt_list_ttpt");
			var dataBN = thongtinhsba.thongtinbn;
			if(checkThanhtoanTTPT(ret.SO_PHIEU_DICHVU, dataBN) == 1) {
				hideSelfLoading(idButton);
				return notifiToClient("Red", "Phiếu xét nghiệm đã được thanh toán, không thể cập nhật.")
			}

			var resCheck = checkTTPT(idButton, ret.SO_PHIEU_DICHVU);
			if(resCheck === false) {
				hideSelfLoading(idButton)
				return resCheck;
			}
			if(resCheck != "") {
				return $.confirm({
					title: 'Xác nhận!',
					type: 'orange',
					content: resCheck  + '.Nhấn tiếp tục để chỉ định - Nhấn hủy để hủy bỏ chỉ định',
					buttons: {
						warning: {
							btnClass: 'btn-warning',
							text: "Tiếp tục",
							action: function(){
								doUpateTTPT(ret, dataBN)
							}
						},
						cancel: function () {
							hideSelfLoading(idButton)
						}
					}
				});
			}
			doUpateTTPT(ret, dataBN);

		} catch (e) {
			console.log("e", e)
			notifiToClient("Red", "Có lỗi xảy ra vui lòng kiểm tra lại.")
			hideSelfLoading(idButton)
		}
	})

	$("#action_tdt_huyttpt").click(function() {
		loadDSTTPT("", thongtinhsba.thongtinbn);
		loadDSPhieuTTPT(thongtinhsba.thongtinbn)
		hideSelfLoading("action_tdt_themoittpt")
		hideSelfLoading("action_tdt_luuttpt")
		hideSelfLoading("action_tdt_xoattpt")
		hideSelfLoading("action_tdt_kysottpt")
		$("#tdt_ttpt_hinhthuc").prop("disabled", false);
		$("#tdt_ttpt_phongttpt").prop("disabled", false);
		$("#tdt_ttpt_loaittpt").prop("disabled", false);
		enableModeButtonTTPT('add')
	})


	$("#tdt_ttpt_loaittpt").change(function() {
		loadDSTTPT("", thongtinhsba.thongtinbn)
	})

	$("#tdt_ttpt_hinhthuc").change(function() {
		loadDSTTPT("", thongtinhsba.thongtinbn)
	})

	$("#action_tdtclose").click(function() {
		$("#modalTodieutri").modal("hide");
	})
	$("#tdt_inputicd_search_yhct").keyup(function (evt) {
		if ($("#tdt_inputicd_search_yhct").val().length == 1) {
			$("#tdt_inputicd_search_yhct").val($("#tdt_inputicd_search_yhct").val().toString().toUpperCase());
		}
		if (evt.keyCode == 13) {
			var _searchMaBenhYhctValue = $("#tdt_inputicd_search_yhct").val().toUpperCase();
			if (_searchMaBenhYhctValue != "") {
				var url = "laymotabenhly_yhct?icd_yhct=" + _searchMaBenhYhctValue;
				$.ajax({
					url: url
				}).done(function (data) {
					if (data.trim() != "") {
						var arr = data.split("!!!");
						var strICD = ";" + _searchMaBenhYhctValue; //";icdYhct"
						var strMaBenhYhct_kb = ";" + $("#tdt_input_icd_ma_yhct").val(); //";i10;i11;i12;<icdYhct>"
						if (strMaBenhYhct_kb.indexOf(strICD) < 0) {
							$("#tdt_input_icd_ma_yhct").val($("#tdt_input_icd_ma_yhct").val() + ";" + _searchMaBenhYhctValue);
							var e = jQuery.Event("keyup");
							e.keyCode = 13; // # Some key code value
							$("#tdt_inputicd").val(arr[1]).trigger(e);
							/////////
							var icd = $("#tdt_inputicd").val();
							showLoaderIntoWrapId("tdt_list_icd_wrap");
							var url = "laymotabenhly?icd=" + icd;
							if(!kiemtraicdtuongtacthuoc(icd.trim().toUpperCase())) {
								hideLoaderIntoWrapId("tdt_list_icd_wrap");
								return false;
							}
							$.ajax({
								url: url
							}).done(function (data) {
								if (data.trim() == "") {
									return notifiToClient("Red", "Không tìm thấy mã ICD")
								}
								$("#tdt_inputten_icd").val(data.split("!!!")[1].trim());
								var tenicd = $("#tdt_inputten_icd").val();

								if(tenicd.trim() == "") {
									return notifiToClient("Red", "Vui lòng nhập tên ICD");
								}
								showLoaderIntoWrapId("tdt_list_icd_wrap");
								$.post("cmu_post", {
									url: [singletonObject.dvtt,
										todieutriObject.SOVAOVIEN,
										todieutriObject.SOVAOVIEN_DT,
										todieutriObject.ID_DIEUTRI,
										$("#tdt_inputicd").val(),
										tenicd.replaceAll("%", "encode_symbol_0025"),
										singletonObject.userId,
										"CMU_TDT_ICD_INS"].join("```")
								}).done(function (data) {
									if(data == 1) {
										return notifiToClient("Red", "Không tìm thấy ICD")
									}
									if(data == 2) {
										return notifiToClient("Red", "ICD đã tồn tại")
									}
									luuLogHSBATheoBN({
										SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
										LOAI: LOGHSBALOAI.TODIEUTRI.KEY,
										NOIDUNGBANDAU: "",
										NOIDUNGMOI: keyLogTodieutri.NGAYGIOLAPTDT + ": " +todieutriObject.NGAYGIOLAPTDT
											+"; Số phiếu: "+ todieutriObject.STT_DIEUTRI + "; Chẩn đoán: "+ $("#tdt_inputicd").val() + "-"+tenicd,
										USERID: singletonObject.userId,
										ACTION: LOGHSBAACTION.INSERT.KEY,
									})
									tdtLoadIcd();
									tdtClearInputIcd();
								}).always(function(){
									hideLoaderIntoWrapId("tdt_list_icd_wrap");
								})
							}).always(function() {
								hideLoaderIntoWrapId("tdt_list_icd_wrap");
							})
							////////
							$("#tdt_inputicd_search_yhct").val("");
							$("#tdt_inputicd_search_yhct").focus();
						}
					} else {

					}
				});
			}
		}
	});
	$("#tdt_inputicd").keypress(function(e) {
		var icd = $("#tdt_inputicd").val();
		if(e.keyCode == 13 && icd.trim() != "") {
			showLoaderIntoWrapId("tdt_list_icd_wrap");
			var url = "laymotabenhly?icd=" + icd;
			if(!kiemtraicdtuongtacthuoc(icd.trim().toUpperCase())) {
				hideLoaderIntoWrapId("tdt_list_icd_wrap");
				return false;
			}
			$.ajax({
				url: url
			}).done(function (data) {
				if (data.trim() == "") {
					return notifiToClient("Red", "Không tìm thấy mã ICD")
				}
				$("#tdt_inputten_icd").val(data.split("!!!")[1].trim());
				$("#tdt_inputten_icd").focus();

			}).always(function() {
				hideLoaderIntoWrapId("tdt_list_icd_wrap");
			})
		}
	})
	$("#tdt_inputten_icd").keypress(function(e) {
		var tenicd = $("#tdt_inputten_icd").val();

		if(e.keyCode == 13) {
			if(tenicd.trim() == "") {
				return notifiToClient("Red", "Vui lòng nhập tên ICD");
			}
			showLoaderIntoWrapId("tdt_list_icd_wrap");
			$.post("cmu_post", {
				url: [singletonObject.dvtt,
					todieutriObject.SOVAOVIEN,
					todieutriObject.SOVAOVIEN_DT,
					todieutriObject.ID_DIEUTRI,
					$("#tdt_inputicd").val(),
					tenicd.replaceAll("%", "encode_symbol_0025"),
					singletonObject.userId,
					"CMU_TDT_ICD_INS"].join("```")
			}).done(function (data) {
				if(data == 1) {
					return notifiToClient("Red", "Không tìm thấy ICD")
				}
				if(data == 2) {
					return notifiToClient("Red", "ICD đã tồn tại")
				}
				luuLogHSBATheoBN({
					SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
					LOAI: LOGHSBALOAI.TODIEUTRI.KEY,
					NOIDUNGBANDAU: "",
					NOIDUNGMOI: keyLogTodieutri.NGAYGIOLAPTDT + ": " +todieutriObject.NGAYGIOLAPTDT
						+"; Số phiếu: "+ todieutriObject.STT_DIEUTRI + "; Chẩn đoán: "+ $("#tdt_inputicd").val() + "-"+tenicd,
					USERID: singletonObject.userId,
					ACTION: LOGHSBAACTION.INSERT.KEY,
				})

				tdtLoadIcd();
				tdtClearInputIcd();

			}).always(function(){
				hideLoaderIntoWrapId("tdt_list_icd_wrap");
			})
		}
	})

	$("#action_xoatodieutri").click(function() {
		var confirmModal = $.confirm({
			title: 'Xác nhận!',
			type: 'orange',
			content: "Bạn có chắc chắn muốn xóa phiếu điều trị này?",
			buttons: {
				warning: {
					btnClass: 'btn-warning',
					text: "Tiếp tục",
					action: function(){
						confirmModal.close();
						showSelfLoading("action_xoatodieutri");
						xoaphieudieutri();
					}
				},
				cancel: function () {
					hideSelfLoading("action_xoatodieutri")
				}
			}
		});
	})
	$("#tdt-previewall").click(function() {
		getFilesign769(
			"TODIEUTRI_NOITRU",
			todieutriObject.ID_DIEUTRI,
			singletonObject.userId,
			singletonObject.dvtt,
			thongtinhsba.thongtinbn.SOVAOVIEN,
			todieutriObject.SOVAOVIEN_DT ,
			-1,
			function(data) {
				if(data.length > 0) {
					var url = "smartca-get-signed-file-minio?keyminio=" + data[0].KEYMINIO + "&type=pdf" ;
					$.ajax({
						method: "POST", url: url, contentType: "charset=utf-8"
					}).done(function (data) {
						var pdf = 'data:application/pdf;base64,' +data.FILE;
						previewPdfIframeLoading(pdf,"tdt-iframe-wrap", 'tdt-iframe')

					}).fail(function() {
						notifiToClient("red", "Không tìm thấy file đã ký số");
					});
				} else {

					previewPdfIframeLoading(getUrlToDieutriById(todieutriObject),"tdt-iframe-wrap", 'tdt-iframe')
				}
			})
	})


	$("#action_luutodieutri").click(function() {
		var idButton = this.id;
		try {
			showSelfLoading(idButton)
			if(!$("#ttdtFormthongtinsinhhieu").valid()){
				hideSelfLoading(idButton)
				return;
			}
			var dataSinhhieu = convertDataFormToJson('ttdtFormthongtinsinhhieu');
			var bmi = "";
			if(dataSinhhieu.tdt_inputcannang  && dataSinhhieu.tdt_inputchieucao) {
				bmi = (dataSinhhieu.tdt_inputcannang / ((dataSinhhieu.tdt_inputchieucao * dataSinhhieu.tdt_inputchieucao) / 100)).toFixed(2);
			}
			var sosanhngaynv = $.ajax({
				url: "cmu_post",
				method: "POST",
				data: {
					url: [
						singletonObject.dvtt,
						todieutriObject.SOVAOVIEN,
						todieutriObject.SOVAOVIEN_DT,
						$("#tdt_ngaygiopdt").val(),
						'CMU_CHECK_NGAYDTNHAPVIEN'].join("```")
				},
				async: false,
			}).responseText;
			if(sosanhngaynv == 1) {
				hideSelfLoading(idButton)
				return notifiToClient("Red", "Ngày điều trị không được nhỏ hơn ngày bắt đầu đợt điều trị.");
			}
			var unqtdtngaylap = $.ajax({
				url: "cmu_post",
				method: "POST",
				data: {
					url: [singletonObject.dvtt, todieutriObject.SOVAOVIEN, $("#tdt_ngaygiopdt").val(), todieutriObject.ID_DIEUTRI, 'CMU_CHECK_UNQNGAYLAPTDT_ID'].join("```")
				},
				async: false,
			}).responseText;
			if(unqtdtngaylap > 0) {
				hideSelfLoading(idButton)
				return notifiToClient("Red", "Ngày lập đã tồn tại");
			}
			if(moment($("#tdt_ngaygiopdt").val(), ['DD/MM/YYYY HH:mm']).format("DD/MM/YYYY")
				!= moment(todieutriObject.NGAYGIO, ['DD/MM/YYYY HH:mm']).format("DD/MM/YYYY")) {
				var ktracothuoc = $.ajax({
					url: "cmu_post",
					method: "POST",
					data: {
						url: [
							singletonObject.dvtt,
							thongtinhsba.thongtinbn.STT_BENHAN,
							thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
							todieutriObject.ID_DIEUTRI,
							'CMU_CHECK_THUOCCLS'].join("```")
					},
					async: false,
				}).responseText;
				if(ktracothuoc > 0) {
					hideSelfLoading(idButton)
					return notifiToClient("Red", "Không thể thay đổi ngày điều trị do có thuốc/vật tư, cận lâm sàng đã được chỉ định");
				}
			}
			$.post("cmu_post", {
				url: [singletonObject.dvtt,
					todieutriObject.ID_DIEUTRI,
					todieutriObject.SOVAOVIEN,
					dataSinhhieu.tdt_inputmach,
					dataSinhhieu.tdt_inputnhiptho,
					dataSinhhieu.tdt_inputchieucao,
					dataSinhhieu.tdt_inputnhietdo,
					dataSinhhieu.tdt_inputhuyetap_tren,
					dataSinhhieu.tdt_inputhuyetap_duoi,
					dataSinhhieu.tdt_inputcannang,
					dataSinhhieu.tdt_inputspo2,
					bmi,
					$("#tdt_ngaygiopdt").val(),
					$("#tdt_dienbien").val().trim(),
					$("#tdt_ylenh").val(),
					singletonObject.userId,
					thongtinhsba.thongtinbn.STT_BENHAN,
					todieutriObject.STT_DOTDIEUTRI,
					$("#tdt_giaidoanbenh").val(),
					$("#tdt_input_icd_ma_yhct").val(),
					"HSBA_DIEUTRI_UPDATE_V4"].join("```")
			}).done(function () {
				var objectBandau = {};
				var objectmoi = {
					...dataSinhhieu,
					TDT_YLENH: $("#tdt_ylenh").val(),
					TDT_DIENBIENBENH: $("#tdt_dienbien").val(),
				}
				Object.keys(keyLogTodieutri).forEach(function(key) {
					objectBandau[key] = todieutriObject[key]? todieutriObject[key] : "";
				})
				var diffObject = findDifferencesBetweenObjects(objectBandau, objectmoi);
				var Logbandau = ["Bác sĩ điều trị: " + todieutriObject['TENBSDIEUTRI'], "Số phiếu: "+ todieutriObject['STT_DIEUTRI']];
				var Logmoi = ["Bác sĩ điều trị: " + todieutriObject['TENBSDIEUTRI'], "Số phiếu: "+ todieutriObject['STT_DIEUTRI']];
				if(!diffObject['NGAYGIOLAPTDT']) {
					Logbandau.push(keyLogTodieutri['NGAYGIOLAPTDT'] + ": " + todieutriObject['NGAYGIOLAPTDT'])
					Logmoi.push(keyLogTodieutri['NGAYGIOLAPTDT'] + ": " + todieutriObject['NGAYGIOLAPTDT'])
				}
				for (let key in diffObject) {
					if (keyLogTodieutri.hasOwnProperty(key)) {
						Logbandau.push( keyLogTodieutri[key] + ": " + _.get(todieutriObject, key, ''))
						Logmoi.push(keyLogTodieutri[key] + ": " + _.get(objectmoi, key, ''))
					}
				}
				luuLogHSBATheoBN({
					SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
					LOAI: LOGHSBALOAI.TODIEUTRI.KEY,
					NOIDUNGBANDAU: Logbandau.join(";"),
					NOIDUNGMOI: Logmoi.join(";"),
					USERID: singletonObject.userId,
					ACTION: LOGHSBAACTION.EDIT.KEY,
				})
				notifiToClient("Green", "Lưu thành công.");
			}).always(function () {
				hideSelfLoading(idButton)
			})

		} catch (e) {
			console.log("e", e)
			hideSelfLoading(idButton)
			notifiToClient("Red", "Có lỗi xảy ra vui lòng kiểm tra lại.")
		}
	})

	$("#ttin_dieutri_btnlammoi").click(function() {
		loadDsToDieutri()
	})

	$("#ttin_dieutri_showkhambenh").click(function() {
		$("#khoabs").val(singletonObject.makhoa).trigger("change");
		var time = moment().format('HH') + ":"+moment().format('mm');
		$("#tdt_xn_giochidinh").val(time);
		$("#tdt_cdha_giochidinh").val(time);
		$("#tdt_ttpt_giochidinh").val(time);
		$("#tdt_ngaygiopdt_date").val(moment().format('DD/MM/YYYY HH:mm'));
		$("#tdt-dieutri").click()
		addTextTitleModal("titleModalFormKhambenh", "Thời gian bắt đầu đợt điều trị: "+ thongtinhsba.thongtinbn.NGAY_VAO_VIEN + " "+ thongtinhsba.thongtinbn.GIO_VAO_VIEN )
		$("#modalFormKhambenh").modal("show");
	})
	$("#tdt-khoabs-change").change(function() {
		loadBsTheokhoaDieutri('tdt-bacsidieutri-change', $("#tdt-khoabs-change").val(), false, singletonObject.userId);
	})

	$("#tdt-thaydoibsdieutri-luu").click(function() {
		var idButton = this.id;
		var mabs = $("#tdt-bacsidieutri-change").val()
		if(!mabs) {
			return notifiToClient("Red","Vui lòng chọn Bác sĩ điều trị")
		}
		showSelfLoading(idButton)
		try {
			var rowData = getThongtinRowSelected("list_todieutri")
			$.post("cmu_post", {
				url: [singletonObject.dvtt, rowData.SOVAOVIEN, rowData.ID_DIEUTRI, mabs, "HSBA_DIEUTRI_UPDATE_BSDIEUTRI"].join("```")
			}).done(function () {
				luuLogHSBATheoBN({
					SOVAOVIEN: rowData.SOVAOVIEN,
					LOAI: "TODIEUTRI",
					NOIDUNGBANDAU: "Tờ điều trị số: "+ rowData.STT_DIEUTRI + " - Bác sĩ điều trị: " + rowData.TEN_NHANVIEN + " - Ngày điều trị: " + rowData.NGAYGIO,
					NOIDUNGMOI: "Thay đổi bác sĩ điều trị từ: "+ rowData.TEN_NHANVIEN +" thành : "+ $("#tdt-bacsidieutri-change option:selected").text(),
					USERID: singletonObject.userId,
				})
				loadDsToDieutri();
				notifiToClient("Green","Lưu thành công")

			}).fail(function() {
				notifiToClient("Red","Lỗi lưu thông tin")
			}).always(function() {
				$("#modalThaydoibsdieutri").modal("hide")
				hideSelfLoading(idButton)
			})
		} catch (e) {
			notifiToClient("Red","Lỗi lưu thông tin")
			hideSelfLoading(idButton)
		}
	})

	$('#modalTodieutri').on('hidden.bs.modal', function () {
		loadDsToDieutri();
	});

	$.validator.addMethod("kiemtrangaycls", function(value, element) {
		var thoigian = $(element).val();
		if(!isValidDateTime(thoigian)) return true;
		if(moment(thoigian, ['DD/MM/YYYY HH:mm']).format("DD/MM/YYYY") == moment(todieutriObject.NGAYGIO, ['DD/MM/YYYY HH:mm']).format("DD/MM/YYYY")) return true;
		var res = $.ajax({
			url: "cmu_post_CMU_VALID_NGAYDIEUTRI",
			method: "POST",
			async: false,
			data: {
				url: [
					singletonObject.dvtt,
					todieutriObject.ID_DIEUTRI,
					thongtinhsba.thongtinbn.STT_BENHAN,
					todieutriObject.STT_DOTDIEUTRI,
					thoigian.split(" ")[0],
				].join("```")
			}
		}).responseText;
		return res == 0;
	}, "Không được thay đổi ngày điều trị, do phiếu chỉ định cân lâm sàng đã được thực hiện.");

	$.extend({
		logXoaCDHATodieutri: function (ret) {
			var logCDHA = [
				"Thời gian y lệnh: " + ret.NGAY_CHI_DINH + " " + ret.GIO_CHI_DINH,
				"Tờ điều trị số: " + todieutriObject.STT_DIEUTRI,
				"Bác sĩ điều trị: "+ todieutriObject['TENBSDIEUTRI'],
				"Hình thức: "+ ($("#tdt_cdha_hinhthuc").val() == 0? "BHYT": "Thu phí"),
				"Số phiếu CDHA: "+ ret.SO_PHIEU_CDHA,
				"Danh sách CDHA: "+ ret.MA_CDHA + " - " + ret.TEN_CDHA + "; Số lượng: "+ ret.SO_LUONG + " lần",
			]
			luuLogHSBATheoBN({
				SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
				LOAI: LOGHSBALOAI.CDHA.KEY,
				NOIDUNGBANDAU: logCDHA.join("; "),
				NOIDUNGMOI: "",
				USERID: singletonObject.userId,
				ACTION: LOGHSBAACTION.DELETE.KEY,
			})
		}
	})
	$("#ttdtFormthongtinsinhhieu").validate({
		rules: {
			tdt_ngaygiopdt:{
				validDateTime: true,
				kiemtrangaycls: true,
				required: true
			},
			tdt_inputmach: {
				range: [0,300]
			},
			tdt_inputnhiptho: {
				range: [0,300]
			},
			tdt_inputchieucao: {
				min: [10,300],
			},
			tdt_inputnhietdo: {
				range: [35,43]
			},
			tdt_inputhuyetap_tren: {
				range: [0, 300]
			},
			tdt_inputhuyetap_duoi: {
				range: [0, 300]
			},
			tdt_inputcannang: {
				range: [0.1, 300]
			}
		}
	})

	function doUpateXetnghiem(ret, dataBN) {
		deleteXetnghiemCT(ret.SO_PHIEU_XN, dataBN)
		insertXetnghiemCT(ret.SO_PHIEU_XN, $("#tdt_xn_giochidinh").val(), dataBN, "action_tdt_luuxn");
		$("#tdt_xn_hinhthuc").prop("disabled", false);
		enableModeButtonXN('add')
	}

	function checkXetnghiem(idButton, SO_PHIEU_XN) {
		var maXNs = "";
		var maXNArray = [];
		var listRowSelected = $("#tdt_list_xetnghiem").jqGrid('getGridParam', 'selarrrow');
		if($("#tdt_xn_giochidinh").val() == "") {
			notifiToClient("Red", "Vui lòng nhập giờ chỉ định");
			return false;
		}
		var ngaygiochidinh = $("#tdt_ngaygiopdt").val().split(" ")[0] + " " + $("#tdt_xn_giochidinh").val();
		var thoigianbatdaudieutri =  thongtinhsba.thongtinbn.NGAY_VAO_VIEN + " "+ thongtinhsba.thongtinbn.GIO_VAO_VIEN;
		var momentNgaygioCD = moment(ngaygiochidinh, ['DD/MM/YYYY HH:mm']);
		if(ngaygiochidinh == moment(thoigianbatdaudieutri, ['DD/MM/YYYY HH:mm:ss']).format("DD/MM/YYYY HH:mm")) {
			notifiToClient("Red", `Giờ chỉ định phải sau giờ nhập viện ${thoigianbatdaudieutri}, vui lòng kiểm tra lại`);
			return false;
		}
		if(momentNgaygioCD.isBefore(moment(thongtinhsba.thongtinbn.THOIGIANKHAMBENH, ['DD/MM/YYYY HH:mm']))) {
			notifiToClient("Red", `Giờ chỉ định phải sau giờ khám ${thongtinhsba.thongtinbn.THOIGIANKHAMBENH}, vui lòng kiểm tra lại`);
			return false;
		}
		if(momentNgaygioCD.isBefore(moment(thoigianbatdaudieutri, ['DD/MM/YYYY HH:mm:ss']))) {
			notifiToClient("Red", "Giờ chỉ định trước giờ vào viện, vui lòng kiểm tra lại");
			return false;
		}
		if(listRowSelected.length == 0) {
			notifiToClient("Red", "Vui chọn xét nghiệm");
			return false;
		}
		var ktradathuchien = $.ajax({
			url: "cmu_post_CMU_CHECKCLSXN_THUCHIEN",
			method: "POST",
			data: {
				url: [singletonObject.dvtt, todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT, SO_PHIEU_XN].join("```")
			},
			async: false,
		}).responseText;
		if(ktradathuchien > 0) {
			notifiToClient("Red", "Xét nghiệm đã được thực hiện không thể cập nhật");
			return false;
		}
		listRowSelected.forEach(function(value) {
			var ret = $("#tdt_list_xetnghiem").jqGrid('getRowData', value);
			maXNs = maXNs + "!" + ret.MA_XN + "!";
			maXNArray.push(ret.MA_XN);
		})
		var dataBN = thongtinhsba.thongtinbn;
		var ngaychidinh = convertStr_MysqlDate($("#tdt_ngaygiopdt").val().split(" ")[0]);
		showSelfLoading(idButton);

		// var resKiemtrathoihan = $.ajax({
		// 	url: "notru_xetnghiem_kiemtra_thoihan",
		// 	method: "POST",
		// 	data: {
		// 		ma_xn: maXNArray.join(","),
		// 		dvtt: singletonObject.dvtt,
		// 		mabenhnhan: dataBN.MA_BENH_NHAN,
		// 		ngaychidinh: ngaychidinh
		// 	},
		// 	async: false,
		// }).responseText;
		// var kiemtrathoihan = JSON.parse(resKiemtrathoihan);
		var notifiThoihan = "";
		// if(kiemtrathoihan.data != "") {
		// 	notifiThoihan = '<p style="color: firebrick">Xét nghiệm còn thời hạn nên chưa được phép chỉ định: ' + kiemtrathoihan.data + "</p>"
		// }

		var notifiThoiHanCMU = "";
		if ($("#tdt_xn_hinhthuc").val() == 0) {
			var objKiemTraThoiHanCMU = checkChanDVKTTheoSoNgay({
				DVTT: singletonObject.dvtt,
				SO_PHIEU_CLS: SO_PHIEU_XN,
				MA_BENH_NHAN: dataBN.MA_BENH_NHAN,
				NGAY_CHI_DINH: ngaychidinh,
				maXNArray: maXNArray,
				LOAI_DVKT: 1,
				LOAI_KHAM: singletonObject.bant == 1 ? 2 : 1,
			});
			if (objKiemTraThoiHanCMU.type == 2) {
				notifiToClient("Red", objKiemTraThoiHanCMU.message);
				return false;
			}
			if (objKiemTraThoiHanCMU.type == 1) {
				notifiThoiHanCMU = objKiemTraThoiHanCMU.message;
			}
		}

		var resKiemtratrung = $.ajax({
			url: "kiemtrachidinhtrung_xetnghiem_noitru?ngay_chi_dinh=" + ngaychidinh + "&danhsachma_xn=" + maXNs + "&sophieu=" + SO_PHIEU_XN
				+ "&sovaovien=" + todieutriObject.SOVAOVIEN + "&sovaovien_dt=" + todieutriObject.SOVAOVIEN_DT,
			async: false,
			method: "GET"
		}).responseText;
		var kiemtratrung = JSON.parse(resKiemtratrung);
		var kiemtraTrung = "";
		$.each(kiemtratrung, function (i) {
			kiemtraTrung = kiemtraTrung + "<br />- " + kiemtratrung[i].TEN_XETNGHIEM;
		});
		if(kiemtraTrung != "") {
			kiemtraTrung = '<p style="color: darkred">' + kiemtraTrung+ " <br/> đã được chỉ định</p>"
		}

		var resKiemtra3thang = $.ajax({
			url: "kiemTra_xetnghiem_3thang_noitru",
			method: "POST",
			data: {
				info_Object: JSON.stringify({
						madv_ktr: maXNs,
						dvtt: singletonObject.dvtt,
						sovaovien: todieutriObject.SOVAOVIEN,
						sovaovien_dt: todieutriObject.SOVAOVIEN_DT,
						ngaychidinh: ngaychidinh,
						sophieu: SO_PHIEU_XN,
					}
				)
			},
			async: false,
		}).responseText;
		var kiemtra3thang = JSON.parse(resKiemtra3thang);
		var notifi3thang = "";
		if(kiemtra3thang.KETQUASQL != 0) {
			notifi3thang = '<p style="color: darkred">Danh sách xét nghiệm vượt quá số lần quy định: <br>' + kiemtra3thang.THONGBAOSQL + "</p>"
		}
		return notifiThoihan + kiemtraTrung + notifi3thang + notifiThoiHanCMU;
	}

	function insertXetnghiem(idButton, arr, dataBN) {
		var momentGioChiDinh = moment($("#tdt_xn_giochidinh").val(), ['HH:mm']);
		var soLan = $("#tdt_xetnghiem_solan").val() || '1';
		var cachPhut = $("#tdt_xetnghiem_cachphut").val() || '0';
		if (soLan == 0 || soLan == "") {
			notifiToClient("Red", "Vui lòng nhập số lần chỉ định");
			hideSelfLoading(idButton)
			return false;
		}
		if (soLan > 1 && cachPhut == 0) {
			notifiToClient("Red", "Vui lòng nhập cách phút chỉ định nếu số lần lớn hơn 1");
			hideSelfLoading(idButton)
			return false;
		}

		var url = "noitru_xetnghiem_insert_bangcha?nguonngoai=" + $("#tdt_xn_trangthai").val();

		function doInsert(timesLeft) {
			const ngayghi = momentGioChiDinh.format("HH:mm");
			$.post(url, { url: convertArray(arr) }).done(function (data) {
				if (data.SAISOT.toString() === "1") {
					notifiToClient("Red", "Bệnh nhân đã thanh toán không thể chỉ định");
					hideSelfLoading(idButton);
					return;
				}

				insertXetnghiemCT(
					data.SO_PHIEU_XN,
					ngayghi,
					dataBN,
					"action_tdt_themoixn"
				);

				if (timesLeft > 1) {
					momentGioChiDinh = momentGioChiDinh.add(cachPhut, "minutes");
					doInsert(timesLeft - 1);
				} else {
					loadDSPhieuXetnghiem(dataBN)
					loadDSXetnghiem("", dataBN);
					hideSelfLoading(idButton);
				}
			}).fail(function (err) {
				notifiToClient("Red", "Lỗi tạo phiếu chỉ định xét nghiệm: " + err.toString());
				hideSelfLoading(idButton);
			});
		}

		doInsert(soLan);
	}

	function insertXetnghiemCT(SO_PHIEU_XN, gioChiDinh, dataBN, idButton) {
		var ngay_arr = $("#tdt_ngaygiopdt").val().split(" ")[0].split("/");
		var ngaychidinh = ngay_arr[1] + "/" + ngay_arr[0] + "/" + ngay_arr[2];
		var arr = [ngaychidinh, gioChiDinh + ":00", SO_PHIEU_XN, singletonObject.dvtt,
			todieutriObject.STT_DIEUTRI, dataBN.STT_BENHAN, todieutriObject.STT_DOTDIEUTRI,
			todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT, '0'];
		$.ajax({
			url: "vlg_noitru_xetnghiem_update_ngaygio",
			method: "POST",
			data: {url: convertArray(arr)},
			async: false,
		})
		$.post("cmu_post", {url: [singletonObject.dvtt, SO_PHIEU_XN, todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT, $("#tdt_xn_trangthai").val(), "CMU_UPDATE_PHIEUXNCHA"].join("```")});
		var listRowSelected = $("#tdt_list_xetnghiem").jqGrid('getGridParam', 'selarrrow');
		var count = listRowSelected.length;
		var logXetNghiem = [
			"Thời gian y lệnh: " + gioChiDinh,
			"Tờ điều trị số: " + todieutriObject.STT_DIEUTRI,
			"Bác sĩ điều trị: "+ todieutriObject['TENBSDIEUTRI'],
			"Hình thức: "+ ($("#tdt_xn_hinhthuc").val() == 0? "BHYT": "Thu phí"),
			"Trạng thái: "+ ($("#tdt_xn_trangthai").val() == 0? "Bình thường": "Cấp cứu"),
			"Số phiếu xét nghiệm: "+ SO_PHIEU_XN,
		]
		var dsXN = []
		for (var i = 0; i < count; i++) {
			var ret = $("#tdt_list_xetnghiem").jqGrid('getRowData', listRowSelected[i]);
			var madv = ret.MA_XN;
			var sl = ret.SO_LUONG;
			var giadv = ret.GIA_XN;
			var thanhtien = ret.THANH_TIEN;
			var arrIns = [SO_PHIEU_XN, madv, singletonObject.dvtt, sl, giadv, thanhtien, $("#tdt_xn_hinhthuc").val() == 1? "true": "false",
				todieutriObject.STT_DIEUTRI, dataBN.STT_BENHAN, dataBN.STT_DOTDIEUTRI, todieutriObject.SOVAOVIEN,
				todieutriObject.SOVAOVIEN_DT, dataBN.MA_BENH_NHAN, ret.GIA_BHYT, ret.THANHTIEN_BHYT, ret.GIA_KBHYT, ret.THANHTIEN_KBHYT];
			dsXN.push(madv+" - "+ ret.TEN_XN + " - Số lượng: "+ sl + " lần")
			$.ajax({
				url: "noittru_xetnghiem_insert_chitiet_svv",
				method: "POST",
				data: {
					url: convertArray(arrIns)
				},
				async: false,
			})
		}
		logXetNghiem.push("Danh sách xét nghiệm: "+ dsXN.join("; "))
		luuLogHSBATheoBN({
			SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
			LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
			NOIDUNGBANDAU: "",
			NOIDUNGMOI: logXetNghiem.join("; "),
			USERID: singletonObject.userId,
			ACTION: LOGHSBAACTION.INSERT.KEY,
		})
		notifiToClient("Green", "Thành công")
		hideSelfLoading(idButton)
	}

	function deleteXetnghiemCT(SO_PHIEU_XN, dataBN) {
		var arr_tong = [];
		var listDataPhieuXN = getAllRowDataJqgrid("tdt_list_phieuxetnghiem");
		var logXetNghiem = [
			"Thời gian y lệnh: " + $("#tdt_xn_giochidinh").val(),
			"Tờ điều trị số: " + todieutriObject.STT_DIEUTRI,
			"Bác sĩ điều trị: "+ todieutriObject['TENBSDIEUTRI'],
			"Hình thức: "+ ($("#tdt_xn_hinhthuc").val() == 0? "BHYT": "Thu phí"),
			"Trạng thái: "+ ($("#tdt_xn_trangthai").val() == 0? "Bình thường": "Cấp cứu"),
			"Số phiếu xét nghiệm: "+ SO_PHIEU_XN,
		]
		var dsXN = []
		listDataPhieuXN.forEach(function (item) {
			if(item.SO_PHIEU_XN == SO_PHIEU_XN){
				arr_tong.push(item.MA_XN);
				dsXN.push(item.MA_XN+" - "+ item.TEN_XN + " - Số lượng: "+ item.SO_LUONG + " lần")
			}
		})
		var arr = [SO_PHIEU_XN, singletonObject.dvtt, arr_tong.toString(), todieutriObject.STT_DIEUTRI, dataBN.STT_BENHAN, todieutriObject.STT_DOTDIEUTRI,
			todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT];
		$.ajax({
			url: "noitru_xetnghiem_delete_cacchitiet_svv",
			method: "POST",
			data: {
				url: convertArray(arr)
			},
			async: false,
		})
		logXetNghiem.push("Danh sách xét nghiệm: "+ dsXN.join("; "))
		luuLogHSBATheoBN({
			SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
			LOAI: LOGHSBALOAI.XETNGHIEM.KEY,
			NOIDUNGBANDAU: logXetNghiem.join("; "),
			NOIDUNGMOI: "",
			USERID: singletonObject.userId,
			ACTION: LOGHSBAACTION.DELETE.KEY,
		})
	}

	function checkCDHA(idButton, SO_PHIEU_CDHA) {
		var maXNs = "";
		var maXNArray = [];
		var listRowSelected = $("#tdt_list_cdha").jqGrid('getGridParam', 'selarrrow');
		if($("#tdt_cdha_giochidinh").val() == "") {
			notifiToClient("Red", "Vui lòng nhập giờ chỉ định");
			return false;
		}
		var ngaygiochidinh = $("#tdt_ngaygiopdt").val().split(" ")[0] + " " + $("#tdt_cdha_giochidinh").val();
		var thoigianbatdaudieutri =  thongtinhsba.thongtinbn.NGAY_VAO_VIEN + " "+ thongtinhsba.thongtinbn.GIO_VAO_VIEN;
		var momentNgaygioCD = moment(ngaygiochidinh, ['DD/MM/YYYY HH:mm']);
		if(ngaygiochidinh == moment(thoigianbatdaudieutri, ['DD/MM/YYYY HH:mm:ss']).format("DD/MM/YYYY HH:mm")) {
			notifiToClient("Red", `Giờ chỉ định phải sau giờ nhập viện ${thoigianbatdaudieutri}, vui lòng kiểm tra lại`);
			return false;
		}
		if(momentNgaygioCD.isBefore(moment(thongtinhsba.thongtinbn.THOIGIANKHAMBENH, ['DD/MM/YYYY HH:mm']))) {
			notifiToClient("Red", `Giờ chỉ định phải sau giờ khám ${thongtinhsba.thongtinbn.THOIGIANKHAMBENH}, vui lòng kiểm tra lại`);
			return false;
		}
		if(momentNgaygioCD.isBefore(moment(thoigianbatdaudieutri, ['DD/MM/YYYY HH:mm:ss']))) {
			notifiToClient("Red", "Giờ chỉ định trước giờ vào viện, vui lòng kiểm tra lại");
			return false;
		}
		if(listRowSelected.length == 0) {
			notifiToClient("Red", "Vui chọn chỉ định");
			return false;
		}
		listRowSelected.forEach(function(value) {
			var ret = $("#tdt_list_cdha").jqGrid('getRowData', value);
			maXNs = maXNs + "!" + ret.MA_CDHA + "!";
			maXNArray.push(ret.MA_CDHA);
		})
		var ktradathuchien = $.ajax({
			url: "cmu_post_CMU_CHECKCLSCDHA_THUCHIEN",
			method: "POST",
			data: {
				url: [singletonObject.dvtt, todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT, SO_PHIEU_CDHA].join("```")
			},
			async: false,
		}).responseText;
		if(ktradathuchien > 0) {
			notifiToClient("Red", "CDHA đã được thực hiện không thể cập nhật");
			return false;
		}
		var dataBN = thongtinhsba.thongtinbn;
		var ngaychidinh = convertStr_MysqlDate($("#tdt_ngaygiopdt").val().split(" ")[0]);
		showSelfLoading(idButton);
		var resKiemtratrung = $.ajax({
			url: "kiemtrachidinhtrung_cdha_noitru?ngay_chi_dinh=" + ngaychidinh + "&danhsachma_cdha=" + maXNs + "&sophieu=" + SO_PHIEU_CDHA
				+ "&sovaovien=" + todieutriObject.SOVAOVIEN + "&sovaovien_dt=" + todieutriObject.SOVAOVIEN_DT,
			async: false,
			method: "GET"
		}).responseText;
		var kiemtratrung = JSON.parse(resKiemtratrung);
		var kiemtraTrung = "";
		$.each(kiemtratrung, function (i) {
			kiemtraTrung = kiemtraTrung + "<br />- " + kiemtratrung[i].TEN_CDHA;
		});
		if(kiemtraTrung != "") {
			kiemtraTrung = '<p style="color: darkred">' + kiemtraTrung+ " <br/> đã được chỉ định</p>"
		}

		var resKiemtra3thang = $.ajax({
			url: "kiemTra_cdha_3thang_noitru",
			method: "POST",
			data: {
				info_Object: JSON.stringify({
						madv_ktr: maXNs,
						dvtt: singletonObject.dvtt,
						sovaovien: todieutriObject.SOVAOVIEN,
						sovaovien_dt: todieutriObject.SOVAOVIEN_DT,
						ngaychidinh: ngaychidinh,
						sophieu: SO_PHIEU_CDHA,
					}
				)
			},
			async: false,
		}).responseText;
		var kiemtra3thang = JSON.parse(resKiemtra3thang);
		var notifi3thang = "";
		if(kiemtra3thang.KETQUASQL != 0) {
			notifi3thang = '<p style="color: darkred">Danh sách CDHA vượt quá số lần quy định: <br>' + kiemtra3thang.THONGBAOSQL + "</p>"
		}

		var notifiThoiHanCMU = "";
		if ($("#tdt_cdha_hinhthuc").val() == 0) {
			var objKiemTraThoiHanCMU = checkChanDVKTTheoSoNgay({
				DVTT: singletonObject.dvtt,
				SO_PHIEU_CLS: SO_PHIEU_CDHA,
				MA_BENH_NHAN: dataBN.MA_BENH_NHAN,
				NGAY_CHI_DINH: ngaychidinh,
				maXNArray: maXNArray,
				LOAI_DVKT: 2,
				LOAI_KHAM: singletonObject.bant == 1 ? 2 : 1,
			});
			if (objKiemTraThoiHanCMU.type == 2) {
				notifiToClient("Red", objKiemTraThoiHanCMU.message);
				return false;
			}
			if (objKiemTraThoiHanCMU.type == 1) {
				notifiThoiHanCMU = objKiemTraThoiHanCMU.message;
			}
		}

		return  kiemtraTrung + notifi3thang + notifiThoiHanCMU;
	}

	function insertCDHA() {
		var ngaychidinh = convertStr_MysqlDate($("#tdt_ngaygiopdt").val().split(" ")[0]);

		var arr = [todieutriObject.STT_DIEUTRI, thongtinhsba.thongtinbn.STT_BENHAN, todieutriObject.STT_DOTDIEUTRI,
			singletonObject.dvtt,
			$("#tdt_cdha_phongcdha").val(),
			todieutriObject.BACSIDIEUTRI,
			singletonObject.userId, singletonObject.maphongbenh,
			$("#tdt_cdha_hinhthuc").val() == 0,
			thongtinhsba.thongtinbn.MA_BENH_NHAN,
			todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT,
			ngaychidinh, $("#tdt_cdha_trangthai").val()];
		var url = "noitru_cdha_insert_bangcha?nguonngoai=" + $("#tdt_xn_trangthai").val();
		$.post(url, {url: convertArray(arr)}).done(function (data) {
			if (data.SAISOT.toString() == "1") {
				return notifiToClient("Red", "Bệnh nhân đã thanh toán không thể chỉ định");
			}
			insertCDHACT(data.SO_PHIEU_CDHA, thongtinhsba.thongtinbn, "action_tdt_themoicdha");
		});
	}

	function insertCDHACT(SO_PHIEU_CDHA, dataBN, idButton) {

		var listRowSelected = $("#tdt_list_cdha").jqGrid('getGridParam', 'selarrrow');
		var count = listRowSelected.length;
		var logCDHA = [
			"Thời gian y lệnh: " + $("#tdt_cdha_giochidinh").val(),
			"Tờ điều trị số: " + todieutriObject.STT_DIEUTRI,
			"Bác sĩ điều trị: "+ todieutriObject['TENBSDIEUTRI'],
			"Hình thức: "+ ($("#tdt_cdha_hinhthuc").val() == 0? "BHYT": "Thu phí"),
			"Trạng thái: "+ ($("#tdt_cdha_trangthai").val() == 0? "Bình thường": "Cấp cứu"),
			"Số phiếu CDHA: "+ SO_PHIEU_CDHA,
		]
		var dsCDHA = []
		for (var i = 0; i < count; i++) {
			var ret = $("#tdt_list_cdha").jqGrid('getRowData', listRowSelected[i]);
			var madv = ret.MA_CDHA;
			var sl = ret.SO_LUONG;
			var giadv = ret.GIA_CDHA;
			var thanhtien = ret.THANH_TIEN;
			dsCDHA.push(madv+" - "+ ret.TEN_CDHA + " - Số lượng: "+ sl + " lần")
			var arrIns = [SO_PHIEU_CDHA, madv, singletonObject.dvtt, sl, giadv, thanhtien, $("#tdt_cdha_hinhthuc").val() == 1? "true": "false",
				todieutriObject.STT_DIEUTRI, dataBN.STT_BENHAN, todieutriObject.STT_DOTDIEUTRI, todieutriObject.SOVAOVIEN,
				todieutriObject.SOVAOVIEN_DT, dataBN.MA_BENH_NHAN, ret.GIA_BHYT, ret.THANHTIEN_BHYT, ret.GIA_KBHYT, ret.THANHTIEN_KBHYT];
			$.ajax({
				url: "noittru_cdha_insert_chitiet_svv",
				method: "POST",
				data: {
					url: convertArray(arrIns)
				},
				async: false,
			})
		}
		logCDHA.push("Danh sách CDHA: "+ dsCDHA.join("; "))
		luuLogHSBATheoBN({
			SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
			LOAI: LOGHSBALOAI.CDHA.KEY,
			NOIDUNGBANDAU: "",
			NOIDUNGMOI: logCDHA.join("; "),
			USERID: singletonObject.userId,
			ACTION: LOGHSBAACTION.INSERT.KEY,
		})
		updatePhieuCDHACHA(SO_PHIEU_CDHA);
		notifiToClient("Green", "Thành công")
		hideSelfLoading(idButton)
		loadDSPhieuCDHA(dataBN)
		loadDSCDHA("", dataBN);
	}

	function updatePhieuCDHACHA(SO_PHIEU_CDHA) {
		$.post("cmu_post",
			{
				url: [
					singletonObject.dvtt, SO_PHIEU_CDHA, todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT,
					$("#tdt_cdha_trangthai").val(), $("#tdt_ngaygiopdt").val().split(" ")[0] + " " + $("#tdt_cdha_giochidinh").val(),
					$("#tdt_cdha_hinhthuc").val(), "CMU_UPDATE_PHIEUCDHA"
				].join("```")
			});

	}

	function doUpateCDHA(ret, dataBN) {
		deleteCDHACT(ret.SO_PHIEU_CDHA, dataBN)
		insertCDHACT(ret.SO_PHIEU_CDHA, dataBN, "action_tdt_luucdha");
		$("#tdt_cdha_hinhthuc").prop("disabled", false);
		$("#tdt_cdha_phongcdha").prop("disabled", false);
		enableModeButtonCDHA('add')
	}

	function deleteCDHACT(SO_PHIEU_CDHA, dataBN) {
		var arr_tong = [];
		var listDataPhieuCDHA = getAllRowDataJqgrid("tdt_list_phieucdha");
		var logCDHA = [
			"Thời gian y lệnh: " + $("#tdt_cdha_giochidinh").val(),
			"Tờ điều trị số: " + todieutriObject.STT_DIEUTRI,
			"Bác sĩ điều trị: "+ todieutriObject['TENBSDIEUTRI'],
			"Hình thức: "+ ($("#tdt_cdha_hinhthuc").val() == 0? "BHYT": "Thu phí"),
			"Trạng thái: "+ ($("#tdt_cdha_trangthai").val() == 0? "Bình thường": "Cấp cứu"),
			"Số phiếu CDHA: "+ SO_PHIEU_CDHA,
		]
		var dsCDHA = []
		listDataPhieuCDHA.forEach(function (item) {
			if(item.SO_PHIEU_CDHA == SO_PHIEU_CDHA){
				arr_tong.push(item.MA_CDHA);
				dsCDHA.push(item.MA_CDHA+" - "+ item.TEN_CDHA + " - Số lượng: "+ item.SO_LUONG + " lần")
			}
		})
		var arr = [SO_PHIEU_CDHA, singletonObject.dvtt, arr_tong.toString(), todieutriObject.STT_DIEUTRI, dataBN.STT_BENHAN, todieutriObject.STT_DOTDIEUTRI,
			todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT];
		$.ajax({
			url: "noitru_cdha_delete_cacchitiet_svv",
			method: "POST",
			data: {
				url: convertArray(arr)
			},
			async: false,
		})
		logCDHA.push("Danh sách CDHA: "+ dsCDHA.join("; "))
		luuLogHSBATheoBN({
			SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
			LOAI: LOGHSBALOAI.CDHA.KEY,
			NOIDUNGBANDAU: logCDHA.join("; "),
			NOIDUNGMOI: "",
			USERID: singletonObject.userId,
			ACTION: LOGHSBAACTION.DELETE.KEY,
		})
	}

	function checkTTPT(idButton, SO_PHIEU_TTPT) {
		var maXNs = "";
		var maXNArray = [];
		var listRowSelected = $("#tdt_list_ttpt").jqGrid('getGridParam', 'selarrrow');
		if($("#tdt_ttpt_giochidinh").val() == "") {
			notifiToClient("Red", "Vui lòng nhập giờ chỉ định");
			return false;
		}
		var ngaygiochidinh = $("#tdt_ngaygiopdt").val().split(" ")[0] + " " + $("#tdt_ttpt_giochidinh").val();
		var thoigianbatdaudieutri =  thongtinhsba.thongtinbn.NGAY_VAO_VIEN + " "+ thongtinhsba.thongtinbn.GIO_VAO_VIEN;
		var momentNgaygioCD = moment(ngaygiochidinh, ['DD/MM/YYYY HH:mm']);
		if(ngaygiochidinh == moment(thoigianbatdaudieutri, ['DD/MM/YYYY HH:mm:ss']).format("DD/MM/YYYY HH:mm")) {
			notifiToClient("Red", `Giờ chỉ định phải sau giờ nhập viện ${thoigianbatdaudieutri}, vui lòng kiểm tra lại`);
			return false;
		}
		if(momentNgaygioCD.isBefore(moment(thongtinhsba.thongtinbn.THOIGIANKHAMBENH, ['DD/MM/YYYY HH:mm']))) {
			notifiToClient("Red", `Giờ chỉ định phải sau giờ khám ${thongtinhsba.thongtinbn.THOIGIANKHAMBENH}, vui lòng kiểm tra lại`);
			return false;
		}
		if(momentNgaygioCD.isBefore(moment(thoigianbatdaudieutri, ['DD/MM/YYYY HH:mm:ss']))) {
			notifiToClient("Red", "Giờ chỉ định trước giờ vào viện, vui lòng kiểm tra lại");
			return false;
		}
		if(listRowSelected.length == 0) {
			notifiToClient("Red", "Vui chọn chỉ định");
			return false;
		}
		listRowSelected.forEach(function(value) {
			var ret = $("#tdt_list_ttpt").jqGrid('getRowData', value);
			maXNs = maXNs + "!" + ret.MA_DV + "!";
			maXNArray.push(ret.MA_DV);
		})
		var ktradathuchien = $.ajax({
			url: "cmu_post_CMU_CHECKCLSTTPT_THUCHIEN",
			method: "POST",
			data: {
				url: [singletonObject.dvtt, todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT, SO_PHIEU_TTPT].join("```")
			},
			async: false,
		}).responseText;
		if(ktradathuchien > 0) {
			notifiToClient("Red", "TTPT đã được thực hiện không thể cập nhật");
			return false;
		}
		var dataBN = thongtinhsba.thongtinbn;
		var ngaychidinh = convertStr_MysqlDate($("#tdt_ngaygiopdt").val().split(" ")[0]);
		showSelfLoading(idButton);
		var resKiemtratrung = $.ajax({
			url: "kiemtrachidinhtrung_dichvu_noitru?ngay_chi_dinh=" + ngaychidinh + "&danhsachma_dichvu=" + maXNs + "&sophieu=" + SO_PHIEU_TTPT
				+ "&sovaovien=" + todieutriObject.SOVAOVIEN + "&sovaovien_dt=" + todieutriObject.SOVAOVIEN_DT,
			async: false,
			method: "GET"
		}).responseText;
		var kiemtratrung = JSON.parse(resKiemtratrung);
		var kiemtraTrung = "";
		$.each(kiemtratrung, function (i) {
			kiemtraTrung = kiemtraTrung + "<br />- " + kiemtratrung[i].TEN_DV;
		});
		if(kiemtraTrung != "") {
			kiemtraTrung = '<p style="color: darkred">' + kiemtraTrung+ " <br/> đã được chỉ định</p>"
		}

		var resKiemtra3thang = $.ajax({
			url: "kiemTra_ttpt_3thang_noitru",
			method: "POST",
			data: {
				info_Object: JSON.stringify({
						madv_ktr: maXNs,
						dvtt: singletonObject.dvtt,
						sovaovien: todieutriObject.SOVAOVIEN,
						sovaovien_dt: todieutriObject.SOVAOVIEN_DT,
						ngaychidinh: ngaychidinh,
						sophieu: SO_PHIEU_TTPT,
					}
				)
			},
			async: false,
		}).responseText;
		var kiemtra3thang = JSON.parse(resKiemtra3thang);
		var notifi3thang = "";
		if(kiemtra3thang.KETQUASQL != 0) {
			notifi3thang = '<p style="color: darkred">Danh sách TTPT vượt quá số lần quy định: <br>' + kiemtra3thang.THONGBAOSQL + "</p>"
		}

		var notifiThoiHanCMU = "";
		if ($("#tdt_ttpt_hinhthuc").val() == 0) {
			var objKiemTraThoiHanCMU = checkChanDVKTTheoSoNgay({
				DVTT: singletonObject.dvtt,
				SO_PHIEU_CLS: SO_PHIEU_TTPT,
				MA_BENH_NHAN: dataBN.MA_BENH_NHAN,
				NGAY_CHI_DINH: ngaychidinh,
				maXNArray: maXNArray,
				LOAI_DVKT: 3,
				LOAI_KHAM: singletonObject.bant == 1 ? 2 : 1,
			});
			if (objKiemTraThoiHanCMU.type == 2) {
				notifiToClient("Red", objKiemTraThoiHanCMU.message);
				return false;
			}
			if (objKiemTraThoiHanCMU.type == 1) {
				notifiThoiHanCMU = objKiemTraThoiHanCMU.message;
			}
		}

		return  kiemtraTrung + notifi3thang + notifiThoiHanCMU;
	}

	function insertTTPT(idButton, ngayGioChiDinh = $("#tdt_ngaygiopdt").val().split(" ")[0] + " " + $("#tdt_ttpt_giochidinh").val()) {
		var ngaychidinh = convertStr_MysqlDate($("#tdt_ngaygiopdt").val().split(" ")[0]);
		var momentNgayGioChiDinh = moment(ngayGioChiDinh, ['DD/MM/YYYY hh:mm']);
		var soLan = $("#tdt_ttpt_solan").val() || '1';
		var cachPhut = $("#tdt_ttpt_cachphut").val() || '0';
		if (soLan == 0 || soLan == "") {
			notifiToClient("Red", "Vui lòng nhập số lần chỉ định");
			hideSelfLoading(idButton)
			return false;
		}
		if (soLan > 1 && cachPhut == 0) {
			notifiToClient("Red", "Vui lòng nhập cách phút chỉ định nếu số lần lớn hơn 1");
			hideSelfLoading(idButton)
			return false;
		}

		if (!momentNgayGioChiDinh.isValid()) {
			notifiToClient("Red", "Định dạng ngày giờ chỉ định không hợp lệ");
			hideSelfLoading(idButton);
			return false;
		}

		var url = "noitru_ttpt_insert_bangcha?nguonngoai="
			+ $("#tdt_ttpt_trangthai").val();
		var arr = [
			todieutriObject.STT_DIEUTRI,
			thongtinhsba.thongtinbn.STT_BENHAN,
			thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
			$("#tdt_ttpt_phongttpt").val(),
			singletonObject.dvtt,
			$("#tdt_ttpt_loaittpt").val(),
			todieutriObject.BACSIDIEUTRI,
			singletonObject.maphongbenh,
			$("#tdt_cdha_hinhthuc").val() == 0,
			0, 0,
			singletonObject.userId,
			todieutriObject.SOVAOVIEN,
			todieutriObject.SOVAOVIEN_DT,
			ngaychidinh,
			thongtinhsba.thongtinbn.MA_BENH_NHAN,
			$("#tdt_cdha_trangthai").val()
		];
		function doInsert(timesLeft) {
			const ngayghi = momentNgayGioChiDinh.format("DD/MM/YYYY HH:mm");
			$.post(url, { url: convertArray(arr) }).done(function (data) {
				if (data.SAISOT.toString() === "1") {
					notifiToClient("Red", "Bệnh nhân đã thanh toán không thể chỉ định");
					hideSelfLoading(idButton);
					return;
				}

				insertTTPTCT(
					data.SO_PHIEU_DV,
					ngayghi,
					thongtinhsba.thongtinbn,
					"action_tdt_themoittpt"
				);

				if (timesLeft > 1) {
					momentNgayGioChiDinh = momentNgayGioChiDinh.add(cachPhut, "minutes");
					doInsert(timesLeft - 1);
				} else {
					loadDSPhieuTTPT(thongtinhsba.thongtinbn)
					loadDSTTPT("", thongtinhsba.thongtinbn);
					hideSelfLoading(idButton);
				}
			}).fail(function (err) {
				notifiToClient("Red", "Lỗi tạo phiếu chỉ định cha: " + err.toString());
				hideSelfLoading(idButton);
			});
		}

		doInsert(soLan);
	}

	function insertTTPTCT(SO_PHIEU_DICHVU, ngayGioChiDinh, dataBN, idButton) {

		var listRowSelected = $("#tdt_list_ttpt").jqGrid('getGridParam', 'selarrrow');
		var count = listRowSelected.length;
		var logTTPT = [
			"Thời gian y lệnh: " + ngayGioChiDinh,
			"Tờ điều trị số: " + todieutriObject.STT_DIEUTRI,
			"Bác sĩ điều trị: "+ todieutriObject['TENBSDIEUTRI'],
			"Hình thức: "+ ($("#tdt_ttpt_hinhthuc").val() == 0? "BHYT": "Thu phí"),
			"Trạng thái: "+ ($("#tdt_ttpt_trangthai").val() == 0? "Bình thường": "Cấp cứu"),
			"Số phiếu thủ thuât/phẫu thuật: "+ SO_PHIEU_DICHVU,
		]
		var dsTTPT = []
		for (var i = 0; i < count; i++) {
			var ret = $("#tdt_list_ttpt").jqGrid('getRowData', listRowSelected[i]);
			var madv = ret.MA_DV;
			var sl = ret.SO_LUONG;
			var giadv = ret.GIA_DV;
			var thanhtien = ret.THANH_TIEN;
			var vitrittpt = ret.VITRITTPT;
			var thoigianttpt = ret.THOIGIANTTPT;
			dsTTPT.push(madv+" - "+ ret.TEN_DV + " - Số lượng: "+ ret.SO_LUONG + " lần")
			var arrIns = [SO_PHIEU_DICHVU, madv, singletonObject.dvtt, sl, giadv, thanhtien, $("#tdt_ttpt_hinhthuc").val() == 1? "true": "false",
				todieutriObject.STT_DIEUTRI, dataBN.STT_BENHAN, todieutriObject.STT_DOTDIEUTRI, todieutriObject.SOVAOVIEN,
				todieutriObject.SOVAOVIEN_DT, dataBN.MA_BENH_NHAN, ret.GIA_BHYT, ret.THANHTIEN_BHYT, ret.GIA_KBHYT, ret.THANHTIEN_KBHYT, ret.TILETT];
			$.ajax({
				url: "noittru_ttpt_insert_chitiet_svv",
				method: "POST",
				data: {
					url: convertArray(arrIns),
					vitrittpt:vitrittpt,
					thoigianttpt:thoigianttpt
				},
				async: false,
			})
		}
		logTTPT.push("Danh sách thủ thuât/phẫu thuật: "+ dsTTPT.join("; "))
		luuLogHSBATheoBN({
			SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
			LOAI: LOGHSBALOAI.TTPT.KEY,
			NOIDUNGBANDAU: "",
			NOIDUNGMOI: logTTPT.join("; "),
			USERID: singletonObject.userId,
			ACTION: LOGHSBAACTION.INSERT.KEY,
		})
		updatePhieuTTPTCHA(SO_PHIEU_DICHVU, ngayGioChiDinh);
		notifiToClient("Green", "Thành công")
		hideSelfLoading(idButton)
	}

	function updatePhieuTTPTCHA(SO_PHIEU_DICHVU, ngayGioChiDinh) {
		$.post("cmu_post",
			{
				url: [
					singletonObject.dvtt, SO_PHIEU_DICHVU, todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT,
					$("#tdt_ttpt_trangthai").val(), ngayGioChiDinh, $("#tdt_ttpt_hinhthuc").val(), "CMU_UPDATE_PHIEUTTPT"
				].join("```")
			});

	}

	function doUpateTTPT(ret, dataBN) {
		deleteTTPTCT(ret.SO_PHIEU_DICHVU, dataBN)
		insertTTPTCT(ret.SO_PHIEU_DICHVU, dataBN, "action_tdt_luuttpt");
		$("#tdt_ttpt_hinhthuc").prop("disabled", false);
		$("#tdt_ttpt_phongttpt").prop("disabled", false);
		$("#tdt_ttpt_loaittpt").prop("disabled", false);
		enableModeButtonTTPT('add')
	}

	function deleteTTPTCT(SO_PHIEU_DICHVU, dataBN) {
		var arr_tong = [];
		var listDataPhieuTTPT = getAllRowDataJqgrid("tdt_list_phieuttpt");
		var logTTPT = [
			"Thời gian y lệnh: " + $("#tdt_ttpt_giochidinh").val(),
			"Tờ điều trị số: " + todieutriObject.STT_DIEUTRI,
			"Bác sĩ điều trị: "+ todieutriObject['TENBSDIEUTRI'],
			"Hình thức: "+ ($("#tdt_ttpt_hinhthuc").val() == 0? "BHYT": "Thu phí"),
			"Trạng thái: "+ ($("#tdt_ttpt_trangthai").val() == 0? "Bình thường": "Cấp cứu"),
			"Số phiếu thủ thuât/phẫu thuật: "+ SO_PHIEU_DICHVU,
		]
		var dsTTPT = []
		listDataPhieuTTPT.forEach(function (item) {
			if(item.SO_PHIEU_DICHVU == SO_PHIEU_DICHVU){
				arr_tong.push(item.MA_DV);
				dsTTPT.push(item.MA_DV+" - "+ item.TEN_DV + " - Số lượng: "+ item.SO_LUONG + " lần")
			}
		})
		var arr = [SO_PHIEU_DICHVU, singletonObject.dvtt, arr_tong.toString(), todieutriObject.STT_DIEUTRI, dataBN.STT_BENHAN, todieutriObject.STT_DOTDIEUTRI,
			todieutriObject.SOVAOVIEN, todieutriObject.SOVAOVIEN_DT];
		console.log("arr", arr)
		$.ajax({
			url: "noitru_ttpt_delete_cacchitiet_svv",
			method: "POST",
			data: {
				url: convertArray(arr)
			},
			async: false,
		})
		logTTPT.push("Danh sách thủ thuât/phẫu thuật: "+ dsTTPT.join("; "))
		luuLogHSBATheoBN({
			SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
			LOAI: LOGHSBALOAI.TTPT.KEY,
			NOIDUNGBANDAU: logTTPT.join("; "),
			NOIDUNGMOI: "",
			USERID: singletonObject.userId,
			ACTION: LOGHSBAACTION.DELETE.KEY,
		})
	}

});