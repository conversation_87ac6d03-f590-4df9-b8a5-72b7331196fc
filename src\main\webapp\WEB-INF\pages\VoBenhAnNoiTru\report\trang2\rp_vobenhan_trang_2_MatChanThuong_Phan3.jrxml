<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rp_vobenhan_trang_2" language="groovy" pageWidth="595" pageHeight="842" columnWidth="580" leftMargin="10" rightMargin="5" topMargin="10" bottomMargin="10" uuid="513ef620-30fb-4b13-96b1-94f754855041">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<subDataset name="Table Dataset 1" uuid="ff3383f9-fc02-4ad9-bcc3-8022a5c48429"/>
	<queryString>
		<![CDATA[select 1 from dual]]>
	</queryString>
	<field name="ID" class="java.math.BigDecimal"/>
	<field name="LY_DO_VAO_VIEN" class="java.lang.String"/>
	<field name="NGAY_CUA_BENH" class="java.lang.String"/>
	<field name="QUA_TRINH_BENH_LY" class="java.lang.String"/>
	<field name="TIEN_SU_BAN_THAN_MAT" class="java.lang.String"/>
	<field name="TIEN_SU_BAN_THAN_TOANTHAN" class="java.lang.String"/>
	<field name="TIEN_SU_GIA_DINH" class="java.lang.String"/>
	<field name="THI_LUC_KHONG_KINH_MP" class="java.lang.String"/>
	<field name="THI_LUC_KHONG_KINH_MT" class="java.lang.String"/>
	<field name="THI_LUC_CO_KINH_MP" class="java.lang.String"/>
	<field name="THI_LUC_CO_KINH_MT" class="java.lang.String"/>
	<field name="THITRUONG_MP" class="java.lang.String"/>
	<field name="THITRUONG_MT" class="java.lang.String"/>
	<field name="NHAN_AP_MP" class="java.lang.String"/>
	<field name="NHAN_AP_MT" class="java.lang.String"/>
	<field name="MI_MAT_MP" class="java.lang.String"/>
	<field name="MI_MAT_DO_MP" class="java.lang.String"/>
	<field name="MI_MAT_SEO_MI_MP" class="java.lang.String"/>
	<field name="MI_MAT_KHAC_MP" class="java.lang.String"/>
	<field name="MI_MAT_MT" class="java.lang.String"/>
	<field name="MI_MAT_DO_MT" class="java.lang.String"/>
	<field name="MI_MAT_SEO_MI_MT" class="java.lang.String"/>
	<field name="MI_MAT_KHAC_MT" class="java.lang.String"/>
	<field name="KM_MP" class="java.lang.String"/>
	<field name="KM_XUAT_HUYET_MP" class="java.lang.String"/>
	<field name="KM_RACH_KM_MP" class="java.lang.String"/>
	<field name="KM_THIEU_MAU_MP" class="java.lang.String"/>
	<field name="KM_TON_THUONG_MP" class="java.lang.String"/>
	<field name="KM_MT" class="java.lang.String"/>
	<field name="KM_XUAT_HUYET_MT" class="java.lang.String"/>
	<field name="KM_RACH_KM_MT" class="java.lang.String"/>
	<field name="KM_THIEU_MAU_MT" class="java.lang.String"/>
	<field name="KM_TON_THUONG_MT" class="java.lang.String"/>
	<field name="HINH_ANH_TON_THUONG_MP" class="java.lang.String"/>
	<field name="HINH_ANH_TON_THUONG_MT" class="java.lang.String"/>
	<field name="GIAC_MAC_MP" class="java.lang.String"/>
	<field name="GIAC_MAC_SEO_MP" class="java.lang.String"/>
	<field name="GIAC_MAC_DI_VAT_MP" class="java.lang.String"/>
	<field name="GIAC_MAC_KICH_THUOC_MP" class="java.lang.String"/>
	<field name="GIAC_MAC_VI_TRI_MP" class="java.lang.String"/>
	<field name="GIAC_MAC_KHAC_MP" class="java.lang.String"/>
	<field name="GIAC_MAC_MT" class="java.lang.String"/>
	<field name="GIAC_MAC_SEO_MT" class="java.lang.String"/>
	<field name="GIAC_MAC_DI_VAT_MT" class="java.lang.String"/>
	<field name="GIAC_MAC_KICH_THUOC_MT" class="java.lang.String"/>
	<field name="GIAC_MAC_VI_TRI_MT" class="java.lang.String"/>
	<field name="GIAC_MAC_KHAC_MT" class="java.lang.String"/>
	<field name="CUNG_MAC_MP" class="java.lang.String"/>
	<field name="CUNG_MAC_KICH_THUOC_MP" class="java.lang.String"/>
	<field name="CUNG_MAC_VI_TRI_MP" class="java.lang.String"/>
	<field name="CUNG_MAC_KHAC_MP" class="java.lang.String"/>
	<field name="CUNG_MAC_MT" class="java.lang.String"/>
	<field name="CUNG_MAC_KICH_THUOC_MT" class="java.lang.String"/>
	<field name="CUNG_MAC_VI_TRI_MT" class="java.lang.String"/>
	<field name="CUNG_MAC_KHAC_MT" class="java.lang.String"/>
	<field name="TIEN_PHONG_MP" class="java.lang.String"/>
	<field name="TIEN_PHONG_MU_MP" class="java.lang.String"/>
	<field name="TIEN_PHONG_SAU_MP" class="java.lang.String"/>
	<field name="TIEN_PHONG_XUAT_TIET_MP" class="java.lang.String"/>
	<field name="TIEN_PHONG_TYNDALL_MP" class="java.lang.String"/>
	<field name="TIEN_PHONG_MUC_DO_MP" class="java.lang.String"/>
	<field name="TIEN_PHONG_DI_VAT_MP" class="java.lang.String"/>
	<field name="TIEN_PHONG_KHAC_MP" class="java.lang.String"/>
	<field name="TIEN_PHONG_MT" class="java.lang.String"/>
	<field name="TIEN_PHONG_SAU_MT" class="java.lang.String"/>
	<field name="TIEN_PHONG_MU_MT" class="java.lang.String"/>
	<field name="TIEN_PHONG_XUAT_TIET_MT" class="java.lang.String"/>
	<field name="TIEN_PHONG_TYNDALL_MT" class="java.lang.String"/>
	<field name="TIEN_PHONG_MUC_DO_MT" class="java.lang.String"/>
	<field name="TIEN_PHONG_DI_VAT_MT" class="java.lang.String"/>
	<field name="TIEN_PHONG_KHAC_MT" class="java.lang.String"/>
	<field name="MONG_MAT_MP" class="java.lang.String"/>
	<field name="MONG_MAT_DUT_CHAN_MP" class="java.lang.String"/>
	<field name="MONG_MAT_MAT_MP" class="java.lang.String"/>
	<field name="MONG_MAT_THUNG_MP" class="java.lang.String"/>
	<field name="MONG_MAT_MT" class="java.lang.String"/>
	<field name="MONG_MAT_DUT_CHAN_MT" class="java.lang.String"/>
	<field name="MONG_MAT_MAT_MT" class="java.lang.String"/>
	<field name="MONG_MAT_THUNG_MT" class="java.lang.String"/>
	<field name="DONG_TU_KICH_THUOC_MP" class="java.lang.String"/>
	<field name="DONG_TU_MP" class="java.lang.String"/>
	<field name="DONG_TU_VT_MP" class="java.lang.String"/>
	<field name="DONG_TU_ANH_MP" class="java.lang.String"/>
	<field name="DONG_TU_KICH_THUOC_MT" class="java.lang.String"/>
	<field name="DONG_TU_MT" class="java.lang.String"/>
	<field name="DONG_TU_VT_MT" class="java.lang.String"/>
	<field name="DONG_TU_ANH_MT" class="java.lang.String"/>
	<field name="TTT_MP" class="java.lang.String"/>
	<field name="TTT_IOL_MP" class="java.lang.String"/>
	<field name="TTT_KHAC_MP" class="java.lang.String"/>
	<field name="TTT_MT" class="java.lang.String"/>
	<field name="TTT_IOL_MT" class="java.lang.String"/>
	<field name="TTT_KHAC_MT" class="java.lang.String"/>
	<field name="DICH_KINH_MP" class="java.lang.String"/>
	<field name="DICH_KINH_DUC_MP" class="java.lang.String"/>
	<field name="DICH_KINH_XUATHUYET_MP" class="java.lang.String"/>
	<field name="DICH_KINH_KHAC_MP" class="java.lang.String"/>
	<field name="DICH_KINH_MT" class="java.lang.String"/>
	<field name="DICH_KINH_DUC_MT" class="java.lang.String"/>
	<field name="DICH_KINH_XUATHUYET_MT" class="java.lang.String"/>
	<field name="DICH_KINH_KHAC_MT" class="java.lang.String"/>
	<field name="VONG_MAC_MP" class="java.lang.String"/>
	<field name="VONG_MAC_MACH_MP" class="java.lang.String"/>
	<field name="VONG_MAC_DIA_THI_MP" class="java.lang.String"/>
	<field name="VONG_MAC_PHU_MP" class="java.lang.String"/>
	<field name="VONG_MAC_XUAT_HUYET_MP" class="java.lang.String"/>
	<field name="VONG_MAC_MUC_DO_MP" class="java.lang.String"/>
	<field name="VONG_MAC_SL_MP" class="java.lang.String"/>
	<field name="VONG_MAC_VT_RACH_MP" class="java.lang.String"/>
	<field name="VONG_MAC_HINH_THAI_MP" class="java.lang.String"/>
	<field name="VONG_MAC_KICH_THUOC_MP" class="java.lang.String"/>
	<field name="VONG_MAC_VT_MP" class="java.lang.String"/>
	<field name="VONG_MAC_KHAC_MP" class="java.lang.String"/>
	<field name="VONG_MAC_HINH_ANH_MP" class="java.lang.String"/>
	<field name="VONG_MAC_MT" class="java.lang.String"/>
	<field name="VONG_MAC_MACH_MT" class="java.lang.String"/>
	<field name="VONG_MAC_DIA_THI_MT" class="java.lang.String"/>
	<field name="VONG_MAC_PHU_MT" class="java.lang.String"/>
	<field name="VONG_MAC_MUC_DO_MT" class="java.lang.String"/>
	<field name="VONG_MAC_XUAT_HUYET_MT" class="java.lang.String"/>
	<field name="VONG_MAC_SL_MT" class="java.lang.String"/>
	<field name="VONG_MAC_VT_RACH_MT" class="java.lang.String"/>
	<field name="VONG_MAC_HINH_THAI_MT" class="java.lang.String"/>
	<field name="VONG_MAC_KICH_THUOC_MT" class="java.lang.String"/>
	<field name="VONG_MAC_VT_MT" class="java.lang.String"/>
	<field name="VONG_MAC_KHAC_MT" class="java.lang.String"/>
	<field name="VONG_MAC_HINH_ANH_MT" class="java.lang.String"/>
	<field name="HOC_MAT_MP" class="java.lang.String"/>
	<field name="HOC_MAT_BENH_LY_MP" class="java.lang.String"/>
	<field name="HOC_MAT_DI_VAT_MP" class="java.lang.String"/>
	<field name="HOC_MAT_VN_BENH_LY_MP" class="java.lang.String"/>
	<field name="HOC_MAT_DO_LOI_MP" class="java.lang.String"/>
	<field name="HOC_MAT_MT" class="java.lang.String"/>
	<field name="HOC_MAT_BENH_LY_MT" class="java.lang.String"/>
	<field name="HOC_MAT_DI_VAT_MT" class="java.lang.String"/>
	<field name="HOC_MAT_VN_BENH_LY_MT" class="java.lang.String"/>
	<field name="HOC_MAT_DO_LOI_MT" class="java.lang.String"/>
	<field name="TOAN_THAN_CHUA_BENH_LY" class="java.lang.String"/>
	<field name="MACH" class="java.lang.String"/>
	<field name="TOAN_THAN_BENH_LY" class="java.lang.String"/>
	<field name="NHIET_DO" class="java.lang.String"/>
	<field name="HUYET_AP" class="java.lang.String"/>
	<field name="NHIP_THO" class="java.lang.String"/>
	<field name="CAN_NANG" class="java.lang.String"/>
	<field name="CLS" class="java.lang.String"/>
	<field name="TOMTAT" class="java.lang.String"/>
	<field name="BENH_CHINH" class="java.lang.String"/>
	<field name="BENH_KEM_THEO" class="java.lang.String"/>
	<field name="PHAN_BIET" class="java.lang.String"/>
	<field name="TIEN_LUONG" class="java.lang.String"/>
	<field name="PP_DIEU_TRI" class="java.lang.String"/>
	<field name="NGAY_LAMBENHAN" class="java.lang.String"/>
	<field name="NV_LAMBENHAN" class="java.lang.String"/>
	<field name="HOCMAT_VOXUONG_MP" class="java.lang.String"/>
	<field name="HOCMAT_VOXUONG_MT" class="java.lang.String"/>
	<field name="ANCHUKY" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="800" splitType="Stretch">
			<staticText>
				<reportElement x="290" y="0" width="290" height="16" uuid="e575b603-5227-4577-954c-0db49092f54d"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Mắt trái]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="290" height="16" uuid="1edaa88d-7437-4146-857c-089828901c9d"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Mắt phải]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="46" y="39" width="10" height="10" uuid="29450d34-dd2c-438d-940e-a6a96e9a29c9"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIAC_MAC_MP}.split("-")[15].trim().equals("1")?"x":""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="36" width="46" height="16" uuid="041b9540-7bc7-4c40-8117-003d680412b0"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Dị vật]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="18" width="148" height="16" uuid="560f7e10-1726-4af5-80e3-e155e1bb0a3a"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Vị trí vết rách "+(($F{VONG_MAC_VT_RACH_MP}!= null && !$F{VONG_MAC_VT_RACH_MP}.isEmpty())?$F{VONG_MAC_VT_RACH_MP}:"............")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="148" y="18" width="142" height="16" uuid="75cdfc33-4772-454e-95ca-b32f84c21e25"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Hình thái "+(($F{VONG_MAC_HINH_THAI_MP}!= null && !$F{VONG_MAC_HINH_THAI_MP}.isEmpty())?$F{VONG_MAC_HINH_THAI_MP}:"............")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="56" y="36" width="105" height="16" uuid="9e8647be-ac6d-40e0-a5c2-27d3e4dffe41"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Kích thước "+(($F{VONG_MAC_KICH_THUOC_MP}!= null && !$F{VONG_MAC_KICH_THUOC_MP}.isEmpty())?$F{VONG_MAC_KICH_THUOC_MP}:"............")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="161" y="36" width="129" height="16" uuid="1f736a01-2960-4179-bfa7-591fd88739ca"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["vị trí "+(($F{VONG_MAC_VT_MP}!= null && !$F{VONG_MAC_VT_MP}.isEmpty())?$F{VONG_MAC_VT_MP}:"............")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="54" width="290" height="16" uuid="921197c9-39a9-43a1-92fc-415ecf5c686e"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Tổn thương khác "+(($F{VONG_MAC_KHAC_MP}!= null && !$F{VONG_MAC_KHAC_MP}.isEmpty())?$F{VONG_MAC_KHAC_MP}:"............")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="72" width="46" height="16" uuid="9fc850f8-f6aa-41b8-9e5f-64b838efa650"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[MP]]></text>
			</staticText>
			<image>
				<reportElement x="48" y="72" width="242" height="127" uuid="0e1c5c7b-1900-4169-9a62-d80ab8f822e2"/>
				<box topPadding="3" leftPadding="3" bottomPadding="3" rightPadding="3"/>
				<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.getInstance(
new SimpleJasperReportsContext()).loadAwtImageFromBytes(Base64.getDecoder().decode($F{VONG_MAC_HINH_ANH_MP}.replace("data:image/png;base64,","").replace("data:image/jpeg;base64,","").getBytes())
)]]></imageExpression>
			</image>
			<image>
				<reportElement x="338" y="72" width="242" height="127" uuid="8f30b703-644f-46c1-8356-90c84a8a6e98"/>
				<box topPadding="3" leftPadding="3" bottomPadding="3" rightPadding="3"/>
				<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.getInstance(
new SimpleJasperReportsContext()).loadAwtImageFromBytes(Base64.getDecoder().decode($F{VONG_MAC_HINH_ANH_MT}.replace("data:image/png;base64,","").replace("data:image/jpeg;base64,","").getBytes())
)]]></imageExpression>
			</image>
			<textField isBlankWhenNull="true">
				<reportElement x="438" y="18" width="142" height="16" uuid="0db3efea-2f05-408a-959b-d704e42661e8"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Hình thái "+(($F{VONG_MAC_HINH_THAI_MT}!= null && !$F{VONG_MAC_HINH_THAI_MT}.isEmpty())?$F{VONG_MAC_HINH_THAI_MT}:"............")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="346" y="36" width="105" height="16" uuid="ca9b9842-d4b1-438e-87ba-65a5edbe8a9e"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Kích thước "+(($F{VONG_MAC_HINH_THAI_MT}!= null && !$F{VONG_MAC_HINH_THAI_MT}.isEmpty())?$F{VONG_MAC_HINH_THAI_MT}:"............")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="290" y="54" width="290" height="16" uuid="6741c0f1-04c1-4d77-9e9d-2c9f7ee2eeb9"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Tổn thương khác "+(($F{VONG_MAC_KHAC_MT}!= null && !$F{VONG_MAC_KHAC_MT}.isEmpty())?$F{VONG_MAC_KHAC_MT}:"............")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="290" y="36" width="46" height="16" uuid="f2f6c276-c146-487f-9166-d5da402a3d2a"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Dị vật]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="336" y="39" width="10" height="10" uuid="731c4e1a-cdab-48c2-9589-8dffcd0f5404"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIAC_MAC_MP}.split("-")[15].trim().equals("1")?"x":""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="290" y="72" width="46" height="16" uuid="1c0dd638-3214-4b16-9050-cafcb57ed549"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[MT]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="290" y="18" width="148" height="16" uuid="d9e15bcc-29c3-4d0e-875a-7b9840a8f553"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Vị trí vết rách "+(($F{VONG_MAC_VT_RACH_MT}!= null && !$F{VONG_MAC_VT_RACH_MT}.isEmpty())?$F{VONG_MAC_VT_RACH_MT}:"............")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="451" y="36" width="129" height="16" uuid="37899140-6ceb-47da-b81e-d2b8a33586ae"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["vị trí "+(($F{VONG_MAC_VT_MT}!= null && !$F{VONG_MAC_VT_MT}.isEmpty())?$F{VONG_MAC_VT_MT}:"............")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="209" width="79" height="16" uuid="469aae37-6152-4307-9cbb-ed26080979f0"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[10. Hốc mắt: ]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="144" y="212" width="10" height="10" uuid="84098048-04fc-45dc-9610-6adcd0f6f82e"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOC_MAT_MP}.split("-")[0].trim().equals("1")?"x":""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="79" y="209" width="64" height="16" uuid="b9158154-fda9-4620-9afc-f76e482e0695"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Bình thường]]></text>
			</staticText>
			<staticText>
				<reportElement x="156" y="209" width="41" height="16" uuid="80ab860f-e695-4ace-98a6-13f00dc4ae1d"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Bệnh lý]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="198" y="212" width="10" height="10" uuid="783a897a-9326-4e2b-b544-ad4a19398167"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOC_MAT_MP}.split("-")[1].trim().equals("1")?"x":""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="209" y="209" width="80" height="16" uuid="b06e8814-047c-4daa-a3bc-e46c7fc2f4c0"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[(($F{HOC_MAT_BENH_LY_MP}!= null && !$F{HOC_MAT_BENH_LY_MP}.isEmpty())?$F{HOC_MAT_BENH_LY_MP}:"............")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="225" width="46" height="16" uuid="20dff433-2f6c-411c-8b8f-c6302b5548d2"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Dị vật]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="60" y="225" width="87" height="16" uuid="814551fd-ca03-4e43-93be-3320358bdbf2"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[(($F{HOC_MAT_DI_VAT_MP}!= null && !$F{HOC_MAT_DI_VAT_MP}.isEmpty())?$F{HOC_MAT_DI_VAT_MP}:"............")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="48" y="228" width="10" height="10" uuid="ec1320f1-21b2-472c-8de4-640cf281ac9d"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOC_MAT_MP}.split("-")[2].trim().equals("1")?"x":""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="147" y="225" width="55" height="16" uuid="d236bc5f-5b02-4537-bb0b-f534ea984928"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Vỡ xương]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="212" y="225" width="78" height="16" uuid="18a958b8-694e-47da-81ad-3d009fd67b48"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOCMAT_VOXUONG_MP}.length() > 2?
$F{HOCMAT_VOXUONG_MP}.split("|")[1]:"......."]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="202" y="228" width="10" height="10" uuid="24a204cf-6f14-4ac8-be35-4351e80fd4d2"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOCMAT_VOXUONG_MP}.split("|")[0].trim().equals("1")?"x":""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="243" width="56" height="16" uuid="fe31a41c-0906-465b-a0af-7b6f95c38809"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Vận nhãn]]></text>
			</staticText>
			<staticText>
				<reportElement x="56" y="243" width="64" height="16" uuid="52ec2256-915b-4615-a0a8-7a8e8ce0bb41"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Bình thường]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="122" y="246" width="10" height="10" uuid="7ad93a3a-84d6-4209-8e13-34f79f5345a6"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOC_MAT_MP}.split("-")[4].trim().equals("1")?"x":""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="137" y="243" width="41" height="16" uuid="d60156cb-f17f-4068-8ac1-4ed6b979bc93"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Bệnh lý]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="178" y="243" width="112" height="16" uuid="b0045af2-4f5b-4c84-8d3d-be25b0c42482"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[(($F{HOC_MAT_BENH_LY_MP}!= null && !$F{HOC_MAT_BENH_LY_MP}.isEmpty())?$F{HOC_MAT_BENH_LY_MP}:"............")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="261" width="56" height="16" uuid="e155c90a-0abd-4440-b5f4-a0ec4156692e"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Nhãn cầu:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="83" y="264" width="10" height="10" uuid="00eeafcd-e02c-4688-b86d-3a1449eb73bb"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOC_MAT_MP}.split("-")[5].trim().equals("1")?"x":""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="56" y="261" width="26" height="16" uuid="59b68b2d-3797-429a-8a13-857a175de65d"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Teo]]></text>
			</staticText>
			<staticText>
				<reportElement x="96" y="261" width="26" height="16" uuid="8a77f08a-eb66-44af-82c2-033f1b782aac"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Lồi]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="123" y="264" width="10" height="10" uuid="0e4fda96-68f5-4d28-95e7-4d4c761c3d87"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOC_MAT_MP}.split("-")[6].trim().equals("1")?"x":""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="137" y="261" width="153" height="16" uuid="46861440-b676-403e-a87a-fc6b58f1cc03"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Độ lồi "+(($F{HOC_MAT_DO_LOI_MP}!= null && !$F{HOC_MAT_DO_LOI_MP}.isEmpty())?$F{HOC_MAT_DO_LOI_MP}:"............")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="413" y="264" width="10" height="10" uuid="b92e2fe8-dc9f-4ee8-9900-0c4d6d3bc6cf"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOC_MAT_MT}.split("-")[6].trim().equals("1")?"x":""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="386" y="261" width="26" height="16" uuid="7a00eed2-d0ee-4e87-ade2-3de151d670a5"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Lồi]]></text>
			</staticText>
			<staticText>
				<reportElement x="447" y="209" width="41" height="16" uuid="1fcbc943-65fe-48fd-b89a-96b9eaa421f6"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Bệnh lý]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="500" y="209" width="80" height="16" uuid="9a7c2fda-b921-4664-9c7e-2f436d779694"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[(($F{HOC_MAT_BENH_LY_MT}!= null && !$F{HOC_MAT_BENH_LY_MT}.isEmpty())?$F{HOC_MAT_BENH_LY_MT}:"............")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="489" y="212" width="10" height="10" uuid="d7688bbf-e276-4e75-8164-42ecbf7870e9"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOC_MAT_MT}.split("-")[1].trim().equals("1")?"x":""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="290" y="209" width="79" height="16" uuid="830ca235-b539-4a4a-a2fd-1eaa5fc0da39"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[10. Hốc mắt: ]]></text>
			</staticText>
			<staticText>
				<reportElement x="369" y="209" width="64" height="16" uuid="42b0ed0a-4de4-454d-ab2b-f6f8fd043ecf"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Bình thường]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="338" y="228" width="10" height="10" uuid="d9a91b99-693c-4a7d-b562-14bd74af8dff"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOC_MAT_MT}.split("-")[2].trim().equals("1")?"x":""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="290" y="225" width="46" height="16" uuid="ef02e267-8e25-43ef-aa0e-9189c2c81063"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Dị vật]]></text>
			</staticText>
			<staticText>
				<reportElement x="346" y="261" width="26" height="16" uuid="2309574d-8503-421c-a279-a6c02f20c989"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Teo]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="412" y="246" width="10" height="10" uuid="e471af72-0e81-4418-82d4-db3e0c439590"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOC_MAT_MT}.split("-")[4].trim().equals("1")?"x":""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="427" y="261" width="153" height="16" uuid="36df13d7-727b-4637-bb6b-e9810a9ef2e5"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Độ lồi "+(($F{HOC_MAT_DO_LOI_MT}!= null && !$F{HOC_MAT_DO_LOI_MT}.isEmpty())?$F{HOC_MAT_DO_LOI_MT}:"............")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="502" y="225" width="78" height="16" uuid="2db3ceaa-2271-4610-b8ba-eaf67d6d8029"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOCMAT_VOXUONG_MT}.length() > 2?
$F{HOCMAT_VOXUONG_MT}.split("|")[1]:"......."]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="373" y="264" width="10" height="10" uuid="9bdba5fa-8b6b-4fe8-9bfe-134e43d3ead8"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOC_MAT_MT}.split("-")[5].trim().equals("1")?"x":""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="492" y="228" width="10" height="10" uuid="ca0b4c96-cb61-4b3a-8420-a3e6fa6623d7"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOCMAT_VOXUONG_MT}.split("|")[0].trim().equals("1")?"x":""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="434" y="212" width="10" height="10" uuid="56196446-9f1c-439d-993a-488d7cc5aeff"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HOC_MAT_MT}.split("-")[0].trim().equals("1")?"x":""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="427" y="243" width="41" height="16" uuid="8abdae47-e5cf-4579-9bea-704d85ce4e50"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Bệnh lý]]></text>
			</staticText>
			<staticText>
				<reportElement x="290" y="261" width="56" height="16" uuid="6c250e3e-8ce4-4e8a-b1fc-c9673beffe32"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Nhãn cầu:]]></text>
			</staticText>
			<staticText>
				<reportElement x="290" y="243" width="56" height="16" uuid="73ba0322-231c-41f4-b3e0-20ce298d5880"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Vận nhãn]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="350" y="225" width="87" height="16" uuid="b3796532-cb71-4bed-9468-e13906b1692f"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[(($F{HOC_MAT_DI_VAT_MT}!= null && !$F{HOC_MAT_DI_VAT_MT}.isEmpty())?$F{HOC_MAT_DI_VAT_MT}:"............")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="346" y="243" width="64" height="16" uuid="908b6de2-859a-4320-ab5a-ded9671c5327"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Bình thường]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="468" y="243" width="112" height="16" uuid="749176ca-149b-4970-bf71-0380bbf79568"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[(($F{HOC_MAT_BENH_LY_MT}!= null && !$F{HOC_MAT_BENH_LY_MT}.isEmpty())?$F{HOC_MAT_BENH_LY_MT}:"............")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="437" y="225" width="55" height="16" uuid="d6a203df-8b22-4ebd-96c6-4bb0a0cd9a6a"/>
				<box leftPadding="3"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Vỡ xương]]></text>
			</staticText>
			<rectangle>
				<reportElement mode="Transparent" x="0" y="16" width="290" height="188" uuid="056f9e88-5fad-48da-a8ec-8af711f54d5e"/>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="290" y="16" width="290" height="188" uuid="1f8838b1-1b7e-49f4-9643-d6dcc32fbbb8"/>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="0" y="204" width="290" height="75" uuid="0b8f5978-9215-4390-a109-dd4c549e1592"/>
			</rectangle>
			<rectangle>
				<reportElement mode="Transparent" x="290" y="204" width="290" height="75" uuid="2fc3b34a-1b08-4bff-bd7b-03a0208f73c7"/>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement x="300" y="305" width="10" height="10" uuid="c8c9c7d1-684a-4c8d-b359-43fd9eb389b0"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TOAN_THAN_CHUA_BENH_LY}.equals("2")?"x":""]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="60" y="323" width="390" height="65" uuid="6f5d46ae-f997-4c19-b2c2-1fa5198a5964"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[(($F{TOAN_THAN_BENH_LY}!= null && !$F{TOAN_THAN_BENH_LY}.isEmpty())?$F{TOAN_THAN_BENH_LY}:"............")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="2" y="284" width="94" height="16" uuid="1f8fbdfd-c3c9-46f4-8bae-368dd89c09b2"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[2. Toàn thân]]></text>
			</staticText>
			<staticText>
				<reportElement x="3" y="302" width="144" height="16" uuid="ba0be1ff-e5de-43be-b693-e33e1d8fa3da"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[- Chưa có biểu hiện bệnh lý]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="147" y="305" width="10" height="10" uuid="e0bcd2d0-cb2f-47b3-b1f9-5bbe07dc1e34"/>
				<box>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TOAN_THAN_CHUA_BENH_LY}.equals("1")?"x":""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="3" y="323" width="53" height="16" uuid="e61d2038-ae92-494f-83e0-091825828b97"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[- Bệnh lý]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="455" y="300" width="125" height="15" uuid="9229a048-075f-473e-8d5e-170e67e629a5"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Mạch "+(($F{MACH}!= null && !$F{MACH}.isEmpty())?$F{MACH}:".......")+" lần/phút"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="455" y="317" width="105" height="15" uuid="faf0ec5a-9eea-4938-a4ae-4ecdf7500cce"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Nhiệt độ "+(($F{NHIET_DO}!= null && !$F{NHIET_DO}.isEmpty())?$F{NHIET_DO}:".......")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="559" y="317" width="8" height="8" uuid="744e18cf-70f1-4e5d-95a5-ddc36c03d9e6"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="6" isBold="false" isItalic="true"/>
				</textElement>
				<text><![CDATA[o]]></text>
			</staticText>
			<staticText>
				<reportElement x="567" y="317" width="10" height="16" uuid="*************-4cc8-9f61-ef0c07a110b9"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
				</textElement>
				<text><![CDATA[C]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="455" y="335" width="125" height="15" uuid="61f15ed5-39f6-47ba-b7a6-797740773c3c"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Huyết áp: "+(($F{HUYET_AP}!= null && !$F{HUYET_AP}.isEmpty())?$F{HUYET_AP}:"......")+" mm/Hg"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="455" y="352" width="125" height="15" uuid="3fd02559-b197-4813-9943-98daf2fc51a5"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Nhịp thở: "+(($F{NHIP_THO}!= null && !$F{NHIP_THO}.isEmpty())?$F{NHIP_THO}:".......")+" lần/phút"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="455" y="369" width="125" height="15" uuid="67923194-76a5-4a02-87d4-6ff078523ffb"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Cân nặng: "+(($F{CAN_NANG}!= null && !$F{CAN_NANG}.isEmpty())?$F{CAN_NANG}:".......")+" Kg"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="3" y="390" width="574" height="16" uuid="6fc03823-3743-4328-b734-b5473fb8dd36"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[IV. CÁC XÉT NGHIỆM CẦN LÀM]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="3" y="408" width="574" height="43" uuid="ac166559-818d-4c20-bc43-8bbdc2a5d330"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[(($F{CLS}!= null && !$F{CLS}.isEmpty())?$F{CLS}:"..................................................................................")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="3" y="453" width="199" height="16" uuid="4c816776-af0f-4b08-bd05-d958603e7d08"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[V. TÓM TẮT]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="3" y="471" width="574" height="43" uuid="613a586c-9965-44a9-b729-bbaa6e88ee0d"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[(($F{TOMTAT}!= null && !$F{TOMTAT}.isEmpty())?$F{TOMTAT}:"..................................................................................")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2" y="535" width="574" height="26" uuid="ed1fde9d-82ea-4b6f-b605-47e1a5628c77"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Bệnh chính "+(($F{BENH_CHINH}!= null && !$F{BENH_CHINH}.isEmpty())?$F{BENH_CHINH}:"..................................................................................")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="2" y="517" width="199" height="16" uuid="fe84deda-f015-4a2f-af3a-4e69892a1840"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[VI. CHẨN ĐOÁN]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="2" y="562" width="574" height="28" uuid="d98f361b-b82c-453b-badd-bff3a04d0416"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Bệnh kèm theo "+(($F{BENH_KEM_THEO}!= null && !$F{BENH_KEM_THEO}.isEmpty())?$F{BENH_KEM_THEO}:"..................................................................................")]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="2" y="591" width="574" height="28" uuid="67d5713a-4f3e-449a-a16a-da58d595f55d"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Phân biệt "+(($F{PHAN_BIET}!= null && !$F{PHAN_BIET}.isEmpty())?$F{PHAN_BIET}:"..................................................................................")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="2" y="621" width="199" height="16" uuid="86803c68-fa7a-43b0-8f0d-a067a8cb3ca8"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[VII. TIÊN LƯỢNG]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="2" y="639" width="574" height="28" uuid="4509dc94-a668-4ec4-ac55-a0c0c1001b49"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[(($F{TIEN_LUONG}!= null && !$F{TIEN_LUONG}.isEmpty())?$F{TIEN_LUONG}:"..................................................................................")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="2" y="669" width="199" height="16" uuid="0e1b3e5c-93ff-435c-985e-3dcfb9c1c85b"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[VIII. ĐIỀU TRỊ]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="2" y="685" width="574" height="28" uuid="d573a76e-e31e-44ee-afbd-53252ba018dd"/>
				<box topPadding="2" leftPadding="3" rightPadding="2"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA["Phương pháp "+(($F{PP_DIEU_TRI}!= null && !$F{PP_DIEU_TRI}.isEmpty())?$F{PP_DIEU_TRI}:"..................................................................................")]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement mode="Transparent" x="453" y="297" width="127" height="90" uuid="11fe7279-9254-4942-b420-ec33273baea5"/>
			</rectangle>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="251" y="713" width="329" height="15" uuid="043dc799-974b-4e15-9e13-df0fd9747df7"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{NGAY_LAMBENHAN} != null && !$F{NGAY_LAMBENHAN}.isEmpty()) ? $F{NGAY_LAMBENHAN} : "Ngày.........tháng.........năm................"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="251" y="728" width="329" height="18" uuid="5df1c166-06d9-4853-a63f-9c1f2fcca329"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Bác sĩ làm bệnh án]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="251" y="785" width="329" height="15" uuid="a4e5e90b-2c9e-4e01-9dd4-f1d0a5e4e7dd">
					<printWhenExpression><![CDATA[$F{ANCHUKY}.equals("0")]]></printWhenExpression>
				</reportElement>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Họ và tên: " + (($F{NV_LAMBENHAN} != null && !$F{NV_LAMBENHAN}.isEmpty()) ?$F{NV_LAMBENHAN} : "....................................................................")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="178" y="302" width="121" height="16" uuid="d5655171-780b-4851-b783-5c181b153953"/>
				<box leftPadding="5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[- Có biểu hiện bệnh lý]]></text>
			</staticText>
		</band>
	</title>
</jasperReport>
