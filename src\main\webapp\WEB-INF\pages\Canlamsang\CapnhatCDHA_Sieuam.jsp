<%@ page import="l2.ThamSoManager" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="google-site-verification" content="u6uNEfD4cb3gidezi4r_6aI8Wb1E07-ufBeCQpvmlqQ"/>
    <title>Hệ thống chăm sóc sức khỏe</title>
    <link rel="icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
    <link rel="shortcut icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>

    <!-- jQuery file -->
    <link href="<c:url value="/resources/css/divheader.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/css/style_new.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/css/magiczoomplus.css" />" rel="stylesheet"/>

    <!--Jquery-->
    <link rel="stylesheet" href="<c:url value="/resources/css/jquery-ui-redmond.1.9.1.css" />"/>
    <script src="<c:url value="/resources/js/jquery.min.1.8.3.js" />"></script>
    <script src="<c:url value="/resources/js/jquery-ui.1.9.1.js" />"></script>
    <!--Grid-->
    <link href="<c:url value="/resources/jqgrid/css/ui.jqgrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/jqgrid/js/i18n/grid.locale-en.js" />"></script>
    <script src="<c:url value="/resources/jqgrid/js/jquery.jqGrid.src.js" />"></script>
    <script src="<c:url value="/resources/js/common_function.js" />"></script>
    <script src="<c:url value="/resources/js/jquery.inputmask.bundle.min.js" />"></script>
    <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
    <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/dialog/jquery.alerts.1.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/dialog/jquery.alerts.js" />"></script>
    <script src="<c:url value="/resources/js/read_file.js" />"></script>
    <link href="<c:url value="/resources/dialog/jBox.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/dialog/jBox.js" />"></script>

    <link href="<c:url value="/resources/combogrid/css/smoothness/jquery.ui.combogrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/combogrid/plugin/jquery.ui.combogrid-1.6.3.js" />"></script>
    <link href="<c:url value="/resources/combogrid/css/smoothness/jquery-ui-1.10.1.custom.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/combogrid/jquery/jquery-ui-1.10.1.custom.min.js" />"></script>
    <script src="<c:url value="/resources/editor/ckeditor.js" /> "language="javascript"></script>
    <script src="<c:url value="/resources/ckeditor/adapters/jquery.js" />"></script>
    <script src="<c:url value="/resources/webcam/say-cheese.js" />"></script>
    <link href="<c:url value="/resources/webcam/pygments.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/webcam/say-cheese.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
    <script src="<c:url value="/resources/js/magiczoomplus.js" />"></script>
    <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/jqueryui/themes/redmond/jquery-ui.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/ris/deployJava.js"/>"></script>
    <script src="<c:url value="/resources/js/canlamsang/grid_danhsachchidinh.js" />"></script>
    <script src="<c:url value="/resources/smoothdivscroll/smoothdivscroll.js" />"></script>
    <script src="<c:url value="/resources/camau/vnpt-plugin_v692020.js" />"></script>
    <script src="<c:url value="/resources/camau/material/moment.js" />"></script>
    <script src="<c:url value="/resources/camau/vgcaplugin.js" />"></script>
    <script src="<c:url value="/resources/camau/keytichhop.js" />"></script>
    <script src="<c:url value="/resources/camau/sha256.min.js"/>" ></script>
    <link href="<c:url value="/resources/camau/css/loader.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/camau/css/custom.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/camau/material/moment.js" />"></script>
    <script src="<c:url value="/resources/camau/js/jquery.validate.min.js" />"></script>
    <script src="<c:url value="/resources/js/jquery-confirm.min.js" />"></script>
    <link href="<c:url value="/resources/css/jquery-confirm.min.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/camau/js/kiemtradulieuthoigiankham.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <script src="<c:url value="/resources/camau/js/common.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <script src="<c:url value="/resources/js/jquery-confirm.min.js" />"></script>
    <link href="<c:url value="/resources/css/jquery-confirm.min.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/camau/js/jquery.toast.min.js" />"></script>
    <link href="<c:url value="/resources/camau/css/jquery.toast.min.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/camau/js/luulog.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <script src="<c:url value="/resources/camau/js/lichsubenhan.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <script src="<c:url value="/resources/camau/js/kysonoitru.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <script src="<c:url value="/resources/camau/smartca769.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <link href="<c:url value="/resources/bootstrap-4.1.3/dist/css/bootstrap.min.css" />" rel="stylesheet"/>

    <%--datetimepicker--%>
    <script src="/web_his/resources/js/datetimepicker.js"></script>
    <link href="/web_his/resources/css/datetimepicker.css" rel="stylesheet"/>
    <style>
        .width1 {
            width: 200px;
        }

        .width2 {
            width: 545px;
        }

        .width3 {
            width: 150px;
        }

        .width4 {
            width: 325px;
        }

        .width5 {
            width: 810px;
        }

        .width6 {
            width: 500px;
        }

        legend {

            color: red;
        }

        .width100 {
            width: 100%;
        }

        span.cellWithoutBackground {
            display: block;
            background-image: none;
            margin-right: -2px;
            margin-left: -2px;
            height: 14px;
            padding: 4px;
        }

        #hsbant_ls_tabs ul li a {
            font-size: 11px;
        }

        #hsbant_ls_tab1 tr td {
            font-size: 13px;
        }

        #cdha_ls_tabs_2 tr td {
            font-size: 13px;
        }

        legend {
            font-size: 14px !important;
            font-weight: bold;
        }

        /*CMU 05/09/2017*/
        #list_lichsusieuam tr.jqgrow td {
            white-space: normal !important;
            height: auto;
            vertical-align: text-top;
        }

        #cke_2_top {
            display: none;
        }

        #cke_2_bottom {
            display: none;
        }
        .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn){
            width:100% !important;
        }
        /*END CMU 05/09/2017*/
    </style>
    <script>
        var sovaovien;
        var sovaovien_noi;
        var sovaovien_dt_noi;
        var maPhongBan;
        var da_thanh_toan;
        var flag_noitru = "-1";
        var matoathuoc;
        var makhovattu;
        var url_loadtonkho;
        var url_loadthuoc;
        var ngay_kb;
        var cobhyt;
        var sophieuthanhtoan;
        var tlmg;
        var giothuchien_cls_timer_is_on = false;
        var giothuchien_cls_timer_previous_status = false;
        var gioth_ct_timer_is_on = false;
        var gioth_ct_timer_previous_status = false;
        var giothuchien_cls_timer;
        var gioth_ct_timer;
        var showtime_giothuchien_cls_cancel = 0;
        var showtime_gioth_ct_cancel = 0;
        var form_sa_mau_doppler;
        var dialog_capnhatketquasieuam_ekip;
        var ma_cdha = 0;
        var sophieu = "";
        var mabenhnhan = "";
        var stt_benhan = "";
        var stt_dotdieutri = "";
        var stt_dieutri = "";
        var manv = "${Sess_UserID}";
        var tennv = "${Sess_User}";
        var admin = "${Sess_Admin}";
        var nv_with_chucdanh = "";
        var phongcdha_ss;
        var noitru_ngoaitru;
        var bacsi_chidinh;
        var co_bao_hiem;
        var sobenhan_noitru_tt;
        var sobenhan_noitru;
        var icd_benhnhan;
        var ten_icd_benhnhan;
        var soba;
        var ngay_chi_dinh;
        var ngaychidinh;
        var giochidinh;
        var tatAutoTime = "<%= ThamSoManager.instance(session).getThamSoString(ThamSoManager.THAMSO_28084881,"0")%>";
        var SESS_PHONG_BAN = "<%= session.getAttribute("Sess_PhongBan").toString()%>";
        var SESS_USER_ID = "<%= session.getAttribute("Sess_UserID").toString()%>";
        var THAMSO_828449 = "<%= ThamSoManager.instance(session).getThamSoString("828449", "0")%>";
        var THAMSO_960623 = "<%= ThamSoManager.instance(session).getThamSoString("960623", "0")%>";
        var THAMSO_960626 = "<%= ThamSoManager.instance(session).getThamSoString("960626", "0")%>";

        function checkNguoiDocKetQua() {
            var _selector = $('#cboNguoiDocKetQua');
            var countOption =  _selector.find("option[value='"+SESS_USER_ID+"']").length;
            return !!!_selector.val() || countOption==0 //|| _selector.find(':selected').data("khoa") != SESS_PHONG_BAN
                ;
        }

        function setNguoiDocKetQua(maBacSi) {
            var _selector = $('#cboNguoiDocKetQua');
            _selector.val(maBacSi||SESS_USER_ID);
        }

        function loadBacSiTheoKhoa(strListMaKhoa = "-1") {
            // Fetch the preselected item, and add to the control
            var _selector = $('#cboNguoiDocKetQua');
            $.ajax({
                type: 'GET',
                url: 'cmu_getlist?url='+convertArray(["${Sess_DVTT}",strListMaKhoa,"SEL_LIST_BAC_SI_THEO_KHOA"]),
            }).then(function (data) {
                if(jQuery.isArray(data) ) {
                    _selector.empty();
                    // $('#cboDoiTuongTiepNhan').append($('<option>', { value: '0', text : 'Khác', color: '' }));
                    _selector.append($('<option>', {
                        value: SESS_USER_ID,
                        text : "${Sess_User}",
                        "data-khoa": SESS_PHONG_BAN
                    }));
                    $.each(data, function (i, item) {
                        if(!!item && item.MA_NHANVIEN && item.TEN_NHANVIEN && item.MA_PHONGBAN && item.MA_NHANVIEN != SESS_USER_ID) {
                            _selector.append($('<option>', {
                                value: item.MA_NHANVIEN,
                                text : item.TEN_NHANVIEN,
                                "data-khoa": item.MA_PHONGBAN
                            }));
                        }
                    });
                    _selector.val(SESS_USER_ID);
                    if(!!!_selector.val()) {
                        // jAlert("Tài khoản đăng nhập không thuộc khoa đã cấu hình", "Cảnh báo");
                    }
                }
            }).always(function( data, textStatus, jqXHR ) {

            });
        }

        function luuNguoiDocKetQua(_soPhieu, _soVaoVien, _soVaoVienDt = 0, _noiTru = 0, _listMaCls = "-1") {
            $.ajax({
                type: 'POST',
                url: 'update-thong-tin-theo-benh-nhan',
                async: false,
                data: {
                    P_LOG_USERID: "${Sess_UserID}",
                    p_dvtt: "${Sess_DVTT}",
                    p_soPhieu: _soPhieu,
                    p_soVaoVien: _soVaoVien,
                    p_soVaoVienDt: _soVaoVienDt,
                    p_noiTru: _noiTru,
                    p_sessKhoaId: SESS_PHONG_BAN,
                    p_nguoiDocKetQua: $('#cboNguoiDocKetQua').val(),
                    p_listMaCls: _listMaCls,
                    p_loai_cls: 'CDHA',
                    action: "UPD_CLS_NGUOI_DOC_KET_QUA"
                }
            }).fail(function(data){
                jAlert("Lỗi cập nhật người đọc kết quả", "Cảnh báo");
            }).then(function (data) {
                console.log(data);
            }).always(function( data, textStatus, jqXHR ) {

            });
        }

        function getNguoiDocKetQua(_soPhieu, _soVaoVien, _soVaoVienDt = 0, _noiTru = 0, _listMaCls = "-1") {
            $.ajax({
                type: 'GET',
                url: 'select-thong-tin-theo-benh-nhan',
                data: {
                    P_LOG_USERID: "${Sess_UserID}",
                    p_dvtt: "${Sess_DVTT}",
                    p_soPhieu: _soPhieu,
                    p_soVaoVien: _soVaoVien,
                    p_soVaoVienDt: _soVaoVienDt,
                    p_noiTru: _noiTru,
                    p_listMaCls: _listMaCls,
                    p_loai_cls: 'CDHA',
                    action: "SEL_CLS_NGUOI_DOC_KET_QUA"
                }
            }).then(function (data) {
                if(!!data && !!data[0]){
                    var obj = data[0];
                    setNguoiDocKetQua(obj.MA_NHANVIEN);
                }
            }).always(function( data, textStatus, jqXHR ) {

            });
        }
        function chamcong_ekip(dvtt, stt_benhan, mabenhnhan, ma_dv, ten_dv, sophieu) {
            var dathuchien = $("#dathuchien").prop('checked');
            if(!dathuchien) jAlert("Phải thực hiện mới có thể chấm công",'Thông báo');
            else{
                $("#madichvu").val(ma_dv);
                $("#tendichvu").val(ten_dv);
                $("#tiencongthuchien").empty();
                $.get("hgi_ekip_maloaitiencong_theoloai", {loai: " ", thamso: ma_dv ,loai_chucnang: 'SA'}).done(function(data) {
                    $("<option value='-1'>--    Chọn vai trò   --</option>").appendTo("#tiencongthuchien");
                    if (data && data.length > 0) {
                        $.each(data, function (i) {
                            $("<option value='" + data[i].MA_TIENCONG + "'>" + data[i].TEN_VAITRO +"-" + data[i].MA_LOAIDICHVU +"-" + data[i].SO_TIEN + "</option>").appendTo("#tiencongthuchien");
                        });
                    }
                });
                $("#ekip_mau").empty();
                $.get("hgi_ekip_maloaiekip_theoloai", {ma_dv: ma_dv ,loai_chucnang: 'SA'}).done(function(data) {
                    $("<option value='-1'>--    Chọn ekip   --</option>").appendTo("#ekip_mau");
                    if (data && data.length > 0) {
                        $.each(data, function (i) {
                            $("<option value='" + data[i].MA_EKIP + "'>" + data[i].TEN_EKIP + "</option>").appendTo("#ekip_mau");
                        });
                    }
                });

                var ekip_thuchien = new jBox('Modal', {
                    title: 'Thực hiện chấm công CĐHA',
                    overlay: false,
                    content: $('#ekip'),
                    draggable: 'title'
                });

                if (dvtt != "" && stt_benhan != "" && mabenhnhan != "" && ma_dv != "" && ten_dv != "" && sophieu != "") {
                    var arr = [dvtt, stt_benhan, mabenhnhan, ma_dv, sophieu,'SA'];
                    var url = 'hgi_ekip_chamcongthuchien_select_theobenhnhan?url=' + convertArray(arr);
                    $("#list_chitiettiencongthuchien").jqGrid('setGridParam', { datatype: 'json', url: url }).trigger('reloadGrid');
                    ekip_thuchien.open();
                }
            }
        }

        //VLG chinh textbox gio tra kq chay thoi gian
        function addZero(i) {
            if (i < 10) {
                i = "0" + i;
            }
            return i;
        }

        function gioThucHienClsTimerChange() {
            if (giothuchien_cls_timer_is_on || ($("#dathuchien").prop('checked') && ${capnhat_cls_timer_off} == 1))
                stopGioThucHienClsTimer();
            else if (tatAutoTime == 1)
                stopGioThucHienClsTimer();
            else
                showtime_giothuchien_cls();
        }

        function gioThCtTimerChange() {
            if("${Sess_DVTT}"=="14017" || "${Sess_DVTT}".substring(0,2) == "96"){
                stopGioThCtTimer();
            } else if (tatAutoTime == 1)
                stopGioThCtTimer();
            else if (gioth_ct_timer_is_on || ($("#gioth_ct").data('da-thuc-hien') && ${capnhat_cls_timer_off} == 1))
                stopGioThCtTimer();
            else
                showtime_gioth_ct();
        }
        if("${thamso_184001}"=="1"){
            var totalSeconds = null;
            var currentClientDate = null;
            var gio_sv, phut_sv, giay_sv;
            function Xulygiosever() {
                var now = new Date();
                var distance = now.getSeconds() - (currentClientDate || now).getSeconds();
                currentClientDate = now;
                if(!totalSeconds){
                    totalSeconds = ${gio} * 3600 + ${phut}*60 + ${giay};
                }else{
                    totalSeconds += distance < 0 ? 0 : distance;
                }
                var total = totalSeconds;
                var hours = Math.floor(total / 3600);
                total %= 3600;
                var minutes = Math.floor(total / 60);
                var seconds = total % 60;
                gio_sv = addZero(hours == 24 ? 0 : hours);
                phut_sv = addZero(minutes);
                giay_sv = addZero(seconds);
            }
        }

        if("${thamso_184001}" == "1"){
            function showtime_giothuchien_cls() {
                Xulygiosever();
                $('#giothuchien_cls').val(gio_sv + ":" + phut_sv + ":" + giay_sv);
                giothuchien_cls_timer = setTimeout(showtime_giothuchien_cls, 1000);
                giothuchien_cls_timer_is_on = true;
            }
        }
        else{
            function showtime_giothuchien_cls() {
                var thoigian = new Date();
                var gio = addZero(thoigian.getHours());
                var phut = addZero(thoigian.getMinutes());
                var giay = addZero(thoigian.getSeconds());
                /*if(showtime_giothuchien_cls_cancel !== 1) {
                        $('#giothuchien_cls').val(gio + ":" + phut + ":" + giay);
                    }
                    t = setTimeout(showtime_giothuchien_cls, 1000);*/
                $('#giothuchien_cls').val(gio + ":" + phut + ":" + giay);
                giothuchien_cls_timer = setTimeout(showtime_giothuchien_cls, 1000);
                giothuchien_cls_timer_is_on = true;
            }
        }


        if ("${thamso_184001}" == "1"){
            function showtime_gioth_ct(){
                Xulygiosever();
                $('#gioth_ct').val(gio_sv + ":" + phut_sv + ":" + giay_sv);
                gioth_ct_timer = setTimeout(showtime_gioth_ct, 1000);
                gioth_ct_timer_is_on = true;
            }
        }
        else{
            function showtime_gioth_ct() {
                var thoigian = new Date();
                var gio = addZero(thoigian.getHours());
                var phut = addZero(thoigian.getMinutes());
                var giay = addZero(thoigian.getSeconds());
                /*if(showtime_gioth_ct_cancel !== 1) {
                        $('#gioth_ct').val(gio + ":" + phut + ":" + giay);
                    }
                    t = setTimeout(showtime_gioth_ct, 1000);*/
                $('#gioth_ct').val(gio + ":" + phut + ":" + giay);
                gioth_ct_timer = setTimeout(showtime_gioth_ct, 1000);
                gioth_ct_timer_is_on = true;
            }
        }


        function stopCount() {
            clearTimeout(t);
            timer_is_on = 0;
        }

        function stopGioThucHienClsTimer() {
            clearTimeout(giothuchien_cls_timer);
            giothuchien_cls_timer_is_on = false;
        }

        function stopGioThCtTimer() {
            clearTimeout(gioth_ct_timer);
            gioth_ct_timer_is_on = false;
        }

        String.prototype.replaceAll = function (search, replacement) {
            var target = this;
            return target.split(search).join(replacement);
        };

        //VLG chinh textbox gio tra kq chay thoi gian
        function delete_thuocnoitru(list, nghiepvu) {
            var id = $("#list_thuocdichvu").jqGrid('getGridParam', 'selrow');
            if (id) {
                var ret = $("#list_thuocdichvu").jqGrid('getRowData', id);
                if (ret.MA_BAC_SI_THEMTHUOC === undefined || ret.MA_BAC_SI_THEMTHUOC === "${Sess_UserID}") {
                    jConfirm('Bạn có muốn xóa không?', 'Thông báo', function (r) {
                        if (r.toString() === "true") {
                            var url = "noitru_toathuoc_delete";
                            if (list === "list_thuoctonghopall")
                                nghiepvu = ret.NGHIEP_VU;
                            var arr = [ret.STT_TOATHUOC, matoathuoc, "${Sess_DVTT}", $("#sttdieutri").val(), $("#sttbenhan").val(), $("#sttdotdieutri").val(), sophieuthanhtoan,
                                ret.THANHTIEN_THUOC, ret.MAKHOVATTU, "0", nghiepvu, ret.MAVATTU, encodeURIComponent(ret.TEN_VAT_TU), ret.SO_LUONG];
                            var sophieu = $("#sophieu").val();
                            var ma_cdha = $("#macdha").val();
                            url += "?url=" + convertArray(arr);
                            if (sophieu) {
                                url += "&sophieu=" + sophieu;
                            }
                            if (ma_cdha) {
                                url += "&ma_cdha=" + ma_cdha;
                            }
                            $.post(url).done(function (data) {
                                if (data === "1")
                                    jAlert("Bệnh nhân đã thanh toán viện phí", 'Cảnh báo');
                                else if (data === "2")
                                    jAlert("Bệnh nhân đã được xuất thuốc", 'Cảnh báo');
                                else if (data == '100') {
                                    jAlert("Đã chốt báo cáo dược, không thể sửa/xóa", 'Cảnh báo');
                                }
                                else {
                                    $("#list_thuocdichvu").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                                }
                            });
                        }
                    });
                } else {
                    jAlert("Bạn không được xóa thuốc trong toa của bác sĩ khác!", 'Cảnh báo');
                }
            } else {
                jAlert("Chọn một dòng thuốc để xóa", 'Cảnh báo');
            }
        }

        function delete_toathuocngoaitru(list, nghiepvu) {
            var id = $("#list_thuocdichvu").jqGrid('getGridParam', 'selrow');
            if (id) {
                var url = "xuatduoc_giamtai_svv";
                $.post(url, {
                    nghiepvu: "ngoaitru_toadichvu",
                    matoathuoc: matoathuoc,
                    makhambenh: $("#makhambenh").val(),
                    xacnhan: "false",
                    mabenhnhan: mabenhnhan,
                    ngaykhambenh: ngay_kb
                }).done(function (data) {
                    if (data == "0") {
                        var ret = $("#list_thuocdichvu").jqGrid('getRowData', id);
                        var dongia = ret.DONGIA_BAN_BH;
                        var arr = [ret.STT_TOATHUOC, matoathuoc, ${Sess_DVTT}, $("#makhambenh").val(), sophieuthanhtoan, ret.THANHTIEN_THUOC,
                            ret.MAKHOVATTU, dongia, nghiepvu, ret.MAVATTU, $("#mabenhnhan").val(), ngay_kb, sovaovien, encodeURIComponent(ret.TEN_VAT_TU), ret.SO_LUONG];
                        var url = "xoathuocngoaitru_giamtai?url=" + convertArray(arr);
                        var sophieu = $("#sophieu").val();
                        var ma_cdha = $("#macdha").val();
                        if (sophieu) {
                            url += "&sophieu=" + sophieu;
                        }
                        if (ma_cdha) {
                            url += "&ma_cdha=" + ma_cdha;
                        }
                        $.ajax({
                            url: url
                        }).done(function (data) {
                            if (data === "1")
                                jAlert("Bệnh nhân đã thanh toán", 'Cảnh báo');
                            else if (data === "2")
                                jAlert("Bệnh nhân đã được xuất thuốc", 'Cảnh báo');
                            else if (data === "3")
                                jAlert("Bệnh nhân đã được trả thuốc về kho", 'Cảnh báo');
                            else if (data == '100') {
                                jAlert("Đã chốt báo cáo dược, không thể sửa/xóa", 'Cảnh báo');
                            }
                            else
                                $("#list_thuocdichvu").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                            var url = "xuatduoc_giamtai_svv";
                            $.post(url, {
                                nghiepvu: "ngoaitru_toadichvu",
                                matoathuoc: matoathuoc,
                                makhambenh: $("#makhambenh").val(),
                                xacnhan: "true",
                                mabenhnhan: mabenhnhan,
                                ngaykhambenh: ngay_kb
                            }).done(function (data) {

                            });
                        });

                    }
                });
            } else {
                jAlert("Chọn 1 dòng thuốc để xóa", 'Cảnh báo');
            }
        }

        function openEkip() {
            dialog_capnhatketquasieuam_ekip.dialog("open");
            var sophieu = $("#sophieu").val();
            var madv = $("#macdha").val();
            var noitru = $("#noitru").val();
            $("#ten_nhanvien").focus();
            $("#list_chitietvaitrothuchien").jqGrid({
                url: "danhsachvaitroekip_ttpt?sophieu_dv=" + sophieu + "&ma_dv=" + madv + "&noitru=" + noitru + "&sovaovien=" + sovaovien + "&sovaovien_noi=" + sovaovien_noi + "&sovaovien_dt_noi=" + sovaovien_dt_noi,
                datatype: "json",
                loadonce: true,
                height: 312,
                width: 650,
                colNames: ['ID_EKIP', 'Mã nhân viên', "Tên nhân viên", "Vai trò"],
                colModel: [
                    {name: 'ID_EKIP', index: 'ID_EKIP', width: 50, hidden: true},
                    {name: 'MA_NHANVIEN', index: 'MA_NHANVIEN', width: 50},
                    {name: 'TEN_NHANVIEN', index: 'TEN_NHANVIEN', width: 150},
                    {name: 'TEN_VAITRO', index: 'TEN_VAITRO', width: 150}
                ],
                sortname: 'MA_VAITRO',
                sortorder: "asc",
                caption: "Danh sách Ekip",
                ignoreCase: true,
                onSelectRow: function (id) {
                    if (id) {
                        var ret = $("#list_chitietvaitrothuchien").jqGrid('getRowData', id);
                        $("#frm_kq_id_ekip").val(ret.ID_EKIP);
                        //load_dmvaitrothuchien(ret);
                    }
                }
            });
            $("#list_chitietvaitrothuchien").jqGrid('setGridParam', {
                url: "danhsachvaitroekip_ttpt?sophieu_dv=" + sophieu + "&madv=" + madv + "&noitru=" + noitru + "&sovaovien=" + sovaovien + "&sovaovien_noi=" + sovaovien_noi + "&sovaovien_dt_noi=" + sovaovien_dt_noi,
                datatype: 'json'
            }).trigger('reloadGrid');
        }

        function combogridTen(ten, ma) {
            ten.combogrid({

                url: "ttpt_ekip_timkiem_bacsi?url=" + convertArray(["${Sess_DVTT}"]),

                debug: true,
                width: "300px",
                colModel: [{'columnName': 'TEN_PHONGBAN', 'label': 'Phòng Khoa', 'width': '30', 'align': 'left'},
                    {'columnName': 'TEN_NHANVIEN', 'width': '40', 'label': 'Tên nhân viên', 'align': 'left'},
                    {'columnName': 'MA_NHANVIEN', index: 'MA_NHANVIEN', hidden: true}
                ],
                select: function (event, ui) {
                    ma.val(ui.item.MA_NHANVIEN);
                    ten.val(ui.item.TEN_NHANVIEN.trim());
                    return false;
                }
            });
        }

        function combogridTenVaiTro(ten, ma) {
            ten.combogrid({

                url: "timkiem_vaitro_ekip?url=" + convertArray(["${Sess_DVTT}"]),
                debug: true,
                width: "300px",
                colModel: [{
                    'columnName': 'MA_VAITRO',
                    'label': 'Phòng Khoa',
                    'width': '30',
                    'align': 'left',
                    hidden: true
                },
                    {'columnName': 'TEN_VAITRO', 'width': '40', 'label': 'Tên vai trò', 'align': 'left'}

                ],
                select: function (event, ui) {
                    ma.val(ui.item.MA_VAITRO);
                    ten.val(ui.item.TEN_VAITRO.trim());
                    return false;
                }
            });
        }

        function combogridMaBS(ten, ma) {
            ma.combogrid({

                url: "noitru_hoichan_timkiem_mabacsi?url=" + convertArray(["${Sess_DVTT}"]),

                debug: true,
                width: "300px",
                colModel: [{'columnName': 'MA_NHANVIEN', 'label': 'Mã nhân viên', 'width': '30', 'align': 'left'},
                    {'columnName': 'TEN_NHANVIEN', 'width': '40', 'label': 'Tên nhân viên', 'align': 'left'},
                ],
                select: function (event, ui) {
                    ma.val(ui.item.MA_NHANVIEN);
                    ten.val(ui.item.TEN_NHANVIEN.trim());
                    return false;
                }
            });
        }

        function nextText(current, next) {
            current.keypress(function (evt) {
                if (evt.keyCode == 13) {
                    next.focus();
                }
            });

        }

        function capNhatEkip(capnhat) {
            if (capnhat == 1) {
                if ($("#ma_nhanvien").val() != null && $("#ma_vaitro").val() != null) {
                    $("#frm_kq_id_ekip").val("");
                    //$("#use_ekip").val("true");
                    $.ajax({
                        type: "POST",
                        contentType: 'application/json; charset=utf-8',
                        dataType: 'json',
                        url: "capnhat_cd_dichvu_ekip",
                        data: JSON.stringify(createJson()), // Note it is important
                    }).success(function () {
                        // jAlert("Cập nhật Ekip thành công", "Thông báo");
                        $("#list_chitietvaitrothuchien").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                        $("#ma_nhanvien").val("");
                        $("#ten_nhanvien").val("");
                        $("#ma_vaitro").val("");
                        $("#ten_vaitro").val("");
                    }).fail(function () {
                        jAlert("Cập nhật Ekip không thành công", "Cảnh báo");
                    });
                }

            } else {
                $("#ma_nhanvien").val("");
                $("#ten_nhanvien").val("");
                $("#ma_vaitro").val("");
                $("#ten_vaitro").val("");
                var url = String.format("delete_cd_dichvu_ekip?id_ekip={0}", $("#frm_kq_id_ekip").val());
                $.ajax({
                    url: url
                }).success(function () {
                    jAlert("Đã xoá Ekip", "Thông báo");
                    $("#list_chitietvaitrothuchien").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                }).fail(function () {
                    jAlert("Xoá Ekip không thành công", "Cảnh báo");
                });
            }

        }

        function createJson() {
            var jsondata = {};
            var alertString = null;
            $("form#form_Ekip").find(".post-data").each(function (index, element) {
                var tag = $(element);
                var type = tag.attr("type");
                var key = tag.attr("key");
                var require = tag.attr("required");
                var value = tag.val();
                if (require == true) {
                    alertString = tag.attr("error-message").val();
                }
                if (alertString != null) {
                    jAlert(alertString, "Cảnh báo");
                    return;
                }
                jsondata[key] = value;
            })
            return jsondata;
        };
        function newStringDateTime() {
            var date = new Date();
            var dateStr =
                ("00" + date.getDate()).slice(-2) + "/" +
                ("00" + (date.getMonth() + 1)).slice(-2) + "/" +
                date.getFullYear() + " " +
                ("00" + date.getHours()).slice(-2) + ":" +
                ("00" + date.getMinutes()).slice(-2) + ":" +
                ("00" + date.getSeconds()).slice(-2);
            return dateStr;
        }
        window.listIdTimer = [];
        window.listIdTimerRunning = [];
        function runningInputTimer() {
            var listIdTimer = window.listIdTimer;
            var listIdTimerRunning = window.listIdTimerRunning;
            var timerSubfix = "_timer";
            listIdTimerRunning.forEach(function (item) {
                clearTimeout(window[item + timerSubfix]);// stop all timer in list
            });
            window.listIdTimerRunning = [];
            listIdTimer.forEach(function (item) {
                window[item + timerSubfix] = setTimeout(runningInputTimer, 1000);
                window.listIdTimerRunning.push(item);
                $("#" + item).val(newStringDateTime());
                // console.log(newStringDateTime());
                // console.log($("#" + item).val());
            });
        }
        function changeInputTimerStatus(selectorId, isStopTimer = false) {
            if (!isStopTimer && $.inArray(selectorId, window.listIdTimer) == -1) {
                window.listIdTimer.push(selectorId);
            }
            if (isStopTimer) {
                window.listIdTimer = $.grep(window.listIdTimer, function(n) {
                    return n != selectorId;
                });
            }
            if(window.listIdTimer && window.listIdTimer.length > 0) {
                runningInputTimer();
            }
            // console.log($("#" + selectorId).val());
        }
        $(function () {
            if("${Sess_DVTT}" == "96166"){
                $(".block_bscd").prop("hidden", true);
                $(".bscd_chitiet").prop("hidden", false);
            }

            //VNPTHIS-4697 23/11/2017
            $("#mausieuam").selectpicker('val',0);
            if ("${Sess_DVTT}".substring(0, 2) == "94") {
                $("#loaiin").val("2");
            }
            //VNPTHIS-4697 23/11/2017
            //VNPTHIS-7757
            if ("${hgi_loaiin}" == "1") {
                $("#loaiin").val("2");
            }
            //VNPTHIS-7757
            $(":input").inputmask();
            $("#ngaythuchien_cls").datepicker();
            $("#ngaythuchien_cls").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngaythuchien_cls").val("${ngayhientai}");

            $("#ngayth_ct").datepicker();
            $("#ngayth_ct").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngayth_ct").val("${ngayhientai}");

            $(":input").inputmask();
            $("#ngaythuchien").datepicker();
            $("#ngaythuchien").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngaythuchien").val("${ngayhientai}");
            $("#tungay1").datepicker();
            $("#tungay1").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#tungay1").val("${ngayhientai}");
            $("#denngay1").datepicker();
            $("#denngay1").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#denngay1").val("${ngayhientai}");
            //$("#checkbox_sa").css("display","none");
            khoiTaoSuKienInputIcd("#icdChanDoanCanLamSang","#btnChanDoanCanLamSang","#tenChanDoanCanLamSang","#maBenhLyChanDoanCanLamSang");
            $("#thoiGianBatDau_cls").inputmask({
                mask: "1/2/y h:s:s",
                placeholder: "dd/mm/yyyy 00:00:00",
                alias: "datetime",
                hourFormat: "24"
            });
            $("#thoiGianBatDau_cls").datetimepicker({
                dateFormat: 'dd/mm/yy',
                timeFormat: 'HH:mm:ss',
                hourFormat: "24"
            });
            /*$("#thoiGianBatDau_cls").click(function (evt) {
                changeInputTimerStatus("thoiGianBatDau_cls", true);
            }).change(function (evt) {
                changeInputTimerStatus("thoiGianBatDau_cls", true);
            }).dblclick(function(){
                changeInputTimerStatus("thoiGianBatDau_cls");
            });*/
            $("#thoiGianBatDau_cls").val(newStringDateTime());
            //changeInputTimerStatus("thoiGianBatDau_cls");
            // HPG--Timkiem benh nhan CLS tu ngay den ngay
            if ("${timkiem_cls}" == "1") {
                $(".hpg_tmp").show();
                $("#tungay").datepicker();
                $("#tungay").datepicker("option", "dateFormat", "dd/mm/yy");
                $("#tungay").val("${ngayhientai}");
                $("#tungay").change(function (evt) {
                    reload_grid();
                });
            } else
                $(".hpg_tmp").hide();
            //--
            // ĐắkLắk: tùy chọn hiển thị lọc theo khoa, phòng, đối tượng
            if ("${choloctheokhoaphong_capnhatketquacls}" == "1")
                $(".dlk_tmp").show();
            else
                $(".dlk_tmp").hide();
            // End ĐắkLắk
            if ("${hienthi_mausac_bn}" == "1") {
                $(".ghichutrangthai").show();
                $(".mausacmacdinh").hide();
            } else {
                $(".ghichutrangthai").hide();
                $(".mausacmacdinh").show();
            }
            //-------
            if ("${hienthi_them_cls}" == "1")
                $(".hpg_hienthithem").show();
            else
                $(".hpg_hienthithem").hide();
            //--End HPG
            if ("${thuocdvthcls}" == "1") {
                $("#thuoccls").show();
            } else {
                $("#thuoccls").hide();
            }
            if ("${tichhopdsbncd}" == "1") {
                $("#form1").hide();
                $("#formdsbn").show();
            } else {
                $("#formdsbn").hide();
                $("#form1").show();
            }
            var dataURL = "";


            $('#browse-file').on('change', function (evt) {
                // BDG nhập file ảnh từ máy tính - report_BinhDuong_CDHA_v1.0.0.doc
//=======
//                //-- VNPT STG 18/4/2017 thêm duyệt ảnh từ máy tính
//                $('#browse-file').on('change', function(evt) {
//>>>>>>> d441356425f8caf2ff9d7b428bbc7a5d72a7ff61
                var bg_preview = document.getElementById('say-cheese-snapshots').childNodes[0];
                var bg_file = document.getElementById('browse-file').files[0];
                var bg_reader = new FileReader();

                bg_reader.addEventListener("load", function () {
                    bg_preview.src = bg_reader.result;
                    dataURL = bg_reader.result;
                }, false);

                if (bg_file) {
                    bg_reader.readAsDataURL(bg_file);
                }
            });
            $('#lssieuam').on('click', function (evt) {
                $("#lichsusieuam").click();
            });

            dialog_capnhatketquasieuam_ekip = $("#dialog_capnhatketquasieuam_ekip").dialog({
                autoOpen: false,
                width: 700,
                modal: true,
                resizable: false,
                position: {my: "center top", at: "center top", of: window}
            });
            $.ajax({
                method : "GET",
                url : "cmu_getlist?url="+convertArray(["${Sess_DVTT}","STG_TAM_SELECT_PHONG_BAN"]),
                success: function(data) {
                    data.forEach(function(_obj) {
                        $("#khoabs_cmu").append("<option value="+_obj.MAKHOA+">"+_obj.TENKHOA+"</option>");
                    })
                }
            })
            $("#khoabs_cmu").change(function (evt) {
                var url = "laybacsi_theokhoa?khoa=" + $("#khoabs_cmu").val();
                $.ajax({
                    url: url
                }).done(function (data) {
                    if (data) {
                        $("#cmu_cbbacsidieutri").empty();
                        $.each(data, function (i) {
                            if (i == 0)
                                $("#cmu_cbbacsidieutri").val(data[i].MA_NHANVIEN);
                            $("<option value='" + data[i].MA_NHANVIEN + "'>" + data[i].TEN_NHANVIEN + "</option>").appendTo("#cmu_cbbacsidieutri");
                        });
                    }
                });
            });

            nextText($("#ten_nhanvien"), $("#ten_vaitro"));
            combogridMaBS($("#ten_nhanvien"), $("#ma_nhanvien"));
            combogridTen($("#ten_nhanvien"), $("#ma_nhanvien"));
            combogridTenVaiTro($("#ten_vaitro"), $("#ma_vaitro"));

            $("#ten_vaitro").keypress(function (evt) {
                if (evt.keyCode == 13) {
                    capNhatEkip(1);
                    $("#ten_nhanvien").focus();
                }
            });

            $("#form_sieuammau_doppler").click(function (evt) {
                form_sa_mau_doppler.dialog("open");
                var sophieu = $("#sophieu").val();
                var dvtt = "${Sess_DVTT}";
                var macdha = $("#macdha").val();
                var mabenhnhan = $("#mabenhnhan").val();
                var arr = [
                    sophieu, dvtt, macdha, mabenhnhan, 0
                ];
                var url = "hienthi_sa_mau_doppler?url=" + convertArray(arr);
                $("#txt_Ao").val("");
                $("#txt_La").val("");
                $("#txt_Avd").val("");
                $("#txt_IVSd").val("");
                $("#txt_IVSs").val("");
                $("#txt_LVDd").val("");
                $("#txt_LVDs").val("");
                $("#txt_LVPWd").val("");
                $("#txt_LVPWs").val("");
                $("#txt_EF").val("");
                $("#txt_FS").val("");
                $("#txt_buongtim").val("Không dãn");
                $("#rl_vandongvung").val("Không");
                $("#txt_vachlienthat").val("Nguyên vẹn");
                $("#txt_vachliennhi").val("Nguyên vẹn");
                $("#txt_mangngoaitim").val("Không tràn dịch");
                $("#vandekhac_mau").val("");
                $("#txt_Ann").val("");
                $("#txt_vongvan_bala").val("");
                $("#txt_goc_ann").val("");
                $("#txt_phai").val("");
                $("#txt_trai").val("");
                $("#txt_than").val("");
                $("#txt_chieudai_truoc_vanhaila").val("");
                $("#txt_E").val("");
                $("#txt_A").val("");
                $("#txt_EPG").val("");
                $("#txt_APG").val("");
                $("#txt_hovan_2la").val("Không");
                $("#txt_hepvan_2la").val("Không");
                $("#txt_Vmax").val("");
                $("#txt_Vmean").val("");
                $("#txt_PGmean").val("");
                $("#txt_MVA").val("");
                $("#txt_Vmax_dmc").val("");
                $("#txt_Vmean_dmc").val("");
                $("#txt_PGmax_dmc").val("");
                $("#txt_PGmean_dmc").val("");
                $("#txt_hovan_DMC").val("Không");
                $("#txt_hepvan_DMC").val("Không");
                $("#txt_DK_dongho").val("");
                $("#txt_dolan_dongho").val("");
                $("#txt_ho_3la").val("");
                $("#txt_vmax_vanbala").val("");
                $("#txt_PGmax_vanbala").val("");
                $("#txt_PAPx").val("");
                $("#txt_Vmax_dmp").val("");
                $("#txt_Vmean_dmp").val("");
                $("#txt_PGmax_dmp").val("");
                $("#txt_PGmean_dmp").val("");
                $("#txt_hepvan_DMP").val("");
                $("#txt_PAPm").val("");
                $("#txt_PAPd").val("");
                $("#dongbatthuong_vachlienthat").val("");
                $("#dongbatthuong_vachliennhi").val("");
                $("#vandekhac_doppler").val("");
                $("#ketluan_doppler").val("");
                $("#ketluan_doppler_mau").val("");
                $("#tile").html("");
                $.getJSON(url, function (result) {
                    $.each(result, function (i, field) {
                        $("#txt_Ao").val(field.AO);
                        $("#txt_La").val(field.LA);
                        $("#txt_Avd").val(field.AVD);
                        $("#txt_IVSd").val(field.IVSD);
                        $("#txt_IVSs").val(field.IVSS);
                        $("#txt_LVDd").val(field.LVDD);
                        $("#txt_LVDs").val(field.LVDS);
                        $("#txt_LVPWd").val(field.LVPWD);
                        $("#txt_LVPWs").val(field.LVPWS);
                        $("#txt_EF").val(field.EF);
                        $("#txt_FS").val(field.FS);
                        $("#txt_buongtim").val(field.CAC_BUONG_TIM);
                        //var buongtim = $("#txt_buongtim").val(field.CAC_BUONG_TIM);
                        //if(buongtim)
                        $("#rl_vandongvung").val(field.RL);
                        $("#txt_vachlienthat").val(field.VACH_LIEN_THAT);
                        $("#txt_vachliennhi").val(field.VACH_LIEN_NHI);
                        $("#txt_mangngoaitim").val(field.MANG_NGOAI_TIM);
                        $("#vandekhac_mau").val(field.VANDEKHAC_MAU);
                        $("#txt_Ann").val(field.ANN);
                        $("#txt_vongvan_bala").val(field.VONG_VAN_BALA);
                        $("#txt_goc_ann").val(field.GOC_ANN);
                        $("#txt_phai").val(field.PHAI);
                        $("#txt_trai").val(field.TRAI);
                        $("#txt_than").val(field.THAN);
                        $("#txt_chieudai_truoc_vanhaila").val(field.CHIEUDAI_TRUOC_VANHAILA);
                        $("#txt_E").val(field.VANTOC_DIEME);
                        $("#txt_A").val(field.VANTOC_DIEMA);
                        $("#txt_EPG").val(field.EPG);
                        $("#txt_APG").val(field.APG);
                        $("#txt_hovan_2la").val(field.HOVAN_HAILA);
                        $("#txt_hepvan_2la").val(field.HEPVAN_HAILA);
                        $("#txt_Vmax").val(field.VMAX);
                        $("#txt_Vmean").val(field.VMEAN);
                        $("#txt_PGmean").val(field.MVA);
                        $("#txt_MVA").val(field.MVA);
                        $("#txt_Vmax_dmc").val(field.VMAX_DMC);
                        $("#txt_Vmean_dmc").val(field.VMEAN_DMC);
                        $("#txt_PGmax_dmc").val(field.PGMAX_DMC);
                        $("#txt_PGmean_dmc").val(field.PGMEAN_DMC);
                        $("#txt_hovan_DMC").val(field.HOVAN_DMC);
                        $("#txt_hepvan_DMC").val(field.HEPVAN_DMC);
                        $("#txt_DK_dongho").val(field.DK_DONGHO);
                        $("#txt_dolan_dongho").val(field.DOLAN_DONGHO);
                        $("#txt_ho_3la").val(field.HOBALA);
                        $("#txt_vmax_vanbala").val(field.VMAX_VANBALA);
                        $("#txt_PGmax_vanbala").val(field.PGMAX_VANBALA);
                        $("#txt_PAPx").val(field.PAPX_VANBALA);
                        $("#txt_Vmax_dmp").val(field.VMAX_DMP);
                        $("#txt_Vmean_dmp").val(field.VMEAN_DMP);
                        $("#txt_PGmax_dmp").val(field.PGMAX_DMP);
                        $("#txt_PGmean_dmp").val(field.PGMEAN_DMP);
                        $("#txt_hepvan_DMP").val(field.HEPVAN_DMP);
                        $("#txt_PAPm").val(field.PAPM);
                        $("#txt_PAPd").val(field.PAPD);
                        $("#dongbatthuong_vachlienthat").val(field.DONGBATTHUONG_VACHLIENTHAT);
                        $("#dongbatthuong_vachliennhi").val(field.DONGBATTHUONG_VACHLIENNHI);
                        $("#vandekhac_doppler").val(field.VANDEKHAC_DOPPLER);
                        $("#ketluan_doppler").val(CKEDITOR.instances.ketluan.document.getBody().getText());
                        $("#ketluan_doppler_mau").val(CKEDITOR.instances.ketluan.document.getBody().getText());
                        var tile_E = $("#txt_E").val();
                        tile_E = tile_E.replace(',', '.');
                        var tile_A = $("#txt_A").val();
                        tile_A = tile_A.replace(',', '.');
                        var result = tile_E / tile_A;
                        var n = parseFloat(result);
                        result = Math.round(n * 100) / 100;
                        $("#tile").html(result);
                    });
                });
            });
            //Lấy Chức Danh
            $.ajax({
                url: "select_tenbacsi?mabacsi=" + ${Sess_UserID} +"&dvtt= " + "${Sess_DVTT}"
            }).done(function (data) {
                nv_with_chucdanh = data;
            });
            <c:forEach var="i" items="${formsieuam}">
            $("#tab_sieuam_" + "${i.MA_MAUSIEUAM}").tabs();
            </c:forEach>
            $("#tab_sa_mau_doppler").tabs();
            /*form_sa_mau_doppler = new jBox('Modal', {
                    overlay: false,
                    content: $('#dialog_sa_mau_doppler'),
                    position: {
                        x: 'center',
                        y: 'top'
                    },
                    blockScroll:true
                });*/
            form_sa_mau_doppler = $("#dialog_sa_mau_doppler").dialog({
                title: "Siêu Âm Màu Doppler",
                autoOpen: false,
                modal: true,
                resizable: false,
                width: "auto",
                position: {my: "center", at: "top", of: window}
            });
            //-- VNPT STG 18/4/2017 thêm duyệt ảnh từ máy tính

            $('#save-snapshot').on('click', function (evt) {
                var macdha = $("#macdha").val();
                var sophieu = $("#sophieu").val();
                var noitru = $("#noitru").val();
                var makhambenh = $("#makhambenh").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                if (dataURL != "" && sophieu != "" && macdha != "") {
                    $.post("luuhinhanh_sieuam", {
                        sophieu: sophieu,
                        macdha: macdha,
                        dvtt: "${Sess_DVTT}",
                        hinh: dataURL,
                        noitru: noitru,
                        sttbenhan: sttbenhan,
                        sttdotdieutri: sttdotdieutri,
                        sttdieutri: sttdieutri,
                        makhambenh: makhambenh
                    })
                        .done(function () {
                            var arr = [sophieu, "${Sess_DVTT}", macdha, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                            var url = 'sieuam_danhsach_hinhanh?url=' + convertArray(arr);
                            $("#list_hinhanhnoisoi").jqGrid('setGridParam', {
                                datatype: 'json',
                                url: url
                            }).trigger('reloadGrid');
                        });
                }
            });
            //Fix remove audio
            var sayCheese = new SayCheese('#say-cheese-container', {snapshots: true});
            sayCheese.on('start', function () {
                $('#action-buttons').fadeIn('fast');
                $('*').keyup(function (e) {
                    if (e.keyCode === 113) {
                        sayCheese.takeSnapshot();
                        var macdha = $("#macdha").val();
                        var sophieu = $("#sophieu").val();
                        var noitru = $("#noitru").val();
                        var makhambenh = $("#makhambenh").val();
                        var sttbenhan = $("#sttbenhan").val();
                        var sttdotdieutri = $("#sttdotdieutri").val();
                        var sttdieutri = $("#sttdieutri").val();
                        if (dataURL != "" && sophieu != "" && macdha != "") {
                            $.post("luuhinhanh_sieuam", {
                                sophieu: sophieu,
                                macdha: macdha,
                                dvtt: "${Sess_DVTT}",
                                hinh: dataURL,
                                noitru: noitru,
                                sttbenhan: sttbenhan,
                                sttdotdieutri: sttdotdieutri,
                                sttdieutri: sttdieutri,
                                makhambenh: makhambenh
                            })
                                .done(function () {
                                    var arr = [sophieu, "${Sess_DVTT}", macdha, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                                    var url = 'sieuam_danhsach_hinhanh?url=' + convertArray(arr);
                                    $("#list_hinhanhnoisoi").jqGrid('setGridParam', {
                                        datatype: 'json',
                                        url: url
                                    }).trigger('reloadGrid');
                                });
                        }
                        return false;
                    }
                });
                $('#take-snapshot').on('click', function (evt) {
                    sayCheese.takeSnapshot();
                });
                //CMU: 12/10/2017
                $('#take-save-snapshot').on('click', function (evt) {
                    sayCheese.takeSnapshot();
                    $('#save-snapshot').trigger("click");
                });
                $('#say-cheese-container').on('click', function (evt) {
                    sayCheese.takeSnapshot();
                    $('#save-snapshot').trigger("click");
                });
                //END CMU: 12/10/2017
            });
            CKEDITOR.instances["ketqua"].on('key', function (e) {
//                    F2 luu anh
                if (e.data.keyCode === 113) {
                    var macdha = $("#macdha").val();
                    var sophieu = $("#sophieu").val();
                    var noitru = $("#noitru").val();
                    var makhambenh = $("#makhambenh").val();
                    var sttbenhan = $("#sttbenhan").val();
                    var sttdotdieutri = $("#sttdotdieutri").val();
                    var sttdieutri = $("#sttdieutri").val();
                    if (dataURL != "" && sophieu != "" && macdha != "") {
                        $.post("luuhinhanh_sieuam", {
                            sophieu: sophieu,
                            macdha: macdha,
                            dvtt: "${Sess_DVTT}",
                            hinh: dataURL,
                            noitru: noitru,
                            sttbenhan: sttbenhan,
                            sttdotdieutri: sttdotdieutri,
                            sttdieutri: sttdieutri,
                            makhambenh: makhambenh
                        })
                            .done(function () {
                                var arr = [sophieu, "${Sess_DVTT}", macdha, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                                var url = 'sieuam_danhsach_hinhanh?url=' + convertArray(arr);
                                $("#list_hinhanhnoisoi").jqGrid('setGridParam', {
                                    datatype: 'json',
                                    url: url
                                }).trigger('reloadGrid');
                            });
                    }
                    return false;
                }

            });
            CKEDITOR.instances["ketluan"].on('key', function (e) {
//                    F2 luu anh
                if (e.data.keyCode === 113) {
                    var macdha = $("#macdha").val();
                    var sophieu = $("#sophieu").val();
                    var noitru = $("#noitru").val();
                    var makhambenh = $("#makhambenh").val();
                    var sttbenhan = $("#sttbenhan").val();
                    var sttdotdieutri = $("#sttdotdieutri").val();
                    var sttdieutri = $("#sttdieutri").val();
                    if (dataURL != "" && sophieu != "" && macdha != "") {
                        $.post("luuhinhanh_sieuam", {
                            sophieu: sophieu,
                            macdha: macdha,
                            dvtt: "${Sess_DVTT}",
                            hinh: dataURL,
                            noitru: noitru,
                            sttbenhan: sttbenhan,
                            sttdotdieutri: sttdotdieutri,
                            sttdieutri: sttdieutri,
                            makhambenh: makhambenh
                        })
                            .done(function () {
                                var arr = [sophieu, "${Sess_DVTT}", macdha, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                                var url = 'sieuam_danhsach_hinhanh?url=' + convertArray(arr);
                                $("#list_hinhanhnoisoi").jqGrid('setGridParam', {
                                    datatype: 'json',
                                    url: url
                                }).trigger('reloadGrid');
                            });
                    }
                    return false;
                } else if (e.data.keyCode === 9) {
                    if ("${Sess_DVTT}" === "70001") {
                        CKEDITOR.instances.ketluan.setData(CKEDITOR.instances.ketluan.document.getBody().getText().toUpperCase())
                    } else {
                        var noidung = CKEDITOR.instances.ketluan.getData();
                        var key = $(noidung).text().split(/[\s\n]/g).pop();
                        $.ajax({
                            url: 'getNoiDungVietTat?key=' + key,
                            type: 'GET',
                            success: function (data) {
                                if (data.length > 0) {
                                    var arrNoiDung = noidung.split(/[\s\n]/g);
                                    arrNoiDung.pop();
                                    if (arrNoiDung.length > 1){
                                        arrNoiDung.pop();
                                        CKEDITOR.instances.ketluan.setData(arrNoiDung.join(' ') + ' ' + data[0].NOIDUNGVT + '&nbsp;</p>');
                                    } else {
                                        CKEDITOR.instances.ketluan.setData('<p>' + data[0].NOIDUNGVT + '&nbsp;</p>');
                                    }
                                }
                                CKEDITOR.instances.ketluan.focus();
                            }
                        })
                        setTimeout( function () {
                                focusEndOfText('ketluan');
                            }, 200
                        )
                    }
                }
            });

            function focusEndOfText(instance) {
                var editor;
                if (instance === 'ketqua') {
                    editor = CKEDITOR.instances.ketqua;
                } else {
                    editor = CKEDITOR.instances.ketluan;
                }
                editor.focus();
                var selection = editor.getSelection();
                var range = selection.getRanges()[0];
                var pCon = range.startContainer.getAscendant({p:2},true); //getAscendant('p',true);
                var newRange = new CKEDITOR.dom.range(range.document);
                newRange.moveToPosition(pCon, CKEDITOR.POSITION_BEFORE_END);
                newRange.select();
            }

            sayCheese.on('error', function (error) {
                var $alert = $('<div>');
                $alert.addClass('alert alert-error').css('margin-top', '20px');
                if (error === 'NOT_SUPPORTED') {
                    $alert.html("<strong>:(</strong> your browser doesn't support video yet!");
                } else if (error === 'AUDIO_NOT_SUPPORTED') {
                    $alert.html("<strong>:(</strong> your browser doesn't support audio yet!");
                } else {
                    $alert.html("<strong>:(</strong> you have to click 'allow' to try me out!");
                }

                $('.say-cheese').prepend($alert);
            });
            sayCheese.on('snapshot', function (snapshot) {
                var img = document.createElement('img');
                $(img).on('load', function () {
                    $('#say-cheese-snapshots').html(img);
                });
                img.src = snapshot.toDataURL('image/png');
                dataURL = snapshot.toDataURL('image/png');
                //var r = dataURL.replace("data:image/png;base64,", "");
                //alert(dataURL);
            });
            sayCheese.start();
            $("#list_benhnhan").jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 345,
                width: 300,
                colNames: ["STT", "TT", "Mã Y tế", "Họ tên", "TENBENHNHAN", "Tuổi", "Nội trú", "gioitinh", "diachi", "sobhyt", "SO_PHIEU", "MA_KHAM_BENH",
                    "stt_benhan", "stt_dotdieutri", "stt_dieutri", "NGUOI_CHI_DINH", "BACSI_THUCHIEN", "PHONG_CHI_DINH", "MA_PHONG_XN", "KET_QUA_CDHA", "MA_PHONGBAN", "tenkhoa", "SOVAOVIEN", "SOVAOVIEN_NOI", "SOVAOVIEN_DT_NOI", "DA_THANH_TOAN", "CAPCUU",
                    "UU_TIEN",
                    //ĐLK Thêm chuẩn đoán ICD, KGG thêm ngay sinh gọi số
                    "CHUANDOANICD","CHUANDOANSOBO", "TENKHOA", "sophieuthanhtoan", "tilemiengiam", "ngay_kb", "namsinh", "NGAY_SINH", "DA_THANH_TOAN", "CO_BHYT", "NGUOI_THUC_HIEN","SOBENHAN", "SOBENHAN_TT", "ICD", "NGAYTHUCHIEN", "NGAY_CHI_DINH"], // TGDEV-36575 thêm NGAYTHUCEHIEN
                //End DLK
                colModel: [
                    <c:choose>
                    <c:when test="${Sess_DVTT =='86003'}">
                    {name: 'STT_CD', index: 'STT_CD', width: 50},
                    </c:when>
                    <c:otherwise>
                    {name: 'STT_CD', index: 'STT_CD', width: 50,hidden:true},
                    </c:otherwise>
                    </c:choose>
                    {name: 'STT_HANGNGAY', index: 'STT_HANGNGAY', width: 30},
                    {name: 'MABENHNHAN', index: 'MABENHNHAN', hidden: false, width: 50},
                    {
                        name: 'TENBENHNHAN_HT',
                        index: 'TENBENHNHAN_HT',
                        hidden: false,
                        width: 200,
                        formatter: function (cellvalue, options, rowObject) {
                            var color;
                            var color_text;
                            if ("${hienthi_mausac_bn}" == 0) {
                                if (rowObject.DA_THANH_TOAN == "1") {
                                    color = '#009900';
                                    color_text = 'white';
                                } else {
                                    color = 'white';
                                    color_text = 'black';
                                }
                            }   //END VTU:25/10/2016
                            return '<span class="cellWithoutBackground" style="background-color:' + color + ';font-weight:bold ;color:' + color_text + '">' + cellvalue + '</span>';
                        }
                    },
                    {
                        name: 'TENBENHNHAN',
                        index: 'TENBENHNHAN',
                        hidden: true,
                        width: 200,
                        searchoptions: {
                            dataInit: function (el) {
                                setTimeout(function () {
                                    $(el).focus().trigger({type: 'keypress', charCode: 13});
                                }, 20);
                            }
                        }
                    },
                    {name: 'TUOI', index: 'TUOI', width: 50, align: 'right'},
                    {name: 'NOITRU', index: 'NOITRU', width: 40, align: 'center'},
                    {name: 'GIOITINH', index: 'GIOITINH', hidden: true},
                    {name: 'DIACHI', index: 'DIACHI', hidden: true},
                    {name: 'SOTHEBHYT', index: 'SOTHEBHYT', hidden: true},
                    {name: 'SO_PHIEU', index: 'SO_PHIEU', hidden: true},
                    {name: 'MA_KHAM_BENH', index: 'MA_KHAM_BENH', hidden: true},
                    {name: 'STT_BENHAN', index: 'STT_BENHAN', hidden: true},
                    {name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', hidden: true},
                    {name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', hidden: true},
                    {name: 'NGUOI_CHI_DINH', index: 'NGUOI_CHI_DINH', hidden: true},
                    {name: 'BACSI_THUCHIEN', index: 'BACSI_THUCHIEN', hidden: true},
                    {name: 'PHONG_CHI_DINH', index: 'PHONG_CHI_DINH', hidden: true},
                    {name: 'MA_PHONG_CDHA', index: 'MA_PHONG_XN', hidden: true},
                    {name: 'KET_QUA_CDHA', index: 'KET_QUA_CDHA', hidden: true},
                    {name: 'TENKHOA', index: 'TENKHOA', hidden: true},
                    {name: 'SOVAOVIEN', index: 'SOVAOVIEN', hidden: true},
                    {name: 'SOVAOVIEN_NOI', index: 'SOVAOVIEN_NOI', hidden: true},
                    {name: 'SOVAOVIEN_DT_NOI', index: 'SOVAOVIEN_DT_NOI', hidden: true},
                    {name: 'DA_THANH_TOAN', index: 'DA_THANH_TOAN', hidden: true},
                    {name: 'CAPCUU', index: 'CAPCUU', hidden: true},
                    {
                        name: 'UU_TIEN',
                        index: 'UU_TIEN',
                        hidden: true,
                        formatter: 'checkbox',
                        formatoptions: {value: 'true:false'},
                    },
                    {name: 'CHUANDOANICD', index: 'CHUANDOANICD', hidden: true},
                    {name: 'CHUANDOANSOBO', index: 'CHUANDOANSOBO', hidden: true},
                    {name: 'MA_PHONGBAN', index: 'MA_PHONGBAN', hidden: true},
                    {name: 'TENKHOA', index: 'TENKHOA', hidden: true},
                    {name: 'SOPHIEUTHANHTOAN', index: 'SOPHIEUTHANHTOAN', hidden: true},
                    {name: 'TI_LE_MIEN_GIAM', index: 'TI_LE_MIEN_GIAM', hidden: true},
                    {name: 'NGAY_KB', index: 'NGAY_KB', hidden: true},
                    {name: 'NAMSINH', index: 'NAMSINH', hidden: true},
                    {name: 'NGAY_SINH', index: 'NGAY_SINH', hidden: true},
                    {name: 'DA_THANH_TOAN', index: 'DA_THANH_TOAN', hidden: true},
                    {name: 'CO_BHYT', index: 'CO_BHYT', hidden: true},
                    {name: 'NGUOI_THUC_HIEN', index: 'NGUOI_THUC_HIEN', hidden: true},
                    {name: 'SOBENHAN', index: 'SOBENHAN', hidden: true},
                    {name: 'SOBENHAN_TT', index: 'SOBENHAN_TT', hidden: true},
                    {name: 'ICD', index: 'ICD', hidden: true},
                    {name: 'NGAY_THUC_HIEN', index: 'NGAY_THUC_HIEN', hidden: true}, // TGGDEV-36575 thêm
                    {name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', hidden: true}
                ],
                caption: "Danh sách bệnh nhân",
                ignoreCase: true,
                rowNum: 1000000,
                gridComplete: function () {
                    var c = $("#list_benhnhan").getGridParam("records");
                    $("#list_benhnhan").jqGrid('setCaption', "Danh sách bệnh nhân (" + c + " bệnh nhân)");
                    if ("${hienthi_mausac_bn}" == "1") {

                        var rows = $('#list_benhnhan').jqGrid('getGridParam','data');

                        rows.forEach(function (object) {
                            var CAPCUU = object["CAPCUU"];
                            var tuoi = "";
                            tuoi = object["TUOI"];
                            var thanhtoan = object["DA_THANH_TOAN"];
                            var SOTHEBHYT =object["SOTHEBHYT"];//CMU:26/10/2017 Chi tiết
                            var CO_BHYT = object["CO_BHYT"];
                            if (CAPCUU == "1") {
                                $("#list_benhnhan").jqGrid('setRowData', object._id_, false, {
                                    color: 'red',
                                    weightfont: 'bold'
                                });
                            } else if (CO_BHYT == 0 && thanhtoan == 0) {//CMU:26/10/2017
                                $("#list_benhnhan").jqGrid('setRowData', object._id_, false, {
                                    color: '#bf00ff',
                                    weightfont: 'bold'
                                });
                            } else if (CO_BHYT == 0 && thanhtoan == "1") {
                                $("#list_benhnhan").jqGrid('setRowData', object._id_, false, {
                                    color: '##EE7600',
                                    weightfont: 'bold'
                                });
                            } else if (tuoi.indexOf("tháng") != -1) {
                                $("#list_benhnhan").jqGrid('setRowData', object._id_, false, {
                                    color: '#00ff00',
                                    weightfont: 'bold'
                                });
                            } else {
                                $("#list_benhnhan").jqGrid('setRowData', object._id_, false, {
                                    color: 'black',
                                    weightfont: 'bold',
                                    background: 'white'
                                });
                            }
                        })
                    }
                },
                onSelectRow: function (id) {
                    if (id) {
                        var ret = $("#list_benhnhan").jqGrid('getRowData', id);

                        //CMU Cảnh báo ngoại trú chưa đóng tiền
                        if ("${cdhacanhbao}" > 0 && ret.NOITRU == 0 && ret.CO_BHYT == 0 && ret.DA_THANH_TOAN == 0) {
                            jAlert('Bệnh nhân ngoại trú chưa đóng viện phí.', 'Thông báo');
                            if ("${cdhacanhbao}" == 2) {
                                clear_benhnhan();
                                $("#list_sieuam_bhyt").jqGrid('clearGridData');
                                return;
                            }
                        }
                        noitru_ngoaitru = ret.NOITRU;
                        co_bao_hiem = ret.CO_BHYT;
                        bacsi_chidinh = ret.NGUOI_CHI_DINH;
                        phongcdha_ss = ret.MA_PHONG_CDHA;
                        sobenhan_noitru = ret.SOBENHAN;
                        sobenhan_noitru_tt = ret.SOBENHAN_TT;
                        icd_benhnhan = ret.ICD;
                        ten_icd_benhnhan = ret.CHUANDOANICD.replace(ret.ICD + ' - ', '');
                        ngay_chi_dinh = ret.NGAY_CHI_DINH;
                        if(ngay_chi_dinh !== null){
                            var ngaygio_chichinh_cls = ngay_chi_dinh.split(" ");
                            ngaychidinh = ngaygio_chichinh_cls[0];
                            giochidinh = ngaygio_chichinh_cls[1];
                            $("#ngaychidinh_cls").val(ngaychidinh);
                            $("#giochidinh_cls").val(giochidinh);
                            $("#ngaychidinh_kq").val(ngaychidinh);
                            $("#giochidinh_kq").val(giochidinh);
                        }
                        loadThongTinBenhNhan(ret);
                        loadDsCD(ret);
                        $.post('cmu_post_CMU_TONGTIEN_MONEY_NOIDUNG', {
                            url: ["${Sess_DVTT}",  sovaovien == 0? sovaovien_noi: sovaovien, sovaovien_dt_noi? sovaovien_dt_noi: 0, ret.SO_PHIEU].join('```')
                        }).done(function (data) {
                            if(data == 0) {
                                $("#vnptmoney").hide();
                            } else {
                                $("#vnptmoney").show();
                                $("#vnptmoney").html("Đã thanh toán qua VNPT-MONEY: "+ data);
                            }
                        })
                        $.post('cmu_post_CMU_TONGTIEN_BANK_NOIDUNG', {
                            url: ["${Sess_DVTT}",
                                sovaovien == 0? sovaovien_noi: sovaovien, sovaovien_dt_noi? sovaovien_dt_noi: 0, ret.SO_PHIEU, 'BIDV'].join('```')
                        }).done(function (data) {
                            if(data == 0) {
                                $("#bidv").hide();
                            } else {
                                $("#bidv").show();
                                $("#bidv").html("Đã thanh toán qua BIDV: "+ new Intl.NumberFormat().format(data));
                            }
                        })
                        $.post('cmu_post_CMU_TONGTIEN_BANK_NOIDUNG', {
                            url: ["${Sess_DVTT}",
                                sovaovien == 0? sovaovien_noi: sovaovien, sovaovien_dt_noi? sovaovien_dt_noi: 0, ret.SO_PHIEU, 'VIETINBANK'].join('```')
                        }).done(function (data) {
                            if(data == 0) {
                                $("#vietinbank").hide();
                            } else {
                                $("#vietinbank").show();
                                $("#vietinbank").html("Đã thanh toán qua VIETINBANK: "+ new Intl.NumberFormat().format(data));
                            }
                        })
                    }
                },
                onRightClickRow: function (id1) {
                    if (id1) {
                        $('#list_benhnhan').jqGrid('setSelection', id1);
                        //alert(id1);

                        $.contextMenu({
                            selector: '#list_benhnhan tr',
                            callback: function (key, options) {
                                if (key == "trathuocvekho") {
                                    if("${Sess_DVTT}" == 96004 && "${Sess_UserID}" != "1344516") {
                                        return jAlert("Bạn không có quyền hủy kết quả xét nghiệm, vui lòng liên hệ Minh", 'Thông báo');
                                    }
                                    var id = $("#list_benhnhan").jqGrid('getGridParam', 'selrow');
                                    var ret = $("#list_benhnhan").jqGrid('getRowData', id);
                                    var arr = ["${Sess_DVTT}", ret.NOITRU, ret.SO_PHIEU, ret.MA_KHAM_BENH, ret.STT_BENHAN, ret.STT_DOTDIEUTRI, ret.STT_DIEUTRI, 0];
                                    // TGGDEV-36575: admin được phép hủy kết quả, nhân viên chỉ được hủy trong ngày, thêm tham số cho phép nhân viên hủy qua ngày
                                    if ("${Sess_Admin}" != "0" || (ret.NGUOI_THUC_HIEN == "${Sess_UserID}" && ret.NGAY_THUC_HIEN == "${ngayhientai}" && "${tsktvhuyKQ}" == "1") || (ret.NGUOI_THUC_HIEN == "${Sess_UserID}" && "${tsktvhuyKQ}" == "0")|| ret.BACSI_THUCHIEN == nv_with_chucdanh) {
                                        var risarr = [ret.SO_PHIEU, ret.MABENHNHAN, ret.NOITRU, "SA", "0"];
                                        var urlarr = "ris_kiemtra_trangthai_cachup";
                                        $.post(urlarr, {
                                            url: convertArray(risarr)
                                        }).done(function (data) {
                                            if (data != "0") {
                                                return jAlert("Có ca chụp đang thực hiện bởi RIS. Không thể hủy phiếu. Vui lòng liên hệ khoa CDHA...", "Thông báo");
                                            } else {
                                                jConfirm('Xác nhận hủy kết quả  ?', 'Thông báo', function (r) {
                                                    if (r.toString() == "true") {
                                                        if (ret.SO_PHIEU != "") {
                                                            var arr_ekip = [ret.SO_PHIEU,'SA'];
                                                            var url_ekip = "hgi_ekip_dachamekip_theophieu?url=" + convertArray(arr_ekip);
                                                            $.ajax({ url: url_ekip }).done(function (data) {
                                                                if (data != "0")
                                                                    jAlert("Đã chấm công ekip, phải hủy ekip mới có thể hủy kết quả!", 'Thông báo');
                                                                else {
                                                                    var url = "huyketquacdha?url=" + convertArray(arr);
                                                                    $.ajax({
                                                                        url: url
                                                                    }).done(function (data) {
                                                                        if (data == "RIS.1") {
                                                                            jAlert("Trạng thái phiếu hiện tại trên RIS không cho phép hủy kết quả CĐHA của phiếu chỉ định này", "Thông báo");
                                                                        } else if (data == "ERRLOGIN") {
                                                                            jAlert("Xác thực đăng nhập RIS Connector thất bại, Vui lòng kiểm tra lại thông tin cấu hình kết nối RIS", "Thông báo");
                                                                        } else if (data == "RISFAIL") {
                                                                            jConfirm("Hủy kết quả CĐHA của phiếu trên RIS thất bại. Bạn có muốn tiếp tục?", "Thông báo", function (r) {
                                                                                if (r.toString() == "true") {
                                                                                    $.post("ris_update_huytrangthai", {  url: convertArray(arr)  }).always(function () {
                                                                                        //code HIS
                                                                                        var arr1 = ["${Sess_DVTT}", "Hủy kết quả CDHA cho bệnh nhân " + ret.TEN_BENH_NHAN + " với phiếu CDHA " + ret.SO_PHIEU, "${Sess_UserID}" + "-" + "${Sess_User}", "Hủy kết quả CDHA"];
                                                                                        $.post("lichsusudung_insert", {url: convertArray(arr1)});
                                                                                        jAlert("Hủy kết quả thành công", "Thông báo");
                                                                                        $("#lammoi").click();
                                                                                    });
                                                                                }
                                                                            });
                                                                        }
                                                                        else if (data == "SUCCESS") {
                                                                            var arr1 = ["${Sess_DVTT}", "Hủy kết quả CDHA cho bệnh nhân " + ret.TEN_BENH_NHAN + " với phiếu CDHA " + ret.SO_PHIEU, "${Sess_UserID}" + "-" + "${Sess_User}", "Hủy kết quả CDHA"];
                                                                            jAlert("Hủy kết quả thành công", "Thông báo");
                                                                            $.post("lichsusudung_insert", {url: convertArray(arr1)});
                                                                            $("#lammoi").click();
                                                                        }
                                                                    });
                                                                }
                                                            });
                                                        }
                                                        else {
                                                            jAlert("Chọn phiếu để hủy", 'Thông báo');
                                                        }
                                                    }
                                                });
                                            }
                                        });
                                    } else {
                                        jAlert("Chỉ Admin hoặc bác sĩ thực hiện mới có quyền hủy!", 'Thông báo');
                                    }
                                }
                                if (key == "goiso") {
                                    var id = $("#list_benhnhan").jqGrid('getGridParam', 'selrow');
                                    var ret = $("#list_benhnhan").jqGrid('getRowData', id);
                                    var uu_tien = ret.UU_TIEN;
                                    if(uu_tien == null)
                                        uu_tien = ret.CAPCUU == 1 ? "Yes" : "No";
                                    var chuoi = ${Sess_PhongDuocSet} +"|" + ret.STT_HANGNGAY.toString().replace('<span class="cellWithoutBackground" style="background-color:white;color:black">', '').replace('</span>', '') + "|" + ret.TENBENHNHAN + "|" + ret.CAPCUU + "|" + "CDHA_SIEUAM" + "|" + "0";
                                    //KGG Goi so
                                    <c:choose>
                                    <c:when test="${kgggoiso == '1'}">
                                    chuoi = fetchListDSBN_SA($("#list_benhnhan"), id, ${Sess_PhongDuocSet}, "${Sess_TenPhong}");
                                    saveTextAsFileL(chuoi);
                                    </c:when>
                                    <c:when test="${kgggoiso == '3'}">
                                    chuoihinhanh = goisolayhinhanh($("#list_benhnhan"), id);
                                    saveTextAsFile(chuoihinhanh);
                                    </c:when>
                                    <c:otherwise>
                                    saveTextAsFile(chuoi);
                                    </c:otherwise>
                                    </c:choose>
                                    //End goi so
                                }
                            },
                            items: {
                                "trathuocvekho": {name: "Hủy kết quả"},
                                "goiso": {
                                    name: "<span style='color:red'>Gọi số bệnh nhân</span>", icon: "goiso"
                                }

                            }
                        });
                    }
                }

            });

            $("#cmu_kyso").click(function() {
                var mabenhnhan = $("#mabenhnhan").val();
                var hoten = $("#hoten").val();
                var diachi = $("#diachi").val();
                var tuoi = $("#tuoi").val();
                var phai = $("#gioitinh").val();
                var sothebhyt = $("#sothebhyt").val();
                var namsinh = $("#namsinh").val();
                var tenkhoa = $("#tenkhoa").val();
                //if (phai.toString() == "true") {
                //  phai = "Nam";
                //} else {
                //  phai = "Nữ";
                //}
                var phai = $("#gioitinh_ct").val();
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var macdha = $("#macdha").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var bssieuam = $("#bacsisieuam").val() != null && $("#bacsisieuam").val() != '' ? $("#bacsisieuam").val() : " ";
                // VNPTHIS-4697 23/11/2017
                var typein = 2;
                // VNPTHIS-4697 23/11/2017
                if (sophieu != "" && macdha != "") {
                    var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha,
                        dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, sothebhyt, "0",
                        sovaovien,
                        sovaovien_noi,
                        sovaovien_dt_noi, 0, $("#trieuchungls").val(), bssieuam, maPhongBan ? maPhongBan : 0, namsinh, tenkhoa, 0, typein,
                        $("#mausieuam").val(), "0"]; // VNPTHIS-4697 23/11/2017 thêm
                    var selected = [];
                    $("#list_hinhanhnoisoi").jqGrid('getGridParam', 'selarrrow').forEach(function (data) {
                        var stt = $("#list_hinhanhnoisoi").jqGrid('getCell', data, 'STT');
                        selected.push(stt);
                    });
                    var url = "inketquasieuam_svv?thongtin=" + convertArray(arr) + "&anh=" + selected;
                    getFilesign769("PHIEUKQ_CDHA_RISPACS", sophieu, -1, ${Sess_DVTT},
                        sovaovien, sovaovien_dt_noi, macdha, function(dataKySo) {
                            if (dataKySo.length > 0) {
                                jAlert("Phiếu đã được ký số")
                            }else{
                                previewAndSignPdfDefaultModal({
                                    url: url,
                                    idButton: 'cdhanoisoi_kyso_action',
                                }, function(){
                                    $("#cdhanoisoi_kyso_action").click(function() {
                                        var idButton = this.id;
                                        showSelfLoading(idButton)
                                        kySoChung({
                                            dvtt: ${Sess_DVTT},
                                            userId: ${Sess_UserID},
                                            url: url,
                                            loaiGiay: "PHIEUKQ_CDHA_RISPACS",
                                            maBenhNhan: $("#mabenhnhan").val(),
                                            noitru: noitru,
                                            soBenhAn: sttbenhan,
                                            soPhieuDichVu: $("#sophieu").val(),
                                            sttDotDieuTri: sttdotdieutri,
                                            soVaoVien: sovaovien,
                                            soVaoVienDT: sovaovien_dt_noi,
                                            maDichVu: macdha,
                                            keyword: "Bác sĩ siêu âm",
                                            fileName: "Phiếu chẩn đoán kết quả siêu âm: " + $("#hoten").val() + " - Mã phiếu: " + $("#sophieu").val(),
                                        }, function(dataKySo) {
                                            hideSelfLoading(idButton)
                                            $("#modalPreviewAndSignPDF").modal("hide");
                                        });
                                    });
                                });
                            }
                        });

                } else {
                    notifiToClient("Red", "Vui lòng chọn phiếu")
                }


            })
            $("#cmu_huykyso").click(function() {
                confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                    huykysoFilesign769("PHIEUKQ_CDHA_RISPACS", $("#sophieu").val(), ${Sess_UserID}, ${Sess_DVTT},
                        sovaovien, sovaovien_dt_noi, $("#macdha").val(), function(data) {
                            if(data && data.ERROR) {
                                notifiToClient("Red", "Hủy ký số thất bại");
                            }
                        })
                }, function () {

                })
            })
            $("#cmu_inkyso").click(function() {
                getFilesign769(
                    "PHIEUKQ_CDHA_RISPACS",
                    $("#sophieu").val(),
                    -1,
                    "${Sess_DVTT}",
                    sovaovien,
                    sovaovien_dt_noi,
                    $("#macdha").val(),
                    function(dataKySo) {
                        if(dataKySo.length == 0) {
                            notifiToClient("Red", "Chưa ký số");
                            return;
                        }
                        if(dataKySo.length > 0) {
                            getCMUFileSigned769(dataKySo[0].KEYMINIO,"pdf")
                        }
                    });
            })

            function clear_benhnhan() {
                $("#sovaovien").val('');
                $("#mabenhnhan").val('');
                $("#bacsichidinh").val('');
                $("#tt_thanhtoan").val('');
                $("#hoten").val('');
                $("#tuoi").val('');
                $("#gioitinh").val('');
                $("#diachi").val('');
                $("#sothebhyt").val('');
                $("#sophieu").val('');
                $("#makhambenh").val('');
                $("#noitru").val('');
                $("#sttbenhan").val('');
                $("#sttdotdieutri").val('');
                $("#sttdieutri").val('');
                $("#mabacsichidinh").val('');
                sovaovien = '';
                sovaovien_noi = '';
                sovaovien_dt_noi = '';
                maPhongBan = '';
                da_thanh_toan = '';
                sophieuthanhtoan = '';
                cobhyt = '';
                ngay_kb = '';
                tlmg = '';
                flag_noitru = '';
                $("#bacsisieuam").val('');
                $("#bacsisieuam").val("");
                $("#_chandoan").val('');
                $("#chandoan").val('');
                $("#tenkhoa").val('');
                $("#hoten_ct").val('');
                $("#tuoi_ct").val('');
                $("#gioitinh_ct").val('');
                $("#mabenhnhan_ct").val('');
                $("#tenkhoa_ct").val('');
                $("#sothebhyt_ct").val('');
                $("#namsinh").val('');
            }

            $("#list_benhnhan").jqGrid('filterToolbar', {
                stringResult: true,
                searchOnEnter: false,
                defaultSearch: "cn"
            });
            $("#list_sieuam_bhyt").jqGrid({
                datatype: "local",
                loadonce: true,
                height: 158,
                width: 660,
                colNames: ["DA_THANH_TOAN", "ma_CDHA", "Yêu cầu chẩn đoán", "Kết quả", "Trangthai", "Ma", "DVT", "NGUOI_THUC_HIEN", "STT_MAYCDHA","Ekip"
                    <c:if test="${thamso_dongia != 0}">,"Đơn giá" </c:if>
                ],
                colModel: [
                    {name: 'DA_THANH_TOAN', index: 'DA_THANH_TOAN', hidden: true},
                    {name: 'MA_CDHA', index: 'MA_CDHA', hidden: true},
                    {
                        name: 'TEN_CDHA', index: 'TEN_CDHA', width: 150,
                        formatter: function (cellvalue, options, rowObject) {
                            var color_text;
                            if (rowObject.DA_THANH_TOAN.toString() === "0") {
                                color_text = 'red';
                            } else {
                                color_text = 'black';
                            }
                            return '<span id="TEN_CDHA_EKIP' +rowObject.MA_CDHA+ '" class="cellWithoutBackground" style="color:' + color_text + '">' + (cellvalue == null ? "" : cellvalue) + '</span>';
                        }
                    },
                    {name: 'KET_QUA', index: 'KET_QUA', width: 60, hidden: true},
                    {name: 'TRANGTHAI_BHYT', index: 'TRANGTHAI_BHYT', hidden: true},
                    {name: 'MABAOCAO', index: 'MABAOCAO', hidden: true},
                    {name: 'DVT_CDHA', index: 'DVT_CDHA', hidden: true},
                    {name: 'NGUOI_THUC_HIEN', index: 'NGUOI_THUC_HIEN', hidden: true},
                    {name: 'STT_MAYCDHA', index: 'STT_MAYCDHA', hidden: true},
                    {name: 'HGI_EKIP', index: 'HGI_EKIP', width: 20, hidden: !${hgi_ekip}}
                    <c:if test="${thamso_dongia != 0}">
                    ,{name: 'DON_GIA', index: 'DON_GIA', width: 60}
                    </c:if>
                ],
                caption: "Yêu cầu chẩn đoán hình ảnh",
                serializeGridData: function (_obj) {
                    return _obj;
                },
                loadComplete: function (_obj) {
                    if (_obj.length > 0 && _obj.length != undefined) {
                        _obj.forEach(function (_data) {
                            tennhanvienthuchien(_data.NGUOI_THUC_HIEN);
                        })
                    }

                    var $self = $(this);
                    var ids = $self.jqGrid('getDataIDs');
                    var dvtt = "${Sess_DVTT}";
                    var mabenhnhan = $("#mabenhnhan").val();
                    var stt_benhan = $("#sttbenhan").val();
                    if (stt_benhan === undefined || stt_benhan.length < 1) {
                        stt_benhan = $("#makhambenh").val();
                    }
                    var sophieu = $("#sophieu").val();
                    for (var i = 0; i < ids.length; i++) {
                        var ret = $("#list_sieuam_bhyt").jqGrid('getRowData', i + 1);
                        var ma_dv = ret.MA_CDHA;
                        var ten_dv =  document.getElementById("TEN_CDHA_EKIP"+ma_dv).innerText;
                        var rowId = ids[i];
                        var chamcong = "<input style='height:23px;width:100%;' " +
                            "type='button' id='chamcong' name='chamcong' value='Chi tiết' " +
                            "onclick=\"chamcong_ekip('" + dvtt + "','" + stt_benhan + "','" + mabenhnhan + "','" + ma_dv + "','" + ten_dv + "','" + sophieu + "');\" />" +
                            "<input type='text' hidden='true' id='madichvu' name='madichvu' value='" + ma_dv + "' >" +
                            "<input type='text' hidden='true' id='tendichvu' name='tendichvu' value='" + ten_dv + "' >";
                        $self.jqGrid('setRowData', rowId, {HGI_EKIP: chamcong});
                    }
                },
                ondblClickRow: function (id) {
                    if (id) {
                        var ret = $("#list_sieuam_bhyt").jqGrid('getRowData', id);

                        /*$("#macdha").val(ret.MA_CDHA);
                            $("#nguoithuchien").val(ret.NGUOI_THUC_HIEN);
                            $("#frm_kq_id_ekip").val(ret.ID_EKIP);
                            $("#frm_kq_so_phieu_dichvu").val($("#sophieu").val());
                            $("#frm_kq_ma_dv").val(ret.MA_CDHA);
                            $("#frm_kq_mabenhnhan").val($("#mabenhnhan").val());
                            $("#frm_kq_sovaovien").val(sovaovien==0?sovaovien_noi:sovaovien);
                            $("#frm_kq_sovaovien_dt").val(sovaovien_dt_noi);
                            $("#frm_kq_noitru").val($("#noitru").val());
                            var sophieu = $("#sophieu").val();
                            var ma_cdha = $("#macdha").val();
                            var makhambenh = $("#makhambenh").val();
                            var noitru = $("#noitru").val();
                            var sttbenhan = $("#sttbenhan").val();
                            var sttdotdieutri = $("#sttdotdieutri").val();
                            var sttdieutri = $("#sttdieutri").val();
                            if (flag_noitru === '1') {
                                load_cttoathuoc("noitru_toadichvu", "list_thuocdichvu");
                            } else {
                                var url = 'chitiettoathuocngoatru_svv?matt=' + matoathuoc + "&nghiepvu=ngoaitru_toadichvu&dvtt=

                        ${Sess_DVTT}" + "&sovaovien=" + sovaovien + "&ma_cdha=" + ma_cdha + "&sophieu=" + sophieu;
                                $("#list_thuocdichvu").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
                            }
                            if (flag_noitru === "0") {
                                makhovattu = $("#kho_dv_ngoai").val();
                            } else {
                                makhovattu = $("#kho_dv_noi").val();
                            }
                            if (flag_noitru === "0") {
                                url_loadtonkho = "layvattu_theoloai?makhovt=" + makhovattu + "&loaivattu="
                            } else {
                                url_loadtonkho = "noitru_load_tonkhohientai?makhovt=" + makhovattu + "&dvtt=

                        ${Sess_DVTT}"
                            }
                            if (flag_noitru === "0") {
                                $("#tenthuongmai_dv").combogrid("option", "url", 'layvattu?makhovt=' + $("#kho_dv_ngoai").val());
                                $("#tengoc_dv").combogrid("option", "url", 'layvattu_theotengoc?makhovt=' + $("#kho_dv_ngoai").val());
                            } else {
                                $("#tenthuongmai_dv").combogrid("option", "url", 'layvattu?makhovt=' + $("#kho_dv_noi").val());
                                $("#tengoc_dv").combogrid("option", "url", 'layvattu_theotengoc?makhovt=' + $("#kho_dv_noi").val());
                            }
                            if("

                        ${hienthi_checkbox}" == "1"){


                        <c:forEach var="i" items="${formsieuam}">
                                $("#checkbox_

                        ${i.MA_MAUSIEUAM}").css("display","none");


                        </c:forEach>
                                $("#form_sieuammau_doppler").css("display","");
                                $("#inphieu_sieuam_mau_doppler").css("display","");
                            var sophieu = $("#sophieu").val();
                            var ma_cdha = $("#macdha").val();
                            var noitru = $("#noitru").val();
                            var url = "xoa_ds_checkbox_chualuu?sophieu="+sophieu + "&ma_cdha="+ma_cdha + "&noitru="+noitru+ "&dvtt=

                        ${Sess_DVTT}";
                            $.ajax({
                                url: url
                            }).done(function (data) {
                            });
                            }
                            var arr = [sophieu, "

                        ${Sess_DVTT}", ret.MA_CDHA, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0", sovaovien, sovaovien_noi, sovaovien_dt_noi];
                            var url = "sieuam_select_ketqua_svv?url=" + convertArray(arr);
                            $.getJSON(url, function (result) {
                                $.each(result, function (i, field) {
                                    $("#bacsichidinh").val(field.BACSI_CHIDINH);
                                    $("#bacsisieuam").val(field.BACSI_THUCHIEN);
                                    $("#mausieuam").val(field.MA_MAUSIEUAM);

                                    // STG
                                    if(field.NGAYTHUCHIEN == null) {
                                        $("#ngayth_ct").val("

                        ${ngayhientai}");
                                    } else {
                                        $("#ngayth_ct").val(field.NGAYTHUCHIEN);
                                    }

                                    /*if(field.GIOTHUCHIEN == null) {
                                     showtime_gioth_ct_cancel = 0;
                                     } else {
                                     showtime_gioth_ct_cancel = 1;
                                     $("#gioth_ct").val(field.GIOTHUCHIEN);
                                     } */
                        /*$("#gioth_ct").data('da-thuc-hien', field.DA_CHAN_DOAN == 1);
                                    if(field.GIOTHUCHIEN == null || field.DA_CHAN_DOAN != 1) {
                                        showtime_gioth_ct();
                                    } else {
                                        stopGioThCtTimer();
                                        $("#gioth_ct").val(field.GIOTHUCHIEN);
                                    }
                                    // STG
                                    if ($("#bacsichidinh").val() == "") {
                                        var url_bs = "select_tenbacsi?mabacsi=" + $("#mabacsichidinh").val() + "&dvtt=" + "

                        ${Sess_DVTT}";
                                        $.ajax({
                                            url: url_bs
                                        }).done(function (data) {
                                            $("#bacsichidinh").val(data);
                                        });
                                    }
                                    if ($("#bacsisieuam").val() == "") {
                                        $("#bacsisieuam").val("

                        ${Sess_User}");
                                    }
                                    if (field.CHANDOAN.toString() != "") {
                                        $("#chandoan").val(field.CHANDOAN);
                                    }
                                    CKEDITOR.instances.ketqua.setData(field.KET_QUA);
                                    CKEDITOR.instances.ketluan.setData(field.MO_TA);
                                    //$("#ketqua").val(field.KET_QUA);
                                    //$("#ketluan").val(field.MO_TA);
                                    $("#loidanbacsi").val(field.LOIDANBACSI);

                                    var ma_sieuam = $("#mausieuam").val();
                                    if("

                        ${hienthi_checkbox}" == "1"){
                                    if(ma_sieuam !== null && ma_sieuam !=0){
                                        $("#checkbox_"+ma_sieuam+" :checkbox").removeAttr('checked');
                                        var url = "lay_checkbox_dacheck?sophieu=" + sophieu + "&ma_cdha="+ ret.MA_CDHA  + "&dvtt=" + "

                        ${Sess_DVTT}";
                                        $.ajax({
                                            url: url
                                        }).done(function (data) {
                                            $.each(data, function (i) {
                                                $("#satq_" + data[i].MA_CHECKBOX).prop('checked', true);
                                            });
                                        });
                                        $("#checkbox_"+ma_sieuam).css("display","");
                                        $("#mausieuam").attr("disabled", "disabled");
                                    }
                                    else{
                                        var dvtt = "

                        ${Sess_DVTT}";
                                        var ma_cdha = $("#macdha").val();
                                        var gioitinh = $("#gioitinh").val();
                                        if(gioitinh == "true"){
                                            gioitinh = 1;
                                        }
                                        else{
                                            gioitinh = 0;
                                        }
                                        var url = "load_mausieuam_tuongung?ma_cdha=" + ma_cdha + "&gioitinh=" + gioitinh + "&dvtt=" + dvtt;
                                        $.ajax({
                                            url: url
                                        }).done(function (data) {
                                            $("#mausieuam").val(data);
                                            $("#checkbox_"+data+" :checkbox").removeAttr('checked');
                                            $("#checkbox_"+data).css("display","");
                                            var mausa = $("#mausieuam").val();
                                            if(mausa != 0) {
                                                if(mausa == 3){
                                                    $("#form_sieuammau_doppler").click();
                                                }
                                                $("#mausieuam").attr("disabled", "disabled");
                                            }
                                            var url = "select_mausieuam_theoma?ma=" + data + "&dvtt=

                        ${Sess_DVTT}";
                                            $.ajax({
                                                url: url
                                            }).done(function (data) {
                                                if(data != 0) {
                                                    CKEDITOR.instances.ketqua.setData(data[0]["NOIDUNG"]);
                                                    CKEDITOR.instances.ketluan.setData(data[0]["KET_LUAN"]);
                                                }
                                                //$('#ketluan').val(data[0]["KET_LUAN"]);
                                                //$('#ketqua').val(data);
                                            });
                                        });

                                    }
                                    }
                                });
                            });
                            var arr = [sophieu, "

                        ${Sess_DVTT}", ret.MA_CDHA, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                            var url1 = "sieuam_danhsach_hinhanh?url=" + convertArray(arr);
                            $("#list_hinhanhnoisoi").jqGrid('setGridParam', {datatype: 'json', url: url1}).trigger('reloadGrid');
                            $("#tab_cdha").tabs("option", "active", 1);*/

                        loadThongTinKetQua(ret);
                        //$(location).attr('href', '#cdha_tabs_2');
                    }
                },
                onSelectRow: function (id) {
                    if (id) {
                        var ret = $("#list_sieuam_bhyt").jqGrid('getRowData', id);
                        $("#macdha").val(ret.MA_CDHA);
                        $("#ma_maycdha_md").val(ret.STT_MAYCDHA);
                    }
                },
                gridComplete: function () {
                    var str = $("#list_sieuam_bhyt").getGridParam("records");
                    if (str != "0") {
                        $('#list_sieuam_bhyt').jqGrid('setSelection', "1");
                    }
                }
            });
            $("#list_chitiettiencongthuchien").jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 312,
                width: 600,
                colNames: ["Người thực hiện", "Vai trò", "Số tiền", "Mã nhân viên"],
                colModel: [
                    {name: 'TEN_NHANVIEN', index: 'TEN_NHANVIEN', width: 30},
                    {name: 'TEN_VAITRO', index: 'TEN_VAITRO', width: 30},
                    {name: 'SO_TIEN', index: 'SO_TIEN', width: 30},
                    {name: 'MA_NHANVIEN', index: 'MA_NHANVIEN', width: 30, hidden: true}
                ],
                sortname: 'TEN_VAITRO',
                sortorder: "asc",
                caption: "Ekip thực hiện CĐHA",
                ignoreCase: true,
                onSelectRow: function (id) {
                    if (id) {
                        var ret = $("#list_chitiettiencongthuchien").jqGrid('getRowData', id);
                        $("#manhanvien").val(ret.MA_NHANVIEN);
                    }
                }
            });
            $("#khoaphong_ekip").change(function (evt) {
                $("#nguoithuchien_ekip").empty();
                $.getJSON("hgi_nhanvientheophongban_select", {maphongban: $("#khoaphong_ekip").val()}, function (data) {
                    $("<option value='-1'>--  Chọn nhân viên  --</option>").appendTo("#nguoithuchien_ekip");
                    if (data && data.length > 0) {
                        $.each(data, function (i) {
                            $("<option value='" + data[i].MA_NHANVIEN + "'>" + data[i].TEN_NHANVIEN + "</option>").appendTo("#nguoithuchien_ekip");
                        });
                    }
                });
            });

            $("#themnguoithuchien").click(function (evt) {
                var dvtt = "${Sess_DVTT}";
                var stt_benhan = $("#sttbenhan").val();//MA_KHAM_BENH
                if (stt_benhan === undefined || stt_benhan.length < 1) {
                    stt_benhan = $("#makhambenh").val();
                }
                var mabenhnhan = $("#mabenhnhan").val();
                var manhanvien = $("#nguoithuchien_ekip").val();
                var maloaidichvu = $("#madichvu").val();
                //var tenloaidichvu = $("#tendichvu").val();
                var phieudichvu = $("#sophieu").val();
                var ma_tiencong = $("#tiencongthuchien").val();
                var ngay_thuchien = convertStr_MysqlDate($("#ngaythuchien").val());
                var nguoi_tao = "${Sess_UserID}";
                var ngaygio_tao = convertStr_MysqlDate("${ngayhientai}");
                var ekip_mau = $("#ekip_mau").val();
                if (ma_tiencong === "-1" && ekip_mau === "-1") {
                    jAlert("Vui lòng chọn Vai trò thực hiện!", 'Thông báo');
                }
                if (manhanvien === "-1" && ekip_mau === "-1") {
                    jAlert("Vui lòng chọn người thực hiện!", 'Thông báo');
                }
                if (ekip_mau == -1 && manhanvien != -1 && ma_tiencong != -1) {
                    var arr = [dvtt, stt_benhan, mabenhnhan, manhanvien, maloaidichvu, phieudichvu, ma_tiencong, ngay_thuchien, nguoi_tao, ngaygio_tao,'SA'];
                    var url = "hgi_ekip_chamcongthuchien_insert?url=" + convertArray(arr);
                    $.ajax({
                        url: url
                    }).done(function () {
                        jAlert("Thêm thành công!", 'Thông báo');
                        $("#list_chitiettiencongthuchien").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                    });
                } else if (ekip_mau != -1) {
                    var arr = [dvtt, stt_benhan, mabenhnhan, ekip_mau, maloaidichvu, phieudichvu, ngay_thuchien, nguoi_tao, ngaygio_tao,'SA'];
                    var url = "hgi_ekip_chamcongthuchien_insert_theoekipmau?url=" + convertArray(arr);
                    $.ajax({
                        url: url
                    }).done(function (data) {
                        if (data === 1) {
                            jAlert("Thêm người thực hiện từ EKIP mẫu thành công!", 'Thông báo');
                            $("#list_chitiettiencongthuchien").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                        } else {
                            jAlert("EKIP mẫu rỗng. Không thể thêm!", 'Thông báo');
                        }
                    });
                }
                ;
            });
            $("#xoanguoithuchien").click(function (evt) {
                var dvtt = "${Sess_DVTT}";
                var stt_benhan = $("#sttbenhan").val();
                if (stt_benhan === undefined || stt_benhan.length < 1) {
                    stt_benhan = $("#makhambenh").val();
                }
                var mabenhnhan = $("#mabenhnhan").val();
                var manhanvien = $("#manhanvien").val();
                var maloaidichvu = $("#madichvu").val();
                var phieudichvu = $("#sophieu").val();
                var str = [dvtt, stt_benhan, mabenhnhan, manhanvien, maloaidichvu, phieudichvu,'SA'];
                var url = "hgi_ekip_chamcongthuchien_delete_nhanvien?url=" + convertArray(str);
                $.ajax({
                    url: url
                }).done(function () {
                    jAlert("Xóa thành công!", 'Thông báo');
                    $("#list_chitiettiencongthuchien").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                });
            });
            $("#xoanguoithuchien_all").click(function (evt) {
                var dvtt = "${Sess_DVTT}";
                var stt_benhan = $("#sttbenhan").val();
                if (stt_benhan === undefined || stt_benhan.length < 1) {
                    stt_benhan = $("#makhambenh").val();
                }
                var mabenhnhan = $("#mabenhnhan").val();
                var maloaidichvu = $("#madichvu").val();
                var phieudichvu = $("#sophieu").val();
                var str = [dvtt, stt_benhan, mabenhnhan, maloaidichvu, phieudichvu,'SA'];
                var url = "hgi_ekip_chamcongthuchien_delete_all?url=" + convertArray(str);
                $.ajax({
                    url: url
                }).done(function () {
                    jAlert("Xóa thành công!", 'Thông báo');
                    $("#list_chitiettiencongthuchien").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                });
            });
            $("#luu_tt_maycdha").click(function (evt) {
                var sophieu = $("#sophieu").val();
                var noitru = $("#noitru").val();
                var makhambenh = $("#makhambenh").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var macdha = $("#macdha").val();
                if (sophieu != "" && macdha != "" && (sovaovien != "" || sovaovien_noi != "")) {
                    $.post("luu_maycdha_vaobangchitiet", {
                        sophieu: sophieu, macdha: macdha, noitru: noitru,
                        sttbenhan: sttbenhan, sttdotdieutri: sttdotdieutri, sttdieutri: sttdieutri,
                        makhambenh: makhambenh, sovaovien: sovaovien,
                        sovaovien_noi: sovaovien_noi,
                        sovaovien_dt_noi: sovaovien_dt_noi,
                        stt_may_cdha: ($("#ma_maycdha_md").val())
                    }).done(function (data) {
                        if (data == "1") {
                            jAlert("Cập nhật thành công!", "Thông báo");
                        } else {
                            jAlert("Vui lòng kiểm tra lại thông tin!", "Thông báo");
                        }
                    }).fail(function () {
                        jAlert("Vui lòng thử lại!", "Thông báo");
                    });
                }
            });

            function checkSTT(value, colname) {

                if ((value + "").match(/^\d+$/)) {
                    if (value < 0 || value > 50) {
                        return [false, "Hãy nhập số thứ tự nhỏ hơn"];
                    } else {

                        return [true, ""];
                    }
                } else {
                    return [false, "Số thứ tự phải là số nguyên dương"];
                }

            }

            $("#list_hinhanhnoisoi").jqGrid({
                datatype: "local",
                loadonce: true,
                multiselect: true,
                height: 235,
                width: 290,
                colNames: ["SO_PHIEU_CDHA", "DVTT", "idauto", "STT", "makhambenh", "Hình ảnh siêu âm", "CHONIN"],
                colModel: [
                    {name: 'SO_PHIEU_CDHA', index: 'SO_PHIEU_CDHA', hidden: true},
                    {name: 'DVTT', index: 'DVTT', hidden: true},
                    {name: 'STT_AUTO', index: 'STT_AUTO', hidden: true, jsonmap: "STT"},
                    {
                        name: 'STT',
                        index: 'STT',
                        editable: true,
                        width: 10,
                        editrules: {custom: true, custom_func: checkSTT},
                        editoptions: {
                            dataInit: function (elem) {
                                $(elem).focus(function () {
                                    this.select();
                                });
                            }
                        }
                    },
                    {name: 'MA_CDHA', index: 'MA_CDHA', hidden: true},
                    {name: 'HINHANH', index: 'HINHANH', width: 80, formatter: imageFormat},
                    {name: 'CHONIN', index: 'CHONIN', hidden: true}
                ],
                rowNum: 200000,
                cellEdit: true,
                cellsubmit: 'clientArray',
                beforeEditCell: function (rowid, cellname, value, iRow, iCol) {
                    console.log("befor edit cell", rowid, cellname, value, iRow, iCol);
                },
                beforeSaveCell: function (rowid, cellname, value, iRow, iCol) {
                    console.log("befor save cell", rowid, cellname, value, iRow, iCol);
                },
                afterSaveCell: function (rowid, name, val, iRow, iCol) {
                    var grid = $('#list_hinhanhnoisoi');
                    var rows = grid.jqGrid('getDataIDs');
                    var sttmoi, sttcu;

                    sttmoi = parseInt(grid.jqGrid('getRowData', iRow).STT);
                    sttcu = parseInt(grid.jqGrid('getRowData', iRow).STT_AUTO);

                    var macdha = $("#macdha").val();
                    var sophieu = $("#sophieu").val();
                    var noitru = $("#noitru").val();
                    var makhambenh = $("#makhambenh").val();
                    var sttbenhan = $("#sttbenhan").val();
                    var sttdotdieutri = $("#sttdotdieutri").val();
                    var sttdieutri = $("#sttdieutri").val();
                    if (sophieu != "" && macdha != "") {
                        $.post("sieuam_update_stt_hinhanh", {
                            sophieu: sophieu,
                            macdha: macdha,
                            dvtt: "${Sess_DVTT}",
                            sttcu: sttcu,
                            sttmoi: sttmoi,
                            noitru: noitru,
                            sttbenhan: sttbenhan,
                            sttdotdieutri: sttdotdieutri,
                            sttdieutri: sttdieutri,
                            makhambenh: makhambenh
                        })
                            .done(function () {
                                var arr = [sophieu, "${Sess_DVTT}", macdha, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                                var url = 'sieuam_danhsach_hinhanh?url=' + convertArray(arr);
                                $("#list_hinhanhnoisoi").jqGrid('setGridParam', {
                                    datatype: 'json',
                                    url: url
                                }).trigger('reloadGrid');
                            });
                    }


                },
                ondblClickRow: function (id) {
                    var rowSelect = $("#list_hinhanhnoisoi").jqGrid('getGridParam', 'selarrrow').includes(id);
                    $("#list_hinhanhnoisoi").jqGrid().setSelection(id, !rowSelect);
                },
                loadComplete: function () {
                    var i = 0, indexes = this.p._index, localdata = this.p.data,
                        rows = this.rows, rowsCount = rows.length, row, rowid, rowData, className;
                    for (; i < rowsCount; i++) {
                        row = rows[i];
                        className = row.className;
                        //if ($(row).hasClass('jqgrow')) { // test for standard row
                        if (className.indexOf('jqgrow') !== -1) {
                            rowid = row.id;
                            rowData = localdata[indexes[rowid]];
                            if (rowData.CHONIN == "1") {
                                $("#list_hinhanhnoisoi").jqGrid().setSelection(rowid, true);
                            }
                        }
                    }
                }
            });

            function imageFormat(cellvalue, options, rowObject) {
                return '<img src="' + cellvalue + '" width="100px" height="80px" />';
            }

            $("#lammoi").click(function (evt) {
                reload_grid();
            });
            $("#lammoi1").click(function (evt) {
                reload_grid_dsbn_cd();
            });
            $("#list_thuocdichvu").jqGrid({
                url: 'chitiettoathuocngoatru?matt=' + matoathuoc + "&nghiepvu=ngoaitru_toaxquang&dvtt=${Sess_DVTT}",
                datatype: "local",
                loadonce: true,
                height: 230,
                width: 300,
                colNames: ["stt_toathuoc", "Tên thương mại", "DVT", "Số lượng", "", "", "", "", ""
                ],
                colModel: [
                    {name: 'STT_TOATHUOC', index: 'STT_TOATHUOC', hidden: true},
                    {name: 'TEN_VAT_TU', index: 'TEN_VAT_TU', width: 80},
                    {name: 'DVT', index: 'DVT', width: 60},
                    {
                        name: "SO_LUONG",
                        index: "SO_LUONG",
                        align: 'center',
                        width: 45,
                        edittype: 'custom',
                        editoptions: {custom_element: myelem, custom_value: myvalue}
                    },
                    {name: 'THANHTIEN_THUOC', index: 'THANHTIEN_THUOC', hidden: true},
                    {name: 'MAVATTU', index: 'MAVATTU', hidden: true},
                    {name: 'MAKHOVATTU', index: 'MAKHOVATTU', hidden: true},
                    {name: 'DONGIA_BAN_BV', index: 'DONGIA_BAN_BV', hidden: true},
                    {name: 'DONGIA_BAN_BH', index: 'DONGIA_BAN_BH', hidden: true}
                ],
                //viewrecords: true,
                rowNum: 1000000,
                //multiselect: true,
                caption: "Toa Siêu Âm"
            });

            function myelem(value, options) {
                var el = document.createElement("input");
                el.type = "text";
                el.value = value;
                el.onkeypress = function (e) {
                    var theEvent = e || window.event;
                    var key = theEvent.keyCode || theEvent.which;
                    key = String.fromCharCode(key);
                    var regex = /[0-9]|\./;
                    if (!regex.test(key)) {
                        theEvent.returnValue = false;
                        if (theEvent.preventDefault)
                            theEvent.preventDefault();
                    }
                };
                return el;
            }

            function myvalue(elem, operation, value) {
                if (operation === 'get') {
                    return $(elem).val();
                } else if (operation === 'set') {
                    $('input', elem).val(value);
                }
            }

            function clear_thuoc_dv() {
                $("#thuocdichvu_tt_div input[type='text']").val("");
                $("#thuocdichvu_tt_div input[type='hidden']").val("");
            }

            $("#tenthuongmai_dv").combogrid({
                url: "noitru_load_tonkhohientai?makhovt=" + $("#kho_dv").val() + "&dvtt=${Sess_DVTT}",
                debug: true,
                width: "700px",
                //replaceNull: true,
                colModel: [
                    {'columnName': 'MAVATTU', 'label': 'mavattu', hidden: true},
                    {'columnName': 'TENVATTU', 'width': '40', 'label': 'Tên vật tư', 'align': 'left'},
                    {'columnName': 'DVT', 'label': 'dvt', hidden: true},
                    {'columnName': 'SOLUONG', 'width': '10', 'label': 'Số lượng', 'align': 'right'},
                ],
                select: function (event, ui) {
                    $("#tenthuongmai_dv").val(ui.item.TENVATTU);
                    $("#mavattu_dv").val(ui.item.MAVATTU);
                    $("#dvt_dv").val(ui.item.DVT);
                    $("#dongia_dv").val(ui.item.DONGIA);
                    //$("#dongia_bv").val(ui.item.dongia);
                    return false;
                }
            });
            $("#tengoc_dv").combogrid({
                url: 'layvattu_theotengoc_theoloai?makhovt=' + $("#kho_dv").val() + '&loaivattu=',
                debug: true,
                width: "700px",
                //replaceNull: true,
                colModel: [{'columnName': 'MAVATTU', 'label': 'mavattu', hidden: true},
                    {'columnName': 'HOATCHAT', 'width': '30', 'label': 'Hoạt chất', 'align': 'left'},
                    {'columnName': 'TENVATTU', 'width': '40', 'label': 'Tên vật tư', 'align': 'left'},
                    {'columnName': 'HAMLUONG', 'width': '20', 'label': 'Hàm lượng', 'align': 'left'},
                    {'columnName': 'DVT', 'label': 'dvt', hidden: true},
                    {'columnName': 'SOLUONG', 'width': '10', 'label': 'Số lượng', 'align': 'right'},
                    {'columnName': 'DONGIA', 'label': 'dongia_bv', hidden: true},
                    {'columnName': 'CACHSUDUNG', 'label': 'cachsudung', hidden: true}
                ],
                select: function (event, ui) {
                    $("#tenthuongmai_dv").val(ui.item.TENVATTU);
                    $("#tengoc_dv").val(ui.item.HOATCHAT);
                    $("#mavattu_dv").val(ui.item.MAVATTU);
                    $("#dvt_dv").val(ui.item.DVT);
                    $("#dongia_dv").val(ui.item.DONGIA);
                    $("#cachdung_dv").val(ui.item.CACHSUDUNG);
                    return false;
                }
            });
            $("#tenthuongmai_dv").keypress(function (evt) {
                if (evt.keyCode === 13 && flag_noitru !== "-1") {
                    $("#soluong_dv").val(1);
                    $("#soluong_dv").focus();
                }
            });
            $("#tengoc_dv").keypress(function (evt) {
                if (evt.keyCode == 13) {
                    $("#soluong_dv").focus();
                }
            });
            $("#soluong_dv").keypress(function (evt) {
                if (evt.keyCode === 13 && flag_noitru !== "-1") {
                    var makhovattu = $("#kho_dv").val();
                    makho = -1;
                    makhokhoaphong = 0;
                    var url_kho = "noitru_kiemtra_tututhuoc";
                    $.post(url_kho, {dvtt: "${Sess_DVTT}", maphongban: "${Sess_PhongBan}", makho: makhovattu})
                        .done(function (data) {
                            var tu_tuthuoc = 1;
                            var mavattu = $("#mavattu_dv").val();
                            var tenthuongmai = $("#tenthuongmai_dv").val();
                            var tengoc = " ";
                            var dvt = $("#dvt_dv").val();
                            var soluong = $("#soluong_dv").val();
                            var sl = (soluong !== "") ? parseFloat(soluong) : 0;
                            var dongia_bh = $("#dongia_dv").val();
                            var dongia_bv = dongia_bh;
                            var songay = "1";
                            var sang = "1";
                            var trua = "0";
                            var chieu = "0";
                            var toi = "0";
                            var dangthuoc = " ";
                            var ma_cdha = $("#macdha").val();
                            var ghichu = $("#sophieu").val() + "-" + ma_cdha;
                            var cachdung = " ";
                            var thanhtien = sl * parseFloat(dongia_bh);
                            var sophieu = $("#sophieu").val();

                            if (sl <= 0)
                                jAlert("Số lượng không hợp lệ", 'Cảnh báo', function (r) {
                                    $("#soluong_dv").focus();
                                });
                            else {
                                var arr = ["${Sess_DVTT}", matoathuoc, makhovattu, mavattu, encodeURIComponent(tenthuongmai), encodeURIComponent(tengoc), dvt, "noitru_toadichvu", soluong, soluong, dongia_bv, dongia_bh, thanhtien, songay, sang, trua,
                                    chieu, toi, ghichu, "${Sess_UserID}", dangthuoc, stt_dieutri, stt_benhan, stt_dotdieutri, tu_tuthuoc, cobhyt, mabenhnhan, sophieuthanhtoan, "${Sess_PhongBan}", sovaovien_noi, sovaovien_dt_noi];
                                var url = "noitru_toathuoc_insert";
                                if (flag_noitru !== "1") {   //Ngoại trú
                                    var makhambenh = $("#makhambenh").val();
                                    var idtiepnhan = makhambenh.replace("kb_", "");
                                    var url = "xuatduoc_giamtai_svv";
                                    $.post(url, {
                                        nghiepvu: "ngoaitru_toadichvu",
                                        matoathuoc: matoathuoc,
                                        makhambenh: makhambenh,
                                        xacnhan: "false",
                                        mabenhnhan: mabenhnhan,
                                        ngaykhambenh: ngay_kb
                                    }).done(function (data) {
                                        if (data == "0") {
                                            var arr = [matoathuoc, mavattu, encodeURIComponent(tenthuongmai), encodeURIComponent(tengoc), soluong, dongia_bv, dongia_bh, songay, sang, trua,
                                                chieu, toi, dangthuoc, ghichu, thanhtien, cobhyt, mabenhnhan, sophieuthanhtoan, idtiepnhan, makhambenh, sovaovien, "${Sess_Phong}", "1", "${Sess_Phong}", "0"];
                                            var url = "themtoathuocngoaitru_giamtai?dongia_bv=" + parseFloat(dongia_bv) +
                                                "&dongia_bh=" + parseFloat(dongia_bh) + "&thanhtien=" + parseFloat(thanhtien) +
                                                "&sang=" + sang + "&trua=" + trua + "&chieu=" + chieu + "&toi=" + toi +
                                                "&url=" + convertArray(arr) + "&nghiepvu=ngoaitru_toadichvu" +
                                                "&kho=" + makhovattu + "&ngaykb=" + ngay_kb;
                                            if (sophieu) {
                                                url += "&sophieu=" + sophieu;
                                            }
                                            if (ma_cdha) {
                                                url += "&ma_cdha=" + ma_cdha;
                                            }
                                            $.ajax({
                                                url: url
                                            }).done(function (data) {
                                                if (data === "4") {
                                                    jAlert("Bệnh nhân đã thanh toán rồi", 'Cảnh báo');
                                                    var url = "xuatduoc_giamtai_svv";
                                                    $.post(url, {
                                                        nghiepvu: "ngoaitru_toadichvu",
                                                        matoathuoc: matoathuoc,
                                                        makhambenh: makhambenh,
                                                        xacnhan: "true",
                                                        mabenhnhan: mabenhnhan,
                                                        ngaykhambenh: ngay_kb
                                                    }).done(function (data) {

                                                    });
                                                } else if (data === "5") {
                                                    jAlert("Bệnh nhân đã xuất thuốc rồi", 'Cảnh báo');
                                                    var url = "xuatduoc_giamtai_svv";
                                                    $.post(url, {
                                                        nghiepvu: "ngoaitru_toadichvu",
                                                        matoathuoc: matoathuoc,
                                                        makhambenh: makhambenh,
                                                        xacnhan: "true",
                                                        mabenhnhan: mabenhnhan,
                                                        ngaykhambenh: ngay_kb
                                                    }).done(function (data) {

                                                    });
                                                } else if (data === "3") {
                                                    jAlert("Thuốc đã có trong toa", 'Cảnh báo');
                                                    var url = "xuatduoc_giamtai_svv";
                                                    $.post(url, {
                                                        nghiepvu: "ngoaitru_toadichvu",
                                                        matoathuoc: matoathuoc,
                                                        makhambenh: makhambenh,
                                                        xacnhan: "true",
                                                        mabenhnhan: mabenhnhan,
                                                        ngaykhambenh: ngay_kb
                                                    }).done(function (data) {

                                                    });
                                                } else if (data === "6") {
                                                    jAlert("Số lượng thuốc vượt số lượng tồn kho", 'Cảnh báo');
                                                    var url = "xuatduoc_giamtai_svv";
                                                    $.post(url, {
                                                        nghiepvu: "ngoaitru_toadichvu",
                                                        matoathuoc: matoathuoc,
                                                        makhambenh: makhambenh,
                                                        xacnhan: "true",
                                                        mabenhnhan: mabenhnhan,
                                                        ngaykhambenh: ngay_kb
                                                    }).done(function (data) {

                                                    });
                                                } else if (data == '100') {
                                                    jAlert("Đã chốt báo cáo dược, không thể xóa/sửa", 'Cảnh báo');
                                                    var url = "xuatduoc_giamtai_svv";
                                                    $.post(url, {
                                                        nghiepvu: "ngoaitru_toadichvu",
                                                        matoathuoc: matoathuoc,
                                                        makhambenh: makhambenh,
                                                        xacnhan: "true",
                                                        mabenhnhan: mabenhnhan,
                                                        ngaykhambenh: ngay_kb
                                                    }).done(function (data) {

                                                    });
                                                } else {
                                                    var url = 'chitiettoathuocngoatru_svv?matt=' + matoathuoc + "&nghiepvu=ngoaitru_toadichvu&dvtt=${Sess_DVTT}" + "&sovaovien=" + sovaovien + "&ma_cdha=" + ma_cdha + "&sophieu=" + sophieu;
                                                    $("#list_thuocdichvu").jqGrid('setGridParam', {
                                                        datatype: 'json',
                                                        url: url
                                                    }).trigger('reloadGrid');
                                                    var url = "xuatduoc_giamtai_svv";
                                                    $.post(url, {
                                                        nghiepvu: "ngoaitru_toadichvu",
                                                        matoathuoc: matoathuoc,
                                                        makhambenh: makhambenh,
                                                        xacnhan: "true",
                                                        mabenhnhan: mabenhnhan,
                                                        ngaykhambenh: ngay_kb
                                                    }).done(function (data) {

                                                    });
                                                }
                                                clear_thuoc_dv();
                                                $("#tenthuongmai_dv").focus();
                                            });
                                        }
                                    });
                                } else {
                                    url += "?url=" + convertArray(arr);
                                    var sophieu = $("#sophieu").val();
                                    if (sophieu) {
                                        url += "&sophieu=" + sophieu;
                                    }
                                    var ma_cdha = $("#macdha").val();
                                    if (ma_cdha) {
                                        url += "&ma_cdha=" + ma_cdha;
                                    }
                                    $.post(url).done(function (data) {
                                        if (data === "4") {
                                            jAlert("Bệnh nhân đã thanh toán rồi", 'Cảnh báo');
                                        } else if (data === "5") {
                                            jAlert("Bệnh nhân đã xuất thuốc rồi", 'Cảnh báo');
                                        } else if (data === "3") {
                                            jAlert("Thuốc đã có trong toa", 'Cảnh báo');
                                        } else if (data === "6") {
                                            jAlert("Số lượng thuốc vượt số lượng tồn kho", 'Cảnh báo');
                                        } else {
                                            load_cttoathuoc("noitru_toadichvu", "list_thuocdichvu");
                                        }
                                        clear_thuoc_dv();
                                        $("#tenthuongmai_dv").focus();
                                    });
                                }
                            }
                        });
                }
            });
            $("#thuocdichvu_div").keyup(function (evt) {
                if (evt.keyCode === 46 && flag_noitru !== "-1") {
                    if (flag_noitru === "1")
                        delete_thuocnoitru("list_thuocdichvu", "noitru_toadichvu");
                    else
                        jConfirm('Bạn có muốn xóa thuốc?', 'Thông báo', function (r) {
                            if (r.toString() === "true") {
                                delete_toathuocngoaitru("list_thuocdichvu", "ngoaitru_toadichvu");
                            }
                        });
                }
            });
            $("#tenthuongmai_dv").keypress(function (evt) {
                if ($("#kho_dv").val() == null) {
                    jAlert("Tủ thuốc trống vui lòng kiểm tra thông tin đăng nhập khoa phòng, tủ thuốc", 'Thông báo');
                }
            });

            /*$("#kho_dv_noi").change(function (evt) {
                    $("#tenthuongmai_dv").combogrid("option", "url", 'layvattu?makhovt=' + $("#kho_dv_noi").val());
                    $("#tengoc_dv").combogrid("option", "url", 'layvattu_theotengoc?makhovt=' + $("#kho_dv_noi").val());
                });

                $("#kho_dv_ngoai").change(function (evt) {
                    $("#tenthuongmai_dv").combogrid("option", "url", 'layvattu?makhovt=' + $("#kho_dv_ngoai").val());
                    $("#tengoc_dv").combogrid("option", "url", 'layvattu_theotengoc?makhovt=' + $("#kho_dv_ngoai").val());
                });*/
            function decodeHTMLEntities(str) {
                if (str && typeof str === 'string') {
                    // strip script/html tags
                    str = str.replace(/<script[^>]*>([\S\s]*?)<\/script>/gmi, '');
                    str = str.replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gmi, '');
                    //element.innerHTML = str;
                    //str = element.textContent;
                    //element.textContent = '';
                }

                return str;
            }

            function canhbaosolanthuchien() {
                if (Number("${ts_93202}")>0)
                {
                    //qti_CanhBaoSoLuotKhamBacSiTrongNgay();
                    var arr1 = [convertStr_MysqlDate($("#ngayth_ct").val()),'SA',0]
                    var url1 = "hgi_get_solan_thuchien_cdha_trongngay?url=" + convertArray(arr1);
                    $.ajax({
                        url: url1,
                        async: false,
                    }).done(function (data) {
                        if (Number(data) >= Number("${ts_93203}"))
                        {

                            alert("Tài khoản  " + "${Sess_User}" + " đã thực hiện của ngày "+ $("#ngayth_ct").val() +" là: " + data + " lượt ( Số lượt được thực hiện trong ngày phải không quá " + "${ts_93203}" + " lượt");
                        }


                    });
                }

            }
            loadBacSiTheoKhoa(SESS_PHONG_BAN);
            $("#luu_tt").click(function (evt) {
                var ketQuaKiemTra = THAMSO_828449=="1"?kiemTraThoiGianHopLe():"1";
                if(ketQuaKiemTra!="1"){
                    jAlert(ketQuaKiemTra, 'Thông báo');
                    return false;
                }
                if (checkNguoiDocKetQua()) {
                    jAlert("Người đọc kết quả không hợp lệ", "Cảnh báo");
                    return false;
                }
                var ngaythuchien = $("#ngayth_ct").val() + " " + $("#gioth_ct").val();
                var ngayThuchienArr = $("#ngayth_ct").val().split("/");
                var gioThucHienArr = $("#gioth_ct").val().split(":");
                var ngayGioChiDinhArr = ngay_chi_dinh.split(" ");
                var ngayChiDinhArr = ngayGioChiDinhArr[0].split("/");
                var gioChiDinhArr = ngayGioChiDinhArr[1].split(":");
                var dateNgayThucHien = new  Date(ngayThuchienArr[2],ngayThuchienArr[1],ngayThuchienArr[0],gioThucHienArr[0],gioThucHienArr[1],gioThucHienArr[2]);
                var dateNgayChiDinh = new Date(ngayChiDinhArr[2],ngayChiDinhArr[1],ngayChiDinhArr[0],gioChiDinhArr[0],gioChiDinhArr[1],gioChiDinhArr[2]);
                if ("${thamso_82860467}" == "1" && (dateNgayThucHien < dateNgayChiDinh)) {
                    jAlert("Thời gian cập nhật kết quả nhỏ hơn thời gian chỉ định","Cảnh báo")
                }else if ($("#nguoithuchien").val() != "0" && $("#nguoithuchien").val() != "${Sess_UserID}" && $("#nguoithuchien").val().trim() != "" && $("#dathuchien").prop("checked") == true) {
                    jAlert("Bạn không thể chỉnh sửa KQ siêu âm của nhân viên khác!", 'Thông báo');
                } else if ("${nhapketluan_cls}" == "1" && (CKEDITOR.instances.ketluan.getData() == "" || CKEDITOR.instances.ketluan.getData() == null)) { // 20171102 NAN VINHVT HISHD-21197 ADD
                    jAlert("Chưa nhập kết luận cho bệnh nhân!", 'Cảnh báo');
                }
                else {
                    var ngayChiDinh = $('#ngaychidinh_kq').val();
                    var gioChiDinh = $('#giochidinh_kq').val();
                    var thoiGianChiDinh = ngayChiDinh + ' ' + gioChiDinh
                    var thoiGianTHYL = $('#thoiGianBatDau_cls').val();
                    var ngayKQ = $('#ngayth_ct').val();
                    var gioKQ = $('#gioth_ct').val();
                    var thoiGianKQ = ngayKQ + ' ' + gioKQ;

                    var momentChiDinh = moment(thoiGianChiDinh, ['DD/MM/YYYY HH:mm:ss']);
                    var momentThucHienYLenh = moment(thoiGianTHYL, ['DD/MM/YYYY HH:mm:ss']);
                    var momentKetQua = moment(thoiGianKQ, ['DD/MM/YYYY HH:mm:ss']);

					if("${Sess_DVTT}" !== '96162') {
						if(momentThucHienYLenh.diff(momentChiDinh, 'minutes') < 1){
							jAlert('THỜI GIAN THỰC HIỆN Y LỆNH : '+thoiGianTHYL+'<br> KHÔNG ĐƯỢC NHỎ HƠN HOẶC BẰNG'+'<br>THỜI GIAN CHỈ ĐỊNH : ' + thoiGianChiDinh , 'Thông báo');
							return;
						}

						if(momentKetQua.diff(momentThucHienYLenh, 'minutes') < 1){
							jAlert('THỜI GIAN KẾT QUẢ : '+thoiGianKQ+'<br> KHÔNG ĐƯỢC NHỎ HƠN HOẶC BẰNG'+'<br>THỜI GIAN THỰC HIỆN Y LỆNH : ' + thoiGianTHYL , 'Thông báo');
							return;
						}

						if(momentKetQua.diff(momentThucHienYLenh, 'minutes') < 5 && THAMSO_960626 != "0"){
							jAlert('THỜI GIAN THỰC HIỆN Y LỆNH: '+thoiGianTHYL+'<br> ĐẾN GIỜ '+'<br>THỜI GIAN KẾT QUẢ : ' + thoiGianKQ + " KHÔNG ĐƯỢC NHỎ HƠN 5 PHÚT", 'Thông báo');
							return;
						}
                        if(THAMSO_960623 == "1" && typeof cmuKiemtratungThoigianCLSTheoNV == 'function' &&  !cmuKiemtratungThoigianCLSTheoNV({
                            dvtt: "${Sess_DVTT}",
                            sovaovien: $("#noitru").val()==1?sovaovien_noi:sovaovien,
                            userId: "${Sess_UserID}",
                            thoigianbd: momentThucHienYLenh.format('DD/MM/YYYY HH:mm'),
                            thoigiankt: momentKetQua.format('DD/MM/YYYY HH:mm'),
                            loaikythuat: "SA"
                        })) {
                            return false;
                        }
					}

                    var sophieu = $("#sophieu").val();
                    var makhambenh = $("#makhambenh").val();
                    var noitru = $("#noitru").val();
                    var sttbenhan = $("#sttbenhan").val();
                    var sttdotdieutri = $("#sttdotdieutri").val();
                    var sttdieutri = $("#sttdieutri").val();
                    var macdha = $("#macdha").val();
                    var dvtt = "${Sess_DVTT}";
                    var ketqua = CKEDITOR.instances.ketqua.getData();
                    //Start HIENNH.NAN : HISPTCN-433
                    if ("${Sess_DVTT}" == "40036") //40036
                    {
                        ketqua = ketqua.replace(/<strong>/g, "<b>");
                        ketqua = ketqua.replace(/<\/strong>/g, "</b>");
                        ketqua = ketqua.replace(/<s>/g, "<strike>");
                        ketqua = ketqua.replace(/<\/s>/g, "<strike>");
                        ketqua = ketqua.replace(/<em>/g, "<i>");
                        ketqua = ketqua.replace(/<\/em>/g, "</i>");
                    }
                    //End HIENNH.NAN : HISPTCN-433
                    //var ketluan = $("#ketluan").val();
                    var a = CKEDITOR.instances.ketluan.getData();
                    var b = a.replace("<p>", "");
                    var ketluan = b.replace("</p>", "").trim();
                    // chỉnh lỗi font trong xml4
                    var mota_xml5 = $('<textarea />').html(ketluan).text().replace(/<[^>]+>/g, '');
                    var kl_xml5 = $('<textarea />').html(ketqua).text();
                    var ketqua_xml5 = decodeHTMLEntities(kl_xml5);
                    //
                    var bacsichidinh = $("#bacsichidinh_chitiet").val();
                    var bacsithuchien = nv_with_chucdanh; //Lấy chức danh
                    var chandoan = $("#chandoan").val();
                    var loidanbacsi = $("#loidanbacsi").val();
                    var nguoithuchien = "${Sess_UserID}";
                    if ("${Sess_DVTT}" == '96011') {
                        nguoithuchien = $("#cmu_cbbacsidieutri").val();
                    }
                    //var mausieuam = $("#mausieuam option:selected").text();
                    var ngaygioth_ct = "";
                    if ($("#ngayth_ct").val() != '' && $("#gioth_ct").val() != '') {
                        ngaygioth_ct = convertStr_MysqlDate($("#ngayth_ct").val()) + " " + $("#gioth_ct").val();
                    }
                    var mausieuam = $("#mausieuam").val();
                    if (mausieuam == "0" || mausieuam == "" || mausieuam == null) {
                        mausieuam = "-1";
                        //jAlert("Chưa chọn mẫu siêu âm", 'Cảnh báo');
                    }
                    var maBenhLyTruocCdha = $("#mabenhly_truoccdha").val();
                    var maBenhLySauCdha = $("#mabenhly_saucdha").val();
                    var chanDoanTruocCdha = $("#chandoan_truoccdha").val();
                    var chanDoanSauCdha = $("#chandoan_saucdha").val();
                    $("#thoiGianBatDau_cls").change();//stop timer
                    var thoiGianBatDauCls = $("#thoiGianBatDau_cls").val();
                    if (macdha == "") {
                        jAlert("Chưa chọn siêu âm để thực hiện", 'Cảnh báo');
                    } else if (sophieu != "") {
                        var selectedIDs = $("#list_hinhanhnoisoi").getGridParam("selarrrow");
                        var chon_hinh = "";
                        for (var i = 0; i < selectedIDs.length; i++) {
                            var data = $("#list_hinhanhnoisoi").jqGrid('getRowData', selectedIDs[i]);
                            chon_hinh += data.STT + ",";
                        }
                        chon_hinh = chon_hinh.substring(0, chon_hinh.length - 1);
                        luuNguoiDocKetQua(sophieu, noitru==1?sovaovien_noi:sovaovien, sovaovien_dt_noi, noitru, macdha);
                        //---------------
                        var thamso960541;
                        var url = 'laythamso_donvi_motthamso?mathamso=960541';
                        $.post(url).done(function(data) {
                            thamso960541 = data
                        });
                        if($("#cbbacsittpt").val() == '') {
                            return jAlert("Vui lòng chọn bác sĩ đọc kết quả");
                        }
                        var ktrathoigian = $.ajax({type: "POST", url: "cmu_post", async: false, //Chỉ định CLS Viện Phí lấy giá trên grid. cho phép sửa giá
                            data: {url: ["${Sess_DVTT}", $("#sophieu").val(),ngaygioth_ct,
                                    sovaovien_noi == 0? sovaovien:sovaovien_noi, noitru,'CMU_KTRATHOI_CDVAKQ'].join('```')}
                        }).responseText;
                        if(ktrathoigian == 1) {
                            jAlert("Thời gian thực hiện không được dưới 5 phút", 'Cảnh báo');
                            return false;
                        }
                        $.post('cmu_post', {
                            url: [
                                sophieu, "${Sess_DVTT}", macdha, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri,
                                sovaovien, sovaovien_noi, sovaovien_dt_noi,
                                'CMU_CHECKMAY_CDHA_SA'
                            ].join('```')
                        }).done(function(data) {
                            if(thamso960541 == 1 && data > 0){
                                jAlert("Vui lòng Lưu máy thực hiện trước khi Lưu thông tin", "Cảnh báo");
                            } else {
                                if(ketluan.length < 5){
                                    jAlert("Kết luận quá ngắn, vui lòng nhập nhiều hơn 5 ký tự", "Cảnh báo");
                                } else
                                if ("${suaketqua_cls}" == "1") {   // kiem tra cho phep sua khi thanh toan
                                    var arr1 = [makhambenh, sophieu, "${Sess_DVTT}", noitru, sttbenhan, sttdotdieutri, sttdieutri, 0]
                                    var url1 = "hpg_sieuam_trangthai_thanhtoan?url=" + convertArray(arr1);
                                    $.ajax({
                                        url: url1
                                    }).done(function (data) {
                                        if (data == "2") {
                                            jAlert("Bệnh nhân đã thanh toán viện phí. Không được thay đổi kết quả.", 'Cảnh báo');
                                            return;
                                        } else {//----cho sua bt
                                            $.post("capnhatketqua_sieuam_svv", {
                                                sophieu: sophieu,
                                                macdha: macdha,
                                                dvtt: dvtt,
                                                ketqua: ketqua,
                                                ketluan: ketluan,
                                                bacsichidinh: bacsichidinh,
                                                bacsithuchien: bacsithuchien,
                                                chandoan: chandoan,
                                                loidanbacsi: loidanbacsi,
                                                mausieuam: mausieuam,
                                                noitru: noitru,
                                                sttbenhan: sttbenhan,
                                                sttdotdieutri: sttdotdieutri,
                                                sttdieutri: sttdieutri,
                                                makhambenh: makhambenh,
                                                sovaovien: sovaovien,
                                                sovaovien_noi: sovaovien_noi,
                                                sovaovien_dt_noi: sovaovien_dt_noi,
                                                chon_hinh: chon_hinh,
                                                nguoithuchien: nguoithuchien,
                                                ngaygioth_ct: ngaygioth_ct,
                                                mota_xml5: mota_xml5,
                                                ketqua_xml5: ketqua_xml5,
                                                maBenhLyTruocCdha: maBenhLyTruocCdha,
                                                maBenhLySauCdha: maBenhLySauCdha,
                                                chanDoanTruocCdha: chanDoanTruocCdha,
                                                chanDoanSauCdha: chanDoanSauCdha
                                                ,thoiGianBatDauCls: thoiGianBatDauCls
                                                ,maIcd:  $("#icdChanDoanCanLamSang").val(),
                                                tenIcd:  $("#tenChanDoanCanLamSang").val(),
                                                maBenhLy:  $("#maBenhLyChanDoanCanLamSang").val()
                                            })
                                                .done(function (datares) {
                                                    if (datares == -1) {
                                                        jAlert("Dữ liệu bệnh nhân đã khóa không thể chỉnh sửa, vui lòng liên hệ admin.", 'Cảnh báo');
                                                        return false;
                                                    }
                                                    reload_grid();
                                                    if ("${thamso_luuvain}" != "0") {
                                                        //Không hiện thông báo
                                                    }else
                                                        jAlert("Cập nhật thành công", 'Thông báo');
                                                    canhbaosolanthuchien();
                                                });
                                        }
                                    });
                                }  //-----Cho sua binh thuong
                                else
                                    $.post("capnhatketqua_sieuam_svv", {
                                        sophieu: sophieu,
                                        macdha: macdha,
                                        dvtt: dvtt,
                                        ketqua: ketqua,
                                        ketluan: ketluan,
                                        bacsichidinh: bacsichidinh,
                                        bacsithuchien: bacsithuchien,
                                        chandoan: chandoan,
                                        loidanbacsi: loidanbacsi,
                                        mausieuam: mausieuam,
                                        noitru: noitru,
                                        sttbenhan: sttbenhan,
                                        sttdotdieutri: sttdotdieutri,
                                        sttdieutri: sttdieutri,
                                        makhambenh: makhambenh,
                                        sovaovien: sovaovien,
                                        sovaovien_noi: sovaovien_noi,
                                        sovaovien_dt_noi: sovaovien_dt_noi,
                                        chon_hinh: chon_hinh,
                                        nguoithuchien: nguoithuchien,
                                        ngaygioth_ct: ngaygioth_ct,
                                        mota_xml5: mota_xml5,
                                        ketqua_xml5: ketqua_xml5,
                                        maBenhLyTruocCdha: maBenhLyTruocCdha,
                                        maBenhLySauCdha: maBenhLySauCdha,
                                        chanDoanTruocCdha: chanDoanTruocCdha,
                                        chanDoanSauCdha: chanDoanSauCdha
                                        ,thoiGianBatDauCls: thoiGianBatDauCls
                                        ,maIcd:  $("#icdChanDoanCanLamSang").val(),
                                        tenIcd:  $("#tenChanDoanCanLamSang").val(),
                                        maBenhLy:  $("#maBenhLyChanDoanCanLamSang").val()
                                    })
                                        .done(function (datares) {
                                            if (datares == -1) {
                                                jAlert("Dữ liệu bệnh nhân đã khóa không thể chỉnh sửa, vui lòng liên hệ admin.", 'Cảnh báo');
                                                return false;
                                            }
                                            reload_grid();
                                            if ("${thamso_luuvain}" != "0") {
                                                //Không hiện thông báo
                                            }else
                                                jAlert("Cập nhật thành công", 'Thông báo');
                                            canhbaosolanthuchien();
                                        });
                            }
                        });

                    }
                }
            });


            function kiemTraThoiGianHopLe() {
                var objThoiGianChiDinhChiTiet = {name: "Thời gian chỉ định", value: $("#ngaycdsa").val()||($("#ngaychidinh_kq").val() + " " + $("#giochidinh_kq").val())};
                var objThoiGianThucHienYLenh = {name: "Thời gian thực hiện Y lệnh", value: $("#thoiGianBatDau_cls").val()};
                var objThoiGianKetQua = {name: "Thời gian kết quả", value: $("#ngayth_ct").val() + " " + $("#gioth_ct").val()};
                //ThoiGianKetQua > ThoiGianThucHienYLenh > ThoiGianChiDinhChiTiet
                var objCompare = validateAndCompareDatesToMinute(objThoiGianKetQua,objThoiGianThucHienYLenh,objThoiGianChiDinhChiTiet);
                if ((objCompare.errorCode=="-1" || objCompare.errorCode=="-2") && objCompare.objects.length>0) {
                    if(objCompare.errorCode=="-1") {
                        return "Lỗi định dạng " + objCompare.objects[0].name;
                    } else {
                        return "Lỗi " + objCompare.objects[0].name + " phải sau " + objCompare.objects[1].name + " tính đến phút";
                    }
                }
                return "1"; //Hợp lệ
            }
            $("#luuthongtin").click(function (evt) {
                $("#luu_tt").click();
            });
            $("#luufile").click(function (evt) {
                if ($("#makhambenh").val() != "") {
                    var sophieu = $("#sophieu").val();
                    var makhambenh = $("#makhambenh").val();
                    var dvtt = "${Sess_DVTT}";
                    var arr2 = [sophieu, dvtt, makhambenh];
                    var url2 = "cdha_select_files?url=" + convertArray(arr2);
                    $("#list_hinhanh").jqGrid('setGridParam', {datatype: 'json', url: url2}).trigger('reloadGrid');
                    dialog_upload.open();
                }
            });
            $("#inphieu_sieuam").click(function (evt) {
                var mabenhnhan = $("#mabenhnhan").val();
                var hoten = $("#hoten").val();
                var diachi = $("#diachi").val();
                var tuoi = $("#tuoi").val();
                var phai = $("#gioitinh").val();
                var sothebhyt = $("#sothebhyt").val();
                var namsinh = $("#namsinh").val();
                var tenkhoa = $("#tenkhoa").val();
                //if (phai.toString() == "true") {
                //  phai = "Nam";
                //} else {
                //  phai = "Nữ";
                //}
                var phai = $("#gioitinh_ct").val();
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var macdha = $("#macdha").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var bssieuam = $("#bacsisieuam").val() != null && $("#bacsisieuam").val() != '' ? $("#bacsisieuam").val() : " ";
                // VNPTHIS-4697 23/11/2017
                var typein = $("#loaiin").val();
                // VNPTHIS-4697 23/11/2017
                if (sophieu != "" && macdha != "") {
                    var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha,
                        dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, sothebhyt, "0",
                        sovaovien,
                        sovaovien_noi,
                        sovaovien_dt_noi, 0, $("#trieuchungls").val(), bssieuam, maPhongBan ? maPhongBan : 0, namsinh, tenkhoa, 0, typein,
                        $("#mausieuam").val(), "0"]; // VNPTHIS-4697 23/11/2017 thêm
                    var selected = [];
                    $("#list_hinhanhnoisoi").jqGrid('getGridParam', 'selarrrow').forEach(function (data) {
                        var stt = $("#list_hinhanhnoisoi").jqGrid('getCell', data, 'STT');
                        selected.push(stt);
                    });
                    var url = "inketquasieuam_svv?thongtin=" + convertArray(arr) + "&anh=" + selected;
                    //HPG--- Xem truc tiep bao cao
                    if ("${xemtructiep_bc}" == "1") {
                        var redirectWindow = window.open(url, '_blank');
                        redirectWindow.location;
                        return false;

                    } else
                        //-------End
                        $(location).attr('href', url);
                }
            });

            $("#intheomau").click(function (evt) {
                var mabenhnhan = $("#mabenhnhan").val();
                var hoten = $("#hoten").val();
                var diachi = $("#diachi").val();
                var tuoi = $("#tuoi").val();
                var phai = $("#gioitinh").val();
                var sothebhyt = $("#sothebhyt").val();
                var namsinh = $("#namsinh").val();
                var tenkhoa = $("#tenkhoa").val();
                //if (phai.toString() == "true") {
                //  phai = "Nam";
                //} else {
                //  phai = "Nữ";
                //}
                var phai = $("#gioitinh_ct").val();
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var macdha = $("#macdha").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var bssieuam = $("#bacsisieuam").val() != null && $("#bacsisieuam").val() != '' ? $("#bacsisieuam").val() : " ";
                // VNPTHIS-4697 23/11/2017
                var typein = $("#loaiin").val();
                // VNPTHIS-4697 23/11/2017
                if (sophieu != "" && macdha != "") {
                    var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha,
                        dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, sothebhyt, "0",
                        sovaovien,
                        sovaovien_noi,
                        sovaovien_dt_noi, 0, $("#trieuchungls").val(), bssieuam, maPhongBan ? maPhongBan : 0, namsinh, tenkhoa, 0, typein,
                        $("#mausieuam").val(), "0"]; // VNPTHIS-4697 23/11/2017 thêm
                    var selected = [];
                    $("#list_hinhanhnoisoi").jqGrid('getGridParam', 'selarrrow').forEach(function (data) {
                        var stt = $("#list_hinhanhnoisoi").jqGrid('getCell', data, 'STT');
                        selected.push(stt);
                    });
                    var url = "inketquasieuam_svv?thongtin=" + convertArray(arr) + "&anh=" + selected + "&theomau=1";
                    //HPG--- Xem truc tiep bao cao
                    if ("${xemtructiep_bc}" == "1") {
                        var redirectWindow = window.open(url, '_blank');
                        redirectWindow.location;
                        return false;

                    } else
                        //-------End
                        $(location).attr('href', url);
                }
            });

            $("#inketquavahinhanh").click(function(){
                var mabenhnhan = $("#mabenhnhan").val();
                var hoten = $("#hoten").val();
                var diachi = $("#diachi").val();
                var tuoi = $("#tuoi").val();
                var phai = $("#gioitinh").val();
                var sothebhyt = $("#sothebhyt").val();
                var namsinh = $("#namsinh").val();
                var khoabenhnhan = $("#tenkhoa").val();
                //if (phai.toString() == "true") {
                //  phai = "Nam";
                //} else {
                //  phai = "Nữ";
                //}
                var phai = $("#gioitinh_ct").val();
                var sophieucdha = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var macdha = $("#macdha").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var bssieuam = $("#bacsisieuam").val() != null && $("#bacsisieuam").val() != '' ? $("#bacsisieuam").val() : " ";
                var typein = $("#loaiin option:selected").text();
                var date = new Date();
                // let today = new Date();
                // let dd = String(today.getDate()).padStart(2, '0');
                // let mm = String(today.getMonth() + 1).padStart(2, '0');
                // let yyyy = today.getFullYear();
                // today = dd + '/' + mm + '/' + yyyy;
                var ngaylap = "Ngày " + date.getDate() + " tháng " + String(date.getMonth() + 1).padStart(2, '0') + " năm " + date.getFullYear();
                var chandoan = $("#chandoan").val();
                if (sophieucdha != "" && macdha != "") {
                    var arr = [
                        mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieucdha, macdha,
                        noitru, sttbenhan, sttdotdieutri, sttdieutri, sothebhyt, bssieuam, maPhongBan ? maPhongBan : 0,
                        namsinh, khoabenhnhan, $("#mausieuam").val(), ngaylap, chandoan,
                        '/WEB-INF/pages/camau/reports/rp_ketquasieuam_tatcaanh_ketqua.jasper',
                        '/WEB-INF/pages/camau/reports/rp_ketquasieuam_tatcaanh_anh.jasper'
                    ];

                    var param = [
                        "mabenhnhan", "hoten", "diachi", "tuoi", "phai", "makhambenh", "sophieucdha", "macdha",
                        "noitru", "stt_benhan", "stt_dotdieutri", "stt_dieutri", "sothebaohiem", "bssieuam", "maphongban",
                        "namsinh", "khoabenhnhan", "mauin", "ngaylap", "chandoan",
                        'FILE_1',
                        'FILE_2'
                    ];
                    var url = "cmu_report_rp_ketquasieuam_tatcaanh?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=" + typein;
                    $(location).attr('href', url);
                    var selected = [];
                    if ("${xemtructiep_bc}" == "1") {
                        var redirectWindow = window.open(url, '_blank');
                        redirectWindow.location;
                        return false;

                    } else
                        $(location).attr('href', url);
                }
            });

            //CMU NGUYEN 01254779592 SKYPE:CONANKUN12
            function tennhanvienthuchien(ncd) {
                if ($("#dathuchien").prop("checked") == true) {
                    var data = {maNhanVien: ncd};
                    var url = 'TT_BS_ChiDinh';
                    $.post(url, data).done(function (resp) {
                        $("#bacsisieuam").val(resp[0].TEN_NHANVIEN);
                        $("#bacsisieuam").prop("disabled", true);
                    });
                    $("#viewimageweb").show();
                    $("#viewimageapp").show();
                } else {
                    $("#bacsisieuam").val('');
                    $('#viewimageweb').hide();
                    $('#viewimageapp').hide();
                }

            } //END CMU NGUYEN 01254779592 SKYPE:CONANKUN12
            $("#inphieu").click(function (evt) {
                $("#inphieu_sieuam").click();
            });

            $("#phieu_cdha_sieuam").click(function (evt) {
                if (noitru_ngoaitru == "0") {
                    var makhambenh = $("#makhambenh").val();
                    var sophieu = $("#sophieu").val();
                    var bhytkhongchi = co_bao_hiem == 1 ? 0 : 1;
                    var arr = [makhambenh, bhytkhongchi, sophieu, "${Sess_DVTT}", "0", "1", noitru_ngoaitru];
                    //Ðo?n mã c?a BDH
                    var hoten = $("#hoten").val();
                    var tuoi = $("#tuoi").val();
                    var phai = $("#gioitinh").val();
                    if (phai.toString() == "true") {
                        phai = "Nam";
                    } else {
                        phai = "Nữ";
                    }
                    var gioitinh = phai;
                    var diachi = $("#diachi").val();
                    var bschidinh = bacsi_chidinh;
                    var sothebaohiem = "";
                    //Ki?m tra b?nh nhân có BHYT
                    if (bhytkhongchi == "0")
                        sothebaohiem = $("#sothebhyt").val();
                    var maphong = phongcdha_ss;
                    if ("${thanhtoannhieunac}" == "1" && bhytkhongchi == "0") {
                        var url_taobk = "taobangke_truocin?makb=" + makhambenh + "&dvtt=" + "${Sess_DVTT}" + "&sophieu=0";
                        $.ajax({
                            url: url_taobk
                        }).done(function (data) {
                            if ("${Sess_DVTT}".indexOf("52") == 0 || "${Sess_DVTT}" == "82023") {
                                var url = "laykyhieubaocaophongcdha?maphongcdha=" + maphong;
                                $.ajax({
                                    url: url
                                }).done(function (data) {
                                    //1:PXQ
                                    if (data == "1" && sophieu != "") {
                                        arr = ["", hoten, diachi, tuoi, gioitinh, makhambenh, sophieu, "0",
                                            "${Sess_DVTT}", "0", "", "", "", "", "", "", sothebaohiem, "", bschidinh, ""];
                                        url = "bdh_inketquaxquang?url=" + convertArray(arr);
                                        $(location).attr('href', url);
                                    } else {
                                        url = "inphieucdha?url=" + convertArray(arr);
                                        $(location).attr('href', url);
                                    }
                                });
                            } else {
                                url = "inphieucdha?url=" + convertArray(arr);
                                var dvtt = "${Sess_DVTT}";
                                if ("${taibaocaovemay}" == "1") {
                                    var redirectWindow = window.open(url, '_blank');
                                    redirectWindow.location;
                                    return false;
                                } else
                                    $(location).attr('href', url);
                            }
                        });
                    } else {
                        if ("${Sess_DVTT}".indexOf("52") == 0 || "${Sess_DVTT}" == "82023") {
                            var url = "laykyhieubaocaophongcdha?maphongcdha=" + maphong;
                            $.ajax({
                                url: url
                            }).done(function (data) {
                                if (data == "1" && sophieu != "") {
                                    arr = ["", hoten, diachi, tuoi, gioitinh, makhambenh, sophieu, "0",
                                        "${Sess_DVTT}", "0", "", "", "", "", "", "", sothebaohiem, "", bschidinh, ""];
                                    url = "bdh_inketquaxquang?url=" + convertArray(arr);
                                    $(location).attr('href', url);
                                } else {
                                    url = "inphieucdha?url=" + convertArray(arr);
                                    $(location).attr('href', url);
                                }
                            });
                        } else {
                            url = "inphieucdha?url=" + convertArray(arr);
                            var dvtt = "${Sess_DVTT}";
                            if ("${taibaocaovemay}" == "1") {
                                var redirectWindow = window.open(url, '_blank');
                                redirectWindow.location;
                                return false;
                            } else
                                $(location).attr('href', url);
                        }
                    }
                }else{
                    var sophieucdha = $("#sophieu").val();
                    var bhytkhongchi = co_bao_hiem == 1 ? 0 : 1;
                    var sobenhantt = sobenhan_noitru_tt;
                    var sobenhan = sobenhan_noitru;
                    var icd_khoadt = icd_benhnhan;
                    var ten_khoadt = ten_icd_benhnhan;
                    if (sobenhan != "")
                        soba = sobenhan;
                    else
                        soba = sobenhantt;
                    var arr = [mabenhnhan, bhytkhongchi, sophieucdha, "${Sess_DVTT}", soba, stt_benhan, stt_dotdieutri, stt_dieutri, icd_khoadt, ten_khoadt, "", "0"
                        , sovaovien_noi, sovaovien_dt_noi, 0, noitru_ngoaitru];
                    var url = "noitru_inphieucdha_svv?url=" + convertArray(arr);
                    $(location).attr('href', url);
                }
            });
            //========= Begin KGG Trạng Ngày 07/12/2016 =======================
            $("#inphieu_sieuam_ngang").click(function (evt) {
                var mabenhnhan = $("#mabenhnhan").val();
                var hoten = $("#hoten").val();
                var diachi = $("#diachi").val();
                var tuoi = $("#tuoi").val();
                var phai = $("#gioitinh").val();
                var sothebhyt = $("#sothebhyt").val();
                if (phai == "true") {
                    phai = "Nam";
                } else {
                    phai = "Nữ";
                }
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var macdha = $("#macdha").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var typein = $("#loaiin").val();

                if (sophieu != "" && macdha != "") {
                    var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha,
                        dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, sothebhyt, "0", maPhongBan ? maPhongBan : 0, typein];
                    var url = "inketquasieuam_ngang?thongtin=" + convertArray(arr) + "&chanDoan=" + $("#trieuchungls").val();
                    $(location).attr('href', url);
                }
            });
            $("#inphieu_sieuam_ngang1").click(function (evt) {
                $("#inphieu_sieuam_ngang").click();
            });
            // START TGGDEV-39861
            if ("${thamso_luuvain}" != "0") {
                $("#luuvain").click(function (evt) {
                    $("#luu_tt").click();
                    $("#inphieu_sieuam").click();
                });
                $("#luuvainphieungang").click(function (evt) {
                    $("#luu_tt").click();
                    $("#inphieu_sieuam_ngang").click();
                });
            }
            // END TGGDEV-39861
            // ======== END KGG Trạng Ngày 07/12/2016=================

            reload_grid();
            //$('textarea#ketqua').ckeditor();
            $("#tab_cdha").tabs();
            $('#tab_cdha').tabs({
                select: function (event, ui) {
                    // alert('selected: '+ui.index);
                    if (ui.index == 0) {
                        gioth_ct_timer_previous_status = gioth_ct_timer_is_on;
                        stopGioThCtTimer();
                        if (giothuchien_cls_timer_previous_status) {
                            if (tatAutoTime != 1)
                                showtime_giothuchien_cls();
                        }
                    } else if (ui.index == 1) {
                        giothuchien_cls_timer_previous_status = giothuchien_cls_timer_is_on;
                        stopGioThucHienClsTimer();
                        if (gioth_ct_timer_previous_status) {
                            if (tatAutoTime != 1)
                                showtime_gioth_ct();
                        }
                    }
                }
            });
            $("#tab_ls_cdha").tabs();
            $("#div_hinhanhnoisoi").keyup(function (evt) {
                if (evt.keyCode == 46) {
                    var id = $("#list_hinhanhnoisoi").jqGrid('getGridParam', 'selrow');
                    if (id) {
                        var makhambenh = $("#makhambenh").val();
                        var noitru = $("#noitru").val();
                        var sttbenhan = $("#sttbenhan").val();
                        var sttdotdieutri = $("#sttdotdieutri").val();
                        var sttdieutri = $("#sttdieutri").val();
                        var ret = $("#list_hinhanhnoisoi").jqGrid('getRowData', id);
                        var arr2 = [ret.SO_PHIEU_CDHA, ret.DVTT, ret.MA_CDHA, ret.STT, noitru];
                        var url2 = "sieuam_xoa_hinhanh?url=" + convertArray(arr2);
                        $.ajax({
                            url: url2
                        }).done(function () {
                            var arr = [ret.SO_PHIEU_CDHA, ret.DVTT, ret.MA_CDHA, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                            var url = 'sieuam_danhsach_hinhanh?url=' + convertArray(arr);
                            $("#list_hinhanhnoisoi").jqGrid('setGridParam', {
                                datatype: 'json',
                                url: url
                            }).trigger('reloadGrid');
                        });
                    }
                }
            });
            $("#mausieuam").change(function (evt) {
                // VNPTHIS-4697 thêm đơn vị 94 không cần thông báo khi đổi mẫu siêu âm
                var xacnhan = "";
                if ("${hienthi_checkbox}" != "1") {
                    xacnhan = true;
                } // VNPTHIS-4697 thêm đơn vị 94 không cần thông báo khi đổi mẫu siêu âm
                else {
                    xacnhan = confirm("Bạn có muốn thay đổi mẫu siêu âm?");
                }
                if (xacnhan) {
                    if ("${hienthi_checkbox}" == "1") {
                        var sophieu = $("#sophieu").val();
                        var ma_cdha = $("#macdha").val();
                        var noitru = $("#noitru").val();
                        var url = "xoa_ds_checkbox_chualuu?sophieu=" + sophieu + "&ma_cdha=" + ma_cdha + "&noitru=" + noitru + "&dvtt=" + ${Sess_DVTT} +"&sovaovien=" + sovaovien + "&sovaovien_noi=" + sovaovien_noi + "&sovaovien_dt_noi=" + sovaovien_dt_noi;
                        $.ajax({
                            url: url
                        }).done(function (data) {
                        });
                        <c:forEach var="i" items="${formsieuam}">
                        $("#checkbox_${i.MA_MAUSIEUAM}").css("display", "none");
                        </c:forEach>
                    }
                    var id = $("#mausieuam").val();
                    if (id !== "0") {
                        if ("${hienthi_checkbox}" == "1") {
                            if (id == "3") {
                                $("#form_sieuammau_doppler").click();
                            }
                            else {
                                $("#checkbox_" + id + " :checkbox").removeAttr('checked');
                                var sophieu = $("#sophieu").val();
                                var ma_cdha = $("#macdha").val();
                                var dvtt = "${Sess_DVTT}";
                                var url = "lay_checkbox_dacheck?sophieu=" + sophieu + "&ma_cdha=" + ma_cdha + "&dvtt=" + dvtt + "&noitru=" + noitru + "&sovaovien=" + sovaovien + "&sovaovien_noi=" + sovaovien_noi + "&sovaovien_dt_noi=" + sovaovien_dt_noi;
                                $.ajax({
                                    url: url
                                }).done(function (data) {
                                    $.each(data, function (i) {
                                        $("#satq_" + data[i].MA_CHECKBOX).prop('checked', true);
                                    });
                                })
                                ;
                                $("#checkbox_" + id).css("display", "");
                            }
                        }
                        var url = "select_mausieuam_theoma?ma=" + id + "&dvtt=${Sess_DVTT}";
                        $.ajax({
                            url: url
                        }).done(function (data) {
                            CKEDITOR.instances.ketqua.setData(data[0]["NOIDUNG"]);
                            CKEDITOR.instances.ketluan.setData(data[0]["KET_LUAN"]);
                            //$('#ketluan').val(data[0]["KET_LUAN"]);
                            //$('#ketqua').val(data);
                        });
                        /*var ma_cdha = $('#ma_cdha').val();
                        var makhambenh = $("#makhambenh").val();
                        var noitru = $("#noitru").val();
                        var sttbenhan = $("#sttbenhan").val();
                        var sttdotdieutri = $("#sttdotdieutri").val();
                        var sttdieutri = $("#sttdieutri").val();
                        var arr = [sophieu, "

                        ${Sess_DVTT}", ma_cdha, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0", sovaovien, sovaovien_noi, sovaovien_dt_noi];
                        var url = "sieuam_select_ketqua_svv?url=" + convertArray(arr);
                        $.getJSON(url, function (result) {
                            $.each(result, function (i, field) {
                                CKEDITOR.instances.ketqua.setData(field.KET_QUA);
                            });
                        });*/

                    }
                }
            });

            <c:forEach var="i" items="${formsieuam}">
            $("#tab_sieuam_${i.MA_MAUSIEUAM}").tabs({
                activate: function (event, ui) {
                    //var tabsa = '#tab_sieuam_' + $("#mausieuam").val();
                    setTenCoquanTemp($(this));
                }
            });
            $("#checks_${i.MA_MAUSIEUAM} :checkbox").change(function (e) {
                var ma_checkbox = $(this).val();
                if ($(this).is(":checked")) {
                    themNoiDung(ma_checkbox);
                }
                else {
                    xoaNoiDung(ma_checkbox);

                }

            });
            </c:forEach>
            // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Tân 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT
            $("#phongban").change(function (evt) {
                var url = "layphongbenh_theokhoaxn?khoa=" + $("#phongban").val() + "&dvtt=${Sess_DVTT}";
                $.ajax({
                    url: url
                }).done(function (data) {
                    if (data) {
                        $("#phongbenh").empty();
                        $.each(data, function (i) {
                            $("<option value='" + data[i].MA_PHONG_BENH + "'>" + data[i].TEN_PHONG_BENH + "</option>").appendTo("#phongbenh");
                        });
                    }
                });
                $("#phongbenh").val(-1);
                reload_grid();
            });
            $("#phongbenh").change(function (evt) {
                reload_grid();
            });
            $("#doituong").change(function (evt) {
                reload_grid();
            });
            // End ĐắkLắk
            $("#dathuchien").change(function (evt) {
                reload_grid();
                if ("${Sess_DVTT}" == 96011) {
                    if($(this).prop("checked")) {
                        $("#khoabs_cmu").hide();
                        $("#cmu_cbbacsidieutri").hide()
                        $("#bacsisieuam").show();
                    } else {
                        $("#khoabs_cmu").show();
                        $("#cmu_cbbacsidieutri").show()
                        $("#bacsisieuam").hide();
                    }
                }
            });
            $("#dathuchien1").change(function (evt) {
                $("#dathuchien").prop('checked', $("#dathuchien1").prop('checked'));

            });
            $("#chandoan").keypress(function (evt) {
                if (evt.keyCode == 13) {
                    $("#mausieuam").focus();
                }
            });
            $("#mausieuam").keypress(function (evt) {
                if (evt.keyCode == 13) {
                    $("#ketqua").focus();
                }
            });
            //Dũng cmu
            var dialog_timkiemhuyen =
                new jBox('Modal', {
                    title: "Lịch sử siêu âm",
                    overlay: false,
                    content: $('#dialog_lichsusieuam'),
                    draggable: 'title'
                });
            //end cmu
            $("#lichsusieuam").click(function (evt) {
                //var stt_benhan = $("#sttbenhan").val();
                var mabenhnhan = $("#mabenhnhan").val();
                var hoten = $("#hoten").val();
                if (hoten !== "") {
                    load_lscdha_bn(mabenhnhan);
                    dialog_timkiemhuyen.open();
                } else {
                    jAlert("Vui lòng chọn một bệnh nhân!", "Thông báo");
                }
            });
            //cmu
//                var editor = CKEDITOR.instances.ls_ketqua;
//                if (editor) { editor.destroy(true); }
//                CKEDITOR.replace('ls_ketqua');
            $("#list_lichsusieuam").jqGrid({
                datatype: "local",
                loadonce: true,
                height: 450,
                width: 900,
                colNames: ["Ngày", "Đơn vị", "Chẩn đoán", "Tên Siêu Âm", "Số lượng", "Đơn giá", "Thành tiền", "BHYT không chi", "Kết quả", "Kết luận",
                    "sophieu", "stt_benhan", "Kết quả"],
                colModel: [
                    {name: 'NGAY_THUC_HIEN', index: 'NGAY_THUC_HIEN', width: 45},
                    {name: 'TEN_DONVI', index: 'TEN_DONVI', width: 80},
                    {name: 'CHANDOAN', index: 'CHANDOAN', width: 100},
                    {name: 'TEN_CDHA', index: 'TEN_CDHA'},
                    {name: 'SO_LUONG', index: 'SO_LUONG', width: 100, hidden: true},
                    {
                        name: 'DON_GIA',
                        index: 'DON_GIA',
                        width: 100,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        hidden: true
                    },
                    {
                        name: 'THANH_TIEN',
                        index: 'THANH_TIEN',
                        width: 100,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        hidden: true
                    },
                    {
                        name: 'BHYTKCHI',
                        index: 'BHYTKCHI',
                        width: 110,
                        align: 'center',
                        formatter: 'checkbox',
                        formatoptions: {value: 'true:false'},
                        hidden: true
                    },
                    {name: 'KET_QUA', index: 'KET_QUA', width: 100, hidden: true},
                    {name: 'MO_TA', index: 'MO_TA', width: 80},
                    {name: 'SO_PHIEU_XN', index: 'SO_PHIEU_XN', width: 100, hidden: true},
                    {name: 'STT_BENHAN', index: 'STT_BENHAN', width: 100, hidden: true},
                    {name: 'KET_QUA', index: 'KET_QUA', width: 150}
                ],
                caption: "Lịch sử siêu âm",
                rowNum: 1000
            });

            // STG
            $("#gioth_ct").click(function (evt) {
                //showtime_gioth_ct_cancel = (showtime_gioth_ct_cancel == 1 ? 0 : 1);
                gioThCtTimerChange();
            });

            $("#giothuchien_cls").click(function (evt) {
                //showtime_giothuchien_cls_cancel = (showtime_giothuchien_cls_cancel == 1 ? 0 : 1);
                gioThucHienClsTimerChange();
            });
            // STG

            //$("#list_lichsusieuam").jqGrid('setGridParam', {datatype: 'json', url: urlcmu}).trigger('reloadGrid');
            /*$("#ketqua").keypress(function (evt) {
                 if (evt.keyCode == 13) {
                 $("#ketluan").focus();
                 }
                 });*/
            /*$("#ketluan").keypress(function (evt) {
                 if (evt.keyCode == 13) {
                 $("#loidanbacsi").focus();
                 }
                 });*/
            //--Start VNPT Bình Định
            if ("${timtheophong_th}" == "1") {
                $("#tr_phong").show();
                $("#dlk_phong").hide();
            } else {
                $("#tr_phong").hide();
            }
            $("#phongban").change(function (evt) {
                reload_grid();
            });
            $("#phongthuchiencdha").change(function (evt) {
                reload_grid();
            });
            //--End VNPT Bình Định

            //RIS
            $("#viewimageweb").click(function (evt) {
                var sophieu = $("#sophieu").val();
                var macdha = $("#macdha").val();
                var arr = [sophieu, macdha];
                if (sophieu != "") {
                    var url = "ris_viewimage_web?url=" + convertArray(arr);
                    $.ajax({
                        url: url
                    }).done(function (urlweb) {
                        if (urlweb == "ERRLOGIN") {
                            jAlert("Xác thực đăng nhập RIS Connector thất bại, Vui lòng kiểm tra lại thông tin cấu hình kết nối RIS", "Thông báo");
                        } else if (urlweb == "ERROR") {
                            jAlert("Đã xảy ra lỗi", 'Thông báo');
                        } else if (urlweb == "RIS.7") {
                            jAlert("Không thể tìm thấy dữ liệu hình ảnh trên PACS", 'Thông báo');
                        } else if (urlweb == "RIS.6") {
                            jAlert("Không thể tìm thấy ca chụp trên RIS", 'Thông báo');
                        } else if (urlweb == "RIS.4") {
                            jAlert("Lấy đường dẫn xem ảnh không thành công", 'Thông báo');
                        } else if (urlweb == "NOTRECEIVE") {
                            jAlert("RIS chưa nhận ca chụp này", 'Thông báo');
                        } else {
                            var redirectWindow = window.open(urlweb, '_blank');
                            redirectWindow.location;
                            return false;
                        }
                    });
                }
            });

            $("#viewimageapp").click(function (evt) {
                var sophieu = $("#sophieu").val();
                var macdha = $("#macdha").val();
                var arr = [sophieu, macdha];
                if (sophieu != "") {
                    var url = "ris_viewimage_app?url=" + convertArray(arr);
                    $.ajax({
                        url: url
                    }).done(function (urlapp) {
                        if (urlapp == "ERRLOGIN") {
                            jAlert("Xác thực đăng nhập RIS Connector thất bại, Vui lòng kiểm tra lại thông tin cấu hình kết nối RIS", "Thông báo");
                        } else if (urlapp == "ERROR") {
                            jAlert("Đã có lỗi xảy ra", 'Thông báo');
                        } else if (urlapp == "RIS.7") {
                            jAlert("Không thể tìm thấy dữ liệu hình ảnh trên PACS", 'Thông báo');
                        } else if (urlapp == "RIS.6") {
                            jAlert("Không thể tìm thấy ca chụp trên RIS", 'Thông báo');
                        } else if (urlapp == "RIS.4") {
                            jAlert("Lấy đường dẫn xem ảnh không thành công", 'Thông báo');
                        } else if (urlapp == "NOTRECEIVE") {
                            jAlert("RIS chưa nhận ca chụp này", 'Thông báo');
                        } else {
                            if (!deployJava.isWebStartInstalled("1.7.0")) {
                                if (deployJava.installLatestJRE()) {
                                    if (deployJava.launch(urlapp)) {
                                    }
                                }
                            } else {
                                if (deployJava.launch(urlapp)) {
                                }
                            }
                        }
                    });
                }
            });
            //--END RIS
            if ("${thamSo8448811}" == 1) {
                $(".icdTruocSau").show();
            }
            else {
                $(".icdTruocSau").hide();
            }
            $("#chandoan_truoccdha").combogrid({
                url: 'laydanhmucicd',
                debug: true,
                width: "400px",
                colModel: [{'columnName': 'ICD', 'label': 'ICD'},
                    {'columnName': 'MO_TA_BENH_LY', 'width': '40', 'label': 'Mô tả bệnh lý'},
                    {'columnName': 'MA_BENH_LY', 'width': '30', 'label': 'mabenhly', hidden: true}
                ],
                select: function (event, ui) {
                    $("#chandoan_truoccdha").val(ui.item.MO_TA_BENH_LY);
                    $("#icd_truoccdha").val(ui.item.ICD);
                    $("#mabenhly_truoccdha").val(ui.item.MA_BENH_LY);
                    return false;
                }
            });
            $("#chandoan_saucdha").combogrid({
                url: 'laydanhmucicd',
                debug: true,
                width: "400px",
                colModel: [{'columnName': 'ICD', 'label': 'ICD'},
                    {'columnName': 'MO_TA_BENH_LY', 'width': '40', 'label': 'Mô tả bệnh lý'},
                    {'columnName': 'MA_BENH_LY', 'width': '30', 'label': 'mabenhly', hidden: true}
                ],
                select: function (event, ui) {
                    $("#chandoan_saucdha").val(ui.item.MO_TA_BENH_LY);
                    $("#icd_saucdha").val(ui.item.ICD);
                    $("#mabenhly_saucdha").val(ui.item.MA_BENH_LY);
                    return false;
                }
            });
            $("#icd_truoccdha").keyup(function (evt) {
                if ($("#icd_truoccdha").val() != "") {
                    var url = "laymotabenhly?icd=" + $("#icd_truoccdha").val();
                    $.ajax({
                        url: url
                    }).done(function (data) {
                        if (data && data != "" && data != " ") {
                            arr = data.split("!!!");
                            $("#icd_truoccdha").val($("#icd_truoccdha").val().toString().toUpperCase());
                            $("#chandoan_truoccdha").val(arr[1]);
                            $("#mabenhly_truoccdha").val(arr[0]);
                        } else {
                            $("#chandoan_truoccdha").val("");
                            $("#mabenhly_truoccdha").val("");
                        }
                    }).error(function () {
                        $("#chandoan_truoccdha").val("");
                        $("#mabenhly_truoccdha").val("");
                    });
                }
            });

            $("#icd_saucdha").keyup(function (evt) {
                if ($("#icd_saucdha").val() != "") {
                    var url = "laymotabenhly?icd=" + $("#icd_saucdha").val();
                    $.ajax({
                        url: url
                    }).done(function (data) {
                        if (data && data != "" && data != " ") {
                            arr = data.split("!!!");
                            $("#icd_saucdha").val($("#icd_saucdha").val().toString().toUpperCase());
                            $("#chandoan_saucdha").val(arr[1]);
                            $("#mabenhly_saucdha").val(arr[0]);
                        } else {
                            $("#chandoan_saucdha").val("");
                            $("#mabenhly_saucdha").val("");
                        }
                    }).error(function () {
                        $("#chandoan_saucdha").val("");
                        $("#mabenhly_saucdha").val("");
                    });;
                }
            });
        });
        $("#hsbant_tabs").tabs({
            activate: function () {
                var selectedTab = $('#hsbant_tabs').tabs('option', 'active');
                if (selectedTab === 10) {
                    phieuchucnangsong_ttbenhnhan();
                    var ttbn = get_param();
                    create_chart(ttbn);

                }
            }
        });


        function themNoiDung(ma_checkbox) {
            var sophieu = $("#sophieu").val();
            var dvtt = "${Sess_DVTT}";
            var noitru = $("#noitru").val();
            var ma_cdha = $("#macdha").val();
            var arr = [
                sophieu, dvtt, ma_checkbox, ma_cdha, noitru, sovaovien, sovaovien_noi, sovaovien_dt_noi, 0
            ];
            var url = "laynoidung_thaydoi";
            $.post(url, {url: convertArray(arr)}).done(function (data) {

                var noidung_replace = data[0].NOIDUNG_THAYDOI;
                var noidung_cu = data[0].NOIDUNGCU;
                var ketluan_replace = data[0].KETLUAN_THAYDOI;
                var ketluan_cu = data[0].KETLUANCU;
                ketluan_cu = ketluan_cu.replaceAll('! ', '\n\n');
                if (ketluan_replace != null) {
                    ketluan_replace = ketluan_replace.replaceAll('! ', '');
                }
                capNhatKetQua(noidung_cu, noidung_replace);
//                    CKEDITOR.instances.ketqua.setData(CKEDITOR.instances.ketqua.getData().replace(noidung_cu,noidung_replace));
                if (ketluan_replace != null) {
                    CKEDITOR.instances.ketluan.setData(CKEDITOR.instances.ketluan.getData().replace(ketluan_cu, ketluan_replace));
                }
            });
        };

        function xoaNoiDung(ma_chk) {
            var sophieu = $("#sophieu").val();
            var dvtt = "${Sess_DVTT}";
            var ma_cdha = $("#macdha").val();
            var noitru = $("#noitru").val();
            var arr = [
                sophieu, dvtt, ma_chk, ma_cdha, noitru, sovaovien, sovaovien_noi, sovaovien_dt_noi, 0
            ];
            var url = "xoanoidung_thaydoi";
            $.post(url, {url: convertArray(arr)}).done(function (data) {
                var noidung_replace = data[0].NOIDUNG_THAYDOI;
                var noidung_cu = data[0].NOIDUNGCU;
                var ketluan_replace = data[0].KETLUAN_THAYDOI;
                var ketluan_cu = data[0].KETLUANCU;
                ketluan_cu = ketluan_cu.replaceAll('! ', '\n\n');
                capNhatKetQua(noidung_cu, noidung_replace);
                //var ketluan_cu= CKEDITOR.instances.ketluan.getData();
//                    CKEDITOR.instances.ketqua.setData(CKEDITOR.instances.ketqua.getData().replace(noidung_cu,noidung_replace));
                if (ketluan_replace != null) {
                    CKEDITOR.instances.ketluan.setData(CKEDITOR.instances.ketluan.getData().replace(ketluan_cu, ketluan_replace));
                }
                var makhambenh = $("#makhambenh").val();
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var macdha = $("#macdha").val();
                var ketqua = CKEDITOR.instances.ketqua.getData();
                //var ketluan = $("#ketluan").val();
                var ketluan = CKEDITOR.instances.ketluan.getData();
                var bacsichidinh = $("#bacsichidinh").val();
                var bacsithuchien = $("#bacsisieuam").val();
                var chandoan = $("#chandoan").val();
                var loidanbacsi = $("#loidanbacsi").val();
                var nguoithuchien = "${Sess_UserID}";
                //var mausieuam = $("#mausieuam option:selected").text();
                var ngaygioth_ct = "";
                if ($("#ngayth_ct").val() != '' && $("#gioth_ct").val() != '') {
                    ngaygioth_ct = convertStr_MysqlDate($("#ngayth_ct").val()) + " " + $("#gioth_ct").val();
                }
                var mausieuam = $("#mausieuam").val();
                $.post("capnhatketqua_sieuam_checkbox", {
                    sophieu: sophieu,
                    macdha: macdha,
                    dvtt: dvtt,
                    ketqua: ketqua,
                    ketluan: ketluan,
                    noitru: noitru,
                    sttbenhan: sttbenhan,
                    sttdotdieutri: sttdotdieutri,
                    sttdieutri: sttdieutri,
                    makhambenh: makhambenh,
                    sovaovien: sovaovien,
                    sovaovien_noi: sovaovien_noi,
                    sovaovien_dt_noi: sovaovien_dt_noi
                })
                    .done(function () {
                    });
            });

        }

        function capNhatKetQua(noidung_cu, noidung_replace) {
            var kqBanDau = CKEDITOR.instances.ketqua.getData();
            var tcq = layNoiDungCoquan();

            var pos = kqBanDau.search(tcq);

            if (pos == -1) {
                CKEDITOR.instances.ketqua.setData(kqBanDau.replace(noidung_cu, noidung_replace));
            } else {
                var kqThayDoi = kqBanDau.substr(0, pos) + kqBanDau.substr(pos).replace(noidung_cu, noidung_replace);
                CKEDITOR.instances.ketqua.setData(kqThayDoi);
            }
        }

        function layNoiDungCoquan() {

            return CKEDITOR.instances.temp.getData().replace('<p>', '').replace('</p>', "").trim();
        }

        function setTenCoquanTemp(tab) {
            CKEDITOR.instances.temp.setData($(tab).find('.ui-tabs-active').text().toUpperCase());
        }

        function loadThongTinBenhNhan(ret) {
            $("#mabenhnhan").val(ret.MABENHNHAN);
            $("#hoten").val(ret.TENBENHNHAN);
            $("#tuoi").val(ret.TUOI);
            $("#gioitinh").val(ret.GIOITINH.toString());
            $("#diachi").val(ret.DIACHI);
            $("#sothebhyt").val(ret.SOTHEBHYT);
            $("#sophieu").val(ret.SO_PHIEU);
            $("#makhambenh").val(ret.MA_KHAM_BENH);
            $("#noitru").val(ret.NOITRU);
            $("#sttbenhan").val(ret.STT_BENHAN);
            $("#sttdotdieutri").val(ret.STT_DOTDIEUTRI);
            $("#sttdieutri").val(ret.STT_DIEUTRI);
            $("#mabacsichidinh").val(ret.NGUOI_CHI_DINH);
            sovaovien = ret.SOVAOVIEN;
            sovaovien_noi = ret.SOVAOVIEN_NOI;
            sovaovien_dt_noi = ret.SOVAOVIEN_DT_NOI;
            $("#sovaovien").val(sovaovien == 0 ? sovaovien_noi : sovaovien);
            maPhongBan = ret.MA_PHONGBAN;
            da_thanh_toan = ret.DA_THANH_TOAN;
            sophieuthanhtoan = ret.SOPHIEUTHANHTOAN;
            cobhyt = ret.TI_LE_MIEN_GIAM.replace('.00', '');
            ngay_kb = ret.NGAY_KB;
            tlmg = ret.TI_LE_MIEN_GIAM;
            flag_noitru = ret.NOITRU;
            mabenhnhan = ret.MABENHNHAN;
            //sovaovien_dt=ret.SOVAOVIEN_DT_NOI;
            stt_dieutri = ret.STT_DIEUTRI;
            stt_benhan = ret.STT_BENHAN;
            stt_dotdieutri = ret.STT_DOTDIEUTRI;
            // Sang start
            $("#bacsisieuam").val(ret.BACSI_THUCHIEN);

            if (ret.BACSI_THUCHIEN == '') {
                $("#bacsisieuam").val(nv_with_chucdanh);
            }
            if (ret.NOITRU === "0") {
                $("#kho_dv_ngoai").show();
                $("#kho_dv_noi").hide();
            } else {
                $("#kho_dv_noi").show();
                $("#kho_dv_ngoai").hide();
            }
            // Sang end

            // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: thêm thông tin khoa chỉ định, chẩn đoán icd
            $("#_chandoan").val(ret.CHUANDOANICD);
            $("#chandoan").val(ret.CHUANDOANICD);
            if("${Sess_DVTT}" == '96170') {
                $("#benhtheobs").val(ret.CHUANDOANSOBO);
            }
            $("#tenkhoa").val(ret.TENKHOA);
            // End ĐắkLắk

            // ĐắkLắk (An Giang yêu cầu) - Ninh 09/12/2016: view thông tin hành chánh của BN lên form nhập kết quả
            $("#hoten_ct").val(ret.TENBENHNHAN);
            $("#tuoi_ct").val(ret.TUOI);
            $("#gioitinh_ct").val(ret.GIOITINH.toString() == "true" ? "Nam" : "Nữ");
            $("#mabenhnhan_ct").val(ret.MABENHNHAN);
            $("#tenkhoa_ct").val(ret.TENKHOA);
            $("#sothebhyt_ct").val(ret.SOTHEBHYT);
            $("#namsinh").val(ret.NAMSINH);
            // End ĐắkLắk

            var url_bs = "select_tenbacsi?mabacsi=" + ret.NGUOI_CHI_DINH + "&dvtt= " + "${Sess_DVTT}";
            $.ajax({
                url: url_bs
            }).done(function (data) {
                $("#bacsichidinh").val(data);
            });
            matoathuoc = flag_noitru === '1' ? (ret.SO_PHIEU.split('.')[2]).split('_')[0] :
                ret.MA_KHAM_BENH.replace("kb_", "tt_");
            $("#trieuchungls").val(ret.CHUANDOANICD);
            if ("${hienthi_them_cls}" == "1") {
                var hpg_STT_BENHAN = "0";
                var hpg_STT_DIEUTRI = "0";
                var hpg_STT_DOTDIEUTRI = "0";
                if (ret.STT_DIEUTRI != "")
                    hpg_STT_BENHAN = ret.STT_BENHAN;
                if (ret.STT_DIEUTRI != "")
                    hpg_STT_DOTDIEUTRI = ret.STT_DOTDIEUTRI;
                if (ret.STT_DIEUTRI != "")
                    hpg_STT_DIEUTRI = ret.STT_DIEUTRI;
                var arr1 = [ret.MA_KHAM_BENH, ret.SO_PHIEU, "${Sess_DVTT}", ret.NOITRU, hpg_STT_BENHAN, hpg_STT_DOTDIEUTRI, hpg_STT_DIEUTRI, 3]
                var url1 = "hpg_thongtin_mo_rong_bn_cls?url=" + convertArray(arr1);
                hienthi_them_cls(url1);
            }


        }

        function loadDsCD(ret) {
            $("#ma_maycdha_md").val("");
            var dathuchien = $("#dathuchien").prop('checked');
            var idath;
            if (dathuchien == true) {
                idath = 1;
            } else {
                idath = 0;
            }
            var arr = [ret.NOITRU, ret.SO_PHIEU, ret.STT_BENHAN, ret.STT_DOTDIEUTRI, ret.STT_DIEUTRI, "${Sess_DVTT}", sovaovien, sovaovien_noi, sovaovien_dt_noi, idath];
            var url = "sieuam_hienthi_chitiet_svv?url=" + convertArray(arr);
            $("#list_sieuam_bhyt").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
        }

        function loadThongTinKetQua(ret) {
            $("#macdha").val(ret.MA_CDHA);
            $("#nguoithuchien").val(ret.NGUOI_THUC_HIEN);
            $("#frm_kq_id_ekip").val(ret.ID_EKIP);
            $("#frm_kq_so_phieu_dichvu").val($("#sophieu").val());
            $("#frm_kq_ma_dv").val(ret.MA_CDHA);
            $("#frm_kq_mabenhnhan").val($("#mabenhnhan").val());
            $("#frm_kq_sovaovien").val(sovaovien == 0 ? sovaovien_noi : sovaovien);
            $("#frm_kq_sovaovien_dt").val(sovaovien_dt_noi);
            $("#frm_kq_noitru").val($("#noitru").val());
            $("#frm_kq_id_dieutri").val(ret.ID_DIEUTRI);
            var sophieu = $("#sophieu").val();
            var ma_cdha = $("#macdha").val();
            var makhambenh = $("#makhambenh").val();
            var noitru = $("#noitru").val();
            var sttbenhan = $("#sttbenhan").val();
            var sttdotdieutri = $("#sttdotdieutri").val();
            var sttdieutri = $("#sttdieutri").val();
            getNguoiDocKetQua(sophieu, noitru==1?sovaovien_noi:sovaovien, sovaovien_dt_noi, noitru, ma_cdha);
            if (flag_noitru === '1') {
                load_cttoathuoc("noitru_toadichvu", "list_thuocdichvu");
            } else {
                var url = 'chitiettoathuocngoatru_svv?matt=' + matoathuoc + "&nghiepvu=ngoaitru_toadichvu&dvtt=${Sess_DVTT}" + "&sovaovien=" + sovaovien + "&ma_cdha=" + ma_cdha + "&sophieu=" + sophieu;
                $("#list_thuocdichvu").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
            }
            $('#mausieuam').removeAttr("disabled");
            if ("${hienthi_checkbox}" == "1") {
                <c:forEach var="i" items="${formsieuam}">
                $("#checkbox_${i.MA_MAUSIEUAM}").css("display", "none");
                </c:forEach>
                $("#form_sieuammau_doppler").css("display", "");
                $("#inphieu_sieuam_mau_doppler").css("display", "");
                var sophieu = $("#sophieu").val();
                var ma_cdha = $("#macdha").val();
                var noitru = $("#noitru").val();
                var url = "xoa_ds_checkbox_chualuu?sophieu=" + sophieu + "&ma_cdha=" + ma_cdha + "&noitru=" + noitru + "&dvtt=" + ${Sess_DVTT} +"&sovaovien=" + sovaovien + "&sovaovien_noi=" + sovaovien_noi + "&sovaovien_dt_noi=" + sovaovien_dt_noi;
                $.ajax({
                    url: url
                }).done(function (data) {
                });
            }
            var arr = [sophieu, "${Sess_DVTT}", ret.MA_CDHA, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0", sovaovien, sovaovien_noi, sovaovien_dt_noi];
            var url = "sieuam_select_ketqua_svv?url=" + convertArray(arr);
            $.getJSON(url, function (result) {
                $.each(result, function (i, field) {
                    $("#bacsichidinh_chitiet").val(field.BACSI_CHIDINH);
                    $("#bacsichidinh").val(field.BACSI_CHIDINH);
                    $("#bacsisieuam").val(field.BACSI_THUCHIEN);
                    $("#mabenhly_truoccdha").val(field.MABENHLY_TRUOCCDHA);
                    $("#mabenhly_saucdha").val(field.MABENHLY_SAUCDHA);
                    $("#icd_truoccdha").val(field.ICD_TRUOCCDHA);
                    $("#icd_saucdha").val(field.ICD_SAUCDHA);
                    $("#chandoan_truoccdha").val(field.CHANDOAN_TRUOCCDHA);
                    $("#chandoan_saucdha").val(field.CHANDOAN_SAUCDHA);
                    if (field.MA_MAUSIEUAM != 0 || field.MA_MAUSIEUAM != "")
                        $("#mausieuam").val(field.MA_MAUSIEUAM);
                    else {
                        $("#mausieuam").val("-1");
                    }

                    // STG
                    if (field.NGAYTHUCHIEN == null) {
                        $("#ngayth_ct").val("${ngayhientai}");
                    } else {
                        $("#ngayth_ct").val(field.NGAYTHUCHIEN);
                    }

                    /*if(field.GIOTHUCHIEN == null) {
                         showtime_gioth_ct_cancel = 0;
                         } else {
                         showtime_gioth_ct_cancel = 1;
                         $("#gioth_ct").val(field.GIOTHUCHIEN);
                         } */
                    $("#gioth_ct").data('da-thuc-hien', field.DA_CHAN_DOAN == 1);
                    if (field.GIOTHUCHIEN == null || field.DA_CHAN_DOAN != 1) {
                        if(tatAutoTime == 1) {
                            var ngayHienTai = new Date();
                            var gioHienTai = addZero(ngayHienTai.getHours());
                            var phutHienTai = addZero(ngayHienTai.getMinutes());
                            var giayHienTai = addZero(ngayHienTai.getSeconds());
                            $('#gioth_ct').val(gioHienTai + ":" + phutHienTai + ":" + giayHienTai);
                        } else
                            showtime_gioth_ct();
                    } else {
                        stopGioThCtTimer();
                        $("#gioth_ct").val(field.GIOTHUCHIEN);
                    }
                    // STG
                    if ($("#bacsichidinh").val() == "") {
                        var url_bs = "select_tenbacsi?mabacsi=" + $("#mabacsichidinh").val() + "&dvtt=" + "${Sess_DVTT}";
                        $.ajax({
                            url: url_bs
                        }).done(function (data) {
                            $("#bacsichidinh").val(data);
                        });
                    }
                    if ($("#bacsisieuam").val() == "") {
                        $("#bacsisieuam").val(nv_with_chucdanh);
                    }
                    if("${layicd_kdt}" != "1") {
                        if (field.CHANDOAN.toString() != "") {
                            $("#chandoan").val(field.CHANDOAN);
                        }
                    }
                    CKEDITOR.instances.ketqua.setData(field.KET_QUA);
                    CKEDITOR.instances.ketluan.setData(field.MO_TA);
                    //$("#ketqua").val(field.KET_QUA);
                    //$("#ketluan").val(field.MO_TA);
                    $("#loidanbacsi").val(field.LOIDANBACSI);

                    var ma_sieuam = $("#mausieuam").val();
                    if ("${hienthi_checkbox}" == "1") {
                        if (ma_sieuam !== null && ma_sieuam != 0) {
                            $("#checkbox_" + ma_sieuam + " :checkbox").removeAttr('checked');
                            var url = "lay_checkbox_dacheck?sophieu=" + sophieu + "&ma_cdha=" + ret.MA_CDHA + "&dvtt=" + "${Sess_DVTT}" + "&noitru=" + noitru + "&sovaovien=" + sovaovien + "&sovaovien_noi=" + sovaovien_noi + "&sovaovien_dt_noi=" + sovaovien_dt_noi;
                            $.ajax({
                                url: url
                            }).done(function (data) {
                                $.each(data, function (i) {
                                    $("#satq_" + data[i].MA_CHECKBOX).prop('checked', true);
                                });
                            });
                            $("#checkbox_" + ma_sieuam).css("display", "");
                            //$("#mausieuam").attr("disabled", "disabled");
                        }
                        else {
                            var dvtt = "${Sess_DVTT}";
                            var ma_cdha = $("#macdha").val();
                            var gioitinh = $("#gioitinh").val();
                            if (gioitinh == "true") {
                                gioitinh = 1;
                            }
                            else {
                                gioitinh = 0;
                            }
                            var url = "load_mausieuam_tuongung?ma_cdha=" + ma_cdha + "&gioitinh=" + gioitinh + "&dvtt=" + dvtt;
                            $.ajax({
                                url: url
                            }).done(function (data) {
                                $("#mausieuam").val(data);
                                $("#checkbox_" + data + " :checkbox").removeAttr('checked');
                                $("#checkbox_" + data).css("display", "");
                                var mausa = $("#mausieuam").val();
                                if (mausa != 0) {
                                    if (mausa == 3) {
                                        $("#form_sieuammau_doppler").click();
                                    }
                                    //$("#mausieuam").attr("disabled", "disabled");
                                }
                                var url = "select_mausieuam_theoma?ma=" + data + "&dvtt=${Sess_DVTT}";
                                $.ajax({
                                    url: url
                                }).done(function (data) {
                                    if (data != 0) {
                                        CKEDITOR.instances.ketqua.setData(data[0]["NOIDUNG"]);
                                        CKEDITOR.instances.ketluan.setData(data[0]["KET_LUAN"]);
                                    }
                                    //$('#ketluan').val(data[0]["KET_LUAN"]);
                                    //$('#ketqua').val(data);
                                });
                            });

                        }
                    }
                    if(field.DA_CHAN_DOAN==1) {
                        $("#thoiGianBatDau_cls").val(field.NGAY_TH_YL).change();
                        // changeInputTimerStatus("thoiGianBatDau_cls", true);
                    } else if (field.DA_CHAN_DOAN ==0) {
                        //changeInputTimerStatus("thoiGianBatDau_cls");
                        $("#thoiGianBatDau_cls").val(newStringDateTime());
                    }
                    $("#icdChanDoanCanLamSang").val(field.MA_ICD);
                    $("#tenChanDoanCanLamSang").val(field.TEN_ICD);
                    $("#maBenhLyChanDoanCanLamSang").val(field.MA_BENH_LY_THEO_ICD);
                });
            });
            var arr = [sophieu, "${Sess_DVTT}", ret.MA_CDHA, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
            var url1 = "sieuam_danhsach_hinhanh?url=" + convertArray(arr);
            $("#list_hinhanhnoisoi").jqGrid('setGridParam', {datatype: 'json', url: url1}).trigger('reloadGrid');
            $("#tab_cdha").tabs("option", "active", 1);
            $('img').attr('src', 'resources/webcam/camera_png.jpg');
        }

        function reload_grid_dsbn_cd() {
            var dscdParam = {
                P_DVTT: ${Sess_DVTT},
                P_TUNGAY: convertStr_MysqlDate($("#tungay1").val()),
                P_DENNGAY: convertStr_MysqlDate($("#denngay1").val()),
                P_DATH: $('#dathuchien1').prop('checked') == true ? 1 : 0,
                P_LOAI: 'SA',
                P_LOAI_TEXT: '-1'
            }
            var url = 'noisoi_ds_benhnhan_chidinh_bpc?' + $.param(dscdParam);
            $.getJSON(url, function (res) {
                $("#grid_dschidinh").jqGrid('clearGridData');
                var data = res.filter(function (i, n) {
                    return $('#dathuchien1').prop('checked') == false || ($('#theobacsi').prop('checked') == false || i.BACSI_THUCHIEN == $("${hotenbacsi}"));
                });

                $("#grid_dschidinh").jqGrid('setGridParam', {data: data}).trigger('reloadGrid');
            });


//                $("#grid_dschidinh").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
        }

        function reload_grid() {
            var ngay = convertStr_MysqlDate($("#ngaythuchien").val());
            var dvtt = "${Sess_DVTT}";
            var phong = "${Sess_Phong}";
            // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT
            var phongban = $("#phongban").val();
            var phongbenh = $("#phongbenh").val();
            var doituong = $("#doituong").val();
            // End ĐắkLắk
            var dathuchien = $("#dathuchien").prop('checked');
            if (dathuchien == true) {
                dath = 1;
                if ("${useris}" === "1") {
                    $('#viewimageweb').show();
                    $('#viewimageapp').show();
                } else {
                    $('#viewimageweb').hide();
                    $('#viewimageapp').hide();
                }
            } else {
                dath = 0;
                $('#viewimageweb').hide();
                $('#viewimageapp').hide();
            }
            //--Start VNPT Bình Định
            if ("${timtheophong_th}" == "1") {
                var maphongban = $("#phongban").val();
                var maphongcdha = $("#phongthuchiencdha").val();
                if("${timtheophong_th_theo_khoang_tg}" == "1"){
                    var tungay = convertStr_MysqlDate($("#tungay").val());
                    var denngay = convertStr_MysqlDate($("#ngaythuchien").val());
                    var arr = [dvtt, tungay, denngay, phong, dath, maphongban, maphongcdha];
                    var url = 'bdh_sieuam_ds_benhnhan_cothamso_v2?url=' + convertArray(arr);
                }else {
                    var arr = [dvtt, ngay, phong, dath, maphongban, maphongcdha];
                    var url = 'bdh_sieuam_ds_benhnhan_cothamso?url=' + convertArray(arr);
                }
                //--End VNPT Bình Định
            } else {
                //Đoạn code ban đầu
                var arr = [dvtt, ngay, phong, dath, $("#phongban").val(), $("#phongbenh").val(), $("#doituong").val(), $("#loaisieuam").val(), "0"];
                //------HPG -Tim kiem benh nhan CLS tu ngay den ngay
                if ("${timkiem_cls}" == "1") {
                    var tungay = convertStr_MysqlDate($("#tungay").val());
                    arr = [dvtt, tungay, ngay, phong, dath, $("#phongban").val(), $("#phongbenh").val(), $("#doituong").val(), $("#loaisieuam").val(), "0"];
                }
                //--End HPG
                var url = 'sieuam_ds_benhnhan_cothamso?url=' + convertArray(arr);
            }

            $("#list_benhnhan").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
        };

        function load_cttoathuoc(nghiepvu, list) {
            var url = 'noitru_load_chitiet_film?matt=' + matoathuoc + '&nghiepvu=' + nghiepvu + '&dvtt=${Sess_DVTT}&stt_benhan=' + stt_benhan +
                '&stt_dotdieutri=' + stt_dotdieutri + '&sovaovien=' + sovaovien_noi + '&sovaovien_dt=' + sovaovien_dt_noi +
                '&phongban=${Sess_PhongBan}' + '&ma_cdha=' + $("#macdha").val();
            +'&sophieu=' + $("#sophieu").val();
            ;
            $('#' + list).jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
        };

        function hienthi_them_cls(url) {
            $.ajax({
                url: url
            }).done(function (data) {
                if (data.length > 0) {
                    if ("${Sess_DVTT}".indexOf("96") != 0) {
                        $('#trieuchungls').val(data[0].TRIEUCHUNGLS);
                    }

                    if ("${Sess_DVTT}" == '96170') {
                        $('#trieuchungls').val(data[0].TRIEUCHUNGLS);
                    }

                    if("${Sess_DVTT}" !== '96170') {
                        $('#benhtheobs').val(data[0].TEN_BENH_THEOBS);
                    }
                    // STG
                    if (data[0].NGAY_THUC_HIEN == null) {
                        $("#ngaythuchien_cls").val("${ngayhientai}");
                    } else {
                        $("#ngaythuchien_cls").val(data[0].NGAY_THUC_HIEN);
                    }

                    /*if(data[0].GIO_TRA_KETQUA == null) {
                                showtime_giothuchien_cls_cancel = 0;
                            } else {
                                showtime_giothuchien_cls_cancel = 1;
                                $("#giothuchien_cls").val(data[0].GIO_TRA_KETQUA);
                            }*/
                    if (data[0].GIO_TRA_KETQUA == null || !$("#dathuchien").prop('checked')) {
                        if (!giothuchien_cls_timer_is_on) {
                            if(tatAutoTime == 1) {
                                var ngayHienTai = new Date();
                                var gioHienTai = addZero(ngayHienTai.getHours());
                                var phutHienTai = addZero(ngayHienTai.getMinutes());
                                var giayHienTai = addZero(ngayHienTai.getSeconds());
                                $('#giothuchien_cls').val(gioHienTai + ":" + phutHienTai + ":" + giayHienTai);
                            } else
                                showtime_giothuchien_cls();
                        }
                    } else {
                        stopGioThucHienClsTimer();
                        $("#giothuchien_cls").val(data[0].GIO_TRA_KETQUA);
                    }
                    // STG
                    if (("${cdhacanhbao}" == 1 && da_thanh_toan == 0) ||
                        ("${cdhacanhbao}" == 0 && data[0].TT_THANHTOAN == "0"))
                        $('#tt_thanhtoan').val("Chưa thanh toán");
                    else
                        $('#tt_thanhtoan').val("Đã thanh toán");
                } else {
                    $('#trieuchungls').val("");
                    $('#benhtheobs').val("");
                    $('#ngaythuchien_cls').val("");
                    $('#tt_thanhtoan').val("");
                }
            });
        }
        ;

        function load_lscdha_bn(mabenhnhan) {
            var url = "cmu_danhsach_lichsieuam?mabenhnhan=" + mabenhnhan;
            $("#list_lichsusieuam").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
        }

        //KGG them gọi số
        function mapUndefinedOrNullTo(value1, value2) {
            if (value1 === undefined || value1 === null) return value2;
            return value1;
        }
        function goisolayhinhanh(mgrid, cur_id) {
            var IDS = mgrid.jqGrid("getDataIDs");
            var count = 5 + parseInt(cur_id - 1);
            var chuoitong = "";
            for (var i = cur_id - 1; i < count; i++) {
                var id = IDS[i];
                var ret = mgrid.jqGrid('getRowData', id);
                if (ret.STT_HANGNGAY == undefined || ret.STT_HANGNGAY == null){
                    break;
                }
                var chuoi = "${Sess_TenPhong}" + "|" + ret.STT_HANGNGAY + "|" + ret.TENBENHNHAN + "|" + "0" + "|" + " " + "|" + "${Sess_DVTT}" + "|" + "" + "|" + " ";
                chuoitong = chuoitong + chuoi + "@";
            }
            return chuoitong;
        }

        function fetchListDSBN_SA(mgrid, cur_id, pkID, pkName) {
            var IDS = mgrid.jqGrid("getDataIDs");
            var count = IDS.length;
            // set default value
            var arr_call = [];
            for (var i = 0; i < count; i++) {
                var id = IDS[i];
                var ret = mgrid.jqGrid('getRowData', id);
                var bn_info = {
                    stt_bn_dv: ret.STT_HANGNGAY,
                    stt_bn_pb: ret.STT_HANGNGAY,
                    ma_bn: ret.MABENHNHAN,
                    ten_bn: ret.TENBENHNHAN,
                    uu_tien: mapUndefinedOrNullTo(ret.UU_TIEN, 'No') == 'Yes' ? 1 : 0,
                    sothebhyt: "BHYT",//ret.SO_THE_BHYT,
                    diachi: "Diachu",//ret.DIA_CHI,
                    ngaysinh: ret.NGAY_SINH.replace('-', '/').replace('-', '/')
                };
                arr_call.push(bn_info);
            }
            //
            var curstt = mgrid.jqGrid('getRowData', cur_id).STT_HANGNGAY;
            var info = {
                pk_id: pkID,
                pk_name: pkName,
                cur_id: curstt,
                dsbn: arr_call
            };
            //
            return JSON.stringify(info);
        }

        function saveTextAsFileL(textToWrite) {
            // grab the content of the form field and place it into a variable
            //  create a new Blob (html5 magic) that conatins the data from your form feild
            var textFileAsBlob = new Blob([textToWrite], {type: 'text/plain'});
            // Specify the name of the file to be saved
            var fileNameToSaveAs = "WEB_HIENTHISO_LIST.lgs";

            // Optionally allow the user to choose a file name by providing
            // an imput field in the HTML and using the collected data here
            // var fileNameToSaveAs = txtFileName.text;

            // create a link for our script to 'click'
            var downloadLink = document.createElement("a");
            //  supply the name of the file (from the var above).
            // you could create the name here but using a var
            // allows more flexability later.
            downloadLink.download = fileNameToSaveAs;
            // provide text for the link. This will be hidden so you
            // can actually use anything you want.
            downloadLink.innerHTML = "download";

            // allow our code to work in webkit & Gecko based browsers
            // without the need for a if / else block.
            window.URL = window.URL || window.webkitURL;

            // Create the link Object.
            downloadLink.href = window.URL.createObjectURL(textFileAsBlob);
            // when link is clicked call a function to remove it from
            // the DOM in case user wants to save a second file.
            downloadLink.onclick = m_destroyClickedElement;
            // make sure the link is hidden.
            downloadLink.style.display = "none";
            // add the link to the DOM
            document.body.appendChild(downloadLink);

            // click the new link
            downloadLink.click();
        }

        function m_destroyClickedElement(event) {
            // remove the link from the DOM
            document.body.removeChild(event.target);
        }

        //KGG End
        function doSignPlugin(url, typeSign) {
            var x = new XMLHttpRequest();
            x.onload = function() {
                // Create a form
                var reader = new FileReader();
                reader.readAsDataURL(x.response);
                reader.onloadend = function() {

                    var base64data = reader.result.replace("data:application/pdf;base64,", "");
                    console.log("base64data", base64data)
                    var sigOptions = null;
                    sigOptions = new PdfSigner();
                    sigOptions.AdvancedCustom = true;
                    SignAdvanced(base64data, 'pdf', sigOptions, typeSign);
                }

            };
            x.responseType = 'blob';    // <-- This is necessary!
            x.open('GET', url, true);
            x.send();
        }
        function kyketquasieuamtoken() {
            if ($("#hoten_ct").val().trim() != "") {
                var mabenhnhan = $("#mabenhnhan").val();
                var hoten = $("#hoten").val();
                var diachi = $("#diachi").val();
                var tuoi = $("#tuoi").val();
                var phai = $("#gioitinh").val();
                var sothebhyt = $("#sothebhyt").val();
                var namsinh = $("#namsinh").val();
                var tenkhoa = $("#tenkhoa").val();
                var phai = $("#gioitinh_ct").val();
                var sophieu = $("#sophieu").val();
                dvtt = "${Sess_DVTT}";
                Sess_UserID = "${Sess_UserID}";

                CMU_SOPHIEU_CDHA = sophieu;
                var makhambenh = $("#makhambenh").val();
                var macdha = $("#macdha").val();
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var bssieuam = $("#bacsisieuam").val() != null && $("#bacsisieuam").val() != '' ? $("#bacsisieuam").val() : " ";
                var typein = '2';
                if (sophieu != "" && macdha != "") {
                    var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha,
                        dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, sothebhyt, "0",
                        sovaovien,
                        sovaovien_noi,
                        sovaovien_dt_noi, 0, $("#trieuchungls").val(), bssieuam, maPhongBan ? maPhongBan : 0, namsinh, tenkhoa, 0, typein,
                        $("#mausieuam").val(), "0"]; // VNPTHIS-4697 23/11/2017 thêm
                    var selected = [];
                    $("#list_hinhanhnoisoi").jqGrid('getGridParam', 'selarrrow').forEach(function (data) {
                        var stt = $("#list_hinhanhnoisoi").jqGrid('getCell', data, 'STT');
                        selected.push(stt);
                    });
                    var url = "inketquasieuam_svv?thongtin=" + convertArray(arr) + "&anh=" + selected;
                    if (dvtt == 96019) {
                        arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha,
                            dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, sothebhyt, "0", maPhongBan ? maPhongBan : 0, typein];
                        url = "inketquasieuam_ngang?thongtin=" + convertArray(arr) + "&chanDoan=" + $("#trieuchungls").val();
                    }
                    doSignPlugin(url);
                }


            }
        }

        function SignAdvanced(data, type, sigOption, typeSign)
        {
            var dataJS = {};

            var arrData = [];
            // 1
            dataJS.data = data;
            dataJS.type = type;
            dataJS.sigOptions = JSON.stringify(sigOption);

            var jsData = "";
            jsData += JSON.stringify(dataJS);
            //
            arrData.push(jsData);
            var serial = "";
            vnpt_plugin.signArrDataAdvanced(arrData, serial, true, showMessageCDHAKQ);

        }

        function lammoithoigianthuchien() {
            var today = new Date();
            $("#gioth_ct").val(moment().format("HH:mm:ss"));
            $("#ngayth_ct").val(moment().format("DD/MM/YYYY"));
        }

    </script>


</head>
<body onload="gioThucHienClsTimerChange();">
<div id="panel_all">
    <%@include file="../../../resources/Theme/include_pages/menu.jsp" %>
    <%@include file="UploadfileSIEUAM.jsp" %>
    <jsp:include page="../camau/jsp/loader.jsp"/>
    <div id="panelwrap">
        <div class="center_content">
            <div id="tab_cdha">
                <ul>
                    <li><a href="#cdha_tabs_1" id="xn_cobhyt">Thông tin bệnh nhân</a></li>
                    <li><a href="#cdha_tabs_2" id="xn_bnyc">Kết quả</a></li>
                </ul>
                <div id="cdha_tabs_1">
                    <form id="form1" name="form1" method="post" action="">
                        <table width="100%">
                            <tr>
                                <td width="302" valign="top">
                                    <table width="302">
                                        <tr class="hpg_tmp">
                                            <td width="62px"><span style=" display:inline-block;">Từ Ngày </span></td>
                                            <td><input type="text" name="tungay" id="tungay"/></td>
                                        </tr>
                                        <tr>
                                            <td width="62px"><span style=" display:inline-block;"><span class="hpg_tmp">Đến </span>Ngày  </span>

                                            </td>
                                            <td>
                                                <input type="text" name="ngaythuchien" id="ngaythuchien"/>
                                                <input type="button" name="lammoi" id="lammoi" value="Làm mới"/>
                                            </td>
                                        </tr>
                                        <!-- ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT -->
                                        <tr class="dlk_tmp">
                                            <td>Khoa</td>
                                            <td><select name="phongban" id="phongban" class="width100">
                                                <c:forEach var="i" items="${phongban}">
                                                    <option value="${i.ma_phongban}">${i.ten_phongban}</option>
                                                </c:forEach>
                                            </select>
                                            </td>
                                        </tr>
                                        <tr class="dlk_tmp" id="dlk_phong">
                                            <td>Phòng</td>
                                            <td><select name="phongbenh" id="phongbenh" class="width100">
                                                <c:forEach var="i" items="${phongbenh}">
                                                    <option value="${i.ma_phong_benh}">${i.ten_phong_benh}</option>
                                                </c:forEach>
                                            </select></td>
                                        </tr>
                                        <!--Start VNPT Bình Định-->
                                        <tr id="tr_phong">
                                            <td><label name="lbl_phongthuchiencdha" id="lbl_phongthuchiencdha"
                                                       style="display: inline-block; width:30">Phòng TH</label></td>
                                            <td><select name="phongthuchiencdha" id="phongthuchiencdha"
                                                        style="width:230px;">
                                                <option value="-1" selected>-- Tất cả --</option>
                                                <c:forEach var="i" items="${phongthuchiencdha}">
                                                    <option value="${i.MA_PHONG_BENH}"> ${i.TEN_PHONG_BENH}</option>
                                                </c:forEach>
                                            </select></td>
                                        </tr>
                                        <!--END VNPT Bình Định-->
                                        <tr class="dlk_tmp">
                                            <td>Đối tượng</td>
                                            <td><select name="doituong" id="doituong" class="width100">
                                                <option value="-1">--Tất cả--</option>
                                                <option value="1">Có BHYT</option>
                                                <option value="0">Không BHYT</option>
                                            </select></td>
                                        </tr>
                                        <!-- End ĐắkLắk -->
                                        <tr ${tsdstheoloaisieuam == "1" ? "" : "hidden"}>
                                            <td>
                                                Loại SA
                                            </td>
                                            <td>
                                                <select name="loaisieuam" id="loaisieuam" class="width100">
                                                    <option value="-1">--Tất cả--</option>
                                                    <c:forEach var="e" items="${listLoaiSieuAm}">
                                                        <option value="${e.LOAISIEUAM}">${e.LOAISIEUAM}</option>
                                                    </c:forEach>
                                                </select>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="2"><label><input type="checkbox" name="dathuchien"
                                                                          id="dathuchien">
                                                <b>Đã thực hiện</b></label></td>
                                        </tr>
                                        <tr>
                                            <td colspan="2">
                                                <table id="list_benhnhan"></table>
                                            </td>
                                        </tr>
                                        <tr class="ghichutrangthai" style="display:none">
                                            <td colspan="2" style="padding-top:10px">
                                                <label style="color:red;font-weight: normal;">BN cấp cứu</label>
                                                <label style="color:#00ff00;margin-left:20px;font-weight: normal;">BN <
                                                    6 tuổi</label><br>
                                                <label style="color:#bf00ff;font-weight: normal;">Bệnh nhân VP, chưa
                                                    đóng tiền</label><br>
                                                <label style="color:#EE7600;font-weight: normal;">Bệnh nhân VP, đã đóng
                                                    tiền</label>
                                            </td>
                                        </tr>
                                        <tr class="mausacmacdinh" style="display:none">
                                            <td colspan="2" style="padding-top:10px">
                                                <label style="background:red;color:#fff">BN cấp cứu</label>
                                                <label style="background:#009900;color:#fff">Đã thanh toán</label>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                                <td width="676" valign="top">
                                    <div>
                                        <fieldset>
                                            <legend>Thông tin bệnh nhân</legend>
                                            <table width="100%">
                                                <tr>
                                                    <td width="127">Số vào viện</td>
                                                    <td width="756">
                                                        <input name="sovaovien" type="text" disabled="disabled"
                                                               class="width3" id="sovaovien"/>
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td width="127">Họ tên
                                                        <input name="macdha" type="hidden" id="macdha"/>
                                                        <input name="sophieu" type="hidden" id="sophieu"/>
                                                        <input name="makhambenh" type="hidden" id="makhambenh"/>
                                                        <input name="noitru" type="hidden" id="noitru"/></td>
                                                    <td width="756"><input name="hoten" type="text" disabled="disabled"
                                                                           class="width1" id="hoten"
                                                                           style="color: red; font-weight: bold"/>
                                                        Tuổi
                                                        <input name="tuoi" type="text" disabled="disabled" id="tuoi"
                                                               style="width: 50px"/>
                                                        <select name="gioitinh" id="gioitinh" disabled="disabled">
                                                            <option value="true">Nam</option>
                                                            <option value="false">Nữ</option>
                                                        </select>
                                                        Mã y tế
                                                        <input name="mabenhnhan" type="text" disabled="disabled"
                                                               id="mabenhnhan" style="width: 100px"/>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Khoa</td>
                                                    <td><input name="tenkhoa" type="text" disabled="disabled"
                                                               class="width100" id="tenkhoa"/></td>
                                                </tr>
                                                <tr>
                                                    <td>Địa chỉ</td>
                                                    <td><input name="diachi" type="text" disabled="disabled"
                                                               class="width100" id="diachi"/></td>
                                                </tr>
                                                <tr>
                                                    <td>Số thẻ BHYT</td>
                                                    <td><input name="sothebhyt" type="text" disabled="disabled"
                                                               class="width100" id="sothebhyt"/></td>
                                                </tr>
                                                <tr class="block_bscd">
                                                    <td>BS chỉ định
                                                        <input name="mabacsichidinh" type="hidden" id="mabacsichidinh"/>
                                                    </td>
                                                    <td><input name="bacsichidinh" type="text" class="width100"
                                                               id="bacsichidinh"/></td>
                                                </tr>
                                                <tr>
                                                    <td>BS Siêu âm</td>
                                                    <td><c:choose>
                                                        <c:when test="${Sess_DVTT=='96011'}">
                                                            <select name="khoabs_cmu" class="width_100per" id="khoabs_cmu"></select>
                                                            <select name="cmu_cbbacsidieutri" class="width_100per" id="cmu_cbbacsidieutri"></select>
                                                            <input name="bacsisieuam" type="text" class="width100"
                                                                   id="bacsisieuam" style="display:none"/>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <input name="bacsisieuam" type="text" class="width100"
                                                                   id="bacsisieuam"/>
                                                        </c:otherwise>
                                                    </c:choose>	 </td>
                                                </tr>
                                                <tr>
                                                    <td>Chọn máy</td>
                                                    <td>
                                                        <select name="ma_maycdha_md" id="ma_maycdha_md"
                                                                class="width100">
                                                            <option value="" selected>Chọn máy CDHA-TDCN mặc định
                                                            </option>
                                                            <c:forEach var="e" items="${dsmaycdha}">
                                                                <option value="${e.STT}">${e.TEN_MAY}</option>
                                                            </c:forEach>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr class="hpg_hienthithem">
                                                    <td>Triệu chứng</td>
                                                    <td>
                                                        <input name="trieuchungls" type="text" disabled="disabled"
                                                               class="width100" id="trieuchungls"/>
                                                    </td>
                                                </tr>
                                                <tr class="hpg_hienthithem">
                                                    <td>Bệnh tật</td>
                                                    <td>
                                                        <input name="benhtheobs" type="text" disabled="disabled"
                                                               class="width100" id="benhtheobs"/>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Ngày chỉ định</td>
                                                    <td>
                                                        <input name="ngaychidinh_cls" type="text" disabled="disabled" id="ngaychidinh_cls" size="10"
                                                               data-inputmask="'alias': 'date'"/>
                                                        <input name="giochidinh_cls" type="text" disabled="disabled" id="giochidinh_cls" size="10"
                                                               data-inputmask="'alias': 'hh:mm:ss'"/>
                                                    </td>
                                                </tr>
                                                <tr class="hpg_hienthithem">
                                                    <td>Ngày thực hiện</td>
                                                    <td>
                                                        <input name="ngaythuchien_cls" type="text" id="ngaythuchien_cls"
                                                               size="10" data-inputmask="'alias': 'date'"/>
                                                        <input name="giothuchien_cls" type="text" id="giothuchien_cls"
                                                               size="10" data-inputmask="'alias': 'hh:mm:ss'"/>
                                                    </td>
                                                </tr>
                                                <tr class="hpg_hienthithem">
                                                    <td>
                                                        Thanh toán
                                                    </td>
                                                    <td>
                                                        <input name="tt_thanhtoan" type="text" disabled="disabled"
                                                               class="width100" id="tt_thanhtoan"/>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2">
                                                        <p id="vnptmoney" style="color:#21409A;;font-weight: bold;"></p>
                                                        <p id="bidv" style="color:#219a5f;;font-weight: bold;margin-top: 10px"></p>
                                                        <p id="vietinbank" style="color:#216b9a;;font-weight: bold;margin-top: 10px"></p>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2">
                                                        <!--<input type="button" name="luufile" id="luufile" value="Chọn file" class="button_shadow"/>-->
                                                        <input type="button" name="luuthongtin" id="luuthongtin"
                                                               value="Lưu thông tin" class="button_shadow"
                                                               style="width: 110px"/>
                                                        <input type="button" name="inphieu" id="inphieu"
                                                               value="In phiếu" class="button_shadow"/>
                                                        <!-- <input type="button" name="inphieu" id="inphieu" value="In phiếu" class="button_shadow"/>-->
                                                        <!-- ===== DLK =========== -->
                                                        <input name="inphieu_sieuam_ngang1" type="button"
                                                               class="button_shadow" id="inphieu_sieuam_ngang1"
                                                               style="width: 130" value="In phiếu (ngang)"/>
                                                        <!-- ======= END DLK====== -->
                                                        <input name="phieu_cdha_sieuam" type="button" class="button_shadow"
                                                               id="phieu_cdha_sieuam" style="width: 100" value="In phiếu CĐ"/>
                                                        <!--Dũng CMU-->
                                                        <input name="lichsusieuam" type="button" class="button_shadow"
                                                               id="lichsusieuam" style="width: 100" value="Lịch sử SA"/>
                                                        <input type="button" name="luu_tt_maycdha" id="luu_tt_maycdha"
                                                               value="Lưu máy thực hiện" style="width: 140px;"
                                                               class="button_shadow"/>
                                                        <input name="sttbenhan" type="hidden" id="sttbenhan"/>
                                                        <input name="sttdotdieutri" type="hidden" id="sttdotdieutri"/>
                                                        <input name="nguoithuchien" type="hidden" id="nguoithuchien"/>
                                                        <input name="sttdieutri" type="hidden" id="sttdieutri"/></td>
                                                    <input name="namsinh" type="hidden" id="namsinh"/>
                                                </tr>
                                            </table>

                                        </fieldset>
                                    </div>
                                    <div style="padding-top: 5px">
                                        <table id="list_sieuam_bhyt"></table>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </form>
                    <form id="formdsbn" name="formdsbn" action="">
                        <table width="100%">
                            <tr>
                                <td width="62px"> Từ ngày</td>
                                <td><input type="text" name="tungay1" id="tungay1"/></td>
                                <td width="62px">Đến ngày</td>
                                <td><input type="text" name="denngay1" id="denngay1"/></td>
                                <td colspan="2"><label><input type="checkbox" name="dathuchien1" id="dathuchien1">
                                    <b>Đã thực hiện</b></label></td>
                                <td colspan="2"><label><input type="checkbox" name="theobacsi" id="theobacsi" checked>
                                    <b>Theo bác sĩ</b></label></td>
                                <td width="65px"> Tổng số</td>
                                <td width="65px"><input type="text" disabled name="tongsoca" id="tongsoca"/></td>
                                <td>
                                    <input type="button" name="lammoi1" id="lammoi1" value="Làm mới"/>
                                </td>
                            </tr>
                            <tr>
                                <tableid
                                ="grid_dschidinh">
                        </table>
                        </tr>
                        </table>
                    </form>
                </div>
                <div id="cdha_tabs_2">

                    <form id="form2" name="form2" method="post" action="">
                        <div>
                            <fieldset>
                                <legend>Chụp và lưu hình ảnh</legend>
                                <table width="100%">
                                    <tr>
                                        <td valign="top">
                                            <div id="say-cheese-container" title="Chụp % Lưu"
                                                 style="cursor: pointer;border:solid 1px #666666; width:320px; height:240px"></div>
                                        </td>
                                        <td rowspan="2" valign="top">
                                            <div id="say-cheese-snapshots"><img
                                                    src="<c:url value="/resources/webcam/camera_png.jpg" />"
                                                    width="230px" height="230px"/></div>
                                        </td>
                                        <td rowspan="2" valign="top">
                                            <div id="div_hinhanhnoisoi">
                                                <table id="list_hinhanhnoisoi"></table>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td valign="top"><input type="button" name="take-snapshot" id="take-snapshot"
                                                                value="Chụp " class="button_shadow"/>
                                            <input type="button" name="take-save-snapshot" id="take-save-snapshot"
                                                   value="Chụp &Lưu " class="button_shadow"/>
                                            <input type="button" name="save-snapshot" id="save-snapshot" value="Lưu"
                                                   class="button_shadow"/>
                                            <!-- STG 18/4/2017 thêm nút duyệt ảnh từ máy tính -->
                                            <label class="button_shadow" style="height: 26px;"> Duyệt ảnh <input
                                                    type="file" name="browse-file" id="browse-file"
                                                    style="display: none;"></label>
                                            <!-- STG 18/4/2017 thêm nút duyệt ảnh từ máy tính --><c:if
                                                    test="${ekipBPC == 1}"><input type="button" name="ekip_sieuam"
                                                                                  id="ekip_sieuam" value="EKIP"
                                                                                  class="button_shadow"
                                                                                  onclick="openEkip()"/></c:if>
                                            <input type="button" name="lssieuam" id="lssieuam" value="LS Siêu Âm"
                                                   class="button_shadow"/>
                                            <input type="button" name="cmuekip" onclick="opencmuekipt()" id="cmuekip" value="EKIP SA"
                                                   class="button_shadow"/>
                                        </td>
                                    </tr>
                                </table>
                            </fieldset>
                        </div>
                        <!--Đắk lắk view thông tin hành chành của bệnh nhân ở form nhập kết quả -->
                        <div>
                            <fieldset>
                                <legend>Thông tin hành chánh của bệnh nhân</legend>
                                <table width="100%">
                                    <tr>
                                        <td width="10%">Họ tên</td>
                                        <td width="40%"><input name="hoten_ct" type="text" disabled="disabled"
                                                               id="hoten_ct"
                                                               style="width: 320px; color: red; font-weight: bold"/>
                                        </td>
                                        <td>Tuổi
                                            <input name="tuoi_ct" type="text" disabled="disabled" id="tuoi_ct"
                                                   style="width: 80px"/>
                                            Giới tính
                                            <input name="gioitinh_ct" type="text" disabled="disabled" id="gioitinh_ct"
                                                   style="width: 70px"/>
                                            Mã y tế
                                            <input name="mabenhnhan_ct" type="text" disabled="disabled"
                                                   id="mabenhnhan_ct" style="width: 170px"/>
                                        </td>
                                    <tr>
                                        <td>Khoa</td>
                                        <td><input name="tenkhoa_ct" type="text" disabled="disabled"
                                                   style="width: 320px" id="tenkhoa_ct"/></td>
                                        <td>Số thẻ BHYT
                                            <input name="sothebhyt_ct" type="text" disabled="disabled"
                                                   style="width: 379px" id="sothebhyt_ct"/></td>
                                    </tr>
                                    <tr>
                                        <td>Thời gian chỉ định:</td>
                                        <td ><input name="ngaychidinh_kq" type="text" disabled="disabled" id="ngaychidinh_kq" size="10"
                                                    data-inputmask="'alias': 'date'"/>
                                            <input name="giochidinh_kq" type="text" disabled="disabled" id="giochidinh_kq" size="10"
                                                   data-inputmask="'alias': 'hh:mm:ss'"/></td>
                                        <td class="bscd_chitiet">
                                            Bác sỉ chỉ định:
                                            <input name="bacsichidinh_chitiet" type="text" id="bacsichidinh_chitiet" style="width:300px"/></td>
                                        </td>
                                    </tr>
                                </table>
                            </fieldset>
                        </div>
                        <!-- End Đắk Lắk -->
                        <div>
                            <fieldset>
                                <legend>Kết quả siêu âm</legend>
                                <input type="button" name="form_sieuammau_doppler" id="form_sieuammau_doppler"
                                       value="SA Màu/Doppler" class="button_shadow"
                                       style="width: 135px;margin-top: 10px; margin-bottom: 10px; display:none;"/>
                                <table width="100%">
                                    <tr>
                                        <td><label for="thoiGianBatDau_cls">Thời gian thực hiện y lệnh:</label></td>
                                        <td>
                                            <input type="text" id="thoiGianBatDau_cls"
                                            <%--                                               data-inputmask="'alias': 'datetime'"--%>
                                                   width="auto"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="11%">Thời gian kết quả:</td>
                                        <!--    <td width="89%"><input name="chandoan" type="text" class="width100" id="chandoan" /></td> -->
                                        <td>
                                            <input name="ngayth_ct" type="text" id="ngayth_ct" size="10"
                                                   data-inputmask="'alias': 'date'"/>
                                            <!--            //VLG chinh textbox gio tra kq chay thoi gian-->
                                            <input name="gioth_ct" type="text" id="gioth_ct" size="10"
                                                   data-inputmask="'alias': 'hh:mm:ss'"/>
                                            <input name="lammoi_thoigian_th" type="button" class="button_shadow" id="lammoi_thoigian_th" onclick="lammoithoigianthuchien()" value="Làm mới">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="11%">Chẩn đoán</td>
                                        <td width="89%"><input name="chandoan" type="text" class="width100"
                                                               id="chandoan"/></td>
                                    </tr>
                                    <tr>
                                        <td>Chẩn đoán CLS</td>
                                        <td>
                                            <input name="maBenhLyChanDoanCanLamSang" type="hidden" id="maBenhLyChanDoanCanLamSang"/>
                                            <input name="icdChanDoanCanLamSang" type="text" class="icd" id="icdChanDoanCanLamSang" style="width: 50px"/>
                                            <input name="btnChanDoanCanLamSang" type="button" id="btnChanDoanCanLamSang" class="icd" value="..."/>
                                            <label>
                                                <input id="tenChanDoanCanLamSang" name="tenChanDoanCanLamSang" class="icd" style="width:342px"/>
                                            </label>
                                        </td>
                                    </tr>
                                    <tr class = "icdTruocSau">
                                        <td>Chẩn đoán trước cdha<span style="color: red">(*):</span></td>
                                        <input name="mabenhly_truoccdha" type="hidden" id="mabenhly_truoccdha" />
                                        <td>
                                            <input name="icd_truoccdha" type="text"  style="width:10%"  id="icd_truoccdha" />
                                            <input name="chandoan_truoccdha" type="text" style="width:89%"  id="chandoan_truoccdha" />
                                        </td>
                                    </tr>
                                    <tr class = "icdTruocSau">
                                        <td>Chẩn đoán sau cdha<span style="color: red">(*):</span></td>
                                        <input name="mabenhly_saucdha" type="hidden" id="mabenhly_saucdha"/>
                                        <td>
                                            <input name="icd_saucdha" type="text"  style="width:10%"  id="icd_saucdha" />
                                            <input name="chandoan_saucdha" type="text" style="width:89%"  id="chandoan_saucdha" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Người đọc kết quả</td>
                                        <td>
                                            <select id="cboNguoiDocKetQua">
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Mẫu siêu âm</td>
                                        <td><select name="mausieuam" class="width100 combobox_jqx" id="mausieuam" data-show-subtext="true" data-live-search="true">
                                            <option value="-1" selected>-- Chọn mẫu siêu âm --</option>
                                            <c:forEach var="i" items="${mausieuam}">
                                                <option value="${i.MA_MAUSIEUAM}"> ${i.TEN_MAUSIEUAM}</option>
                                            </c:forEach>
                                        </select></td>
                                    </tr>
                                    <c:forEach var="i" items="${formsieuam}">
                                        <tr id="checkbox_${i.MA_MAUSIEUAM}" style="display:none;">
                                            <td colspan="2" style="text-align: left" id="checks_${i.MA_MAUSIEUAM}">
                                                    <%--$("#tab_sieuam_"+1).tabs();--%>
                                                <div id="tab_sieuam_${i.MA_MAUSIEUAM}">
                                                    <ul>
                                                        <c:forEach var="a" items="${satq}">
                                                            <c:if test="${a.MOTA_LOAICHECKBOX == i.MOTA && (a.GIOITINH == i.GIOITINH or a.GIOITINH == '-1')}">
                                                                <li><a href="#tab_${a.MA_LOAI_CHECKBOX}"
                                                                       id="tq_${a.MA_LOAI_CHECKBOX}">${a.TEN_LOAI_CHECKBOX}</a>
                                                                </li>
                                                            </c:if>
                                                        </c:forEach>
                                                    </ul>
                                                    <c:forEach var="b" items="${satq}">
                                                        <c:if test="${b.MOTA_LOAICHECKBOX == i.MOTA && (b.GIOITINH == i.GIOITINH or b.GIOITINH == '-1')}">
                                                            <div id="tab_${b.MA_LOAI_CHECKBOX}">
                                                                <c:forEach var="c" items="${checkbox_sa}">
                                                                    <c:if test="${c.LOAI_CHECKBOX == b.MA_LOAI_CHECKBOX}">
                                                                        <input type="checkbox" value="${c.MA_CHECKBOX}"
                                                                               style="margin-left: 20px"
                                                                               id="satq_${c.MA_CHECKBOX}"/> ${c.TEN_CHECKBOX}
                                                                    </c:if>
                                                                </c:forEach>
                                                            </div>
                                                        </c:if>
                                                    </c:forEach>
                                                </div>
                                            </td>
                                        </tr>
                                    </c:forEach>

                                    <tr>
                                        <td>Mô tả</td>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td colspan="2"><textarea name="ketqua" cols="80" rows="10" class="width100"
                                                                  id="ketqua"></textarea>
                                            <script type="text/javascript" language="javascript">
                                                CKEDITOR.replace('ketqua');
                                            </script>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Kết luận</td>
                                        <td>
                                            <textarea name="ketluan" rows="2" class="width100" id="ketluan"></textarea>
                                            <script type="text/javascript" language="javascript">
                                                CKEDITOR.replace('ketluan', {height: '100px'});
                                            </script>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Lời dặn bác sĩ</td>
                                        <td><input name="loidanbacsi" type="text" class="width100" id="loidanbacsi"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td><input name="luu_tt" type="button" class="button_shadow" id="luu_tt"
                                                   value="Lưu"/>
                                            <input name="inphieu_sieuam" type="button" class="button_shadow"
                                                   id="inphieu_sieuam" value="In phiếu"/>
                                            <!-- ===== Begin KGG Trạng Ngày 07/12/2016 =========== -->
                                            <input name="inphieu_sieuam_ngang" type="button" class="button_shadow"
                                                   id="inphieu_sieuam_ngang" style="width: 150"
                                                   value="In phiếu (ngang)"/>
                                            <c:if test="${thamso_luuvain != 0}">
                                                <input name="luuvain" type="button" class="button_shadow"
                                                       id="luuvain"
                                                       value="Lưu & In"/>
                                                <input name="luuvainphieungang" type="button" class="button_shadow"
                                                       id="luuvainphieungang" style="width: 150"
                                                       value="Lưu & In (ngang)"/>
                                            </c:if>
                                            <input name="intheomau" type="button" class="button_shadow"
                                                   id="intheomau" value="In theo mẫu"/>
                                            <c:if test='<%= ThamSoManager.instance(session).getThamSoString("960554","0").equals("1")%>'>
                                                <input name="inketquavahinhanh" type="button" class="button_shadow" id="inketquavahinhanh" value="In tất cả ảnh" style="width: 110px;"/>
                                            </c:if>

                                            <!-- ======= END KGG Trạng Ngày 07/12/2016====== -->
                                            <!--VNPTHIS-4697 thêm in pdf -->
                                            <select name="loaiin" id="loaiin">
                                                <option value="1">RTF</option>
                                                <option value="2">PDF</option>
                                            </select>
                                            <input style="width: 150px" type="button" id="cmu_kyso" class="button_shadow" value="Ký Số SMARTCA">
                                            <input style="width: 150px" type="button" id="cmu_inkyso" class="button_shadow" value="In Ký Số SMARTCA">
                                            <input style="width: 150px" type="button" id="cmu_huykyso" class="button_shadow" value="Hủy ký Số SMARTCA">
                                            <input type="button" name="viewimageweb" id="viewimageweb"
                                                   value="Xem ảnh Web" class="button_shadow" style="width: auto"/>
                                            <input type="button" name="viewimageapp" id="viewimageapp"
                                                   value="Xem ảnh App " class="button_shadow" style="width: auto"/>

                                            <!--VNPTHIS-4697 thêm in pdf -->
                                        </td>

                                        <%--<textarea name="temp" id="temp" style="display:none;"></textarea>--%>
                                        <%--<script type="text/javascript" language="javascript">--%>
                                        <%--CKEDITOR.replace('temp');--%>
                                        <%--</script>--%>
                                    </tr>
                                    <tr hidden="hidden">
                                        <td colspan="2">
                                            <div>
                                                <table width="300">
                                                    <tr>
                                                        <td width="70">Chọn kho:
                                                            <input name="matoathuoc_dv" type="hidden"
                                                                   id="matoathuoc_dv"/></td>
                                                        <td>
                                                            <select name="kho_dv" id="kho_dv" style="width:220px">
                                                                <c:forEach var="i" items="${khodichvu}">
                                                                    <option value="${i.MAKHO}">${i.TENKHO}</option>
                                                                </c:forEach>
                                                            </select>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                            <div style="width:300px">
                                                <div class="table_beauty">
                                                    <c:if test="${AnNhapThuocToaDichVu == '0'}">
                                                        <table width="300">
                                                            <thead>
                                                            <tr>
                                                                <th width="132">
                                                                    Tên thuốc, vật tư
                                                                </th>
                                                                <th width="51">ĐVT</th>
                                                                <th width="72"><input name="dongia_dv" type="hidden"
                                                                                      id="dongia_dv"/>
                                                                    Số lượng
                                                                </th>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                            <tr id="thuocdichvu_tt_div">
                                                                <td><input name="makhovattu_dv" type="hidden"
                                                                           id="makhovattu_dv"/><input name="mavattu_dv"
                                                                                                      type="hidden"
                                                                                                      id="mavattu_dv"/>
                                                                    <input name="sott_toathuoc_dv" type="hidden"
                                                                           id="sott_toathuoc_dv"/>
                                                                    <input name="tenthuongmai_dv" type="text"
                                                                           id="tenthuongmai_dv" size="13"
                                                                           class="width_100per"/></td>
                                                                <td><input name="dvt_dv" type="text" id="dvt_dv"
                                                                           size="4" readonly class="width_100per"/></td>
                                                                <td><input name="soluong_dv" type="text" id="soluong_dv"
                                                                           size="12" onKeyPress="validate_number(event)"
                                                                           class="width_100per"/></td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </c:if>
                                                </div>
                                                <div id="thuocdichvu_div" style="width:300px">
                                                    <table id="list_thuocdichvu" width="300"></table>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                                <div style="display: none">
                                    <textarea name='temp' id='temp' cols="1" rows="1"></textarea>
                                    <script type="text/javascript" language="javascript">
                                        CKEDITOR.replace('temp');
                                    </script>
                                </div>

                            </fieldset>
                        </div>
                    </form>
                </div>
            </div>
        </div> <!--end of center_content-->
        <%@include file="../../../resources/Theme/include_pages/footer.jsp"%>
    </div>
</div>
<!-- Dũng CMU -->
<div id="dialog_lichsusieuam" title="Lịch sử siêu âm" style="display: none">
    <div id="tab_ls_cdha">
        <ul>
            <li><a href="#cdha_ls_tabs_1" id="xn_cobhyt">Thông tin bệnh nhân</a></li>
            <li style="display:none"><a href="#cdha_ls_tabs_2" id="xn_bnyc">Kết quả</a></li>
        </ul>
        <div id="cdha_ls_tabs_1">
            <table id="list_lichsusieuam"></table>
        </div>
        <div id="cdha_ls_tabs_2" style="display:none">
            <!--<form id="form2" name="form2" method="post" action="">
                        <table width="900" align="center">
                            <tr valign="top">
                                <td width="47%" align="right"><div id="say-cheese-container" style="border:solid 1px #666666; width:320px; height:240px">
                                    </div></td>
                                <td width="53%"><div id="say-cheese-snapshots"><img src="<c:url value="/resources/webcam/camera_png.jpg" />" width="230px" height="230px"/></div></td>
                                <td width="53%" rowspan="2"><table id="list_hinhanhnoisoi"></table></td>
                            </tr>
                            <tr>
                                <td align="center"><input type="button" name="take-snapshot" id="take-snapshot" value="Chụp ảnh" class="button_shadow"/>
                                    <input type="button" name="save-snapshot" id="save-snapshot" value="Lưu ảnh" class="button_shadow"/></td>
                                <td>&nbsp;</td>
                            </tr>
                        </table>

                    </form>-->
            <form id="form2" name="form2" method="post" action="">
                <div>
                    <fieldset>
                        <legend>Kết quả siêu âm</legend>
                        <table width="100%">
                            <tr>
                                <td width="11%">Chẩn đoán</td>
                                <td width="89%"><input name="ls_chandoan" type="text" class="width100"
                                                       id="ls_chandoan"/></td>
                            </tr>
                            <tr>
                                <td>Kết quả</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                            <textarea name="ls_ketqua" cols="80" rows="10" class="width100"
                                                      id="ls_ketqua">

                                            </textarea>
                                </td>
                            </tr>
                            <tr>
                                <td>Kết luận</td>
                                <td>
                                    <textarea name="ls_ketluan" rows="2" class="width100" id="ketluan"></textarea>
                                </td>
                            </tr>
                            <tr>
                                <td>Lời dặn bác sĩ</td>
                                <td><input name="ls_loidanbacsi" type="text" class="width100" id="loidanbacsi"/></td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>
                                    <input name="ls_inphieu_sieuam" type="button" class="button_shadow"
                                           id="ls_inphieu_sieuam" value="In phiếu"/>
                                    <input name="ls_inphieu_sieuam_ngang" type="button" class="button_shadow"
                                           id="ls_inphieu_sieuam_ngang" style="width: 150px" value="In phiếu (ngang)"/>
                                </td>
                            </tr>
                        </table>

                    </fieldset>
                </div>
            </form>
        </div>

    </div>
    <%@include file="SA_Tim_mau_doppler.jsp" %>
</div>
<div id="dialog_capnhatketquasieuam_ekip" title="Ekip Siêu Âm">
    <form id="form_Ekip" name="form_Ekip" method="post" action="">
        <input id="frm_kq_id_ekip" class="post-data" key="ID_EKIP" hidden/>
        <input id="frm_kq_so_phieu_dichvu" class="post-data" key="SO_PHIEU_DICHVU" hidden/>
        <input id="frm_kq_ma_dv" class="post-data" key="MA_DV" hidden/>
        <input id="frm_kq_mabenhnhan" class="post-data" key="MABENHNHAN" hidden/>
        <input id="frm_kq_sovaovien" class="post-data" key="SOVAOVIEN" hidden/>
        <input id="frm_kq_sovaovien_dt" class="post-data" key="SOVAOVIEN_DT" hidden/>
        <input id="frm_kq_noitru" class="post-data" key="NOITRU" hidden/>
        <input id="frm_kq_id_dieutri" class="post-data" key="ID_DIEUTRI" hidden/>

        <table style="width:650px">

            <tr>
                <td width="150">Mã nhân viên<input name="ma_nhanvien" type="text" id="ma_nhanvien" class="post-data"
                                                   key="MA_NHANVIEN_EKIP"
                                                   style="color: blue; font-weight:  bold; width: 100px"/>
                <td width="200">Tên nhân viên<input name="ten_nhanvien" type="text" id="ten_nhanvien"
                                                    style="color: blue; font-weight:  bold"/></td>
                <td width="200">Vai trò<input name="ma_vaitro" type="hidden" id="ma_vaitro" class="post-data"
                                              key="MA_VAITRO_EKIP" style="color: blue; font-weight:  bold"/>
                    <input name="ten_vaitro" type="text" id="ten_vaitro" style="color: blue; font-weight:  bold"/></td>
            </tr>
            <tr>
                <table id="list_chitietvaitrothuchien" style="font-size: 12px"></table>
            </tr>
            <tr>

                <td align="right">
                    <%--<input type="button" name="ekip_CapNhat" id="ekip_CapNhat" value="Cập nhật" onclick="capNhatEkip(1)" class="button_shadow">--%>
                    <input type="button" name="ekip_Xoa" id="ekip_Xoa" value="Xoá Ekip" onclick="capNhatEkip(0)"
                           class="button_shadow">
                </td>
            </tr>
        </table>
    </form>
</div>
<div id="ekip" style="display: none">
    <form id="tiencong" name="tiencong" method="post" action="">
        <table width="600" border="0">
            <tr>
                <td colspan="2">
                    <table width="600" border="0">
                        <tr>
                            <td width="100" align="right" style="font-size: 12px">Phòng ban</td>
                            <td><select name="khoaphong_ekip" id="khoaphong_ekip" style="width: 200px">
                                <c:forEach var="i" items="${phongban}">
                                    <option value="${i.MA_PHONGBAN}">${i.TEN_PHONGBAN}</option>
                                </c:forEach>
                            </select>
                            </td>
                            <td width="100" align="right" style="font-size: 12px">Thực hiện</td>
                            <td><select name="nguoithuchien_ekip" id="nguoithuchien_ekip" style="width: 200px">
                                <option value="-1">-- Chọn nhân viên --</option>
                            </select>
                            </td>
                            <td><input name="manhanvien" type="hidden" id="manhanvien"/></td>
                        </tr>
                        <tr>
                            <td width="100" align="right" style="font-size: 12px">Ekip mẫu</td>
                            <td><select name="ekip_mau" id="ekip_mau" style="width: 200px">
                                <option value="-1">-- Chọn ekip --</option>
                            </select>
                            </td>
                            <td width="70" align="right" style="font-size: 12px">Vai trò</td>
                            <td><select name="tiencongthuchien" id="tiencongthuchien" style="width: 200px">
                                <option value="-1">-- Chọn vai trò --</option>
                            </select>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="4" align="center">
                                <input type="button" class="button_shadow" name="themnguoithuchien"
                                       id="themnguoithuchien" value="Thêm"/>
                                <input type="button" class="button_shadow" name="xoanguoithuchien"
                                       id="xoanguoithuchien" value="Xóa"/>
                                <input type="button" class="button_shadow" name="xoanguoithuchien_all"
                                       id="xoanguoithuchien_all" value="Xóa tất cả"/></td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <table id="list_chitiettiencongthuchien" style="font-size: 12px"></table>
            </tr>
        </table>
    </form>
</div>
<jsp:include page="../Canlamsang/dialog/diaglog-tim-kiem-icd.jsp"/>
<%@include file="../camau/canlamsang/cmuekipsieuam.jsp" %>
</body>
</html>
