CREATE OR REPLACE FUNCTION "CMU_UPDATE_NGAYTH_YLENH_XML3" (
    p_dvtt        VARCHAR2,
    p_thoigian    VARCHAR2,
    p_sovaovien   VARCHAR2,
    p_sophieu     VARCHAR2,
    p_madv        VARCHAR2,
    p_hinhthuc    VARCHAR2,
    p_loaidv      VARCHAR2,
    p_user        VARCHAR2
) RETURN VARCHAR2 IS

    v_minngay       DATE;
    v_olduser       VARCHAR2(255);
    p_thaotac       VARCHAR2(255);
    cur             SYS_REFCURSOR;
    v_bant          NUMBER(18) := 0;
    v_maben<PERSON>han    NUMBER;
    v_ngaycu        VARCHAR2(255);
    v_tendichvu     VARCHAR2(1000);
    v_tenbnehnhan   VARCHAR2(255);
    p_manhanvien    NUMBER;
BEGIN
    p_thaotac := 'THAYDOINGAYKQ_' || p_loaidv;
    IF p_hinhthuc = 1 THEN
        IF p_loaidv = 'CDHA' THEN
SELECT
    ten_cdha
INTO v_tendichvu
FROM
    cls_cdha
WHERE
    dvtt = p_dvtt
  AND ma_cdha = p_madv;

SELECT
    TO_CHAR(ngay_th_yl, 'DD/MM/YYYY HH24:MI'),
    mabenhnhan
INTO
    v_ngaycu,
    v_mabenhnhan
FROM
    kb_cd_cdha_ct
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_madv;

UPDATE kb_cd_cdha_ct
SET
    ngay_th_yl = to_timestamp_tz(p_thoigian, 'dd/mm/yyyy hh24:mi:ss')
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_madv;

UPDATE kb_cd_cdha_ct
SET
    ngay_th_yl = to_timestamp_tz(p_thoigian, 'dd/mm/yyyy hh24:mi:ss')
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_madv;

ELSIF p_loaidv = 'TTPT' THEN
SELECT
    ten_dv
INTO v_tendichvu
FROM
    dm_dich_vu_kham
WHERE
    dvtt = p_dvtt
  AND ma_dv = p_madv;

SELECT
    TO_CHAR(ngay_gio_pttt, 'DD/MM/YYYY HH24:MI'),
    mabenhnhan
INTO
    v_ngaycu,
    v_mabenhnhan
FROM
    kb_cd_dichvu_ct
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_dichvu = p_sophieu
  AND ma_dv = p_madv;

UPDATE kb_cd_dichvu_ct
SET
    ngay_gio_pttt = to_timestamp_tz(p_thoigian, 'dd/mm/yyyy hh24:mi:ss')
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND ma_dv = p_madv
  AND so_phieu_dichvu = p_sophieu;

ELSE
SELECT
    ten_xetnghiem
INTO v_tendichvu
FROM
    cls_xetnghiem
WHERE
    dvtt = p_dvtt
  AND ma_xetnghiem = p_madv;

SELECT
    TO_CHAR(ngay_th_yl, 'DD/MM/YYYY HH24:MI'),
    mabenhnhan
INTO
    v_ngaycu,
    v_mabenhnhan
FROM
    kb_cd_xet_nghiem_chi_tiet
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_xn = p_sophieu
  AND ma_xet_nghiem = p_madv;

UPDATE kb_cd_xet_nghiem_chi_tiet
SET
    ngay_th_yl = to_timestamp_tz(p_thoigian, 'dd/mm/yyyy hh24:mi:ss')
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_xn = p_sophieu;
--and MA_XET_NGHIEM = p_madv;

UPDATE kb_cd_xet_nghiem_chi_tiet
SET
    ngay_th_yl = to_timestamp_tz(p_thoigian, 'dd/mm/yyyy hh24:mi:ss')
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_xn = p_sophieu
  AND id_chisocha = p_madv;

END IF;

ELSE
        IF p_loaidv = 'CDHA' THEN
SELECT
    ten_cdha
INTO v_tendichvu
FROM
    cls_cdha
WHERE
    dvtt = p_dvtt
  AND ma_cdha = p_madv;

SELECT
    TO_CHAR(ngay_th_yl, 'DD/MM/YYYY HH24:MI'),
    mabenhnhan
INTO
    v_ngaycu,
    v_mabenhnhan
FROM
    noitru_cd_cdha_chi_tiet
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_madv;

UPDATE noitru_cd_cdha_chi_tiet
SET
    ngay_th_yl = to_timestamp_tz(p_thoigian, 'dd/mm/yyyy hh24:mi:ss')
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_madv;

ELSIF p_loaidv = 'TTPT' THEN
SELECT
    ten_dv
INTO v_tendichvu
FROM
    dm_dich_vu_kham
WHERE
    dvtt = p_dvtt
  AND ma_dv = p_madv;

SELECT
    TO_CHAR(ngay_gio_pttt, 'DD/MM/YYYY HH24:MI'),
    mabenhnhan
INTO
    v_ngaycu,
    v_mabenhnhan
FROM
    noitru_cd_dichvu_ct
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_dichvu = p_sophieu
  AND ma_dv = p_madv;

UPDATE noitru_cd_dichvu_ct
SET
    ngay_gio_pttt = to_timestamp_tz(p_thoigian, 'dd/mm/yyyy hh24:mi:ss')
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_dichvu = p_sophieu
  AND ma_dv = p_madv;

ELSE
SELECT
    ten_xetnghiem
INTO v_tendichvu
FROM
    cls_xetnghiem
WHERE
    dvtt = p_dvtt
  AND ma_xetnghiem = p_madv;

SELECT
    TO_CHAR(ngay_th_yl, 'DD/MM/YYYY HH24:MI'),
    mabenhnhan
INTO
    v_ngaycu,
    v_mabenhnhan
FROM
    noitru_cd_xet_nghiem_ct
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_xn = p_sophieu
  AND ma_xet_nghiem = p_madv;

UPDATE noitru_cd_xet_nghiem_ct
SET
    ngay_th_yl = to_timestamp_tz(p_thoigian, 'dd/mm/yyyy hh24:mi:ss')
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND so_phieu_xn = p_sophieu
  AND ma_xet_nghiem = p_madv;

END IF;
END IF;

SELECT
    ten_benh_nhan
INTO v_tenbnehnhan
FROM
    his_public_list.dm_benh_nhan
WHERE
    ma_benh_nhan = v_mabenhnhan;

UPDATE cmu_thoigian_nhanvien
SET
    thoigian_bd = trunc(to_timestamp_tz(p_thoigian, 'dd/mm/yyyy hh24:mi:ss'), 'MI')
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND noitru =
      CASE
          WHEN p_hinhthuc = 1 THEN
              0
          ELSE
              1
          END
  AND sophieu = p_sophieu
  AND ma_dv = p_madv;

cmu_log(p_dvtt, 'Thay đổi ngày  thực hiện : '
                    || v_tenbnehnhan
                    || ' - Mã bệnh nhân: '
                    || v_mabenhnhan
                    || '- Thời gian cũ: '
                    || v_ngaycu
                    || ' - Tên dịch vụ: '
                    || v_tendichvu
                    || ' - Số phiếu: '
                    || p_sophieu
                    || ' - MADV: '
                    || p_madv
                    || ' - Thời gian mới: '
                    || p_thoigian, p_user, p_thaotac);

RETURN '0';
END;