create or replace procedure     HIS_MANAGER.CMU_IN_PHIEUDGDD_SANNHI(
    p_dvtt              IN  VARCHAR2,
    p_ma_phieu          IN  NUMBER,
    p_ma_ben<PERSON>_<PERSON>han      IN  VARCHAR2,
    cur out SYS_REFCURSOR
)
IS
v_thamso960616 number(10) := cmu_tsdv(p_dvtt, 960616, 0);
BEGIN
OPEN cur FOR SELECT DISTINCT
                    dgdd.MA_PHIEU,
                    dgdd.MA_BENH_NHAN,
                    dgdd.TEN_BENH_NHAN,
                    dgdd.NAM_SINH,
                    dgdd.GIOI_TINH,
                    dgdd.CHAN_DOAN,
                    dgdd.CAN_NANG_VAO_VIEN,
                    dgdd.CHIEU_CAO,
                    dgdd.BMI,
                    dgdd.CAN_NANG_RA_VIEN,
                    dgdd.SANG_LOC_BMI,
                    dgdd.SANG_LOC_SUT_CAN,
                    dgdd.SANG_LOC_LUONG_AN,
                    dgdd.SANG_LOC_BENH_NANG,
                    dgdd.KET_LUAN_SANG_LOC,
                    dgdd.DANH_GIA_BMI,
                    dgdd.DANH_GIA_SUT_CAN,
                    dgdd.DANH_GIA_LUONG_AN,
                    dgdd.DANH_GIA_BENH_LY,
                    dgdd.DANH_GIA_TONG_DIEM,
                    dgdd.KET_LUAN,
                    dgdd.CHE_DO_AN,
--                    dgdd.DUONG_NUOI_AN,

                    CASE
                       WHEN dgdd.DUONG_NUOI_AN = 'DUONG_MIENG' THEN 'MIENG'
                       ELSE dgdd.DUONG_NUOI_AN
                    END AS DUONG_NUOI_AN,

                    dgdd.MOI_HOI_CHAN_DD,
                    dgdd.TAI_DANH_GIA,
                    TO_CHAR(dgdd.NGAY_TAO_PHIEU, 'dd/MM/yyyy') NGAY_TAO_PHIEU,
                    dgdd.BAC_SI_DIEU_TRI,
                    SUM(dgdd.DANH_GIA_BMI + dgdd.DANH_GIA_SUT_CAN + dgdd.DANH_GIA_LUONG_AN) AS TONG_DANH_GIA,
                    SUM(dgdd.DANH_GIA_BMI + dgdd.DANH_GIA_CV_VONG_CANH_TAY + dgdd.DANH_GIA_TD_TANG_CAN + dgdd.DANH_GIA_BENH_KEM_THEO) AS TONG_DANH_GIA_SAN_PHU,
                    bs.TEN_NHANVIEN_CD TEN_BAC_SI,
                    dgdd.TUOI_THAI,
                    dgdd.TUOI_THAI_THEO,
                    dgdd.CHU_VI_VONG_CANH_TAY,
                    dgdd.DANH_GIA_CV_VONG_CANH_TAY,
                    dgdd.DANH_GIA_TD_TANG_CAN,
                    dgdd.DANH_GIA_BENH_KEM_THEO,
                    dgdd.DANH_GIA_GIA_TRI,
                    v_thamso960616 ANCHUKY
             FROM
                    CMU_PHIEU_DANH_GIA_DD_SAN_NHI     dgdd
                    LEFT JOIN his_fw.dm_nhanvien_cd           bs ON dgdd.bac_si_dieu_tri = bs.ma_nhanvien
             WHERE
                   dgdd.DVTT = p_dvtt
               AND dgdd.ma_phieu = p_ma_phieu
               AND dgdd.MA_BENH_NHAN = p_ma_benh_nhan
             GROUP BY
                    dgdd.MA_PHIEU,
                    dgdd.MA_BENH_NHAN,
                    dgdd.TEN_BENH_NHAN,
                    dgdd.NAM_SINH,
                    dgdd.GIOI_TINH,
                    dgdd.CHAN_DOAN,
                    dgdd.CAN_NANG_VAO_VIEN,
                    dgdd.CHIEU_CAO,
                    dgdd.BMI,
                    dgdd.CAN_NANG_RA_VIEN,
                    dgdd.SANG_LOC_BMI,
                    dgdd.SANG_LOC_SUT_CAN,
                    dgdd.SANG_LOC_LUONG_AN,
                    dgdd.SANG_LOC_BENH_NANG,
                    dgdd.KET_LUAN_SANG_LOC,
                    dgdd.DANH_GIA_BMI,
                    dgdd.DANH_GIA_SUT_CAN,
                    dgdd.DANH_GIA_LUONG_AN,
                    dgdd.DANH_GIA_BENH_LY,
                    dgdd.DANH_GIA_TONG_DIEM,
                    dgdd.KET_LUAN,
                    dgdd.CHE_DO_AN,
                    dgdd.DUONG_NUOI_AN,
                    dgdd.MOI_HOI_CHAN_DD,
                    dgdd.TAI_DANH_GIA,
                    TO_CHAR(dgdd.NGAY_TAO_PHIEU, 'dd/MM/yyyy'),
                    dgdd.BAC_SI_DIEU_TRI,
                    bs.TEN_NHANVIEN_CD,
                    dgdd.TUOI_THAI,
                    dgdd.TUOI_THAI_THEO,
                    dgdd.CHU_VI_VONG_CANH_TAY,
                    dgdd.DANH_GIA_CV_VONG_CANH_TAY,
                    dgdd.DANH_GIA_TD_TANG_CAN,
                    dgdd.DANH_GIA_BENH_KEM_THEO,
                    dgdd.DANH_GIA_GIA_TRI
             ORDER BY
                    NGAY_TAO_PHIEU ASC;
END;