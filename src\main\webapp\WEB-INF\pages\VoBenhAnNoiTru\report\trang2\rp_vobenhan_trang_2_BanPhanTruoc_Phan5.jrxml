<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report name" language="groovy" pageWidth="595" pageHeight="842" columnWidth="580" leftMargin="10" rightMargin="5" topMargin="10" bottomMargin="10" uuid="094bb44e-c3a4-4e55-be44-2d9dcaeab41b">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="133"/>
	<property name="ireport.y" value="144"/>
	<queryString language="plsql">
		<![CDATA[select 1 from dual]]>
	</queryString>
	<field name="ID" class="java.math.BigDecimal"/>
	<field name="LYDOVAOVIEN" class="java.lang.String"/>
	<field name="THOIGIANXUATHIENBENH" class="java.lang.String"/>
	<field name="NGUYENNHAN" class="java.lang.String"/>
	<field name="CACPHUONGPHAPDADIEUTRI" class="java.lang.String"/>
	<field name="TIENSUBANTHANTAIMAT" class="java.lang.String"/>
	<field name="TIENSUBANTHANTOANTHAN" class="java.lang.String"/>
	<field name="TIENSUGIADINHBENHMAT" class="java.lang.String"/>
	<field name="TIENSUGIADINHBENHTOANTHAN" class="java.lang.String"/>
	<field name="THILUCKHONGKINHMP" class="java.lang.String"/>
	<field name="THILUCQUALOMP" class="java.lang.String"/>
	<field name="THILUCKHONGKINHMT" class="java.lang.String"/>
	<field name="THILUCQUALOMT" class="java.lang.String"/>
	<field name="THILUCCOCHINHKINHMP" class="java.lang.String"/>
	<field name="THILUCCOCHINHKINHMT" class="java.lang.String"/>
	<field name="THILUCNHINGANMP" class="java.lang.String"/>
	<field name="THILUCNHINGANMT" class="java.lang.String"/>
	<field name="NHANAPMP" class="java.lang.String"/>
	<field name="NHANAPMT" class="java.lang.String"/>
	<field name="LACVANNHANMP" class="java.lang.String"/>
	<field name="LACVANNHANMT" class="java.lang.String"/>
	<field name="LEDAONUOCTHOATTOTMP" class="java.lang.String"/>
	<field name="LEDAONUOCTHOATTOTMT" class="java.lang.String"/>
	<field name="LEDAOTRAOLEQUANMP" class="java.lang.String"/>
	<field name="LEDAOTRAOLEQUANMT" class="java.lang.String"/>
	<field name="LEDAOTRAOTAICHOMP" class="java.lang.String"/>
	<field name="LEDAOTRAOTAICHOMT" class="java.lang.String"/>
	<field name="MIMATTINHTRANGMP" class="java.lang.String"/>
	<field name="MIMATTINHTRANGMT" class="java.lang.String"/>
	<field name="MIMATTINHTRANGKHACMP" class="java.lang.String"/>
	<field name="MIMATTINHTRANGKHACMT" class="java.lang.String"/>
	<field name="MIMATUMIMP" class="java.lang.String"/>
	<field name="MIMATTINHCHATUMP" class="java.lang.String"/>
	<field name="MIMATUMIMT" class="java.lang.String"/>
	<field name="MIMATTINHCHATUMT" class="java.lang.String"/>
	<field name="MIMATVITRIMP" class="java.lang.String"/>
	<field name="MIMATKICHTHUOCMP" class="java.lang.String"/>
	<field name="MIMATVITRIMT" class="java.lang.String"/>
	<field name="MIMATKICHTHUOCMT" class="java.lang.String"/>
	<field name="MIMATQUAMMP" class="java.lang.String"/>
	<field name="MIMATQUAMMT" class="java.lang.String"/>
	<field name="MIMATQUAMMITRENMP" class="java.lang.String"/>
	<field name="MIMATQUAMMITRENMT" class="java.lang.String"/>
	<field name="MIMATQUAMMIDUOIMP" class="java.lang.String"/>
	<field name="MIMATQUAMMIDUOIMT" class="java.lang.String"/>
	<field name="HOMIMP" class="java.lang.String"/>
	<field name="HOMIMT" class="java.lang.String"/>
	<field name="TREMIMP" class="java.lang.String"/>
	<field name="TREMIMT" class="java.lang.String"/>
	<field name="MIMATKHUYETMIMP" class="java.lang.String"/>
	<field name="MIMATKHUYETMIMT" class="java.lang.String"/>
	<field name="MIMATTUYENBOMIMP" class="java.lang.String"/>
	<field name="MIMATTUYENBOMIMT" class="java.lang.String"/>
	<field name="MIMATVIEMBOMIMP" class="java.lang.String"/>
	<field name="MIMATVIEMBOMIMT" class="java.lang.String"/>
	<field name="MIMATTONTHUONGKHACMP" class="java.lang.String"/>
	<field name="MIMATTONTHUONGKHACMT" class="java.lang.String"/>
	<field name="KETMACCUONGTUMP" class="java.lang.String"/>
	<field name="KETMACCUONGTUMT" class="java.lang.String"/>
	<field name="KETMACPHUNEMP" class="java.lang.String"/>
	<field name="KETMACPHUNEMT" class="java.lang.String"/>
	<field name="KETMACNHUMP" class="java.lang.String"/>
	<field name="KETMACNHUMT" class="java.lang.String"/>
	<field name="KETMACTIETTOMUMP" class="java.lang.String"/>
	<field name="KETMACTIETTOMUMT" class="java.lang.String"/>
	<field name="KETMACFLUORMP" class="java.lang.String"/>
	<field name="KETMACFLUORMT" class="java.lang.String"/>
	<field name="KETMACUKETMACTINHCHATMP" class="java.lang.String"/>
	<field name="KETMACUKETMACTINHCHATMT" class="java.lang.String"/>
	<field name="KETMACUKETMACVITRIMP" class="java.lang.String"/>
	<field name="KETMACUKETMACVITRIMT" class="java.lang.String"/>
	<field name="KETMACUKETMACKICHTHUOCMP" class="java.lang.String"/>
	<field name="KETMACUKETMACKICHTHUOCMT" class="java.lang.String"/>
	<field name="KETMACCUNGDOMP" class="java.lang.String"/>
	<field name="KETMACCUNGDOMT" class="java.lang.String"/>
	<field name="KETMACCHIEUCAOCUACAUDINHMP" class="java.lang.String"/>
	<field name="KETMACCHIEUCAOCUACAUDINHMT" class="java.lang.String"/>
	<field name="KETMACDORONGCUACAUDINHMP" class="java.lang.String"/>
	<field name="KETMACDORONGCUACAUDINHMT" class="java.lang.String"/>
	<field name="KETMACTONTHUONGKHACMP" class="java.lang.String"/>
	<field name="KETMACTONTHUONGKHACMT" class="java.lang.String"/>
	<field name="GIACMACMP" class="java.lang.String"/>
	<field name="GIACMACMT" class="java.lang.String"/>
	<field name="GIACMACKICHTHUOCMP" class="java.lang.String"/>
	<field name="GIACMACKICHTHUOCMT" class="java.lang.String"/>
	<field name="GIACMACHINHDANGMP" class="java.lang.String"/>
	<field name="GIACMACHINHDANGMT" class="java.lang.String"/>
	<field name="GIACMACTONTHUONGDANCHAMMP" class="java.lang.String"/>
	<field name="GIACMACTONTHUONGDANCHAMMT" class="java.lang.String"/>
	<field name="GIACMACPHUBONGBIEUMOMP" class="java.lang.String"/>
	<field name="GIACMACPHUBONGBIEUMOMT" class="java.lang.String"/>
	<field name="GIACMACMATBIEUMOMP" class="java.lang.String"/>
	<field name="GIACMACMATBIEUMOMT" class="java.lang.String"/>
	<field name="GIACMACVITRIMATBIEUMOMP" class="java.lang.String"/>
	<field name="GIACMACVITRIMATBIEUMOMT" class="java.lang.String"/>
	<field name="GIACMACBOTONTHUONGMP" class="java.lang.String"/>
	<field name="GIACMACBOTONTHUONGMT" class="java.lang.String"/>
	<field name="GIACMACTHOAIHOADAIBANGMP" class="java.lang.String"/>
	<field name="GIACMACTHOAIHOADAIBANGMT" class="java.lang.String"/>
	<field name="GIACMACLANGDONGTHUOCMP" class="java.lang.String"/>
	<field name="GIACMACLANGDONGTHUOCMT" class="java.lang.String"/>
	<field name="GIACMACBIEUMOTONTHUONGKHACMP" class="java.lang.String"/>
	<field name="GIACMACBIEUMOTONTHUONGKHACMT" class="java.lang.String"/>
	<field name="GIACMACNHUMOPHUMP" class="java.lang.String"/>
	<field name="GIACMACNHUMOPHUMT" class="java.lang.String"/>
	<field name="GIACMACNHUMOTHAMLAUMP" class="java.lang.String"/>
	<field name="GIACMACNHUMOTHAMLAUMT" class="java.lang.String"/>
	<field name="GIACMACNHUMOVITRITHAMLAUMP" class="java.lang.String"/>
	<field name="GIACMACNHUMOVITRITHAMLAUMT" class="java.lang.String"/>
	<field name="GIACMACNHUMOTIEUMONGMP" class="java.lang.String"/>
	<field name="GIACMACNHUMOTIEUMONGMT" class="java.lang.String"/>
	<field name="GIACMACNHUMOVITRITIEUMONGMP" class="java.lang.String"/>
	<field name="GIACMACNHUMOVITRITIEUMONGMT" class="java.lang.String"/>
	<field name="GIACMACNHUMOTONTHUONGKHACMP" class="java.lang.String"/>
	<field name="GIACMACNHUMOTONTHUONGKHACMT" class="java.lang.String"/>
	<field name="GIACMACNOIMOMP" class="java.lang.String"/>
	<field name="GIACMACNOIMOMT" class="java.lang.String"/>
	<field name="GIACMACNOIMOTUASACTOMP" class="java.lang.String"/>
	<field name="GIACMACNOIMOTUASACTOMT" class="java.lang.String"/>
	<field name="GIACMACNOIMOXUATTIETMP" class="java.lang.String"/>
	<field name="GIACMACNOIMOXUATTIETMT" class="java.lang.String"/>
	<field name="GIACMACNOIMODESCEMETMP" class="java.lang.String"/>
	<field name="GIACMACNOIMODESCEMETMT" class="java.lang.String"/>
	<field name="GIACMACNOIMOTONTHUONGKHACMP" class="java.lang.String"/>
	<field name="GIACMACNOIMOTONTHUONGKHACMT" class="java.lang.String"/>
	<field name="GIACMACDOATHUNGMP" class="java.lang.String"/>
	<field name="GIACMACDOATHUNGMT" class="java.lang.String"/>
	<field name="GIACMACTHUNGGIACMACMP" class="java.lang.String"/>
	<field name="GIACMACTHUNGGIACMACMT" class="java.lang.String"/>
	<field name="GIACMACSEIDELMP" class="java.lang.String"/>
	<field name="GIACMACDUONGKINHTHUNGMP" class="java.lang.String"/>
	<field name="GIACMACSEIDELMT" class="java.lang.String"/>
	<field name="GIACMACDUONGKINHTHUNGMT" class="java.lang.String"/>
	<field name="GIACMACTHUNGBITMP" class="java.lang.String"/>
	<field name="GIACMACTHUNGBITMT" class="java.lang.String"/>
	<field name="GIACMACCAMGIACGIACMACMP" class="java.lang.String"/>
	<field name="GIACMACCAMGIACGIACMACMT" class="java.lang.String"/>
	<field name="GIACMACTANMACHMP" class="java.lang.String"/>
	<field name="GIACMACTANMACHMT" class="java.lang.String"/>
	<field name="GIACMACMUCDOMP" class="java.lang.String"/>
	<field name="GIACMACMUCDOMT" class="java.lang.String"/>
	<field name="GIACMACSUYTEBAONGUONMP" class="java.lang.String"/>
	<field name="GIACMACSUYTEBAONGUONMT" class="java.lang.String"/>
	<field name="GIACMACTHOAIHOAGIAMP" class="java.lang.String"/>
	<field name="GIACMACTHOAIHOAGIAMT" class="java.lang.String"/>
	<field name="GIACMACLANGDONGCANXIMP" class="java.lang.String"/>
	<field name="GIACMACLANGDONGCANXIMT" class="java.lang.String"/>
	<field name="GIACMACBATTHUONGKHACMP" class="java.lang.String"/>
	<field name="GIACMACBATTHUONGKHACMT" class="java.lang.String"/>
	<field name="CUNGMACVIEMMP" class="java.lang.String"/>
	<field name="CUNGMACVIEMMT" class="java.lang.String"/>
	<field name="CUNGMACDOVIEMMP" class="java.lang.String"/>
	<field name="CUNGMACDOVIEMMT" class="java.lang.String"/>
	<field name="CUNGMACVIEMTHUONGCUNGMACMP" class="java.lang.String"/>
	<field name="CUNGMACVIEMTHUONGCUNGMACMT" class="java.lang.String"/>
	<field name="CUNGMACGIANLOIMP" class="java.lang.String"/>
	<field name="CUNGMACGIANLOIMT" class="java.lang.String"/>
	<field name="CUNGMACCHITIETKHACMP" class="java.lang.String"/>
	<field name="CUNGMACCHITIETKHACMT" class="java.lang.String"/>
	<field name="TIENPHONGBINHTHUONGMP" class="java.lang.String"/>
	<field name="TIENPHONGBINHTHUONGMT" class="java.lang.String"/>
	<field name="TIENPHONGMUMP" class="java.lang.String"/>
	<field name="TIENPHONGTYNDALMP" class="java.lang.String"/>
	<field name="TIENPHONGMUMT" class="java.lang.String"/>
	<field name="TIENPHONGTYNDALMT" class="java.lang.String"/>
	<field name="TIENPHONGMANGXUATTIETMP" class="java.lang.String"/>
	<field name="TIENPHONGMANGXUATTIETMT" class="java.lang.String"/>
	<field name="TIENPHONGMAUMP" class="java.lang.String"/>
	<field name="TIENPHONGMAUMT" class="java.lang.String"/>
	<field name="TIENPHONGTONTHUONGKHACMP" class="java.lang.String"/>
	<field name="TIENPHONGTONTHUONGKHACMT" class="java.lang.String"/>
	<field name="MONGMATNAUXOPMP" class="java.lang.String"/>
	<field name="MONGMATNAUXOPMT" class="java.lang.String"/>
	<field name="MONGMATCUONGTUMP" class="java.lang.String"/>
	<field name="MONGMATCUONGTUMT" class="java.lang.String"/>
	<field name="MONGMATPHOIMP" class="java.lang.String"/>
	<field name="MONGMATPHOIMT" class="java.lang.String"/>
	<field name="DONGTUDUONGKINHMP" class="java.lang.String"/>
	<field name="DONGTUDUONGKINHMT" class="java.lang.String"/>
	<field name="DONGTUTRONMP" class="java.lang.String"/>
	<field name="DONGTUVITRIMP" class="java.lang.String"/>
	<field name="DONGTUTRONMT" class="java.lang.String"/>
	<field name="DONGTUVITRIMT" class="java.lang.String"/>
	<field name="DONGTUPHANXAMP" class="java.lang.String"/>
	<field name="DONGTUPHANXAMT" class="java.lang.String"/>
	<field name="DONGTUTONTHUONGKHACMP" class="java.lang.String"/>
	<field name="DONGTUTONTHUONGKHACMT" class="java.lang.String"/>
	<field name="THUYTINHTHEBINHTHUONGMP" class="java.lang.String"/>
	<field name="THUYTINHTHEBINHTHUONGMT" class="java.lang.String"/>
	<field name="THUYTINHTHEHINHTHAIDUCMP" class="java.lang.String"/>
	<field name="THUYTINHTHEHINHTHAIDUCMT" class="java.lang.String"/>
	<field name="THUYTINHTHEIOLMP" class="java.lang.String"/>
	<field name="THUYTINHTHEIOLMT" class="java.lang.String"/>
	<field name="THUYTINHTHETONTHUONGKHACMP" class="java.lang.String"/>
	<field name="THUYTINHTHETONTHUONGKHACMT" class="java.lang.String"/>
	<field name="ANHDONGTUHONGMP" class="java.lang.String"/>
	<field name="ANHDONGTUHONGMT" class="java.lang.String"/>
	<field name="DICHKINHSACHMP" class="java.lang.String"/>
	<field name="DICHKINHSACHMT" class="java.lang.String"/>
	<field name="DICHKINHTONTHUONGKHACMP" class="java.lang.String"/>
	<field name="DICHKINHTONTHUONGKHACMT" class="java.lang.String"/>
	<field name="DAYMATGAITHIMP" class="java.lang.String"/>
	<field name="DAYMATGAITHIMT" class="java.lang.String"/>
	<field name="DAYMATCDMP" class="java.lang.String"/>
	<field name="DAYMATCDMT" class="java.lang.String"/>
	<field name="DAYMATVONGMACMP" class="java.lang.String"/>
	<field name="DAYMATVONGMACMT" class="java.lang.String"/>
	<field name="DAYMATHEMACHMAUMP" class="java.lang.String"/>
	<field name="DAYMATHEMACHMAUMT" class="java.lang.String"/>
	<field name="DAYMATTONTHUONGKHACMP" class="java.lang.String"/>
	<field name="DAYMATTONTHUONGKHACMT" class="java.lang.String"/>
	<field name="HUYETAP" class="java.lang.String"/>
	<field name="NHIETDO" class="java.lang.String"/>
	<field name="MACH" class="java.lang.String"/>
	<field name="NOITIET" class="java.lang.String"/>
	<field name="NOITIETBENHLY" class="java.lang.String"/>
	<field name="THANKINH" class="java.lang.String"/>
	<field name="THANKINHBENHLY" class="java.lang.String"/>
	<field name="TUANHOAN" class="java.lang.String"/>
	<field name="TUANHOANBENHLY" class="java.lang.String"/>
	<field name="HOHAP" class="java.lang.String"/>
	<field name="HOHAPBENHLY" class="java.lang.String"/>
	<field name="TIEUHOA" class="java.lang.String"/>
	<field name="TIEUHOABENHLY" class="java.lang.String"/>
	<field name="COXUONGKHOP" class="java.lang.String"/>
	<field name="COXUONGKHOPBENHLY" class="java.lang.String"/>
	<field name="TIETNIEU" class="java.lang.String"/>
	<field name="TIETNIEUBENHLY" class="java.lang.String"/>
	<field name="TOANTHANKHAC" class="java.lang.String"/>
	<field name="CLS" class="java.lang.String"/>
	<field name="BENHCHINH" class="java.lang.String"/>
	<field name="BENHKEMTHEO" class="java.lang.String"/>
	<field name="PHANBIET" class="java.lang.String"/>
	<field name="TIENLUONG" class="java.lang.String"/>
	<field name="NV_LAMBENHAN" class="java.lang.String"/>
	<field name="NGAY_LAMBENHAN" class="java.lang.String"/>
	<field name="HUONGDIEUTRI" class="java.lang.String"/>
	<field name="TOMTAT_BENHAN" class="java.lang.String"/>
	<field name="ANCHUKY" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="753">
			<staticText>
				<reportElement positionType="Float" x="0" y="36" width="580" height="17" uuid="a35615a7-4c66-4e5d-ac05-1b65edb73a09"/>
				<box leftPadding="6"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[VII. Chẩn đoán:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="121" width="580" height="17" uuid="abba2632-0023-4141-9c28-ab230ca859bd"/>
				<box leftPadding="8"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[(($F{TIENLUONG} != null && !$F{TIENLUONG}.isEmpty())?$F{TIENLUONG}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="333" y="259" width="247" height="18" uuid="58ae33c0-2547-49e9-91ea-65bd51afca9e">
					<printWhenExpression><![CDATA[$F{ANCHUKY}.equals("0")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Họ và tên "+ ($F{NV_LAMBENHAN}!=null?$F{NV_LAMBENHAN}:"............................................................")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="70" width="580" height="17" uuid="6d7a93d2-0604-4674-8905-8d3cbca67c8c"/>
				<box leftPadding="8"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Bệnh kèm theo: "+(($F{BENHKEMTHEO} != null && !$F{BENHKEMTHEO}.isEmpty())?$F{BENHKEMTHEO}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="53" width="580" height="17" uuid="29f0d276-b071-4577-b352-512dfc47be53"/>
				<box leftPadding="8"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Bệnh chính: "+(($F{BENHCHINH} != null && !$F{BENHCHINH}.isEmpty())?$F{BENHCHINH}:"")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="104" width="580" height="17" uuid="5f408979-c88c-413b-bc11-66da3849b3bd"/>
				<box leftPadding="6"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[VIII. Tiên lượng:]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="333" y="189" width="247" height="18" uuid="9f4141fc-13b7-49d3-ae55-db9b627677f5"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY_LAMBENHAN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="333" y="208" width="247" height="18" uuid="75792e22-d1d1-4270-808e-20ea38785b72"/>
				<box leftPadding="6"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Bác sĩ làm bệnh án]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="87" width="580" height="17" uuid="b04f778d-41b9-45da-b2d2-c306d74ee893"/>
				<box leftPadding="8"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Phân biệt: "+(($F{PHANBIET} != null && !$F{PHANBIET}.isEmpty())?$F{PHANBIET}:"")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="138" width="580" height="17" uuid="69a801a3-f158-4b18-951a-2b79b45ac43c"/>
				<box leftPadding="6"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[IX. Hướng điều trị:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="155" width="580" height="17" uuid="db5ce8da-0471-4dbb-8765-0ad9a81e49af"/>
				<box leftPadding="8"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[(($F{HUONGDIEUTRI} != null && !$F{HUONGDIEUTRI}.isEmpty())?$F{HUONGDIEUTRI}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="0" y="17" width="580" height="17" uuid="c1ad9e50-ebe5-44d8-887b-581cb166b7e6"/>
				<box leftPadding="8"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[(($F{TOMTAT_BENHAN} != null && !$F{TOMTAT_BENHAN}.isEmpty())?$F{TOMTAT_BENHAN}:"")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="580" height="17" uuid="bfab4d2a-2e4b-420a-83fc-40e2a33c9e49"/>
				<box leftPadding="6"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[VI. Tóm tắt bệnh án:]]></text>
			</staticText>
		</band>
	</title>
</jasperReport>
