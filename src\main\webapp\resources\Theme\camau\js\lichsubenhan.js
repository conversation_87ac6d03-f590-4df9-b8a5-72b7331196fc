var LOGHSBALOAI = {
    PHIEULOETTIDE: {
        KEY: 'PHIEULOETTIDE',
        VALUE: 'Phiếu loét tì đè'
    },
    PHIEUTHEODOIBILAN: {
        KEY: 'PHIEUTHEODOIBILAN',
        VALUE: 'Phiếu theo dõi bilan'
    },
    PHIEUTHEODOIBILANCHITIET: {
        KEY: 'PHIEUTHEODOIBILANCHITIET',
        VALUE: 'Phiếu theo dõi bilan chi tiết'
    },
    HUONGDANTHUCHIENNOIQUY: {
        KEY: 'HUONGDANTHUCHIENNOIQUY',
        VALUE: 'Hướng dẫn thực hiện nội quy '
    },
    PHIEUTOMTATTHONGTINDIEUTRI: {
        KEY: 'PHIEUTOMTATTHONGTINDIEUTRI',
        VALUE: 'Phiếu tóm tắt thông tin điều trị'
    },
    PHIEUKHAMBENHVAOVIEN: {
        KEY: 'PHIE<PERSON><PERSON><PERSON><PERSON><PERSON>HVAOVIEN',
        VALUE: '<PERSON>ế<PERSON> khám bệnh vào viện'
    },
    PHIEUCUNGCAPTHONGTIN: {
        KEY: 'PHIEUCUNGCAPTHONGTIN',
        VALUE: 'Phiếu cung cấp thông tin và cam kết chung về nhập viện nội trú'
    },
    PHIEUHOICHAN: {
        KEY: 'PHIEUHOICHAN',
        VALUE: 'Phiếu hội chẩn'
    },
    PHIEUSOKET15NGAYDIEUTRI: {
        KEY: 'PHIEUSOKET15NGAYDIEUTRI',
        VALUE: 'Phiếu sơ kết 15 ngày điều trị'
    },
    PHIEUKHAMCHUYENKHOA: {
        KEY: 'PHIEUKHAMCHUYENKHOA',
        VALUE: 'Phiếu khám chuyên khoa'
    },
    PHIEUKHAMTIENME: {
        KEY: 'PHIEUKHAMTIENME',
        VALUE: 'Phiếu khám tiền mê'
    },
    PHIEUXNGPBENHSINHTHIET: {
        KEY: 'PHIEUXNGPBENHSINHTHIET',
        VALUE: 'Phiếu xét nghiệm giải phẫu bệnh sinh thiết'
    },
    KYSOPHIEUXNGPBENHSINHTHIET: {
        KEY: 'PHIEUXNGPBENHSINHTHIET',
        VALUE: 'Ký số phiếu xét nghiệm giải phẫu bệnh sinh thiết'
    },
    PHIEUDANHGIATINHTRANGDINHDUONG: {
        TREN18TUOI: {
            KEY: 'PHIEUDANHGIATINHTRANGDINHDUONGTREN18TUOI',
            VALUE: 'Phiếu đánh giá tình trạng đinh dưỡng trên 18 tuổi '
        },
        NGUOILON: {
            KEY: 'PHIEUDANHGIATINHTRANGDINHDUONGNGUOILON',
            VALUE: 'Phiếu đánh giá tình trạng đinh dưỡng người lớn '
        },
        SANPHU: {
            KEY: 'PHIEUDANHGIATINHTRANGDINHDUONGSANPHU',
            VALUE: 'Phiếu đánh giá tình trạng đinh dưỡng sản phụ '
        },
        TREEM: {
            KEY: 'PHIEUDANHGIATINHTRANGDINHDUONGTREEM',
            VALUE: 'Phiếu đánh giá tình trạng đinh dưỡng trẻ em '
        }
    },
    PHIEUTHEODOICHAYTHAN: {
        KEY: 'PHIEUTHEODOICHAYTHAN',
        VALUE: 'Phiếu theo dõi chạy thận'
    },
    PHIEUTHEODOICHAYTHANCT: {
        KEY: 'PHIEUTHEODOICHAYTHANCT',
        VALUE: 'Phiếu theo dõi chạy thận chi tiết'
    },
    BANGKIEMGAC: {
        KIEMTRADOICHIEU: {
            KEY: 'KIEMTRADOICHIEU',
            VALUE: 'Kiểm tra đối chiếu '
        },
        THONGTINKIEMGAC: {
            KEY: 'THONGTINKIEMGAC',
            VALUE: 'Thông tin kiểm gạc '
        },
        THONGTINKIEMGACTONGTRUOC: {
            KEY: 'THONGTINKIEMGACTONGTRUOC',
            VALUE: 'Thông tin kiểm gạc - tổng trước '
        },
        THONGTINKIEMGACTONGSAU: {
            KEY: 'THONGTINKIEMGACTONGSAU',
            VALUE: 'Thông tin kiểm gạc - tổng sau '
        }
    },
    GIUONGBENH: {
        KEY: 'GIUONGBENH',
        VALUE: 'Giường bệnh '
    },
    PHIEUCHUCNANGSONG: {
        KEY: 'PHIEUCHUCNANGSONG',
        VALUE: 'Phiếu chức năng sống'
    },
    KEHOACHCHAMSOC: {
        KEY: 'KEHOACHCHAMSOC',
        VALUE: 'Kế hoạch chăm sóc'
    },
    PHIEUPCA: {
        KEY: 'PHIEUPCA',
        VALUE: 'Phiếu PCA'
    },
    BIENBANTUVONG: {
        KEY: 'BIENBANTUVONG',
        VALUE: 'Biên bản tử vong'
    },
    TTXUATVIEN: {
        KEY: 'TTXUATVIEN',
        VALUE: 'Thông tin xuất viện'
    },
    XUATVIEN: {
        KEY: 'XUATVIEN',
        VALUE: 'Xuất viện'
    },
    SOGIAYHENNOITRU: {
        KEY: 'SOGIAYHENNOITRU',
        VALUE: 'Số giấy hẹn xuất viện'
    },
    SOLUUTRUNOITRU: {
        KEY: 'SOLUUTRUNOITRU',
        VALUE: 'Số lưu trữ xuất viện'
    },
    SOLUUTRUTAMNOITRU: {
        KEY: 'SOLUUTRUTAMNOITRU',
        VALUE: 'Số lưu trữ tạm xuất viện'
    },
    GIAYBAOTU: {
        KEY: 'GIAYBAOTU',
        VALUE: 'Giấy báo tử'
    },
    TTCHUYENTUYEN: {
        KEY: 'TTCHUYENTUYEN',
        VALUE: 'Thông tin chuyển tuyến'
    },
    CHUYENTUYEN: {
        KEY: 'CHUYENTUYEN',
        VALUE: 'Chuyển tuyến'
    },
    SOLUUTRUCHUYENTUYEN: {
        KEY: 'SOLUUTRUCHUYENTUYEN',
        VALUE: 'Cập nhật số lưu trữ chuyển tuyến'
    },
    TAOSOLUUTRUCHUYENTUYEN: {
        KEY: 'SOLUUTRUCHUYENTUYEN',
        VALUE: 'Tạo số lưu trữ chuyển tuyến'
    },
    SOLUUTRUTAMCHUYENTUYEN: {
        KEY: 'SOLUUTRUTAMCHUYENTUYEN',
        VALUE: 'Tạo số lưu trữ tạm chuyển tuyến'
    },
    BIENBANHOP: {
        KEY: 'BIENBANHOP',
        VALUE: 'Biên bản họp'
    },
    TIENSUDIUNG: {
        KEY: 'TIENSUDIUNG',
        VALUE: 'Tiền sử dị ứng'
    },

    THONGTINHANHCHINH: {
        KEY: 'THONGTINHANHCHINH',
        VALUE: 'Thông tin hành chính'
    },
    BENHAN: {
        KEY: 'BENHAN',
        VALUE: 'Bệnh án'
    },
    NHI: {
        KEY: 'NHI',
        VALUE: 'Bệnh án nhi'
    },
    PHUKHOA: {
        KEY: 'PHUKHOA',
        VALUE: 'Bệnh án phụ khoa'
    },
    SANKHOA: {
        KEY: 'SANKHOA',
        VALUE: 'Bệnh án sản khoa',
        TIENSUSK: {
            KEY: "TIENSUSK",
            VALUE: "Tiền sử sản khoa"
        },
        DACDIEMTRESS: {
            KEY: "DACDIEMTRESS",
            VALUE: "Đặc điểm trẻ sơ sinh"
        },
        DACDIEMSORAU: {
            KEY: "DACDIEMSORAU",
            VALUE: "Đặc điểm sổ rau"
        },
    },
    SOSINH: {
        KEY: 'SOSINH',
        VALUE: 'Bệnh án sơ sinh'
    },
    MATLAC: {
        KEY: 'MATLAC',
        VALUE: 'Bệnh án mắt lác'
    },
    MATTREEM: {
        KEY: 'MATTREEM',
        VALUE: 'Bệnh án mắt trẻ em'
    },
    BONG: {
        KEY: 'BONG',
        VALUE: 'Bệnh án bỏng'
    },
    NGOAITRURHM: {
        KEY: 'NGOAITRURHM',
        VALUE: 'Bệnh án ngoại trú RHM'
    },
    TAYCHANMIENG: {
        KEY: 'TAYCHANMIENG',
        VALUE: 'Bệnh án tay chân miệng'
    },
    NGOAITRU_CHUNG: {
        KEY: 'NGOAITRU_CHUNG',
        VALUE: 'Bệnh án ngoại trú chung'
    },
    NOI: {
        KEY: 'NOI',
        VALUE: 'Bệnh án nội'
    },
    TRUYENDICH: {
        KEY: 'TRUYENDICH',
        VALUE: 'Phiếu truyền dịch',
        TRUYENDICHYLENH: {
            KEY: 'TRUYENDICHYLENH',
            VALUE: 'Truyền dịch y lệnh'
        },
        PHATHUOC: {
            KEY: 'TRUYENDICHPHATHUOC',
            VALUE: 'Truyền dịch pha thuốc'
        },
    },
    TRUYENNHIEM: {
        KEY: 'TRUYENNHIEM',
        VALUE: 'Bệnh án truyền nhiễm'
    },
    TAIMUIHONG: {
        KEY: 'TAIMUIHONG',
        VALUE: 'Bệnh án tai mũi họng'
    },
    DALIEU: {
        KEY: 'DALIEU',
        VALUE: 'Bệnh án da liễu'
    },
    NGOAIKHOA: {
        KEY: 'NGOAIKHOA',
        VALUE: 'Bệnh án ngoại khoa'
    },
    TAMTHAN: {
        KEY: 'TAMTHAN',
        VALUE: 'Bệnh án tâm thần'
    },
    NHIEM: {
        KEY: 'NHIEM',
        VALUE: 'Bệnh án nhiễm'
    },
    UNGBUOU: {
        KEY: 'UNGBUOU',
        VALUE: 'Bệnh án ung bướu'
    },
    DAYMAT: {
        KEY: 'DAYMAT',
        VALUE: 'Bệnh án đáy mắt'
    },
    RANGHAMMAT: {
        KEY: 'RANGHAMMAT',
        VALUE: 'Bệnh án răng hàm mặt'
    },
    NOITRUYHCT: {
        KEY: 'NOITRUYHCT',
        VALUE: 'Bệnh án nội trú YHCT'
    },
    MATGLOCOM: {
        KEY: 'MATGLOCOM',
        VALUE: 'Bệnh án mắt Glocom'
    },
    THUOCVATTU: {
        KEY: 'THUOCVATTU',
        VALUE: 'Thuốc/vật tư'
    },
    TTPT: {
        KEY: 'TTPT',
        VALUE: 'Thủ thuật phẫu thuật'
    },
    CDHA: {
        KEY: 'CDHA',
        VALUE: 'Chẩn đoán hình ảnh'
    },
    XETNGHIEM: {
        KEY: 'XETNGHIEM',
        VALUE: 'Xét nghiệm'
    },
    TODIEUTRI: {
        KEY: 'TODIEUTRI',
        VALUE: 'Tờ điều trị'
    },
    PHIEUCHAMSOC: {
        KEY: 'PHIEUCHAMSOC',
        VALUE: 'Phiếu chăm sóc'
    },
    THEODOIBILAN: {
        KEY: 'THEODOIBILAN',
        VALUE: 'Theo dõi Bilan',
        THEODOIBILANCHITIET: {
            KEY: "THEODOIBILANCHITIET",
            VALUE: "Theo dõi Bilan chi tiết"
        }
    },
    BIEUDOCHUYENDA: {
        KEY: 'BIEUDOCHUYENDA',
        VALUE: 'Biểu đồ chuyển dạ',
        BIEUDOCHUYENDACHITIET: {
            KEY: "BIEUDOCHUYENDACHITIET",
            VALUE: "Biểu đồ chuyển dạ chi tiết"
        }
    },
    DONGYXETNGHIEMHIV: {
        KEY: 'DONGYXETNGHIEMHIV',
        VALUE: 'Phiếu đồng ý xét nghiệm HIV ghi tên',
    },
    PHIEUCHAMSOCCAP1: {
        KEY: 'PHIEUCHAMSOCCAP1',
        VALUE: 'Phiếu chăm sóc cấp 1',
        PHIEUCHAMSOCCAP1CT: {
            KEY: 'PHIEUCHAMSOCCAP1CT',
            VALUE: 'Chi tiết phiếu chăm sóc cấp 1',
        }
    },
    CHAMSOCCAP1CHITIET: {
        KEY: "CHAMSOCCAP1CHITIET",
        VALUE: "Chăm sóc cấp 1 chi tiết"
    },
    CHAMSOCCAP1TOANTHAN: {
        KEY: "CHAMSOCCAP1TOANTHAN",
        VALUE: "Chăm sóc cấp 1 toàn thân"
    },
    CHAMSOCCAP1HOHAP: {
        KEY: "CHAMSOCCAP1HOHAP",
        VALUE: "Chăm sóc cấp 1 hô hấp"
    },
    CHAMSOCCAP1TUANHOAN: {
        KEY: "CHAMSOCCAP1TUANHOAN",
        VALUE: "Chăm sóc cấp 1 tuần hoàn"
    },
    CHAMSOCCAP1THONGTINTHEM: {
        KEY: "CHAMSOCCAP1THONGTINTHEM",
        VALUE: "Chăm sóc cấp 1 thông tin thêm"
    },
    CHAMSOCCAP1NUOCNHAP: {
        KEY: "CHAMSOCCAP1NUOCNHAP",
        VALUE: "Chăm sóc cấp 1 nước nhập"
    },
    CHAMSOCCAP1NUOCXUAT: {
        KEY: "CHAMSOCCAP1NUOCXUAT",
        VALUE: "Chăm sóc cấp 1 nước xuất"
    },
    CHAMSOCCAP1: {
        KEY: 'CHAMSOCCAP1',
        VALUE: 'Chăm sóc cấp 1',
    },
    CHAMSOCCAP1CHANDOAN: {
        KEY: 'CHAMSOCCAP1CHANDOAN',
        VALUE: 'Chăm sóc cấp 1 chẩn đoán',
    },
    CHAMSOCCAP1GHICHU: {
        KEY: 'CHAMSOCCAP1GHICHU',
        VALUE: 'Chăm sóc cấp 1 ghi chú',
    },
    CHAMSOCCAP1QUYUOC: {
        KEY: 'CHAMSOCCAP1QUYUOC',
        VALUE: 'Chăm sóc cấp 1 quy ước',
    },
    CHAMSOCCAP2CHITIET: {
        KEY: "CHAMSOCCAP2CHITIET",
        VALUE: "Chăm sóc cấp 2 chi tiết"
    },
    CHAMSOCCAP2HOHAP: {
        KEY: "CHAMSOCCAP2HOHAP",
        VALUE: "Chăm sóc cấp 2 hô hấp"
    },
    CHAMSOCCAP2TUANHOAN: {
        KEY: "CHAMSOCCAP2TUANHOAN",
        VALUE: "Chăm sóc cấp 2 tuần hoàn"
    },
    CHAMSOCCAP2DINHDUONG: {
        KEY: "CHAMSOCCAP2DINHDUONG",
        VALUE: "Chăm sóc cấp 2 dinh dưỡng"
    },
    CHAMSOCCAP2GIACNGU: {
        KEY: "CHAMSOCCAP2GIACNGU",
        VALUE: "Chăm sóc cấp 2 giấc ngủ"
    },
    CHAMSOCCAP2VSCN: {
        KEY: "CHAMSOCCAP2VSCN",
        VALUE: "Chăm sóc cấp 2 vệ sinh cá nhân"
    },
    CHAMSOCCAP2TINHTHAN: {
        KEY: "CHAMSOCCAP2TINHTHAN",
        VALUE: "Chăm sóc cấp 2 tinh thần"
    },
    CHAMSOCCAP2VANDONG: {
        KEY: "CHAMSOCCAP2VANDONG",
        VALUE: "Chăm sóc cấp 2 vận động"
    },
    CHAMSOCCAP2GDSK: {
        KEY: "CHAMSOCCAP2GDSK",
        VALUE: "Chăm sóc cấp 2 giáo dục sức khỏe"
    },
    CHAMSOCCAP2TOANTHAN: {
        KEY: "CHAMSOCCAP2TOANTHAN",
        VALUE: "Chăm sóc cấp 2 hô hấp"
    },
    CHAMSOCCAP2THEODOIKHAC: {
        KEY: "CHAMSOCCAP2GDSK",
        VALUE: "Chăm sóc cấp 2 giáo dục sức khỏe"
    },
    CHAMSOCCAP2CANTHIEPDD: {
        KEY: "CHAMSOCCAP2GDSK",
        VALUE: "Chăm sóc cấp 2 giáo dục sức khỏe"
    },
    CHAMSOCCAP2BANGIAO: {
        KEY: "CHAMSOCCAP2BANGIAO",
        VALUE: "Chăm sóc cấp 2 bàn giao"
    },
    CHAMSOCCAP2: {
        KEY: 'CHAMSOCCAP2',
        VALUE: 'Chăm sóc cấp 2',
    },
    CHAMSOCCAP2CHANDOAN: {
        KEY: 'CHAMSOCCAP2CHANDOAN',
        VALUE: 'Chăm sóc cấp 2 chẩn đoán',
    },
    CHAMSOCCAP2QUYUOC: {
        KEY: 'CHAMSOCCAP2QUYUOC',
        VALUE: 'Chăm sóc cấp 2 quy ước',
    },
    KEHOACHCHAMSOCNGUOIBENH: {
        KEY: 'KEHOACHCHAMSOCNGUOIBENH',
        VALUE: 'Kế hoạch chăm sóc người bệnh'
    },
    CAMKETCHAPNHANTESTDA: {
        KEY: 'CAMKETCHAPNHANTESTDA',
        VALUE: 'Cam kết chấp nhận test da'
    },
    BANGKECHANCHINHHOSOBENHAN: {
        KEY: 'BANGKECHANCHINHHOSOBENHAN',
        VALUE: 'Bảng kê chấn chỉnh hồ sơ bệnh án'
    },
    PHIEUTNTT: {
        KEY: 'PHIEUTNTT',
        VALUE: 'Phiếu TNTT'
    },
    LUONGGIAHDCN: {
        KEY: 'LUONGGIAHDCN',
        VALUE: 'Phiếu lượng giá hoạt động chức năng',
    },
    KHAMCHIDINHPHCN: {
        KEY: 'KHAMCHIDINHPHCN',
        VALUE: 'Phiếu khám và chỉ định phục hồi chức năng',
    },
    KYTHUATPHCN: {
        KEY: 'KYTHUATPHCN',
        VALUE: 'Phiếu thực hiện kỹ thuật phục hồi chức năng',
    },
    CAMKETTUCHOISDDV: {
        KEY: 'CAMKETTUCHOISDDV',
        VALUE: 'Phiếu cam kết từ chối sử dụng dịch vụ khám bệnh, chữa bệnh',
    },
    KHAMBENHTHEOYC: {
        KEY: 'KHAMBENHTHEOYC',
        VALUE: 'Phiếu khám chữa bệnh theo yêu cầu',
    },
    CAMKETPHAUTHUATGMHS: {
        KEY: 'CAMKETPHAUTHUATGMHS',
        VALUE: 'Giấy cam kết chấp thuận phẫu thuật, thủ thuật và gây mê hồi sức',
    },
    CAMKETDTXATRI: {
        KEY: 'CAMKETDTXATRI',
        VALUE: 'Phiếu cam kết chấp thuận điều trị bằng xạ trị',
    },
    CAMKETDTHOAXATRI: {
        KEY: 'CAMKETDTHOAXATRI',
        VALUE: 'Phiếu cam kết chấp thuận điều trị bằng hóa trị - xạ trị',
    },
    CAMKETCHUYENCSKB: {
        KEY: 'CAMKETCHUYENCSKB',
        VALUE: 'Phiếu cam kết chuyển cơ sở khám bệnh, chữa bệnh',
    },
    CUNGCAPTTNGUOIBENH: {
        KEY: 'CUNGCAPTTNGUOIBENH',
        VALUE: 'Phiếu cung cấp thông tin về người bệnh (tại khoa hồi sức tích cực)',
    },
    TOMTATHSBA: {
        KEY: 'TOMTATHSBA',
        VALUE: 'Phiếu tóm tắt hồ sơ bệnh án',
    },
    CAMKETRAVIENKHONGBS: {
        KEY: 'CAMKETRAVIENKHONGBS',
        VALUE: 'Giấy cam kết ra viện không theo chỉ định bác sĩ(Khi chưa kết thúc việc chữa bệnh)',
    },
    PHANLOAINGUOIBENH: {
        KEY: 'PHANLOAINGUOIBENH',
        VALUE: 'Phiếu nhận định, phân loại người bệnh'
    },
    CHUYENKHOADIEUDUONG: {
        KEY: 'CHUYENKHOADIEUDUONG',
        VALUE: 'Phiếu chuyển khoa điều dưỡng'
    },
    DEXUATPTTT: {
        KEY: 'DEXUATPTTT',
        VALUE: 'Phiếu đề xuất phẫu thuật, thủ thuật theo yêu cầu',
    },
    GIAYCAMDOANSUDUNG: {
        KEY: 'GIAYCAMDOANSUDUNG',
        VALUE: 'Phiếu cam đoan (Sử dụng cho bệnh nhân dưới 06 tuổi miễn phí, viện phí và người bệnh có thẻ BHYT)',
    },
    DANGKYKBTHEOYC: {
        KEY: 'DANGKYKBTHEOYC',
        VALUE: 'Phiếu đăng ký khám chữa bệnh theo yêu cầu',
    },
    THEODOISDKHANGSINHUT: {
        KEY: 'THEODOISDKHANGSINHUT',
        VALUE: 'Phiếu theo dõi sử dụng kháng sinh dự phòng',
    },
    CHUANDOANNGUYENNHANTUVONG: {
        KEY: 'CHUANDOANNGUYENNHANTUVONG',
        VALUE: 'Phiếu chẩn đoán nguyên nhân tử vong',
    },
    BAOCAOTUVONG: {
        KEY: 'BAOCAOTUVONG',
        VALUE: 'Báo cáo tử vong',
    },
    TINHDICHDO: {
        KEY: 'TINHDICHDO',
        VALUE: 'Phiếu tinh dịch đồ',
    },
    KETQUASANGLOCBENHLYTIMBAMSINH: {
        KEY: 'KETQUASANGLOCBENHLYTIMBAMSINH',
        VALUE: 'Phiếu kết quả sàng lọc bệnh lý tim bẩm sinh nặng ở trẻ sơ sinh bằng phương pháp đo độ bảo hòa oxy qua da',
    },
    GIAYCAMKETNAMVIENDTNT: {
        KEY: 'GIAYCAMKETNAMVIENDTNT',
        VALUE: 'Giấy cam kết nằm viện điều trị nội trú',
    },
    KIEMTRABENHAN: {
        KEY: 'KIEMTRABENHAN',
        VALUE: 'Phiếu Kiểm tra bệnh án',
    },
    GIAYCHUNGSINH: {
        KEY: 'GIAYCHUNGSINH',
        VALUE: 'Giấy chứng sinh',
    },
    HENTAIKHAM: {
        KEY: 'HENTAIKHAM',
        VALUE: 'Giấy hẹn tái khám',
    },
    GIAYCHUNGTU: {
        KEY: 'GIAYCHUNGTU',
        VALUE: 'Giấy chứng tử',
    },
    MOIKHAM: {
        KEY: 'MOIKHAM',
        VALUE: 'Mời khám'
    },
    TAMUNG: {
        KEY: 'TAMUNG',
        VALUE: 'Tạm ứng'
    },
    TRUYENMAU: {
        KEY: "TRUYENMAU",
        VALUE: "Truyền máu"
    },
    YLENHTHUOC: {
        KEY: "YLENHTHUOC",
        VALUE: "Y lệnh thuốc"
    },
    BOSUNGYLENH: {
        KEY: 'BOSUNGYLENH',
        VALUE: 'Bổ sung y lệnh',
    },
    THONGTINHANHCHINHNGOAITRU: {
        KEY: 'THONGTINHANHCHINHNGOAITRU',
        VALUE: 'Thông tin hành chính ngoại trú'
    },
    KHAMBENHNGOAITRU: {
        KEY: "KHAMBENHNGOAITRU",
        VALUE: "Khám bệnh ngoại trú"
    }
}
var LOGHSBAACTION = {
    INSERT: {
        KEY: 'INSERT',
        VALUE: 'Thêm'
    },
    DELETE: {
        KEY: 'DELETE',
        VALUE: 'Xóa'
    },
    EDIT: {
        KEY: 'EDIT',
        VALUE: 'Sửa'
    },
}
$(function() {
    $("#v-thongtinlichsubenhan-tab").click(function() {
        initGridLichSuBenhAn()
        loadGridLichSuBenhAn()
    })
    $("#hsba_log_lammoi").click(function() {
        loadGridLichSuBenhAn()
    })

    function initGridLichSuBenhAn() {
        var list = $("#hsba_log_table");
        if(!list[0].grid) {
            list.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 500,
                width: null,
                shrinkToFit: false,
                colModel: [
                    { label: "Ngày giờ", name: "NGAY_TAO", width: 100,
                        cellattr: function(rowId, val, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    { label: "Loại", name: "LOAI", width: 100 ,
                        cellattr: function(rowId, val, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        },
                        formatter: function(cellvalue, options, rowObject) {
                            return LOGHSBALOAI[cellvalue]? LOGHSBALOAI[cellvalue].VALUE: cellvalue
                        }
                    },
                    { label: "Hành động", name: "ACTION", width: 100,
                        formatter: function(cellvalue, options, rowObject) {
                            return LOGHSBAACTION[cellvalue]? LOGHSBAACTION[cellvalue].VALUE:cellvalue
                        }
                    },
                    { label: "Nội dung cũ", name: "NOIDUNGBANDAU", width: 300 ,
                        cellattr: function(rowId, val, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    { label: "Nội dung mới", name: "NOIDUNGMOI", width: 300,
                        cellattr: function(rowId, val, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    { label: "Người thực hiện", name: "TENNHANVIEN", width: 150 },

                ],
                caption: "Lịch sử bệnh án",

            })
            list.jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
        }

    }
    function loadGridLichSuBenhAn() {
        var arr = [
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            $("#hsba_log_tungay").val(),
            $("#hsba_log_denngay").val()
        ];
        var url = "cmu_list_CMU_HSBA_LOG_GET?url=" + convertArray(arr);
        var list = $("#hsba_log_table");
        loadDataGridGroupBy(list, url);
    }
})