CREATE OR REPLACE PROCEDURE     "HIS_MANAGER"."CMU_QT_BCVP_TONGHOP_96161" (
    v_tungay     IN VARCHAR2,
    v_denngay    IN VARCHAR2,
    v_manhanvien IN VARCHAR2,
    p_cachlay    IN VARCHAR2,
    p_dvtt       IN VARCHAR2,
    p_hinh<PERSON>uctt IN VARCHAR2,
    cur          OUT SYS_REFCURSOR
) IS
    p_tungay DATE := TO_DATE(v_tungay || ':00', 'dd/mm/yyyy hh24:mi:ss');
    p_denngay DATE := TO_DATE(v_denngay || ':59', 'dd/mm/yyyy hh24:mi:ss');
BEGIN
OPEN cur FOR
SELECT bn.ten<PERSON><PERSON><PERSON>,
       tongtien AS thanhtien,
       TO_CHAR(ngaythanhtoan, 'DD/MM/YYYY HH24:MI:SS') AS ngaythanhtoan,
       bn.ngaytao,
       ngaythanhtoan AS tt_day,
       tt.mad<PERSON><PERSON><PERSON>,
       nv.ten_nhanvien AS nguoiban,
       bank.TT_THANHTOAN_QRCODE
FROM cmu_qt_thanhtoan tt
         INNER JOIN cmu_qt_bn_banle bn ON tt.dvtt = bn.dvtt
    AND tt.madonthuoc = bn.madonthuoc
         LEFT JOIN bank_payment bank ON bn.dvtt = bank.dvtt
    AND 'NHATHUOC_' || bn.madonthuoc = bank.noidung
    AND bank.noidung IS NOT NULL
    AND bank.ngay_tao BETWEEN p_tungay AND p_denngay
         INNER JOIN his_fw.dm_nhanvien nv ON bn.nguoitao = nv.ma_nhanvien
WHERE tt.dvtt = p_dvtt
  AND tt.trangthai != 2
           AND (v_manhanvien = '-1' OR tt.nguoitao = v_manhanvien)
           AND ( (p_cachlay = '0' AND ngaythanhtoan BETWEEN p_tungay AND p_denngay)
               OR (p_cachlay != '0' AND bn.ngaytao BETWEEN p_tungay AND p_denngay) )
           AND ( (p_hinhthuctt = '0' AND (bank.TT_THANHTOAN_QRCODE = 0 OR bank.TT_THANHTOAN_QRCODE IS NULL))
               OR (p_hinhthuctt != '0' AND bank.TT_THANHTOAN_QRCODE = 1) )
ORDER BY nguoiban, ngaytao, tt_day ASC;
END;

