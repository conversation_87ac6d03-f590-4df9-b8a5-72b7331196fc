CREATE OR REPLACE FUNCTION dsbn_gchung<PERSON>h_mobile (
    p_dvtt          IN              VARCHAR2,
    p_ngaybatdau    IN              VARCHAR2,
    p_ngayketthuc   IN              VARCHAR2,
    p_trangthai     IN              VARCHAR2,
    p_chucvu        IN              VARCHAR2,
    p_manhanvien    IN              VARCHAR2
) RETURN SYS_REFCURSOR IS

    cur             SYS_REFCURSOR;
    v_ngaykethuc    DATE := TO_DATE(p_ngayketthuc || ' 23:59:59', 'DD/MM/YYYY HH24:MI:SS');
    v_ngaybatdau    DATE := TO_DATE(p_ngaybatdau || ' 00:00:00', 'DD/MM/YYYY HH24:MI:SS');
    v_tennhanvien   VARCHAR2(500);
BEGIN
SELECT
    ten_nhanvien_cd
INTO v_tennhanvien
FROM
    his_fw.dm_nhanvien_cd
WHERE
    ma_nhanvien = p_manhanvien;

OPEN cur FOR SELECT
                    bn.ma_benh_nhan,
                    ba.stt_ben<PERSON>,
                    ba.sobenhan,
                    ddt.stt_dotdieutri,
                    bn.ten_benh_nhan,
                    bn.gioi_tinh,
                    ba.sovaovien,
                    ddt.sovaovien_dt,
                    EXTRACT(YEAR FROM trunc(ba.ngaynhapvien)) - EXTRACT(YEAR FROM trunc(bn.ngay_sinh)) AS tuoi,
                    cmu_hienthi_thangv2(bn.ngay_sinh, ba.ngaynhapvien, p_dvtt) thang,
                    cmu_hienthi_ngayv2(bn.ngay_sinh, ba.ngaynhapvien, p_dvtt) ngay,
                    bn.dia_chi,
                    giuong.stt_buong    ten_phong,
                    giuong.stt_giuong   sogiuong,
                    'NOITRU_GIAYCHUNGSINH' ky_hieu_phieu,
                    grv.id_chungsinh    so_phieu_dv,
                    '-1' ma_dich_vu,
                    'cmu_in_rp_giaychungsinh_mobile' urlgetpdf,
                    'Người đỡ đẻ' keyword,
                    'Người đỡ đẻ' keywordcomment,
                    '0' cx1,
                    '140' cx,
                    '-40' cy1,
                    '-105' cy2,
                    'NOITRU_GIAYCHUNGSINH' kyhieukyso,
                    '-1' id_dieutri,
                    '1' visibletype,
                    v_tennhanvien       tennhanvien,
                    grv.ngaychungsinh   ngayravien,
                    'rp_giaychungsinh_mobile' jasper,
                    TO_CHAR(grv.ngaychungsinh, 'DD/MM/YYYY ') ngayrv,
                    pban.ten_phongban,
                    '13' fontsize,
                    his_ytcs.id_nhankhau_sel_mbn(bn.ma_benh_nhan) b_id_nhankhau,
                    bn.ma_benh_nhan     b_mabenhnhan,
                    1 b_tuvong,
                    'Ngày '
                    || TO_CHAR(grv.ngaychungsinh, 'DD')
                    || ' tháng '
                    || TO_CHAR(grv.ngaychungsinh, 'MM')
                    || ' năm '
                    || TO_CHAR(grv.ngaychungsinh, 'YYYY') ngaythangnam,
                    grv.id_chungsinh    b_idchungsinh,
                    cmu_getminiokey(p_dvtt, grv.sovaovien, 'NOITRU_GIAYCHUNGSINH', grv.id_chungsinh, '-1') keyminio,
                    cmu_getsignkey(p_dvtt, grv.sovaovien, 'NOITRU_GIAYCHUNGSINH', grv.id_chungsinh, '-1') keysign
                FROM
                    cmu_giay_chung_sinh            grv
                    JOIN noitru_benhan                  ba ON grv.dvtt = ba.dvtt
                                             AND grv.sovaovien = ba.sovaovien
                    JOIN noitru_dotdieutri              ddt ON ddt.stt_benhan = ba.stt_benhan
                                                  AND ddt.dvtt = ba.dvtt
                    LEFT JOIN cmu_sobuonggiuong              giuong ON ddt.dvtt = giuong.dvtt
                                                          AND ddt.stt_benhan = giuong.stt_benhan
                                                          AND giuong.stt_dotdieutri = ddt.stt_dotdieutri
                    JOIN his_public_list.dm_benh_nhan   bn ON ba.mabenhnhan = bn.ma_benh_nhan
                    JOIN his_fw.dm_phongban             pban ON pban.ma_phongban = grv.makhoa
                    JOIN cmu_khoakyso                   khoaks ON khoaks.dvtt = grv.dvtt
                                                AND khoaks.makhoa = grv.makhoa
                                                AND khoaks.manhanvien = p_manhanvien
                                                AND khoaks.loai = 'TRUONGKHOA'
                WHERE
                    grv.dvtt = p_dvtt
                    AND grv.ngaychungsinh BETWEEN v_ngaybatdau AND v_ngaykethuc
                    AND ( ( p_trangthai = 1
                            AND matruongkhoa IS NOT NULL )
                          OR ( matruongkhoa IS NULL
                               AND p_trangthai = 0 ) )
                ORDER BY
                    ngayravien DESC;

RETURN cur;
END;