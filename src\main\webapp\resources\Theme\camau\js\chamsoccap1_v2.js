function veBieuDoChamSocCap1(objectChart) {
    var canvas = document.getElementById('myChartCSC1');
    var ctx = canvas.getContext('2d');
    var canvasWidth = canvas.width;
    var canvasHeight = canvas.height;
    ctx.clearRect(0, 0, canvasWidth, canvasHeight)
    var margin = 10;
    var Hmargin = 20;
    var chartWidth = canvasWidth - 2 * margin;
    var chartHeight = canvasHeight - 2 * Hmargin;
    var barWidth = chartWidth / 24;

    function drawChart() {
        ctx.lineWidth = 1;
        ctx.strokeStyle = '#000000';
        ctx.fillStyle = '#000000';
        ctx.beginPath();
        for (let i = 0; i <= 8; i++) {
            var yPos = Hmargin + i * (chartHeight / 8);
            ctx.beginPath();
            ctx.moveTo(margin, yPos);
            ctx.lineTo(canvasWidth - margin, yPos);
            ctx.stroke();
        }
        for (let i = 0; i <= 24; i++) {
            var xPos = margin + i * barWidth;
            ctx.beginPath();
            ctx.moveTo(xPos, Hmargin);
            ctx.lineTo(xPos, canvasHeight - Hmargin);
            ctx.setLineDash([5, 5]);
            ctx.stroke();
        }
        ctx.stroke();
    }

    function drawMachChart(data) {
        ctx.lineWidth = 1;
        ctx.setLineDash([]);
        ctx.fillStyle = '#ff4136';
        ctx.strokeStyle = '#ff4136';
        var x = 0;
        var y = 0;
        var stepH = (chartHeight / 8);
        for (let i = 0; i < data.length; i++) {
            if(data[i]){
                x = margin + i * barWidth;
                y = canvasHeight - Hmargin - ((data[i]-40)/20) * stepH;
                if (y <= canvasHeight - Hmargin && y >= Hmargin) {
                    ctx.beginPath();
                    ctx.arc(x, y, 4, 0, 2 * Math.PI);
                    ctx.fill();
                }
                if(data[i] <= 40) {
                    ctx.font = "bold 15px Arial";
                    ctx.fillText("M:"+data[i], x - margin, canvasHeight -stepH );
                }
            }
        }
        for (let i = 0; i < data.length; i++) {
            if (data[i]){
                x = margin + i * barWidth;
                y = canvasHeight - Hmargin - ((data[i]-40)/20) * stepH;
                if (i === 0  ) {
                    ctx.moveTo(x, y);
                } else {
                    if (y > canvasHeight - Hmargin) {
                        y = canvasHeight - Hmargin;
                    }
                    ctx.lineTo(x, y);
                }
            }
        }
        ctx.stroke();
    }

    function drawDownArrow(x, y, size, text, draw) {
        ctx.font = "bold 14px Arial";
        ctx.fillStyle = "#09329a";
        ctx.beginPath();
        if (draw == 1) {
            ctx.moveTo(x, y);
            ctx.lineTo(x + size, y - size);
            ctx.moveTo(x, y);
            ctx.lineTo(x - size, y - size);
            ctx.fillText(text, x-margin, y - 10);
        } else {
            ctx.fillText(text, x-margin, y - 5);
        }
        ctx.stroke();
    }

    function drawUpArrow(x, y, size, text, draw) {
        ctx.font = "bold 14px Arial";
        ctx.fillStyle = "#09329a";
        ctx.beginPath();
        if (draw == 1) {
            ctx.moveTo(x, y);
            ctx.lineTo(x + size, y + size);
            ctx.moveTo(x, y);
            ctx.lineTo(x - size, y + size);
            ctx.fillText(text, x - margin, y + 20);
        } else {
            ctx.fillText(text, x - margin, y + 10);
        }
        ctx.stroke();
    }

    function drawHuyetap(dataTamtruong, dataTamthu) {
        var stepH = (chartHeight/8);
        ctx.strokeStyle = '#0074d9';
        ctx.lineWidth = 2;
        for (let i = 0; i < dataTamtruong.length; i++) {
            if (dataTamtruong[i]){
                var xPos = margin + i * barWidth;
                if(dataTamtruong[i] < 20 && dataTamthu[i] < 20) {
                    ctx.beginPath();
                    ctx.fillText(dataTamtruong[i]+"/"+dataTamthu[i], xPos - 10+2, chartHeight  - stepH);
                    ctx.stroke();
                } else {
                    var yTamThu = 0;
                    var drawTamThu = 1;
                    var yTamTruong = 0;
                    var drawTamTruong = 1;
                    if (dataTamthu[i] <= 120) {
                        yTamThu = chartHeight + Hmargin - ((dataTamthu[i]-20)/20) * stepH;
                    } else {
                        var tamThuTemp = dataTamthu[i] - 120;
                        yTamThu = (stepH * 3 + Hmargin) - (tamThuTemp/50) * stepH;
                    }
                    if (dataTamtruong[i] <= 120) {
                        yTamTruong = chartHeight + Hmargin - ((dataTamtruong[i]-20)/20) * stepH;
                    } else {
                        var tamTruongTemp = dataTamtruong[i] - 120;
                        yTamTruong = (stepH * 3 + Hmargin) - (tamTruongTemp/50) * stepH;
                    }
                    if (yTamThu > canvasHeight - Hmargin) {
                        yTamThu = canvasHeight - Hmargin;
                        drawTamThu = 0;
                    }
                    if (yTamThu < Hmargin) {
                        yTamThu = Hmargin;
                        drawTamThu = 0;
                    }
                    if (yTamTruong > canvasHeight - Hmargin) {
                        yTamTruong = canvasHeight - Hmargin;
                        drawTamTruong = 0;
                    }
                    if (yTamTruong < Hmargin) {
                        yTamTruong = Hmargin;
                        drawTamTruong = 0;
                    }
                    drawDownArrow(xPos, yTamThu,8, dataTamthu[i], drawTamThu);
                    drawUpArrow(xPos, yTamTruong, 8, dataTamtruong[i], drawTamTruong);
                    ctx.beginPath();
                    ctx.moveTo(xPos, yTamThu);
                    ctx.lineTo(xPos, yTamTruong);
                    ctx.stroke();
                }
            }
        }
    }

    function drawNhietDoChart(data) {
        ctx.lineWidth = 1;
        ctx.setLineDash([]);
        ctx.fillStyle = '#50008d';
        ctx.strokeStyle = '#50008d';
        var x = 0;
        var y = 0;
        var stepH = (chartHeight / 8);
        for (let i = 0; i < data.length; i++) {
            if (data[i]){
                x = margin + i * barWidth;
                y = canvasHeight - Hmargin - (data[i]-34) * stepH;
                if (y <= canvasHeight - Hmargin && y >= Hmargin) {
                    ctx.beginPath();
                    ctx.fillRect(x-3, y-3, 6, 6);
                }
                if(data[i] < 34) {
                    ctx.font = "bold 15px Arial";
                    ctx.fillText("t:"+data[i], x - margin, canvasHeight - stepH - 25);
                }
            }
        }

        for (let i = 0; i < data.length; i++) {
            if (data[i]){
                x = margin + i * barWidth;
                y = canvasHeight - Hmargin - (data[i]-34) * stepH;
                if (y > canvasHeight - Hmargin) {
                    y = canvasHeight - Hmargin;
                }
                if (i === 0  ) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
        }
        ctx.stroke();
    }

    function drawSPO2Chart(data) {
        ctx.lineWidth = 1;
        ctx.setLineDash([]);
        ctx.fillStyle = '#1c8c00';
        ctx.strokeStyle = '#1c8c00';
        var x = 0;
        var y = 0;
        var stepH = (chartHeight / 8);
        for (let i = 0; i < data.length; i++) {
            if (data[i]){
                x = margin + i * barWidth;
                if(data[i] <= 92){
                    y = canvasHeight - Hmargin - ((data[i]-80)/2) * stepH;
                } else {
                    var itemTemp = data[i] - 92;
                    y = (stepH * 2 + Hmargin) - (itemTemp/4) * stepH;
                }
                if (y <= canvasHeight - Hmargin && y >= Hmargin) {
                    ctx.beginPath();
                    ctx.moveTo(x, y-5);
                    ctx.lineTo(x-5, y+5);
                    ctx.lineTo(x+5, y+5);
                    ctx.fill();
                }
                if(data[i] < 80) {
                    ctx.font = "bold 15px Arial";
                    ctx.fillText("S:"+data[i], x - margin, canvasHeight - stepH - 10);
                }
            }
        }
        for (let i = 0; i < data.length; i++) {
            if(data[i]){
                x = margin + i * barWidth;
                if(data[i] <= 92){
                    y = canvasHeight - Hmargin - ((data[i]-80)/2) * stepH;
                } else {
                    var itemTemp = data[i] - 92;
                    y = (stepH * 2 + Hmargin) - (itemTemp/4) * stepH;
                }
                if (y > canvasHeight - Hmargin) {
                    y = canvasHeight - Hmargin;
                }
                if (y < Hmargin){
                    y = Hmargin;
                }
                if (i === 0  ) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
        }
        ctx.stroke();
    }
    drawChart();
    drawMachChart(objectChart.MACH);
    drawHuyetap(objectChart.HUYET_AP_DUOI, objectChart.HUYET_AP_TREN);
    drawNhietDoChart(objectChart.NHIET_DO);
    drawSPO2Chart(objectChart.SPO2);
}

function getUrlChamSocCap1(dataCSC1, kySo = 0) {
    return new Promise(async (resolve, reject) => {
        getFilesign769(
            "PHIEU_NOITRU_CHAMSOCCAP1",
            dataCSC1.ID_CHAM_SOC_CAP_1,
            -1,//singletonObject.userId,
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            -1,
            function(data) {
                if(data.length > 0 && kySo == 0) {
                    getCMUFileSigned769GetLink(data[0].KEYMINIO, 'pdf').then(pdfData => {
                        resolve({
                            url: pdfData,
                            kySo: 1,
                            isError: 0,
                            message: 'Thành công',
                        });
                    });
                } else {
                    $.get("cmu_getlist?url=" + convertArray([
                        dataCSC1.ID_CHAM_SOC_CAP_1,
                        "CMU_LCSC1_CHITIET_SEL24ROW"
                    ])).done(function(data) {
                        if (data.length > 0) {
                            var dsPdf = [];
                            for(var p = 0; p < Math.ceil(data.length/24); p++){
                                var dataMach = [];
                                var dataHuyetApTren = [];
                                var dataHuyetApDuoi = [];
                                var dataSPO2 = [];
                                var dataNhietDo = [];
                                var objectGio = {};
                                var objectXoayTro = {};
                                var objectDieuDuong = {};
                                var objectMach = {};
                                var objectHuyetAp = {};
                                var objectSPO2 = {};
                                var objectNhietDo = {};
                                var objectIdChiTiet = {};
                                var objectKeySign = {};
                                for(var i = 0; i < 24; i++) {
                                    var item = data[i + p * 24];
                                    if (item) {
                                        var xoaytro = [];
                                        item.XOAY_TRO ? xoaytro.push(item.XOAY_TRO) : '';
                                        item.CHAM_SOC_DIEU_DUONG ? xoaytro.push(item.CHAM_SOC_DIEU_DUONG) : '';
                                        dataMach.push(item.MACH);
                                        dataHuyetApDuoi.push(item.HUYET_AP_DUOI);
                                        dataHuyetApTren.push(item.HUYET_AP_TREN);
                                        dataSPO2.push(item.SPO2);
                                        dataNhietDo.push(item.NHIET_DO);
                                        objectGio['ngay_' + (i+1)] = item.ONLY_NGAY ? item.ONLY_NGAY.split('/').slice(0, 2).join('/') : "";
                                        objectGio['gio_' + (i+1)] = item.ONLY_GIO;
                                        objectXoayTro['xoaytro_' + (i+1)] = xoaytro.join(" - ");
                                        objectDieuDuong['dieuduong_' + (i+1)] = item.TEN_NGUOI_TAO;
                                        objectMach['mach_' + (i+1)] = item.MACH;
                                        objectHuyetAp['huyetap_' + (i + 1)] = (item.HUYET_AP_TREN || '') + '/' + (item.HUYET_AP_DUOI || '');
                                        objectSPO2['spo2_' + (i+1)] = item.SPO2;
                                        objectNhietDo['nhietdo_' + (i+1)] = item.NHIET_DO;
                                        objectIdChiTiet['id_chi_tiet_' + (i+1)] = item.ID_CHI_TIET;
                                        objectKeySign['keysign_' + (i+1)] = item.KEYSIGN;
                                    }
                                }
                                var params = {
                                    dvtt: singletonObject.dvtt,
                                    mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                                    ngayvaovien: thongtinhsba.thongtinbn.NGAYGIO_NHAPVIEN,
                                    ngaydautien: data[0].ONLY_NGAY,
                                    id_phieu: dataCSC1.ID_CHAM_SOC_CAP_1,
                                    id_dang_ky: dataCSC1.ID_DANG_KY || 0,
                                    maxrow: singletonObject.maxRowCSC1,
                                    page: p,
                                    ...objectGio,
                                    ...objectXoayTro,
                                    ...objectDieuDuong,
                                    ...objectMach,
                                    ...objectHuyetAp,
                                    ...objectSPO2,
                                    ...objectNhietDo,
                                    ...objectIdChiTiet,
                                    ...objectKeySign
                                };
                                if (singletonObject.thamSo960605 == 0) {
                                    resolve({
                                        url: 'cmu_in_CMU_CHAMSOCCAP1_KHONGHINH?type=pdf&' + $.param(params),
                                        kySo: 1,
                                        isError: 0,
                                        message: 'Thành công',
                                    });
                                } else {
                                    $("#wrap_canvas").html("<canvas id='myChartCSC1' width='1028' height='584'></canvas>")
                                    veBieuDoChamSocCap1({
                                        MACH: dataMach,
                                        HUYET_AP_TREN: dataHuyetApTren,
                                        HUYET_AP_DUOI: dataHuyetApDuoi,
                                        SPO2: dataSPO2,
                                        NHIET_DO: dataNhietDo
                                    });
                                    var image = $("#myChartCSC1").get(0).toDataURL("image/png").replace("data:image/png;base64,", "");
                                    $.ajax({
                                        url: "cmu_post_CMU_CHART_INS",
                                        type: "POST",
                                        data: {
                                            url: [singletonObject.dvtt, dataCSC1.ID_CHAM_SOC_CAP_1, 'CHAMSOCCAP1', p, image].join("```"),
                                        },
                                        async: false
                                    }).done(function(count) {
                                        if(count > 0) {
                                            resolve({
                                                url: 'cmu_in_CMU_CHAMSOCCAP1_COHINH?type=pdf&' + $.param(params),
                                                kySo: 1,
                                                isError: 0,
                                                message: 'Thành công',
                                            });
                                        }
                                    });
                                }
                            }
                        }
                    });
                }
            }
        );
    });
}
$(function() {
    var formCSC1ThongTinChung,
        formCSC1ChanDoanMucTieu;

    let ttPhieuCSC1SauKhiTao = {};

    $("#lanchamsoccap1_formtaophieu").validate({
        rules: {
            lanchamsoc1_ngaybatdau: {
                required: true,
                validDateTime: true,
                validDateNgayhientai: true,
                validDateNgaynhapvien: true
            },
        }
    });

    $("#modalLanChamSocCap1").on("hidden.bs.modal", function() {
        thongtinhsba.thongtinbn.lanChamSocCap1 = {};
        reloadDSLanChamSocCap1();
    });

    $("#csc1_lammoi").click(function(){
        $("#ttchamsoc-chamsoccap1").click();
    });

    $("#modalTaoLanChamSocCap1").on("show.bs.modal", function() {
        $("#lanchamsoc1_ngaybatdau").val(moment().format('DD/MM/YYYY HH:mm'));
    });

    $("#ttchamsoc-chamsoccap1").click(function(){
        instanceGridChamSocCap1();
        loadConfigChamSocCap1();
    });

    $("#csc1_them").click(function(){
        if (singletonObject.configGroupChamSocCap1.length == 0){
            return notifiToClient("Red", "Vui lòng liên hệ IT để cấu hình nhóm chỉ số chăm sóc cấp 2, 3");
        }
        thongtinhsba.thongtinbn.lanChamSocCap1 = {};
        $("#modalTaoLanChamSocCap1").modal("show");
    });

    $("#lanchamsoc1_action_taolan").click(function() {
        if($("#lanchamsoccap1_formtaophieu").valid()) {
            var idButton = "lanchamsoc1_action_taolan";
            var ngay = $("#lanchamsoc1_ngaybatdau").val();
            var data = thongtinhsba.thongtinbn;
            showSelfLoading(idButton);
            $.post("cmu_post", {url:[singletonObject.dvtt,
                    singletonObject.userId,
                    singletonObject.makhoa,
                    data.SOVAOVIEN,
                    data.SOVAOVIEN_DT,
                    data.MA_BENH_NHAN,
                    data.STT_BENHAN,
                    ngay,
                    "CMU_LANCHAMSOCCAP1_INS"].join("```")}).done(function (data) {
                if(data > 0){
                    ttPhieuCSC1SauKhiTao.ID_CHAM_SOC_CAP_1 = data;
                    $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, data, 'CMU_LANCHAMSOCCAP1_SEL_ID'])).done(function(dataLog){
                        luuLogHSBATheoBN({
                            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                            LOAI: LOGHSBALOAI.CHAMSOCCAP1.KEY,
                            NOIDUNGBANDAU: "Tạo đợt chăm sóc cấp 1 tờ số: " + dataLog[0].TO_SO,
                            NOIDUNGMOI: "",
                            USERID: singletonObject.userId,
                            ACTION: LOGHSBAACTION.INSERT.KEY,
                        });
                    });
                    $("#modalTaoLanChamSocCap1").modal("hide");
                    createFormChamSocCap1({ID_CHAM_SOC_CAP_1: data});
                } else {
                    notifiToClient("Red",MESSAGEAJAX.ERROR);
                }
            }).fail(function(error) {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }).always(function() {
                hideSelfLoading(idButton);
            });
        }
    });

    $("#lanchamsoccap1_action_luu").click(function() {
        if(formCSC1ThongTinChung.checkValidity()) {
            var dataSubmit = formCSC1ThongTinChung.submission.data;
            var idButton = "lanchamsoccap1_action_luu";
            showSelfLoading(idButton);
            $.post("cmu_post", {url:[dataSubmit.ID_CHAM_SOC_CAP_1,
                    dataSubmit.TO_SO,
                    dataSubmit.SO_GIUONG,
                    dataSubmit.CAN_NANG,
                    dataSubmit.IS_DI_UNG,
                    dataSubmit.STRING_DI_UNG,
                    dataSubmit.ICD_CHANDOAN,
                    dataSubmit.TENICD_CHANDOAN,
                    "CMU_LANCHAMSOCCAP1_UPD"].join("```")}).done(function (data) {
                if(data > 0){
                    luuLogHSBAChinhSua(singletonObject.thongTinCSC1TruocKhiChinhSua, dataSubmit, LOGHSBALOAI.CHAMSOCCAP1.KEY, keyLuuLog);
                    notifiToClient("Green",MESSAGEAJAX.ADD_SUCCESS);
                    createFormChamSocCap1(dataSubmit);
                } else {
                    notifiToClient("Red",MESSAGEAJAX.ERROR);
                }
            }).fail(function(error) {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }).always(function() {
                hideSelfLoading(idButton);
            });
        }
    });

    $("#lanchamsoc1_action_ketthuclan").click(function() {
        var ngay = moment($("#lanchamsoc1_ngayketthuc").val(), "DD/MM/YYYY HH:mm");
        var ret = getThongtinRowSelected("ttcs_list_chamsoccap1");
        var ngayTemp = moment(ret.NGAY_KET_THUC_TEMP, "DD/MM/YYYY HH:mm");
        if (ngay < ngayTemp) {
            return notifiToClient("Red", "Ngày kết thúc không được nhỏ hơn " + ret.NGAY_KET_THUC_TEMP);
        }
        $.ajax({
            url: "cmu_post",
            method: "POST",
            async: false,
            data: {
                url:[
                    ret.ID_CHAM_SOC_CAP_1,
                    $("#lanchamsoc1_ngayketthuc").val(),
                    "CMU_LANCHAMSOCCAP1_KETTHUC"
                ].join("```")
            },
            success: function (countIns) {
                if(countIns > 0){
                    luuLogHSBATheoBN({
                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                        LOAI: LOGHSBALOAI.CHAMSOCCAP1.KEY,
                        NOIDUNGBANDAU: "",
                        NOIDUNGMOI: "Kết thúc đợt chăm sóc cấp 1 tờ số: " + ret.TO_SO + " - " + $("#lanchamsoc1_ngayketthuc").val(),
                        USERID: singletonObject.userId,
                        ACTION: LOGHSBAACTION.EDIT.KEY,
                    });
                    notifiToClient("Green",MESSAGEAJAX.EDIT_SUCCESS);
                    $("#modalKetThucLanChamSocCap1").modal("hide");
                    $("#ttchamsoc-chamsoccap1").click();
                } else {
                    notifiToClient("Red",MESSAGEAJAX.ERROR);
                }
            }
        });
    });

    $("#lanchamsoccap1_action_xem").click(function() {
        var ret = getThongtinRowSelected("ttcs_list_chamsoccap1");
        ret = $.isEmptyObject(ret) ? ttPhieuCSC1SauKhiTao : ret;
        xemChamSocCap1(ret);
    });

    function instanceGridChamSocCap1() {
        var list = $("#ttcs_list_chamsoccap1");
        if(!list[0].grid) {
            list.jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 450,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {
                        name: "TT",
                        label: "TT",
                        align: 'left',
                        width: 120,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.NGAY_KET_THUC) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: green">Kết thúc</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: red">Đang điều trị</span>';
                            }
                        }
                    },
                    {label: "KEYSIGN",name: 'KEYSIGN', index: 'KEYSIGN', width: 50, hidden: true},
                    {label: "ID_CHAM_SOC_CAP_1",name: 'ID_CHAM_SOC_CAP_1', index: 'ID_CHAM_SOC_CAP_1', width: 50, hidden: true},
                    {label: "MA_KHOA_TAO_PHIEU",name: 'MA_KHOA_TAO_PHIEU', index: 'MA_KHOA_TAO_PHIEU', width: 10, hidden: true},
                    {label: "Tờ",name: 'TO_SO', index: 'TO_SO', width: 50, align: 'center'},
                    {label: "Chẩn đoán",name: 'CHAN_DOAN', index: 'CHAN_DOAN', width: 400,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "Giường",name: 'SO_GIUONG', index: 'SO_GIUONG', width: 70},
                    {label: "C.Nặng",name: 'CAN_NANG', index: 'CAN_NANG', width: 70},
                    {label: "Dị ứng",name: 'DI_UNG_SHOW', index: 'DI_UNG_SHOW', width: 300, hidden: true},
                    {label: "IS_DI_UNG",name: 'IS_DI_UNG', index: 'IS_DI_UNG', width: 150, hidden: true},
                    {label: "STRING_DI_UNG",name: 'STRING_DI_UNG', index: 'STRING_DI_UNG', width: 150, hidden: true},
                    {label: "Người tạo phiếu",name: 'TEN_NGUOI_TAO_PHIEU', index: 'TEN_NGUOI_TAO_PHIEU', width: 150},
                    {label: "Ngày bắt đầu",name: 'NGAY_TAO_PHIEU', index: 'NGAY_TAO_PHIEU', width: 150},
                    {label: "Ngày kết thúc",name: 'NGAY_KET_THUC', index: 'NGAY_KET_THUC', width: 150},
                    {label: "NGAY_KET_THUC_TEMP",name: 'NGAY_KET_THUC_TEMP', index: 'NGAY_KET_THUC_TEMP', width: 150, hidden: true},
                ],
                rowNum: 1000000,
                caption: "Danh sách đợt chăm sóc cấp 1",
                onSelectRow: function (id) {
                },
                onRightClickRow: function(id) {
                    if (id) {
                        var ret = getThongtinRowSelected("ttcs_list_chamsoccap1");
                        var items = {
                            "xem": {name: '<p class="text-primary"><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                        }
                        if (ret.NGAY_KET_THUC) {
                            items = {
                                ...items,
                                "huyketthuc": {name: '<p class="text-danger"><i class="fa fa-ban text-danger" aria-hidden="true"></i> Huỷ kết thúc</p>'},
                            }
                        } else {
                            items = {
                                ...items,
                                "ketthuc": {name: '<p class="text-success"><i class="fa fa-check text-success" aria-hidden="true"></i> Kết thúc</p>'},
                                "capnhat": {name: '<p class="text-success"><i class="fa fa-pencil-square-o text-success" aria-hidden="true"></i> Cập nhật</p>'},
                                "xoa": {name: '<p class="text-danger"><i class="fa fa-remove text-danger" aria-hidden="true"></i> Xoá</p>'},
                            }
                        }
                        $.contextMenu('destroy', '#ttcs_list_chamsoccap1 tr');
                        $.contextMenu({
                            selector: '#ttcs_list_chamsoccap1 tr',
                            reposition : false,
                            callback: function (key, options) {
                                if(key == 'capnhat') {
                                    createFormChamSocCap1(ret);
                                }
                                if(key == 'xoa'){
                                    confirmToClient("Xác nhận xóa đợt chăm sóc cấp 1", function (confirm) {
                                        $.ajax({
                                            url: "cmu_post",
                                            method: "POST",
                                            data: {url:[ret.ID_CHAM_SOC_CAP_1, "CMU_LANCHAMSOCCAP1_DEL"].join("```")},
                                            success: function (data) {
                                                if(data > 0){
                                                    luuLogHSBATheoBN({
                                                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                        LOAI: LOGHSBALOAI.CHAMSOCCAP1.KEY,
                                                        NOIDUNGBANDAU: "",
                                                        NOIDUNGMOI: "Xoá đợt chăm sóc cấp 1 tờ số: " + ret.TO_SO,
                                                        USERID: singletonObject.userId,
                                                        ACTION: LOGHSBAACTION.DELETE.KEY,
                                                    });
                                                    notifiToClient("Green",MESSAGEAJAX.DEL_SUCCESS);
                                                    $("#ttchamsoc-chamsoccap1").click();
                                                } else if (data == '-1'){
                                                    notifiToClient("Red", "Vui lòng xoá tất cả chi tiết đợt chăm sóc.");
                                                } else {
                                                    notifiToClient("Red",MESSAGEAJAX.ERROR);
                                                }
                                            },
                                            error: function (error) {
                                                notifiToClient("Red",MESSAGEAJAX.ERROR);
                                            }
                                        });
                                    });
                                }
                                if (key == 'xem'){
                                    xemChamSocCap1(ret);
                                }
                                if (key == 'ketthuc') {
                                    if (!ret.NGAY_KET_THUC_TEMP) {
                                        return notifiToClient("Red", "Đợt chăm sóc cấp 1 chưa cập nhật chi tiết.");
                                    }
                                    $("#lanchamsoc1_ngayketthuc").val(ret.NGAY_KET_THUC_TEMP);
                                    $("#modalKetThucLanChamSocCap1").modal("show");
                                }
                                if (key == 'kyso') {
                                    xemChamSocCap1(ret, 1);
                                }
                                if (key == 'huykyso') {
                                    confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                        huykysoFilesign769("PHIEU_NOITRU_CHAMSOCCAP1", ret.ID_CHAM_SOC_CAP_1, singletonObject.userId, singletonObject.dvtt,
                                            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                                                $("#ttchamsoc-chamsoccap1").click();
                                            })
                                    }, function () {

                                    })
                                }
                                if (key == 'huyketthuc') {
                                    confirmToClient("Xác nhận huỷ kết thúc đợt chăm sóc cấp 1", function (confirm) {
                                        $.ajax({
                                            url: "cmu_post",
                                            method: "POST",
                                            async: false,
                                            data: {
                                                url:[
                                                    ret.ID_CHAM_SOC_CAP_1,
                                                    "CMU_LANCHAMSOCCAP1_HUYKETTHUC"
                                                ].join("```")
                                            },
                                            success: function (countIns) {
                                                if(countIns > 0){
                                                    luuLogHSBATheoBN({
                                                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                        LOAI: LOGHSBALOAI.CHAMSOCCAP1.KEY,
                                                        NOIDUNGBANDAU: "",
                                                        NOIDUNGMOI: "Huỷ kết thúc đợt chăm sóc cấp 1 tờ số: " + ret.TO_SO,
                                                        USERID: singletonObject.userId,
                                                        ACTION: LOGHSBAACTION.EDIT.KEY,
                                                    });
                                                    notifiToClient("Green",MESSAGEAJAX.EDIT_SUCCESS);
                                                    $("#ttchamsoc-chamsoccap1").click();
                                                } else {
                                                    notifiToClient("Red",MESSAGEAJAX.ERROR);
                                                }
                                            }
                                        });
                                    });
                                }
                            },
                            items: items
                        });
                    }
                }
            });
        }
        reloadDSLanChamSocCap1();
    }
    function reloadDSLanChamSocCap1() {
        var data = thongtinhsba.thongtinbn;
        var url = "cmu_getlist?url=" + convertArray([singletonObject.dvtt, data.MABENHNHAN, data.SOVAOVIEN, data.SOVAOVIEN_DT, "CMU_LANCHAMSOCCAP1_SEL"]);
        $("#ttcs_list_chamsoccap1").jqGrid('setGridParam', {
            datatype: 'json',
            url: url
        }).trigger('reloadGrid')
    }

    function createFormChamSocCap1(dataID) {
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, dataID.ID_CHAM_SOC_CAP_1, 'CMU_LANCHAMSOCCAP1_SEL_ID'])).done(function(data){
            if(data.length > 0) {
                thongtinhsba.thongtinbn.lanChamSocCap1 = data[0];
                singletonObject.thongTinCSC1TruocKhiChinhSua = JSON.parse(JSON.stringify(data[0]));
                Formio.createForm(document.getElementById('formCSC1ThongTinChung'),
                    {
                        "display": "form",
                        "components": [
                            {
                                label: "",
                                key: "wrap_benhchinh",
                                columns: [
                                    {
                                        "components": [
                                            {
                                                "label": "Tờ số",
                                                "key": "TO_SO",
                                                "type": "textfield",
                                                customClass: "pr-2",
                                                validate: {
                                                    required: true
                                                },
                                            },
                                        ],
                                        "width": 4,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "Số giường",
                                                "key": "SO_GIUONG",
                                                "type": "textfield",
                                                customClass: "pr-2",
                                                "placeholder": "Số giường",
                                            },
                                        ],
                                        "width": 4,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "Cân nặng",
                                                "key": "CAN_NANG",
                                                "type": "textfield",
                                                validate: {
                                                    // required: true
                                                },
                                                "placeholder": "Cân nặng",
                                            },
                                        ],
                                        "width": 4,
                                        "size": "md",
                                    },

                                ],
                                "customClass": "ml-0 mr-0",
                                "type": "columns",
                            },
                            {
                                label: "",
                                key: "wrap_benhchinh",
                                columns: [
                                    {
                                        "components": [
                                            {
                                                "tag": "label",
                                                "content": "Chẩn đoán",
                                                "key": "htmllabel",
                                                "type": "htmlelement",
                                            },
                                        ],
                                        "width": 12,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "",
                                                "key": "ICD_CHANDOAN",
                                                "type": "textfield",
                                                customClass: "pr-2",
                                                validate: {
                                                    required: true
                                                },
                                                "placeholder": "ICD",
                                            },
                                        ],
                                        "width": 2,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "",
                                                "key": "TENICD_CHANDOAN",
                                                "type": "textfield",
                                                customClass: "",
                                                validate: {
                                                    required: true
                                                },
                                                "placeholder": "Tên bệnh chính",
                                            },
                                        ],
                                        "width": 10,
                                        "size": "md",
                                    },

                                ],
                                "customClass": "ml-0 mr-0",
                                "type": "columns",
                            },
                            {
                                label: "",
                                key: "wrap_benhchinh",
                                columns: [
                                    {
                                        "components": [
                                            {
                                                "tag": "label",
                                                "content": "Dị ứng",
                                                "key": "htmllabel",
                                                "type": "htmlelement",
                                            },
                                        ],
                                        "width": 12,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "",
                                                "key": "IS_DI_UNG",
                                                "type": "select",
                                                "data": {
                                                    "values": [
                                                        {
                                                            "label": "Không",
                                                            "value": '0'
                                                        },
                                                        {
                                                            "label": "Có",
                                                            "value": '1'
                                                        },
                                                    ]
                                                },
                                                "defaultValue": "0",
                                                "customClass": "pr-2",
                                                validate: {
                                                    required: true
                                                }
                                            },
                                        ],
                                        "width": 2,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "",
                                                "key": "STRING_DI_UNG",
                                                "type": "textfield",
                                                "placeholder": "Dị ứng",
                                                "customConditional": "show = (data.IS_DI_UNG == '1' ? 1 : 0);",
                                            },
                                        ],
                                        "width": 10,
                                        "size": "md",
                                    },

                                ],
                                "customClass": "ml-0 mr-0",
                                "type": "columns",
                            },
                        ]
                    }
                ).then(function (form) {
                    formCSC1ThongTinChung = form;
                    var tenBenhchinhElement = form.getComponent('TENICD_CHANDOAN');
                    var icdBenhchinhElement = form.getComponent('ICD_CHANDOAN');
                    actionICDBenhChinh("ICD_CHANDOAN", "TENICD_CHANDOAN", form);
                    combgridTenICD(getIdElmentFormio(form,'TENICD_CHANDOAN'), function(item) {
                        icdBenhchinhElement.setValue(item.ICD);
                        tenBenhchinhElement.setValue(item.MO_TA_BENH_LY);
                    });

                    // var icdChanDoanDieuDuongElement = form.getComponent('ICD_DIEUDUONG');
                    // var tenICDChanDoanDieuDuongElement = form.getComponent('TENICD_DIEUDUONG');
                    // actionICDBenhChinh("ICD_DIEUDUONG", "TENICD_DIEUDUONG", form);
                    // combgridTenICD(getIdElmentFormio(form,'TENICD_DIEUDUONG'), function(item) {
                    //     icdChanDoanDieuDuongElement.setValue(item.ICD);
                    //     tenICDChanDoanDieuDuongElement.setValue(item.MO_TA_BENH_LY);
                    // });
                    //
                    // instanceGridChamSocCap1ChanDoanDieuDuong();
                    // $("#"+getIdElmentFormio(form,'TENICD_DIEUDUONG')).on('keypress', function(event) {
                    //     if(event.keyCode == 13) {
                    //         if (form.submission.data.TENICD_DIEUDUONG){
                    //             var dataSubmit = form.submission.data;
                    //             $.post("cmu_post", {url:[thongtinhsba.thongtinbn.lanChamSocCap1.ID_CHAM_SOC_CAP_1,
                    //                     dataSubmit.ICD_DIEUDUONG,
                    //                     dataSubmit.TENICD_DIEUDUONG,
                    //                     singletonObject.userId,
                    //                     singletonObject.makhoa,
                    //                     "CMU_LCSC1_CHANDOAN_INS"].join("```")}).done(function (id) {
                    //                 if(id > 0){
                    //                     luuLogHSBATheoBN({
                    //                         SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    //                         LOAI: LOGHSBALOAI.CHAMSOCCAP1CHANDOAN.KEY,
                    //                         NOIDUNGBANDAU: "Thêm chẩn đoán điều dưỡng/lượng giá mục tiêu '" + dataSubmit.ICD_DIEUDUONG + " - " + dataSubmit.TENICD_DIEUDUONG + "' tờ chăm sóc cấp 1 số: " + thongtinhsba.thongtinbn.lanChamSocCap1.TO_SO,
                    //                         NOIDUNGMOI: "",
                    //                         USERID: singletonObject.userId,
                    //                         ACTION: LOGHSBAACTION.INSERT.KEY,
                    //                     });
                    //                     releadGridChamSocCap1ChanDoanDieuDuong();
                    //                     icdChanDoanDieuDuongElement.setValue("");
                    //                     tenICDChanDoanDieuDuongElement.setValue("");
                    //                     icdChanDoanDieuDuongElement.focus();
                    //                 } else if (id == -1) {
                    //                     notifiToClient("Red", "ICD đã tồn tại");
                    //                 } else if (id == -2) {
                    //                     notifiToClient("Red", "Chỉ được nhập tối đa 4 chẩn đoán");
                    //                 } else {
                    //                     notifiToClient("Red",MESSAGEAJAX.ERROR);
                    //                 }
                    //             }).fail(function(error) {
                    //                 notifiToClient("Red",MESSAGEAJAX.ERROR);
                    //             });
                    //         } else {
                    //             notifiToClient("Red","Vui lòng chọn ICD trước")
                    //         }
                    //     }
                    // });
                    //
                    // instanceGridChamSocCap1GhiChuBanGiao();
                    // var ghiChuElement = form.getComponent('GHI_CHU_BAN_GIAO');
                    // $("#"+getIdElmentFormio(form,'GHI_CHU_BAN_GIAO')).on('keypress', function(event) {
                    //     if(event.keyCode == 13) {
                    //         if (form.submission.data.GHI_CHU_BAN_GIAO){
                    //             var dataSubmit = form.submission.data;
                    //             $.post("cmu_post", {url:[thongtinhsba.thongtinbn.lanChamSocCap1.ID_CHAM_SOC_CAP_1,
                    //                     dataSubmit.GHI_CHU_BAN_GIAO,
                    //                     singletonObject.userId,
                    //                     singletonObject.makhoa,
                    //                     "CMU_LCSC1_GHICHU_INS"].join("```")}).done(function (id) {
                    //                 if(id > 0){
                    //                     luuLogHSBATheoBN({
                    //                         SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    //                         LOAI: LOGHSBALOAI.CHAMSOCCAP1GHICHU.KEY,
                    //                         NOIDUNGBANDAU: "Thêm ghi chú/bàn giao '" + dataSubmit.GHI_CHU_BAN_GIAO + "' tờ chăm sóc cấp 1 số: " + thongtinhsba.thongtinbn.lanChamSocCap1.TO_SO,
                    //                         NOIDUNGMOI: "",
                    //                         USERID: singletonObject.userId,
                    //                         ACTION: LOGHSBAACTION.INSERT.KEY,
                    //                     });
                    //                     releadGridChamSocCap1GhiChuBanGiao();
                    //                     ghiChuElement.setValue("");
                    //                     ghiChuElement.focus();
                    //                 } else {
                    //                     notifiToClient("Red",MESSAGEAJAX.ERROR);
                    //                 }
                    //             }).fail(function(error) {
                    //                 notifiToClient("Red",MESSAGEAJAX.ERROR);
                    //             });
                    //         } else {
                    //             notifiToClient("Red","Vui lòng chọn ICD trước")
                    //         }
                    //     }
                    // });
                    //
                    // instanceGridChamSocCap1QuyUocKyHieu();
                    // var quyUocElement = form.getComponent('QUY_UOC_KY_HIEU');
                    // $("#"+getIdElmentFormio(form,'QUY_UOC_KY_HIEU')).on('keypress', function(event) {
                    //     if(event.keyCode == 13) {
                    //         if (form.submission.data.QUY_UOC_KY_HIEU){
                    //             var dataSubmit = form.submission.data;
                    //             $.post("cmu_post", {url:[thongtinhsba.thongtinbn.lanChamSocCap1.ID_CHAM_SOC_CAP_1,
                    //                     dataSubmit.QUY_UOC_KY_HIEU,
                    //                     singletonObject.userId,
                    //                     singletonObject.makhoa,
                    //                     "CMU_LCSC1_QUYUOC_INS"].join("```")}).done(function (id) {
                    //                 if(id > 0){
                    //                     luuLogHSBATheoBN({
                    //                         SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    //                         LOAI: LOGHSBALOAI.CHAMSOCCAP1QUYUOC.KEY,
                    //                         NOIDUNGBANDAU: "Thêm quy ước ký hiệu '" + dataSubmit.QUY_UOC_KY_HIEU + "' tờ chăm sóc cấp 1 số: " + thongtinhsba.thongtinbn.lanChamSocCap1.TO_SO,
                    //                         NOIDUNGMOI: "",
                    //                         USERID: singletonObject.userId,
                    //                         ACTION: LOGHSBAACTION.INSERT.KEY,
                    //                     });
                    //                     releadGridChamSocCap1QuyUocKyHieu();
                    //                     quyUocElement.setValue("");
                    //                     quyUocElement.focus();
                    //                 } else {
                    //                     notifiToClient("Red",MESSAGEAJAX.ERROR);
                    //                 }
                    //             }).fail(function(error) {
                    //                 notifiToClient("Red",MESSAGEAJAX.ERROR);
                    //             });
                    //         }
                    //     }
                    // });
                    var giuong;
                    layThongTinBuongGiuong(thongtinhsba.thongtinbn.STT_BENHAN, thongtinhsba.thongtinbn.STT_DOTDIEUTRI, function(result) {
                        giuong = result.so_giuong;
                        form.submission =  {
                            data: {
                                ...form.submission.data,
                                giuong: giuong
                            }
                        };
                    });
                    thongtinhsba.thongtinbn.lanChamSocCap1.CAN_NANG = thongtinhsba.thongtinbn.lanChamSocCap1.CAN_NANG ? thongtinhsba.thongtinbn.lanChamSocCap1.CAN_NANG : thongtinhsba.thongtinbn.CANNANG;
                    thongtinhsba.thongtinbn.lanChamSocCap1.SO_GIUONG = thongtinhsba.thongtinbn.lanChamSocCap1.SO_GIUONG ? thongtinhsba.thongtinbn.lanChamSocCap1.SO_GIUONG : giuong;
                    thongtinhsba.thongtinbn.lanChamSocCap1.TENICD_CHANDOAN = thongtinhsba.thongtinbn.lanChamSocCap1.TENICD_CHANDOAN ? thongtinhsba.thongtinbn.lanChamSocCap1.TENICD_CHANDOAN : thongtinhsba.thongtinbn.ICD_HT.split('-')[1];
                    thongtinhsba.thongtinbn.lanChamSocCap1.ICD_CHANDOAN = thongtinhsba.thongtinbn.lanChamSocCap1.ICD_CHANDOAN ? thongtinhsba.thongtinbn.lanChamSocCap1.ICD_CHANDOAN : thongtinhsba.thongtinbn.ICD_HT.split('-')[0];
                    form.submission =  {
                        data: {
                            ...thongtinhsba.thongtinbn.lanChamSocCap1
                        }
                    };

                    $("#modalLanChamSocCap1").modal("show");
                });
            }
        });
    }

    function xemChamSocCap1(dataCSC1, kySo = 0) {
        getUrlChamSocCap1(dataCSC1, kySo).then(objReturn => {
            if (objReturn.isError == 0) {
                previewPdfDefaultModal(objReturn.url, 'td_frame_inphieutruyendich');
            } else {
                notifiToClient("Red", objReturn.message);
            }
        }).catch(error => {
            notifiToClient("Red", error.message || "Lỗi không xác định");
        });
    }

    function loadConfigChamSocCap1() {
        $("#dli_action_them").show();
        $("#dli_action_them_csc2").hide();
        $.get("cmu_getlist?url=" + convertArray([singletonObject.dvtt, "-1", "CMU_CHAMSOCCAP1_CONFIG_GET"])).done(function(data) {
            if (data.length > 0) {
                singletonObject.configChamSocCap1 = data;
                $.ajax({
                    url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, "-1", "CMU_CSC1_GROUP_CONFIG_GET"]),
                    type: 'GET',
                    async: false
                }).done(function (data) {
                    if (data.length > 0) {
                        singletonObject.configGroupChamSocCap1 = data;
                        var arrJsonGroup = [];
                        for(var i=0; i < data.length; i++) {
                            arrJsonGroup.push(genJsonGroupCSC1({
                                "labelGroup": data[i].TENHIENTHI,
                                "key": data[i].LOAI,
                            }));
                        }
                        singletonObject.jsonCSC1 = {
                            "display": "form",
                            "components": [
                                {
                                    "customClass": "mr-0 ml-0",
                                    "key": "columns1",
                                    "type": "columns",
                                    "label": "Columns",
                                    "columns": arrJsonGroup
                                }
                            ]
                        }
                    } else {
                        notifiToClient("Red", "Vui lòng liên hệ IT để cấu hình nhóm chỉ số chăm sóc cấp 1");
                    }
                });
            }
        });

        singletonObject.maxRowCSC1 = 32;
        if (singletonObject.thamSo960605 == 0) {
            singletonObject.maxRowCSC1 = 40;
        }

        $.get("cmu_getlist?url=" + convertArray([singletonObject.dvtt, singletonObject.makhoa, "-1", "CMU_CSC1_DATALIST_ITEM_SEL"])).done(function(data) {
            if (data && data.length > 0) {
                singletonObject.danhSachItemChamSocCap1 = data.map(function(item) {
                    return {
                        label: item.TEN_ITEM,
                        value: item.TEN_ITEM,
                        ghiChu: item.MOTA_ITEM,
                        loai: item.LOAI_ITEM,
                    }
                })
            }
        });
    }
});