package BaocaoBHXH.xmlCV130;

import BaocaoBHXH.xmlCV130.dto.*;
import freemarker.template.Template;
import freemarker.template.TemplateNumberModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Created at 08:54:23 February 22, 2023,
 */
@Service
public class XmlCV130Service {

    @Autowired
    private XmlCV130DAO xmlCV130DAO;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private dmthamsodonvi.thamsodonviDAO thamsodonviDAO;

    private final XmlCV130TemplateFactory xmlCV130TemplateFactory = new XmlCV130TemplateFactory();

    private String base64(String input) {
        byte[] inputBytes = input.getBytes(StandardCharsets.UTF_8);
        return Base64.getEncoder().encodeToString(inputBytes);
    }

    private <T extends BaseSTTXml> void addFileHoSo(List<GiamDinhHS.FileHoSo> listToAdd, TemplateType templateType, List<T> data, AtomicInteger stt, boolean maHoa) {
        if (data == null || data.isEmpty()) {
            return;
        }
        Map<String, Object> model = new HashMap<>();
        model.put("data", data);
        model.put("stt", (TemplateNumberModel) stt::getAndIncrement);
        GiamDinhHS.FileHoSo fileHoSo = generateFileHoSo(templateType, model, maHoa);
        listToAdd.add(fileHoSo);
    }

    private <T extends BaseXml> void addFileHoSo(List<GiamDinhHS.FileHoSo> listToAdd, TemplateType templateType, List<T> data, boolean maHoa) {
        if (data == null || data.isEmpty()) {
            return;
        }
        Map<String, Object> model = new HashMap<>();
        model.put("data", data);
        GiamDinhHS.FileHoSo fileHoSo = generateFileHoSo(templateType, model, maHoa);
        listToAdd.add(fileHoSo);
    }

    private <T extends BaseXml> void addFileHoSoFirstElement(List<GiamDinhHS.FileHoSo> listToAdd, TemplateType templateType, List<T> data, boolean maHoa) {
        if (data == null || data.isEmpty()) {
            return;
        }
        GiamDinhHS.FileHoSo fileHoSo = generateFileHoSo(templateType, data.get(0), maHoa);
        listToAdd.add(fileHoSo);
    }

    private GiamDinhHS.FileHoSo generateFileHoSo(TemplateType templateType, Object model, boolean maHoa) {
        GiamDinhHS.FileHoSo xml = new GiamDinhHS.FileHoSo(templateType.name());
        Template template = xmlCV130TemplateFactory.getTemplate(templateType, request);
        String noiDung = xmlCV130TemplateFactory.processTemplate(template, model);
        if (maHoa) {
            xml.setNOIDUNGFILE(base64(noiDung));
        }
        else {
            xml.setNOIDUNGFILE(noiDung);
        }
        return xml;
    }

    private <T extends BaseXml> Map<String, List<T>> groupByMaLk(List<T> list) {
        if (list == null) {
            return new HashMap<>();
        }
        return list.stream()
                .collect(Collectors.groupingBy(BaseXml::getMA_LK));
    }

    public String exportXmlCV130(XmlCV130Model model, XuatXmlCv130Option option) {
        String dvtt = model.getDvtt();
        boolean maHoa = model.getMaHoa();

        List<Xml1> b1List = model.getB1List();

        Map<String, List<Xml2>> b2Group = groupByMaLk(model.getB2List());
        Map<String, List<Xml3>> b3Group = groupByMaLk(model.getB3List());
        Map<String, List<Xml4>> b4Group = groupByMaLk(model.getB4List());
        Map<String, List<Xml5>> b5Group = groupByMaLk(model.getB5List());
        Map<String, List<Xml6>> b6Group = groupByMaLk(model.getB6List());
        Map<String, List<Xml7>> b7Group = groupByMaLk(model.getB7List());
        Map<String, List<Xml8>> b8Group = groupByMaLk(model.getB8List());
        Map<String, List<Xml9>> b9Group = groupByMaLk(model.getB9List());
        Map<String, List<Xml10>> b10Group = groupByMaLk(model.getB10List());
        Map<String, List<Xml11>> b11Group = groupByMaLk(model.getB11List());
        Map<String, List<Xml13>> b31Group = groupByMaLk(model.getB13List());
        Map<String, List<Xml14>> b14Group = groupByMaLk(model.getB14List());
        Map<String, List<Xml15>> b15Group = groupByMaLk(model.getB15List());

        String maDonViMoi = xmlCV130DAO.layMaDonViMoi(option.getSoVaoVien(), option.getSoVaoVienDt(),dvtt, option.getLoaiKcb());

        GiamDinhHS giamDinhHoSo = new GiamDinhHS();
        giamDinhHoSo.setMACSKCB(maDonViMoi);
        giamDinhHoSo.setNGAYLAP(new SimpleDateFormat("yyyyMMdd").format(Calendar.getInstance().getTime()));
        giamDinhHoSo.setSOLUONGHOSO(b1List.size() + "");

        AtomicInteger sttB1 = new AtomicInteger(1);
        AtomicInteger sttB2 = new AtomicInteger(1);
        AtomicInteger sttB3 = new AtomicInteger(1);
        AtomicInteger sttB4 = new AtomicInteger(1);
        AtomicInteger sttB5 = new AtomicInteger(1);
        AtomicInteger sttB15 = new AtomicInteger(1);
        List<GiamDinhHS.HoSo> dsHoSo = b1List.stream()
                .map(b1 -> {
                    List<GiamDinhHS.FileHoSo> fileHoSoList = new ArrayList<>();
                    String maLk = b1.getMA_LK();
                    b1.setSTT(sttB1.getAndIncrement() + "");

                    GiamDinhHS.FileHoSo hoSoXml1 = generateFileHoSo(TemplateType.XML1, b1, maHoa);
                    fileHoSoList.add(hoSoXml1);

                    addFileHoSo(fileHoSoList, TemplateType.XML2, b2Group.get(maLk), sttB2, maHoa);
                    addFileHoSo(fileHoSoList, TemplateType.XML3, b3Group.get(maLk), sttB3, maHoa);
                    addFileHoSo(fileHoSoList, TemplateType.XML4, b4Group.get(maLk), sttB4, maHoa);
                    addFileHoSo(fileHoSoList, TemplateType.XML5, b5Group.get(maLk), sttB5, maHoa);
                    addFileHoSo(fileHoSoList, TemplateType.XML6, b6Group.get(maLk), maHoa);
                    addFileHoSoFirstElement(fileHoSoList, TemplateType.XML7, b7Group.get(maLk), maHoa);
                    addFileHoSoFirstElement(fileHoSoList, TemplateType.XML8, b8Group.get(maLk), maHoa);
                    addFileHoSo(fileHoSoList, TemplateType.XML9, b9Group.get(maLk), maHoa);
                    addFileHoSoFirstElement(fileHoSoList, TemplateType.XML10, b10Group.get(maLk), maHoa);
                    addFileHoSoFirstElement(fileHoSoList, TemplateType.XML11, b11Group.get(maLk), maHoa);
                    addFileHoSoFirstElement(fileHoSoList, TemplateType.XML13, b31Group.get(maLk), maHoa);
                    addFileHoSoFirstElement(fileHoSoList, TemplateType.XML14, b14Group.get(maLk), maHoa);
                    addFileHoSo(fileHoSoList, TemplateType.XML15, b15Group.get(maLk), sttB15, maHoa);

                    return new GiamDinhHS.HoSo(fileHoSoList);
                })
                .collect(Collectors.toList());

        giamDinhHoSo.setDANHSACHHOSO(dsHoSo);

        Template template = xmlCV130TemplateFactory.getTemplate(TemplateType.GIAMDINHHS, request);
        return xmlCV130TemplateFactory.processTemplate(template, giamDinhHoSo);
    }

    public String exportXmlCV130(XuatXmlCv130Option option, boolean maHoa) {
        XmlCV130Model model = XmlCV130Model.builder()
                .dvtt(option.getDvtt())
                .maHoa(maHoa)
                .b1List(xmlCV130DAO.getXml1CV130List(option))
                .b2List(xmlCV130DAO.getXml2CV130List(option))
                .b3List(xmlCV130DAO.getXml3CV130List(option))
                .b4List(xmlCV130DAO.getXml4CV130List(option))
                .b5List(xmlCV130DAO.getXml5CV130List(option))
                .b6List(xmlCV130DAO.getXml6CV130List(option))
                .b7List(xmlCV130DAO.getXml7CV130List(option))
                .b8List(xmlCV130DAO.getXml8CV130List(option))
                .b9List(xmlCV130DAO.getXml9CV130List(option))
                .b10List(xmlCV130DAO.getXml10CV130List(option))
                .b11List(xmlCV130DAO.getXml11CV130List(option))
                .build();
        String thamSo82172 = thamsodonviDAO.laythamso_donvi_motthamso(option.getDvtt(), "82172");
        if ("1".equals(thamSo82172)) {
            model.setB13List(xmlCV130DAO.getXml13CV130List(option));
            model.setB14List(xmlCV130DAO.getXml14CV130List(option));
            model.setB15List(xmlCV130DAO.getXml15CV130List(option));
        }
        return exportXmlCV130(model, option);
    }

    public String exportXmlCheckInCV130(XuatXmlCv130Option option, boolean maHoa) {
        List<CheckIn> checkInList = xmlCV130DAO.getXmlCheckInCV130List(option);
        CheckIn checkIn = checkInList != null && !checkInList.isEmpty() ?
                checkInList.get(0) : new CheckIn();
        Template template = xmlCV130TemplateFactory.getTemplate(TemplateType.CHECKIN, request);
        String noiDung = xmlCV130TemplateFactory.processTemplate(template, checkIn);
        if (maHoa) {
            return base64(noiDung);
        }
        return noiDung;
    }

    public String xuatXML12CV130(XuatXmlCv130Option option, boolean maHoa) {
        List<Xml12> xml12List = xmlCV130DAO.getXml12CV130List(option);
        Template template = xmlCV130TemplateFactory.getTemplate(TemplateType.XML12, request);
        String noiDung = xmlCV130TemplateFactory.processTemplate(template, xml12List.isEmpty() ? new Xml12() : xml12List.get(0));
        if (maHoa) {
            return base64(noiDung);
        }
        return noiDung;
    }

}
