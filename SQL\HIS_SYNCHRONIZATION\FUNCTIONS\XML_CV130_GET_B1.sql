CREATE OR REPLACE FUNCTION HIS_SYNCHRONIZATION.XML_CV130_GET_B1(
    p_sovaovien NUMBER,
    p_sovaovien_dt NUMBER,
    p_loaikcb NUMBER,
    p_dvtt VARCHAR2,
    p_cobaohiem NUMBER,
    p_tungay VARCHAR2,
    p_denngay VARCHAR2,
    p_doituong VARCHAR2
)
    RETURN SYS_REFCURSOR IS
    cur SYS_REFCURSOR;
    v_thamso_82435 number(1) := his_manager.GET_TSDV(p_dvtt, 82435, '0'); -- Tham số bỏ tiền xét nghiệm HIV ngoài danh mục (XN-HIV-NGOAIDM) ra khỏi XML
    v_thamso_82175 number(1) := his_manager.GET_TSDV(p_dvtt, 82175, '0'); -- Tham số Map mã bệnh YHCT trong thẻ MA_BENH_CHINH và MA_BENH_KT về ICD-10 XML130
    v_thamso_108918 number(1) := his_manager.GET_TSDV(p_dvtt, 108918, '0');  -- Tham số lấy CAN_NANG XML1 hiển thị lẻ XX.00; Mặc định số nguyên thì không có thập phân, số lẽ làm tròn 2 số thập phân
    p_ts_t_bntt_traituyen number(1) := his_manager.GET_TSDV(p_dvtt, '820328','0');
BEGIN
    IF p_cobaohiem = 1 THEN
        cur := XML_CV130_GET_B1_COBH(p_sovaovien, p_sovaovien_dt, p_loaikcb, p_dvtt, p_tungay, p_denngay, p_doituong);
    ELSE
        IF p_loaikcb = 1 THEN
            OPEN cur FOR
                SELECT t.MA_LK                                                                   AS MA_LK,
                       b1.STT                                                                    AS STT,
                       b1.MA_BN                                                                  AS MA_BN,
                       REGEXP_REPLACE(b1.HO_TEN, '[^[:print:]]', '')                             AS HO_TEN,
                       b1.CMT_BENHNHAN                                                           AS SO_CCCD,
                       b1.NHOMMAU                                                                AS NHOM_MAU,
                       b1.NGAY_SINH_CV130                                                        AS NGAY_SINH,
                       b1.GIOI_TINH                                                              AS GIOI_TINH,
                       b1.MA_QUOC_TICH                                                           AS MA_QUOCTICH,
                       b1.MADANTOC                                                               AS MA_DANTOC,
                       b1.MA_NGHENGHIEP_CV130                                                    AS MA_NGHE_NGHIEP,
                       REGEXP_REPLACE(b1.DIA_CHI, '[^[:print:]]', '')                            AS DIA_CHI,
                       b1.MATINH                                                                 AS MATINH_CU_TRU,
                       b1.MAHUYEN                                                                AS MAHUYEN_CU_TRU,
                       case when b1.MAXA_CU_TRU = '0' then '' else b1.MAXA_CU_TRU end            AS MAXA_CU_TRU,
                       b1.SO_DIEN_THOAI_BN                                                       AS DIEN_THOAI,
                       REGEXP_REPLACE(NVL(b1.MA_THE_XML_4210_B1, b1.MA_THE), '[^[:print:]]', '') AS MA_THE_BHYT,
                       NVL(b1.MADKBD_THE_XML_4210_B1, b1.MA_DKBD)                                AS MA_DKBD,
                       NVL(b1.HANBATDAU_THE_XML_4210_B1, b1.GT_THE_TU)                           AS GT_THE_TU,
                       NVL(b1.HANKETTHUC_THE_XML_4210_B1, b1.GT_THE_DEN)                         AS GT_THE_DEN,
                       b1.MIEN_CUNG_CT_CV130                                                     AS NGAY_MIEN_CCT,
                       b1.LY_DO_VV                                                               AS LY_DO_VV,
                       b1.B_LYDO_NHAPVIEN                                                        AS LY_DO_VNT,
                       b1.MA_LY_DO_VNT                                                           AS MA_LY_DO_VNT,
                       b1.CHAN_DOAN_VAO                                                          AS CHAN_DOAN_VAO,
                       b1.TEN_BENH                                                               AS CHAN_DOAN_RV,
                       DECODE(v_thamso_82175, 1, b1.MA_BENH_KO_YHCT, b1.MA_BENH)                 AS MA_BENH_CHINH,
                       DECODE(v_thamso_82175, 1, b1.MA_BENH_KT_KO_YHCT, b1.MA_BENHKHAC)          AS MA_BENH_KT,
                       b1.MA_BENH_YHCT                                                           AS MA_BENH_YHCT,
                       b1.MA_PTTT_QT                                                             AS MA_PTTT_QT,
                       b1.MA_DOI_TUONG_KCB                                                       AS MA_DOITUONG_KCB,
                       b1.MA_NOI_DI                                                              AS MA_NOI_DI,
                       b1.MA_NOI_DEN                                                             AS MA_NOI_DEN,
                       b1.MA_TAI_NAN_XML_B1                                                      AS MA_TAI_NAN,
                       b1.NGAY_VAO_CV130                                                         AS NGAY_VAO,
                       b1.NGAY_VAO_NOI_TRU                                                       AS NGAY_VAO_NOI_TRU,
                       b1.NGAY_RA                                                                AS NGAY_RA,
                       b1.GIAY_CHUYEN_TUYEN_3176                                                 AS GIAY_CHUYEN_TUYEN,
                       b1.SO_NGAY_DIEU_TRI_CV130                                                 AS SO_NGAY_DTRI,
                       b1.PP_DIEU_TRI                                                            AS PP_DIEU_TRI,
                       b1.KET_QUA_DTRI                                                           AS KET_QUA_DTRI,
                       b1.MA_LOAI_RV                                                             AS MA_LOAI_RV,
                       b1.GHI_CHU_CV130                                                          AS GHI_CHU,
                       b1.NGAY_TTOAN_CV130                                                       AS NGAY_TTOAN,
                       NVL(b1.T_THUOC, 0) + NVL(b1.T_MAU, 0)                                     AS T_THUOC,
                       NVL(b1.T_VTYT, 0)                                                         AS T_VTYT,
                       NVL(b1.T_TONGCHI_BV_CV130, 0) - case when v_thamso_82435 = 1 then NVL(b1.T_XN_HIV_NGOAIDM,0) + nvl(b1.T_CDHA_QUYTOANCAU,0) else 0 end AS T_TONGCHI_BV,
                       NVL(b1.T_TONGCHI_BH_CV130, 0) - case when v_thamso_82435 = 1 then NVL(b1.T_XN_HIV_NGOAIDM,0) + nvl(b1.T_CDHA_QUYTOANCAU,0) else 0 end AS T_TONGCHI_BH,
                       CASE p_ts_t_bntt_traituyen
                           WHEN 1 THEN nvl(b1.T_BNTT_B1_CV130,0)
                           ELSE
                               CASE b1.MA_LYDO_VVIEN
                                   WHEN 3 THEN NVL(b1.T_BNTT_B1_CV130, 0) + NVL(b1.T_BNCCT_XML_B1, 0)
                                   ELSE NVL(b1.T_BNTT_B1_CV130, 0)
                                   END
                           END                                                                   AS T_BNTT,
                       CASE p_ts_t_bntt_traituyen
                           WHEN 1 THEN nvl(T_BNCCT_XML_B1,0)
                           ELSE
                               CASE b1.MA_LYDO_VVIEN
                                   WHEN 3 THEN 0
                                   ELSE NVL(b1.T_BNCCT_XML_B1, 0)
                                   END
                           END                                                                   AS T_BNCCT,
                       NVL(b1.T_BHTT_BHYT, 0)                                                    AS T_BHTT,
                       NVL(b1.T_NGUONKHAC, 0) - case when v_thamso_82435 = 1 then NVL(b1.T_XN_HIV_NGOAIDM,0) + nvl(b1.T_CDHA_QUYTOANCAU,0) else 0 end AS T_NGUONKHAC,
                       NVL(b1.T_BHTT_GDV, 0)                                                     AS T_BHTT_GDV,
                       b1.NAM_QT_CV130                                                           AS NAM_QT,
                       b1.THANG_QT_CV130                                                         AS THANG_QT,
                       CASE
                           WHEN LENGTH(NVL(b1.MA_LOAIKCB_XML, b1.MA_LOAIKCB)) < 2
                               THEN '0' || TO_CHAR(NVL(b1.MA_LOAIKCB_XML, b1.MA_LOAIKCB))
                           ELSE TO_CHAR(NVL(b1.MA_LOAIKCB_XML, b1.MA_LOAIKCB))
                       END                                                                       AS MA_LOAI_KCB,
                       REPLACE(b1.MA_KHOA_CV130, ' ', '')                                        AS MA_KHOA,
                       his_manager.F_MA_DONVI_MOI(p_sovaovien, p_sovaovien_dt, b1.DVTT, p_loaikcb)           AS MA_CSKCB,
                       b1.MA_KHUVUC                                                              AS MA_KHUVUC,
                       CASE WHEN v_thamso_108918 = 1 THEN NVL(b1.CAN_NANG, '0')
                           ELSE
                               CASE WHEN MOD(b1.CAN_NANG, 1) = 0 THEN TO_CHAR(TO_NUMBER(b1.CAN_NANG))
                                  ELSE TO_CHAR(TRUNC(b1.CAN_NANG, 2), 'FM99999999999999990.99') END
                           END                                                                   AS CAN_NANG,
                       b1.CAN_NANG_CON                                                           AS CAN_NANG_CON,
                       b1.THOIDIEM_5NAM_LIENTUC                                                  AS NAM_NAM_LIEN_TUC,
                       b1.NGAY_TAI_KHAM                                                          AS NGAY_TAI_KHAM,
                       b1.MA_HSBA                                                                AS MA_HSBA,
                       b1.MA_TTDV                                                                AS MA_TTDV,
                       b1.CHIEU_CAO                                                              AS DU_PHONG,
                       1                                                                         AS TRANG_THAI,
                       b1.MA_NGHE_NGHIEP_3176                                                    AS MA_NGHE_NGHIEP_3176,
                       b1.SO_NGAY_DIEU_TRI_3176                                                  AS SO_NGAY_DIEU_TRI_3176
                FROM HIS_SYNCHRONIZATION.B1_CHITIEUTONGHOP_KCB b1,
                     HIS_SYNCHRONIZATION.B1_CV130_KEY_TENP t
                WHERE b1.SOVAOVIEN = t.SOVAOVIEN
                  AND b1.DVTT = t.DVTT
                  AND b1.MA_LOAIKCB = t.MA_LOAIKCB
                ORDER BY b1.MA_LK;
        ELSIF p_loaikcb = 2 THEN
            OPEN cur FOR
                SELECT t.MA_LK                                                                   AS MA_LK,
                       b1.STT                                                                    AS STT,
                       b1.MA_BN                                                                  AS MA_BN,
                       REGEXP_REPLACE(b1.HO_TEN, '[^[:print:]]', '')                             AS HO_TEN,
                       b1.CMT_BENHNHAN                                                           AS SO_CCCD,
                       b1.NHOMMAU                                                                AS NHOM_MAU,
                       b1.NGAY_SINH_CV130                                                        AS NGAY_SINH,
                       b1.GIOI_TINH                                                              AS GIOI_TINH,
                       b1.MA_QUOC_TICH                                                           AS MA_QUOCTICH,
                       b1.MADANTOC                                                               AS MA_DANTOC,
                       b1.MA_NGHENGHIEP_CV130                                                    AS MA_NGHE_NGHIEP,
                       REGEXP_REPLACE(b1.DIA_CHI, '[^[:print:]]', '')                            AS DIA_CHI,
                       b1.MATINH                                                                 AS MATINH_CU_TRU,
                       b1.MAHUYEN                                                                AS MAHUYEN_CU_TRU,
                       case when b1.MAXA_CU_TRU = '0' then '' else b1.MAXA_CU_TRU end            AS MAXA_CU_TRU,
                       b1.SO_DIEN_THOAI_BN                                                       AS DIEN_THOAI,
                       REGEXP_REPLACE(NVL(b1.MA_THE_XML_4210_B1, b1.MA_THE), '[^[:print:]]', '') AS MA_THE_BHYT,
                       NVL(b1.MADKBD_THE_XML_4210_B1, b1.MA_DKBD)                                AS MA_DKBD,
                       NVL(b1.HANBATDAU_THE_XML_4210_B1, b1.GT_THE_TU)                           AS GT_THE_TU,
                       NVL(b1.HANKETTHUC_THE_XML_4210_B1, b1.GT_THE_DEN)                         AS GT_THE_DEN,
                       b1.MIEN_CUNG_CT_CV130                                                     AS NGAY_MIEN_CCT,
                       b1.LY_DO_VV                                                               AS LY_DO_VV,
                       b1.B_LYDO_NHAPVIEN                                                        AS LY_DO_VNT,
                       b1.MA_LY_DO_VNT                                                           AS MA_LY_DO_VNT,
                       b1.CHAN_DOAN_VAO                                                          AS CHAN_DOAN_VAO,
                       b1.TEN_BENH                                                               AS CHAN_DOAN_RV,
                       DECODE(v_thamso_82175, 1, b1.MA_BENH_KO_YHCT, b1.MA_BENH)                 AS MA_BENH_CHINH,
                       DECODE(v_thamso_82175, 1, b1.MA_BENH_KT_KO_YHCT, b1.MA_BENHKHAC)          AS MA_BENH_KT,
                       b1.MA_BENH_YHCT                                                           AS MA_BENH_YHCT,
                       b1.MA_PTTT_QT                                                             AS MA_PTTT_QT,
                       b1.MA_DOI_TUONG_KCB                                                       AS MA_DOITUONG_KCB,
                       b1.MA_NOI_DI                                                              AS MA_NOI_DI,
                       b1.MA_NOI_DEN                                                             AS MA_NOI_DEN,
                       decode(NVL(b1.MA_TAI_NAN_XML_B1,0),0, b1.MA_TAI_NAN, b1.MA_TAI_NAN_XML_B1)   AS MA_TAI_NAN,
                       b1.NGAY_VAO_CV130                                                         AS NGAY_VAO,
                       b1.NGAY_VAO_NOI_TRU                                                       AS NGAY_VAO_NOI_TRU,
                       b1.NGAY_RA                                                                AS NGAY_RA,
                       b1.GIAY_CHUYEN_TUYEN_3176                                                 AS GIAY_CHUYEN_TUYEN,
                       b1.SO_NGAY_DIEU_TRI_CV130                                                 AS SO_NGAY_DTRI,
                       b1.PP_DIEU_TRI                                                            AS PP_DIEU_TRI,
                       b1.KET_QUA_DTRI                                                           AS KET_QUA_DTRI,
                       b1.MA_LOAI_RV                                                             AS MA_LOAI_RV,
                       b1.GHI_CHU_CV130                                                          AS GHI_CHU,
                       b1.NGAY_TTOAN_CV130                                                       AS NGAY_TTOAN,
                       (NVL(b1.T_THUOC_BANT, 0) + NVL(b1.T_MAU_BANT, 0))                         AS T_THUOC,
                       NVL(b1.T_VTYT_BANT, 0)                                                    AS T_VTYT,
                       NVL(b1.T_TONGCHI_BV_CV130, 0) - case when v_thamso_82435 = 1 then (NVL(b1.T_XN_HIV_NGOAIDM,0) + nvl(b1.T_CDHA_QUYTOANCAU,0)) else 0 end AS T_TONGCHI_BV,
                       NVL(b1.T_TONGCHI_BH_CV130, 0) - case when v_thamso_82435 = 1 then (NVL(b1.T_XN_HIV_NGOAIDM,0) + nvl(b1.T_CDHA_QUYTOANCAU,0)) else 0 end AS T_TONGCHI_BH,
                       CASE p_ts_t_bntt_traituyen
                           WHEN 1 THEN nvl(b1.T_BNTT_B1_CV130,0)
                           ELSE
                               CASE b1.MA_LYDO_VVIEN
                                   WHEN 3 THEN NVL(b1.T_BNTT_B1_CV130, 0) + NVL(b1.T_BNCCT_XML_B1, 0)
                                   ELSE NVL(b1.T_BNTT_B1_CV130, 0)
                                   END
                           END                                                                   AS T_BNTT,
                       CASE p_ts_t_bntt_traituyen
                           WHEN 1 THEN nvl(T_BNCCT_XML_B1,0)
                           ELSE
                               CASE b1.MA_LYDO_VVIEN
                                   WHEN 3 THEN 0
                                   ELSE NVL(b1.T_BNCCT_XML_B1, 0)
                                   END
                           END                                                                   AS T_BNCCT,
                       NVL(b1.T_BHTT, 0)                                                         AS T_BHTT,
                       NVL(b1.T_NGUONKHAC, 0) - case when v_thamso_82435 = 1 then (NVL(b1.T_XN_HIV_NGOAIDM,0) + nvl(b1.T_CDHA_QUYTOANCAU,0)) else 0 end AS T_NGUONKHAC,
                       NVL(b1.T_BHTT_GDV, 0)                                                     AS T_BHTT_GDV,
                       b1.NAM_QT_CV130                                                           AS NAM_QT,
                       b1.THANG_QT_CV130                                                         AS THANG_QT,
                       CASE
                           WHEN LENGTH(NVL(b1.MA_LOAIKCB_XML, b1.MA_LOAIKCB)) < 2
                               THEN '0' || TO_CHAR(NVL(b1.MA_LOAIKCB_XML, b1.MA_LOAIKCB))
                           ELSE TO_CHAR(NVL(b1.MA_LOAIKCB_XML, b1.MA_LOAIKCB))
                       END                                                                       AS MA_LOAI_KCB,
                       REPLACE(b1.MA_KHOA_CV130, ' ', '')                                        AS MA_KHOA,
                       his_manager.F_MA_DONVI_MOI(p_sovaovien, p_sovaovien_dt, b1.DVTT, p_loaikcb)           AS MA_CSKCB,
                       b1.MA_KHUVUC                                                              AS MA_KHUVUC,
                       CASE WHEN v_thamso_108918 = 1 THEN NVL(b1.CAN_NANG, '0')
                           ELSE
                               CASE WHEN MOD(b1.CAN_NANG, 1) = 0 THEN TO_CHAR(TO_NUMBER(b1.CAN_NANG))
                                  ELSE TO_CHAR(TRUNC(b1.CAN_NANG, 2), 'FM99999999999999990.99') END
                           END                                                                   AS CAN_NANG,
                       b1.CAN_NANG_CON                                                           AS CAN_NANG_CON,
                       b1.THOIDIEM_5NAM_LIENTUC                                                  AS NAM_NAM_LIEN_TUC,
                       b1.NGAY_TAI_KHAM                                                          AS NGAY_TAI_KHAM,
                       b1.MA_HSBA                                                                AS MA_HSBA,
                       b1.MA_TTDV                                                                AS MA_TTDV,
                       b1.CHIEU_CAO                                                              AS DU_PHONG,
                       1                                                                         AS TRANG_THAI,
                       b1.MA_NGHE_NGHIEP_3176                                                    AS MA_NGHE_NGHIEP_3176,
                       b1.SO_NGAY_DIEU_TRI_3176                                                  AS SO_NGAY_DIEU_TRI_3176
                FROM HIS_SYNCHRONIZATION.B1_CHITIEUTONGHOP_KCB b1,
                     HIS_SYNCHRONIZATION.B1_CV130_KEY_TENP t
                WHERE b1.SOVAOVIEN_NOI = t.SOVAOVIEN_NOI
                  AND b1.SOVAOVIEN_DT_NOI = t.SOVAOVIEN_DT_NOI
                  AND b1.DVTT = t.DVTT
                  AND b1.MA_LOAIKCB = t.MA_LOAIKCB
                ORDER BY b1.MA_LK;
        ELSIF p_loaikcb = 3 THEN
            OPEN cur FOR
                SELECT t.MA_LK                                                                   AS MA_LK,
                       b1.STT                                                                    AS STT,
                       b1.MA_BN                                                                  AS MA_BN,
                       REGEXP_REPLACE(b1.HO_TEN, '[^[:print:]]', '')                             AS HO_TEN,
                       b1.CMT_BENHNHAN                                                           AS SO_CCCD,
                       b1.NHOMMAU                                                                AS NHOM_MAU,
                       b1.NGAY_SINH_CV130                                                        AS NGAY_SINH,
                       b1.GIOI_TINH                                                              AS GIOI_TINH,
                       b1.MA_QUOC_TICH                                                           AS MA_QUOCTICH,
                       b1.MADANTOC                                                               AS MA_DANTOC,
                       b1.MA_NGHENGHIEP_CV130                                                    AS MA_NGHE_NGHIEP,
                       REGEXP_REPLACE(b1.DIA_CHI, '[^[:print:]]', '')                            AS DIA_CHI,
                       b1.MATINH                                                                 AS MATINH_CU_TRU,
                       b1.MAHUYEN                                                                AS MAHUYEN_CU_TRU,
                       case when b1.MAXA_CU_TRU = '0' then '' else b1.MAXA_CU_TRU end            AS MAXA_CU_TRU,
                       b1.SO_DIEN_THOAI_BN                                                       AS DIEN_THOAI,
                       REGEXP_REPLACE(NVL(b1.MA_THE_XML_4210_B1, b1.MA_THE), '[^[:print:]]', '') AS MA_THE_BHYT,
                       NVL(b1.MADKBD_THE_XML_4210_B1, b1.MA_DKBD)                                AS MA_DKBD,
                       NVL(b1.HANBATDAU_THE_XML_4210_B1, b1.GT_THE_TU)                           AS GT_THE_TU,
                       NVL(b1.HANKETTHUC_THE_XML_4210_B1, b1.GT_THE_DEN)                         AS GT_THE_DEN,
                       b1.MIEN_CUNG_CT_CV130                                                     AS NGAY_MIEN_CCT,
                       b1.LY_DO_VV                                                               AS LY_DO_VV,
                       b1.B_LYDO_NHAPVIEN                                                        AS LY_DO_VNT,
                       b1.MA_LY_DO_VNT                                                           AS MA_LY_DO_VNT,
                       b1.CHAN_DOAN_VAO                                                          AS CHAN_DOAN_VAO,
                       b1.TEN_BENH                                                               AS CHAN_DOAN_RV,
                       DECODE(v_thamso_82175, 1, b1.MA_BENH_KO_YHCT, b1.MA_BENH)                 AS MA_BENH_CHINH,
                       DECODE(v_thamso_82175, 1, b1.MA_BENH_KT_KO_YHCT, b1.MA_BENHKHAC)          AS MA_BENH_KT,
                       b1.MA_BENH_YHCT                                                           AS MA_BENH_YHCT,
                       b1.MA_PTTT_QT                                                             AS MA_PTTT_QT,
                       b1.MA_DOI_TUONG_KCB                                                       AS MA_DOITUONG_KCB,
                       b1.MA_NOI_DI                                                              AS MA_NOI_DI,
                       b1.MA_NOI_DEN                                                             AS MA_NOI_DEN,
                       decode(NVL(b1.MA_TAI_NAN_XML_B1,0),0, b1.MA_TAI_NAN, b1.MA_TAI_NAN_XML_B1)   AS MA_TAI_NAN,
                       b1.NGAY_VAO_CV130                                                         AS NGAY_VAO,
                       b1.NGAY_VAO_NOI_TRU                                                       AS NGAY_VAO_NOI_TRU,
                       b1.NGAY_RA                                                                AS NGAY_RA,
                       b1.GIAY_CHUYEN_TUYEN_3176                                                 AS GIAY_CHUYEN_TUYEN,
                       b1.SO_NGAY_DIEU_TRI_CV130                                                 AS SO_NGAY_DTRI,
                       b1.PP_DIEU_TRI                                                            AS PP_DIEU_TRI,
                       b1.KET_QUA_DTRI                                                           AS KET_QUA_DTRI,
                       b1.MA_LOAI_RV                                                             AS MA_LOAI_RV,
                       b1.GHI_CHU_CV130                                                          AS GHI_CHU,
                       b1.NGAY_TTOAN_CV130                                                       AS NGAY_TTOAN,
                       NVL(b1.T_THUOC, 0) + NVL(b1.T_MAU, 0)                                     AS T_THUOC,
                       NVL(b1.T_VTYT, 0)                                                         AS T_VTYT,
                       NVL(b1.T_TONGCHI_BV_CV130, 0) - case when v_thamso_82435 = 1 then (NVL(b1.T_XN_HIV_NGOAIDM,0) + nvl(b1.T_CDHA_QUYTOANCAU,0)) else 0 end AS T_TONGCHI_BV,
                       NVL(b1.T_TONGCHI_BH_CV130, 0) - case when v_thamso_82435 = 1 then (NVL(b1.T_XN_HIV_NGOAIDM,0) + nvl(b1.T_CDHA_QUYTOANCAU,0)) else 0 end AS T_TONGCHI_BH,
                       CASE p_ts_t_bntt_traituyen
                           WHEN 1 THEN nvl(b1.T_BNTT_B1_CV130,0)
                           ELSE
                               CASE b1.MA_LYDO_VVIEN
                                   WHEN 3 THEN NVL(b1.T_BNTT_B1_CV130, 0) + NVL(b1.T_BNCCT_XML_B1, 0)
                                   ELSE NVL(b1.T_BNTT_B1_CV130, 0)
                                   END
                           END                                                                   AS T_BNTT,
                       CASE p_ts_t_bntt_traituyen
                           WHEN 1 THEN nvl(T_BNCCT_XML_B1,0)
                           ELSE
                               CASE b1.MA_LYDO_VVIEN
                                   WHEN 3 THEN 0
                                   ELSE NVL(b1.T_BNCCT_XML_B1, 0)
                                   END
                           END                                                                   AS T_BNCCT,
                       NVL(b1.T_BHTT_BHYT, 0)                                                    AS T_BHTT,
                       NVL(b1.T_NGUONKHAC, 0) - case when v_thamso_82435 = 1 then (NVL(b1.T_XN_HIV_NGOAIDM,0) + nvl(b1.T_CDHA_QUYTOANCAU,0)) else 0 end AS T_NGUONKHAC,
                       NVL(b1.T_BHTT_GDV, 0)                                                     AS T_BHTT_GDV,
                       b1.NAM_QT_CV130                                                           AS NAM_QT,
                       b1.THANG_QT_CV130                                                         AS THANG_QT,
                       CASE
                           WHEN LENGTH(NVL(b1.MA_LOAIKCB_XML, b1.MA_LOAIKCB)) < 2
                               THEN '0' || TO_CHAR(NVL(b1.MA_LOAIKCB_XML, b1.MA_LOAIKCB))
                           ELSE TO_CHAR(NVL(b1.MA_LOAIKCB_XML, b1.MA_LOAIKCB))
                       END                                                                       AS MA_LOAI_KCB,
                       REPLACE(b1.MA_KHOA_CV130, ' ', '')                                        AS MA_KHOA,
                       his_manager.F_MA_DONVI_MOI(p_sovaovien, p_sovaovien_dt, b1.DVTT, p_loaikcb)           AS MA_CSKCB,
                       b1.MA_KHUVUC                                                              AS MA_KHUVUC,
                       CASE WHEN v_thamso_108918 = 1 THEN NVL(b1.CAN_NANG, '0')
                           ELSE
                               CASE WHEN MOD(b1.CAN_NANG, 1) = 0 THEN TO_CHAR(TO_NUMBER(b1.CAN_NANG))
                                  ELSE TO_CHAR(TRUNC(b1.CAN_NANG, 2), 'FM99999999999999990.99') END
                           END                                                                   AS CAN_NANG,
                       b1.CAN_NANG_CON                                                           AS CAN_NANG_CON,
                       b1.THOIDIEM_5NAM_LIENTUC                                                  AS NAM_NAM_LIEN_TUC,
                       b1.NGAY_TAI_KHAM                                                          AS NGAY_TAI_KHAM,
                       b1.MA_HSBA                                                                AS MA_HSBA,
                       b1.MA_TTDV                                                                AS MA_TTDV,
                       b1.CHIEU_CAO                                                              AS DU_PHONG,
                       1                                                                         AS TRANG_THAI,
                       b1.MA_NGHE_NGHIEP_3176                                                    AS MA_NGHE_NGHIEP_3176,
                       b1.SO_NGAY_DIEU_TRI_3176                                                  AS SO_NGAY_DIEU_TRI_3176
                FROM HIS_SYNCHRONIZATION.B1_CHITIEUTONGHOP_KCB b1,
                     HIS_SYNCHRONIZATION.B1_CV130_KEY_TENP t
                WHERE b1.SOVAOVIEN_NOI = t.SOVAOVIEN_NOI
                  AND b1.SOVAOVIEN_DT_NOI = t.SOVAOVIEN_DT_NOI
                  AND b1.DVTT = t.DVTT
                  AND b1.MA_LOAIKCB = t.MA_LOAIKCB
                ORDER BY b1.MA_LK;
        END IF;
    END IF;
    RETURN cur;
END;
/
