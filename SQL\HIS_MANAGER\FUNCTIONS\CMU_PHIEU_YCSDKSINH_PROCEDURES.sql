CREATE OR REPLACE
PROCEDURE HIS_MANAGER.CMU_PHIEU_YCSDKSINH_PROCEDURES(
    p_dvtt              IN     VARCHAR2,
    p_id               IN     NUMBER,
    p_ma_benh_nhan      IN     NUMBER,
    cur OUT SYS_REFCURSOR
) AS
    v_thamso960616   NUMBER(10) := cmu_tsdv(p_dvtt, 960616, 0);
BEGIN
open cur for
select
    phieu.ID,
    phieu.DVTT,
    phieu.SOVAOVIEN,
    phieu.MA_BENH_NHAN,
    phieu.DI_UNG,
    phieu.CHAN_DOAN_BENH,
    phieu.BAC_SI_DIEU_TRI,
    TO_CHAR(phieu.NGAY_TAO_PHIEU, 'dd/MM/yyyy') NGAY_TAO_PHIEU,
    phieu.NGUOI_TAO,
    bn.TEN_BENH_NHAN,
    bn.G<PERSON>I_TINH,
    bn.<PERSON><PERSON><PERSON><PERSON>,
    nv.TEN_NHANVIEN TEN_BAC_SI,
    nt.SO<PERSON><PERSON><PERSON>N MABENHAN,
    v_thamso960616 as <PERSON><PERSON><PERSON><PERSON><PERSON>,
    nt.TENKHOA_NHAPVIENVAOKHOA KHOA,
    nvnt.CAN_NANG

FROM CMU_PHIEU_YCSUDUNG_KSINH phieu
         LEFT JOIN his_public_list.dm_benh_nhan bn ON phieu.MA_BENH_NHAN = bn.ma_benh_nhan
         LEFT JOIN his_manager.noitru_benhan nt ON phieu.MA_BENH_NHAN = nt.MABENHNHAN
         LEFT JOIN his_manager.kb_phieunhapviennoitru nvnt ON phieu.MA_BENH_NHAN = nvnt.MA_BENH_NHAN
         LEFT JOIN HIS_FW.DM_NHANVIEN nv ON phieu.BAC_SI_DIEU_TRI = nv.MA_NHANVIEN
WHERE phieu.ID = p_id
order by phieu.NGAY_TAO_PHIEU;
END;
