create or replace PROCED<PERSON><PERSON> "HIS_MANAGER"."CMU_KTBA_BANG_DANH_GIA_P" (
    p_ID in NUMBER,
    CUR out SYS_REFCURSOR
)
IS

BEGIN
OPEN CUR FOR
SELECT jt.STT,
       jt.NOI_DUNG,
       jt.<PERSON><PERSON><PERSON>_CHUAN,
       jt.DIEM_DAT,
       jt.GHI_CHU,
       jt.KEY
FROM "HIS_MANAGER"."CMU_PHIEU_KIEMTRA_BENHAN" m,
     json_table(
             m.DATA_PHIEU,
             '$.BANG_KTRA_BA[*]'
                 COLUMNS (
                 STT VARCHAR2(10) PATH '$.STT',
                 NOI_DUNG VARCHAR2(2000) PATH '$.NOI_DUNG',
                 DIEM_CHUAN VARCHAR2(10) PATH '$.DIEM_CHUAN',
                 DIEM_DAT VARCHAR2(10) PATH '$.DIEM_DAT',
                 GHI_CHU VARCHAR2(2000) PATH '$.GHI_CHU',
                 <PERSON>EY VARCHAR2(50) PATH '$.KEY'
             )
     ) jt
WHERE m.ID = p_ID;
END;