create or replace PROCEDUR<PERSON> "HIS_MANGER"."NOITRU_CDHA_HIENTHILUOI_DACHON" (p_BHYT_KOCHI    IN varchar2,
                                                             p_DVTT          IN varchar2,
                                                             p_SO_PHIEU_CDHA IN varchar2,
                                                             cur             OUT SYS_REFCURSOR) IS
  p_tenht        number(1) default 0;
  p_stt_hangngay number default 0;
  V_THAMSO_94301 VARCHAR2(1) DEFAULT '0'; -- Sang STG Them
V_THAMSO_208 VARCHAR2(1) DEFAULT '0';
   -- VNPTHIS-3379 thêm
   V_STG_TUOI VARCHAR(100) DEFAULT ' ';
   -- VNPTHIS-3379 thêm
  -- HGI TGGDEV-28223 10/04/2018 thêm
    V_THAMSO_93129 VARCHAR2(1) DEFAULT '0';
    v_ngay_sinh date default sysdate;
    v_ngay_nhapvien date default sysdate;
  -- HGI TGGDEV-28223 10/04/2018 thêm
  v_thamso208 number(1) default '0';
  p_ngaytao DATE;
  v_capcuu_cls number(1) default '0';
  v_thamso820894 number(1) default '0';
  v_thamso820900 number(1) default '0';
  v_thamso960616 number(10) := cmu_tsdv(p_dvtt, 960616, 0);
BEGIN
begin
select mota_thamso into v_thamso208 FROM HIS_FW.DM_THAMSO_DONVI where dvtt=p_dvtt and ma_thamso = 208;
exception when no_data_found then
        v_thamso208:='0';
end;

SELECT trunc(cdha.ngay_tao)
INTO p_ngaytao
FROM his_manager.noitru_cd_cdha cdha
WHERE dvtt = p_dvtt
  AND cdha.so_phieu_cdha = p_SO_PHIEU_CDHA;
--AND sovaovien = p_sovaovien
--AND sovaovien_dt = p_sovaovien_Dt;
-- Sang VNPT STG thêm cho Vinh Phúc: Cho phép in phiếu chỉ định của CDHA hoặc loại CDHA đã ngưng hoạt động thông qua tham số 94301
-- Ân thêm tham số 208
BEGIN
SELECT NVL(MOTA_THAMSO, '0') INTO V_THAMSO_208
FROM HIS_FW.DM_THAMSO_DONVI
WHERE MA_THAMSO = 208
  and dvtt=p_DVTT;
EXCEPTION WHEN NO_DATA_FOUND THEN V_THAMSO_208 := '0';
END;
  -- Sang STG Them Begin 1
BEGIN
SELECT NVL(MOTA_THAMSO, '0')
INTO V_THAMSO_94301
FROM HIS_FW.DM_THAMSO_DONVI
WHERE MA_THAMSO = 94301
  and dvtt = p_DVTT;
EXCEPTION
    WHEN NO_DATA_FOUND THEN
      V_THAMSO_94301 := '0';
END;
  -- Sang STG Them End 1
  -- AGG le select stt_hangngay
BEGIN
SELECT cd.stt_hangngay
INTO p_stt_hangngay
FROM his_manager.noitru_cd_cdha cd
WHERE cd.dvtt = p_DVTT
  and cd.so_phieu_cdha = p_SO_PHIEU_CDHA;
EXCEPTION
    WHEN NO_DATA_FOUND THEN
      p_stt_hangngay := '0';
END;
  -- end AGG Le
  -- HGI TGGDEV-28223 10/04/2018 thêm
BEGIN
SELECT NVL(MOTA_THAMSO, '0') INTO V_THAMSO_93129
FROM HIS_FW.DM_THAMSO_DONVI
WHERE MA_THAMSO = 93129 AND DVTT = p_DVTT;
EXCEPTION WHEN NO_DATA_FOUND THEN V_THAMSO_93129 := '0';
END;
  -- HGI TGGDEV-28223 10/04/2018 thêm
   -- VNPTHIS-3379 thêm -- HGI TGGDEV-28223 10/04/2018 lấy thêm ngày sinh, ngày nhập viện
SELECT his_manager.stg_tuoi_te_f(to_char(bn.ngay_sinh,'yyyy-mm-dd'), to_char(trunc(ba.ngaynhapvien),'yyyy-mm-dd')),
       bn.ngay_sinh, ba.ngaynhapvien
INTO V_STG_TUOI, v_ngay_sinh, v_ngay_nhapvien
FROM his_public_list.dm_benh_nhan bn,
     his_manager.noitru_benhan ba,
     his_manager.NOITRU_CD_CDHA_CHI_TIET cd
WHERE ba.dvtt = p_DVTT
  AND cd.SO_PHIEU_CDHA = p_SO_PHIEU_CDHA
  AND ba.dvtt = cd.dvtt
  AND ba.stt_benhan = cd.stt_benhan
  AND ba.mabenhnhan = bn.ma_benh_nhan
  AND ROWNUM = 1;
-- VNPTHIS-3379 thêm
begin
select mota_thamso
into p_tenht
from his_fw.dm_thamso_donvi
where dvtt = p_dvtt
  and ma_thamso = 31016;
exception
    when NO_DATA_FOUND THEN
      p_tenht := '0';
end;
begin
select mota_thamso into v_capcuu_cls FROM HIS_FW.DM_THAMSO_DONVI where dvtt=p_dvtt and ma_thamso = 820877;
exception when no_data_found then
        v_capcuu_cls := '0';
end;

begin
select mota_thamso
into v_thamso820894
from his_fw.dm_thamso_donvi
where dvtt = p_dvtt
  and ma_thamso = 820894;
exception
    when NO_DATA_FOUND THEN
      v_thamso820894 := '0';
end;
  --thêm tham số hiển thị số thứ tự trên phiếu chỉ định
begin
select mota_thamso into v_thamso820900 FROM HIS_FW.DM_THAMSO_DONVI where dvtt=p_dvtt and ma_thamso = 820900;
exception when no_data_found then
        v_thamso820900 := '0';
end;
  if p_tenht = 1 then

    IF p_BHYT_KOCHI = 0 THEN
      OPEN cur FOR
SELECT rownum as STT,
       CDHA.ma_CDHA as MA_CDHA,
       CDHA.ma_loai_cdha as LOAI_CDHA,
       case
           when cdha.ten_hien_thi != ' ' then
               cdha.ten_hien_thi
           else
               --CDHA.ten_cdha
               case when v_thamso208 in (0,4) and SUDUNG_TT37=0 and trunc(p_ngaytao) >= to_date('2018-07-15','yyyy-mm-dd') then
                        case when NVL(TEN_TT15,' ')=' ' then CDHA.ten_cdha else TEN_TT15 end else CDHA.ten_cdha end
           end || decode(ct.ghichu_tencdha, null, '', '[' || ct.ghichu_tencdha || ']') as TEN_CDHA,
       CDHA.dvt_cdha as DVT_CDHA,
       LOAI.ten_loai_cdha as TEN_LOAI_CDHA,
       NVL(ct.SO_LUONG, 1) AS SO_LUONG,
       --CDHA.gia_cdha as GIA_CDHA,
       ct.don_gia as GIA_CDHA,
       --NVL(ct.SO_LUONG,1) * NVL(CDHA.gia_cdha,0) AS THANH_TIEN,
       (case NVL(ct.MA_CDHA, 0)
            when 0 then
                0
            else
                1
           end) as CHON,
       -- ifnull(!ct.BHYTKCHI,0) as  CHON,
       CDHA.hoatdong,
       CDHA.trangthai_bhyt,
       cdha.sapxep,
       p_BHYT_KOCHI        AS BHYT_KOCHI,
       CT.DON_GIA_BHYT     AS DON_GIA,
       CT.THANH_TIEN_BHYT  AS THANH_TIEN,
       -- VNPTHIS-3379 thêm
       V_STG_TUOI as tuoi,
       TO_CHAR(v_ngay_nhapvien, 'dd/MM/yyyy') NGAY_NHAP_VIEN
       -- VNPTHIS-3379 thêm
       -- VNPTHIS-3379 thêm
       -- vlg ghi chu
        , ct.ghichu_chidinh as ghichu
       --
        ,p_stt_hangngay as STT_HANGNGAY,
       -- HGI TGGDEV-28223 10/04/2018 thêm
       CASE V_THAMSO_93129
           WHEN '0' THEN his_manager.HGI_HIENTHI_TUOI_BENHNHAN(v_ngay_sinh, v_ngay_nhapvien, p_DVTT)
           WHEN '1' THEN to_char(v_ngay_sinh,'yyyy')
           WHEN '2' THEN to_char(v_ngay_sinh,'dd/mm/yyyy')
           END as ngaytuoihgi,
       V_THAMSO_93129 tshtngaytuoi,
       to_char(cd.ngay_chi_dinh, 'HH24') || ' ' || ' ' || 'giờ' || ' ' ||
       to_char(cd.ngay_chi_dinh, 'mi') || ' ' || 'phút' || ' ' || 'ngày' || ' ' ||
       to_char(cd.ngay_chi_dinh, 'DD') || ' ' || 'tháng' || ' ' ||
       to_char(cd.ngay_chi_dinh, 'MM') || ' ' || 'năm' || ' ' ||
       to_char(cd.ngay_chi_dinh, 'YYYY') as NGAY_CHI_DINH,
       -- BDH thêm loại CDHA đơn vị 82008
       (CASE WHEN LOAI.MOTA_LOAI_CDHA = 'TDCN' AND LOAI.MOTA_TEXT = 'DDT' THEN 'HH'
             WHEN LOAI.MOTA_LOAI_CDHA = 'TDCN' AND LOAI.MOTA_TEXT = 'DHH' THEN 'DT'
             WHEN LOAI.MOTA_LOAI_CDHA = 'SA' THEN 'SA'
             WHEN LOAI.MOTA_LOAI_CDHA = 'XQ' THEN 'XQ'
           END) AS LOAI
       -- HGI TGGDEV-28223 10/04/2018 thêm
        , case when v_capcuu_cls = 1 then cd.capcuu else dot.capcuu end as thuong_capcuu  -- TGGDEV-38285 thêm TGGDEV-48254 bổ sung
        ,(ba.icd_nhapvien || '  ' || ba.tenbenhchinh_nhapvien) as benhchinh,
       v_thamso820894 as bacsichidinh,
       chd.mota_chucdanh||'. '||nv.ten_nhanvien as tenbacsichidinh,
       v_thamso820900 as HIENTHI,
       to_char(cd.NGAY_CHI_DINH, 'dd/mm/yyyy HH24:MI:SS') as NGAY_CHI_DINH_HT,
       v_thamso960616 ANCHUKY
FROM cls_cdha CDHA, NOITRU_CD_CDHA_CHI_TIET ct, NOITRU_CD_CDHA cd, cls_loaicdha LOAI,
     his_fw.dm_nhanvien nv
         left join his_fw.dm_chucdanh_nhanvien chd on nv.chucdanh_nhanvien = chd.ma_chucdanh,
     noitru_dotdieutri dot -- TGGDEV-38285 thêm
         left join noitru_benhan ba
                   on dot.stt_benhan = ba.stt_benhan
                       and dot.dvtt = ba.dvtt
                       and dot.sovaovien = ba.sovaovien
WHERE CDHA.dvtt = p_DVTT
  and ct.DVTT = p_DVTT
  and ct.SO_PHIEU_CDHA = p_SO_PHIEU_CDHA
  and CDHA.DVTT = ct.DVTT
  and CDHA.ma_CDHA = ct.MA_CDHA
  and CDHA.dvtt = p_DVTT
  AND LOAI.dvtt = p_DVTT
  and cd.dvtt = p_dvtt
  and cd.so_phieu_cdha = ct.so_phieu_cdha
  and cd.sovaovien = ct.sovaovien
  and cd.sovaovien_dt = ct.sovaovien_dt
  and cd.nguoi_chi_dinh = nv.ma_nhanvien
  AND (LOAI.hoatdong = 1 OR V_THAMSO_94301 = '1') -- Sang STG S?a, code g?c (AND LOAI.hoatdong = 1)
  AND (CDHA.hoatdong = 1 OR V_THAMSO_94301 = '1') -- Sang STG S?a, code g?c (AND CDHA.hoatdong = 1)
  -- AND CDHA.trangthai_bhyt IN (1,2)
  AND CDHA.ma_loai_cdha = LOAI.ma_loai_cdha
  -- TGGDEV-38285 thêm
  and ct.dvtt = dot.dvtt
  and ct.stt_benhan = dot.stt_benhan
  and ct.stt_dotdieutri = dot.stt_dotdieutri
  and dot.dvtt = p_dvtt
  -- TGGDEV-38285 end thêm
order by cdha.sapxep asc;

ELSE

      OPEN cur FOR
SELECT rownum as STT,
       CDHA.ma_CDHA as MA_CDHA,
       CDHA.ma_loai_cdha as LOAI_CDHA,
       case
           when cdha.ten_hien_thi != ' ' then
               cdha.ten_hien_thi
           else
               --CDHA.ten_cdha
               case when v_thamso208 in (0,4) and SUDUNG_TT37=0 and trunc(p_ngaytao) >= to_date('2018-07-15','yyyy-mm-dd') then
                        case when NVL(TEN_TT15,' ')=' ' then CDHA.ten_cdha else TEN_TT15 end else CDHA.ten_cdha end
           end || decode(ct.ghichu_tencdha, null, '', '[' || ct.ghichu_tencdha || ']') as TEN_CDHA,
       CDHA.dvt_cdha as DVT_CDHA,
       LOAI.ten_loai_cdha as TEN_LOAI_CDHA,
       NVL(ct.SO_LUONG, 1) AS SO_LUONG,
       --CDHA.gia_cdha as GIA_CDHA,
       ct.don_gia as GIA_CDHA,
       --NVL(ct.SO_LUONG,1) * NVL(CDHA.gia_cdha,0) AS THANH_TIEN,
       (case NVL(ct.MA_CDHA, 0)
            when 0 then
                0
            else
                1
           end) as CHON,
       -- ifnull(!ct.BHYTKCHI,0) as  CHON,
       CDHA.hoatdong,
       CDHA.trangthai_bhyt,
       cdha.sapxep,
       p_BHYT_KOCHI          AS BHYT_KOCHI,
       CT.DON_GIA_KO_BHYT    AS DON_GIA,
       CT.THANH_TIEN_KO_BHYT AS THANH_TIEN,
       -- VNPTHIS-3379 thêm
       V_STG_TUOI as tuoi,
       TO_CHAR(v_ngay_nhapvien, 'dd/MM/yyyy') NGAY_NHAP_VIEN
       -- VNPTHIS-3379 thêm
       -- vlg ghi chu
        , ct.ghichu_chidinh as ghichu
       --
        ,p_stt_hangngay as STT_HANGNGAY,
       -- HGI TGGDEV-28223 10/04/2018 thêm
       CASE V_THAMSO_93129
           WHEN '0' THEN his_manager.HGI_HIENTHI_TUOI_BENHNHAN(v_ngay_sinh, v_ngay_nhapvien, p_DVTT)
           WHEN '1' THEN to_char(v_ngay_sinh,'yyyy')
           WHEN '2' THEN to_char(v_ngay_sinh,'dd/mm/yyyy')
           END as ngaytuoihgi,
       V_THAMSO_93129 tshtngaytuoi,
       to_char(cd.ngay_chi_dinh, 'HH24') || ' ' || ' ' || 'giờ' || ' ' ||
       to_char(cd.ngay_chi_dinh, 'mi') || ' ' || 'phút' || ' ' || 'ngày' || ' ' ||
       to_char(cd.ngay_chi_dinh, 'DD') || ' ' || 'tháng' || ' ' ||
       to_char(cd.ngay_chi_dinh, 'MM') || ' ' || 'năm' || ' ' ||
       to_char(cd.ngay_chi_dinh, 'YYYY') as NGAY_CHI_DINH,
       -- BDH thêm loại CDHA đơn vị 82008
       (CASE WHEN LOAI.MOTA_LOAI_CDHA = 'TDCN' AND LOAI.MOTA_TEXT = 'DDT' THEN 'HH'
             WHEN LOAI.MOTA_LOAI_CDHA = 'TDCN' AND LOAI.MOTA_TEXT = 'DHH' THEN 'DT'
             WHEN LOAI.MOTA_LOAI_CDHA = 'SA' THEN 'SA'
             WHEN LOAI.MOTA_LOAI_CDHA = 'XQ' THEN 'XQ'
           END) AS LOAI
       -- HGI TGGDEV-28223 10/04/2018 thêm
        , case when v_capcuu_cls = 1 then cd.capcuu else dot.capcuu end as thuong_capcuu  -- TGGDEV-38285 thêm TGGDEV-48254 bổ sung
        ,(ba.icd_nhapvien || '  ' || ba.tenbenhchinh_nhapvien) as benhchinh,
       v_thamso820894 as bacsichidinh,
       chd.mota_chucdanh ||'. '|| nv.ten_nhanvien as tenbacsichidinh,
       v_thamso820900 as HIENTHI,
       to_char(cd.NGAY_CHI_DINH, 'dd/mm/yyyy HH24:MI:SS') as NGAY_CHI_DINH_HT,
       v_thamso960616 ANCHUKY
FROM cls_cdha CDHA, NOITRU_CD_CDHA_CHI_TIET ct, NOITRU_CD_CDHA cd, cls_loaicdha LOAI,
     his_fw.dm_nhanvien nv
         left join his_fw.dm_chucdanh_nhanvien chd on nv.chucdanh_nhanvien = chd.ma_chucdanh,
     noitru_dotdieutri dot -- TGGDEV-38285 thêm
         left join noitru_benhan ba
                   on dot.stt_benhan = ba.stt_benhan
                       and dot.dvtt = ba.dvtt
                       and dot.sovaovien = ba.sovaovien
WHERE CDHA.dvtt = p_DVTT
  and ct.DVTT = p_DVTT
  and ct.SO_PHIEU_CDHA = p_SO_PHIEU_CDHA
  and CDHA.DVTT = ct.DVTT
  and CDHA.ma_CDHA = ct.MA_CDHA
  and CDHA.dvtt = p_DVTT
  AND LOAI.dvtt = p_DVTT
  and cd.dvtt = p_dvtt
  and cd.so_phieu_cdha = ct.so_phieu_cdha
  and cd.sovaovien = ct.sovaovien
  and cd.sovaovien_dt = ct.sovaovien_dt
  and cd.nguoi_chi_dinh = nv.ma_nhanvien
  AND (LOAI.hoatdong = 1 OR V_THAMSO_94301 = '1') -- Sang STG S?a, code g?c (AND LOAI.hoatdong = 1)
  AND (CDHA.hoatdong = 1 OR V_THAMSO_94301 = '1') -- Sang STG S?a, code g?c (AND CDHA.hoatdong = 1)
  -- AND CDHA.trangthai_bhyt IN (0,2)
  AND CDHA.ma_loai_cdha = LOAI.ma_loai_cdha
  -- TGGDEV-38285 thêm
  and ct.dvtt = dot.dvtt
  and ct.stt_benhan = dot.stt_benhan
  and ct.stt_dotdieutri = dot.stt_dotdieutri
  and dot.dvtt = p_dvtt
  -- TGGDEV-38285 end thêm
order by cdha.sapxep asc;
END IF;
else
    IF p_BHYT_KOCHI = 0 THEN
      OPEN cur FOR
SELECT rownum as STT,
       CDHA.ma_CDHA as MA_CDHA,
       CDHA.ma_loai_cdha as LOAI_CDHA,
       --CDHA.ten_cdha as TEN_CDHA,
       case when v_thamso208 in (0,4) and SUDUNG_TT37=0 and trunc(p_ngaytao) >= to_date('2018-07-15','yyyy-mm-dd') then
                case when NVL(TEN_TT15,' ')=' ' then CDHA.ten_cdha else TEN_TT15 end else CDHA.ten_cdha end || decode(ct.ghichu_tencdha, null, '', '[' || ct.ghichu_tencdha || ']') as TEN_CDHA,
       CDHA.dvt_cdha as DVT_CDHA,
       LOAI.ten_loai_cdha as TEN_LOAI_CDHA,
       NVL(ct.SO_LUONG, 1) AS SO_LUONG,
       --CDHA.gia_cdha as GIA_CDHA,
       ct.don_gia as GIA_CDHA,
       NVL(ct.SO_LUONG, 1) * NVL(ct.don_gia, 0) AS THANH_TIEN,
       (case NVL(ct.MA_CDHA, 0)
            when 0 then
                0
            else
                1
           end) as CHON,
       -- ifnull(!ct.BHYTKCHI,0) as  CHON,
       CDHA.hoatdong,
       CDHA.trangthai_bhyt,
       cdha.sapxep,
       p_BHYT_KOCHI        AS BHYT_KOCHI,
       -- VNPTHIS-3379 thêm
       V_STG_TUOI as tuoi,
       TO_CHAR(v_ngay_nhapvien, 'dd/MM/yyyy') NGAY_NHAP_VIEN
       -- VNPTHIS-3379 thêm
       -- vlg ghi chu
        , ct.ghichu_chidinh as ghichu
       --
        ,p_stt_hangngay as STT_HANGNGAY,
       -- HGI TGGDEV-28223 10/04/2018 thêm
       CASE V_THAMSO_93129
           WHEN '0' THEN his_manager.HGI_HIENTHI_TUOI_BENHNHAN(v_ngay_sinh, v_ngay_nhapvien, p_DVTT)
           WHEN '1' THEN to_char(v_ngay_sinh,'yyyy')
           WHEN '2' THEN to_char(v_ngay_sinh,'dd/mm/yyyy')
           END as ngaytuoihgi,
       V_THAMSO_93129 tshtngaytuoi,
       to_char(cd.ngay_chi_dinh, 'HH24') || ' ' || ' ' || 'giờ' || ' ' ||
       to_char(cd.ngay_chi_dinh, 'mi') || ' ' || 'phút' || ' ' || 'ngày' || ' ' ||
       to_char(cd.ngay_chi_dinh, 'DD') || ' ' || 'tháng' || ' ' ||
       to_char(cd.ngay_chi_dinh, 'MM') || ' ' || 'năm' || ' ' ||
       to_char(cd.ngay_chi_dinh, 'YYYY') as NGAY_CHI_DINH,
       -- BDH thêm loại CDHA đơn vị 82008
       (CASE WHEN LOAI.MOTA_LOAI_CDHA = 'TDCN' AND LOAI.MOTA_TEXT = 'DDT' THEN 'HH'
             WHEN LOAI.MOTA_LOAI_CDHA = 'TDCN' AND LOAI.MOTA_TEXT = 'DHH' THEN 'DT'
             WHEN LOAI.MOTA_LOAI_CDHA = 'SA' THEN 'SA'
             WHEN LOAI.MOTA_LOAI_CDHA = 'XQ' THEN 'XQ'
           END) AS LOAI
       -- HGI TGGDEV-28223 10/04/2018 thêm
        ,case when v_capcuu_cls = 1 then cd.capcuu else dot.capcuu end as thuong_capcuu  -- TGGDEV-38285 thêm TGGDEV-48254 bổ sung
        ,(ba.icd_nhapvien || '  ' || ba.tenbenhchinh_nhapvien) as benhchinh,
       v_thamso820894 as bacsichidinh,
       chd.mota_chucdanh || '. ' || nv.ten_nhanvien as tenbacsichidinh,
       v_thamso820900 as HIENTHI,
       to_char(cd.NGAY_CHI_DINH, 'dd/mm/yyyy HH24:MI:SS') as NGAY_CHI_DINH_HT,
       v_thamso960616 ANCHUKY
FROM cls_cdha CDHA, NOITRU_CD_CDHA_CHI_TIET ct, NOITRU_CD_CDHA cd, cls_loaicdha LOAI,
     his_fw.dm_nhanvien nv
         left join his_fw.dm_chucdanh_nhanvien chd on nv.chucdanh_nhanvien = chd.ma_chucdanh,
     noitru_dotdieutri dot -- TGGDEV-38285 thêm
         left join noitru_benhan ba
                   on dot.stt_benhan = ba.stt_benhan
                       and dot.dvtt = ba.dvtt
                       and dot.sovaovien = ba.sovaovien
WHERE CDHA.dvtt = p_DVTT
  and ct.DVTT = p_DVTT
  and ct.SO_PHIEU_CDHA = p_SO_PHIEU_CDHA
  and CDHA.DVTT = ct.DVTT
  and CDHA.ma_CDHA = ct.MA_CDHA
  and CDHA.dvtt = p_DVTT
  AND LOAI.dvtt = p_DVTT
  and cd.dvtt = p_dvtt
  and cd.so_phieu_cdha = ct.so_phieu_cdha
  and cd.sovaovien = ct.sovaovien
  and cd.sovaovien_dt = ct.sovaovien_dt
  and cd.nguoi_chi_dinh = nv.ma_nhanvien
  AND (LOAI.hoatdong = 1 OR V_THAMSO_94301 = '1') -- Sang STG S?a, code g?c (AND LOAI.hoatdong = 1)
  AND (CDHA.hoatdong = 1 OR V_THAMSO_94301 = '1') -- Sang STG S?a, code g?c (AND CDHA.hoatdong = 1)
  -- AND CDHA.trangthai_bhyt IN (1,2)
  AND CDHA.ma_loai_cdha = LOAI.ma_loai_cdha
  -- TGGDEV-38285 thêm
  and ct.dvtt = dot.dvtt
  and ct.stt_benhan = dot.stt_benhan
  and ct.stt_dotdieutri = dot.stt_dotdieutri
  and dot.dvtt = p_dvtt
  -- TGGDEV-38285 end thêm
order by cdha.sapxep asc;

ELSE

      OPEN cur FOR
SELECT rownum as STT,
       CDHA.ma_CDHA as MA_CDHA,
       CDHA.ma_loai_cdha as LOAI_CDHA,
       --CDHA.ten_cdha as TEN_CDHA,
       case when v_thamso208 in (0,4) and SUDUNG_TT37=0 and trunc(p_ngaytao) >= to_date('2018-07-15','yyyy-mm-dd') then
                case when NVL(TEN_TT15,' ')=' ' then CDHA.ten_cdha else TEN_TT15 end else CDHA.ten_cdha end || decode(ct.ghichu_tencdha, null, '', '[' || ct.ghichu_tencdha || ']') as TEN_CDHA,
       CDHA.dvt_cdha as DVT_CDHA,
       LOAI.ten_loai_cdha as TEN_LOAI_CDHA,
       NVL(ct.SO_LUONG, 1) AS SO_LUONG,
       --CDHA.gia_cdha as GIA_CDHA,
       ct.don_gia as GIA_CDHA,
       NVL(ct.SO_LUONG, 1) * NVL(ct.don_gia, 0) AS THANH_TIEN,
       (case NVL(ct.MA_CDHA, 0)
            when 0 then
                0
            else
                1
           end) as CHON,
       -- ifnull(!ct.BHYTKCHI,0) as  CHON,
       CDHA.hoatdong,
       CDHA.trangthai_bhyt,
       cdha.sapxep,
       p_BHYT_KOCHI        AS BHYT_KOCHI,
       -- VNPTHIS-3379 thêm
       V_STG_TUOI as tuoi,
       TO_CHAR(v_ngay_nhapvien, 'dd/MM/yyyy') NGAY_NHAP_VIEN
       -- VNPTHIS-3379 thêm
       -- vlg ghi chu
        , ct.ghichu_chidinh as ghichu
       --
        ,p_stt_hangngay as STT_HANGNGAY,
       -- HGI TGGDEV-28223 10/04/2018 thêm
       CASE V_THAMSO_93129
           WHEN '0' THEN his_manager.HGI_HIENTHI_TUOI_BENHNHAN(v_ngay_sinh, v_ngay_nhapvien, p_DVTT)
           WHEN '1' THEN to_char(v_ngay_sinh,'yyyy')
           WHEN '2' THEN to_char(v_ngay_sinh,'dd/mm/yyyy')
           END as ngaytuoihgi,
       V_THAMSO_93129 tshtngaytuoi,
       to_char(cd.ngay_chi_dinh, 'HH24') || ' ' || ' ' || 'giờ' || ' ' ||
       to_char(cd.ngay_chi_dinh, 'mi') || ' ' || 'phút' || ' ' || 'ngày' || ' ' ||
       to_char(cd.ngay_chi_dinh, 'DD') || ' ' || 'tháng' || ' ' ||
       to_char(cd.ngay_chi_dinh, 'MM') || ' ' || 'năm' || ' ' ||
       to_char(cd.ngay_chi_dinh, 'YYYY') as NGAY_CHI_DINH,
       -- BDH thêm loại CDHA đơn vị 82008
       (CASE WHEN LOAI.MOTA_LOAI_CDHA = 'TDCN' AND LOAI.MOTA_TEXT = 'DDT' THEN 'HH'
             WHEN LOAI.MOTA_LOAI_CDHA = 'TDCN' AND LOAI.MOTA_TEXT = 'DHH' THEN 'DT'
             WHEN LOAI.MOTA_LOAI_CDHA = 'SA' THEN 'SA'
             WHEN LOAI.MOTA_LOAI_CDHA = 'XQ' THEN 'XQ'
           END) AS LOAI
       -- HGI TGGDEV-28223 10/04/2018 thêm
        ,case when v_capcuu_cls = 1 then cd.capcuu else dot.capcuu end as thuong_capcuu  -- TGGDEV-38285 thêm TGGDEV-48254 bổ sung
        ,(ba.icd_nhapvien || '  ' || ba.tenbenhchinh_nhapvien) as benhchinh,
       v_thamso820894 as bacsichidinh,
       chd.mota_chucdanh || '. ' || nv.ten_nhanvien as tenbacsichidinh,
       v_thamso820900 as HIENTHI,
       to_char(cd.NGAY_CHI_DINH, 'dd/mm/yyyy HH24:MI:SS') as NGAY_CHI_DINH_HT,
       v_thamso960616 ANCHUKY
FROM cls_cdha CDHA, NOITRU_CD_CDHA_CHI_TIET ct, NOITRU_CD_CDHA cd, cls_loaicdha LOAI,
     his_fw.dm_nhanvien nv
         left join his_fw.dm_chucdanh_nhanvien chd on nv.chucdanh_nhanvien = chd.ma_chucdanh,
     noitru_dotdieutri dot -- TGGDEV-38285 thêm
         left join noitru_benhan ba
                   on dot.stt_benhan = ba.stt_benhan
                       and dot.dvtt = ba.dvtt
                       and dot.sovaovien = ba.sovaovien
WHERE CDHA.dvtt = p_DVTT
  and ct.DVTT = p_DVTT
  and ct.SO_PHIEU_CDHA = p_SO_PHIEU_CDHA
  and CDHA.DVTT = ct.DVTT
  and CDHA.ma_CDHA = ct.MA_CDHA
  and CDHA.dvtt = p_DVTT
  AND LOAI.dvtt = p_DVTT
  and cd.dvtt = p_dvtt
  and cd.so_phieu_cdha = ct.so_phieu_cdha
  and cd.sovaovien = ct.sovaovien
  and cd.sovaovien_dt = ct.sovaovien_dt
  and cd.nguoi_chi_dinh = nv.ma_nhanvien
  AND (LOAI.hoatdong = 1 OR V_THAMSO_94301 = '1') -- Sang STG S?a, code g?c (AND LOAI.hoatdong = 1)
  AND (CDHA.hoatdong = 1 OR V_THAMSO_94301 = '1') -- Sang STG S?a, code g?c (AND CDHA.hoatdong = 1)
  -- AND CDHA.trangthai_bhyt IN (0,2)
  AND CDHA.ma_loai_cdha = LOAI.ma_loai_cdha
  -- TGGDEV-38285
  and ct.dvtt = dot.dvtt
  and ct.stt_benhan = dot.stt_benhan
  and ct.stt_dotdieutri = dot.stt_dotdieutri
  and dot.dvtt = p_dvtt
  -- TGGDEV-38285 end thêm
order by cdha.sapxep asc;
END IF;
end IF;
END;