<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@page contentType="text/html" pageEncoding="UTF-8" buffer="10000kb" autoFlush="false" %>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="google-site-verification" content="u6uNEfD4cb3gidezi4r_6aI8Wb1E07-ufBeCQpvmlqQ" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Hệ thống chăm sóc sức khỏe</title>
    <link rel="icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
    <link rel="shortcut icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
    <script src="<c:url value="/resources/js/jquery.min.1.9.1.js" />"></script>
    <%@include file="css/include_css.jsp"%>

    <%@include file="css/einvoice_inline.jsp"%>
    <style>
        .autocomplete-custom-template li {
            border-bottom: 1px solid #ccc;
            height: auto;
            padding-top: 8px;
            padding-bottom: 8px;
            white-space: normal;
        }
        .autocomplete-custom-template li:last-child {
            border-bottom-width: 0;
        }
        .autocomplete-custom-template .item-title,
        .autocomplete-custom-template .item-metadata {
            display: block;
            line-height: 2;
        }
        .autocomplete-custom-template .item-title md-icon {
            height: 18px;
            width: 18px;
        }
        .custome-autocomplete input:not(.md-input) {
            width: 90% !important;
        }
        md-autocomplete{
            min-width: 100px !important;
        }
        #snackbar {
            visibility: hidden;
            min-width: 250px;
            margin-left: -125px;
            background-color: #21409A;
            color: #fff;
            text-align: center;
            border-radius: 2px;
            padding: 16px;
            position: fixed;
            z-index: 1;
            left: 50%;
            bottom: 30px;
            font-size: 17px;
        }

        #snackbar.show {
            visibility: visible;
            -webkit-animation: fadein 0.5s, fadeout 0.5s 2.5s;
            animation: fadein 0.5s, fadeout 0.5s 2.5s;
        }

        @-webkit-keyframes fadein {
            from {bottom: 0; opacity: 0;}
            to {bottom: 30px; opacity: 1;}
        }

        @keyframes fadein {
            from {bottom: 0; opacity: 0;}
            to {bottom: 30px; opacity: 1;}
        }

        @-webkit-keyframes fadeout {
            from {bottom: 30px; opacity: 1;}
            to {bottom: 0; opacity: 0;}
        }

        @keyframes fadeout {
            from {bottom: 30px; opacity: 1;}
            to {bottom: 0; opacity: 0;}
        }
    </style>
</head>

<body ng-app="HISAPP" ng-cloak class="background-ccd6da" style="height:auto">
<div id="panel_all mh-0">
    <%@include file="../../../resources/Theme/include_pages/menu.jsp"%>

</div>
<input type="hidden" id="cmu_ctlaynguoibenh" value="${cmu_ctlaynguoibenh}">
<input type="hidden" id="cmu_inappagg" value="${cmu_inappagg}">
<input type="hidden" id="hgi_hoanung" value="${hgi_hoanung}">
<div style="position:relative" ng-controller="AppCtrl" ng-cloak class="c-wrapper background-w autocompletedemoCustomTemplate"
     ng-init="init('${Sess_DVTT}','${Sess_UserID}','${Sess_Admin}', '', '',
                  '', '', '${businessservice}', '${portalservice}', '${publishservice}', '${pattern}'
                  , '${serial}', '${serialbhyt}', '${ngaytrienkhai}','${Sess_PhongBan}','${Sess_User}','${cmu_hoadonchitiet}', '${hgi_hoanung}', '${cmu_laphoadon}')">
    <div ng-show="showLoading" class="c-loadinggrid"><md-progress-circular style="top: calc(50% - 50px);left: calc(50% - 50px);" md-mode="indeterminate"></md-progress-circular></div>
    <md-content>
        <md-tabs md-dynamic-height md-border-bottom>
            <md-tab label="Thu viện phí">

                <md-content class="md-padding background-w " style="overflow: visible;">
                    <div layout="row" class="c-pos-rela c-height700">
                        <div flex="30" layout="row" layout-wrap class="c-pos-obso">
                            <div flex="grow" class="c-wrapdate">
                                <md-input-container class="c-height40 c-bottom0" flex>
                                    <label>Từ ngày</label>
                                    <md-datepicker ng-model="ngaybatdau" class="c-padding-r0"></md-datepicker>
                                </md-input-container>
                            </div>
                            <div flex="grow" class="c-wrapdate">
                                <md-input-container class="c-height40 c-bottom0" flex>
                                    <label>Đến ngày</label>
                                    <md-datepicker ng-model="ngayketthuc" class="c-padding-r0"></md-datepicker>
                                </md-input-container>
                            </div>
                            <div flex="grow">
                                <md-select class="c-no-margin c-bottom10" ng-model="loaivienphi" placeholder="Loại viện phí" class="md-no-underline">
                                    <md-option value="BHYT">Ngoại trú - BHYT</md-option>
                                    <md-option value="THUPHI">Ngoại trú - Thu phí</md-option>
                                    <md-option value="BANT">BANT</md-option>
                                    <md-option value="NOITRUBHYT">Nội trú - BHYT</md-option>
                                    <md-option value="NOITRUTHUPHI">Nội trú - Thu phí</md-option>
                                    <md-option value="NOITRUMIENGIAM">Nội trú - Miễn giảm</md-option>
                                    <md-option value="THUDICHVU">Nội trú - Dịch vụ</md-option>
                                    <md-option value="NOITRUTAMUNG">Nội trú - Tạm thu</md-option>
                                </md-select>
                            </div>
                            <div flex="grow">
                                <md-select class="c-no-margin c-bottom10" ng-model="khoa" placeholder="Khoa" class="md-no-underline">
                                    <md-option value="{{k.MA_PHONGBAN}}" ng-repeat="k in listkhoa track by $index">{{k.TEN_PHONGBAN}}</md-option>
                                </md-select>
                            </div>
                            <div flex="grow">
                                <md-checkbox ng-change="reloaddsbn()" ng-model="trangthaibn" ng-true-value="1" ng-false-value="0" aria-label="Đã thanh toán">Đã thanh toán</md-checkbox>
                            </div>
                            <md-button ng-click="laydsbn($event)" class="md-raised md-primary c-no-margin-lr" flex><i class="material-icons c-icon-refresh">cached</i> Làm mới</md-button>
                            <div flex="grow">
                                <div ng-show="loadingDs" flex class="c-wrapper-loading">
                                    <md-progress-linear md-mode="query"></md-progress-linear>
                                </div>
                                <c-grid id="listbn" data="listbn" config="listbnConfig" footer="listbnfooter" cfilter="{{true}}" vheight="{{450}}"></c-grid>
                            </div>

                        </div>
                        <div flex="70" class="c-pos-obso c-panel-l">
                            <md-progress-circular style="display: none" md-mode="indeterminate"></md-progress-circular>
                            <div class="c-wrap-content c-padding-l10 c-font-verdana">
                                <div class="c-title-panel">THÔNG TIN BỆNH NHÂN</div>
                                <div layout="row" class="c-padding-tb5">
                                    <div flex="15">Mã BN: </div>
                                    <div flex="33"><strong>{{thongtinbn.MA_BENH_NHAN}}</strong></div>
                                    <div flex="4">&nbsp;</div>
                                    <div flex="15">Số Phiếu: </div>
                                    <div flex="33"><strong>{{thongtinbn.SOPHIEUTHANHTOAN}}</strong></div>
                                </div>
                                <md-divider ></md-divider>
                                <div layout="row" class="c-padding-tb5">
                                    <div flex="15">Họ Tên: </div>
                                    <div flex="33" class="c-colorFF1744">{{thongtinbn.TEN_BENH_NHAN}}</div>
                                    <div flex="4">&nbsp;</div>
                                    <div flex="15">Số BHYT: </div>
                                    <div flex="33"><strong>{{thongtinbn.load.SOBAOHIEMYTE}}</strong></div>
                                </div>
                                <md-divider ></md-divider>

                                <div layout="row" class="c-padding-tb5">
                                    <div flex="15">Miễn giảm: </div>
                                    <div flex="33">{{thongtinbn.load.TYLEBAOHIEM + "%"}}</div>
                                    <div flex="4">&nbsp;</div>
                                    <div flex="15">Giá trị: </div>
                                    <div flex="33">{{thongtinbn.NGAY_BATDAU?thongtinbn.NGAY_BATDAU: thongtinbn.load.TUNGAY}}-{{thongtinbn.NGAY_HETHAN?thongtinbn.NGAY_HETHAN: thongtinbn.load.DENNGAY}}</div>

                                </div>
                                <md-divider ></md-divider>
                                <div layout="row" class="c-padding-tb5">
                                    <div flex="15">Địa chỉ: </div>
                                    <div flex="85">{{thongtinbn.load.DIACHI}}</div>

                                </div>
                                <md-divider ></md-divider>
                                <div layout="row" class="c-padding-tb5">
                                    <div flex="15">Khoa ĐT: </div>
                                    <div flex="85">{{thongtinbn.TEN_PHONGBAN}}</div>

                                </div>
                                <md-divider ></md-divider>
                                <div layout="row" class="c-padding-tb5">
                                    <div flex="15">Phòng: </div>
                                    <div flex="33">{{thongtinbn.load.TEN_PHONG_BENH}}</div>
                                    <div flex="4">&nbsp;</div>
                                    <div flex="15">Tuổi: </div>
                                    <div flex="33">{{thongtinbn.load.TUOI}}</div>


                                </div>
                                <md-divider ></md-divider>
                                <div layout="row" class="c-padding-tb5">
                                    <div flex="15">Giới tính: </div>
                                    <div flex="33">{{thongtinbn.GIOI_TINH == 0 || thongtinbn.load.GIOITINH == 0? "Nữ": "Nam"}}</div>
                                    <div flex="4">&nbsp;</div>
                                    <div flex="15">Đợt Điều Trị: </div>
                                    <div flex="33">{{thongtinbn.STT_DOTDIEUTRI}}</div>

                                </div>
                                <md-divider ></md-divider>
                                <div layout="row" class="c-padding-tb5">
                                    <div flex="15">Ngày Vào: </div>
                                    <div flex="15">{{thongtinbn.load.NGAYVAO}}</div>
                                    <div flex="4">&nbsp;</div>
                                    <div flex="15">Ngày ra: </div>
                                    <div flex="15">{{thongtinbn.load.NGAYRA}}</div>
                                    <div flex="4">&nbsp;</div>
                                    <div flex="15">Trạng Thái: </div>
                                    <div flex="15">{{thongtinbn.HINHTHUCRAVIEN}}</div>
                                </div>
                                <md-divider ></md-divider>
                                <div layout="row" class="c-padding-tb5">
                                    <div flex="15">Tổng Tiền BN: </div>
                                    <div flex="30" ><strong class="c-colorFF1744">{{thongtinthanhtoan.sotientt|format}}</strong> VND</div>

                                </div>
                                <div layout="row" class="c-padding-tb5">
                                    <div flex="50" style="color: #21409A;font-weight: bold">Số tiền đã thanh toán bằng Ví VNPT-MONEY: </div>
                                    <div flex="20" ><strong class="c-colorFF1744">{{thongtinthanhtoan.sotienttmoney|format}}</strong> VND</div>
                                </div>
                                <md-divider ></md-divider>
                                <div layout="row" class="c-padding-tb5" ng-if="thongtinthanhtoan.sotienttbidv > 0 || thongtinthanhtoan.sotienttvietinbank > 0">
                                    <div flex="20" style="color: #219a5f;font-weight: bold">BIDV: </div>
                                    <div flex="20" ><strong class="c-colorFF1744">{{thongtinthanhtoan.sotienttbidv|format}}</strong> VND</div>
                                    <div flex="20" style="color: #216b9a;font-weight: bold">VIETINBANK: </div>
                                    <div flex="20" ><strong class="c-colorFF1744">{{thongtinthanhtoan.sotienttvietinbank|format}}</strong> VND</div>
                                </div>
                            </div>
                            <div flex="grow" class="c-padding-l10" ng-if="selectedbn">
                                <md-button ng-if="!trangthaibn" class="md-raised md-primary" ng-click="showAdvanced($event,false)"><i class="material-icons c-icon-refresh">&#xE8A1;</i> THANH TOÁN</md-button>
                                <md-button ng-click="inbangke()" class="md-raised md-button"><i class="material-icons c-icon-refresh">&#xE8AD;</i> BẢNG KÊ</md-button>
                                <md-button ng-if="!trangthaibn && trangthaihuyhoadon" class="md-raised md-warn" ng-click="showAdvanced($event,true)"><i class="material-icons c-icon-refresh">&#xE929;</i> LẬP HÓA ĐƠN THAY THẾ</md-button>
                                <md-button ng-if="trangthaibn && thongtinthanhtoan.hgi_hoanung ==1 && (thongtinbn.LOAIVP == 'NOITRU' || thongtinbn.LOAIVP == 'BANT')" class="md-raised md-button" ng-click="hoanungbenhnhan($event,true)"><i class="material-icons c-icon-refresh">&#xE25C;</i> HOÀN ỨNG</md-button>
                                <md-button ng-if="(thongtinbn.LOAIVP == 'NOITRU' || thongtinbn.LOAIVP == 'BANT')" class="md-raised md-button" ng-click="inphieutongchiphi($event,true)"><i class="material-icons c-icon-refresh">&#xE25C;</i> IN HOÀN ỨNG</md-button>
                                <md-button ng-if="thongtinthanhtoan.hgi_hoanung ==1 && (thongtinbn.LOAIVP == 'NOITRU' || thongtinbn.LOAIVP == 'BANT')" class="md-raised md-button" ng-click="guibhxh($event,true)"><i class="material-icons c-icon-refresh">&#xE25C;</i> GỬI BHXH</md-button>
                            </div>
                            <div flex="grow" class="c-padding-l10 c-padding-t15">
                                <div class="c-title-panel">LẦN THANH TOÁN</div>
                                <c-grid id='listlanthanhtoan' data="listthanhtoan" config="listttConfig" footer="flantt" cfilter="{{false}}" vheight="{{150}}"></c-grid>
                            </div>
                            <div ng-if="listtamung.length > 0" flex="grow" class="c-padding-l10 c-padding-t15">
                                <div class="c-title-panel">LẦN TẠM ỨNG</div>
                                <c-grid id='listlantamung' data="listtamung" config="listtuConfig" footer="ftamung" cfilter="{{false}}" vheight="{{150}}"></c-grid>
                            </div>
                        </div>
                    </div>
                </md-content>
            </md-tab>
            <md-tab label="DANH SÁCH HÓA ĐƠN" ng-show="admin != 0">
                <md-content class="md-padding background-w ">
                    <div flex layout="row" layout-wrap >
                        <div flex="15" class="c-wrapdate">
                            <md-input-container class="c-height40 c-bottom0" flex>
                                <label>Từ ngày</label>
                                <md-datepicker ng-model="ngaybd" class="c-padding-r0"></md-datepicker>
                            </md-input-container>
                        </div>
                        <div flex="15" class="c-wrapdate">
                            <md-input-container class="c-height40 c-bottom0" flex>
                                <label>Đến ngày</label>
                                <md-datepicker ng-model="ngaykt" class="c-padding-r0"></md-datepicker>
                            </md-input-container>
                        </div>
                        <div flex="20" class="c-padding-l10">
                            <md-input-container flex style="width:100%">
                                <label>Quyển biên lai</label>
                                <md-select ng-model="filtermaquyenbienlai" multiple>
                                    <md-option ng-value="qbl.MA_QUYEN_BIENLAI" ng-repeat="qbl in dsqbl track by $index">{{qbl.KYHIEU_QUYEN_BIENLAI}}</md-option>
                                </md-select>
                            </md-input-container>
                        </div>
                        <div flex="20" class="c-padding-l10">
                            <md-input-container flex style="width:100%">
                                <label>Nhân viên</label>
                                <md-select class="c-bottom10 c-font-verdana" ng-model="filternhanvien"  placeholder="Nhân Viên">
                                    <md-option value="{{nv.MANHANVIEN}}" ng-repeat="nv in dsnhanvien track by $index">{{nv.TENNHANVIEN}}</md-option>
                                </md-select>
                            </md-input-container>
                        </div>
                        <div flex="15" class="c-padding-l10">
                            <md-input-container flex style="width:100%">
                                <label>Trạng thái HĐ</label>
                                <md-select class="c-bottom10 c-font-verdana" ng-model="loaihd"  placeholder="Loại viện phí">
                                    <md-option value="-1">Tất cả</md-option>
                                    <md-option value="0">Chưa phát hành</md-option>
                                    <md-option value="3">Đang phát hành</md-option>
                                    <md-option value="1">Đã phát hành</md-option>
                                    <md-option value="2">Hủy phát hành</md-option>
                                </md-select>
                            </md-input-container>
                        </div>
                        <div flex="15" class="c-padding-l10">
                            <md-input-container flex style="width:100%">
                                <label>Loại HĐ</label>
                                <md-select class="c-bottom10 c-font-verdana" ng-model="loaibnhd" placeholder="Loại viện phí">
                                    <md-option value="-1">Tất cả</md-option>
                                    <md-option value="1">Nội trú</md-option>
                                    <md-option value="0">Ngoại trú</md-option>
                                    <md-option value="2">Dịch vụ</md-option>
                                    <md-option value="3">Hóa đơn bán lẻ</md-option>
                                </md-select>
                            </md-input-container>
                        </div>
                        <div flex="15" class="c-padding-l10">
                            <md-input-container flex style="width:100%">
                                <label>Loại BN</label>
                                <md-select class="c-bottom10 c-font-verdana" ng-model="filtercobhyt" placeholder="">
                                    <md-option value="-1">Tất cả</md-option>
                                    <md-option value="1">BHYT</md-option>
                                    <md-option value="0">Thu phí</md-option>
                                </md-select>
                            </md-input-container>
                        </div>
                        <div flex="40">
                            <md-button ng-click="laydshd()" class="md-raised md-primary c-top6" flex><i class="material-icons c-icon-refresh">cached</i> Làm mới</md-button>
                            <md-button ng-click="indshoadon()" class="md-raised md-primary c-top6" flex><i class="material-icons  c-icon-refresh">&#xE0C3;</i> In danh sách</md-button>
                        </div>
                    </div>
                    <div flex>
                        <c-grid data="listdshd" id='lissthdshd' footer="fdshd"  config="listdshdConfig" cfilter="{{true}}" vheight="{{350}}" vwidth="{{150}}"></c-grid>
                    </div>
                </md-content>
            </md-tab>
            <md-tab label="PHÁT HÀNH HÓA ĐƠN LẺ">
                <md-content class="md-padding background-w ">
                    <div flex="80" flex-offset="10" layout="row" layout-wrap class="md-whiteframe-z1 c-form c-font-verdana" style="max-height: none">
                        <md-toolbar>
                            <div class="md-toolbar-tools">
                                <h2>TẠO HÓA ĐƠN BÁN LẺ</h2>
                                <span flex></span>
                            </div>
                        </md-toolbar>
                        <div flex="60" class="c-padding-l10">
                            <md-input-container flex style="width:100%">
                                <label>Quyển biên lai</label>
                                <md-select ng-change="chonbienlaihoadon()" ng-model="hoadonle.maquyenbienlai">
                                    <md-option ng-value="qbl.MA_QUYEN_BIENLAI" ng-repeat="qbl in dsqbl track by $index">{{qbl.KYHIEU_QUYEN_BIENLAI}}</md-option>
                                </md-select>
                            </md-input-container>
                        </div>
                        <div flex="40">
                            <md-input-container class="md-icon-float md-block">
                                <!-- Use floating label instead of placeholder -->
                                <label>Số Biên Lai</label>
                                <md-icon class="material-icons">&#xE06A;</md-icon>
                                <input ng-model="hoadonle.sobienlai" type="text" disabled>
                            </md-input-container>
                        </div>
                        <div flex="60">
                            <md-input-container class="md-icon-float md-block">
                                <!-- Use floating label instead of placeholder -->
                                <label>Tên khách hàng (Customer’s name)</label>
                                <md-icon class="material-icons">&#xE853;</md-icon>
                                <input ng-model="hoadonle.tenkh" type="text">
                            </md-input-container>
                        </div>
                        <div flex="40">
                            <md-input-container class="md-icon-float md-block">
                                <!-- Use floating label instead of placeholder -->
                                <label>Mã số thuế</label>
                                <md-icon class="material-icons">&#xE03F;</md-icon>
                                <input ng-model="hoadonle.masothue" type="text">
                            </md-input-container>
                        </div>
                        <div flex="60">
                            <md-input-container class="md-icon-float md-block">
                                <!-- Use floating label instead of placeholder -->
                                <label>Tên đơn vị</label>
                                <md-icon class="material-icons">&#xE8D3;</md-icon>
                                <input ng-model="hoadonle.tendonvi" type="text">
                            </md-input-container>
                        </div>
                        <div flex="40">
                            <md-input-container class="md-icon-float md-block">
                                <!-- Use floating label instead of placeholder -->
                                <label>Hình thức thanh toán (Kind of Payment)</label>
                                <md-icon class="material-icons">&#xE8A1;</md-icon>
                                <md-select class="c-bottom10 c-font-verdana" ng-model="hoadonle.hinhthuctt" >
                                    <md-option value="Tiền mặt">Thanh toán tiền mặt</md-option>
                                    <md-option value="Ví điện tử">Ví điện tử VNPT-MONEY</md-option>
                                    <md-option value="Chuyển khoản">Thanh toán chuyển khoản</md-option>
                                    <md-option value="Thẻ tín dụng">Thanh toán thẻ tín dụng</md-option>
                                    <md-option value="Hình thức HDDT">Hình thức HDDT</md-option>
                                    <md-option value="Tiền mặt hoặc chuyển khoản">Hình thức thanh toán tiền mặt hoặc chuyển khoản</md-option>
                                    <md-option value="Bù trừ">Thanh toán bù trừ</md-option>
                                </md-select>
                            </md-input-container>
                        </div>
                        <div flex="60">
                            <md-input-container class="md-icon-float md-block">
                                <!-- Use floating label instead of placeholder -->
                                <label>Địa chỉ</label>
                                <md-icon class="material-icons">&#xE0BA;</md-icon>
                                <input ng-model="hoadonle.diachi" type="text">
                            </md-input-container>
                        </div>
                        <div flex="40">
                            <md-input-container class="md-icon-float md-block">
                                <!-- Use floating label instead of placeholder -->
                                <label>Số điện thoại</label>
                                <md-icon class="material-icons">&#xE0CF;</md-icon>
                                <input ng-model="hoadonle.sodienthoai" type="text">
                            </md-input-container>
                        </div>

                        <div flex="60">
                            <md-input-container class="md-icon-float md-block">
                                <!-- Use floating label instead of placeholder -->
                                <label>Dân tộc</label>
                                <md-icon class="material-icons">&#xE8A6;</md-icon>
                                <input ng-model="hoadonle.dantoc" type="text">
                            </md-input-container>
                        </div>
                        <div flex="40">
                            <md-input-container class="md-icon-float md-block" flex style="width:100%">
                                <!-- Use floating label instead of placeholder -->
                                <label>Giới tính</label>
                                <md-icon class="material-icons">&#xE87C;</md-icon>
                                <md-select class="c-bottom10 c-font-verdana" ng-model="hoadonle.gioitinh" >
                                    <md-option value=" "> </md-option>
                                    <md-option value="0">Nữ</md-option>
                                    <md-option value="1">Nam</md-option>
                                </md-select>

                            </md-input-container>
                        </div>
                        <div flex="100" layout="row" ng-repeat="chitietthanhtoan in hoadonle.chitietthanhtoan">
                            <div flex="50">
                                <md-content layout-padding layout="column" class="custome-autocomplete">
                                    <form ng-submit="$event.preventDefault()">
                                        <md-autocomplete
                                                ng-disabled="isDisabled"
                                                md-no-cache="noCache"
                                                md-selected-item="selectedItem"
                                                md-search-text-change="searchTextChange(searchText,$index)"
                                                md-search-text="searchText"
                                                md-selected-item-change="selectedItemChange(item,$index)"
                                                md-items="item in querySearch(searchText)"
                                                md-item-text="item.TENDICHVU"
                                                md-min-length="0"
                                                placeholder="Tên dịch vụ, hàng hóa"
                                                md-menu-class="autocomplete-custom-template">
                                            <md-item-template>
                              <span class="item-title">
                                <span> {{item.TENDICHVU}} </span>
                              </span>
                                            </md-item-template>
                                        </md-autocomplete>
                                    </form>
                                </md-content>
                            </div>
                            <div flex="10" style="display: none">
                                <md-input-container class="md-icon-float md-block">
                                    <input ng-model="chitietthanhtoan.mahanghoa" type="text">
                                </md-input-container>
                            </div>
                            <div flex="10">
                                <md-input-container class="md-icon-float md-block">
                                    <!-- Use floating label instead of placeholder -->
                                    <label>Đơn vị tính</label>
                                    <md-icon class="material-icons">calendar_view_day</md-icon>
                                    <input ng-model="chitietthanhtoan.dvt" type="text">
                                </md-input-container>
                            </div>
                            <div flex="10">
                                <md-input-container class="md-icon-float md-block">
                                    <!-- Use floating label instead of placeholder -->
                                    <label>Số lượng</label>
                                    <md-icon class="material-icons">playlist_add</md-icon>
                                    <input ng-model="chitietthanhtoan.soluong" type="text" ng-keyup="thaydoigiatri($index)">
                                </md-input-container>
                            </div>
                            <div flex="15">
                                <md-input-container class="md-icon-float md-block">
                                    <!-- Use floating label instead of placeholder -->
                                    <label>Đơn giá</label>
                                    <md-icon class="material-icons">&#xE227;</md-icon>
                                    <input ng-model="chitietthanhtoan.dongia" type="text" ng-keyup="thaydoigiatri($index)">
                                </md-input-container>
                            </div>
                            <div flex="15">
                                <md-input-container class="md-icon-float md-block">
                                    <!-- Use floating label instead of placeholder -->
                                    <label>Thành tiền</label>
                                    <md-icon class="material-icons">monetization_on</md-icon>
                                    <input ng-model="chitietthanhtoan.thanhtien" type="text" ng-keydown="addnewrow($event,$index)">
                                </md-input-container>
                            </div>
                        </div>
                        <div class="c-form-bottom">
                            <md-button ng-click="taohoadonbanle(true)" class="md-raised md-primary"> <i class="material-icons c-icon-refresh">&#xE877;</i> TẠO VÀ IN HÓA ĐƠN</md-button>
                            <md-button ng-click="taohoadonbanle(false)" class="md-raised"> <i class="material-icons c-icon-refresh">&#xE876;</i> TẠO HÓA ĐƠN</md-button>
                        </div>


                    </div>
                </md-content>
            </md-tab>
            <md-tab label="VNPT-MONEY" ng-show="admin != 0">
                <md-content class="md-padding background-w ">
                    <div flex layout="row" layout-wrap >
                        <div flex="15" class="c-wrapdate">
                            <md-input-container class="c-height40 c-bottom0" flex>
                                <label>Từ ngày</label>
                                <md-datepicker ng-model="ngaymoneybd" class="c-padding-r0"></md-datepicker>
                            </md-input-container>
                        </div>
                        <div flex="15" class="c-wrapdate">
                            <md-input-container class="c-height40 c-bottom0" flex>
                                <label>Đến ngày</label>
                                <md-datepicker ng-model="ngaymoneykt" class="c-padding-r0"></md-datepicker>
                            </md-input-container>
                        </div>

                        <div flex="40">
                            <md-button ng-click="laydsmoney()" class="md-raised md-primary c-top6" flex><i class="material-icons c-icon-refresh">cached</i> Làm mới</md-button>
                            <md-button ng-click="indsmoney()" class="md-raised md-primary c-top6" flex><i class="material-icons  c-icon-refresh">&#xE0C3;</i> In danh sách</md-button>
                            <md-select style="display:inline-block" flex="20" class="c-no-margin c-bottom10" ng-model="loaifilemoney" placeholder="Loại File" class="md-no-underline">
                                <md-option value="pdf">PDF</md-option>
                                <md-option value="xlsx">EXCEL</md-option>
                            </md-select>
                        </div>
                    </div>
                    <div flex>
                        <c-grid data="listdsmoney" id='lissthdshdmoney' footer="fdsmoney"  config="listdsmoneyConfig" cfilter="{{true}}" vheight="{{350}}" vwidth="{{100}}"></c-grid>
                    </div>
                </md-content>
            </md-tab>
            <md-tab label="Chuyển khoản QR" ng-show="admin != 0">
                <md-content class="md-padding background-w ">
                    <div flex layout="row" layout-wrap >
                        <div flex="15" class="c-wrapdate">
                            <md-input-container class="c-height40 c-bottom0" flex>
                                <label>Từ ngày</label>
                                <md-datepicker ng-model="ngaybankbd" class="c-padding-r0"></md-datepicker>
                            </md-input-container>
                        </div>
                        <div flex="15" class="c-wrapdate">
                            <md-input-container class="c-height40 c-bottom0" flex>
                                <label>Đến ngày</label>
                                <md-datepicker ng-model="ngaybankkt" class="c-padding-r0"></md-datepicker>
                            </md-input-container>
                        </div>
                        <div flex="20" class="c-padding-l10">
                            <md-input-container flex style="width:100%">
                                <label>Ngân hàng</label>
                                <md-select class="c-no-margin c-bottom10" ng-model="bankname" placeholder="" >
                                    <md-option value="BIDV">BIDV</md-option>
                                    <md-option value="VIETINBANK">VIETINBANK</md-option>
                                    <md-option value="VIETCOMBANK">VIETCOMBANK</md-option>
                                    <md-option value="HD_BANK_KIOS">HD BANK</md-option>
                                </md-select>
                            </md-input-container>
                        </div>

                        <div flex="40">
                            <md-button ng-click="laydsbank()" class="md-raised md-primary c-top6" flex><i class="material-icons c-icon-refresh">cached</i> Làm mới</md-button>
                            <md-button ng-click="indsbank()" class="md-raised md-primary c-top6" flex><i class="material-icons  c-icon-refresh">&#xE0C3;</i> In danh sách</md-button>
                            <md-select style="display:inline-block" flex="20" class="c-no-margin c-bottom10" ng-model="loaifilebank" placeholder="Loại File" class="md-no-underline">
                                <md-option value="pdf">PDF</md-option>
                                <md-option value="xlsx">EXCEL</md-option>
                            </md-select>
                        </div>
                    </div>
                    <div flex>
                        <c-grid data="listdsbank" id='lissthdshdbank' footer="fdsbank"  config="listdsbankConfig" cfilter="{{true}}" vheight="{{350}}" vwidth="{{100}}"></c-grid>
                    </div>
                </md-content>
            </md-tab>
        </md-tabs>
    </md-content>
    <div id="snackbar" class='show' ng-if="notifyMoney">{{content}}</div>
</div>
<%@include file="template/dialog_vienphi.jsp"%>
<%@include file="template/table_directive.jsp"%>
<%@include file="template/loading.jsp"%>
<%@include file="template/thongbaohuy.jsp"%>
<%@include file="template/hoadonthaythe.jsp"%>
<%@include file="template/dialog_hoanung.jsp"%>
<%@include file="js/include_js.jsp"%>
<jsp:include page="../kiengiang/xml/checkxml.jsp"/>

</body>
</html>