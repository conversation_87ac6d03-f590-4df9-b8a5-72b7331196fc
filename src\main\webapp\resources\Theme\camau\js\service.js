myapp.service('DataService', function($http, $q) {
    
    this.getData = function (url,data) {
         var defered = $q.defer();
        $http({
            method : "GET",
            url : url+"?url="+convertArray(data)
        }).then(function mySuccess(response) {
            defered.resolve(response.data); 
        }, function myError(response) {
             defered.reject(response);
        });
        return defered.promise;
    }
    this.postData = function (url,dt) {
         var defered = $q.defer();
        var t = "";
        dt.forEach(function(_o,index) {
            if(Array.isArray(_o.data)) {
                console.log("v",_o.data);
                t+=_o.param+'='+convertArray(_o.data);
            } else {
                t+=_o.param+'='+_o.data;
            }
            if(index < dt.length - 1) {
                t+= '&';
            }
        })
        $http({
            method : "POST",
            url : url+'?'+t,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
            }
            
                    
        }).then(function mySuccess(response) {
            console.log("response", response);
             defered.resolve(response.data); 
            
        }, function myError(response) {
            defered.reject(response);
        });
        return defered.promise;
    }
    this.postDataJSON = function (url,dt) {
        var defered = $q.defer();
        $http({
            method : "POST",
            url : url,
            headers: {
                'Content-Type': 'application/json; charset=UTF-8'
            },
            data: JSON.stringify(dt)
        }).then(function mySuccess(response) {
            defered.resolve(response.data);

        }, function myError(response) {
            defered.reject(response);
        });
        return defered.promise;
    }
    function convertArray(arr) {
        if (arr != null && arr.length > 0) {
            var str = arr[0].toString().replace("```", "");
            str=str.replace("&", "+");

            for (i = 1; i < arr.length; i++) {
                if(arr[i] == undefined){
                    arr[i] ="";
                }
                str += "```" + arr[i].toString().replace("```", "").replace("&","+");
            }
            return encodeURIComponent(str);
        }
    }
    
    this.thanhtoanall = function ($scope) {
        var t = 1;
        $scope.chitietthanhtoan.forEach(function(_obj) {
            if(_obj.selected != true) t = 0;
        })
        if($scope.loaivienphi == 'NOITRUTAMUNG') {
            return 0
        }
        return t;
    }
    
    this.thanhtoan = function(objData,sophieu,inhd,callback) {
        if(objData.thongtinbn.LOAIVP == 'BHYT') {
            this.thanhtoanbhyt(objData,sophieu,inhd,callback);
        } else if (objData.thongtinbn.LOAIVP == 'THUPHI') {
            this.thanhtoanthuphi(objData,sophieu,inhd,callback);
        } else if (objData.thongtinbn.LOAIVP == 'NTDICHVU') {
            this.thanhtoandichvu(objData,sophieu,inhd,callback);
        } else {
            this.thanhtoantienbenhnhan(objData,sophieu,inhd,callback);
        }
    }
    this.thanhtoanhoanung_khongchitiet = function($scope,sophieu,inhd,callback) {
        var self = this;
        console.log("$scope.thongtinthanhtoan.sotienbntra", $scope.thongtinthanhtoan.sotienbntra)
        $.post('cmu_post',{
            url: [
                $scope.thongtinthanhtoan.dvtt,
                $scope.thongtinbn.SOPHIEUTHANHTOAN,
                parseDateToString($scope.thongtinthanhtoan.ngaythu),
                $scope.thongtinthanhtoan.manv,
                Number($scope.thongtinthanhtoan.sotienthoilai).toFixed(2),
                $scope.thongtinbn.MA_BENH_NHAN,
                $scope.thongtinbn.load.TYLEBAOHIEM,
                $scope.thongtinthanhtoan.kyhieu_quyenbienlai,
                $scope.thongtinthanhtoan.maquyenbienlai,
                $scope.thongtinbn.SOVAOVIEN,
                $scope.thongtinbn.SOVAOVIEN_DT,
                1,
                $scope.thongtinthanhtoan.tientamung,
                'CMU_THANHTOAN_HOANUNG'
            ].join("```")

        }).done(function(data) {
            if (data == "5") {
                callback(inhd,data,{message: "Trong quá trình thanh toán, có phát sinh vấn đề. Vui lòng xem lại thông tin để thanh toán lại"});
            } else if (data != "") {
                self.lockbienlai($scope,data,$scope.thongtinthanhtoan.maquyenbienlai,0,0,0);
                $scope.thongtinbn['SOVAOVIEN_NOI'] = $scope.thongtinbn.SOVAOVIEN;
                $scope.thongtinbn['SOVAOVIEN_DT'] = $scope.thongtinbn.SOVAOVIEN_DT;
                $scope.thongtinbn['NOITRU'] = 1;
                $scope.thongtinbn['COBHYT'] = $scope.thongtinbn.SOBAOHIEMYTE != ""? 1:0;
                callback(inhd,data);
            }
        })
    }
    
    
    this.thanhtoanbhyt = function($scope,sophieu,inhd,callback) {
        var self = this;
        var arr = [
                    $scope.thongtinbn.SOPHIEUTHANHTOAN,
                    $scope.thongtinthanhtoan.manv, 
                    $scope.thongtinthanhtoan.sotientt, 
                    $scope.thongtinthanhtoan.sotienbntra,
                    $scope.thongtinthanhtoan.sotienthoilai, 
                    "false", 
                    0, //SO BIEN LAI 0
                    $scope.thongtinthanhtoan.ghichu == undefined? " ": $scope.thongtinthanhtoan.ghichu, 
                    $scope.thongtinthanhtoan.dvtt,
                    $scope.thongtinthanhtoan.sotientt, 
                    parseDateToString($scope.thongtinthanhtoan.ngaythu),
                    this.thanhtoanall($scope), 
                    $scope.thongtinthanhtoan.maquyenbienlai, 
                    $scope.thongtinbn.TEN_BENH_NHAN,0];
         $.post('themlantt_cobhyt_giamtai_svv_moi_miengiam', 
                {
                    sovaovien: $scope.thongtinbn.SOVAOVIEN, 
                    url: c_convert_to_string(arr),
                    sotienphaitra: $scope.thongtinthanhtoan.sotientt, 
                    sotienthanhtoan: $scope.thongtinthanhtoan.sotienbntra,
                    sotienphaitt: $scope.thongtinthanhtoan.sotientt, 
                    sophieu: sophieu,
                    sotienmiengiam: $scope.thongtinthanhtoan.tienmiengiam, 
                    sotiensaumiengiam: $scope.thongtinthanhtoan.sotienbntra
                }).done(function (data) {
                    if (data == "5") {
                        return callback(inhd,data,{message: "Trong quá trình thanh toán, có phát sinh vấn đề. Vui lòng xem lại thông tin để thanh toán lại"});
                    }
                    if (data != "") {
                        self.lockbienlai($scope,data,$scope.thongtinthanhtoan.maquyenbienlai,0,0,0);
                        $scope.thongtinbn['SOVAOVIEN_NOI'] = 0;
                        $scope.thongtinbn['SOVAOVIEN_DT'] = 0;
                        $scope.thongtinbn['NOITRU'] = 0;
                        $scope.thongtinbn['COBHYT'] = 1;
                                var arr = [$scope.thongtinthanhtoan.dvtt, 
                        "kb_" + $scope.thongtinbn.ID_TIEPNHAN,
                        $scope.thongtinbn.SOPHIEUTHANHTOAN,
                        parseDateToString($scope.thongtinthanhtoan.ngaythu), $scope.thongtinbn.SOVAOVIEN];
                        $.post("update_trangthai_xacnhanthanhtoan_svv", {url: c_convert_to_string(arr)})
                                .done(function (data) {
                        });
                        callback(inhd,data)
                        
                    }
        }); 
    }
    
    this.thanhtoanthuphi = function($scope,sophieu,inhd,callback) {
        var self = this;
      
       
        if ($scope.thongtinthanhtoan.hoantatkham > 2 ) {
            if ($scope.thongtinthanhtoan.trutamung == 0) {
                $scope.thongtinthanhtoan.trutamung = 1;
            } else {
                $scope.thongtinthanhtoan.trutamung = 0;
            }
            
        }
        console.log("$scope.thongtinthanhtoan.trutamung = 1;", $scope.thongtinthanhtoan.trutamung)
        var arr = [
                $scope.thongtinbn.ID_TIEPNHAN, 
                $scope.thongtinthanhtoan.manv, 
                $scope.thongtinthanhtoan.sotientt,
                $scope.thongtinthanhtoan.sotienbntra,
                $scope.thongtinthanhtoan.sotienthoilai,
                "false",
                $scope.thongtinthanhtoan.sobienlai,
                $scope.thongtinthanhtoan.ghichu == undefined? " ": $scope.thongtinthanhtoan.ghichu, 
                $scope.thongtinthanhtoan.dvtt,
                parseDateToString($scope.thongtinthanhtoan.ngaythu), 
                $scope.thongtinbn.SOVAOVIEN, 
                this.thanhtoanall($scope), 
                $scope.thongtinthanhtoan.maquyenbienlai,
                $scope.thongtinbn.TEN_BENH_NHAN,
                $scope.thongtinthanhtoan.hoantatkham > 2 ? $scope.thongtinthanhtoan.trutamung : 0,
                0
            ];
        $.post('themlantt_khongbhyt_svv_moi_miengiam', {
                    url: c_convert_to_string(arr),
                    sophieu: sophieu,
                    sotienmiengiam: $scope.thongtinthanhtoan.tienmiengiam,
                    sotiensaumiengiam: $scope.thongtinthanhtoan.sotienbntra
                }).done(function (data) {
                    if (data == "5") {
                        return callback(inhd,data,{message: "Trong quá trình thanh toán, có phát sinh vấn đề. Vui lòng xem lại thông tin để thanh toán lại"});
                    }
                    if (data != "") {
                        self.lockbienlai($scope,data,$scope.thongtinthanhtoan.maquyenbienlai,0,0,0);
                        $scope.thongtinbn['SOVAOVIEN_NOI'] = 0;
                        $scope.thongtinbn['SOVAOVIEN_DT'] = 0;
                        $scope.thongtinbn['NOITRU'] = 0;
                        $scope.thongtinbn['COBHYT'] = 0;
                        callback(inhd,data);
                    }
                });    
    }
    
    this.thanhtoandichvu = function($scope,sophieu,inhd,callback) {
        var self = this;
        this.postData("cmu_post",
                                [   
                                    {
                                        data:[
                                            $scope.thongtinthanhtoan.dvtt,
                                            $scope.thongtinbn.STT_LANTT,
                                            $scope.thongtinbn.SOPHIEUTHANHTOAN,
                                            $scope.thongtinthanhtoan.manv,
                                            $scope.thongtinthanhtoan.sotientt,
                                            $scope.thongtinthanhtoan.sotienbntra,
                                            $scope.thongtinthanhtoan.sotienthoilai,
                                            0,
                                            $scope.thongtinthanhtoan.sobienlai =="" ? 0 : $scope.thongtinthanhtoan.sobienlai,
                                            '',
                                            parseDateToString($scope.thongtinthanhtoan.ngaythu),
                                            $scope.thongtinbn.STT_BENHAN,
                                            $scope.thongtinbn.STT_DOTDIEUTRI,
                                            $scope.thongtinbn.MA_BENH_NHAN,
                                            0,
                                            $scope.thongtinbn.load.SOBAOHIEMYTE,
                                            $scope.thongtinthanhtoan.sotientt,
                                            $scope.thongtinthanhtoan.sobienlai =="" ? 0 : $scope.thongtinthanhtoan.sobienlai,
                                            $scope.thongtinthanhtoan.kyhieu_quyenbienlai,
                                            $scope.thongtinthanhtoan.maquyenbienlai,
                                            $scope.thongtinbn.SOVAOVIEN,
                                            $scope.thongtinbn.SOVAOVIEN_DT,
                                            "!!!"+sophieu+"!!!",
                                            0,
                                            0,
                                            0,
                                            parseDateToString($scope.thongtinthanhtoan.ngaythu),
                                            $scope.thongtinthanhtoan.sotienbntra,
                                            0,
                                            'CMU_LTT_DICHVU_INSERT'
                                            
                                        ],
                                        param: 'url'
                                    }
                                ]
                            )
                    .then(function(dt){ 
                        if (data == "5") {
                            return callback(inhd,data,{message: "Trong quá trình thanh toán, có phát sinh vấn đề. Vui lòng xem lại thông tin để thanh toán lại"});
                        }
                        if(dt != "") {
                            $scope.thongtinbn.STT_LANTT = $scope.thongtinbn.STT_LANTT.replace(/_\d/g,'') + "_"+dt
                            var data = $scope.thongtinbn.STT_LANTT;
                            self.lockbienlai($scope,data,$scope.thongtinthanhtoan.maquyenbienlai,0,0,0);
                            $scope.thongtinbn['SOVAOVIEN_NOI'] = $scope.thongtinbn.SOVAOVIEN;
                            $scope.thongtinbn['SOVAOVIEN_DT'] = $scope.thongtinbn.SOVAOVIEN_DT;
                            $scope.thongtinbn['NOITRU'] = -1;
                            $scope.thongtinbn['COBHYT'] = $scope.thongtinbn.SOBAOHIEMYTE != ""? 1:0;
                            callback(inhd,data);
                        }
                    })
    }
    
    this.thanhtoantienbenhnhan = function($scope,sophieu,inhd,callback) {
        var self = this;
        console.log("$scope.thongtinthanhtoan.sotienbntra", $scope.thongtinthanhtoan.sotienbntra)
        $.post('noitru_lantt_insert_svv_moi_miengiam',{
            url: c_convert_to_string([
                                            $scope.thongtinbn.SOPHIEUTHANHTOAN,
                                            Number($scope.thongtinthanhtoan.sotientt).toFixed(2),
                                            Number($scope.thongtinthanhtoan.sotienbntra).toFixed(2),
                                            Number($scope.thongtinthanhtoan.sotienthoilai).toFixed(2),
                                            0,
                                            parseDateToString($scope.thongtinthanhtoan.ngaythu),
                                            $scope.thongtinbn.STT_BENHAN,
                                            $scope.thongtinbn.STT_DOTDIEUTRI,
                                            $scope.thongtinbn.MA_BENH_NHAN,
                                            Number($scope.thongtinthanhtoan.sotientt).toFixed(2),
                                            0,
                                            $scope.thongtinthanhtoan.kyhieu_quyenbienlai,
                                            $scope.thongtinthanhtoan.maquyenbienlai,
                                            parseDateToString($scope.thongtinthanhtoan.ngaythu),
                                            $scope.thongtinbn.SOVAOVIEN,
                                            $scope.thongtinbn.SOVAOVIEN_DT,
                                            this.thanhtoanall($scope),
                                            $scope.thongtinthanhtoan.trutamung == 0? 1:0,
                                            $scope.thongtinthanhtoan.tienmiengiam > 0? 1: 0,
                                            $scope.thongtinthanhtoan.tienmiengiam,
                                            parseDateToString($scope.thongtinthanhtoan.ngaythu)
                                            ,0,"0",
                                            $scope.thongtinthanhtoan.ghichu == undefined? " ": ($scope.thongtinthanhtoan.ghichu + " ")
                                        ]),
            tongtien:        Number($scope.thongtinthanhtoan.sotientt).toFixed(2),
            sophieu: sophieu,
            tongtiensaumiengiam:  Number($scope.thongtinthanhtoan.sotienbntra).toFixed(2),
            tongtientmaung: $scope.thongtinthanhtoan.tientamung == "" || $scope.thongtinthanhtoan.trutamung == 1 ? 0: $scope.thongtinthanhtoan.tientamung,
            ghichu: $scope.thongtinthanhtoan.ghichu == undefined? " ": $scope.thongtinthanhtoan.ghichu
                                        
        }).done(function(data) {
            if (data == "-1") {
                callback(inhd,data,{message: "Còn cận lâm sàng chưa thực hiện"})
            } else if (data == "5") {
                callback(inhd,data,{message: "Trong quá trình thanh toán, có phát sinh vấn đề. Vui lòng xem lại thông tin để thanh toán lại"});
            } else if (data != "") {
                self.lockbienlai($scope,data,$scope.thongtinthanhtoan.maquyenbienlai,0,0,0);
                $scope.thongtinbn['SOVAOVIEN_NOI'] = $scope.thongtinbn.SOVAOVIEN;
                $scope.thongtinbn['SOVAOVIEN_DT'] = $scope.thongtinbn.SOVAOVIEN_DT;
                $scope.thongtinbn['NOITRU'] = 1;
                $scope.thongtinbn['COBHYT'] = $scope.thongtinbn.SOBAOHIEMYTE != ""? 1:0;
                callback(inhd,data);           
            }
        })
    }
    
    this.inserthoadon = function($scope) {
        if ($scope.thongtinbn.load == undefined) {
           $scope.thongtinbn.load = {
               DIACHI: "",
               SOBAOHIEMYTE: ""
           };
        
        }

        return $.post("insert_hoadon_kb", {
            stt_lantt: $scope.thongtinthanhtoan.STT_LANTT,
            sovaovien: $scope.thongtinbn.SOVAOVIEN,
            ma_lan_kham: $scope.thongtinbn.SOPHIEUTHANHTOAN,
            ma_bn: $scope.thongtinbn.MA_BENH_NHAN,
            ho_ten: $scope.thongtinbn.TEN_BENH_NHAN + "!!!" + $scope.thongtinthanhtoan.hinhthucthanhtoan + "!!!" + ($scope.thongtinthanhtoan.gophoadondv === true? 1: 0),
            diachi: $scope.thongtinbn.load.DIACHI ,
            ma_the: $scope.thongtinbn.load.SOBAOHIEMYTE == undefined?'':$scope.thongtinbn.load.SOBAOHIEMYTE,
            thanhthien_chitra:$scope.thongtinthanhtoan.tmiengiam == true ? Number($scope.thongtinthanhtoan.sotienbntra):Number($scope.thongtinthanhtoan.sotientt).toFixed(),
            ma_lk: $scope.thongtinbn.SOPHIEUTHANHTOAN,
            sovaovien_noi: $scope.thongtinbn.SOVAOVIEN_NOI,
            sovaovien_dt: $scope.thongtinbn.SOVAOVIEN_DT,
            noitru: $scope.thongtinbn.NOITRU,
            cobhyt: $scope.thongtinbn.COBHYT
            }); 
    }
    
    this.lockbienlai = function($scope,data,maquyenbienlai,sobienlai,trangthai,huy) {
        return $.post("unlock_xoa_bienlai", {
                                dvtt: $scope.thongtinthanhtoan.dvtt,
                                nhanvienthu: $scope.thongtinthanhtoan.manv,
                                maquyenbienlai: maquyenbienlai,
                                sobienlai: sobienlai,
                                trangthai: trangthai,
                                keyQL: data + "-" +$scope.thongtinthanhtoan.manv,
                                huy: huy,
                                log: ""
                        });
    }
    
    this.laphoadonthaythe = function($scope,data,fkey) {
        console.log("$scope.thongtinthanhtoan.hienthistgchitiet", $scope.thongtinthanhtoan)
        if($scope.thongtinthanhtoan.apdung7dong_kgg == 1)
        {
            return this.phathanhhoadon_tach7dong($scope, data,keyold);
        }
        if($scope.thongtinthanhtoan.apdungchitiet_agg == 1)
        {
            return this.phathanhhoadon_agg($scope, data,keyold);
        }
        if($scope.thongtinthanhtoan.apdungnhombk_hgi == 1)
        {
            return this.phathanhhoadon_nhombk_hgi($scope, data,keyold);
        }

        if($scope.thongtinthanhtoan.hienthistgchitiet == 1) {
            return this.phathanhhoadonstgchitiet($scope, data);
        }
        var nguoibenh = $("#cmu_ctlaynguoibenh").val()
        var nd_all = '';
        var sl_all = '';
        var dg_all = '';
        var tt_all = '';
        var tt78_all = '';
        var dvt_all = '';
        var issum_all = '';
        var ttchiphi_all = '';
        var arr = [];
        var arrtt78 = [];
        var arr_chiphitt78 = [];
        var noitru = "";
        var tilebhyt = $scope.thongtinbn.load.TYLEBAOHIEM;
        var baohiemchi = 0;
        var tongtienbn = 0;
        var tongtienbntt78 = 0;
        var tongtientt78 = 0;
        var paymentmethod =  $scope.thongtinthanhtoan.hinhthucthanhtoan;
        var tendonvi = $scope.thongtinthanhtoan.tendonvi == undefined? "":escapeXml($scope.thongtinthanhtoan.tendonvi);
        var masothue  = $scope.thongtinthanhtoan.masothue == undefined? "":$scope.thongtinthanhtoan.masothue;
        var sodienthoai = "";
        var vat = $scope.thongtinthanhtoan.vat;
        var vatAmount = "0";
        var chitietNgoaitru = 0;
        if ($scope.thongtinbn.LOAIVP == 'BHYT' || $scope.thongtinbn.LOAIVP == 'THUPHI') {
            if ($scope.thongtinthanhtoan.dvtt == 96145) {
                chitietNgoaitru = 1;
            }
            if($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  ( chitietNgoaitru == 1 || $scope.thongtinbn.LOAIVP == 'THUPHI' || ($scope.thongtinbn.LOAIVP == 'BHYT' && $scope.chitietthanhtoan.length < 6))) {
                if ($scope.thongtinbn.LOAIVP == 'BHYT') {
                    $scope.chitietthanhtoan.forEach(function(_obj,_index) {
                        nd_all += escapeXml(_obj.NOIDUNG);
                        if ($scope.thongtinthanhtoan.hienthistg==1) {
                            sl_all += '1';
                            dg_all += _obj.THANH_TIEN.toFixed(2);
                            dvt_all += 'Lần';
                        } else {
                            if(nguoibenh == 0) {
                                sl_all += _obj.SO_LUONG;
                                dg_all += _obj.DON_GIA;
                                dvt_all += _obj.DVT;
                            } else {
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                            }

                        }

                        tt_all +=  nguoibenh == 1?_obj.NGUOI_BENH.toFixed(2):_obj.THANH_TIEN.toFixed(2) ;
                        tt78_all +=  _obj.NGUOI_BENH.toFixed(2);
                        ttchiphi_all +=  _obj.THANH_TIEN.toFixed(2);
                        tongtienbn+=  nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN);
                        tongtienbntt78+=  Number(_obj.NGUOI_BENH);
                        tongtientt78+=  Number(_obj.THANH_TIEN);
                        issum_all +=  '0';
                        if (_index < $scope.chitietthanhtoan.length - 1) {
                            sl_all +='$$';
                            dg_all += '$$';
                            nd_all += '$$';
                            tt_all += '$$';
                            tt78_all += '$$';
                            ttchiphi_all += '$$';
                            dvt_all+='$$';
                            issum_all+='$$';
                        }
                    })
                } else  {
                    $scope.chitietthanhtoan.forEach(function(_obj,_index) {
                        nd_all += escapeXml(_obj.NOIDUNG);
                        sl_all += _obj.SO_LUONG;
                        dg_all +=  _obj.DON_GIA;
                        if(_obj.GHI_CHU.includes("THUOC")) {
                            dvt_all += 'Viên';
                        } else {
                            dvt_all += 'Lần';
                        }

                        tt_all += _obj.THANH_TIEN.toFixed(2);
                        tongtienbn+=  _obj.THANH_TIEN;
                        tt78_all +=  _obj.NGUOI_BENH.toFixed(2);
                        ttchiphi_all +=  _obj.THANH_TIEN.toFixed(2) ;
                        tongtienbntt78+=  Number(_obj.NGUOI_BENH);
                        tongtientt78+=  Number(_obj.THANH_TIEN);
                        issum_all +=  '0';

                        if (_index < $scope.chitietthanhtoan.length - 1) {
                            sl_all +='$$';
                            dg_all += '$$';
                            nd_all += '$$';
                            tt_all += '$$';
                            dvt_all += '$$ ';
                            tt78_all += '$$';
                            ttchiphi_all += '$$';
                            issum_all+='$$';
                        } else {
                            if ($scope.thongtinthanhtoan.hienthistg==1) {
                                nd_all+='$$Mã khách hàng '+ $scope.thongtinbn.MA_BENH_NHAN
                                sl_all += '$$0';
                                dg_all += '$$0';
                                dvt_all += '$$ ';
                                tt78_all += '$$';
                                ttchiphi_all += '$$';
                                issum_all+='$$';
                            }
                        }
                    })
                }

                if ($scope.thongtinthanhtoan.dvtt != 96162 && $scope.chitietthanhtoan.length > 5 && chitietNgoaitru == 0) {
                    nd_all = '';
                    sl_all = '';
                    dg_all = '';
                    tt_all = '';
                    dvt_all = '';
                    tt78_all = '';
                    ttchiphi_all = '';
                    tongtienbn = 0;
                    tongtientt78 = 0;
                    tongtienbntt78 = 0;
                    if($scope.thongtinthanhtoan.hienthistg == 1) {
                        $scope.chitietthanhtoan.forEach(function (_obj) {
                            switch (_obj.GHI_CHU) {
                                case 'CONGKHAM':
                                    arr[0] = (arr[0] == undefined ? 0 : arr[0]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[0] = (arrtt78[0] == undefined ? 0 : arrtt78[0]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[0] = (arr_chiphitt78[0] == undefined ? 0 : arr_chiphitt78[0]) + Number( _obj.THANH_TIEN);
                                    break;
                                case 'XETNGHIEM':
                                    arr[2] = (arr[2] == undefined ? 0 : arr[2]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[2] = (arrtt78[2] == undefined ? 0 : arrtt78[2]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[2] = (arr_chiphitt78[2] == undefined ? 0 : arr_chiphitt78[2]) + Number( _obj.THANH_TIEN);
                                    break;
                                case 'CHANDOANHINHANH':
                                    arr[3] = (arr[3] == undefined ? 0 : arr[3]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[3] = (arrtt78[3] == undefined ? 0 : arrtt78[3]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[3] = (arr_chiphitt78[3] == undefined ? 0 : arr_chiphitt78[3]) + Number( _obj.THANH_TIEN);
                                    break;
                                case 'THUTHUATPHAUTHUAT':
                                    arr[4] = (arr[4] == undefined ? 0 : arr[4]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[4] = (arrtt78[4] == undefined ? 0 : arrtt78[4]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[4] = (arr_chiphitt78[4] == undefined ? 0 : arr_chiphitt78[4]) + Number( _obj.THANH_TIEN);
                                    break;
                                default:
                                    arr[5] = (arr[5] == undefined ? 0 : arr[5]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[5] = (arrtt78[5] == undefined ? 0 : arrtt78[5]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[5] = (arr_chiphitt78[5] == undefined ? 0 : arr_chiphitt78[5]) + Number( _obj.THANH_TIEN);
                                    break;
                            }
                            tongtienbn += Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN);
                            tongtienbntt78+=  Number(_obj.NGUOI_BENH);
                            tongtientt78+=  Number(_obj.THANH_TIEN);
                        })
                    } else {
                        $scope.chitietthanhtoan.forEach(function (_obj) {
                            switch (_obj.GHI_CHU) {
                                case 'CONGKHAM':
                                    arr[0] = (arr[0] == undefined ? 0 : arr[0]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[0] = (arrtt78[0] == undefined ? 0 : arrtt78[0]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[0] = (arr_chiphitt78[0] == undefined ? 0 : arr_chiphitt78[0]) + Number( _obj.THANH_TIEN);
                                    break;
                                case 'XETNGHIEM':
                                    arr[1] = (arr[1] == undefined ? 0 : arr[1]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[1] = (arrtt78[1] == undefined ? 0 : arrtt78[1]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[1] = (arr_chiphitt78[1] == undefined ? 0 : arr_chiphitt78[1]) + Number( _obj.THANH_TIEN);
                                    break;
                                case 'CHANDOANHINHANH':
                                    arr[2] = (arr[2] == undefined ? 0 : arr[2]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[2] = (arrtt78[2] == undefined ? 0 : arrtt78[2]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[2] = (arr_chiphitt78[2] == undefined ? 0 : arr_chiphitt78[2]) + Number( _obj.THANH_TIEN);
                                    break;
                                case 'THUTHUATPHAUTHUAT':
                                    arr[3] = (arr[3] == undefined ? 0 : arr[3]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[3] = (arrtt78[3] == undefined ? 0 : arrtt78[3]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[3] = (arr_chiphitt78[3] == undefined ? 0 : arr_chiphitt78[3]) + Number( _obj.THANH_TIEN);
                                    break;
                                default:
                                    arr[4] = (arr[4] == undefined ? 0 : arr[4]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[4] = (arrtt78[4] == undefined ? 0 : arrtt78[4]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[4] = (arr_chiphitt78[4] == undefined ? 0 : arr_chiphitt78[4]) + Number( _obj.THANH_TIEN);
                                    break;
                            }
                            tongtienbn += Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN);
                            tongtienbntt78+=  Number(_obj.NGUOI_BENH);
                            tongtientt78+=  Number(_obj.THANH_TIEN);
                        })
                    }
                }
            } else {

                if($scope.thongtinthanhtoan.hienthistg == 1) {
                    $scope.chitietthanhtoan.forEach(function (_obj) {
                        switch (_obj.GHI_CHU) {
                            case 'CONGKHAM':
                                arr[0] = (arr[0] == undefined ? 0 : arr[0]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[0] = (arrtt78[0] == undefined ? 0 : arrtt78[0]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[0] = (arr_chiphitt78[0] == undefined ? 0 : arr_chiphitt78[0]) + Number( _obj.THANH_TIEN);
                                break;
                            case 'XETNGHIEM':
                                arr[2] = (arr[2] == undefined ? 0 : arr[2]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[2] = (arrtt78[2] == undefined ? 0 : arrtt78[2]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[2] = (arr_chiphitt78[2] == undefined ? 0 : arr_chiphitt78[2]) + Number( _obj.THANH_TIEN);
                                break;
                            case 'CHANDOANHINHANH':
                                arr[3] = (arr[3] == undefined ? 0 : arr[3]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[3] = (arrtt78[3] == undefined ? 0 : arrtt78[3]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[3] = (arr_chiphitt78[3] == undefined ? 0 : arr_chiphitt78[3]) + Number( _obj.THANH_TIEN);
                                break;
                            case 'THUTHUATPHAUTHUAT':
                                arr[4] = (arr[4] == undefined ? 0 : arr[4]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[4] = (arrtt78[4] == undefined ? 0 : arrtt78[4]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[4] = (arr_chiphitt78[4] == undefined ? 0 : arr_chiphitt78[4]) + Number( _obj.THANH_TIEN);
                                break;
                            default:
                                arr[5] = (arr[5] == undefined ? 0 : arr[5]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[5] = (arrtt78[5] == undefined ? 0 : arrtt78[5]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[5] = (arr_chiphitt78[5] == undefined ? 0 : arr_chiphitt78[5]) + Number( _obj.THANH_TIEN);
                                break;
                        }
                        tongtienbn += Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN);
                        tongtienbntt78+=  Number(_obj.NGUOI_BENH);
                        tongtientt78+=  Number(_obj.THANH_TIEN);
                    })
                } else {
                    $scope.chitietthanhtoan.forEach(function (_obj) {
                        switch (_obj.GHI_CHU) {
                            case 'CONGKHAM': case'CONGKHAMCHUYENDEN':
                                arr[0] = (arr[0] == undefined ? 0 : arr[0]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[0] = (arrtt78[0] == undefined ? 0 : arrtt78[0]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[0] = (arr_chiphitt78[0] == undefined ? 0 : arr_chiphitt78[0]) + Number( _obj.THANH_TIEN);
                                break;
                            case 'XETNGHIEM':
                                arr[1] = (arr[1] == undefined ? 0 : arr[1]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[1] = (arrtt78[1] == undefined ? 0 : arrtt78[1]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[1] = (arr_chiphitt78[1] == undefined ? 0 : arr_chiphitt78[1]) + Number( _obj.THANH_TIEN);
                                break;
                            case 'CHANDOANHINHANH':
                                arr[2] = (arr[2] == undefined ? 0 : arr[2]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[2] = (arrtt78[2] == undefined ? 0 : arrtt78[2]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[2] = (arr_chiphitt78[2] == undefined ? 0 : arr_chiphitt78[2]) + Number( _obj.THANH_TIEN);
                                break;
                            case 'THUTHUATPHAUTHUAT':
                                arr[3] = (arr[3] == undefined ? 0 : arr[3]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[3] = (arrtt78[3] == undefined ? 0 : arrtt78[3]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[3] = (arr_chiphitt78[3] == undefined ? 0 : arr_chiphitt78[3]) + Number( _obj.THANH_TIEN);
                                break;
                            default:
                                arr[4] = (arr[4] == undefined ? 0 : arr[4]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[4] = (arrtt78[4] == undefined ? 0 : arrtt78[4]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[4] = (arr_chiphitt78[4] == undefined ? 0 : arr_chiphitt78[4]) + Number( _obj.THANH_TIEN);
                                break;
                        }
                        tongtienbn += Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN);
                        tongtienbntt78+=  Number(_obj.NGUOI_BENH);
                        tongtientt78+=  Number(_obj.THANH_TIEN);
                    })
                }

            }

            baohiemchi = sumData($scope.chitietthanhtoan, 'THANH_TIEN') - $scope.thongtinthanhtoan.sotientt;
        } else if($scope.thongtinbn.LOAIVP == 'NTDICHVU')
        {
            if($scope.thongtinthanhtoan.chitiettiendichvu == 0) {
                nd_all = "Thu tiền dịch vụ";
                tt_all = 0;
                tt78_all = 0;
                issum_all =  '0';
                $scope.chitietthanhtoan.forEach(function (_obj) {
                    tt_all += Number(_obj.NGUOI_BENH);
                    tt78_all += Number(_obj.NGUOI_BENH);
                    tongtienbn += _obj.THANH_TIEN;
                    tongtienbntt78+=  Number(_obj.NGUOI_BENH);
                    tongtientt78+=  Number(_obj.THANH_TIEN);
                })
                baohiemchi = 0;
                tilebhyt = 0;
            } else {


                $scope.chitietthanhtoan.forEach(function (_obj,_index) {
                    nd_all += escapeXml(_obj.NOIDUNG);
                    tt_all += Number(_obj.NGUOI_BENH);
                    tt78_all += Number(_obj.NGUOI_BENH);
                    dg_all += Number(_obj.DON_GIA);
                    dvt_all += 'Ngày';
                    sl_all += Number(_obj.SO_LUONG);
                    tongtienbn += _obj.THANH_TIEN;
                    tongtienbntt78 += Number(_obj.NGUOI_BENH);
                    tongtientt78 += Number(_obj.THANH_TIEN);
                    ttchiphi_all += _obj.THANH_TIEN;
                    issum_all +=  '0';
                    if (_index < $scope.chitietthanhtoan.length - 1) {
                        sl_all +='$$';
                        dg_all += '$$';
                        nd_all += '$$';
                        tt_all += '$$';
                        tt78_all += '$$';
                        ttchiphi_all += '$$';
                        issum_all += '$$';
                        dvt_all += '$$ ';
                    }
                })
                baohiemchi = 0;
                tilebhyt = 0;
            }
        } else if ($scope.thongtinbn.LOAIVP == 'HDBL') {
            var temp = data.data.noidung.split(';');
            temp.forEach(function(value,index) {
                var ndsplit = value.split("_!!!_")
                nd_all+= ndsplit[0];
                sl_all += ndsplit[2];
                dg_all += ndsplit[3];
                dvt_all += ndsplit[1];
                tt_all += ndsplit[4];
                tt78_all += ndsplit[4];
                ttchiphi_all += ndsplit[4];
                issum_all += '0';
                if (index < temp.length - 1) {
                    sl_all +='$$';
                    dg_all += '$$';
                    nd_all += '$$';
                    tt_all += '$$';
                    dvt_all += '$$';
                    tt78_all += '$$';
                    ttchiphi_all += '$$';
                    issum_all += '$$';
                }

            })
            $scope.chitietthanhtoan.forEach(function(_obj) {
                tongtienbn+=  Number(_obj.THANH_TIEN);
                tongtienbntt78+=  Number(_obj.THANH_TIEN);
                tongtientt78+=  Number(_obj.THANH_TIEN);
            })
            baohiemchi = 0;
            tilebhyt = 0;
            $scope.thongtinbn['load']['TEN_DANTOC'] = 'Kinh';
            tendonvi = escapeXml($scope.hoadonle.tendonvi);
            masothue = $scope.hoadonle.masothue;
            sodienthoai = $scope.hoadonle.sodienthoai;
            if($scope.hoadonle.dantoc != undefined) {

                $scope.thongtinbn['load']['TEN_DANTOC'] = $scope.hoadonle.dantoc.trim();
            }
            paymentmethod =  $scope.thongtinbn.hinhthucthanhtoan;

        } else {
            if ($scope.thongtinthanhtoan.hienthistg == 1) {
                $scope.chitietthanhtoan.forEach(function(_obj) {
                    switch(_obj.UU_TIEN_IN) {
                        case 1:
                            if(_obj.NOIDUNG == 'Công khám') {
                                arr[0] = (arr[0] == undefined? 0: arr[0]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                arrtt78[0] = (arrtt78[0] == undefined ? 0 : arrtt78[0]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[0] = (arr_chiphitt78[0] == undefined ? 0 : arr_chiphitt78[0]) + Number( _obj.THANH_TIEN);
                            } else {
                                arr[1] = (arr[1] == undefined? 0: arr[1]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                arrtt78[1] = (arrtt78[1] == undefined ? 0 : arrtt78[1]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[1] = (arr_chiphitt78[1] == undefined ? 0 : arr_chiphitt78[1]) + Number( _obj.THANH_TIEN);
                            }

                            break;
                        case 2:
                            arr[2] = (arr[2] == undefined? 0: arr[2]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            arrtt78[2] = (arrtt78[2] == undefined ? 0 : arrtt78[2]) + Number( _obj.NGUOI_BENH);
                            arr_chiphitt78[2] = (arr_chiphitt78[2] == undefined ? 0 : arr_chiphitt78[2]) + Number( _obj.THANH_TIEN);
                            break;
                        case 3:
                            arr[3] = (arr[3] == undefined? 0: arr[3]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            arrtt78[3] = (arrtt78[3] == undefined ? 0 : arrtt78[3]) + Number( _obj.NGUOI_BENH);
                            arr_chiphitt78[3] = (arr_chiphitt78[3] == undefined ? 0 : arr_chiphitt78[3]) + Number( _obj.THANH_TIEN);
                            break;
                        case 4:
                            arr[4] = (arr[4] == undefined? 0: arr[4]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            arrtt78[4] = (arrtt78[4] == undefined ? 0 : arrtt78[4]) + Number( _obj.NGUOI_BENH);
                            arr_chiphitt78[4] = (arr_chiphitt78[4] == undefined ? 0 : arr_chiphitt78[4]) + Number( _obj.THANH_TIEN);
                            break;
                        default:
                            arr[5] = (arr[5] == undefined? 0: arr[5]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            arrtt78[5] = (arrtt78[5] == undefined ? 0 : arrtt78[5]) + Number( _obj.NGUOI_BENH);
                            arr_chiphitt78[5] = (arr_chiphitt78[5] == undefined ? 0 : arr_chiphitt78[5]) + Number( _obj.THANH_TIEN);
                            break;
                    }
                    tongtienbn+=  nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN;
                    tongtienbntt78 += Number(_obj.NGUOI_BENH);
                    tongtientt78 += Number(_obj.THANH_TIEN);
                })
                baohiemchi = nguoibenh == 1?0:sumData($scope.chitietthanhtoan, 'THANH_TIEN') - $scope.thongtinthanhtoan.sotientt;
            } else {
                noitru = " và ngày giường"
                $scope.chitietthanhtoan.forEach(function(_obj) {
                    switch(_obj.UU_TIEN_IN) {
                        case 1:
                            arr[0] = (arr[0] == undefined? 0: arr[0]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            arrtt78[0] = (arrtt78[0] == undefined ? 0 : arrtt78[0]) + Number( _obj.NGUOI_BENH);
                            arr_chiphitt78[0] = (arr_chiphitt78[0] == undefined ? 0 : arr_chiphitt78[0]) + Number( _obj.THANH_TIEN);
                            break;
                        case 2:
                            arr[1] = (arr[1] == undefined? 0: arr[1]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            arrtt78[1] = (arrtt78[1] == undefined ? 0 : arrtt78[1]) + Number( _obj.NGUOI_BENH);
                            arr_chiphitt78[1] = (arr_chiphitt78[1] == undefined ? 0 : arr_chiphitt78[1]) + Number( _obj.THANH_TIEN);
                            break;
                        case 3:
                            arr[2] = (arr[2] == undefined? 0: arr[2]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            arrtt78[2] = (arrtt78[2] == undefined ? 0 : arrtt78[2]) + Number( _obj.NGUOI_BENH);
                            arr_chiphitt78[2] = (arr_chiphitt78[2] == undefined ? 0 : arr_chiphitt78[2]) + Number( _obj.THANH_TIEN);
                            break;
                        case 4:
                            arr[3] = (arr[3] == undefined? 0: arr[3]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            arrtt78[3] = (arrtt78[3] == undefined ? 0 : arrtt78[3]) + Number( _obj.NGUOI_BENH);
                            arr_chiphitt78[3] = (arr_chiphitt78[3] == undefined ? 0 : arr_chiphitt78[3]) + Number( _obj.THANH_TIEN);
                            break;
                        default:
                            arr[4] = (arr[4] == undefined? 0: arr[4]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            arrtt78[4] = (arrtt78[4] == undefined ? 0 : arrtt78[4]) + Number( _obj.NGUOI_BENH);
                            arr_chiphitt78[4] = (arr_chiphitt78[4] == undefined ? 0 : arr_chiphitt78[4]) + Number( _obj.THANH_TIEN);
                            break;
                    }
                    tongtienbn+=  nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN;
                    tongtienbntt78 += Number(_obj.NGUOI_BENH);
                    tongtientt78 += Number(_obj.THANH_TIEN);
                })
                baohiemchi = nguoibenh == 1?0:sumData($scope.chitietthanhtoan, 'THANH_TIEN') - $scope.thongtinthanhtoan.sotientt;
            }
        }
        if ($scope.thongtinthanhtoan.hoadonchitiet != 1 ||
            ($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  $scope.thongtinbn.LOAIVP != 'THUPHI' && chitietNgoaitru == 0)
            ||  ($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  $scope.thongtinbn.LOAIVP == 'THUPHI'
                && $scope.thongtinthanhtoan.dvtt != 96162 && $scope.chitietthanhtoan.length > 5 && chitietNgoaitru == 0)
        )
        {
            var temp = 1;
            if($scope.thongtinthanhtoan.hienthistg == 1) {
                for (var i = 0; i < arr.length; i++) {
                    switch(i) {
                        case 0:
                            if(arr[0] != undefined) {
                                nd_all += escapeXml("Công khám" + noitru);
                                sl_all += '1';
                                dg_all += arr[0].toFixed(2);
                                dvt_all += 'Lần';
                                tt_all += arr[0].toFixed(2);
                                tt78_all += arrtt78[0].toFixed(2);
                                ttchiphi_all += arr_chiphitt78[0].toFixed(2);
                                issum_all += '0';
                                temp++;
                            }

                            break;
                        case 1:
                            if (arr[1] != undefined) {
                                tt_all += arr[1].toFixed(2);
                                nd_all += escapeXml("Giường bệnh");
                                sl_all += '1';
                                dg_all += arr[1].toFixed(2);
                                dvt_all += 'Lần';
                                tt78_all += arrtt78[1].toFixed(2);
                                ttchiphi_all += arr_chiphitt78[1].toFixed(2);
                                issum_all += '0';
                                temp++;
                            }

                            break;
                        case 2:
                            if (arr[2] != undefined) {
                                tt_all += arr[2].toFixed(2);
                                nd_all += escapeXml("Xét nghiệm");
                                sl_all += '1';
                                dg_all += arr[2].toFixed(2);
                                dvt_all += 'Lần';
                                tt78_all += arrtt78[2].toFixed(2);
                                ttchiphi_all += arr_chiphitt78[2].toFixed(2);
                                issum_all += '0';
                                temp++;
                            }

                            break;
                        case 3:
                            if (arr[3] != undefined) {
                                tt_all += arr[3].toFixed(2);
                                nd_all += escapeXml("Chẩn đoán hình ảnh và TDCN");
                                sl_all += '1';
                                dg_all += arr[3].toFixed(2);
                                dvt_all += 'Lần';
                                tt78_all += arrtt78[3].toFixed(2);
                                ttchiphi_all += arr_chiphitt78[3].toFixed(2);
                                issum_all += '0';
                                temp++;
                            }

                            break;
                        case 4:
                            if (arr[4] != undefined) {
                                tt_all += arr[4].toFixed(2);
                                nd_all += escapeXml("Thủ thuật phẫu thuật");
                                sl_all += '1';
                                dg_all += arr[4].toFixed(2);
                                dvt_all += 'Lần';
                                tt78_all += arrtt78[4].toFixed(2);
                                ttchiphi_all += arr_chiphitt78[4].toFixed(2);
                                issum_all += '0';
                            }

                            break;
                        case 5:
                            if (arr[5] != undefined) {
                                tt_all += arr[5].toFixed(2);
                                nd_all += escapeXml("Thuốc, vật tư và chi phí khác");
                                sl_all += '1';
                                dg_all += arr[5].toFixed(2);
                                dvt_all += 'Lần';
                                tt78_all += arrtt78[5].toFixed(2);
                                ttchiphi_all += arr_chiphitt78[5].toFixed(2);
                                issum_all += '0';
                            }

                            break;
                    }
                    if (i < arr.length - 1) {
                        if (arr[i] != undefined) {
                            sl_all +='$$';
                            dg_all += '$$';
                            nd_all += '$$';
                            tt_all += '$$';
                            dvt_all += '$$';
                            tt78_all += '$$';
                            ttchiphi_all += '$$';
                            issum_all += '$$';
                        }

                    }else {
                        if ($scope.thongtinbn.LOAIVP == 'NOITRU') {
                            nd_all+='$$Mẫu số 02/BV số bệnh án '+ $scope.thongtinbn.SOBENHAN
                            sl_all += '$$0';
                            dg_all += '$$0';
                            dvt_all += '$$ ';
                            tt78_all += '$$ ';
                            ttchiphi_all += '$$ ';
                            issum_all += '$$ ';
                        }
                        if ($scope.thongtinbn.LOAIVP == 'BANT') {
                            nd_all+='$$Mã khách hàng '+ $scope.thongtinbn.MA_BENH_NHAN
                            sl_all += '$$0';
                            dg_all += '$$0';
                            dvt_all += '$$ ';
                            tt78_all += '$$ ';
                            ttchiphi_all += '$$ ';
                            issum_all += '$$ ';
                        }
                    }
                }
            } else {
                for (var i = 0; i < arr.length; i++) {
                    switch(i) {
                        case 0:
                            if(arr[0] != undefined) {
                                nd_all += escapeXml("Công khám" + noitru);
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                                tt_all += arr[0].toFixed(2);
                                tt78_all += arrtt78[0].toFixed(2);
                                ttchiphi_all += arr_chiphitt78[0].toFixed(2);
                                issum_all += '0';
                                temp++;
                            }

                            break;
                        case 1:
                            if (arr[1] != undefined) {
                                tt_all += arr[1].toFixed(2);
                                nd_all += escapeXml("Xét Nghiệm");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                                tt78_all += arrtt78[1].toFixed(2);
                                ttchiphi_all += arr_chiphitt78[1].toFixed(2);
                                issum_all += '0';
                                temp++;
                            }

                            break;
                        case 2:
                            if (arr[2] != undefined) {
                                tt_all += arr[2].toFixed(2);
                                nd_all += escapeXml("Chẩn đoán hình ảnh và TDCN");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                                tt78_all += arrtt78[2].toFixed(2);
                                ttchiphi_all += arr_chiphitt78[2].toFixed(2);
                                issum_all += '0';
                                temp++;
                            }

                            break;
                        case 3:
                            if (arr[3] != undefined) {
                                tt_all += arr[3].toFixed(2);
                                nd_all += escapeXml("Thủ thuật phẫu thuật");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                                tt78_all += arrtt78[3].toFixed(2);
                                ttchiphi_all += arr_chiphitt78[3].toFixed(2);
                                issum_all += '0';
                                temp++;
                            }

                            break;
                        case 4:
                            if (arr[4] != undefined) {
                                tt_all += arr[4].toFixed(2);
                                nd_all += escapeXml("Thuốc, vật tư và chi phí khác");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                                tt78_all += arrtt78[4].toFixed(2);
                                ttchiphi_all += arr_chiphitt78[4].toFixed(2);
                                issum_all += '0';
                                temp++;
                            }

                            break;
                    }
                    if (i < arr.length - 1) {
                        if (arr[i] != undefined) {
                            sl_all +='$$';
                            dg_all += '$$';
                            nd_all += '$$';
                            tt_all += '$$';
                            dvt_all += '$$';
                            tt78_all += '$$';
                            ttchiphi_all += '$$';
                            issum_all += '$$';
                        }

                    }
                }
            }
        }
        if ( ['NOITRU','BANT'].indexOf($scope.thongtinbn.LOAIVP) == 0 && $scope.chitietthanhtoan.length < 6 && $scope.thongtinthanhtoan.hienthichitietnoitru ==1) {
            nd_all = '';
            sl_all = '';
            dg_all = '';
            tt_all = '';
            dvt_all = '';
            tt78_all = '';
            issum_all = '';
            ttchiphi_all = '';
            tongtienbn = 0;
            tongtienbntt78 = 0;
            tongtientt78 = 0;
            $scope.chitietthanhtoan.forEach(function(_obj,_index) {
                nd_all += escapeXml(_obj.NOIDUNG);
                sl_all += '0';
                dg_all += '0';
                issum_all += '0';
                dvt_all+= ' '
                tt_all += _obj.THANH_TIEN.toFixed(2);
                tt78_all += _obj.NGUOI_BENH.toFixed(2);
                ttchiphi_all += _obj.THANH_TIEN.toFixed(2);
                tongtienbn+=  nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN);
                tongtienbntt78+=  Number(_obj.NGUOI_BENH);
                tongtientt78+=  Number(_obj.THANH_TIEN);
                if (_index < $scope.chitietthanhtoan.length - 1) {
                    sl_all +='$$';
                    dg_all += '$$';
                    nd_all += '$$';
                    tt_all += '$$';
                    tt78_all += '$$';
                    ttchiphi_all += '$$';
                    dvt_all +='$$';
                    issum_all +='$$';
                }
            })
        }
        var d = new Date();
        var n = d.getTime();
        var arisingdate = parseDateToString($scope.thongtinthanhtoan.ngaythu);
        var key =  $scope.thongtinthanhtoan.dvtt+"-"+n + "-" + arisingdate.replace("-","") + "-" + $scope.thongtinbn.MA_BENH_NHAN + "-" + $scope.thongtinbn.SOVAOVIEN;

        var url = "cmu_thaythe_invoice";
        data.data['KEY_HD'] = key;
        var dt =  $scope.thongtinbn['load'].TEN_DANTOC == undefined? 'Kinh': $scope.thongtinbn['load'].TEN_DANTOC;
        if($scope.thongtinthanhtoan.hoadonchitiet == 1 && $scope.thongtinthanhtoan.dvtt == 96162) {
            if($scope.thongtinbn.LOAIVP == 'HDBL') {
                dt = ' ';
            } else {
                if($scope.thongtinbn.LOAIVP == 'NOITRU') {
                    var d = new Date();
                    var n = d.getFullYear();
                    dt =n - $scope.thongtinbn.NAMSINH
                } else {
                    dt = $scope.thongtinbn['load'].TUOI
                }

            }

        }
        if(tongtienbn.toFixed() == 0 || $scope.thongtinthanhtoan.sotientt.toFixed() == 0) {
            alert("Không được phát hành hóa đơn số tiền bằng không, Nếu có lỗi xảy ra vui lòng phát hành lại.")
            return false;
        }
        if ($scope.thongtinthanhtoan.tmiengiam == true) {
            tongtienbn = Number($scope.thongtinthanhtoan.sotienbntra);
            tongtientt78 = Number($scope.thongtinthanhtoan.sotienbntra);
            tongtienbntt78 = Number($scope.thongtinthanhtoan.sotienbntra);
        }

        if (vat > 0) {
            var temp = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra:$scope.thongtinthanhtoan.sotientt;
            vatAmount = Number(temp)*Number(vat)/100;
            vatAmount  =vatAmount.toFixed();
        }
        if ($scope.thongtinthanhtoan.hienthistg== 1) {
            if($scope.thongtinbn.LOAIVP == 'BHYT') {
                sl_all ='1';
                dg_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                nd_all = ' Thu '+ (100 - Number($scope.thongtinbn.load.TYLEBAOHIEM)) + "% mẫu số 01/BV mã khách hàng "+$scope.thongtinbn.MA_BENH_NHAN;
                tt_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                tt78_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                ttchiphi_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                dvt_all = 'Lần';
            }
            if ($scope.thongtinbn.LOAIVP == 'BANT' && $scope.thongtinbn.load.TYLEBAOHIEM > 0) {
                sl_all ='1';
                dg_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                nd_all = ' Thu '+ (100 - Number($scope.thongtinbn.load.TYLEBAOHIEM)) + "% mẫu số 01/BV mã khách hàng "+$scope.thongtinbn.MA_BENH_NHAN;
                tt_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                tt78_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                ttchiphi_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                dvt_all = 'Lần';
            }
            if($scope.thongtinbn.LOAIVP == 'NOITRU' && $scope.thongtinbn.load.TYLEBAOHIEM > 0) {
                sl_all ='1';
                dg_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                nd_all = ' Thu '+ (100 - Number($scope.thongtinbn.load.TYLEBAOHIEM)) + "% mẫu số 02/BV số bệnh án "+$scope.thongtinbn.load.SOBENHAN;
                tt_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                tt78_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                ttchiphi_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                dvt_all = 'Lần';
            }
            if ($scope.thongtinthanhtoan.tmiengiam == true && $scope.thongtinbn.load.TYLEBAOHIEM == 0) {
                sl_all ='1$$0';
                dg_all =  Number($scope.thongtinthanhtoan.sotienbntra).toFixed();
                dg_all += '$$0'
                nd_all = "Thuốc và chi phí khác;Mẫu số 02/BV số bệnh án "+$scope.thongtinbn.load.SOBENHAN;
                tt_all = $scope.thongtinthanhtoan.sotienbntra.toFixed();
                tt78_all = $scope.thongtinthanhtoan.sotienbntra.toFixed();
                ttchiphi_all = $scope.thongtinthanhtoan.sotienbntra.toFixed();
                tt_all += '$$0';
                tt78_all += '$$0';
                ttchiphi_all += '$$0';
                dvt_all = 'Lần; ';
                tongtienbn =  Number($scope.thongtinthanhtoan.sotienbntra).toFixed()
                tongtienbntt78 =  Number($scope.thongtinthanhtoan.sotienbntra).toFixed()
                tongtientt78 =  Number($scope.thongtinthanhtoan.sotienbntra).toFixed()
            }
        }
        var diachi = ' ';
        var gioitinh =' ';
        var sothebaohiem = ' ';
        var tuoi =  ' ';
        if($scope.thongtinbn.load !== undefined)  {
            diachi = $scope.thongtinbn.load.DIACHI == undefined? ' ': $scope.thongtinbn.load.DIACHI;
            console.log("thong tin bn", $scope.thongtinbn, $scope.thongtinbn.GIOI_TINH , $scope.thongtinbn.load.GIOITINH )
            var tempGioitinh = $scope.thongtinbn.GIOI_TINH || $scope.thongtinbn.load.GIOITINH;
            if (tempGioitinh !== undefined && !isNaN(tempGioitinh) && tempGioitinh != ' ' && tempGioitinh != '') {
                gioitinh = Number(tempGioitinh) === 0 ? 'Nữ': 'Nam'
            } else {
                gioitinh = '';
            }
            sothebaohiem = $scope.thongtinbn.load.SOBAOHIEMYTE == undefined? ' ': $scope.thongtinbn.load.SOBAOHIEMYTE
            tuoi = $scope.thongtinbn.load.TUOI == undefined? ' ': $scope.thongtinbn.load.TUOI
        }
        if ($scope.thongtinthanhtoan.dvtt == '96014') {
            if ($scope.thongtinthanhtoan.pattern == '02GTTT0/002'
                || $scope.thongtinthanhtoan.pattern == '02GTTT0/001') {
                dt = tuoi
            }
        }
        if(tongtienbn.toFixed() == 0 || $scope.thongtinthanhtoan.sotientt.toFixed() == 0) {
            alert("Không được phát hành hóa đơn số tiền bằng không, Nếu có lỗi xảy ra vui lòng phát hành lại.")
            return false;
        }
        return $.post(url, {
            id: data.data.ID,
            key: key,
            account: $scope.thongtinthanhtoan.account,
            acpass: $scope.thongtinthanhtoan.acpass,
            username: $scope.thongtinthanhtoan.username,
            password: $scope.thongtinthanhtoan.password,
            publishservice: $scope.thongtinthanhtoan.businessservice,
            pattern: $scope.thongtinthanhtoan.pattern,
            serial: $scope.thongtinthanhtoan.serial,

            ma_bn: escapeXml($scope.thongtinbn.MA_BENH_NHAN + ''),
            ten_bn: escapeXml($scope.thongtinbn.TEN_BENH_NHAN+''),
            diachi: escapeXml(diachi+''),
            paymethod: paymentmethod == undefined? 'Tiền mặt':paymentmethod,

            nd_all: nd_all,
            sl_all: sl_all,
            dg_all: dg_all,
            tt_all: tt_all,
            tt78_all: tt78_all,
            ttchiphi_all: ttchiphi_all,
            tongtientt78: Math.round(tongtientt78),
            tongtienbntt78: Math.round(tongtienbntt78),
            issum_all: issum_all,
            dvt_all: dvt_all,

            total: $scope.thongtinthanhtoan.tmiengiam == true? Number($scope.thongtinthanhtoan.sotienbntra).toFixed():$scope.thongtinthanhtoan.sotientt.toFixed(),
            totaltt78: $scope.thongtinthanhtoan.tmiengiam == true? Number($scope.thongtinthanhtoan.sotienbntra).toFixed():$scope.thongtinthanhtoan.sotientt.toFixed(),
            arisingdate: arisingdate,
            dantoc: dt,
            tuoi: tuoi,
            gioitinh: gioitinh,
            tenKhoa: $scope.thongtinbn.TEN_PHONGBAN,
            thebhyt: sothebaohiem,
            tilebhyt: tilebhyt == undefined? '0':tilebhyt,
            baohiemchi: baohiemchi.toFixed(),
            tongtienbn: tongtienbn.toFixed(),
            tendonvi: tendonvi,
            masothue: masothue,
            sodienthoai: sodienthoai,
            vat: vat,
            vatAmount: vatAmount,
            fkey: fkey

        });
    }
    
    this.capnhathoadonsai = function() {
        
    }
    
    this.laphoadon = function($scope, data) {
        var nguoibenh = $("#cmu_ctlaynguoibenh").val()
        var nd_all = '';
        var sl_all = '';
        var dg_all = '';
        var tt_all = '';
        var arr = [];
        var noitru = "";
        var tilebhyt = $scope.thongtinbn.load.TYLEBAOHIEM;
        var baohiemchi = 0;
        var tongtienbn = 0;
        var paymentmethod = "Tiền Mặt";
        var tendonvi = "";
        var masothue  = "";
        var sodienthoai = "";
        var vat = "0";
        var vatAmount = "0";
        if ($scope.thongtinbn.LOAIVP == 'BHYT' || $scope.thongtinbn.LOAIVP == 'THUPHI') {
            
            if($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  $scope.thongtinbn.LOAIVP == 'THUPHI') {
                $scope.chitietthanhtoan.forEach(function(_obj,_index) {
                    nd_all += escapeXml(_obj.NOIDUNG);
                    sl_all += '0';
                    dg_all += '0';
                    tt_all += _obj.THANH_TIEN.toFixed(2);
                    tongtienbn+=  _obj.THANH_TIEN;
                    if (_index < $scope.chitietthanhtoan.length - 1) {
                        sl_all +='$$';
                        dg_all += '$$';
                        nd_all += '$$';
                        tt_all += '$$';

                    }
                })
                if ($scope.thongtinthanhtoan.dvtt != 96977 && $scope.chitietthanhtoan.length > 5) {
                    nd_all = '';
                    sl_all = '';
                    dg_all = '';
                    tt_all = '';
                    tongtienbn = 0;
                    $scope.chitietthanhtoan.forEach(function(_obj) {
                        switch(_obj.GHI_CHU) {
                            case 'CONGKHAM':
                                arr[0] = (arr[0] == undefined? 0: arr[0]) + (Number(nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN));
                                break;
                            case 'XETNGHIEM':
                                arr[1] = (arr[1] == undefined? 0: arr[1]) + (Number(nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN));
                                break;
                            case 'CHANDOANHINHANH':
                                arr[2] = (arr[2] == undefined? 0: arr[2]) + (Number(nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN));
                                break;
                            case 'THUTHUATPHAUTHUAT':
                                arr[3] = (arr[3] == undefined? 0: arr[3]) + (Number(nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN));
                                break;
                            default:
                                arr[4] = (arr[4] == undefined? 0: arr[4]) + (Number(nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN));
                                break;    
                        }
                        tongtienbn+=  Number(nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN);
                        })
                }
            } else {
                $scope.chitietthanhtoan.forEach(function(_obj) {
                switch(_obj.GHI_CHU) {
                    case 'CONGKHAM':
                        arr[0] = (arr[0] == undefined? 0: arr[0]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;
                    case 'XETNGHIEM':
                        arr[1] = (arr[1] == undefined? 0: arr[1]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;
                    case 'CHANDOANHINHANH':
                        arr[2] = (arr[2] == undefined? 0: arr[2]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;
                    case 'THUTHUATPHAUTHUAT':
                        arr[3] = (arr[3] == undefined? 0: arr[3]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;
                    default:
                        arr[4] = (arr[4] == undefined? 0: arr[4]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;    
                }
                tongtienbn+=  nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN;
                console.log("tongtienbn",tongtienbn,_obj)
                })
            }
            
            console.log("nguoibenh",nguoibenh)
            console.log("tongtienbn",tongtienbn)
            baohiemchi = sumData($scope.chitietthanhtoan, 'THANH_TIEN') - $scope.thongtinthanhtoan.sotientt;
        } else if($scope.thongtinbn.LOAIVP == 'NTDICHVU')
        {
            nd_all = "Thu tiền dịch vụ";
            tt_all = 0;
            $scope.chitietthanhtoan.forEach(function(_obj) {
                tt_all += Number(_obj.NGUOI_BENH);
                tongtienbn+=  _obj.THANH_TIEN;
            })
            baohiemchi = 0;
            tilebhyt = 0;
        } else if ($scope.thongtinbn.LOAIVP == 'HDBL') {
            nd_all = data.data.noidung;
            $scope.chitietthanhtoan.forEach(function(_obj) {
                tt_all += Number(_obj.NGUOI_BENH);
                tongtienbn+=  Number(_obj.THANH_TIEN);
            })
            baohiemchi = 0;
            tilebhyt = 0;
            $scope.thongtinbn['load']['TEN_DANTOC'] = 'Kinh';
            tendonvi = $scope.hoadonle.tendonvi;
            masothue = $scope.hoadonle.masothue;
            sodienthoai = $scope.hoadonle.sodienthoai;
            if($scope.hoadonle.dantoc != undefined) {
                paymentmethod = $scope.hoadonle.hinhthuctt;
                $scope.thongtinbn['load']['TEN_DANTOC'] = $scope.hoadonle.dantoc.trim() == ''? 'Kinh': $scope.hoadonle.dantoc; 
            } 
            
        } else {
            noitru = " và ngày giường"
            $scope.chitietthanhtoan.forEach(function(_obj) {
                switch(_obj.UU_TIEN_IN) {
                    case 1:
                        arr[0] = (arr[0] == undefined? 0: arr[0]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;
                    case 2:
                        arr[1] = (arr[1] == undefined? 0: arr[1]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;
                    case 3:
                        arr[2] = (arr[2] == undefined? 0: arr[2]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;
                    case 4:
                        arr[3] = (arr[3] == undefined? 0: arr[3]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;
                    default:
                        arr[4] = (arr[4] == undefined? 0: arr[4]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;    
                }
                tongtienbn+=  nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN;
            })
            baohiemchi = nguoibenh == 1?0:sumData($scope.chitietthanhtoan, 'THANH_TIEN') - $scope.thongtinthanhtoan.sotientt;
        }
        if ($scope.thongtinthanhtoan.hoadonchitiet != 1 || 
                ($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  $scope.thongtinbn.LOAIVP != 'THUPHI')
                ||  ($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  $scope.thongtinbn.LOAIVP == 'THUPHI'
                    && $scope.thongtinthanhtoan.dvtt != 96977 && $scope.chitietthanhtoan.length > 5)
                ) {
            var temp = 1;   
            for (var i = 0; i < arr.length; i++) {
               switch(i) {
                   case 0:
                       if(arr[0] != undefined) {
                           nd_all += escapeXml("Công khám" + noitru);
                           sl_all += '0';
                           dg_all += '0';
                           tt_all += arr[0].toFixed(2);
                           temp++;
                       }

                       break;
                   case 1:
                       if (arr[1] != undefined) {
                           tt_all += arr[1].toFixed(2);
                           nd_all += escapeXml("Xét Nghiệm");
                           sl_all += '0';
                           dg_all += '0';
                           temp++;
                       }

                       break;
                   case 2:
                       if (arr[2] != undefined) {
                           tt_all += arr[2].toFixed(2);
                           nd_all += escapeXml("Chẩn đoán hình ảnh và TDCN");
                           sl_all += '0';
                           dg_all += '0';
                           temp++;
                       }

                       break;
                   case 3:
                       if (arr[3] != undefined) {
                           tt_all += arr[3].toFixed(2);
                           nd_all += escapeXml("Thủ thuật phẫu thuật");
                           sl_all += '0';
                           dg_all += '0';
                           temp++;
                       }

                       break;
                   case 4:
                       if (arr[4] != undefined) {
                           tt_all += arr[4].toFixed(2);
                           nd_all += escapeXml("Thuốc, vật tư và chi phí khác");
                           sl_all += '0';
                           dg_all += '0';
                       }

                       break;    
               }
               if (i < arr.length - 1) {
                   if (arr[i] != undefined) {
                       sl_all +='$$';
                   dg_all += '$$';
                   nd_all += '$$';
                   tt_all += '$$';
                   }

               }
           }
       }

        var d = new Date();
        var n = d.getTime();
        var arisingdate = parseDateToString($scope.thongtinthanhtoan.ngaythu);
        var key =  $scope.thongtinthanhtoan.dvtt+"-"+n + "-" + arisingdate.replace("-","") + "-" + $scope.thongtinbn.MA_BENH_NHAN + "-" + $scope.thongtinbn.SOVAOVIEN;

        var url = "cmu_laphoadon_invoice";   
        data.data['KEY_HD'] = key; 
        var dt =  $scope.thongtinbn['load'].TEN_DANTOC == undefined? 'Kinh': $scope.thongtinbn['load'].TEN_DANTOC;
        if($scope.thongtinthanhtoan.hoadonchitiet == 1 && $scope.thongtinthanhtoan.dvtt == 96977) {
            if($scope.thongtinbn.LOAIVP == 'HDBL') {
                dt = ' ';
            } else {
                if($scope.thongtinbn.LOAIVP == 'NOITRU') {
                    var d = new Date();
                    var n = d.getFullYear();
                    dt =n - $scope.thongtinbn.NAMSINH
                } else {
                    dt = $scope.thongtinbn['load'].TUOI
                }
                
            }
            
        }
        console.log("tongtienbn",tongtienbn,$scope.thongtinthanhtoan.sotientt)
        if(tongtienbn.toFixed() == 0 || $scope.thongtinthanhtoan.sotientt.toFixed() == 0) {
            alert("Không được phát hành hóa đơn số tiền bằng không, Nếu có lỗi xảy ra vui lòng phát hành lại.")
            return false;
	}
        if ($scope.thongtinthanhtoan.tmiengiam == true) {
            tongtienbn = Number($scope.thongtinthanhtoan.sotienbntra);
        }
        return $.post(url, {
                    id: data.data.ID,
                    key: key,
                    account: $scope.thongtinthanhtoan.account,
                    acpass: $scope.thongtinthanhtoan.acpass,
                    username: $scope.thongtinthanhtoan.username,
                    password: $scope.thongtinthanhtoan.password,
                    publishservice: $scope.thongtinthanhtoan.publishservice,
                    pattern: $scope.thongtinthanhtoan.pattern,
                    serial: $scope.thongtinthanhtoan.serial,

                    ma_bn: escapeXml($scope.thongtinbn.MA_BENH_NHAN + ''),
                    ten_bn: escapeXml($scope.thongtinbn.TEN_BENH_NHAN+''),
                    diachi: escapeXml($scope.thongtinbn.load.DIACHI+''),
                    paymethod: paymentmethod,

                    nd_all: nd_all,
                    sl_all: sl_all,
                    dg_all: dg_all,
                    tt_all: tt_all,

                    total: $scope.thongtinthanhtoan.sotientt.toFixed(),
                    arisingdate: arisingdate,
                    dantoc: dt,
                    gioitinh: $scope.thongtinbn.GIOI_TINH == 0 || $scope.thongtinbn.load.GIOITINH == 0? "Nữ": "Nam",
                    tenKhoa: $scope.thongtinbn.TEN_PHONGBAN,
                    thebhyt: $scope.thongtinbn.load.SOBAOHIEMYTE,
                    tilebhyt: tilebhyt,
                    baohiemchi: baohiemchi.toFixed(),
                    tongtienbn: tongtienbn.toFixed(),
                    tendonvi: tendonvi,
                    masothue: masothue,
                    sodienthoai: sodienthoai,
                    vat: vat,
                    vatAmount: vatAmount

                });
        
    }
    
    this.phathanhhoadon = function($scope, data, keyold) {
        console.log("$scope.thongtinthanhtoan.hienthistgchitiet", $scope.thongtinthanhtoan)
        if($scope.thongtinthanhtoan.apdung7dong_kgg == 1)
        {
            return this.phathanhhoadon_tach7dong($scope, data,keyold);
        }
        if($scope.thongtinthanhtoan.apdungchitiet_agg == 1)
        {
            return this.phathanhhoadon_agg($scope, data,keyold);
        }
        if($scope.thongtinthanhtoan.apdungnhombk_hgi == 1)
        {
            return this.phathanhhoadon_nhombk_hgi($scope, data,keyold);
        }

        if($scope.thongtinthanhtoan.hienthistgchitiet == 1) {
            return this.phathanhhoadonstgchitiet($scope, data);
        }
        var nguoibenh = $("#cmu_ctlaynguoibenh").val()
        var nd_all = '';
        var sl_all = '';
        var dg_all = '';
        var tt_all = '';
        var tt78_all = '';
        var dvt_all = '';
        var issum_all = '';
        var ttchiphi_all = '';
        var arr = [];
        var arrtt78 = [];
        var arr_chiphitt78 = [];
        var noitru = "";
        var tilebhyt = $scope.thongtinbn.load.TYLEBAOHIEM;
        var baohiemchi = 0;
        var tongtienbn = 0;
        var tongtienbntt78 = 0;
        var tongtientt78 = 0;
        var paymentmethod =  $scope.thongtinthanhtoan.hinhthucthanhtoan;
        var tendonvi = $scope.thongtinthanhtoan.tendonvi == undefined? "":escapeXml($scope.thongtinthanhtoan.tendonvi);
        var masothue  = $scope.thongtinthanhtoan.masothue == undefined? "":$scope.thongtinthanhtoan.masothue;
        var sodienthoai = "";
        var vat = $scope.thongtinthanhtoan.vat;
        var vatAmount = "0";
        var chitietNgoaitru = 0;
        if ($scope.thongtinbn.LOAIVP == 'BHYT' || $scope.thongtinbn.LOAIVP == 'THUPHI') {
            if ($scope.thongtinthanhtoan.dvtt == 96145) {
                chitietNgoaitru = 1;
            } else {
                chitietNgoaitru = $scope.thongtinthanhtoan.thamsodonvi960620;
            }
            if($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  ( chitietNgoaitru == 1 || $scope.thongtinbn.LOAIVP == 'THUPHI' || ($scope.thongtinbn.LOAIVP == 'BHYT' && $scope.chitietthanhtoan.length < 6))) {
                if ($scope.thongtinbn.LOAIVP == 'BHYT') {
                    $scope.chitietthanhtoan.forEach(function(_obj,_index) {
                        nd_all += escapeXml(_obj.NOIDUNG);
                        if ($scope.thongtinthanhtoan.hienthistg==1) {
                            sl_all += '1';
                            dg_all += _obj.THANH_TIEN.toFixed(2);
                            dvt_all += 'Lần';
                        } else {
                            if(nguoibenh == 0) {
                                sl_all += _obj.SO_LUONG;
                                dg_all += _obj.DON_GIA;
                                dvt_all += _obj.DVT;
                            } else {
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                            }

                        }

                        tt_all +=  nguoibenh == 1?_obj.NGUOI_BENH.toFixed(2):_obj.THANH_TIEN.toFixed(2) ;
                        tt78_all +=  _obj.NGUOI_BENH.toFixed(2);
                        ttchiphi_all +=  _obj.THANH_TIEN.toFixed(2);
                        tongtienbn+=  nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN);
                        tongtienbntt78+=  Number(_obj.NGUOI_BENH);
                        tongtientt78+=  Number(_obj.THANH_TIEN);
                        issum_all +=  '0';
                        if (_index < $scope.chitietthanhtoan.length - 1) {
                            sl_all +='$$';
                            dg_all += '$$';
                            nd_all += '$$';
                            tt_all += '$$';
                            tt78_all += '$$';
                            ttchiphi_all += '$$';
                            dvt_all+='$$';
                            issum_all+='$$';
                        }
                    })
                } else  {
                    $scope.chitietthanhtoan.forEach(function(_obj,_index) {
                        nd_all += escapeXml(_obj.NOIDUNG);
                        sl_all += _obj.SO_LUONG;
                        dg_all +=  _obj.DON_GIA;
                        if(_obj.GHI_CHU.includes("THUOC")) {
                            dvt_all += 'Viên';
                        } else {
                            dvt_all += 'Lần';
                        }

                        tt_all += _obj.THANH_TIEN.toFixed(2);
                        tongtienbn+=  _obj.THANH_TIEN;
                        tt78_all +=  _obj.NGUOI_BENH.toFixed(2);
                        ttchiphi_all +=  _obj.THANH_TIEN.toFixed(2) ;
                        tongtienbntt78+=  Number(_obj.NGUOI_BENH);
                        tongtientt78+=  Number(_obj.THANH_TIEN);
                        issum_all +=  '0';

                        if (_index < $scope.chitietthanhtoan.length - 1) {
                            sl_all +='$$';
                            dg_all += '$$';
                            nd_all += '$$';
                            tt_all += '$$';
                            dvt_all += '$$ ';
                            tt78_all += '$$';
                            ttchiphi_all += '$$';
                            issum_all+='$$';
                        } else {
                            if ($scope.thongtinthanhtoan.hienthistg==1) {
                                nd_all+='$$Mã khách hàng '+ $scope.thongtinbn.MA_BENH_NHAN
                                sl_all += '$$0';
                                dg_all += '$$0';
                                dvt_all += '$$ ';
                                tt78_all += '$$';
                                ttchiphi_all += '$$';
                                issum_all+='$$';
                            }
                        }
                    })
                }
                
                if ($scope.thongtinthanhtoan.dvtt != 96162 && $scope.chitietthanhtoan.length > 5 && chitietNgoaitru == 0) {
                    nd_all = '';
                    sl_all = '';
                    dg_all = '';
                    tt_all = '';
                    dvt_all = '';
                    tt78_all = '';
                    ttchiphi_all = '';
                    tongtienbn = 0;
                    tongtientt78 = 0;
                    tongtienbntt78 = 0;
                    if($scope.thongtinthanhtoan.hienthistg == 1) {
                        $scope.chitietthanhtoan.forEach(function (_obj) {
                            switch (_obj.GHI_CHU) {
                                case 'CONGKHAM':
                                    arr[0] = (arr[0] == undefined ? 0 : arr[0]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[0] = (arrtt78[0] == undefined ? 0 : arrtt78[0]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[0] = (arr_chiphitt78[0] == undefined ? 0 : arr_chiphitt78[0]) + Number( _obj.THANH_TIEN);
                                    break;
                                case 'XETNGHIEM':
                                    arr[2] = (arr[2] == undefined ? 0 : arr[2]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[2] = (arrtt78[2] == undefined ? 0 : arrtt78[2]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[2] = (arr_chiphitt78[2] == undefined ? 0 : arr_chiphitt78[2]) + Number( _obj.THANH_TIEN);
                                    break;
                                case 'CHANDOANHINHANH':
                                    arr[3] = (arr[3] == undefined ? 0 : arr[3]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[3] = (arrtt78[3] == undefined ? 0 : arrtt78[3]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[3] = (arr_chiphitt78[3] == undefined ? 0 : arr_chiphitt78[3]) + Number( _obj.THANH_TIEN);
                                    break;
                                case 'THUTHUATPHAUTHUAT':
                                    arr[4] = (arr[4] == undefined ? 0 : arr[4]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[4] = (arrtt78[4] == undefined ? 0 : arrtt78[4]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[4] = (arr_chiphitt78[4] == undefined ? 0 : arr_chiphitt78[4]) + Number( _obj.THANH_TIEN);
                                    break;
                                default:
                                    arr[5] = (arr[5] == undefined ? 0 : arr[5]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[5] = (arrtt78[5] == undefined ? 0 : arrtt78[5]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[5] = (arr_chiphitt78[5] == undefined ? 0 : arr_chiphitt78[5]) + Number( _obj.THANH_TIEN);
                                    break;
                            }
                            tongtienbn += Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN);
                            tongtienbntt78+=  Number(_obj.NGUOI_BENH);
                            tongtientt78+=  Number(_obj.THANH_TIEN);
                        })
                    } else {
                        $scope.chitietthanhtoan.forEach(function (_obj) {
                            switch (_obj.GHI_CHU) {
                                case 'CONGKHAM':
                                    arr[0] = (arr[0] == undefined ? 0 : arr[0]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[0] = (arrtt78[0] == undefined ? 0 : arrtt78[0]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[0] = (arr_chiphitt78[0] == undefined ? 0 : arr_chiphitt78[0]) + Number( _obj.THANH_TIEN);
                                    break;
                                case 'XETNGHIEM':
                                    arr[1] = (arr[1] == undefined ? 0 : arr[1]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[1] = (arrtt78[1] == undefined ? 0 : arrtt78[1]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[1] = (arr_chiphitt78[1] == undefined ? 0 : arr_chiphitt78[1]) + Number( _obj.THANH_TIEN);
                                    break;
                                case 'CHANDOANHINHANH':
                                    arr[2] = (arr[2] == undefined ? 0 : arr[2]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[2] = (arrtt78[2] == undefined ? 0 : arrtt78[2]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[2] = (arr_chiphitt78[2] == undefined ? 0 : arr_chiphitt78[2]) + Number( _obj.THANH_TIEN);
                                    break;
                                case 'THUTHUATPHAUTHUAT':
                                    arr[3] = (arr[3] == undefined ? 0 : arr[3]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[3] = (arrtt78[3] == undefined ? 0 : arrtt78[3]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[3] = (arr_chiphitt78[3] == undefined ? 0 : arr_chiphitt78[3]) + Number( _obj.THANH_TIEN);
                                    break;
                                default:
                                    arr[4] = (arr[4] == undefined ? 0 : arr[4]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    arrtt78[4] = (arrtt78[4] == undefined ? 0 : arrtt78[4]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[4] = (arr_chiphitt78[4] == undefined ? 0 : arr_chiphitt78[4]) + Number( _obj.THANH_TIEN);
                                    break;
                            }
                            tongtienbn += Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN);
                            tongtienbntt78+=  Number(_obj.NGUOI_BENH);
                            tongtientt78+=  Number(_obj.THANH_TIEN);
                        })
                    }
                }
            } else {

                if($scope.thongtinthanhtoan.hienthistg == 1) {
                    $scope.chitietthanhtoan.forEach(function (_obj) {
                        switch (_obj.GHI_CHU) {
                            case 'CONGKHAM':
                                arr[0] = (arr[0] == undefined ? 0 : arr[0]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[0] = (arrtt78[0] == undefined ? 0 : arrtt78[0]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[0] = (arr_chiphitt78[0] == undefined ? 0 : arr_chiphitt78[0]) + Number( _obj.THANH_TIEN);
                                break;
                            case 'XETNGHIEM':
                                arr[2] = (arr[2] == undefined ? 0 : arr[2]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[2] = (arrtt78[2] == undefined ? 0 : arrtt78[2]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[2] = (arr_chiphitt78[2] == undefined ? 0 : arr_chiphitt78[2]) + Number( _obj.THANH_TIEN);
                                break;
                            case 'CHANDOANHINHANH':
                                arr[3] = (arr[3] == undefined ? 0 : arr[3]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[3] = (arrtt78[3] == undefined ? 0 : arrtt78[3]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[3] = (arr_chiphitt78[3] == undefined ? 0 : arr_chiphitt78[3]) + Number( _obj.THANH_TIEN);
                                break;
                            case 'THUTHUATPHAUTHUAT':
                                arr[4] = (arr[4] == undefined ? 0 : arr[4]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[4] = (arrtt78[4] == undefined ? 0 : arrtt78[4]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[4] = (arr_chiphitt78[4] == undefined ? 0 : arr_chiphitt78[4]) + Number( _obj.THANH_TIEN);
                                break;
                            default:
                                arr[5] = (arr[5] == undefined ? 0 : arr[5]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[5] = (arrtt78[5] == undefined ? 0 : arrtt78[5]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[5] = (arr_chiphitt78[5] == undefined ? 0 : arr_chiphitt78[5]) + Number( _obj.THANH_TIEN);
                                break;
                        }
                        tongtienbn += Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN);
                        tongtienbntt78+=  Number(_obj.NGUOI_BENH);
                        tongtientt78+=  Number(_obj.THANH_TIEN);
                    })
                } else {
                    $scope.chitietthanhtoan.forEach(function (_obj) {
                        switch (_obj.GHI_CHU) {
                            case 'CONGKHAM': case'CONGKHAMCHUYENDEN':
                                arr[0] = (arr[0] == undefined ? 0 : arr[0]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[0] = (arrtt78[0] == undefined ? 0 : arrtt78[0]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[0] = (arr_chiphitt78[0] == undefined ? 0 : arr_chiphitt78[0]) + Number( _obj.THANH_TIEN);
                                break;
                            case 'XETNGHIEM':
                                arr[1] = (arr[1] == undefined ? 0 : arr[1]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[1] = (arrtt78[1] == undefined ? 0 : arrtt78[1]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[1] = (arr_chiphitt78[1] == undefined ? 0 : arr_chiphitt78[1]) + Number( _obj.THANH_TIEN);
                                break;
                            case 'CHANDOANHINHANH':
                                arr[2] = (arr[2] == undefined ? 0 : arr[2]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[2] = (arrtt78[2] == undefined ? 0 : arrtt78[2]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[2] = (arr_chiphitt78[2] == undefined ? 0 : arr_chiphitt78[2]) + Number( _obj.THANH_TIEN);
                                break;
                            case 'THUTHUATPHAUTHUAT':
                                arr[3] = (arr[3] == undefined ? 0 : arr[3]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[3] = (arrtt78[3] == undefined ? 0 : arrtt78[3]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[3] = (arr_chiphitt78[3] == undefined ? 0 : arr_chiphitt78[3]) + Number( _obj.THANH_TIEN);
                                break;
                            default:
                                arr[4] = (arr[4] == undefined ? 0 : arr[4]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                arrtt78[4] = (arrtt78[4] == undefined ? 0 : arrtt78[4]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[4] = (arr_chiphitt78[4] == undefined ? 0 : arr_chiphitt78[4]) + Number( _obj.THANH_TIEN);
                                break;
                        }
                        tongtienbn += Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN);
                        tongtienbntt78+=  Number(_obj.NGUOI_BENH);
                        tongtientt78+=  Number(_obj.THANH_TIEN);
                    })
                }

            }

            baohiemchi = sumData($scope.chitietthanhtoan, 'THANH_TIEN') - $scope.thongtinthanhtoan.sotientt;
        } else if($scope.thongtinbn.LOAIVP == 'NTDICHVU')
        {
            if($scope.thongtinthanhtoan.chitiettiendichvu == 0) {
                nd_all = "Thu tiền dịch vụ";
                tt_all = 0;
                tt78_all = 0;
                issum_all =  '0';
                $scope.chitietthanhtoan.forEach(function (_obj) {
                    tt_all += Number(_obj.NGUOI_BENH);
                    tt78_all += Number(_obj.NGUOI_BENH);
                    tongtienbn += _obj.THANH_TIEN;
                    tongtienbntt78+=  Number(_obj.NGUOI_BENH);
                    tongtientt78+=  Number(_obj.THANH_TIEN);
                })
                baohiemchi = 0;
                tilebhyt = 0;
            } else {


                $scope.chitietthanhtoan.forEach(function (_obj,_index) {
                    nd_all += escapeXml(_obj.NOIDUNG);
                    tt_all += Number(_obj.NGUOI_BENH);
                    tt78_all += Number(_obj.NGUOI_BENH);
                    dg_all += Number(_obj.DON_GIA);
                    dvt_all += 'Ngày';
                    sl_all += Number(_obj.SO_LUONG);
                    tongtienbn += _obj.THANH_TIEN;
                    tongtienbntt78 += Number(_obj.NGUOI_BENH);
                    tongtientt78 += Number(_obj.THANH_TIEN);
                    ttchiphi_all += _obj.THANH_TIEN;
                    issum_all +=  '0';
                    if (_index < $scope.chitietthanhtoan.length - 1) {
                        sl_all +='$$';
                        dg_all += '$$';
                        nd_all += '$$';
                        tt_all += '$$';
                        tt78_all += '$$';
                        ttchiphi_all += '$$';
                        issum_all += '$$';
                        dvt_all += '$$ ';
                    }
                })
                baohiemchi = 0;
                tilebhyt = 0;
            }
        } else if ($scope.thongtinbn.LOAIVP == 'HDBL') {
            var temp = data.data.noidung.split(';');
            temp.forEach(function(value,index) {
                var ndsplit = value.split("_!!!_")
                nd_all+= ndsplit[0];
                sl_all += ndsplit[2];
                dg_all += ndsplit[3];
                dvt_all += ndsplit[1];
                tt_all += ndsplit[4];
                tt78_all += ndsplit[4];
                ttchiphi_all += ndsplit[4];
                issum_all += '0';
                if (index < temp.length - 1) {
                    sl_all +='$$';
                    dg_all += '$$';
                    nd_all += '$$';
                    tt_all += '$$';
                    dvt_all += '$$';
                    tt78_all += '$$';
                    ttchiphi_all += '$$';
                    issum_all += '$$';
                }

            })
            $scope.chitietthanhtoan.forEach(function(_obj) {
                tongtienbn+=  Number(_obj.THANH_TIEN);
                tongtienbntt78+=  Number(_obj.THANH_TIEN);
                tongtientt78+=  Number(_obj.THANH_TIEN);
            })
            baohiemchi = 0;
            tilebhyt = 0;
            $scope.thongtinbn['load']['TEN_DANTOC'] = 'Kinh';
            tendonvi = escapeXml($scope.hoadonle.tendonvi);
            masothue = $scope.hoadonle.masothue;
            sodienthoai = $scope.hoadonle.sodienthoai;
            if($scope.hoadonle.dantoc != undefined) {

                $scope.thongtinbn['load']['TEN_DANTOC'] = $scope.hoadonle.dantoc.trim();
            }
            paymentmethod =  $scope.thongtinbn.hinhthucthanhtoan;
            
        } else {
                if ($scope.thongtinthanhtoan.hienthistg == 1) {
                    $scope.chitietthanhtoan.forEach(function(_obj) {
                        switch(_obj.UU_TIEN_IN) {
                            case 1:
                                if(_obj.NOIDUNG == 'Công khám') {
                                    arr[0] = (arr[0] == undefined? 0: arr[0]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    arrtt78[0] = (arrtt78[0] == undefined ? 0 : arrtt78[0]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[0] = (arr_chiphitt78[0] == undefined ? 0 : arr_chiphitt78[0]) + Number( _obj.THANH_TIEN);
                                } else {
                                    arr[1] = (arr[1] == undefined? 0: arr[1]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    arrtt78[1] = (arrtt78[1] == undefined ? 0 : arrtt78[1]) + Number( _obj.NGUOI_BENH);
                                    arr_chiphitt78[1] = (arr_chiphitt78[1] == undefined ? 0 : arr_chiphitt78[1]) + Number( _obj.THANH_TIEN);
                                }

                                break;
                            case 2:
                                arr[2] = (arr[2] == undefined? 0: arr[2]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                arrtt78[2] = (arrtt78[2] == undefined ? 0 : arrtt78[2]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[2] = (arr_chiphitt78[2] == undefined ? 0 : arr_chiphitt78[2]) + Number( _obj.THANH_TIEN);
                                break;
                            case 3:
                                arr[3] = (arr[3] == undefined? 0: arr[3]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                arrtt78[3] = (arrtt78[3] == undefined ? 0 : arrtt78[3]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[3] = (arr_chiphitt78[3] == undefined ? 0 : arr_chiphitt78[3]) + Number( _obj.THANH_TIEN);
                                break;
                            case 4:
                                arr[4] = (arr[4] == undefined? 0: arr[4]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                arrtt78[4] = (arrtt78[4] == undefined ? 0 : arrtt78[4]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[4] = (arr_chiphitt78[4] == undefined ? 0 : arr_chiphitt78[4]) + Number( _obj.THANH_TIEN);
                                break;
                            default:
                                arr[5] = (arr[5] == undefined? 0: arr[5]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                arrtt78[5] = (arrtt78[5] == undefined ? 0 : arrtt78[5]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[5] = (arr_chiphitt78[5] == undefined ? 0 : arr_chiphitt78[5]) + Number( _obj.THANH_TIEN);
                                break;
                        }
                        tongtienbn+=  nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN;
                        tongtienbntt78 += Number(_obj.NGUOI_BENH);
                        tongtientt78 += Number(_obj.THANH_TIEN);
                    })
                    baohiemchi = nguoibenh == 1?0:sumData($scope.chitietthanhtoan, 'THANH_TIEN') - $scope.thongtinthanhtoan.sotientt;
                } else {
                    noitru = " và ngày giường"
                    $scope.chitietthanhtoan.forEach(function(_obj) {
                        switch(_obj.UU_TIEN_IN) {
                            case 1:
                                arr[0] = (arr[0] == undefined? 0: arr[0]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                arrtt78[0] = (arrtt78[0] == undefined ? 0 : arrtt78[0]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[0] = (arr_chiphitt78[0] == undefined ? 0 : arr_chiphitt78[0]) + Number( _obj.THANH_TIEN);
                                break;
                            case 2:
                                arr[1] = (arr[1] == undefined? 0: arr[1]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                arrtt78[1] = (arrtt78[1] == undefined ? 0 : arrtt78[1]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[1] = (arr_chiphitt78[1] == undefined ? 0 : arr_chiphitt78[1]) + Number( _obj.THANH_TIEN);
                                break;
                            case 3:
                                arr[2] = (arr[2] == undefined? 0: arr[2]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                arrtt78[2] = (arrtt78[2] == undefined ? 0 : arrtt78[2]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[2] = (arr_chiphitt78[2] == undefined ? 0 : arr_chiphitt78[2]) + Number( _obj.THANH_TIEN);
                                break;
                            case 4:
                                arr[3] = (arr[3] == undefined? 0: arr[3]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                arrtt78[3] = (arrtt78[3] == undefined ? 0 : arrtt78[3]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[3] = (arr_chiphitt78[3] == undefined ? 0 : arr_chiphitt78[3]) + Number( _obj.THANH_TIEN);
                                break;
                            default:
                                arr[4] = (arr[4] == undefined? 0: arr[4]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                arrtt78[4] = (arrtt78[4] == undefined ? 0 : arrtt78[4]) + Number( _obj.NGUOI_BENH);
                                arr_chiphitt78[4] = (arr_chiphitt78[4] == undefined ? 0 : arr_chiphitt78[4]) + Number( _obj.THANH_TIEN);
                                break;
                        }
                        tongtienbn+=  nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN;
                        tongtienbntt78 += Number(_obj.NGUOI_BENH);
                        tongtientt78 += Number(_obj.THANH_TIEN);
                    })
                    baohiemchi = nguoibenh == 1?0:sumData($scope.chitietthanhtoan, 'THANH_TIEN') - $scope.thongtinthanhtoan.sotientt;
                }
        }
        if ($scope.thongtinthanhtoan.hoadonchitiet != 1 || 
                ($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  $scope.thongtinbn.LOAIVP != 'THUPHI' && chitietNgoaitru == 0)
                ||  ($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  $scope.thongtinbn.LOAIVP == 'THUPHI'
                    && $scope.thongtinthanhtoan.dvtt != 96162 && $scope.chitietthanhtoan.length > 5 && chitietNgoaitru == 0)
                )
        {
            var temp = 1;   
            if($scope.thongtinthanhtoan.hienthistg == 1) {
            for (var i = 0; i < arr.length; i++) {
               switch(i) {
                   case 0:
                       if(arr[0] != undefined) {
                           nd_all += escapeXml("Công khám" + noitru);
                               sl_all += '1';
                               dg_all += arr[0].toFixed(2);
                               dvt_all += 'Lần';
                               tt_all += arr[0].toFixed(2);
                               tt78_all += arrtt78[0].toFixed(2);
                               ttchiphi_all += arr_chiphitt78[0].toFixed(2);
                                issum_all += '0';
                               temp++;
                           }

                           break;
                       case 1:
                           if (arr[1] != undefined) {
                               tt_all += arr[1].toFixed(2);
                               nd_all += escapeXml("Giường bệnh");
                               sl_all += '1';
                               dg_all += arr[1].toFixed(2);
                               dvt_all += 'Lần';
                               tt78_all += arrtt78[1].toFixed(2);
                               ttchiphi_all += arr_chiphitt78[1].toFixed(2);
                               issum_all += '0';
                               temp++;
                           }

                           break;
                       case 2:
                           if (arr[2] != undefined) {
                               tt_all += arr[2].toFixed(2);
                               nd_all += escapeXml("Xét nghiệm");
                               sl_all += '1';
                               dg_all += arr[2].toFixed(2);
                               dvt_all += 'Lần';
                               tt78_all += arrtt78[2].toFixed(2);
                               ttchiphi_all += arr_chiphitt78[2].toFixed(2);
                               issum_all += '0';
                               temp++;
                           }

                           break;
                       case 3:
                           if (arr[3] != undefined) {
                               tt_all += arr[3].toFixed(2);
                               nd_all += escapeXml("Chẩn đoán hình ảnh và TDCN");
                               sl_all += '1';
                               dg_all += arr[3].toFixed(2);
                               dvt_all += 'Lần';
                               tt78_all += arrtt78[3].toFixed(2);
                               ttchiphi_all += arr_chiphitt78[3].toFixed(2);
                               issum_all += '0';
                               temp++;
                           }

                           break;
                       case 4:
                           if (arr[4] != undefined) {
                               tt_all += arr[4].toFixed(2);
                               nd_all += escapeXml("Thủ thuật phẫu thuật");
                               sl_all += '1';
                               dg_all += arr[4].toFixed(2);
                               dvt_all += 'Lần';
                               tt78_all += arrtt78[4].toFixed(2);
                               ttchiphi_all += arr_chiphitt78[4].toFixed(2);
                               issum_all += '0';
                           }

                           break;
                       case 5:
                           if (arr[5] != undefined) {
                               tt_all += arr[5].toFixed(2);
                               nd_all += escapeXml("Thuốc, vật tư và chi phí khác");
                               sl_all += '1';
                               dg_all += arr[5].toFixed(2);
                               dvt_all += 'Lần';
                               tt78_all += arrtt78[5].toFixed(2);
                               ttchiphi_all += arr_chiphitt78[5].toFixed(2);
                               issum_all += '0';
                           }

                           break;   
                   }
                   if (i < arr.length - 1) {
                       if (arr[i] != undefined) {
                           sl_all +='$$';
                            dg_all += '$$';
                            nd_all += '$$';
                            tt_all += '$$';
                            dvt_all += '$$';
                            tt78_all += '$$';
                            ttchiphi_all += '$$';
                            issum_all += '$$';
                       }

                   }else {
                        if ($scope.thongtinbn.LOAIVP == 'NOITRU') {
                            nd_all+='$$Mẫu số 02/BV số bệnh án '+ $scope.thongtinbn.SOBENHAN
                            sl_all += '$$0';
                            dg_all += '$$0';
                            dvt_all += '$$ ';
                            tt78_all += '$$ ';
                            ttchiphi_all += '$$ ';
                            issum_all += '$$ ';
                        }
                        if ($scope.thongtinbn.LOAIVP == 'BANT') {
                            nd_all+='$$Mã khách hàng '+ $scope.thongtinbn.MA_BENH_NHAN
                            sl_all += '$$0';
                            dg_all += '$$0';
                            dvt_all += '$$ ';
                            tt78_all += '$$ ';
                            ttchiphi_all += '$$ ';
                            issum_all += '$$ ';
                        }
                    }
               }
            } else {
                for (var i = 0; i < arr.length; i++) {
                   switch(i) {
                       case 0:
                           if(arr[0] != undefined) {
                               nd_all += escapeXml("Công khám" + noitru);
                               sl_all += '0';
                               dg_all += '0';
                               dvt_all += ' ';
                               tt_all += arr[0].toFixed(2);
                               tt78_all += arrtt78[0].toFixed(2);
                               ttchiphi_all += arr_chiphitt78[0].toFixed(2);
                               issum_all += '0';
                               temp++;
                            }

                       break;
                   case 1:
                       if (arr[1] != undefined) {
                           tt_all += arr[1].toFixed(2);
                           nd_all += escapeXml("Xét Nghiệm");
                           sl_all += '0';
                           dg_all += '0';
                           dvt_all += ' ';
                           tt78_all += arrtt78[1].toFixed(2);
                           ttchiphi_all += arr_chiphitt78[1].toFixed(2);
                           issum_all += '0';
                           temp++;
                       }

                       break;
                   case 2:
                       if (arr[2] != undefined) {
                           tt_all += arr[2].toFixed(2);
                           nd_all += escapeXml("Chẩn đoán hình ảnh và TDCN");
                           sl_all += '0';
                           dg_all += '0';
                               dvt_all += ' ';
                           tt78_all += arrtt78[2].toFixed(2);
                           ttchiphi_all += arr_chiphitt78[2].toFixed(2);
                           issum_all += '0';
                           temp++;
                       }

                       break;
                   case 3:
                       if (arr[3] != undefined) {
                           tt_all += arr[3].toFixed(2);
                           nd_all += escapeXml("Thủ thuật phẫu thuật");
                           sl_all += '0';
                           dg_all += '0';
                               dvt_all += ' ';
                           tt78_all += arrtt78[3].toFixed(2);
                           ttchiphi_all += arr_chiphitt78[3].toFixed(2);
                           issum_all += '0';
                           temp++;
                       }

                       break;
                   case 4:
                       if (arr[4] != undefined) {
                           tt_all += arr[4].toFixed(2);
                           nd_all += escapeXml("Thuốc, vật tư và chi phí khác");
                           sl_all += '0';
                           dg_all += '0';
                               dvt_all += ' ';
                           tt78_all += arrtt78[4].toFixed(2);
                           ttchiphi_all += arr_chiphitt78[4].toFixed(2);
                           issum_all += '0';
                           temp++;
                       }

                       break;    
                }
               if (i < arr.length - 1) {
                   if (arr[i] != undefined) {
                       sl_all +='$$';
                        dg_all += '$$';
                        nd_all += '$$';
                        tt_all += '$$';
                        dvt_all += '$$';
                       tt78_all += '$$';
                       ttchiphi_all += '$$';
                       issum_all += '$$';
                   }

               }
           }
            }
        }
        if ( ['NOITRU','BANT'].indexOf($scope.thongtinbn.LOAIVP) == 0 && $scope.chitietthanhtoan.length < 6 && $scope.thongtinthanhtoan.hienthichitietnoitru ==1) {
            nd_all = '';
            sl_all = '';
            dg_all = '';
            tt_all = '';
            dvt_all = '';
            tt78_all = '';
            issum_all = '';
            ttchiphi_all = '';
            tongtienbn = 0;
            tongtienbntt78 = 0;
            tongtientt78 = 0;
            $scope.chitietthanhtoan.forEach(function(_obj,_index) {
                nd_all += escapeXml(_obj.NOIDUNG);
                sl_all += '0';
                dg_all += '0';
                issum_all += '0';
                dvt_all+= ' '
                tt_all += _obj.THANH_TIEN.toFixed(2);
                tt78_all += _obj.NGUOI_BENH.toFixed(2);
                ttchiphi_all += _obj.THANH_TIEN.toFixed(2);
                tongtienbn+=  nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN);
                tongtienbntt78+=  Number(_obj.NGUOI_BENH);
                tongtientt78+=  Number(_obj.THANH_TIEN);
                if (_index < $scope.chitietthanhtoan.length - 1) {
                    sl_all +='$$';
                    dg_all += '$$';
                    nd_all += '$$';
                    tt_all += '$$';
                    tt78_all += '$$';
                    ttchiphi_all += '$$';
                    dvt_all +='$$';
                    issum_all +='$$';
                }
            })
        }
        var d = new Date();
        var n = d.getTime();
        var arisingdate = parseDateToString($scope.thongtinthanhtoan.ngaythu);
        var key =  $scope.thongtinthanhtoan.dvtt+"-"+n + "-" + arisingdate.replace("-","") + "-" + $scope.thongtinbn.MA_BENH_NHAN + "-" + $scope.thongtinbn.SOVAOVIEN;
        if (keyold != undefined && keyold != '-1') {
            key = keyold;
        }
        var url = "cmu_taohoadon_invoice";
        data.data['KEY_HD'] = key; 
        var dt =  $scope.thongtinbn['load'].TEN_DANTOC == undefined? 'Kinh': $scope.thongtinbn['load'].TEN_DANTOC;
        if($scope.thongtinthanhtoan.hoadonchitiet == 1 && $scope.thongtinthanhtoan.dvtt == 96162) {
            if($scope.thongtinbn.LOAIVP == 'HDBL') {
                dt = ' ';
            } else {
                if($scope.thongtinbn.LOAIVP == 'NOITRU') {
                    var d = new Date();
                    var n = d.getFullYear();
                    dt =n - $scope.thongtinbn.NAMSINH
                } else {
                    dt = $scope.thongtinbn['load'].TUOI
                }
                
            }
            
        }
        if(tongtienbn.toFixed() == 0 || $scope.thongtinthanhtoan.sotientt.toFixed() == 0) {
            alert("Không được phát hành hóa đơn số tiền bằng không, Nếu có lỗi xảy ra vui lòng phát hành lại.")
            return false;
	}
        if ($scope.thongtinthanhtoan.tmiengiam == true) {
            tongtienbn = Number($scope.thongtinthanhtoan.sotienbntra);
            tongtientt78 = Number($scope.thongtinthanhtoan.sotienbntra);
            tongtienbntt78 = Number($scope.thongtinthanhtoan.sotienbntra);
        }

        if (vat > 0) {
            var temp = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra:$scope.thongtinthanhtoan.sotientt;
            vatAmount = Number(temp)*Number(vat)/100;
            vatAmount  =vatAmount.toFixed();
        }
        if ($scope.thongtinthanhtoan.hienthistg== 1) {
            if($scope.thongtinbn.LOAIVP == 'BHYT') {
                sl_all ='1';
                dg_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                nd_all = ' Thu '+ (100 - Number($scope.thongtinbn.load.TYLEBAOHIEM)) + "% mẫu số 01/BV mã khách hàng "+$scope.thongtinbn.MA_BENH_NHAN;
                tt_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                tt78_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                ttchiphi_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                dvt_all = 'Lần';
            }
            if ($scope.thongtinbn.LOAIVP == 'BANT' && $scope.thongtinbn.load.TYLEBAOHIEM > 0) {
                sl_all ='1';
                dg_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                nd_all = ' Thu '+ (100 - Number($scope.thongtinbn.load.TYLEBAOHIEM)) + "% mẫu số 01/BV mã khách hàng "+$scope.thongtinbn.MA_BENH_NHAN;
                tt_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                tt78_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                ttchiphi_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                dvt_all = 'Lần';
            }
            if($scope.thongtinbn.LOAIVP == 'NOITRU' && $scope.thongtinbn.load.TYLEBAOHIEM > 0) {
                sl_all ='1';
                dg_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                nd_all = ' Thu '+ (100 - Number($scope.thongtinbn.load.TYLEBAOHIEM)) + "% mẫu số 02/BV số bệnh án "+$scope.thongtinbn.load.SOBENHAN;
                tt_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                tt78_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                ttchiphi_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                dvt_all = 'Lần';
            }
            if ($scope.thongtinthanhtoan.tmiengiam == true && $scope.thongtinbn.load.TYLEBAOHIEM == 0) {
                sl_all ='1$$0';
                dg_all =  Number($scope.thongtinthanhtoan.sotienbntra).toFixed();
                dg_all += '$$0'
                nd_all = "Thuốc và chi phí khác;Mẫu số 02/BV số bệnh án "+$scope.thongtinbn.load.SOBENHAN;
                tt_all = $scope.thongtinthanhtoan.sotienbntra.toFixed();
                tt78_all = $scope.thongtinthanhtoan.sotienbntra.toFixed();
                ttchiphi_all = $scope.thongtinthanhtoan.sotienbntra.toFixed();
                tt_all += '$$0';
                tt78_all += '$$0';
                ttchiphi_all += '$$0';
                dvt_all = 'Lần; ';
                tongtienbn =  Number($scope.thongtinthanhtoan.sotienbntra).toFixed()
                tongtienbntt78 =  Number($scope.thongtinthanhtoan.sotienbntra).toFixed()
                tongtientt78 =  Number($scope.thongtinthanhtoan.sotienbntra).toFixed()
            }
        }
        var diachi = ' ';
        var gioitinh =' ';
        var sothebaohiem = ' ';
        var tuoi =  ' ';
        if($scope.thongtinbn.load !== undefined)  {
            diachi = $scope.thongtinbn.load.DIACHI == undefined? ' ': $scope.thongtinbn.load.DIACHI;
            console.log("thong tin bn", $scope.thongtinbn, $scope.thongtinbn.GIOI_TINH , $scope.thongtinbn.load.GIOITINH )
            var tempGioitinh = $scope.thongtinbn.GIOI_TINH || $scope.thongtinbn.load.GIOITINH;
            if (tempGioitinh !== undefined && !isNaN(tempGioitinh) && tempGioitinh != ' ' && tempGioitinh != '') {
                gioitinh = Number(tempGioitinh) === 0 ? 'Nữ': 'Nam'
            } else {
                gioitinh = '';
            }
            sothebaohiem = $scope.thongtinbn.load.SOBAOHIEMYTE == undefined? ' ': $scope.thongtinbn.load.SOBAOHIEMYTE
            tuoi = $scope.thongtinbn.load.TUOI == undefined? ' ': $scope.thongtinbn.load.TUOI
        }
        if ($scope.thongtinthanhtoan.dvtt == '96014') {
            if ($scope.thongtinthanhtoan.pattern == '02GTTT0/002'
                || $scope.thongtinthanhtoan.pattern == '02GTTT0/001') {
                dt = tuoi
            }
        }
        if(tongtienbn.toFixed() == 0 || $scope.thongtinthanhtoan.sotientt.toFixed() == 0) {
            alert("Không được phát hành hóa đơn số tiền bằng không, Nếu có lỗi xảy ra vui lòng phát hành lại.")
            return false;
        }
        return $.post(url, {
                    id: data.data.ID,
                    key: key,
                    account: $scope.thongtinthanhtoan.account,
                    acpass: $scope.thongtinthanhtoan.acpass,
                    username: $scope.thongtinthanhtoan.username,
                    password: $scope.thongtinthanhtoan.password,
                    publishservice: $scope.thongtinthanhtoan.publishservice,
                    pattern: $scope.thongtinthanhtoan.pattern,
                    serial: $scope.thongtinthanhtoan.serial,

                    ma_bn: escapeXml($scope.thongtinbn.MA_BENH_NHAN + ''),
                    ten_bn: escapeXml($scope.thongtinbn.TEN_BENH_NHAN+''),
                    diachi: escapeXml(diachi+''),
                    paymethod: paymentmethod == undefined? 'Tiền mặt':paymentmethod,

                    nd_all: nd_all,
                    sl_all: sl_all,
                    dg_all: dg_all,
                    tt_all: tt_all,
                    tt78_all: tt78_all,
                    ttchiphi_all: ttchiphi_all,
                    tongtientt78: Math.round(tongtientt78),
                    tongtienbntt78: $scope.thongtinthanhtoan.tmiengiam == true? Number($scope.thongtinthanhtoan.sotienbntra).toFixed():$scope.thongtinthanhtoan.sotientt.toFixed(),
                    issum_all: issum_all,
                    dvt_all: dvt_all,
                    
                    total: $scope.thongtinthanhtoan.tmiengiam == true? Number($scope.thongtinthanhtoan.sotienbntra).toFixed():$scope.thongtinthanhtoan.sotientt.toFixed(),
                    totaltt78: $scope.thongtinthanhtoan.tmiengiam == true? Number($scope.thongtinthanhtoan.sotienbntra).toFixed():$scope.thongtinthanhtoan.sotientt.toFixed(),
                    arisingdate: arisingdate,
                    dantoc: dt,
                    tuoi: tuoi,
                    gioitinh: gioitinh,
                    tenKhoa: $scope.thongtinbn.TEN_PHONGBAN,
                    thebhyt: sothebaohiem,
                    tilebhyt: tilebhyt == undefined? '0':tilebhyt,
                    baohiemchi: baohiemchi.toFixed(),
                    tongtienbn: tongtienbn.toFixed(),
                    tendonvi: tendonvi,
                    masothue: masothue,
                    sodienthoai: sodienthoai,
                    vat: vat,
                    vatAmount: vatAmount

                });
        
    }
    
    this.thanhtoanhddt = function($scope,data) {
        var url = "thanhtoan_invoice";
        return $.post(url, {
            id: data.data.ID,
            ma_bn: data.data.MA_BN,
            account: $scope.thongtinthanhtoan.account,
            acpass: $scope.thongtinthanhtoan.acpass,
            username: $scope.thongtinthanhtoan.username,
            password: $scope.thongtinthanhtoan.password,
            key_hd: data.data.KEY_HD,
            businessservice: $scope.thongtinthanhtoan.businessservice,
            arisingdate: c_string_formatdate(data.data.NGAYTHUVIENPHI)
        });
    }
    
    
    this.capnhatsobienlai = function($scope,stt_lantt,sohoadon,keyhd) {
        return this.postData("cmu_post", [{
                param:'url',
                data: [
                    $scope.thongtinthanhtoan.dvtt,
                    stt_lantt,
                    $scope.thongtinbn.SOVAOVIEN,
                    sohoadon,
                    $scope.thongtinbn.LOAIVP,
                   keyhd,
                    'CMU_UPDATE_SOHOADON'
                ]
            }])
    }
    
    this.capnhathoadonhuy = function(item) {
        console.log("lantt ", item)
        this.postData("cmu_post", [{
                param:'url',
                data: [
                    item.dvtt,
                    item.stt_lantt,
                    item.key_hd,
                    item.sovaovien,
                    'CMU_INSERT_HUYHD'
                ]
            }])
    }
    
    this.layidhdt = function($scope) {
        var noitru = 1;
        if ($scope.thongtinbn.LOAIVP == 'BHYT' || $scope.thongtinbn.LOAIVP == 'THUPHI') {
            noitru = 0
        }
        if ($scope.thongtinbn.LOAIVP == 'HDBL') {
            noitru = -2
        }
        return this.postData("cmu_post", 
            [{
                param:'url',
                data: [
                    $scope.thongtinthanhtoan.dvtt,
                    $scope.thongtinbn.SOVAOVIEN,
                    $scope.thongtinthanhtoan.STT_LANTT,
                    $scope.thongtinbn.SOVAOVIEN,
                    $scope.thongtinbn.SOVAOVIEN_DT == undefined? 0:$scope.thongtinbn.SOVAOVIEN_DT,
                    noitru,
                    'CMU_LAYID_HDDT'
                ]
            }])
    }
    
    this.capnhathoadonphathanh = function($scope) {
        return this.postData("cmu_post", 
            [{
                param:'url',
                data: [
                    $scope.thongtinthanhtoan.dvtt,
                    $scope.thongtinthanhtoan.key_hd,
                    $scope.thongtinthanhtoan.STT_LANTT,
                    'CMU_CAPNHAT_HD_LANTTHUY'
                ]
            }])
    }
    
    
    this.xoahddtchuaphathanh = function($scope) {
        return this.postData("cmu_post", [{
                param:'url',
                data: [
                    $scope.thongtinthanhtoan.dvtt,
                    $scope.thongtinthanhtoan.STT_LANTT,
                    $scope.thongtinbn.SOVAOVIEN,
                    $scope.thongtinbn.SOVAOVIEN_NOI,
                    $scope.thongtinbn.SOVAOVIEN_DT,
                   $scope.thongtinbn.SOPHIEUTHANHTOAN,
                    'CMU_XOAHDDT_CPH'
                ]
            }])
    }
    
    this.getPattern = function(dvtt,maquyenbienlai) {
        return  this.getData("cmu_getlist",
                        [   
                            dvtt,
                            maquyenbienlai,'CMU_LAY_PATTERN'
                        ]
                    )
    }
    
    
    this.huyphathanhhoad = function($scope,data) {
        var url = "huyphathanh_invoice";
        return $.post(url, {
                id: data.data.ID,
                ma_bn: data.data.MA_BN,
                account: $scope.thongtinthanhtoan.account,
                acpass: $scope.thongtinthanhtoan.acpass,
                username: $scope.thongtinthanhtoan.username,
                password: $scope.thongtinthanhtoan.password,
                key_hd: data.data.KEY_HD,
                businessservice: $scope.thongtinthanhtoan.businessservice,
                arisingdate: c_string_formatdate(data.data.NGAYTHUVIENPHI)
            });
    }
    this.huythanhtoanhddt = function($scope,data) {
        var url = "huythanhtoan_invoice";
        return $.post(url, {
            id: data.data.ID,
             ma_bn: data.data.MA_BN,
             account: $scope.thongtinthanhtoan.account,
             pass: $scope.thongtinthanhtoan.password,
             acpass: $scope.thongtinthanhtoan.acpass,
             username: $scope.thongtinthanhtoan.username,
             password: $scope.thongtinthanhtoan.password,
             key_hd: data.data.KEY_HD,
             businessservice: $scope.thongtinthanhtoan.businessservice,
             arisingdate: c_string_formatdate(data.data.NGAYTHUVIENPHI)
        });
    }
    
    this.chuyendoihoadon = function($scope,data,nguoichuyendoi) {
        var url = "cmu_chuyendoihoadon?username=" + $scope.thongtinthanhtoan.username + "&password="
                                                + $scope.thongtinthanhtoan.password + "&key_hd=" 
                                                + data.data.KEY_HD + "&businessservice=" 
                                                + $scope.thongtinthanhtoan.portalservice
                                                + '&nguoichuyendoi='+ nguoichuyendoi   ;
       $(location).attr('href', url);
    }

    this.chuyendoihoadon_backup = function($scope,data,nguoichuyendoi) {
        var url = "cmu_chuyendoihoadon_backup?username=" + $scope.thongtinthanhtoan.username + "&password="
            + $scope.thongtinthanhtoan.password + "&key_hd="
            + data.data.KEY_HD + "&businessservice="
            + $scope.thongtinthanhtoan.portalservice
            + '&nguoichuyendoi='+ nguoichuyendoi   ;
        $(location).attr('href', url);
    }
    
    this.showLoading = function($mdDialog) {
        return $mdDialog.show({
                                     multiple: false,
                                     skipHide: true,
                                    templateUrl: 'loading.html',
                                    parent: angular.element(document.body),
                                    clickOutsideToClose:false
                                  })
    }
    this.hideLoading = function($mdDialog) {
        $mdDialog.hide();
    }
    
    this.inphieuhoanung = function($scope,stt_lantt) {
        var arr = [$scope.thongtinthanhtoan.dvtt, 
                    $scope.thongtinbn.STT_BENHAN,
                    stt_lantt,
                    $scope.thongtinthanhtoan.sotienthoilai, 
                    $scope.thongtinbn.STT_DOTDIEUTRI, 
                    new Intl.NumberFormat("en-US").format($scope.thongtinthanhtoan.sotienthoilai),
                    $scope.thongtinthanhtoan.tennhanvien, "NOP",'0','0'];
        var url = "noitru_inphieuhoanung?url=" + c_convert_to_string(arr);
        $(location).attr('href', url);
    }
    
    this.inapphoadon = function($scope,chitietthanhtoan,ngaythu,data,sohoadon) {
        var nguoibenh = $("#cmu_ctlaynguoibenh").val()
        var nd_all = '';
        var sl_all = '';
        var dg_all = '';
        var tt_all = '';
        var arr = [];
        var noitru = "";
        var tilebhyt = $scope.thongtinbn.load.TYLEBAOHIEM;
        var baohiemchi = 0;
        var tongtienbn = 0;
        var chitiethd = '';
        if ($scope.thongtinbn.LOAIVP == 'BHYT' || $scope.thongtinbn.LOAIVP == 'THUPHI') {
            
            if($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  $scope.thongtinbn.LOAIVP == 'THUPHI') {
                $scope.chitietthanhtoan.forEach(function(_obj,_index) {
                    nd_all += escapeXml(_obj.NOIDUNG);
                    sl_all += '0';
                    dg_all += '0';
                    tt_all += _obj.THANH_TIEN.toFixed(2);
                    tongtienbn+=  _obj.THANH_TIEN;
                    if (_index < $scope.chitietthanhtoan.length - 1) {
                        sl_all +='$$';
                        dg_all += '$$';
                        nd_all += '$$';
                        tt_all += '$$';

                    }
                })
            } else {
                chitietthanhtoan.forEach(function(_obj) {
                switch(_obj.GHI_CHU) {
                    case 'CONGKHAM':
                        arr[0] = (arr[0] == undefined? 0: arr[0]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;
                    case 'XETNGHIEM':
                        arr[1] = (arr[1] == undefined? 0: arr[1]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;
                    case 'CHANDOANHINHANH':
                        arr[2] = (arr[2] == undefined? 0: arr[2]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;
                    case 'THUTHUATPHAUTHUAT':
                        arr[3] = (arr[3] == undefined? 0: arr[3]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;
                    default:
                        arr[4] = (arr[4] == undefined? 0: arr[4]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;    
                }
                tongtienbn+=  nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN;
                })
            }        
            
            baohiemchi = sumData($scope.chitietthanhtoan, 'THANH_TIEN') - $scope.thongtinthanhtoan.sotientt;
        } else if($scope.thongtinbn.LOAIVP == 'NTDICHVU')
        {
            nd_all = "Thu tiền dịch vụ";
            tt_all = 0;
            chitietthanhtoan.forEach(function(_obj) {
                tt_all += Number(_obj.NGUOI_BENH);
                tongtienbn+=  _obj.THANH_TIEN;
            })
            baohiemchi = 0;
            tilebhyt = 0;
        } else if ($scope.thongtinbn.LOAIVP == 'HDBL') {
            nd_all = data.data.noidung;
            chitietthanhtoan.forEach(function(_obj) {
                tt_all += Number(_obj.NGUOI_BENH);
                tongtienbn+=  Number(_obj.THANH_TIEN);
            })
            baohiemchi = 0;
            tilebhyt = 0;
        } else {
            noitru = " và ngày giường"
            chitietthanhtoan.forEach(function(_obj) {
                switch(_obj.UU_TIEN_IN) {
                    case 1:
                        arr[0] = (arr[0] == undefined? 0: arr[0]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;
                    case 2:
                        arr[1] = (arr[1] == undefined? 0: arr[1]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;
                    case 3:
                        arr[2] = (arr[2] == undefined? 0: arr[2]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;
                    case 4:
                        arr[3] = (arr[3] == undefined? 0: arr[3]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;
                    default:
                        arr[4] = (arr[4] == undefined? 0: arr[4]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                        break;    
                }
                tongtienbn+=  nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN;
            })
            baohiemchi = nguoibenh == 1?0:sumData($scope.chitietthanhtoan, 'THANH_TIEN') - $scope.thongtinthanhtoan.sotientt;
        }
        if ($scope.thongtinthanhtoan.hoadonchitiet != 1 || ($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  $scope.thongtinbn.LOAIVP != 'THUPHI')) {
            var temp = 1;   
            for (var i = 0; i < arr.length; i++) {
               switch(i) {
                   case 0:
                       if(arr[0] != undefined) {
                           nd_all += escapeXml("Công khám" + noitru);
                           sl_all += '0';
                           dg_all += '0';
                           tt_all += arr[0].toFixed(2);
                           chitiethd="|"+escapeXml("Công khám" + noitru)+"|null|null|null|";
                           chitiethd+=arr[0].toFixed(2);
                           temp++;
                       }

                       break;
                   case 1:
                       if (arr[1] != undefined) {
                           tt_all += arr[1].toFixed(2);
                           nd_all += escapeXml("Xét Nghiệm");
                           sl_all += '0';
                           dg_all += '0';
                           chitiethd="|"+escapeXml("Xét Nghiệm")+"|null|null|null|";
                           chitiethd+=arr[1].toFixed(2);
                           temp++;
                       }

                       break;
                   case 2:
                       if (arr[2] != undefined) {
                           tt_all += arr[2].toFixed(2);
                           nd_all += escapeXml("Chẩn đoán hình ảnh và TDCN");
                           sl_all += '0';
                           dg_all += '0';
                           chitiethd="|"+escapeXml("Chẩn đoán hình ảnh và TDCN")+"|null|null|null|";
                           chitiethd+=arr[2].toFixed(2);
                           temp++;
                       }

                       break;
                   case 3:
                       if (arr[3] != undefined) {
                           tt_all += arr[3].toFixed(2);
                           nd_all += escapeXml("Thủ thuật phẫu thuật");
                           sl_all += '0';
                           dg_all += '0';
                           chitiethd="|"+escapeXml("Thủ thuật phẫu thuật")+"|null|null|null|";
                           chitiethd+=arr[3].toFixed(2);
                           temp++;
                       }

                       break;
                   case 4:
                       if (arr[4] != undefined) {
                           tt_all += arr[4].toFixed(2);
                           nd_all += escapeXml("Thuốc, vật tư và chi phí khác");
                           sl_all += '0';
                           dg_all += '0';
                           chitiethd="|"+escapeXml("Thuốc, vật tư và chi phí khác")+"|null|null|null|";
                           chitiethd+=arr[4].toFixed(2);
                       }

                       break;    
               }
               if (i < arr.length - 1) {
                   if (arr[i] != undefined) {
                   sl_all +='$$';
                   dg_all += '$$';
                   nd_all += '$$';
                   tt_all += '$$';
                   }

               }
           }
       }
    var filetxt = 'InHoaDonHDDT|'+$scope.thongtinthanhtoan.pattern+'|'+
                    $scope.thongtinthanhtoan.serial+'|'+$scope.thongtinthanhtoan.manv
                    +"|"+$scope.thongtinthanhtoan.tennhanvien + "|" 
                    + parseDateToString(ngaythu) + "|"
                    + parseDateToString(new Date()) + "|"     
                    +$scope.thongtinbn.load.SOBAOHIEMYTE + "|"
                    + $scope.thongtinbn.TEN_PHONGBAN +"|"+$scope.thongtinbn.MA_BENH_NHAN + "|" 
                    +$scope.thongtinbn.TEN_BENH_NHAN + "|"
                    +$scope.thongtinbn.load.DIACHI + "|"
                    +$scope.thongtinbn.load.TYLEBAOHIEM + "|"
                    +sohoadon+chitiethd;
    console.log("filetxt", filetxt)        
    var element = document.createElement('a');
    element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(filetxt));
    element.setAttribute('download', 'web_app.txt');
    element.style.display = 'none';
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
    }

    this.kiemtrahoadondatontai = function($scope,data) {
        return $.post('cmu_checkfkey', {
                    id: data.data.ID,
                    key: data.data.KEY_HD,
                    account: $scope.thongtinthanhtoan.username,
                    password: $scope.thongtinthanhtoan.password,
                    publishservice: $scope.thongtinthanhtoan.portalservice,
                   

                });
    }
    this.phathanhhoadon_tach7dong = function($scope, data, keyold) {
        var nguoibenh = $("#cmu_ctlaynguoibenh").val()
        var nd_all = '';
        var sl_all = '';
        var dg_all = '';
        var tt_all = '';
        var dvt_all = '';
        var arr = [];
        var noitru = "";
        var tilebhyt = $scope.thongtinbn.load.TYLEBAOHIEM;
        var baohiemchi = 0;
        var tongtienbn = 0;
        var paymentmethod =  $scope.thongtinthanhtoan.hinhthucthanhtoan;
        var tendonvi = $scope.thongtinthanhtoan.tendonvi == undefined? "":$scope.thongtinthanhtoan.tendonvi;
        var masothue  = $scope.thongtinthanhtoan.masothue == undefined? "":$scope.thongtinthanhtoan.masothue;
        var sodienthoai = "";
        var vat = $scope.thongtinthanhtoan.vat;
        var vatAmount = "0";
        if ($scope.thongtinbn.LOAIVP == 'BHYT' || $scope.thongtinbn.LOAIVP == 'THUPHI') {

            if($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  ($scope.thongtinbn.LOAIVP == 'THUPHI' || ($scope.thongtinbn.LOAIVP == 'BHYT' && $scope.chitietthanhtoan.length < 6))) {
                if ($scope.thongtinbn.LOAIVP == 'BHYT') {
                    $scope.chitietthanhtoan.forEach(function(_obj,_index) {
                        nd_all += escapeXml(_obj.NOIDUNG);
                        if ($scope.thongtinthanhtoan.hienthistg==1) {
                            sl_all += '1';
                            dg_all += _obj.THANH_TIEN.toFixed(2);
                            dvt_all += 'Lần';
                        } else {
                            sl_all += '0';
                            dg_all += '0';
                            dvt_all += ' ';
                        }

                        tt_all += _obj.THANH_TIEN.toFixed(2);
                        tongtienbn+=  nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN);
                        if (_index < $scope.chitietthanhtoan.length - 1) {
                            sl_all +='$$';
                            dg_all += '$$';
                            nd_all += '$$';
                            tt_all += '$$';
                            dvt_all+='$$';
                        }
                    })
                } else  {
                    $scope.chitietthanhtoan.forEach(function(_obj,_index) {
                        nd_all += escapeXml(_obj.NOIDUNG);
                        sl_all += '0';
                        dg_all += '0';
                        tt_all += _obj.THANH_TIEN.toFixed(2);
                        tongtienbn+=  _obj.THANH_TIEN;

                        if (_index < $scope.chitietthanhtoan.length - 1) {
                            sl_all +='$$';
                            dg_all += '$$';
                            nd_all += '$$';
                            tt_all += '$$';
                            dvt_all += '$$ ';
                        } else {
                            if ($scope.thongtinthanhtoan.hienthistg==1) {
                                nd_all+=';Mã khách hàng '+ $scope.thongtinbn.MA_BENH_NHAN
                                sl_all += ';0';
                                dg_all += ';0';
                                dvt_all += '$$ ';
                            }
                        }
                    })
                }

                if ($scope.thongtinthanhtoan.dvtt != 96977 && $scope.chitietthanhtoan.length > 5) {
                    nd_all = '';
                    sl_all = '';
                    dg_all = '';
                    tt_all = '';
                    dvt_all = '';
                    tongtienbn = 0;
                    if($scope.thongtinthanhtoan.hienthistg == 1) {
                        $scope.chitietthanhtoan.forEach(function (_obj) {
                            switch (_obj.GHI_CHU) {
                                case 'CONGKHAM':
                                    arr[0] = (arr[0] == undefined ? 0 : arr[0]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                                case 'XETNGHIEM':
                                    arr[2] = (arr[2] == undefined ? 0 : arr[2]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                                case 'CHANDOANHINHANH':
                                    arr[2] = (arr[3] == undefined ? 0 : arr[3]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                                case 'THUTHUATPHAUTHUAT':
                                    arr[3] = (arr[4] == undefined ? 0 : arr[4]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                                default:
                                    arr[5] = (arr[5] == undefined ? 0 : arr[5]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                            }
                            tongtienbn += Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN);
                        })
                    } else {
                        $scope.chitietthanhtoan.forEach(function (_obj) {
                            switch (_obj.GHI_CHU) {
                                case 'CONGKHAM':
                                    arr[0] = (arr[0] == undefined ? 0 : arr[0]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                                case 'XETNGHIEM':
                                    arr[1] = (arr[1] == undefined ? 0 : arr[1]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                                case 'CHANDOANHINHANH':
                                    arr[2] = (arr[2] == undefined ? 0 : arr[2]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                                case 'THUTHUATPHAUTHUAT':
                                    arr[3] = (arr[3] == undefined ? 0 : arr[3]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                                default:
                                    arr[4] = (arr[4] == undefined ? 0 : arr[4]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                            }
                            tongtienbn += Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN);
                        })
                    }
                }
            } else {

                if($scope.thongtinthanhtoan.hienthistg == 1) {
                    $scope.chitietthanhtoan.forEach(function (_obj) {
                        switch (_obj.GHI_CHU) {
                            case 'CONGKHAM':
                                arr[0] = (arr[0] == undefined ? 0 : arr[0]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                            case 'XETNGHIEM':
                                arr[2] = (arr[2] == undefined ? 0 : arr[2]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                            case 'CHANDOANHINHANH':
                                arr[2] = (arr[3] == undefined ? 0 : arr[3]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                            case 'THUTHUATPHAUTHUAT':
                                arr[3] = (arr[4] == undefined ? 0 : arr[4]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                            default:
                                arr[5] = (arr[5] == undefined ? 0 : arr[5]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                        }
                        tongtienbn += Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN);
                    })
                } else {
                    $scope.chitietthanhtoan.forEach(function (_obj) {
                        switch (_obj.GHI_CHU) {
                            case 'CONGKHAM':
                                arr[0] = (arr[0] == undefined ? 0 : arr[0]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                            case 'XETNGHIEM':
                                arr[1] = (arr[1] == undefined ? 0 : arr[1]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                            case 'CHANDOANHINHANH':
                                arr[2] = (arr[2] == undefined ? 0 : arr[2]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                            case 'THUTHUATPHAUTHUAT':
                                arr[3] = (arr[3] == undefined ? 0 : arr[3]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                            case 'vanchuyen':
                                arr[6] = (arr[6] == undefined ? 0 : arr[6]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                            case 'VANCHUYEN':
                                arr[6] = (arr[6] == undefined ? 0 : arr[6]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                            case 'TOATHUOC':
                                arr[4] = (arr[4] == undefined ? 0 : arr[4]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                            default:
                                arr[7] = (arr[7] == undefined ? 0 : arr[7]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                        }
                        tongtienbn += Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN);
                    })
                }

            }

            console.log("nguoibenh",nguoibenh)
            console.log("tongtienbn",tongtienbn)
            baohiemchi = sumData($scope.chitietthanhtoan, 'THANH_TIEN') - $scope.thongtinthanhtoan.sotientt;
        } else if($scope.thongtinbn.LOAIVP == 'NTDICHVU')
        {
            nd_all = "Thu tiền dịch vụ";
            tt_all = 0;
            $scope.chitietthanhtoan.forEach(function(_obj) {
                tt_all += Number(_obj.NGUOI_BENH);
                tongtienbn+=  _obj.THANH_TIEN;
            })
            baohiemchi = 0;
            tilebhyt = 0;
        } else if ($scope.thongtinbn.LOAIVP == 'HDBL') {
            var temp = data.data.noidung.split('$$');
            temp.forEach(function(value,index) {
                var ndsplit = value.split("_!!!_")
                nd_all+=ndsplit[0];
                sl_all += ndsplit[2];
                dg_all += ndsplit[3];
                dvt_all += ndsplit[1];
                tt_all += ndsplit[4];
                if (index < temp.length - 1) {
                    sl_all +='$$';
                    dg_all += '$$';
                    nd_all += '$$';
                    tt_all += '$$';
                    dvt_all += '$$';
                }

            })
            $scope.chitietthanhtoan.forEach(function(_obj) {
                tongtienbn+=  Number(_obj.THANH_TIEN);
            })
            baohiemchi = 0;
            tilebhyt = 0;
            $scope.thongtinbn['load']['TEN_DANTOC'] = 'Kinh';
            tendonvi = $scope.hoadonle.tendonvi;
            masothue = $scope.hoadonle.masothue;
            sodienthoai = $scope.hoadonle.sodienthoai;
            if($scope.hoadonle.dantoc != undefined) {

                $scope.thongtinbn['load']['TEN_DANTOC'] = $scope.hoadonle.dantoc.trim() == ''? 'Kinh': $scope.hoadonle.dantoc;
            }
            paymentmethod =  $scope.thongtinbn.hinhthucthanhtoan;

        } else {
            if ($scope.thongtinthanhtoan.hienthistg == 1) {
                $scope.chitietthanhtoan.forEach(function(_obj) {
                    switch(_obj.UU_TIEN_IN) {
                        case 1:
                            if(_obj.NOIDUNG == 'Công khám') {
                                arr[0] = (arr[0] == undefined? 0: arr[0]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            } else {
                                arr[1] = (arr[1] == undefined? 0: arr[1]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            }

                            break;
                        case 2:
                            arr[2] = (arr[2] == undefined? 0: arr[2]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 3:
                            arr[3] = (arr[3] == undefined? 0: arr[3]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 4:
                            arr[4] = (arr[4] == undefined? 0: arr[4]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        default:
                            arr[5] = (arr[5] == undefined? 0: arr[5]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                    }
                    tongtienbn+=  nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN;
                })
                baohiemchi = nguoibenh == 1?0:sumData($scope.chitietthanhtoan, 'THANH_TIEN') - $scope.thongtinthanhtoan.sotientt;
            } else {
                noitru = " và ngày giường"
                $scope.chitietthanhtoan.forEach(function(_obj) {
                    switch(_obj.UU_TIEN_IN) {
                        case 1:
                            arr[0] = (arr[0] == undefined? 0: arr[0]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 2:
                            arr[1] = (arr[1] == undefined? 0: arr[1]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 3:
                            arr[2] = (arr[2] == undefined? 0: arr[2]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 5:
                            arr[3] = (arr[3] == undefined? 0: arr[3]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 12:
                            arr[5] = (arr[5] == undefined? 0: arr[5]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 10:
                            arr[6] = (arr[6] == undefined? 0: arr[6]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 8:
                            arr[4] = (arr[4] == undefined? 0: arr[4]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 9:
                            arr[4] = (arr[4] == undefined? 0: arr[4]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        default:
                            arr[7] = (arr[7] == undefined? 0: arr[7]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                    }
                    tongtienbn+=  nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN;
                })
                baohiemchi = nguoibenh == 1?0:sumData($scope.chitietthanhtoan, 'THANH_TIEN') - $scope.thongtinthanhtoan.sotientt;
            }
        }
        if ($scope.thongtinthanhtoan.hoadonchitiet != 1 ||
            ($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  $scope.thongtinbn.LOAIVP != 'THUPHI')
            ||  ($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  $scope.thongtinbn.LOAIVP == 'THUPHI'
                && $scope.thongtinthanhtoan.dvtt != 96977 && $scope.chitietthanhtoan.length > 5)
        ) {
            var temp = 1;
            if($scope.thongtinthanhtoan.hienthistg == 1) {
                for (var i = 0; i < arr.length; i++) {
                    switch(i) {
                        case 0:
                            if(arr[0] != undefined) {
                                nd_all += escapeXml("Công khám" + noitru);
                                sl_all += '1';
                                dg_all += arr[0].toFixed(2);
                                dvt_all += 'Lần';
                                tt_all += arr[0].toFixed(2);
                                temp++;
                            }

                            break;
                        case 1:
                            if (arr[1] != undefined) {
                                tt_all += arr[1].toFixed(2);
                                nd_all += escapeXml("Giường bệnh");
                                sl_all += '1';
                                dg_all += arr[1].toFixed(2);
                                dvt_all += 'Lần';
                                temp++;
                            }

                            break;
                        case 2:
                            if (arr[2] != undefined) {
                                tt_all += arr[2].toFixed(2);
                                nd_all += escapeXml("Xét nghiệm");
                                sl_all += '1';
                                dg_all += arr[2].toFixed(2);
                                dvt_all += 'Lần';
                                temp++;
                            }

                            break;
                        case 3:
                            if (arr[3] != undefined) {
                                tt_all += arr[3].toFixed(2);
                                nd_all += escapeXml("Chẩn đoán hình ảnh và TDCN");
                                sl_all += '1';
                                dg_all += arr[3].toFixed(2);
                                dvt_all += 'Lần';
                                temp++;
                            }

                            break;
                        case 4:
                            if (arr[4] != undefined) {
                                tt_all += arr[4].toFixed(2);
                                nd_all += escapeXml("Thủ thuật phẫu thuật");
                                sl_all += '1';
                                dg_all += arr[4].toFixed(2);
                                dvt_all += 'Lần';
                            }

                            break;
                        case 5:
                            if (arr[5] != undefined) {
                                tt_all += arr[5].toFixed(2);
                                nd_all += escapeXml("Thuốc, vật tư và chi phí khác");
                                sl_all += '1';
                                dg_all += arr[5].toFixed(2);
                                dvt_all += 'Lần';
                            }

                            break;
                    }
                    if (i < arr.length - 1) {
                        if (arr[i] != undefined) {
                            sl_all +='$$';
                            dg_all += '$$';
                            nd_all += '$$';
                            tt_all += '$$';
                            dvt_all += '$$';
                        }

                    }else {
                        if ($scope.thongtinbn.LOAIVP == 'NOITRU') {
                            nd_all+=';Mẫu số 02/BV số bệnh án '+ $scope.thongtinbn.SOBENHAN
                            sl_all += ';0';
                            dg_all += ';0';
                            dvt_all += '$$ ';
                        }
                        if ($scope.thongtinbn.LOAIVP == 'BANT') {
                            nd_all+=';Mã khách hàng '+ $scope.thongtinbn.MA_BENH_NHAN
                            sl_all += ';0';
                            dg_all += ';0';
                            dvt_all += '$$ ';
                        }
                    }
                }
            } else {
                for (var i = 0; i < arr.length; i++) {
                    switch(i) {
                        case 0:
                            if(arr[0] != undefined && arr[0].toFixed(2) > 0) {
                                nd_all += escapeXml("Công khám" + noitru);
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                                tt_all += arr[0].toFixed(2);
                                temp++;
                            }

                            break;
                        case 1:
                            if (arr[1] != undefined && arr[1].toFixed(2) > 0) {
                                tt_all += arr[1].toFixed(2);
                                nd_all += escapeXml("Xét Nghiệm");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                                temp++;
                            }

                            break;
                        case 2:
                            if (arr[2] != undefined && arr[2].toFixed(2) >0) {
                                tt_all += arr[2].toFixed(2);
                                nd_all += escapeXml("Chẩn đoán hình ảnh và TDCN");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                                temp++;
                            }

                            break;
                        case 3:
                            if (arr[3] != undefined && arr[3].toFixed(2) > 0) {
                                tt_all += arr[3].toFixed(2);
                                nd_all += escapeXml("Thủ thuật phẫu thuật");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                                temp++;
                            }

                            break;
                        case 4:
                            if (arr[4] != undefined && arr[4].toFixed(2) > 0) {
                                tt_all += arr[4].toFixed(2);
                                nd_all += escapeXml("Thuốc và VTYT");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                            }

                            break;
                        case 5:
                            if (arr[5] != undefined && arr[5].toFixed(2) > 0) {
                                tt_all += arr[5].toFixed(2);
                                nd_all += escapeXml("Phụ thu");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                            }

                            break;
                        case 6:
                            if (arr[6] != undefined && arr[6].toFixed(2) > 0) {
                                tt_all += arr[6].toFixed(2);
                                nd_all += escapeXml("Vận chuyển");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                            }

                            break;
                        case 7:
                            if (arr[7] != undefined && arr[7].toFixed(2) > 0) {
                                tt_all += arr[7].toFixed(2);
                                nd_all += escapeXml("Chi phí khác");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                            }

                            break;
                    }
                    if (i < arr.length - 1) {
                        if (arr[i] != undefined && arr[i].toFixed(2) > 0) {
                            sl_all +='$$';
                            dg_all += '$$';
                            nd_all += '$$';
                            tt_all += '$$';
                            dvt_all += '$$';
                        }

                    }
                }
            }
        }
        if ( ['NOITRU','BANT'].indexOf($scope.thongtinbn.LOAIVP) == 0 && $scope.chitietthanhtoan.length < 6 && $scope.thongtinthanhtoan.hienthichitietnoitru ==1) {
            nd_all = '';
            sl_all = '';
            dg_all = '';
            tt_all = '';
            dvt_all = '';
            tongtienbn = 0;
            $scope.chitietthanhtoan.forEach(function(_obj,_index) {
                nd_all += escapeXml(_obj.NOIDUNG);
                sl_all += '0';
                dg_all += '0';
                dvt_all+= ' '
                tt_all += _obj.THANH_TIEN.toFixed(2);
                tongtienbn+=  nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN);
                if (_index < $scope.chitietthanhtoan.length - 1) {
                    sl_all +='$$';
                    dg_all += '$$';
                    nd_all += '$$';
                    tt_all += '$$';
                    dvt_all +='$$';
                }
            })
        }
        var d = new Date();
        var n = d.getTime();
        var arisingdate = parseDateToString($scope.thongtinthanhtoan.ngaythu);
        var key =  $scope.thongtinthanhtoan.dvtt+"-"+n + "-" + arisingdate.replace("-","") + "-" + $scope.thongtinbn.MA_BENH_NHAN + "-" + $scope.thongtinbn.SOVAOVIEN;
        if (keyold != undefined && keyold != '-1') {
            key = keyold;
        }
        var url = "cmu_taohoadon_invoice";
        data.data['KEY_HD'] = key;
        var dt =  $scope.thongtinbn['load'].TEN_DANTOC == undefined? 'Kinh': $scope.thongtinbn['load'].TEN_DANTOC;
        if($scope.thongtinthanhtoan.hoadonchitiet == 1 && $scope.thongtinthanhtoan.dvtt == 96977) {
            if($scope.thongtinbn.LOAIVP == 'HDBL') {
                dt = ' ';
            } else {
                if($scope.thongtinbn.LOAIVP == 'NOITRU') {
                    var d = new Date();
                    var n = d.getFullYear();
                    dt =n - $scope.thongtinbn.NAMSINH
                } else {
                    dt = $scope.thongtinbn['load'].TUOI
                }

            }

        }
        if(tongtienbn.toFixed() == 0 || $scope.thongtinthanhtoan.sotientt.toFixed() == 0) {
            alert("Không được phát hành hóa đơn số tiền bằng không, Nếu có lỗi xảy ra vui lòng phát hành lại.")
            return false;
        }
        if ($scope.thongtinthanhtoan.tmiengiam == true) {
            tongtienbn = Number($scope.thongtinthanhtoan.sotienbntra);
        }
        if (vat != '-1') {
            var temp = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra:$scope.thongtinthanhtoan.sotientt;
            vatAmount = Number(temp)*Number(vat)/100;
            vatAmount  =vatAmount.toFixed();
        }
        if ($scope.thongtinthanhtoan.hienthistg== 1) {
            if($scope.thongtinbn.LOAIVP == 'BHYT') {
                sl_all ='1';
                dg_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                nd_all = ' Thu '+ (100 - Number($scope.thongtinbn.load.TYLEBAOHIEM)) + "% mẫu số 01/BV mã khách hàng "+$scope.thongtinbn.MA_BENH_NHAN;
                tt_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                dvt_all = 'Lần';
            }
            if ($scope.thongtinbn.LOAIVP == 'BANT' && $scope.thongtinbn.load.TYLEBAOHIEM > 0) {
                sl_all ='1';
                dg_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                nd_all = ' Thu '+ (100 - Number($scope.thongtinbn.load.TYLEBAOHIEM)) + "% mẫu số 01/BV mã khách hàng "+$scope.thongtinbn.MA_BENH_NHAN;
                tt_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                dvt_all = 'Lần';
            }
            if($scope.thongtinbn.LOAIVP == 'NOITRU' && $scope.thongtinbn.load.TYLEBAOHIEM > 0) {
                sl_all ='1';
                dg_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                nd_all = ' Thu '+ (100 - Number($scope.thongtinbn.load.TYLEBAOHIEM)) + "% mẫu số 02/BV số bệnh án "+$scope.thongtinbn.load.SOBENHAN;
                tt_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                dvt_all = 'Lần';
            }
            if ($scope.thongtinthanhtoan.tmiengiam == true && $scope.thongtinbn.load.TYLEBAOHIEM == 0) {
                sl_all ='1;0';
                dg_all =  Number($scope.thongtinthanhtoan.sotienbntra).toFixed();
                dg_all += ';0'
                nd_all = "Thuốc và chi phí khác;Mẫu số 02/BV số bệnh án "+$scope.thongtinbn.load.SOBENHAN;
                tt_all = $scope.thongtinthanhtoan.sotienbntra.toFixed()
                tt_all += ';0'
                dvt_all = 'Lần; ';
                tongtienbn = Number($scope.thongtinthanhtoan.sotienbntra).toFixed()
            }
        }
        var diachi = ' ';
        var gioitinh =' ';
        var sothebaohiem = ' ';
        var tuoi =  ' ';
        if($scope.thongtinbn.load !== undefined)  {
            diachi = $scope.thongtinbn.load.DIACHI == undefined? ' ': $scope.thongtinbn.load.DIACHI;
            gioitinh = $scope.thongtinbn.GIOI_TINH == 0 || $scope.thongtinbn.load.GIOITINH == 0? "Nữ": "Nam";
            sothebaohiem = $scope.thongtinbn.load.SOBAOHIEMYTE == undefined? ' ': $scope.thongtinbn.load.SOBAOHIEMYTE
            tuoi = $scope.thongtinbn.load.TUOI == undefined? ' ': $scope.thongtinbn.load.TUOI
        }
        return $.post(url, {
            id: data.data.ID,
            key: key,
            account: $scope.thongtinthanhtoan.account,
            acpass: $scope.thongtinthanhtoan.acpass,
            username: $scope.thongtinthanhtoan.username,
            password: $scope.thongtinthanhtoan.password,
            publishservice: $scope.thongtinthanhtoan.publishservice,
            pattern: $scope.thongtinthanhtoan.pattern,
            serial: $scope.thongtinthanhtoan.serial,

            ma_bn: escapeXml($scope.thongtinbn.MA_BENH_NHAN + ''),
            ten_bn: escapeXml($scope.thongtinbn.TEN_BENH_NHAN+''),
            diachi: escapeXml(diachi+''),
            paymethod: paymentmethod == undefined? 'Tiền mặt':paymentmethod,

            nd_all: nd_all,
            sl_all: sl_all,
            dg_all: dg_all,
            tt_all: tt_all,
            dvt_all: dvt_all,

            total: $scope.thongtinthanhtoan.tmiengiam == true? Number($scope.thongtinthanhtoan.sotienbntra).toFixed():$scope.thongtinthanhtoan.sotientt.toFixed(),
            arisingdate: arisingdate,
            dantoc: dt,
            tuoi: tuoi,
            gioitinh: gioitinh,
            tenKhoa: $scope.thongtinbn.TEN_PHONGBAN,
            thebhyt: sothebaohiem,
            tilebhyt: tilebhyt == undefined? '0':tilebhyt,
            baohiemchi: baohiemchi.toFixed(),
            tongtienbn: tongtienbn.toFixed(),
            tendonvi: tendonvi,
            masothue: masothue,
            sodienthoai: sodienthoai,
            vat: vat,
            vatAmount: vatAmount

        });
    }

    this.phathanhhoadon_agg = function($scope, data,keyold) {
        var nguoibenh = $("#cmu_ctlaynguoibenh").val()
        var nd_all = '';
        var sl_all = '';
        var dg_all = '';
        var tt_all = '';
        var dvt_all = '';
        var arr = [];
        var noitru = "";
        var tilebhyt = $scope.thongtinbn.load.TYLEBAOHIEM;
        var baohiemchi = 0;
        var tongtienbn = 0;
        var paymentmethod =  $scope.thongtinthanhtoan.hinhthucthanhtoan;
        var tendonvi = $scope.thongtinthanhtoan.tendonvi == undefined? "":$scope.thongtinthanhtoan.tendonvi;
        var masothue  = $scope.thongtinthanhtoan.masothue == undefined? "":$scope.thongtinthanhtoan.masothue;
        var sodienthoai = "";
        var vat = $scope.thongtinthanhtoan.vat;
        var vatAmount = 0;

        if ($scope.thongtinbn.LOAIVP == 'BHYT' || $scope.thongtinbn.LOAIVP == 'THUPHI') {

            if($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  $scope.thongtinbn.LOAIVP == 'THUPHI') {
                $scope.chitietthanhtoan.forEach(function(_obj,_index) {
                    nd_all += escapeXml(_obj.NOIDUNG);
                    sl_all += '0';
                    dg_all += '0';
                    tt_all += _obj.THANH_TIEN.toFixed(2);
                    tongtienbn+=  _obj.THANH_TIEN;
                    if (_index < $scope.chitietthanhtoan.length - 1) {
                        sl_all +='$$';
                        dg_all += '$$';
                        nd_all += '$$';
                        tt_all += '$$';

                    }
                })
                if ($scope.thongtinthanhtoan.dvtt != 96977 && $scope.chitietthanhtoan.length > 5) {
                    nd_all = '';
                    sl_all = '';
                    dg_all = '';
                    tt_all = '';
                    tongtienbn = 0;
                    $scope.chitietthanhtoan.forEach(function(_obj) {
                        switch(_obj.GHI_CHU) {
                            case 'CONGKHAM':
                                arr[0] = (arr[0] == undefined? 0: arr[0]) + (Number(nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN));
                                break;
                            case 'XETNGHIEM':
                                if ($scope.thongtinthanhtoan.dvtt == 89005 || $scope.thongtinthanhtoan.dvtt == 89011 || $scope.thongtinthanhtoan.dvtt == 89001) {
                                    arr[2] = (arr[2] == undefined? 0: arr[2]) + (Number(nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN));
                                } else {
                                    arr[1] = (arr[1] == undefined? 0: arr[1]) + (Number(nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN));
                                }

                                break;
                            case 'CHANDOANHINHANH':
                                if ($scope.thongtinthanhtoan.dvtt == 89005){
                                    arr[3] = (arr[3] == undefined? 0: arr[3]) + (Number(nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN));
                                }else if($scope.thongtinthanhtoan.dvtt == 89011 || $scope.thongtinthanhtoan.dvtt == 89001) {
                                    if (_obj.GROUP_NHOM == "5. Thăm dò chức năng") {
                                        arr[4] = (arr[4] == undefined? 0: arr[4]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    } else {
                                        arr[3] = (arr[3] == undefined? 0: arr[3]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    }

                                } else {
                                    arr[2] = (arr[2] == undefined? 0: arr[2]) + (Number(nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN));
                                }

                                break;
                            case 'THUTHUATPHAUTHUAT':
                                if ($scope.thongtinthanhtoan.dvtt == 89005) {
                                    arr[4] = (arr[4] == undefined? 0: arr[4]) + (Number(nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN));
                                } else if ($scope.thongtinthanhtoan.dvtt == 89011 || $scope.thongtinthanhtoan.dvtt == 89001) {
                                    arr[5] = (arr[5] == undefined? 0: arr[5]) + (Number(nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN));
                                } else {
                                    arr[3] = (arr[3] == undefined? 0: arr[3]) + (Number(nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN));
                                }

                                break;
                            default:
                                if ($scope.thongtinthanhtoan.dvtt == 89005) {
                                    arr[5] = (arr[5] == undefined? 0: arr[5]) + (Number(nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN));
                                } else if ($scope.thongtinthanhtoan.dvtt == 89011 || $scope.thongtinthanhtoan.dvtt == 89001)  {
                                    arr[9] = (arr[9] == undefined? 0: arr[9]) + (Number(nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN));
                                } else {
                                    arr[4] = (arr[4] == undefined? 0: arr[4]) + (Number(nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN));
                                }
                                break;
                        }
                        tongtienbn+=  Number(nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN);
                    })
                }
            } else {
                $scope.chitietthanhtoan.forEach(function(_obj)
                    {
                        if ($scope.thongtinthanhtoan.dvtt == 89005){
                            switch(_obj.GHI_CHU) {
                                case 'CONGKHAM':
                                    arr[0] = (arr[0] == undefined? 0: arr[0]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    break;
                                case 'CONGKHAMCHUYENDEN':
                                    arr[0] = (arr[0] == undefined? 0: arr[0]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    break;
                                case 'XETNGHIEM':
                                    arr[2] = (arr[2] == undefined? 0: arr[2]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    break;
                                case 'CHANDOANHINHANH':
                                    arr[3] = (arr[3] == undefined? 0: arr[3]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    break;
                                case 'THUTHUATPHAUTHUAT':
                                    arr[4] = (arr[4] == undefined? 0: arr[4]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    break;
                                default:
                                    arr[5] = (arr[5] == undefined? 0: arr[5]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    break;
                            }
                        } else if ($scope.thongtinthanhtoan.dvtt == 89011 || $scope.thongtinthanhtoan.dvtt == 89001){
                            switch(_obj.GHI_CHU) {
                                case 'CONGKHAM':
                                    arr[0] = (arr[0] == undefined? 0: arr[0]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    break;
                                case 'CONGKHAMCHUYENDEN':
                                    arr[0] = (arr[0] == undefined? 0: arr[0]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    break;
                                case 'XETNGHIEM':
                                    arr[2] = (arr[2] == undefined? 0: arr[2]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    break;
                                case 'CHANDOANHINHANH':
                                    if (_obj.GROUP_NHOM == "5. Thăm dò chức năng") {
                                        arr[4] = (arr[4] == undefined? 0: arr[4]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));

                                    } else {
                                        arr[3] = (arr[3] == undefined? 0: arr[3]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    }

                                    break;
                                case 'THUTHUATPHAUTHUAT':
                                    arr[5] = (arr[5] == undefined? 0: arr[5]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    break;
                                case 'TOATHUOC':
                                    arr[6] = (arr[6] == undefined? 0: arr[6]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    break;
                                default:
                                    arr[9] = (arr[9] == undefined? 0: arr[9]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    break;
                            }
                        }
                        else{
                            switch(_obj.GHI_CHU) {
                                case 'CONGKHAM':
                                    arr[0] = (arr[0] == undefined? 0: arr[0]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    break;
                                case 'XETNGHIEM':
                                    arr[1] = (arr[1] == undefined? 0: arr[1]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    break;
                                case 'CHANDOANHINHANH':
                                    arr[2] = (arr[2] == undefined? 0: arr[2]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    break;
                                case 'THUTHUATPHAUTHUAT':
                                    arr[3] = (arr[3] == undefined? 0: arr[3]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    break;
                                default:
                                    arr[4] = (arr[4] == undefined? 0: arr[4]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                                    break;
                            }
                        }

                        tongtienbn+=  nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN;
                    }


                )
            }

            baohiemchi = sumData($scope.chitietthanhtoan, 'THANH_TIEN') - $scope.thongtinthanhtoan.sotientt;
        } else if($scope.thongtinbn.LOAIVP == 'NTDICHVU')
        {
            nd_all = "Thu tiền dịch vụ";
            tt_all = 0;
            $scope.chitietthanhtoan.forEach(function(_obj) {
                tt_all += Number(_obj.NGUOI_BENH);
                tongtienbn+=  _obj.THANH_TIEN;
            })
            baohiemchi = 0;
            tilebhyt = 0;
        } else if ($scope.thongtinbn.LOAIVP == 'HDBL') {
            var temp = data.data.noidung.split('$$');
            temp.forEach(function(value,index) {
                var ndsplit = value.split("_!!!_")
                nd_all+=ndsplit[0];
                sl_all += ndsplit[2];
                dg_all += ndsplit[3];
                dvt_all += ndsplit[1];
                tt_all += ndsplit[4];
                if (index < temp.length - 1) {
                    sl_all +='$$';
                    dg_all += '$$';
                    nd_all += '$$';
                    tt_all += '$$';
                    dvt_all += '$$';
                }

            })
            $scope.chitietthanhtoan.forEach(function(_obj) {
                tongtienbn+=  Number(_obj.THANH_TIEN);
            })
            baohiemchi = 0;
            tilebhyt = 0;
            $scope.thongtinbn['load']['TEN_DANTOC'] = 'Kinh';
            tendonvi = $scope.hoadonle.tendonvi;
            masothue = $scope.hoadonle.masothue;
            sodienthoai = $scope.hoadonle.sodienthoai;
            if($scope.hoadonle.dantoc != undefined) {

                $scope.thongtinbn['load']['TEN_DANTOC'] = $scope.hoadonle.dantoc.trim() == ''? 'Kinh': $scope.hoadonle.dantoc;
            }
            paymentmethod =  $scope.thongtinbn.hinhthucthanhtoan;
        } else {
            noitru = " và ngày giường"
            $scope.chitietthanhtoan.forEach(function(_obj) {
                if ($scope.thongtinthanhtoan.dvtt == 89005) {
                    switch(_obj.UU_TIEN_IN) {
                        case 1:
                            if (_obj.NOIDUNG == "Công khám") {
                                arr[0] = (arr[0] == undefined? 0: arr[0]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            } else {
                                arr[1] = (arr[1] == undefined? 0: arr[1]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));

                            }
                            break;
                        case 2:
                            arr[2] = (arr[2] == undefined? 0: arr[2]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 3:
                            arr[3] = (arr[3] == undefined? 0: arr[3]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 5:
                            arr[4] = (arr[4] == undefined? 0: arr[4]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 8:
                            arr[5] = (arr[5] == undefined? 0: arr[5]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 9:
                            arr[5] = (arr[5] == undefined? 0: arr[5]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 10:
                            arr[6] = (arr[6] == undefined? 0: arr[6]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 12:
                            arr[7] = (arr[7] == undefined? 0: arr[7]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        default:
                            arr[7] = (arr[7] == undefined? 0: arr[7]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                    }
                } else if ($scope.thongtinthanhtoan.dvtt == 89011 || $scope.thongtinthanhtoan.dvtt == 89001){
                    switch(_obj.UU_TIEN_IN) {
                        case 1:
                            if (_obj.NOIDUNG == "Công khám") {
                                arr[0] = (arr[0] == undefined? 0: arr[0]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            } else {
                                arr[1] = (arr[1] == undefined? 0: arr[1]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));

                            }
                            break;
                        case 2:
                            arr[2] = (arr[2] == undefined? 0: arr[2]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 3:
                            if (_obj.GROUP_NHOM == "4. Thăm dò chức năng") {
                                arr[4] = (arr[4] == undefined? 0: arr[4]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            } else {
                                arr[3] = (arr[3] == undefined? 0: arr[3]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            }
                            break;
                        case 5:
                            arr[5] = (arr[5] == undefined? 0: arr[5]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 8:
                            arr[6] = (arr[6] == undefined? 0: arr[6]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 9:
                            arr[7] = (arr[7] == undefined? 0: arr[7]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 10:
                            arr[8] = (arr[8] == undefined? 0: arr[8]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 12:
                            arr[9] = (arr[9] == undefined? 0: arr[9]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        default:
                            arr[9] = (arr[9] == undefined? 0: arr[9]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                    }
                }
                else {
                    switch(_obj.UU_TIEN_IN) {
                        case 1:
                            arr[0] = (arr[0] == undefined? 0: arr[0]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 2:
                            arr[1] = (arr[1] == undefined? 0: arr[1]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 3:
                            arr[2] = (arr[2] == undefined? 0: arr[2]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 5:
                            arr[3] = (arr[3] == undefined? 0: arr[3]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        default:
                            arr[4] = (arr[4] == undefined? 0: arr[4]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                    }
                }
                tongtienbn+=  nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN;
            })
            baohiemchi = nguoibenh == 1?0:sumData($scope.chitietthanhtoan, 'THANH_TIEN') - $scope.thongtinthanhtoan.sotientt;
        }
        if ($scope.thongtinthanhtoan.hoadonchitiet != 1 || ($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  $scope.thongtinbn.LOAIVP != 'THUPHI')
            ||  ($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  $scope.thongtinbn.LOAIVP == 'THUPHI'
                && $scope.thongtinthanhtoan.dvtt != 96977 && $scope.chitietthanhtoan.length > 5)) {
            var temp = 1;
            for (var i = 0; i < arr.length; i++) {
                if ($scope.thongtinthanhtoan.dvtt == 89005) {
                    switch(i) {
                        case 0:
                            if(arr[0] != undefined) {
                                nd_all += escapeXml("Công khám");
                                sl_all += '0';
                                dg_all += '0';
                                tt_all += arr[0].toFixed(2);
                                temp++;
                            }

                            break;
                        case 1:
                            if(arr[1] != undefined) {
                                nd_all += escapeXml("Tiền giường" );
                                sl_all += '0';
                                dg_all += '0';
                                tt_all += arr[1].toFixed(2);
                                temp++;
                            }

                            break;
                        case 2:
                            if (arr[2] != undefined) {
                                tt_all += arr[2].toFixed(2);
                                nd_all += escapeXml("Xét Nghiệm");
                                sl_all += '0';
                                dg_all += '0';
                                temp++;
                            }

                            break;
                        case 3:
                            if (arr[3] != undefined) {
                                tt_all += arr[3].toFixed(2);
                                nd_all += escapeXml("Chẩn đoán hình ảnh và TDCN");
                                sl_all += '0';
                                dg_all += '0';
                                temp++;
                            }

                            break;
                        case 4:
                            if (arr[4] != undefined) {
                                tt_all += arr[4].toFixed(2);
                                nd_all += escapeXml("Thủ thuật phẫu thuật");
                                sl_all += '0';
                                dg_all += '0';
                                temp++;
                            }

                            break;
                        case 5:
                            if (arr[5] != undefined) {
                                tt_all += arr[5].toFixed(2);
                                nd_all += escapeXml("Thuốc, vật tư y tế");
                                sl_all += '0';
                                dg_all += '0';
                            }

                            break;
                        case 6:
                            if (arr[6] != undefined) {
                                tt_all += arr[6].toFixed(2);
                                nd_all += escapeXml("Tiền xe");
                                sl_all += '0';
                                dg_all += '0';
                            }

                            break;
                        case 7:
                            if (arr[7] != undefined) {
                                tt_all += arr[7].toFixed(2);
                                nd_all += escapeXml("Tiền ăn và chi phí khác");
                                sl_all += '0';
                                dg_all += '0';
                            }

                            break;
                    }
                }  else if ($scope.thongtinthanhtoan.dvtt == 89011 || $scope.thongtinthanhtoan.dvtt == 89001){
                    switch(i) {
                        case 0:
                            if(arr[0] != undefined) {
                                nd_all += escapeXml("Công khám");
                                sl_all += '0';
                                dg_all += '0';
                                tt_all += arr[0].toFixed(2);
                                temp++;
                            }

                            break;
                        case 1:
                            if(arr[1] != undefined) {
                                nd_all += escapeXml("Tiền giường" );
                                sl_all += '0';
                                dg_all += '0';
                                tt_all += arr[1].toFixed(2);
                                temp++;
                            }

                            break;
                        case 2:
                            if (arr[2] != undefined) {
                                tt_all += arr[2].toFixed(2);
                                nd_all += escapeXml("Xét Nghiệm");
                                sl_all += '0';
                                dg_all += '0';
                                temp++;
                            }

                            break;
                        case 3:
                            if (arr[3] != undefined) {
                                tt_all += arr[3].toFixed(2);
                                nd_all += escapeXml("Chẩn đoán hình ảnh");
                                sl_all += '0';
                                dg_all += '0';
                                temp++;
                            }

                            break;
                        case 4:
                            if (arr[4] != undefined) {
                                tt_all += arr[4].toFixed(2);
                                nd_all += escapeXml("Thăm dò chức năng");
                                sl_all += '0';
                                dg_all += '0';
                                temp++;
                            }

                            break;
                        case 5:
                            if (arr[5] != undefined) {
                                tt_all += arr[5].toFixed(2);
                                nd_all += escapeXml("Thủ thuật phẫu thuật");
                                sl_all += '0';
                                dg_all += '0';
                                temp++;
                            }

                            break;
                        case 6:
                            if (arr[6] != undefined) {
                                tt_all += arr[6].toFixed(2);
                                nd_all += escapeXml("Thuốc, dịch truyền");
                                sl_all += '0';
                                dg_all += '0';
                            }

                            break;
                        case 7:
                            if (arr[7] != undefined) {
                                tt_all += arr[7].toFixed(2);
                                nd_all += escapeXml("Vật tư y tế");
                                sl_all += '0';
                                dg_all += '0';
                            }

                            break;
                        case 8:
                            if (arr[8] != undefined) {
                                tt_all += arr[8].toFixed(2);
                                nd_all += escapeXml("Vận chuyển");
                                sl_all += '0';
                                dg_all += '0';
                            }

                            break;
                        case 9:
                            if (arr[9] != undefined) {
                                tt_all += arr[9].toFixed(2);
                                nd_all += escapeXml("Phụ thu và chi phí khác");
                                sl_all += '0';
                                dg_all += '0';
                            }

                            break;
                    }
                }
                else {
                    switch(i) {
                        case 0:
                            if(arr[0] != undefined) {
                                nd_all += escapeXml("Công khám" + noitru);
                                sl_all += '0';
                                dg_all += '0';
                                tt_all += arr[0].toFixed(2);
                                temp++;
                            }

                            break;
                        case 1:
                            if (arr[1] != undefined) {
                                tt_all += arr[1].toFixed(2);
                                nd_all += escapeXml("Xét Nghiệm");
                                sl_all += '0';
                                dg_all += '0';
                                temp++;
                            }

                            break;
                        case 2:
                            if (arr[2] != undefined) {
                                tt_all += arr[2].toFixed(2);
                                nd_all += escapeXml("Chẩn đoán hình ảnh và TDCN");
                                sl_all += '0';
                                dg_all += '0';
                                temp++;
                            }

                            break;
                        case 3:
                            if (arr[3] != undefined) {
                                tt_all += arr[3].toFixed(2);
                                nd_all += escapeXml("Thủ thuật phẫu thuật");
                                sl_all += '0';
                                dg_all += '0';
                                temp++;
                            }

                            break;
                        case 4:
                            if (arr[4] != undefined) {
                                tt_all += arr[4].toFixed(2);
                                nd_all += escapeXml("Thuốc, vật tư và chi phí khác");
                                sl_all += '0';
                                dg_all += '0';
                            }

                            break;
                    }
                }
                if (i < arr.length - 1) {
                    if (arr[i] != undefined) {
                        sl_all +='$$';
                        dg_all += '$$';
                        nd_all += '$$';
                        tt_all += '$$';
                    }

                }
            }
        }

        var d = new Date();
        var n = d.getTime();
        var arisingdate = parseDateToString($scope.thongtinthanhtoan.ngaythu);
        var key =  $scope.thongtinthanhtoan.dvtt+"-"+n + "-" + arisingdate.replace("-","") + "-" + $scope.thongtinbn.MA_BENH_NHAN + "-" + $scope.thongtinbn.SOVAOVIEN;
        if (keyold != undefined && keyold != '-1') {
            key = keyold;
        }
        var url = "cmu_taohoadon_invoice";
        data.data['KEY_HD'] = key;
        var dt = data.data.dantoc == undefined? 'Kinh': data.data.dantoc;
        console.log("tongtienbn", tongtienbn)
        if($scope.thongtinthanhtoan.hoadonchitiet == 1) {
            if($scope.thongtinbn.LOAIVP == 'HDBL') {
                dt = ' ';
            } else {
                if($scope.thongtinbn.LOAIVP == 'NOITRU') {
                    var d = new Date();
                    var n = d.getFullYear();
                    dt =n - $scope.thongtinbn.NAMSINH
                } else {
                    dt = $scope.thongtinbn['load'].TUOI
                }

            }

        }
        if (vat != '-1') {
            var temp = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra:$scope.thongtinthanhtoan.sotientt;
            vatAmount = Number(temp)*Number(vat)/100;
            vatAmount  =vatAmount.toFixed();
        }
        if(tongtienbn.toFixed() == 0 || $scope.thongtinthanhtoan.sotientt.toFixed() == 0) {
            alert("Không được phát hành hóa đơn số tiền bằng không, Nếu có lỗi xảy ra vui lòng phát hành lại.")
            return false;
        }
        return $.post(url, {
            id: data.data.ID,
            key: key,
            account: $scope.thongtinthanhtoan.account,
            acpass: $scope.thongtinthanhtoan.acpass,
            username: $scope.thongtinthanhtoan.username,
            password: $scope.thongtinthanhtoan.password,
            publishservice: $scope.thongtinthanhtoan.publishservice,
            pattern: $scope.thongtinthanhtoan.pattern,
            serial: $scope.thongtinthanhtoan.serial,

            ma_bn: escapeXml($scope.thongtinbn.MA_BENH_NHAN + ''),
            ten_bn: escapeXml($scope.thongtinbn.TEN_BENH_NHAN+''),
            diachi: escapeXml($scope.thongtinbn.load.DIACHI+''),
            paymethod: paymentmethod,

            nd_all: nd_all,
            sl_all: sl_all,
            dg_all: dg_all,
            tt_all: tt_all,

            total: $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed(),
            arisingdate: arisingdate,
            dantoc: dt,
            gioitinh: $scope.thongtinbn.GIOI_TINH == 0 || $scope.thongtinbn.load.GIOITINH == 0? "Nữ": "Nam",
            tenKhoa: $scope.thongtinbn.TEN_PHONGBAN,
            thebhyt: $scope.thongtinbn.load.SOBAOHIEMYTE,
            tilebhyt: tilebhyt,
            baohiemchi: baohiemchi.toFixed(),
            tongtienbn: tongtienbn.toFixed(),
            tendonvi: tendonvi,
            masothue: masothue,
            sodienthoai: sodienthoai,
            vat: vat,
            vatAmount: vatAmount

        });

    }

    this.phathanhhoadon_nhombk_hgi = function($scope, data, keyold) {
        var nguoibenh = $("#cmu_ctlaynguoibenh").val()
        var nd_all = '';
        var sl_all = '';
        var dg_all = '';
        var tt_all = '';
        var dvt_all = '';
        var arr = [];
        var noitru = "";
        var tilebhyt = $scope.thongtinbn.load.TYLEBAOHIEM;
        var baohiemchi = 0;
        var tongtienbn = 0;
        var paymentmethod =  $scope.thongtinthanhtoan.hinhthucthanhtoan;
        var tendonvi = $scope.thongtinthanhtoan.tendonvi == undefined? "":$scope.thongtinthanhtoan.tendonvi;
        var masothue  = $scope.thongtinthanhtoan.masothue == undefined? "":$scope.thongtinthanhtoan.masothue;
        var sodienthoai = "";
        var vat = $scope.thongtinthanhtoan.vat;
        var vatAmount = "0";
        if ($scope.thongtinbn.LOAIVP == 'BHYT' || $scope.thongtinbn.LOAIVP == 'THUPHI') {

            if($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  ($scope.thongtinbn.LOAIVP == 'THUPHI' || ($scope.thongtinbn.LOAIVP == 'BHYT' && $scope.chitietthanhtoan.length < 6))) {
                if ($scope.thongtinbn.LOAIVP == 'BHYT') {
                    $scope.chitietthanhtoan.forEach(function(_obj,_index) {
                        nd_all += escapeXml(_obj.NOIDUNG);
                        if ($scope.thongtinthanhtoan.hienthistg==1) {
                            sl_all += '1';
                            dg_all += _obj.THANH_TIEN.toFixed(2);
                            dvt_all += 'Lần';
                        } else {
                            if(nguoibenh == 0) {
                                sl_all += _obj.SO_LUONG;
                                dg_all += _obj.DON_GIA;
                                dvt_all += _obj.DVT;
                            } else {
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                            }

                        }

                        tt_all +=  nguoibenh == 1?_obj.NGUOI_BENH.toFixed(2):_obj.THANH_TIEN.toFixed(2) ;
                        tongtienbn+=  nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN);
                        if (_index < $scope.chitietthanhtoan.length - 1) {
                            sl_all +='$$';
                            dg_all += '$$';
                            nd_all += '$$';
                            tt_all += '$$';
                            dvt_all+='$$';
                        }
                    })
                } else  {
                    $scope.chitietthanhtoan.forEach(function(_obj,_index) {
                        nd_all += escapeXml(_obj.NOIDUNG);
                        sl_all += _obj.SO_LUONG;
                        dg_all +=  _obj.DON_GIA;
                        if(_obj.GHI_CHU.includes("THUOC")) {
                            dvt_all += 'Viên';
                        } else {
                            dvt_all += 'Lần';
                        }

                        tt_all += _obj.THANH_TIEN.toFixed(2);
                        tongtienbn+=  _obj.THANH_TIEN;

                        if (_index < $scope.chitietthanhtoan.length - 1) {
                            sl_all +='$$';
                            dg_all += '$$';
                            nd_all += '$$';
                            tt_all += '$$';
                            dvt_all += '$$ ';
                        } else {
                            if ($scope.thongtinthanhtoan.hienthistg==1) {
                                nd_all+=';Mã khách hàng '+ $scope.thongtinbn.MA_BENH_NHAN
                                sl_all += ';0';
                                dg_all += ';0';
                                dvt_all += '$$ ';
                            }
                        }
                    })
                }

                if ($scope.thongtinthanhtoan.dvtt != 96977 && $scope.chitietthanhtoan.length > 5) {
                    nd_all = '';
                    sl_all = '';
                    dg_all = '';
                    tt_all = '';
                    dvt_all = '';
                    tongtienbn = 0;
                    if($scope.thongtinthanhtoan.hienthistg == 1) {
                        $scope.chitietthanhtoan.forEach(function (_obj) {
                            switch (_obj.GHI_CHU) {
                                case 'CONGKHAM':
                                    arr[0] = (arr[0] == undefined ? 0 : arr[0]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                                case 'XETNGHIEM':
                                    arr[2] = (arr[2] == undefined ? 0 : arr[2]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                                case 'CHANDOANHINHANH':
                                    arr[2] = (arr[3] == undefined ? 0 : arr[3]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                                case 'THUTHUATPHAUTHUAT':
                                    arr[3] = (arr[4] == undefined ? 0 : arr[4]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                                default:
                                    arr[5] = (arr[5] == undefined ? 0 : arr[5]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                            }
                            tongtienbn += Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN);
                        })
                    } else {
                        $scope.chitietthanhtoan.forEach(function (_obj) {
                            if(_obj.GROUP_NHOM == undefined) {
                                switch (_obj.GHI_CHU) {
                                    case 'CONGKHAM':
                                    case 'CONGKHAMCHUYENDEN':
                                        arr[0] = (arr[0] == undefined ? 0 : arr[0]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                        break;
                                    case 'XETNGHIEM':
                                        arr[3] = (arr[3] == undefined ? 0 : arr[3]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                        break;
                                    case 'CHANDOANHINHANH':
                                        arr[4] = (arr[4] == undefined ? 0 : arr[4]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                        break;
                                    case 'THUTHUATPHAUTHUAT':
                                        arr[6] = (arr[6] == undefined ? 0 : arr[6]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                        break;
                                    case 'TOATHUOCBANTAIQUAYTHUOCBV':
                                        arr[8] = (arr[8] == undefined ? 0 : arr[8]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                        break;
                                    case 'TOADONGY_KHONGBHYT':
                                        arr[8] = (arr[8] == undefined ? 0 : arr[8]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                        break;
                                    case 'VANCHUYEN':
                                        arr[11] = (arr[11] == undefined ? 0 : arr[11]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                        break;
                                    default:
                                        arr[12] = (arr[12] == undefined ? 0 : arr[12]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                        break;
                                }
                            } else {
                                var _tien = Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN);
                                if (_tien > 0) {
                                    if (_obj.GROUP_NHOM.startsWith("1.")) {
                                        arr[0] = (arr[0] == undefined ? 0 : arr[0]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    } else if (_obj.GROUP_NHOM.startsWith("2.")) {
                                        arr[2] = (arr[2] == undefined ? 0 : arr[2]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    } else if (_obj.GROUP_NHOM.startsWith("3.")) {
                                        arr[3] = (arr[3] == undefined ? 0 : arr[3]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    } else if (_obj.GROUP_NHOM.startsWith("4.")) {
                                        arr[4] = (arr[4] == undefined ? 0 : arr[4]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    } else if (_obj.GROUP_NHOM.startsWith("5.")) {
                                        arr[5] = (arr[5] == undefined ? 0 : arr[5]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    } else if (_obj.GROUP_NHOM.startsWith("6.")) {
                                        arr[6] = (arr[6] == undefined ? 0 : arr[6]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    } else if (_obj.GROUP_NHOM.startsWith("7.")) {
                                        arr[7] = (arr[7] == undefined ? 0 : arr[7]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    } else if (_obj.GROUP_NHOM.startsWith("8.")) {
                                        arr[8] = (arr[8] == undefined ? 0 : arr[8]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    } else if (_obj.GROUP_NHOM.startsWith("9.")) {
                                        arr[9] = (arr[9] == undefined ? 0 : arr[9]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    } else if (_obj.GROUP_NHOM.startsWith("10.")) {
                                        arr[10] = (arr[10] == undefined ? 0 : arr[10]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    } else if (_obj.GROUP_NHOM.startsWith("11.")) {
                                        arr[11] = (arr[11] == undefined ? 0 : arr[11]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    } else {
                                        arr[12] = (arr[12] == undefined ? 0 : arr[12]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    }
                                }

                            }
                            tongtienbn += Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN);
                        })
                    }
                }
            } else {

                if($scope.thongtinthanhtoan.hienthistg == 1) {
                    $scope.chitietthanhtoan.forEach(function (_obj) {
                        switch (_obj.GHI_CHU) {
                            case 'CONGKHAM':
                                arr[0] = (arr[0] == undefined ? 0 : arr[0]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                            case 'XETNGHIEM':
                                arr[2] = (arr[2] == undefined ? 0 : arr[2]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                            case 'CHANDOANHINHANH':
                                arr[3] = (arr[3] == undefined ? 0 : arr[3]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                            case 'THUTHUATPHAUTHUAT':
                                arr[4] = (arr[4] == undefined ? 0 : arr[4]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                            case 'TOATHUOCBANTAIQUAYTHUOCBV':
                                arr[8] = (arr[8] == undefined ? 0 : arr[8]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                            case 'VANCHUYEN':
                                arr[11] = (arr[11] == undefined ? 0 : arr[11]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                            default:
                                arr[12] = (arr[12] == undefined ? 0 : arr[12]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                break;
                        }
                        tongtienbn += Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN);
                    })
                } else {
                    $scope.chitietthanhtoan.forEach(function (_obj) {
                        if(_obj.GROUP_NHOM == undefined) {
                            switch (_obj.GHI_CHU) {
                                case 'CONGKHAM':
                                case 'CONGKHAMCHUYENDEN':
                                    arr[0] = (arr[0] == undefined ? 0 : arr[0]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                                case 'XETNGHIEM':
                                    arr[3] = (arr[3] == undefined ? 0 : arr[3]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                                case 'CHANDOANHINHANH':
                                    arr[4] = (arr[4] == undefined ? 0 : arr[4]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                                case 'THUTHUATPHAUTHUAT':
                                    arr[6] = (arr[6] == undefined ? 0 : arr[6]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                                case 'TOATHUOCBANTAIQUAYTHUOCBV':
                                    arr[8] = (arr[8] == undefined ? 0 : arr[8]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                                case 'TOADONGY_KHONGBHYT':
                                    arr[8] = (arr[8] == undefined ? 0 : arr[8]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                                case 'VANCHUYEN':
                                    arr[11] = (arr[11] == undefined ? 0 : arr[11]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                                default:
                                    arr[12] = (arr[12] == undefined ? 0 : arr[12]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                    break;
                            }
                        } else {
                            var _tien = Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN);
                            if (_tien > 0) {
                                if (_obj.GROUP_NHOM.startsWith("1.")) {
                                    arr[0] = (arr[0] == undefined ? 0 : arr[0]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                } else if (_obj.GROUP_NHOM.startsWith("2.")) {
                                    arr[2] = (arr[2] == undefined ? 0 : arr[2]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                } else if (_obj.GROUP_NHOM.startsWith("3.")) {
                                    arr[3] = (arr[3] == undefined ? 0 : arr[3]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                } else if (_obj.GROUP_NHOM.startsWith("4.")) {
                                    arr[4] = (arr[4] == undefined ? 0 : arr[4]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                } else if (_obj.GROUP_NHOM.startsWith("5.")) {
                                    arr[5] = (arr[5] == undefined ? 0 : arr[5]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                } else if (_obj.GROUP_NHOM.startsWith("6.")) {
                                    arr[6] = (arr[6] == undefined ? 0 : arr[6]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                } else if (_obj.GROUP_NHOM.startsWith("7.")) {
                                    arr[7] = (arr[7] == undefined ? 0 : arr[7]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                } else if (_obj.GROUP_NHOM.startsWith("8.")) {
                                    arr[8] = (arr[8] == undefined ? 0 : arr[8]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                } else if (_obj.GROUP_NHOM.startsWith("9.")) {
                                    arr[9] = (arr[9] == undefined ? 0 : arr[9]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                } else if (_obj.GROUP_NHOM.startsWith("10.")) {
                                    arr[10] = (arr[10] == undefined ? 0 : arr[10]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                } else if (_obj.GROUP_NHOM.startsWith("11.")) {
                                    arr[11] = (arr[11] == undefined ? 0 : arr[11]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                } else {
                                    arr[12] = (arr[12] == undefined ? 0 : arr[12]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                                }
                            }

                        }

                        tongtienbn += Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN);
                    })
                }

            }

            console.log("nguoibenh",nguoibenh)
            console.log("tongtienbn",tongtienbn)
            baohiemchi = sumData($scope.chitietthanhtoan, 'THANH_TIEN') - $scope.thongtinthanhtoan.sotientt;
        } else if($scope.thongtinbn.LOAIVP == 'NTDICHVU')
        {
            if($scope.thongtinthanhtoan.chitiettiendichvu == 0) {
                nd_all = "Thu tiền dịch vụ";
                tt_all = 0;
                $scope.chitietthanhtoan.forEach(function (_obj) {
                    tt_all += Number(_obj.NGUOI_BENH);
                    tongtienbn += _obj.THANH_TIEN;
                })
                baohiemchi = 0;
                tilebhyt = 0;
            } else {


                $scope.chitietthanhtoan.forEach(function (_obj,_index) {
                    nd_all += escapeXml(_obj.NOIDUNG);
                    tt_all += Number(_obj.NGUOI_BENH);
                    dg_all += Number(_obj.NGUOI_BENH);
                    dvt_all += 'Ngày';
                    sl_all += Number(_obj.SO_LUONG);
                    tongtienbn += _obj.THANH_TIEN;
                    if (_index < $scope.chitietthanhtoan.length - 1) {
                        sl_all +='$$';
                        dg_all += '$$';
                        nd_all += '$$';
                        tt_all += '$$';
                        dvt_all += '$$ ';
                    }
                })
                baohiemchi = 0;
                tilebhyt = 0;
            }
        } else if ($scope.thongtinbn.LOAIVP == 'HDBL') {
            var temp = data.data.noidung.split('$$');
            temp.forEach(function(value,index) {
                var ndsplit = value.split("_!!!_")
                nd_all+=ndsplit[0];
                sl_all += ndsplit[2];
                dg_all += ndsplit[3];
                dvt_all += ndsplit[1];
                tt_all += ndsplit[4];
                if (index < temp.length - 1) {
                    sl_all +='$$';
                    dg_all += '$$';
                    nd_all += '$$';
                    tt_all += '$$';
                    dvt_all += '$$';
                }

            })
            $scope.chitietthanhtoan.forEach(function(_obj) {
                tongtienbn+=  Number(_obj.THANH_TIEN);
            })
            baohiemchi = 0;
            tilebhyt = 0;
            $scope.thongtinbn['load']['TEN_DANTOC'] = 'Kinh';
            tendonvi = $scope.hoadonle.tendonvi;
            masothue = $scope.hoadonle.masothue;
            sodienthoai = $scope.hoadonle.sodienthoai;
            if($scope.hoadonle.dantoc != undefined) {

                $scope.thongtinbn['load']['TEN_DANTOC'] = $scope.hoadonle.dantoc.trim() == ''? 'Kinh': $scope.hoadonle.dantoc;
            }
            paymentmethod =  $scope.thongtinbn.hinhthucthanhtoan;

        } else {
            if ($scope.thongtinthanhtoan.hienthistg == 1) {
                $scope.chitietthanhtoan.forEach(function(_obj) {
                    switch(_obj.UU_TIEN_IN) {
                        case 1:
                            if(_obj.NOIDUNG == 'Công khám') {
                                arr[0] = (arr[0] == undefined? 0: arr[0]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            } else {
                                arr[1] = (arr[1] == undefined? 0: arr[1]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            }

                            break;
                        case 2:
                            arr[2] = (arr[2] == undefined? 0: arr[2]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 3:
                            arr[3] = (arr[3] == undefined? 0: arr[3]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        case 4:
                            arr[4] = (arr[4] == undefined? 0: arr[4]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                        default:
                            arr[5] = (arr[5] == undefined? 0: arr[5]) + (nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN));
                            break;
                    }
                    tongtienbn+=  nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN;
                })
                baohiemchi = nguoibenh == 1?0:sumData($scope.chitietthanhtoan, 'THANH_TIEN') - $scope.thongtinthanhtoan.sotientt;
            } else {
                noitru = ""
                $scope.chitietthanhtoan.forEach(function(_obj) {
                    var _tien = Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN);
                    if (_tien > 0) {
                        if (_obj.GROUP_NHOM.startsWith("1.")) {
                            arr[0] = (arr[0] == undefined ? 0 : arr[0]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                        } else if (_obj.GROUP_NHOM.startsWith("2.")) {
                            arr[2] = (arr[2] == undefined ? 0 : arr[2]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                        } else if (_obj.GROUP_NHOM.startsWith("3.")) {
                            arr[3] = (arr[3] == undefined ? 0 : arr[3]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                        } else if (_obj.GROUP_NHOM.startsWith("4.")) {
                            arr[4] = (arr[4] == undefined ? 0 : arr[4]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                        } else if (_obj.GROUP_NHOM.startsWith("5.")) {
                            arr[5] = (arr[5] == undefined ? 0 : arr[5]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                        } else if (_obj.GROUP_NHOM.startsWith("6.")) {
                            arr[6] = (arr[6] == undefined ? 0 : arr[6]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                        } else if (_obj.GROUP_NHOM.startsWith("7.")) {
                            arr[7] = (arr[7] == undefined ? 0 : arr[7]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                        } else if (_obj.GROUP_NHOM.startsWith("8.")) {
                            arr[8] = (arr[8] == undefined ? 0 : arr[8]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                        } else if (_obj.GROUP_NHOM.startsWith("9.")) {
                            arr[9] = (arr[9] == undefined ? 0 : arr[9]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                        } else if (_obj.GROUP_NHOM.startsWith("10.")) {
                            arr[10] = (arr[10] == undefined ? 0 : arr[10]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                        } else if (_obj.GROUP_NHOM.startsWith("11.")) {
                            arr[11] = (arr[11] == undefined ? 0 : arr[11]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                        } else {
                            arr[12] = (arr[12] == undefined ? 0 : arr[12]) + (Number(nguoibenh == 1 ? _obj.NGUOI_BENH : _obj.THANH_TIEN));
                        }
                    }
                    tongtienbn+=  nguoibenh == 1?_obj.NGUOI_BENH:_obj.THANH_TIEN;
                })
                baohiemchi = nguoibenh == 1?0:sumData($scope.chitietthanhtoan, 'THANH_TIEN') - $scope.thongtinthanhtoan.sotientt;
            }
        }
        if ($scope.thongtinthanhtoan.hoadonchitiet != 1 ||
            ($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  $scope.thongtinbn.LOAIVP != 'THUPHI')
            ||  ($scope.thongtinthanhtoan.hoadonchitiet == 1 &&  $scope.thongtinbn.LOAIVP == 'THUPHI'
                && $scope.thongtinthanhtoan.dvtt != 96977 && $scope.chitietthanhtoan.length > 5)
        ) {
            var temp = 1;
            if($scope.thongtinthanhtoan.hienthistg == 1) {
                for (var i = 0; i < arr.length; i++) {
                    switch(i) {
                        case 0:
                            if(arr[0] != undefined) {
                                nd_all += escapeXml("Công khám" + noitru);
                                sl_all += '1';
                                dg_all += arr[0].toFixed(2);
                                dvt_all += 'Lần';
                                tt_all += arr[0].toFixed(2);
                                temp++;
                            }

                            break;
                        case 1:
                            if (arr[1] != undefined) {
                                tt_all += arr[1].toFixed(2);
                                nd_all += escapeXml("Giường bệnh");
                                sl_all += '1';
                                dg_all += arr[1].toFixed(2);
                                dvt_all += 'Lần';
                                temp++;
                            }

                            break;
                        case 2:
                            if (arr[2] != undefined) {
                                tt_all += arr[2].toFixed(2);
                                nd_all += escapeXml("Xét nghiệm");
                                sl_all += '1';
                                dg_all += arr[2].toFixed(2);
                                dvt_all += 'Lần';
                                temp++;
                            }

                            break;
                        case 3:
                            if (arr[3] != undefined) {
                                tt_all += arr[3].toFixed(2);
                                nd_all += escapeXml("Chẩn đoán hình ảnh và TDCN");
                                sl_all += '1';
                                dg_all += arr[3].toFixed(2);
                                dvt_all += 'Lần';
                                temp++;
                            }

                            break;
                        case 4:
                            if (arr[4] != undefined) {
                                tt_all += arr[4].toFixed(2);
                                nd_all += escapeXml("Thủ thuật phẫu thuật");
                                sl_all += '1';
                                dg_all += arr[4].toFixed(2);
                                dvt_all += 'Lần';
                            }

                            break;
                        case 5:
                            if (arr[5] != undefined) {
                                tt_all += arr[5].toFixed(2);
                                nd_all += escapeXml("Thuốc, vật tư và chi phí khác");
                                sl_all += '1';
                                dg_all += arr[5].toFixed(2);
                                dvt_all += 'Lần';
                            }

                            break;
                    }
                    if (i < arr.length - 1) {
                        if (arr[i] != undefined) {
                            sl_all +='$$';
                            dg_all += '$$';
                            nd_all += '$$';
                            tt_all += '$$';
                            dvt_all += '$$';
                        }

                    }else {
                        if ($scope.thongtinbn.LOAIVP == 'NOITRU') {
                            nd_all+=';Mẫu số 02/BV số bệnh án '+ $scope.thongtinbn.SOBENHAN
                            sl_all += ';0';
                            dg_all += ';0';
                            dvt_all += '$$ ';
                        }
                        if ($scope.thongtinbn.LOAIVP == 'BANT') {
                            nd_all+=';Mã khách hàng '+ $scope.thongtinbn.MA_BENH_NHAN
                            sl_all += ';0';
                            dg_all += ';0';
                            dvt_all += '$$ ';
                        }
                    }
                }
            } else {
                for (var i = 0; i < arr.length; i++) {
                    switch(i) {
                        case 0:
                            if(arr[i] != undefined) {
                                nd_all += escapeXml("Khám bệnh" + noitru);
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                                tt_all += arr[0].toFixed(2);
                                temp++;
                            }

                            break;
                        case 2:
                            if (arr[i] != undefined) {
                                tt_all += arr[2].toFixed(2);
                                nd_all += escapeXml("Ngày giường");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                                temp++;
                            }

                            break;
                        case 3:
                            if (arr[i] != undefined) {
                                tt_all += arr[3].toFixed(2);
                                nd_all += escapeXml("Xét nghiệm");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                                temp++;
                            }

                            break;
                        case 4:
                            if (arr[i] != undefined) {
                                tt_all += arr[4].toFixed(2);
                                nd_all += escapeXml("Chẩn đoán hình ảnh");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                                temp++;
                            }

                            break;
                        case 5:
                            if (arr[i] != undefined) {
                                tt_all += arr[i].toFixed(2);
                                nd_all += escapeXml("Thăm dò chức năng");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                            }

                            break;
                        case 6:
                            if(arr[i] != undefined) {
                                nd_all += escapeXml("Thủ thuật, phẫu thuật");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                                tt_all += arr[i].toFixed(2);
                                temp++;
                            }

                            break;
                        case 7:
                            if (arr[i] != undefined) {
                                tt_all += arr[i].toFixed(2);
                                nd_all += escapeXml("Máu, chế phẩm máu, vận chuyển máu");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                                temp++;
                            }

                            break;
                        case 8:
                            if (arr[i] != undefined) {
                                tt_all += arr[i].toFixed(2);
                                nd_all += escapeXml("Thuốc, dịch truyền");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                                temp++;
                            }

                            break;
                        case 9:
                            if (arr[i] != undefined) {
                                tt_all += arr[i].toFixed(2);
                                nd_all += escapeXml("Vật tư y tế");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                                temp++;
                            }

                            break;
                        case 10:
                            if (arr[i] != undefined) {
                                tt_all += arr[i].toFixed(2);
                                nd_all += escapeXml("Gói vật tư y tế");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                            }

                            break;
                        case 11:
                            if(arr[i] != undefined) {
                                nd_all += escapeXml("Vận chuyển người bệnh" );
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                                tt_all += arr[i].toFixed(2);
                                temp++;
                            }

                            break;
                        case 12:
                            if (arr[i] != undefined) {
                                tt_all += arr[i].toFixed(2);
                                nd_all += escapeXml("Dịch vụ khác");
                                sl_all += '0';
                                dg_all += '0';
                                dvt_all += ' ';
                                temp++;
                            }

                            break;

                    }
                    if (i < arr.length - 1) {
                        if (arr[i] != undefined) {
                            sl_all +='$$';
                            dg_all += '$$';
                            nd_all += '$$';
                            tt_all += '$$';
                            dvt_all += '$$';
                        }

                    }
                }
            }
        }
        if ( ['NOITRU','BANT'].indexOf($scope.thongtinbn.LOAIVP) == 0 && $scope.chitietthanhtoan.length < 6 && $scope.thongtinthanhtoan.hienthichitietnoitru ==1) {
            nd_all = '';
            sl_all = '';
            dg_all = '';
            tt_all = '';
            dvt_all = '';
            tongtienbn = 0;
            $scope.chitietthanhtoan.forEach(function(_obj,_index) {
                nd_all += escapeXml(_obj.NOIDUNG);
                sl_all += '0';
                dg_all += '0';
                dvt_all+= ' '
                tt_all += _obj.THANH_TIEN.toFixed(2);
                tongtienbn+=  nguoibenh == 1?Number(_obj.NGUOI_BENH):Number(_obj.THANH_TIEN);
                if (_index < $scope.chitietthanhtoan.length - 1) {
                    sl_all +='$$';
                    dg_all += '$$';
                    nd_all += '$$';
                    tt_all += '$$';
                    dvt_all +='$$';
                }
            })
        }
        var d = new Date();
        var n = d.getTime();
        var arisingdate = parseDateToString($scope.thongtinthanhtoan.ngaythu);
        var key =  $scope.thongtinthanhtoan.dvtt+"-"+n + "-" + arisingdate.replace("-","") + "-" + $scope.thongtinbn.MA_BENH_NHAN + "-" + $scope.thongtinbn.SOVAOVIEN;
        if (keyold != undefined && keyold != '-1') {
            key = keyold;
        }
        var url = "cmu_taohoadon_invoice";
        data.data['KEY_HD'] = key;
        var dt =  $scope.thongtinbn['load'].TEN_DANTOC == undefined? 'Kinh': $scope.thongtinbn['load'].TEN_DANTOC;
        if($scope.thongtinthanhtoan.hoadonchitiet == 1 && $scope.thongtinthanhtoan.dvtt == 96977) {
            if($scope.thongtinbn.LOAIVP == 'HDBL') {
                dt = ' ';
            } else {
                if($scope.thongtinbn.LOAIVP == 'NOITRU') {
                    var d = new Date();
                    var n = d.getFullYear();
                    dt =n - $scope.thongtinbn.NAMSINH
                } else {
                    dt = $scope.thongtinbn['load'].TUOI
                }

            }

        }
        if(tongtienbn.toFixed() == 0 || $scope.thongtinthanhtoan.sotientt.toFixed() == 0) {
            alert("Không được phát hành hóa đơn số tiền bằng không, Nếu có lỗi xảy ra vui lòng phát hành lại.")
            return false;
        }
        if ($scope.thongtinthanhtoan.tmiengiam == true) {
            tongtienbn = Number($scope.thongtinthanhtoan.sotienbntra);
        }

        if (vat != '-1') {
            var temp = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra:$scope.thongtinthanhtoan.sotientt;
            vatAmount = Number(temp)*Number(vat)/100;
            vatAmount  =vatAmount.toFixed();
        }
        if ($scope.thongtinthanhtoan.hienthistg== 1) {
            if($scope.thongtinbn.LOAIVP == 'BHYT') {
                sl_all ='1';
                dg_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                nd_all = ' Thu '+ (100 - Number($scope.thongtinbn.load.TYLEBAOHIEM)) + "% mẫu số 01/BV mã khách hàng "+$scope.thongtinbn.MA_BENH_NHAN;
                tt_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                dvt_all = 'Lần';
            }
            if ($scope.thongtinbn.LOAIVP == 'BANT' && $scope.thongtinbn.load.TYLEBAOHIEM > 0) {
                sl_all ='1';
                dg_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                nd_all = ' Thu '+ (100 - Number($scope.thongtinbn.load.TYLEBAOHIEM)) + "% mẫu số 01/BV mã khách hàng "+$scope.thongtinbn.MA_BENH_NHAN;
                tt_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                dvt_all = 'Lần';
            }
            if($scope.thongtinbn.LOAIVP == 'NOITRU' && $scope.thongtinbn.load.TYLEBAOHIEM > 0) {
                sl_all ='1';
                dg_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                nd_all = ' Thu '+ (100 - Number($scope.thongtinbn.load.TYLEBAOHIEM)) + "% mẫu số 02/BV số bệnh án "+$scope.thongtinbn.load.SOBENHAN;
                tt_all = $scope.thongtinthanhtoan.tmiengiam == true? $scope.thongtinthanhtoan.sotienbntra.toFixed():$scope.thongtinthanhtoan.sotientt.toFixed();
                dvt_all = 'Lần';
            }
            if ($scope.thongtinthanhtoan.tmiengiam == true && $scope.thongtinbn.load.TYLEBAOHIEM == 0) {
                sl_all ='1;0';
                dg_all =  Number($scope.thongtinthanhtoan.sotienbntra).toFixed();
                dg_all += ';0'
                nd_all = "Thuốc và chi phí khác;Mẫu số 02/BV số bệnh án "+$scope.thongtinbn.load.SOBENHAN;
                tt_all = $scope.thongtinthanhtoan.sotienbntra.toFixed()
                tt_all += ';0'
                dvt_all = 'Lần; ';
                tongtienbn =  Number($scope.thongtinthanhtoan.sotienbntra).toFixed()
            }
        }
        var diachi = ' ';
        var gioitinh =' ';
        var sothebaohiem = ' ';
        var tuoi =  ' ';
        if($scope.thongtinbn.load !== undefined)  {
            diachi = $scope.thongtinbn.load.DIACHI == undefined? ' ': $scope.thongtinbn.load.DIACHI;
            gioitinh = $scope.thongtinbn.GIOI_TINH == 0 || $scope.thongtinbn.load.GIOITINH == 0? "Nữ": "Nam";
            sothebaohiem = $scope.thongtinbn.load.SOBAOHIEMYTE == undefined? ' ': $scope.thongtinbn.load.SOBAOHIEMYTE
            tuoi = $scope.thongtinbn.load.TUOI == undefined? ' ': $scope.thongtinbn.load.TUOI
        }
        console.log("$scope.thongtinthanhtoan.sotienbntra", $scope.thongtinthanhtoan.sotienbntra)
        return $.post(url, {
            id: data.data.ID,
            key: key,
            account: $scope.thongtinthanhtoan.account,
            acpass: $scope.thongtinthanhtoan.acpass,
            username: $scope.thongtinthanhtoan.username,
            password: $scope.thongtinthanhtoan.password,
            publishservice: $scope.thongtinthanhtoan.publishservice,
            pattern: $scope.thongtinthanhtoan.pattern,
            serial: $scope.thongtinthanhtoan.serial,

            ma_bn: escapeXml($scope.thongtinbn.MA_BENH_NHAN + ''),
            ten_bn: escapeXml($scope.thongtinbn.TEN_BENH_NHAN+''),
            diachi: escapeXml(diachi+''),
            paymethod: paymentmethod == undefined? 'Tiền mặt':paymentmethod,

            nd_all: nd_all,
            sl_all: sl_all,
            dg_all: dg_all,
            tt_all: tt_all,
            dvt_all: dvt_all,

            total: $scope.thongtinthanhtoan.tmiengiam == true? Number($scope.thongtinthanhtoan.sotienbntra).toFixed():$scope.thongtinthanhtoan.sotientt.toFixed(),
            arisingdate: arisingdate,
            dantoc: dt,
            tuoi: tuoi,
            gioitinh: gioitinh,
            tenKhoa: $scope.thongtinbn.TEN_PHONGBAN,
            thebhyt: sothebaohiem,
            tilebhyt: tilebhyt == undefined? '0':tilebhyt,
            baohiemchi: baohiemchi.toFixed(),
            tongtienbn: tongtienbn.toFixed(),
            tendonvi: tendonvi,
            masothue: masothue,
            sodienthoai: sodienthoai,
            vat: vat,
            vatAmount: vatAmount

        });

    }
    this.laythongtincqt = function($scope, data) {
        return $.get("cmu_getlist?url="+convertArray(
                [
                    $scope.thongtinthanhtoan.dvtt,
                    data.data.MA_LAN_TT,
                    data.data.KEY_HD,
                    data.data.SOVAOVIEN,
                    data.data.MA_QUYEN_BIENLAI,
                    'CMU_GET_THONGHDSAISOT'
                ])
            );
    };

    this.guisaisotcqt = function($scope,data, thongtinsaisot, mcqt) {
        return $.post('sendXML', {
            url: $scope.thongtinthanhtoan.publishservice,
            xml: '<tem:xml><![CDATA[<DLTBao><TNNT>'+
                thongtinsaisot[0].TNNT
                +'</TNNT><TCQT>'+
                thongtinsaisot[0].TCQT
                +'</TCQT><NTBao>'+
                thongtinsaisot[0].NTBAO
                +'</NTBao><DDanh>'+
                thongtinsaisot[0].DDANH
                +'</DDanh><Loai>1</Loai><So></So><NTBCCQT></NTBCCQT><DSHDon><HDon><STT>1</STT><MCQTCap>'+(mcqt == undefined? '': mcqt)+'</MCQTCap><KHMSHDon>'+
                thongtinsaisot[0].PATTERN
                +'</KHMSHDon><KHHDon>'+
                thongtinsaisot[0].SERIAL
                +'</KHHDon><SHDon>'+
                thongtinsaisot[0].SOHOADON
                +'</SHDon><Ngay>'+
                thongtinsaisot[0].NGAYPHATHANH
                +'</Ngay><LADHDDT>1</LADHDDT><TCTBao>1</TCTBao><LDo>'+
                thongtinsaisot[0].LYDO
                +'</LDo><Fkey>'+
                data.data.KEY_HD
                +'</Fkey></HDon></DSHDon></DLTBao>]]></tem:xml>',
            function: 'SendInvNoticeErrors',
            isNeedAccount: 1,
        })
    }
    this.laymcqt = function(publishservice, key_hd, pattern) {
        return $.post('sendXML', {
            url: publishservice,
            xml: '<tem:pattern>'+pattern+'</tem:pattern><tem:fkeys>'+key_hd+'</tem:fkeys>',
            function: 'GetMCCQThueByFkeysNoXMLSign',
            isNeedAccount: 1,
        })
    }
});

