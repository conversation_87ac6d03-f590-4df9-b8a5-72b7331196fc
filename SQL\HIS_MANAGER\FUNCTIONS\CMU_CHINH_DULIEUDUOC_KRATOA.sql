CREATE OR REPLACE
FUNCTION CMU_CHINH_DULIEUDUOC_KRATOA(
p_dvtt varchar2,
p_sl_ton number,
p_ngaysau varchar2,
p_sonhapkhochitiet varchar2,
p_makhovattu varchar2
) RETURN  VARCHAR2
IS
v_lech number;
BEGIN

UPDATE
    DC_TB_XNT_BIENDONG
set sl_ton = p_sl_ton - SL_XUAT_NV3_NGOAI - SL_XUAT_NV3_NOI -  + sl_nhap + sl_chuyenden - SL_CHUYENDI
WHERE  DVTT = p_dvtt
  AND MAKHOVATTU = p_makhovattu
  AND NGAYBIENDONG = to_date(p_ngaysau, 'dd/mm/yyyy')
  AND SONHAPKHOCHITIET = p_sonhapkhochitiet;


RETURN 0;
END;
