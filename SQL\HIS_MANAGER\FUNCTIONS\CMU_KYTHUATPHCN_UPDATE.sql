create or replace FUNCTION                 HIS_MANAGER.CMU_KYTHUATPHCN_UPDATE(
    p_id                        IN NUMBER,
    p_dvtt                      IN VARCHAR2,
    p_thoigian           IN VARCHAR2,
    p_thoigianphut   IN VARCHAR2,
    p_die<PERSON><PERSON><PERSON><PERSON>h           IN VARCHAR2,
    p_tendichvu           IN VARCHAR2,
    p_nguoithuchien           IN NUMBER,
    p_bacsichidinh           IN NUMBER,
    p_stt_dieutri         IN VARCHAR2
)
RETURN NUMBER IS
    v_thoigian   DATE := TO_DATE(p_thoigian, 'dd/mm/yyyy hh24:mi');
BEGIN
UPDATE CMU_KYTHUATPHCN
SET
    THOIGIAN = v_thoigian,
    THOIGIANPHUT = p_thoigianphut,
    DIENBIENBENH = p_dienbienbenh,
    TENDICHVU = p_tendichvu,
    NGUOITHUCHIEN = p_nguoithuchien,
    BACSICHIDINH = p_bacsichidinh,
    STT_DIEUTRI = p_stt_dieutri
WHERE ID = p_id AND DVTT = p_dvtt;
RETURN 2;
END;