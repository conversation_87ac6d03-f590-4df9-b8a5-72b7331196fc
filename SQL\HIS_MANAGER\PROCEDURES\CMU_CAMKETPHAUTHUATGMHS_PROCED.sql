create or replace PROCEDURE      "HIS_MANAGER".CMU_CAMKETPHAUTHUATGMHS_PROCED(
    p_dvtt              IN     VARCHAR2,
    p_id               IN     NUMBER,
    p_sovaovien      IN     NUMBER,
    cur OUT SYS_REFCURSOR
) IS
v_thamso960622   varchar2(255) := his_fw.cmu_tsdv(p_dvtt, 960622, 0);
v_thamso960616   NUMBER(10) := cmu_tsdv(p_dvtt, 960616, 0);
BEGIN
open cur for
select
    phieu.*,
    TO_CHAR(phieu.NGAY_TAO_PHIEU, ' "<PERSON><PERSON><PERSON>" DD "tháng" MM "năm" YYYY') NGAY_TAO_PHIEU_TEXT,
    nv.TEN_NHANVIEN NGUOITHUCHIEN,
    nv1.TEN_NHANVIEN_CD TEN_BAC_SI_PT,
    nv1.TEN_CHUCDANH TEN_CDBAC_SI_PT,
    pb.TEN_PHON<PERSON>AN TEN_PBBAC_SI_PT,
    v_thamso960622 as THA<PERSON><PERSON>,
    v_thamso960616 as <PERSON><PERSON><PERSON><PERSON><PERSON>,
    nv2.TEN_NHANVIEN_CD TEN_BAC_SI_GMHS,
    nv2.TEN_CHUCDANH TEN_CDBAC_SI_GMHS
FROM CMU_CAMKETPHAUTHUATGMHS phieu
         LEFT JOIN HIS_FW.DM_NHANVIEN nv ON phieu.NGUOI_TAO = nv.MA_NHANVIEN
         LEFT JOIN HIS_FW.DM_PHONGBAN pb ON phieu.MAKHOABSPT = pb.MA_PHONGBAN
         LEFT JOIN HIS_FW.DM_NHANVIEN_CD nv1 ON phieu.BACSIPT = nv1.MA_NHANVIEN
         LEFT JOIN HIS_FW.DM_NHANVIEN_CD nv2 ON phieu.BACSIGMHS = nv2.MA_NHANVIEN

WHERE phieu.ID = p_id and phieu.SOVAOVIEN = p_sovaovien
order by phieu.NGAY_TAO_PHIEU;
END;
