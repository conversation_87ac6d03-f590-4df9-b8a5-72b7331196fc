
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%> <%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%> <%@page contentType="text/html" pageEncoding="UTF-8"  %> <html xmlns="http://www.w3.org/1999/xhtml"> <head> <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/> <meta name="google-site-verification" content="u6uNEfD4cb3gidezi4r_6aI8Wb1E07-ufBeCQpvmlqQ"/> <title>Hệ thống chăm sóc sức khỏe</title> <link rel="icon" href="<c:url value="/resources/images/favicon.ico"/>" type="image/x-icon"/> <link rel="shortcut icon" href="<c:url value="/resources/images/favicon.ico"/>" type="image/x-icon"/> <link href="<c:url value="/resources/css/divheader.css"/>" rel="stylesheet"/> <link href="<c:url value="/resources/css/style_new.css"/>" rel="stylesheet"/> <link rel="stylesheet" href="<c:url value="/resources/css/jquery-ui-redmond.1.9.1.css"/>" /> <script src="<c:url value="/resources/js/jquery.min.1.8.3.js"/>"></script> <script src="<c:url value="/resources/js/jquery-ui.1.9.1.js"/>"></script> <link rel="stylesheet" href="<c:url value="/resources/multiselect_dropdown/jquery.multiselect.css"/>"/> <script src="<c:url value="/resources/multiselect_dropdown/jquery.multiselect.js"/>"></script> <link href="<c:url value="/resources/jqgrid/css/ui.jqgrid.css"/>" rel="stylesheet"/> <script src="<c:url value="/resources/jqgrid/js/i18n/grid.locale-en.js"/>"></script> <script src="<c:url value="/resources/jqgrid/js/jquery.jqGrid.src.js"/>"></script> <script src="<c:url value="/resources/js/common_function.js"/>"></script> <script src="<c:url value="/resources/js/jquery.inputmask.bundle.min.js"/>"></script> <link href="<c:url value="/resources/dialog/jquery.alerts.1.css"/>" rel="stylesheet"/> <link href="<c:url value="/resources/dialog/jBox.css"/>" rel="stylesheet"/> <script src="<c:url value="/resources/dialog/jBox.js"/>"></script> <script src="<c:url value="/resources/dialog/jquery.alerts.js"/>"></script> <script src="<c:url value="/resources/js/jquery.tabify.js"/>"></script>
    <script>
    var checkRequired = 0;
    var madonvi_macdinh = "${Sess_DVTT}";

    console.log(madonvi_macdinh);
    $(function() {
        $(":input").inputmask();
        $("#madonvi").val(madonvi_macdinh);
        $("#madonvi_them").val(madonvi_macdinh);
        var thongtin = new jBox('Modal', {
            title: "Thông tin gộp xã phường",
            overlay : true,
            modal : true,
            content : $('#thongtin'),
            position : {
                my : "center top",
                at : "center top",
                of : window
            },
            closeOnClick: false
        });

        function  urltimKiem(){
            var madonvi = $("#madonvi").val().trim();
            var madonvi_moi = $("#madonvi_moi").val().trim();

            var url = 'api-gop-xa-phuong-list?madonvi=' +  encodeURIComponent(madonvi)
                + "&madonvi_moi=" + encodeURIComponent(madonvi_moi);
            return url;
        }


        $("#timkiem").click(function() {
            var url = urltimKiem();
            $("#list").jqGrid('setGridParam', {url : url, page : 1, datatype : "json"}).trigger('reloadGrid');
        });
        var url = urltimKiem();

        $("#list").jqGrid({

            url: url,
            datatype: "json",
            height: 440,
            width: 988,
            colNames: ["ID", "Mã vị cũ", "Tên đơn vị cũ","Mã đơn vị mới", "Ngày hiệu lực từ", "Ngày hiệu lực đến"],
            colModel: [
                {name: 'ID', index: 'ID', width: 25,  hidden:true },
                {name: 'MADONVI', index: 'MADONVI', width: 25, align: "center"},
                {name: 'TEN_DONVI', index: 'TEN_DONVI', width: 50},
                {name: 'MADONVI_MOI', index: 'MADONVI_MOI', width: 25, align: "center"},
                {name: 'NGAY_HIEU_LUC', index: 'NGAY_HIEU_LUC', width: 30, align: "center"},
                {name: 'NGAY_HET_HIEU_LUC', index: 'NGAY_HET_HIEU_LUC', width: 30, align: "center"},
            ],
            rowNum : 10,
            rowList : [ 10, 20, 50 ],
            rownumbers : true,
            pager : '#pager',
            gridview : true,
            autoencode:true,
            viewrecords : true,
            loadonce : false,
            jsonReader : {
                repeatitems : false,
                id : "id",
                root : "rows",
                page : "page",
                total : "total",
                records : "records"
            }
            ,
            onSelectRow : function(id) { // nap data vao modal box để khi ấn vào nút sửa hiện
                if (id) {
                    var ret = $("#list").jqGrid('getRowData', id);

                    $("#id").val(ret.ID);
                    $("#madonvi_them").val(ret.MADONVI);
                    $("#madonvi_moi_them").val(ret.MADONVI_MOI);
                    $("#ngayhieuluctu_them").val(ret.NGAY_HIEU_LUC);
                    $("#ngayhieulucden_them").val(ret.NGAY_HET_HIEU_LUC);
                }
            },
            ondblClickRow : function(id) { // ấn show model box khi update
                var ret = $("#list").jqGrid('getRowData', id);

                $(":input").inputmask();

                $("#id").val(ret.ID);
                $("#madonvi_them").val(ret.MADONVI);
                $("#madonvi_moi_them").val(ret.MADONVI_MOI);
                $("#ngayhieuluctu_them").val(ret.NGAY_HIEU_LUC);
                $("#ngayhieulucden_them").val(ret.NGAY_HET_HIEU_LUC);

                $("#xoa").addClass( "button_shadow");
                $("#xoa").prop('hidden', false);
                thongtin.open();
            },
            onCellSelect : function(rowid, iCol, cellcontent, e) {
                if (rowid) {
                    $('#list').jqGrid('setSelection', rowid);
                }
            }
        });

        $("#huy").click(function() {
            thongtin.close();
        });

        $("#xoa").click(function() {
            jConfirm('Bạn muốn xóa thông tin này?', 'Thông báo', function(r) {
                if (r.toString() == "true") {
                    var params = $("#id").val();
                    var url = "api-gop-xa-phuong-del";

                    console.log(params);
                    $.post(url, {id : params}).done(function(data) {
                        if (data = 0) {
                            jAlert("Xóa thông tin không thành công!", 'Thông báo');
                        } else {
                            jAlert("Xóa thông tin thành công!", 'Thông báo');
                            thongtin.close();
                            $("#list").jqGrid('setGridParam', {datatype : 'json'}).trigger('reloadGrid');
                        }
                    });
                }
            });
        });

        $("#themmoi").click(function() {
            $("#xoa").removeClass( "button_shadow");
            $("#xoa").prop("hidden", true);
            $("#id").val("");
            $("#madonvi_them").val(madonvi_macdinh);
            $("#madonvi_moi_them").val("");

            $("#ngayhieuluctu_them").val("");
            $("#ngayhieulucden_them").val("");
            thongtin.open();
        });

        $("#capnhat").click(function() {
            if ($("#id").val() == "" || $("#id").val() == null) {
                jAlert("Chưa chọn thông tin cần sửa", "Thông báo");
            } else {
                $("#xoa").addClass( "button_shadow");
                $("#xoa").prop('hidden', false);
                thongtin.open();
            }
        });

        $("#luu").click(function() { // ham luu
            checkdulieu();
            if (checkRequired == 0) {
                let ngayhethieuluc = ''; let ngayhieuluc = '';
                let dateStr = $("#ngayhieuluctu_them").val();
                if (dateStr) {
                    let parts = dateStr.split("/");
                    ngayhieuluc = parts[2] + parts[1] + parts[0];
                }

                dateStr = $("#ngayhieulucden_them").val();
                if (dateStr) {
                    let parts = dateStr.split("/");
                    let ngayhethieuluc = parts[2] + parts[1] + parts[0];
                }
                var Object = {
                    ID: $("#id").val(),
                    MADONVI: $("#madonvi_them").val(),
                    MADONVI_MOI: $("#madonvi_moi_them").val(),
                    NGAY_HIEU_LUC: ngayhieuluc,
                    NGAY_HET_HIEU_LUC: ngayhethieuluc,
                };
                var url = "api-gop-xa-phuong-save";

                console.log(Object);
                $.post(url, {
                    data: JSON.stringify(Object)
                }).done(function (data) {
                    if (data == 1) {
                        jAlert("Cập nhật thông tin thành công !", "Thông báo");
                        $("#list").jqGrid('setGridParam', {datatype : 'json'}).trigger('reloadGrid');
                    }
                    else{
                        if (data == 2 ) {
                            jAlert("Ngày hiệu lực nhập trùng với bản ghi đã nhập trước đó !", "Thông báo");
                            $("#list").jqGrid('setGridParam', {datatype : 'json'}).trigger('reloadGrid');
                        }
                        else {
                            jAlert("Cập nhật thông tin không thành công!", "Thông báo");
                            return false;
                        }
                    }
                })
                thongtin.close();
            }
        });

        function checkdulieu() {
            checkRequired = 0;
            if ($("#madonvi_them").val().trim() == "") {
                checkRequired = 1;
                jAlert("❌ Chưa chọn đơn vị cũ !!!", 'Thông báo');
                return ;
            }
            if ($("#madonvi_moi_them").val().trim() == "") {
                checkRequired = 1;
                jAlert("❌ Chưa nhập mã đơn vị mới !!!", 'Thông báo');
                return ;
            }

            if ($("#ngayhieuluctu_them").val().trim() == "") {
                checkRequired = 1;
                jAlert("❌ Chưa nhập ngày hiệu lực từ !!!", 'Thông báo');
                return ;
            }

            const tu = document.getElementById("ngayhieuluctu_them").value;
            const den = document.getElementById("ngayhieulucden_them").value;

            if (!tu || !den) return true; // cho qua nếu chưa nhập đủ

            const [day1, month1, year1] = tu.split("/");
            const [day2, month2, year2] = den.split("/");

            const dateFrom = new Date(year1, month1 - 1, day1);
            const dateTo = new Date(year2, month2 - 1, day2);

            if (dateFrom > dateTo) {
                alert("❌ Thời gian hiệu lực 'Từ' phải nhỏ hơn hoặc bằng 'Đến'");
                return false;
            }

            return true;
        }
    });

</script>
    <style>
        .width100per{
            width:100%;
        }
        #bang1 tr td{
            padding: 2px 5px 2px 5px;
        }
    </style>
</head>
<body>
<div id="panel_all">
    <%@include file="../../../../resources/Theme/include_pages/menu.jsp"%>
    <div id="panelwrap">
        <div class="center_content">
            <div class="panel_with_title">
                <div class="panel_title">Thông tin</div>
                <div class="panel_body" style="display: block">
                    <form method="get" name="donvi" id="donvi">
                        <table width="60%" border="0" id="bang1" align="center">
                            <tr>
                                <td>Đơn vị</td>
                                <td>
                                    <select name="madonvi" class="width100per" id="madonvi" style="width: 100%">
                                        <option value="-1">Chọn đươn vị</option>
                                        <c:forEach var="i" items="${donvi}">
                                        <option value="${i.ma_donvi}"
                                                <c:if test="${i.ma_donvi == madonvi_macdinh}">selected="selected"</c:if>>
                                                ${i.ten_donvi}
                                        </option>
                                        </c:forEach>
                                </td>
                                <td>Mã đơn vị mới</td>
                                <td><input name="madonvi_moi" type="text" class="width100per" id="madonvi_moi"></td>
                            </tr>
                            <tr>
                                <td colspan="6" align="center"> <input type="button" name="timkiem" id="timkiem" value="Tìm kiếm" class="button_shadow"/> <input type="button" name="themmoi" id="themmoi" value="Thêm" class="button_shadow"/> <input type="button" name="capnhat" id="capnhat" value="Sửa" class="button_shadow"/>   </td>
                            </tr>
                        </table>
                    </form>
                </div>
            </div>
            <div>
                <div class="panel_title">Danh sách</div>
                <div class="panel_body">
                <table id="list" style="font-size: 12px"></table>
                <div id="pager"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="thongtin" title="Thông tin chứng minh thư" style="display:none;">
    <table width="900px" border="0" align="center" style="font-size: 12px">
        <tr>
            <td>
                <input type="text" style="display: none"   id ="id" name = "id">

            </td>
        </tr>
        <tr style="display: flex;flex-direction: column">
        <tr>
            <td>Đơn vị</td>
            <td><select name="madonvi_them" class="width100per" id="madonvi_them" style="width: 100%">
                <option value="-1">Chọn đươn vị</option>
                <c:forEach var="i" items="${donvi}">
                <option value="${i.ma_donvi}"
                        <c:if test="${i.ma_donvi == madonvi_macdinh}">selected="selected"</c:if>>
                        ${i.ten_donvi}
                </option>
                </c:forEach>
            </td>
            <td>Mã đơn vị mới</td>
            <td>
                <input name="madonvi_moi_them" class="width100per" id="madonvi_moi_them"/>
            </td>
        </tr>
        <tr>
            <td>Thời gian hiệu lực từ</td>
            <td><input name="ngayhieuluctu_them" class="width100per" id="ngayhieuluctu_them" data-inputmask="'alias': 'date'"/></td>
            <td>Thời gian hiệu lực đến</td>
            <td><input name="ngayhieulucden_them" class="width100per" id="ngayhieulucden_them" data-inputmask="'alias': 'date'"/></td>
        </tr>
        </tr>

        <tr >
            <td colspan="4" style="text-align:center">
                <input type="button" class="button_shadow" name="luu" id="luu" value="Lưu"/>
                <input type="button" class="button_shadow" name="xoa" id="xoa" value="Xóa"/>
                <input type="button" class="button_shadow" name="huy" id="huy" value="Thoát"/> </td>
        </tr>
    </table>
</div>
</body>
</html>