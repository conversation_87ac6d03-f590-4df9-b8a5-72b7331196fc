<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rp_phieuxetnghiemhuyethoc" pageWidth="595" pageHeight="842" columnWidth="575" leftMargin="0" rightMargin="20" topMargin="0" bottomMargin="0" isIgnorePagination="true" uuid="86cebd97-b77b-4bdf-93cb-4fcd0dcdb35f">
	<property name="ireport.zoom" value="1.4641000000000006"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="madonvi" class="java.lang.String"/>
	<parameter name="makhambenh" class="java.lang.String"/>
	<parameter name="tenbenhvien" class="java.lang.String"/>
	<parameter name="hoten" class="java.lang.String"/>
	<parameter name="tuoi" class="java.lang.String"/>
	<parameter name="gioitinh" class="java.lang.String"/>
	<parameter name="diachi_bn" class="java.lang.String"/>
	<parameter name="chandoan" class="java.lang.String"/>
	<parameter name="ngayxetnghiem" class="java.lang.String"/>
	<parameter name="bsdieutri" class="java.lang.String"/>
	<parameter name="sophieuthanhtoan" class="java.lang.String"/>
	<parameter name="noitru" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_benhan" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_dotdieutri" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_dieutri" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="sophieuxn" class="java.lang.String"/>
	<parameter name="capcuu" class="java.lang.String"/>
	<parameter name="thuong" class="java.lang.String"/>
	<parameter name="sothebaohiem" class="java.lang.String"/>
	<parameter name="ngaydieutri" class="java.lang.String"/>
	<parameter name="tensoyte" class="java.lang.String"/>
	<parameter name="khoa" class="java.lang.String"/>
	<parameter name="giuong" class="java.lang.String"/>
	<parameter name="buong" class="java.lang.String"/>
	<parameter name="hthi" class="java.lang.String"/>
	<parameter name="mabenhnhan" class="java.lang.String"/>
	<parameter name="loaixetnghiem" class="java.lang.String"/>
	<parameter name="bsphutrach" class="java.lang.String"/>
	<parameter name="maxetnghiem" class="java.lang.String"/>
	<parameter name="soyte" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call CMU_INPHIEUXETNGHIEM_V2($P{madonvi},$P{sophieuxn},$P{noitru},$P{makhambenh},$P{stt_benhan},$P{stt_dotdieutri},$P{stt_dieutri},'VS',$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="dvtt" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="sophieu" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="noitru" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="mota_loai_xetnghiem" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ten_xetnghiem" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="dvnghiepvu_xetnghiem" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="chisobinhthuong" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ket_qua" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ket_luan_tong" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="nguoi_lay_mau" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ngay_lay_mau" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="sapxep" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="nhom_xetnghiem" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="TT" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="HH_ABO" class="java.lang.String"/>
	<field name="HH_Rh" class="java.lang.String"/>
	<field name="nguoi_chi_dinh" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="tuoi" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="kq_ABO" class="java.lang.String"/>
	<field name="kq_Rh" class="java.lang.String"/>
	<field name="kq_mauchay" class="java.lang.String"/>
	<field name="kq_maudong" class="java.lang.String"/>
	<field name="NGAYGIOLAYMAU" class="java.lang.String"/>
	<field name="NGAYCHIDINH" class="java.lang.String"/>
	<field name="giolaymau" class="java.lang.String"/>
	<field name="BINHTHUONG" class="java.lang.String"/>
	<field name="kq_ABO_binhthuong" class="java.lang.String"/>
	<field name="kq_Rh_binhthuong" class="java.lang.String"/>
	<field name="kq_mauchay_binhthuong" class="java.lang.String"/>
	<field name="kq_maudong_binhthuong" class="java.lang.String"/>
	<field name="NGAYTRAKETQUA" class="java.lang.String"/>
	<field name="thamsocmu" class="java.lang.String"/>
	<field name="NGUOI_THUC_HIEN" class="java.lang.String"/>
	<field name="nguoilaymau_cmu" class="java.lang.String"/>
	<field name="nguoigiaomau_cmu" class="java.lang.String"/>
	<field name="benhpham_cmu" class="java.lang.String"/>
	<field name="solanin" class="java.lang.Integer"/>
	<field name="TEN_MAY_XN" class="java.lang.String"/>
	<field name="CANHBAO_LAMSANG" class="java.lang.String"/>
	<field name="ngthuchien_in" class="java.lang.String"/>
	<field name="ngaylaynhanmau" class="java.lang.String"/>
	<field name="ANCHUKY" class="java.lang.String"/>
	<variable name="count page" class="java.lang.Integer" resetType="Page" calculation="DistinctCount">
		<variableExpression><![CDATA[$V{PAGE_NUMBER}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="195" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="392" y="83" width="19" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="5dde74bc-76b0-4ee3-b8be-e7b594683dc1"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("")? $P{sothebaohiem}.substring(02,03):$P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="46" y="103" width="227" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="87199ecf-cccf-457b-b374-9df9c5218ca2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{khoa}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="439" y="83" width="27" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="36022da6-ff71-454a-bd8e-e16667bf7adc"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("")? $P{sothebaohiem}.substring(05,8):$P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="472" y="63" width="63" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="8f557dd1-fa53-407a-beab-c087f77361c7"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{gioitinh}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="385" y="63" width="59" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="49408c31-7944-4f16-9764-cc36111a6ec6"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tuoi}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="2" y="83" width="44" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="d16979d5-4b73-4d2a-8c71-8a6be4ecf3ba"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Địa chỉ:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="261" y="41" width="21" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="4dd1f011-93a3-4799-b843-9e45da4b5b68"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{capcuu}.equals("1")? "": "X"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="356" y="63" width="28" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="25c8ce30-4b8c-4754-99f7-b88c77c8bc8b"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Tuổi:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="444" y="63" width="28" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="f8c9449e-9410-41ca-9b52-e516d6b6472c"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Giới:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="1" y="0" width="203" height="21" forecolor="#000000" uuid="7c02c0ea-0584-4de5-8f3e-974534dde6a8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{soyte}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="340" y="41" width="21" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF" uuid="854946b6-59f5-48e5-8046-7d76bb8ac98e"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{capcuu}.equals("1")? "X": ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="2" y="63" width="87" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="959277bd-0539-4771-80a0-35f30d3cf0a1"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Họ tên người bệnh:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="456" y="104" width="39" height="19" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="8ee5e489-ccd7-47c0-a62c-5d3aa294670d"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Giường:"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="204" y="21" width="235" height="20" forecolor="#000000" uuid="12c4c181-47ee-4df0-841d-78b670b5561f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="16" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[VI SINH]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="466" y="83" width="29" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="7419e623-f678-44b7-88c7-497e24f2a7c1"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("")? $P{sothebaohiem}.substring(8,11):$P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="2" y="103" width="44" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="04765170-c756-4567-9149-427a4228ce78"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Khoa:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="88" y="63" width="268" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="78dc903d-ae12-43ca-97bd-19c44e313f35"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{hoten}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="217" y="41" width="44" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="6ecd2ecd-22da-47ff-91be-2b8d53659ef0"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Thường:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="310" y="83" width="58" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="e44a7e13-0cbd-4531-9395-2905e487420f"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Số thẻ BHYT:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="64" y="124" width="472" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="a9d309fb-cdf4-4451-aef3-42adc5ce4e74"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{chandoan}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="495" y="83" width="40" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="6ded5daf-968a-4a69-b14f-bad1af575162"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("")? $P{sothebaohiem}.substring(11,15):$P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="296" y="41" width="44" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="727f0deb-611a-42ea-a3a9-d6116fba5631"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Cấp cứu:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="2" y="124" width="62" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="af30d7b3-65d7-437f-9628-f57cd2992d0e"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Chẩn đoán:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="46" y="83" width="264" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="afb29251-c501-4b61-855a-921060a65d16"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{diachi_bn}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="204" y="0" width="235" height="20" forecolor="#000000" uuid="e7039c2f-f9bd-40a2-97f6-966d25bb3f1f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="16" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[PHIẾU KẾT QUẢ XÉT NGHIỆM 
]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="379" y="103" width="32" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="d3a82263-bebf-4a5c-8938-e90f2685065d"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Buồng:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="411" y="83" width="28" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="f692979e-7d2a-435c-b332-8b5aa2432381"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("")? $P{sothebaohiem}.substring(03,05):$P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="368" y="83" width="24" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="8f4a6c0f-83ac-4f49-a7c5-b78012d94264"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("")? $P{sothebaohiem}.substring(00,02):$P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="1" y="21" width="203" height="20" forecolor="#000000" uuid="3f0cb6fe-e471-49e5-b707-d54334d486b9"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenbenhvien}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement positionType="Float" x="466" y="14" width="70" height="20" forecolor="#000000" uuid="95b85073-d71c-47f6-a6ee-d4ae77762a8e"/>
				<jr:barbecue xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" type="Code128" drawText="false" checksumRequired="true" barHeight="45">
					<jr:codeExpression><![CDATA[$P{mabenhnhan}]]></jr:codeExpression>
					<jr:applicationIdentifierExpression><![CDATA[$P{mabenhnhan}]]></jr:applicationIdentifierExpression>
				</jr:barbecue>
			</componentElement>
			<staticText>
				<reportElement positionType="Float" x="466" y="3" width="70" height="11" forecolor="#000000" uuid="0cda1228-d20d-408e-9d8d-c8c0b572ccc0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Mã bệnh nhân]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="466" y="34" width="75" height="11" forecolor="#000000" uuid="adb9dfdf-d008-4751-8cae-f250ff552a6e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{mabenhnhan}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="495" y="104" width="40" height="19" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="2ebc2e38-48af-41fd-b118-e6715889b82c"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{giuong}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="411" y="104" width="45" height="19" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="13bf0ba4-f943-43c3-b372-91192573df66"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{buong}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="0" y="144" width="106" height="17" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="e8d91de6-d6a2-4d4f-bc14-17bbda9deed9"/>
				<box leftPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Bác sĩ điều trị:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="106" y="144" width="430" height="17" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="20020962-e7de-48ec-adf5-2de4f2692545"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nguoi_chi_dinh}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="106" y="161" width="167" height="17" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="b0319c49-95c2-4b42-9b35-1a6a27b292cb"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ngay_lay_mau}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="340" y="103" width="39" height="20" forecolor="#000000" uuid="983fcef2-ed04-4f39-b316-7df40f1934da">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="273" y="103" width="67" height="20" forecolor="#000000" uuid="3da5af1e-9147-4834-927b-327977805ce3"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Bệnh phẩm:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="379" y="178" width="157" height="17" forecolor="#000000" uuid="7d9cc645-1139-42ed-8f4a-194e05af4729">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nguoigiaomau_cmu}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="106" y="178" width="167" height="17" forecolor="#000000" uuid="d80ee931-3b56-4f13-92f2-179f3b4cd25a">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nguoilaymau_cmu}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="285" y="178" width="94" height="17" forecolor="#000000" uuid="0e3dc607-3565-45fb-93ec-b21264b8a5e7"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Người giao mẫu:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="-1" y="178" width="107" height="17" forecolor="#000000" uuid="10d56344-0b00-450d-9ffc-ba6dc7aa8eb1"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Người nhận mẫu:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="1" y="161" width="105" height="17" forecolor="#000000" uuid="19e50c7c-e160-41bf-8329-79f059e05f02"/>
				<box leftPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Ngày giờ nhận mẫu:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="285" y="161" width="94" height="17" forecolor="#000000" uuid="67837bbd-2751-4e47-8c1b-9f7e15deecf9"/>
				<box leftPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày giờ lấy mẫu:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="379" y="161" width="156" height="17" forecolor="#000000" uuid="a8174a26-63b8-4ab7-ba34-8b9dc4f896bf">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ngaylaynhanmau}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="1" y="41" width="203" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" uuid="39e6722a-4277-4e13-bbc0-3eb0a70f6e82"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Số xét nghiệm:........................."]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="20">
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="282" y="0" width="253" height="20" forecolor="#000000" uuid="cdc992a8-65fe-4886-acd0-4e8e52f6b333"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[KẾT QUẢ	]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="282" height="20" forecolor="#000000" uuid="3e06da36-1aee-47e2-86a1-e9600b07051e"/>
				<box topPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[XÉT NGHIỆM]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="282" height="20" forecolor="#000000" uuid="a02f80ed-c353-4240-a4da-0d1e0864a657"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ten_xetnghiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="282" y="0" width="253" height="20" forecolor="#000000" uuid="a3a5a02a-aef3-4f61-b297-f161b77d0daf"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="12" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BINHTHUONG}.equals("-1")? "<style size='11' isBold='true' pdfFontName='Times New Roman'>"+$F{ket_qua}+"</style>":$F{BINHTHUONG}.equals("0")? "<style size='11' isBold='true' pdfFontName='Times New Roman'>"+$F{ket_qua}+"</style>":$F{ket_qua}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="100" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" x="310" y="0" width="227" height="16" forecolor="#000000" uuid="325f31c7-805d-479b-9a9b-afd96409ecb0"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isItalic="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYTRAKETQUA}]]></textFieldExpression>
			</textField>
			<break>
				<reportElement x="0" y="99" width="575" height="1" uuid="fddba0f0-d6db-4d9b-bc29-048785b37c49"/>
			</break>
			<textField isBlankWhenNull="true">
				<reportElement x="310" y="15" width="227" height="20" forecolor="#000000" uuid="324d694c-abe1-4603-85b2-fa6ff6984845"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["KÝ DUYỆT KẾT QUẢ"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="3" y="0" width="258" height="20" forecolor="#000000" uuid="1451d6fb-679a-4da1-9581-333003c727ab"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Người thực hiện: "+ $F{ngthuchien_in}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="3" y="20" width="74" height="15" forecolor="#000000" uuid="82dd7053-c264-4609-a3ea-d0bdbc7e3254"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Lưu ý: "]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="77" y="50" width="184" height="15" uuid="5f98f302-c855-4023-9e5c-374158c47052"/>
				<box>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="77" y="35" width="184" height="15" uuid="897b710a-ca58-43c2-9d53-5b698a44e7e0"/>
				<box>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="77" y="20" width="184" height="15" uuid="bf52266a-4d40-46fc-9182-f69a2c1042fb"/>
				<box>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="3" y="35" width="74" height="15" forecolor="#000000" uuid="5df3f05d-f42b-4366-a31e-b2edbaa3ebbd"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Ý kiến bác sĩ:"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="315" y="79" width="226" height="20" forecolor="#000000" uuid="f0346151-8eeb-486e-b28f-1ab592875c37">
					<printWhenExpression><![CDATA[$F{ANCHUKY}.equals("0")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGUOI_THUC_HIEN}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement x="26" y="65" width="128" height="20" uuid="14334ca7-0c67-4a92-9833-4f3411946ff1"/>
				<staticText>
					<reportElement x="0" y="0" width="128" height="20" forecolor="#FFFFFF" uuid="5033b05a-7b0f-4fa0-bac5-77f19c015ee9"/>
					<textElement>
						<font fontName="Times New Roman"/>
					</textElement>
					<text><![CDATA[KYXEMKQ]]></text>
				</staticText>
			</frame>
		</band>
	</summary>
</jasperReport>
