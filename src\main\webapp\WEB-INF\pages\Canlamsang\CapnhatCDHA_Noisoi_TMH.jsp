<%@ page import="l2.ThamSoManager" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="google-site-verification" content="u6uNEfD4cb3gidezi4r_6aI8Wb1E07-ufBeCQpvmlqQ"/>
    <title>Hệ thống chăm sóc sức khỏe</title>
    <link rel="icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
    <link rel="shortcut icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>

    <%--Bootstrap--%>
    <link rel="stylesheet" href="<c:url value="/resources/font-awesome-4.7.0/css/font-awesome.min.css"/>">
    <link href="<c:url value="/resources/bootstrap-4.1.3/dist/css/bootstrap.min.css" />" rel="stylesheet"/>

    <!-- jQuery file -->
    <link href="<c:url value="/resources/css/divheader.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/css/style_new.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/css/magiczoomplus.css" />" rel="stylesheet"/>

    <!--Jquery-->
    <link rel="stylesheet" href="<c:url value="/resources/css/jquery-ui-redmond.1.9.1.css" />"/>
    <script src="<c:url value="/resources/js/jquery.min.1.8.3.js" />"></script>
    <script src="<c:url value="/resources/js/jquery-ui.1.9.1.js" />"></script>
    <!--Grid-->
    <link href="<c:url value="/resources/jqgrid/css/ui.jqgrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/jqgrid/js/i18n/grid.locale-en.js" />"></script>
    <script src="<c:url value="/resources/jqgrid/js/jquery.jqGrid.src.js" />"></script>
    <script src="<c:url value="/resources/js/common_function.js" />"></script>
    <script src="<c:url value="/resources/js/jquery.inputmask.bundle.min.js" />"></script>
    <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
    <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/dialog/jquery.alerts.1.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/dialog/jquery.alerts.js" />"></script>
    <script src="<c:url value="/resources/js/read_file.js" />"></script>
    <link href="<c:url value="/resources/dialog/jBox.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/dialog/jBox.js" />"></script>
    <script src="<c:url value="/resources/camau/popper.js" />"></script>
    <script src="<c:url value="/resources/bootstrap-4.4.1-dist/js/bootstrap.min.js"/>" ></script>

    <script src="<c:url value="/resources/camau/js/jquery.validate.min.js" />"></script>
    <link href="<c:url value="/resources/combogrid/css/smoothness/jquery.ui.combogrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/combogrid/plugin/jquery.ui.combogrid-1.6.3.js" />"></script>
    <script src="<c:url value="/resources/ckeditor/ckeditor.js" />"></script>
    <script src="<c:url value="/resources/ckeditor/adapters/jquery.js" />"></script>
    <script src="<c:url value="/resources/webcam/say-cheese.js" />"></script>
    <link href="<c:url value="/resources/webcam/pygments.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/webcam/say-cheese.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
    <script src="<c:url value="/resources/js/magiczoomplus.js" />"></script>
    <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/blockUI/jquery.blockUI.js" />"></script>
    <script src="<c:url value="/resources/js/jquery-confirm.min.js" />"></script>
    <link href="<c:url value="/resources/css/jquery-confirm.min.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/camau/css/custom.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/camau/smartca769.js" />"></script>
    <script src="<c:url value="/resources/camau/material/moment.js" />"></script>
    <script src="<c:url value="/resources/camau/js/common.js" />"></script>
    <script src="<c:url value="/resources/camau/js/kiemtradulieuthoigiankham.js" />"></script>
    <script src="<c:url value="/resources/chart/canvasjs.js" />"></script>
    <link href="<c:url value="/resources/bootstrap/css/select2.min.css"/>" rel="stylesheet">
    <script src="<c:url value="/resources/bootstrap/js/select2.min.js"/>"></script>

    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">

    <jsp:include page="NoisoiChuyenChiDinh.jsp"/>
    <%--datetimepicker--%>
    <script src="/web_his/resources/js/datetimepicker.js"></script>
    <link href="/web_his/resources/css/datetimepicker.css" rel="stylesheet"/>
    <style>
        .width1 {
            width: 200px;
        }

        .width2 {
            width: 545px;
        }

        .width3 {
            width: 150px;
        }

        .width4 {
            width: 325px;
        }

        .width5 {
            width: 810px;
        }

        .width6 {
            width: 500px;
        }

        legend {

            color: red;
        }

        .width100 {
            width: 100%;
        }

        span.cellWithoutBackground {
            display: block;
            background-image: none;
            margin-right: -2px;
            margin-left: -2px;
            height: 14px;
            padding: 4px;
        }

        /*CMU 2606/2017*/
        #list_lichsuCDHA tr.jqgrow td {
            white-space: normal !important;
            height: auto;
            vertical-align: text-top;
        }

        #cke_2_top {
            display: none;
        }

        #cke_2_bottom {
            display: none;
        }

        .picedit_box{
            max-width:230px;
            max-height:230px;
        }
        #cg-divHeader{
            height:27px !important;
        }

        fieldset.scheduler-border {
            border-color: rgb(192, 192, 192);
            padding: 0 1.4em 1.4em 1.4em !important;
            margin: 0 0 1.5em 0 !important;
            -webkit-box-shadow:  0px 0px 0px 0px #000;
            box-shadow:  0px 0px 0px 0px #000;
        }

        legend.scheduler-border {
            font-size: 1.2em !important;
            font-weight: bold !important;
            text-align: left !important;
            width:auto;
            padding:0 10px;
            border-bottom:none;
        }
        fieldset {
            display: block;
            margin-inline-start: 2px;
            margin-inline-end: 2px;
            padding-block-start: 0.35em;
            padding-inline-start: 0.75em;
            padding-inline-end: 0.75em;
            padding-block-end: 0.625em;
            min-inline-size: min-content;
            border-width: 2px;
            border-style: groove;
            border-color: rgb(192, 192, 192);
            border-image: initial;
        }
    </style>
    <script>
        var sovaovien;
        var sovaovien_noi;
        var sovaovien_dt_noi;
        var da_thanh_toan;
        var flag_noitru = "-1";
        var matoathuoc;
        var makhovattu;
        var url_loadtonkho;
        var url_loadthuoc;
        var ngay_kb;
        var cobhyt;
        var sophieuthanhtoan;
        var tlmg;
        var bhytchi;
        var phongcdha;
        var dathanhtoan;
        var ma_cdha = 0;
        var sophieu = "";
        var mabenhnhan = "";
        var stt_benhan = "";
        var stt_dotdieutri = "";
        var stt_dieutri = "";
        var manv = "${Sess_UserID}";
        var tennv = "${Sess_User}";
        var admin = "${Sess_Admin}";
        var phongcdha_ss;
        var noitru_ngoaitru;
        var bacsi_chidinh;
        var co_bao_hiem;
        var sobenhan_noitru_tt;
        var sobenhan_noitru;
        var icd_benhnhan;
        var ten_icd_benhnhan;
        var soba;
        if("${thamso_184002}"=="1"){
            var gioth_ct_timer_is_on = false;
            var gioth_ct_timer_previous_status = false;
            var gioth_ct_timer;
            var showtime_gioth_ct_cancel = 0;
        }
        var ngay_chi_dinh;
        var ngaychidinh;
        var giochidinh;
        var SESS_PHONG_BAN = "<%= session.getAttribute("Sess_PhongBan").toString()%>";
        var SESS_USER_ID = "<%= session.getAttribute("Sess_UserID").toString()%>";
        var dvtt = "${Sess_DVTT}";
        var THAMSO_828449 = "<%= ThamSoManager.instance(session).getThamSoString("828449", "0")%>";
        var THAMSO_960623 = "<%= ThamSoManager.instance(session).getThamSoString("960623", "0")%>";
        var THAMSO_960626 = "<%= ThamSoManager.instance(session).getThamSoString("960626", "0")%>";
        function checkNguoiDocKetQua() {
            var _selector = $('#cboNguoiDocKetQua');
            var countOption =  _selector.find("option[value='"+SESS_USER_ID+"']").length;
            return !!!_selector.val() || countOption==0 //|| _selector.find(':selected').data("khoa") != SESS_PHONG_BAN
                ;
        }

        function setNguoiDocKetQua(maBacSi) {
            var _selector = $('#cboNguoiDocKetQua');
            _selector.val(maBacSi||SESS_USER_ID);
        }

        function loadBacSiTheoKhoa(strListMaKhoa = "-1") {
            // Fetch the preselected item, and add to the control
            var _selector = $('#cboNguoiDocKetQua');
            $.ajax({
                type: 'GET',
                url: 'lay-bac-si-theo-khoa',
                data: {
                    p_strListMaKhoa: strListMaKhoa
                }
            }).then(function (data) {
                if(jQuery.isArray(data) ) {
                    _selector.empty();
                    // $('#cboDoiTuongTiepNhan').append($('<option>', { value: '0', text : 'Khác', color: '' }));
                    _selector.append($('<option>', {
                        value: SESS_USER_ID,
                        text : "${Sess_User}",
                        "data-khoa": SESS_PHONG_BAN
                    }));
                    $.each(data, function (i, item) {
                        if(!!item && item.MA_NHANVIEN && item.TEN_NHANVIEN && item.MA_PHONGBAN && item.MA_NHANVIEN != SESS_USER_ID) {
                            _selector.append($('<option>', {
                                value: item.MA_NHANVIEN,
                                text : item.TEN_NHANVIEN,
                                "data-khoa": item.MA_PHONGBAN
                            }));
                        }
                    });
                    _selector.val(SESS_USER_ID);
                    if(!!!_selector.val()) {
                        // jAlert("Tài khoản đăng nhập không thuộc khoa đã cấu hình", "Cảnh báo");
                    }
                }
            }).always(function( data, textStatus, jqXHR ) {

            });
        }

        function luuNguoiDocKetQua(_soPhieu, _soVaoVien, _soVaoVienDt = 0, _noiTru = 0, _listMaCls = "-1") {
            $.ajax({
                type: 'POST',
                url: 'update-thong-tin-theo-benh-nhan',
                async: false,
                data: {
                    P_LOG_USERID: "${Sess_UserID}",
                    p_dvtt: "${Sess_DVTT}",
                    p_soPhieu: _soPhieu,
                    p_soVaoVien: _soVaoVien,
                    p_soVaoVienDt: _soVaoVienDt,
                    p_noiTru: _noiTru,
                    p_sessKhoaId: SESS_PHONG_BAN,
                    p_nguoiDocKetQua: $('#cboNguoiDocKetQua').val(),
                    p_listMaCls: _listMaCls,
                    p_loai_cls: 'CDHA',
                    action: "UPD_CLS_NGUOI_DOC_KET_QUA"
                }
            }).fail(function(data){
                jAlert("Lỗi cập nhật người đọc kết quả", "Cảnh báo");
            }).then(function (data) {
                console.log(data);
            }).always(function( data, textStatus, jqXHR ) {

            });
        }

        function getNguoiDocKetQua(_soPhieu, _soVaoVien, _soVaoVienDt = 0, _noiTru = 0, _listMaCls = "-1") {
            $.ajax({
                type: 'GET',
                url: 'select-thong-tin-theo-benh-nhan',
                data: {
                    P_LOG_USERID: "${Sess_UserID}",
                    p_dvtt: "${Sess_DVTT}",
                    p_soPhieu: _soPhieu,
                    p_soVaoVien: _soVaoVien,
                    p_soVaoVienDt: _soVaoVienDt,
                    p_noiTru: _noiTru,
                    p_listMaCls: _listMaCls,
                    p_loai_cls: 'CDHA',
                    action: "SEL_CLS_NGUOI_DOC_KET_QUA"
                }
            }).then(function (data) {
                if(!!data && !!data[0]){
                    var obj = data[0];
                    setNguoiDocKetQua(obj.MA_NHANVIEN);
                }
            }).always(function( data, textStatus, jqXHR ) {

            });
        }
        String.prototype.replaceAll = function (search, replacement) {
            var target = this;
            return target.split(search).join(replacement);
        };
        function chamcong_ekip(dvtt, stt_benhan, mabenhnhan, ma_dv, ten_dv, sophieu) {
            var dathuchien = $("#dathuchien").prop('checked');
            if(!dathuchien) jAlert("Phải thực hiện mới có thể chấm công",'Thông báo');
            else{
                $("#madichvu").val(ma_dv);
                $("#tendichvu").val(ten_dv);
                $("#tiencongthuchien").empty();
                $.get("hgi_ekip_maloaitiencong_theoloai", {loai: " ", thamso: ma_dv ,loai_chucnang: 'NS'}).done(function(data) {
                    $("<option value='-1'>--    Chọn vai trò   --</option>").appendTo("#tiencongthuchien");
                    if (data && data.length > 0) {
                        $.each(data, function (i) {
                            $("<option value='" + data[i].MA_TIENCONG + "'>" + data[i].TEN_VAITRO +"-" + data[i].MA_LOAIDICHVU +"-" + data[i].SO_TIEN + "</option>").appendTo("#tiencongthuchien");
                        });
                    }
                });
                $("#ekip_mau").empty();
                $.get("hgi_ekip_maloaiekip_theoloai", {ma_dv: ma_dv ,loai_chucnang: 'NS'}).done(function(data) {
                    $("<option value='-1'>--    Chọn ekip   --</option>").appendTo("#ekip_mau");
                    if (data && data.length > 0) {
                        $.each(data, function (i) {
                            $("<option value='" + data[i].MA_EKIP + "'>" + data[i].TEN_EKIP + "</option>").appendTo("#ekip_mau");
                        });
                    }
                });

                var ekip_thuchien = new jBox('Modal', {
                    title: 'Thực hiện chấm công CĐHA',
                    overlay: false,
                    content: $('#ekip'),
                    draggable: 'title'
                });

                if (dvtt != "" && stt_benhan != "" && mabenhnhan != "" && ma_dv != "" && ten_dv != "" && sophieu != "") {
                    var arr = [dvtt, stt_benhan, mabenhnhan, ma_dv, sophieu,'NS'];
                    var url = 'hgi_ekip_chamcongthuchien_select_theobenhnhan?url=' + convertArray(arr);
                    $("#list_chitiettiencongthuchien").jqGrid('setGridParam', { datatype: 'json', url: url }).trigger('reloadGrid');
                    ekip_thuchien.open();
                }
            }
        }
        if("${thamso_184002}"=="1"){
            var totalSeconds = null;
            var currentClientDate = null;
            var gio_sv, phut_sv, giay_sv;
            function Xulygiosever() {
                var now = new Date();
                var distance = now.getSeconds() - (currentClientDate || now).getSeconds();
                currentClientDate = now;
                if(!totalSeconds){
                    totalSeconds = Number("${gio}")* 3600 + Number("${phut}")*60 + Number("${giay}");
                }else{
                    totalSeconds += distance < 0 ? 0 : distance;
                }
                var total = totalSeconds;
                var hours = Math.floor(total / 3600);
                total %= 3600;
                var minutes = Math.floor(total / 60);
                var seconds = total % 60;
                gio_sv = addZero(hours == 24 ? 0 : hours);
                phut_sv = addZero(minutes);
                giay_sv = addZero(seconds);
            }
        }

        if("${thamso_184002}"=="1"){
            function addZero(i) {
                if (i < 10) {
                    i = "0" + i;
                }
                return i;
            }
            function gioThCtTimerChange() {
                if (gioth_ct_timer_is_on || ($("#gioth_ct").data('da-thuc-hien')) )
                    stopGioThCtTimer();
                else
                    showtime_gioth_ct();
            }

            function showtime_gioth_ct() {
                Xulygiosever();
                $('#gioth_ct').val(gio_sv + ":" + phut_sv + ":" + giay_sv);
                gioth_ct_timer = setTimeout(showtime_gioth_ct, 1000);
                gioth_ct_timer_is_on = true;
            }
            function stopCount() {
                clearTimeout(t);
                timer_is_on = 0;
            }
            function stopGioThCtTimer() {
                clearTimeout(gioth_ct_timer);
                gioth_ct_timer_is_on = false;
            }
        }
        function combogridTen(ten, ma) {
            ten.combogrid({

                url: "ttpt_ekip_timkiem_bacsi?url=" + convertArray(["${Sess_DVTT}"]),

                debug: true,
                width: "300px",
                colModel: [{'columnName': 'TEN_PHONGBAN', 'label': 'Phòng Khoa', 'width': '30', 'align': 'left'},
                    {'columnName': 'TEN_NHANVIEN', 'width': '40', 'label': 'Tên nhân viên', 'align': 'left'},
                    {'columnName': 'MA_NHANVIEN', index: 'MA_NHANVIEN', hidden: true}
                ],
                select: function (event, ui) {
                    ma.val(ui.item.MA_NHANVIEN);
                    ten.val(ui.item.TEN_NHANVIEN.trim());
                    return false;
                }
            });
        }

        function combogridTenVaiTro(ten, ma) {
            ten.combogrid({

                url: "timkiem_vaitro_ekip?url=" + convertArray(["${Sess_DVTT}"]),
                debug: true,
                width: "300px",
                colModel: [{
                    'columnName': 'MA_VAITRO',
                    'label': 'Phòng Khoa',
                    'width': '30',
                    'align': 'left',
                    hidden: true
                },
                    {'columnName': 'TEN_VAITRO', 'width': '40', 'label': 'Tên vai trò', 'align': 'left'}

                ],
                select: function (event, ui) {
                    ma.val(ui.item.MA_VAITRO);
                    ten.val(ui.item.TEN_VAITRO.trim());
                    return false;
                }
            });
        }

        function nextText(current, next) {
            current.keypress(function (evt) {
                if (evt.keyCode == 13) {
                    next.focus();
                }
            });

        }

        function combogridMaBS(ten, ma) {
            ma.combogrid({

                url: "noitru_hoichan_timkiem_mabacsi?url=" + convertArray(["${Sess_DVTT}"]),

                debug: true,
                width: "300px",
                colModel: [{'columnName': 'MA_NHANVIEN', 'label': 'Mã nhân viên', 'width': '30', 'align': 'left'},
                    {'columnName': 'TEN_NHANVIEN', 'width': '40', 'label': 'Tên nhân viên', 'align': 'left'},
                ],
                select: function (event, ui) {
                    ma.val(ui.item.MA_NHANVIEN);
                    ten.val(ui.item.TEN_NHANVIEN.trim());
                    return false;
                }
            });
        }

        function openEkip() {
            dialog_capnhatketquattpt_ekip.dialog("open");
            var sophieu = $("#sophieu").val();
            var madv = $("#macdha").val();
            var noitru = $("#noitru").val();
            $("#ten_nhanvien").focus();
            $("#list_chitietvaitrothuchien").jqGrid({
                url: "danhsachvaitroekip_ttpt?sophieu_dv=" + sophieu + "&madv=" + madv + "&noitru=" + noitru + "&sovaovien=" + sovaovien + "&sovaovien_noi=" + sovaovien_noi + "&sovaovien_dt_noi=" + sovaovien_dt_noi,
                datatype: "json",
                loadonce: true,
                height: 312,
                width: 650,
                colNames: ['ID_EKIP', 'Mã nhân viên', "Tên nhân viên", "Vai trò"],
                colModel: [
                    {name: 'ID_EKIP', index: 'ID_EKIP', width: 50, hidden: true},
                    {name: 'MA_NHANVIEN', index: 'MA_NHANVIEN', width: 50},
                    {name: 'TEN_NHANVIEN', index: 'TEN_NHANVIEN', width: 150},
                    {name: 'TEN_VAITRO', index: 'TEN_VAITRO', width: 150}
                ],
                sortname: 'MA_VAITRO',
                sortorder: "asc",
                caption: "Danh sách Ekip",
                ignoreCase: true,
                onSelectRow: function (id) {
                    if (id) {
                        var ret = $("#list_chitietvaitrothuchien").jqGrid('getRowData', id);
                        $("#frm_kq_id_ekip").val(ret.ID_EKIP);
                        //load_dmvaitrothuchien(ret);
                    }
                }
            });
            $("#list_chitietvaitrothuchien").jqGrid('setGridParam', {
                url: "danhsachvaitroekip_ttpt?sophieu_dv=" + sophieu + "&madv=" + madv + "&noitru=" + noitru + "&sovaovien=" + sovaovien + "&sovaovien_noi=" + sovaovien_noi + "&sovaovien_dt_noi=" + sovaovien_dt_noi,
                datatype: 'json'
            }).trigger('reloadGrid');
        }

        function delete_thuocnoitru(list, nghiepvu) {
            var id = $("#list_thuocdichvu").jqGrid('getGridParam', 'selrow');
            if (id) {
                var ret = $("#list_thuocdichvu").jqGrid('getRowData', id);
                if (ret.MA_BAC_SI_THEMTHUOC === undefined || ret.MA_BAC_SI_THEMTHUOC === "${Sess_UserID}") {
                    jConfirm('Bạn có muốn xóa không?', 'Thông báo', function (r) {
                        if (r.toString() === "true") {
                            var url = "noitru_toathuoc_delete";
                            if (list === "list_thuoctonghopall")
                                nghiepvu = ret.NGHIEP_VU;
                            var arr = [ret.STT_TOATHUOC, matoathuoc, "${Sess_DVTT}", $("#sttdieutri").val(), $("#sttbenhan").val(), $("#sttdotdieutri").val(), sophieuthanhtoan,
                                ret.THANHTIEN_THUOC, ret.MAKHOVATTU, "0", nghiepvu, ret.MAVATTU, encodeURIComponent(ret.TEN_VAT_TU), ret.SO_LUONG];
                            var sophieu = $("#sophieu").val();
                            var ma_cdha = $("#macdha").val();
                            url += "?url=" + convertArray(arr);
                            if (sophieu) {
                                url += "&sophieu=" + sophieu;
                            }
                            if (ma_cdha) {
                                url += "&ma_cdha=" + ma_cdha;
                            }
                            $.post(url).done(function (data) {
                                if (data === "1")
                                    jAlert("Bệnh nhân đã thanh toán viện phí", 'Cảnh báo');
                                else if (data === "2")
                                    jAlert("Bệnh nhân đã được xuất thuốc", 'Cảnh báo');
                                else if (data == '100') {
                                    jAlert("Đã chốt báo cáo dược, không thể sửa/xóa", 'Cảnh báo');
                                }
                                else {
                                    $("#list_thuocdichvu").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                                }
                            });
                        }
                    });
                } else {
                    jAlert("Bạn không được xóa thuốc trong toa của bác sĩ khác!", 'Cảnh báo');
                }
            } else {
                jAlert("Chọn một dòng thuốc để xóa", 'Cảnh báo');
            }
        }

        function capNhatEkip(capnhat) {
            if (capnhat == 1) {
                if ($("#ma_nhanvien").val() != null && $("#ma_vaitro").val() != null) {
                    $("#frm_kq_id_ekip").val("");

                    $("#use_ekip").val("true");
                    $.ajax({
                        type: "POST",
                        contentType: 'application/json; charset=utf-8',
                        dataType: 'json',
                        url: "capnhat_cd_dichvu_ekip",
                        data: JSON.stringify(createJson()), // Note it is important
                    }).success(function () {
                        // jAlert("Cập nhật Ekip thành công", "Thông báo");
                        $("#list_chitietvaitrothuchien").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                        $("#ma_nhanvien").val("");
                        $("#ten_nhanvien").val("");
                        $("#ma_vaitro").val("");
                        $("#ten_vaitro").val("");
                    }).fail(function () {
                        jAlert("Cập nhật Ekip không thành công", "Cảnh báo");
                    });
                }

            } else {
                $("#ma_nhanvien").val("");
                $("#ten_nhanvien").val("");
                $("#ma_vaitro").val("");
                $("#ten_vaitro").val("");

                var url = String.format("delete_cd_dichvu_ekip?id_ekip={0}", $("#frm_kq_id_ekip").val());
                $.ajax({
                    url: url
                }).success(function () {
                    jAlert("Đã xoá Ekip", "Thông báo");
                    $("#list_chitietvaitrothuchien").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                }).fail(function () {
                    jAlert("Xoá Ekip không thành công", "Cảnh báo");
                });
            }

        }

        function delete_toathuocngoaitru(list, nghiepvu) {
            var id = $("#list_thuocdichvu").jqGrid('getGridParam', 'selrow');
            if (id) {
                var url = "xuatduoc_giamtai_svv";
                $.post(url, {
                    nghiepvu: "ngoaitru_toadichvu",
                    matoathuoc: matoathuoc,
                    makhambenh: $("#makhambenh").val(),
                    xacnhan: "false",
                    mabenhnhan: mabenhnhan,
                    ngaykhambenh: ngay_kb
                }).done(function (data) {
                    if (data == "0") {
                        var ret = $("#list_thuocdichvu").jqGrid('getRowData', id);
                        var dongia = ret.DONGIA_BAN_BH;
                        var arr = [ret.STT_TOATHUOC, matoathuoc, ${Sess_DVTT}, $("#makhambenh").val(), sophieuthanhtoan, ret.THANHTIEN_THUOC,
                            ret.MAKHOVATTU, dongia, nghiepvu, ret.MAVATTU, $("#mabenhnhan").val(), ngay_kb, sovaovien, encodeURIComponent(ret.TEN_VAT_TU), ret.SO_LUONG];
                        var url = "xoathuocngoaitru_giamtai?url=" + convertArray(arr);
                        var sophieu = $("#sophieu").val();
                        var ma_cdha = $("#macdha").val();
                        if (sophieu) {
                            url += "&sophieu=" + sophieu;
                        }
                        if (ma_cdha) {
                            url += "&ma_cdha=" + ma_cdha;
                        }
                        $.ajax({
                            url: url
                        }).done(function (data) {
                            if (data === "1")
                                jAlert("Bệnh nhân đã thanh toán", 'Cảnh báo');
                            else if (data === "2")
                                jAlert("Bệnh nhân đã được xuất thuốc", 'Cảnh báo');
                            else if (data === "3")
                                jAlert("Bệnh nhân đã được trả thuốc về kho", 'Cảnh báo');
                            else if (data == '100') {
                                jAlert("Đã chốt báo cáo dược, không thể sửa/xóa", 'Cảnh báo');
                            }
                            else
                                $("#list_thuocdichvu").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                            var url = "xuatduoc_giamtai_svv";
                            $.post(url, {
                                nghiepvu: "ngoaitru_toadichvu",
                                matoathuoc: matoathuoc,
                                makhambenh: $("#makhambenh").val(),
                                xacnhan: "true",
                                mabenhnhan: mabenhnhan,
                                ngaykhambenh: ngay_kb
                            }).done(function (data) {

                            });
                        });

                    }
                });
            } else {
                jAlert("Chọn 1 dòng thuốc để xóa", 'Cảnh báo');
            }
        }

        function createJson() {
            var jsondata = {};
            var alertString = null;
            $("form#form_Ekip").find(".post-data").each(function (index, element) {
                var tag = $(element);
                var type = tag.attr("type");
                var key = tag.attr("key");
                var require = tag.attr("required");
                var value = tag.val();
                if (require == true) {
                    alertString = tag.attr("error-message").val();
                }
                if (alertString != null) {
                    jAlert(alertString, "Cảnh báo");
                    return;
                }
                jsondata[key] = value;
            })
            return jsondata;
        };
        function newStringDateTime() {
            var date = new Date();
            var dateStr =
                ("00" + date.getDate()).slice(-2) + "/" +
                ("00" + (date.getMonth() + 1)).slice(-2) + "/" +
                date.getFullYear() + " " +
                ("00" + date.getHours()).slice(-2) + ":" +
                ("00" + date.getMinutes()).slice(-2) + ":" +
                ("00" + date.getSeconds()).slice(-2);
            return dateStr;
        }
        window.listIdTimer = [];
        window.listIdTimerRunning = [];
        function runningInputTimer() {
            var listIdTimer = window.listIdTimer;
            var listIdTimerRunning = window.listIdTimerRunning;
            var timerSubfix = "_timer";
            listIdTimerRunning.forEach(function (item) {
                clearTimeout(window[item + timerSubfix]);// stop all timer in list
            });
            window.listIdTimerRunning = [];
            listIdTimer.forEach(function (item) {
                window[item + timerSubfix] = setTimeout(runningInputTimer, 1000);
                window.listIdTimerRunning.push(item);
                $("#" + item).val(newStringDateTime());
                // console.log(newStringDateTime());
                // console.log($("#" + item).val());
            });
        }

        function changeInputTimerStatus(selectorId, isStopTimer = false) {
            if (!isStopTimer && $.inArray(selectorId, window.listIdTimer) == -1) {
                window.listIdTimer.push(selectorId);
            }
            if (isStopTimer) {
                window.listIdTimer = $.grep(window.listIdTimer, function(n) {
                    return n != selectorId;
                });
            }
            if(window.listIdTimer && window.listIdTimer.length > 0) {
                runningInputTimer();
            }
            // console.log($("#" + selectorId).val());
        }
        $(function () {
            $(":input").inputmask();
            $("#ngaythuchien").datepicker();
            $("#ngaythuchien").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngaythuchien").val("${ngayhientai}");
            if ("${thamSo_87100111888}" == "1") {
                $("#trThoiGianKetThuc").show();
                $("#textThoiGianBatDau").html("Thời gian kết quả");
            } else {
                $("#trThoiGianKetThuc").hide();
                $("#textThoiGianBatDau").html("Thời gian kết quả");
            }

            if("${thamso_184002}"== "1"){
                $("#ngayth_ct").datepicker();
                $("#ngayth_ct").datepicker("option", "dateFormat", "dd/mm/yy");
                $("#ngayth_ct").val("${ngayhientai}");
                $("#ngayKtThucHienCdha").datepicker();
                $("#ngayKtThucHienCdha").datepicker("option", "dateFormat", "dd/mm/yy");

                $("#gioth_ct").click(function (evt) {
                    gioThCtTimerChange();
                });
            }
            $("#tungay1").datepicker();
            $("#tungay1").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#tungay1").val("${ngayhientai}");
            $("#denngay1").datepicker();
            $("#thoiGianBatDau_cls,#ngaygioth_ct").inputmask({
                mask: "1/2/y h:s:s",
                placeholder: "dd/mm/yyyy 00:00:00",
                alias: "datetime",
                hourFormat: "24"
            });
            khoiTaoSuKienInputIcd("#icdChanDoanCanLamSang","#btnChanDoanCanLamSang","#tenChanDoanCanLamSang","#maBenhLyChanDoanCanLamSang");
            $("#thoiGianBatDau_cls,#ngaygioth_ct").datetimepicker({
                dateFormat: 'dd/mm/yy',
                timeFormat: 'HH:mm:ss',
                hourFormat: "24"
            });
            /*$("#thoiGianBatDau_cls").click(function (evt) {
                changeInputTimerStatus("thoiGianBatDau_cls", true);
            }).change(function (evt) {
                changeInputTimerStatus("thoiGianBatDau_cls", true);
            }).dblclick(function(){
                changeInputTimerStatus("thoiGianBatDau_cls");
            });*/
            /*$("#ngaygioth_ct").click(function (evt) {
                changeInputTimerStatus("ngaygioth_ct", true);
            }).change(function (evt) {
                changeInputTimerStatus("ngaygioth_ct", true);
            }).dblclick(function(){
                changeInputTimerStatus("ngaygioth_ct");
            });*/
            $("#thoiGianBatDau_cls").val(newStringDateTime());
            $("#ngaygioth_ct").val(newStringDateTime());
            //changeInputTimerStatus("thoiGianBatDau_cls");
            //changeInputTimerStatus("ngaygioth_ct");
            $("#denngay1").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#denngay1").val("${ngayhientai}");
            $("#ten_vaitro").keypress(function (evt) {
                if (evt.keyCode == 13) {
                    capNhatEkip(1);
                    $("#ten_nhanvien").focus();
                }
            });
            nextText($("#ten_nhanvien"), $("#ten_vaitro"));
            combogridMaBS($("#ten_nhanvien"), $("#ma_nhanvien"));
            combogridTen($("#ten_nhanvien"), $("#ma_nhanvien"));
            combogridTenVaiTro($("#ten_vaitro"), $("#ma_vaitro"));
            // HPG--Tim kiem cls tu ngay den ngay
            if ("${tichhopdsbncd}" == "1") {
                $("#form1").hide();
                $("#formdsbn").show();
            } else {
                $("#formdsbn").hide();
                $("#form1").show();
            }
            if ("${timkiem_cls}" == "1") {
                $(".hpg_tmp").show();
                $("#tungay").datepicker();
                $("#tungay").datepicker("option", "dateFormat", "dd/mm/yy");
                $("#tungay").val("${ngayhientai}");
                $("#tungay").change(function (evt) {
                    reload_grid();
                });

            } else
                $(".hpg_tmp").hide();
            //--
            // ĐắkLắk: tùy chọn hiển thị lọc theo khoa, phòng, đối tượng
            if ("${choloctheokhoaphong_capnhatketquacls}" == "1")
                $(".dlk_tmp").show();
            else
                $(".dlk_tmp").hide();
            // End ĐắkLắk
            if ("${hienthi_mausac_bn}" == "1")
                $(".ghichutrangthai").show();
            else
                $(".ghichutrangthai").hide();
            //-------
            if ("${hienthi_them_cls}" == "1")
                $(".hpg_hienthithem").show();
            else
                $(".hpg_hienthithem").hide();
            if ("${Sess_DVTT}" == "34004") {
                $("#phongbenh").val("${Sess_Phong}");
            }
            if ("${thuocdvthcls}" == "1") {
                $("#thuoccls").show();
            } else {
                $("#thuoccls").hide();
            }
            var dataURL = "";
            $('#browse-file').on('change', function (evt) {
                // BDG nhập file ảnh từ máy tính - report_BinhDuong_CDHA_v1.0.0.doc
                var bg_preview = document.getElementById('say-cheese-snapshots').childNodes[0];
                var bg_file = document.getElementById('browse-file').files[0];
                var bg_reader = new FileReader();

                bg_reader.addEventListener("load", function () {
                    bg_preview.src = bg_reader.result;
                    dataURL = bg_reader.result;
                }, false);

                if (bg_file) {
                    bg_reader.readAsDataURL(bg_file);
                }
            });

            <c:forEach var="i" items="${formnoisoi}">
            $("#tab_noisoi_" + "${i.MA_MAUNOISOI}").tabs();
            </c:forEach>

            $('#save-snapshot').on('click', function (evt) {
                luuanh();
            });

            function luuanh() {
                var macdha = $("#macdha").val();
                var sophieu = $("#sophieu").val();
                var noitru = $("#noitru").val();
                var makhambenh = $("#makhambenh").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                if (dataURL != "" && sophieu != "" && macdha != "") {
                    $.post("luuhinhanh_sieuam", {
                        sophieu: sophieu,
                        macdha: macdha,
                        dvtt: "${Sess_DVTT}",
                        hinh: dataURL,
                        noitru: noitru,
                        sttbenhan: sttbenhan,
                        sttdotdieutri: sttdotdieutri,
                        sttdieutri: sttdieutri,
                        makhambenh: makhambenh
                    })
                        .done(function () {
                            var arr = [sophieu, "${Sess_DVTT}", macdha, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                            var url = 'sieuam_danhsach_hinhanh?url=' + convertArray(arr);
                            $("#list_hinhanhnoisoi").jqGrid('setGridParam', {
                                datatype: 'json',
                                url: url
                            }).trigger('reloadGrid');
                        });
                }
            };
            // Remove Audio
            var sayCheese = new SayCheese('#say-cheese-container', {snapshots: true});
            sayCheese.on('start', function () {
                $('#action-buttons').fadeIn('fast');
                $('*').keyup(function (e) {
                    if (e.keyCode === 113) {
                        sayCheese.takeSnapshot();
                        var macdha = $("#macdha").val();
                        var sophieu = $("#sophieu").val();
                        var noitru = $("#noitru").val();
                        var makhambenh = $("#makhambenh").val();
                        var sttbenhan = $("#sttbenhan").val();
                        var sttdotdieutri = $("#sttdotdieutri").val();
                        var sttdieutri = $("#sttdieutri").val();
                        if (dataURL != "" && sophieu != "" && macdha != "") {
                            $.post("luuhinhanh_sieuam", {
                                sophieu: sophieu,
                                macdha: macdha,
                                dvtt: "${Sess_DVTT}",
                                hinh: dataURL,
                                noitru: noitru,
                                sttbenhan: sttbenhan,
                                sttdotdieutri: sttdotdieutri,
                                sttdieutri: sttdieutri,
                                makhambenh: makhambenh
                            })
                                .done(function () {
                                    var arr = [sophieu, "${Sess_DVTT}", macdha, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                                    var url = 'sieuam_danhsach_hinhanh?url=' + convertArray(arr);
                                    $("#list_hinhanhnoisoi").jqGrid('setGridParam', {
                                        datatype: 'json',
                                        url: url
                                    }).trigger('reloadGrid');
                                });
                        }
                        return false;

                    }
                });

                $('#take-snapshot').on('click', function (evt) {
                    sayCheese.takeSnapshot();
                });
                //LDG: 16/07/2018 nút F3
                $(document.body).on('keydown', this, function (event4) {
                    if (event4.keyCode == 114) {
                        $('#take-save-snapshot').click();
                    }
                });
                //CMU: 12/10/2017
                $('#take-save-snapshot').on('click', function (evt) {
                    sayCheese.takeSnapshot();
                    $('#save-snapshot').trigger("click");
                });
                $('#say-cheese-container').on('click', function (evt) {
                    sayCheese.takeSnapshot();
                    $('#save-snapshot').trigger("click");
                });
            });
            sayCheese.on('error', function (error) {
                var $alert = $('<div>');
                $alert.addClass('alert alert-error').css('margin-top', '20px');
                if (error === 'NOT_SUPPORTED') {
                    $alert.html("<strong>:(</strong> your browser doesn't support video yet!");
                } else if (error === 'AUDIO_NOT_SUPPORTED') {
                    $alert.html("<strong>:(</strong> your browser doesn't support audio yet!");
                } else {
                    $alert.html("<strong>:(</strong> you have to click 'allow' to try me out!");
                }

                $('.say-cheese').prepend($alert);
            });

            sayCheese.on('snapshot', function (snapshot) {
                var img = document.createElement('img');
                $(img).on('load', function () {
                    $('#say-cheese-snapshots').html(img);
                });
                img.src = snapshot.toDataURL('image/png');
                var canvas = document.createElement('canvas');
                var ctx = canvas.getContext('2d');
                canvas.width = snapshot.width * 0.5;
                canvas.height = snapshot.height * 0.5;
                ctx.drawImage(snapshot, 0, 0, canvas.width, canvas.height);
                dataURL = canvas.toDataURL('image/jpeg', 0.7);
            });

           $(document).ready(function () {
                document.addEventListener("keydown", function (e) {
                    if (e.key === "F11") {
                        e.preventDefault();
                        console.log("F11 pressed");
                        $('#take-save-snapshot').click();
                    }
                });
            });


            CKEDITOR.instances["ketqua"].on('key', function (e) {
//                    F2 luu anh
                if (e.data.keyCode === 113) {
                    var macdha = $("#macdha").val();
                    var sophieu = $("#sophieu").val();
                    var noitru = $("#noitru").val();
                    var makhambenh = $("#makhambenh").val();
                    var sttbenhan = $("#sttbenhan").val();
                    var sttdotdieutri = $("#sttdotdieutri").val();
                    var sttdieutri = $("#sttdieutri").val();
                    if (dataURL != "" && sophieu != "" && macdha != "") {
                        $.post("luuhinhanh_sieuam", {
                            sophieu: sophieu,
                            macdha: macdha,
                            dvtt: "${Sess_DVTT}",
                            hinh: dataURL,
                            noitru: noitru,
                            sttbenhan: sttbenhan,
                            sttdotdieutri: sttdotdieutri,
                            sttdieutri: sttdieutri,
                            makhambenh: makhambenh
                        })
                            .done(function () {
                                var arr = [sophieu, "${Sess_DVTT}", macdha, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                                var url = 'sieuam_danhsach_hinhanh?url=' + convertArray(arr);
                                $("#list_hinhanhnoisoi").jqGrid('setGridParam', {
                                    datatype: 'json',
                                    url: url
                                }).trigger('reloadGrid');
                            });
                    }
                    return false;
                }

            });
            CKEDITOR.instances["ketluan"].on('key', function (e) {
//                    F2 luu anh
                if (e.data.keyCode === 113) {
                    var macdha = $("#macdha").val();
                    var sophieu = $("#sophieu").val();
                    var noitru = $("#noitru").val();
                    var makhambenh = $("#makhambenh").val();
                    var sttbenhan = $("#sttbenhan").val();
                    var sttdotdieutri = $("#sttdotdieutri").val();
                    var sttdieutri = $("#sttdieutri").val();
                    if (dataURL != "" && sophieu != "" && macdha != "") {
                        $.post("luuhinhanh_sieuam", {
                            sophieu: sophieu,
                            macdha: macdha,
                            dvtt: "${Sess_DVTT}",
                            hinh: dataURL,
                            noitru: noitru,
                            sttbenhan: sttbenhan,
                            sttdotdieutri: sttdotdieutri,
                            sttdieutri: sttdieutri,
                            makhambenh: makhambenh
                        })
                            .done(function () {
                                var arr = [sophieu, "${Sess_DVTT}", macdha, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                                var url = 'sieuam_danhsach_hinhanh?url=' + convertArray(arr);
                                $("#list_hinhanhnoisoi").jqGrid('setGridParam', {
                                    datatype: 'json',
                                    url: url
                                }).trigger('reloadGrid');
                            });
                    }
                    return false;
                } else if (e.data.keyCode === 9) {
                    if ("${Sess_DVTT}" === "70001") {
                        CKEDITOR.instances.ketluan.setData(CKEDITOR.instances.ketluan.document.getBody().getText().toUpperCase())
                    }
                }

            });
            sayCheese.start();
            $("#tenthuongmai_dv").keypress(function (evt) {
                if ($("#kho_dv").val() == null) {
                    jAlert("Tủ thuốc trống vui lòng kiểm tra thông tin đăng nhập khoa phòng, tủ thuốc", 'Thông báo');
                }
            });
            $('#lsnoisoi').on('click', function (evt) {
                $("#lichsuCDHA").click();
            });
            $("#list_benhnhan").jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 345,
                width: 300,
                colNames: ["Mã y tế", "Họ tên", "TENBENHNHAN", "Tuổi", "Nội trú", "gioitinh", "diachi", "sobhyt", "SO_PHIEU", "MA_KHAM_BENH", "stt_benhan", "stt_dotdieutri",
                    "stt_dieutri", "NGUOI_CHI_DINH", "PHONG_CHI_DINH", "BACSI_THUCHIEN", "MA_PHONG_XN", "KET_QUA_CDHA", "STT", "ngaybatdau", "ngayketthuc", "noidk", "namsinh", "SOVAOVIEN", "SOVAOVIEN_NOI", "SOVAOVIEN_DT_NOI", "CAPCUU", "DA_THANH_TOAN",
                    "CHUANDOANICD", "TENKHOA", "sophieuthanhtoan", "tilemiengiam", "ngay_kb", "NGAY_SINH", "CO_BHYT","SOBENHAN", "SOBENHAN_TT", "ICD", "NGAYTHUCHIEN", "NGUOI_THUC_HIEN", "NGAY_CHI_DINH"],//CMU: 27/10/2017
                colModel: [
                    {name: 'MABENHNHAN', index: 'MABENHNHAN', width: 70},
                    {
                        name: 'TENBENHNHAN_HT',
                        index: 'TENBENHNHAN_HT',
                        width: 200,
                        formatter: function (cellvalue, options, rowObject) {
                            var color;
                            var color_text;
                            if ("${hienthi_mausac_bn}" == "0") {
                                if (rowObject.DA_THANH_TOAN == "1") {
                                    color = '#009900';
                                    color_text = 'white';
                                }   //END VTU:25/10/2016
                                else {
                                    color = 'white';
                                    color_text = 'black';
                                }
                            }
                            return '<span class="cellWithoutBackground" style="background-color:' + color + ';font-weight:bold ;color:' + color_text + '">' + cellvalue + '</span>';
                        }
                    },
                    {name: 'TENBENHNHAN', index: 'TENBENHNHAN', hidden: true, width: 200, sorttype: 'string'},
                    {name: 'TUOI', index: 'TUOI', width: 50, align: 'right'},
                    {name: 'NOITRU', index: 'NOITRU', width: 50, align: 'center'},
                    {name: 'GIOITINH', index: 'GIOITINH', hidden: true},
                    {name: 'DIACHI', index: 'DIACHI', hidden: true},
                    {name: 'SOTHEBHYT', index: 'SOTHEBHYT', hidden: true},
                    {name: 'SO_PHIEU', index: 'SO_PHIEU', hidden: true},
                    {name: 'MA_KHAM_BENH', index: 'MA_KHAM_BENH', hidden: true},
                    {name: 'STT_BENHAN', index: 'STT_BENHAN', hidden: true},
                    {name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', hidden: true},
                    {name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', hidden: true},
                    {name: 'NGUOI_CHI_DINH', index: 'NGUOI_CHI_DINH', hidden: true},
                    {name: 'BACSI_THUCHIEN', index: 'BACSI_THUCHIEN', hidden: true},
                    {name: 'PHONG_CHI_DINH', index: 'PHONG_CHI_DINH', hidden: true},
                    {name: 'MA_PHONG_CDHA', index: 'MA_PHONG_XN', hidden: true},
                    {name: 'KET_QUA_CDHA', index: 'KET_QUA_CDHA', hidden: true},
                    {name: 'STT_HANGNGAY', index: 'STT_HANGNGAY', width: 50, sorttype: 'int'},
                    {name: 'NGAYBATDAU', index: 'NGAYBATDAU', hidden: true},
                    {name: 'NGAYKETTHUC', index: 'NGAYKETTHUC', hidden: true},
                    {name: 'NOIDK', index: 'NOIDK', hidden: true},
                    {name: 'NAMSINH', index: 'NAMSINH', hidden: true},
                    {name: 'SOVAOVIEN', index: 'SOVAOVIEN', hidden: true},
                    {name: 'SOVAOVIEN_NOI', index: 'SOVAOVIEN_NOI', hidden: true},
                    {name: 'SOVAOVIEN_DT_NOI', index: 'SOVAOVIEN_DT_NOI', hidden: true},
                    {name: 'CAPCUU', index: 'CAPCUU', hidden: true},
                    {name: 'DA_THANH_TOAN', index: 'DA_THANH_TOAN', hidden: true},
                    {name: 'CHUANDOANICD', index: 'CHUANDOANICD', hidden: true},
                    {name: 'TENKHOA', index: 'TENKHOA', hidden: true},
                    {name: 'SOPHIEUTHANHTOAN', index: 'SOPHIEUTHANHTOAN', hidden: true},
                    {name: 'TI_LE_MIEN_GIAM', index: 'TI_LE_MIEN_GIAM', hidden: true},
                    {name: 'NGAY_KB', index: 'NGAY_KB', hidden: true},
                    {name: 'NGAY_SINH', index: 'NGAY_SINH', hidden: true},//KGG them ngay sinh goi so
                    {name: 'CO_BHYT', index: 'CO_BHYT', hidden: true},
                    {name: 'SOBENHAN', index: 'SOBENHAN', hidden: true},
                    {name: 'SOBENHAN_TT', index: 'SOBENHAN_TT', hidden: true},
                    {name: 'ICD', index: 'ICD', hidden: true},
                    {name: 'NGAY_THUC_HIEN', index: 'NGAY_THUC_HIEN', hidden: true}, // TGGDEV:36575 thêm
                    {name: 'NGUOI_THUC_HIEN', index: 'NGUOI_THUC_HIEN', hidden: true},
                    {name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', hidden: true}
                ],
                caption: "Danh sách bệnh nhân",
                ignoreCase: true,
                rowNum: 1000000,
                gridComplete: function () {
                    var c = $("#list_benhnhan").getGridParam("records");
                    $("#list_benhnhan").jqGrid('setCaption', "Danh sách bệnh nhân (" + c + " bệnh nhân)");
                    if ("${hienthi_mausac_bn}" == "1") {
                        var rows = $("#list_benhnhan").getDataIDs();
                        for (var i = 0; i < rows.length; i++) {
                            var CAPCUU = $("#list_benhnhan").jqGrid('getRowData')[i]["CAPCUU"];

                            var SOTHEBHYT = $("#list_benhnhan").jqGrid('getRowData')[i]["SOTHEBHYT"].toString().length;
                            var tuoi = $("#list_benhnhan").jqGrid('getRowData')[i]["TUOI"];
                            var thanhtoan = $("#list_benhnhan").jqGrid('getRowData')[i]["DA_THANH_TOAN"];
                            var CO_BHYT = $("#list_benhnhan").jqGrid('getRowData')[i]["CO_BHYT"];//CMU:26/10/2017
                            if (CAPCUU === "1") {
                                $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {
                                    color: 'red',
                                    weightfont: 'bold'
                                });
                            } else if (CO_BHYT == 0 && thanhtoan == 0) {//CMU:26/10/2017
                                $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {
                                    color: '#bf00ff',
                                    weightfont: 'bold'
                                });
                            } else if (CO_BHYT == 0 && thanhtoan == 1) {
                                $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {
                                    color: '##EE7600',
                                    weightfont: 'bold'
                                });
                            } else if (tuoi.indexOf("tháng") != -1) {
                                $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {
                                    color: '#00ff00',
                                    weightfont: 'bold'
                                });
                            } else {
                                $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {
                                    color: 'black',
                                    weightfont: 'bold',
                                    background: 'white'
                                });
                            }

                        }
                    }
                },
                ondblClickRow: function (id) {
                    if (id) {
                        var ret = $("#list_benhnhan").jqGrid('getRowData', id);
                        noitru_ngoaitru = ret.NOITRU;
                        co_bao_hiem = ret.CO_BHYT;
                        bacsi_chidinh = ret.NGUOI_CHI_DINH;
                        phongcdha_ss = ret.MA_PHONG_CDHA;
                        sobenhan_noitru = ret.SOBENHAN;
                        sobenhan_noitru_tt = ret.SOBENHAN_TT;
                        icd_benhnhan = ret.ICD;
                        ten_icd_benhnhan = ret.CHUANDOANICD.replace(ret.ICD + ' - ', '');
                        ngay_chi_dinh = ret.NGAY_CHI_DINH;
                        if(ngay_chi_dinh !== null){
                            var ngaygio_chichinh_cls = ngay_chi_dinh.split(" ");
                            ngaychidinh = ngaygio_chichinh_cls[0];
                            giochidinh = ngaygio_chichinh_cls[1];
                            $("#ngaychidinh_cls").val(ngaychidinh);
                            $("#giochidinh_cls").val(giochidinh);
                            $("#ngaychidinh_kq").val(ngaychidinh);
                            $("#giochidinh_kq").val(giochidinh);
                        }
                        loadThongTinBenhNhan(ret);
                        loadDsCD(ret);
                    }
                },
                onRightClickRow: function (id1) {
                    if (id1) {
                        $('#list_benhnhan').jqGrid('setSelection', id1);
                        //alert(id1);

                        $.contextMenu({
                            selector: '#list_benhnhan tr',
                            callback: function (key, options) {
                                if (key == "trathuocvekho") {
                                    var id = $("#list_benhnhan").jqGrid('getGridParam', 'selrow');
                                    var ret = $("#list_benhnhan").jqGrid('getRowData', id);
                                    var arr = ["${Sess_DVTT}", ret.NOITRU, ret.SO_PHIEU, ret.MA_KHAM_BENH, ret.STT_BENHAN, ret.STT_DOTDIEUTRI, ret.STT_DIEUTRI, 0];
                                    // TGGDEV-36575: admin được phép hủy kết quả, nhân viên chỉ được hủy trong ngày, thêm tham số cho phép nhân viên hủy qua ngày
                                    if ("${Sess_Admin}" != "0" || (ret.NGUOI_THUC_HIEN == "${Sess_UserID}" && ret.NGAY_THUC_HIEN == "${ngayhientai}" && "${tsktvhuyKQ}" == "1") || (ret.NGUOI_THUC_HIEN == "${Sess_UserID}" && "${tsktvhuyKQ}" == "0")) {
                                        var risarr = [ret.SO_PHIEU, ret.MABENHNHAN, ret.NOITRU, "NS", "0"];
                                        var urlarr = "ris_kiemtra_trangthai_cachup";
                                        $.post(urlarr, {
                                            url: convertArray(risarr)
                                        }).done(function (data) {
                                            if (data != "0") {
                                                return jAlert("Có ca chụp đang thực hiện bởi RIS. Không thể hủy phiếu. Vui lòng liên hệ khoa CDHA...", "Thông báo");
                                            } else {
                                                jConfirm('Xác nhận hủy kết quả  ?', 'Thông báo', function (r) {
                                                    if (r.toString() == "true") {
                                                        if (ret.SO_PHIEU != "") {
                                                            var arr_ekip = [ret.SO_PHIEU,'NS'];
                                                            var url_ekip = "hgi_ekip_dachamekip_theophieu?url=" + convertArray(arr_ekip);
                                                            $.ajax({ url: url_ekip }).done(function (data) {
                                                                if (data != "0")
                                                                    jAlert("Đã chấm công ekip, phải hủy ekip mới có thể hủy kết quả!", 'Thông báo');
                                                                else {
                                                                    var url = "huyketquacdha?url=" + convertArray(arr);
                                                                    $.ajax({
                                                                        url: url
                                                                    }).done(function (data) {
                                                                        if (data == "RIS.1") {
                                                                            jAlert("Trạng thái phiếu hiện tại trên RIS không cho phép hủy kết quả CĐHA của phiếu chỉ định này", "Thông báo");
                                                                        } else if (data == "ERRLOGIN") {
                                                                            jAlert("Xác thực đăng nhập RIS Connector thất bại, Vui lòng kiểm tra lại thông tin cấu hình kết nối RIS", "Thông báo");
                                                                        } else if (data == "RISFAIL") {
                                                                            jConfirm("Hủy kết quả CĐHA của phiếu trên RIS thất bại. Bạn có muốn tiếp tục?", "Thông báo", function (r) {
                                                                                if (r.toString() == "true") {
                                                                                    $.post("ris_update_huytrangthai", {
                                                                                        url: convertArray(arr)
                                                                                    }).always(function () {
                                                                                        //code HIS
                                                                                        var arr1 = ["${Sess_DVTT}", "Hủy kết quả CDHA cho bệnh nhân " + ret.TEN_BENH_NHAN + " với phiếu CDHA " + ret.SO_PHIEU, "${Sess_UserID}" + "-" + "${Sess_User}", "Hủy kết quả CDHA"];
                                                                                        $.post("lichsusudung_insert", {url: convertArray(arr1)});
                                                                                        jAlert("Hủy kết quả thành công", "Thông báo");
                                                                                        $("#lammoi").click();
                                                                                    });
                                                                                }
                                                                            });
                                                                        } else if (data == "SUCCESS") {
                                                                            var arr1 = ["${Sess_DVTT}", "Hủy kết quả CDHA cho bệnh nhân " + ret.TEN_BENH_NHAN + " với phiếu CDHA " + ret.SO_PHIEU, "${Sess_UserID}" + "-" + "${Sess_User}", "Hủy kết quả CDHA"];
                                                                            $.post("lichsusudung_insert", {url: convertArray(arr1)});
                                                                            jAlert("Hủy kết quả thành công", "Thông báo");
                                                                            $("#lammoi").click();
                                                                        }
                                                                    });
                                                                }
                                                            });
                                                        }
                                                    }
                                                });
                                            }
                                        });
                                    } else {
                                        jAlert("Chỉ Admin hoặc bác sĩ thực hiện mới có quyền hủy!", 'Thông báo');
                                    }
                                }
                                if (key == "goiso") {

                                    var id = $("#list_benhnhan").jqGrid('getGridParam', 'selrow');
                                    var ret = $("#list_benhnhan").jqGrid('getRowData', id);
                                    var chuoi = ${Sess_PhongDuocSet} +"|" + ret.STT_HANGNGAY.toString().replace('<span class="cellWithoutBackground" style="background-color:white;color:black">', '').replace('</span>', '') + "|" + ret.TENBENHNHAN + "|" + ret.CAPCUU + "|" + "CDHA_NOISOI" + "|" + "0";
                                    //KGG Goi so
                                    <c:choose>
                                    <c:when test="${kgggoiso == '1'}">
                                    chuoi = fetchListDSBN_NS($("#list_benhnhan"), id, ${Sess_PhongDuocSet}, "${Sess_TenPhong}");
                                    saveTextAsFileL(chuoi);
                                    </c:when>
                                    <c:when test="${kgggoiso == '3'}">
                                    chuoihinhanh = goisolayhinhanh($("#list_benhnhan"), id);
                                    saveTextAsFile(chuoihinhanh);
                                    </c:when>
                                    <c:otherwise>
                                    saveTextAsFile(chuoi);
                                    </c:otherwise>
                                    </c:choose>
                                    //End goi so
                                }
                            },
                            items: {
                                "trathuocvekho": {name: "Hủy kết quả"},
                                "goiso": {
                                    name: "<span style='color:red'>Gọi số bệnh nhân</span>", icon: "goiso"
                                }

                            }
                        });
                    }
                }

            });

            function clear_benhnhan() {
                $("#sovaovien").val('');
                $("#mabenhnhan").val('');
                $("#hoten").val('');
                $("#tuoi").val('');
                $("#gioitinh").val('');
                $("#diachi").val('');
                $("#sothebhyt").val('');
                $("#sophieu").val('');
                $("#makhambenh").val('');
                $("#noitru").val('');
                $("#sttbenhan").val('');
                $("#sttdotdieutri").val('');
                $("#sttdieutri").val('');
                $("#mabacsichidinh").val('');
                $("#ngaybatdau").val('');
                $("#ngayketthuc").val('');
                $("#noidk").val('');
                $("#namsinh").val('');
                sovaovien = '';
                sovaovien_noi = '';
                sovaovien_dt_noi = '';
                da_thanh_toan = '';
                sophieuthanhtoan = '';
                cobhyt = '';
                ngay_kb = '';
                tlmg = '';
                flag_noitru = '';
                $("#bacsinoisoi").val('');
                $("#bacsinoisoi").val("");
                $("#_chandoan").val('');
                $("#chandoan").val('');
                $("#tenkhoa").val('');
                $("#hoten_ct").val('');
                $("#tuoi_ct").val('');
                $("#gioitinh_ct").val('');
                $("#mabenhnhan_ct").val('');
                $("#tenkhoa_ct").val('');
                $("#sothebhyt_ct").val('');
                $("#maunoisoi").val("-1");
                $("#ketqua").val("");
                $("#ketluan").val("");
                $("#tt_thanhtoan").val('');
                $("#bacsichidinh").val('');
            }

            <c:forEach var="i" items="${formnoisoi}">
            $("#tab_noisoi_" + "${i.MA_MAUNOISOI}").tabs();
            </c:forEach>
            dialog_capnhatketquattpt_ekip = $("#dialog_capnhatketquattpt_ekip").dialog({
                autoOpen: false,
                width: 700,
                modal: true,
                resizable: false,
                position: {my: "center top", at: "center top", of: window}
            });

            //CMU NGUYEN 01254779592 SKYPE:CONANKUN12
            function tennhanvienthuchien(ncd) {
                if ($("#dathuchien").prop("checked") == true) {
                    var data = {maNhanVien: ncd};
                    var url = 'TT_BS_ChiDinh';
                    $.post(url, data).done(function (resp) {
                        $("#bacsinoisoi").val(resp[0].TEN_NHANVIEN);
                        $("#bacsinoisoi").prop("disabled", true);
                    });
                } else {
                    $("#bacsinoisoi").val('');
                }

            } //END CMU NGUYEN 01254779592 SKYPE:CONANKUN12
            $("#list_benhnhan").jqGrid('filterToolbar', {
                stringResult: true,
                searchOnEnter: false,
                defaultSearch: "cn"
            });
            $("#list_noisoi_bhyt").jqGrid({
                datatype: "local",
                loadonce: true,
                //height: 158,
                height: 245, // ĐắkLắk (An Giang yêu cầu) - Ninh 09/12/2016: điều chỉnh độ cao lưới
                width: 660,
                colNames: ["DA_THANH_TOAN", "ma_CDHA", "Yêu cầu chẩn đoán", "TEN_CDHA_TEXT", "Kết quả", "Trangthai", "Ma", "DVT", "NGUOI_THUC_HIEN", "ID_DIEUTRI", "STT_MAYCDHA", "Ekip" <c:if test="${thamso_dongia != 0}">,"Đơn giá" </c:if>
                    , "NGAY_GIO_CHI_DINH"
                ],
                colModel: [
                    {name: 'DA_THANH_TOAN', index: 'DA_THANH_TOAN', hidden: true},
                    {name: 'MA_CDHA', index: 'MA_CDHA', hidden: true},
                    {
                        name: 'TEN_CDHA', index: 'TEN_CDHA', width: 150,
                        formatter: function (cellvalue, options, rowObject) {
                            var color_text;
                            if (rowObject.DA_THANH_TOAN.toString() === "0") {
                                color_text = 'red';
                            } else {
                                color_text = 'black';
                            }
                            return '<span id="TEN_CDHA_EKIP' +rowObject.MA_CDHA+ '" class="cellWithoutBackground" style="color:' + color_text + '">' + (cellvalue == null ? "" : cellvalue) + '</span>';
                        }
                    },
                    {
                        name: 'TEN_CDHA_TEXT', index: 'TEN_CDHA_TEXT', width: 150,
                        formatter: function (cellvalue, options, rowObject) {
                            return rowObject.TEN_CDHA;
                        }
                    },
                    {name: 'KET_QUA', index: 'KET_QUA', width: 60, hidden: true},
                    {name: 'TRANGTHAI_BHYT', index: 'TRANGTHAI_BHYT', hidden: true},
                    {name: 'MABAOCAO', index: 'MABAOCAO', hidden: true},
                    {name: 'DVT_CDHA', index: 'DVT_CDHA', hidden: true},
                    {name: 'NGUOI_THUC_HIEN', index: 'NGUOI_THUC_HIEN', hidden: true},
                    {name: 'ID_DIEUTRI', index: 'ID_DIEUTRI', hidden: true},
                    {name: 'STT_MAYCDHA', index: 'STT_MAYCDHA', hidden: true},
                    {name: 'HGI_EKIP', index: 'HGI_EKIP', width: 20, hidden: !"${hgi_ekip}"}
                    <c:if test="${thamso_dongia != 0}">
                    ,{name: 'DON_GIA', index: 'DON_GIA', width: 40}
                    </c:if>
                    ,{name: 'NGAY_GIO_CHI_DINH', index: 'NGAY_GIO_CHI_DINH', hidden: true}
                ],
                caption: "Yêu cầu chẩn đoán hình ảnh",
                serializeGridData: function (_obj) {
                    return _obj;
                },
                loadComplete: function (_obj) {
                    if (_obj.length > 0 && _obj.length != undefined) {
                        _obj.forEach(function (_data) {
                            tennhanvienthuchien(_data.NGUOI_THUC_HIEN);
                        })
                    }
                    var $self = $(this);
                    var ids = $self.jqGrid('getDataIDs');
                    var dvtt = "${Sess_DVTT}";
                    var mabenhnhan = $("#mabenhnhan").val();
                    var stt_benhan = $("#sttbenhan").val();
                    if (stt_benhan === undefined || stt_benhan.length < 1) {
                        stt_benhan = $("#makhambenh").val();
                    }
                    var sophieu = $("#sophieu").val();
                    for (var i = 0; i < ids.length; i++) {
                        var ret = $("#list_noisoi_bhyt").jqGrid('getRowData', i + 1);
                        var ma_dv = ret.MA_CDHA;
                        var ten_dv =  document.getElementById("TEN_CDHA_EKIP"+ma_dv).innerText;
                        var rowId = ids[i];
                        var chamcong = "<input style='height:23px;width:100%;' " +
                            "type='button' id='chamcong' name='chamcong' value='Chi tiết' " +
                            "onclick=\"chamcong_ekip('" + dvtt + "','" + stt_benhan + "','" + mabenhnhan + "','" + ma_dv + "','" + ten_dv + "','" + sophieu + "');\" />" +
                            "<input type='text' hidden='true' id='madichvu' name='madichvu' value='" + ma_dv + "' >" +
                            "<input type='text' hidden='true' id='tendichvu' name='tendichvu' value='" + ten_dv + "' >";
                        $self.jqGrid('setRowData', rowId, {HGI_EKIP: chamcong});
                    }
                },
                ondblClickRow: function (id) {
                    var ret = $("#list_noisoi_bhyt").jqGrid('getRowData', id);
                    if("${Sess_DVTT}" == '96175') {
                        $('#ma_pp_vo_cam').val('4').change()
                    }
                    // var ngay_chi_dinh_ct = ret.NGAY_GIO_CHI_DINH;
                    // if (ngay_chi_dinh_ct !== null) {
                    //     var ngaygio_chichinh_cls_ct = ngay_chi_dinh_ct.split(" ");
                    //     var ngaychidinh_ct = ngaygio_chichinh_cls_ct[0];
                    //     var giochidinh_ct = ngaygio_chichinh_cls_ct[1];
                    //     $("#ngaychidinh_kq").val(ngaychidinh_ct);
                    //     $("#giochidinh_kq").val(giochidinh_ct);
                    // }
                    loadThongTinKetQua(ret);
                },
                onSelectRow: function (id) {
                    if (id) {

                        var ret = $("#list_noisoi_bhyt").jqGrid('getRowData', id);
                        $("#macdha").val(ret.MA_CDHA);
                        $("#ht_chidinh").val(ret.TEN_CDHA);
                        $("#ma_maycdha_md").val(ret.STT_MAYCDHA);
                    }
                },
                onRightClickRow: function (id1) {
                    if (id1) {
                        $('#list_noisoi_bhyt').jqGrid('setSelection', id1);
                        $.contextMenu({
                            selector: '#list_noisoi_bhyt tr',
                            callback: function (key, options) {
                                if (key == "tuongtrinhpttt") {
                                    $(".clear-input").val("");
                                    var id = $("#list_noisoi_bhyt").jqGrid('getGridParam', 'selrow');
                                    var ret = $("#list_noisoi_bhyt").jqGrid('getRowData', id);
                                    hideSelfLoadingByClass("btn");
                                    var today = moment(new Date()).format("DD/MM/YYYY hh:mm:ss");
                                    var data = {
                                        LOAI_TUONG_TRINH: "NOISOI",
                                        NOI_TRU: $("#noitru").val(),
                                        PHIEU_CHI_DINH: $("#sophieu").val(),
                                        MA_CLS: ret.MA_CDHA,
                                        TEN_CLS: ret.TEN_CDHA_TEXT,
                                        MA_BENH_NHAN: mabenhnhan,
                                        MA_KHAM_BENH: $("#makhambenh").val(),
                                        SO_VAO_VIEN: sovaovien,
                                        SO_VAO_VIEN_DT: sovaovien_dt_noi,
                                        STT_DIEUTRI: stt_dieutri,
                                        STT_DOT_DIEU_TRI: stt_dotdieutri,
                                        STT_BENHAN: stt_benhan,
                                        ID_DIEUTRI: ret.ID_DIEUTRI,
                                        HO_TEN: $("#hoten").val(),
                                        TUOI: $("#tuoi").val(),
                                        GIOI_TINH: $("#gioitinh option:selected").text(),
                                        TEN_KHOA: $("#tenkhoa").val(),
                                        BHYT: $("#sothebhyt").val(),
                                        NGAY_CHI_DINH: $("#ngaychidinh_cls").val() + " " + $("#giochidinh_cls").val(),
                                        NGAY_THUC_HIEN: today,
                                        CHAN_DOAN_TRUOC: $("#_chandoan").val(),
                                        CHAN_DOAN_SAU: $("#_chandoan").val(),
                                        DVTT: ${Sess_DVTT}
                                    }
                                    loadDataTuongTrinh(data)
                                    $("#modalTuongtrinhpttt").modal("show")
                                }
                            },
                            items: {
                                "tuongtrinhpttt": {name: '<p><i class="fa fa-pencil-square-o" aria-hidden="true"></i> Tường trình phẫu thuật, thủ thuật</p>'},
                            }
                        });
                    }
                },
                gridComplete: function () {
                    var str = $("#list_noisoi_bhyt").getGridParam("records");
                    if (str != "0") {
                        $('#list_noisoi_bhyt').jqGrid('setSelection', "1");
                    }
                }
            });
            $("#list_chitiettiencongthuchien").jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 312,
                width: 600,
                colNames: ["Người thực hiện", "Vai trò", "Số tiền", "Mã nhân viên"],
                colModel: [
                    {name: 'TEN_NHANVIEN', index: 'TEN_NHANVIEN', width: 30},
                    {name: 'TEN_VAITRO', index: 'TEN_VAITRO', width: 30},
                    {name: 'SO_TIEN', index: 'SO_TIEN', width: 30},
                    {name: 'MA_NHANVIEN', index: 'MA_NHANVIEN', width: 30, hidden: true}
                ],
                sortname: 'TEN_VAITRO',
                sortorder: "asc",
                caption: "Ekip thực hiện CĐHA",
                ignoreCase: true,
                onSelectRow: function (id) {
                    if (id) {
                        var ret = $("#list_chitiettiencongthuchien").jqGrid('getRowData', id);
                        $("#manhanvien").val(ret.MA_NHANVIEN);
                    }
                }
            });
            $("#khoaphong_ekip").change(function (evt) {
                $("#nguoithuchien_ekip").empty();
                $.getJSON("hgi_nhanvientheophongban_select", {maphongban: $("#khoaphong_ekip").val()}, function (data) {
                    $("<option value='-1'>--  Chọn nhân viên  --</option>").appendTo("#nguoithuchien_ekip");
                    if (data && data.length > 0) {
                        $.each(data, function (i) {
                            $("<option value='" + data[i].MA_NHANVIEN + "'>" + data[i].TEN_NHANVIEN + "</option>").appendTo("#nguoithuchien_ekip");
                        });
                    }
                });
            });

            $("#themnguoithuchien").click(function (evt) {
                var dvtt = "${Sess_DVTT}";
                var stt_benhan = $("#sttbenhan").val();//MA_KHAM_BENH
                if (stt_benhan === undefined || stt_benhan.length < 1) {
                    stt_benhan = $("#makhambenh").val();
                }
                var mabenhnhan = $("#mabenhnhan").val();
                var manhanvien = $("#nguoithuchien_ekip").val();
                var maloaidichvu = $("#madichvu").val();
                //var tenloaidichvu = $("#tendichvu").val();
                var phieudichvu = $("#sophieu").val();
                var ma_tiencong = $("#tiencongthuchien").val();
                var ngay_thuchien = convertStr_MysqlDate($("#ngaythuchien").val());
                var nguoi_tao = "${Sess_UserID}";
                var ngaygio_tao = convertStr_MysqlDate("${ngayhientai}");
                var ekip_mau = $("#ekip_mau").val();
                if (ma_tiencong === "-1" && ekip_mau === "-1") {
                    jAlert("Vui lòng chọn Vai trò thực hiện!", 'Thông báo');
                }
                if (manhanvien === "-1" && ekip_mau === "-1") {
                    jAlert("Vui lòng chọn người thực hiện!", 'Thông báo');
                }
                if (ekip_mau == -1 && manhanvien != -1 && ma_tiencong != -1) {
                    var arr = [dvtt, stt_benhan, mabenhnhan, manhanvien, maloaidichvu, phieudichvu, ma_tiencong, ngay_thuchien, nguoi_tao, ngaygio_tao,'NS'];
                    var url = "hgi_ekip_chamcongthuchien_insert?url=" + convertArray(arr);
                    $.ajax({
                        url: url
                    }).done(function () {
                        jAlert("Thêm thành công!", 'Thông báo');
                        $("#list_chitiettiencongthuchien").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                    });
                } else if (ekip_mau != -1) {
                    var arr = [dvtt, stt_benhan, mabenhnhan, ekip_mau, maloaidichvu, phieudichvu, ngay_thuchien, nguoi_tao, ngaygio_tao,'NS'];
                    var url = "hgi_ekip_chamcongthuchien_insert_theoekipmau?url=" + convertArray(arr);
                    $.ajax({
                        url: url
                    }).done(function (data) {
                        if (data === 1) {
                            jAlert("Thêm người thực hiện từ EKIP mẫu thành công!", 'Thông báo');
                            $("#list_chitiettiencongthuchien").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                        } else {
                            jAlert("EKIP mẫu rỗng. Không thể thêm!", 'Thông báo');
                        }
                    });
                }
                ;
            });
            $("#xoanguoithuchien").click(function (evt) {
                var dvtt = "${Sess_DVTT}";
                var stt_benhan = $("#sttbenhan").val();
                if (stt_benhan === undefined || stt_benhan.length < 1) {
                    stt_benhan = $("#makhambenh").val();
                }
                var mabenhnhan = $("#mabenhnhan").val();
                var manhanvien = $("#manhanvien").val();
                var maloaidichvu = $("#madichvu").val();
                var phieudichvu = $("#sophieu").val();
                var str = [dvtt, stt_benhan, mabenhnhan, manhanvien, maloaidichvu, phieudichvu,'NS'];
                var url = "hgi_ekip_chamcongthuchien_delete_nhanvien?url=" + convertArray(str);
                $.ajax({
                    url: url
                }).done(function () {
                    jAlert("Xóa thành công!", 'Thông báo');
                    $("#list_chitiettiencongthuchien").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                });
            });
            $("#xoanguoithuchien_all").click(function (evt) {
                var dvtt = "${Sess_DVTT}";
                var stt_benhan = $("#sttbenhan").val();
                if (stt_benhan === undefined || stt_benhan.length < 1) {
                    stt_benhan = $("#makhambenh").val();
                }
                var mabenhnhan = $("#mabenhnhan").val();
                var maloaidichvu = $("#madichvu").val();
                var phieudichvu = $("#sophieu").val();
                var str = [dvtt, stt_benhan, mabenhnhan, maloaidichvu, phieudichvu,'NS'];
                var url = "hgi_ekip_chamcongthuchien_delete_all?url=" + convertArray(str);
                $.ajax({
                    url: url
                }).done(function () {
                    jAlert("Xóa thành công!", 'Thông báo');
                    $("#list_chitiettiencongthuchien").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                });
            });
            $("#luu_tt_maycdha").click(function (evt) {
                var sophieu = $("#sophieu").val();
                var noitru = $("#noitru").val();
                var makhambenh = $("#makhambenh").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var macdha = $("#macdha").val();
                if (sophieu != "" && macdha != "" && (sovaovien != "" || sovaovien_noi != "")) {
                    $.post("luu_maycdha_vaobangchitiet", {
                        sophieu: sophieu, macdha: macdha, noitru: noitru,
                        sttbenhan: sttbenhan, sttdotdieutri: sttdotdieutri, sttdieutri: sttdieutri,
                        makhambenh: makhambenh, sovaovien: sovaovien,
                        sovaovien_noi: sovaovien_noi,
                        sovaovien_dt_noi: sovaovien_dt_noi,
                        stt_may_cdha: ($("#ma_maycdha_md").val())
                    }).done(function (data) {
                        if (data == "1") {
                            jAlert("Cập nhật thành công!", "Thông báo");
                        } else {
                            jAlert("Vui lòng kiểm tra lại thông tin!", "Thông báo");
                        }
                    }).fail(function () {
                        jAlert("Vui lòng thử lại!", "Thông báo");
                    });
                }
            });

            function checkSTT(value, colname) {

                if ((value + "").match(/^\d+$/)) {
                    if (value < 0 || value > 50) {
                        return [false, "Hãy nhập số thứ tự nhỏ hơn"];
                    } else {

                        return [true, ""];
                    }
                } else {
                    return [false, "Số thứ tự phải là số nguyên dương"];
                }

            }

            $("#list_hinhanhnoisoi").jqGrid({
                datatype: "local",
                loadonce: true,
                height: 235,
                multiselect: true,
                width: 290,
                colNames: ["SO_PHIEU_CDHA", "DVTT", "idauto", "STT", "makhambenh", "Hình ảnh nội soi", "Chọn in"],
                colModel: [
                    {name: 'SO_PHIEU_CDHA', index: 'SO_PHIEU_CDHA', hidden: true},
                    {name: 'DVTT', index: 'DVTT', hidden: true},
                    {name: 'STT_AUTO', index: 'STT_AUTO', hidden: true, jsonmap: "STT"},
                    {
                        name: 'STT',
                        index: 'STT',
                        editable: true,
                        width: 10,
                        editrules: {custom: true, custom_func: checkSTT},
                        editoptions: {
                            dataInit: function (elem) {
                                $(elem).focus(function () {
                                    this.select();
                                });
                            }
                        }
                    },
                    {name: 'MA_CDHA', index: 'MA_CDHA', hidden: true},
                    {name: 'HINHANH', index: 'HINHANH', width: 80, formatter: imageFormat},
                    {name: 'CHONIN', index: 'CHONIN', hidden: true}
                ],
                rowNum: 200000,
                cellEdit: true,
                cellsubmit: 'clientArray',
                beforeEditCell: function (rowid, cellname, value, iRow, iCol) {
                    console.log("befor edit cell", rowid, cellname, value, iRow, iCol);
                },
                beforeSaveCell: function (rowid, cellname, value, iRow, iCol) {
                    console.log("befor save cell", rowid, cellname, value, iRow, iCol);
                },
                afterSaveCell: function (rowid, name, val, iRow, iCol) {
                    var grid = $('#list_hinhanhnoisoi');
                    var rows = grid.jqGrid('getDataIDs');
                    var sttmoi, sttcu;

                    sttmoi = parseInt(grid.jqGrid('getRowData', iRow).STT);
                    sttcu = parseInt(grid.jqGrid('getRowData', iRow).STT_AUTO);

                    var macdha = $("#macdha").val();
                    var sophieu = $("#sophieu").val();
                    var noitru = $("#noitru").val();
                    var makhambenh = $("#makhambenh").val();
                    var sttbenhan = $("#sttbenhan").val();
                    var sttdotdieutri = $("#sttdotdieutri").val();
                    var sttdieutri = $("#sttdieutri").val();
                    if (sophieu != "" && macdha != "") {
                        $.post("sieuam_update_stt_hinhanh", {
                            sophieu: sophieu,
                            macdha: macdha,
                            dvtt: "${Sess_DVTT}",
                            sttcu: sttcu,
                            sttmoi: sttmoi,
                            noitru: noitru,
                            sttbenhan: sttbenhan,
                            sttdotdieutri: sttdotdieutri,
                            sttdieutri: sttdieutri,
                            makhambenh: makhambenh
                        })
                            .done(function () {
                                var arr = [sophieu, "${Sess_DVTT}", macdha, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                                var url = 'sieuam_danhsach_hinhanh?url=' + convertArray(arr);
                                $("#list_hinhanhnoisoi").jqGrid('setGridParam', {
                                    datatype: 'json',
                                    url: url
                                }).trigger('reloadGrid');
                            });
                    }


                },
                ondblClickRow: function (id) {
                    var rowSelect = $("#list_hinhanhnoisoi").jqGrid('getGridParam', 'selarrrow').includes(id);
                    $("#list_hinhanhnoisoi").jqGrid().setSelection(id, !rowSelect);
                },
                loadComplete: function () {
                    var i = 0, indexes = this.p._index, localdata = this.p.data,
                        rows = this.rows, rowsCount = rows.length, row, rowid, rowData, className;
                    for (; i < rowsCount; i++) {
                        row = rows[i];
                        className = row.className;
                        //if ($(row).hasClass('jqgrow')) { // test for standard row
                        if (className.indexOf('jqgrow') !== -1) {
                            rowid = row.id;
                            rowData = localdata[indexes[rowid]];
                            if (rowData.CHONIN == "1") {
                                $("#list_hinhanhnoisoi").jqGrid().setSelection(rowid, true);
                            }
                        }
                    }
                }
            });

            function imageFormat(cellvalue, options, rowObject) {
                return '<img src="' + cellvalue + '" width="100px" height="80px" />';
            }

            $("#lammoi").click(function (evt) {
                reload_grid();
            });
            $("#lammoi1").click(function (evt) {
                reload_grid_dsbn_cd();
            });
            $("#list_thuocdichvu").jqGrid({
                url: 'chitiettoathuocngoatru?matt=' + matoathuoc + "&nghiepvu=ngoaitru_toaxquang&dvtt=${Sess_DVTT}",
                datatype: "local",
                loadonce: true,
                height: 230,
                width: 300,
                colNames: ["stt_toathuoc", "Tên thương mại", "DVT", "Số lượng", "", "", "", "", ""
                ],
                colModel: [
                    {name: 'STT_TOATHUOC', index: 'STT_TOATHUOC', hidden: true},
                    {name: 'TEN_VAT_TU', index: 'TEN_VAT_TU', width: 80},
                    {name: 'DVT', index: 'DVT', width: 60},
                    {
                        name: "SO_LUONG",
                        index: "SO_LUONG",
                        align: 'center',
                        width: 45,
                        edittype: 'custom',
                        editoptions: {custom_element: myelem, custom_value: myvalue}
                    },
                    {name: 'THANHTIEN_THUOC', index: 'THANHTIEN_THUOC', hidden: true},
                    {name: 'MAVATTU', index: 'MAVATTU', hidden: true},
                    {name: 'MAKHOVATTU', index: 'MAKHOVATTU', hidden: true},
                    {name: 'DONGIA_BAN_BV', index: 'DONGIA_BAN_BV', hidden: true},
                    {name: 'DONGIA_BAN_BH', index: 'DONGIA_BAN_BH', hidden: true}
                ],
                //viewrecords: true,
                rowNum: 1000000,
                //multiselect: true,
                caption: "Toa Siêu Âm"
            });

            function myelem(value, options) {
                var el = document.createElement("input");
                el.type = "text";
                el.value = value;
                el.onkeypress = function (e) {
                    var theEvent = e || window.event;
                    var key = theEvent.keyCode || theEvent.which;
                    key = String.fromCharCode(key);
                    var regex = /[0-9]|\./;
                    if (!regex.test(key)) {
                        theEvent.returnValue = false;
                        if (theEvent.preventDefault)
                            theEvent.preventDefault();
                    }
                };
                return el;
            }

            function myvalue(elem, operation, value) {
                if (operation === 'get') {
                    return $(elem).val();
                } else if (operation === 'set') {
                    $('input', elem).val(value);
                }
            }

            function clear_thuoc_dv() {
                $("#thuocdichvu_tt_div input[type='text']").val("");
                $("#thuocdichvu_tt_div input[type='hidden']").val("");
            }

            function themNoiDung(ma_checkbox) {
                var sophieu = $("#sophieu").val();
                var dvtt = "${Sess_DVTT}";
                var ma_cdha = $("#macdha").val();
                var noitru = $("#noitru").val();
                var arr = [
                    sophieu, dvtt, ma_checkbox, ma_cdha, noitru, sovaovien, sovaovien_noi, sovaovien_dt_noi, 0
                ];
                var url = "laynoidung_thaydoi_noisoi";
                $.post(url, {url: convertArray(arr)}).done(function (data) {
                    //console.log(data);
                    var noidung_replace = data[0].NOIDUNG_THAYDOI;
                    var noidung_cu = data[0].NOIDUNGCU;
                    var ketluan_replace = data[0].KETLUAN_THAYDOI;
                    var ketluan_cu = data[0].KETLUANCU;
                    ketluan_cu = ketluan_cu.replaceAll('! ', '\n\n');
                    if (ketluan_replace != null) {
                        ketluan_replace = ketluan_replace.replaceAll('! ', '');
                    }
                    capNhatKetQua(noidung_cu, noidung_replace);
                    //CKEDITOR.instances.ketqua.setData(CKEDITOR.instances.ketqua.getData().replace(noidung_cu,noidung_replace));
                    if (ketluan_replace != null) {
                        var kl = CKEDITOR.instances.ketluan.getData().replace(ketluan_cu, ketluan_replace);
                        CKEDITOR.instances.ketluan.setData(kl);
                    }
                });

            };

            function xoaNoiDung(ma_checkbox) {
                var sophieu = $("#sophieu").val();
                var dvtt = "${Sess_DVTT}";
                var ma_cdha = $("#macdha").val();
                var noitru = $("#noitru").val();
                var arr = [
                    sophieu, dvtt, ma_checkbox, ma_cdha, noitru, sovaovien, sovaovien_noi, sovaovien_dt_noi, 0
                ];
                var url = "xoanoidung_thaydoi_noisoi";
                $.post(url, {url: convertArray(arr)}).done(function (data) {
                    var noidung_replace = data[0].NOIDUNG_THAYDOI;
                    var noidung_cu = data[0].NOIDUNGCU;
                    var ketluan_replace = data[0].KETLUAN_THAYDOI;
                    var ketluan_cu = data[0].KETLUANCU;
                    ketluan_cu = ketluan_cu.replaceAll('! ', '\n\n');
                    capNhatKetQua(noidung_cu, noidung_replace);
                    //var ketluan_cu= CKEDITOR.instances.ketluan.getData();
//                        CKEDITOR.instances.ketqua.setData(CKEDITOR.instances.ketqua.getData().replace(noidung_cu,noidung_replace));
                    CKEDITOR.instances.ketluan.setData(CKEDITOR.instances.ketluan.getData().replace(ketluan_cu, ketluan_replace));
                    var makhambenh = $("#makhambenh").val();
                    var noitru = $("#noitru").val();
                    var sttbenhan = $("#sttbenhan").val();
                    var sttdotdieutri = $("#sttdotdieutri").val();
                    var sttdieutri = $("#sttdieutri").val();
                    var macdha = $("#macdha").val();
                    var ketqua = CKEDITOR.instances.ketqua.getData();
                    var ketluan = CKEDITOR.instances.ketluan.getData();
                    //var ketluan = $("#ketluan").val();
                    var bacsichidinh = $("#bacsichidinh").val();
                    var bacsithuchien = $("#bacsisieuam").val();
                    var chandoan = $("#chandoan").val();
                    var loidanbacsi = $("#loidanbacsi").val();
                    var nguoithuchien = "${Sess_UserID}";
                    //var mausieuam = $("#mausieuam option:selected").text();
                    var ngaygioth_ct = "";
                    //if($("#ngayth_ct").val() != '' && $("#gioth_ct").val() != ''){
                    // ngaygioth_ct = convertStr_MysqlDate($("#ngayth_ct").val())+" "+$("#gioth_ct").val();
                    // }
                    var maunoisoi = $("#maunoisoi").val();
                    $.post("capnhatketqua_noisoi_checkbox", {
                        sophieu: sophieu,
                        macdha: macdha,
                        dvtt: dvtt,
                        ketqua: ketqua,
                        ketluan: ketluan,
                        noitru: noitru,
                        sttbenhan: sttbenhan,
                        sttdotdieutri: sttdotdieutri,
                        sttdieutri: sttdieutri,
                        makhambenh: makhambenh,
                        sovaovien: sovaovien,
                        sovaovien_noi: sovaovien_noi,
                        sovaovien_dt_noi: sovaovien_dt_noi
                    })
                        .done(function () {
                            reload_grid();
                        });
                });
            };

            function capNhatKetQua(noidung_cu, noidung_replace) {
                var kqBanDau = CKEDITOR.instances.ketqua.getData();
                var tcq = layNoiDungCoquan();

                var pos = kqBanDau.search(tcq);

                if (pos == -1) {
                    CKEDITOR.instances.ketqua.setData(kqBanDau.replace(noidung_cu, noidung_replace));
                } else {
                    var kqThayDoi = kqBanDau.substr(0, pos) + kqBanDau.substr(pos).replace(noidung_cu, noidung_replace);
                    CKEDITOR.instances.ketqua.setData(kqThayDoi);
                }
            }

            function layNoiDungCoquan() {

                return CKEDITOR.instances.temp.getData().replace('<p>', '').replace('</p>', "").trim();
            }

            function setTenCoquanTemp(tab) {
                CKEDITOR.instances.temp.setData($(tab).find('.ui-tabs-active').text().toUpperCase());
            }

            $("#tenthuongmai_dv").combogrid({
                url: "noitru_load_tonkhohientai?makhovt=" + $("#kho_dv").val() + "&dvtt=${Sess_DVTT}",
                debug: true,
                width: "700px",
                //replaceNull: true,
                colModel: [
                    {'columnName': 'MAVATTU', 'label': 'mavattu', hidden: true},
                    {'columnName': 'TENVATTU', 'width': '40', 'label': 'Tên vật tư', 'align': 'left'},
                    {'columnName': 'DVT', 'label': 'dvt', hidden: true},
                    {'columnName': 'SOLUONG', 'width': '10', 'label': 'Số lượng', 'align': 'right'},
                ],
                select: function (event, ui) {
                    $("#tenthuongmai_dv").val(ui.item.TENVATTU);
                    $("#mavattu_dv").val(ui.item.MAVATTU);
                    $("#dvt_dv").val(ui.item.DVT);
                    $("#dongia_dv").val(ui.item.DONGIA);
                    //$("#dongia_bv").val(ui.item.dongia);
                    return false;
                }
            });
            $("#tengoc_dv").combogrid({
                url: 'layvattu_theotengoc_theoloai?makhovt=' + $("#kho_dv").val() + '&loaivattu=',
                debug: true,
                width: "700px",
                //replaceNull: true,
                colModel: [{'columnName': 'MAVATTU', 'label': 'mavattu', hidden: true},
                    {'columnName': 'HOATCHAT', 'width': '30', 'label': 'Hoạt chất', 'align': 'left'},
                    {'columnName': 'TENVATTU', 'width': '40', 'label': 'Tên vật tư', 'align': 'left'},
                    {'columnName': 'HAMLUONG', 'width': '20', 'label': 'Hàm lượng', 'align': 'left'},
                    {'columnName': 'DVT', 'label': 'dvt', hidden: true},
                    {'columnName': 'SOLUONG', 'width': '10', 'label': 'Số lượng', 'align': 'right'},
                    {'columnName': 'DONGIA', 'label': 'dongia_bv', hidden: true},
                    {'columnName': 'CACHSUDUNG', 'label': 'cachsudung', hidden: true}
                ],
                select: function (event, ui) {
                    $("#tenthuongmai_dv").val(ui.item.TENVATTU);
                    $("#tengoc_dv").val(ui.item.HOATCHAT);
                    $("#mavattu_dv").val(ui.item.MAVATTU);
                    $("#dvt_dv").val(ui.item.DVT);
                    $("#dongia_dv").val(ui.item.DONGIA);
                    $("#cachdung_dv").val(ui.item.CACHSUDUNG);
                    return false;
                }
            });
            $("#tenthuongmai_dv").keypress(function (evt) {
                if (evt.keyCode === 13 && flag_noitru !== "-1") {
                    $("#soluong_dv").val(1);
                    $("#soluong_dv").focus();
                }
            });
            $("#tengoc_dv").keypress(function (evt) {
                if (evt.keyCode == 13) {
                    $("#soluong_dv").focus();
                }
            });
            //CMU: 26062017
            var dialog_lichsuCHHA =
                new jBox('Modal', {
                    title: "Lịch sử nội soi",
                    overlay: false,
                    content: $('#dialog_lichsuCDHA'),
                    draggable: 'title'
                });
            $("#lichsuCDHA").click(function (evt) {
                //var stt_benhan = $("#sttbenhan").val();
                var mabenhnhan = $("#mabenhnhan").val();
                var hoten = $("#hoten").val();
                if (hoten !== "") {
                    load_lscdha_bn(mabenhnhan);
                    dialog_lichsuCHHA.open();
                } else {
                    jAlert("Vui lòng chọn một bệnh nhân!", "Thông báo");
                }
            });
            $("#list_lichsuCDHA").jqGrid({
                datatype: "local",
                loadonce: true,
                height: 450,
                width: 900,
                colNames: ["Ngày", "Đơn vị", "Chẩn đoán", "Tên nội soi", "Số lượng", "Đơn giá", "Thành tiền", "BHYT không chi", "Kết quả", "Kết luận",
                    "sophieu", "stt_benhan", "Kết quả"],
                colModel: [
                    {name: 'NGAY_THUC_HIEN', index: 'NGAY_THUC_HIEN', width: 55},
                    {name: 'TEN_DONVI', index: 'TEN_DONVI', width: 80},
                    {name: 'CHANDOAN', index: 'CHANDOAN', width: 100},
                    {name: 'TEN_CDHA', index: 'TEN_CDHA'},
                    {name: 'SO_LUONG', index: 'SO_LUONG', width: 100, hidden: true},
                    {
                        name: 'DON_GIA',
                        index: 'DON_GIA',
                        width: 100,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        hidden: true
                    },
                    {
                        name: 'THANH_TIEN',
                        index: 'THANH_TIEN',
                        width: 100,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2},
                        hidden: true
                    },
                    {
                        name: 'BHYTKCHI',
                        index: 'BHYTKCHI',
                        width: 110,
                        align: 'center',
                        formatter: 'checkbox',
                        formatoptions: {value: 'true:false'},
                        hidden: true
                    },
                    {name: 'KET_QUA', index: 'KET_QUA', width: 100, hidden: true},
                    {name: 'MO_TA', index: 'MO_TA', width: 80},
                    {name: 'SO_PHIEU_XN', index: 'SO_PHIEU_XN', width: 100, hidden: true},
                    {name: 'STT_BENHAN', index: 'STT_BENHAN', width: 100, hidden: true},
                    {name: 'KET_QUA', index: 'KET_QUA', width: 150}
                ],
                caption: "Lịch sử nội soi",
                rowNum: 1000
            });
            $("#tab_ls_cdha").tabs();
            //END CMU 26062017
            $("#soluong_dv").keypress(function (evt) {
                if (evt.keyCode == 13 && flag_noitru !== "-1") {
                    var makhovattu = $("#kho_dv").val();
                    makho = -1;
                    makhokhoaphong = 0;
                    var url_kho = "noitru_kiemtra_tututhuoc";
                    $.post(url_kho, {dvtt: "${Sess_DVTT}", maphongban: "${Sess_PhongBan}", makho: makhovattu})
                        .done(function (data) {
                            var tu_tuthuoc = 1;
                            var mavattu = $("#mavattu_dv").val();
                            var tenthuongmai = $("#tenthuongmai_dv").val();
                            var tengoc = " ";
                            var dvt = $("#dvt_dv").val();
                            var soluong = $("#soluong_dv").val();
                            var sl = (soluong !== "") ? parseFloat(soluong) : 0;
                            var dongia_bh = $("#dongia_dv").val();
                            var dongia_bv = dongia_bh;
                            var songay = "1";
                            var sang = "1";
                            var trua = "0";
                            var chieu = "0";
                            var toi = "0";
                            var dangthuoc = " ";
                            var ma_cdha = $("#macdha").val();
                            var ghichu = $("#sophieu").val() + "-" + ma_cdha;
                            var cachdung = " ";
                            var thanhtien = sl * parseFloat(dongia_bh);
                            var sophieu = $("#sophieu").val();

                            if (sl <= 0)
                                jAlert("Số lượng không hợp lệ", 'Cảnh báo', function (r) {
                                    $("#soluong_dv").focus();
                                });
                            else {
                                var arr = ["${Sess_DVTT}", matoathuoc, makhovattu, mavattu, encodeURIComponent(tenthuongmai), encodeURIComponent(tengoc), dvt, "noitru_toadichvu", soluong, soluong, dongia_bv, dongia_bh, thanhtien, songay, sang, trua,
                                    chieu, toi, ghichu, "${Sess_UserID}", dangthuoc, stt_dieutri, stt_benhan, stt_dotdieutri, tu_tuthuoc, cobhyt, mabenhnhan, sophieuthanhtoan, "${Sess_PhongBan}", sovaovien_noi, sovaovien_dt_noi];
                                var url = "noitru_toathuoc_insert";
                                if (flag_noitru !== "1") {   //Ngoại trú
                                    var makhambenh = $("#makhambenh").val();
                                    var idtiepnhan = makhambenh.replace("kb_", "");
                                    var url = "xuatduoc_giamtai_svv";
                                    $.post(url, {
                                        nghiepvu: "ngoaitru_toadichvu",
                                        matoathuoc: matoathuoc,
                                        makhambenh: makhambenh,
                                        xacnhan: "false",
                                        mabenhnhan: mabenhnhan,
                                        ngaykhambenh: ngay_kb
                                    }).done(function (data) {
                                        if (data == "0") {
                                            var arr = [matoathuoc, mavattu, encodeURIComponent(tenthuongmai), encodeURIComponent(tengoc), soluong, dongia_bv, dongia_bh, songay, sang, trua,
                                                chieu, toi, dangthuoc, ghichu, thanhtien, cobhyt, mabenhnhan, sophieuthanhtoan, idtiepnhan, makhambenh, sovaovien, "${Sess_Phong}", "1", "${Sess_Phong}", "0"];
                                            var url = "themtoathuocngoaitru_giamtai?dongia_bv=" + parseFloat(dongia_bv) +
                                                "&dongia_bh=" + parseFloat(dongia_bh) + "&thanhtien=" + parseFloat(thanhtien) +
                                                "&sang=" + sang + "&trua=" + trua + "&chieu=" + chieu + "&toi=" + toi +
                                                "&url=" + convertArray(arr) + "&nghiepvu=ngoaitru_toadichvu" +
                                                "&kho=" + makhovattu + "&ngaykb=" + ngay_kb;
                                            if (sophieu) {
                                                url += "&sophieu=" + sophieu;
                                            }
                                            if (ma_cdha) {
                                                url += "&ma_cdha=" + ma_cdha;
                                            }
                                            $.ajax({
                                                url: url
                                            }).done(function (data) {
                                                if (data === "4") {
                                                    jAlert("Bệnh nhân đã thanh toán rồi", 'Cảnh báo');
                                                    var url = "xuatduoc_giamtai_svv";
                                                    $.post(url, {
                                                        nghiepvu: "ngoaitru_toadichvu",
                                                        matoathuoc: matoathuoc,
                                                        makhambenh: makhambenh,
                                                        xacnhan: "true",
                                                        mabenhnhan: mabenhnhan,
                                                        ngaykhambenh: ngay_kb
                                                    }).done(function (data) {
                                                    });
                                                } else if (data === "5") {
                                                    jAlert("Bệnh nhân đã xuất thuốc rồi", 'Cảnh báo');
                                                    var url = "xuatduoc_giamtai_svv";
                                                    $.post(url, {
                                                        nghiepvu: "ngoaitru_toadichvu",
                                                        matoathuoc: matoathuoc,
                                                        makhambenh: makhambenh,
                                                        xacnhan: "true",
                                                        mabenhnhan: mabenhnhan,
                                                        ngaykhambenh: ngay_kb
                                                    }).done(function (data) {
                                                    });
                                                } else if (data === "3") {
                                                    jAlert("Thuốc đã có trong toa", 'Cảnh báo');
                                                    var url = "xuatduoc_giamtai_svv";
                                                    $.post(url, {
                                                        nghiepvu: "ngoaitru_toadichvu",
                                                        matoathuoc: matoathuoc,
                                                        makhambenh: makhambenh,
                                                        xacnhan: "true",
                                                        mabenhnhan: mabenhnhan,
                                                        ngaykhambenh: ngay_kb
                                                    }).done(function (data) {
                                                    });
                                                } else if (data === "6") {
                                                    jAlert("Số lượng thuốc vượt số lượng tồn kho", 'Cảnh báo');
                                                    var url = "xuatduoc_giamtai_svv";
                                                    $.post(url, {
                                                        nghiepvu: "ngoaitru_toadichvu",
                                                        matoathuoc: matoathuoc,
                                                        makhambenh: makhambenh,
                                                        xacnhan: "true",
                                                        mabenhnhan: mabenhnhan,
                                                        ngaykhambenh: ngay_kb
                                                    }).done(function (data) {
                                                    });
                                                } else if (data == '100') {
                                                    jAlert("Đã chốt báo cáo dược, không thể xóa/sửa", 'Cảnh báo');
                                                    var url = "xuatduoc_giamtai_svv";
                                                    $.post(url, {
                                                        nghiepvu: "ngoaitru_toadichvu",
                                                        matoathuoc: matoathuoc,
                                                        makhambenh: makhambenh,
                                                        xacnhan: "true",
                                                        mabenhnhan: mabenhnhan,
                                                        ngaykhambenh: ngay_kb
                                                    }).done(function (data) {
                                                    });
                                                } else {
                                                    var url = 'chitiettoathuocngoatru_svv?matt=' + matoathuoc + "&nghiepvu=ngoaitru_toadichvu&dvtt=${Sess_DVTT}" + "&sovaovien=" + sovaovien + "&ma_cdha=" + ma_cdha + "&sophieu=" + sophieu;
                                                    $("#list_thuocdichvu").jqGrid('setGridParam', {
                                                        datatype: 'json',
                                                        url: url
                                                    }).trigger('reloadGrid');
                                                    var url = "xuatduoc_giamtai_svv";
                                                    $.post(url, {
                                                        nghiepvu: "ngoaitru_toadichvu",
                                                        matoathuoc: matoathuoc,
                                                        makhambenh: makhambenh,
                                                        xacnhan: "true",
                                                        mabenhnhan: mabenhnhan,
                                                        ngaykhambenh: ngay_kb
                                                    }).done(function (data) {

                                                    });
                                                }
                                                clear_thuoc_dv();
                                                $("#tenthuongmai_dv").focus();
                                            });
                                        }
                                    });
                                } else {
                                    url += "?url=" + convertArray(arr);
                                    var sophieu = $("#sophieu").val();
                                    if (sophieu) {
                                        url += "&sophieu=" + sophieu;
                                    }
                                    var ma_cdha = $("#macdha").val();
                                    if (ma_cdha) {
                                        url += "&ma_cdha=" + ma_cdha;
                                    }
                                    $.post(url).done(function (data) {
                                        if (data === "4") {
                                            jAlert("Bệnh nhân đã thanh toán rồi", 'Cảnh báo');
                                        } else if (data === "5") {
                                            jAlert("Bệnh nhân đã xuất thuốc rồi", 'Cảnh báo');
                                        } else if (data === "3") {
                                            jAlert("Thuốc đã có trong toa", 'Cảnh báo');
                                        } else if (data === "6") {
                                            jAlert("Số lượng thuốc vượt số lượng tồn kho", 'Cảnh báo');
                                        } else {
                                            load_cttoathuoc("noitru_toadichvu", "list_thuocdichvu");
                                        }
                                        clear_thuoc_dv();
                                        $("#tenthuongmai_dv").focus();
                                    });
                                }
                            }
                        });
                }

            });
            $("#thuocdichvu_div").keyup(function (evt) {
                if (evt.keyCode === 46 && flag_noitru !== "-1") {
                    if (flag_noitru === "1")
                        delete_thuocnoitru("list_thuocdichvu", "noitru_toadichvu");
                    else
                        jConfirm('Bạn có muốn xóa thuốc?', 'Thông báo', function (r) {
                            if (r.toString() === "true") {
                                delete_toathuocngoaitru("list_thuocdichvu", "ngoaitru_toadichvu");
                            }
                        });
                }
            });

            /*$("#kho_dv_noi").change(function (evt) {
                    $("#tenthuongmai_dv").combogrid("option", "url", 'layvattu?makhovt=' + $("#kho_dv_noi").val());
                    $("#tengoc_dv").combogrid("option", "url", 'layvattu_theotengoc?makhovt=' + $("#kho_dv_noi").val());
                });

                $("#kho_dv_ngoai").change(function (evt) {
                    $("#tenthuongmai_dv").combogrid("option", "url", 'layvattu?makhovt=' + $("#kho_dv_ngoai").val());
                    $("#tengoc_dv").combogrid("option", "url", 'layvattu_theotengoc?makhovt=' + $("#kho_dv_ngoai").val());
                });*/
            function decodeHTMLEntities(str) {
                if (str && typeof str === 'string') {
                    // strip script/html tags
                    str = str.replace(/<script[^>]*>([\S\s]*?)<\/script>/gmi, '');
                    str = str.replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gmi, '');
                    //element.innerHTML = str;
                    //str = element.textContent;
                    //element.textContent = '';
                }

                return str;
            }

            function canhbaosolanthuchien() {
                if (Number("${ts_93205}")>0)
                {
                    var today  = new Date();
                    var d = today .getDate();
                    var m = today .getMonth() + 1; //Month from 0 to 11
                    var y = today .getFullYear();
                    //today = '' + (d <= 9 ? '0' + d : d) + '/' + (m<=9 ? '0' + m : m) + '/' + y;
                    today = y+'-'+(m<=9 ? '0' + m : m)+'-'+(d <= 9 ? '0' + d : d);
                    //qti_CanhBaoSoLuotKhamBacSiTrongNgay();
                    var arr1 = [today,'NS',0]
                    var url1 = "hgi_get_solan_thuchien_cdha_trongngay?url=" + convertArray(arr1);
                    $.ajax({
                        url: url1,
                        async: false,
                    }).done(function (data) {
                        if (Number(data) >= Number("${ts_93206}"))
                        {

                            alert("Tài khoản  " + "${Sess_User}" + " đã thực hiện của ngày "+ today +" là: " + data + " lượt ( Số lượt được thực hiện trong ngày phải không quá " + "${ts_93206}" + " lượt)");
                        }


                    });
                }

            }
            loadBacSiTheoKhoa(SESS_PHONG_BAN);
            $("#luu_tt").click(function (evt) {
                var ketQuaKiemTra = THAMSO_828449=="1"?kiemTraThoiGianHopLe():"1";
                if(ketQuaKiemTra!="1"){
                    jAlert(ketQuaKiemTra, 'Thông báo');
                    return false;
                }
                if(!$("#ma_pp_vo_cam").val()){
                    jAlert("Vui lòng chọn mã phương pháp vô cảm", 'Thông báo');
                    return false;
                }
                if (checkNguoiDocKetQua()) {
                    jAlert("Người đọc kết quả không hợp lệ", "Cảnh báo");
                    return false;
                }
                if ($("#nguoithuchien").val() != "0" && $("#nguoithuchien").val() != "${Sess_UserID}" && $("#nguoithuchien").val().trim() != "" && $("#dathuchien").prop("checked") == true) {
                    jAlert("Bạn không thể chỉnh sửa KQ nội soi của nhân viên khác!", 'Thông báo');
                } else if ("${nhapketluan_cls}" == "1" && CKEDITOR.instances.ketluan.getData() == "") { // 20171102 NAN VINHVT HISHD-21197 ADD
                    jAlert("Chưa nhập kết luận!", 'Cảnh báo');
                }
                else {
                    var ngayChiDinh = $('#ngaychidinh_kq').val();
                    var gioChiDinh = $('#giochidinh_kq').val();
                    var thoiGianChiDinh = ngayChiDinh + ' ' + gioChiDinh
                    var thoiGianTHYL = $('#thoiGianBatDau_cls').val();
                    var thoiGianKQ = convertStr_MysqlDate($('#ngaygioth_ct').val());

                    var momentChiDinh = moment(thoiGianChiDinh, ['DD/MM/YYYY HH:mm:ss']);
                    var momentThucHienYLenh = moment(thoiGianTHYL, ['DD/MM/YYYY HH:mm:ss']);
                    var momentKetQua = moment(thoiGianKQ, ['DD/MM/YYYY HH:mm:ss']);

                    var soTheBHYT = $('#sothebhyt_ct').val();

                    if (soTheBHYT !== '') {
                        if(momentThucHienYLenh.diff(momentChiDinh, 'minutes') < 1){
                            jAlert('THỜI GIAN THỰC HIỆN Y LỆNH : '+thoiGianTHYL+'<br> KHÔNG ĐƯỢC NHỎ HƠN HOẶC BẰNG'+'<br>THỜI GIAN CHỈ ĐỊNH : ' + thoiGianChiDinh , 'Thông báo');
                            return;
                        }

                        if(momentKetQua.diff(momentThucHienYLenh, 'minutes') < 1){
                            jAlert('THỜI GIAN KẾT QUẢ : '+thoiGianKQ+'<br> KHÔNG ĐƯỢC NHỎ HƠN HOẶC BẰNG'+'<br>THỜI GIAN THỰC HIỆN Y LỆNH : ' + thoiGianTHYL , 'Thông báo');
                            return;
                        }

                        if(momentKetQua.diff(momentThucHienYLenh, 'minutes') < 5 && THAMSO_960626 != '0'){
                            jAlert('THỜI GIAN THỰC HIỆN Y LỆNH: '+thoiGianTHYL+'<br> ĐẾN GIỜ '+'<br>THỜI GIAN KẾT QUẢ : ' + thoiGianKQ + " KHÔNG ĐƯỢC NHỎ HƠN 5 PHÚT", 'Thông báo');
                            return;
                        }
                        if(THAMSO_960623 == "1" && typeof cmuKiemtratungThoigianCLSTheoNV == 'function' &&  !cmuKiemtratungThoigianCLSTheoNV({
                            dvtt: "${Sess_DVTT}",
                            sovaovien: $("#noitru").val()==1?sovaovien_noi:sovaovien,
                            userId: "${Sess_UserID}",
                            thoigianbd: momentThucHienYLenh.format('DD/MM/YYYY HH:mm'),
                            thoigiankt: momentKetQua.format('DD/MM/YYYY HH:mm'),
                            loaikythuat: "NS"
                        })) {
                            return false;
                        }
                    }

                    var sophieu = $("#sophieu").val();
                    var noitru = $("#noitru").val();
                    var makhambenh = $("#makhambenh").val();
                    var sttbenhan = $("#sttbenhan").val();
                    var sttdotdieutri = $("#sttdotdieutri").val();
                    var sttdieutri = $("#sttdieutri").val();
                    var macdha = $("#macdha").val();
                    var dvtt = "${Sess_DVTT}";
                    var ketqua = CKEDITOR.instances.ketqua.getData();
                    //var ketluan = $("#ketluan").val();
                    var a = CKEDITOR.instances.ketluan.getData();
                    var b = a.replace("<p>", "");
                    var ketluan = b.replace("</p>", "").trim();
                    // chỉnh lỗi font trong xml4
                    var mota_xml5 = $('<textarea />').html(ketluan).text().replace(/<[^>]+>/g, '');
                    var kl_xml5 = $('<textarea />').html(ketqua).text();
                    var ketqua_xml5 = decodeHTMLEntities(kl_xml5);
                    var ngaygioth_ct="";
                    var ngayKtThucHien = "";
                    var gioKtThucHien = "";
                    if("${thamso_184002}"=="1" && $("#ngayth_ct").val() != '' && $("#gioth_ct").val() != ''){
                        ngaygioth_ct = convertStr_MysqlDate($("#ngayth_ct").val()) + " " + $("#gioth_ct").val();
                        ngayKtThucHien = convertStr_MysqlDate($("#ngayKtThucHienCdha").val());
                        gioKtThucHien = $("#gioKtThucHienCdha").val();
                    }
                    //
                    var bacsichidinh = $("#bacsichidinh").val();
                    var bacsithuchien = "${Sess_User}";
                    var chandoan = $("#chandoan").val();
                    //var maunoisoi = $("#maunoisoi option:selected").text();
                    var maunoisoi = $("#maunoisoi").val();
                    var nguoithuchien = "${Sess_UserID}";
                    var lydonhapvien = $("#lydonhapvien").val();
                    var tiensubenh = $("#tiensubenh").val();
                    var may = $("#may").val();
                    var thuoc = $("#thuoc").val();
                    var denghi = $("#denghi").val();
                    var duongvao = $("#duongvao").val();
                    var ns_chandoan = $("#nschandoan").prop('checked');
                    ns_chandoan = (ns_chandoan.toString() == "true") ? 1 : 0;
                    var ns_dieutri = $("#nsdieutri").prop('checked');
                    ns_dieutri = (ns_dieutri.toString() == "true") ? 1 : 0;
                    var capcuu = $("#capcuu").prop('checked');
                    capcuu = (capcuu.toString() == "true") ? 1 : 0;
                    var ns_laydivat = $("#nslaydivat").prop('checked');
                    ns_laydivat = (ns_laydivat.toString() == "true") ? 1 : 0;
                    var sinhthiet = $("#sinhthiet").prop('checked');
                    sinhthiet = (sinhthiet.toString() == "true") ? 1 : 0;
                    var clotest = $("#clotest").prop('checked');
                    clotest = (clotest.toString() == "true") ? 1 : 0;
                    var giauphaubenh = $("#giaiphaubenh").prop('checked');
                    giauphaubenh = (giauphaubenh.toString() == "true") ? 1 : 0;
                    if (maunoisoi == "" || maunoisoi == null) {
                        //jAlert("Chưa chọn mẫu nội soi", 'Cảnh báo');
                        maunoisoi = "-1";
                    }
                    var maBenhLyTruocCdha = $("#mabenhly_truoccdha").val();
                    var maBenhLySauCdha = $("#mabenhly_saucdha").val();
                    var chanDoanTruocCdha = $("#chandoan_truoccdha").val();
                    var chanDoanSauCdha = $("#chandoan_saucdha").val();
                    // $("#ngaygioth_ct").change();//stop timer
                    $("#thoiGianBatDau_cls").change();//stop timer
                    var thoiGianBatDauCls = $("#thoiGianBatDau_cls").val();
                    //-----HPG
                    luuNguoiDocKetQua(sophieu, noitru==1?sovaovien_noi:sovaovien, sovaovien_dt_noi, noitru, macdha);
                    if ("${suaketqua_cls}" == "1") { //----khong cho phep sua ket qua
                        var arr1 = [makhambenh, sophieu, "${Sess_DVTT}", noitru, sttbenhan, sttdotdieutri, sttdieutri, 0]
                        var url1 = "hpg_noisoi_trangthai_thanhtoan?url=" + convertArray(arr1);
                        $.ajax({
                            url: url1
                        }).done(function (data) {
                            if (data == "2") {
                                jAlert("Bệnh nhân đã thanh toán viện phí. Không được thay đổi kết quả.", 'Cảnh báo');
                                return;
                            } else {//--Cho phep sua
                                if (maunoisoi == "") {
                                    jAlert("Chưa chọn mẫu nội soi", 'Cảnh báo');
                                } else if (macdha == "") {
                                    jAlert("Chưa chọn nội soi để thực hiện", 'Cảnh báo');
                                } else if (CKEDITOR.instances.ketqua.getData() == "") {
                                    jAlert("Kết quả không được phép rỗng");
                                } else if (sophieu != "") {
                                    var ktrathoigian = $.ajax({type: "POST", url: "cmu_post", async: false, //Chỉ định CLS Viện Phí lấy giá trên grid. cho phép sửa giá
                                        data: {url: ["${Sess_DVTT}", $("#sophieu").val(),ngaygioth_ct,
                                                sovaovien_noi == 0? sovaovien: sovaovien_noi, noitru,'CMU_KTRATHOI_CDVAKQ'].join('```')}
                                    }).responseText;
                                    if(ktrathoigian == 1) {
                                        jAlert("Thời gian thực hiện không được dưới 5 phút", 'Cảnh báo');
                                        return false;
                                    }
                                    $.post("capnhatketqua_noisoi_svv", {
                                        sophieu: sophieu,
                                        macdha: macdha,
                                        dvtt: dvtt,
                                        ketqua: ketqua,
                                        ketluan: ketluan,
                                        bacsichidinh: bacsichidinh,
                                        bacsithuchien: bacsithuchien,
                                        chandoan: chandoan,
                                        maunoisoi: maunoisoi,
                                        lydonhapvien: lydonhapvien,
                                        tiensubenh: tiensubenh,
                                        may: may,
                                        thuoc: thuoc,
                                        denghi: denghi,
                                        ns_chandoan: ns_chandoan,
                                        ns_dieutri: ns_dieutri,
                                        capcuu: capcuu,
                                        ns_laydivat: ns_laydivat,
                                        sinhthiet: sinhthiet,
                                        clotest: clotest,
                                        giauphaubenh: giauphaubenh,
                                        noitru: noitru,
                                        sttbenhan: sttbenhan,
                                        sttdotdieutri: sttdotdieutri,
                                        sttdieutri: sttdieutri,
                                        makhambenh: makhambenh,
                                        sovaovien: sovaovien,
                                        sovaovien_noi: sovaovien_noi,
                                        sovaovien_dt_noi: sovaovien_dt_noi,
                                        nguoithuchien: nguoithuchien,
                                        // ngaythuchien:  convertStr_MysqlDate($("#ngaygioth_ct").val()),
                                        duongvao: duongvao,
                                        mota_xml5: mota_xml5, ketqua_xml5: ketqua_xml5,
                                        ngaygioth_ct: thoiGianKQ,
                                        ngayKtThucHien: ngayKtThucHien,
                                        gioKtThucHien: gioKtThucHien,
                                        maBenhLyTruocCdha: maBenhLyTruocCdha,
                                        maBenhLySauCdha: maBenhLySauCdha,
                                        chanDoanTruocCdha: chanDoanTruocCdha,
                                        chanDoanSauCdha: chanDoanSauCdha
                                        ,thoiGianBatDauCls: thoiGianBatDauCls
                                        ,maIcd:  $("#icdChanDoanCanLamSang").val(),
                                        tenIcd:  $("#tenChanDoanCanLamSang").val(),
                                        maBenhLy:  $("#maBenhLyChanDoanCanLamSang").val()
                                    })
                                        .done(function (datares) {
                                            if (datares == -1) {
                                                jAlert("Dữ liệu bệnh nhân đã khóa không thể chỉnh sửa, vui lòng liên hệ admin.", 'Cảnh báo');
                                                return false;
                                            }
                                            canhbaosolanthuchien();
                                            reload_grid();
                                            $.post("cmu_post", {
                                                url: [
                                                    dvtt,
                                                    sophieu,
                                                    macdha,
                                                    sovaovien_noi == 0? sovaovien: sovaovien_noi,
                                                    sovaovien_dt_noi,
                                                    noitru,
                                                    $("#ma_pp_vo_cam").val(),
                                                    "CMU_NOISOI_UPDATE_PPVC"
                                                ].join("```")
                                            })
                                            jAlert("Cập nhật thành công", 'Thông báo');
                                            if (parseInt($("#luuinphieu_status").val()) === 1) {
                                                $("#inphieu_noisoi").click();
                                            }
                                            $("#luuinphieu_status").val('0');
                                        });
                                }
                            }
                        });
                    } else {  //----- cho phep sua binh thuong
                        if (macdha == "") {
                            jAlert("Chưa chọn nội soi để thực hiện", 'Cảnh báo');
                        } else if (CKEDITOR.instances.ketqua.getData() == "") {
                            jAlert("Kết quả không được phép rỗng");
                        } else if (sophieu != "") {
                            var ktrathoigian = $.ajax({type: "POST", url: "cmu_post", async: false, //Chỉ định CLS Viện Phí lấy giá trên grid. cho phép sửa giá
                                data: {url: ["${Sess_DVTT}", $("#sophieu").val(),ngaygioth_ct,
                                        sovaovien_noi == 0? sovaovien: sovaovien_noi, noitru,'CMU_KTRATHOI_CDVAKQ'].join('```')}
                            }).responseText;
                            if(ktrathoigian == 1) {
                                jAlert("Thời gian thực hiện không được dưới 5 phút", 'Cảnh báo');
                                return false;
                            }
                            $.post("capnhatketqua_noisoi_svv", {
                                sophieu: sophieu,
                                macdha: macdha,
                                dvtt: dvtt,
                                ketqua: ketqua,
                                ketluan: ketluan,
                                bacsichidinh: bacsichidinh,
                                bacsithuchien: bacsithuchien,
                                chandoan: chandoan,
                                maunoisoi: maunoisoi,
                                lydonhapvien: lydonhapvien,
                                tiensubenh: tiensubenh,
                                may: may,
                                thuoc: thuoc,
                                denghi: denghi,
                                ns_chandoan: ns_chandoan,
                                ns_dieutri: ns_dieutri,
                                capcuu: capcuu,
                                ns_laydivat: ns_laydivat,
                                sinhthiet: sinhthiet,
                                clotest: clotest,
                                giauphaubenh: giauphaubenh,
                                noitru: noitru,
                                sttbenhan: sttbenhan,
                                sttdotdieutri: sttdotdieutri,
                                sttdieutri: sttdieutri,
                                makhambenh: makhambenh,
                                sovaovien: sovaovien,
                                sovaovien_noi: sovaovien_noi,
                                sovaovien_dt_noi: sovaovien_dt_noi,
                                nguoithuchien: nguoithuchien,
                                // ngaythuchien: convertStr_MysqlDate($("#ngaygioth_ct").val()),
                                duongvao: duongvao,
                                mota_xml5: mota_xml5, ketqua_xml5: ketqua_xml5,
                                ngaygioth_ct: thoiGianKQ,
                                ngayKtThucHien: ngayKtThucHien,
                                gioKtThucHien: gioKtThucHien,
                                maBenhLyTruocCdha: maBenhLyTruocCdha,
                                maBenhLySauCdha: maBenhLySauCdha,
                                chanDoanTruocCdha: chanDoanTruocCdha,
                                chanDoanSauCdha: chanDoanSauCdha
                                ,thoiGianBatDauCls: thoiGianBatDauCls
                                ,maIcd:  $("#icdChanDoanCanLamSang").val(),
                                tenIcd:  $("#tenChanDoanCanLamSang").val(),
                                maBenhLy:  $("#maBenhLyChanDoanCanLamSang").val()
                            })
                                .done(function (datares) {
                                    if (datares == -1) {
                                        jAlert("Dữ liệu bệnh nhân đã khóa không thể chỉnh sửa, vui lòng liên hệ admin.", 'Cảnh báo');
                                        return false;
                                    }
                                    canhbaosolanthuchien();
                                    reload_grid();
                                    jAlert("Cập nhật thành công", 'Thông báo');
                                    $.post("cmu_post", {
                                        url: [
                                            dvtt,
                                            sophieu,
                                            macdha,
                                            sovaovien_noi == 0? sovaovien: sovaovien_noi,
                                            sovaovien_dt_noi,
                                            noitru,
                                            $("#ma_pp_vo_cam").val(),
                                            "CMU_NOISOI_UPDATE_PPVC"
                                        ].join("```")
                                    })
                                    if (parseInt($("#luuinphieu_status").val()) === 1) {
                                        $("#inphieu_noisoi").click();
                                    }
                                    $("#luuinphieu_status").val('0');
                                });
                        }
                    } // End ----
                }
            });

            function kiemTraThoiGianHopLe() {
                var objThoiGianChiDinhChiTiet = {name: "Thời gian chỉ định", value: $("#ngaychidinh_kq").val() + " " + $("#giochidinh_kq").val()};
                var objThoiGianThucHienYLenh = {name: "Thời gian thực hiện Y lệnh", value: $("#thoiGianBatDau_cls").val()};
                var objThoiGianKetQua = {name: "Thời gian kết quả", value: $("#ngaygioth_ct").val()||($("#ngayth_ct").val() + " " + $("#gioth_ct").val()) };
                //ThoiGianKetQua > ThoiGianThucHienYLenh > ThoiGianChiDinhChiTiet
                var objCompare = validateAndCompareDatesToMinute(objThoiGianKetQua,objThoiGianThucHienYLenh,objThoiGianChiDinhChiTiet);
                if ((objCompare.errorCode=="-1" || objCompare.errorCode=="-2") && objCompare.objects.length>0) {
                    if(objCompare.errorCode=="-1") {
                        return "Lỗi định dạng " + objCompare.objects[0].name;
                    } else {
                        return "Lỗi " + objCompare.objects[0].name + " phải sau " + objCompare.objects[1].name + " tính đến phút";
                    }
                }
                return "1"; //Hợp lệ
            }
            $("#luuthongtin").click(function (evt) {
                $("#luu_tt").click();
            });
            $("#luufile").click(function (evt) {
                if ($("#makhambenh").val() != "") {
                    var sophieu = $("#sophieu").val();
                    var makhambenh = $("#makhambenh").val();
                    var dvtt = "${Sess_DVTT}";
                    var arr2 = [sophieu, dvtt, makhambenh];
                    var url2 = "cdha_select_files?url=" + convertArray(arr2);
                    $("#list_hinhanh").jqGrid('setGridParam', {datatype: 'json', url: url2}).trigger('reloadGrid');
                    dialog_upload.open();
                }
            });
            $("#inphieu_noisoi").click(function (evt) {
                var mabenhnhan = $("#mabenhnhan").val();
                var hoten = $("#hoten").val();
                var diachi = $("#diachi").val();
                var tuoi = $("#tuoi").val();
                var phai = $("#gioitinh").val();
                if (phai.toString() == "true") {
                    phai = "Nam";
                } else {
                    phai = "Nữ";
                }
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var macdha = $("#macdha").val();
                var sobhyt = $("#sothebhyt").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var ngaybatdau = $("#ngaybatdau").val();
                var ngayketthuc = $("#ngayketthuc").val();
                var noidk = $("#noidk").val();
                var namsinh = $("#namsinh").val();
                if (dvtt == "17013") {
                    var sothebhyt = $("#sothebhyt").val();
                    var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha,
                        dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, sothebhyt, "0", sovaovien
                        , sovaovien_noi
                        , sovaovien_dt_noi, 0];
                    var url = "inphieunoisoi_svv?thongtin=" + convertArray(arr);
                    $(location).attr('href', url);
                } else if (sophieu != "" && macdha != "") {
                    var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha, sobhyt,
                        dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, ngaybatdau, ngayketthuc, noidk, namsinh, "0",
                        sovaovien,
                        sovaovien_noi,
                        sovaovien_dt_noi, 0, $("#_chandoan").val(), $("#bacsinoisoi").val(), 0];
                    var selected = [];
                    $("#list_hinhanhnoisoi").jqGrid('getGridParam', 'selarrrow').forEach(function (data) {
                        var stt = $("#list_hinhanhnoisoi").jqGrid('getCell', data, 'STT');
                        selected.push(stt);
                    });
                    var url = "inketquanoisoi_svv?thongtin=" + convertArray(arr) + "&anh=" + selected;
                    //HPG--- Xem truc tiep bao cao
                    if ("${xemtructiep_bc}" == "1") {
                        var redirectWindow = window.open(url, '_blank');
                        redirectWindow.location;
                        return false;

                    } else
                        //-------End
                        $(location).attr('href', url);
                }
            });

            $("#chuyen_cd_noisoi").click(function (evt) {
                cdha_dialog.dialog('open');
                reloadGridDsCd(flag_noitru, bhytchi, "${Sess_DVTT}", $("#sophieu").val(), phongcdha, "0", sovaovien, sovaovien_noi, sovaovien_dt_noi);
            });
            $("#phieu_cdha_noisoi").click(function (evt) {
                if (noitru_ngoaitru == "0") {
                    var makhambenh = $("#makhambenh").val();
                    var sophieu = $("#sophieu").val();
                    var bhytkhongchi = co_bao_hiem == 1 ? 0 : 1;
                    var arr = [makhambenh, bhytkhongchi, sophieu, "${Sess_DVTT}", "0", "1", noitru_ngoaitru];
                    //Ðo?n mã c?a BDH
                    var hoten = $("#hoten").val();
                    var tuoi = $("#tuoi").val();
                    var phai = $("#gioitinh").val();
                    if (phai.toString() == "true") {
                        phai = "Nam";
                    } else {
                        phai = "Nữ";
                    }
                    var gioitinh = phai;
                    var diachi = $("#diachi").val();
                    var bschidinh = bacsi_chidinh;
                    var sothebaohiem = "";
                    //Ki?m tra b?nh nhân có BHYT
                    if (bhytkhongchi == "0")
                        sothebaohiem = $("#sothebhyt").val();
                    var maphong = phongcdha_ss;
                    if ("${thanhtoannhieunac}" == "1" && bhytkhongchi == "0") {
                        var url_taobk = "taobangke_truocin?makb=" + makhambenh + "&dvtt=" + "${Sess_DVTT}" + "&sophieu=0";
                        $.ajax({
                            url: url_taobk
                        }).done(function (data) {
                            if ("${Sess_DVTT}".indexOf("52") == 0 || "${Sess_DVTT}" == "82023") {
                                var url = "laykyhieubaocaophongcdha?maphongcdha=" + maphong;
                                $.ajax({
                                    url: url
                                }).done(function (data) {
                                    //1:PXQ
                                    if (data == "1" && sophieu != "") {
                                        arr = ["", hoten, diachi, tuoi, gioitinh, makhambenh, sophieu, "0",
                                            "${Sess_DVTT}", "0", "", "", "", "", "", "", sothebaohiem, "", bschidinh, ""];
                                        url = "bdh_inketquaxquang?url=" + convertArray(arr);
                                        $(location).attr('href', url);
                                    } else {
                                        url = "inphieucdha?url=" + convertArray(arr);
                                        $(location).attr('href', url);
                                    }
                                });
                            } else {
                                url = "inphieucdha?url=" + convertArray(arr);
                                var dvtt = "${Sess_DVTT}";
                                if ("${taibaocaovemay}" == "1") {
                                    var redirectWindow = window.open(url, '_blank');
                                    redirectWindow.location;
                                    return false;
                                } else
                                    $(location).attr('href', url);
                            }
                        });
                    } else {
                        if ("${Sess_DVTT}".indexOf("52") == 0 || "${Sess_DVTT}" == "82023") {
                            var url = "laykyhieubaocaophongcdha?maphongcdha=" + maphong;
                            $.ajax({
                                url: url
                            }).done(function (data) {
                                if (data == "1" && sophieu != "") {
                                    arr = ["", hoten, diachi, tuoi, gioitinh, makhambenh, sophieu, "0",
                                        "${Sess_DVTT}", "0", "", "", "", "", "", "", sothebaohiem, "", bschidinh, ""];
                                    url = "bdh_inketquaxquang?url=" + convertArray(arr);
                                    $(location).attr('href', url);
                                } else {
                                    url = "inphieucdha?url=" + convertArray(arr);
                                    $(location).attr('href', url);
                                }
                            });
                        } else {
                            url = "inphieucdha?url=" + convertArray(arr);
                            var dvtt = "${Sess_DVTT}";
                            if ("${taibaocaovemay}" == "1") {
                                var redirectWindow = window.open(url, '_blank');
                                redirectWindow.location;
                                return false;
                            } else
                                $(location).attr('href', url);
                        }
                    }
                }else{
                    var sophieucdha = $("#sophieu").val();
                    var bhytkhongchi = co_bao_hiem == 1 ? 0 : 1;
                    var sobenhantt = sobenhan_noitru_tt;
                    var sobenhan = sobenhan_noitru;
                    var icd_khoadt = icd_benhnhan;
                    var ten_khoadt = ten_icd_benhnhan;
                    if (sobenhan != "")
                        soba = sobenhan;
                    else
                        soba = sobenhantt;
                    var arr = [mabenhnhan, bhytkhongchi, sophieucdha, "${Sess_DVTT}", soba, stt_benhan, stt_dotdieutri, stt_dieutri, icd_khoadt, ten_khoadt, "", "0"
                        , sovaovien_noi, sovaovien_dt_noi, 0, noitru_ngoaitru];
                    var url = "noitru_inphieucdha_svv?url=" + convertArray(arr);
                    $(location).attr('href', url);
                }
            });

            $("#inphieu").click(function (evt) {
                $("#inphieu_noisoi").click();
            });
            $("#luuinphieu").click(function (evt) {
                $("#luuinphieu_status").val("1");
                $("#luu_tt").click();
            });
            reload_grid();
            //$('textarea#ketqua').ckeditor();
            $("#tab_cdha").tabs();
            $("#div_hinhanhnoisoi").keyup(function (evt) {
                if (evt.keyCode == 46) {
                    var id = $("#list_hinhanhnoisoi").jqGrid('getGridParam', 'selrow');
                    if (id) {
                        var noitru = $("#noitru").val();
                        var makhambenh = $("#makhambenh").val();
                        var sttbenhan = $("#sttbenhan").val();
                        var sttdotdieutri = $("#sttdotdieutri").val();
                        var sttdieutri = $("#sttdieutri").val();
                        var ret = $("#list_hinhanhnoisoi").jqGrid('getRowData', id);
                        var arr2 = [ret.SO_PHIEU_CDHA, ret.DVTT, ret.MA_CDHA, ret.STT, noitru];
                        var url2 = "sieuam_xoa_hinhanh?url=" + convertArray(arr2);
                        $.ajax({
                            url: url2
                        }).done(function () {
                            var arr = [ret.SO_PHIEU_CDHA, ret.DVTT, ret.MA_CDHA, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                            var url = 'sieuam_danhsach_hinhanh?url=' + convertArray(arr);
                            $("#list_hinhanhnoisoi").jqGrid('setGridParam', {
                                datatype: 'json',
                                url: url
                            }).trigger('reloadGrid');
                        });
                    }
                }
            });
            $("#maunoisoi").change(function (evt) {
                var xacnhan = "";
                if ("${hienthi_checkbox}" != "1") {
                    xacnhan = true;
                } else {
                    var xacnhan = confirm("Bạn có muốn thay đổi mẫu nội soi?");
                }
                if (xacnhan) {
                    if ("${hienthi_checkbox}" == "1") {
                        var sophieu = $("#sophieu").val();
                        var ma_cdha = $("#macdha").val();
                        var noitru = $("#noitru").val();
                        var url = "xoa_ds_checkbox_chualuu_noisoi?sophieu=" + sophieu + "&ma_cdha=" + ma_cdha + "&noitru=" + noitru + "&dvtt=${Sess_DVTT}" + "&sovaovien=" + sovaovien + "&sovaovien_noi=" + sovaovien_noi + "&sovaovien_dt_noi=" + sovaovien_dt_noi;
                        $.ajax({
                            url: url
                        }).done(function (data) {
                        });
                        <c:forEach var="i" items="${formnoisoi}">
                        $("#checkbox_${i.MA_MAUNOISOI}").css("display", "none");
                        </c:forEach>
                    }
                    var id = $("#maunoisoi").val();
                    if (id !== "0") {
                        if ("${hienthi_checkbox}" == "1") {
                            $("#checkbox_" + id + " :checkbox").removeAttr('checked');
                            var sophieu = $("#sophieu").val();
                            var ma_cdha = $("#macdha").val();
                            var url = "lay_checkbox_dacheck_noisoi?sophieu=" + sophieu + "&ma_cdha=" + ma_cdha + "&noitru=" + noitru + "&sovaovien=" + sovaovien + "&sovaovien_noi=" + sovaovien_noi + "&sovaovien_dt_noi=" + sovaovien_dt_noi;
                            $.ajax({
                                url: url
                            }).done(function (data) {
                                $.each(data, function (i) {
                                    $("#nstq_" + data[i].MA_CHECKBOX).prop('checked', true);
                                });
                            });
                            $("#checkbox_" + id).css("display", "");
                        }
                        var url = "select_maunoisoi_theoma?ma=" + id + "&dvtt=${Sess_DVTT}";
                        $.ajax({
                            url: url
                        }).done(function (data) {
                            CKEDITOR.instances.ketqua.setData(data[0]["NOIDUNG"]);
                            CKEDITOR.instances.ketluan.setData(data[0]["KET_LUAN"]);
                        });

                    }
                }
            });

            <c:forEach var="i" items="${formnoisoi}">
            $("#tab_noisoi_${i.MA_MAUNOISOI}").tabs({
                activate: function (event, ui) {
                    setTenCoquanTemp($(this));
                }
            });
            $("#checks_${i.MA_MAUNOISOI} :checkbox").change(function (e) {
                var ma_checkbox = $(this).val();
                if ($(this).is(":checked")) {
                    themNoiDung(ma_checkbox);
                }
                else {
                    xoaNoiDung(ma_checkbox);
                }

            });
            </c:forEach>

            // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT
            $("#phongban").change(function (evt) {
                var url = "layphongbenh_theokhoaxn?khoa=" + $("#phongban").val() + "&dvtt=${Sess_DVTT}";
                $.ajax({
                    url: url
                }).done(function (data) {
                    if (data) {
                        $("#phongbenh").empty();
                        $.each(data, function (i) {
                            $("<option value='" + data[i].MA_PHONG_BENH + "'>" + data[i].TEN_PHONG_BENH + "</option>").appendTo("#phongbenh");
                        });
                    }
                });
                $("#phongbenh").val(-1);
                reload_grid();
            });
            $("#phongbenh").change(function (evt) {
                reload_grid();
            });
            $("#doituong").change(function (evt) {
                reload_grid();
            });
            // End ĐắkLắk
            $("#dathuchien").change(function (evt) {
                reload_grid();
            });
            $("#dathuchien1").change(function (evt) {
                $("#dathuchien").prop('checked', $("#dathuchien1").prop('checked'));
            });
            // 24/04/2017: STG thêm
            $("#loai_ns").change(function (evt) {
                reload_grid();
            });
            // 24/04/2017: STG thêm
            $("#lydonhapvien").keypress(function (evt) {
                if (evt.keyCode == 13) {
                    $("#chandoan").focus();
                }
            });
            $("#chandoan").keypress(function (evt) {
                if (evt.keyCode == 13) {
                    $("#tiensubenh").focus();
                }
            });
            $("#tiensubenh").keypress(function (evt) {
                if (evt.keyCode == 13) {
                    $("#may").focus();
                }
            });
            $("#may").keypress(function (evt) {
                if (evt.keyCode == 13) {
                    $("#thuoc").focus();
                }
            });
            $("#thuoc").keypress(function (evt) {
                if (evt.keyCode == 13) {
                    $("#maunoisoi").focus();
                }
            });
            $("#maunoisoi").keypress(function (evt) {
                if (evt.keyCode == 13) {
                    $("#ketqua").focus();
                }
            });
            $("#ketqua").keypress(function (evt) {
                if (evt.keyCode == 13 && "${Sess_DVTT}".indexOf("96") != 0) {
                    $("#ketluan").focus();
                }
            });
            $("#ketluan").keypress(function (evt) {
                if (evt.keyCode == 13 && "${Sess_DVTT}".indexOf("96") != 0) {
                    $("#denghi").focus();
                }
            });
            //CMU: 29/10/2017
            var loainoisoi = "${loainoisoi}";
            if (loainoisoi == "TMH" || loainoisoi == "DADAY" || loainoisoi == "NOISOICOTUCUNG" || loainoisoi == "NOISOIPHEQUAN") {
                $("#loai_ns").attr("disabled", "disabled");
                $("#loai_ns").val(loainoisoi);
            }

            $("#viewimageweb").click(function (evt) {
                var sophieu = $("#sophieu").val();
                var macdha = $("#macdha").val();
                var arr = [sophieu, macdha];
                if (sophieu != "") {
                    var url = "ris_viewimage_web?url=" + convertArray(arr);
                    $.ajax({
                        url: url
                    }).done(function (urlweb) {
                        if (urlweb == "ERRLOGIN") {
                            jAlert("Xác thực đăng nhập RIS Connector thất bại, Vui lòng kiểm tra lại thông tin cấu hình kết nối RIS", "Thông báo");
                        } else if (urlweb == "ERROR") {
                            jAlert("Đã xảy ra lỗi", 'Thông báo');
                        } else if (urlweb == "RIS.7") {
                            jAlert("Không thể tìm thấy dữ liệu hình ảnh trên PACS", 'Thông báo');
                        } else if (urlweb == "RIS.6") {
                            jAlert("Không thể tìm thấy ca chụp trên RIS", 'Thông báo');
                        } else if (urlweb == "RIS.4") {
                            jAlert("Lấy đường dẫn xem ảnh không thành công", 'Thông báo');
                        } else if (urlweb == "NOTRECEIVE") {
                            jAlert("RIS chưa nhận ca chụp này", 'Thông báo');
                        } else {
                            var redirectWindow = window.open(urlweb, '_blank');
                            redirectWindow.location;
                            return false;
                        }
                    });
                }
            });

            $("#viewimageapp").click(function (evt) {
                var sophieu = $("#sophieu").val();
                var macdha = $("#macdha").val();
                var arr = [sophieu, macdha];
                if (sophieu != "") {
                    var url = "ris_viewimage_app?url=" + convertArray(arr);
                    $.ajax({
                        url: url
                    }).done(function (urlapp) {
                        if (urlapp == "ERRLOGIN") {
                            jAlert("Xác thực đăng nhập RIS Connector thất bại, Vui lòng kiểm tra lại thông tin cấu hình kết nối RIS", "Thông báo");
                        } else if (urlapp == "ERROR") {
                            jAlert("Đã có lỗi xảy ra", 'Thông báo');
                        } else if (urlapp == "RIS.7") {
                            jAlert("Không thể tìm thấy dữ liệu hình ảnh trên PACS", 'Thông báo');
                        } else if (urlapp == "RIS.6") {
                            jAlert("Không thể tìm thấy ca chụp trên RIS", 'Thông báo');
                        } else if (urlapp == "RIS.4") {
                            jAlert("Lấy đường dẫn xem ảnh không thành công", 'Thông báo');
                        } else if (urlapp == "NOTRECEIVE") {
                            jAlert("RIS chưa nhận ca chụp này", 'Thông báo');
                        } else {
                            if (!deployJava.isWebStartInstalled("1.7.0")) {
                                if (deployJava.installLatestJRE()) {
                                    if (deployJava.launch(urlapp)) {
                                    }
                                }
                            } else {
                                if (deployJava.launch(urlapp)) {
                                }
                            }
                        }
                    });
                }
            });

            if ("${thamSo8448811}" == 1)
                $(".icdTruocSau").show();
            else
                $(".icdTruocSau").hide();

            $("#chandoan_truoccdha").combogrid({
                url: 'laydanhmucicd',
                debug: true,
                width: "400px",
                colModel: [{'columnName': 'ICD', 'label': 'ICD'},
                    {'columnName': 'MO_TA_BENH_LY', 'width': '40', 'label': 'Mô tả bệnh lý'},
                    {'columnName': 'MA_BENH_LY', 'width': '30', 'label': 'mabenhly', hidden: true}
                ],
                select: function (event, ui) {
                    $("#chandoan_truoccdha").val(ui.item.MO_TA_BENH_LY);
                    $("#icd_truoccdha").val(ui.item.ICD);
                    $("#mabenhly_truoccdha").val(ui.item.MA_BENH_LY);
                    return false;
                }
            });
            $("#chandoan_saucdha").combogrid({
                url: 'laydanhmucicd',
                debug: true,
                width: "400px",
                colModel: [{'columnName': 'ICD', 'label': 'ICD'},
                    {'columnName': 'MO_TA_BENH_LY', 'width': '40', 'label': 'Mô tả bệnh lý'},
                    {'columnName': 'MA_BENH_LY', 'width': '30', 'label': 'mabenhly', hidden: true}
                ],
                select: function (event, ui) {
                    $("#chandoan_saucdha").val(ui.item.MO_TA_BENH_LY);
                    $("#icd_saucdha").val(ui.item.ICD);
                    $("#mabenhly_saucdha").val(ui.item.MA_BENH_LY);
                    return false;
                }
            });
            $("#icd_truoccdha").keyup(function (evt) {
                if ($("#icd_truoccdha").val() != "") {
                    var url = "laymotabenhly?icd=" + $("#icd_truoccdha").val();
                    $.ajax({
                        url: url
                    }).done(function (data) {
                        if (data && data != "" && data != " ") {
                            arr = data.split("!!!");
                            $("#icd_truoccdha").val($("#icd_truoccdha").val().toString().toUpperCase());
                            $("#chandoan_truoccdha").val(arr[1]);
                            $("#mabenhly_truoccdha").val(arr[0]);
                        } else {
                            $("#chandoan_truoccdha").val("");
                            $("#mabenhly_truoccdha").val("");
                        }
                    }).error(function () {
                        $("#chandoan_truoccdha").val("");
                        $("#mabenhly_truoccdha").val("");
                    });
                }
            });

            $("#icd_saucdha").keyup(function (evt) {
                if ($("#icd_saucdha").val() != "") {
                    var url = "laymotabenhly?icd=" + $("#icd_saucdha").val();
                    $.ajax({
                        url: url
                    }).done(function (data) {
                        if (data && data != "" && data != " ") {
                            arr = data.split("!!!");
                            $("#icd_saucdha").val($("#icd_saucdha").val().toString().toUpperCase());
                            $("#chandoan_saucdha").val(arr[1]);
                            $("#mabenhly_saucdha").val(arr[0]);
                        } else {
                            $("#chandoan_saucdha").val("");
                            $("#mabenhly_saucdha").val("");
                        }
                    }).error(function () {
                        $("#chandoan_saucdha").val("");
                        $("#mabenhly_saucdha").val("");
                    });;
                }
            });
            // if(THAMSO_828449=="1" && checkInputStatus("ngayth_ct")) {
            //     $("#ngaygioth_ct").hide();
            // }


            $('#in_giay_cam_doan').click(function () {
                var [day, month, year] = "${ngayhientai}".split('/');
                var params = {
                    mabenhnhan: mabenhnhan,
                    sophieuthanhtoan: sophieuthanhtoan,
                    ngaythuchien: "Ngày " + day + " tháng " + month + " năm " + year
                }
                var url = "cmu_in_rp_giaycamdoan_cdha?type=pdf&" + $.param(params);
                $(location).attr('href', url);
            })
        });

        // function checkInputStatus(inputId) {
        //     var $input = $("#" + inputId);
        //     return $input.length > 0 && $input.is(':visible');
        // }
        //CMU Lấy DS Máy trường hợp thiếu parameter
        if ($("#ma_maycdha_md option").length <= 1) {
            $.ajax({
                method: "GET",
                url: "cmu_getlist?url=" + convertArray(["${Sess_DVTT}", "DANHSACH_MAYCDHA_DMCDHA_NS"]),
                success: function (data) {
                    data.forEach(function (item) {
                        $("#ma_maycdha_md").append("<option value=" + item.STT + ">" + item.TEN_MAY + "</option>");
                    });
                }
            });
        }

        function reload_grid_dsbn_cd() {
            var dscdParam = {
                P_DVTT: ${Sess_DVTT},
                P_TUNGAY: convertStr_MysqlDate($("#tungay1").val()),
                P_DENNGAY: convertStr_MysqlDate($("#denngay1").val()),
                P_DATH: $('#dathuchien1').prop('checked') == true ? 1 : 0,
                P_LOAI: 'NS',
                P_LOAI_TEXT: "${TMH}" == 'TMH' ? 'TMH' : 'NS'
            }
            var url = 'noisoi_ds_benhnhan_chidinh_bpc?' + $.param(dscdParam);
            $.getJSON(url, function (res) {
                $("#grid_dschidinh").jqGrid('clearGridData');
                var data = res.filter(function (i, n) {
                    return $('#dathuchien1').prop('checked') == false || ($('#theobacsi').prop('checked') == false || i.BACSI_THUCHIEN == tennv);
                });

                $("#grid_dschidinh").jqGrid('setGridParam', {data: data}).trigger('reloadGrid');
            });

//                $("#grid_dschidinh").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
        }

        function reload_grid() {
            var ngay = convertStr_MysqlDate($("#ngaythuchien").val());
            var dvtt = "${Sess_DVTT}";
            var phong = "${Sess_Phong}";
            // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT
            var phongban = $("#phongban").val();
            var phongbenh = $("#phongbenh").val();
            var doituong = $("#doituong").val();
            // -- VPC khai báo loại cdha
            var loaicdha = $("#loai_ns").val();
            // --END VPC
            // End ĐắkLắk
            var dathuchien = $("#dathuchien").prop('checked');
            if (dathuchien == true) {
                dath = 1;
                if ("${useris}" === "1") {
                    $('#viewimageweb').show();
                    $('#viewimageapp').show();
                } else {
                    $('#viewimageweb').hide();
                    $('#viewimageapp').hide();
                }
            } else {
                dath = 0;
                $('#viewimageweb').hide();
                $('#viewimageapp').hide();
            }
            // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT - 24/04/2017: STG thêm lấy theo loại CDHA
            var arr = [dvtt, ngay, phong, dath, phongban, phongbenh, doituong, loaicdha];
            //------HPG -Tim kiem benh nhan CLS tu ngay den ngay
            if ("${timkiem_cls}" == "1") {
                var tungay = convertStr_MysqlDate($("#tungay").val());
                arr = [dvtt, tungay, ngay, phong, dath, loaicdha, phongban, phongbenh, doituong];
            }
            // --- VPC cấu hình tách loại nội soi TMH và nội soi dạ dầy VPC
            if (dvtt.substring(0, 2) == "26") {
                var arr1 = [dvtt, ngay, dath, -1, -1, doituong, loaicdha];
                if (loaicdha != -1) {
                    var url = 'noisoi_ds_benhnhan_cothamso_vpc?url=' + convertArray(arr1);
                } else {
                    var url = 'noisoi_ds_benhnhan_cothamso_vpc?url=' + convertArray(arr1);
                }
            } else
                var url = 'noisoi_ds_benhnhan_cothamso?url=' + convertArray(arr);
            $("#list_benhnhan").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
        }
        ;

        function hienthi_them_cls(url) {
            $.ajax({
                url: url
            }).done(function (data) {
                $('#trieuchungls').val(data[0].TRIEUCHUNGLS);
                $('#benhtheobs').val(data[0].TEN_BENH_THEOBS);
                $('#ngaythuchien_cls').val(data[0].NGAY_THUC_HIEN);
                if (data[0].TT_THANHTOAN == "0")
                    $('#tt_thanhtoan').val("Chưa thanh toán");
                else
                    $('#tt_thanhtoan').val("Đã thanh toán");
            });
        }
        ;

        //CMU: 26062017
        function load_lscdha_bn(mabenhnhan) {
            var url = "cmu_danhsach_lichsu_cdha?mabenhnhan=" + mabenhnhan + "&type=NS";
            $("#list_lichsuCDHA").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
        }

        //KGG them gọi số
        function mapUndefinedOrNullTo(value1, value2) {
            if (value1 === undefined || value1 === null) return value2;
            return value1;
        }

        function load_cttoathuoc(nghiepvu, list) {
            var url = 'noitru_load_chitiet_film?matt=' + matoathuoc + '&nghiepvu=' + nghiepvu + '&dvtt=${Sess_DVTT}&stt_benhan=' + stt_benhan +
                '&stt_dotdieutri=' + stt_dotdieutri + '&sovaovien=' + sovaovien_noi + '&sovaovien_dt=' + sovaovien_dt_noi +
                '&phongban=${Sess_PhongBan}' + '&ma_cdha=' + $("#macdha").val();
            +'&sophieu=' + $("#sophieu").val();
            ;
            $('#' + list).jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
        }

        function loadThongTinKetQua(ret) {
            $("#macdha").val(ret.MA_CDHA);
            $("#nguoithuchien").val(ret.NGUOI_THUC_HIEN);
            $("#frm_kq_id_ekip").val(ret.ID_EKIP);
            $("#frm_kq_so_phieu_dichvu").val($("#sophieu").val());
            $("#frm_kq_ma_dv").val(ret.MA_CDHA);
            $("#frm_kq_mabenhnhan").val($("#mabenhnhan").val());
            $("#frm_kq_sovaovien").val(sovaovien == 0 ? sovaovien_noi : sovaovien);
            $("#frm_kq_sovaovien_dt").val(sovaovien_dt_noi);
            $("#frm_kq_noitru").val($("#noitru").val());
            $("#frm_kq_id_dieutri").val(ret.ID_DIEUTRI);
            var sophieu = $("#sophieu").val();
            var ma_cdha = $("#macdha").val();
            var noitru = $("#noitru").val();
            var makhambenh = $("#makhambenh").val();
            var sttbenhan = $("#sttbenhan").val();
            var sttdotdieutri = $("#sttdotdieutri").val();
            var sttdieutri = $("#sttdieutri").val();
            getNguoiDocKetQua(sophieu, noitru==1?sovaovien_noi:sovaovien, sovaovien_dt_noi, noitru, ma_cdha);
            if (flag_noitru === '1') {
                load_cttoathuoc("noitru_toadichvu", "list_thuocdichvu");
            } else {
                var url = 'chitiettoathuocngoatru_svv?matt=' + matoathuoc + "&nghiepvu=ngoaitru_toadichvu&dvtt=${Sess_DVTT}" + "&sovaovien=" + sovaovien + "&ma_cdha=" + ma_cdha + "&sophieu=" + sophieu;
                $("#list_thuocdichvu").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
            }
            $('#maunoisoi').removeAttr("disabled");
            if ("${hienthi_checkbox}" == "1") {
                <c:forEach var="i" items="${formnoisoi}">
                $("#checkbox_${i.MA_MAUNOISOI}").css("display", "none");
                </c:forEach>
                var sophieu = $("#sophieu").val();
                var ma_cdha = $("#macdha").val();
                var noitru = $("#noitru").val();
                var url = "xoa_ds_checkbox_chualuu_noisoi?sophieu=" + sophieu + "&ma_cdha=" + ma_cdha + "&noitru=" + noitru + "&dvtt=${Sess_DVTT}" + "&sovaovien=" + sovaovien + "&sovaovien_noi=" + sovaovien_noi + "&sovaovien_dt_noi=" + sovaovien_dt_noi;
                $.ajax({
                    url: url
                }).done(function (data) {
                });
            }
            var arr = [sophieu, "${Sess_DVTT}", ret.MA_CDHA, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0", sovaovien, sovaovien_noi, sovaovien_dt_noi];
            var url = "noisoi_select_ketqua_svv?url=" + convertArray(arr);
            $.getJSON(url, function (result) {
                $("#bacsinoisoi").val("${Sess_User}");
                $("#chandoan").val("");
                $("#maunoisoi").val("-1");
                $("#ketluan").val("");
                $("#lydonhapvien").val("");
                $("#tiensubenh").val("");
                $("#may").val("");
                $("#thuoc").val("");
                $("#denghi").val("");
                $("#nschandoan").attr('Checked', false);
                $("#nsdieutri").attr('Checked', false);
                $("#capcuu").attr('Checked', false);
                $("#ns_laydivat").attr('Checked', false);
                $("#sinhthiet").attr('Checked', false);
                $("#clotest").attr('Checked', false);
                $("#giauphaubenh").attr('Checked', false);
                $.each(result, function (i, field) {
                    $("#bacsichidinh").val(field.BACSI_CHIDINH);
                    $("#bacsinoisoi").val(field.BACSI_THUCHIEN);
                    if (field.MA_MAUNOISOI == null || field.MA_MAUNOISOI == "") {
                        $("#maunoisoi").val("-1");
                    } else {
                        $("#maunoisoi").val(field.MA_MAUNOISOI);
                    }
                    if ($("#bacsichidinh").val() == "") {
                        var url_bs = "select_tenbacsi?mabacsi=" + $("#mabacsichidinh").val() + "&dvtt= " + "${Sess_DVTT}";
                        $.ajax({
                            url: url_bs
                        }).done(function (data) {
                            $("#bacsichidinh").val(data);
                        });

                    }
                    if ($("#bacsinoisoi").val() == "") {
                        $("#bacsinoisoi").val("${Sess_User}");
                    }
                    if (field.CHANDOAN.toString() != " ") {
                        $("#chandoan").val(field.CHANDOAN);
                    }
                    else {
                        var cd = $("#_chandoan").val();
                        $("#chandoan").val(cd);
                    }
                    CKEDITOR.instances.ketqua.setData(field.KET_QUA);
                    CKEDITOR.instances.ketluan.setData(field.MO_TA);
                    $("#lydonhapvien").val(field.LYDO_NHAPVIEN);
                    $("#tiensubenh").val(field.TIENSUBENH);
                    $("#may").val(field.MAY);
                    $("#thuoc").val(field.THUOC);
                    $("#denghi").val(field.LOIDANBACSI);
                    var nschandoan = (field.NS_CHUANDOAN) == 'x' ? true : false;
                    $("#nschandoan").attr('Checked', nschandoan);
                    var nsdieutri = (field.NS_DIEUTRI) == 'x' ? true : false;
                    $("#nsdieutri").attr('Checked', nsdieutri);
                    var capcuu = (field.CAPCUU) == 'x' ? true : false;
                    $("#capcuu").attr('Checked', capcuu);
                    var ns_laydivat = (field.NS_LAYDIVAT) == 'x' ? true : false;
                    $("#ns_laydivat").attr('Checked', ns_laydivat);
                    var sinhthiet = (field.TRANGTHAI) == 'x' ? true : false;
                    $("#sinhthiet").attr('Checked', sinhthiet);
                    var clotest = (field.CLOTEST) == 'x' ? true : false;
                    $("#clotest").attr('Checked', clotest);
                    var giauphaubenh = (field.GIAUPHAU_BENH) == 'x' ? true : false;
                    $("#giauphaubenh").attr('Checked', giauphaubenh);
                    $("#duongvao").val(field.DUONGVAO);
                    $("#bacsichidinh").val(field.BACSI_CHIDINH);
                    $("#bacsinoisoi").val(field.BACSI_THUCHIEN);
                    $("#mabenhly_truoccdha").val(field.MABENHLY_TRUOCCDHA);
                    $("#mabenhly_saucdha").val(field.MABENHLY_SAUCDHA);
                    $("#icd_truoccdha").val(field.ICD_TRUOCCDHA);
                    $("#icd_saucdha").val(field.ICD_SAUCDHA);
                    $("#chandoan_truoccdha").val(field.CHANDOAN_TRUOCCDHA);
                    $("#chandoan_saucdha").val(field.CHANDOAN_SAUCDHA);
                    if (field.PP_VO_CAM !== null) {
                        $("#ma_pp_vo_cam").val(field.PP_VO_CAM);
                    }
                    if("${thamso_184002}"=="1"){
                        if (field.NGAYTHUCHIEN == null) {
                            $("#ngayth_ct").val("${ngayhientai}");
                        } else {
                            $("#ngayth_ct").val(field.NGAYTHUCHIEN);
                        }
                        $("#gioth_ct").data('da-thuc-hien', field.DA_CHAN_DOAN == 1);
                        if (field.GIOTHUCHIEN == null || field.DA_CHAN_DOAN != 1) {
                            showtime_gioth_ct();
                        } else {
                            stopGioThCtTimer();
                            $("#gioth_ct").val(field.GIOTHUCHIEN);
                        }

                        $("#ngayKtThucHienCdha").val(field.NGAYTHUCHIEN_KT);
                        $("#gioKtThucHienCdha").val(field.GIOTHUCHIEN_KT);
                    }
                    var ma_noisoi = $("#maunoisoi").val();
                    if ("${hienthi_checkbox}" == "1") {
                        if (ma_noisoi !== null && ma_noisoi != 0) {
                            $("#checkbox_" + ma_noisoi + " :checkbox").removeAttr('checked');
                            var url = "lay_checkbox_dacheck_noisoi?sophieu=" + sophieu + "&ma_cdha=" + ret.MA_CDHA + "&noitru=" + noitru + "&sovaovien=" + sovaovien + "&sovaovien_noi=" + sovaovien_noi + "&sovaovien_dt_noi=" + sovaovien_dt_noi;
                            $.ajax({
                                url: url
                            }).done(function (data) {
                                $.each(data, function (i) {
                                    $("#nstq_" + data[i].MA_CHECKBOX).prop('checked', true);
                                });
                            });
                            $("#checkbox_" + ma_noisoi).css("display", "");
                            //$("#maunoisoi").attr("disabled", "disabled");
                        }
                        else {
                            var dvtt = "${Sess_DVTT}";
                            var ma_cdha = $("#macdha").val();
                            var gioitinh = $("#gioitinh").val();
                            if (gioitinh == "true") {
                                gioitinh = 1;
                            }
                            else {
                                gioitinh = 0;
                            }
                            var url = "load_maunoisoi_tuongung?ma_cdha=" + ma_cdha + "&gioitinh=" + gioitinh + "&dvtt=" + dvtt;
                            $.ajax({
                                url: url
                            }).done(function (data) {
                                $("#maunoisoi").val(data);
                                $("#checkbox_" + data).css("display", "");
                                var url = "select_maunoisoi_theoma?ma=" + data + "&dvtt=${Sess_DVTT}";
                                $.ajax({
                                    url: url
                                }).done(function (data) {
                                    if (data != 0) {
                                        CKEDITOR.instances.ketqua.setData(data[0]["NOIDUNG"]);
                                        CKEDITOR.instances.ketluan.setData(data[0]["KET_LUAN"]);
                                    }
                                    //$('#ketluan').val(data[0]["KET_LUAN"]);
                                    //$('#ketqua').val(data);
                                });
                            });
                        }
                    }
                    if(field.DA_CHAN_DOAN==1) {
                        $("#thoiGianBatDau_cls").val(field.NGAY_TH_YL).change();
                        // $("#ngaygioth_ct").val(field.NGAY_GIO_THUC_HIEN).change();
                        // changeInputTimerStatus("thoiGianBatDau_cls", true);
                    } else if (field.DA_CHAN_DOAN ==0) {
                        $("#thoiGianBatDau_cls").val(newStringDateTime());
                        //changeInputTimerStatus("thoiGianBatDau_cls");
                        // changeInputTimerStatus("ngaygioth_ct");
                    }
                    $("#icdChanDoanCanLamSang").val(field.MA_ICD);
                    $("#tenChanDoanCanLamSang").val(field.TEN_ICD);
                    $("#maBenhLyChanDoanCanLamSang").val(field.MA_BENH_LY_THEO_ICD);
                    $("#ngaygioth_ct").val(field.NGAYTHUCHIEN + field.GIOTHUCHIEN);
                });
                if ($("#maunoisoi").val() == "0") {
                    CKEDITOR.instances.ketqua.setData("");
                }
                ;
                var arr = [sophieu, "${Sess_DVTT}", ret.MA_CDHA, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                var url1 = "sieuam_danhsach_hinhanh?url=" + convertArray(arr);
                $("#list_hinhanhnoisoi").jqGrid('setGridParam', {datatype: 'json', url: url1}).trigger('reloadGrid');
                $("#tab_cdha").tabs("option", "active", 1);
                $('img').attr('src', 'resources/webcam/camera_png.jpg');
                //$(location).attr('href', '#cdha_tabs_2');
            });
        }

        function loadThongTinBenhNhan(ret, macdha) {
            console.log('ret', ret)
            //CMU Cảnh báo ngoại trú chưa đóng tiền
            if ("${cdhacanhbao}" > 0 && ret.NOITRU == 0 && ret.CO_BHYT == 0 && ret.DA_THANH_TOAN == 0)
            {
                jAlert('Bệnh nhân ngoại trú chưa đóng viện phí.', 'Thông báo');
                if("${cdhacanhbao}" == 2){
                    clear_benhnhan();
                    $("#list_noisoi_bhyt").jqGrid('clearGridData');
                    return;
                }
            }
            $("#mabenhnhan").val(ret.MABENHNHAN);
            $("#hoten").val(ret.TENBENHNHAN);
            $("#tuoi").val(ret.TUOI);
            $("#gioitinh").val(ret.GIOITINH.toString());
            $("#diachi").val(ret.DIACHI);
            $("#sothebhyt").val(ret.SOTHEBHYT);
            $("#sophieu").val(ret.SO_PHIEU);
            $("#makhambenh").val(ret.MA_KHAM_BENH);
            $("#noitru").val(ret.NOITRU);
            $("#sttbenhan").val(ret.STT_BENHAN);
            $("#sttdotdieutri").val(ret.STT_DOTDIEUTRI);
            $("#sttdieutri").val(ret.STT_DIEUTRI);
            $("#mabacsichidinh").val(ret.NGUOI_CHI_DINH);
            $("#ngaybatdau").val(ret.NGAYBATDAU);
            $("#ngayketthuc").val(ret.NGAYKETTHUC);
            $("#noidk").val(ret.NOIDK);
            $("#namsinh").val(ret.NAMSINH);
            phongcdha = ret.MA_PHONG_CDHA;
            sovaovien = ret.SOVAOVIEN;
            sovaovien_noi = ret.SOVAOVIEN_NOI;

            $("#sovaovien").val(sovaovien == 0 ? sovaovien_noi : sovaovien);

            sovaovien_dt_noi = ret.SOVAOVIEN_DT_NOI;
            da_thanh_toan = ret.DA_THANH_TOAN;
            sophieuthanhtoan = ret.SOPHIEUTHANHTOAN;
            cobhyt = ret.TI_LE_MIEN_GIAM.replace('.00', '');
            ngay_kb = ret.NGAY_KB;
            tlmg = ret.TI_LE_MIEN_GIAM;
            flag_noitru = ret.NOITRU;
            bhytchi = ret.CO_BHYT;
            mabenhnhan = ret.MABENHNHAN;
            //sovaovien_dt=ret.SOVAOVIEN_DT_NOI;
            stt_dieutri = ret.STT_DIEUTRI;
            stt_benhan = ret.STT_BENHAN;
            stt_dotdieutri = ret.STT_DOTDIEUTRI;
            // Sang start
            $("#bacsinoisoi").val(ret.BACSI_THUCHIEN);

            if (ret.BACSI_THUCHIEN == '') {
                $("#bacsinoisoi").val("${Sess_User}");
            }
            if (ret.NOITRU === "0") {
                $("#kho_dv_ngoai").show();
                $("#kho_dv_noi").hide();
            } else {
                $("#kho_dv_noi").show();
                $("#kho_dv_ngoai").hide();
            }
            if (ret.DA_THANH_TOAN == 1) {
                $("#chuyen_cd_noisoi").hide();
            } else {
                $("#chuyen_cd_noisoi").show();

            }
            // Sang end

            // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: thêm thông tin khoa chỉ định, chẩn đoán icd
            $("#_chandoan").val(ret.CHUANDOANICD);
            $("#chandoan").val(ret.CHUANDOANICD);
            $("#tenkhoa").val(ret.TENKHOA);
            // End ĐắkLắk

            // ĐắkLắk (An Giang yêu cầu) - Ninh 09/12/2016: view thông tin hành chánh của BN lên form nhập kết quả
            $("#hoten_ct").val(ret.TENBENHNHAN);
            $("#tuoi_ct").val(ret.TUOI);
            $("#gioitinh_ct").val(ret.GIOITINH.toString() == "true" ? "Nam" : "Nữ");
            $("#mabenhnhan_ct").val(ret.MABENHNHAN);
            $("#tenkhoa_ct").val(ret.TENKHOA);
            $("#sothebhyt_ct").val(ret.SOTHEBHYT);
            // End ĐắkLắk

            var url_bs = "select_tenbacsi?mabacsi=" + ret.NGUOI_CHI_DINH + "&dvtt=" + "${Sess_DVTT}";
            $.ajax({
                url: url_bs
            }).done(function (data) {
                $("#bacsichidinh").val(data);
            });
            matoathuoc = flag_noitru === '1' ? (ret.SO_PHIEU.split('.')[2]).split('_')[0] :
                ret.MA_KHAM_BENH.replace("kb_", "tt_");
            var dathuchien = $("#dathuchien").prop('checked');


            // ĐắkLắk (Cà Mau yêu cầu) - Ninh 09/12/2016: reset mô tả khi chọn bệnh nhân mới
            $("#maunoisoi").val("-1");
            $("#ketqua").val("");
            $("#ketluan").val("");
            // End ĐắkLắk
            if ("${hienthi_them_cls}" == "1") {
                var hpg_STT_BENHAN = "0";
                var hpg_STT_DIEUTRI = "0";
                var hpg_STT_DOTDIEUTRI = "0";
                if (ret.STT_DIEUTRI != "")
                    hpg_STT_BENHAN = ret.STT_BENHAN;
                if (ret.STT_DIEUTRI != "")
                    hpg_STT_DOTDIEUTRI = ret.STT_DOTDIEUTRI;
                if (ret.STT_DIEUTRI != "")
                    hpg_STT_DIEUTRI = ret.STT_DIEUTRI;
                var arr1 = [ret.MA_KHAM_BENH, ret.SO_PHIEU, "${Sess_DVTT}", ret.NOITRU, hpg_STT_BENHAN, hpg_STT_DOTDIEUTRI, hpg_STT_DIEUTRI, 3]
                var url1 = "hpg_thongtin_mo_rong_bn_cls?url=" + convertArray(arr1);
                hienthi_them_cls(url1);
                console.log('Done 1')
            }
        }

        function loadDsCD(ret) {
            $("#ma_maycdha_md").val("");
            var idath;
            var dathuchien = $("#dathuchien").prop('checked');
            if (dathuchien == true) {
                idath = 1;
            } else {
                idath = 0;
            }

            var arr = [ret.NOITRU, ret.SO_PHIEU, ret.STT_BENHAN, ret.STT_DOTDIEUTRI, ret.STT_DIEUTRI, "${Sess_DVTT}", sovaovien, sovaovien_noi, sovaovien_dt_noi, idath];
            var url = "noisoi_hienthi_chitiet_svv?url=" + convertArray(arr);
            $("#list_noisoi_bhyt").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
        }
        function goisolayhinhanh(mgrid, cur_id) {
            var IDS = mgrid.jqGrid("getDataIDs");
            var count = 5 + parseInt(cur_id - 1);
            var chuoitong = "";
            for (var i = cur_id - 1; i < count; i++) {
                var id = IDS[i];
                var ret = mgrid.jqGrid('getRowData', id);
                if (ret.STT_HANGNGAY == undefined || ret.STT_HANGNGAY == null){
                    break;
                }
                var chuoi = "${Sess_TenPhong}" + "|" + ret.STT_HANGNGAY + "|" + ret.TENBENHNHAN + "|" + "0" + "|" + " " + "|" + "${Sess_DVTT}" + "|" + "" + "|" + " ";
                chuoitong = chuoitong + chuoi + "@";
            }
            return chuoitong;
        }

        function fetchListDSBN_NS(mgrid, cur_id, pkID, pkName) {
            var IDS = mgrid.jqGrid("getDataIDs");
            var count = IDS.length;
            // set default value
            var arr_call = [];
            for (var i = 0; i < count; i++) {
                var id = IDS[i];
                var ret = mgrid.jqGrid('getRowData', id);
                var bn_info = {
                    stt_bn_dv: ret.STT_HANGNGAY,
                    stt_bn_pb: ret.STT_HANGNGAY,
                    ma_bn: ret.MABENHNHAN,
                    ten_bn: ret.TENBENHNHAN,
                    uu_tien: mapUndefinedOrNullTo(ret.UU_TIEN, 'No') == 'Yes' ? 1 : 0,
                    sothebhyt: "BHYT",//ret.SO_THE_BHYT,
                    diachi: "Diachu",//ret.DIA_CHI,
                    ngaysinh: ret.NGAY_SINH.replace('-', '/').replace('-', '/')
                };
                arr_call.push(bn_info);
            }
            //
            var curstt = mgrid.jqGrid('getRowData', cur_id).STT_HANGNGAY;
            var info = {
                pk_id: pkID,
                pk_name: pkName,
                cur_id: curstt,
                dsbn: arr_call
            };
            //
            return JSON.stringify(info);
        }

        function saveTextAsFileL(textToWrite) {
            // grab the content of the form field and place it into a variable
            //  create a new Blob (html5 magic) that conatins the data from your form feild
            var textFileAsBlob = new Blob([textToWrite], {type: 'text/plain'});
            // Specify the name of the file to be saved
            var fileNameToSaveAs = "WEB_HIENTHISO_LIST.lgs";

            // Optionally allow the user to choose a file name by providing
            // an imput field in the HTML and using the collected data here
            // var fileNameToSaveAs = txtFileName.text;

            // create a link for our script to 'click'
            var downloadLink = document.createElement("a");
            //  supply the name of the file (from the var above).
            // you could create the name here but using a var
            // allows more flexability later.
            downloadLink.download = fileNameToSaveAs;
            // provide text for the link. This will be hidden so you
            // can actually use anything you want.
            downloadLink.innerHTML = "download";

            // allow our code to work in webkit & Gecko based browsers
            // without the need for a if / else block.
            window.URL = window.URL || window.webkitURL;

            // Create the link Object.
            downloadLink.href = window.URL.createObjectURL(textFileAsBlob);
            // when link is clicked call a function to remove it from
            // the DOM in case user wants to save a second file.
            downloadLink.onclick = m_destroyClickedElement;
            // make sure the link is hidden.
            downloadLink.style.display = "none";
            // add the link to the DOM
            document.body.appendChild(downloadLink);

            // click the new link
            downloadLink.click();
        }

        function m_destroyClickedElement(event) {
            // remove the link from the DOM
            document.body.removeChild(event.target);
        }

        //KGG End
        function doSignPlugin(url, typeSign) {
            var x = new XMLHttpRequest();
            x.onload = function() {
                // Create a form
                var reader = new FileReader();
                reader.readAsDataURL(x.response);
                reader.onloadend = function() {

                    var base64data = reader.result.replace("data:application/pdf;base64,", "");
                    console.log("base64data", base64data)
                    var sigOptions = null;
                    sigOptions = new PdfSigner();
                    sigOptions.AdvancedCustom = true;
                    SignAdvanced(base64data, 'pdf', sigOptions, typeSign);
                }

            };
            x.responseType = 'blob';    // <-- This is necessary!
            x.open('GET', url, true);
            x.send();
        }
        function kyketquasieuamtoken() {
            if ($("#hoten_ct").val().trim() != "") {
                var mabenhnhan = $("#mabenhnhan").val();
                var hoten = $("#hoten").val();
                var diachi = $("#diachi").val();
                var tuoi = $("#tuoi").val();
                var phai = $("#gioitinh").val();
                var sothebhyt = $("#sothebhyt").val();
                var namsinh = $("#namsinh").val();
                var tenkhoa = $("#tenkhoa").val();
                var phai = $("#gioitinh_ct").val();
                var sophieu = $("#sophieu").val();
                CMU_SOPHIEU_CDHA = sophieu;
                var makhambenh = $("#makhambenh").val();
                var macdha = $("#macdha").val();
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var bssieuam = $("#bacsisieuam").val() != null && $("#bacsisieuam").val() != '' ? $("#bacsisieuam").val() : " ";
                var typein = '2';
                if (sophieu != "" && macdha != "") {
                    var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha,
                        dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, sothebhyt, "0",
                        sovaovien,
                        sovaovien_noi,
                        sovaovien_dt_noi, 0, $("#trieuchungls").val(), bssieuam, maPhongBan ? maPhongBan : 0, namsinh, tenkhoa, 0, typein,
                        $("#mausieuam").val(), "0"]; // VNPTHIS-4697 23/11/2017 thêm
                    var selected = [];
                    $("#list_hinhanhnoisoi").jqGrid('getGridParam', 'selarrrow').forEach(function (data) {
                        var stt = $("#list_hinhanhnoisoi").jqGrid('getCell', data, 'STT');
                        selected.push(stt);
                    });
                    var url = "inketquasieuam_svv?thongtin=" + convertArray(arr) + "&anh=" + selected;
                    doSignPlugin(url);
                }

            }
        }

        function SignAdvanced(data, type, sigOption, typeSign)
        {
            var dataJS = {};

            var arrData = [];
            // 1
            dataJS.data = data;
            dataJS.type = type;
            dataJS.sigOptions = JSON.stringify(sigOption);

            var jsData = "";
            jsData += JSON.stringify(dataJS);
            //
            arrData.push(jsData);
            var serial = "";
            vnpt_plugin.signArrDataAdvanced(arrData, serial, true, showMessageCDHAKQ);

        }
    </script>


</head>
<body>
<jsp:include page="../camau/jsp/cmu_tuongtrinh_pttt.jsp"/>
<script src="<c:url value="/resources/camau/js/tuongtrinhpttt.js"/>" async></script>
<div id="panel_all">
    <%@include file="../../../resources/Theme/include_pages/menu.jsp" %>
    <div id="panelwrap">
        <div class="center_content">
            <div id="tab_cdha">
                <ul>
                    <li><a href="#cdha_tabs_1" id="xn_cobhyt">Thông tin bệnh nhân</a></li>
                    <li><a href="#cdha_tabs_2" id="xn_bnyc">Kết quả</a></li>
                </ul>
                <div id="cdha_tabs_1">
                    <form id="form1" name="form1" style="display: none;" method="post" action="">
                        <table width="100%">
                            <tr>
                                <td width="302" valign="top">
                                    <table width="302">
                                        <tr class="hpg_tmp">
                                            <td width="62px"><span style=" display:inline-block;">Từ Ngày </span></td>
                                            <td><input type="text" name="tungay" id="tungay"/></td>
                                        </tr>
                                        <tr>
                                            <td width="62px"><span style=" display:inline-block;"><span class="hpg_tmp">Đến </span>Ngày  </span>

                                            </td>
                                            <td>
                                                <input type="text" name="ngaythuchien" id="ngaythuchien" style="width: 150px"/>
                                                <input type="button" name="lammoi" id="lammoi" value="Làm mới"/>
                                            </td>
                                        </tr>
                                        <!-- ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT -->
                                        <tr class="dlk_tmp">
                                            <td>Khoa</td>
                                            <td><select name="phongban" id="phongban" class="width100">
                                                <c:forEach var="i" items="${phongban}">
                                                    <option value="${i.ma_phongban}">${i.ten_phongban}</option>
                                                </c:forEach>
                                            </select>
                                            </td>
                                        </tr>
                                        <tr class="dlk_tmp">
                                            <td>Phòng</td>
                                            <td><select name="phongbenh" id="phongbenh" class="width100">
                                                <c:forEach var="i" items="${phongbenh}">
                                                    <option value="${i.ma_phong_benh}">${i.ten_phong_benh}</option>
                                                </c:forEach>
                                            </select></td>
                                        </tr>
                                        <tr class="dlk_tmp">
                                            <td>Đối tượng</td>
                                            <td><select name="doituong" id="doituong" class="width100">
                                                <option value="-1">--Tất cả--</option>
                                                <option value="1">Có BHYT</option>
                                                <option value="0">Không BHYT</option>
                                            </select></td>
                                        </tr>
                                        <!-- End ĐắkLắk -->
                                        <!-- VPC thêm loại CDHA -->
                                        <tr>
                                            <td>Loại</td>
                                            <td>
                                                <select id="loai_ns" name="loai_ns">
                                                    <option value="-1"> -- Tất cả --</option>
                                                    <option value="DADAY"> Nội soi dạ dày</option>
                                                    <option value="TMH">Nội soi TMH</option>
                                                    <option value="NOISOICOTUCUNG">Nội soi cổ tử cung</option>
                                                    <c:choose>
                                                        <c:when test="${Sess_DVTT == '96163'}">
                                                            <option value="NOISOIPHEQUAN">Nội soi phế quản</option>
                                                        </c:when>
                                                    </c:choose>
                                                </select>
                                            </td>
                                        </tr>
                                        <!-- End VPC -->
                                        <tr>
                                            <td colspan="2"><label><input type="checkbox" name="dathuchien"
                                                                          id="dathuchien">
                                                <b>Đã thực hiện</b></label></td>
                                        </tr>
                                        <tr>
                                            <td colspan="2">
                                                <table id="list_benhnhan"></table>
                                            </td>
                                        </tr>
                                        <tr id="ghichutrangthai">
                                            <td colspan="2" style="padding-top:10px">
                                                <!--CMU: 27/10/2017-->
                                                <label style="color:red;font-weight: normal;">BN cấp cứu</label>
                                                <label style="color:#00ff00;margin-left:20px;font-weight: normal;">BN <
                                                    6 tuổi</label><br>
                                                <label style="color:#bf00ff;font-weight: normal;">Bệnh nhân VP, chưa
                                                    đóng tiền</label><br>
                                                <label style="color:#EE7600;font-weight: normal;">Bệnh nhân VP, đã đóng
                                                    tiền</label>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                                <td width="676" valign="top">
                                    <div>
                                        <fieldset class="scheduler-border">
                                            <legend class="scheduler-border">Thông tin bệnh nhân</legend>
                                            <table width="100%">
                                                <tr>
                                                    <td width="127">Số vào viện</td>
                                                    <td width="756">
                                                        <input name="sovaovien" type="text" disabled="disabled"
                                                               class="width3" id="sovaovien"/>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td width="127">Họ tên<input name="mabenhnhan" type="hidden"
                                                                                 id="mabenhnhan"/>
                                                        <input name="macdha" type="hidden" id="macdha"/>
                                                        <input name="sophieu" type="hidden" id="sophieu"/>
                                                        <input name="makhambenh" type="hidden" id="makhambenh"/>
                                                        <input name="noitru" type="hidden" id="noitru"/></td>
                                                    <td width="756"><input name="hoten" type="text" disabled="disabled"
                                                                           class="width1" id="hoten"
                                                                           style="color: red; font-weight: bold"/>
                                                        Tuổi
                                                        <input name="tuoi" type="text" disabled="disabled"
                                                               class="width3" id="tuoi"/>
                                                        <select name="gioitinh" id="gioitinh" disabled="disabled">
                                                            <option value="true">Nam</option>
                                                            <option value="false">Nữ</option>
                                                        </select></td>
                                                </tr>
                                                <!-- ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: thêm thông tin khoa chỉ định -->
                                                <tr>
                                                    <td>Khoa</td>
                                                    <td><input name="tenkhoa" type="text" disabled="disabled"
                                                               class="width100" id="tenkhoa"/></td>
                                                </tr>
                                                <!-- End ĐắkLắk -->
                                                <tr>
                                                    <td>Địa chỉ</td>
                                                    <td><input name="diachi" type="text" disabled="disabled"
                                                               class="width100" id="diachi"/></td>
                                                </tr>
                                                <tr>
                                                    <td>Số thẻ BHYT</td>
                                                    <td><input name="sothebhyt" type="text" disabled="disabled"
                                                               class="width100" id="sothebhyt"/></td>
                                                </tr>
                                                <!-- ĐắkLắk (Cà Mau yêu cầu) - Ninh 09/12/2016: thêm thông tin chẩn đoán icd -->
                                                <tr>
                                                    <td>Chẩn đoán</td>
                                                    <td><input name="_chandoan" type="text" disabled="disabled"
                                                               class="width100" id="_chandoan"/></td>
                                                </tr>
                                                <!-- End ĐắkLắk -->
                                                <tr>
                                                    <td>BS chỉ định
                                                        <input name="mabacsichidinh" type="hidden" id="mabacsichidinh"/>
                                                    </td>
                                                    <td><input name="bacsichidinh" type="text" class="width100"
                                                               id="bacsichidinh"/></td>
                                                </tr>
                                                <tr>
                                                    <td>BS nội soi</td>
                                                    <td><input name="bacsinoisoi" type="text" class="width100"
                                                               id="bacsinoisoi"/></td>
                                                </tr>
                                                <tr>
                                                    <td>Chọn máy</td>
                                                    <td>
                                                        <select name="ma_maycdha_md" id="ma_maycdha_md"
                                                                class="width100">
                                                            <option value="" selected>Chọn máy CDHA-TDCN mặc định
                                                            </option>
                                                            <c:forEach var="e" items="${dsmaycdha}">
                                                                <option value="${e.STT}">${e.TEN_MAY}</option>
                                                            </c:forEach>
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr class="hpg_hienthithem">
                                                    <td>Triệu chứng</td>
                                                    <td>
                                                        <input name="trieuchungls" type="text" disabled="disabled"
                                                               class="width100" id="trieuchungls"/>
                                                    </td>
                                                </tr>
                                                <tr class="hpg_hienthithem">
                                                    <td>Bệnh tật</td>
                                                    <td>
                                                        <input name="benhtheobs" type="text" disabled="disabled"
                                                               class="width100" id="benhtheobs"/>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Ngày chỉ định</td>
                                                    <td>
                                                        <input name="ngaychidinh_cls" type="text" disabled="disabled" id="ngaychidinh_cls" size="10"
                                                               data-inputmask="'alias': 'date'"/>
                                                        <input name="giochidinh_cls" type="text" disabled="disabled" id="giochidinh_cls" size="10"
                                                               data-inputmask="'alias': 'hh:mm:ss'"/>
                                                    </td>
                                                </tr>
                                                <tr class="hpg_hienthithem">
                                                    <td>Ngày thực hiện</td>
                                                    <td>
                                                        <input name="ngaythuchien_cls" type="text" disabled="disabled"
                                                               class="width100" id="ngaythuchien_cls"/>
                                                    </td>
                                                </tr>
                                                <tr class="hpg_hienthithem">
                                                    <td>
                                                        Thanh toán
                                                    </td>
                                                    <td>
                                                        <input name="tt_thanhtoan" type="text" disabled="disabled"
                                                               class="width100" id="tt_thanhtoan"/>
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td colspan="2">
                                                        <!--<input type="button" name="luufile" id="luufile" value="Chọn file" class="button_shadow"/>-->
                                                        <input type="button" name="luuthongtin" id="luuthongtin"
                                                               value="Lưu thông tin" class="button_shadow"
                                                               style="width: 110px"/>
                                                        <input type="button" name="inphieu" id="inphieu"
                                                               value="In phiếu" class="button_shadow"/>
                                                        <input type="button" name="phieu_cdha_noisoi" id="phieu_cdha_noisoi"
                                                               value="In phiếu CĐ" class="button_shadow"/>
                                                        <!--CMU: 26062017-->
                                                        <input name="lichsuCDHA" type="button" class="button_shadow"
                                                               id="lichsuCDHA" style="width: 150px;" value="Lịch sử NS">
                                                        <input type="button" name="luu_tt_maycdha" id="luu_tt_maycdha"
                                                               value="Lưu máy thực hiện" style="width: 150px;"
                                                               class="button_shadow"/>
                                                        <c:if test="${Sess_DVTT == '96001' || Sess_DVTT == '96168' || Sess_DVTT == '96014' || Sess_DVTT == '96066'}">
                                                            <input type="button" name="in_giay_cam_doan" id="in_giay_cam_doan"
                                                                   value="In giấy cam đoan" class="button_shadow" style="width: 150px"/>
                                                        </c:if>
                                                        <input name="sttbenhan" type="hidden" id="sttbenhan"/>
                                                        <input name="sttdotdieutri" type="hidden" id="sttdotdieutri"/>
                                                        <input name="sttdieutri" type="hidden" id="sttdieutri"/>
                                                        <input name="ngaybatdau" type="hidden" id="ngaybatdau"/>
                                                        <input name="ngayketthuc" type="hidden" id="ngayketthuc"/>
                                                        <input name="noidk" type="hidden" id="noidk"/>
                                                        <input name="namsinh" type="hidden" id="namsinh"/>
                                                        <input name="nguoithuchien" type="hidden" id="nguoithuchien"/>
                                                    </td>
                                                </tr>
                                            </table>

                                        </fieldset>
                                    </div>
                                    <div style="padding-top: 5px">
                                        <table id="list_noisoi_bhyt"></table>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </form>
                    <form id="formdsbn" name="formdsbn" action="">
                        <table width="100%">
                            <tr>
                                <td width="62px"> Từ ngày</td>
                                <td><input type="text" name="tungay1" id="tungay1"/></td>
                                <td width="62px">Đến ngày</td>
                                <td><input type="text" name="denngay1" id="denngay1"/></td>
                                <td colspan="2"><label><input type="checkbox" name="dathuchien1" id="dathuchien1">
                                    <b>Đã thực hiện</b></label></td>
                                <td colspan="2"><label><input type="checkbox" name="theobacsi" id="theobacsi" checked>
                                    <b>Theo bác sĩ</b></label></td>
                                <td width="65px"> Tổng số</td>
                                <td width="65px"><input type="text" disabled name="tongsoca" id="tongsoca"/></td>
                                <td>
                                    <input type="button" name="lammoi1" id="lammoi1" value="Làm mới"/>
                                </td>
                            </tr>
                            <tr>
                                <table id="grid_dschidinh"></table>
                            </tr>
                        </table>
                    </form>
                </div>
                <div id="cdha_tabs_2">
                    <!--<form id="form2" name="form2" method="post" action="">
                                <table width="900" align="center">
                                    <tr valign="top">
                                        <td width="47%" align="right"><div id="say-cheese-container" style="border:solid 1px #666666; width:320px; height:240px">
                                            </div></td>
                                        <td width="53%"><div id="say-cheese-snapshots"><img src="<c:url value="/resources/webcam/camera_png.jpg" />" width="230px" height="230px"/></div></td>
                                        <td width="53%" rowspan="2"><table id="list_hinhanhnoisoi"></table></td>
                                    </tr>
                                    <tr>
                                        <td align="center"><input type="button" name="take-snapshot" id="take-snapshot" value="Chụp ảnh" class="button_shadow"/>
                                            <input type="button" name="save-snapshot" id="save-snapshot" value="Lưu ảnh" class="button_shadow"/></td>
                                        <td>&nbsp;</td>
                                    </tr>
                                </table>

                            </form>-->
                    <form id="form2" name="form2" method="post" action="">
                        <div>
                            <fieldset class="scheduler-border">
                                <legend class="scheduler-border">Chụp và lưu hình ảnh</legend>
                                <table width="100%">
                                    <tr>
                                        <td valign="top">
                                            <div id="say-cheese-container" title="Chụp % Lưu"
                                                 style="cursor:pointer; border:solid 1px #666666; width:320px; height:240px"></div>
                                        </td>
                                        <td rowspan="2" valign="top">
                                            <div id="say-cheese-snapshots"><img
                                                    src="<c:url value="/resources/webcam/camera_png.jpg" />"
                                                    width="230px" height="230px"/></div>
                                        </td>
                                        <td rowspan="2" valign="top">
                                            <div id=div_hinhanhnoisoi>
                                                <table id="list_hinhanhnoisoi"></table>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td valign="top">
                                            <input type="button" name="take-snapshot" id="take-snapshot" value="Chụp"
                                                   class="button_shadow" style="width: 70px;"/>
                                            <input type="button" name="take-save-snapshot" id="take-save-snapshot"
                                                   value="Chụp & Lưu" class="button_shadow"/>
                                            <input type="button" name="save-snapshot" id="save-snapshot" value="Lưu"
                                                   class="button_shadow" style="width: 70px;"/>
                                            <label class="button_shadow" style="height: 26px;" style="width: 70px;">
                                                Duyệt <input type="file" name="browse-file" id="browse-file"
                                                             style="display: none; "></label>
                                            <c:if test="${ekipBPC == 1}"><input type="button" id="ekip_BPC" value="Ekip"
                                                                                rowspan="2" class="button_shadow"
                                                                                onclick="openEkip()"></c:if>
                                            <input type="button" name="lsnoisoi" id="lsnoisoi" value="Lịch sử nội soi"
                                                   class="button_shadow" style="width: 110px;"/>
                                            <input type="button" name="cmuekip" onclick="opencmuekipt()" id="cmuekip" value="EKIP NS"
                                                   class="button_shadow"/>
                                        </td>

                                    </tr>
                                </table>
                            </fieldset>
                        </div>
                        <!-- ĐắkLắk (An Giang yêu cầu) - Ninh 09/12/2016: view thông tin hành chánh của BN lên form nhập kết quả -->
                        <div>
                            <fieldset class="scheduler-border">
                                <legend class="scheduler-border">Thông tin hành chính của bệnh nhân</legend>
                                <table width="100%">
                                    <tr>
                                        <td width="10%">Họ tên</td>
                                        <td width="40%"><input name="hoten_ct" type="text" disabled="disabled"
                                                               id="hoten_ct"
                                                               style="width: 320px; color: red; font-weight: bold"/>
                                        </td>
                                        <td>Tuổi
                                            <input name="tuoi_ct" type="text" disabled="disabled" id="tuoi_ct"
                                                   style="width: 80px"/>
                                            Giới tính
                                            <input name="gioitinh_ct" type="text" disabled="disabled" id="gioitinh_ct"
                                                   style="width: 70px"/>
                                            Mã y tế
                                            <input name="mabenhnhan_ct" type="text" disabled="disabled"
                                                   id="mabenhnhan_ct" style="width: 170px"/>
                                        </td>
                                    <tr>
                                        <td>Khoa</td>
                                        <td><input name="tenkhoa_ct" type="text" disabled="disabled"
                                                   style="width: 320px" id="tenkhoa_ct"/></td>
                                        <td>Số thẻ BHYT
                                            <input name="sothebhyt_ct" type="text" disabled="disabled"
                                                   style="width: 379px" id="sothebhyt_ct"/></td>
                                    </tr>
                                    <tr>
                                        <td>Thời gian chỉ định</td>
                                        <td colspan="2"><input name="ngaychidinh_kq" type="text" disabled="disabled" id="ngaychidinh_kq" size="10"
                                                               data-inputmask="'alias': 'date'"/>
                                            <input name="giochidinh_kq" type="text" disabled="disabled" id="giochidinh_kq" size="10"
                                                   data-inputmask="'alias': 'hh:mm:ss'"/></td>
                                    </tr>
                                </table>
                            </fieldset>
                        </div>
                        <!-- End ĐắkLắk -->
                        <div>
                            <fieldset class="scheduler-border">
                                <legend class="scheduler-border">Kết quả nội soi</legend>
                                <table width="100%">

                                    <tr>
                                        <td><label for="thoiGianBatDau_cls">Thời gian thực hiện y lệnh</label></td>
                                        <td colspan="6">
                                            <input type="text" id="thoiGianBatDau_cls"
                                            <%--                                               data-inputmask="'alias': 'datetime'"--%>
                                                   width="auto"/>
                                        </td>
                                    </tr>
                                    <c:if test="${thamso_184002 == '1'}">
                                        <tr>
                                            <td id="textThoiGianBatDau">Thời gian kết quả</td>
                                            <td colspan="6">
                                                <input name="ngayth_ct" type="text" id="ngayth_ct" size="10"
                                                       data-inputmask="'alias': 'date'"/>
                                                <input name="gioth_ct" type="text" id="gioth_ct" size="10"
                                                       data-inputmask="'alias': 'hh:mm:ss'"/>
                                            </td>
                                        </tr>
                                        <tr id="trThoiGianKetThuc">
                                            <td >Thời gian kết thúc thực hiện</td>
                                            <td colspan="6">
                                                <input name="ngayKtThucHienCdha" type="text" id="ngayKtThucHienCdha" size="10"
                                                       data-inputmask="'alias': 'date'"/>
                                                <input name="gioKtThucHienCdha" type="text" id="gioKtThucHienCdha" size="10"
                                                       data-inputmask="'alias': 'hh:mm:ss'"/>
                                            </td>
                                        </tr>
                                    </c:if>
                                    <tr>
                                        <td>Thời gian kết quả</td>
                                        <td colspan="6">
                                            <input name="ngaygioth_ct" type="text" id="ngaygioth_ct" width="auto"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Lý do nhập viện</td>
                                        <td colspan="6"><input name="lydonhapvien" type="text" class="width100"
                                                               id="lydonhapvien"/></td>
                                    </tr>
                                    <tr>
                                        <td width="12%">Chẩn đoán</td>
                                        <!-- ĐắkLắk (An Giang yêu cầu) - Ninh 09/12/2016: mở rộng textbox hiện thông tin chẩn đoán -->
                                        <td colspan="6"><textarea name="chandoan" rows="2" class="width100"
                                                                  id="chandoan"></textarea></td>
                                        <!-- End ĐắkLắk -->
                                    </tr>
                                    <tr>
                                        <td>Chẩn đoán CLS</td>
                                        <td colspan="6">
                                            <input name="maBenhLyChanDoanCanLamSang" type="hidden" id="maBenhLyChanDoanCanLamSang"/>
                                            <input name="icdChanDoanCanLamSang" type="text" class="icd" id="icdChanDoanCanLamSang" style="width: 50px"/>
                                            <input name="btnChanDoanCanLamSang" type="button" id="btnChanDoanCanLamSang" class="icd" value="..."/>
                                            <label>
                                                <input id="tenChanDoanCanLamSang" name="tenChanDoanCanLamSang" class="icd" style="width:342px"/>
                                            </label>
                                        </td>
                                    </tr>
                                    <tr class = "icdTruocSau">
                                        <td>Chẩn đoán trước cdha<span style="color: red">(*):</span></td>
                                        <input name="mabenhly_truoccdha" type="hidden" id="mabenhly_truoccdha" />
                                        <td colspan="6">
                                            <input name="icd_truoccdha" type="text"  style="width:10%"  id="icd_truoccdha" />
                                            <input name="chandoan_truoccdha" type="text" style="width:89%"  id="chandoan_truoccdha" />
                                        </td>
                                    </tr>
                                    <tr class = "icdTruocSau">
                                        <td>Chẩn đoán sau cdha<span style="color: red">(*):</span></td>
                                        <input name="mabenhly_saucdha" type="hidden" id="mabenhly_saucdha"/>
                                        <td colspan="6">
                                            <input name="icd_saucdha" type="text"  style="width:10%"  id="icd_saucdha" />
                                            <input name="chandoan_saucdha" type="text" style="width:89%"  id="chandoan_saucdha" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Tiền sử bệnh</td>
                                        <td colspan="6"><input name="tiensubenh" type="text" class="width100"
                                                               id="tiensubenh"/></td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td width="14%"><input type="checkbox" name="nschandoan" id="nschandoan">
                                            NS chẩn đoán
                                        </td>
                                        <td width="14%"><input type="checkbox" name="nsdieutri" id="nsdieutri">
                                            NS điều trị
                                        </td>
                                        <td width="12%"><input type="checkbox" name="sinhthiet" id="sinhthiet">
                                            Sinh thiết
                                        </td>
                                        <td width="15%"><input type="checkbox" name="giaiphaubenh" id="giaiphaubenh">
                                            Giải phẫu bệnh
                                        </td>
                                        <td width="5%" align="right">Máy</td>
                                        <td width="28%">
                                            <input type="text" name="may" id="may" class="width100"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td><input type="checkbox" name="capcuu" id="capcuu">
                                            Cấp cứu
                                        </td>
                                        <td><input type="checkbox" name="nslaydivat" id="nslaydivat">
                                            NS lấy dị vật
                                        </td>
                                        <td><input type="checkbox" name="clotest" id="clotest">
                                            Clotest
                                        </td>
                                        <td>&nbsp;</td>
                                        <td align="right">Thuốc</td>
                                        <td>
                                            <input type="text" name="thuoc" id="thuoc" class="width100"/>
                                    </tr>
                                    <tr>
                                        <td>Đường vào</td>
                                        <td colspan="3"><input type="text" name="duongvao" id="duongvao"
                                                               class="width100"/></td>
                                    </tr>
                                    <tr>
                                        <td width="167">Mã phương pháp vô cảm (CV130) <span style="color: red">(*):</span></td>
                                        <td colspan="4">
                                            <select name="ma_pp_vo_cam" id="ma_pp_vo_cam" class="width100">
                                                <option value="" >Chọn mã phương pháp</option>
                                                <option value="1">Gây mê</option>
                                                <option value="2">Gây tê</option>
                                                <option value="3">Châm tê</option>
                                                <option value="4">Các phương pháp vô cảm khác</option>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Người đọc kết quả</td>
                                        <td colspan="6">
                                            <select id="cboNguoiDocKetQua">
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Mẫu nội soi</td>
                                        <td colspan="6"><select name="maunoisoi" class="width100" id="maunoisoi">
                                            <option value="-1" selected>-- Chọn mẫu nội soi --</option>
                                            <c:forEach var="i" items="${maunoisoi}">
                                                <option value="${i.ma_maunoisoi}"> ${i.ten_maunoisoi}</option>
                                            </c:forEach>
                                        </select></td>
                                    </tr>
                                    <c:forEach var="i" items="${formnoisoi}">
                                        <tr id="checkbox_${i.MA_MAUNOISOI}" style="display:none;">
                                            <td colspan="8" style="text-align: left" id="checks_${i.MA_MAUNOISOI}">
                                                    <%--$("#tab_sieuam_"+1).tabs();--%>
                                                <div id="tab_noisoi_${i.MA_MAUNOISOI}">
                                                    <ul>
                                                        <c:forEach var="a" items="${nstq}">
                                                            <c:if test="${a.MOTA_LOAICHECKBOX == i.MOTA && (a.GIOITINH == i.GIOITINH or a.GIOITINH == '-1')}">
                                                                <li><a href="#tab_${a.MA_LOAI_CHECKBOX}"
                                                                       id="tq_${a.MA_LOAI_CHECKBOX}">${a.TEN_LOAI_CHECKBOX}</a>
                                                                </li>
                                                            </c:if>
                                                        </c:forEach>
                                                    </ul>
                                                    <c:forEach var="b" items="${nstq}">
                                                        <c:if test="${b.MOTA_LOAICHECKBOX == i.MOTA && (b.GIOITINH == i.GIOITINH or b.GIOITINH == '-1')}">
                                                            <div id="tab_${b.MA_LOAI_CHECKBOX}">
                                                                <c:forEach var="c" items="${checkbox_noisoi}">
                                                                    <c:if test="${c.LOAI_CHECKBOX == b.MA_LOAI_CHECKBOX}">
                                                                        <span style="display:inline-block;"><input
                                                                                type="checkbox" value="${c.MA_CHECKBOX}"
                                                                                style="margin-left: 20px"
                                                                                id="nstq_${c.MA_CHECKBOX}"/> ${c.TEN_CHECKBOX}</span>
                                                                    </c:if>
                                                                </c:forEach>
                                                            </div>
                                                        </c:if>
                                                    </c:forEach>
                                                </div>
                                            </td>
                                        </tr>
                                    </c:forEach>
                                    <tr>
                                        <td>Mô tả</td>
                                        <td colspan="6">&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td colspan="7"><textarea name="ketqua" cols="80" rows="10" class="width100"
                                                                  id="ketqua"></textarea>
                                            <script type="text/javascript" language="javascript">
                                                CKEDITOR.replace('ketqua');
                                            </script>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Kết luận</td>
                                        <td colspan="6">
                                            <textarea name="ketluan" rows="2" class="width100" id="ketluan"></textarea>
                                            <script type="text/javascript" language="javascript">
                                                CKEDITOR.replace('ketluan', {height: '100px'});

                                            </script>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Lời dặn bác sĩ</td>
                                        <td colspan="6"><input name="denghi" type="text" class="width100" id="denghi"/>
                                        </td>
                                    </tr>
                                    <tr hidden="hidden">
                                        <td colspan="2">
                                            <div>
                                                <table width="300">
                                                    <tr>
                                                        <td width="70">Chọn kho:
                                                            <input name="matoathuoc_dv" type="hidden"
                                                                   id="matoathuoc_dv"/></td>
                                                        <td>
                                                            <select name="kho_dv" id="kho_dv" style="width:220px">
                                                                <c:forEach var="i" items="${khodichvu}">
                                                                    <option value="${i.MAKHO}">${i.TENKHO}</option>
                                                                </c:forEach>
                                                            </select>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                            <div style="width:300px">

                                                <div class="table_beauty">
                                                    <c:if test="${AnNhapThuocToaDichVu == '0'}">
                                                        <table width="300px">
                                                            <thead>
                                                            <tr>
                                                                <th width="132">
                                                                    Tên thuốc, vật tư
                                                                </th>
                                                                <th width="51">ĐVT</th>
                                                                <th width="72"><input name="dongia_dv" type="hidden"
                                                                                      id="dongia_dv"/>
                                                                    Số lượng
                                                                </th>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                            <tr id="thuocdichvu_tt_div">
                                                                <td><input name="makhovattu_dv" type="hidden"
                                                                           id="makhovattu_dv"/><input name="mavattu_dv"
                                                                                                      type="hidden"
                                                                                                      id="mavattu_dv"/>
                                                                    <input name="sott_toathuoc_dv" type="hidden"
                                                                           id="sott_toathuoc_dv"/>
                                                                    <input name="tenthuongmai_dv" type="text"
                                                                           id="tenthuongmai_dv" size="13"
                                                                           class="width_100per"/></td>
                                                                <td><input name="dvt_dv" type="text" id="dvt_dv"
                                                                           size="4" readonly class="width_100per"/></td>
                                                                <td><input name="soluong_dv" type="text" id="soluong_dv"
                                                                           size="12" onKeyPress="validate_number(event)"
                                                                           class="width_100per"/></td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </c:if>
                                                </div>
                                                <div id="thuocdichvu_div" style="width:300px">
                                                    <table id="list_thuocdichvu" width="300"></table>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td colspan="6">
                                            <input name="luu_tt" type="button" class="button_shadow" id="luu_tt"
                                                   value="Lưu"/>
                                            <input name="inphieu_noisoi" type="button" class="button_shadow"
                                                   id="inphieu_noisoi" value="In phiếu"/>
                                            <input type="button" name="luuinphieu" id="luuinphieu" value="Lưu và In"
                                                   class="button_shadow"/>
                                            <input type="hidden" name="luuinphieu_status" id="luuinphieu_status"
                                                   value="0"/>
                                            <input style="width: 150px" type="button" onclick='kyketquanoisoi("${Sess_UserID}", "${Sess_DVTT}")' class="button_shadow" value="Ký Số SMARTCA">
                                            <input style="width: 150px" type="button" onclick='inketquanoisoi("${Sess_UserID}", "${Sess_DVTT}")' class="button_shadow" value="In Ký Số SMARTCA">
                                            <input style="width: 150px" type="button" onclick='kyketquasieuamtoken("${Sess_UserID}")' class="button_shadow" value="Ký Số Token">
                                            <input style="width: 150px" type="button" onclick='cmuinkisotokensieuamkq("${Sess_DVTT}")' class="button_shadow" value="In Ký Số Token">
                                            <input name="chuyen_cd_noisoi" type="button" class="button_shadow"
                                                   style="width: 120px" id="chuyen_cd_noisoi" value="Chuyển chỉ định"/>
                                            <input type="button" name="viewimageweb" id="viewimageweb"
                                                   value="Xem ảnh Web" class="button_shadow" style="width: auto"/>
                                            <input type="button" name="viewimageapp" id="viewimageapp"
                                                   value="Xem ảnh App " class="button_shadow" style="width: auto"/>
                                        </td>
                                    </tr>
                                </table>
                                <div style="display: none">
                                    <textarea name='temp' id='temp' cols="1" rows="1"></textarea>
                                    <script type="text/javascript" language="javascript">
                                        CKEDITOR.replace('temp');
                                    </script>
                                </div>
                            </fieldset>
                        </div>
                    </form>
                </div>
            </div>
        </div> <!--end of center_content-->
        <%@include file="../../../resources/Theme/include_pages/footer.jsp"%>
    </div>
</div>
<div id="dialog_lichsuCDHA" title="Lịch sử nội soi" style="display: none">
    <div id="tab_ls_cdha">
        <ul>
            <li><a href="#cdha_ls_tabs_1" id="xn_cobhyt">Thông tin bệnh nhân</a></li>
            <li style="display:none"><a href="#cdha_ls_tabs_2" id="xn_bnyc">Kết quả</a></li>
        </ul>
        <div id="cdha_ls_tabs_1">
            <table id="list_lichsuCDHA"></table>
        </div>
        <div id="cdha_ls_tabs_2" style="display:none">
            <form id="form2" name="form2" method="post" action="">
                <div>
                    <fieldset class="scheduler-border">
                        <legend class="scheduler-border">Kết quả nội soi</legend>
                        <table width="100%">
                            <tr>
                                <td width="11%">Chẩn đoán</td>
                                <td width="89%"><input name="ls_chandoan" type="text" class="width100"
                                                       id="ls_chandoan"/></td>
                            </tr>
                            <tr>
                                <td>Kết quả</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                        <textarea name="ls_ketqua" cols="80" rows="10" class="width100" id="ls_ketqua">

                                        </textarea>
                                </td>
                            </tr>
                            <tr>
                                <td>Kết luận</td>
                                <td>
                                    <textarea name="ls_ketluan" rows="2" class="width100" id="ketluan"></textarea>
                                </td>
                            </tr>
                            <tr>
                                <td>Lời dặn bác sĩ</td>
                                <td><input name="ls_loidanbacsi" type="text" class="width100" id="loidanbacsi"/></td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>
                                    <input name="ls_inphieu_noisoi" type="button" class="button_shadow"
                                           id="ls_inphieu_noisoi" value="In phiếu"/>
                                    <input name="ls_inphieu_noisoi_ngang" type="button" class="button_shadow"
                                           id="ls_inphieu_noisoi_ngang" style="width: 150" value="In phiếu (ngang)"/>
                                </td>
                            </tr>
                        </table>

                    </fieldset>
                </div>
            </form>
            <div id="dialog_capnhatketquattpt_ekip" title="Ekip phẫu thuật">
                <form id="form_Ekip" name="form_Ekip" method="post" action="">
                    <input id="frm_kq_id_ekip" class="post-data" key="ID_EKIP" hidden/>
                    <input id="frm_kq_so_phieu_dichvu" class="post-data" key="SO_PHIEU_DICHVU" hidden/>
                    <input id="frm_kq_ma_dv" class="post-data" key="MA_DV" hidden/>
                    <input id="frm_kq_mabenhnhan" class="post-data" key="MABENHNHAN" hidden/>
                    <input id="frm_kq_sovaovien" class="post-data" key="SOVAOVIEN" hidden/>
                    <input id="frm_kq_sovaovien_dt" class="post-data" key="SOVAOVIEN_DT" hidden/>
                    <input id="frm_kq_noitru" class="post-data" key="NOITRU" hidden/>
                    <input id="frm_kq_id_dieutri" class="post-data" key="ID_DIEUTRI" hidden/>

                    <table style="width:650px">

                        <tr>
                            <td width="150">Mã nhân viên<input name="ma_nhanvien" type="text" id="ma_nhanvien"
                                                               class="post-data" key="MA_NHANVIEN_EKIP"
                                                               style="color: blue; font-weight:  bold; width: 100px"/>
                            <td width="200">Tên nhân viên<input name="ten_nhanvien" type="text" id="ten_nhanvien"
                                                                style="color: blue; font-weight:  bold"/></td>
                            <td width="200">Vai trò<input name="ma_vaitro" type="hidden" id="ma_vaitro"
                                                          class="post-data" key="MA_VAITRO_EKIP"
                                                          style="color: blue; font-weight:  bold"/>
                                <input name="ten_vaitro" type="text" id="ten_vaitro"
                                       style="color: blue; font-weight:  bold"/></td>
                        </tr>
                        <tr>
                            <table id="list_chitietvaitrothuchien" style="font-size: 12px"></table>
                        </tr>
                        <tr>

                            <td align="right">
                                <%--<input type="button" name="ekip_CapNhat" id="ekip_CapNhat" value="Cập nhật" onclick="capNhatEkip(1)" class="button_shadow">--%>
                                <input type="button" name="ekip_Xoa" id="ekip_Xoa" value="Xoá Ekip"
                                       onclick="capNhatEkip(0)" class="button_shadow">
                            </td>


                        </tr>
                    </table>
                </form>
                <div id="dialog-benhly" title="Danh mục bệnh lý" style="display: none;">
                    <table id="list_benhly"></table>
                    <div id="pager_benhly"></div>
                </div>
            </div>
        </div>
    </div>
    <div id="ekip" style="display: none">
        <form id="tiencong" name="tiencong" method="post" action="">
            <table width="600" border="0">
                <tr>
                    <td colspan="2">
                        <table width="600" border="0">
                            <tr>
                                <td width="100" align="right" style="font-size: 12px">Phòng ban</td>
                                <td><select name="khoaphong_ekip" id="khoaphong_ekip" style="width: 200px">
                                    <c:forEach var="i" items="${phongban}">
                                        <option value="${i.MA_PHONGBAN}">${i.TEN_PHONGBAN}</option>
                                    </c:forEach>
                                </select>
                                </td>
                                <td width="100" align="right" style="font-size: 12px">Thực hiện</td>
                                <td><select name="nguoithuchien_ekip" id="nguoithuchien_ekip" style="width: 200px">
                                    <option value="-1">-- Chọn nhân viên --</option>
                                </select>
                                </td>
                                <td><input name="manhanvien" type="hidden" id="manhanvien"/></td>
                            </tr>
                            <tr>
                                <td width="100" align="right" style="font-size: 12px">Ekip mẫu</td>
                                <td><select name="ekip_mau" id="ekip_mau" style="width: 200px">
                                    <option value="-1">-- Chọn ekip --</option>
                                </select>
                                </td>
                                <td width="70" align="right" style="font-size: 12px">Vai trò</td>
                                <td><select name="tiencongthuchien" id="tiencongthuchien" style="width: 200px">
                                    <option value="-1">-- Chọn vai trò --</option>
                                </select>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4" align="center">
                                    <input type="button" class="button_shadow" name="themnguoithuchien"
                                           id="themnguoithuchien" value="Thêm"/>
                                    <input type="button" class="button_shadow" name="xoanguoithuchien"
                                           id="xoanguoithuchien" value="Xóa"/>
                                    <input type="button" class="button_shadow" name="xoanguoithuchien_all"
                                           id="xoanguoithuchien_all" value="Xóa tất cả"/></td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <table id="list_chitiettiencongthuchien" style="font-size: 12px"></table>
                </tr>
            </table>
        </form>
    </div>
    <jsp:include page="../Canlamsang/dialog/diaglog-tim-kiem-icd.jsp"/>
</div>
<%@include file="../camau/canlamsang/cmuekipnoisoi.jsp" %>
</body>
</html>
