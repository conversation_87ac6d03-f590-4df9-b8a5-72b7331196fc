create or replace FUNCTION          HIS_MANAGER."CMU_GET_KYTHUATPHCN" (
    p_dvtt              IN     VARCHAR2,
    p_so_vao_vien      IN     NUMBER
) RETURN SYS_REFCURSOR IS
    cur         SYS_REFCURSOR;
BEGIN
OPEN cur FOR SELECT
        phieu.ID,
        phieu.DVTT,
        phieu.SOVAOVIEN,
        phieu.MA_BENH_NHAN,
        TO_CHAR(phieu.THOIGIAN, 'dd/MM/yyyy hh24:mi') THOIGIAN,
        phieu.THOIGIANPHUT,
        phieu.DIENBIENBENH,
        phieu.TENDICHVU,
        phieu.NGUOITHUCHIEN,
        phieu.BACSICHIDINH,
        TO_CHAR(phieu.NGAY_TAO_PHIEU, 'dd/MM/yyyy') NGAY_TAO_PHIEU,
        phieu.NGUOI_TAO,
        phieu.STT_DIEUTRI,
        nv.TEN_NHANVIEN TENNGUOITAO,
        nv1.TEN_NHANVIEN TENNGUOITHUCHIEN,
        nv2.TEN_NHANVIEN TENBACSICHIDINH,
        signkcb.keysign
     FROM CMU_KYTHUATPHCN phieu
            LEFT JOIN HIS_FW.DM_NHANVIEN nv ON phieu.NGUOI_TAO = nv.MA_NHANVIEN
            LEFT JOIN HIS_FW.DM_NHANVIEN nv1 ON phieu.NGUOITHUCHIEN = nv1.MA_NHANVIEN
            LEFT JOIN HIS_FW.DM_NHANVIEN nv2 ON phieu.BACSICHIDINH = nv2.MA_NHANVIEN
            LEFT JOIN smartca_signed_kcb signkcb ON phieu.dvtt = signkcb.dvtt
                                                                     AND phieu.SOVAOVIEN = signkcb.SOVAOVIEN
--                                                                      AND signkcb.so_phieu_dv = phieu.ID
                                                                     AND signkcb.status = 0
                                                                     AND signkcb.ky_hieu_phieu IN ('PHIEU_NOITRU_KYTHUATPHCN')
     WHERE phieu.DVTT = p_dvtt AND phieu.SOVAOVIEN = p_so_vao_vien
    ORDER BY phieu.ID desc;
RETURN cur;
END;