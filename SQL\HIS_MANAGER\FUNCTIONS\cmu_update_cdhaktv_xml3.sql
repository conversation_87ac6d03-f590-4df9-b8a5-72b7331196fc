CREATE OR REPLACE FUNCTION cmu_update_cdhaktv_xml3 (
    p_dvtt         VARCHAR2,
    p_sovaovien    VARCHAR2,
    p_sophieu      VARCHAR2,
    p_madv         VARCHAR2,
    p_manhanvien   VARCHAR2,
    p_ktv          VARCHAR2,
    p_user         VARCHAR2
) RETURN VARCHAR2 IS
    v_mabs      NUMBER;
    p_thaotac   VARCHAR2(255) := '';
BEGIN
    p_thaotac := 'THAYDOIKTVBACSICDHA';
    IF p_ktv = 1 THEN
        p_thaotac := 'người thực hiện ';
        IF p_sophieu LIKE '%/%' THEN
SELECT
    kythuatvien
INTO v_mabs
FROM
    noitru_cd_cdha_chi_tiet
WHERE
    sovaovien = p_sovaovien
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_madv;

UPDATE noitru_cd_cdha_chi_tiet
SET
    kythuatvien = p_manhanvien
WHERE
    sovaovien = p_sovaovien
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_madv;

ELSE
SELECT
    kythuatvien
INTO v_mabs
FROM
    kb_cd_cdha_ct
WHERE
    sovaovien = p_sovaovien
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_madv;

UPDATE kb_cd_cdha_ct
SET
    kythuatvien = p_manhanvien
WHERE
    sovaovien = p_sovaovien
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_madv;

END IF;

ELSE
        p_thaotac := ' người đọc kết quả ';
        IF p_sophieu LIKE '%/%' THEN
SELECT
    nguoi_thuc_hien
INTO v_mabs
FROM
    noitru_cd_cdha_chi_tiet
WHERE
    sovaovien = p_sovaovien
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_madv;

UPDATE noitru_cd_cdha_chi_tiet
SET
    nguoi_thuc_hien = p_manhanvien
WHERE
    sovaovien = p_sovaovien
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_madv;

ELSE
SELECT
    nguoi_thuc_hien
INTO v_mabs
FROM
    kb_cd_cdha_ct
WHERE
    sovaovien = p_sovaovien
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_madv;

UPDATE kb_cd_cdha_ct
SET
    nguoi_thuc_hien = p_manhanvien
WHERE
    sovaovien = p_sovaovien
  AND so_phieu_cdha = p_sophieu
  AND ma_cdha = p_madv;

END IF;

END IF;

UPDATE cmu_thoigian_nhanvien
SET
    manhanvien = p_manhanvien
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND manhanvien = v_mabs
  AND noitru =
      CASE
          WHEN p_sophieu LIKE '%/%' THEN
              1
          ELSE
              0
          END
  AND sophieu = p_sophieu
  AND ma_dv = p_madv;

cmu_log(p_dvtt, 'Thay đổi '
                    || p_thaotac
                    || p_sophieu
                    || ' - MADV: '
                    || p_madv
                    || ' -> Mã nhân viên cũ: '
                    || v_mabs
                    || ' sang mã nhân viên mới: '
                    || p_manhanvien, p_user, p_thaotac);

RETURN '0';
END;