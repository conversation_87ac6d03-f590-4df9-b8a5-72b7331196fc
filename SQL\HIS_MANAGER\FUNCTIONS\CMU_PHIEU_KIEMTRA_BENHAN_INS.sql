create or replace FUNCTION "HIS_MANAGER"."CMU_PHIEU_KIEMTRA_BENHAN_INS" (
    p_DVTT IN VARCHAR2,
    p_MA_<PERSON>ENHNHAN IN VARCHAR2,
    p_<PERSON><PERSON><PERSON>_PHIEU IN CLOB,
    p_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> IN VARCHAR2,
    p_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_DT IN VARCHAR2,
    p_STT_BENHAN IN VARCHAR2,
    p_NGUOI_TAO IN VARCHAR2,
    p_<PERSON>HOA_TAO IN VARCHAR2
) RETURN NUMBER IS
    v_id CMU_PHIEU_KIEMTRA_BENHAN.id%TYPE;
BEGIN
    INSERT INTO "HIS_MANAGER"."CMU_PHIEU_KIEMTRA_BENHAN" (
        DVTT, MA_BENHNHAN, DATA_PHIEU, createdDate,
        SO<PERSON><PERSON>VIEN, SOVAOVIEN_DT, STT_BENHAN, NGUOI_TAO, KHOA_TAO
    ) VALUES (
        p_<PERSON>VTT, p_MA_BENHNHAN, p_<PERSON><PERSON><PERSON>_PHIEU, <PERSON><PERSON><PERSON><PERSON><PERSON>,
        p_<PERSON>O<PERSON>OVIEN, p_SO<PERSON>O<PERSON>EN_DT, p_STT_BENHAN, p_NGUOI_TAO,
        p_KHOA_TAO
    )
    RETURNING id INTO v_id;

RETURN v_id;
END CMU_PHIEU_KIEMTRA_BENHAN_INS;