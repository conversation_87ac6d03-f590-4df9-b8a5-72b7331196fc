create or replace FUNCTION HIS_MANAGER."CMU_DT_EMR_GET_INFO" (
    p_dvtt           IN               VARCHAR2,
    p_sophieu        IN               VARCHAR2,
    p_sovaovien      IN               VARCHAR2,
    p_sovaovien_dt   IN               VARCHAR2,
    p_stt_<PERSON>han     IN               VARCHAR2,
    p_noitru     IN               VARCHAR2
) RETURN SYS_REFCURSOR IS

    cur                    SYS_REFCURSOR;
    v_keysign              VARCHAR2(500) := '';
    v_giuong varchar2(255):=' ';
    v_buong varchar2(255):=' ';
BEGIN

BEGIN
SELECT STT_GIUONG, STT_BUONG into v_giuong , v_buong
from CMU_SOBUONGGIUONG
where DVTT = p_dvtt AND STT_BENHAN = p_stt_benhan
  and rownum = 1;
EXCEPTION
		when no_data_found then
		v_giuong:=' ';v_buong:=' ';
END;

BEGIN
SELECT
    kcb.keysign
INTO v_keysign
FROM
    smartca_signed_kcb kcb
WHERE
        kcb.dvtt = p_dvtt
  AND kcb.sovaovien = p_sovaovien
  AND kcb.sovaovien_dt = p_sovaovien_dt
  AND kcb.so_phieu_dv = p_sophieu
  AND kcb.ky_hieu_phieu = 'PHIEUKQ_DIENTIM'
  AND kcb.status IN (
                     0,
                     2
    );

EXCEPTION
        WHEN no_data_found THEN
            v_keysign := '';
END;

    IF p_noitru = 0 THEN
        OPEN cur FOR SELECT
--                         tn.so_the_bhyt   AS sothebhyt,
--                         tn.co_bao_hiem,
--                         ptt.sophieuthanhtoan,
--                         tn.ti_le_mien_giam,
--                         TO_CHAR(kb.ngay_kb, 'YYYY-MM-DD') AS ngay_kb,
--                         tn.mienphi,
--                         '0' thanhtoanmoney,
--                         v_chandoanbenh   chandoan,
v_keysign        keysign,
CT.MO_TA,
to_char(CT.ket_qua) KET_QUA,
CT.LOIDANBACSI,
CT.ma_mausieuam,
CT.mau_sieuam,
nvl(CT.da_chan_doan, 0) as da_chan_doan,
ct.MABENHLY_TRUOCCDHA,
ct.MABENHLY_SAUCDHA,
ct.CHANDOAN_TRUOCCDHA,
ct.CHANDOAN_SAUCDHA,
ct.MA_BENH_LY_THEO_ICD,
ct.MA_ICD,
ct.TEN_ICD,
ct.don_gia,
to_char(ct.NGAY_TH_YL, 'dd/mm/yyyy hh24:mi:ss') as NGAY_TH_YL,
to_char(ct.ngay_thuc_hien, 'dd/mm/yyyy hh24:mi:ss') as NGAY_GIO_TH,
NVL(CT.STT_MAYCDHA,CDHA.STT_MAYCDHA) AS STT_MAYCDHA,
ct.KYTHUATVIEN,
' ' BUONG,
' ' AS GIUONG
                     FROM
                                kb_kham_benh                   kb
                                    INNER JOIN KB_CD_CDHA   cd ON kb.dvtt = cd.dvtt
                                    AND cd.so_phieu_cdha = p_sophieu
                                    inner join KB_CD_CDHA_CT CT on cd.dvtt = ct.dvtt and cd.sovaovien = ct.sovaovien and cd.so_phieu_cdha = ct.so_phieu_cdha
                                    inner join CLS_CDHA CDHA on ct.dvtt = cdha.dvtt and ct.ma_cdha = cdha.ma_cdha
                     WHERE
                                    kb.dvtt = p_dvtt
                       AND kb.sovaovien = p_sovaovien;

ELSE
        OPEN cur FOR SELECT
--                         dot.sobaohiemyte   AS sothebhyt,
--                         dot.cobhyt         AS co_bao_hiem,
--                         '' AS sophieuthanhtoan,
--                         dot.tylebaohiem    AS ti_le_mien_giam,
--                         NULL AS ngay_kb,
--                         0 mienphi,
--                         '0' thanhtoanmoney,
--                         v_chandoanbenh     chandoan,
v_keysign          keysign,
CT.MO_TA,
to_char(CT.ket_qua) KET_QUA,
CT.LOIDANBACSI,
CT.ma_mausieuam,
CT.mau_sieuam,
nvl(CT.da_chan_doan, 0) as da_chan_doan,
ct.MABENHLY_TRUOCCDHA,
ct.MABENHLY_SAUCDHA,
ct.CHANDOAN_TRUOCCDHA,
ct.CHANDOAN_SAUCDHA,
ct.MA_BENH_LY_THEO_ICD,
ct.MA_ICD,
ct.TEN_ICD,
ct.don_gia,
to_char(ct.NGAY_TH_YL, 'dd/mm/yyyy hh24:mi:ss') as NGAY_TH_YL,
to_char(ct.ngay_thuc_hien, 'dd/mm/yyyy hh24:mi:ss') as NGAY_GIO_TH,
NVL(CT.STT_MAYCDHA,CDHA.STT_MAYCDHA) AS STT_MAYCDHA,
ct.KYTHUATVIEN,
v_buong as BUONG,
v_giuong as GIUONG
--               gb.STT_BUONG BUONG,
--               gb.STT_GIUONG AS GIUONG
                     FROM
                         his_manager.noitru_dotdieutri      dot
                             inner join NOITRU_CD_CDHA CD ON dot.dvtt = cd.dvtt AND cd.so_phieu_cdha = p_sophieu
--                        LEFT JOIN CMU_SOBUONGGIUONG gb ON gb.dvtt= p_dvtt AND gb.STT_BENHAN = CD.STT_BENHAN
                             inner join NOITRU_CD_CDHA_CHI_TIET CT on cd.dvtt = ct.dvtt and cd.sovaovien = ct.sovaovien and cd.sovaovien_dt = ct.sovaovien_dt and cd.so_phieu_cdha = ct.so_phieu_cdha
                             inner join CLS_CDHA CDHA on ct.dvtt = cdha.dvtt and ct.ma_cdha = cdha.ma_cdha
                     WHERE
                             dot.dvtt = p_dvtt
                       AND dot.sovaovien = p_sovaovien
                       AND dot.sovaovien_dt = p_sovaovien_dt
                       AND dot.stt_benhan = p_stt_benhan;

END IF;

RETURN cur;
END;