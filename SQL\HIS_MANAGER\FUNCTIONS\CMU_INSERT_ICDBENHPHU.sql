create or replace FUNCTION HIS_MANAGER.CMU_INSERT_ICDBENHPHU(
p_dvtt varchar2,
p_sovaovien varchar2,
p_icd varchar2,
p_tenbenh varchar2,
p_nguoitao varchar2,
p_maphongchidinh varchar2
) RETURN  VARCHAR2
IS
v_benhphu varchar2(2500):='';
v_exist number(10):=0;
BEGIN
select count(1) into v_exist from HIS_PUBLIC_LIST.DM_BENH_LY
where UPPER(ICD)=UPPER(p_icd);
if v_exist > 0 then
	insert into CMU_DSICD_BENHPHU
		(DVTT, SOVAOVIEN, ICD, TENBENH, NGUOICHIDINH, MAPHONGCHIDINH, NGAYGIOTAO)
		VALUES (p_dvtt, p_sovaovien, p_icd, p_tenbenh, p_nguoitao, p_maphongchidinh, sysdate);
commit;
select
    LISTAGG('('||ICD||') '|| TENBENH, '; ') WITHIN GROUP (ORDER BY ICD) into v_benhphu
from
    CMU_DSICD_BENHPHU
where dvtt = p_dvtt and SOVAOVIEN = p_sovaovien and nvl(benhchinh,0) = 0;
update KB_KHAM_BENH
set BENH_PHU = ';'||REGEXP_REPLACE(REGEXP_REPLACE(v_benhphu,'(=)','+'),'(encode_symbol_0025)','%')
where dvtt = p_dvtt and SOVAOVIEN = p_sovaovien;
end if;
RETURN '0';
END;