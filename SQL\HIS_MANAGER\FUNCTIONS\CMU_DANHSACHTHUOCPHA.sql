create or replace FUNCTION "HIS_MANAGER"."CMU_DANHSACHTHUOCPHA" (
    p_sovaovien        IN                 NUMBER,
    p_id_dieutri       IN                 NUMBER,
    p_stt_dieutri      IN                 NUMBER,
    p_stt_dotdieutri   IN                 NUMBER,
    p_dvtt             IN                 VARCHAR2
) RETURN SYS_REFCURSOR IS
    cur SYS_REFCURSOR;
BEGIN
OPEN cur FOR SELECT
                     pt.id_phathuoc,
                     pt.id_dieutri,
                     pt.stt_<PERSON>han,
                     pt.stt_dotdieutri,
                     pt.stt_dieutri,
                     pt.sovaovien,
                     pt.<PERSON><PERSON><PERSON><PERSON>,
                     pt.g<PERSON>hu,
                     pt.solo_tong,
                     pt.soluong<PERSON>_tong,
                     pt.loai_ylenh,
                     pt.loai_tocdo,
                     pt.tocdo,
                     pt.solu<PERSON><PERSON><PERSON><PERSON>,
                     CASE
                         WHEN pt.loai_ylenh = 'truyendich' THEN
                             'Truyền dịch '
                             || pt.soluong<PERSON><PERSON><PERSON>
                             || ' ('
                             || pt.tocdo
                             || ' '
                             || (
                                 CASE
                                     WHEN pt.loai_tocdo = 'giot/phut' THEN
                                         'Giọt/phút'
                                     ELSE
                                         'ml/giờ'
                                 END
                             )
                             || ')'
                             || CASE
                                      WHEN pt.lieudung IS NULL THEN ' '
                                      ELSE ', liều dùng: ' || pt.lieudung
                                END
                                  ELSE
                             'Thuốc'
END || ' - '|| to_char(NGAYGIO_YLENH, 'DD/MM/YYYY HH24:MI') ylenh_show,
                     ylenh.id id_ylenh,
                     PT.DVT_PHATHUOC,
                     to_char(NGAYGIO_YLENH, 'DD/MM/YYYY HH24:MI') NGAYGIOYLENH
                 FROM
                     his_manager.cmu_phathuoc   pt
                     LEFT JOIN cmu_ylenhthuoc_theogio     ylenh ON ylenh.dvtt = pt.dvtt
                                                               AND ylenh.stt_toathuoc = 'PHA_' || pt.id_phathuoc
                                                               and ylenh.loai_ylenh = 'truyendich'
                 WHERE
                     pt.dvtt = p_dvtt
                     AND pt.stt_dotdieutri = p_stt_dotdieutri
                     AND pt.stt_dieutri = p_stt_dieutri
                     AND pt.sovaovien = p_sovaovien
                     AND pt.id_dieutri = p_id_dieutri;

RETURN cur;
END;