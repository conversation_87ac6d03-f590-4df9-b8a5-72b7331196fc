create or replace FUNCTION             HSBA_TODIEUTRI_CMU_SELV2(p_stt_benhan     IN VARCHAR2,
																 p_makhoa IN VARCHAR2,
																 p_userid IN VARCHAR2,
																 p_dvtt           IN VARCHAR2
) return SYS_REFCURSOR
IS
  V_THAMSO_94300           VARCHAR2(1) := cmu_tsdv(p_dvtt, 94300, 0);
  V_THAMSO_86979           VARCHAR2(1) := cmu_tsdv(p_dvtt, 86979, 0);
  v_kiemtrathuoc           number(11) default 0;
  V_THAMSO_86971           VARCHAR2(1) := cmu_tsdv(p_dvtt, 86971, 0);
  v_thamso_bo_phieuhoantra number(1) default '0';
  v_svv                    number(11);
  v_svv_dt                 number(11);
  v_disable_auto           number(10) := cmu_tsdv(p_dvtt, 96206, 0);
  v_disable_auto_ttpt      number(10) := cmu_tsdv(p_dvtt, 96212, 0);
	cur SYS_REFCURSOR;
	v_sovaovien number;
	v_stt_dotdieutri number;
	v_phongbenh varchar2(255);
	v_sogiuong varchar2(255);
    v_chandoan varchar2(1000):='';
    v_chandoanphanbiet varchar2(2000):='';
BEGIN


select SOVAOVIEN into v_sovaovien from NOITRU_BENHAN
where stt_benhan = p_stt_benhan and dvtt = p_dvtt;
delete from NOITRU_DIEUTRI_HSBA_TEMP
where dvtt = p_Dvtt and STT_BENHAN = p_stt_benhan;

DELETE FROM tem_noitru_todieutri_in
WHERE dvtt = p_dvtt
  and stt_benhan = p_stt_benhan;


delete from NOITRU_CT_TOA_THUOC_TEMP
where dvtt = P_dvtt
  and STT_BENHAN =p_stt_benhan;

insert into NOITRU_DIEUTRI_HSBA_TEMP
(
    DVTT,
    STT_DIEUTRI,
    STT_DOTDIEUTRI,
    MABENHNHAN,
    SOVAOVIEN,
    SOVAOVIEN_DT,
    ID_DIEUTRI,
    STT_BENHAN,
    NGAYGIOLAP_TDT,
    TDT_NGUOILAP,
    TDT_DIENBIENBENH,
    TDT_YLENH,
    ICD_DIEUTRI,
    TENICD_DIEUTRI,
    TEN_BENHPHU,
    BMI,
    MACH,
    NHIETDO,
    HUYETAPTREN,
    HUYETAPDUOI,
    CHIEUCAO,
    NHIPTHO,
    CANNANG,
    NGUOITAO,
    SPO2
)
select
    DVTT,
    STT_DIEUTRI,
    STT_DOTDIEUTRI,
    MABENHNHAN,
    SOVAOVIEN,
    SOVAOVIEN_DT,
    ID_DIEUTRI,
    STT_BENHAN,
    NGAYGIOLAP_TDT,
    TDT_NGUOILAP,
    TDT_DIENBIENBENH,
    TDT_YLENH,
    ICD_DIEUTRI,
    TENICD_DIEUTRI,
    TEN_BENHPHU,
    BMI,
    MACH,
    NHIETDO,
    HUYETAPTREN,
    HUYETAPDUOI,
    CHIEUCAO,
    NHIPTHO,
    CANNANG,
    NGUOITAO,
    SPO2
from NOITRU_DIEUTRI
where dvtt = p_Dvtt and STT_BENHAN = p_stt_benhan and sovaovien = v_sovaovien
  and (p_makhoa = '0' or KHOALAP = p_makhoa) and (p_userid = '-1' or TDT_NGUOILAP = p_userid);

SELECT
    icd_dieutri
        || '-'
        || tenicd_dieutri ||'; ' ||  nvl(ten_benhphu, ' '),
    ' '-- nvl(ten_benhphu, ' ')
INTO
    v_chandoan,
    v_chandoanphanbiet
FROM
    noitru_dieutri_hsba_temp
WHERE
    ngaygiolap_tdt = (
        SELECT
            MIN(ngaygiolap_tdt)
        FROM
            noitru_dieutri_hsba_temp
        WHERE
            dvtt = p_dvtt
          AND stt_benhan = p_stt_benhan
          AND sovaovien = v_sovaovien
    )
  AND dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND sovaovien = v_sovaovien
  AND ROWNUM <= 1;

begin
--select pb.TEN_PHONG, gb.TENGIUONG into v_phongbenh, v_sogiuong
--from CMU_HSBA_PHONGBENH pb
--         inner join CMU_HSBA_PHONGBENH_GB gb on pb.dvtt = GB.dvtt
--    and pb."ID" = gb.MA_PHONG
--where gb.dvtt = p_Dvtt and GB.SOVAOVIEN = v_sovaovien
--  and rownum <=1;
select STT_BUONG, STT_GIUONG
into v_phongbenh, v_sogiuong
from CMU_SOBUONGGIUONG
WHERE DVTT = p_Dvtt AND
    STT_BENHAN = p_stt_benhan
  and STT_DOTDIEUTRI = 1 and rownum<=1;
exception
	when no_data_found then v_phongbenh:=null;v_sogiuong:=null;
end;



OPEN cur FOR
select a.stt_dieutri,
       a.dvtt,
       a.stt_benhan,
       a.stt_dotdieutri,
       a.ngaygio,
       '' AS Y_LENH,
       DIEN_BIEN_BENH AS DIEN_BIEN_BENH,
       a.ngaygiolap_tdt,
       a.ten_nhanvien,
       a.SOVAOVIEN,
       a.SOVAOVIEN_DT,
       a.ID_DIEUTRI,
       SIGNKCB.KEYSIGN,
       v_phongbenh PHONGBENH,
       v_sogiuong SOGIUONG,
       a.tenbs,
       TO_CHAR(SIGNKCB.ngay_ky, 'DD/MM/YYYY HH24:MI:SS') ngay_ky,
       v_chandoan CHANDOAN,
       v_chandoanphanbiet CHANDOANPHANBIET
from (SELECT p_dvtt,
             p_stt_benhan STT_BENHAN,
             b.stt_dotdieutri,
             b.stt_dieutri,
             to_char(b.ngaygiolap_tdt, 'DD/MM/YYYY HH24:MI') AS ngaygio,
             nvl(b.tdt_ylenh, ' ') AS Y_LENH,
             LIStagg(
                 --
                     CASE
                         WHEN nvl(b.MACH, ' ') = ' ' THEN
                             ' '
                         ELSE
                             ' - Mạch: ' || b.MACH || ' lần/phút,'
                         END ||
                         --
                     CASE
                         WHEN nvl(b.NHIETDO, ' ') = ' ' THEN
                             ' '
                         ELSE
                             CHR(10) || ' - Nhiệt độ: ' || b.NHIETDO || ' ᵒC,'
                         END ||
                         --
                     CASE
                         WHEN nvl(b.HUYETAPTREN || '/' || b.HUYETAPDUOI, ' ') = '/' THEN
                             ' '
                         ELSE
                             CHR(10) || ' - Huyết áp: ' || b.HUYETAPTREN || '/' || b.HUYETAPDUOI ||
                             ' mmHg,'
                         END ||
                         --
                     CASE
                         WHEN nvl(b.NHIPTHO, ' ') = ' ' THEN
                             ' '
                         ELSE
                             CHR(10) || ' - Nhịp thở: ' || b.NHIPTHO || ' lần/phút,'
                         END ||
                         --
                     CASE
                         WHEN nvl(b.CHIEUCAO, ' ') = ' ' THEN
                             ' '
                         ELSE
                             CHR(10) || ' - Chiều cao: ' || b.CHIEUCAO || ' cm,'
                         END ||
                     CASE
                         WHEN nvl(b.CANNANG, ' ') = ' ' THEN
                             ' '
                         ELSE
                             CHR(10) || ' - Cân nặng: ' || b.CANNANG || ' kg,'
                         END ||
                         --BMI
                     CASE
                         WHEN b.BMI != null and b.CHIEUCAO != null and b.CANNANG != null THEN
                         CHR(10) || ' - BMI: ' || b.BMI || ','
                         ELSE
                         ' '
                         END ||
                         --SPO2
                         CASE
                         WHEN nvl(b.SPO2, ' ') = ' ' THEN
                         ' '
                         ELSE
                         CHR(10) || ' - SPO2: ' || b.SPO2 || ' %,'
                         END ||
                         --Diễn biến bệnh
                         CASE
                         WHEN nvl(b.tdt_dienbienbenh, ' ') = ' ' THEN
                         ' '
                         ELSE
                         concat(CHR(10), b.tdt_dienbienbenh)
                         END ||
                         CASE WHEN b.TEN_BENHPHU is not null or b.ICD_DIEUTRI is not null or  b.TENICD_DIEUTRI is not null then
                         case
                         when p_dvtt in 96029 then
                         CHR(10) || ' - Δ: '
                         else
                         CHR(10) || ' - Chẩn đoán: '
                         end || b.ICD_DIEUTRI || ' - ' || b.TENICD_DIEUTRI || ';'||
                         b.TEN_BENHPHU || chr(10)
                         END ,
                     ' - ') WITHIN GROUP(ORDER BY NULL) AS DIEN_BIEN_BENH,
       b.ngaygiolap_tdt,
       c.ten_nhanvien_cd ten_nhanvien,
			 B.sovaovien,
			 B.sovaovien_Dt,
			 B.ID_DIEUTRI,
			 B.DVTT,
			 c.ten_nhanvien tenbs
      FROM NOITRU_DIEUTRI_HSBA_TEMP b
          LEFT JOIN his_fw.dm_nhanvien_cd c
      ON b.tdt_nguoilap = c.ma_nhanvien
      WHERE b.stt_benhan = p_stt_benhan
        AND b.DVTT = p_dvtt
      GROUP BY p_dvtt,
          p_stt_benhan,
          b.tdt_ylenh,
          b.STT_DOTDIEUTRI,
          b.stt_dieutri,
          b.ngaygiolap,
          b.ngaygiolap_tdt,
          b.ngaylap,
          c.ten_nhanvien_cd,
          b.stt_benhan,
          b.stt_dotdieutri,
          b.dvtt,
          B.sovaovien,
          B.sovaovien_Dt,
          B.DVTT,
          B.ID_DIEUTRI,
          c.ten_nhanvien
      ORDER BY ngaygiolap_tdt ASC) a
         left join SMARTCA_SIGNED_KCB SIGNKCB ON SIGNKCB.DVTT = a.dvtt
    and signkcb.sovaovien = a.sovaovien and signkcb.sovaovien_dt = signkcb.sovaovien_dt
    and signkcb.ky_hieu_phieu = 'TODIEUTRI_NOITRU' and signkcb.ID_DIEUTRI = a.ID_DIEUTRI
    and signkcb.so_phieu_dv = to_char(a.ID_DIEUTRI) AND signkcb.STATUS=0
         LEFT JOIN CMU_YLENHTRUYENMAU truyenmau on a.dvtt = truyenmau.dvtt and a.sovaovien = truyenmau.sovaovien
    and a.id_dieutri = truyenmau.id_dieutri
order by ngaygiolap_tdt
;
return cur;
END;