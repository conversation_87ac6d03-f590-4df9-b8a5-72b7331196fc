create or replace FUNCTION "HIS_MANAGER"."LAYDS_CHUNGCHI_HANHNGHE_NV" (
    P_DVTT in varchar,
    P_MAPHONGBAN in varchar
) RETURN SYS_REFCURSOR
IS CUR SYS_REFCURSOR;

BEGIN
    OPEN CUR FOR
    SELECT NV.MA_PHONGBAN,
           NV.CHUNGCHI_HANHNGHE,
           NV.MA_NHANVIEN AS VALUE,
           CDNV.MOTA_CHUCDANH || '. ' || NV.TEN_NHANVIEN AS TEXT
    FROM HIS_FW.DM_NHANVIEN NV
        INNER JOIN HIS_FW.DM_CHUCVU CV ON CV.MA_CHUCVU = NV.CHUCVU_NHANVIEN
        INNER JOIN HIS_FW.DM_CHUCDANH_NHANVIEN CDNV ON CDNV.MA_CHUCDANH = NV.CHUCDANH_NHANVIEN
        INNER JOIN HIS_FW.DM_PHONGBAN PB ON PB.MA_PHONGBAN = NV.MA_PHONGBAN
    WHERE (PB.MA_PHONGBAN = P_MAPHONGBAN or P_MAPHONGBAN = '-1')
        AND PB.MA_DONVI = P_DVTT;
    RETURN CUR;
END;