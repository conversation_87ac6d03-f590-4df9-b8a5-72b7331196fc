<%@page import="l2.ThamSoManager" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@page contentType="text/html" pageEncoding="UTF-8"  %>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="google-site-verification" content="u6uNEfD4cb3gidezi4r_6aI8Wb1E07-ufBeCQpvmlqQ" />
    <title>Hệ thống chăm sóc sức khỏe</title>
    <link rel="icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
    <link rel="shortcut icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>

    <!-- jQuery file -->
    <link href="<c:url value="/resources/css/divheader.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/css/style_new.css" />" rel="stylesheet"/>

    <!--Jquery-->
    <link rel="stylesheet" href="<c:url value="/resources/css/jquery-ui-redmond.1.9.1.css" />" />
    <script src="<c:url value="/resources/js/jquery.min.1.8.3.js" />"></script>
    <script src="<c:url value="/resources/js/jquery-ui.1.9.1.js" />"></script>
    <!--Grid-->
    <link href="<c:url value="/resources/jqgrid/css/ui.jqgrid.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/bootstrap-4.1.3/dist/css/bootstrap.min.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/jqgrid/js/i18n/grid.locale-en.js" />"></script>
    <script src="<c:url value="/resources/jqgrid/js/jquery.jqGrid.src.js" />"></script>
    <script src="<c:url value="/resources/js/common_function.js" />"></script>
    <script src="<c:url value="/resources/js/jquery.inputmask.bundle.min.js" />"></script>
    <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
    <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/dialog/jquery.alerts.1.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/dialog/jquery.alerts.js" />"></script>
    <link href="<c:url value="/resources/dialog/jBox.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/dialog/jBox.js" />"></script>

    <link href="<c:url value="/resources/combogrid/css/smoothness/jquery.ui.combogrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/combogrid/plugin/jquery.ui.combogrid-1.6.3.js" />"></script>
    <script src="<c:url value="/resources/ckeditor/ckeditor.js" />"></script>
    <script src="<c:url value="/resources/ckeditor/adapters/jquery.js" />"></script>
    <script src="<c:url value="/resources/webcam/say-cheese.js" />"></script>
    <link href="<c:url value="/resources/webcam/pygments.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/webcam/say-cheese.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
    <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/js/canlamsang/grid_danhsachchidinh.js" />"></script>
    <script src="<c:url value="/resources/camau/vnpt-plugin_v692020.js" />"></script>
    <script src="<c:url value="/resources/camau/vgcaplugin.js" />"></script>
    <script src="<c:url value="/resources/camau/keytichhop.js" />"></script>
    <script src="<c:url value="/resources/camau/sha256.min.js"/>" ></script>
    <script src="<c:url value="/resources/js/read_file.js" />"></script>
    <script src="<c:url value="/resources/js/datetimepicker.js" />"></script>
    <link rel="stylesheet" href="<c:url value="/resources/css/datetimepicker.css" />" />
    <link href="<c:url value="/resources/camau/css/loader.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/camau/css/custom.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/camau/material/moment.js" />"></script>
    <script src="<c:url value="/resources/camau/js/jquery.validate.min.js" />"></script>
    <script src="<c:url value="/resources/camau/js/kiemtradulieuthoigiankham.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <script src="<c:url value="/resources/camau/js/common.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <script src="<c:url value="/resources/js/jquery-confirm.min.js" />"></script>
    <link href="<c:url value="/resources/css/jquery-confirm.min.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/camau/js/jquery.toast.min.js" />"></script>
    <link href="<c:url value="/resources/camau/css/jquery.toast.min.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/camau/js/luulog.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <script src="<c:url value="/resources/camau/js/lichsubenhan.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <script src="<c:url value="/resources/camau/js/kysonoitru.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <script src="<c:url value="/resources/camau/smartca769.js?timestamp=${System.currentTimeMillis()}" />"></script>

    <style>
        .ui-widget-content .bncapcuuClass { color: red; weight-font:bold; background-image: none; }
        .ui-widget-content .treemClass { color: #00ff00; weight-font:bold; background-image: none; }
        .ui-widget-content .kobhytClass { color: #bf00ff; weight-font:bold; background-image: none; }
        .ui-widget-content .vienphiClass { color: #EE7600; weight-font:bold; background-image: none; }
        .width1{
            width: 200px;
        }
        .width2{
            width: 545px;
        }
        .width3{
            width: 150px;
        }
        .width4{
            width: 325px;
        }
        .width5{
            width: 810px;
        }
        .width6{
            width:500px;
        }
        legend{

            color:red;
        }
        .width100{
            width:100%;
        }
        span.cellWithoutBackground
        {
            display:block;
            background-image:none;
            margin-right:-2px;
            margin-left:-2px;
            height:14px;
            padding:4px;
        }
        /*CMU 2606/2017*/
        #list_lichsuCDHA tr.jqgrow td {
            white-space: normal !important;
            height:auto;
            vertical-align:text-top;
        }
        #list_xquang_bhyt .center_content table tr td {
            white-space: normal !important;
        }
        .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn){
            width:100% !important;
        }
    </style>
    <script>
        var stt_benhan = "";
        var sophieu = "";
        var sovaovien;
        var sovaovien_noi;
        var sovaovien_dt_noi;
        var co_bao_hiem;
        var phongcdha_ss;
        var bacsi_chidinh;
        var flag_noitru = "-1";//0 Ngoại trú, 1 Nọi trú
        var noitru_ngoaitru;
        var ma_cdha = 0;
        var sobenhantru;
        var sobenhantru_tt;
        var icd_benhnhan;
        var ten_icd_benhnhan;
        var soba;
        var mabenhnhan;
        var stt_benhan;
        var stt_dotdieutri;
        var manv = "${Sess_UserID}";
        var tennv = "${Sess_User}";
        var admin = "${Sess_Admin}";
        var sobenhan;
        var sobenhan_tt;
        var stt_dieutri;
        var da_thanh_toan;
        var matoathuoc;
        var makhovattu;
        var url_loadtonkho;
        var url_loadthuoc;
        var ngay_kb = "";
        var cobhyt;
        var sophieuthanhtoan;
        var tlmg;
        var giothuchien_cls_timer_is_on = false;
        var giothuchien_cls_timer_previous_status = false;
        var gioth_ct_timer_is_on = false;
        var gioth_ct_timer_previous_status = false;
        var giothuchien_cls_timer;
        var gioth_ct_timer;
        var laydl_tungoaitru = 0;
        var sophieu_ngoaitru;

        var showtime_giothuchien_cls_cancel = 0;
        var showtime_gioth_ct_cancel = 0;
        var tatAutoTime = "<%= ThamSoManager.instance(session).getThamSoString(ThamSoManager.THAMSO_28084881,"0")%>";
        var show_bscd = "<%= ThamSoManager.instance(session).getThamSoString("960547","0")%>";
        var THAMSO_960624 = "<%= ThamSoManager.instance(session).getThamSoString("960624", "0")%>";
        var THAMSO_960626 = "<%= ThamSoManager.instance(session).getThamSoString("960626", "0")%>";
        function combogridTen(ten,ma) {
            ten.combogrid({

                url: "ttpt_ekip_timkiem_bacsi?url=" + convertArray(["${Sess_DVTT}"]),

                debug: true,
                width: "300px",
                colModel: [{'columnName': 'TEN_PHONGBAN','label': 'Phòng Khoa','width': '30', 'align': 'left'},
                    {'columnName': 'TEN_NHANVIEN', 'width': '40','label':'Tên nhân viên', 'align': 'left'},
                    {'columnName': 'MA_NHANVIEN', index: 'MA_NHANVIEN', hidden: true}
                ],
                select: function (event, ui) {
                    ma.val(ui.item.MA_NHANVIEN);
                    ten.val(ui.item.TEN_NHANVIEN.trim());
                    return false;
                }
            });
        }
        function combogridTenVaiTro(ten,ma) {
            ten.combogrid({

                url: "timkiem_vaitro_ekip?url=" + convertArray(["${Sess_DVTT}"]),
                debug: true,
                width: "300px",
                colModel: [{'columnName': 'MA_VAITRO','label': 'Phòng Khoa','width': '30', 'align': 'left',hidden: true},
                    {'columnName': 'TEN_VAITRO', 'width': '40','label':'Tên vai trò', 'align': 'left'}

                ],
                select: function (event, ui) {
                    ma.val(ui.item.MA_VAITRO);
                    ten.val(ui.item.TEN_VAITRO.trim());
                    return false;
                }
            });
        }
        function nextText(current, next) {
            current.keypress(function (evt) {
                if (evt.keyCode == 13) {
                    next.focus();
                }
            });

        }
        function combogridMaBS(ten,ma) {
            ma.combogrid({

                url: "noitru_hoichan_timkiem_mabacsi?url=" + convertArray(["${Sess_DVTT}"]),

                debug: true,
                width: "300px",
                colModel: [{'columnName': 'MA_NHANVIEN','label': 'Mã nhân viên','width': '30', 'align': 'left'},
                    {'columnName': 'TEN_NHANVIEN', 'width': '40','label':'Tên nhân viên', 'align': 'left'},
                ],
                select: function (event, ui) {
                    ma.val(ui.item.MA_NHANVIEN);
                    ten.val(ui.item.TEN_NHANVIEN.trim());
                    return false;
                }
            });
        }

        function openEkip() {
            dialog_capnhatketquattpt_ekip.dialog("open");
            var sophieu = $("#sophieu").val();
            var madv = $("#macdha").val();
            var noitru = $("#noitru").val();
            $("#ten_nhanvien").focus();
            $("#list_chitietvaitrothuchien").jqGrid({
                url: "danhsachvaitroekip_ttpt?sophieu_dv=" + sophieu + "&madv=" + madv +"&noitru=" + noitru + "&sovaovien=" + sovaovien + "&sovaovien_noi=" + sovaovien_noi + "&sovaovien_dt_noi=" + sovaovien_dt_noi,
                datatype: "json",
                loadonce: true,
                height: 312,
                width: 650,
                colNames: ['ID_EKIP', 'Mã nhân viên', "Tên nhân viên", "Vai trò"],
                colModel: [
                    {name: 'ID_EKIP', index: 'ID_EKIP', width: 50, hidden: true},
                    {name: 'MA_NHANVIEN', index: 'MA_NHANVIEN', width: 50},
                    {name: 'TEN_NHANVIEN', index: 'TEN_NHANVIEN', width: 150},
                    {name: 'TEN_VAITRO', index: 'TEN_VAITRO', width: 150}
                ],
                sortname: 'MA_VAITRO',
                sortorder: "asc",
                caption: "Danh sách Ekip",
                ignoreCase: true,
                onSelectRow: function (id) {
                    if (id) {
                        var ret = $("#list_chitietvaitrothuchien").jqGrid('getRowData', id);
                        $("#frm_kq_id_ekip").val(ret.ID_EKIP);
                        //load_dmvaitrothuchien(ret);
                    }
                }
            });
            $("#list_chitietvaitrothuchien").jqGrid('setGridParam', {url:"danhsachvaitroekip_ttpt?sophieu_dv=" + sophieu + "&madv=" + madv +"&noitru=" + noitru + "&sovaovien=" + sovaovien + "&sovaovien_noi=" + sovaovien_noi + "&sovaovien_dt_noi=" + sovaovien_dt_noi,datatype: 'json'}).trigger('reloadGrid');
        }
        function capNhatEkip(capnhat){
            if(capnhat==1){
                if($("#ma_nhanvien").val()!=null&&$("#ma_vaitro").val()!=null){
                    $("#frm_kq_id_ekip").val("");

                    $("#use_ekip").val("true");
                    $.ajax({
                        type: "POST",
                        contentType : 'application/json; charset=utf-8',
                        dataType : 'json',
                        url: "capnhat_cd_dichvu_ekip",
                        data: JSON.stringify(createJson()), // Note it is important
                    }).success(function () {
                        // jAlert("Cập nhật Ekip thành công", "Thông báo");
                        $("#list_chitietvaitrothuchien").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                        $("#ma_nhanvien").val("");
                        $("#ten_nhanvien").val("");
                        $("#ma_vaitro").val("");
                        $("#ten_vaitro").val("");
                    }).fail(function () {
                        jAlert("Cập nhật Ekip không thành công", "Cảnh báo");
                    });
                }

            }else{
                $("#ma_nhanvien").val("");
                $("#ten_nhanvien").val("");
                $("#ma_vaitro").val("");
                $("#ten_vaitro").val("");

                var url = String.format("delete_cd_dichvu_ekip?id_ekip={0}", $("#frm_kq_id_ekip").val());
                $.ajax({
                    url: url
                }).success(function () {
                    jAlert("Đã xoá Ekip", "Thông báo");
                    $("#list_chitietvaitrothuchien").jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                }).fail(function () {
                    jAlert("Xoá Ekip không thành công", "Cảnh báo");
                });
            }

        }
        function createJson() {
            var jsondata = {};
            var alertString = null;
            $("form#form_Ekip").find(".post-data").each(function (index, element) {
                var tag = $(element);
                var type = tag.attr("type");
                var key = tag.attr("key");
                var require = tag.attr("required");
                var value = tag.val();
                if (require == true) {
                    alertString = tag.attr("error-message").val();
                }
                if (alertString != null) {
                    jAlert(alertString, "Cảnh báo");
                    return;
                }
                jsondata[key] = value;
            })
            return jsondata;
        }
        //VLG chinh textbox gio tra kq chay thoi gian
        function addZero(i) {
            if (i < 10) {
                i = "0" + i;
            }
            return i;
        }
        function loadKhoakythuatvien() {
            if($("#khoakythuatvien").val() != "-1") {
                var url = "cmu_list_CMU_DS_NHANVIEN_ALL?url=" + convertArray([$("#khoakythuatvien").val()]);
                $.ajax({
                    url: url
                }).done(function (data) {
                    if (data) {
                        $("#cmu_kythuatvien").empty();
                        $.each(data, function (i) {
                            var selected = (i == 0) || data[i].MA_NHANVIEN == '${Sess_UserID}'?"selected":"";
                            $("<option value='" + data[i].MA_NHANVIEN + "' " + selected + ">" + data[i].TEN_NHANVIEN + "</option>").appendTo("#cmu_kythuatvien");
                        });
                    }
                });
            }

        }
        function loadKhoabacsidocketqua() {
            if($("#khoabsttpt").val() != "-1") {
                var url = "laybacsi_theokhoa_all?khoa=" + $("#khoabsttpt").val();
                $.ajax({
                    url: url
                }).done(function (data) {
                    if (data) {
                        $("#cbbacsittpt").empty();
                        $.each(data, function (i) {
                            var selected = (i == 0) || data[i].MA_NHANVIEN == '${Sess_UserID}'?"selected":"";
                            $("<option value='" + data[i].MA_NHANVIEN + "' " + selected + ">" + data[i].TEN_NHANVIEN + "</option>").appendTo("#cbbacsittpt");
                        });
                    }
                });
            }

        }
        function gioThucHienClsTimerChange() {
            if (giothuchien_cls_timer_is_on || ($("#dathuchien").prop('checked') && '${capnhat_cls_timer_off}' == '1'))
                stopGioThucHienClsTimer();
            else if (tatAutoTime == 1)
                stopGioThucHienClsTimer();
            else
                showtime_giothuchien_cls();
        }

        function gioThCtTimerChange() {
            if("${Sess_DVTT}".substring(0,2) == "96"){
                stopGioThCtTimer();
            }
            else if (tatAutoTime == 1)
                stopGioThCtTimer();
            else if (gioth_ct_timer_is_on || ($("#gioth_ct").data('da-thuc-hien') && '${capnhat_cls_timer_off}' == 1))
                stopGioThCtTimer();
            else
                showtime_gioth_ct();
        }

        function showtime_giothuchien_cls() {
            var thoigian = new Date();
            var gio = addZero(thoigian.getHours());
            var phut = addZero(thoigian.getMinutes());
            var giay = addZero(thoigian.getSeconds());
            /*if(showtime_giothuchien_cls_cancel !== 1) {
                $('#giothuchien_cls').val(gio + ":" + phut + ":" + giay);
            }
            t = setTimeout(showtime_giothuchien_cls, 1000);*/
            $('#giothuchien_cls').val(gio + ":" + phut + ":" + giay);
            giothuchien_cls_timer = setTimeout(showtime_giothuchien_cls, 1000);
            giothuchien_cls_timer_is_on = true;

        }

        function showtime_gioth_ct() {
            var thoigian = new Date();
            var gio = addZero(thoigian.getHours());
            var phut = addZero(thoigian.getMinutes());
            var giay = addZero(thoigian.getSeconds());
            /*if(showtime_gioth_ct_cancel !== 1) {
                $('#gioth_ct').val(gio + ":" + phut + ":" + giay);
            }
            t = setTimeout(showtime_gioth_ct, 1000);*/
            $('#gioth_ct').val(gio + ":" + phut + ":" + giay);
            gioth_ct_timer = setTimeout(showtime_gioth_ct, 1000);
            gioth_ct_timer_is_on = true;
        }

        function stopCount() {
            clearTimeout(t);
            timer_is_on = 0;
        }
        function stopGioThucHienClsTimer(){
            clearTimeout(giothuchien_cls_timer);
            giothuchien_cls_timer_is_on = false;
        }

        function stopGioThCtTimer() {
            clearTimeout(gioth_ct_timer);
            gioth_ct_timer_is_on = false;
        }

        function delete_thuocnoitru(list, nghiepvu) {
            var id = $("#" + list).jqGrid('getGridParam', 'selrow');
            if (id) {
                var ret = $("#" + list).jqGrid('getRowData', id);
                if (ret.MA_BAC_SI_THEMTHUOC === undefined || ret.MA_BAC_SI_THEMTHUOC === "${Sess_UserID}") {
                    jConfirm('Bạn có muốn xóa không?', 'Thông báo', function (r) {
                        if (r.toString() === "true") {
                            var url = "noitru_toathuoc_delete";
                            if (list === "list_thuoctonghopall")
                                nghiepvu = ret.NGHIEP_VU;
                            var arr = [ret.STT_TOATHUOC, matoathuoc, "${Sess_DVTT}", stt_dieutri, stt_benhan, stt_dotdieutri, sophieuthanhtoan,
                                ret.THANHTIEN_THUOC, ret.MAKHOVATTU, "0", nghiepvu, ret.MAVATTU, encodeURIComponent(ret.TEN_VAT_TU), ret.SO_LUONG];
                            var sophieu = $("#sophieu").val();
                            var ma_cdha = $("#ds_cdha").val();
                            url += "?url=" +convertArray(arr);
//                                if (sophieu){
//                                    url += "&sophieu=" + sophieu;
//                                }
//                                if (ma_cdha){
//                                    url += "&ma_cdha=" + ma_cdha;
//                                }
                            $.post(url).done(function (data) {
                                if (data === "1")
                                    jAlert("Bệnh nhân đã thanh toán viện phí", 'Cảnh báo');
                                else if (data === "2")
                                    jAlert("Bệnh nhân đã được xuất thuốc", 'Cảnh báo');
                                else if (data == '100') {
                                    jAlert("Đã chốt báo cáo dược, không thể sửa/xóa", 'Cảnh báo');
                                }
                                else {
                                    $("#" + list).jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
                                    cmu_insertbcfilmxquang(["${Sess_DVTT}",ret.MAVATTU,ret.TEN_VAT_TU,$("#sophieu").val(),ma_cdha,sovaovien,mabenhnhan,ret.SO_LUONG,1,'CMU_INSERTBCFILMXQUANG']);
                                }
                            });
                        }
                    });
                } else {
                    jAlert("Bạn không được xóa thuốc trong toa của bác sĩ khác!", 'Cảnh báo');
                }
            } else {
                jAlert("Chọn một dòng thuốc để xóa", 'Cảnh báo');
            }
        }
        function cmu_insertbcfilmxquang(arr) {
            $.post('cmu_post', {
                url: arr.join("```")
            })
        }
        function delete_toathuocngoaitru(list, nghiepvu) {
            var id = $("#" + list).jqGrid('getGridParam', 'selrow');
            if (id) {
//                    var url = "xuatduoc_giamtai_svv";
//                    $.post(url, {
//                        nghiepvu: "ngoaitru_toadichvu",
//                        matoathuoc: matoathuoc,
//                        makhambenh: $("#makhambenh").val(),
//                        xacnhan: "false",
//                        mabenhnhan: mabenhnhan,
//                        ngaykhambenh: ngay_kb
//                    }).done(function (data) {
//                        if(data == "0"){
                var ret = $("#" + list).jqGrid('getRowData', id);
                var dongia = ret.DONGIA_BAN_BH;
                var arr = [ret.STT_TOATHUOC, matoathuoc,${Sess_DVTT}, $("#makhambenh").val(), sophieuthanhtoan, ret.THANHTIEN_THUOC,
                    ret.MAKHOVATTU, dongia, nghiepvu, ret.MAVATTU, mabenhnhan, ngay_kb, sovaovien, encodeURIComponent(ret.TEN_VAT_TU), ret.SO_LUONG];
                var url = "xoathuocngoaitru_giamtai?url=" + convertArray(arr);
                var sophieu = $("#sophieu").val();
                var ma_cdha = $("#ds_cdha").val();
//                        if (sophieu){
//                            url += "&sophieu=" + sophieu;
//                        }
//                        if (ma_cdha){
//                            url += "&ma_cdha=" + ma_cdha;
//                        }
                $.ajax({
                    url: url
                }).done(function (data) {
                    if (data == "1")
                        jAlert("Bệnh nhân đã thanh toán", 'Cảnh báo');
                    else if (data == "2")
                        jAlert("Bệnh nhân đã được xuất thuốc", 'Cảnh báo');
                    else if (data == "3")
                        jAlert("Bệnh nhân đã được trả thuốc về kho", 'Cảnh báo');
                    //AGG Begin Khang 8/5/2017 kiem tra so luong thuoc truoc khi delete
                    else if (data == "4")
                        jAlert("Toa thuốc có số lượng nhiều, Nên xóa cả toa!", 'Cảnh báo');
                    //AGG End Khang 8/5/2017 kiem tra so luong thuoc truoc khi delete
                    else if (data == '100') {
                        jAlert("Đã chốt báo cáo dược, không thể sửa/xóa", 'Cảnh báo');
                    }
                    else{
                        $("#" + list).jqGrid('setGridParam', {datatype: 'json'}).trigger('reloadGrid');
//                                var url = "xuatduoc_giamtai_svv";
//                                $.post(url, {
//                                    nghiepvu: "ngoaitru_toadichvu",
//                                    matoathuoc: matoathuoc,
//                                    makhambenh: $("#makhambenh").val(),
//                                    xacnhan: "true",
//                                    mabenhnhan: mabenhnhan,
//                                    ngaykhambenh: ngay_kb
//                                }).done(function (data) {
//
//                                });
                    }
                });
                //}
                //});
            } else {
                jAlert("Chọn 1 dòng thuốc để xóa", 'Cảnh báo');
            }
        }
        $(function () {
            if(show_bscd != '0'){
                $(".cmu_bacsichidinh").show();
            }
            $("#mauxquang").selectpicker('val',0);
            dialog_capnhatketquattpt_ekip = $("#dialog_capnhatketquattpt_ekip").dialog({
                autoOpen: false,
                width: 700,
                modal:true,
                resizable: false,
                position: {my: "center top", at: "center top", of: window}
            });
            $("#ten_vaitro").keypress(function (evt) {
                if (evt.keyCode == 13) {
                    capNhatEkip(1);
                    $("#ten_nhanvien").focus();
                }
            });
            //VNPTHIS-4697 23/11/2017
            $(":input").inputmask();
            $("#ngaythuchien_cls").datepicker();
            $("#ngaythuchien_cls").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngaythuchien_cls").val("${ngayhientai}");

            $("#ngayth_ct").datepicker();
            $("#ngayth_ct").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngayth_ct").val("${ngayhientai}");

            $(":input").inputmask();
            $("#ngaythuchien").datepicker();
            $("#ngaythuchien").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngaythuchien").val("${ngayhientai}");
            $("#ngaybatdauthuchien").inputmask({
                mask: "1/2/y h:s:s",
                placeholder: "dd/mm/yyyy hh:mm:ss",
                alias: "datetime",
                hourFormat: "24"
            });
            $("#ngaybatdauthuchien").datetimepicker({
                dateFormat: "dd/mm/yy",
                timeFormat: 'HH:mm:ss',
                showSecond: true,
            });
            $("#ngaybatdauthuchien").val("${ngayhientai} 00:00:00");

            nextText($("#ten_nhanvien"),$("#ten_vaitro"));
            combogridMaBS($("#ten_nhanvien"),$("#ma_nhanvien"));
            combogridTen($("#ten_nhanvien"),$("#ma_nhanvien"));
            combogridTenVaiTro($("#ten_vaitro"),$("#ma_vaitro"));
            $("#tungay1").datepicker();
            $("#tungay1").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#tungay1").val("${ngayhientai}");
            $("#denngay1").datepicker();
            $("#denngay1").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#denngay1").val("${ngayhientai}");
            $("#ngayth_ct").datepicker();
            $("#ngayth_ct").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngayth_ct").val("${ngayhientai}");
            if ("${tichhopdsbncd}" == "1"){
                $("#form1").hide();
                $("#formdsbn").show();
            }else{
                $("#formdsbn").hide();
                $("#form1").show();
            }
            if ("${timkiem_cls}" === "1")
            {
                $(".hpg_tmp").show();
                $("#tungay").datepicker();
                $("#tungay").datepicker("option", "dateFormat", "dd/mm/yy");
                $("#tungay").val("${ngayhientai}");
                $("#tungay").change(function () {
                    reload_grid();
                });
            } else
                $(".hpg_tmp").hide();
            if ("${choloctheokhoaphong_capnhatketquacls}" === "1")
                $(".dlk_tmp").show();
            else
                $(".dlk_tmp").hide();
            if ("${hienthi_them_cls}" === "1")
                $(".ghichutrangthai").show();
            else
                $(".ghichutrangthai").hide();
            //-------
            if ("${hienthi_them_cls}" == "1")
                $(".hpg_hienthithem").show();
            else
                $(".hpg_hienthithem").hide();
            if ("${thuocdvthcls}" == "1") {
                $("#thuoccls").show();
            } else {
                $("#thuoccls").hide();
            }
            $("#ket_luan").keydown(function (evt) {
                if (evt.keyCode == 9 && "${Sess_DVTT}"== "70001") {
                    evt.preventDefault();
                    $("#ket_luan").val($("#ket_luan").val().toUpperCase())
                }
            });
            //--End HPG
            var dataURL = "";
            $("#list_benhnhan").jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 400,
                width: 300,
                rowattr: function (rd) {
                    console.log("red", rd)
                    if (rd.CAPCUU === "1") {
                        return {"class": "bncapcuuClass"};
                    }
                    //Khong bhyt va da thanh toan
                    else if (rd.CO_BHYT == 0 && rd.DA_THANH_TOAN == 0) {
                        return {"class": "kobhytClass"};
                    }
                    //Doi tuong ko bao hiem va ko phai la tre em
                    else if (rd.CO_BHYT == 0 && rd.DA_THANH_TOAN == 1) {
                        return {"class": "vienphiClass"};
                    }
                    //la tre em
                    else if (rd.TUOI.indexOf("tháng") != -1) {
                        return {"class": "treemClass"};
                    }
                },
                colNames: ["TT","CO_BAO_HIEM", "Mã y tế", "Họ tên", "Họ tên", "Tuổi", "Nội trú", "gioitinh", "diachi", "sobhyt", "SO_PHIEU", "MA_KHAM_BENH",
                    "stt_benhan", "stt_dotdieutri", "stt_dieutri", "NGUOI_CHI_DINH", "PHONG_CHI_DINH", "BACSI_THUCHIEN", "MA_PHONG_XN", "KET_QUA_CDHA", "MA_PHONGBAN", "khoa", "Buong", "Giuong", "chuandoanicd", "CHUANDOANSOBO", "ngaychidinh", "SOVAOVIEN", "SOVAOVIEN_NOI", "SOVAOVIEN_DT_NOI", "DA_THANH_TOAN", "CAPCUU",
                    "SOBENHAN", "SOBENHAN_TT", "ICD", "sophieuthanhtoan", "tilemiengiam", "ngay_kb",
                    //KGG them goi so
                    "NGAY_SINH", "CO_BHYT", "NGAY_CHI_DINH", "SOTHEBHYT", "SOPHIEU_NGOAITRU", "LAYDL_TU_NGT"
                ],
                colModel: [
                    {name: 'STT_HANGNGAY', index: 'STT_HANGNGAY', width: 50, sorttype: 'int'},
                    {name: 'CO_BAO_HIEM', index: 'CO_BAO_HIEM', hidden: true},
                    {name: 'MABENHNHAN', index: 'MABENHNHAN', hidden: false, width: 100},
                    {name: 'TENBENHNHAN', index: 'TENBENHNHAN', width: 200, hidden: true, sorttype: 'string', searchoptions: {dataInit: function (el) {
                                setTimeout(function () {
                                    $(el).focus().trigger({type: 'keypress', charCode: 13});
                                }, 20);
                            }
                        }
                    },
                    {name: 'TENBENHNHAN_HT', index: 'TENBENHNHAN_HT', hidden: false, width: 200, formatter: function (cellvalue, options, rowObject) {
                            var color;
                            var color_text;
                            if ("${hienthi_mausac_bn}" == "0"){
                                if (rowObject.DA_THANH_TOAN == "1") {
                                    color = '#009900';
                                    color_text = 'white';
                                }   //END VTU:25/10/2016
                                else {
                                    if ("${hienthi_mausac_bn}" == "0")
                                    {
                                        color = 'white';
                                        color_text = 'black';
                                    }
                                }
                            }
                            return '<span class="cellWithoutBackground" style="background-color:' + color + ';font-weight:bold ;color:' + color_text + '">' + cellvalue + '</span>';
                        }},
                    {name: 'TUOI', index: 'TUOI', width: 50, align: 'right'},
                    {name: 'NOITRU', index: 'NOITRU', width: 50, align: 'center'},
                    {name: 'GIOITINH', index: 'GIOITINH', hidden: true},
                    {name: 'DIACHI', index: 'DIACHI', hidden: true},
                    {name: 'SOTHEBHYT', index: 'SOTHEBHYT', hidden: true},
                    {name: 'SO_PHIEU', index: 'SO_PHIEU', hidden: true},
                    {name: 'MA_KHAM_BENH', index: 'MA_KHAM_BENH', hidden: true},
                    {name: 'STT_BENHAN', index: 'STT_BENHAN', hidden: true},
                    {name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', hidden: true},
                    {name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', hidden: true},
                    {name: 'NGUOI_CHI_DINH', index: 'NGUOI_CHI_DINH', hidden: true},
                    {name: 'BACSI_THUCHIEN', index: 'BACSI_THUCHIEN', hidden: true},
                    {name: 'PHONG_CHI_DINH', index: 'PHONG_CHI_DINH', hidden: true},
                    {name: 'MA_PHONG_CDHA', index: 'MA_PHONG_CDHA', hidden: true},
                    {name: 'KET_QUA_CDHA', index: 'KET_QUA_CDHA', hidden: true},
                    {name: 'MA_PHONGBAN', index: 'MA_PHONGBAN', hidden: true},
                    {name: 'KHOA', index: 'KHOA', hidden: true},
                    {name: 'BUONG', index: 'BUONG', hidden: true},
                    {name: 'GIUONG', index: 'GIUONG', hidden: true},
                    {name: 'CHUANDOANICD', index: 'CHUANDOANICD', hidden: true},
                    {name: 'CHUANDOANSOBO', index: 'CHUANDOANSOBO', hidden: true},
                    {name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', hidden: true},
                    {name: 'SOVAOVIEN', index: 'SOVAOVIEN', hidden: true},
                    {name: 'SOVAOVIEN_NOI', index: 'SOVAOVIEN_NOI', hidden: true},
                    {name: 'SOVAOVIEN_DT_NOI', index: 'SOVAOVIEN_DT_NOI', hidden: true},
                    {name: 'DA_THANH_TOAN', index: 'DA_THANH_TOAN', hidden: true},
                    {name: 'CAPCUU', index: 'CAPCUU', hidden: true},
                    {name: 'SOBENHAN', index: 'SOBENHAN', hidden: true},
                    {name: 'SOBENHAN_TT', index: 'SOBENHAN_TT', hidden: true},
                    {name: 'ICD', index: 'ICD', hidden: true},
                    {name: 'SOPHIEUTHANHTOAN', index: 'SOPHIEUTHANHTOAN', hidden: true},
                    {name: 'TI_LE_MIEN_GIAM', index: 'TI_LE_MIEN_GIAM', hidden: true},
                    {name: 'NGAY_KB', index: 'NGAY_KB', hidden: true},
                    {name: 'NGAY_SINH', index: 'NGAY_SINH', hidden: true},
                    {name: 'CO_BHYT', index: 'CO_BHYT', hidden: true},
                    {name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', hidden: true},
                    {name: 'SOTHEBHYT', index: 'SOTHEBHYT', hidden: true},
                    {name: 'SOPHIEU_NGOAITRU', index: 'SOPHIEU_NGOAITRU', hidden: true},
                    {name: 'LAYDL_TU_NGT', index: 'LAYDL_TU_NGT', hidden: true},
                ],
                caption: "Danh sách bệnh nhân",
                ignoreCase: true,
                viewrecords: true,
                rowNum: 200,
                pager: '#pager2',
                gridComplete: function ()
                {
                    var c = $("#list_benhnhan").getGridParam("records");
                    $("#list_benhnhan").jqGrid('setCaption', "Danh sách bệnh nhân (" + c + " bệnh nhân)");
                    if ("${hienthi_mausac_bn}" == "1") {
                        var rows = $("#list_benhnhan").getDataIDs();
                        for (var i = 0; i < rows.length; i++)
                        {
                            var CAPCUU = $("#list_benhnhan").jqGrid('getRowData')[i]["CAPCUU"];
                            var SOTHEBHYT = $("#list_benhnhan").jqGrid('getRowData')[i]["SOTHEBHYT"].toString().length;
                            var CO_BHYT = $("#list_benhnhan").jqGrid('getRowData')[i]["CO_BHYT"].toString();
                            var tuoi = $("#list_benhnhan").jqGrid('getRowData')[i]["TUOI"];
                            var thanhtoan = $("#list_benhnhan").jqGrid('getRowData')[i]["DA_THANH_TOAN"];
                            if (CAPCUU === "1"){
                                $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {color: 'red', weightfont: 'bold'});
                            } else if (CO_BHYT == 0 && thanhtoan == 0) {//CMU:26/10/2017
                                $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {color: '#bf00ff', weightfont: 'bold'});
                            } else if (CO_BHYT == 0 && thanhtoan == 1) {
                                $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {color: '##EE7600', weightfont: 'bold'});
                            } else if (tuoi.indexOf("tháng") != -1) {
                                $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {color: '#00ff00', weightfont: 'bold'});
                            } else {
                                $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {color: 'black', weightfont: 'bold', background: 'white'});
                            }
                        }
                    }
                },
                onSelectRow: function (id) {
                    if (id) {
                        var ret = $("#list_benhnhan").jqGrid('getRowData', id);
                        $("#cmu_ngaychidinh").val(ret.NGAY_CHI_DINH)
                        //CMU Cảnh báo ngoại trú chưa đóng tiền
                        if ("${cdhacanhbao}" > 0 && ret.NOITRU == 0 && !ret.SOTHEBHYT && ret.DA_THANH_TOAN == 0)
                        {
                            jAlert('Bệnh nhân ngoại trú chưa đóng viện phí.', 'Thông báo');
                            if("${cdhacanhbao}" == 2){
                                clear_benhnhan();
                                $("#list_xquang_bhyt").jqGrid('clearGridData');
                                return;
                            }
                        }
                        loadThongTinBenhNhan(ret);
                    }
                },
                onRightClickRow: function (id1) {
                    if (id1) {
                        $('#list_benhnhan').jqGrid('setSelection', id1);
                        $.contextMenu({
                            selector: '#list_benhnhan tr',
                            callback: function (key, options) {
                                if (key == "trathuocvekho") {
                                    var id = $("#list_benhnhan").jqGrid('getGridParam', 'selrow');
                                    var ret = $("#list_benhnhan").jqGrid('getRowData', id);
                                    var arr = ["${Sess_DVTT}", ret.NOITRU, ret.SO_PHIEU, ret.MA_KHAM_BENH, ret.STT_BENHAN, ret.STT_DOTDIEUTRI, ret.STT_DIEUTRI, 0];
                                    if ("${Sess_Admin}" != "0" || ret.BACSI_THUCHIEN == "${hotenbacsi}") {
                                        if("${Sess_DVTT}" == 96004 && "${Sess_UserID}" != "1344516") {
                                            return jAlert("Bạn không có quyền hủy kết quả xét nghiệm, vui lòng liên hệ Minh", 'Thông báo');
                                        }
                                        jConfirm('Xác nhận hủy kết quả  ?', 'Thông báo', function (r) {
                                            if (r.toString() == "true") {
                                                if (ret.SO_PHIEU != "") {
                                                    /*var url = "huyketquacdha?url=" + convertArray(arr);
                                                    $.ajax({
                                                        url: url
                                                    }).always(function () {
                                                        var arr1 = ["${Sess_DVTT}", "Hủy kết quả CDHA cho bệnh nhân " + ret.TEN_BENH_NHAN + " với phiếu CDHA " + ret.SO_PHIEU, "${Sess_UserID}" + "-" + "${Sess_User}", "Hủy kết quả CDHA"];
                                                            $.post("lichsusudung_insert", {url: convertArray(arr1)});
                                                            $("#lammoi").click();
                                                        });*/
                                                    // CMU kiểm tra toa phim
//                                                        var arrp = ["${Sess_DVTT}", ret.SO_PHIEU, ret.NOITRU, ret.SOVAOVIEN, ret.SOVAOVIEN_NOI, ret.SOVAOVIEN_DT_NOI]
//                                                        var urlp = "cmu_kiemtra_toaphim_res?url=" + convertArray(arrp);
//                                                        $.ajax({
//                                                            url: urlp
//                                                        }).done(function (data) {
//                                                            if(data === 0){
                                                    var url = "huyketquacdha?url=" + convertArray(arr);
                                                    $.ajax({
                                                        url: url
                                                    }).always(function () {
                                                        var arr1 = ["${Sess_DVTT}", "Hủy kết quả CDHA cho bệnh nhân " + ret.TEN_BENH_NHAN + " với phiếu CDHA " + ret.SO_PHIEU, "${Sess_UserID}" + "-" + "${Sess_User}", "Hủy kết quả CDHA"];
                                                        $.post("lichsusudung_insert", {url: convertArray(arr1)});
                                                        $("#lammoi").click();
                                                    });
//                                                            } else
//                                                            {
//                                                                jAlert("Xóa phim trước khi hủy", 'Thông báo');
//                                                            }
//                                                        });
                                                    // CMU kiểm tra toa phim

                                                }
                                                else
                                                {
                                                    jAlert("Chọn phiếu để hủy", 'Thông báo');
                                                }
                                            }
                                        });
                                    } else
                                    {
                                        jAlert("Chỉ Admin hoặc bác sĩ thực hiện mới có quyền hủy!", 'Thông báo');
                                    }
                                }
                                if (key == "goiso") {
                                    var id = $("#list_benhnhan").jqGrid('getGridParam', 'selrow');
                                    var ret = $("#list_benhnhan").jqGrid('getRowData', id);
                                    var chuoi = ${Sess_PhongDuocSet} +"|" + ret.STT_HANGNGAY + "|" + ret.TENBENHNHAN + "|" + "0" + "|" + "X Quang" + "|" + "0";
                                    saveTextAsFile(chuoi);
                                }
                            },
                            items: {
                                "goiso": {name: "<span style='color:red'>Gọi số bệnh nhân</span>", icon: "goiso"},
                                "trathuocvekho": {name: "Hủy kết quả"}
                            }
                        });
                    }
                }
            });
            $("#list_benhnhan").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
            $("#list_xquang_bhyt").jqGrid({
                datatype: "local",
                loadonce: true,
                //height: 158,
                height: 260, // ĐắkLắk (An Giang yêu cầu) - Ninh 09/12/2016: điều chỉnh độ cao lưới
                width: 660,
                colNames: ["ma_CDHA", "Yêu cầu chẩn đoán",
                    "Kỹ thuật viên","Bác sĩ đọc kết quả","KYTHUATVIEN",
                    "Kết quả", "Trangthai", "Ma", "DVT", "STT_MAYCDHA", "NGUOI_THUC_HIEN", "Đơn Giá"],
                colModel: [
                    {name: 'MA_CDHA', index: 'MA_CDHA', hidden: true},
                    {name: 'TEN_CDHA', index: 'TEN_CDHA', width: 150},
                    {name: 'KTVTHUCHIEN', index: 'KTVTHUCHIEN', width: 50},
                    {name: 'BACSIDOCKQ', index: 'BACSIDOCKQ', width: 50},
                    {name: 'KYTHUATVIEN', index: 'KYTHUATVIEN', width: 60, hidden: true},
                    {name: 'KET_QUA', index: 'KET_QUA', width: 60, hidden: true},
                    {name: 'TRANGTHAI_BHYT', index: 'TRANGTHAI_BHYT', hidden: true},
                    {name: 'MABAOCAO', index: 'MABAOCAO', hidden: true},
                    {name: 'DVT_CDHA', index: 'DVT_CDHA', hidden: true},
                    {name: 'STT_MAYCDHA', index: 'STT_MAYCDHA', hidden: true},
                    {name: 'NGUOI_THUC_HIEN', index: 'NGUOI_THUC_HIEN', hidden: true},
                    <c:if test="${lay_dongia !='1'}">
                    {name: 'DON_GIA', index: 'DON_GIA', width: 40, hidden: true}
                    </c:if>
                    <c:if test="${lay_dongia =='1'}">
                    {name: 'DON_GIA', index: 'DON_GIA', width: 40}
                    </c:if>
                ],
                caption: "Yêu cầu chẩn đoán hình ảnh",
//                    afterSaveCell: function (rowid, name, val, iRow, iCol) {
//                        var ret = $("#list_xquang_bhyt").jqGrid('getRowData', rowid);
//                        var arr = [$("#sophieu").val(), ret.MA_CDHA, val, "${Sess_DVTT}"];
//                        var url = "cdha_update_ketqua_chitiet?url=" + convertArray(arr);
//                        $.ajax({
//                            url: url
//                        }).done(function () {
//                            var sophieu = $("#sophieu").val();
//                            var noitru = $("#noitru").val();
//                            var sttbenhan = $("#sttbenhan").val();
//                            var sttdotdieutri = $("#sttdotdieutri").val();
//                            var sttdieutri = $("#sttdieutri").val();
//                            var arr1 = [noitru, sophieu, sttbenhan, sttdotdieutri, sttdieutri, "${Sess_DVTT}", sovaovien, sovaovien_noi, sovaovien_dt_noi];
//                            var url1 = "xquang_hienthi_chitiet_svv?url=" + convertArray(arr1);
//                            $("#list_xquang_bhyt").jqGrid('setGridParam', {datatype: 'json', url: url1}).trigger('reloadGrid');
//                        });
//                    },
                ondblClickRow: function (id) {
                    if (id) {
                        var ret = $("#list_xquang_bhyt").jqGrid('getRowData', id);
                        loadThongTinKetQua(ret);
                        $("#tab_cdha").tabs("option", "active", 1);
                    }
                },
                onSelectRow: function (id) {
                    if (id) {
                        var ret = $("#list_xquang_bhyt").jqGrid('getRowData', id);
                        $("#macdha").val(ret.MA_CDHA);
                        $("#ma_maycdha_md").val(ret.STT_MAYCDHA);
                        $("#ten_cdha").val(ret.TEN_CDHA);
                        $("#mauxquang").selectpicker('val',0);//reset mẫu
                        ma_cdha = ret.MA_CDHA;
                        var idBN = $("#list_benhnhan").jqGrid("getGridParam", "selrow");
                        var rowData = $("#list_benhnhan").jqGrid("getRowData", idBN);
                        if (cobhyt.trim() === "")
                            cobhyt = "0";
                        if ("${Sess_DVTT}" == '96004' || "${Sess_DVTT}" == '96014') {
                            if(flag_noitru === '1' && laydl_tungoaitru == 0){
                                load_cttoathuoc("noitru_toadichvu", "list_thuocdichvu");
                            } else if(flag_noitru === '1' && laydl_tungoaitru == 1 && ("${Sess_DVTT}" == '96004' || "${Sess_DVTT}" == '96014')) {
                                var url_nt = "cmu_getlist?url=" + convertArray(["${Sess_DVTT}", mabenhnhan, 'ngoaitru_toadichvu', sophieu_ngoaitru, "CMU_SELECT_FILM_NGOAITRU"]);
                                $("#list_thuocdichvu").jqGrid('setGridParam', {
                                    datatype: 'json',
                                    url: url_nt
                                }).trigger('reloadGrid')
                            }
                            else{
                                var url = 'chitiettoathuocngoatru_svv?matt=' + matoathuoc + "&nghiepvu=ngoaitru_toadichvu&dvtt=${Sess_DVTT}" + "&sovaovien=" + sovaovien + "&ma=" + ma_cdha + "&sophieu=" + rowData.SO_PHIEU + "&theo_dv=1"
                                $("#list_thuocdichvu").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
                            }
                        } else {
                            if(flag_noitru === '1'){
                                load_cttoathuoc("noitru_toadichvu", "list_thuocdichvu");
                            }
                            else{
                                var url = 'chitiettoathuocngoatru_svv?matt=' + matoathuoc + "&nghiepvu=ngoaitru_toadichvu&dvtt=${Sess_DVTT}" + "&sovaovien=" + sovaovien +
                                    "&ma=" + ma_cdha + "&sophieu=" +  rowData.SO_PHIEU + "&theo_dv=1";
                                $("#list_thuocdichvu").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
                            }
                        }
                    }
                },
                gridComplete: function () {
                    var str = $("#list_xquang_bhyt").getGridParam("records");
                    if (str != "0") {
//                            $('#list_xquang_bhyt').jqGrid('setSelection', "1");
                    }
                }
            });
            $("#lammoi").click(function (evt) {
                reload_grid();
            });
            $("#luu_tt_maycdha").click(function (evt) {
                var sophieu = $("#sophieu").val();
                var noitru = $("#noitru").val();
                var makhambenh = $("#makhambenh").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var macdha = $("#macdha").val();
                if(!macdha){
                    jAlert("Chọn yêu cầu CDHA trước để cập nhật");
                    return;
                }
                if (sophieu != "" && macdha != "" && (sovaovien!= ""|| sovaovien_noi!="")) {
                    $.post("luu_maycdha_vaobangchitiet", {
                        sophieu: sophieu, macdha: macdha,noitru: noitru,
                        sttbenhan: sttbenhan, sttdotdieutri: sttdotdieutri, sttdieutri: sttdieutri,
                        makhambenh: makhambenh,sovaovien:sovaovien,
                        sovaovien_noi:sovaovien_noi,
                        sovaovien_dt_noi:sovaovien_dt_noi,
                        stt_may_cdha:($("#ma_maycdha_md").val())
                    }).done(function (data) {
                        if (data=="1"){
                            jAlert("Cập nhật thành công!", "Thông báo");
                        }else{
                            jAlert("Vui lòng kiểm tra lại thông tin!", "Thông báo");
                        }
                    }).fail(function(){
                        jAlert("Vui lòng thử lại!", "Thông báo");
                    });
                }
            });
            $("#ds_cdha").change(function(){
                $(".tr_ketqua").css('display','');
                var sophieu = $("#sophieu").val();
                var ma_cdha_xquang = $(this).val();
                var noitru = $("#noitru").val();
                if(ma_cdha_xquang == 0){
                    $("#mauxquang").selectpicker('val',0);
                    CKEDITOR.instances.ketqua.setData("");
                    $("#ket_luan").val("");
                }else{
                    var arr = ["${Sess_DVTT}",ma_cdha_xquang, sophieu, noitru,sovaovien,sovaovien_noi,sovaovien_dt_noi,0];
                    var url = "lay_mau_xquang_bpc?url=" + convertArray(arr);
                    $.getJSON(url, function (result) {
                        $.each(result, function (i, field) {
                            //  var ma_mau = field.MA_MAU_XQUANG;
                            if($('#dathuchien1').prop('checked') == false) {
                                //$("#mauxquang").val(ma_mau);
                                CKEDITOR.instances.ketqua.setData(field.NOIDUNG);
                                $("#ket_luan").val(field.KET_LUAN);
                            }
                            else{
                                $("#mauxquang").selectpicker('val',0);
                                //CKEDITOR.instances.ketqua.setData("");
                                //$("#ket_luan").val("");
//                                CKEDITOR.instances.ketqua.setData(field.NOIDUNG);
//                                $("#ket_luan").val(field.KET_LUAN);

                                //$("#loidanbacsi").css('display','none');
                            }
                            if("${CT}" != 'CT'){
                                if("${Sess_DVTT}"=="70001") {
                                    $(".tr_ketqua").css('display', 'none');
                                }else{
                                    $(".tr_ketqua").css('display','');
                                }
                            }else{
                                $(".tr_ketqua").css('display','');
                            }
                        });
                    });
                }
                ma_cdha = $("#ds_cdha").val();
                sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var idBN = $("#list_benhnhan").jqGrid("getGridParam", "selrow");
                var rowData = $("#list_benhnhan").jqGrid("getRowData", idBN);
                if ("${Sess_DVTT}" == '96004' || "${Sess_DVTT}" == '96014') {
                    if(flag_noitru === '1' && laydl_tungoaitru == 0){
                        load_cttoathuoc("noitru_toadichvu", "list_thuocdichvu");
                    } else if(flag_noitru === '1' && laydl_tungoaitru == 1) {
                        var url_nt = "cmu_getlist?url=" + convertArray(["${Sess_DVTT}", mabenhnhan, 'ngoaitru_toadichvu', sophieu_ngoaitru, "CMU_SELECT_FILM_NGOAITRU"]);
                        $("#list_thuocdichvu").jqGrid('setGridParam', {
                            datatype: 'json',
                            url: url_nt
                        }).trigger('reloadGrid')
                    } else{
                        var url = 'chitiettoathuocngoatru_svv?matt=' + matoathuoc + "&nghiepvu=ngoaitru_toadichvu&dvtt=${Sess_DVTT}" + "&sovaovien=" + sovaovien + "&ma=" + ma_cdha + "&sophieu=" +  rowData.SO_PHIEU;
                        $("#list_thuocdichvu").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
                    }
                } else {
                    if(flag_noitru === '1'){
                        load_cttoathuoc("noitru_toadichvu", "list_thuocdichvu");
                    }
                    else{
                        var url = 'chitiettoathuocngoatru_svv?matt=' + matoathuoc + "&nghiepvu=ngoaitru_toadichvu&dvtt=${Sess_DVTT}" + "&sovaovien=" + sovaovien + "&ma=" + ma_cdha + "&sophieu=" +  rowData.SO_PHIEU;
                        $("#list_thuocdichvu").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
                    }
                }
            });
            function clear_benhnhan(){
                $("#mabenhnhan").val('');
                $("#hoten").val('');
                $("#tuoi").val('');
                $("#gioitinh").val('');
                $("#diachi").val('');
                $("#sothebhyt").val('');
                $("#sophieu").val('');
                $("#makhambenh").val('');
                $("#noitru").val('');
                $("#sttbenhan").val('');
                $("#sttdotdieutri").val('');
                $("#sttdieutri").val('');
                $("#mabacsichidinh").val('');
                $("#cmu_bacsichidinh").val('');
                $("#bacsichuyenkhoa").val('');
                sovaovien = '';
                sovaovien_noi = '';
                sovaovien_dt_noi = '';
                mabenhnhan='';
                stt_dieutri = '';
                stt_benhan = '';
                stt_dotdieutri = '';
                sophieuthanhtoan='';
                flag_noitru='';
                cobhyt = '';
                ngay_kb = '';
                $('#tt_thanhtoan').val("");
                $("#Khoa").val('');
                $("#buong").val('');
                $("#giuong").val('');
                $("#chandoan").val('');
                $("#solan").val('');
                $("#ngaychidinh").val('');
                $("#hoten_ct").val('')
                $("#tuoi_ct").val('')
                $("#gioitinh_ct").val('')
                $("#mabenhnhan_ct").val('')
                $("#tenkhoa_ct").val('')
                $("#sothebhyt_ct").val('')
            }
            //CMU: 26062017
            var dialog_lichsuCHHA =
                new jBox('Modal', {
                    title: "Lịch sử X-Quang",
                    overlay: false,
                    content: $('#dialog_lichsuCDHA'),
                    draggable: 'title'
                });
            $("#lichsuCDHA").click(function (evt) {
                //var stt_benhan = $("#sttbenhan").val();
                var mabenhnhan = $("#mabenhnhan").val();
                var hoten = $("#hoten").val();
                if (hoten !== "") {
                    load_lscdha_bn(mabenhnhan);
                    dialog_lichsuCHHA.open();
                } else {
                    jAlert("Vui lòng chọn một bệnh nhân!", "Thông báo");
                }
            });
            $("#list_lichsuCDHA").jqGrid({
                datatype: "local",
                loadonce: true,
                height: 450,
                width: 900,
                colNames: ["Ngày", "Đơn vị", "Chẩn đoán", "Tên X-Quang", "Số lượng", "Đơn giá", "Thành tiền", "BHYT không chi", "Kết quả", "Kết luận",
                    "sophieu", "stt_benhan","Kết quả"],
                colModel: [
                    {name: 'NGAY_THUC_HIEN', index: 'NGAY_THUC_HIEN', width: 55},
                    {name: 'TEN_DONVI', index: 'TEN_DONVI', width: 80},
                    {name: 'CHANDOAN', index: 'CHANDOAN', width: 100},
                    {name: 'TEN_CDHA', index: 'TEN_CDHA'},
                    {name: 'SO_LUONG', index: 'SO_LUONG', width: 100, hidden: true},
                    {name: 'DON_GIA', index: 'DON_GIA', width: 100, align: 'right', formatter: 'integer', formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}, hidden: true},
                    {name: 'THANH_TIEN', index: 'THANH_TIEN', width: 100, align: 'right', formatter: 'integer', formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}, hidden: true},
                    {name: 'BHYTKCHI', index: 'BHYTKCHI', width: 110, align: 'center', formatter: 'checkbox', formatoptions: {value: 'true:false'}, hidden: true},
                    {name: 'KET_QUA', index: 'KET_QUA', width: 100, hidden: true},
                    {name: 'MO_TA', index: 'MO_TA', width: 80},
                    {name: 'SO_PHIEU_XN', index: 'SO_PHIEU_XN', width: 100, hidden: true},
                    {name: 'STT_BENHAN', index: 'STT_BENHAN', width: 100, hidden: true},
                    {name: 'KET_QUA', index: 'KET_QUA', width: 150}
                ],
                caption: "Lịch sử X-Quang",
                rowNum: 1000
            });
            $("#tab_ls_cdha").tabs();
            //END CMU: 26062017

            function myelem(value, options) {
                var el = document.createElement("input");
                el.type = "text";
                el.value = value;
                el.onkeypress = function (e) {
                    var theEvent = e || window.event;
                    var key = theEvent.keyCode || theEvent.which;
                    key = String.fromCharCode(key);
                    var regex = /[0-9]|\./;
                    if (!regex.test(key)) {
                        theEvent.returnValue = false;
                        if (theEvent.preventDefault)
                            theEvent.preventDefault();
                    }
                };
                return el;
            }

            function myvalue(elem, operation, value) {
                if (operation === 'get') {
                    return $(elem).val();
                } else if (operation === 'set') {
                    $('input', elem).val(value);
                }
            }

            function checkTrungTG() {
                var thoiGianTHYL = $('#ngaybatdauthuchien').val().substring(0, 16);
                var ngayKQ = $('#ngayth_ct').val();
                var gioKQ = $('#gioth_ct').val();
                var thoiGianKQ = ngayKQ + ' ' + gioKQ;
                var noitru = $("#noitru").val();
                var arr = ["${Sess_DVTT}", $('#cboNguoiDocKetQua').val(), thoiGianTHYL, thoiGianKQ.substring(0, 16), noitru];
                var url = "cmu_list_CMU_KT_THOIGIAN_CLS?url=" + convertArray(arr);
                var result = true;
                $.ajax({
                    url: url,
                    async: false,
                    success: function (data) {
                        if (data && data.length > 0) {
                            result = false;
                            $.alert({
                                title: 'Cảnh báo!',
                                content: '<b style="color:#c10606">Dữ liệu không hợp lệ, vui lòng kiểm tra lại.</b> </br><table id="kt_trung_thoigian_cls" class="jqx-grid-cell-wrap"></table>',
                                type: 'red',
                                boxWidth: '750px',
                                useBootstrap: false,
                                escapeKey: true,
                                closeIcon: true,
                                typeAnimated: true,
                                onContentReady: function () {
                                    $("#kt_trung_thoigian_cls").jqGrid({
                                        datatype: 'local',
                                        data: data,
                                        rownumbers: true,
                                        height: 'auto',
                                        colModel: [
                                            { name: 'LOI', label: 'Lỗi', width: 380, align: 'left' },
                                            { name: 'NGAY_THUC_HIEN_YL', label: 'Ngày thực hiện YL', width: 160, align: 'center'},
                                            { name: 'NGAY_THUC_HIEN', label: 'Ngày thực hiện', width: 160, align: 'center' },
                                        ],
                                    });
                                }
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error("Error: ", error);
                    }
                });

                return result;
            }

            $("#luu_tt").click(function (evt) {
                if ("${Sess_Admin}" == "0" && $("#nguoithuchien").val() != "0" && $("#nguoithuchien").val() != "${Sess_UserID}" && $("#nguoithuchien").val().trim() != "" && $("#dathuchien").prop("checked") == true) {
                    jAlert("Bạn không thể chỉnh sửa KQ x quang của nhân viên khác!", 'Thông báo');
                } else if ("${nhapketluan_cls}" == "1" && ($("#loidanbacsi").val() == "" || $("#loidanbacsi").val() == null)) { // 20171102 NAN VINHVT HISHD-21197 ADD
                    jAlert("Chưa nhập kết luận cho bệnh nhân!", 'Cảnh báo');
                }
                else {
                    var thoiGianChiDinh = $('#thoigianchidinh').val();
                    var thoiGianTHYL = $('#ngaybatdauthuchien').val();
                    var ngayKQ = $('#ngayth_ct').val();
                    var gioKQ = $('#gioth_ct').val();
                    var thoiGianKQ = ngayKQ + ' ' + gioKQ;

                    var momentChiDinh = moment(thoiGianChiDinh, ['DD/MM/YYYY HH:mm:ss']);
                    var momentThucHienYLenh = moment(thoiGianTHYL, ['DD/MM/YYYY HH:mm:ss']);
                    var momentKetQua = moment(thoiGianKQ, ['DD/MM/YYYY HH:mm:ss']);

                    if(momentThucHienYLenh.diff(momentChiDinh, 'minutes') < 1){
                        jAlert('THỜI GIAN BĐ : '+thoiGianTHYL+'<br> KHÔNG ĐƯỢC NHỎ HƠN HOẶC BẰNG'+'<br>THỜI GIAN CHỈ ĐỊNH : ' + thoiGianChiDinh , 'Thông báo');
                        return;
                    }

                    if(momentKetQua.diff(momentThucHienYLenh, 'minutes') < 1){
                        jAlert('THỜI GIAN TH : '+thoiGianKQ+'<br> KHÔNG ĐƯỢC NHỎ HƠN HOẶC BẰNG'+'<br>THỜI GIAN BĐ : ' + thoiGianTHYL , 'Thông báo');
                        return;
                    }

                    if(momentKetQua.diff(momentThucHienYLenh, 'minutes') < 5 && "${Sess_DVTT}" != '96025'){
                        jAlert('THỜI GIAN BĐ: '+thoiGianTHYL+'<br> ĐẾN GIỜ '+'<br>THỜI GIAN TH : ' + thoiGianKQ + " KHÔNG ĐƯỢC NHỎ HƠN 5 PHÚT", 'Thông báo');
                        return;
                    }

                    if(THAMSO_960624 == "1" && typeof cmuKiemtratungThoigianCLSTheoNV == 'function' &&  !cmuKiemtratungThoigianCLSTheoNV({
                        dvtt: "${Sess_DVTT}",
                        sovaovien: $("#noitru").val()==1?sovaovien_noi:sovaovien,
                        userId: $("#cbbacsittpt").val(),
                        thoigianbd: momentThucHienYLenh.format('DD/MM/YYYY HH:mm'),
                        thoigiankt: momentKetQua.format('DD/MM/YYYY HH:mm'),
                        loaikythuat: "XQUANG"
                    })) {
                        return false;
                    }
                    if(THAMSO_960624 == "1" && typeof cmuKiemtratungThoigianCLSTheoNV == 'function' &&  !cmuKiemtratungThoigianCLSTheoNV({
                        dvtt: "${Sess_DVTT}",
                        sovaovien: $("#noitru").val()==1?sovaovien_noi:sovaovien,
                        userId: $("#cmu_kythuatvien").val(),
                        thoigianbd: momentThucHienYLenh.format('DD/MM/YYYY HH:mm'),
                        thoigiankt: momentKetQua.format('DD/MM/YYYY HH:mm'),
                        loaikythuat: "XQUANG"
                    })) {
                        return false;
                    }


                    if (!checkTrungTG()) {
                        return false;
                    }

                    var cophim1318 = 0;
                    var cophim1820 = 0;
                    var cophim2430 = 0;
                    var cophim3040 = 0;
                    var solanchup=0;
                    var sophieu = $("#sophieu").val();
                    var makhambenh = $("#makhambenh").val();
                    var noitru = $("#noitru").val();
                    var sttbenhan = $("#sttbenhan").val();
                    var sttdotdieutri = $("#sttdotdieutri").val();
                    var sttdieutri = $("#sttdieutri").val();
                    var macdha = $("#macdha").val();
                    if("${hienthi_checkbox}" == "1"){
                        var macdha = $("#ds_cdha").val();
                    }
                    else {
                        var macdha = $("#macdha").val();
                    }
                    var dvtt = "${Sess_DVTT}";
                    var ketqua = CKEDITOR.instances.ketqua.getData();
                    var bacsichidinh = $("#bacsichidinh").val();
                    var bacsithuchien = !!$("#cbbacsittpt").val() ? $("#cbbacsittpt option:selected").text() : "${hotenbacsi}";
                    var loidanbacsi = $("#loidanbacsi").val();
                    var ketluan = $("#ket_luan").val();
                    var nguoithuchien = !!$("#cbbacsittpt").val() ? $("#cbbacsittpt").val() : "${Sess_UserID}";
                    var ngaythuchien = $("#ngaythuchien_cls").val();
                    //--HPG- Khong cho phep sua ket qua CLS
                    var ngaygioth_ct = "";
                    if ($("#ngayth_ct").val() != '' && $("#gioth_ct").val() != '') {
                        ngaygioth_ct = convertStr_MysqlDate($("#ngayth_ct").val()) + " " + $("#gioth_ct").val();
                    }
                    if ($("#ngaythuchien_cls").val() != '' && $("#giothuchien_cls").val() != '') {
                        ngaythuchien = convertStr_MysqlDate($("#ngaythuchien_cls").val()) + " " + $("#giothuchien_cls").val();
                    }
                    if ($("#cophim13x18").val() != "") {
                        cophim1318 = $("#cophim13x18").val();
                    } else
                    {
                        cophim1318 = 0;
                    }
                    if ($("#cophim18x20").val()) {
                        cophim1820 = $("#cophim18x20").val();
                    } else
                    {
                        cophim1820 = 0;
                    }
                    if ($("#cophim24x30").val()) {
                        cophim2430 = $("#cophim24x30").val();
                    } else
                    {
                        cophim2430 = 0;
                    }
                    //jAlert(cophim2430);
                    if ($("#cophim30x40").val()) {
                        cophim3040 = $("#cophim30x40").val();
                    } else
                    {
                        cophim3040 = 0;
                    }
                    if ($("#solanchup").val() != "") {
                        solanchup = $("#solanchup").val();
                    }else{
                        solanchup=0;
                    }

                    var thamso960542;
                    var url = 'laythamso_donvi_motthamso?mathamso=960542';
                    $.post(url).done(function(data) {
                        thamso960542 = data
                    });
                    if($("#cbbacsittpt").val() == '') {
                        return jAlert("Vui lòng chọn bác sĩ đọc kết quả");
                    }
                    if($("#cmu_kythuatvien").val() == '') {
                        return jAlert("Vui lòng chọn kỹ thuật viện thực hiện");
                    }

                    var ngaybatdauthuchien = $("#ngaybatdauthuchien").val() ;
                    var ngaygiochidinh = $("#thoigianchidinh").val()
                    var ngaygiothuchien = $("#ngayth_ct").val() + " " + $("#gioth_ct").val();
                    var momentKQ = moment(ngaygiothuchien, ['DD/MM/YYYY HH:mm:ss']);
                    var momentCD = moment(ngaygiochidinh, ['DD/MM/YYYY HH:mm:ss']);
                    var momentBatdau = moment(ngaybatdauthuchien, ['DD/MM/YYYY HH:mm:ss']);

                    if(!isValidDateTimeSecond(ngaybatdauthuchien)) {
                        jAlert('Ngày giờ bắt đầu thực hiện không hợp lệ', 'Thông báo');
                        return false;
                    }
                    if( !isValidDateTimeSecond(ngaygiothuchien)) {
                        jAlert('Ngày giờ thực hiện không hợp lệ', 'Thông báo');
                        return false;
                    }
                    if(momentBatdau.isBefore(momentCD)) {
                        jAlert('NGÀY-GIỜ CHỈ ĐỊNH: '+ngaygiochidinh+'<br> KHÔNG ĐƯỢC LỚN HƠN '+'<br>NGÀY-GIỜ BẮT ĐẦU : ' + ngaybatdauthuchien + " ", 'Thông báo');
                        return;
                    }
                    var ktrathoigian = $.ajax({type: "POST", url: "cmu_post", async: false, //Chỉ định CLS Viện Phí lấy giá trên grid. cho phép sửa giá
                        data: {url: ["${Sess_DVTT}", $("#sophieu").val(),ngaygioth_ct,
                                sovaovien_noi == 0? sovaovien:sovaovien_noi, noitru,'CMU_KTRATHOI_CDVAKQ'].join('```')}
                    }).responseText;
                    if(ktrathoigian == 1) {
                        jAlert("Thời gian thực hiện không được dưới 5 phút", 'Cảnh báo');
                        return false;
                    }

                    $.post('cmu_post', {
                        url: [
                            sophieu, "${Sess_DVTT}", macdha, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri,
                            sovaovien, sovaovien_noi, sovaovien_dt_noi,
                            'CMU_CHECKMAY_CDHA_SA'
                        ].join('```')
                    }).done(function(data) {
                        if(thamso960542 == 1 && data > 0){
                            jAlert("Vui lòng Lưu máy thực hiện trước khi Lưu thông tin", "Cảnh báo");
                        } else {
                            if(ketluan.length < 5){
                                jAlert("Kết luận quá ngắn, vui lòng nhập nhiều hơn 5 ký tự", "Cảnh báo");
                            } else
                            if ("${suaketqua_cls}" == "1") {
                                var arr1 = [makhambenh, sophieu, "${Sess_DVTT}", noitru, sttbenhan, sttdotdieutri, sttdieutri, 0]
                                var url1 = "hpg_xquang_trangthai_thanhtoan?url=" + convertArray(arr1);
                                $.ajax({
                                    url: url1
                                }).done(function (data) {
                                    if (data === "2") {
                                        jAlert("Bệnh nhân đã thanh toán viện phí. Không được thay đổi kết quả.", 'Cảnh báo');
                                        return false;
                                    } else
                                    if (sophieu !== "") {
                                        $.post("capnhatketqua_xquang_svv", {
                                            sophieu: sophieu,
                                            macdha: macdha,
                                            dvtt: dvtt,
                                            ketqua: ketqua,
                                            bacsichidinh: bacsichidinh,
                                            bacsithuchien: bacsithuchien,
                                            loidanbacsi: loidanbacsi,
                                            noitru: noitru,
                                            sttbenhan: sttbenhan,
                                            sttdotdieutri: sttdotdieutri,
                                            sttdieutri: sttdieutri,
                                            makhambenh: makhambenh,
                                            cophim1318: cophim1318,
                                            cophim1820: cophim1820,
                                            cophim2430: cophim2430,
                                            cophim3040: cophim3040,
                                            sovaovien: sovaovien,
                                            sovaovien_noi: sovaovien_noi,
                                            ketluan: ketluan,
                                            sovaovien_dt_noi: sovaovien_dt_noi,
                                            nguoithuchien: nguoithuchien,
                                            ngaygioth_ct: ngaygioth_ct,
                                            ngaythuchien: ngaygioth_ct,
                                            thoiGianBatDauCls: ngaybatdauthuchien,
                                            solanchup: solanchup
                                        })//CMU lấy ngày chi tiết luôn
                                            .done(function (datares) {
                                                if (datares == -1) {
                                                    jAlert("Dữ liệu bệnh nhân đã khóa không thể chỉnh sửa, vui lòng liên hệ admin.", 'Cảnh báo');
                                                    return false;
                                                }
                                                $.post('cmu_post', {
                                                    url: [
                                                        "${Sess_DVTT}", sophieu, macdha,
                                                        sovaovien == 0? sovaovien_noi: sovaovien, sovaovien_dt_noi,
                                                        $("#cmu_kythuatvien").val(), $("#cbbacsittpt").val(),
                                                        'CMU_UPDATE_KTV_XQUANG'
                                                    ].join('```')
                                                })
                                                jAlert("Cập nhật thành công", 'Thông báo');
                                            });
                                    }
                                });
                            } else
                                //-----End HPG
                            if (sophieu != "") {
                                $.post("capnhatketqua_xquang_svv", {sophieu: sophieu, macdha: macdha, dvtt: dvtt,
                                    ketqua: ketqua, bacsichidinh: bacsichidinh,
                                    bacsithuchien: bacsithuchien, loidanbacsi: loidanbacsi, noitru: noitru,
                                    sttbenhan: sttbenhan, sttdotdieutri: sttdotdieutri, sttdieutri: sttdieutri, makhambenh: makhambenh, cophim1318: cophim1318,
                                    cophim1820: cophim1820, cophim2430: cophim2430, cophim3040: cophim3040, sovaovien: sovaovien, sovaovien_noi: sovaovien_noi, ketluan: ketluan,
                                    sovaovien_dt_noi: sovaovien_dt_noi, nguoithuchien: nguoithuchien, ngaygioth_ct: ngaygioth_ct,ngaythuchien: ngaythuchien,thoiGianBatDauCls: ngaybatdauthuchien,solanchup: solanchup
                                })
                                    .done(function (datares) {
                                        if (datares == -1) {
                                            jAlert("Dữ liệu bệnh nhân đã khóa không thể chỉnh sửa, vui lòng liên hệ admin.", 'Cảnh báo');
                                            return false;
                                        }
                                        $.post('cmu_post', {
                                            url: ["${Sess_DVTT}", sophieu, macdha,
                                                sovaovien == 0? sovaovien_noi: sovaovien, sovaovien_dt_noi,
                                                $("#cmu_kythuatvien").val(), $("#cbbacsittpt").val(),
                                                'CMU_UPDATE_KTV_XQUANG'
                                            ].join('```')
                                        })
                                        jAlert("Cập nhật thành công", 'Thông báo');
                                    });
                            }
                        }
                    });
                    var arr = [noitru, sophieu, sttbenhan, sttdotdieutri, sttdieutri, dvtt, sovaovien, sovaovien_noi, sovaovien_dt_noi, 0];
                    if("${CT}" === "CT"){
                        var url = "ct_hienthi_chitiet_svv?url=" + convertArray(arr);
                    }
                    else {
                        var url = "xquang_hienthi_chitiet_svv?url=" + convertArray(arr);
                    }
                    if($('#dathuchien').prop('checked') == false) {
                        reload_grid_dscd(url);
                    }
                }
            });
            $("#luuthongtin").click(function (evt) {
                $("#luu_tt").click();
            });
            $("#ketqua_cdha").click(function (evt) {
                if (noitru_ngoaitru == 0)
                {
                    if ($("#hoten").val().trim() != "") {
                        var makhambenh = $("#makhambenh").val();
                        var hoten = $("#hoten").val();
                        var tuoi = $("#tuoi").val();
                        var namhientai = getDate("${ngayhientai}").getFullYear();
                        var namsinh = namhientai - parseInt(tuoi);
                        var gioitinh = $("#gioitinh option:selected").text();
                        var diachi = $("#diachi").val();
                        var chandoan = $("#chandoan").val();
                        if ("${Sess_DVTT}" == "82008")
                        {
                            var bschidinh = $("#mabacsichidinh").val();
                        } else
                        {
                            var bschidinh = "${Sess_UserID}";
                        }
                        var sophieucdha = $("#sophieu").val();
                        var noitru = "0";
                        var sttbenhan = "";
                        var sttdotdieutri = "";
                        var sttdieutri = "";
                        var sothebaohiem = $("#sothebhyt").val();
                        if (sothebaohiem == undefined)
                            sothebaohiem = $("#sothebhyt").val();
                        var arr = [makhambenh, hoten, namsinh, gioitinh, diachi, chandoan,
                            bschidinh, sophieucdha, noitru, sttbenhan, sttdotdieutri, sttdieutri, sothebaohiem, "0"];
                        var url = "chandoan_inketqua?url=" + convertArray(arr);
                        if ("${taibaocaovemay}" == "1") {
                            var redirectWindow = window.open(url, '_blank');
                            redirectWindow.location;
                            return false;
                        } else
                            $(location).attr('href', url);
                    }
                } else
                {
                    if ($("#hoten").val().trim() != "") {
                        var hoten = $("#hoten").val();
                        var tuoi = $("#tuoi").val();
                        var namhientai = getDate("${ngayhientai}").getFullYear();
                        var namsinh = namhientai - parseInt(tuoi);
                        var gioitinh = $("#gioitinh option:selected").text();
                        var diachi = $("#diachi").val();
                        var chandoan = $("#chandoan").val();
                        var makhambenh = $("#makhambenh").val();

                        if ("${Sess_DVTT}" == "82008")
                        {
                            var bschidinh = $("#mabacsichidinh").val();
                        } else
                        {
                            var bschidinh = "${Sess_UserID}";
                        }
                        var sophieucdha = $("#sophieu").val();
                        var sothebaohiem = $("#sothebhyt").val();
                        var noitru = "1";
                        var arr = [makhambenh, hoten, namsinh, gioitinh, diachi, chandoan,
                            bschidinh, sophieucdha, noitru, stt_benhan, stt_dotdieutri, stt_dieutri, sothebaohiem, "0"];
                        var url = "chandoan_inketqua?url=" + convertArray(arr);
                        $(location).attr('href', url);
                    }
                }
            });
            $("#phieu_cdha_xquang").click(function (evt) {
                if (noitru_ngoaitru == "0") {
                    var makhambenh = $("#makhambenh").val();
                    var sophieu = $("#sophieu").val();
                    var bhytkhongchi = co_bao_hiem == 1 ? 0 : 1;
                    var arr = [makhambenh, bhytkhongchi, sophieu, "${Sess_DVTT}", "0", "1"];
                    //Ðo?n mã c?a BDH
                    var hoten = $("#hoten").val();
                    var tuoi = $("#tuoi").val();
                    var phai = $("#gioitinh").val();
                    if (phai.toString() == "true") {
                        phai = "Nam";
                    } else {
                        phai = "Nữ";
                    }
                    var gioitinh = phai;
                    var diachi = $("#diachi").val();
                    var bschidinh = bacsi_chidinh;
                    var sothebaohiem = "";
                    //Ki?m tra b?nh nhân có BHYT
                    if (bhytkhongchi == "0")
                        sothebaohiem = $("#sothebhyt").val();
                    var maphong = phongcdha_ss;
                    if ("${thanhtoannhieunac}" == "1" && bhytkhongchi == "0") {
                        var url_taobk = "taobangke_truocin?makb=" + makhambenh + "&dvtt=" + "${Sess_DVTT}" + "&sophieu=0";
                        $.ajax({
                            url: url_taobk
                        }).done(function (data) {
                            if ("${Sess_DVTT}".indexOf("52") == 0 || "${Sess_DVTT}" == "82023") {
                                var url = "laykyhieubaocaophongcdha?maphongcdha=" + maphong;
                                $.ajax({
                                    url: url
                                }).done(function (data) {
                                    //1:PXQ
                                    if (data == "1" && sophieu != "") {
                                        arr = ["", hoten, diachi, tuoi, gioitinh, makhambenh, sophieu, "0",
                                            "${Sess_DVTT}", "0", "", "", "", "", "", "", sothebaohiem, "", bschidinh, ""];
                                        url = "bdh_inketquaxquang?url=" + convertArray(arr);
                                        $(location).attr('href', url);
                                    } else {
                                        url = "inphieucdha?url=" + convertArray(arr);
                                        $(location).attr('href', url);
                                    }
                                });
                            } else {
                                url = "inphieucdha?url=" + convertArray(arr);
                                var dvtt = "${Sess_DVTT}";
                                if ("${taibaocaovemay}" == "1") {
                                    var redirectWindow = window.open(url, '_blank');
                                    redirectWindow.location;
                                    return false;
                                } else
                                    $(location).attr('href', url);
                            }
                        });
                    } else {
                        if ("${Sess_DVTT}".indexOf("52") == 0 || "${Sess_DVTT}" == "82023") {
                            var url = "laykyhieubaocaophongcdha?maphongcdha=" + maphong;
                            $.ajax({
                                url: url
                            }).done(function (data) {
                                if (data == "1" && sophieu != "") {
                                    arr = ["", hoten, diachi, tuoi, gioitinh, makhambenh, sophieu, "0",
                                        "${Sess_DVTT}", "0", "", "", "", "", "", "", sothebaohiem, "", bschidinh, ""];
                                    url = "bdh_inketquaxquang?url=" + convertArray(arr);
                                    $(location).attr('href', url);
                                } else {
                                    url = "inphieucdha?url=" + convertArray(arr);
                                    $(location).attr('href', url);
                                }
                            });
                        } else {
                            url = "inphieucdha?url=" + convertArray(arr);
                            var dvtt = "${Sess_DVTT}";
                            if ("${taibaocaovemay}" == "1") {
                                var redirectWindow = window.open(url, '_blank');
                                redirectWindow.location;
                                return false;
                            } else
                                $(location).attr('href', url);
                        }
                    }
                } else {

                    var sophieucdha = $("#sophieu").val();
                    var bhytkhongchi = co_bao_hiem == 1 ? 0 : 1;
                    var sobenhantt = sobenhan_tt;
                    var sobenhan = sobenhan;
                    var icd_khoadt = icd_benhnhan;
                    var ten_khoadt = ten_icd_benhnhan;
                    if (sobenhan != "")
                        soba = sobenhan_nt;
                    else
                        soba = sobenhantt;
                    var arr = [mabenhnhan, bhytkhongchi, sophieucdha, "${Sess_DVTT}", soba, stt_benhan, stt_dotdieutri, stt_dieutri, icd_khoadt, ten_khoadt, "", "0"
                        , sovaovien_noi, sovaovien_dt_noi, 0];
                    var url = "noitru_inphieucdha_svv?url=" + convertArray(arr);
                    $(location).attr('href', url);
                }
            });
            $("#inphieu_xquang").click(function (evt) {
                var maPhongBan = $("#phongban").val();
                var mabenhnhan = $("#mabenhnhan").val();
                var hoten = $("#hoten").val();
                var diachi = $("#diachi").val();
                var tuoi = $("#tuoi").val();
                var phai = $("#gioitinh").val() === "true" ? "Nam" : "Nữ";
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var macdha = $("#macdha").val();
                if("${hienthi_checkbox}" == "1"){
                    var macdha = $("#ds_cdha").val();
                    if(macdha == "0"){
                        macdha = ma_cdha;
                    }
                }
                else {
                    var macdha = $("#macdha").val();
                }
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var khoa = $("#Khoa").val();
                var giuong = $("#giuong").val();
                var buong = $("#buong").val();
                var sothebaohiem = $("#sothebhyt").val();
                var chandoan = $("#chandoan").val();
                var bacsidieutri = $("#bacsichidinh").val();
                var mabacsichidinh = $("#mabacsichidinh").val();
                var bacsichuyenkhoa = $("#bacsichuyenkhoa").val();
                var ngaychidinh = $("#ngaychidinh").val();
                var solan = $("#solan").val();
                //VNPTHIS-4697 23/11/2017
                var typein = $("#loaiin").val();
                //VNPTHIS-4697 23/11/2017
                if (sophieu != "" && macdha != "") {
                    var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha,
                        dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, khoa, giuong, buong, sothebaohiem, chandoan, bacsidieutri, bacsichuyenkhoa, solan, mabacsichidinh, ngaychidinh, "0",
                        sovaovien,
                        sovaovien_noi,
                        sovaovien_dt_noi, 0, maPhongBan,0, typein]; //VNPTHIS-4697 23/11/2017 thêm loại in
                    //jAlert(arr);
                    var url = "inketquaxquang_svv?url=" + convertArray(arr) + "&ngaytrakq=" + $("#ngayth_ct").val();
                    //HPG--- Xem truc tiep bao cao
                    if ("${xemtructiep_bc}" == "1")
                    {
                        var redirectWindow = window.open(url, '_blank');
                        redirectWindow.location;
                        return false;
                    } else
                        //-------End
                        $(location).attr('href', url);
                }
            });

            $("#inphieutong").click(function (evt) {
                var mabenhnhan = $("#mabenhnhan").val();
                var maPhongBan = $("#phongban").val();
                var hoten = $("#hoten").val();
                var diachi = $("#diachi").val();
                var tuoi = $("#tuoi").val();
                var phai = $("#gioitinh").val();
                if (phai.toString() == "true") {
                    phai = "Nam";
                } else {
                    phai = "Nữ";
                }
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var khoa = $("#Khoa").val();
                var giuong = $("#giuong").val();
                var buong = $("#buong").val();
                var sothebaohiem = $("#sothebhyt").val();
                var chandoan = $("#chandoan").val();
                var bacsidieutri = $("#bacsichidinh").val();
                var mabacsichidinh = $("#mabacsichidinh").val();
                var bacsichuyenkhoa = $("#bacsichuyenkhoa").val();
                var ngaychidinh = $("#ngaychidinh").val();
                var solan = $("#solan").val();
                //VNPTHIS-4697 23/11/2017
                var typein = $("#loaiin").val();
                //VNPTHIS-4697 23/11/2017
                if (sophieu != "" && macdha != "") {
                    var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu,
                        dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, khoa, giuong, buong, sothebaohiem, chandoan, bacsidieutri, bacsichuyenkhoa, solan, mabacsichidinh, ngaychidinh, "0",
                        sovaovien,
                        sovaovien_noi,
                        sovaovien_dt_noi, 0, maPhongBan,0, typein]; //VNPTHIS-4697 23/11/2017 thêm loại in
                    //jAlert(arr);
                    var url = "inketquaxquangtong_svv?url=" + convertArray(arr);
                    //HPG--- Xem truc tiep bao cao
                    if ("${xemtructiep_bc}" == "1")
                    {
                        var redirectWindow = window.open(url, '_blank');
                        redirectWindow.location;
                        return false;
                    } else
                        //-------End
                        $(location).attr('href', url);
                }
            });
            // KGG
            $("#inphieu_ct").click(function (evt) {
                var mabenhnhan = $("#mabenhnhan").val();
                var hoten = $("#hoten").val();
                var diachi = $("#diachi").val();
                var tuoi = $("#tuoi").val();
                var phai = $("#gioitinh").val() === "true" ? "Nam" : "Nữ";
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var macdha = $("#macdha").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var khoa = $("#Khoa").val();
                var giuong = $("#giuong").val();
                var buong = $("#buong").val();
                var sothebaohiem = $("#sothebhyt").val();
                var chandoan = $("#chandoan").val();
                var bacsidieutri = $("#bacsichidinh").val();
                var mabacsichidinh = $("#mabacsichidinh").val();
                var bacsichuyenkhoa = $("#bacsichuyenkhoa").val();
                var solan = $("#solan").val();
                //VNPTHIS-4697 23/11/2017
                var typein = $("#loaiin").val();
                //VNPTHIS-4697 23/11/2017
                if (sophieu != "" && macdha != "") {
                    var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha,
                        dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, khoa, giuong, buong, sothebaohiem, chandoan, bacsidieutri, bacsichuyenkhoa, solan, mabacsichidinh, "0"
                        ,
                        sovaovien,
                        sovaovien_noi,
                        sovaovien_dt_noi, 0, typein]; //VNPTHIS-4697 23/11/2017 thêm loại in
                    //jAlert(arr);
                    var url = "inketquact_svv?url=" + convertArray(arr);
                    $(location).attr('href', url);
                }
            });
            // AGG in phieu do loang xuong
            $("#inphieudoloangxuong").click(function (evt) {
                var mabenhnhan = $("#mabenhnhan").val();
                var hoten = $("#hoten").val();
                var diachi = $("#diachi").val();
                var tuoi = $("#tuoi").val();
                var phai = $("#gioitinh").val();
                if (phai.toString() == "true") {
                    phai = "Nam";
                } else {
                    phai = "Nữ";
                }
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var macdha = $("#macdha").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var khoa = $("#Khoa").val();
                var giuong = $("#giuong").val();
                var buong = $("#buong").val();
                var sothebaohiem = $("#sothebhyt").val();
                var chandoan = $("#chandoan").val();
                var bacsidieutri = $("#bacsichidinh").val();
                var mabacsichidinh = $("#mabacsichidinh").val();
                var bacsichuyenkhoa = $("#bacsichuyenkhoa").val();
                var solan = $("#solan").val();
                //VNPTHIS-4697 23/11/2017
                var typein = $("#loaiin").val();
                //VNPTHIS-4697 23/11/2017
                if (sophieu != "" && macdha != "") {
                    var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha,
                        dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, khoa, giuong, buong, sothebaohiem, chandoan, bacsidieutri, bacsichuyenkhoa, solan, mabacsichidinh, "0"
                        ,
                        sovaovien,
                        sovaovien_noi,
                        sovaovien_dt_noi, 0, typein]; //VNPTHIS-4697 23/11/2017 thêm loại in
                    //jAlert(arr);
                    var url = "inketquadoloangxuong?url=" + convertArray(arr);
                    $(location).attr('href', url);
                }
            });
            $("#inphieu_ct_trong").click(function (evt) {
                $("#inphieu_ct").click();
            });
//                if ("${Sess_DVTT}".indexOf("91") != 0) {
//                    $("#inphieu_ct").hide();
//                    $("#inphieu_ct_trong").hide();
//                }
            // END KGG
            $("#mauxquang").change(function (evt) {
                var id = $("#mauxquang").val();
                if (id !== "0") {
                    $(".tr_ketqua").css('display','');
                    var url = "select_mauxquang_theoma?ma=" + id + "&dvtt=${Sess_DVTT}";
                    $.ajax({
                        url: url
                    }).done(function (data) {
                        CKEDITOR.instances.ketqua.setData(data[0]["NOIDUNG"]);
                        $('#ket_luan').val(data[0]["KL_MACDINH"]);
                    });
                } else {
                    CKEDITOR.instances.ketqua.setData(null);
                    $('#ket_luan').val(null);
                    $('#loidanbacsi').val(null);
                }
            });
            $("#inphieu").click(function (evt) {
                $("#inphieu_xquang").click();
            });
            $("#dathuchien").change(function (evt) {
                reload_grid();
            });
            $("#dathuchien1").change(function (evt) {

                $("#dathuchien").prop('checked', $("#dathuchien1").prop('checked'));
            });
            // CMU chọn bác sĩ theo khoa
            $("#khoabsttpt").on("change", function (evt) {

                loadKhoabacsidocketqua()
            });
            $("#khoakythuatvien").on("change", function (evt) {
                loadKhoakythuatvien()
            });

            // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT
            $("#phongban").change(function (evt) {
                var url = "layphongbenh_theokhoaxn?khoa=" + $("#phongban").val() + "&dvtt=${Sess_DVTT}";
                $.ajax({
                    url: url
                }).done(function (data) {
                    if (data) {
                        $("#phongbenh").empty();
                        $.each(data, function (i) {
                            $("<option value='" + data[i].MA_PHONG_BENH + "'>" + data[i].TEN_PHONG_BENH + "</option>").appendTo("#phongbenh");
                        });
                    }
                });
                $("#phongbenh").val(-1);
                reload_grid();
            });
            $("#phongbenh").change(function (evt) {
                reload_grid();
            });
            $("#doituong").change(function (evt) {
                reload_grid();
            });
            // End ĐắkLắk
            $("#ngaythuchien").change(function (evt) {
                reload_grid();
            });
            $("#list_thuocdichvu").jqGrid({
                url: 'chitiettoathuocngoatru?matt=' + matoathuoc + "&nghiepvu=ngoaitru_toaxquang&dvtt=${Sess_DVTT}",
                datatype: "local",
                loadonce: true,
                height: 430,
                width: 300,
                colNames: ["stt_toathuoc", "Tên thương mại", "DVT", "Số lượng", "", "", "", "", ""
                ],
                colModel: [
                    {name: 'STT_TOATHUOC', index: 'STT_TOATHUOC', hidden: true},
                    {name: 'TEN_VAT_TU', index: 'TEN_VAT_TU', width: 80},
                    {name: 'DVT', index: 'DVT', width: 60},
                    {name: "SO_LUONG", index: "SO_LUONG", align: 'center', width: 45, edittype: 'custom', editoptions: {custom_element: myelem, custom_value: myvalue}},
                    {name: 'THANHTIEN_THUOC', index: 'THANHTIEN_THUOC', hidden: true},
                    {name: 'MAVATTU', index: 'MAVATTU', hidden: true},
                    {name: 'MAKHOVATTU', index: 'MAKHOVATTU', hidden: true},
                    {name: 'DONGIA_BAN_BV', index: 'DONGIA_BAN_BV', hidden: true},
                    {name: 'DONGIA_BAN_BH', index: 'DONGIA_BAN_BH', hidden: true}
                ],
                //viewrecords: true,
                rowNum: 1000000,
                //multiselect: true,
                caption: "Toa phim X-Quang"
            });
            reload_grid();
            // $('textarea#ketqua').ckeditor();
            $("#tab_cdha").tabs();
            $('#tab_cdha').tabs({
                select: function(event, ui) {
                    // alert('selected: '+ui.index);
                    if (ui.index == 0){
                        gioth_ct_timer_previous_status = gioth_ct_timer_is_on;
                        stopGioThCtTimer();
                        if (giothuchien_cls_timer_previous_status)
                            if (tatAutoTime != 1)
                                showtime_giothuchien_cls();
                    } else if (ui.index == 1) {
                        giothuchien_cls_timer_previous_status = giothuchien_cls_timer_is_on;
                        stopGioThucHienClsTimer();
                        if (gioth_ct_timer_previous_status)
                            if (tatAutoTime != 1)
                                showtime_gioth_ct();
                    }
                }
            });
            // STG
            $("#gioth_ct").click(function(evt) {
                //showtime_gioth_ct_cancel = (showtime_gioth_ct_cancel == 1 ? 0 : 1);
                gioThCtTimerChange();
            });

            $("#giothuchien_cls").click(function(evt) {
                //showtime_giothuchien_cls_cancel = (showtime_giothuchien_cls_cancel == 1 ? 0 : 1);
                gioThucHienClsTimerChange();
            });
            // STG

            $("#viewimageweb").click(function(evt){
                var sophieu = $("#sophieu").val();
                var macdha = $("#macdha").val();
                var arr = [sophieu, macdha];
                if (sophieu!= "") {
                    var url = "ris_viewimage_web?url=" + convertArray(arr);
                    $.ajax({
                        url: url
                    }).done(function (urlweb) {
                        if (urlweb == "ERRLOGIN") {
                            jAlert("Xác thực đăng nhập RIS Connector thất bại, Vui lòng kiểm tra lại thông tin cấu hình kết nối RIS", "Thông báo");
                        }else if(urlweb == "ERROR") {
                            jAlert("Đã xảy ra lỗi", 'Thông báo');
                        }else if(urlweb == "RIS.7"){
                            jAlert("Không thể tìm thấy dữ liệu hình ảnh trên PACS", 'Thông báo');
                        }else if(urlweb == "RIS.6"){
                            jAlert("Không thể tìm thấy ca chụp trên RIS", 'Thông báo');
                        }else if(urlweb == "RIS.4"){
                            jAlert("Lấy đường dẫn xem ảnh không thành công", 'Thông báo');
                        }else if(urlweb == "NOTRECEIVE"){
                            jAlert("RIS chưa nhận ca chụp này", 'Thông báo');
                        }else{
                            var redirectWindow = window.open(urlweb, '_blank');
                            redirectWindow.location;
                            return false;                            }
                    });
                }
            });

            $("#kho_dv").change(function (evt) {
                var url = "noitru_load_tonkhohientai?makhovt=" + $("#kho_dv").val() + "&dvtt=${Sess_DVTT}";
                $("#tenthuongmai_dv").combogrid("option", "url", url);
            });
            $("#tenthuongmai_dv").keypress(function (evt) {
                if ($("#kho_dv").val()==null) {
                    jAlert("Tủ thuốc trống vui lòng kiểm tra thông tin đăng nhập khoa phòng, tủ thuốc", 'Thông báo');
                    return;
                }
                if (evt.keyCode === 13 && flag_noitru !== "-1") {
                    $("#soluong_dv").val(1);
                    $("#soluong_dv").focus();
                }
            });
            $("#soluong_dv").keypress(function (evt) {
                if (evt.keyCode === 13 && flag_noitru !== "-1") {
                    var makhovattu = $("#kho_dv").val();
                    var ma_cdha_ins = ma_cdha;
                    makho = -1;
                    makhokhoaphong = 0;
                    var url_kho = "noitru_kiemtra_tututhuoc";
                    $.post(url_kho, {dvtt: "${Sess_DVTT}", maphongban: "${Sess_PhongBan}", makho: makhovattu})
                        .done(function (data) {
                            var tu_tuthuoc = 1;
                            var mavattu = $("#mavattu_dv").val();
                            var tenthuongmai = $("#tenthuongmai_dv").val();
                            var tengoc = " ";
                            var dvt = $("#dvt_dv").val();
                            var soluong = $("#soluong_dv").val();
                            var sl = (soluong !== "") ? parseInt(soluong) : 0;
                            var dongia_bh = $("#dongia_dv").val();
                            var dongia_bv = dongia_bh;
                            var songay = "1";
                            var sang = "1";
                            var trua = "0";
                            var chieu = "0";
                            var toi = "0";
                            var dangthuoc = " ";
                            //var ma_cdha = $("#ds_cdha").val();
                            var ghichu = $("#sophieu").val() + "-" + ma_cdha_ins;
                            var cachdung = " ";
                            var thanhtien = parseInt(soluong) * parseFloat(dongia_bh);
                            var sophieu = $("#sophieu").val();
                            if (sl <= 0)
                                jAlert("Số lượng không hợp lệ", 'Cảnh báo', function (r) {
                                    $("#soluong_dv").focus();
                                });
                            else {
                                var arr = ["${Sess_DVTT}", matoathuoc, makhovattu, mavattu, tenthuongmai,tengoc, dvt, "noitru_toadichvu", soluong, soluong, dongia_bv, dongia_bh, thanhtien, songay, sang, trua,
                                    chieu, toi, ghichu, "${Sess_UserID}", dangthuoc, stt_dieutri, stt_benhan, stt_dotdieutri, tu_tuthuoc, cobhyt, mabenhnhan, sophieuthanhtoan, "${Sess_PhongBan}", sovaovien_noi, sovaovien_dt_noi];
                                var url = "noitru_toathuoc_insert";
                                if (flag_noitru !== "1") {  //Ngoại trú
                                    var makhambenh = $("#makhambenh").val();
                                    var idtiepnhan = makhambenh.replace("kb_", "");
                                    var sodk = 0;
                                    var arr = [matoathuoc, mavattu, encodeURIComponent(tenthuongmai), encodeURIComponent(tengoc), soluong, dongia_bv, dongia_bh, songay, sang, trua,
                                        chieu, toi, dangthuoc, ghichu, thanhtien, cobhyt, mabenhnhan, sophieuthanhtoan, idtiepnhan, makhambenh, sovaovien, "${Sess_Phong}","0","1", sodk, 0];
                                    var url = "themtoathuocngoaitru_giamtai?dongia_bv=" + parseFloat(dongia_bv) +
                                        "&dongia_bh=" + parseFloat(dongia_bh) + "&thanhtien=" + parseFloat(thanhtien) +
                                        "&sang=" + sang + "&trua=" + trua + "&chieu=" + chieu + "&toi=" + toi +
                                        "&url=" + convertArray(arr) + "&nghiepvu=ngoaitru_toadichvu" +
                                        "&kho=" + makhovattu +"&ngaykb=" + ngay_kb;
                                    if (sophieu){
                                        url += "&sophieu=" + sophieu;
                                    }
                                    if (ma_cdha_ins == "0"){
                                        jAlert("Mã X Quang không được trống", 'Cảnh báo');
//                                        var url = "xuatduoc_giamtai_svv";
//                                        $.post(url, {
//                                                nghiepvu: "ngoaitru_toadichvu",
//                                                matoathuoc: matoathuoc,
//                                                makhambenh: makhambenh,
//                                                xacnhan: "true",
//                                                mabenhnhan: mabenhnhan,
//                                                ngaykhambenh: ngay_kb
//                  xquang_hienthi_chitiet_svv                      }).done(function (data) {
//                                            //
//                                        });
                                    }else{
                                        url += "&ma_cdha=" + ma_cdha_ins;
                                        $.ajax({
                                            url: url,
                                            magoidichvu: ""
                                        }).done(function (data) {
                                            if (data === "4") {
                                                jAlert("Bệnh nhân đã thanh toán rồi", 'Cảnh báo');
                                            } else if (data === "5") {
                                                jAlert("Bệnh nhân đã xuất thuốc rồi", 'Cảnh báo');
                                            } else if (data === "3") {
                                                jAlert("Thuốc đã có trong toa", 'Cảnh báo');
                                            } else if (data === "6") {
                                                jAlert("Số lượng thuốc vượt số lượng tồn kho", 'Cảnh báo');
                                            } else if (data == '100') {
                                                jAlert("Đã chốt báo cáo dược, không thể xóa/sửa", 'Cảnh báo');
                                            } else {
                                                var url = 'chitiettoathuocngoatru_svv?matt=' + matoathuoc + "&nghiepvu=ngoaitru_toadichvu&dvtt=${Sess_DVTT}"
                                                    + "&sovaovien=" + sovaovien + "&ma_=" + ma_cdha_ins + "&sophieu=" + sophieu
                                                    + '&theo_dv=1';
                                                $("#list_thuocdichvu").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
                                                cmu_insertbcfilmxquang(["${Sess_DVTT}",mavattu,tenthuongmai,$("#sophieu").val(),ma_cdha_ins,sovaovien,mabenhnhan,soluong,0,'CMU_INSERTBCFILMXQUANG']);
                                            }
//                                            var url = "xuatduoc_giamtai_svv";
//                                            $.post(url, {
//                                                    nghiepvu: "ngoaitru_toadichvu",
//                                                    matoathuoc: matoathuoc,
//                                                    makhambenh: makhambenh,
//                                                    xacnhan: "true",
//                                                    mabenhnhan: mabenhnhan,
//                                                    ngaykhambenh: ngay_kb
//                                            }).done(function (data) {
//
//                                            });
                                            clear_thuoc_dv();
                                            $("#tenthuongmai_dv").focus();
                                        });
                                    }
                                } else {
                                    url += "?url=" + convertArray(arr);
                                    var sophieu = $("#sophieu").val();
                                    if (sophieu){
                                        url += "&sophieu=" + sophieu;
                                    }
                                    //var ma_cdha_ins = $("#ds_cdha").val();
                                    if (ma_cdha_ins == "0") {
                                        jAlert("Mã X Quang không được trống", 'Cảnh báo');
                                    } else {
                                        url += "&ma_cdha=" + ma_cdha_ins;
                                        $.post(url, {
                                            magoidichvu: ""
                                        }).done(function (data) {
                                            if (data === "4") {
                                                jAlert("Bệnh nhân đã thanh toán rồi", 'Cảnh báo');
                                            } else if (data === "5") {
                                                jAlert("Bệnh nhân đã xuất thuốc rồi", 'Cảnh báo');
                                            } else if (data === "3") {
                                                jAlert("Thuốc đã có trong toa", 'Cảnh báo');
                                            } else if (data === "6") {
                                                jAlert("Số lượng thuốc vượt số lượng tồn kho", 'Cảnh báo');
                                            } else {
                                                load_cttoathuoc("noitru_toadichvu", "list_thuocdichvu");
                                                cmu_insertbcfilmxquang(["${Sess_DVTT}",mavattu,tenthuongmai,$("#sophieu").val(),ma_cdha_ins,sovaovien,mabenhnhan,soluong,0,'CMU_INSERTBCFILMXQUANG']);
                                            }
                                            clear_thuoc_dv();
                                            $("#tenthuongmai_dv").focus();
                                        });
                                    }
                                }
                            }
                        });
                }
            });

            $("#viewimageapp").click(function(evt){
                var sophieu = $("#sophieu").val();
                var macdha = $("#macdha").val();
                var arr = [sophieu, macdha];
                if (sophieu != "") {
                    var url = "ris_viewimage_app?url=" + convertArray(arr);
                    $.ajax({
                        url: url
                    }).done(function (urlapp) {
                        if (urlapp == "ERRLOGIN") {
                            jAlert("Xác thực đăng nhập RIS Connector thất bại, Vui lòng kiểm tra lại thông tin cấu hình kết nối RIS", "Thông báo");
                        }else if(urlapp == "ERROR"){
                            jAlert("Đã có lỗi xảy ra", 'Thông báo');
                        }else if(urlapp == "RIS.7"){
                            jAlert("Không thể tìm thấy dữ liệu hình ảnh trên PACS", 'Thông báo');
                        }else if(urlapp == "RIS.6"){
                            jAlert("Không thể tìm thấy ca chụp trên RIS", 'Thông báo');
                        }else if(urlapp == "RIS.4"){
                            jAlert("Lấy đường dẫn xem ảnh không thành công", 'Thông báo');
                        }else if(urlapp == "NOTRECEIVE"){
                            jAlert("RIS chưa nhận ca chụp này", 'Thông báo');
                        }else{
                            if (!deployJava.isWebStartInstalled("1.7.0")) {
                                if (deployJava.installLatestJRE()) {
                                    if (deployJava.launch(urlapp)) {
                                    }
                                }
                            } else {
                                if (deployJava.launch(urlapp)) {
                                }
                            }
                        }
                    });
                }
            });
            $("#thuocdichvu_div").keyup(function (evt) {
                if (evt.keyCode === 46 && flag_noitru !== "-1") {
                    if (flag_noitru === "1")
                        delete_thuocnoitru("list_thuocdichvu", "noitru_toadichvu");
                    else
                        jConfirm('Bạn có muốn xóa thuốc?', 'Thông báo', function (r) {
                            if (r.toString() === "true") {
                                delete_toathuocngoaitru("list_thuocdichvu", "ngoaitru_toadichvu");
                            }
                        });
                }
            });
            $("#cmu_kyso").click(function() {
                var maPhongBan = $("#phongban").val();
                var mabenhnhan = $("#mabenhnhan").val();
                var hoten = $("#hoten").val();
                var diachi = $("#diachi").val();
                var tuoi = $("#tuoi").val();
                var phai = $("#gioitinh").val() === "true" ? "Nam" : "Nữ";
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var macdha = $("#macdha").val();
                if("${hienthi_checkbox}" == "1"){
                    var macdha = $("#ds_cdha").val();
                    if(macdha == "0"){
                        macdha = ma_cdha;
                    }
                }
                else {
                    var macdha = $("#macdha").val();
                }
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var khoa = $("#Khoa").val();
                var giuong = $("#giuong").val();
                var buong = $("#buong").val();
                var sothebaohiem = $("#sothebhyt").val();
                var chandoan = $("#chandoan").val();
                var bacsidieutri = $("#bacsichidinh").val();
                var mabacsichidinh = $("#mabacsichidinh").val();
                var bacsichuyenkhoa = $("#bacsichuyenkhoa").val();
                var ngaychidinh = $("#ngaychidinh").val();
                var solan = $("#solan").val();
                //VNPTHIS-4697 23/11/2017
                var typein = $("#loaiin").val();
                //VNPTHIS-4697 23/11/2017
                if($("#cbbacsittpt").val() != "${Sess_UserID}") {
                    notifiToClient("Red", "Bạn không có quyền ký số cho phiếu này");
                    return;
                }
                if (sophieu != "" && macdha != "") {
                    var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha,
                        dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, khoa, giuong, buong, sothebaohiem, chandoan, bacsidieutri, bacsichuyenkhoa, solan, mabacsichidinh, ngaychidinh, "0",
                        sovaovien,
                        sovaovien_noi,
                        sovaovien_dt_noi, 0, maPhongBan,0, typein];
                    var url = "inketquaxquang_svv?url=" + convertArray(arr) + "&ngaytrakq=" + $("#ngayth_ct").val();
                    getFilesign769("PHIEUKQ_CDHA_RISPACS", sophieu, -1, ${Sess_DVTT},
                        sovaovien, sovaovien_dt_noi, macdha, function(dataKySo) {
                            if (dataKySo.length > 0) {
                                jAlert("Phiếu đã được ký số")
                            }else{
                                previewAndSignPdfDefaultModal({
                                    url: url,
                                    idButton: 'cdhanoisoi_kyso_action',
                                }, function(){
                                    $("#cdhanoisoi_kyso_action").click(function() {
                                        var idButton = this.id;
                                        showSelfLoading(idButton)
                                        kySoChung({
                                            dvtt: ${Sess_DVTT},
                                            userId: ${Sess_UserID},
                                            url: url,
                                            loaiGiay: "PHIEUKQ_CDHA_RISPACS",
                                            maBenhNhan: $("#mabenhnhan").val(),
                                            noitru: noitru,
                                            soBenhAn: sttbenhan,
                                            soPhieuDichVu: $("#sophieu").val(),
                                            sttDotDieuTri: sttdotdieutri,
                                            soVaoVien: sovaovien,
                                            soVaoVienDT: sovaovien_dt_noi,
                                            maDichVu: macdha,
                                            keyword: "BÁC SĨ CHUYÊN KHOA",
                                            fileName: "Phiếu chẩn đoán kết quả xquang: " + $("#hoten").val() + " - Mã phiếu: " + $("#sophieu").val(),
                                        }, function(dataKySo) {
                                            hideSelfLoading(idButton)
                                            $("#modalPreviewAndSignPDF").modal("hide");
                                        });
                                    });
                                });
                            }
                        });

                } else {
                    jAlert("Vui lòng chọn dịch vụ ký số")
                }

            })
            $("#cmu_huykyso").click(function() {
                if($("#cbbacsittpt").val() != "${Sess_UserID}") {
                    notifiToClient("Red", "Bạn không có hủy quyền ký số cho phiếu này");
                    return;
                }
                confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                    huykysoFilesign769("PHIEUKQ_CDHA_RISPACS", $("#sophieu").val(), ${Sess_UserID}, ${Sess_DVTT},
                        sovaovien, sovaovien_dt_noi, $("#macdha").val(), function(data) {
                            if(data && data.ERROR) {
                                notifiToClient("Red", "Hủy ký số thất bại");
                            } else {
                                notifiToClient("Green", "Ký số thành công");
                            }
                        })
                }, function () {

                })
            })
            $("#cmu_inkyso").click(function() {
                getFilesign769(
                    "PHIEUKQ_CDHA_RISPACS",
                    $("#sophieu").val(),
                    -1,
                    "${Sess_DVTT}",
                    sovaovien,
                    sovaovien_dt_noi,
                    $("#macdha").val(),
                    function(dataKySo) {
                        if(dataKySo.length == 0) {
                            notifiToClient("Red", "Chưa ký số");
                            return;
                        }
                        if(dataKySo.length > 0) {
                            getCMUFileSigned769(dataKySo[0].KEYMINIO,"pdf")
                        }
                    });
            })
            function load_cttoathuoc(nghiepvu, list) {
                var id = $("#list_benhnhan").jqGrid("getGridParam", "selrow");
                var rowData = $("#list_benhnhan").jqGrid("getRowData", id);
                var url = 'noitru_load_chitiet_film?matt=' + matoathuoc + '&nghiepvu=' + nghiepvu + '&dvtt=${Sess_DVTT}&stt_benhan=' + stt_benhan +
                    '&stt_dotdieutri=' + stt_dotdieutri + '&sovaovien=' + sovaovien_noi + '&sovaovien_dt=' + sovaovien_dt_noi +
                    '&phongban=${Sess_PhongBan}' + '&ma_cdha=' + ma_cdha + '&sophieu=' + rowData.SO_PHIEU;
                $('#' + list).jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
            }
            function clear_thuoc_dv() {
                $("#thuocdichvu_tt_div input[type='text']").val("");
                $("#thuocdichvu_tt_div input[type='hidden']").val("");
            }
            reload_grid();
            $("#tab_cdha").tabs();
            $("#tab_cdha").tabs({
                select: function(event, ui) {
                    if(ui.index == 0) {
                        var xquangcount = $("#list_xquang_bhyt").getGridParam("reccount");
                        if(xquangcount == 1)
                            reload_grid();
                        else
                            reload_xquang_grid();
                    }
                }
            });

            $("#khoabsttpt").val(${Sess_PhongBan}).change();
            $.get("cmu_list_KB_NOI_khoaphong_GET_khoduoc?url="+ convertArray(["${Sess_DVTT}", "${Sess_PhongBan}", "noitru_toadichvu"])).done(function(data){
                $("#kho_dv").html("")
                data.forEach(function(obj, index) {
                    $("#kho_dv").append("<option value="+obj.MAKHO+">"+obj.TENKHO+"</option>")
                    if(index == 0) {
                        $("#kho_dv").val(obj.MAKHO)
                    }

                })
                $("#tenthuongmai_dv").combogrid({
                    url: "noitru_load_tonkhohientai?makhovt=" + $("#kho_dv").val() + "&dvtt=${Sess_DVTT}",
                    debug: true,
                    width: "700px",
                    //replaceNull: true,
                    colModel: [
                        {'columnName': 'MAVATTU', 'label': 'mavattu', hidden: true},
                        {'columnName': 'TENVATTU', 'width': '40', 'label': 'Tên vật tư', 'align': 'left'},
                        {'columnName': 'DVT', 'label': 'dvt', hidden: true},
                        {'columnName': 'SOLUONG', 'width': '10', 'label': 'SL', 'align': 'left'}
                    ],
                    select: function (event, ui) {
                        $("#tenthuongmai_dv").val(ui.item.TENVATTU);
                        $("#mavattu_dv").val(ui.item.MAVATTU);
                        $("#dvt_dv").val(ui.item.DVT);
                        $("#dongia_dv").val(ui.item.DONGIA);
                        //$("#dongia_bv").val(ui.item.dongia);
                        return false;
                    }
                });
            })
        });
        function reload_grid_dscd(url, macdha){
            $("#list_xquang_bhyt").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
            $.getJSON(url, function (data) {
                //$("#list_xquang_bhyt").jqGrid('setGridParam', {data: data}).trigger('reloadGrid');
                var listItems = '<option selected="selected" value="0">- Yêu cầu CĐHA -</option>';
                $.each(data,function(i,item){
                    listItems += '<option value='+ item.MA_CDHA+ '>'+ item.TEN_CDHA + '</option>';
                });
                $("#ds_cdha").html(listItems);
                if(macdha){
                    $("#ds_cdha").val(macdha).trigger('change');
                }
            });

        }
        function load_cttoathuoc(nghiepvu, list) {
            var id = $("#list_benhnhan").jqGrid("getGridParam", "selrow");
            var rowData = $("#list_benhnhan").jqGrid("getRowData", id);
            var url = 'noitru_load_chitiet_film?matt=' + matoathuoc + '&nghiepvu=' + nghiepvu + '&dvtt=${Sess_DVTT}&stt_benhan=' + stt_benhan +
                '&stt_dotdieutri=' + stt_dotdieutri + '&sovaovien=' + sovaovien_noi + '&sovaovien_dt=' + sovaovien_dt_noi +
                '&phongban=${Sess_PhongBan}' + '&ma_cdha=' + ma_cdha + '&sophieu=' + rowData.SO_PHIEU;
            $('#' + list).jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
        }
        function reload_grid() {
            var ngay = convertStr_MysqlDate($("#ngaythuchien").val());
            var dvtt = "${Sess_DVTT}";
            //var phong = "${Sess_Phong}";
            var phongban = $("#phongban").val();
            var phongbenh = $("#phongbenh").val();
            var doituong = $("#doituong").val();
            var loaicdha = $("#loai_xquang").val();
            var dathuchien = $("#dathuchien").prop('checked');
            if (dathuchien == true) {
                dath = 1;
                if("${useris}" === "1") {
                    $('#viewimageweb').show();
                    $('#viewimageapp').show();
                }else {
                    $('#viewimageweb').hide();
                    $('#viewimageapp').hide();
                }
            } else {
                dath = 0;
                $('#viewimageweb').hide();
                $('#viewimageapp').hide();
            }
            var arr = [dvtt, ngay, dath, phongban, phongbenh, doituong, loaicdha];
            if ("${timkiem_cls}" == "1"){//------HPG -Tim kiem benh nhan CLS tu ngay den ngay
                var tungay = convertStr_MysqlDate($("#tungay").val());
                arr = [dvtt, tungay, dath, phongban, phongbenh, doituong, loaicdha, ngay,"0"];
            }
            if("${CT}" === "CT"){
                var url = 'ct_ds_benhnhan_cothamso_sdphim?url=' + convertArray(arr);
            }else {
                var url = 'xquang_ds_benhnhan_cothamso_sdphim?url=' + convertArray(arr);
            }
            $("#list_benhnhan").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
            $("#list_xquang_bhyt").jqGrid('clearGridData');
        }
        function reload_xquang_grid(){
            var i_dath = $("#dathuchien").prop('checked') ? 1 : 0;
            var arrct = [flag_noitru, sophieu, stt_benhan, stt_dotdieutri, stt_dieutri, "${Sess_DVTT}", sovaovien, sovaovien_noi, sovaovien_dt_noi, i_dath];
            var urlct = "xquang_hienthi_chitiet_svv?url=" + convertArray(arrct);
            $("#list_xquang_bhyt").jqGrid('setGridParam', {datatype: 'json', url: urlct}).trigger('reloadGrid');
        }
        function hienthi_them_cls(url) {
            $.ajax({
                url: url
            }).done(function (data) {
                if (data.length > 0) {
                    $('#trieuchungls').val(data[0].TRIEUCHUNGLS);
                    if("${Sess_DVTT}" !== '96170') {
                        $('#benhtheobs').val(data[0].TEN_BENH_THEOBS);
                    }

                    // STG
                    if(data[0].NGAY_TRA_KETQUA == null) {
                        $("#ngaythuchien_cls").val("${ngayhientai}");
                    } else {
                        $("#ngaythuchien_cls").val(data[0].NGAY_TRA_KETQUA);
                        $("#giothuchien_cls").val(data[0].GIO_TRA_KETQUA);
                    }

                    /*if(data[0].GIO_TRA_KETQUA == null) {
                        showtime_giothuchien_cls_cancel = 0;
                    } else {
                        showtime_giothuchien_cls_cancel = 1;
                        $("#giothuchien_cls").val(data[0].GIO_TRA_KETQUA);
                    }*/
                    if(data[0].GIO_TRA_KETQUA == null || !$("#dathuchien").prop('checked')) {
                        if (!giothuchien_cls_timer_is_on)
                            if(tatAutoTime == 1) {
                                var ngayHienTai = new Date();
                                var gioHienTai = addZero(ngayHienTai.getHours());
                                var phutHienTai = addZero(ngayHienTai.getMinutes());
                                var giayHienTai = addZero(ngayHienTai.getSeconds());
                                $('#giothuchien_cls').val(gioHienTai + ":" + phutHienTai + ":" + giayHienTai);
                            } else
                                showtime_giothuchien_cls();
                    } else {
                        stopGioThucHienClsTimer();
                        //$("#giothuchien_cls").val(data[0].GIO_TRA_KETQUA);
                        $("#giothuchien_cls").val(data[0].GIO_TRA_KETQUA);
                    }
                    // STG
                    if (data[0].TT_THANHTOAN == "0")
                        $('#tt_thanhtoan').val("Chưa thanh toán");
                    else
                        $('#tt_thanhtoan').val("Đã thanh toán");
                } else {
                    $('#trieuchungls').val("");
                    $('#benhtheobs').val("");
                    $('#ngaythuchien_cls').val("");
                    $('#tt_thanhtoan').val("");
                }
            });
        }
        //CMU: 26062017
        function load_lscdha_bn(mabenhnhan) {
            var url = "cmu_danhsach_lichsu_cdha?mabenhnhan=" + mabenhnhan + "&type=XQ";
            $("#list_lichsuCDHA").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
        }
        function loadThongTinBenhNhan(ret){
            $("#ma_maycdha_md").val("");
            $("#mabenhnhan").val(ret.MABENHNHAN);
            $("#hoten").val(ret.TENBENHNHAN);
            $("#tuoi").val(ret.TUOI);
            $("#gioitinh").val(ret.GIOITINH.toString());
            $("#diachi").val(ret.DIACHI);
            $("#sothebhyt").val(ret.SOTHEBHYT);
            $("#sophieu").val(ret.SO_PHIEU);
            $("#makhambenh").val(ret.MA_KHAM_BENH);
            $("#noitru").val(ret.NOITRU);
            $("#sttbenhan").val(ret.STT_BENHAN);
            $("#sttdotdieutri").val(ret.STT_DOTDIEUTRI);
            $("#sttdieutri").val(ret.STT_DIEUTRI);
            $("#mabacsichidinh").val(ret.NGUOI_CHI_DINH);
            $("#ngaysinh_ct").val(ret.NAMSINH);
            var bacsithuchien = ret.BACSI_THUCHIEN === '' ? "${hotenbacsi}" : ret.BACSI_THUCHIEN;
            $("#bacsichuyenkhoa").val(bacsithuchien);
            $("#ma_maycdha_md").val("");
            $("#macdha").val("");
            $("#sophieu").val(ret.SO_PHIEU);
            sovaovien = ret.SOVAOVIEN;
            sovaovien_noi = ret.SOVAOVIEN_NOI;
            mabenhnhan = ret.MABENHNHAN;
            sovaovien_dt_noi = ret.SOVAOVIEN_DT_NOI;
            stt_dieutri = ret.STT_DIEUTRI;
            stt_benhan = ret.STT_BENHAN;
            stt_dotdieutri = ret.STT_DOTDIEUTRI;
            sophieuthanhtoan=ret.SOPHIEUTHANHTOAN;
            flag_noitru=ret.NOITRU;
            co_bao_hiem = ret.CO_BHYT;
            var dathuchien = $("#dathuchien").prop('checked');
            var i_dath = dathuchien === true ? 1 : 0;
            sobenhan=ret.SOBENHAN;
            sobenhan_tt=ret.SOBENHAN_TT;
            icd_benhnhan=ret.ICD;
            ten_icd_benhnhan=$("#chandoan").val();
            sophieu_ngoaitru = ret.SOPHIEU_NGOAITRU;
            laydl_tungoaitru = ret.LAYDL_TU_NGT;
            var arr = [ret.NOITRU, ret.SO_PHIEU, ret.STT_BENHAN, ret.STT_DOTDIEUTRI, ret.STT_DIEUTRI, "${Sess_DVTT}", sovaovien, sovaovien_noi, sovaovien_dt_noi, i_dath];
            if("${CT}" === "CT"){
                var url = "ct_hienthi_chitiet_svv?url=" + convertArray(arr);
            }
            else {
                var url = "xquang_hienthi_chitiet_svv?url=" + convertArray(arr);
            }
            reload_grid_dscd(url, ret.MA_CDHA);
            cobhyt = ret.TI_LE_MIEN_GIAM.replace('.00', '');
            ngay_kb = ret.NGAY_KB;
            if (ret.DA_THANH_TOAN == "0")
                $('#tt_thanhtoan').val("Chưa thanh toán");
            else
                $('#tt_thanhtoan').val("Đã thanh toán");
            $("#Khoa").val(ret.KHOA);
            $("#buong").val(ret.BUONG);
            $("#giuong").val(ret.GIUONG);
            $("#chandoan").val(ret.CHUANDOANICD);
            if("${Sess_DVTT}" == '96170') {
                $("#benhtheobs").val(ret.CHUANDOANSOBO);
            }
            $("#solan").val(ret.STT_HANGNGAY);
            $("#ngaychidinh").val(ret.NGAY_CHI_DINH);
            var url_bs = "select_tenbacsi?mabacsi=" + ret.NGUOI_CHI_DINH + "&dvtt= " + "${Sess_DVTT}";
            $.ajax({
                url: url_bs
            }).done(function (data) {
                $("#bacsichidinh").val(data);
                $("#cmu_bacsichidinh").val(data);
            });
            $("#hoten_ct").val(ret.TENBENHNHAN);
            $("#tuoi_ct").val(ret.TUOI);
            $("#gioitinh_ct").val(ret.GIOITINH.toString() === "true" ? "Nam" : "Nữ");
            $("#mabenhnhan_ct").val(ret.MABENHNHAN);
            $("#tenkhoa_ct").val(ret.KHOA);
            $("#sothebhyt_ct").val(ret.SOTHEBHYT);
            if (cobhyt.trim() === '')
                cobhyt = '0';
            matoathuoc=flag_noitru === '1'?(ret.SO_PHIEU.split('.')[2]).split('_')[0]:ret.MA_KHAM_BENH.replace("kb_", "tt_");
            if ("${hienthi_them_cls}" === "1") {
                var hpg_STT_BENHAN = "0";
                var hpg_STT_DIEUTRI = "0";
                var hpg_STT_DOTDIEUTRI = "0";
                if (ret.STT_DIEUTRI !== "")
                    hpg_STT_BENHAN = ret.STT_BENHAN;
                if (ret.STT_DIEUTRI !== "")
                    hpg_STT_DOTDIEUTRI = ret.STT_DOTDIEUTRI;
                if (ret.STT_DIEUTRI !== "")
                    hpg_STT_DIEUTRI = ret.STT_DIEUTRI;
                var arr1 = [ret.MA_KHAM_BENH, ret.SO_PHIEU, "${Sess_DVTT}", ret.NOITRU, hpg_STT_BENHAN, hpg_STT_DOTDIEUTRI, hpg_STT_DIEUTRI, 3]
                var url1 = "hpg_thongtin_mo_rong_bn_cls?url=" + convertArray(arr1);
                hienthi_them_cls(url1);
            }
            if("${Sess_DVTT}" == '96004') {
                $("#khoabsttpt").val(9600413).trigger("change")
            }
            $.post('cmu_post_CMU_TONGTIEN_MONEY_NOIDUNG', {
                url: ["${Sess_DVTT}",  sovaovien == 0? sovaovien_noi: sovaovien, sovaovien_dt_noi? sovaovien_dt_noi: 0, ret.SO_PHIEU].join('```')
            }).done(function (data) {
                if(data == 0) {
                    $("#vnptmoney").hide();
                } else {
                    $("#vnptmoney").show();
                    $("#vnptmoney").html("Đã thanh toán qua VNPT-MONEY: "+ data);
                }
            })
            $.post('cmu_post_CMU_TONGTIEN_BANK_NOIDUNG', {
                url: ["${Sess_DVTT}",
                    sovaovien == 0? sovaovien_noi: sovaovien, sovaovien_dt_noi? sovaovien_dt_noi: 0, ret.SO_PHIEU, 'BIDV'].join('```')
            }).done(function (data) {
                if(data == 0) {
                    $("#bidv").hide();
                } else {
                    $("#bidv").show();
                    $("#bidv").html("Đã thanh toán qua BIDV: "+ new Intl.NumberFormat().format(data));
                }
            })
            $.post('cmu_post_CMU_TONGTIEN_BANK_NOIDUNG', {
                url: ["${Sess_DVTT}",
                    sovaovien == 0? sovaovien_noi: sovaovien, sovaovien_dt_noi? sovaovien_dt_noi: 0, ret.SO_PHIEU, 'VIETINBANK'].join('```')
            }).done(function (data) {
                if(data == 0) {
                    $("#vietinbank").hide();
                } else {
                    $("#vietinbank").show();
                    $("#vietinbank").html("Đã thanh toán qua VIETINBANK: "+ new Intl.NumberFormat().format(data));
                }
            })
        }
        function loadThongTinKetQua(ret){
            $("#ma_maycdha_md").val(ret.STT_MAYCDHA);
            $("#macdha").val(ret.MA_CDHA);
            //$("#mauxquang").val('0');
            ma_cdha = $("#macdha").val();
            $("#nguoithuchien").val(ret.NGUOI_THUC_HIEN);
            $("#frm_kq_id_ekip").val(ret.ID_EKIP);
            $("#frm_kq_so_phieu_dichvu").val($("#sophieu").val());
            $("#frm_kq_ma_dv").val(ret.MA_CDHA);
            $("#frm_kq_mabenhnhan").val($("#mabenhnhan").val());
            $("#frm_kq_sovaovien").val(sovaovien==0?sovaovien_noi:sovaovien);
            $("#frm_kq_sovaovien_dt").val(sovaovien_dt_noi);
            $("#frm_kq_noitru").val($("#noitru").val());
            $("#frm_kq_id_dieutri").val(ret.ID_DIEUTRI);
            $("#thoigianchidinh").val($("#cmu_ngaychidinh").val());
            sophieu = $("#sophieu").val();
            var makhambenh = $("#makhambenh").val();
            var noitru = $("#noitru").val();
            var sttbenhan = $("#sttbenhan").val();
            var sttdotdieutri = $("#sttdotdieutri").val();
            var sttdieutri = $("#sttdieutri").val();
            var idBN = $("#list_benhnhan").jqGrid("getGridParam", "selrow");
            var rowData = $("#list_benhnhan").jqGrid("getRowData", idBN);
            if ("${Sess_DVTT}" == '96004' || "${Sess_DVTT}" == '96014') {
                if(flag_noitru === '1' && laydl_tungoaitru == 0){
                    load_cttoathuoc("noitru_toadichvu", "list_thuocdichvu");
                } else if(flag_noitru === '1' && laydl_tungoaitru == 1 && ("${Sess_DVTT}" == '96004' || "${Sess_DVTT}" == '96014')) {
                    var url_nt = "cmu_getlist?url=" + convertArray(["${Sess_DVTT}", mabenhnhan, 'ngoaitru_toadichvu', sophieu_ngoaitru, "CMU_SELECT_FILM_NGOAITRU"]);
                    $("#list_thuocdichvu").jqGrid('setGridParam', {
                        datatype: 'json',
                        url: url_nt
                    }).trigger('reloadGrid')
                }
                else{
                    var url = 'chitiettoathuocngoatru_svv?matt=' + matoathuoc + "&nghiepvu=ngoaitru_toadichvu&dvtt=${Sess_DVTT}" + "&sovaovien=" + sovaovien + "&ma=" + ma_cdha + "&sophieu=" +  rowData.SO_PHIEU;
                    $("#list_thuocdichvu").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
                }
            } else {
                if(flag_noitru === '1'){
                    load_cttoathuoc("noitru_toadichvu", "list_thuocdichvu");
                }
                else{
                    var url = 'chitiettoathuocngoatru_svv?matt=' + matoathuoc + "&nghiepvu=ngoaitru_toadichvu&dvtt=${Sess_DVTT}" + "&sovaovien=" + sovaovien + "&ma=" + ma_cdha + "&sophieu=" + rowData.SO_PHIEU;
                    $("#list_thuocdichvu").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
                }
            }

            $("#cbbacsittpt").html("")
            $("#cmu_kythuatvien").html("")
            console.log("ret", ret)
            if(ret.KYTHUATVIEN != null && ret.KYTHUATVIEN != "") {
                $("#cmu_kythuatvien").append("<option value="+ret.KYTHUATVIEN+">"+ret.KTVTHUCHIEN+"</option>")
                $("#cmu_kythuatvien").val(ret.KYTHUATVIEN)
            } else {
                $("#cmu_kythuatvien").append("<option value=${Sess_UserID}>${Sess_User}</option>")
                $("#cmu_kythuatvien").val("${Sess_UserID}")
            }
            if(ret.NGUOI_THUC_HIEN != 0) {
                $("#cbbacsittpt").append("<option value="+ret.NGUOI_THUC_HIEN+">"+ret.BACSIDOCKQ+"</option>")
                $("#cbbacsittpt").val(ret.NGUOI_THUC_HIEN)
            } else {

                loadKhoabacsidocketqua();
            }
            var arr = [sophieu, "${Sess_DVTT}", ret.MA_CDHA, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0", sovaovien, sovaovien_noi, sovaovien_dt_noi];
            var url = "xquang_select_ketqua_svv?url=" + convertArray(arr);
            $.getJSON(url, function (result) {
                //Reset
                if(result.length===0){
                    $("#bacsichidinh").val('');
                    $("#cmu_bacsichidinh").val("");
                    $("#bacsichuyenkhoa").val('');
                    $("#loidanbacsi").val('');
                    CKEDITOR.instances.ketqua.setData('');
                    $("#ket_luan").val('');
                    $("#cophim13x18").val(result.SL_COPHIM_1318);
                    $("#cophim18x20").val(result.SL_COPHIM_1820);
                    $("#cophim24x30").val(result.SL_COPHIM_2430);
                    $("#cophim30x40").val(result.SL_COPHIM_3040);
                    $("#solanchup").val(result.SOLANCHUP);
                    $("#ngayth_ct").val($("#ngaythuchien_cls").val());
                    // $("#gioth_ct").val($("#giothuchien_cls").val());
                    var thoigian = new Date();
                    $('#gioth_ct').val(addZero(thoigian.getHours()) + ":" + addZero(thoigian.getMinutes()) + ":" + addZero(thoigian.getSeconds()));
                }else{
                    //Set Result
                    $.each(result, function (i, field) {
                        $("#bacsichidinh").val(field.BACSI_CHIDINH);
                        $("#cmu_bacsichidinh").val(field.BACSO_CHIDINH);
                        $("#bacsichuyenkhoa").val(field.BACSI_THUCHIEN);
                        $("#loidanbacsi").val(field.LOIDANBACSI);
                        CKEDITOR.instances.ketqua.setData(field.KET_QUA);
                        $("#ket_luan").val(field.MO_TA);
                        $("#cophim13x18").val(field.SL_COPHIM_1318);
                        $("#cophim18x20").val(field.SL_COPHIM_1820);
                        $("#cophim24x30").val(field.SL_COPHIM_2430);
                        $("#cophim30x40").val(field.SL_COPHIM_3040);
                        $("#solanchup").val(field.SOLANCHUP);
                        $("#ngaybatdauthuchien").val(field.NGAY_TH_YL);
                        if(!field.NGAY_TH_YL) {
                            $("#ngaybatdauthuchien").val(moment().format("DD/MM/YYYY HH:mm:ss"));
                        }
                        $("#ngayth_ct").val(field.NGAYTHUCHIEN||$("#ngaythuchien_cls").val());
                        // $("#gioth_ct").val(field.GIOTHUCHIEN||$("#giothuchien_cls").val());
                        if (field.GIOTHUCHIEN == null || field.GIOTHUCHIEN == ""){
                            var thoigian = new Date();
                            $('#gioth_ct').val(addZero(thoigian.getHours()) + ":" + addZero(thoigian.getMinutes()) + ":" + addZero(thoigian.getSeconds()));
                        }else {
                            $("#gioth_ct").val(field.GIOTHUCHIEN||$("#giothuchien_cls").val())
                        }
                    });
                }

            });
            $("#tab_cdha").tabs("option", "active", 1);
        }
        function reload_grid_dsbn_cd(){
            var dscdParam= {
                P_DVTT: ${Sess_DVTT},
                P_TUNGAY: convertStr_MysqlDate($("#tungay1").val()),
                P_DENNGAY: convertStr_MysqlDate($("#denngay1").val()),
                P_DATH: $('#dathuchien1').prop('checked') == true ? 1 : 0,
                P_LOAI: 'XQ',
                P_LOAI_TEXT:  "${CT}" == 'CT' ? 'CT' : 'X-QUANG'
            }
            var url = 'noisoi_ds_benhnhan_chidinh_bpc?' + $.param(dscdParam) ;
            $.getJSON(url,function(res){
                $("#grid_dschidinh").jqGrid('clearGridData');
                var data = res.filter(function (i,n){
                    return $('#dathuchien1').prop('checked') == false || ( $('#theobacsi').prop('checked') == false || i.BACSI_THUCHIEN =="${hotenbacsi}" );
                });

                $("#grid_dschidinh").jqGrid('setGridParam', { data: data}).trigger('reloadGrid');
            });
        }
        //KGG them gọi số
        function mapUndefinedOrNullTo(value1, value2) {
            if (value1 === undefined || value1 === null) return value2;
            return value1;
        }
        function fetchListDSBN_XQ(mgrid, cur_id, pkID, pkName) {
            var IDS = mgrid.jqGrid("getDataIDs");
            var count = IDS.length;
            // set default value
            var arr_call = [];
            for (var i = 0; i < count; i++) {
                var id = IDS[i];
                var ret = mgrid.jqGrid('getRowData', id);
                var bn_info = {
                    stt_bn_dv: ret.STT_HANGNGAY,
                    stt_bn_pb: ret.STT_HANGNGAY,
                    ma_bn: ret.MABENHNHAN,
                    ten_bn: ret.TENBENHNHAN,
                    uu_tien: mapUndefinedOrNullTo(ret.UU_TIEN, 'No') == 'Yes'?1:0,
                    sothebhyt: "BHYT",//ret.SO_THE_BHYT,
                    diachi: "Diachu",//ret.DIA_CHI,
                    ngaysinh: ret.NGAY_SINH.replace('-','/').replace('-','/')
                };
                arr_call.push(bn_info);
            }
            //
            var curstt = mgrid.jqGrid('getRowData', cur_id).STT_HANGNGAY;
            var info = {
                pk_id: pkID,
                pk_name: pkName,
                cur_id: curstt,
                dsbn: arr_call
            };
            //
            return JSON.stringify(info);
        }
        function saveTextAsFileL(textToWrite) {
            // grab the content of the form field and place it into a variable
            //  create a new Blob (html5 magic) that conatins the data from your form feild
            var textFileAsBlob = new Blob([textToWrite], {type:'text/plain'});
            // Specify the name of the file to be saved
            var fileNameToSaveAs = "WEB_HIENTHISO_LIST.lgs";

            // Optionally allow the user to choose a file name by providing
            // an imput field in the HTML and using the collected data here
            // var fileNameToSaveAs = txtFileName.text;

            // create a link for our script to 'click'
            var downloadLink = document.createElement("a");
            //  supply the name of the file (from the var above).
            // you could create the name here but using a var
            // allows more flexability later.
            downloadLink.download = fileNameToSaveAs;
            // provide text for the link. This will be hidden so you
            // can actually use anything you want.
            downloadLink.innerHTML = "download";

            // allow our code to work in webkit & Gecko based browsers
            // without the need for a if / else block.
            window.URL = window.URL || window.webkitURL;

            // Create the link Object.
            downloadLink.href = window.URL.createObjectURL(textFileAsBlob);
            // when link is clicked call a function to remove it from
            // the DOM in case user wants to save a second file.
            downloadLink.onclick = m_destroyClickedElement;
            // make sure the link is hidden.
            downloadLink.style.display = "none";
            // add the link to the DOM
            document.body.appendChild(downloadLink);

            // click the new link
            downloadLink.click();
        }
        function m_destroyClickedElement(event){
            // remove the link from the DOM
            document.body.removeChild(event.target);
        }
        //KGG End
        function doSignPlugin(url, typeSign) {
            var x = new XMLHttpRequest();
            x.onload = function() {
                // Create a form
                var reader = new FileReader();
                reader.readAsDataURL(x.response);
                reader.onloadend = function() {

                    var base64data = reader.result.replace("data:application/pdf;base64,", "");
                    console.log("base64data", base64data)
                    var sigOptions = null;
                    sigOptions = new PdfSigner();
                    sigOptions.AdvancedCustom = true;
                    SignAdvanced(base64data, 'pdf', sigOptions, typeSign);
                }

            };
            x.responseType = 'blob';    // <-- This is necessary!
            x.open('GET', url, true);
            x.send();
        }
        function kyketquaxquangtoken() {
            var maPhongBan = $("#phongban").val();
            var mabenhnhan = $("#mabenhnhan").val();
            var hoten = $("#hoten").val();
            var diachi = $("#diachi").val();
            var tuoi = $("#tuoi").val();
            var phai = $("#gioitinh").val() === "true" ? "Nam" : "Nữ";
            var sophieu = $("#sophieu").val();
            var makhambenh = $("#makhambenh").val();
            var macdha = $("#macdha").val();
            if("${hienthi_checkbox}" == "1"){
                var macdha = $("#ds_cdha").val();
                if(macdha == "0"){
                    macdha = ma_cdha;
                }
            }
            else {
                var macdha = $("#macdha").val();
            }
            dvtt = "${Sess_DVTT}";
            Sess_UserID = "${Sess_UserID}";
            var noitru = $("#noitru").val();
            var sttbenhan = $("#sttbenhan").val();
            var sttdotdieutri = $("#sttdotdieutri").val();
            var sttdieutri = $("#sttdieutri").val();
            var khoa = $("#Khoa").val();
            var giuong = $("#giuong").val();
            var buong = $("#buong").val();
            var sothebaohiem = $("#sothebhyt").val();
            var chandoan = $("#chandoan").val();
            var bacsidieutri = $("#bacsichidinh").val();
            var mabacsichidinh = $("#mabacsichidinh").val();
            var bacsichuyenkhoa = $("#bacsichuyenkhoa").val();
            var ngaychidinh = $("#ngaychidinh").val();
            var solan = $("#solan").val();
            CMU_SOPHIEU_CDHA = sophieu;
            //VNPTHIS-4697 23/11/2017
            var typein = $("#loaiin").val();
            //VNPTHIS-4697 23/11/2017
            if (sophieu != "" && macdha != "") {
                var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha,
                    dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, khoa, giuong, buong, sothebaohiem, chandoan, bacsidieutri, bacsichuyenkhoa, solan, mabacsichidinh, ngaychidinh, "0",
                    sovaovien,
                    sovaovien_noi,
                    sovaovien_dt_noi, 0, maPhongBan,0, typein]; //VNPTHIS-4697 23/11/2017 thêm loại in
                //jAlert(arr);
                var url = "inketquaxquang_svv?url=" + convertArray(arr) + "&ngaytrakq=" + $("#ngayth_ct").val();
                doSignPlugin(url);
            }
        }

        function SignAdvanced(data, type, sigOption, typeSign)
        {
            var dataJS = {};

            var arrData = [];
            // 1
            dataJS.data = data;
            dataJS.type = type;
            dataJS.sigOptions = JSON.stringify(sigOption);

            var jsData = "";
            jsData += JSON.stringify(dataJS);
            //
            arrData.push(jsData);
            var serial = "";
            vnpt_plugin.signArrDataAdvanced(arrData, serial, true, showMessageCDHAKQ);

        }
    </script>
</head>
<body onload="gioThucHienClsTimerChange();">
<div id="panel_all">
    <%@include file="../../../resources/Theme/include_pages/menu.jsp"%>
    <jsp:include page="../camau/jsp/loader.jsp"/>
    <div id="panelwrap">
        <div class="center_content">
            <div id="tab_cdha">
                <ul>
                    <li><a href="#cdha_tabs_1" id="xn_cobhyt">Thông tin bệnh nhân</a></li>
                    <li  hidden="true"><a href="#cdha_tabs_2" id="xn_bnyc">Kết quả</a></li>
                </ul>
                <div id="cdha_tabs_1">
                    <form id="form1" name="form1" method="post" action="">
                        <table width="100%">
                            <tr>
                                <td width="302" valign="top">
                                    <table width="302">
                                        <tr class="hpg_tmp" >
                                            <td width="62px" > <span style =" display:inline-block;">Từ Ngày </span> </td>
                                            <td > <input type="text" name="tungay" id="tungay" />    </td>
                                        </tr>
                                        <tr>
                                            <td width="62px" >
                                                        <span style=" display:inline-block;">
                                                            <span class="hpg_tmp">Đến </span>Ngày
                                                        </span>

                                            </td>
                                            <td>
                                                <input  type="text" name="ngaythuchien" id="ngaythuchien" />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Loại</td>
                                            <td>
                                                <select id="loai_xquang" name="loai_xquang">
                                                    <option value="-1"> -- Tất cả -- </option>
                                                    <option value="CITI"> CT </option>
                                                    <option value="XQUANG">XQuang</option>
                                                </select>
                                            </td>
                                        </tr>
                                        <!-- ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT -->
                                        <tr class="dlk_tmp">
                                            <td>Khoa</td>
                                            <td><select name="phongban" id="phongban" class="width100">
                                                <c:forEach var="i" items="${phongban}">
                                                    <option value="${i.MA_PHONGBAN}">${i.TEN_PHONGBAN}</option>
                                                </c:forEach>
                                            </select>
                                            </td>
                                        </tr>
                                        <tr class="dlk_tmp">
                                            <td>Phòng</td>
                                            <td><select name="phongbenh" id="phongbenh" class="width100">
                                                <c:forEach var="i" items="${phongbenh}">
                                                    <option value="${i.MA_PHONG_BENH}">${i.TEN_PHONG_BENH}</option>
                                                </c:forEach>
                                            </select></td>
                                        </tr>
                                        <tr class="dlk_tmp">
                                            <td>Đối tượng</td>
                                            <td><select name="doituong" id="doituong" class="width100">
                                                <option value="-1">--Tất cả--</option>
                                                <option value="1">Có BHYT</option>
                                                <option value="0">Không BHYT</option>
                                            </select></td>
                                        </tr>
                                        <!-- End ĐắkLắk -->
                                        <tr>
                                            <td colspan="2">
                                                <label><input type="checkbox" name="dathuchien" id="dathuchien">
                                                    <b>Đã thực hiện</b></label>

                                                <input type="button" name="lammoi" id="lammoi" value="Làm mới" class="button_shadow" style="float:right"/>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="2"><table id="list_benhnhan"></table>
                                                <div id="pager2"></div></td>
                                        </tr>
                                        <tr id="ghichutrangthai">
                                            <td colspan="2" style="padding-top:10px">
                                                <!--CMU: 27/10/2017-->
                                                <label style="color:red;font-weight: normal;">BN cấp cứu</label>
                                                <label style="color:#00ff00;margin-left:20px;font-weight: normal;">BN < 6 tuổi</label><br>
                                                <label style="color:#bf00ff;font-weight: normal;">Bệnh nhân VP, chưa đóng tiền</label><br>
                                                <label style="color:#EE7600;font-weight: normal;">Bệnh nhân VP, đã đóng tiền</label>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                                <td width="676" valign="top">
                                    <div>
                                        <fieldset>
                                            <legend>Thông tin bệnh nhân</legend>
                                            <table width="100%">
                                                <tr>
                                                    <td width="127">Họ tên
                                                        <input name="macdha" type="hidden" id="macdha" />
                                                        <input name="sophieu" type="hidden" id="sophieu" />
                                                        <input name="makhambenh" type="hidden" id="makhambenh" />
                                                        <input name="noitru" type="hidden" id="noitru" />
                                                        <input name="solan" type="hidden" id="solan" />
                                                        <input name="ngaychidinh" type="hidden" id="ngaychidinh" />
                                                        <input name="bacsichidinh" type="hidden" id="bacsichidinh" />
                                                        <input name="mabacsichidinh" type="hidden" id="mabacsichidinh" />
                                                        <input name="bacsichuyenkhoa" type="hidden" id="bacsichuyenkhoa" /></td>
                                                    <td width="756"><input name="hoten" type="text" disabled="disabled" class="width1" id="hoten" style=""/>
                                                        Tuổi
                                                        <input name="tuoi" type="text" disabled="disabled" class="width3" id="tuoi" style="width:50px" />
                                                        <select name="gioitinh" id="gioitinh" disabled="disabled">
                                                            <option value="true">Nam</option>
                                                            <option value="false">Nữ</option>
                                                        </select>
                                                        Mã y tế
                                                        <input name="mabenhnhan" type="text" disabled="disabled" class="width3" id="mabenhnhan" style="width:100px" />
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td width="127">Khoa
                                                    </td>
                                                    <td width="745"><input name="Khoa" type="text" disabled="disabled" class="width1" id="Khoa" style="width:188px;";/>
                                                        Buồng
                                                        <input name="buong" type="text" disabled="disabled" class="width3" id="buong" />
                                                    </td>
                                                </tr>
                                                <tr style="display: none">
                                                    <td>Giường</td>
                                                    <td><input name="giuong" type="text" disabled="disabled" class="width100" id="giuong" /></td>
                                                </tr>
                                                <tr>
                                                    <td>Địa chỉ</td>
                                                    <td><input name="diachi" type="text" disabled="disabled" class="width100" id="diachi" /></td>
                                                </tr>

                                                <tr>
                                                    <td>Số thẻ BHYT</td>
                                                    <td><input name="sothebhyt" type="text" disabled="disabled" class="width100" id="sothebhyt" /></td>
                                                </tr>
                                                <tr>
                                                    <td>Chẩn đoán</td>
                                                    <td><input name="chandoan" type="text" disabled="disabled" class="width100" id="chandoan" /></td>
                                                </tr>
                                                <tr class="hpg_hienthithem" >
                                                    <td>Triệu chứng</td>
                                                    <td>
                                                        <input name="trieuchungls" type="text" disabled="disabled" class="width100" id="trieuchungls" />
                                                    </td>
                                                </tr>
                                                <tr class="hpg_hienthithem" >
                                                    <td>Ngày chỉ định</td>
                                                    <td>
                                                        <input name="cmu_ngaychidinh" type="text" disabled="disabled" class="width100" id="cmu_ngaychidinh" />
                                                    </td>
                                                </tr>
                                                <tr class="cmu_bacsichidinh">
                                                    <td>BS chỉ định</td>
                                                    <td>
                                                        <input name="cmu_bacsichidinh" type="text" disabled="disabled" class="width100" id="cmu_bacsichidinh" />
                                                    </td>
                                                </tr>
                                                <tr class="hpg_hienthithem" >
                                                    <td>Bệnh tật</td>
                                                    <td>
                                                        <input name="benhtheobs" type="text" disabled="disabled" class="width100" id="benhtheobs" />
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Chọn máy</td>
                                                    <td>
                                                        <select name="ma_maycdha_md" id="ma_maycdha_md"  style="width: 380px;">
                                                            <option value="" selected>Chọn máy CDHA-TDCN mặc định</option>
                                                            <c:forEach var="e" items="${dsmaycdha}">
                                                                <option value="${e.STT}">${e.TEN_MAY}</option>
                                                            </c:forEach>
                                                        </select>
                                                        <input type="button" name="luu_tt_maycdha" id="luu_tt_maycdha" value="Lưu máy thực hiện" style="width: 150px;" class="button_shadow"/>
                                                    </td>
                                                </tr>
                                                <tr class="hpg_hienthithem" >
                                                    <td>Ngày thực hiện</td>
                                                    <td>
                                                        <input name="ngaythuchien_cls" type="text" disabled="disabled" id="ngaythuchien_cls" size="10" data-inputmask="'alias': 'date'"/>
                                                        <input name="giothuchien_cls" type="text" disabled="disabled" id="giothuchien_cls" size="10"  data-inputmask="'alias': 'hh:mm:ss'"/>
                                                    </td>
                                                </tr>
                                                <tr class="hpg_hienthithem" >
                                                    <td>
                                                        Thanh toán
                                                    </td>
                                                    <td>
                                                        <input name="tt_thanhtoan" type="text" disabled="disabled" class="width100" id="tt_thanhtoan"  />
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2">
                                                        <p id="vnptmoney" style="color:#21409A;;font-weight: bold;"></p>
                                                        <p id="bidv" style="color:#219a5f;;font-weight: bold;margin-top: 10px"></p>
                                                        <p id="vietinbank" style="color:#216b9a;;font-weight: bold;margin-top: 10px"></p>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <td>
                                                        <input name="inphieutong" type="button" class="button_shadow" id="inphieutong" value="In phiếu KQ Tổng" style="width: 150px">
                                                        <input name="lichsuCDHA" type="button" class="button_shadow" id="lichsuCDHA" style="width: 150px; float:right" value="Lịch sử XQ">
                                                    </td>
                                                </tr>
                                                <tr style="display:none">
                                                    <td colspan="2">
                                                        <input type="button" name="luuthongtin" id="luuthongtin" value="Lưu thông tin" class="button_shadow" style="width: 110px"/>
                                                        <input type="button" name="inphieu" id="inphieu" value="In phiếu" class="button_shadow"/>
                                                        <input name="phieu_cdha_xquang" type="button" id="phieu_cdha_xquang" value="In phiếu CĐ CĐHA" class="button_shadow"/>

                                                        <!-- KGG -->
                                                        <input type="button" name="inphieu_ct" id="inphieu_ct" value="In phiếu CT" class="button_shadow" style="width: 120px"/>
                                                        <!-- END KGG -->
                                                        <input name="inphieutong" type="button" class="button_shadow" id="inphieutong" value="In phiếu KQ Tổng" style="width: 150px"/>

                                                        <input name="sttbenhan" type="hidden" id="sttbenhan" />
                                                        <input name="sttdotdieutri" type="hidden" id="sttdotdieutri" />
                                                        <input name="sttdieutri" type="hidden" id="sttdieutri" />
                                                        <input name="nguoithuchien" type="hidden" id="nguoithuchien" />
                                                    </td>
                                                </tr>
                                            </table>
                                        </fieldset>
                                    </div>
                                    <div style="padding-top: 5px"><table id="list_xquang_bhyt"></table></div>
                                </td>
                            </tr>
                        </table>
                    </form>
                    <form id="formdsbn" name="formdsbn" action="">
                        <table width="100%">
                            <tr >
                                <td width="62px" > Từ ngày</td>
                                <td > <input type="text" name="tungay1" id="tungay1" />    </td>
                                <td width="62px" >Đến ngày</td>
                                <td > <input type="text" name="denngay1" id="denngay1" />    </td>
                                <td colspan="2"><label><input type="checkbox" name="dathuchien1" id="dathuchien1">
                                    <b>Đã thực hiện</b></label></td>
                                <td colspan="2"><label><input type="checkbox" name="theobacsi" id="theobacsi" checked>
                                    <b>Theo bác sĩ</b></label></td>
                                <td width="65px"> Tổng số</td>
                                <td width="65px"> <input type="text" disabled name="tongsoca" id="tongsoca" />    </td>
                                <td>
                                    <input type="button" name="lammoi1" id="lammoi1" value="Làm mới" />
                                </td>
                            </tr>
                            <tr>
                                <table id="grid_dschidinh"></table>
                            </tr>
                        </table>
                    </form>
                </div>
                <div id="cdha_tabs_2">
                    <form id="form2" name="form2" method="post" action="">
                        <!-- ĐắkLắk (An Giang yêu cầu) - Ninh 09/12/2016: view thông tin hành chánh của BN lên form nhập kết quả -->
                        <div>
                            <fieldset>
                                <legend>Thông tin hành chính của bệnh nhân</legend>
                                <table width="100%">
                                    <tr>
                                        <td width="10%">Họ tên</td>
                                        <td width="40%"><input name="hoten_ct" type="text" disabled="disabled" id="hoten_ct" style="width: 320px; color: red; font-weight: bold" /></td>
                                        <td>Tuổi
                                            <input name="tuoi_ct" type="text" disabled="disabled" id="tuoi_ct" style="width: 80px" />
                                            Giới tính
                                            <input name="gioitinh_ct" type="text" disabled="disabled" id="gioitinh_ct" style="width: 70px" />
                                            Mã y tế
                                            <input name="mabenhnhan_ct" type="text" disabled="disabled" id="mabenhnhan_ct" style="width: 170px" />
                                        </td>
                                    <tr>
                                        <td>Khoa</td>
                                        <td><input name="tenkhoa_ct" type="text" disabled="disabled" style="width: 320px" id="tenkhoa_ct" /></td>
                                        <td>Số thẻ BHYT
                                            <input name="sothebhyt_ct" type="text" disabled="disabled" style="width: 379px" id="sothebhyt_ct" /></td>
                                    </tr>
                                </table>
                            </fieldset>
                        </div>
                        <!-- End ĐắkLắk -->
                        <div>
                            <fieldset>
                                <legend>Kết quả X Quang</legend>
                                <table width="100%">
                                    <tr>
                                        <td>BS đọc kết quả</td>
                                        <td>
                                            <select name="cbbacsittpt" style="width:50%" id="cbbacsittpt">
                                                <option value=""></option>
                                            </select>
                                            <select name="khoabsttpt" style="width:49%; float:right" id="khoabsttpt">
                                                <c:forEach var="i" items="${phongban}">
                                                    <c:if test="${i.makhoa != '0' &&  i.ma_phongban != '-1'}">
                                                        <option value="${i.ma_phongban}">
                                                                ${i.ten_phongban}
                                                        </option>
                                                    </c:if>
                                                </c:forEach>
                                            </select>
                                        </td>
                                        <td rowspan="8" valign="top">
                                            <div>
                                                <table width="300">
                                                    <tr>
                                                        <td width="70">Chọn kho:
                                                            <input name="matoathuoc_dv" type="hidden" id="matoathuoc_dv"/></td>
                                                        <td>
                                                            <select name="kho_dv" id="kho_dv" style="width:220px">
                                                                <c:forEach var="i" items="${khodichvu}">
                                                                    <option value="${i.MAKHO}">${i.TENKHO}</option>
                                                                </c:forEach>
                                                            </select>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                            <div>
                                                <div class="table_beauty">
                                                    <table width="300" >
                                                        <thead>
                                                        <tr>
                                                            <th width="132">
                                                                Tên thuốc, vật tư</th>
                                                            <th width="51">ĐVT</th>
                                                            <th width="72"><input name="dongia_dv" type="hidden" id="dongia_dv" />
                                                                Số lượng</th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        <tr id="thuocdichvu_tt_div">
                                                            <td ><input name="makhovattu_dv" type="hidden" id="makhovattu_dv" /><input name="mavattu_dv" type="hidden" id="mavattu_dv" />
                                                                <input name="sott_toathuoc_dv" type="hidden" id="sott_toathuoc_dv" />
                                                                <input name="tenthuongmai_dv" type="text" id="tenthuongmai_dv" size="13" class="width_100per"/></td>
                                                            <td><input name="dvt_dv" type="text" id="dvt_dv" size="4" readonly  class="width_100per"/></td>
                                                            <td><input name="soluong_dv" type="text" id="soluong_dv" size="12" onKeyPress="validate_number(event)"  class="width_100per"/></td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                            <div id="thuocdichvu_div">
                                                <table id="list_thuocdichvu"></table>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="11%">Kỹ thuật viên</td>
                                        <td colspan="">
                                            <select name="cmu_kythuatvien" style="width:50%" id="cmu_kythuatvien">
                                            </select>
                                            <select name="khoakythuatvien" style="width:49%; float:right" id="khoakythuatvien">
                                                <c:forEach var="i" items="${phongban}">
                                                    <c:if test="${i.makhoa != '0' &&  i.ma_phongban != '-1'}">
                                                        <option value="${i.ma_phongban}">
                                                                ${i.ten_phongban}
                                                        </option>
                                                    </c:if>
                                                </c:forEach>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="11%">Thời gian chỉ định</td>
                                        <td>
                                            <input name="thoigianchidinh" style="width: 200px" type="text" id="thoigianchidinh" size="10" disabled readonly/>
                                            Thời gian BĐ
                                            <input name="ngaybatdauthuchien"  style="width: 200px" type="text" id="ngaybatdauthuchien" size="10" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="11%">Thời gian TH</td>
                                        <td>
                                            <input name="ngayth_ct" type="text" id="ngayth_ct" size="10" data-inputmask="'alias': 'date'"/>
                                            <input name="gioth_ct" type="text" id="gioth_ct" size="10"  data-inputmask="'alias': 'hh:mm:ss'"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td  width="11%">Tên CDHA</td>
                                        <td width="89%"><input type="text" name="ten_cdha" disabled="disabled" class="width100" id="ten_cdha" ></td>
                                    </tr>
                                    <tr>
                                        <td  width="15%">Mẫu X Quang</td>
                                        <td width="85%"><select name="mauxquang" class="width100 combobox_jqx" id="mauxquang" data-show-subtext="true" data-live-search="true">
                                            <option value="0" selected>-- Chọn mẫu X Quang--</option>
                                            <c:forEach var="i" items="${mauxquang}">
                                                <option value="${i.MA_MAUXQUANG}" > ${i.TEN_MAUXQUANG}</option>
                                            </c:forEach>
                                        </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><b>Kết quả</b></td>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td colspan="2"><textarea name="ketqua" cols="80" rows="10" class="width100" id="ketqua"></textarea>
                                            <script type="text/javascript" language="javascript">
                                                CKEDITOR.replace('ketqua');
                                            </script>
                                        </td>
                                    </tr>
                                    <tr >
                                        <td width="11%">Lời dặn bác sĩ</td>
                                        <td  width="89%"><input name="loidanbacsi" type="text" class="width100" id="loidanbacsi" /></td>
                                    </tr>
                                    <tr >
                                        <td width="11%">Kết luận</td>
                                        <td  width="89%">
                                            <textarea style="height: 80px;" name="ket_luan" rows="2" class="width100" id="ket_luan"></textarea>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td ><input name="luu_tt" type="button" class="button_shadow" id="luu_tt" value="Lưu"/>
                                            <input type="button" name="cmuekip" onclick="opencmuekipt()" id="cmuekip" value="EKIP"
                                                   class="button_shadow"/>
                                            <c:if test="${Sess_DVTT!='82008'}">
                                                <input name="inphieu_xquang" type="button" class="button_shadow" id="inphieu_xquang" value="In phiếu"/>
                                                <!-- KGG -->
                                                <input name="inphieu_ct_trong" type="button" class="button_shadow" id="inphieu_ct_trong" value="In phiếu CT"/>
                                                <input name="inphieudoloangxuong" type="button" class="button_shadow" id="inphieudoloangxuong" value="In phiếu đo Loãng Xương" style="width:195px;"/>
                                            </c:if>
                                            <!-- END KGG -->
                                            <c:if test="${Sess_DVTT=='82008'}">
                                                <input name="ketqua_cdha" type="button" id="ketqua_cdha" value="In kết quả" class="button_shadow"/>
                                            </c:if>
                                            <!--VNPTHIS-4697 23/11/2017 thêm in pdf -->
                                            <select name="loaiin" id="loaiin"><option value="1">RTF</option>
                                                <option selected value="2">PDF</option></select>
                                            <!--VNPTHIS-4697 23/11/2017 thêm in pdf-->
                                            <!--RIS-->
                                            <input style="width: 150px" type="button" id="cmu_kyso" class="button_shadow" value="Ký Số SMARTCA">
                                            <input style="width: 150px" type="button" id="cmu_inkyso" class="button_shadow" value="In Ký Số SMARTCA">
                                            <input style="width: 150px" type="button" id="cmu_huykyso" class="button_shadow" value="Hủy ký Số SMARTCA">
                                            <input type="button" name="viewimageweb" id="viewimageweb" value="Xem ảnh Web" class="button_shadow" style="width: auto"/>
                                            <input type="button" name="viewimageapp" id="viewimageapp" value="Xem ảnh App " class="button_shadow" style="width: auto"/>
                                            <!--END RIS-->
                                        </td>
                                    </tr>
                                </table>
                            </fieldset>
                        </div>
                    </form>
                    <%@include file="./canlamsang/cmuekipxquang.jsp" %>
                </div>
            </div>
        </div> <!--end of center_content-->
    </div>
</div>
<!--CMU:-->
<div id="dialog_lichsuCDHA" title="Lịch sử điện tim" style="display: none">
    <div id="dialog_capnhatketquattpt_ekip" title="Ekip phẫu thuật">
        <form id="form_Ekip" name="form_Ekip" method="post" action="">
            <input id="frm_kq_id_ekip" class="post-data" key="ID_EKIP" hidden />
            <input id="frm_kq_so_phieu_dichvu" class="post-data" key="SO_PHIEU_DICHVU" hidden />
            <input id="frm_kq_ma_dv" class="post-data" key="MA_DV" hidden />
            <input id="frm_kq_mabenhnhan" class="post-data" key="MABENHNHAN" hidden />
            <input id="frm_kq_sovaovien" class="post-data" key="SOVAOVIEN" hidden />
            <input id="frm_kq_sovaovien_dt" class="post-data" key="SOVAOVIEN_DT" hidden />
            <input id="frm_kq_noitru" class="post-data" key="NOITRU" hidden />
            <input id="frm_kq_id_dieutri" class="post-data" key="ID_DIEUTRI" hidden />

            <table style="width:650px">

                <tr>
                    <td width="150">Mã nhân viên<input name="ma_nhanvien" type="text" id="ma_nhanvien" class="post-data" key="MA_NHANVIEN_EKIP" style="color: blue; font-weight:  bold; width: 100px"/>
                    <td width="200">Tên nhân viên<input name="ten_nhanvien" type="text"  id="ten_nhanvien"  style="color: blue; font-weight:  bold"/></td>
                    <td width="200">Vai trò<input name="ma_vaitro" type="hidden"  id="ma_vaitro" class="post-data" key="MA_VAITRO_EKIP" style="color: blue; font-weight:  bold"/>
                        <input name="ten_vaitro" type="text"  id="ten_vaitro" style="color: blue; font-weight:  bold"/></td>
                </tr>
                <tr>
                    <table id="list_chitietvaitrothuchien" style="font-size: 12px"></table>
                </tr>
                <tr>

                    <td align="right">
                        <%--<input type="button" name="ekip_CapNhat" id="ekip_CapNhat" value="Cập nhật" onclick="capNhatEkip(1)" class="button_shadow">--%>
                        <input type="button" name="ekip_Xoa" id="ekip_Xoa" value="Xoá Ekip" onclick="capNhatEkip(0)" class="button_shadow" >
                    </td>


                </tr>
            </table>
        </form>
        <div id="dialog-benhly" title="Danh mục bệnh lý" style="display: none;">
            <table id="list_benhly"></table>
            <div id="pager_benhly"></div>
        </div>
    </div>
    <div id="tab_ls_cdha">
        <ul>
            <li><a href="#cdha_ls_tabs_1" id="xn_cobhyt">Thông tin bệnh nhân</a></li>
            <li style="display:none"><a href="#cdha_ls_tabs_2" id="xn_bnyc">Kết quả</a></li>
        </ul>
        <div id="cdha_ls_tabs_1">
            <table id="list_lichsuCDHA"></table>
        </div>
        <div id="cdha_ls_tabs_2" style="display:none">
            <form id="form2" name="form2" method="post" action="">
                <div>
                    <fieldset>
                        <legend>Kết quả điện tim</legend>
                        <table width="100%">
                            <tr>
                                <td width="11%">Chẩn đoán</td>
                                <td width="89%"><input name="ls_chandoan" type="text" class="width100" id="ls_chandoan" /></td>
                            </tr>
                            <tr>
                                <td>Kết quả</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                        <textarea name="ls_ketqua" cols="80" rows="10" class="width100" id="ls_ketqua">

                                        </textarea>
                                </td>
                            </tr>
                            <tr>
                                <td>Kết luận</td>
                                <td>
                                    <textarea name="ls_ketluan" rows="2" class="width100" id="ketluan"></textarea>
                                </td>
                            </tr>
                            <tr>
                                <td>Lời dặn bác sĩ</td>
                                <td><input name="ls_loidanbacsi" type="text" class="width100" id="loidanbacsi" /></td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>
                                    <input name="ls_inphieu_dientim" type="button" class="button_shadow" id="ls_inphieu_dientim" value="In phiếu"/>
                                    <input name="ls_inphieu_dientim_ngang" type="button" class="button_shadow" id="ls_inphieu_dientim_ngang" style="width: 150" value="In phiếu (ngang)"/>
                                </td>
                            </tr>
                        </table>

                    </fieldset>
                </div>
            </form>
        </div>
    </div>
</body>
</html>