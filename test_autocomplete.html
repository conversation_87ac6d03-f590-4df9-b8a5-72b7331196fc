<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Autocomplete - Lời dặn bác sĩ</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        textarea {
            width: 100%;
            min-height: 100px;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
            box-sizing: border-box;
        }
        textarea:focus {
            border-color: #007bff;
            outline: none;
        }
        .instructions {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }
        .test-buttons {
            margin-top: 20px;
            text-align: center;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
            font-size: 14px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #218838;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🩺 Test Autocomplete - Lời dặn bác sĩ</h1>

        <div class="instructions">
            <h3>📋 Hướng dẫn test:</h3>
            <ol>
                <li><strong>Click vào textarea</strong> "Lời dặn bác sĩ" bên dưới</li>
                <li><strong>Dropdown sẽ xuất hiện</strong> với các gợi ý có sẵn</li>
                <li><strong>Click vào gợi ý</strong> để chọn</li>
                <li><strong>Nhập text mới</strong> và nhấn "Lưu" để thêm gợi ý</li>
                <li><strong>Refresh trang</strong> để kiểm tra gợi ý đã được lưu</li>
            </ol>
        </div>

        <div class="form-group">
            <label for="loidanbacsi">Lời dặn bác sĩ:</label>
            <textarea name="data[LOIDANBACSI]" id="loidanbacsi" placeholder="Nhập lời dặn bác sĩ... (sẽ hiển thị gợi ý từ các lần nhập trước)"></textarea>
        </div>

        <div class="test-buttons">
            <button class="btn btn-success" onclick="saveSuggestion()">💾 Lưu gợi ý</button>
            <button class="btn" onclick="clearSuggestions()">🗑️ Xóa tất cả gợi ý</button>
            <button class="btn" onclick="showSuggestions()">📋 Xem danh sách gợi ý</button>
        </div>

        <div id="status"></div>
    </div>

    <script>
        // Code tối giản từ dientim.js
        var LoiDanBacSiSuggestions = {
            storageKey: 'dientim_loidanbacsi_suggestions',
            maxSuggestions: 20,

            getSuggestions: function() {
                try {
                    var suggestions = localStorage.getItem(this.storageKey);
                    return suggestions ? JSON.parse(suggestions) : [];
                } catch (e) {
                    return [];
                }
            },

            addSuggestion: function(text) {
                if (!text) return;
                text = text.trim();
                if (!text) return;

                var suggestions = this.getSuggestions();
                suggestions = suggestions.filter(function(item) {
                    return item.text.toLowerCase() !== text.toLowerCase();
                });

                suggestions.unshift({
                    text: text,
                    timestamp: new Date().getTime()
                });

                if (suggestions.length > this.maxSuggestions) {
                    suggestions = suggestions.slice(0, this.maxSuggestions);
                }

                try {
                    localStorage.setItem(this.storageKey, JSON.stringify(suggestions));
                } catch (e) {
                    // Ignore error
                }
            },

            initAutocomplete: function() {
                var self = this;

                function createDropdown() {
                    var $textarea = $('textarea[name="data[LOIDANBACSI]"]');
                    if ($textarea.length === 0) return;

                    $('.loidanbacsi-dropdown').remove();

                    var $dropdown = $('<div class="loidanbacsi-dropdown"></div>');
                    $dropdown.css({
                        position: 'fixed',
                        backgroundColor: '#ffffff',
                        border: '2px solid #007bff',
                        borderRadius: '4px',
                        maxHeight: '200px',
                        overflowY: 'auto',
                        zIndex: 9999,
                        display: 'none',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
                        minWidth: '300px'
                    });

                    $('body').append($dropdown);

                    function showDropdown() {
                        var suggestions = self.getSuggestions();
                        if (suggestions.length === 0) {
                            $dropdown.hide();
                            return;
                        }

                        var value = $textarea.val().toLowerCase();
                        var filtered = suggestions.filter(function(s) {
                            return value === '' || s.text.toLowerCase().includes(value);
                        });

                        if (filtered.length > 0) {
                            $dropdown.empty();

                            filtered.slice(0, 6).forEach(function(suggestion, index) {
                                var $item = $('<div></div>');
                                $item.text((index + 1) + '. ' + suggestion.text);
                                $item.css({
                                    padding: '8px 12px',
                                    cursor: 'pointer',
                                    borderBottom: '1px solid #f0f0f0',
                                    fontSize: '13px'
                                });

                                $item.hover(
                                    function() { $(this).css('backgroundColor', '#f5f5f5'); },
                                    function() { $(this).css('backgroundColor', 'white'); }
                                );

                                $item.click(function() {
                                    $textarea.val(suggestion.text);
                                    $textarea.focus();
                                    $dropdown.hide();
                                    self.addSuggestion(suggestion.text);
                                    showStatus('✅ Đã chọn: ' + suggestion.text, 'success');
                                });

                                $dropdown.append($item);
                            });

                            var textareaOffset = $textarea.offset();
                            var textareaHeight = $textarea.outerHeight();
                            var textareaWidth = $textarea.outerWidth();
                            var windowScrollTop = $(window).scrollTop();
                            var windowHeight = $(window).height();

                            var fixedTop = textareaOffset.top - windowScrollTop + textareaHeight + 5;
                            var fixedLeft = textareaOffset.left;

                            if (fixedTop + 200 > windowHeight) {
                                fixedTop = textareaOffset.top - windowScrollTop - 200 - 5;
                            }

                            $dropdown.css({
                                top: Math.max(5, fixedTop) + 'px',
                                left: Math.max(5, fixedLeft) + 'px',
                                width: Math.max(textareaWidth, 300) + 'px',
                                display: 'block'
                            });
                        } else {
                            $dropdown.hide();
                        }
                    }

                    $textarea.off('.loidanbacsi').on('focus.loidanbacsi click.loidanbacsi', function() {
                        setTimeout(showDropdown, 50);
                    });

                    $textarea.on('input.loidanbacsi', function() {
                        showDropdown();
                    });

                    $(document).off('click.loidanbacsi').on('click.loidanbacsi', function(e) {
                        if (!$(e.target).closest($textarea).length &&
                            !$(e.target).closest($dropdown).length) {
                            $dropdown.hide();
                        }
                    });

                    $(window).off('resize.loidanbacsi scroll.loidanbacsi').on('resize.loidanbacsi scroll.loidanbacsi', function() {
                        $dropdown.hide();
                    });
                }

                createDropdown();
                return this;
            }
        };

        // Khởi tạo khi trang load
        $(document).ready(function() {
            LoiDanBacSiSuggestions.initAutocomplete();
        });

        // Các hàm test
        function saveSuggestion() {
            var text = $('#loidanbacsi').val().trim();
            if (!text) {
                showStatus('❌ Vui lòng nhập nội dung', 'error');
                return;
            }

            LoiDanBacSiSuggestions.addSuggestion(text);
            showStatus('✅ Đã lưu gợi ý: ' + text, 'success');
            $('#loidanbacsi').val('');
        }

        function clearSuggestions() {
            localStorage.removeItem('dientim_loidanbacsi_suggestions');
            showStatus('🗑️ Đã xóa tất cả gợi ý', 'success');
        }

        function showSuggestions() {
            var suggestions = LoiDanBacSiSuggestions.getSuggestions();
            if (suggestions.length === 0) {
                showStatus('📋 Chưa có gợi ý nào', 'error');
            } else {
                var list = suggestions.map(function(s, i) {
                    return (i + 1) + '. ' + s.text;
                }).join('\n');
                alert('📋 Danh sách gợi ý hiện tại:\n\n' + list);
            }
        }

        function showStatus(message, type) {
            var $status = $('#status');
            $status.removeClass('success error').addClass(type).text(message).show();
            setTimeout(function() {
                $status.fadeOut();
            }, 3000);
        }
    </script>
</body>
</html>
