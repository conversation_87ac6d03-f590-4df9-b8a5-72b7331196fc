CREATE OR REPLACE FUNCTION cmu_xemct_trungtgthuchien_ttpt (
    p_dvtt         VARCHAR2,
    p_sovaovien    VARCHAR2,
    p_userid       VARCHAR2,
    p_thoigianbd   VARCHAR2,
    p_thoigiankt   VARCHAR2,
    p_loaikt       VARCHAR2
) RETURN SYS_REFCURSOR IS
    v_cur SYS_REFCURSOR;
BEGIN
OPEN v_cur FOR SELECT
                       tg.maben<PERSON>han ma_bn,
                       bn.ten_benh_nhan,
                       cmu_getten_trungtgthuchien(p_dvtt, tg.sophieu, tg.ma_dv) ten_dich_vu,
                       TO_CHAR(thoigian_bd, 'dd/mm/yyyy hh24:mi') thoigianbd,
                       TO_CHAR(thoigian_kt, 'dd/mm/yyyy hh24:mi') thoigiankt,
                       tg.sovaovien,
                       'Th<PERSON><PERSON> gian thực hiện bị trùng: '
                       || p_thoigianbd
                       || ' -> '
                       || p_thoigiankt ghichu
                   FROM
                       cmu_thoigian_nhanvien          tg
                       JOIN his_public_list.dm_benh_nhan   bn ON tg.maben<PERSON>han = bn.ma_benh_nhan
                   WHERE
                       tg.dvtt = p_dvtt
                       AND tg.manhanvien in (SELECT * FROM TABLE(hpg_split_string(p_userid)))
                       AND tg.sovaovien != p_sovaovien
                       AND ( TO_DATE(p_thoigianbd, 'dd/mm/yyyy hh24:mi') BETWEEN thoigian_bd AND thoigian_kt
                             OR TO_DATE(p_thoigiankt, 'dd/mm/yyyy hh24:mi') BETWEEN thoigian_bd AND thoigian_kt
                             OR ( TO_DATE(p_thoigianbd, 'dd/mm/yyyy hh24:mi') < thoigian_bd
                                  AND TO_DATE(p_thoigiankt, 'dd/mm/yyyy hh24:mi') > thoigian_kt ) );

RETURN v_cur;
END;