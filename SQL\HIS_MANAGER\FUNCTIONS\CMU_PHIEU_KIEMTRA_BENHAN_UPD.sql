CREATE OR REPLACE FUNCTION "HIS_MANAGER"."CMU_PHIEU_KIEMTRA_BENHAN_UPD" (
    p_id IN NUMBER,
    p_DVTT IN VARCHAR2,
    p_DATA_PHIEU IN CLOB
) RETURN NUMBER IS
    v_count NUMBER;
BEGIN
    UPDATE "HIS_MANAGER"."CMU_PHIEU_KIEMTRA_BENHAN"
    SET DATA_PHIEU = p_DATA_PHIEU
    WHERE id = p_id
        AND DVTT = p_DVTT;

    v_count := SQL%ROWCOUNT;

    RETURN v_count;
END CMU_PHIEU_KIEMTRA_BENHAN_UPD;