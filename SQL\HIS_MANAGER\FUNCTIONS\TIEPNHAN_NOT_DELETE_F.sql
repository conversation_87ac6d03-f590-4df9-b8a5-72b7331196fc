CREATE OR REPLACE FUNCTION "TIEPNHAN_NOT_DELETE_F" (
    p_stt_<PERSON><PERSON>         IN                   VARCHAR2,
    p_stt_dotdieutri     IN                   VARCHAR2,
    p_stt_logkhoaphong   IN                   VARCHAR2,
    p_dvtt               IN                   VARCHAR2
) RETURN VARCHAR2 IS

    v_saisot               NUMBER(10) DEFAULT 0;
    v_xettruonghop         NUMBER(10);
    v_dieutri              NUMBER(10) DEFAULT 0;
    v_makhambenh           VARCHAR2(50) DEFAULT '';
    v_idtiepnhan           VARCHAR2(50) DEFAULT '';
    v_xettruonghoptamung   NUMBER(10) DEFAULT 0;
    v_hoantatkhambenh      TIMESTAMP;
    v_sovaovien_ngoai      NUMBER(18);
    v_sovaovien_noi        NUMBER(18);
    v_sovaovien_dt_noi     NUMBER(18);
    v_thamso_52049         NUMBER(1) DEFAULT 0;
    v_ts960502             NUMBER(10) := his_fw.cmu_tsdv(p_dvtt, 960502, 0);
    v_tmpreturn960502      VARCHAR2(10) := '0';
    p_bant                 NUMBER(18) := 0;
    v_sobenhan             VARCHAR2(50) DEFAULT '';
BEGIN
BEGIN
SELECT
    mota_thamso
INTO v_thamso_52049
FROM
    his_fw.dm_thamso_donvi
WHERE
    ma_thamso = 52049
  AND dvtt = p_dvtt;

EXCEPTION
        WHEN no_data_found THEN
            v_thamso_52049 := '0';
END;

SELECT
    trang_thai,
    makhambenhngoaitru_nhapvien,
    sovaovien,
    bant,
    sobenhan
INTO
    v_xettruonghop,
    v_makhambenh,
    v_sovaovien_noi,
    p_bant,
    v_sobenhan
FROM
    noitru_benhan
WHERE
    stt_benhan = p_stt_benhan
  AND dvtt = p_dvtt;

SELECT
    sovaovien_dt
INTO v_sovaovien_dt_noi
FROM
    noitru_dotdieutri
WHERE
    stt_benhan = p_stt_benhan
  AND dvtt = p_dvtt
  AND sovaovien = v_sovaovien_noi
  AND stt_dotdieutri = p_stt_dotdieutri;

SELECT
    nvl(COUNT(1), 0)
INTO v_dieutri
FROM
    noitru_dieutri
WHERE
    stt_benhan = p_stt_benhan
  AND dvtt = p_dvtt
  AND sovaovien = v_sovaovien_noi;

SELECT
    COUNT(1)
INTO v_xettruonghoptamung
FROM
    vienphinoitru_lantamung
WHERE
    stt_benhan = p_stt_benhan
  AND tt_lantt = 1
  AND dvtt = p_dvtt
  AND ngoaitru = 0
  AND sovaovien = v_sovaovien_noi;

IF nvl(v_makhambenh, ' ') != ' ' THEN
        v_idtiepnhan := replace(v_makhambenh, 'kb_', '');
END IF;
   -- select v_xettruonghop;

    IF v_thamso_52049 = 1 THEN
        IF v_xettruonghop > 1 OR v_dieutri != 0 THEN
            IF v_xettruonghop = 2 AND v_dieutri = 0 THEN
                v_saisot := 0; -- 2) �ang di?u tr?
            ELSIF ( v_xettruonghop = 2 OR v_dieutri != 0 ) AND p_dvtt != 96172 THEN
                v_saisot := 1;
            ELSIF v_xettruonghop >= 3 THEN
                v_saisot := 2; -- , 3 xu?t vi?n
END IF;

END IF;
ELSE
        IF v_xettruonghop > 1 OR v_dieutri != 0 THEN
            IF ( v_xettruonghop = 2 OR v_dieutri != 0 ) THEN
                v_saisot := 1; -- 2) �ang di?u tr?
            ELSIF v_xettruonghop >= 3 THEN
                v_saisot := 2; -- , 3 xu?t vi?n
END IF;

END IF;
END IF;

    IF v_xettruonghoptamung > 0 THEN
        v_saisot := 5; -- d� t?m ?ng r?i
END IF;
    IF v_thamso_52049 = 1 THEN
        IF v_xettruonghop <= 2 AND v_dieutri = 0 AND v_saisot = 0 THEN
DELETE FROM noitru_logkhoaphong
WHERE
    dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND sovaovien = v_sovaovien_noi
  AND sovaovien_dt = v_sovaovien_dt_noi;

DELETE FROM noitru_phieuthanhtoan
WHERE
    stt_dotdieutri = p_stt_dotdieutri
  AND stt_benhan = p_stt_benhan
  AND dvtt = p_dvtt
  AND sovaovien = v_sovaovien_noi
  AND sovaovien_dt = v_sovaovien_dt_noi;

DELETE FROM noitru_dotdieutri
WHERE
    stt_dotdieutri = p_stt_dotdieutri
  AND stt_benhan = p_stt_benhan
  AND dvtt = p_dvtt
  AND sovaovien = v_sovaovien_noi
  AND sovaovien_dt = v_sovaovien_dt_noi;

DELETE FROM noitru_benhan
WHERE
    stt_benhan = p_stt_benhan
  AND dvtt = p_dvtt
  AND sovaovien = v_sovaovien_noi;

DELETE FROM cmu_soba_uniq
WHERE
    sobenhan = v_sobenhan
  AND dvtt = p_dvtt;

DELETE FROM his_public_list.ls_tiepnhanbenhnhan_noitru
WHERE
    stt_benhan = p_stt_benhan
  AND dvtt = p_dvtt;

IF v_ts960502 = 1 AND p_bant = 0 THEN
                v_tmpreturn960502 := cmu_del_xnt_bn(p_dvtt, v_sovaovien_noi);
END IF;

            IF nvl(v_idtiepnhan, ' ') != ' ' THEN
BEGIN
SELECT
    sovaovien_ngoai
INTO v_sovaovien_ngoai
FROM
    vienphinoitru_lantamung tu
WHERE
    stt_dotdieutri = p_stt_dotdieutri
  AND stt_benhan = p_stt_benhan
  AND tu.tt_lantt = 1
  AND tu.ngoaitru = 1
  AND ROWNUM <= 1
  AND sovaovien = v_sovaovien_noi
  AND sovaovien_dt = v_sovaovien_dt_noi;

EXCEPTION
                    WHEN no_data_found THEN
                        v_sovaovien_ngoai := 0;
END;

UPDATE vienphingoaitru_lantamung tu
SET
    tu.tt_lantt = 1,
    tu.nhapvien = 0
WHERE
    tu.tt_lantt = 2
  AND tu.nhapvien = 1
  AND tu.sovaovien = v_sovaovien_ngoai
  AND tu.id_tiepnhan = v_idtiepnhan;

UPDATE vienphinoitru_lantamung tu
SET
    tu.tt_lantt = 2,
    tu.ngoaitru = 1
WHERE
    tu.tt_lantt = 1
  AND tu.ngoaitru = 1
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_benhan = p_stt_benhan
  AND dvtt = p_dvtt
  AND sovaovien = v_sovaovien_noi
  AND sovaovien_dt = v_sovaovien_dt_noi;

BEGIN
SELECT
    hoantatkhambenh
INTO v_hoantatkhambenh
FROM
    kb_kham_benh
WHERE
    ma_kham_benh = v_makhambenh
  AND dvtt = p_dvtt;

EXCEPTION
                    WHEN no_data_found THEN
                        NULL;
END;

                IF v_hoantatkhambenh IS NULL THEN
UPDATE kb_tiep_nhan
SET
    trang_thai = 2
WHERE
    id_tiepnhan = v_idtiepnhan
  AND dvtt = p_dvtt;

UPDATE tiep_nhan_phong_benh
SET
    trang_thai_bn = 2
WHERE
    dvtt = p_dvtt
  AND id_tiepnhan = v_idtiepnhan
  AND trang_thai_bn = 7;

ELSE
UPDATE kb_tiep_nhan
SET
    trang_thai = 3
WHERE
    id_tiepnhan = v_idtiepnhan
  AND dvtt = p_dvtt;

UPDATE tiep_nhan_phong_benh
SET
    trang_thai_bn = 3
WHERE
    dvtt = p_dvtt
  AND id_tiepnhan = v_idtiepnhan
  AND trang_thai_bn = 7;

UPDATE kb_kham_benh
SET
    ma_huong_dieu_tri = 1
WHERE
    dvtt = p_dvtt
  AND id_tiepnhan = v_idtiepnhan;

END IF;

END IF;

            v_saisot := 0;
END IF;
ELSE
        IF v_xettruonghop <= 1 AND v_dieutri = 0 AND v_saisot = 0 THEN

       -- 2) B?nh nh�n d� d�ng ti?n

       -- x? l� x�a c�c b?ng bi?u
DELETE FROM noitru_logkhoaphong
WHERE
    dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND sovaovien = v_sovaovien_noi
  AND sovaovien_dt = v_sovaovien_dt_noi;

DELETE FROM noitru_phieuthanhtoan
WHERE
    stt_dotdieutri = p_stt_dotdieutri
  AND stt_benhan = p_stt_benhan
  AND dvtt = p_dvtt
  AND sovaovien = v_sovaovien_noi
  AND sovaovien_dt = v_sovaovien_dt_noi;

DELETE FROM noitru_dotdieutri
WHERE
    stt_dotdieutri = p_stt_dotdieutri
  AND stt_benhan = p_stt_benhan
  AND dvtt = p_dvtt
  AND sovaovien = v_sovaovien_noi
  AND sovaovien_dt = v_sovaovien_dt_noi;

DELETE FROM noitru_benhan
WHERE
    stt_benhan = p_stt_benhan
  AND dvtt = p_dvtt
  AND sovaovien = v_sovaovien_noi;

DELETE FROM cmu_soba_uniq
WHERE
    sobenhan = v_sobenhan
  AND dvtt = p_dvtt;
UPDATE CMU_HSBA_PHONGBENH_GB
set SOVAOViEN = null
WHERE DVTT = P_DVTT AND SOVAOViEN = v_sovaovien_noi;

DELETE FROM his_public_list.ls_tiepnhanbenhnhan_noitru
WHERE
    stt_benhan = p_stt_benhan
  AND dvtt = p_dvtt;

IF v_ts960502 = 1 AND p_bant = 0 THEN
                v_tmpreturn960502 := cmu_del_xnt_bn(p_dvtt, v_sovaovien_noi);
END IF;

            IF nvl(v_idtiepnhan, ' ') != ' ' THEN
BEGIN
SELECT
    sovaovien_ngoai
INTO v_sovaovien_ngoai
FROM
    vienphinoitru_lantamung tu
WHERE
    stt_dotdieutri = p_stt_dotdieutri
  AND stt_benhan = p_stt_benhan
  AND tu.tt_lantt = 1
  AND tu.ngoaitru = 1
  AND ROWNUM <= 1
  AND sovaovien = v_sovaovien_noi
  AND sovaovien_dt = v_sovaovien_dt_noi;

EXCEPTION
                    WHEN no_data_found THEN
                        v_sovaovien_ngoai := 0;
END;

UPDATE vienphingoaitru_lantamung tu
SET
    tu.tt_lantt = 1,
    tu.nhapvien = 0
WHERE
    tu.tt_lantt = 2
  AND tu.nhapvien = 1
  AND tu.sovaovien = v_sovaovien_ngoai
  AND tu.id_tiepnhan = v_idtiepnhan;

UPDATE vienphinoitru_lantamung tu
SET
    tu.tt_lantt = 2,
    tu.ngoaitru = 1
WHERE
    tu.tt_lantt = 1
  AND tu.ngoaitru = 1
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_benhan = p_stt_benhan
  AND dvtt = p_dvtt
  AND sovaovien = v_sovaovien_noi
  AND sovaovien_dt = v_sovaovien_dt_noi;

SELECT
    hoantatkhambenh
INTO v_hoantatkhambenh
FROM
    kb_kham_benh
WHERE
    ma_kham_benh = v_makhambenh
  AND dvtt = p_dvtt;

IF v_hoantatkhambenh IS NULL THEN
UPDATE kb_tiep_nhan
SET
    trang_thai = 2
WHERE
    id_tiepnhan = v_idtiepnhan
  AND dvtt = p_dvtt;

UPDATE tiep_nhan_phong_benh
SET
    trang_thai_bn = 2
WHERE
    dvtt = p_dvtt
  AND id_tiepnhan = v_idtiepnhan
  AND trang_thai_bn = 7;

ELSE
UPDATE kb_tiep_nhan
SET
    trang_thai = 3
WHERE
    id_tiepnhan = v_idtiepnhan
  AND dvtt = p_dvtt;

UPDATE tiep_nhan_phong_benh
SET
    trang_thai_bn = 3
WHERE
    dvtt = p_dvtt
  AND id_tiepnhan = v_idtiepnhan
  AND trang_thai_bn = 7;

UPDATE kb_kham_benh
SET
    ma_huong_dieu_tri = 1
WHERE
    dvtt = p_dvtt
  AND id_tiepnhan = v_idtiepnhan;

END IF;

END IF;

            v_saisot := 0;
END IF;
END IF;

RETURN v_saisot;
END;