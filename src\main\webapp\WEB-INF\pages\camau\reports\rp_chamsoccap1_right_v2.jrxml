<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="phieugaymehoisuc" language="groovy" pageWidth="1190" pageHeight="842" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="1190" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="5f982810-2f9d-40df-b4a6-deec4a399123">
	<property name="ireport.zoom" value="0.8467108950806675"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="table">
		<box>
			<pen lineWidth="1.0" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<parameter name="dvtt" class="java.lang.String"/>
	<parameter name="id_phieu" class="java.lang.String"/>
	<parameter name="page" class="java.lang.String"/>
	<parameter name="lastpage" class="java.lang.String"/>
	<parameter name="maxrow" class="java.lang.Integer"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String"/>
	<parameter name="khonghinh" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[SELECT 1 FROM dual]]>
	</queryString>
	<title>
		<band height="15">
			<frame>
				<reportElement positionType="Float" x="0" y="0" width="250" height="15" isPrintWhenDetailOverflows="true" uuid="e751914b-49ce-49d3-ae0f-07648b68085d"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<subreport isUsingCache="true">
					<reportElement x="0" y="0" width="250" height="15" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true" uuid="7acf31a8-8448-42b2-9780-9ac855cf32cb"/>
					<subreportParameter name="idphieu">
						<subreportParameterExpression><![CDATA[$P{id_phieu}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="page">
						<subreportParameterExpression><![CDATA[$P{page}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="dvtt">
						<subreportParameterExpression><![CDATA[$P{dvtt}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "//rp_chamsoccap1_chandoan.jasper"]]></subreportExpression>
				</subreport>
			</frame>
		</band>
	</title>
	<pageHeader>
		<band height="30">
			<frame>
				<reportElement positionType="Float" x="0" y="15" width="250" height="15" isPrintWhenDetailOverflows="true" uuid="6a3e828d-9a47-4db7-a2b1-608bf22c031d"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<subreport>
					<reportElement x="0" y="0" width="250" height="15" uuid="edea25b9-2c49-4f1b-b4ca-89f2c490131f"/>
					<subreportParameter name="idphieu">
						<subreportParameterExpression><![CDATA[$P{id_phieu}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="page">
						<subreportParameterExpression><![CDATA[$P{page}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="dvtt">
						<subreportParameterExpression><![CDATA[$P{dvtt}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "//rp_chamsoccap1_ghichu.jasper"]]></subreportExpression>
				</subreport>
			</frame>
			<textField>
				<reportElement positionType="Float" mode="Opaque" x="0" y="0" width="250" height="15" backcolor="#CCCCCC" uuid="2ac10925-ff44-4ff7-a1bc-42e0660c38e9"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["GHI CHÚ / BÀN GIAO"]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="30">
			<frame>
				<reportElement positionType="Float" x="0" y="0" width="250" height="30" uuid="5dac864f-d862-4ff8-80e8-0171478df5be"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textField>
					<reportElement positionType="Float" x="0" y="0" width="250" height="15" uuid="d0a334b7-5b49-49f8-9a62-f533d07c31ac"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
						<font fontName="Times New Roman" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["QUY ƯỚC KÝ HIỆU"]]></textFieldExpression>
				</textField>
				<frame>
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="0" y="15" width="250" height="15" isPrintWhenDetailOverflows="true" uuid="80aeb930-c5cc-44c4-b777-4f18d491c2ad"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<subreport runToBottom="false">
						<reportElement x="0" y="0" width="250" height="15" uuid="a5459837-b38f-40b4-864b-523c0531c854"/>
						<subreportParameter name="idphieu">
							<subreportParameterExpression><![CDATA[$P{id_phieu}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="page">
							<subreportParameterExpression><![CDATA[$P{page}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="dvtt">
							<subreportParameterExpression><![CDATA[$P{dvtt}]]></subreportParameterExpression>
						</subreportParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
						<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + ($P{khonghinh}.equals("1") ? "//rp_chamsoccap1_kyhieu_v2_khonghinh.jasper" : "//rp_chamsoccap1_kyhieu_v2.jasper")]]></subreportExpression>
					</subreport>
				</frame>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band splitType="Stretch"/>
	</detail>
</jasperReport>
