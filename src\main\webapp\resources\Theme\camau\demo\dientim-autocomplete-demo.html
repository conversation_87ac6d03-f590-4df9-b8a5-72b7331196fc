<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Autocomplete - Lời dặn bác sĩ</title>
    <link href="../css/dientim-autocomplete.css" rel="stylesheet"/>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .demo-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        textarea {
            width: 100%;
            min-height: 100px;
            padding: 12px;
            border: 2px solid #e3e6f0;
            border-radius: 4px;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .info-box {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .suggestions-list {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
        }
        .suggestion-item {
            padding: 8px;
            border-bottom: 1px solid #dee2e6;
            cursor: pointer;
        }
        .suggestion-item:hover {
            background-color: #e9ecef;
        }
        .suggestion-item:last-child {
            border-bottom: none;
        }
        .stats {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>Demo Tính năng Autocomplete - Lời dặn bác sĩ</h1>
        
        <div class="info-box">
            <strong>Hướng dẫn sử dụng:</strong>
            <ul>
                <li>Nhập nội dung vào ô "Lời dặn bác sĩ" và nhấn "Lưu gợi ý"</li>
                <li>Click vào ô nhập để xem danh sách gợi ý</li>
                <li>Bắt đầu nhập để lọc gợi ý phù hợp</li>
                <li>Chọn gợi ý từ dropdown hoặc tiếp tục nhập</li>
            </ul>
        </div>

        <div class="form-group">
            <label for="loidanbacsi">Lời dặn bác sĩ:</label>
            <textarea 
                id="loidanbacsi" 
                name="data[LOIDANBACSI]" 
                placeholder="Nhập lời dặn bác sĩ... (sẽ hiển thị gợi ý từ các lần nhập trước)"
            ></textarea>
        </div>

        <div>
            <button class="btn" onclick="saveSuggestion()">Lưu gợi ý</button>
            <button class="btn btn-secondary" onclick="clearSuggestions()">Xóa tất cả gợi ý</button>
            <button class="btn btn-secondary" onclick="loadSampleData()">Tải dữ liệu mẫu</button>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="totalSuggestions">0</div>
                <div class="stat-label">Tổng gợi ý</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="filteredSuggestions">0</div>
                <div class="stat-label">Gợi ý hiển thị</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="currentLength">0</div>
                <div class="stat-label">Độ dài hiện tại</div>
            </div>
        </div>

        <div class="suggestions-list">
            <h3>Danh sách gợi ý hiện có:</h3>
            <div id="suggestionsList"></div>
        </div>
    </div>

    <script>
        // Copy code từ dientim.js
        var LoiDanBacSiSuggestions = {
            storageKey: 'dientim_loidanbacsi_suggestions',
            maxSuggestions: 20,
            
            getSuggestions: function() {
                try {
                    var suggestions = localStorage.getItem(this.storageKey);
                    return suggestions ? JSON.parse(suggestions) : [];
                } catch (e) {
                    console.error('Lỗi khi lấy gợi ý:', e);
                    return [];
                }
            },
            
            addSuggestion: function(text) {
                if (!text || text.trim().length < 3) return;
                
                text = text.trim();
                var suggestions = this.getSuggestions();
                
                suggestions = suggestions.filter(function(item) {
                    return item.text.toLowerCase() !== text.toLowerCase();
                });
                
                suggestions.unshift({
                    text: text,
                    timestamp: new Date().getTime()
                });
                
                if (suggestions.length > this.maxSuggestions) {
                    suggestions = suggestions.slice(0, this.maxSuggestions);
                }
                
                try {
                    localStorage.setItem(this.storageKey, JSON.stringify(suggestions));
                } catch (e) {
                    console.error('Lỗi khi lưu gợi ý:', e);
                }
            },
            
            createDatalist: function() {
                var suggestions = this.getSuggestions();
                var datalistId = 'loidanbacsi-suggestions';
                
                var existingDatalist = document.getElementById(datalistId);
                if (existingDatalist) {
                    existingDatalist.remove();
                }
                
                var datalist = document.createElement('datalist');
                datalist.id = datalistId;
                
                suggestions.forEach(function(suggestion) {
                    var option = document.createElement('option');
                    option.value = suggestion.text;
                    datalist.appendChild(option);
                });
                
                document.body.appendChild(datalist);
                return datalistId;
            },

            clearAll: function() {
                localStorage.removeItem(this.storageKey);
            }
        };

        // Khởi tạo autocomplete
        function initAutocomplete() {
            var datalistId = LoiDanBacSiSuggestions.createDatalist();
            var textarea = document.getElementById('loidanbacsi');
            
            textarea.setAttribute('list', datalistId);
            
            textarea.addEventListener('focus', function() {
                LoiDanBacSiSuggestions.createDatalist();
                updateStats();
            });
            
            textarea.addEventListener('input', function() {
                var currentValue = this.value.toLowerCase();
                if (currentValue.length >= 2) {
                    updateFilteredSuggestions(datalistId, currentValue);
                }
                updateStats();
            });
        }

        function updateFilteredSuggestions(datalistId, searchText) {
            var suggestions = LoiDanBacSiSuggestions.getSuggestions();
            var datalist = document.getElementById(datalistId);
            
            if (!datalist) return;
            
            datalist.innerHTML = '';
            
            var filteredSuggestions = suggestions.filter(function(suggestion) {
                return suggestion.text.toLowerCase().indexOf(searchText) !== -1;
            });
            
            filteredSuggestions.slice(0, 10).forEach(function(suggestion) {
                var option = document.createElement('option');
                option.value = suggestion.text;
                datalist.appendChild(option);
            });

            document.getElementById('filteredSuggestions').textContent = filteredSuggestions.length;
        }

        function saveSuggestion() {
            var textarea = document.getElementById('loidanbacsi');
            var text = textarea.value.trim();
            
            if (text.length < 3) {
                alert('Vui lòng nhập ít nhất 3 ký tự');
                return;
            }
            
            LoiDanBacSiSuggestions.addSuggestion(text);
            LoiDanBacSiSuggestions.createDatalist();
            updateSuggestionsList();
            updateStats();
            
            alert('Đã lưu gợi ý thành công!');
        }

        function clearSuggestions() {
            if (confirm('Bạn có chắc muốn xóa tất cả gợi ý?')) {
                LoiDanBacSiSuggestions.clearAll();
                LoiDanBacSiSuggestions.createDatalist();
                updateSuggestionsList();
                updateStats();
                alert('Đã xóa tất cả gợi ý!');
            }
        }

        function loadSampleData() {
            var sampleData = [
                'Tái khám sau 1 tuần',
                'Uống thuốc đúng giờ, đủ liều',
                'Nghỉ ngơi, tránh căng thẳng',
                'Ăn nhạt, hạn chế muối',
                'Tập thể dục nhẹ nhàng',
                'Theo dõi huyết áp hàng ngày',
                'Đến viện ngay nếu có triệu chứng bất thường'
            ];
            
            sampleData.forEach(function(text) {
                LoiDanBacSiSuggestions.addSuggestion(text);
            });
            
            LoiDanBacSiSuggestions.createDatalist();
            updateSuggestionsList();
            updateStats();
            
            alert('Đã tải dữ liệu mẫu!');
        }

        function updateSuggestionsList() {
            var suggestions = LoiDanBacSiSuggestions.getSuggestions();
            var listElement = document.getElementById('suggestionsList');
            
            if (suggestions.length === 0) {
                listElement.innerHTML = '<p style="color: #6c757d; font-style: italic;">Chưa có gợi ý nào</p>';
                return;
            }
            
            var html = '';
            suggestions.forEach(function(suggestion, index) {
                var date = new Date(suggestion.timestamp);
                html += '<div class="suggestion-item" onclick="selectSuggestion(\'' + 
                        suggestion.text.replace(/'/g, "\\'") + '\')">' +
                        '<strong>' + (index + 1) + '.</strong> ' + suggestion.text +
                        '<br><small style="color: #6c757d;">Lưu lúc: ' + date.toLocaleString('vi-VN') + '</small>' +
                        '</div>';
            });
            
            listElement.innerHTML = html;
        }

        function selectSuggestion(text) {
            document.getElementById('loidanbacsi').value = text;
            updateStats();
        }

        function updateStats() {
            var suggestions = LoiDanBacSiSuggestions.getSuggestions();
            var currentText = document.getElementById('loidanbacsi').value;
            
            document.getElementById('totalSuggestions').textContent = suggestions.length;
            document.getElementById('currentLength').textContent = currentText.length;
            
            if (currentText.length >= 2) {
                var filtered = suggestions.filter(function(suggestion) {
                    return suggestion.text.toLowerCase().indexOf(currentText.toLowerCase()) !== -1;
                });
                document.getElementById('filteredSuggestions').textContent = filtered.length;
            } else {
                document.getElementById('filteredSuggestions').textContent = suggestions.length;
            }
        }

        // Khởi tạo khi trang load
        document.addEventListener('DOMContentLoaded', function() {
            initAutocomplete();
            updateSuggestionsList();
            updateStats();
        });
    </script>
</body>
</html>
