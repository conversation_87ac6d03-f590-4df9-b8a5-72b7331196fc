$(function (){
    var thongTinCamKetPhauThuatGMHSMoiNhat = {};
    var thongTinCamKetPhauThuatGMHSTruocChinhSua = {};
    var formCamKetPhauThuatGMHS;

    $("#ttchamsoc-phieukhac").click(function () {
        instanceGridCamKetPhauThuatGMHS();
        reloadDSCamKetPhauThuatGMHS();
        showFormCamKetPhauThuatGMHS();
    });
    $("#camketphauthuatgmhs_lammoi").click(function () {
        reloadDSCamKetPhauThuatGMHS();
    });

    $(".themcamketphauthuatgmhs").click(function () {
        $("#modalFormCamKetPhauThuatGMHS").modal("show");
        addTextTitleModal("titleFormCamKetPhauThuatGMHS", " Phiếu cam kết chấp thuận phẫu thuật, thủ thuật và gây mê hồi sức");
        showFormCamKetPhauThuatGMHS();
        $("#camketphauthuatgmhs_luu").attr("data-action", "THEM");
    });

    $("#camketphauthuatgmhs_luu").click(function () {
        var btnAction = $('#camketphauthuatgmhs_luu').attr("data-action");
        if (btnAction == "THEM"){
            themCamKetPhauThuatGMHS();
        } else {
            updateCamKetPhauThuatGMHS();
        }
    });

    $(document).on('click', '#camketphauthuatgmhs_kysopt_action', function() {
        kySoChung({
            dvtt: singletonObject.dvtt,
            userId: singletonObject.userId,
            url: $('#iframePreviewAndSign').attr('src'),
            loaiGiay: "PHIEU_NOITRU_CAMKETPHAUTHUATGMHS_BSPT",
            maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
            soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
            soPhieuDichVu: thongTinCamKetPhauThuatGMHSTruocChinhSua.ID,
            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            keyword: "bác sĩ pt ký",
            fileName: "Phiếu cam kết chấp thuận phẫu thuật, thủ thuật và gây mê hồi sức: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Mã phiếu: " + thongTinCamKetPhauThuatGMHSTruocChinhSua.ID,
        }, function(dataKySo) {
            $("#modalPreviewAndSignPDF").modal("hide");
            reloadDSCamKetPhauThuatGMHS();
        });
    });

    $(document).on('click', '#camketphauthuatgmhs_kysogm_action', function() {
        kySoChung({
            dvtt: singletonObject.dvtt,
            userId: singletonObject.userId,
            url: $('#iframePreviewAndSign').attr('src'),
            loaiGiay: "PHIEU_NOITRU_CAMKETPHAUTHUATGMHS_BSGM",
            maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
            soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
            soPhieuDichVu: thongTinCamKetPhauThuatGMHSTruocChinhSua.ID,
            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            keyword: "bác sĩ gm ký",
            fileName: "Phiếu cam kết chấp thuận phẫu thuật, thủ thuật và gây mê hồi sức: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Mã phiếu: " + thongTinCamKetPhauThuatGMHSTruocChinhSua.ID,
        }, function(dataKySo) {
            $("#modalPreviewAndSignPDF").modal("hide");
            reloadDSCamKetPhauThuatGMHS();
        });
    });

    $('#view_single_camketphauthuatgmhs').click(function () {
        xemCamKetPhauThuatGMHS(thongTinCamKetPhauThuatGMHSMoiNhat)
    })

    $('#edit_single_camketphauthuatgmhs').click(function () {
        showUpdateCamKetPhauThuatGMHS(thongTinCamKetPhauThuatGMHSMoiNhat)
    });

    $('#delete_single_camketphauthuatgmhs').click(function () {
        deleteCamKetPhauThuatGMHS(thongTinCamKetPhauThuatGMHSMoiNhat)
    })

    function showFormCamKetPhauThuatGMHS() {
        var jsonForm = getJSONObjectForm([
            {
                "label": "Loại giấy cam đoan",
                others: {
                    "data": {
                        "values": [
                            {
                                "label": "Cấp cứu",
                                "value": "1"
                            },
                            {
                                "label": "Bán cấp",
                                "value": "2"
                            },
                            {
                                "label": "Chương trình/ Phiên",
                                "value": "3"
                            },

                        ]
                    },
                },
                "validate": {
                    "required": true,
                },
                key: "LOAIGIAYCK",
                "customClass": "pr-2",
                "type": "select",
            },
            {
                "label": "",
                "content": "<b>Bác sĩ phẫu thuật</b>",
                "refreshOnChange": false,
                "key": "TB_1",
                "type": "htmlelement",
                "customClass": "pr-4",
                "input": false,
                "tableView": false,
            },
            getBacsiAllKhoaFormio("MAKHOABSPT", "BACSIPT"),
            {
                "label": "Người nhà",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "<b>Bác sĩ gây mê</b>",
                                others: {
                                    "data": {
                                        "values": singletonObject.danhsachnhanvienFormio
                                    },
                                    defaultValue: singletonObject.userId,
                                },
                                "customClass": "pr-2",
                                "key": "BACSIGMHS",
                                "type": "select",
                            },
                        ],
                        "width": 6,
                    },
                ],
                "customClass": "ml-0 mr-0",
                "key": "THOIGIANTUVAN",
                "type": "columns",
            },
            {
                label: "",
                key: "wrap_benhphu",
                columns: [
                    {
                        "components": [
                            {
                                "tag": "label",
                                "attrs": [
                                    {
                                        "attr": "",
                                        "value": ""
                                    }
                                ],
                                "content": "Chẩn đoán",
                                "key": "htmllabel_benhphu",
                                "type": "htmlelement",
                            },
                        ],
                        "width": 12,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "",
                                "key": "ICD_CHANDOAN",
                                "type": "textfield",
                                customClass: "pr-2",
                                others: {
                                    "placeholder": "ICD",
                                }
                            },
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "",
                                "key": "TENICD_CHANDOAN",
                                "type": "textfield",
                                others: {
                                    "placeholder": "Tên bệnh",
                                }
                            },
                        ],
                        "width": 10,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "",
                                "key": "CHANDOAN",
                                "type": "textarea",
                                "rows": 2,
                                "input": true
                            },
                        ],
                        "width": 12,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Vấn đề đã giải thích với người bệnh/ thân nhân",
                                "customClass": "pr-2",
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Chẩn đoán",
                                                "value": "1"
                                            },
                                            {
                                                "label": "Lý do phẫu thuật/thủ thuật",
                                                "value": "2"
                                            },
                                            {
                                                "label": "Rủi ro, nguy cơ nếu không thực hiện phẫu thuật/thủ thuật",
                                                "value": "3"
                                            },
                                            {
                                                "label": "Kết quả sau phẫu thuật/thủ thuật (Dự kiến)",
                                                "value": "4"
                                            },
                                        ]
                                    },
                                    multiple: true
                                },
                                "key": "THONGTINGIAITHICH",
                                "type": "select",
                            }
                        ],
                        "width": 6,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Kết quả sau phẫu thuật/thủ thuật (Dự kiến)",
                                "key": "KETQUADUKIEN",
                                "type": "textfield",
                                "customClass": "pr-2",
                                others: {
                                    "placeholder": "Nếu có",
                                    // "customConditional": "show = data.THONGTINGIAITHICH && data.THONGTINGIAITHICH.includes('4');"
                                }
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Phương pháp phẫu thuật/thủ thuật dự kiến",
                                "customClass": "pr-2",
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Phẫu thuật mở",
                                                "value": "1"
                                            },
                                            {
                                                "label": "Phẫu thuật nội soi",
                                                "value": "2"
                                            },
                                            {
                                                "label": "Thủ thuật",
                                                "value": "3"
                                            },
                                        ]
                                    },
                                },
                                "key": "PHUONGPHAPPT",
                                "type": "select",
                            }
                        ],
                        "width": 6,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Phương pháp gây mê hồi sức dự kiến",
                                "customClass": "pr-2",
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Mê nội khí quản",
                                                "value": "1"
                                            },
                                            {
                                                "label": "Mê mask thanh quản",
                                                "value": "2"
                                            },
                                            {
                                                "label": "Mê tĩnh mạch",
                                                "value": "3"
                                            },
                                            {
                                                "label": "Tê tủy sống",
                                                "value": "4"
                                            },
                                            {
                                                "label": "Tê ngoài màng cứng",
                                                "value": "5"
                                            },
                                            {
                                                "label": "Tê đám rối thần kinh",
                                                "value": "6"
                                            },
                                            {
                                                "label": "Tiền mê + Tê tại chỗ",
                                                "value": "7"
                                            },
                                            {
                                                "label": "Mê mạch cầu",
                                                "value": "9"
                                            },
                                            {
                                                "label": "Khác",
                                                "value": "8"
                                            },
                                        ]
                                    },
                                    "customConditional": "show = singletonObject.thamSo960622 == '1';",
                                    multiple: true
                                },
                                "key": "PHUONGPHAPGMHS",
                                "type": "select",
                            }
                        ],
                        "width": 6,
                        "size": "md",
                    },

                    {
                        "components": [
                            {
                                "label": "Phương pháp gây mê hồi sức dự kiến",
                                "customClass": "pr-2",
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Mê nội khí quản",
                                                "value": "1"
                                            },
                                            {
                                                "label": "Mê mask thanh quản",
                                                "value": "2"
                                            },
                                            {
                                                "label": "Mê tĩnh mạch",
                                                "value": "3"
                                            },
                                            {
                                                "label": "Tê tủy sống",
                                                "value": "4"
                                            },
                                            {
                                                "label": "Tê ngoài màng cứng",
                                                "value": "5"
                                            },
                                            {
                                                "label": "Tê đám rối thần kinh",
                                                "value": "6"
                                            },
                                            {
                                                "label": "Tiền mê + Tê tại chỗ",
                                                "value": "7"
                                            },
                                            {
                                                "label": "Khác",
                                                "value": "8"
                                            },
                                        ]
                                    },
                                    "customConditional": "show = singletonObject.thamSo960622 == '0';",
                                    multiple: true
                                },
                                "key": "PHUONGPHAPGMHS",
                                "type": "select",
                            }
                        ],
                        "width": 6,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Phương pháp điều trị khác",
                                "customClass": "pr-2",
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Không",
                                                "value": "0"
                                            },
                                            {
                                                "label": "Có",
                                                "value": "1"
                                            },
                                        ]
                                    },
                                },
                                "key": "PHUONGPHAPNGOAIPT",
                                "type": "select",
                            }
                        ],
                        "width": 4,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Cụ thể",
                                "key": "PHUONGPHAPNGOAIPTDT",
                                "type": "textfield",
                                others: {
                                    "customConditional": "show = data.PHUONGPHAPNGOAIPT == 1;"
                                }
                            },
                        ],
                        "width": 8,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Nguy cơ, tai biến có thể xảy ra",
                                "customClass": "pr-2",
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Phản ứng thuốc",
                                                "value": "1"
                                            },
                                            {
                                                "label": "Suy hô hấp - tuần hoàn",
                                                "value": "2"
                                            },
                                            {
                                                "label": "Chảy máu",
                                                "value": "3"
                                            },
                                            {
                                                "label": "Nhiễm trùng",
                                                "value": "4"
                                            },
                                            {
                                                "label": "Tử vong",
                                                "value": "5"
                                            },
                                            {
                                                "label": "Nguy cơ/rủi ro khác",
                                                "value": "6"
                                            },
                                        ]
                                    },
                                    multiple: true,
                                },
                                "key": "NGUYCOTAIBIEN",
                                "type": "select",
                            }
                        ],
                        "width": 4,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Rủi ro khác",
                                "key": "NGUYCOKHAC",
                                "type": "textfield",
                                others: {
                                    "placeholder": "Nếu có",
                                    // "customConditional": "show = data.NGUYCOTAIBIEN == 6;"
                                }
                            },
                        ],
                        "width": 8,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },

            {
                "label": "",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Họ tên thân nhân",
                                "key": "HOTENTHANNHAN",
                                "type": "textfield",
                                "customClass": "pr-2",
                                others: {
                                    defaultValue: thongtinhsba.thongtinbn.NGUOI_LIEN_HE,
                                },
                            },
                        ],
                        "width": 4,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Năm sinh",
                                "key": "NAMSINHTN",
                                "type": "number",
                                "customClass": "pr-2",
                                others: {
                                    "min": 1900,
                                }
                            },
                        ],
                        "width": 4,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Mối quan hệ",
                                "key": "MOIQUANHE",
                                "type": "textfield",
                                "customClass": "pr-2",
                            },
                        ],
                        "width": 4,
                        "size": "md",
                    },
                ],
                "key": "columns_nhin",
                "type": "columns",
                "customClass": "ml-0 mr-0 pb-2",
            },
            {
                "label": "Nguy cơ có thể xảy ra",
                "key": "NGUYCOXAYRA",
                "type": "textfield",
                "customClass": "pr-2",
            },
            {
                "label": "Người nhà",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Ngày tạo phiếu",
                                "key": "NGAY_TAO_PHIEU",
                                "type": "datetime",
                                format: "dd/MM/yyyy",
                                "customClass": "pr-2",
                                enableTime: false,
                                minDate: moment(thongtinhsba.thongtinbn.NGAY_VAO_VIEN, ['DD/MM/YYYY']).format("YYYY-MM-DD"),
                                maxDate: moment().format("YYYY-MM-DD"),
                                "validate": {
                                    "required": true
                                },
                            },
                        ],
                        "width": 6,
                    },
                    {
                        "components": [
                            {
                                "label": "Đại diện",
                                others: {
                                    "data": {
                                        "values": [
                                            {
                                                "label": "Người bệnh",
                                                "value": "1"
                                            },
                                            {
                                                "label": "Thân nhân người bệnh",
                                                "value": "2"
                                            },

                                        ]
                                    },
                                    defaultValue: 1,
                                },
                                "validate": {
                                    "required": true,
                                },
                                key: "DAIDIEN",
                                "customClass": "pr-2",
                                "type": "select",
                            },
                        ],
                        "width": 6,
                    },
                ],
                "customClass": "ml-0 mr-0",
                "key": "THONGTINNGUOINHANHANVIEN",
                "type": "columns",
            },
        ])
        Formio.createForm(document.getElementById('formNhapCamKetPhauThuatGMHS'),
            jsonForm,{}
        ).then(function(form) {
            formCamKetPhauThuatGMHS = form;
            formCamKetPhauThuatGMHS.submission =  {
                data: {
                    ...formCamKetPhauThuatGMHS.submission.data,
                    CHANDOAN:  thongtinhsba.thongtinbn.ICD_NHAPVIEN + "-" + thongtinhsba.thongtinbn.TENBENHCHINH_NHAPVIEN,
                }
            };
            var nhanvienElement = form.getComponent('BACSIPT');
            var khoaElement = form.getComponent('MAKHOABSPT');
            var tenBenhphuElement = form.getComponent('TENICD_CHANDOAN');
            var icdBenhphuElement = form.getComponent('ICD_CHANDOAN');
            var textBenhphuElement = form.getComponent('CHANDOAN');

            $("#"+getIdElmentFormio(form,'ICD_CHANDOAN')).on('keypress', function(event) {
                var mabenhICD = $(this).val();
                if(event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        var splitIcd = data.split("!!!")
                        tenBenhphuElement.setValue(splitIcd[1]);
                        tenBenhphuElement.focus()
                        icdBenhphuElement.setValue(mabenhICD)
                    })
                }
            })
            $("#"+getIdElmentFormio(form,'TENICD_CHANDOAN')).on('keypress', function(event) {
                if(event.keyCode == 13) {
                    var stringIcd = textBenhphuElement.getValue();
                    var mabenhICD = icdBenhphuElement.getValue()
                    if(!stringIcd.includes(mabenhICD)) {
                        textBenhphuElement.setValue( stringIcd + "; (" +mabenhICD.toUpperCase() + ") " + tenBenhphuElement.getValue());
                    }
                    icdBenhphuElement.setValue("")
                    tenBenhphuElement.setValue("")
                    icdBenhphuElement.focus()
                }
            })
            combgridTenICD(getIdElmentFormio(form,'TENICD_CHANDOAN'), function(item) {
                icdBenhphuElement.setValue(item.ICD);
                tenBenhphuElement.setValue(item.MO_TA_BENH_LY);
            });
            getBacsiByKhoaFormio(khoaElement.getValue(), nhanvienElement)
            $("#" + getIdElmentFormio(form, 'MAKHOABSPT')).change(function () {
                if (!$(this).val()) {
                    return;
                }
                getBacsiByKhoaFormio($(this).val(), nhanvienElement)
            })

        });
    }

    function reloadDSCamKetPhauThuatGMHS(){
        var url = "cmu_getlist?url=" + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, "CMU_GET_CAMKETPHAUTHUATGMHS"]);
        $.get(url).done(function(data){
            if (data && data.length > 0) {
                $("#data_camketphauthuatgmhs").html(thongtinhsba.thongtinbn.TEN_PHONGBAN + ' - ' + data[0].NGAY_TAO_PHIEU);
                thongTinCamKetPhauThuatGMHSMoiNhat = data[0];
                if (data[0].KEYSIGN_BSGM || data[0].KEYSIGN_BSPT){
                    $('#edit_single_camketphauthuatgmhs').css('visibility', 'hidden');
                    $('#delete_single_camketphauthuatgmhs').css('visibility', 'hidden');
                }else{
                    $('#handle_icon_camketphauthuatgmhs').css('visibility', 'unset');
                    $('#view_single_camketphauthuatgmhs').css('visibility', 'unset');
                    $('#edit_single_camketphauthuatgmhs').css('visibility', 'unset');
                    $('#delete_single_camketphauthuatgmhs').css('visibility', 'unset');
                }
            } else  {
                $("#data_camketphauthuatgmhs").html('Không có dữ liệu');
                $('#handle_icon_camketphauthuatgmhs').css('visibility', 'hidden');
            }
        });
        $("#list_camketphauthuatgmhs").jqGrid('setGridParam', {
            datatype: 'json',
            url: url
        }).trigger('reloadGrid')
        hideLoaderIntoWrapId("list_ttcs-bdcdct-wrap");
        // showFormCamKetPhauThuatGMHS();
    }

    function themCamKetPhauThuatGMHS() {
        showSelfLoading("camketphauthuatgmhs_luu");
        formCamKetPhauThuatGMHS.emit("checkValidity");
        if (!formCamKetPhauThuatGMHS.checkValidity(null, false, null, true)) {
            hideSelfLoading("camketphauthuatgmhs_luu");
            return;
        }
        var actionUrl;
        var url;
        var dataSubmit = formCamKetPhauThuatGMHS.submission.data;
        console.log(dataSubmit)
        actionUrl = "cmu_post";
        url = [
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.MA_BENH_NHAN,
            dataSubmit.LOAIGIAYCK,
            dataSubmit.DAIDIEN,
            dataSubmit.MAKHOABSPT,
            dataSubmit.BACSIPT,
            dataSubmit.BACSIGMHS,
            dataSubmit.CHANDOAN,
            dataSubmit.THONGTINGIAITHICH,
            dataSubmit.KETQUADUKIEN,
            dataSubmit.PHUONGPHAPPT,
            dataSubmit.PHUONGPHAPGMHS,
            dataSubmit.PHUONGPHAPNGOAIPT,
            dataSubmit.PHUONGPHAPNGOAIPTDT,
            dataSubmit.NGUYCOTAIBIEN,
            dataSubmit.NGUYCOKHAC,
            dataSubmit.HOTENTHANNHAN,
            dataSubmit.NAMSINHTN,
            dataSubmit.MOIQUANHE,
            dataSubmit.NGUYCOXAYRA,
            moment(dataSubmit.NGAY_TAO_PHIEU).format("DD/MM/YYYY"),
            singletonObject.userId,
            "CMU_CAMKETPHAUTHUATGMHS_INSERT"
        ];

        $.post(actionUrl, {
            url: url.join('```')
        }).done(function (data) {
            if(data > 0){
                notifiToClient('Green', 'Thêm phiếu thành công');
                $("#modalFormCamKetPhauThuatGMHS").modal("hide");
                // luuLogHSBAChinhSuaFormio({}, {...dataSubmit},"PHIEUCAMKETPHAUTHUATGMHS", keyLuuLogCamKetPhauThuatGMHS);
                var noidung = ["Số phiếu:"+ data]
                for (const key in dataSubmit) {
                    noidung.push(formCamKetPhauThuatGMHS.getComponent(key).label.trim(":") + ": " + getValueOfFormIO(formCamKetPhauThuatGMHS.getComponent(key)));
                }
                var dataLog = {
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: LOGHSBALOAI.CAMKETPHAUTHUATGMHS.KEY,
                    NOIDUNGBANDAU: "",
                    NOIDUNGMOI: noidung.join("; "),
                    USERID: singletonObject.userId,
                    ACTION: LOGHSBAACTION.INSERT.KEY,
                }
                luuLogHSBATheoBN(dataLog);
            } else {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }
        }).fail(function(error) {
            notifiToClient("Red",MESSAGEAJAX.ERROR);
        }).always(function() {
            hideSelfLoading("camketphauthuatgmhs_luu");
            setTimeout(function(){
                reloadDSCamKetPhauThuatGMHS();
            })
        });
    }

    function updateCamKetPhauThuatGMHS() {
        showSelfLoading("camketphauthuatgmhs_luu");
        formCamKetPhauThuatGMHS.emit("checkValidity");
        if (!formCamKetPhauThuatGMHS.checkValidity(null, false, null, true)) {
            hideSelfLoading("camketphauthuatgmhs_luu");
            return;
        }
        var actionUrl;
        var url;
        var dataSubmit = formCamKetPhauThuatGMHS.submission.data;
        actionUrl = "cmu_post";
        url = [
            thongTinCamKetPhauThuatGMHSTruocChinhSua.ID,
            singletonObject.dvtt,
            dataSubmit.LOAIGIAYCK,
            dataSubmit.DAIDIEN,
            dataSubmit.MAKHOABSPT,
            dataSubmit.BACSIPT,
            dataSubmit.BACSIGMHS,
            dataSubmit.CHANDOAN,
            dataSubmit.THONGTINGIAITHICH,
            dataSubmit.KETQUADUKIEN,
            dataSubmit.PHUONGPHAPPT,
            dataSubmit.PHUONGPHAPGMHS,
            dataSubmit.PHUONGPHAPNGOAIPT,
            dataSubmit.PHUONGPHAPNGOAIPTDT,
            dataSubmit.NGUYCOTAIBIEN,
            dataSubmit.NGUYCOKHAC,
            dataSubmit.HOTENTHANNHAN,
            dataSubmit.NAMSINHTN,
            dataSubmit.MOIQUANHE,
            dataSubmit.NGUYCOXAYRA,
            moment(dataSubmit.NGAY_TAO_PHIEU).format("DD/MM/YYYY"),
            "CMU_CAMKETPHAUTHUATGMHS_UPDATE"
        ];
        $.post(actionUrl, {
            url: url.join('```')
        }).done(function (data) {
            if(data > 0){
                notifiToClient('Green', 'Cập nhật phiếu thành công');
                $("#modalFormCamKetPhauThuatGMHS").modal("hide");
                // luuLogHSBAChinhSuaFormio({...thongTinCamKetPhauThuatGMHSTruocChinhSua}, {...dataSubmit},"PHIEUCAMKETPHAUTHUATGMHS", keyLuuLogCamKetPhauThuatGMHS);
                var noidungold = []
                var noidungnew = []
                var luutru = ""

                dataSubmit.NGAY_TAO_PHIEU = moment(dataSubmit.NGAY_TAO_PHIEU).format("DD/MM/YYYY")
                var diffObject = findDifferencesBetweenObjects(thongTinCamKetPhauThuatGMHSTruocChinhSua, dataSubmit);
                for (const key in diffObject) {
                    try {
                        luutru = formCamKetPhauThuatGMHS.getComponent(key).label
                        if (luutru) {
                            noidungold.push(luutru.trim(":") + ": " + thongTinCamKetPhauThuatGMHSTruocChinhSua[key]);
                            noidungnew.push(luutru.trim(":") + ": " + getValueOfFormIO(formCamKetPhauThuatGMHS.getComponent(key)));                        }
                    } catch (error) {
                        // console.log("Error: ", key);
                    }
                }
                var dataLog = {
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: LOGHSBALOAI.CAMKETPHAUTHUATGMHS.KEY,
                    NOIDUNGBANDAU: noidungold.join("; "),
                    NOIDUNGMOI: noidungnew.join("; "),
                    USERID: singletonObject.userId,
                    ACTION: LOGHSBAACTION.EDIT.KEY,
                }
                luuLogHSBATheoBN(dataLog);
            } else {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }
        }).fail(function(error) {
            notifiToClient("Red",MESSAGEAJAX.ERROR);
        }).always(function() {
            hideSelfLoading("camketphauthuatgmhs_luu");
            setTimeout(function(){
                reloadDSCamKetPhauThuatGMHS();
            })
        });
    }

    function instanceGridCamKetPhauThuatGMHS(){
        if (!$("#list_camketphauthuatgmhs")[0].grid) {
            $("#list_camketphauthuatgmhs").jqGrid({
                datatype: "local",
                loadonce: false,
                height: 100,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {
                        name: "KYSO",
                        label: "Ký số BSPT",
                        align: 'left',
                        width: 100,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.KEYSIGN_BSPT) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: green">Đã ký</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: red">Chưa ký</span>';
                            }
                        }
                    },
                    {
                        name: "KYSO",
                        label: "Ký số BSGM",
                        align: 'left',
                        width: 100,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.KEYSIGN_BSGM) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: green">Đã ký</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: red">Chưa ký</span>';
                            }
                        }
                    },
                    {label: 'ID',name: 'ID', index: 'ID', width: 50, align: 'center'},
                    {label: 'LOAIGIAYCK',name: 'LOAIGIAYCK', index: 'LOAIGIAYCK', width: 250, align: 'center', hidden: true},
                    {label: 'DAIDIEN',name: 'DAIDIEN', index: 'DAIDIEN', width: 250, align: 'center', hidden: true},
                    {label: 'MAKHOABSPT',name: 'MAKHOABSPT', index: 'MAKHOABSPT', width: 250, align: 'center', hidden: true},
                    {label: 'BACSIPT',name: 'BACSIPT', index: 'BACSIPT', width: 250, align: 'center', hidden: true},
                    {label: 'BACSIGMHS',name: 'BACSIGMHS', index: 'BACSIGMHS', width: 250, align: 'center', hidden: true},
                    {label: 'CHANDOAN',name: 'CHANDOAN', index: 'CHANDOAN', width: 250, align: 'center'},
                    {label: 'THONGTINGIAITHICH',name: 'THONGTINGIAITHICH', index: 'THONGTINGIAITHICH', width: 250, align: 'center', hidden: true},
                    {label: 'KETQUADUKIEN',name: 'KETQUADUKIEN', index: 'KETQUADUKIEN', width: 250, align: 'center', hidden: true},
                    {label: 'PHUONGPHAPPT',name: 'PHUONGPHAPPT', index: 'PHUONGPHAPPT', width: 250, align: 'center', hidden: true},
                    {label: 'PHUONGPHAPGMHS',name: 'PHUONGPHAPGMHS', index: 'PHUONGPHAPGMHS', width: 250, align: 'center', hidden: true},
                    {label: 'PHUONGPHAPNGOAIPT',name: 'PHUONGPHAPNGOAIPT', index: 'PHUONGPHAPNGOAIPT', width: 250, align: 'center', hidden: true},
                    {label: 'PHUONGPHAPNGOAIPTDT',name: 'PHUONGPHAPNGOAIPTDT', index: 'PHUONGPHAPNGOAIPTDT', width: 250, align: 'center', hidden: true},
                    {label: 'NGUYCOTAIBIEN',name: 'NGUYCOTAIBIEN', index: 'NGUYCOTAIBIEN', width: 250, align: 'center', hidden: true},
                    {label: 'NGUYCOKHAC',name: 'NGUYCOKHAC', index: 'NGUYCOKHAC', width: 250, align: 'center', hidden: true},
                    {label: 'HOTENTHANNHAN',name: 'HOTENTHANNHAN', index: 'HOTENTHANNHAN', width: 250, align: 'center', hidden: true},
                    {label: 'NAMSINHTN',name: 'NAMSINHTN', index: 'NAMSINHTN', width: 250, align: 'center', hidden: true},
                    {label: 'MOIQUANHE',name: 'MOIQUANHE', index: 'MOIQUANHE', width: 250, align: 'center', hidden: true},
                    {label: 'NGUYCOXAYRA',name: 'NGUYCOXAYRA', index: 'NGUYCOXAYRA', width: 250, align: 'center', hidden: true},
                    {label: 'Bác sĩ PT',name: 'TEN_BAC_SI_PT', index: 'TEN_BAC_SI_PT', width: 250, align: 'center'},
                    {label: 'Bác sĩ GMHS',name: 'TEN_BAC_SI_GMHS', index: 'TEN_BAC_SI_GMHS', width: 250, align: 'center'},
                    {label: 'Ngày tạo phiếu',name: 'NGAY_TAO_PHIEU', index: 'NGAY_TAO_PHIEU', width: 250, align: 'center'},
                    {name: "KEYSIGN_BSPT", label: "KEYSIGN_BSPT", align: 'center', width: 150, hidden: true},
                    {name: "KEYSIGN_BSGM", label: "KEYSIGN_BSGM", align: 'center', width: 150, hidden: true},
                ],
                rowNum: 1000000,
                caption: "Danh sách phiếu cam kết chấp thuận phẫu thuật, thủ thuật và gây mê hồi sức",
                onRightClickRow: function (id1) {
                    if (id1) {
                        var ret = getThongtinRowSelected("list_camketphauthuatgmhs");
                        var items = {
                            "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                        }
                        $.contextMenu('destroy', '#list_camketphauthuatgmhs tr');
                        if (ret.KEYSIGN_BSPT && ret.KEYSIGN_BSGM){
                            var daygd, daytk;
                            getFilesign769("PHIEU_NOITRU_CAMKETPHAUTHUATGMHS_BSPT", ret.ID, -1, singletonObject.dvtt,
                                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                                    if (dataKySo.length > 0) {
                                        daygd = moment(dataKySo[0].CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                                    }
                                });
                            getFilesign769("PHIEU_NOITRU_CAMKETPHAUTHUATGMHS_BSGM", ret.ID, -1, singletonObject.dvtt,
                                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                                    if (dataKySo.length > 0) {
                                        daytk = moment(dataKySo[0].CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                                    }
                                });
                            if (daygd.isAfter(daytk)){
                                items = {
                                    "huykysopt": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số BSPT</p>'},
                                    ...items
                                }
                            }else{
                                items = {
                                    "huykysogm": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số BSGM</p>'},
                                    ...items
                                }
                            }
                        }else if (ret.KEYSIGN_BSPT){
                            items = {
                                "huykysopt": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số BSPT</p>'},
                                "kysogm": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Ký số BSGM</p>'},
                                ...items
                            }
                        }else if (ret.KEYSIGN_BSGM){
                            items = {
                                "huykysogm": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số BSGM</p>'},
                                "kysopt": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Ký số BSPT</p>'},
                                ...items
                            }
                        }else{
                            items = {
                                "kysopt": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số BSPT</p>'},
                                "kysogm": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Ký số BSGM</p>'},
                                ...items,
                                "sua": {name: '<p><i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Sửa</p>'},
                                "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'},
                            }
                        }
                        $.contextMenu({
                            selector: '#list_camketphauthuatgmhs tr',
                            callback: function (key, options) {
                                var id = $("#list_camketphauthuatgmhs").jqGrid('getGridParam', 'selrow');
                                var ret = $("#list_camketphauthuatgmhs").jqGrid('getRowData', id);
                                var params = {
                                    ID: ret.ID,
                                }
                                getUrlCamKetPhauThuatGMHS(params).then(objReturn => {
                                    if (objReturn.isError == 0) {
                                        if (key == "kysopt") {
                                            // if(ret.BAC_SI != singletonObject.userId) {
                                            //     return notifiToClient("Red", MESSAGEAJAX.PERMISSION);
                                            // }
                                            thongTinCamKetPhauThuatGMHSTruocChinhSua = ret
                                            previewAndSignPdfDefaultModal({
                                                url: objReturn.url,
                                                idButton: 'camketphauthuatgmhs_kysopt_action',
                                            }, function(){

                                            });
                                        }
                                        if (key == "kysogm") {
                                            // if(ret.DIEU_DUONG != singletonObject.userId) {
                                            //     return notifiToClient("Red", MESSAGEAJAX.PERMISSION);
                                            // }
                                            thongTinCamKetPhauThuatGMHSTruocChinhSua = ret
                                            previewAndSignPdfDefaultModal({
                                                url: objReturn.url,
                                                idButton: 'camketphauthuatgmhs_kysogm_action',
                                            }, function(){

                                            });
                                        }
                                        if (key == "xem") {
                                            previewPdfDefaultModal(objReturn.url, 'preview_giaycdphauthuatgm');
                                        }
                                    } else {
                                        notifiToClient("Red", objReturn.message);
                                    }
                                }).catch(error => {
                                    notifiToClient("Red", error.message || "Lỗi không xác định");
                                });
                                if (key == "huykysopt") {
                                    // if(ret.BAC_SI != singletonObject.userId) {
                                    //     return notifiToClient("Red", MESSAGEAJAX.PERMISSION);
                                    // }
                                    confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                        huykysoFilesign769("PHIEU_NOITRU_CAMKETPHAUTHUATGMHS_BSPT", ret.ID, singletonObject.userId, singletonObject.dvtt,
                                            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                                                reloadDSCamKetPhauThuatGMHS();
                                            })
                                    }, function () {

                                    })
                                }
                                if (key == "huykysogm") {
                                    // if(ret.DIEU_DUONG != singletonObject.userId) {
                                    //     return notifiToClient("Red", MESSAGEAJAX.PERMISSION);
                                    // }
                                    confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                        huykysoFilesign769("PHIEU_NOITRU_CAMKETPHAUTHUATGMHS_BSGM", ret.ID, singletonObject.userId, singletonObject.dvtt,
                                            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                                                reloadDSCamKetPhauThuatGMHS();
                                            })
                                    }, function () {

                                    })
                                }
                                if (key== "sua"){
                                    showUpdateCamKetPhauThuatGMHS(ret)
                                }
                                if (key == "xoa") {
                                    deleteCamKetPhauThuatGMHS(ret)
                                }
                            },
                            items: items
                        });
                    }
                }

            });
            $("#list_camketphauthuatgmhs").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
        }
    }

    function xemCamKetPhauThuatGMHS(ret){
        var params = {
            ID: ret.ID,
        }
        getUrlCamKetPhauThuatGMHS(params).then(objReturn => {
            if (objReturn.isError == 0) {
                previewPdfDefaultModal(objReturn.url, 'preview_giaycdphauthuatgm');
            } else {
                notifiToClient("Red", objReturn.message);
            }
        }).catch(error => {
            notifiToClient("Red", error.message || "Lỗi không xác định");
        });
    }

    function showUpdateCamKetPhauThuatGMHS(ret){
        $("#modalFormCamKetPhauThuatGMHS").modal("show");
        $("#camketphauthuatgmhs_luu").attr("data-action", "CAP_NHAT");
        addTextTitleModal('titleFormCamKetPhauThuatGMHS', "Phiếu cam kết chấp thuận phẫu thuật, thủ thuật và gây mê hồi sức");
        thongTinCamKetPhauThuatGMHSTruocChinhSua = ret
        formCamKetPhauThuatGMHS.submission =  {
            data: {
                ...ret,
                THONGTINGIAITHICH:  ret.THONGTINGIAITHICH ? ret.THONGTINGIAITHICH.split(","): [],
                PHUONGPHAPGMHS:  ret.PHUONGPHAPGMHS? ret.PHUONGPHAPGMHS.split(","): [],
                NGUYCOTAIBIEN:  ret.NGUYCOTAIBIEN? ret.PHUONGPHAPGMHS.split(","): [],
                NGAY_TAO_PHIEU:  (ret.NGAY_TAO_PHIEU? moment(ret.NGAY_TAO_PHIEU, ['DD/MM/YYYY']): moment()).toISOString(),
            }
        };
    }

    function deleteCamKetPhauThuatGMHS(ret){
        var maGiay = ret.ID;
        thongTinCamKetPhauThuatGMHSTruocChinhSua = ret
        confirmToClient("Bạn có chắc chắn muốn xóa phiếu này?", function() {
            var arr = [maGiay, singletonObject.dvtt]
            var url = "cmu_post_CMU_CAMKETPHAUTHUATGMHS_DELETE";
            $.post(url, {
                url: arr.join("```")
            }).done(function (data) {
                if (data === "1") {
                    notifiToClient("Green", MESSAGEAJAX.DEL_SUCCESS)
                    reloadDSCamKetPhauThuatGMHS();
                    var formData = { ...formCamKetPhauThuatGMHS.submission.data}
                    var noidung = ["Số phiếu:"+ thongTinCamKetPhauThuatGMHSTruocChinhSua.ID]
                    for (const key in formData) {
                        try {
                            var label = formCamKetPhauThuatGMHS.getComponent(key).label;
                            if (label) {
                                noidung.push(formCamKetPhauThuatGMHS.getComponent(key).label + ": " + getValueOfFormIO(formCamKetPhauThuatGMHS.getComponent(key)));
                            }
                        } catch (error) {
                            // console.log("Error: ", error);
                        }
                    }
                    var dataLog = {
                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                        LOAI: LOGHSBALOAI.CAMKETPHAUTHUATGMHS.KEY,
                        NOIDUNGBANDAU: noidung.join("; "),
                        NOIDUNGMOI: "",
                        USERID: singletonObject.userId,
                        ACTION: LOGHSBAACTION.DELETE.KEY,
                    }
                    luuLogHSBATheoBN(dataLog);

                } else {
                    notifiToClient("Red", MESSAGEAJAX.ERROR)
                }
            }).fail(function() {
                notifiToClient("Red", MESSAGEAJAX.ERROR)
            })
        }, function () {

        })
    }

    $("#camketphauthuatgmhs_mau").click(function() {
        var element = $("#mau_danhsachmaujson_wrap");
        element.attr("function-add", 'insertMauHSBACAMKETPHAUTHUATGMHS');
        element.attr("function-chinhsua", 'editMauHSBACAMKETPHAUTHUATGMHS');
        element.attr("function-select", 'selectMauHSBACAMKETPHAUTHUATGMHS');
        element.attr("function-getdata", 'getdataMauHSBACAMKETPHAUTHUATGMHS');
        element.attr("function-validate", 'formioHSBACAMKETPHAUTHUATGMHSValidate');
        element.attr("data-key", 'MAUCAMKETPHAUTHUATGMHS');
        $("#modalMauChungJSON").modal("show");
        $.loadDanhSachMauChungJSON('MAUCAMKETPHAUTHUATGMHS')
    })

    $.extend({
        insertMauHSBACAMKETPHAUTHUATGMHS: function () {
            generateFormMauCAMKETPHAUTHUATGMHS({})
        },
        editMauHSBACAMKETPHAUTHUATGMHS: function (rowSelect) {
            var json = JSON.parse(rowSelect.NOIDUNG);
            var dataMau = {}
            json.forEach(function(item) {
                dataMau[item.key] = item.value
            })

            generateFormMauCAMKETPHAUTHUATGMHS({
                ID: rowSelect.ID,
                TENMAU: rowSelect.TENMAU,
                ...dataMau
            })
        },
        selectMauHSBACAMKETPHAUTHUATGMHS: function (rowSelect) {
            var json = JSON.parse(rowSelect.NOIDUNG);
            var dataMau = {
                ...formKyThuatPHCN.submission.data,
            }
            json.forEach(function(item) {
                dataMau[item.key] = item.value
            })
            formKyThuatPHCN.submission = {
                data: {
                    ...dataMau
                }
            }
            $("#modalMauChungJSON").modal("hide");
        },
        getdataMauHSBACAMKETPHAUTHUATGMHS: function () {
            var objectNoidung = [];
            getObjectMauCAMKETPHAUTHUATGMHS().forEach(function(item) {
                if(item.key != 'ID' && item.key != 'TENMAU') {
                    objectNoidung.push({
                        "label": item.label,
                        "value": formioMauHSBA.submission.data[item.key],
                        "key": item.key,
                    })
                }
            })
            return {
                ID: formioMauHSBA.submission.data.ID,
                TENMAU: formioMauHSBA.submission.data.TENMAU,
                NOIDUNG: JSON.stringify(objectNoidung),
                KEYMAUCHUNG: 'MAUCAMKETPHAUTHUATGMHS'
            };
        },
        formioHSBACAMKETPHAUTHUATGMHSValidate: function() {
            formioMauHSBA.emit("checkValidity");
            if (!formioMauHSBA.checkValidity(null, false, null, true)) {
                return false;
            }
            return true;
        },
    })
    function generateFormMauCAMKETPHAUTHUATGMHS(dataForm) {
        var jsonForm = getJSONObjectForm(getObjectMauCAMKETPHAUTHUATGMHS());
        Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
            jsonForm,{}
        ).then(function(form) {
            formioMauHSBA = form;
            var tenBenhphuElement = form.getComponent('TENICD_CHANDOAN');
            var icdBenhphuElement = form.getComponent('ICD_CHANDOAN');
            var textBenhphuElement = form.getComponent('CHANDOAN');
            $("#"+getIdElmentFormio(form,'ICD_CHANDOAN')).on('keypress', function(event) {
                var mabenhICD = $(this).val();
                if(event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        var splitIcd = data.split("!!!")
                        tenBenhphuElement.setValue(splitIcd[1]);
                        tenBenhphuElement.focus()
                        icdBenhphuElement.setValue(mabenhICD)
                    })
                }
            })
            $("#"+getIdElmentFormio(form,'TENICD_CHANDOAN')).on('keypress', function(event) {
                if(event.keyCode == 13) {
                    var stringIcd = textBenhphuElement.getValue();
                    var mabenhICD = icdBenhphuElement.getValue()
                    if(!stringIcd.includes(mabenhICD)) {
                        textBenhphuElement.setValue( stringIcd + "; (" +mabenhICD.toUpperCase() + ") " + tenBenhphuElement.getValue());
                    }
                    icdBenhphuElement.setValue("")
                    tenBenhphuElement.setValue("")
                    icdBenhphuElement.focus()
                }
            })
            combgridTenICD(getIdElmentFormio(form,'TENICD_CHANDOAN'), function(item) {
                icdBenhphuElement.setValue(item.ICD);
                tenBenhphuElement.setValue(item.MO_TA_BENH_LY);
            });
            formioMauHSBA.submission = {
                data: {
                    ...dataForm
                }
            }
        });
    };
    function getObjectMauCAMKETPHAUTHUATGMHS() {
        return [
            {
                "label": "ID",
                "key": "ID",
                "type": "textfield",
                others: {
                    hidden: true
                }
            },
            {
                "label": "Tên mẫu",
                "key": "TENMAU",
                "type": "textarea",
                validate: {
                    required: true
                },
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Loại giấy cam đoan",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10,
                    "data": {
                        "values": [
                            {
                                "label": "Cấp cứu",
                                "value": "1"
                            },
                            {
                                "label": "Bán cấp",
                                "value": "2"
                            },
                            {
                                "label": "Chương trình/ Phiên",
                                "value": "3"
                            },

                        ]
                    },
                },
                key: "LOAIGIAYCK",
                "type": "select",
            },
            {
                label: "",
                key: "wrap_benhphu",
                columns: [
                    {
                        "components": [
                            {
                                "tag": "label",
                                "attrs": [
                                    {
                                        "attr": "",
                                        "value": ""
                                    }
                                ],
                                "content": "Chẩn đoán",
                                "key": "htmllabel_benhphu",
                                "type": "htmlelement",
                            },
                        ],
                        "width": 12,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "",
                                "key": "ICD_CHANDOAN",
                                "type": "textfield",
                                customClass: "pr-2",
                                others: {
                                    "placeholder": "ICD",
                                }
                            },
                        ],
                        "width": 2,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "",
                                "key": "TENICD_CHANDOAN",
                                "type": "textfield",
                                others: {
                                    "placeholder": "Tên bệnh",
                                }
                            },
                        ],
                        "width": 10,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "",
                                "key": "CHANDOAN",
                                "type": "textarea",
                                "rows": 2,
                                "input": true
                            },
                        ],
                        "width": 12,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                "label": "Phương pháp phẫu thuật/thủ thuật dự kiến",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10,
                    "data": {
                        "values": [
                            {
                                "label": "Phẫu thuật mở",
                                "value": "1"
                            },
                            {
                                "label": "Phẫu thuật nội soi",
                                "value": "2"
                            },
                            {
                                "label": "Thủ thuật",
                                "value": "3"
                            },
                        ]
                    },
                },
                "key": "PHUONGPHAPPT",
                "type": "select",
            },
            {
                "label": "Phương pháp điều trị khác",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10,
                    "data": {
                        "values": [
                            {
                                "label": "Không",
                                "value": "0"
                            },
                            {
                                "label": "Có",
                                "value": "1"
                            },
                        ]
                    },
                },
                "key": "PHUONGPHAPNGOAIPT",
                "type": "select",
            },
            {
                "label": "Cụ thể",
                "key": "PHUONGPHAPNGOAIPTDT",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10,
                    "customConditional": "show = data.PHUONGPHAPNGOAIPT == 1;"
                }
            },
            {
                "label": "Nguy cơ có thể xảy ra",
                "key": "NGUYCOXAYRA",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10,
                }
            },
        ];
    }

    // Bổ sung Mẫu chuẩn bị v2
    $("#mauChuanBiPhieuCamKetPhauThuatGMHS").click(function() {
        let element = $("#mau_danhsachmaujson_wrap");
        element.attr("function-add", 'insertMauCBPhieuCamKetPhauThuatGMHS');
        element.attr("function-chinhsua", 'editMauCBPhieuCamKetPhauThuatGMHS');
        element.attr("function-select", 'selectMauCBPhieuCamKetPhauThuatGMHS');
        element.attr("function-getdata", 'getdataMauCBPhieuCamKetPhauThuatGMHS');
        element.attr("function-validate", 'formioCBPhieuCamKetPhauThuatGMHSValidate');
        element.attr("data-key", 'MAUCBPHIEUCAMKETPHAUTHUATGMHS');
        $("#modalMauChungJSON").modal("show");
        $.loadDanhSachMauChungJSON('MAUCBPHIEUCAMKETPHAUTHUATGMHS')
    }); $.extend({
        insertMauCBPhieuCamKetPhauThuatGMHS: function () {
            generateFormMauCBPhieuCamKetPhauThuatGMHS({})
        },
        editMauCBPhieuCamKetPhauThuatGMHS: function (rowSelect) {
            let json = JSON.parse(rowSelect.NOIDUNG);
            let dataMau = {}
            json.forEach(function(item) {
                dataMau[item.key] = item.value
            })
            generateFormMauCBPhieuCamKetPhauThuatGMHS({
                ID: rowSelect.ID,
                TENMAU: rowSelect.TENMAU,
                ...dataMau
            })
        },
        selectMauCBPhieuCamKetPhauThuatGMHS: function (rowSelect) {
            let json = JSON.parse(rowSelect.NOIDUNG);
            json.forEach(function(item) {
                $(`#formNhapCamKetPhauThuatGMHS [name="data[${item.key}]"]`).val(item.value)
                formCamKetPhauThuatGMHS.data[item.key] = item.value
            })
            $("#modalMauChungJSON").modal("hide");
        },
        getdataMauCBPhieuCamKetPhauThuatGMHS: function () {
            let objectNoidung = [];
            getObjectMauCBPhieuCamKetPhauThuatGMHS().forEach(function(item) {
                if (item.key !== 'ID' && item.key !== 'TENMAU') {
                    objectNoidung.push({
                        "label": item.label,
                        "value": formioMauHSBA.submission.data[item.key],
                        "key": item.key,
                    })
                }
            })
            return {
                ID: formioMauHSBA.submission.data.ID,
                TENMAU: formioMauHSBA.submission.data.TENMAU,
                NOIDUNG: JSON.stringify(objectNoidung),
                KEYMAUCHUNG: 'MAUCBPHIEUCAMKETPHAUTHUATGMHS'
            };
        },
        formioCBPhieuCamKetPhauThuatGMHSValidate: function() {
            formioMauHSBA.emit("checkValidity");
            return formioMauHSBA.checkValidity(null, false, null, true);

        },
    });

    function generateFormMauCBPhieuCamKetPhauThuatGMHS(dataForm) {
        let jsonForm = getJSONObjectForm(getObjectMauCBPhieuCamKetPhauThuatGMHS());
        Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
            jsonForm,{}
        ).then(function(form) {
            formioMauHSBA = form;
            formioMauHSBA.submission = { data: { ...dataForm }}
        });
    }

    function getObjectMauCBPhieuCamKetPhauThuatGMHS() {
        return [
            {
                "label": "ID",
                "key": "ID",
                "type": "textfield",
                others: {
                    hidden: true
                }
            },
            {
                "label": "Tên mẫu",
                "key": "TENMAU",
                "type": "textarea",
                validate: {
                    required: true
                },
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Kết quả sau phẫu thuật/thủ thuật (Dự kiến)",
                "key": "KETQUADUKIEN",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Rủi ro khác",
                "key": "NGUYCOKHAC",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Họ tên thân nhân",
                "key": "HOTENTHANNHAN",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Năm sinh",
                "key": "NAMSINHTN",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Mối quan hệ",
                "key": "MOIQUANHE",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Nguy cơ có thể xảy ra",
                "key": "NGUYCOXAYRA",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            }
        ];
    }
})