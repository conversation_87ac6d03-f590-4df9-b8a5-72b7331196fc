package BaocaoBHXH.xmlCV130;

import BaocaoBHXH.xmlCV130.dto.*;
import oracle.jdbc.OracleTypes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.CallableStatementCreator;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;
import tienich.jdbc.HisCallTemplate;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.CallableStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created at 09:20:06 February 22, 2023,
 */
@Component
public class XmlCV130DAOImpl implements XmlCV130DAO {

    @Autowired
    @Resource(name = "dataSource_synchronization")
    private DataSource dataSourceSYN;

    @Autowired
    @Resource(name = "dataSourceMNG")
    DataSource dataSourceMNG;

    private TransactionTemplate transactionTemplate;

    private TransactionTemplate prepareTransactionTemplate() {
        if (transactionTemplate == null) {
            DataSourceTransactionManager transactionManager = new DataSourceTransactionManager(dataSourceSYN);
            transactionTemplate = new TransactionTemplate(transactionManager);
        }
        return transactionTemplate;
    }

    private <T> List<T> callFunction(String functionName, Object[] inputParams, int[] paramTypes, RowMapper<T> rowMapper) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceSYN);
        String sql = "{? = call " + functionName + "(" + String.join(",", Collections.nCopies(inputParams.length, "?")) + ")}";
        return jdbcTemplate.execute(
                (CallableStatementCreator) con -> {
                    CallableStatement cs = con.prepareCall(sql);
                    cs.registerOutParameter(1, OracleTypes.CURSOR);
                    for (int i = 0; i < inputParams.length; i++) {
                        cs.setObject(i + 2, inputParams[i], paramTypes[i]);
                    }
                    return cs;
                },
                cs -> {
                    cs.execute();
                    List<T> resultList = new ArrayList<>();
                    ResultSet rs = (ResultSet) cs.getObject(1);
                    int rowNum = 0;
                    while (rs.next()) {
                        resultList.add(rowMapper.mapRow(rs, rowNum++));
                    }
                    return resultList;
                });
    }

    private <T> List<T> getXmlCV130List(XuatXmlCv130Option option, String loaiXML, Class<T> xmlClass) {
        if ("0".equals(option.getCoBaoHiem()) && option.getDsBangLayDuLieu() != null && !option.getDsBangLayDuLieu().contains(loaiXML)) {
            return new ArrayList<>();
        }
        Object[] inputParams = new Object[]{
                loaiXML,
                option.getSoVaoVien(),
                option.getSoVaoVienDt(),
                option.getLoaiKcb(),
                option.getDvtt(),
                option.getCoBaoHiem(),
                option.getTuNgay(),
                option.getDenNgay(),
                option.getDoiTuong()
        };
        int[] paramTypes = new int[]{
                OracleTypes.VARCHAR,
                option.getSoVaoVien() == null ? OracleTypes.NULL : OracleTypes.NUMBER,
                option.getSoVaoVienDt() == null ? OracleTypes.NULL : OracleTypes.NUMBER,
                option.getLoaiKcb() == null ? OracleTypes.NULL : OracleTypes.NUMBER,
                option.getDvtt() == null ? OracleTypes.NULL : OracleTypes.VARCHAR,
                option.getCoBaoHiem() == null ? OracleTypes.NULL : OracleTypes.NUMBER,
                option.getTuNgay() == null ? OracleTypes.NULL : OracleTypes.VARCHAR,
                option.getDenNgay() == null ? OracleTypes.NULL : OracleTypes.VARCHAR,
                option.getDoiTuong() == null ? OracleTypes.NULL : OracleTypes.VARCHAR,
        };
        RowMapper<T> rowMapper = StringBeanPropertyRowMapper.newInstance(xmlClass, true);
        TransactionTemplate template = prepareTransactionTemplate();
        // Phải thực hiện trong transaction vì bảng tạm xoá hết dữ liệu sau khi commit
        return template.execute(status -> callFunction("XML_CV130", inputParams, paramTypes, rowMapper));
    }

    @Override
    public List<CheckIn> getXmlCheckInCV130List(XuatXmlCv130Option option) {
        return getXmlCV130List(option, "0", CheckIn.class);
    }

    @Override
    public List<Xml1> getXml1CV130List(XuatXmlCv130Option option) {
        return getXmlCV130List(option, "1", Xml1.class);
    }

    @Override
    public List<Xml2> getXml2CV130List(XuatXmlCv130Option option) {
        return getXmlCV130List(option, "2", Xml2.class);
    }

    @Override
    public List<Xml3> getXml3CV130List(XuatXmlCv130Option option) {
        return getXmlCV130List(option, "3", Xml3.class);
    }

    @Override
    public List<Xml4> getXml4CV130List(XuatXmlCv130Option option) {
        return getXmlCV130List(option, "4", Xml4.class);
    }

    @Override
    public List<Xml5> getXml5CV130List(XuatXmlCv130Option option) {
        return getXmlCV130List(option, "5", Xml5.class);
    }

    @Override
    public List<Xml6> getXml6CV130List(XuatXmlCv130Option option) {
        return getXmlCV130List(option, "6", Xml6.class);
    }

    @Override
    public List<Xml7> getXml7CV130List(XuatXmlCv130Option option) {
        return getXmlCV130List(option, "7", Xml7.class);
    }

    @Override
    public List<Xml8> getXml8CV130List(XuatXmlCv130Option option) {
        return getXmlCV130List(option, "8", Xml8.class);
    }

    @Override
    public List<Xml9> getXml9CV130List(XuatXmlCv130Option option) {
        return getXmlCV130List(option, "9", Xml9.class);
    }

    @Override
    public List<Xml10> getXml10CV130List(XuatXmlCv130Option option) {
        return getXmlCV130List(option, "10", Xml10.class);
    }

    @Override
    public List<Xml11> getXml11CV130List(XuatXmlCv130Option option) {
        return getXmlCV130List(option, "11", Xml11.class);
    }

    @Override
    public List<Xml12> getXml12CV130List(XuatXmlCv130Option option) {
        return getXmlCV130List(option, "12", Xml12.class);
    }

    @Override
    public List<Xml13> getXml13CV130List(XuatXmlCv130Option option) {
        return getXmlCV130List(option, "13", Xml13.class);
    }

    @Override
    public List<Xml14> getXml14CV130List(XuatXmlCv130Option option) {
        return getXmlCV130List(option, "14", Xml14.class);
    }

    @Override
    public List<Xml15> getXml15CV130List(XuatXmlCv130Option option) {
        return getXmlCV130List(option, "15", Xml15.class);
    }

    @Override
    public List layDsBangMapMaTTDV(String dvtt) {
        String sql = "call DS_BANG_MAP_MA_TTDV(?)#c,s";
        VSC.jdbc.JdbcTemplate jdbcTemplate = new VSC.jdbc.JdbcTemplate(dataSourceSYN);
        return jdbcTemplate.queryForList(sql, new Object[]{dvtt});
    }

    @Override
    public List layDsDaMapMaTTDV(String dvtt) {
        String sql = "call DS_DA_MAP_MA_TTDV(?)#c,s";
        VSC.jdbc.JdbcTemplate jdbcTemplate = new VSC.jdbc.JdbcTemplate(dataSourceSYN);
        return jdbcTemplate.queryForList(sql, new Object[]{dvtt});
    }

    @Override
    public String themMapMaTTDV(MapMaTTDVObj map){
        String sql = "call THEM_MAP_MA_TTDV(?,?,?,?,?,?,?,?,?,?,?)#l,s,l,s,l,s,s,s,s,l,s,l";
        VSC.jdbc.JdbcTemplate jdbcTemplate = new VSC.jdbc.JdbcTemplate(dataSourceSYN);
        return jdbcTemplate.queryForObject(sql, new Object[]{
                        map.dvtt,
                        map.maBang,
                        map.tenBang,
                        map.maNhanVien,
                        map.tenNhanVien,
                        map.maTTDV,
                        map.ngayBatDau,
                        map.ngayKetThuc,
                        map.nhanVienThucHien,
                        map.ngayGioThucHien,
                        map.nhanVienThayThe
                },
                String.class);
    }

    @Override
    public int xoaMapMaTTDV(String dvtt, int stt, String nhanvienthuchien, String ngaygiothuchien) {
        String sql = "call XOA_MAP_MA_TTDV(?,?,?,?)#l,s,l,s,s";
        VSC.jdbc.JdbcTemplate jdbcTemplate = new VSC.jdbc.JdbcTemplate(dataSourceSYN);
        return jdbcTemplate.queryForInt(sql, new Object[]{dvtt, stt, nhanvienthuchien, ngaygiothuchien});
    }

    @Override
    public Map getSignedFileXML130(String loaiXML, XuatXmlCv130Option option) {
        return HisCallTemplate.instance.queryMap(dataSourceSYN, "XML_130_GET_SIGNED_FILE",
                loaiXML,
                option.getSoVaoVien(),
                option.getSoVaoVienDt(),
                option.getLoaiKcb(),
                option.getDvtt()
        );
    }

    @Override
    public String layMaDonViMoi(String sovaovien, String sovaovien_dt, String dvtt, String loai_kcb) {
        String sql = "call F_MA_DONVI_MOI(?,?,?,?)#s,s,s,s,s";
        VSC.jdbc.JdbcTemplate jdbcTemplate = new VSC.jdbc.JdbcTemplate(dataSourceMNG);
        return jdbcTemplate.queryForObject(sql, new Object[]{sovaovien, sovaovien_dt, dvtt, loai_kcb}, String.class);
    }
}
