create or replace FUNCTION cmu_getdsbnhentaikham (
    p_dvtt VARCHAR2
) RETURN SYS_REFCURSOR IS
    cur SYS_REFCURSOR;
BEGIN
OPEN cur FOR SELECT
                     bn.so_dien_thoai   so_dien_thoai,
                     CASE
                         WHEN p_dvtt = 96066 THEN
                             'Xin nhac Anh/Chi lich tai kham ngay '
                             || TO_CHAR(kb.ngay_hen, 'DD/MM/YYYY')
                             || ' tai Khoa kham benh.'
                             || ' Rat mong Anh/Chi sap xep den dung hen de viec cham soc suc khoe duoc tot nhat. Xin cam on!'
                         ELSE
                                 CASE
                                     WHEN p_dvtt = 96172 THEN
                                         'PKSG TAMDUC kinh chao quy khach. Moi '
                                     ELSE
                                         'Lich tai kham cua Quy benh nhan '
END
|| cmu_replacestring_vn(bn.ten_benh_nhan)
                                 ||
                                 CASE
                                     WHEN p_dvtt = 96034 THEN
                                         ' tai BV Nam Can vao ngay '
                                     WHEN p_dvtt = 96019 THEN
                                         ' tai BV Cai Nuoc vao ngay '
                                     WHEN p_dvtt = 96172 THEN
                                         ' tai kham tai PKDK Sai Gon Tam Duc '
                                     ELSE
                                         ''
END
|| TO_CHAR(kb.ngay_hen, 'DD/MM/YYYY')
                                 ||
                                 CASE
                                     WHEN p_dvtt = 96172 THEN
                                         ' Cam on quy khach da tin tuong va su dung dich vu cua chung toi'
                                     ELSE
                                         ' moi den kham dung hen. Xin cam on'
END
END AS noidung,
                     bn.ma_benh_nhan    mabenhnhan,
                     kb.sovaovien
                 FROM
                     his_manager.kb_kham_benh       kb
                     JOIN his_public_list.dm_benh_nhan   bn ON bn.ma_benh_nhan = kb.mabenhnhan
                                                             AND TRIM(bn.so_dien_thoai) IS NOT NULL
                 WHERE
                     kb.dvtt = p_dvtt
                     AND trunc(kb.ngay_hen) = trunc(SYSDATE +
                         CASE
                             WHEN p_dvtt = 96172 THEN
                                 2
                             ELSE
                                 1
                         END
                     )
                 UNION ALL
SELECT
    bn.so_dien_thoai   so_dien_thoai,
    CASE
        WHEN p_dvtt = 96066 THEN
            'Xin nhac Anh/Chi lich tai kham ngay '
                || TO_CHAR(xv.ngay_hentaikham, 'DD/MM/YYYY')
                || ' tai Khoa kham benh.'
                || ' Rat mong Anh/Chi sap xep den dung hen de viec cham soc suc khoe duoc tot nhat. Xin cam on!'
        ELSE
            'Lich tai kham cua Quy benh nhan '
                || cmu_replacestring_vn(bn.ten_benh_nhan)
                ||
            CASE
                WHEN p_dvtt = 96034 THEN
                    ' tai BV Nam Can vao ngay '
                WHEN p_dvtt = 96019 THEN
                    ' tai BV Cai Nuoc vao ngay '
                ELSE
                    ''
                END
                || TO_CHAR(xv.ngay_hentaikham, 'DD/MM/YYYY')
                || ' moi den kham dung hen. Xin cam on'
        END noidung,
    bn.ma_benh_nhan    mabenhnhan,
    xv.sovaovien
FROM
    his_manager.noitru_xuatvien    xv
        LEFT JOIN his_manager.tgg_hentaikham     tk ON xv.dvtt = tk.dvtt
        AND xv.mabenhnhan = tk.mabenhnhan
        AND xv.sovaovien = tk.sovaovien_noi
        LEFT JOIN his_manager.dm_phong_benh      pb ON pb.ma_phong_benh = xv.phongxuatvien
        LEFT JOIN his_public_list.dm_benh_nhan   bn ON bn.ma_benh_nhan = xv.mabenhnhan
WHERE
    xv.ngay_hentaikham BETWEEN trunc(SYSDATE + 1) AND TO_DATE(TO_CHAR(trunc(SYSDATE + 1), 'DD/MM/YYYY')
                                                                  || ' 23:59:59', 'DD/MM/YYYY HH24:MI:SS')
  AND xv.dvtt = p_dvtt
  AND TRIM(bn.so_dien_thoai) IS NOT NULL;

RETURN cur;
END;