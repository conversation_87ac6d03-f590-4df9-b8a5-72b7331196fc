<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report name" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="557" leftMargin="22" rightMargin="16" topMargin="10" bottomMargin="20" uuid="6012d0bd-f305-4d4b-b38a-8f001e774017">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="tentinh" class="java.lang.String"/>
	<parameter name="ngayky" class="java.lang.String"/>
	<parameter name="hotenbacsi" class="java.lang.String"/>
	<parameter name="dudieukien" class="java.lang.String"/>
	<parameter name="theoyeucau" class="java.lang.String"/>
	<parameter name="tenbenhvien" class="java.lang.String"/>
	<parameter name="soluutru" class="java.lang.String"/>
	<parameter name="tenbenhvienchuyen" class="java.lang.String"/>
	<parameter name="tieude" class="java.lang.String"/>
	<parameter name="hotennguoibenh" class="java.lang.String"/>
	<parameter name="nam" class="java.lang.String"/>
	<parameter name="nu" class="java.lang.String"/>
	<parameter name="tuoi" class="java.lang.String"/>
	<parameter name="diachi" class="java.lang.String"/>
	<parameter name="dantoc" class="java.lang.String"/>
	<parameter name="ngoaikieu" class="java.lang.String"/>
	<parameter name="nghenghiep" class="java.lang.String"/>
	<parameter name="noilamviec" class="java.lang.String"/>
	<parameter name="bhyttungay" class="java.lang.String"/>
	<parameter name="bhytdenngay" class="java.lang.String"/>
	<parameter name="2kytudau" class="java.lang.String"/>
	<parameter name="kytu3" class="java.lang.String"/>
	<parameter name="kytu45" class="java.lang.String"/>
	<parameter name="kytu67" class="java.lang.String"/>
	<parameter name="kytu8910" class="java.lang.String"/>
	<parameter name="kytucuoi" class="java.lang.String"/>
	<parameter name="noidangkykcb" class="java.lang.String"/>
	<parameter name="sochuyentyt" class="java.lang.String"/>
	<parameter name="sochuyenpkkv" class="java.lang.String"/>
	<parameter name="tuyentyt" class="java.lang.String"/>
	<parameter name="tuyenpkkv" class="java.lang.String"/>
	<parameter name="tuyenttyt" class="java.lang.String"/>
	<parameter name="tungaytyt" class="java.lang.String"/>
	<parameter name="denngaytyt" class="java.lang.String"/>
	<parameter name="tungaypkkv" class="java.lang.String"/>
	<parameter name="denngaypkkv" class="java.lang.String"/>
	<parameter name="ngayvaovienttyt" class="java.lang.String"/>
	<parameter name="ngaychuyenvienttyt" class="java.lang.String"/>
	<parameter name="namsinh" class="java.lang.String"/>
	<parameter name="dauhieulamsang" class="java.lang.String"/>
	<parameter name="kqxetnghiem" class="java.lang.String"/>
	<parameter name="chandoan" class="java.lang.String"/>
	<parameter name="phuongphapdadung" class="java.lang.String"/>
	<parameter name="tinhtrangbenhnhan" class="java.lang.String"/>
	<parameter name="huongdieutri" class="java.lang.String"/>
	<parameter name="ngaygiochuyen" class="java.lang.String"/>
	<parameter name="phuongtienvc" class="java.lang.String"/>
	<parameter name="hotennguoichuyen" class="java.lang.String"/>
	<parameter name="makhambenh" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="dvtt" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="sochuyenttyt" class="java.lang.String"/>
	<parameter name="chucvu" class="java.lang.String"/>
	<parameter name="sohoso" class="java.lang.String"/>
	<parameter name="tentruongtram" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call HIS_MANAGER.PRO_IN_CHUYEN_TUYEN_ND_75($P{makhambenh},$P{dvtt},$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="TEN_BENH_NHAN" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ngoaikieu" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="nam" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="nu" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="TUOI" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="DIA_CHI" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="TEN_DANTOC" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="TEN_NGHE_NGHIEP" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="tungay" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="denngay" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="sothe12" class="java.lang.String"/>
	<field name="sothe3" class="java.lang.String"/>
	<field name="sothe45" class="java.lang.String"/>
	<field name="sothe67" class="java.lang.String"/>
	<field name="sothe8910" class="java.lang.String"/>
	<field name="sothecuoi" class="java.lang.String"/>
	<field name="NOIDANGKY_KCB" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="dieutri" class="java.lang.String"/>
	<field name="tuyendieutri" class="java.lang.String"/>
	<field name="noi_lamviec" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="SOCHUYENVIEN_LUUTRU" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="tenbenhvien_chuyendi" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="SOHOSO" class="java.lang.String"/>
	<field name="SOVAOSOCHUYENTUYEN" class="java.lang.String"/>
	<field name="dauhieulamsang" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ketquaxetnghiem_cls" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="chandoan" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="PP_TTPT_THUOC_DADUNG" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="TINHTRANGBENHNHAN" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="dudieukien" class="java.lang.String"/>
	<field name="dudieukiensonla" class="java.lang.String"/>
	<field name="kodudieukien" class="java.lang.String"/>
	<field name="kodudieukiensonla" class="java.lang.String"/>
	<field name="HUONGDIEUTRI" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ngaychuyen" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="PHUONGTIENVANCHUYEN" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ngayky" class="java.lang.String"/>
	<field name="ten_nhanvien" class="java.lang.String"/>
	<field name="hotennguoiduadi" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="SOVAOSOCHUYENTUYENTHANG" class="java.lang.String"/>
	<field name="LYDOCHUYENTUYEN" class="java.lang.String"/>
	<field name="SOCHUYENVIEN_LUUTRU_BOGCT" class="java.lang.String"/>
	<field name="ngaysinh" class="java.lang.String"/>
	<field name="cobhyt" class="java.lang.Boolean"/>
	<field name="duockhamdieutritai" class="java.lang.String"/>
	<field name="SO_THE_BHYT" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="548" splitType="Stretch">
			<ellipse>
				<reportElement positionType="Float" x="18" y="466" width="17" height="17" uuid="a24154ef-e90c-4609-8749-16b2ba3f66ed">
					<printWhenExpression><![CDATA[$F{LYDOCHUYENTUYEN}.equals( "1" )]]></printWhenExpression>
				</reportElement>
			</ellipse>
			<ellipse>
				<reportElement positionType="Float" x="18" y="417" width="17" height="17" uuid="52f675f0-31f3-4ff5-8bc5-ab881248e841">
					<printWhenExpression><![CDATA[$F{LYDOCHUYENTUYEN}.equals( "0" )
||
$F{LYDOCHUYENTUYEN}.equals( "2" )
||
$F{LYDOCHUYENTUYEN}.equals( "3" )]]></printWhenExpression>
				</reportElement>
			</ellipse>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="0" width="195" height="21" uuid="548ca402-599e-42b9-b376-4379301a0227"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tentinh}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="0" y="21" width="262" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="3708b400-9001-440a-bfd9-f2c99fcc1704"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenbenhvien}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="0" y="37" width="245" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="9d57a416-7752-4e47-b694-11ec5da2f8a6"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Số: " + (($F{SOCHUYENVIEN_LUUTRU}.isEmpty() || $F{SOCHUYENVIEN_LUUTRU}.equals( " " )) ? "......................................." : $F{SOCHUYENVIEN_LUUTRU})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="195" y="0" width="247" height="21" forecolor="#000000" backcolor="#FFFFFF" uuid="b65fc4d7-b6c8-46c5-893c-1c35f7942b6c"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="262" y="20" width="180" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="e9407b28-da11-4fd8-9352-85b1e3be779c"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Độc lập - Tự do - Hạnh phúc]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="445" y="0" width="47" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="e487c05b-e994-4f0d-8f51-63294151b435"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Số hồ sơ:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="445" y="21" width="111" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="deed16e0-054b-48c0-9fe1-4f224bd173d2"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Vào sổ chuyển tuyến số:]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="0" y="56" width="557" height="30" uuid="ee87bb50-36cf-4cb2-8c03-4844148bfe1d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="16" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{sothe12}.isEmpty() || $F{sothe12}.equals( " " )) ? "GIẤY CHUYỂN TUYẾN" : "GIẤY CHUYỂN TUYẾN KHÁM BỆNH, CHỮA BỆNH BẢO HIỂM Y TẾ"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="80" y="86" width="56" height="18" forecolor="#000000" backcolor="#FFFFFF" uuid="b80cb4bc-7296-4e86-bb7e-4b824b287b44"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Kính gửi:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="136" y="86" width="420" height="18" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF" uuid="e9d3e21b-547b-44cc-b249-4986eca3bfd0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tenbenhvien_chuyendi}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement positionType="Float" mode="Transparent" x="19" y="105" width="538" height="16" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF" uuid="ee11c82f-3c88-4ba8-8577-f39824bc31b4"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Cơ sở khám bệnh, chữa bệnh: " + $P{tieude}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="19" y="121" width="130" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="86bd8d9c-42a3-4809-8d68-f04296d7a1ee"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[- Họ và tên người bệnh:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="19" y="157" width="61" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="5653fc88-5fd1-40e9-8cc7-bafea5df6484"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[- Dân tộc:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="19" y="174" width="81" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="aa0b9bfc-ad4e-46fb-912d-e1ab9a1035e1"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[- Nghề nghiệp:]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" mode="Transparent" x="19" y="191" width="537" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="10658cb8-e408-46a6-87aa-1a13f64b944d"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Số thẻ bảo hiểm y tế: "
+($F{SO_THE_BHYT} == null? "": $F{SO_THE_BHYT} )]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="19" y="240" width="537" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="847ab45d-80ce-41b5-9dc7-0a796e4ce547"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[- Đã được khám bệnh, điều trị:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="19" y="288" width="218" height="16" uuid="549827f8-3bff-4db9-8cda-a7ea821c5143"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<text><![CDATA[TÓM TẮT BỆNH ÁN]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="149" y="121" width="176" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="2161dc67-d8c7-4eca-9195-eba1702a84b1"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_BENH_NHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="19" y="137" width="538" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="08eaa10e-0294-4eb6-90bb-e5c53bfe88a9"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Địa chỉ: " + $F{DIA_CHI}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="80" y="157" width="212" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="a33b4c64-11e7-4044-ba64-e6c122d0cb06"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_DANTOC}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="292" y="157" width="115" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="55ef2dcb-101b-437f-91df-98f11c47d13a"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Quốc tịch: Việt Nam]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="100" y="174" width="192" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="e46a56d3-96bc-457a-a1af-c01177715acb"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_NGHE_NGHIEP}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="292" y="174" width="79" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="f23ef947-de47-48e4-a90f-3dd04ae4730a"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Nơi làm việc:]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="430" y="121" width="127" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="769e74bf-c6ca-46bf-a0a6-2cadd243038e"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{dvtt}.equals("96025") ?
" Năm sinh: "
+ (new SimpleDateFormat("yyyy").format(new SimpleDateFormat("dd/MM/yyyy").parse($F{ngaysinh}))).toString()
: " Năm sinh: " + $F{ngaysinh}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="19" y="335" width="538" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="f03701dc-e416-4000-b17f-29886f773e7f"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{chandoan}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="19" y="351" width="538" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="d221eefd-7f2f-4585-8a79-cf6941af7ef7"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PP_TTPT_THUOC_DADUNG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="19" y="367" width="538" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="7b2fbb5d-e4e3-40a8-af5b-0b7ad8a516c6"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TINHTRANGBENHNHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="19" y="483" width="538" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="d194daa5-3ee3-427a-ab39-07449625c055"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HUONGDIEUTRI}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="19" y="498" width="538" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="5dd0f63e-fdab-4844-b373-df39aadc0135"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Chuyển tuyến hồi: "+$F{ngaychuyen}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="19" y="514" width="538" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="a132c1f5-7095-43fb-9130-d80b425a3174"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Phương tiện vận chuyển: "+$F{PHUONGTIENVANCHUYEN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="19" y="304" width="538" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="f2c36ee0-a355-4111-b4f3-dba1a9f5a9d3"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dauhieulamsang}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="19" y="320" width="538" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="44b73daf-390e-4194-a393-54231ce6022d"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ketquaxetnghiem_cls}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="19" y="529" width="538" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="0338bff5-8d20-43f4-a8b2-7fa3534e1e51"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Họ tên, chức danh, trình độ chuyên môn của người hộ tống (nếu có): " + ($F{hotennguoiduadi} == null ? "" : $F{hotennguoiduadi})]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="Float" x="264" y="37" width="130" height="1" uuid="b517bfb9-c74b-4d80-bed0-34400122af3a"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="492" y="0" width="64" height="20" uuid="212e4961-c356-41e0-8226-************"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOHOSO}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="445" y="36" width="111" height="20" uuid="94b6a972-1d81-44ca-8e4d-5ce0f91de41b"/>
				<textElement textAlignment="Justified" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOVAOSOCHUYENTUYEN}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="19" y="383" width="538" height="34" forecolor="#000000" backcolor="#FFFFFF" uuid="1206c52c-7b53-4f2e-9c26-884a9ba18c23"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Lí do chuyển tuyến: Khoanh tròn vào mục 1 hoặc 2 lý do chuyển tuyến. Trường hợp chọn mục 1, đánh dấu (X) vào ô tương ứng."]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="35" y="417" width="522" height="17" forecolor="#000000" backcolor="#FFFFFF" uuid="21e9e2de-9f4e-47a5-8f44-2726ad294d8f"/>
				<box leftPadding="3"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Đủ điều kiện chuyển tuyến:"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="35" y="466" width="522" height="17" forecolor="#000000" backcolor="#FFFFFF" uuid="f25d2218-f363-4e1e-958f-2fb5b5579587"/>
				<box leftPadding="4"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Theo yêu cầu của người bệnh hoặc người đại diện hợp pháp của người bệnh."]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="371" y="174" width="186" height="15" uuid="30e46b32-9cb8-4f69-bd2a-3c411221d9b2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{noi_lamviec}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="325" y="121" width="52" height="16" uuid="d75b97c3-6b6e-4584-b770-8a424a3b29f2"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13"/>
				</textElement>
				<text><![CDATA[Nam/Nữ: ]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="377" y="121" width="53" height="16" isPrintWhenDetailOverflows="true" uuid="8e83ea9a-ff7e-40ab-a213-5c4ddf64512f"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nam}.equals( "X" ) ? "Nam" : "Nữ"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" mode="Transparent" x="20" y="208" width="538" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="7add23b6-7d86-492e-b945-************"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["- Thời hạn sử dụng của thẻ bảo hiểm y tế đến ngày " +
($F{cobhyt}
?
(
(new SimpleDateFormat("dd").format(new SimpleDateFormat("dd/MM/yyyy").parse($F{denngay}))).toString() +
" tháng " +
(new SimpleDateFormat("MM").format(new SimpleDateFormat("dd/MM/yyyy").parse($F{denngay}))).toString() +
" năm " +
(new SimpleDateFormat("yyyy").format(new SimpleDateFormat("dd/MM/yyyy").parse($F{denngay}))).toString()
)
:
".....tháng.....Năm......"
)]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="20" y="417" width="15" height="17" forecolor="#000000" backcolor="#FFFFFF" uuid="aab228dd-7224-4a9e-a737-596f9e7aa880"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[1.]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="20" y="466" width="16" height="17" forecolor="#000000" backcolor="#FFFFFF" uuid="9797fe55-8e82-44a2-ba29-c8be9a57bc75"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[2.]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="101" y="224" width="16" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="a9b6a9a8-4074-43fc-956a-c9c96acc6e68"/>
				<box leftPadding="0">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="20" y="224" width="81" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="dd8dd3b9-6204-4a98-b24e-6fd8b98107ea"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Hết thời hạn:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="149" y="225" width="143" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="83f49be3-fc69-4a14-9bcf-719895ea21dc"/>
				<box leftPadding="5" rightPadding="0"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Không xác định thời hạn:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="292" y="225" width="16" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="d57555fb-33c7-4bbc-835c-bd425ffc05a8"/>
				<box leftPadding="0">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="19" y="272" width="538" height="16" uuid="1c270399-f967-4cec-99c9-5e6248796622"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA["+ Tại:..........(Tuyến...................................) từ ngày.... tháng.... năm 202... đến ngày.... tháng.... năm 202..."]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="19" y="256" width="537" height="16" uuid="b7d9f005-f69d-49f0-b7f4-70460657e051"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{duockhamdieutritai}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="237" y="434" width="16" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="67bc19db-791c-4022-9311-ae86c43a946a"/>
				<box leftPadding="0">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{LYDOCHUYENTUYEN}.equals("0") ? "X" : 
$F{LYDOCHUYENTUYEN}.equals( "2" ) ? "X" : 
($F{LYDOCHUYENTUYEN}.equals( "3" ) ? "X" : "")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="19" y="434" width="218" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="814bb709-71eb-4927-9366-42710973b1a4"/>
				<box leftPadding="5" rightPadding="0"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[a) Phù hợp với quy định chuyển tuyến:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="429" y="450" width="16" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="5393723b-1480-4f45-8310-d4a7976e412e"/>
				<box leftPadding="0">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{LYDOCHUYENTUYEN}.equals( "2" ) ? "X" : ($F{LYDOCHUYENTUYEN}.equals( "3" ) ? "X" : "")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="19" y="450" width="410" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="fe875163-0ec2-4438-917f-3f241bfaf6c2"/>
				<box leftPadding="5" rightPadding="0"/>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[b) Không phù hợp với khả năng đáp ứng của cơ sở khám bệnh, chữa bệnh:]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band splitType="Stretch"/>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band height="132" splitType="Stretch">
			<staticText>
				<reportElement positionType="Float" x="19" y="15" width="206" height="30" uuid="f57c14db-a8b0-40ca-b0ae-8282db66de04"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[BÁC SĨ, Y SĨ KHÁM,
ĐIỀU TRỊ]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="270" y="0" width="287" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="7b060ac8-b635-4d19-8ad7-3b15b7858e7f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ngayky}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="19" y="115" width="206" height="17" forecolor="#000000" backcolor="#FFFFFF" uuid="62761bed-3251-434b-8631-e2a9a3135299">
					<printWhenExpression><![CDATA[!$P{dvtt}.equals("17264")&&!$P{dvtt}.substring(0,2).equals("38")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{dvtt}.substring(0,2).equals("80")?" ":$F{ten_nhanvien}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="20" y="45" width="206" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="f842253c-f970-4c3b-acce-42f4b6340d46">
					<printWhenExpression><![CDATA[!$P{dvtt}.equals( "36032" )]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[(Ký và ghi rõ họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="270" y="45" width="287" height="17" forecolor="#000000" backcolor="#FFFFFF" uuid="9d281fcf-0090-43b8-ab93-8e05afd62db9"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[(Ký tên, đóng dấu của cơ sở khám bệnh, chữa bệnh)]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="269" y="115" width="287" height="17" forecolor="#000000" backcolor="#FFFFFF" uuid="ebc00312-5aaa-4a3d-a556-3c2da047d3a5"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="13" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tentruongtram}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="270" y="15" width="287" height="30" uuid="35033a99-2f29-434b-929d-12d81a91b630"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[GIÁM ĐỐC]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
