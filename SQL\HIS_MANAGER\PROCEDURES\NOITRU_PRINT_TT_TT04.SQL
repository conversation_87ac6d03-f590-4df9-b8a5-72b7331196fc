CREATE OR REPLACE PROCEDURE noitru_print_tt_tt04 (
    p_ma_toa_thuoc     IN                 VARCHAR2,
    p_dvtt             IN                 VARCHAR2,
    p_nghiep_vu        IN                 VARCHAR2,
    p_stt_<PERSON>han       IN                 VARCHAR2,
    p_stt_dotdieutri   IN                 VARCHAR2,
    p_stt_dieutri      IN                 VARCHAR2,
    p_toa              IN                 VARCHAR2 DEFAULT 'toa_t',
    cur                OUT                SYS_REFCURSOR
) IS

    v_countbs               NUMBER(10) DEFAULT 0;
    v_maloaivattu           VARCHAR2(11) DEFAULT '';
    v_hienthigiatoadichvu   VARCHAR2(11) DEFAULT '0';
    v_nguoilienhe           VARCHAR2(255) DEFAULT '';
    v_socmt                 VARCHAR2(255) DEFAULT '';
    v_thamso82446           NUMBER(10) := his_fw.cmu_tsdv(p_dvtt, 82446, 0);
    v_stg_tuoi              VARCHAR2(100) DEFAULT ' ';
    v_sodienthoai_bacsi     VARCHAR2(255) DEFAULT '';
    v_dientienbenh          VARCHAR2(3000) DEFAULT '';
    v_hienthisdt            VARCHAR2(10) := his_fw.dm_tsdv_sl_mtso(p_dvtt, '82233');
    v_sdt_benhnhan          VARCHAR2(3000) DEFAULT '';
    v_ngaysinh              VARCHAR2(50) DEFAULT ' ';
    v_songayuong            NUMBER(11);
    v_ngaykham              DATE DEFAULT SYSDATE;
    vmadonthuocqg           VARCHAR2(20) DEFAULT '';
    vnghiepvudtdt           VARCHAR2(100) DEFAULT '';
BEGIN
BEGIN
SELECT
    his_manager.stg_tuoi_te_f(TO_CHAR(bn.ngay_sinh, 'yyyy-mm-dd'), TO_CHAR(trunc(ba.ngaynhapvien), 'yyyy-mm-dd')),
    icd_nhapvien
        || ' - '
        || tenbenhchinh_nhapvien
INTO
    v_stg_tuoi,
    v_dientienbenh
FROM
    his_public_list.dm_benh_nhan   bn,
    his_manager.noitru_benhan      ba
WHERE
    ba.dvtt = p_dvtt
  AND ba.stt_benhan = p_stt_benhan
  AND ba.mabenhnhan = bn.ma_benh_nhan
  AND ROWNUM = 1;

EXCEPTION
        WHEN no_data_found THEN
            v_stg_tuoi := '';
            v_dientienbenh := '';
END;

BEGIN
SELECT
    mota_thamso
INTO v_hienthigiatoadichvu
FROM
    his_fw.dm_thamso_donvi
WHERE
    dvtt = p_dvtt
  AND ma_thamso = 52037; -- BDH Tham số hiển thị giá trong toa dịch vụ

EXCEPTION
        WHEN no_data_found THEN
            v_hienthigiatoadichvu := '0';
END;

BEGIN
SELECT
    nvl(cmt_benhnhan, ' '),
    nvl(bn.so_dien_thoai, bn.sdtnguoilh),
    TO_CHAR(ngay_sinh, 'dd/mm/yyyy')
INTO
    v_socmt,
    v_sdt_benhnhan,
    v_ngaysinh
FROM
    his_public_list.dm_benh_nhan   bn
        LEFT JOIN noitru_ct_toa_thuoc            tt ON bn.ma_benh_nhan = tt.mabenhnhan
WHERE
    tt.ma_toa_thuoc = p_ma_toa_thuoc
  AND tt.dvtt = p_dvtt
  AND tt.stt_dieutri = p_stt_dieutri
  AND tt.stt_benhan = p_stt_benhan
  AND tt.stt_dotdieutri = p_stt_dotdieutri
  --AND tt.maloaivattu = v_maloaivattu
  AND ROWNUM = 1;

EXCEPTION
        WHEN no_data_found THEN
            v_socmt := ' ';
            v_sdt_benhnhan := ' ';
END;

BEGIN
SELECT
    nvl(nguoi_lien_he, ' ')
INTO v_nguoilienhe
FROM
    his_public_list.dm_benh_nhan   bn
        LEFT JOIN noitru_ct_toa_thuoc            tt ON bn.ma_benh_nhan = tt.mabenhnhan
WHERE
    tt.ma_toa_thuoc = p_ma_toa_thuoc
  AND tt.dvtt = p_dvtt
  AND tt.stt_dieutri = p_stt_dieutri
  AND tt.stt_benhan = p_stt_benhan
  AND tt.stt_dotdieutri = p_stt_dotdieutri
  --AND tt.maloaivattu = v_maloaivattu
  AND ROWNUM = 1;

EXCEPTION
        WHEN no_data_found THEN
            v_nguoilienhe := ' ';
END;

BEGIN
SELECT
    nvl(nv.sodienthoai_nhanvien, ' ')
INTO v_sodienthoai_bacsi
FROM
    his_manager.noitru_dieutri   dt
        LEFT JOIN his_fw.dm_nhanvien           nv ON nv.ma_nhanvien = dt.tdt_nguoilap
WHERE
    dt.stt_benhan = p_stt_benhan
  AND dt.dvtt = p_dvtt
  AND dt.stt_dieutri = p_stt_dieutri
  AND dt.stt_dotdieutri = p_stt_dotdieutri;

END;

BEGIN
SELECT
    ngay_ra_toa
INTO v_ngaykham
FROM
    his_manager.noitru_toa_thuoc tt
WHERE
    tt.stt_benhan = p_stt_benhan
  AND tt.dvtt = p_dvtt
  AND tt.stt_dieutri = p_stt_dieutri
  AND tt.stt_dotdieutri = p_stt_dotdieutri;

END;

BEGIN
SELECT
    MAX(so_ngay_uong)
INTO v_songayuong
FROM
    his_manager.noitru_ct_toa_thuoc tt
WHERE
    tt.stt_benhan = p_stt_benhan
  AND tt.dvtt = p_dvtt
  AND tt.stt_dieutri = p_stt_dieutri
  AND tt.stt_dotdieutri = p_stt_dotdieutri;

END;

    IF ( p_nghiep_vu = 'ba_ngoaitru_toanghien' OR p_nghiep_vu = 'noitru_toanghien' OR p_toa = 'toa_n' OR p_nghiep_vu = 'ba_ngoaitru_toadichvu_nghien'

    OR p_nghiep_vu = 'noitru_toadichvu_nghien' ) THEN
        v_maloaivattu := 'TH_NGHIEN';
END IF;

    IF ( p_nghiep_vu = 'ba_ngoaitru_toahtt' OR p_nghiep_vu = 'noitru_toahtt' OR p_toa = 'toa_h' OR p_nghiep_vu = 'ba_ngoaitru_toadichvu_htt'

    OR p_nghiep_vu = 'noitru_toadichvu_htt' ) THEN
        v_maloaivattu := 'TH_HTT';
END IF;

    IF ( p_nghiep_vu = 'ba_ngoaitru_toanghien' OR p_nghiep_vu = 'ba_ngoaitru_toahtt' ) THEN
        vnghiepvudtdt := 'ba_ngoaitru_toathuoc';
    ELSIF ( p_nghiep_vu = 'noitru_toanghien' OR p_nghiep_vu = 'noitru_toahtt' ) THEN
        vnghiepvudtdt := 'noitru_toathuoc';
ELSE
        vnghiepvudtdt := p_nghiep_vu;
END IF;

BEGIN
SELECT
    ma_don_thuoc_qg
INTO vmadonthuocqg
FROM
    (
        SELECT
            dtdt.ma_don_thuoc_qg
        FROM
            dtdt_lien_thong_da_gui dtdt
        WHERE
            dtdt.dvtt = p_dvtt
          AND dtdt.stt_benhan = p_stt_benhan
          AND dtdt.stt_dotdieutri = p_stt_dotdieutri
          AND dtdt.ma_toa_thuoc = p_ma_toa_thuoc
          AND dtdt.nghiep_vu = vnghiepvudtdt
          AND dtdt.loai_toa_thuoc = p_toa
        ORDER BY
            dtdt.create_date DESC
    )
WHERE
    ROWNUM <= 1;

EXCEPTION
        WHEN no_data_found THEN
            vmadonthuocqg := '';
END;

    IF p_nghiep_vu = 'ba_ngoaitru_tonghop' THEN
SELECT
    COUNT(DISTINCT ma_bac_si_themthuoc)
INTO v_countbs
FROM
    noitru_ct_toa_thuoc
WHERE
    dvtt = p_dvtt
  AND ma_toa_thuoc = p_ma_toa_thuoc
  AND nghiep_vu IN (
                    'ngoaitru_toathuoc',
                    'ngoaitru_toavattu'
    );

OPEN cur FOR SELECT
                        MIN(ct.stt_order) AS stt_order,
                        nvl(v_nguoilienhe, ' ') AS nguoilienhe,
                        v_socmt               AS socmt,
                        concat('Bác sĩ chỉ định thuốc: ', nv.ten_nhanvien) AS ten_nhanvien,
                        vt.dvt,
                        kvt.tenkhovattu,  -- Vietdn
                        upper(vt.tenvattu) AS tenvattu,
                        CASE
                            WHEN vt.maloaihinh = 5005 THEN
                                upper(vt.tenvattu)
                            ELSE
                                (
                                    CASE
                                        WHEN TRIM(vt.hoatchat) IS NULL THEN
                                            concat(upper(vt.tenvattu)
                                                   || ' '
                                                   || vt.hamluong
                                                   || ' ('
                                                   || vt.tenvattu, ')')
                                        ELSE
                                            upper(substr(vt.hoatchat, 1, 1))
                                            || lower(substr(vt.hoatchat, 2))
                                            || ' ('
                                            || vt.tenvattu
                                            || ') '
                                            || vt.hamluong
                                    END
                                )
END AS mota_chitiet3,
                        CASE
                            WHEN vt.maloaihinh = 5005 THEN
                                upper(vt.tenvattu)
                            ELSE
                                (
                                    CASE
                                        WHEN TRIM(vt.hoatchat) IS NULL THEN
                                            concat(upper(vt.tenvattu)
                                                   || ' '
                                                   || vt.hamluong
                                                   || ' ('
                                                   || vt.tenvattu, ')')
                                        ELSE
                                            upper(substr(vt.hoatchat, 1, 1))
                                            || lower(substr(vt.hoatchat, 2))
                                            || ' '
                                            || vt.hamluong
                                            || ' ('
                                            || vt.tenvattu
                                            || ')'
                                    END
                                )
END AS mota_chitiet1,
                        CASE
                            WHEN vt.maloaihinh = 5005 THEN
                                upper(vt.tenvattu)
                            ELSE
                                (
                                    CASE
                                        WHEN TRIM(vt.hoatchat) IS NULL THEN
                                            concat(upper(vt.tenvattu)
                                                   || ' '
                                                   || vt.hamluong
                                                   || ' ('
                                                   || vt.tenvattu, ')')
                                        ELSE
                                            vt.tenvattu
                                            || ' '
                                            || vt.hamluong
                                            || ' ('
                                            || upper(substr(vt.hoatchat, 1, 1))
                                            || lower(substr(vt.hoatchat, 2))
                                            || ')'
                                    END
                                )
END AS mota_chitiet2,
                        upper(vt.tenvattu)
                        || ' '
                        || nvl(vt.hamluong, '') AS mota_chitiet3,
                        'SL: '
                        ||
                            CASE instr(CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30)), '.')
                                WHEN 1 THEN
                                    '0'
                                    || CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30))
                                ELSE
                                    CASE
                                        WHEN SUM(ct.so_luong_thuc_linh) < 10 THEN
                                            concat(0, CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30)))
                                        ELSE
                                            CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30))
END
END
|| concat(' ', vt.dvt)
                        || ' '
                        || '' AS so_luong_uong,
                        CASE
                            WHEN sang_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Sáng ', chuyen_thapphan_sang_phanso(sang_uong))
                                || ' '
                                || cach_su_dung
END AS sang_uong,
                        CASE
                            WHEN trua_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Trưa ', chuyen_thapphan_sang_phanso(trua_uong))
                                || ' '
                                || cach_su_dung
END AS trua_uong,
                        CASE
                            WHEN chieu_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Chiều ', chuyen_thapphan_sang_phanso(chieu_uong))
                                || ' '
                                || cach_su_dung
END AS chieu_uong,
                        CASE
                            WHEN toi_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Tối ', chuyen_thapphan_sang_phanso(toi_uong))
                                || ' '
                                || cach_su_dung
END AS toi_uong,
                        CASE ghi_chu_ct_toa_thuoc
                            WHEN NULL THEN
                                ''
                            ELSE
                                concat('Ghi chú: ', ghi_chu_ct_toa_thuoc)
END AS ghi_chu_ct_toa_thuoc,
                        ma_toa_thuoc,
                        SUM(so_luong) AS so_luong,
                        ma_bac_si_themthuoc,
                        SUM(so_luong_thuc_linh) AS so_luong_thuc_linh,
                        SUM(thanhtien_thuoc) AS thanhtien_thuoc,
                        CASE cach_su_dung
                            WHEN NULL THEN
                                ''
                            ELSE
                                '( '
                                || cach_su_dung
                                || ' )'
END AS cach_su_dung,
                        CASE ghi_chu_ct_toa_thuoc
                            WHEN NULL THEN
                                ''
                            ELSE
                                concat(ghi_chu_ct_toa_thuoc, ' :')
END AS ghi_chu_ct_toa_thuoc1,
                        v_countbs             AS tt,
                        vt.hoatchat,
                        vt.hamluong,
                        CASE
                            WHEN vt.manhomvattu = 33 THEN
                                upper(vt.tenvattu)
                            ELSE
                                upper(vt.tenvattu)
                                || ' '
                                || nvl(vt.hamluong, '')
END AS mota_chitiet,
                        CASE
                            WHEN --start
                             vt.hoatchat IS NULL
                                 OR TRIM(vt.hoatchat) = ''
                                 OR instr(vt.hoatchat, ',') > 0
                                 OR instr(vt.hoatchat, ';') > 0
                                 OR instr(vt.hoatchat, '+') > 0 THEN
                                upper(vt.tenvattu)
                            ELSE
                                upper(vt.hoatchat)
                                || ' ('
                                || vt.tenvattu
                                || ')'
                                || ' '
                                || nvl(vt.hamluong, ' ')
END AS mota_ct_tvh1--tvh them mo ta
                   -- vlg Lợi lấy số giường buồng
                        ,
                        sogiuong.stt_giuong,
                        sogiuong.stt_buong
                   --end vlg Lợi lấy số giường buồng
                        ,
                        ddt.tuoi,
                        ddt.thang
--             ,nvl(dv.sodienthoai,' ') sodienthoai
                        ,
                        CASE
                            WHEN v_hienthisdt = '1' THEN
                                CASE
                                    WHEN nvl(dv.sodienthoai, ' ') = ' ' THEN
                                        nvl(nv.sodienthoai_nhanvien, ' ')
                                    ELSE
                                        nvl(dv.sodienthoai, ' ')
END
WHEN v_hienthisdt = '2' THEN
                                CASE
                                    WHEN nvl(dmpb.sodienthoai, ' ') = ' ' THEN
                                        nvl(nv.sodienthoai_nhanvien, ' ')
                                    ELSE
                                        nvl(dmpb.sodienthoai, ' ')
END
ELSE
                                nvl(dv.sodienthoai, ' ')
END AS sodienthoai,
                        nvl(dt.cannang, ' ') cannang,
                        MAX(ct.so_ngay_uong) AS so_ngay_uong,
                        v_sodienthoai_bacsi   AS sdt_bacsi,
                        v_dientienbenh        AS dientienbenh,
                        ct.bant,
                        v_sdt_benhnhan        AS sdt_benhnhan,
                        dv.diachi,
                        v_ngaysinh            AS ngay_sinh,
                        vmadonthuocqg         AS ma_don_thuoc,
                        TO_CHAR(v_songayuong + v_ngaykham, 'dd/mm/yyyy') AS ngaythuoc,
                        '('||dt.ICD_DIEUTRI||') '||DT.TENICD_DIEUTRI || ';' ||DT.TEN_BENHPHU CHANDOAN
                    FROM
                        noitru_ct_toa_thuoc             ct
                        JOIN his_manager.noitru_dieutri      dt ON ct.dvtt = dt.dvtt
                                                              AND ct.sovaovien = dt.sovaovien
                                                              AND ct.sovaovien_dt = dt.sovaovien_dt
                                                              AND ct.id_dieutri = dt.id_dieutri
                        LEFT JOIN his_fw.dm_phongban              dmpb ON dmpb.ma_phongban = dt.khoalap
                        JOIN his_manager.noitru_dotdieutri   ddt ON ddt.dvtt = ct.dvtt
                                                                  AND ddt.sovaovien = ct.sovaovien
                                                                  AND ddt.sovaovien_dt = ct.sovaovien_dt
                        JOIN his_fw.dm_donvi                 dv ON dv.ma_donvi = ddt.dvtt
                        LEFT JOIN his_manager.dc_tb_khovattu      kvt ON ct.dvtt = kvt.dvtt
                                                                    AND ct.makhovattu = kvt.makhovattu   -- vietdn
            -- vlg Lợi lấy số giường buồng
                        LEFT JOIN his_manager.cmu_sobuonggiuong   sogiuong ON ct.dvtt = sogiuong.dvtt
                                                                            AND ct.stt_benhan = sogiuong.stt_benhan
                                                                            AND ct.stt_dotdieutri = sogiuong.stt_dotdieutri
               -- vlg Lợi lấy số giường buồng
                                                                            ,
                        dc_tb_vattu                     vt,
                        his_fw.dm_nhanvien              nv
                    WHERE
                        ma_toa_thuoc = p_ma_toa_thuoc
              -- and ct.NGHIEP_VU in ( 'noitru_toathuoc','noitru_toavattu')
                        AND ct.dvtt = p_dvtt
                        AND ct.mavattu = vt.mavattu
                        AND vt.dvtt = p_dvtt
                        AND ct.stt_dieutri = p_stt_dieutri
                        AND ct.stt_benhan = p_stt_benhan
                        AND ct.stt_dotdieutri = p_stt_dotdieutri
                        AND nv.ma_nhanvien = ct.ma_bac_si_themthuoc
                    GROUP BY
                        nv.ten_nhanvien,
                        vt.dvt,
                        vt.tenvattu,
                        vt.hoatchat,
                        vt.hamluong,
                        sogiuong.stt_giuong,
                        sogiuong.stt_buong,
                        sang_uong,
                        trua_uong,
                        chieu_uong,
                        toi_uong,
                        ghi_chu_ct_toa_thuoc,
                        ma_toa_thuoc,
                        ma_bac_si_themthuoc,
                        vt.maloaihinh,
                        cach_su_dung,
                        vt.manhomvattu,
                        kvt.tenkhovattu,
                        ddt.tuoi,
                        ddt.thang,
                        dv.sodienthoai,
                        dt.cannang,
                        ct.bant,
                        dv.sodienthoai,
                        dmpb.sodienthoai,
                        nv.sodienthoai_nhanvien,
                        dv.diachi,
                        ct.id_dieutri,
                        '('||dt.ICD_DIEUTRI||') '||DT.TENICD_DIEUTRI || ';' ||DT.TEN_BENHPHU
                    HAVING
                        SUM(so_luong) > 0
                    ORDER BY
                        stt_order ASC;

    ELSIF p_nghiep_vu = 'noitru_toathuoc' AND v_thamso82446 != 1 THEN
SELECT
    COUNT(DISTINCT ma_bac_si_themthuoc)
INTO v_countbs
FROM
    noitru_ct_toa_thuoc
WHERE
    dvtt = p_dvtt
  AND ma_toa_thuoc = p_ma_toa_thuoc
  AND stt_dieutri = p_stt_dieutri
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND nghiep_vu IN (
                    'noitru_toathuoc',
                    'noitru_toavattu'
    );

OPEN cur FOR SELECT
                        MIN(ct.stt_order) AS stt_order,
                        nvl(v_nguoilienhe, ' ') AS nguoilienhe,
                        v_socmt               AS socmt,
                        CASE
                            WHEN p_dvtt = '89014' THEN
                                ' '
                            ELSE
                                concat('Bác sĩ chỉ định thuốc: ', nv.ten_nhanvien)
END AS ten_nhanvien,
                        vt.dvt,
                        kvt.tenkhovattu,    -- vietdn
                        upper(vt.tenvattu) AS tenvattu,
                        CASE
                            WHEN vt.maloaihinh = 5005 THEN
                                upper(vt.tenvattu)
                            ELSE
                                (
                                    CASE
                                        WHEN TRIM(vt.hoatchat) IS NULL THEN
                                            concat(upper(vt.tenvattu)
                                                   || ' '
                                                   || vt.hamluong
                                                   || ' ('
                                                   || vt.tenvattu, ')')
                                        ELSE
                                            upper(substr(vt.hoatchat, 1, 1))
                                            || lower(substr(vt.hoatchat, 2))
                                            || ' '
                                            || vt.hamluong
                                            || ' ('
                                            || vt.tenvattu
                                            || ')'
                                    END
                                )
END AS mota_chitiet1,
                        CASE
                            WHEN vt.maloaihinh = 5005 THEN
                                upper(vt.tenvattu)
                            ELSE
                                (
                                    CASE
                                        WHEN TRIM(vt.hoatchat) IS NULL THEN
                                            concat(upper(vt.tenvattu)
                                                   || ' '
                                                   || vt.hamluong
                                                   || ' ('
                                                   || vt.tenvattu, ')')
                                        ELSE
                                            vt.tenvattu
                                            || ' '
                                            || vt.hamluong
                                            || ' ('
                                            || upper(substr(vt.hoatchat, 1, 1))
                                            || lower(substr(vt.hoatchat, 2))
                                            || ')'
                                    END
                                )
END AS mota_chitiet2,
                        upper(vt.tenvattu)
                        || ' '
                        || nvl(vt.hamluong, '') AS mota_chitiet3,
                        'SL: '
                        ||
                            CASE instr(CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30)), '.')
                                WHEN 1 THEN
                                    '0'
                                    || CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30))
                                ELSE
                                    CASE
                                        WHEN SUM(ct.so_luong_thuc_linh) < 10 THEN
                                            concat(0, CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30)))
                                        ELSE
                                            CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30))
END
END
|| concat(' ', vt.dvt)
                        || ' '
                        || '' AS so_luong_uong,
                        CASE
                            WHEN sang_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Sáng ', chuyen_thapphan_sang_phanso(sang_uong))
                                || ' '
                                || cach_su_dung
END AS sang_uong,
                        CASE
                            WHEN trua_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Trưa ', chuyen_thapphan_sang_phanso(trua_uong))
                                || ' '
                                || cach_su_dung
END AS trua_uong,
                        CASE
                            WHEN chieu_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Chiều ', chuyen_thapphan_sang_phanso(chieu_uong))
                                || ' '
                                || cach_su_dung
END AS chieu_uong,
                        CASE
                            WHEN toi_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Tối ', chuyen_thapphan_sang_phanso(toi_uong))
                                || ' '
                                || cach_su_dung
END AS toi_uong,
                        CASE ghi_chu_ct_toa_thuoc
                            WHEN NULL THEN
                                ''
                            ELSE
                                concat('Ghi chú: ', ghi_chu_ct_toa_thuoc)
END AS ghi_chu_ct_toa_thuoc,
                        ma_toa_thuoc,
                        SUM(so_luong) AS so_luong,
                        ma_bac_si_themthuoc,
                        SUM(so_luong_thuc_linh) AS so_luong_thuc_linh,
                        SUM(thanhtien_thuoc) AS thanhtien_thuoc,
                        CASE cach_su_dung
                            WHEN NULL THEN
                                ''
                            ELSE
                                '( '
                                || cach_su_dung
                                || ' )'
END AS cach_su_dung,
                        CASE ghi_chu_ct_toa_thuoc
                            WHEN NULL THEN
                                ''
                            ELSE
                                concat(ghi_chu_ct_toa_thuoc, ' :')
END AS ghi_chu_ct_toa_thuoc1,
                        v_countbs             AS tt,
                        vt.hoatchat,
                        vt.hamluong,
                        CASE
                            WHEN vt.manhomvattu = 33 THEN
                                upper(vt.tenvattu)
                            ELSE
                                upper(vt.tenvattu)
                                || ' '
                                || nvl(vt.hamluong, '')
END AS mota_chitiet,
                        CASE
                            WHEN --start
                             vt.hoatchat IS NULL
                                 OR TRIM(vt.hoatchat) = ''
                                 OR instr(vt.hoatchat, ',') > 0
                                 OR instr(vt.hoatchat, ';') > 0
                                 OR instr(vt.hoatchat, '+') > 0 THEN
                                upper(vt.tenvattu)
                            ELSE
                                upper(vt.hoatchat)
                                || ' ('
                                || vt.tenvattu
                                || ')'
                                || ' '
                                || nvl(vt.hamluong, ' ')
END AS mota_ct_tvh1--tvh them mo ta
                   -- vlg Lợi lấy số giường buồng
                        ,
                        sogiuong.stt_giuong,
                        sogiuong.stt_buong
                   --end vlg Lợi lấy số giường buồng
                        ,
                        ddt.tuoi,
                        ddt.thang
--             ,nvl(dv.sodienthoai,' ') sodienthoai
                        ,
                        CASE
                            WHEN v_hienthisdt = '1' THEN
                                CASE
                                    WHEN nvl(dv.sodienthoai, ' ') = ' ' THEN
                                        nvl(nv.sodienthoai_nhanvien, ' ')
                                    ELSE
                                        nvl(dv.sodienthoai, ' ')
END
WHEN v_hienthisdt = '2' THEN
                                CASE
                                    WHEN nvl(dmpb.sodienthoai, ' ') = ' ' THEN
                                        nvl(nv.sodienthoai_nhanvien, ' ')
                                    ELSE
                                        nvl(dmpb.sodienthoai, ' ')
END
ELSE
                                nvl(dv.sodienthoai, ' ')
END AS sodienthoai,
                        nvl(dt.cannang, ' ') cannang,
                        MAX(ct.so_ngay_uong) AS so_ngay_uong,
                        v_sodienthoai_bacsi   AS sdt_bacsi,
                        v_dientienbenh        AS dientienbenh,
                        ct.bant,
                        v_sdt_benhnhan        AS sdt_benhnhan,
                        dv.diachi,
                        v_ngaysinh            AS ngay_sinh,
                        vmadonthuocqg         AS ma_don_thuoc,
                        TO_CHAR(v_songayuong + v_ngaykham, 'dd/mm/yyyy') AS ngaythuoc,
                        '('||dt.ICD_DIEUTRI||') '||DT.TENICD_DIEUTRI || ';' ||DT.TEN_BENHPHU CHANDOAN
                    FROM
                        noitru_ct_toa_thuoc             ct
                        JOIN his_manager.noitru_dieutri      dt ON ct.dvtt = dt.dvtt
                                                              AND ct.sovaovien = dt.sovaovien
                                                              AND ct.sovaovien_dt = dt.sovaovien_dt
                                                              AND ct.id_dieutri = dt.id_dieutri
                        LEFT JOIN his_fw.dm_phongban              dmpb ON dmpb.ma_phongban = dt.khoalap
                        JOIN his_manager.noitru_dotdieutri   ddt ON ddt.dvtt = ct.dvtt
                                                                  AND ddt.sovaovien = ct.sovaovien
                                                                  AND ddt.sovaovien_dt = ct.sovaovien_dt
                        JOIN his_fw.dm_donvi                 dv ON dv.ma_donvi = ddt.dvtt
                        LEFT JOIN his_manager.dc_tb_khovattu      kvt ON ct.dvtt = kvt.dvtt
                                                                    AND ct.makhovattu = kvt.makhovattu   -- vietdn
            -- vlg Lợi lấy số giường buồng
                        LEFT JOIN his_manager.cmu_sobuonggiuong   sogiuong ON ct.dvtt = sogiuong.dvtt
                                                                            AND ct.stt_benhan = sogiuong.stt_benhan
                                                                            AND ct.stt_dotdieutri = sogiuong.stt_dotdieutri
                -- vlg Lợi lấy số giường buồng
                        LEFT JOIN his_fw.dm_nhanvien              nv ON nv.ma_nhanvien = ct.ma_bac_si_themthuoc, dc_tb_vattu                     vt
                        LEFT JOIN dc_tb_nhomvattu                 nhom ON nhom.dvtt = vt.dvtt
                                                          AND vt.manhomvattu = nhom.manhomvattu
                    WHERE
                        ma_toa_thuoc = p_ma_toa_thuoc
                        AND ct.nghiep_vu IN (
                            'noitru_toathuoc',
                            'noitru_toavattu'
                        )
                        AND ct.dvtt = p_dvtt
                        AND ct.mavattu = vt.mavattu
                        AND vt.dvtt = p_dvtt
                        AND ct.stt_dieutri = p_stt_dieutri
                        AND ct.stt_benhan = p_stt_benhan
                        AND ct.stt_dotdieutri = p_stt_dotdieutri
                        AND nhom.maloaivattu NOT IN (
                            'TH_NGHIEN',
                            'TH_HTT'
                        )
                    GROUP BY
                        nv.ten_nhanvien,
                        vt.dvt,
                        vt.tenvattu,
                        vt.hoatchat,
                        vt.hamluong,
                        sogiuong.stt_giuong,
                        sogiuong.stt_buong,
                        sang_uong,
                        trua_uong,
                        chieu_uong,
                        toi_uong,
                        ghi_chu_ct_toa_thuoc,
                        ma_toa_thuoc,
                        ma_bac_si_themthuoc,
                        vt.maloaihinh,
                        cach_su_dung,
                        vt.manhomvattu,
                        kvt.tenkhovattu,
                        ddt.tuoi,
                        ddt.thang,
                        dv.sodienthoai,
                        dt.cannang,
                        ct.bant,
                        dv.sodienthoai,
                        dmpb.sodienthoai,
                        nv.sodienthoai_nhanvien,
                        dv.diachi,
                        ct.id_dieutri,
                        '('||dt.ICD_DIEUTRI||') '||DT.TENICD_DIEUTRI || ';' ||DT.TEN_BENHPHU
                    HAVING
                        SUM(so_luong) > 0
                    ORDER BY
                        stt_order ASC;

    ELSIF p_nghiep_vu = 'ba_ngoaitru_toanghien' OR p_nghiep_vu = 'ba_ngoaitru_toahtt' OR p_nghiep_vu = 'noitru_toanghien' OR p_nghiep_vu

    = 'noitru_toahtt' THEN
SELECT
    COUNT(DISTINCT ma_bac_si_themthuoc)
INTO v_countbs
FROM
    noitru_ct_toa_thuoc
WHERE
    dvtt = p_dvtt
  AND ma_toa_thuoc = p_ma_toa_thuoc
  AND stt_dieutri = p_stt_dieutri
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND nghiep_vu = p_nghiep_vu;

OPEN cur FOR SELECT
                        MIN(ct.stt_order) AS stt_order,
                        nvl(v_nguoilienhe, ' ') AS nguoilienhe,
                        v_socmt               AS socmt,
                        concat('Bác sĩ chỉ định thuốc: ', nv.ten_nhanvien) AS ten_nhanvien,
                        vt.dvt,
                        kvt.tenkhovattu,    -- vietdn
                        upper(vt.tenvattu) AS tenvattu,
                        CASE
                            WHEN vt.maloaihinh = 5005 THEN
                                upper(vt.tenvattu)
                            ELSE
                                (
                                    CASE
                                        WHEN TRIM(vt.hoatchat) IS NULL THEN
                                            concat(upper(vt.tenvattu)
                                                   || ' '
                                                   || vt.hamluong
                                                   || ' ('
                                                   || vt.tenvattu, ')')
                                        ELSE
                                            upper(substr(vt.hoatchat, 1, 1))
                                            || lower(substr(vt.hoatchat, 2))
                                            || ' '
                                            || vt.hamluong
                                            || ' ('
                                            || vt.tenvattu
                                            || ')'
                                    END
                                )
END AS mota_chitiet1,
                        CASE
                            WHEN vt.maloaihinh = 5005 THEN
                                upper(vt.tenvattu)
                            ELSE
                                (
                                    CASE
                                        WHEN TRIM(vt.hoatchat) IS NULL THEN
                                            concat(upper(vt.tenvattu)
                                                   || ' '
                                                   || vt.hamluong
                                                   || ' ('
                                                   || vt.tenvattu, ')')
                                        ELSE
                                            vt.tenvattu
                                            || ' '
                                            || vt.hamluong
                                            || ' ('
                                            || upper(substr(vt.hoatchat, 1, 1))
                                            || lower(substr(vt.hoatchat, 2))
                                            || ')'
                                    END
                                )
END AS mota_chitiet2,
                        upper(vt.tenvattu)
                        || ' '
                        || nvl(vt.hamluong, '') AS mota_chitiet3,
                        'SL: '
                        ||
                            CASE instr(CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30)), '.')
                                WHEN 1 THEN
                                    '0'
                                    || CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30))
                                ELSE
                                    CASE
                                        WHEN SUM(ct.so_luong_thuc_linh) < 10 THEN
                                            concat(0, CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30)))
                                        ELSE
                                            CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30))
END
END
|| concat(' ', vt.dvt)
                        || ' '
                        || '' AS so_luong_uong,
                        CASE
                            WHEN sang_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Sáng ', chuyen_thapphan_sang_phanso(sang_uong))
                                || ' '
                                || cach_su_dung
END AS sang_uong,
                        CASE
                            WHEN trua_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Trưa ', chuyen_thapphan_sang_phanso(trua_uong))
                                || ' '
                                || cach_su_dung
END AS trua_uong,
                        CASE
                            WHEN chieu_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Chiều ', chuyen_thapphan_sang_phanso(chieu_uong))
                                || ' '
                                || cach_su_dung
END AS chieu_uong,
                        CASE
                            WHEN toi_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Tối ', chuyen_thapphan_sang_phanso(toi_uong))
                                || ' '
                                || cach_su_dung
END AS toi_uong,
                        CASE ghi_chu_ct_toa_thuoc
                            WHEN NULL   THEN
                                ''
                            WHEN ''     THEN
                                ''
                            ELSE
                                concat('Ghi chú: ', ghi_chu_ct_toa_thuoc)
END AS ghi_chu_ct_toa_thuoc,
                        ma_toa_thuoc,
                        SUM(so_luong) AS so_luong,
                        ma_bac_si_themthuoc,
                        SUM(so_luong_thuc_linh) AS so_luong_thuc_linh,
                        SUM(thanhtien_thuoc) AS thanhtien_thuoc,
                        CASE cach_su_dung
                            WHEN NULL THEN
                                ''
                            ELSE
                                '( '
                                || cach_su_dung
                                || ' )'
END AS cach_su_dung,
                        CASE ghi_chu_ct_toa_thuoc
                            WHEN NULL THEN
                                ''
                            ELSE
                                concat(ghi_chu_ct_toa_thuoc, ' :')
END AS ghi_chu_ct_toa_thuoc1,
                        v_countbs             AS tt,
                        vt.hoatchat,
                        vt.hamluong,
                        CASE
                            WHEN vt.manhomvattu = 33 THEN
                                upper(vt.tenvattu)
                            ELSE
                                upper(vt.tenvattu)
                                || ' '
                                || nvl(vt.hamluong, '')
END AS mota_chitiet,
                        CASE
                            WHEN --start
                             vt.hoatchat IS NULL
                                 OR TRIM(vt.hoatchat) = ''
                                 OR instr(vt.hoatchat, ',') > 0
                                 OR instr(vt.hoatchat, ';') > 0
                                 OR instr(vt.hoatchat, '+') > 0 THEN
                                upper(vt.tenvattu)
                            ELSE
                                upper(vt.hoatchat)
                                || ' ('
                                || vt.tenvattu
                                || ')'
                                || ' '
                                || nvl(vt.hamluong, ' ')
END AS mota_ct_tvh1--tvh them mo ta
                   -- vlg Lợi lấy số giường buồng
                        ,
                        sogiuong.stt_giuong,
                        sogiuong.stt_buong
                   --end vlg Lợi lấy số giường buồng
                        ,
                        ddt.tuoi,
                        ddt.thang
--             ,nvl(dv.sodienthoai,' ') sodienthoai
                        ,
                        CASE
                            WHEN v_hienthisdt = '1' THEN
                                CASE
                                    WHEN nvl(dv.sodienthoai, ' ') = ' ' THEN
                                        nvl(nv.sodienthoai_nhanvien, ' ')
                                    ELSE
                                        nvl(dv.sodienthoai, ' ')
END
WHEN v_hienthisdt = '2' THEN
                                CASE
                                    WHEN nvl(dmpb.sodienthoai, ' ') = ' ' THEN
                                        nvl(nv.sodienthoai_nhanvien, ' ')
                                    ELSE
                                        nvl(dmpb.sodienthoai, ' ')
END
ELSE
                                nvl(dv.sodienthoai, ' ')
END AS sodienthoai,
                        nvl(dt.cannang, ' ') cannang,
                        MAX(ct.so_ngay_uong) AS so_ngay_uong,
                        v_stg_tuoi            AS tuoi_stg,
                        v_sodienthoai_bacsi   AS sdt_bacsi,
                        v_dientienbenh        AS dientienbenh,
                        ct.bant,
                        v_sdt_benhnhan        AS sdt_benhnhan,
                        dv.diachi,
                        v_ngaysinh            AS ngay_sinh,
                        vmadonthuocqg         AS ma_don_thuoc,
                        TO_CHAR(v_songayuong + v_ngaykham, 'dd/mm/yyyy') AS ngaythuoc,
                        '('||dt.ICD_DIEUTRI||') '||DT.TENICD_DIEUTRI || ';' ||DT.TEN_BENHPHU CHANDOAN
                    FROM
                        noitru_ct_toa_thuoc             ct
                        JOIN his_manager.noitru_dieutri      dt ON ct.dvtt = dt.dvtt
                                                              AND ct.sovaovien = dt.sovaovien
                                                              AND ct.sovaovien_dt = dt.sovaovien_dt
                                                              AND ct.id_dieutri = dt.id_dieutri
                        LEFT JOIN his_fw.dm_phongban              dmpb ON dmpb.ma_phongban = dt.khoalap
                        JOIN his_manager.noitru_dotdieutri   ddt ON ddt.dvtt = ct.dvtt
                                                                  AND ddt.sovaovien = ct.sovaovien
                                                                  AND ddt.sovaovien_dt = ct.sovaovien_dt
                        JOIN his_fw.dm_donvi                 dv ON dv.ma_donvi = ddt.dvtt
                        LEFT JOIN his_manager.dc_tb_khovattu      kvt ON ct.dvtt = kvt.dvtt
                                                                    AND ct.makhovattu = kvt.makhovattu   -- vietdn
            -- vlg Lợi lấy số giường buồng
                        LEFT JOIN his_manager.cmu_sobuonggiuong   sogiuong ON ct.dvtt = sogiuong.dvtt
                                                                            AND ct.stt_benhan = sogiuong.stt_benhan
                                                                            AND ct.stt_dotdieutri = sogiuong.stt_dotdieutri
                 -- vlg Lợi lấy số giường buồng
                                                                            , dc_tb_vattu                     vt
                        LEFT JOIN dc_tb_nhomvattu                 nhom ON nhom.dvtt = vt.dvtt
                                                          AND vt.manhomvattu = nhom.manhomvattu,
                        his_fw.dm_nhanvien              nv
                    WHERE
                        ma_toa_thuoc = p_ma_toa_thuoc
                        AND ct.dvtt = p_dvtt
                        AND ct.mavattu = vt.mavattu
                        AND vt.dvtt = p_dvtt
                        AND ct.stt_dieutri = p_stt_dieutri
                        AND ct.stt_benhan = p_stt_benhan
                        AND ct.stt_dotdieutri = p_stt_dotdieutri
                        AND nv.ma_nhanvien = ct.ma_bac_si_themthuoc
                        AND nhom.maloaivattu = v_maloaivattu
                    GROUP BY
                        nv.ten_nhanvien,
                        vt.dvt,
                        vt.tenvattu,
                        vt.hoatchat,
                        vt.hamluong,
                        sogiuong.stt_giuong,
                        sogiuong.stt_buong,
                        sang_uong,
                        trua_uong,
                        chieu_uong,
                        toi_uong,
                        ghi_chu_ct_toa_thuoc,
                        ma_toa_thuoc,
                        ma_bac_si_themthuoc,
                        vt.maloaihinh,
                        cach_su_dung,
                        vt.manhomvattu,
                        kvt.tenkhovattu,
                        ddt.tuoi,
                        ddt.thang,
                        dv.sodienthoai,
                        dt.cannang,
                        v_stg_tuoi,
                        ct.bant,
                        dv.sodienthoai,
                        dmpb.sodienthoai,
                        nv.sodienthoai_nhanvien,
                        dv.diachi,
                        ct.id_dieutri,
                        '('||dt.ICD_DIEUTRI||') '||DT.TENICD_DIEUTRI || ';' ||DT.TEN_BENHPHU
                    HAVING
                        SUM(so_luong) > 0
                    ORDER BY
                        stt_order ASC;

    ELSIF p_nghiep_vu = 'ba_ngoaitru_toadichvu_nghien' OR p_nghiep_vu = 'ba_ngoaitru_toadichvu_htt' OR p_nghiep_vu = 'noitru_toadichvu_nghien'

    OR p_nghiep_vu = 'noitru_toadichvu_htt' THEN
SELECT
    COUNT(DISTINCT ma_bac_si_themthuoc)
INTO v_countbs
FROM
    noitru_ct_toa_thuoc
WHERE
    dvtt = p_dvtt
  AND ma_toa_thuoc = p_ma_toa_thuoc
  AND stt_dieutri = p_stt_dieutri
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND nghiep_vu IN (
                    'ba_ngoaitru_toadichvu',
                    'noitru_toadichvu'
    );

OPEN cur FOR SELECT
                        MIN(ct.stt_order) AS stt_order,
                        nvl(v_nguoilienhe, ' ') AS nguoilienhe,
                        v_socmt               AS socmt,
                        concat('Bác sĩ chỉ định thuốc: ', nv.ten_nhanvien) AS ten_nhanvien,
                        vt.dvt,
                        kvt.tenkhovattu,    -- vietdn
                        upper(vt.tenvattu) AS tenvattu,
                        CASE
                            WHEN vt.maloaihinh = 5005 THEN
                                upper(vt.tenvattu)
                            ELSE
                                (
                                    CASE
                                        WHEN TRIM(vt.hoatchat) IS NULL THEN
                                            concat(upper(vt.tenvattu)
                                                   || ' '
                                                   || vt.hamluong
                                                   || ' ('
                                                   || vt.tenvattu, ')')
                                        ELSE
                                            upper(substr(vt.hoatchat, 1, 1))
                                            || lower(substr(vt.hoatchat, 2))
                                            || ' '
                                            || vt.hamluong
                                            || ' ('
                                            || vt.tenvattu
                                            || ')'
                                    END
                                )
END AS mota_chitiet1,
                        CASE
                            WHEN vt.maloaihinh = 5005 THEN
                                upper(vt.tenvattu)
                            ELSE
                                (
                                    CASE
                                        WHEN TRIM(vt.hoatchat) IS NULL THEN
                                            concat(upper(vt.tenvattu)
                                                   || ' '
                                                   || vt.hamluong
                                                   || ' ('
                                                   || vt.tenvattu, ')')
                                        ELSE
                                            vt.tenvattu
                                            || ' '
                                            || vt.hamluong
                                            || ' ('
                                            || upper(substr(vt.hoatchat, 1, 1))
                                            || lower(substr(vt.hoatchat, 2))
                                            || ')'
                                    END
                                )
END AS mota_chitiet2,
                        upper(vt.tenvattu)
                        || ' '
                        || nvl(vt.hamluong, '') AS mota_chitiet3,
                        'SL: '
                        ||
                            CASE instr(CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30)), '.')
                                WHEN 1 THEN
                                    '0'
                                    || CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30))
                                ELSE
                                    CASE
                                        WHEN SUM(ct.so_luong_thuc_linh) < 10 THEN
                                            concat(0, CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30)))
                                        ELSE
                                            CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30))
END
END
|| concat(' ', vt.dvt)
                        || ' '
                        || '' AS so_luong_uong,
                        CASE
                            WHEN sang_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Sáng ', chuyen_thapphan_sang_phanso(sang_uong))
                                || ' '
                                || cach_su_dung
END AS sang_uong,
                        CASE
                            WHEN trua_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Trưa ', chuyen_thapphan_sang_phanso(trua_uong))
                                || ' '
                                || cach_su_dung
END AS trua_uong,
                        CASE
                            WHEN chieu_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Chiều ', chuyen_thapphan_sang_phanso(chieu_uong))
                                || ' '
                                || cach_su_dung
END AS chieu_uong,
                        CASE
                            WHEN toi_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Tối ', chuyen_thapphan_sang_phanso(toi_uong))
                                || ' '
                                || cach_su_dung
END AS toi_uong,
                        CASE ghi_chu_ct_toa_thuoc
                            WHEN NULL   THEN
                                ''
                            WHEN ''     THEN
                                ''
                            ELSE
                                concat('Ghi chú: ', ghi_chu_ct_toa_thuoc)
END AS ghi_chu_ct_toa_thuoc,
                        ma_toa_thuoc,
                        SUM(so_luong) AS so_luong,
                        ma_bac_si_themthuoc,
                        SUM(so_luong_thuc_linh) AS so_luong_thuc_linh,
                        SUM(thanhtien_thuoc) AS thanhtien_thuoc,
                        CASE cach_su_dung
                            WHEN NULL THEN
                                ''
                            ELSE
                                '( '
                                || cach_su_dung
                                || ' )'
END AS cach_su_dung,
                        CASE ghi_chu_ct_toa_thuoc
                            WHEN NULL THEN
                                ''
                            ELSE
                                concat(ghi_chu_ct_toa_thuoc, ' :')
END AS ghi_chu_ct_toa_thuoc1,
                        v_countbs             AS tt,
                        vt.hoatchat,
                        vt.hamluong,
                        CASE
                            WHEN vt.manhomvattu = 33 THEN
                                upper(vt.tenvattu)
                            ELSE
                                upper(vt.tenvattu)
                                || ' '
                                || nvl(vt.hamluong, '')
END AS mota_chitiet,
                        CASE
                            WHEN --start
                             vt.hoatchat IS NULL
                                 OR TRIM(vt.hoatchat) = ''
                                 OR instr(vt.hoatchat, ',') > 0
                                 OR instr(vt.hoatchat, ';') > 0
                                 OR instr(vt.hoatchat, '+') > 0 THEN
                                upper(vt.tenvattu)
                            ELSE
                                upper(vt.hoatchat)
                                || ' ('
                                || vt.tenvattu
                                || ')'
                                || ' '
                                || nvl(vt.hamluong, ' ')
END AS mota_ct_tvh1--tvh them mo ta
                   -- vlg Lợi lấy số giường buồng
                        ,
                        sogiuong.stt_giuong,
                        sogiuong.stt_buong
                   --end vlg Lợi lấy số giường buồng
                        ,
                        ddt.tuoi,
                        ddt.thang
--             ,nvl(dv.sodienthoai,' ') sodienthoai
                        ,
                        CASE
                            WHEN v_hienthisdt = '1' THEN
                                CASE
                                    WHEN nvl(dv.sodienthoai, ' ') = ' ' THEN
                                        nvl(nv.sodienthoai_nhanvien, ' ')
                                    ELSE
                                        nvl(dv.sodienthoai, ' ')
END
WHEN v_hienthisdt = '2' THEN
                                CASE
                                    WHEN nvl(dmpb.sodienthoai, ' ') = ' ' THEN
                                        nvl(nv.sodienthoai_nhanvien, ' ')
                                    ELSE
                                        nvl(dmpb.sodienthoai, ' ')
END
ELSE
                                nvl(dv.sodienthoai, ' ')
END AS sodienthoai,
                        nvl(dt.cannang, ' ') cannang,
                        MAX(ct.so_ngay_uong) AS so_ngay_uong,
                        v_stg_tuoi            AS tuoi_stg,
                        v_sodienthoai_bacsi   AS sdt_bacsi,
                        v_dientienbenh        AS dientienbenh,
                        ct.bant,
                        v_sdt_benhnhan        AS sdt_benhnhan,
                        dv.diachi,
                        v_ngaysinh            AS ngay_sinh,
                        vmadonthuocqg         AS ma_don_thuoc,
                        TO_CHAR(v_songayuong + v_ngaykham, 'dd/mm/yyyy') AS ngaythuoc,
                        '('||dt.ICD_DIEUTRI||') '||DT.TENICD_DIEUTRI || ';' ||DT.TEN_BENHPHU CHANDOAN
                    FROM
                        noitru_ct_toa_thuoc             ct
                        JOIN his_manager.noitru_dieutri      dt ON ct.dvtt = dt.dvtt
                                                              AND ct.sovaovien = dt.sovaovien
                                                              AND ct.sovaovien_dt = dt.sovaovien_dt
                                                              AND ct.id_dieutri = dt.id_dieutri
                        LEFT JOIN his_fw.dm_phongban              dmpb ON dmpb.ma_phongban = dt.khoalap
                        JOIN his_manager.noitru_dotdieutri   ddt ON ddt.dvtt = ct.dvtt
                                                                  AND ddt.sovaovien = ct.sovaovien
                                                                  AND ddt.sovaovien_dt = ct.sovaovien_dt
                        JOIN his_fw.dm_donvi                 dv ON dv.ma_donvi = ddt.dvtt
                        LEFT JOIN his_manager.dc_tb_khovattu      kvt ON ct.dvtt = kvt.dvtt
                                                                    AND ct.makhovattu = kvt.makhovattu   -- vietdn
            -- vlg Lợi lấy số giường buồng
                        LEFT JOIN his_manager.cmu_sobuonggiuong   sogiuong ON ct.dvtt = sogiuong.dvtt
                                                                            AND ct.stt_benhan = sogiuong.stt_benhan
                                                                            AND ct.stt_dotdieutri = sogiuong.stt_dotdieutri
               -- vlg Lợi lấy số giường buồng
                                                                            ,
                        dc_tb_vattu                     vt,
                        his_fw.dm_nhanvien              nv
                    WHERE
                        ma_toa_thuoc = p_ma_toa_thuoc
                        AND ct.nghiep_vu IN (
                            'ba_ngoaitru_toadichvu',
                            'noitru_toadichvu'
                        )
                        AND ct.dvtt = p_dvtt
                        AND ct.mavattu = vt.mavattu
                        AND vt.dvtt = p_dvtt
                        AND ct.stt_dieutri = p_stt_dieutri
                        AND ct.stt_benhan = p_stt_benhan
                        AND ct.stt_dotdieutri = p_stt_dotdieutri
                        AND nv.ma_nhanvien = ct.ma_bac_si_themthuoc
                        AND ct.maloaivattu = v_maloaivattu
                    GROUP BY
                        nv.ten_nhanvien,
                        vt.dvt,
                        vt.tenvattu,
                        vt.hoatchat,
                        vt.hamluong,
                        sogiuong.stt_giuong,
                        sogiuong.stt_buong,
                        sang_uong,
                        trua_uong,
                        chieu_uong,
                        toi_uong,
                        ghi_chu_ct_toa_thuoc,
                        ma_toa_thuoc,
                        ma_bac_si_themthuoc,
                        vt.maloaihinh,
                        cach_su_dung,
                        vt.manhomvattu,
                        kvt.tenkhovattu,
                        ddt.tuoi,
                        ddt.thang,
                        dv.sodienthoai,
                        dt.cannang,
                        v_stg_tuoi,
                        ct.bant,
                        dv.sodienthoai,
                        dmpb.sodienthoai,
                        nv.sodienthoai_nhanvien,
                        dv.diachi,
                        ct.id_dieutri,
                        '('||dt.ICD_DIEUTRI||') '||DT.TENICD_DIEUTRI || ';' ||DT.TEN_BENHPHU
                    HAVING
                        SUM(so_luong) > 0
                    ORDER BY
                        stt_order ASC;

    ELSIF p_nghiep_vu = 'ba_ngoaitru_toathuong' THEN
SELECT
    COUNT(DISTINCT ma_bac_si_themthuoc)
INTO v_countbs
FROM
    noitru_ct_toa_thuoc
WHERE
    dvtt = p_dvtt
  AND ma_toa_thuoc = p_ma_toa_thuoc
  AND stt_dieutri = p_stt_dieutri
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND nghiep_vu = p_nghiep_vu;

OPEN cur FOR SELECT
                        MIN(ct.stt_order) AS stt_order,
                        nvl(v_nguoilienhe, ' ') AS nguoilienhe,
                        v_socmt               AS socmt,
                        concat('Bác sĩ chỉ định thuốc: ', nv.ten_nhanvien) AS ten_nhanvien,
                        vt.dvt,
                        kvt.tenkhovattu,    -- vietdn
                        upper(vt.tenvattu) AS tenvattu,
                        (
                            CASE
                                WHEN v_hienthigiatoadichvu = '1'
                                     AND ct.nghiep_vu IN (
                                    'noitru_toadichvu',
                                    'ba_ngoaitru_toadichvu'
                                ) THEN   -- BDH Thêm đơn giá
                                    (
                                        CASE
                                            WHEN vt.maloaihinh = 5005 THEN
                                                upper(vt.tenvattu)
                                                || ' ('
                                                || ct.dongia_ban_bv
                                                || ' đ)'
                                            ELSE
                                                (
                                                    CASE
                                                        WHEN TRIM(vt.hoatchat) IS NULL THEN
                                                            concat(upper(vt.tenvattu)
                                                                   || ' '
                                                                   || vt.hamluong
                                                                   || ' ('
                                                                   || vt.tenvattu, ')')
                                                            || ' ('
                                                            || ct.dongia_ban_bv
                                                            || ')'
                                                        ELSE
                                                            upper(substr(vt.hoatchat, 1, 1))
                                                            || lower(substr(vt.hoatchat, 2))
                                                            || ' '
                                                            || vt.hamluong
                                                            || ' ('
                                                            || vt.tenvattu
                                                            || ')'
                                                            || ' ('
                                                            || ct.dongia_ban_bv
                                                            || ' đ)'
                                                    END
                                                )
                                        END
                                    )
                                ELSE                                       -- END BDH
                                    (
                                        CASE
                                            WHEN vt.maloaihinh = 5005 THEN
                                                upper(vt.tenvattu)
                                            ELSE
                                                (
                                                    CASE
                                                        WHEN TRIM(vt.hoatchat) IS NULL THEN
                                                            concat(upper(vt.tenvattu)
                                                                   || ' '
                                                                   || vt.hamluong
                                                                   || ' ('
                                                                   || vt.tenvattu, ')')
                                                        ELSE
                                                            upper(substr(vt.hoatchat, 1, 1))
                                                            || lower(substr(vt.hoatchat, 2))
                                                            || ' '
                                                            || vt.hamluong
                                                            || ' ('
                                                            || vt.tenvattu
                                                            || ')'
                                                    END
                                                )
                                        END
                                    )
                            END
                        ) AS mota_chitiet1,
                        (
                            CASE
                                WHEN v_hienthigiatoadichvu = '1'
                                     AND ct.nghiep_vu IN (
                                    'noitru_toadichvu',
                                    'ba_ngoaitru_toadichvu'
                                ) THEN   -- BDH Thêm đơn giá
                                    (
                                        CASE
                                            WHEN vt.maloaihinh = 5005 THEN
                                                upper(vt.tenvattu)
                                                || ' ('
                                                || ct.dongia_ban_bv
                                                || ' đ)'
                                            ELSE
                                                (
                                                    CASE
                                                        WHEN TRIM(vt.hoatchat) IS NULL THEN
                                                            concat(upper(vt.tenvattu)
                                                                   || ' '
                                                                   || vt.hamluong
                                                                   || ' ('
                                                                   || vt.tenvattu, ')')
                                                            || ' ('
                                                            || ct.dongia_ban_bv
                                                            || ' đ)'
                                                        ELSE
                                                            vt.tenvattu
                                                            || ' '
                                                            || vt.hamluong
                                                            || ' ('
                                                            || upper(substr(vt.hoatchat, 1, 1))
                                                            || lower(substr(vt.hoatchat, 2))
                                                            || ')'
                                                            || ' ('
                                                            || ct.dongia_ban_bv
                                                            || ' đ)'
                                                    END
                                                )
                                        END
                                    )
                                ELSE                                        -- END BDH
                                    (
                                        CASE
                                            WHEN vt.maloaihinh = 5005 THEN
                                                upper(vt.tenvattu)
                                            ELSE
                                                (
                                                    CASE
                                                        WHEN TRIM(vt.hoatchat) IS NULL THEN
                                                            concat(upper(vt.tenvattu)
                                                                   || ' '
                                                                   || vt.hamluong
                                                                   || ' ('
                                                                   || vt.tenvattu, ')')
                                                        ELSE
                                                            vt.tenvattu
                                                            || ' '
                                                            || vt.hamluong
                                                            || ' ('
                                                            || upper(substr(vt.hoatchat, 1, 1))
                                                            || lower(substr(vt.hoatchat, 2))
                                                            || ')'
                                                    END
                                                )
                                        END
                                    )
                            END
                        ) AS mota_chitiet2,
                        (
                            CASE
                                WHEN v_hienthigiatoadichvu = '1'
                                     AND ct.nghiep_vu IN (
                                    'noitru_toadichvu',
                                    'ba_ngoaitru_toadichvu'
                                ) THEN   -- BDH Thêm đơn giá
                                    upper(vt.tenvattu)
                                    || ' '
                                    || nvl(vt.hamluong, '')
                                    || ' ('
                                    || ct.dongia_ban_bv
                                    || ' đ)'
                                ELSE                                          -- END BDH
                                    upper(vt.tenvattu)
                                    || ' '
                                    || nvl(vt.hamluong, '')
                            END
                        ) AS mota_chitiet3,
                        'SL: '
                        ||
                            CASE instr(CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30)), '.')
                                WHEN 1 THEN
                                    '0'
                                    || CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30))
                                ELSE
                                    CASE
                                        WHEN SUM(ct.so_luong_thuc_linh) < 10 THEN
                                            concat(0, CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30)))
                                        ELSE
                                            CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30))
END
END
|| concat(' ', vt.dvt)
                        || ' '
                        || '' AS so_luong_uong,
                        CASE
                            WHEN sang_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Sáng ', chuyen_thapphan_sang_phanso(sang_uong))
                                || ' '
                                || cach_su_dung
END AS sang_uong,
                        CASE
                            WHEN trua_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Trưa ', chuyen_thapphan_sang_phanso(trua_uong))
                                || ' '
                                || cach_su_dung
END AS trua_uong,
                        CASE
                            WHEN chieu_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Chiều ', chuyen_thapphan_sang_phanso(chieu_uong))
                                || ' '
                                || cach_su_dung
END AS chieu_uong,
                        CASE
                            WHEN toi_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Tối ', chuyen_thapphan_sang_phanso(toi_uong))
                                || ' '
                                || cach_su_dung
END AS toi_uong,
                        CASE ghi_chu_ct_toa_thuoc
                            WHEN NULL   THEN
                                ''
                            WHEN ''     THEN
                                ''
                            ELSE
                                concat('Ghi chú: ', ghi_chu_ct_toa_thuoc)
END AS ghi_chu_ct_toa_thuoc,
                        ma_toa_thuoc,
                        SUM(so_luong) AS so_luong,
                        ma_bac_si_themthuoc,
                        SUM(so_luong_thuc_linh) AS so_luong_thuc_linh,
                        SUM(thanhtien_thuoc) AS thanhtien_thuoc,
                        CASE cach_su_dung
                            WHEN NULL THEN
                                ''
                            ELSE
                                '( '
                                || cach_su_dung
                                || ' )'
END AS cach_su_dung,
                        CASE ghi_chu_ct_toa_thuoc
                            WHEN NULL THEN
                                ''
                            ELSE
                                concat(ghi_chu_ct_toa_thuoc, ' :')
END AS ghi_chu_ct_toa_thuoc1,
                        v_countbs             AS tt,
                        vt.dongia_bv          AS dongiathuoc_bv,
                        vt.hoatchat,
                        vt.hamluong,
                        CASE
                            WHEN vt.manhomvattu = 33 THEN
                                upper(vt.tenvattu)
                            ELSE
                                upper(vt.tenvattu)
                                || ' '
                                || nvl(vt.hamluong, '')
END AS mota_chitiet,
                        CASE
                            WHEN --start
                             vt.hoatchat IS NULL
                                 OR TRIM(vt.hoatchat) = ''
                                 OR instr(vt.hoatchat, ',') > 0
                                 OR instr(vt.hoatchat, ';') > 0
                                 OR instr(vt.hoatchat, '+') > 0 THEN
                                upper(vt.tenvattu)
                            ELSE
                                upper(vt.hoatchat)
                                || ' ('
                                || vt.tenvattu
                                || ')'
                                || ' '
                                || nvl(vt.hamluong, ' ')
END AS mota_ct_tvh1--tvh them mo ta
                   -- vlg Lợi lấy số giường buồng
                        ,
                        sogiuong.stt_giuong,
                        sogiuong.stt_buong
                   --end vlg Lợi lấy số giường buồng
                        ,
                        ddt.tuoi,
                        ddt.thang
--                   ,nvl(dv.sodienthoai,' ') sodienthoai
                        ,
                        CASE
                            WHEN v_hienthisdt = '1' THEN
                                CASE
                                    WHEN nvl(dv.sodienthoai, ' ') = ' ' THEN
                                        nvl(nv.sodienthoai_nhanvien, ' ')
                                    ELSE
                                        nvl(dv.sodienthoai, ' ')
END
WHEN v_hienthisdt = '2' THEN
                                CASE
                                    WHEN nvl(dmpb.sodienthoai, ' ') = ' ' THEN
                                        nvl(nv.sodienthoai_nhanvien, ' ')
                                    ELSE
                                        nvl(dmpb.sodienthoai, ' ')
END
ELSE
                                nvl(dv.sodienthoai, ' ')
END AS sodienthoai,
                        nvl(dt.cannang, ' ') cannang,
                        MAX(ct.so_ngay_uong) AS so_ngay_uong,
                        v_sodienthoai_bacsi   AS sdt_bacsi,
                        v_dientienbenh        AS dientienbenh,
                        ct.bant,
                        v_sdt_benhnhan        AS sdt_benhnhan,
                        dv.diachi,
                        v_ngaysinh            AS ngay_sinh,
                        vmadonthuocqg         AS ma_don_thuoc,
                        TO_CHAR(v_songayuong + v_ngaykham, 'dd/mm/yyyy') AS ngaythuoc,
                        '('||dt.ICD_DIEUTRI||') '||DT.TENICD_DIEUTRI || ';' ||DT.TEN_BENHPHU CHANDOAN
                    FROM
                        noitru_ct_toa_thuoc             ct
                        JOIN his_manager.noitru_dieutri      dt ON ct.dvtt = dt.dvtt
                                                              AND ct.sovaovien = dt.sovaovien
                                                              AND ct.sovaovien_dt = dt.sovaovien_dt
                                                              AND ct.id_dieutri = dt.id_dieutri
                        LEFT JOIN his_fw.dm_phongban              dmpb ON dmpb.ma_phongban = dt.khoalap
                        JOIN his_manager.noitru_dotdieutri   ddt ON ddt.dvtt = ct.dvtt
                                                                  AND ddt.sovaovien = ct.sovaovien
                                                                  AND ddt.sovaovien_dt = ct.sovaovien_dt
                        JOIN his_fw.dm_donvi                 dv ON dv.ma_donvi = ddt.dvtt
                        LEFT JOIN his_manager.dc_tb_khovattu      kvt ON ct.dvtt = kvt.dvtt
                                                                    AND ct.makhovattu = kvt.makhovattu
                        LEFT JOIN his_manager.cmu_sobuonggiuong   sogiuong ON ct.dvtt = sogiuong.dvtt
                                                                            AND ct.stt_benhan = sogiuong.stt_benhan
                                                                            AND ct.stt_dotdieutri = sogiuong.stt_dotdieutri,
                        dc_tb_vattu                     vt,
                        his_fw.dm_nhanvien              nv
                    WHERE
                        ma_toa_thuoc = p_ma_toa_thuoc
                        AND ct.nghiep_vu = 'ba_ngoaitru_toathuoc'
                        AND ct.dvtt = p_dvtt
                        AND ct.mavattu = vt.mavattu
                        AND vt.dvtt = p_dvtt
                        AND ct.stt_dieutri = p_stt_dieutri
                        AND ct.stt_benhan = p_stt_benhan
                        AND ct.stt_dotdieutri = p_stt_dotdieutri
                        AND nv.ma_nhanvien = ct.ma_bac_si_themthuoc
                        AND ct.maloaivattu NOT LIKE 'TH_HTT'
                        AND ct.maloaivattu NOT LIKE 'TH_NGHIEN'
                    GROUP BY
                        nv.ten_nhanvien,
                        vt.dvt,
                        vt.tenvattu,
                        vt.hoatchat,
                        vt.hamluong,
                        sogiuong.stt_giuong,
                        sogiuong.stt_buong,
                        sang_uong,
                        trua_uong,
                        chieu_uong,
                        toi_uong,
                        ghi_chu_ct_toa_thuoc,
                        ma_toa_thuoc,
                        ma_bac_si_themthuoc,
                        vt.maloaihinh,
                        cach_su_dung,
                        vt.dongia_bv,
                        ct.dongia_ban_bv, -- BDH
                        ct.nghiep_vu,
                        vt.manhomvattu,
                        kvt.tenkhovattu,
                        ddt.tuoi,
                        ddt.thang,
                        dv.sodienthoai,
                        dt.cannang,
                        ct.bant,
                        dv.sodienthoai,
                        dmpb.sodienthoai,
                        nv.sodienthoai_nhanvien,
                        dv.diachi,
                        ct.id_dieutri,
                        '('||dt.ICD_DIEUTRI||') '||DT.TENICD_DIEUTRI || ';' ||DT.TEN_BENHPHU
                    HAVING
                        SUM(so_luong) > 0
                    ORDER BY
                        stt_order ASC;

ELSE
SELECT
    COUNT(DISTINCT ma_bac_si_themthuoc)
INTO v_countbs
FROM
    noitru_ct_toa_thuoc
WHERE
    dvtt = p_dvtt
  AND ma_toa_thuoc = p_ma_toa_thuoc
  AND stt_dieutri = p_stt_dieutri
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND nghiep_vu = p_nghiep_vu;

OPEN cur FOR SELECT
                        MIN(ct.stt_order) AS stt_order,
                        nvl(v_nguoilienhe, ' ') AS nguoilienhe,
                        v_socmt               AS socmt,
                        concat('Bác sĩ chỉ định thuốc: ', nv.ten_nhanvien) AS ten_nhanvien,
                        vt.dvt,
                        kvt.tenkhovattu,    -- vietdn
                        upper(vt.tenvattu) AS tenvattu,
                        (
                            CASE
                                WHEN v_hienthigiatoadichvu = '1'
                                     AND ct.nghiep_vu IN (
                                    'noitru_toadichvu',
                                    'ba_ngoaitru_toadichvu'
                                ) THEN   -- BDH Thêm đơn giá
                                    (
                                        CASE
                                            WHEN vt.maloaihinh = 5005 THEN
                                                upper(vt.tenvattu)
                                                || ' ('
                                                || ct.dongia_ban_bv
                                                || ' đ)'
                                            ELSE
                                                (
                                                    CASE
                                                        WHEN TRIM(vt.hoatchat) IS NULL THEN
                                                            concat(upper(vt.tenvattu)
                                                                   || ' '
                                                                   || vt.hamluong
                                                                   || ' ('
                                                                   || vt.tenvattu, ')')
                                                            || ' ('
                                                            || ct.dongia_ban_bv
                                                            || ')'
                                                        ELSE
                                                            upper(substr(vt.hoatchat, 1, 1))
                                                            || lower(substr(vt.hoatchat, 2))
                                                            || ' '
                                                            || vt.hamluong
                                                            || ' ('
                                                            || vt.tenvattu
                                                            || ')'
                                                            || ' ('
                                                            || ct.dongia_ban_bv
                                                            || ' đ)'
                                                    END
                                                )
                                        END
                                    )
                                ELSE                                       -- END BDH
                                    (
                                        CASE
                                            WHEN vt.maloaihinh = 5005 THEN
                                                upper(vt.tenvattu)
                                            ELSE
                                                (
                                                    CASE
                                                        WHEN TRIM(vt.hoatchat) IS NULL THEN
                                                            concat(upper(vt.tenvattu)
                                                                   || ' '
                                                                   || vt.hamluong
                                                                   || ' ('
                                                                   || vt.tenvattu, ')')
                                                        ELSE
                                                            upper(substr(vt.hoatchat, 1, 1))
                                                            || lower(substr(vt.hoatchat, 2))
                                                            || ' '
                                                            || vt.hamluong
                                                            || ' ('
                                                            || vt.tenvattu
                                                            || ')'
                                                    END
                                                )
                                        END
                                    )
                            END
                        ) AS mota_chitiet1,
                        (
                            CASE
                                WHEN v_hienthigiatoadichvu = '1'
                                     AND ct.nghiep_vu IN (
                                    'noitru_toadichvu',
                                    'ba_ngoaitru_toadichvu'
                                ) THEN   -- BDH Thêm đơn giá
                                    (
                                        CASE
                                            WHEN vt.maloaihinh = 5005 THEN
                                                upper(vt.tenvattu)
                                                || ' ('
                                                || ct.dongia_ban_bv
                                                || ' đ)'
                                            ELSE
                                                (
                                                    CASE
                                                        WHEN TRIM(vt.hoatchat) IS NULL THEN
                                                            concat(upper(vt.tenvattu)
                                                                   || ' '
                                                                   || vt.hamluong
                                                                   || ' ('
                                                                   || vt.tenvattu, ')')
                                                            || ' ('
                                                            || ct.dongia_ban_bv
                                                            || ' đ)'
                                                        ELSE
                                                            vt.tenvattu
                                                            || ' '
                                                            || vt.hamluong
                                                            || ' ('
                                                            || upper(substr(vt.hoatchat, 1, 1))
                                                            || lower(substr(vt.hoatchat, 2))
                                                            || ')'
                                                            || ' ('
                                                            || ct.dongia_ban_bv
                                                            || ' đ)'
                                                    END
                                                )
                                        END
                                    )
                                ELSE                                        -- END BDH
                                    (
                                        CASE
                                            WHEN vt.maloaihinh = 5005 THEN
                                                upper(vt.tenvattu)
                                            ELSE
                                                (
                                                    CASE
                                                        WHEN TRIM(vt.hoatchat) IS NULL THEN
                                                            concat(upper(vt.tenvattu)
                                                                   || ' '
                                                                   || vt.hamluong
                                                                   || ' ('
                                                                   || vt.tenvattu, ')')
                                                        ELSE
                                                            vt.tenvattu
                                                            || ' '
                                                            || vt.hamluong
                                                            || ' ('
                                                            || upper(substr(vt.hoatchat, 1, 1))
                                                            || lower(substr(vt.hoatchat, 2))
                                                            || ')'
                                                    END
                                                )
                                        END
                                    )
                            END
                        ) AS mota_chitiet2,
                        (
                            CASE
                                WHEN v_hienthigiatoadichvu = '1'
                                     AND ct.nghiep_vu IN (
                                    'noitru_toadichvu',
                                    'ba_ngoaitru_toadichvu'
                                ) THEN   -- BDH Thêm đơn giá
                                    upper(vt.tenvattu)
                                    || ' '
                                    || nvl(vt.hamluong, '')
                                    || ' ('
                                    || ct.dongia_ban_bv
                                    || ' đ)'
                                ELSE                                          -- END BDH
                                    upper(vt.tenvattu)
                                    || ' '
                                    || nvl(vt.hamluong, '')
                            END
                        ) AS mota_chitiet3,
                        'SL: '
                        ||
                            CASE instr(CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30)), '.')
                                WHEN 1 THEN
                                    '0'
                                    || CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30))
                                ELSE
                                    CASE
                                        WHEN SUM(ct.so_luong_thuc_linh) < 10 THEN
                                            concat(0, CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30)))
                                        ELSE
                                            CAST(CAST(SUM(ct.so_luong_thuc_linh) AS NUMBER(18, 4)) AS VARCHAR2(30))
END
END
|| concat(' ', vt.dvt)
                        || ' '
                        || '' AS so_luong_uong,
                        CASE
                            WHEN sang_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Sáng ', chuyen_thapphan_sang_phanso(sang_uong))
                                || ' '
                                || cach_su_dung
END AS sang_uong,
                        CASE
                            WHEN trua_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Trưa ', chuyen_thapphan_sang_phanso(trua_uong))
                                || ' '
                                || cach_su_dung
END AS trua_uong,
                        CASE
                            WHEN chieu_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Chiều ', chuyen_thapphan_sang_phanso(chieu_uong))
                                || ' '
                                || cach_su_dung
END AS chieu_uong,
                        CASE
                            WHEN toi_uong = 0 THEN
                                NULL
                            ELSE
                                concat('Tối ', chuyen_thapphan_sang_phanso(toi_uong))
                                || ' '
                                || cach_su_dung
END AS toi_uong,
                        CASE ghi_chu_ct_toa_thuoc
                            WHEN NULL   THEN
                                ''
                            WHEN ''     THEN
                                ''
                            ELSE
                                concat('Ghi chú: ', ghi_chu_ct_toa_thuoc)
END AS ghi_chu_ct_toa_thuoc,
                        ma_toa_thuoc,
                        SUM(so_luong) AS so_luong,
                        ma_bac_si_themthuoc,
                        SUM(so_luong_thuc_linh) AS so_luong_thuc_linh,
                        SUM(thanhtien_thuoc) AS thanhtien_thuoc,
                        CASE cach_su_dung
                            WHEN NULL THEN
                                ''
                            ELSE
                                '( '
                                || cach_su_dung
                                || ' )'
END AS cach_su_dung,
                        CASE ghi_chu_ct_toa_thuoc
                            WHEN NULL THEN
                                ''
                            ELSE
                                concat(ghi_chu_ct_toa_thuoc, ' :')
END AS ghi_chu_ct_toa_thuoc1,
                        v_countbs             AS tt,
                        vt.dongia_bv          AS dongiathuoc_bv,
                        vt.hoatchat,
                        vt.hamluong,
                        CASE
                            WHEN vt.manhomvattu = 33 THEN
                                upper(vt.tenvattu)
                            ELSE
                                upper(vt.tenvattu)
                                || ' '
                                || nvl(vt.hamluong, '')
END AS mota_chitiet,
                        CASE
                            WHEN --start
                             vt.hoatchat IS NULL
                                 OR TRIM(vt.hoatchat) = ''
                                 OR instr(vt.hoatchat, ',') > 0
                                 OR instr(vt.hoatchat, ';') > 0
                                 OR instr(vt.hoatchat, '+') > 0 THEN
                                upper(vt.tenvattu)
                            ELSE
                                upper(vt.hoatchat)
                                || ' ('
                                || vt.tenvattu
                                || ')'
                                || ' '
                                || nvl(vt.hamluong, ' ')
END AS mota_ct_tvh1--tvh them mo ta
                   -- vlg Lợi lấy số giường buồng
                        ,
                        sogiuong.stt_giuong,
                        sogiuong.stt_buong
                   --end vlg Lợi lấy số giường buồng
                        ,
                        ddt.tuoi,
                        ddt.thang
--             ,nvl(dv.sodienthoai,' ') sodienthoai
                        ,
                        CASE
                            WHEN v_hienthisdt = '1' THEN
                                CASE
                                    WHEN nvl(dv.sodienthoai, ' ') = ' ' THEN
                                        nvl(nv.sodienthoai_nhanvien, ' ')
                                    ELSE
                                        nvl(dv.sodienthoai, ' ')
END
WHEN v_hienthisdt = '2' THEN
                                CASE
                                    WHEN nvl(dmpb.sodienthoai, ' ') = ' ' THEN
                                        nvl(nv.sodienthoai_nhanvien, ' ')
                                    ELSE
                                        nvl(dmpb.sodienthoai, ' ')
END
ELSE
                                nvl(dv.sodienthoai, ' ')
END AS sodienthoai,
                        nvl(dt.cannang, ' ') cannang,
                        MAX(ct.so_ngay_uong) AS so_ngay_uong,
                        v_sodienthoai_bacsi   AS sdt_bacsi,
                        v_dientienbenh        AS dientienbenh,
                        ct.bant,
                        v_sdt_benhnhan        AS sdt_benhnhan,
                        dv.diachi,
                        v_ngaysinh            AS ngay_sinh,
                        vmadonthuocqg         AS ma_don_thuoc,
                        TO_CHAR(v_songayuong + v_ngaykham, 'dd/mm/yyyy') AS ngaythuoc,
                        '('||dt.ICD_DIEUTRI||') '||DT.TENICD_DIEUTRI || ';' ||DT.TEN_BENHPHU CHANDOAN
                    FROM
                        noitru_ct_toa_thuoc             ct
                        JOIN his_manager.noitru_dieutri      dt ON ct.dvtt = dt.dvtt
                                                              AND ct.sovaovien = dt.sovaovien
                                                              AND ct.sovaovien_dt = dt.sovaovien_dt
                                                              AND ct.id_dieutri = dt.id_dieutri
                        LEFT JOIN his_fw.dm_phongban              dmpb ON dmpb.ma_phongban = dt.khoalap
                        JOIN his_manager.noitru_dotdieutri   ddt ON ddt.dvtt = ct.dvtt
                                                                  AND ddt.sovaovien = ct.sovaovien
                                                                  AND ddt.sovaovien_dt = ct.sovaovien_dt
                        JOIN his_fw.dm_donvi                 dv ON dv.ma_donvi = ddt.dvtt
                        LEFT JOIN his_manager.dc_tb_khovattu      kvt ON ct.dvtt = kvt.dvtt
                                                                    AND ct.makhovattu = kvt.makhovattu   -- vietdn
            -- vlg Lợi lấy số giường buồng
                        LEFT JOIN his_manager.cmu_sobuonggiuong   sogiuong ON ct.dvtt = sogiuong.dvtt
                                                                            AND ct.stt_benhan = sogiuong.stt_benhan
                                                                            AND ct.stt_dotdieutri = sogiuong.stt_dotdieutri
               -- vlg Lợi lấy số giường buồng
                                                                            ,
                        dc_tb_vattu                     vt,
                        his_fw.dm_nhanvien              nv
                    WHERE
                        ma_toa_thuoc = p_ma_toa_thuoc
                        AND ct.nghiep_vu = p_nghiep_vu
                        AND ct.dvtt = p_dvtt
                        AND ct.mavattu = vt.mavattu
                        AND vt.dvtt = p_dvtt
                        AND ct.stt_dieutri = p_stt_dieutri
                        AND ct.stt_benhan = p_stt_benhan
                        AND ct.stt_dotdieutri = p_stt_dotdieutri
                        AND nv.ma_nhanvien = ct.ma_bac_si_themthuoc
                        AND ( p_toa = 'toa_t'
                              OR ct.maloaivattu = v_maloaivattu )
                    GROUP BY
                        nv.ten_nhanvien,
                        vt.dvt,
                        vt.tenvattu,
                        vt.hoatchat,
                        vt.hamluong,
                        sogiuong.stt_giuong,
                        sogiuong.stt_buong,
                        sang_uong,
                        trua_uong,
                        chieu_uong,
                        toi_uong,
                        ghi_chu_ct_toa_thuoc,
                        ma_toa_thuoc,
                        ma_bac_si_themthuoc,
                        vt.maloaihinh,
                        cach_su_dung,
                        vt.dongia_bv,
                        ct.dongia_ban_bv, -- BDH
                        ct.nghiep_vu,
                        vt.manhomvattu,
                        kvt.tenkhovattu,
                        ddt.tuoi,
                        ddt.thang,
                        dv.sodienthoai,
                        dt.cannang,
                        ct.bant,
                        dv.sodienthoai,
                        dmpb.sodienthoai,
                        nv.sodienthoai_nhanvien,
                        dv.diachi,
                        ct.id_dieutri,
                        '('||dt.ICD_DIEUTRI||') '||DT.TENICD_DIEUTRI || ';' ||DT.TEN_BENHPHU
                    HAVING
                        SUM(so_luong) > 0
                    ORDER BY
                        stt_order ASC;

END IF;

EXCEPTION
    WHEN OTHERS THEN
        raise_application_error(-20001, dbms_utility.format_error_stack);
END;