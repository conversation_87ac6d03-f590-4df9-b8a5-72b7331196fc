function getBANGOAITRUYHCTJSON () {
    let form;
    let formTongket;
    let formPage1;
    return {
        script: {},
        scriptTongket: {},
        initObjectFormPage1: function(edit, hidden) {
            return getJSONObjectForm([
                {
                    "key": "p-chandoan",
                    "type": "tabs",
                    "customClass": "hsba-tabs-wrap",
                    "components": [
                        {
                            "label": "QUẢN LÝ NGƯỜI BỆNH",
                            "key": "tabQuanLyNguoiBenh",
                            "components": [
                                getObjectQuanLyNguoiBenhVBAT1_1(edit, hidden),
                            ]
                        },
                        {
                            "label": "CHẨN ĐOÁN",
                            "key": "tabChanDoan",
                            "components": [
                                getObjectChanDoanVBAT1_3(edit, hidden),
                            ]
                        },
                        {
                            "label": "TÌNH TRẠNG RA VIỆN",
                            "key": "tabTinhTrangRaVien",
                            "components": [
                                getObjectTinhTrangRaVienVBAT1_2(edit, hidden),
                            ]
                        },
                    ]
                },
            ])
        },
        initObjectFormPage2: function() {
            return getJSONObjectForm([
                {
                    "collapsible": true,
                    "key": "p-lydovaovien",
                    "type": "panel",
                    "label": "Lý do vào viện",
                    "title": "A. Y HỌC HIỆN ĐẠI",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap hsba-tabs-wrap--input-40px",
                    "components": [
                        {
                            "collapsible": true,
                            "key": "p-lydovaovien",
                            "type": "panel",
                            "label": "Lý do vào viện",
                            "title": "BỆNH ÁN VÀ HỎI BỆNH",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components": [
                                {
                                    "label": "Lý do vào viện",
                                    "key": "LYDOVAOVIEN",
                                    "type": "textarea",
                                    rows: 2,
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                                {
                                    "label": "Bệnh sử",
                                    others: {
                                        "tooltip": "Quá trình bệnh lý: (khởi phát, diễn biến, chẩn đoán, điều trị tuyến dưới, v.v...)",
                                    },
                                    "key": "BENHSU",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                                {
                                    "label": "Tiền sử bệnh (bản thân)",
                                    others: {
                                        "tooltip": " Bản thân: (phát triển thể lực từ nhỏ đến lớn, những bệnh đã mắc, phương pháp ĐTr, tiêm phòng, ăn uống, sinh hoạt vv...)",
                                    },
                                    "key": "TIENSUBANTHAN",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                                {
                                    "label": " ",
                                    "validate": {
                                        "custom": "if (data.TIENSU.length > 4) {\r\n    valid = \"Không thể chọn quá 4 lựa chọn\";\r\n} else {\r\n    valid = true;\r\n}",
                                    },
                                    "key": "TIENSU",
                                    others: {
                                        "data": {
                                            "values": [
                                                {
                                                    "label": "1. Dị ứng",
                                                    "value": "1"
                                                },
                                                {
                                                    "label": "2. Rượu",
                                                    "value": "2"
                                                },
                                                {
                                                    "label": "3. Ma túy",
                                                    "value": "3"
                                                },
                                                {
                                                    "label": "4. Thuốc lá",
                                                    "value": "4"
                                                },
                                                {
                                                    "label": "5. Khác",
                                                    "value": "5"
                                                }
                                            ]
                                        },
                                        "input": true,
                                        "widget": "choicesjs",
                                        "tableView": true,
                                        "multiple": true,
                                    },
                                    "type": "select",
                                },
                                {
                                    "label": "Mô tả (nếu có)",
                                    "key": "MOTABANTHAN",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                                {
                                    "label": "Đặc điểm liên quan đến bệnh tật",
                                    "key": "TIENSUBENHTAT",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                                {
                                    "label": "Tiền sử bệnh (gia đình)",
                                    "key": "TIENSUGIADINH",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                            ]
                        },
                        {
                            "collapsible": true,
                            "key": "p-khambenh",
                            "type": "panel",
                            "label": "Khám bệnh",
                            "title": "KHÁM BỆNH",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components": [
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "Khám toàn thân",
                                                    "others": {
                                                        "tooltip": " Toàn thân: (ý thức, da niêm mạc, hệ thống hạch, tuyến giáp, vị trí, kích thước)",
                                                    },
                                                    "key": "KHAMTOANTHAN",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    }
                                                },
                                                {
                                                    "label": "Tuần hoàn",
                                                    "key": "TUANHOAN",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    }
                                                },
                                                {
                                                    "label": "Hô hấp",
                                                    "key": "HOHAP",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    }
                                                },
                                                {
                                                    "label": "Tiêu hóa",
                                                    "key": "TIEUHOA",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    }
                                                }
                                            ],
                                            "width": 7,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 7
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "Tabs",
                                                    "components": [
                                                        {
                                                            "label": "Chỉ số sinh tồn",
                                                            "key": "sinhhieu",
                                                            "components": [
                                                                {
                                                                    "label": "chisosinhton1",
                                                                    "columns": [
                                                                        {
                                                                            "components": [
                                                                                {
                                                                                    "label": "Mạch",
                                                                                    "customClass": "pr-2",
                                                                                    "validate": {
                                                                                        "maxLength": 20,
                                                                                        required: true
                                                                                    },
                                                                                    "key": "MACH",
                                                                                    "type": "textarea",
                                                                                }
                                                                            ],
                                                                            "width": 4,
                                                                            "offset": 0,
                                                                            "push": 0,
                                                                            "pull": 0,
                                                                            "size": "md",
                                                                            "currentWidth": 4
                                                                        },
                                                                        {
                                                                            "components": [
                                                                                {
                                                                                    "label": "Nhiệt độ",
                                                                                    "customClass": "pr-2",
                                                                                    "validate": {
                                                                                        "min": 35,
                                                                                        "max": 43,
                                                                                        required: true
                                                                                    },
                                                                                    "key": "NHIETDO",
                                                                                    "type": "number",
                                                                                }
                                                                            ],
                                                                            "width": 4,
                                                                            "offset": 0,
                                                                            "push": 0,
                                                                            "pull": 0,
                                                                            "size": "md",
                                                                            "currentWidth": 4
                                                                        },
                                                                        {
                                                                            "components": [
                                                                                {
                                                                                    "label": "Nhịp thở",
                                                                                    "validate": {
                                                                                        "maxLength": 20,
                                                                                        required: true
                                                                                    },
                                                                                    "key": "NHIPTHO",
                                                                                    "type": "textarea",
                                                                                }
                                                                            ],
                                                                            "size": "md",
                                                                            "width": 4,
                                                                            "offset": 0,
                                                                            "push": 0,
                                                                            "pull": 0,
                                                                            "currentWidth": 4
                                                                        }
                                                                    ],
                                                                    "customClass": "ml-0 mr-0",
                                                                    "key": "chisosinhton1",
                                                                    "type": "columns",
                                                                },
                                                                {
                                                                    "label": "chisosinhton2",
                                                                    "columns": [
                                                                        {
                                                                            "components": [
                                                                                {
                                                                                    "label": "Cân nặng (kg)",
                                                                                    "customClass": "pr-2",
                                                                                    "validate": {
                                                                                        "min": 0,
                                                                                        "max": 400,
                                                                                        required: true
                                                                                    },
                                                                                    "key": "CANNANG",
                                                                                    "type": "number",
                                                                                }
                                                                            ],
                                                                            "width": 4,
                                                                            "size": "md",
                                                                        },
                                                                        {
                                                                            "components": [
                                                                                {
                                                                                    "label": "Chiều cao (cm)",
                                                                                    "customClass": "pr-2",
                                                                                    "validate": {
                                                                                        "min": 1,
                                                                                        "max": 400,
                                                                                        required: true
                                                                                    },
                                                                                    "key": "CHIEUCAO",
                                                                                    "type": "number",
                                                                                }
                                                                            ],
                                                                            "width": 4,
                                                                            "offset": 0,
                                                                            "push": 0,
                                                                            "pull": 0,
                                                                            "size": "md",
                                                                            "currentWidth": 4
                                                                        },
                                                                        {
                                                                            "components": [
                                                                                {
                                                                                    "label": "BMI",
                                                                                    "key": "BMI",
                                                                                    others: {
                                                                                        "disabled": true,
                                                                                        "attributes": {
                                                                                            "readonly": "true"
                                                                                        },
                                                                                    },
                                                                                    "type": "number",
                                                                                }
                                                                            ],
                                                                            "size": "md",
                                                                            "width": 4,
                                                                            "offset": 0,
                                                                            "push": 0,
                                                                            "pull": 0,
                                                                            "currentWidth": 4
                                                                        }
                                                                    ],
                                                                    "customClass": "ml-0 mr-0",
                                                                    "key": "chisosinhton2",
                                                                    "type": "columns",
                                                                },
                                                                {
                                                                    "label": "chisosinhton3",
                                                                    "columns": [
                                                                        {
                                                                            "components": [
                                                                                {
                                                                                    "tag": "label",
                                                                                    "content": "Huyết áp",
                                                                                    "refreshOnChange": false,
                                                                                    "key": "htmllabel_huyetap",
                                                                                    "type": "htmlelement",
                                                                                },
                                                                            ],
                                                                            "width": 12,
                                                                            "size": "md",
                                                                        },
                                                                        {
                                                                            "components": [
                                                                                {
                                                                                    "label": "",
                                                                                    "customClass": "pr-2",
                                                                                    "validate": {
                                                                                        "maxLength": 20,
                                                                                        required: true
                                                                                    },
                                                                                    "key": "HUYETAPTREN",
                                                                                    "type": "textarea",
                                                                                }
                                                                            ],
                                                                            "width": 6,
                                                                            "size": "md",
                                                                        },
                                                                        {
                                                                            "components": [
                                                                                {
                                                                                    "label": "",
                                                                                    "validate": {
                                                                                        "maxLength": 20,
                                                                                        required: true
                                                                                    },
                                                                                    "key": "HUYETAPDUOI",
                                                                                    "type": "textarea",
                                                                                }
                                                                            ],
                                                                            "width": 6,
                                                                            "offset": 0,
                                                                            "push": 0,
                                                                            "pull": 0,
                                                                            "size": "md",
                                                                            "currentWidth": 6
                                                                        }
                                                                    ],
                                                                    "customClass": "ml-0 mr-0",
                                                                    "key": "chisosinhton3",
                                                                    "type": "columns",
                                                                }
                                                            ]
                                                        }
                                                    ],
                                                    "customClass": "hsba-tabs-wrap pl-3",
                                                    "key": "tabs",
                                                    "type": "tabs",
                                                },
                                            ],
                                            "width": 5,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            customClass: "pl-2",
                                            "currentWidth": 5
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "Tiết niệu - Sinh dục",
                                                    "key": "TIETNIEUSINHDUC",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    },
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "Thần kinh",
                                                    "key": "THANKINH",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    }
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "Cơ - Xương - Khớp",
                                                    "key": "XUONGKHOP",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    },
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "Tai - Mũi - Họng",
                                                    "key": "TMH",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    }
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "Răng - Hàm - Mặt",
                                                    "key": "RHM",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    },
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "Mắt",
                                                    "key": "MAT",
                                                    "type": "textarea",
                                                    "rows": 2,
                                                    "input": true,
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    }
                                                },
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 6
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                                {
                                    "label": "Nội tiết, dinh dưỡng và các bệnh lý khác",
                                    "key": "NOITIET",
                                    "type": "textarea",
                                    "rows": 1,
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                                {
                                    "label": "Copy cận lâm sàng",
                                    "customClass": "text-right form-control-sm line-height-1",
                                    "key": "copyclstdt",
                                    "type": "button",
                                    others: {
                                        "leftIcon": "fa fa-ellipsis-v",
                                        "action": "event",
                                        "showValidations": false,
                                        "event": "openmodalcopycls",
                                        "type": "button",
                                    }
                                },
                                {
                                    "label": "Các xét nghiệm cận lâm sàng cần làm",
                                    "key": "CLS",
                                    "type": "textarea",
                                    "rows": 2,
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                            ]
                        },
                        {
                            "collapsible": true,
                            "key": "p-chandoanvadieutri",
                            "type": "panel",
                            "label": "CHẨN ĐOÁN VÀ ĐIỀU TRỊ",
                            "title": "CHẨN ĐOÁN VÀ ĐIỀU TRỊ",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components": [
                                {
                                    label: "",
                                    key: "wrap_benhchinh",
                                    columns: [
                                        {
                                            "components": [
                                                {
                                                    "tag": "label",
                                                    "content": "Bệnh chính",
                                                    "refreshOnChange": false,
                                                    "key": "htmllabel_benhchinh",
                                                    "type": "htmlelement",
                                                },
                                            ],
                                            "width": 12,
                                            "size": "md",
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "",
                                                    "key": "BENHCHINH",
                                                    "type": "textfield",
                                                    customClass: "pr-2",
                                                    others: {
                                                        "placeholder": "ICD bệnh chính",
                                                    }
                                                },
                                            ],
                                            "width": 2,
                                            "size": "md",
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "",
                                                    "key": "TENBENHCHINH",
                                                    "type": "textfield",
                                                    others: {
                                                        "placeholder": "Tên bệnh chính",
                                                    },
                                                    validate: {
                                                        required: true
                                                    },
                                                },
                                            ],
                                            "width": 10,
                                            "size": "md",
                                        },
                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "type": "columns",
                                },
                                {
                                    label: "",
                                    key: "wrap_benhphu",
                                    columns: [
                                        {
                                            "components": [
                                                {
                                                    "tag": "label",
                                                    "attrs": [
                                                        {
                                                            "attr": "",
                                                            "value": ""
                                                        }
                                                    ],
                                                    "content": "Bệnh kèm theo (nếu có)",
                                                    "key": "htmllabel_benhphu",
                                                    "type": "htmlelement",
                                                },
                                            ],
                                            "width": 12,
                                            "size": "md",
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "",
                                                    "key": "MABENHPHU1",
                                                    "type": "textfield",
                                                    customClass: "pr-2",
                                                    others: {
                                                        "placeholder": "ICD bệnh phụ",
                                                    }
                                                },
                                            ],
                                            "width": 2,
                                            "size": "md",
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "",
                                                    "key": "TENBENHPHU1",
                                                    "type": "textfield",
                                                    others: {
                                                        "placeholder": "Tên bệnh phụ",
                                                    }
                                                },
                                            ],
                                            "width": 10,
                                            "size": "md",
                                        },
                                        {
                                            "components": [
                                                {
                                                    "tag": "label",
                                                    "content": "Phân biệt",
                                                    "refreshOnChange": false,
                                                    "key": "htmllabel_phanbiet",
                                                    "type": "htmlelement",
                                                },
                                            ],
                                            "width": 12,
                                            "size": "md",
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "",
                                                    "key": "PHANBIET",
                                                    "type": "textarea",
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    },
                                                    "rows": 2,
                                                    "input": true,
                                                    customClass: "pr-2",
                                                },
                                            ],
                                            "width": 10,
                                            "size": "md",
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "",
                                                    "key": "BENHPHU",
                                                    "type": "textfield",
                                                },
                                            ],
                                            "width": 2,
                                            "size": "md",
                                        },
                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "type": "columns",
                                },
                            ]
                        },
                    ]
                },
                {
                    "collapsible": true,
                    "key": "p-lydovaovien",
                    "type": "panel",
                    "label": "Lý do vào viện",
                    "title": "B. Y HỌC CỔ TRUYỀN",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap hsba-tabs-wrap--input-40px",
                    "components": [
                        {
                            "collapsible": true,
                            "key": "p-lydovaovien",
                            "type": "panel",
                            "label": "Lý do vào viện",
                            "title": "I. VỌNG CHẨN",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components": [
                                {
                                    "label": "I. VỌNG CHẨN",
                                    "key": "MOTAVONGCHAN",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    },
                                    others: {
                                        "hideLabel": true,
                                    }
                                },
                            ]
                        },
                        {
                            "collapsible": true,
                            "key": "p-lydovaovien",
                            "type": "panel",
                            "label": "Lý do vào viện",
                            "title": "II. VĂN CHẨN",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components": [
                                {
                                    "label": "II. VĂN CHẨN",
                                    "key": "MOTAVANCHAN",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    },
                                    others: {
                                        "hideLabel": true,
                                    }
                                },
                            ]
                        },
                        {
                            "collapsible": true,
                            "key": "p-lydovaovien",
                            "type": "panel",
                            "label": "Lý do vào viện",
                            "title": "III. VẤN CHẨN",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components": [
                                {
                                    "label": "III. VẤN CHẨN",
                                    "key": "MOTAKHACVANCHAN",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    },
                                    others: {
                                        "hideLabel": true,
                                    }
                                },
                            ]
                        },
                        {
                            "collapsible": true,
                            "key": "p-lydovaovien",
                            "type": "panel",
                            "label": "Lý do vào viện",
                            "title": "IV. THIẾT CHẨN",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components": [
                                {
                                    "label": "1. Xúc chẩn",
                                    "key": "XUCCHAN",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    },
                                },
                                {
                                    "label": "left",
                                    "columns": [
                                        {
                                            "components": [
                                                {
                                                    "label": "2. Mạch chẩn: Mạch tay trái",
                                                    "key": "MACHTAYTRAI",
                                                    "type": "textarea",
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    },
                                                    customClass: "pr-2",
                                                }
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 4
                                        },
                                        {
                                            "components": [
                                                {
                                                    "label": "Mạch tay phải",
                                                    "key": "MACHTAYPHAI",
                                                    "type": "textarea",
                                                    "validate": {
                                                        "maxLength": 3000,
                                                    },
                                                }
                                            ],
                                            "width": 6,
                                            "offset": 0,
                                            "push": 0,
                                            "pull": 0,
                                            "size": "md",
                                            "currentWidth": 4
                                        },

                                    ],
                                    "customClass": "ml-0 mr-0",
                                    "key": "kb-column",
                                    "type": "columns",
                                    "input": false,
                                    "tableView": false
                                },
                            ]
                        },
                        {
                            "collapsible": true,
                            "key": "p-lydovaovien",
                            "type": "panel",
                            "label": "Lý do vào viện",
                            "title": "V. TÓM TẮT TỨ CHẨN",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components": [
                                {
                                    "label": "V. TÓM TẮT TỨ CHẨN",
                                    "key": "TOMTATTUCHAN",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    },
                                    others: {
                                        "hideLabel": true,
                                    }
                                },
                            ]
                        },
                        {
                            "collapsible": true,
                            "key": "p-lydovaovien",
                            "type": "panel",
                            "label": "Lý do vào viện",
                            "title": "VI. BIỆN CHỨNG LUẬN TRỊ",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components": [
                                {
                                    "label": "VI. BIỆN CHỨNG LUẬN TRỊ",
                                    "key": "BIENCHUNGLUANTRI",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    },
                                    others: {
                                        "hideLabel": true,
                                    }
                                },
                            ]
                        },
                    ]
                },
                {
                    "collapsible": true,
                    "key": "p-lydovaovien",
                    "type": "panel",
                    "label": "Lý do vào viện",
                    "title": "VII. CHẨN ĐOÁN",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap hsba-tabs-wrap--input-40px",
                    "components": [
                        {
                            label: "",
                            key: "wrap_benhdanh",
                            columns: [
                                {
                                    "components": [
                                        {
                                            "label": "1. Bệnh danh",
                                            "key": "BENHDANH",
                                            "type": "textarea",
                                            "validate": {
                                                "maxLength": 3000,
                                            },
                                            customClass: "pr-2",
                                        },
                                    ],
                                    "width": 6,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "2. Bát cương",
                                            "key": "BATCUONG",
                                            "type": "textarea",
                                            "validate": {
                                                "maxLength": 3000,
                                            },
                                        },
                                    ],
                                    "width": 6,
                                    "size": "md",
                                },
                            ],
                            "customClass": "ml-0 mr-0",
                            "type": "columns",
                        },
                        {
                            label: "",
                            key: "wrap_benhdanh",
                            columns: [
                                {
                                    "components": [
                                        {
                                            "label": "3. Nguyên nhân",
                                            "key": "NGUYENNHAN",
                                            "type": "textarea",
                                            "validate": {
                                                "maxLength": 3000,
                                            },
                                            customClass: "pr-2",
                                        },
                                    ],
                                    "width": 6,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "4. Tạng phủ",
                                            "key": "TANGPHU",
                                            "type": "textarea",
                                            "validate": {
                                                "maxLength": 3000,
                                            },
                                        },
                                    ],
                                    "width": 6,
                                    "size": "md",
                                },
                            ],
                            "customClass": "ml-0 mr-0",
                            "type": "columns",
                        },
                        {
                            label: "",
                            key: "wrap_benhdanh",
                            columns: [
                                {
                                    "components": [
                                        {
                                            "label": "5. Kinh mạch",
                                            "key": "KINHMACH",
                                            "type": "textarea",
                                            "validate": {
                                                "maxLength": 3000,
                                            },
                                            customClass: "pr-2",
                                        },
                                    ],
                                    "width": 6,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "6. Định vị bệnh (dinh, vệ, khí, huyết)",
                                            "key": "DINHVIBENH",
                                            "type": "textarea",
                                            "validate": {
                                                "maxLength": 3000,
                                            },
                                        },
                                    ],
                                    "width": 6,
                                    "size": "md",
                                },
                            ],
                            "customClass": "ml-0 mr-0",
                            "type": "columns",
                        },
                    ]
                },
                {
                    "collapsible": true,
                    "key": "p-lydovaovien",
                    "type": "panel",
                    "label": "C. ĐIỀU TRỊ",
                    "title": "C. ĐIỀU TRỊ",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap hsba-tabs-wrap--input-40px",
                    "components": [
                        {
                            "collapsible": true,
                            "key": "p-lydovaovien",
                            "type": "panel",
                            "label": "I. Y HỌC CỔ TRUYỀN",
                            "title": "I. Y HỌC CỔ TRUYỀN",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components": [
                                {
                                    "label": "1. Pháp điều trị",
                                    "key": "PHAPDIEUTRI",
                                    "type": "textarea",
                                    "rows": 2,
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                                {
                                    "label": "2.1. Phương dược",
                                    "key": "PHUONGDUOC",
                                    "type": "textarea",
                                    "rows": 2,
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                                {
                                    "label": "2.2. Phương pháp điều trị không dùng thuốc",
                                    "key": "PPKHONGDUNGTHUOC",
                                    "type": "textarea",
                                    "rows": 2,
                                    "validate": {
                                        "maxLength": 3000,
                                    }
                                },
                            ]
                        },
                        {
                            "collapsible": true,
                            "key": "p-lydovaovien",
                            "type": "panel",
                            "label": "II. Y HỌC HIỆN ĐẠI",
                            "title": "II. Y HỌC HIỆN ĐẠI",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components":[
                                {
                                    "label": "II. Y HỌC HIỆN ĐẠI",
                                    others: {
                                        'hideLabel': true,
                                        'tooltip': 'Hướng điều trị: (Phương pháp điều trị, chế độ dinh dưỡng, chế độ chăm sóc,...)'
                                    },
                                    "key": "YHOCHIENDAI",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    },
                                },
                            ]
                        },
                        {
                            "collapsible": true,
                            "key": "p-lydovaovien",
                            "type": "panel",
                            "label": "III. DỰ HẬU (TIÊN LƯỢNG)",
                            "title": "III. DỰ HẬU (TIÊN LƯỢNG)",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            "components":[
                                {
                                    "label": "III. DỰ HẬU (TIÊN LƯỢNG)",
                                    others: {
                                        'hideLabel': true,
                                    },
                                    "key": "DUHAU",
                                    "type": "textarea",
                                    "validate": {
                                        "maxLength": 3000,
                                    },
                                },
                            ]
                        }
                    ]
                },
                {
                    "label": "Ngày làm bệnh án",
                    "key": "NGAYBSLAMBENHAN",
                    "type": "datetime",
                    format: "dd/MM/yyyy",
                    enableTime: false,
                    customClass: "pr-2",
                    minDate: moment(thongtinhsba.thongtinbn.NGAY_VAO_VIEN, ['DD/MM/YYYY']).format("YYYY-MM-DD"),
                    "validate": {
                        "required": true
                    },
                    others: {
                        "hidden": true
                    }
                }
            ])
        },
        initObjectFormPage3: function() {
            return getJSONObjectForm([
                {
                    "collapsible": true,
                    "key": "p-tongketdieutri",
                    "type": "panel",
                    "label": "TỔNG KẾT BỆNH ÁN",
                    "title": "TỔNG KẾT BỆNH ÁN",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap",
                    components: [
                        {
                            "label": "Lý do vào viện",
                            "key": "LYDOVAOVIEN",
                            "type": "textarea",
                            customClass: "pr-2",
                            rows: 2,
                            "validate": {
                                "maxLength": 3000,
                            }
                        },
                        {
                            "label": "Quá trình bệnh lý và diễn biến lâm sàng",
                            "key": "QUATRINH_BENHLY",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "maxLength": 3000,
                            }
                        },
                        {
                        	"label": "Copy cận lâm sàng",
                        	"customClass": "text-right form-control-sm line-height-1",
                        	"key": "copytomtatcls",
                        	"type": "button",
                        	others: {
                        		"leftIcon": "fa fa-ellipsis-v",
                        		"action": "event",
                        		"showValidations": false,
                        		"event": "openmodalcopytomtatcls",
                        		"type": "button",
                        	}
                        },
                        {
                            "label": "Tóm tắt kết quả cận lâm sàng",
                            "key": "tomTatKetQuaXNCLS",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "maxLength": 3000,
                            }
                        },
                        {
                            "collapsible": true,
                            "key": "p-tongketdieutri",
                            "type": "panel",
                            "label": "4. Chẩn đoán vào viện",
                            "title": "4. Chẩn đoán vào viện",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            components: [
                                {
                                    "collapsible": true,
                                    "key": "p-tongketdieutri",
                                    "type": "panel",
                                    "label": "   - Y học hiện đại",
                                    "title": "   - Y học hiện đại",
                                    "collapsed": false,
                                    "input": false,
                                    "tableView": false,
                                    "customClass": "hsba-tabs-wrap",
                                    components: [
                                        {
                                            label: "",
                                            key: "wrap_yhhdbenhchinh",
                                            columns: [
                                                {
                                                    "components": [
                                                        {
                                                            "tag": "label",
                                                            "content": "Bệnh chính",
                                                            "refreshOnChange": false,
                                                            "key": "htmllabel_benhchinh",
                                                            "type": "htmlelement",
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHHDICDBENHCHINH",
                                                            "type": "textfield",
                                                            customClass: "pr-2",
                                                            others: {
                                                                "placeholder": "ICD",
                                                            }
                                                        },
                                                    ],
                                                    "width": 2,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHHDICDBENHCHINH_TEN",
                                                            "type": "textfield",
                                                            others: {
                                                                "placeholder": "Tên bệnh chính",
                                                            },
                                                            validate: {
                                                                required: true
                                                            },
                                                        },
                                                    ],
                                                    "width": 10,
                                                    "size": "md",
                                                },

                                            ],
                                            "customClass": "ml-0 mr-0",
                                            "type": "columns",
                                        },
                                        {
                                            label: "",
                                            key: "wrap_benhphu",
                                            columns: [
                                                {
                                                    "components": [
                                                        {
                                                            "tag": "label",
                                                            "attrs": [
                                                                {
                                                                    "attr": "",
                                                                    "value": ""
                                                                }
                                                            ],
                                                            "content": "Bệnh kèm theo (nếu có)",
                                                            "key": "htmllabel_benhphu",
                                                            "type": "htmlelement",
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "ICD_VAOVIEN_YHHDBENHPHU",
                                                            "type": "textfield",
                                                            customClass: "pr-2",
                                                            others: {
                                                                "placeholder": "ICD",
                                                            }
                                                        },
                                                    ],
                                                    "width": 2,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "TENICD_VAOVIEN_YHHDBENHPHU",
                                                            "type": "textfield",
                                                            others: {
                                                                "placeholder": "Tên bệnh",
                                                            }
                                                        },
                                                    ],
                                                    "width": 10,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHHDBENHKEMTHEO",
                                                            "type": "textarea",
                                                            "validate": {
                                                                "maxLength": 3000,
                                                            },
                                                            "rows": 2,
                                                            "input": true,
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                            ],
                                            "customClass": "ml-0 mr-0",
                                            "type": "columns",
                                        },
                                    ]
                                },
                                {
                                    "collapsible": true,
                                    "key": "p-tongketdieutri",
                                    "type": "panel",
                                    "label": "   - Y học cổ truyền",
                                    "title": "   - Y học cổ truyền",
                                    "collapsed": false,
                                    "input": false,
                                    "tableView": false,
                                    "customClass": "hsba-tabs-wrap",
                                    components: [
                                        {
                                            label: "",
                                            key: "wrap_yhhdbenhchinh",
                                            columns: [
                                                {
                                                    "components": [
                                                        {
                                                            "tag": "label",
                                                            "content": "Bệnh chính",
                                                            "refreshOnChange": false,
                                                            "key": "htmllabel_benhchinh",
                                                            "type": "htmlelement",
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHCTICDBENHCHINH",
                                                            "type": "textfield",
                                                            customClass: "pr-2",
                                                            others: {
                                                                "placeholder": "ICD",
                                                            }
                                                        },
                                                    ],
                                                    "width": 2,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHCTICDBENHCHINH_TEN",
                                                            "type": "textfield",
                                                            others: {
                                                                "placeholder": "Tên bệnh chính",
                                                            },
                                                            validate: {
                                                                required: true
                                                            },
                                                        },
                                                    ],
                                                    "width": 10,
                                                    "size": "md",
                                                },

                                            ],
                                            "customClass": "ml-0 mr-0",
                                            "type": "columns",
                                        },
                                        {
                                            label: "",
                                            key: "wrap_benhphu",
                                            columns: [
                                                {
                                                    "components": [
                                                        {
                                                            "tag": "label",
                                                            "attrs": [
                                                                {
                                                                    "attr": "",
                                                                    "value": ""
                                                                }
                                                            ],
                                                            "content": "Bệnh kèm theo (nếu có)",
                                                            "key": "htmllabel_benhphu",
                                                            "type": "htmlelement",
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "ICD_VAOVIEN_YHCTBENHPHU",
                                                            "type": "textfield",
                                                            customClass: "pr-2",
                                                            others: {
                                                                "placeholder": "ICD",
                                                            }
                                                        },
                                                    ],
                                                    "width": 2,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "TENICD_VAOVIEN_YHCTBENHPHU",
                                                            "type": "textfield",
                                                            others: {
                                                                "placeholder": "Tên bệnh",
                                                            }
                                                        },
                                                    ],
                                                    "width": 10,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHCTBENHKEMTHEO",
                                                            "type": "textarea",
                                                            "validate": {
                                                                "maxLength": 3000,
                                                            },
                                                            "rows": 2,
                                                            "input": true,
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                            ],
                                            "customClass": "ml-0 mr-0",
                                            "type": "columns",
                                        },
                                    ]
                                }
                            ]
                        },
                        {
                            "label": "Phương pháp điều trị - Y học hiện đại",
                            "key": "YHHDPHUONGPHAPDIEUTRI",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "maxLength": 3000,
                            }
                        },
                        {
                            "label": "Phương pháp điều trị - Y học cổ truyền",
                            "key": "YHCTPHUONGPHAPDIEUTRI",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "maxLength": 3000,
                            }
                        },
                        {
                            "label": "6. Kết quả điều trị",
                            others: {
                                "data": {
                                    "values": [
                                        {
                                            "label": "1. Khỏi",
                                            "value": "1"
                                        },
                                        {
                                            "label": "2. Đỡ",
                                            "value": "2"
                                        },
                                        {
                                            "label": "3. Không thay đổi",
                                            "value": "3"
                                        },
                                        {
                                            "label": "4. Nặng hơn",
                                            "value": "4"
                                        },
                                        {
                                            "label": "5. Tử vong",
                                            "value": "5"
                                        },
                                    ]
                                },
                            },
                            key: "KETQUADIEUTRI",
                            "type": "select",
                        },
                        {
                            "collapsible": true,
                            "key": "p-tongketdieutri",
                            "type": "panel",
                            "label": "7. Chẩn đoán ra viện",
                            "title": "7. Chẩn đoán ra viện",
                            "collapsed": false,
                            "input": false,
                            "tableView": false,
                            "customClass": "hsba-tabs-wrap",
                            components: [
                                {
                                    "collapsible": true,
                                    "key": "p-tongketdieutri",
                                    "type": "panel",
                                    "label": "   - Y học hiện đại",
                                    "title": "   - Y học hiện đại",
                                    "collapsed": false,
                                    "input": false,
                                    "tableView": false,
                                    "customClass": "hsba-tabs-wrap",
                                    components: [
                                        {
                                            label: "",
                                            key: "wrap_yhhdbenhchinh",
                                            columns: [
                                                {
                                                    "components": [
                                                        {
                                                            "tag": "label",
                                                            "content": "Bệnh chính",
                                                            "refreshOnChange": false,
                                                            "key": "htmllabel_benhchinh",
                                                            "type": "htmlelement",
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHHDRAVIENICDBENHCHINH",
                                                            "type": "textfield",
                                                            customClass: "pr-2",
                                                            others: {
                                                                "placeholder": "ICD",
                                                            }
                                                        },
                                                    ],
                                                    "width": 2,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHHDRAVIENICDBENHCHINH_TEN",
                                                            "type": "textfield",
                                                            others: {
                                                                "placeholder": "Tên bệnh chính",
                                                            },
                                                            validate: {
                                                                required: true
                                                            },
                                                        },
                                                    ],
                                                    "width": 10,
                                                    "size": "md",
                                                },

                                            ],
                                            "customClass": "ml-0 mr-0",
                                            "type": "columns",
                                        },
                                        {
                                            label: "",
                                            key: "wrap_benhphu",
                                            columns: [
                                                {
                                                    "components": [
                                                        {
                                                            "tag": "label",
                                                            "attrs": [
                                                                {
                                                                    "attr": "",
                                                                    "value": ""
                                                                }
                                                            ],
                                                            "content": "Bệnh kèm theo (nếu có)",
                                                            "key": "htmllabel_benhphu",
                                                            "type": "htmlelement",
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "ICD_RAVIEN_YHHDBENHPHU",
                                                            "type": "textfield",
                                                            customClass: "pr-2",
                                                            others: {
                                                                "placeholder": "ICD",
                                                            }
                                                        },
                                                    ],
                                                    "width": 2,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "TENICD_RAVIEN_YHHDBENHPHU",
                                                            "type": "textfield",
                                                            others: {
                                                                "placeholder": "Tên bệnh",
                                                            }
                                                        },
                                                    ],
                                                    "width": 10,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHHDRAVIENBENHKEMTHEO_TEN",
                                                            "type": "textarea",
                                                            "validate": {
                                                                "maxLength": 3000,
                                                            },
                                                            "rows": 2,
                                                            "input": true,
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                            ],
                                            "customClass": "ml-0 mr-0",
                                            "type": "columns",
                                        },
                                    ]
                                },
                                {
                                    "collapsible": true,
                                    "key": "p-tongketdieutri",
                                    "type": "panel",
                                    "label": "   - Y học cổ truyền",
                                    "title": "   - Y học cổ truyền",
                                    "collapsed": false,
                                    "input": false,
                                    "tableView": false,
                                    "customClass": "hsba-tabs-wrap",
                                    components: [
                                        {
                                            label: "",
                                            key: "wrap_yhhdbenhchinh",
                                            columns: [
                                                {
                                                    "components": [
                                                        {
                                                            "tag": "label",
                                                            "content": "Bệnh chính",
                                                            "refreshOnChange": false,
                                                            "key": "htmllabel_benhchinh",
                                                            "type": "htmlelement",
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHCTRAVIENICDBENHCHINH",
                                                            "type": "textfield",
                                                            customClass: "pr-2",
                                                            others: {
                                                                "placeholder": "ICD",
                                                            }
                                                        },
                                                    ],
                                                    "width": 2,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHCTRAVIENICDBENHCHINH_TEN",
                                                            "type": "textfield",
                                                            others: {
                                                                "placeholder": "Tên bệnh chính",
                                                            },
                                                            validate: {
                                                                required: true
                                                            },
                                                        },
                                                    ],
                                                    "width": 10,
                                                    "size": "md",
                                                },

                                            ],
                                            "customClass": "ml-0 mr-0",
                                            "type": "columns",
                                        },
                                        {
                                            label: "",
                                            key: "wrap_benhphu",
                                            columns: [
                                                {
                                                    "components": [
                                                        {
                                                            "tag": "label",
                                                            "attrs": [
                                                                {
                                                                    "attr": "",
                                                                    "value": ""
                                                                }
                                                            ],
                                                            "content": "Bệnh kèm theo (nếu có)",
                                                            "key": "htmllabel_benhphu",
                                                            "type": "htmlelement",
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "ICD_RAVIEN_YHCTBENHPHU",
                                                            "type": "textfield",
                                                            customClass: "pr-2",
                                                            others: {
                                                                "placeholder": "ICD",
                                                            }
                                                        },
                                                    ],
                                                    "width": 2,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "TENICD_RAVIEN_YHCTBENHPHU",
                                                            "type": "textfield",
                                                            others: {
                                                                "placeholder": "Tên bệnh",
                                                            }
                                                        },
                                                    ],
                                                    "width": 10,
                                                    "size": "md",
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "",
                                                            "key": "YHCTRAVIENBENHKEMTHEO_TEN",
                                                            "type": "textarea",
                                                            "validate": {
                                                                "maxLength": 3000,
                                                            },
                                                            "rows": 2,
                                                            "input": true,
                                                        },
                                                    ],
                                                    "width": 12,
                                                    "size": "md",
                                                },
                                            ],
                                            "customClass": "ml-0 mr-0",
                                            "type": "columns",
                                        },
                                    ]
                                }
                            ]
                        },
                        {
                            "label": "8. Tình trạng người bệnh khi ra viện",
                            "key": "TINHTRANG_RAVIEN",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "maxLength": 3000,
                            }
                        },
                        {
                            "label": "9. Hướng điều trị và các chế độ tiếp theo",
                            "key": "HUONG_DIEUTRI",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "maxLength": 3000,
                            }
                        },
                    ]
                },
                {
                    "collapsible": true,
                    "key": "p-tongsohosophimanh",
                    "type": "panel",
                    "label": "THỜI GIAN BÁC SỸ LÀM TỔNG KẾT",
                    "title": "THỜI GIAN BÁC SỸ LÀM TỔNG KẾT",
                    "collapsed": false,
                    "customClass": "hsba-tabs-wrap",
                    components: [
                        {
                            "label": "HSPHIMANH",
                            "columns": [
                                {
                                    "components": [
                                        {
                                            "label": "Ngày giờ kết thúc điều trị",
                                            "key": "NGAY_TONGKET",
                                            "type": "datetime",
                                            format: "dd/MM/yyyy HH:mm",
                                            enableTime: true,
                                            minDate: moment(
                                                thongtinhsba.thongtinbn.NGAY_VAO_VIEN, ['DD/MM/YYYY']
                                            ).format("YYYY-MM-DD"),
                                            validate: {
                                                required: true,
                                            },
                                            customClass: "pr-2",
                                        },
                                    ],
                                    "width": 6,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Thầy thuốc khám bệnh, chữa bệnh",
                                            "key": "MANHANVIEN_GIAOHOSO",
                                            "type": "select",
                                            options: singletonObject.danhsachtatcanhanvien,
                                        },
                                    ],
                                    "width": 6,
                                    "size": "md",
                                },
                            ],
                            "customClass": "mr-0 ml-0",
                            "key": "HSPHIMANH",
                            "type": "columns",
                        },
                    ]
                }
            ]);
        },
        callbackAfterLoad: function (instance) {
            form = instance;

            let tenBenhchinhElement = form.getComponent('TENBENHCHINH');
            let icdBenhchinhElement = form.getComponent('BENHCHINH');
            let tenBenhphuElement = form.getComponent('TENBENHPHU1');
            let icdBenhphuElement = form.getComponent('MABENHPHU1');

            let bmiElement = form.getComponent('BMI');
            let cannangElement = form.getComponent('CANNANG');
            let chieucaoElement = form.getComponent('CHIEUCAO');

            $("#"+getIdElmentFormio(form,'CANNANG')).change(function() {
                if(!$(this).val() || !chieucaoElement.getValue()) { return; }
                bmiElement.setValue(
                    (chieucaoElement.getValue()/Math.pow($(this).val()/100, 2)).toFixed(2));
            });

            $("#"+getIdElmentFormio(form,'CHIEUCAO')).change(function() {
                if(!$(this).val() || !cannangElement.getValue()) { return; }
                bmiElement.setValue((
                    cannangElement.getValue()/Math.pow($(this).val()/100, 2)).toFixed(2));
            });

            instance.on('openmodalcopycls', function() {
                addTextTitleModal("titleModalFormhsbacopyylenhcls")
                $("#modalFormhsbacopyylenhcls").modal("show");
                $(document).trigger("reloadDSCLSChiDinh");
            });

            $("#" + getIdElmentFormio(form, 'BENHCHINH')).on('keypress', function (event) {
                var mabenhICD = $(this).val();
                if (event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function (data) {
                        var splitIcd = data.split("!!!")
                        tenBenhchinhElement.setValue(splitIcd[1]);
                        icdBenhchinhElement.setValue(mabenhICD)
                    })
                }
            });
            $("#" + getIdElmentFormio(form, 'MABENHPHU1')).on('keypress', function (event) {
                var mabenhICD = $(this).val();
                if (event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function (data) {
                        var splitIcd = data.split("!!!")
                        tenBenhphuElement.setValue(splitIcd[1]);
                        tenBenhphuElement.focus()
                        icdBenhphuElement.setValue(mabenhICD)
                    })
                }
            });

            combgridTenICD(getIdElmentFormio(form,'TENBENHCHINH'), function(item) {
                icdBenhchinhElement.setValue(item.ICD);
                tenBenhchinhElement.setValue(item.MO_TA_BENH_LY);
            });
            combgridTenICD(getIdElmentFormio(form,'TENBENHPHU1'), function(item) {
                icdBenhphuElement.setValue(item.ICD);
                tenBenhphuElement.setValue(item.MO_TA_BENH_LY);
            });

            let idWrap = "hsba_vba_trang2-tab";
            showLoaderIntoWrapId(idWrap)
            getThongtinBenhan(
                thongtinhsba.thongtinbn.VOBENHAN[0].ID,
                'NGOAITRUYHCT1941',
                function(dataTrang2) {
                    hideLoaderIntoWrapId(idWrap)
                    delete dataTrang2.ID;
                    dataTrang2.MAKHOA = dataTrang2.MAKHOA? dataTrang2.MAKHOA: singletonObject.makhoa;
                    if(dataTrang2.BENHCHINH && dataTrang2.BENHCHINH.includes(" - ")) {
                        let splitIcd = dataTrang2.BENHCHINH.split(" - ");
                        dataTrang2.ICD_BENHCHINH = splitIcd[0];
                        dataTrang2.TENICD_BENHCHINH = splitIcd[1];
                    }
                    dataTrang2.NGAYBSLAMBENHAN =  (dataTrang2.NGAYBSLAMBENHAN? moment(dataTrang2.NGAYBSLAMBENHAN): moment()).toISOString();
                    mergeMultiSelectAddToObjectFormio(dataTrang2, 'TIENSU', 4);

                    if (dataTrang2.MACH === null || typeof dataTrang2.MACH === 'undefined' || dataTrang2.MACH === "") {
                        let res = $.ajax({
                            url:"cmu_list_CMU_HSBA_GETDEF?url="+ convertArray([
                                singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN,
                                thongtinhsba.thongtinbn.SOVAOVIEN_DT
                            ]),
                            type:"GET",
                            async: false
                        }).responseText;

                        let dataDef = JSON.parse(res);
                        dataTrang2.MACH = dataDef[0].MACH;
                        dataTrang2.NHIETDO = dataDef[0].NHIETDO;
                        dataTrang2.NHIPTHO = dataDef[0].NHIPTHO;
                        dataTrang2.HUYETAPTREN = dataDef[0].HUYETAPTREN;
                        dataTrang2.HUYETAPDUOI = dataDef[0].HUYETAPDUOI;
                        dataTrang2.CANNANG = dataDef[0].CANNANG;
                        dataTrang2.CHIEUCAO = dataDef[0].CHIEUCAO;
                        if(!isNaN(dataTrang2.CANNANG) && !isNaN(dataTrang2.CHIEUCAO)) {
                            dataTrang2.BMI = (
                                dataTrang2.CANNANG/Math.pow(dataTrang2.CHIEUCAO/100, 2)).toFixed(2);
                        }
                    }
                    form.submission =  {
                        data: {
                            ...dataTrang2
                        }
                    };
                },
                function() {
                    hideLoaderIntoWrapId(idWrap)
                    notifiToClient("Red", "Lỗi load thông tin bệnh án")
                });
        },
        save: function(element) {
            let idButton = element.id;
            form.emit("checkValidity");
            if (!form.checkValidity(null, false, null, true)) {
                hideSelfLoading(idButton);
                return;
            }
            let dataSubmit = form.submission.data;
            delete dataSubmit.copyclstdt;
            splitMultiSelectAddToObjectFormio(dataSubmit, 'TIENSU', 4);
            dataSubmit.ID = thongtinhsba.thongtinbn.VOBENHAN[0].ID;
            let ngayba = moment(dataSubmit.NGAYBSLAMBENHAN)
            dataSubmit.NGAYLAMBENHAN = "Ngày " + ngayba.format("DD") + " tháng " + ngayba.format("MM") + " năm " + ngayba.format("YYYY");
            dataSubmit.MABACSILAMBENHAN = singletonObject.userId;
            // dataSubmit.BENHCHINH = dataSubmit.ICD_BENHCHINH + " - " + dataSubmit.TENICD_BENHCHINH;
            console.log(dataSubmit)
            $.post("update-benhan-ngoaitruyhct1941", dataSubmit)
                .done(function () {
                    notifiToClient("Green", "Lưu thành công")

                    $.post("cmu_post_CMU_VBA_NGOAITRUYHCT1941_UPD", {
                        url: [
                            dataSubmit.ID,
                            singletonObject.dvtt,
                            dataSubmit.NGAYLAMBENHAN,
                            dataSubmit.MABACSILAMBENHAN,
                        ].join("```")
                    }).fail(function() {
                        notifiToClient('Red', 'Lưu thông tin người làm không thành công');
                    }).done(function(data) {
                        if (data <= 0) {
                            notifiToClient('Red', 'Lưu thông tin người làm không thành công');
                        }
                    });

                    let Logbandau = []
                    let Logmoi = []
                    let newdata = {};
                    console.log(oldDataTrang2)
                    dataSubmit.NGAYBSLAMBENHAN = moment(dataSubmit.NGAYBSLAMBENHAN).format("MM/DD/YYYY")
                    assignNonNullValuesBA(newdata,dataSubmit);
                    console.log(newdata)
                    let diffObject = findDifferencesBetweenObjects(oldDataTrang2, newdata);
                    console.log(diffObject)
                    for (let key in diffObject) {
                        if (keyLuuLogTrang2.hasOwnProperty(key)) {
                            Logbandau.push(getLabelValueBATrang2(key, oldDataTrang2))
                            Logmoi.push(getLabelValueBATrang2(key, newdata))
                        }
                    }
                    console.log(Logbandau)
                    console.log(Logmoi)
                    if (Logbandau.length !== 0 || Logmoi.length !== 0){
                        luuLogHSBATheoBN({
                            SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                            LOAI: LOGHSBALOAI.NOITRUYHCT.KEY,
                            NOIDUNGBANDAU: Logbandau.join(";"),
                            NOIDUNGMOI: Logmoi.join(";"),
                            USERID: singletonObject.userId,
                            ACTION: LOGHSBAACTION.EDIT.KEY,
                        })
                    }

                }).fail(function () {
                notifiToClient("Red", "Lỗi lưu thông tin")
            }).always(function () {
                hideSelfLoading(idButton);
            })
        },
        callbackAfterLoadTongket: function (instance) {
            formTongket = instance;

            let idWrap = "hsba_vba_trang3-tab";

            $("#"+getIdElmentFormio(formTongket,'YHHDICDBENHCHINH')).on('keypress', function(event) {
                let tenBenhchinhElement = formTongket.getComponent('YHHDICDBENHCHINH_TEN');
                let icdBenhchinhElement = formTongket.getComponent('YHHDICDBENHCHINH');
                let mabenhICD = $(this).val();
                if(event.keyCode === 13 && mabenhICD !== "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        let splitIcd = data.split("!!!")
                        tenBenhchinhElement.setValue(splitIcd[1]);
                        icdBenhchinhElement.setValue(mabenhICD);
                    })
                }
            });

            let icdBenhPhuVaoVienYHHDElement = formTongket.getComponent('ICD_VAOVIEN_YHHDBENHPHU');
            let tenBenhPhuVaoVienYHHDElement = formTongket.getComponent('TENICD_VAOVIEN_YHHDBENHPHU');
            let textBenhPhuVaoVienYHHDElement = formTongket.getComponent('YHHDBENHKEMTHEO');
            $("#"+getIdElmentFormio(formTongket,'ICD_VAOVIEN_YHHDBENHPHU')).on('keypress', function(event) {
                let mabenhICD = $(this).val();
                if(event.keyCode === 13 && mabenhICD !== "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        let splitIcd = data.split("!!!")
                        tenBenhPhuVaoVienYHHDElement.setValue(splitIcd[1]);
                        tenBenhPhuVaoVienYHHDElement.focus()
                        icdBenhPhuVaoVienYHHDElement.setValue(mabenhICD)
                    })
                }
            })
            $("#"+getIdElmentFormio(formTongket,'TENICD_VAOVIEN_YHHDBENHPHU')).on('keypress', function(event) {
                if(event.keyCode === 13) {
                    let stringIcd = textBenhPhuVaoVienYHHDElement.getValue();
                    let mabenhICD = icdBenhPhuVaoVienYHHDElement.getValue()
                    if(!stringIcd.includes(mabenhICD)) {
                        textBenhPhuVaoVienYHHDElement.setValue( stringIcd + "; (" +mabenhICD.toUpperCase() + ") " + tenBenhPhuVaoVienYHHDElement.getValue());
                    }
                    icdBenhPhuVaoVienYHHDElement.setValue("")
                    tenBenhPhuVaoVienYHHDElement.setValue("")
                    icdBenhPhuVaoVienYHHDElement.focus()
                }
            })

            $("#"+getIdElmentFormio(formTongket,'YHCTICDBENHCHINH')).on('keypress', function(event) {
                let tenBenhchinhElement = formTongket.getComponent('YHCTICDBENHCHINH_TEN');
                let icdBenhchinhElement = formTongket.getComponent('YHCTICDBENHCHINH');
                let mabenhICD = $(this).val();
                if(event.keyCode === 13 && mabenhICD !== "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        let splitIcd = data.split("!!!")
                        tenBenhchinhElement.setValue(splitIcd[1]);
                        icdBenhchinhElement.setValue(mabenhICD)
                    })
                }
            });

            let icdBenhPhuVaoVienYHCTElement = formTongket.getComponent('ICD_VAOVIEN_YHCTBENHPHU');
            let tenBenhPhuVaoVienYHCTElement = formTongket.getComponent('TENICD_VAOVIEN_YHCTBENHPHU');
            let textBenhPhuVaoVienYHCTElement = formTongket.getComponent('YHCTBENHKEMTHEO');
            $("#"+getIdElmentFormio(formTongket,'ICD_VAOVIEN_YHCTBENHPHU')).on('keypress', function(event) {
                let mabenhICD = $(this).val();
                if(event.keyCode === 13 && mabenhICD !== "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        let splitIcd = data.split("!!!")
                        tenBenhPhuVaoVienYHCTElement.setValue(splitIcd[1]);
                        tenBenhPhuVaoVienYHCTElement.focus()
                        icdBenhPhuVaoVienYHCTElement.setValue(mabenhICD)
                    })
                }
            });
            $("#"+getIdElmentFormio(formTongket,'TENICD_VAOVIEN_YHCTBENHPHU')).on('keypress', function(event) {
                if(event.keyCode === 13) {
                    let stringIcd = textBenhPhuVaoVienYHCTElement.getValue();
                    let mabenhICD = icdBenhPhuVaoVienYHCTElement.getValue()
                    if(!stringIcd.includes(mabenhICD)) {
                        textBenhPhuVaoVienYHCTElement.setValue( stringIcd + "; (" +mabenhICD.toUpperCase() + ") " + tenBenhPhuVaoVienYHCTElement.getValue());
                    }
                    icdBenhPhuVaoVienYHCTElement.setValue("")
                    tenBenhPhuVaoVienYHCTElement.setValue("")
                    icdBenhPhuVaoVienYHCTElement.focus()
                }
            });

            $("#"+getIdElmentFormio(formTongket,'YHHDRAVIENICDBENHCHINH')).on('keypress', function(event) {
                let tenBenhchinhElement = formTongket.getComponent('YHHDRAVIENICDBENHCHINH_TEN');
                let icdBenhchinhElement = formTongket.getComponent('YHHDRAVIENICDBENHCHINH');
                let mabenhICD = $(this).val();
                if(event.keyCode === 13 && mabenhICD !== "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        let splitIcd = data.split("!!!")
                        tenBenhchinhElement.setValue(splitIcd[1]);
                        icdBenhchinhElement.setValue(mabenhICD)
                    })
                }
            });

            let icdBenhPhuRaVienYHHDElement = formTongket.getComponent('ICD_RAVIEN_YHHDBENHPHU');
            let tenBenhPhuRaVienYHHDElement = formTongket.getComponent('TENICD_RAVIEN_YHHDBENHPHU');
            let textBenhPhuRaVienYHHDElement = formTongket.getComponent('YHHDRAVIENBENHKEMTHEO_TEN');
            $("#"+getIdElmentFormio(formTongket,'ICD_RAVIEN_YHHDBENHPHU')).on('keypress', function(event) {
                let mabenhICD = $(this).val();
                if(event.keyCode === 13 && mabenhICD !== "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        let splitIcd = data.split("!!!")
                        tenBenhPhuRaVienYHHDElement.setValue(splitIcd[1]);
                        tenBenhPhuRaVienYHHDElement.focus()
                        icdBenhPhuRaVienYHHDElement.setValue(mabenhICD)
                    })
                }
            });
            $("#"+getIdElmentFormio(formTongket,'TENICD_RAVIEN_YHHDBENHPHU')).on('keypress', function(event) {
                if(event.keyCode === 13) {
                    let stringIcd = textBenhPhuRaVienYHHDElement.getValue();
                    let mabenhICD = icdBenhPhuRaVienYHHDElement.getValue()
                    if(!stringIcd.includes(mabenhICD)) {
                        textBenhPhuRaVienYHHDElement.setValue( stringIcd + "; (" +mabenhICD.toUpperCase() + ") " + tenBenhPhuRaVienYHHDElement.getValue());
                    }
                    icdBenhPhuRaVienYHHDElement.setValue("")
                    tenBenhPhuRaVienYHHDElement.setValue("")
                    icdBenhPhuRaVienYHHDElement.focus()
                }
            });

            $("#"+getIdElmentFormio(formTongket,'YHCTRAVIENICDBENHCHINH')).on('keypress', function(event) {
                let tenBenhchinhElement = formTongket.getComponent('YHCTRAVIENICDBENHCHINH_TEN');
                let icdBenhchinhElement = formTongket.getComponent('YHCTRAVIENICDBENHCHINH');
                let mabenhICD = $(this).val();
                if(event.keyCode === 13 && mabenhICD !== "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        let splitIcd = data.split("!!!")
                        tenBenhchinhElement.setValue(splitIcd[1]);
                        icdBenhchinhElement.setValue(mabenhICD)
                    })
                }
            });
            let icdBenhPhuRaVienYHCTElement = formTongket.getComponent('ICD_RAVIEN_YHCTBENHPHU');
            let tenBenhPhuRaVienYHCTElement = formTongket.getComponent('TENICD_RAVIEN_YHCTBENHPHU');
            let textBenhPhuRaVienYHCTElement = formTongket.getComponent('YHCTRAVIENBENHKEMTHEO_TEN');
            $("#"+getIdElmentFormio(formTongket,'ICD_RAVIEN_YHCTBENHPHU')).on('keypress', function(event) {
                let mabenhICD = $(this).val();
                if(event.keyCode === 13 && mabenhICD !== "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        let splitIcd = data.split("!!!")
                        tenBenhPhuRaVienYHCTElement.setValue(splitIcd[1]);
                        tenBenhPhuRaVienYHCTElement.focus()
                        icdBenhPhuRaVienYHCTElement.setValue(mabenhICD)
                    })
                }
            });
            $("#"+getIdElmentFormio(formTongket,'TENICD_RAVIEN_YHCTBENHPHU')).on('keypress', function(event) {
                if(event.keyCode === 13) {
                    let stringIcd = textBenhPhuRaVienYHCTElement.getValue();
                    let mabenhICD = icdBenhPhuRaVienYHCTElement.getValue()
                    if(!stringIcd.includes(mabenhICD)) {
                        textBenhPhuRaVienYHCTElement.setValue( stringIcd + "; (" +mabenhICD.toUpperCase() + ") " + tenBenhPhuRaVienYHCTElement.getValue());
                    }
                    icdBenhPhuRaVienYHCTElement.setValue("")
                    tenBenhPhuRaVienYHCTElement.setValue("")
                    icdBenhPhuRaVienYHCTElement.focus()
                }
            });

            instance.on('openmodalcopytomtatcls', function(click) {
                addTextTitleModal("titleModalTomtatketCLSDieutri")
                $("#modalTomtatketCLSDieutri").modal("show");
                $(document).trigger("reloadDSTomtatCLS");
                $("#tomtatketCLSDieutriTabs").attr("data-function-copy", "copyTomtatKetquaCLSPage3")
            });

            showLoaderIntoWrapId(idWrap)
            getThongtinTongket(
                thongtinhsba.thongtinbn.VOBENHAN[0].ID, 'NGOAITRUYHCT1941', function(dataTrang3) {
                hideLoaderIntoWrapId(idWrap)
                formTongket.getComponent('MANHANVIEN_GIAOHOSO', function(component) {
                    danhsachnhanvien = component.component.data.values;
                });
                let NGUOIGIAO_HOSO = danhsachnhanvien.find(opt => opt.label.includes(dataTrang3.NGUOIGIAO_HOSO));
                dataTrang3.KETQUADIEUTRI = dataTrang3.KETQUADIEUTRI ? dataTrang3.KETQUADIEUTRI.trim() : "";
                dataTrang3.MAKHOA_KETHUC = dataTrang3.MAKHOA_KETHUC? dataTrang3.MAKHOA_KETHUC: singletonObject.makhoa;
                dataTrang3.soToCTScanner = dataTrang3.SOTO_CTSCANNER ? dataTrang3.SOTO_CTSCANNER : 0;
                dataTrang3.soToKhac = dataTrang3.SOTO_KHAC ? dataTrang3.SOTO_KHAC : 0;
                dataTrang3.soToMri = dataTrang3.SOTO_MRI ? dataTrang3.SOTO_MRI : 0;
                dataTrang3.soToSieuAm = dataTrang3.SOTO_SIEUAM ? dataTrang3.SOTO_SIEUAM : 0;
                dataTrang3.soToXQuang = dataTrang3.SOTO_XQUANG ? dataTrang3.SOTO_XQUANG : 0;
                dataTrang3.soToXetNghiem = dataTrang3.SOTO_XETNGHIEM ? dataTrang3.SOTO_XETNGHIEM : 0;
                dataTrang3.toanBoHoSo = dataTrang3.SOTO_TOANBOHS ? dataTrang3.SOTO_TOANBOHS : 0;
                dataTrang3.MANHANVIEN_GIAOHOSO = NGUOIGIAO_HOSO ? NGUOIGIAO_HOSO.value: "";
                dataTrang3.tomTatKetQuaXNCLS = dataTrang3.TOMTAT_KETQUA;
                dataTrang3.NGAY_TONGKET = (dataTrang3.NGAY_TONGKET_DATETIME? moment(dataTrang3.NGAY_TONGKET_DATETIME, ['DD/MM/YYYY HH:mm']): moment()).toISOString();

                formTongket.submission = {
                    data: {
                        ...dataTrang3
                    }
                };

            }, function() {
                hideLoaderIntoWrapId(idWrap)
                notifiToClient("Red", "Lỗi load thông tin bệnh án")
            });
        },
        saveTongket: function(element) {
            let idButton = element.id;
            formTongket.emit("checkValidity");
            if (!formTongket.checkValidity(null, false, null, true)) {
                hideSelfLoading(idButton);
                return;
            }
            let dataSubmit = formTongket.submission.data;
            dataSubmit.id = thongtinhsba.thongtinbn.VOBENHAN[0].ID;
            dataSubmit.nguoiGiaoHoSo = getTextSelectedFormio(formTongket.getComponent('MANHANVIEN_GIAOHOSO'));
            dataSubmit.benh = "";
            dataSubmit.chiTietThuThuatPhauThuat = "";
            dataSubmit.giaiPhauBenh = "";
            dataSubmit.huongDieuTriVaCacCheDo = dataSubmit.HUONG_DIEUTRI;
            dataSubmit.ketQuaDieuTri = dataSubmit.KETQUADIEUTRI;
            dataSubmit.lyDoVaoVien = dataSubmit.LYDOVAOVIEN;
            let ngayba = moment(dataSubmit.NGAY_TONGKET)
            dataSubmit.ngayTongKet =  ngayba.format("DD/MM/YYYY HH:mm");
            dataSubmit.quaTrinhBenhLy = dataSubmit.QUATRINH_BENHLY;
            dataSubmit.thuThuatPhauThuat = "";
            dataSubmit.tinhTrangNguoiBenhRaVien = dataSubmit.TINHTRANG_RAVIEN;

            dataSubmit.yHCTBenhKemTheo = dataSubmit.YHCTBENHKEMTHEO;
            dataSubmit.yHCTICDBenhChinh = dataSubmit.YHCTICDBENHCHINH;
            dataSubmit.yHCTICDBenhChinh_Ten = dataSubmit.YHCTICDBENHCHINH_TEN;

            dataSubmit.yHCTRaVienBenhKemTheo = dataSubmit.YHCTRAVIENBENHKEMTHEO_TEN;
            dataSubmit.yHCTRaVienICDBenhChinh = dataSubmit.YHCTRAVIENICDBENHCHINH;
            dataSubmit.yHCTRaVienICDBenhChinh_Ten = dataSubmit.YHCTRAVIENICDBENHCHINH_TEN;
            dataSubmit.yHCTphuongPhapDieuTri = dataSubmit.YHCTPHUONGPHAPDIEUTRI;

            dataSubmit.yHHDBenhKemTheo = dataSubmit.YHHDBENHKEMTHEO;
            dataSubmit.yHHDICDBenhChinh = dataSubmit.YHHDICDBENHCHINH;
            dataSubmit.yHHDICDBenhChinh_Ten = dataSubmit.YHHDICDBENHCHINH_TEN;

            dataSubmit.yHHDRaVienBenhKemTheo = dataSubmit.YHHDRAVIENBENHKEMTHEO_TEN;
            dataSubmit.yHHDRaVienICDBenhChinh = dataSubmit.YHHDRAVIENICDBENHCHINH;
            dataSubmit.yHHDRaVienICDBenhChinh_Ten = dataSubmit.YHHDRAVIENICDBENHCHINH_TEN;
            dataSubmit.yHHDphuongPhapDieuTri = dataSubmit.YHHDPHUONGPHAPDIEUTRI;

            dataSubmit.phuongPhapDieuTri = dataSubmit.YHHDPHUONGPHAPDIEUTRI;

            console.log(dataSubmit)
            $.ajax({
                url: "TongKetBenhAn_UpdateYHCTNoiTru",
                type: 'POST',
                data: JSON.stringify(dataSubmit),
                contentType: 'application/json',
                success: function (data) {
                    if (data.SUCCESS == 1) {
                        notifiToClient("Green", "Lưu thành công")
                        updateThongtinPage3(dataSubmit);

                        let Logbandau = []
                        let Logmoi = []
                        let newdata = {};
                        let dataSubmitnew = formTongket.submission.data;

                        dataSubmitnew.BACSIDIEUTRI = dataSubmitnew.bacSiDieuTri;
                        dataSubmitnew.ID = dataSubmitnew.id;
                        dataSubmitnew.LOAI_GIAYTO_KHAC = dataSubmitnew.loaiGiayToKhac;
                        let ngaytknew = moment(dataSubmitnew.NGAY_TONGKET)
                        dataSubmitnew.NGAY_TONGKET = ngaytknew.format("HH") + " Giờ " + ngaytknew.format("mm") + " phút, " + "Ngày " + ngaytknew.format("DD") + " tháng " + ngaytknew.format("MM") + " năm " + ngaytknew.format("YYYY");
                        dataSubmitnew.NGUOIGIAO_HOSO = dataSubmitnew.nguoiGiaoHoSo;
                        dataSubmitnew.NGUOINHAN_HOSO = dataSubmitnew.nguoiNhanHoSo;
                        dataSubmitnew.PHUONGPHAP_DIEUTRI = dataSubmitnew.phuongPhapDieuTri;
                        dataSubmitnew.QUATRINH_BENHLY = dataSubmitnew.quaTrinhBenhLy;
                        dataSubmitnew.SOTO_CTSCANNER = dataSubmitnew.soToCTScanner;
                        dataSubmitnew.SOTO_KHAC = dataSubmitnew.soToKhac;
                        dataSubmitnew.SOTO_SIEUAM = dataSubmitnew.soToSieuAm;
                        dataSubmitnew.SOTO_XQUANG = dataSubmitnew.soToXQuang;
                        dataSubmitnew.SOTO_XETNGHIEM = dataSubmitnew.soToXetNghiem;
                        dataSubmitnew.THUTHUAT_PHAUTHUAT = dataSubmitnew.thuThuatPhauThuat;
                        dataSubmitnew.TINHTRANG_RAVIEN = dataSubmitnew.tinhTrangNguoiBenhRaVien;
                        dataSubmitnew.SOTO_TOANBOHS = dataSubmitnew.toanBoHoSo;

                        assignNonNullValuesBA(newdata,dataSubmitnew);
                        let diffObject = findDifferencesBetweenObjects(oldDataTrang3, newdata);
                        console.log(diffObject)
                        for (let key in diffObject) {
                            if (keyLuuLogTrang3.hasOwnProperty(key)) {
                                Logbandau.push(getLabelValueBATrang3(key, oldDataTrang3))
                                Logmoi.push(getLabelValueBATrang3(key, newdata))
                            }
                        }
                        console.log(Logbandau)
                        console.log(Logmoi)
                        if (Logbandau.length !== 0 || Logmoi.length !== 0){
                            luuLogHSBATheoBN({
                                SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                LOAI: LOGHSBALOAI.NOITRUYHCT.KEY,
                                NOIDUNGBANDAU: Logbandau.join(";"),
                                NOIDUNGMOI: Logmoi.join(";"),
                                USERID: singletonObject.userId,
                                ACTION: LOGHSBAACTION.EDIT.KEY,
                            })
                        }

                    } else {
                        notifiToClient("Red", "Lưu thông tin bệnh án không thành công")
                    }
                    hideSelfLoading(idButton);
                },
                error: function () {
                    notifiToClient("Red", "Lỗi lưu thông tin")
                    hideSelfLoading(idButton);
                }
            })

        },
        callbackAfterLoadPage1: function (instance) {
            formPage1 = instance;
            let idWrap = "hsba_vba_trang1-tab";
            showLoaderIntoWrapId(idWrap);
            let dataTrang1 = thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1.INFO;
            const promises = [
                actionLoadObjectQuanLyNguoiBenhVBAT1_1(formPage1, dataTrang1),
                actionLoadObjectChanDoanVBAT1_3(formPage1, dataTrang1),
                actionLoadObjectTinhTrangRaVienVBAT1_1(formPage1, dataTrang1)
            ];
            Promise.all(promises)
                .then(results => {
                    formPage1.submission =  {
                        data: {
                            ...dataTrang1
                        }
                    };
                    hideLoaderIntoWrapId(idWrap)
                })
                .catch(error => {
                    console.error("An error occurred:", error);
                    hideLoaderIntoWrapId(idWrap);
                });
        },
        savePage1: function(element) {
            let idButton = element.id;
            formPage1.emit("checkValidity");
            if (!formPage1.checkValidity(null, false, null, true)) {
                hideSelfLoading(idButton);
                return;
            }
            let dataSubmit = formPage1.submission.data;
            let dataTrang1 = thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1;
            dataTrang1.INFO.CHUYENKHOASONGAY0 = dataSubmit.CHUYENKHOASONGAY0;
            dataTrang1.INFO.CHUYENKHOATHOIGIAN0 = moment(dataSubmit.CHUYENKHOATHOIGIAN0).format("DD/MM/YYYY HH:mm:ss");
            dataTrang1.INFO.ICD_KHOADT = dataSubmit.ICD_KHOADT;
            dataTrang1.INFO.ICD_KHOADT_TEN = dataSubmit.ICD_KHOADT_TEN;

            dataTrang1.INFO.NOIGIOITHIEU = dataSubmit.NOIGIOITHIEU;
            dataTrang1.INFO.VAOVIENLANTHU = dataSubmit.VAOVIENLANTHU;
            // Thêm CĐ YHHĐ
            dataTrang1.INFO.THUTHUAT_PHAUTHUAT = dataSubmit.THUTHUAT_PHAUTHUAT;
            dataTrang1.INFO.ICDPHU1_KHOADT = dataSubmit.ICDPHU1_KHOADT;
            dataTrang1.INFO.ICDPHU1_KHOADT_TEN = dataSubmit.ICDPHU1_KHOADT_TEN;
            dataTrang1.INFO.TAIBIEN_BIENCHUNG = dataSubmit.TAIBIEN_BIENCHUNG;

            // Thêm CĐ YHCT
            dataTrang1.INFO.ICDYHCT_KHOADT = dataSubmit.ICDYHCT_KHOADT;
            dataTrang1.INFO.ICDYHCT_KHOADT_TEN = dataSubmit.ICDYHCT_KHOADT_TEN;
            dataTrang1.INFO.ICDYHCTPHU1_KHOADT = dataSubmit.ICDYHCTPHU1_KHOADT;
            dataTrang1.INFO.ICDYHCTPHU1_KHOADT_TEN = dataSubmit.ICDYHCTPHU1_KHOADT_TEN;
            dataTrang1.INFO.YHCT_THUTHUAT_PHAUTHUAT = dataSubmit.YHCT_THUTHUAT_PHAUTHUAT;
            dataTrang1.INFO.YHCT_TAIBIEN_BIENCHUNG = dataSubmit.YHCT_TAIBIEN_BIENCHUNG;
            // Ra viện
            dataTrang1.INFO.GIAIPHAUBENH = dataSubmit.GIAIPHAUBENH;
            dataTrang1.INFO.NN_TUVONG = dataSubmit.NN_TUVONG;
            dataTrang1.INFO.KHOANGTG_TUVONG = dataSubmit.KHOANGTG_TUVONG;
            dataTrang1.INFO.KHAMNGHIEM = dataSubmit.KHAMNGHIEM === true ? 1 : 0;
            dataTrang1.INFO.ICD_GIAIPHAU = dataSubmit.ICD_GIAIPHAU;
            dataTrang1.INFO.TEN_ICD_GIAIPHAU = "";
            luuThongTinVBATrang1();

            let Logbandau = []
            let Logmoi = []
            let newdata = {};
            assignNonNullValuesTrang1(newdata,dataSubmit);
            let diffObject = findDifferencesBetweenObjects(oldDataTrang1, newdata);
            console.log(diffObject)
            for (let key in diffObject) {
                if (keyLuuLogTrang1.hasOwnProperty(key)) {
                    Logbandau.push(getLabelValueBATrang1(key, oldDataTrang1))
                    Logmoi.push(getLabelValueBATrang1(key, newdata))
                }
            }
            console.log(Logbandau)
            console.log(Logmoi)
            if (Logbandau.length !== 0 || Logmoi.length !== 0){
                luuLogHSBATheoBN({
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: LOGHSBALOAI.NOITRUYHCT.KEY,
                    NOIDUNGBANDAU: Logbandau.join(";"),
                    NOIDUNGMOI: Logmoi.join(";"),
                    USERID: singletonObject.userId,
                    ACTION: LOGHSBAACTION.EDIT.KEY,
                })
            }

            reloadFormVBAPage1(0, 0, idButton);
        },
        saveThongtinHC: function(element) {
            let idButton = element.id;
            showSelfLoading(idButton);
            let dataSubmit = convertDataFormToJson("formHsbatthcqlnb");
            updateQuanlynbvaChandoan(dataSubmit, function() {
                hideSelfLoading(idButton);
                notifiToClient("Green", "Lưu thành công")
            }, function() {
                hideSelfLoading(idButton);
                notifiToClient("Red", "Lỗi lưu thông tin")
            });
        },
        loadThongtinPage1: function() {
            let idWrap = "hsba_vba_trang1-tab";
            showLoaderIntoWrapId(idWrap)
            getThongtinPage1Benhan(thongtinhsba.thongtinbn.VOBENHAN[0].ID, function() {
                hideLoaderIntoWrapId(idWrap)
            }, function() {
                hideLoaderIntoWrapId(idWrap)
                notifiToClient("Red", "Lỗi load thông tin")
            });
        },
        copyChidinhCLS: function(cls) {
            form.submission = {
                data: {
                    ...form.submission.data,
                    CLS: cls
                }
            }
        }
    }
}