create or replace FUNCTION his_manager.cmu_ylenhthuoc_theogio_group (
    p_dvtt           VARCHAR2,
    p_id_dieutri     VARCHAR2,
    p_sovaovien      VARCHAR2,
    p_sovaovien_dt   VARCHAR2,
    p_stt_toathuoc   VARCHAR2
) RETURN VARCHAR2 IS

    v_groupylenh              VARCHAR2(4000) := NULL;
    v_groupylenh_truyendich   VARCHAR2(4000) := NULL;
    v_result                  VARCHAR2(4000) := '';
BEGIN
SELECT
    concat(XMLCAST(XMLAGG(XMLELEMENT(
            e,('  + '
                || TO_CHAR(thoigianylenh, 'DD/MM/YYYY HH24:MI')
                || ' '
                || ghichu
                || '<br/>')
        )
        ORDER BY
                thoigianylenh
        ).extract('//text()') AS VARCHAR2(4000)), '')
INTO v_groupylenh
FROM
    cmu_ylenhthuoc_theogio ylenh
WHERE
        dvtt = p_dvtt
  AND id_dieutri = p_id_dieutri
  AND stt_toathuoc = p_stt_toathuoc
  AND sovaovien = p_sovaovien
  AND ( sovaovien_dt = p_sovaovien_dt
    OR p_sovaovien_dt = '-1' )
  AND nvl(loai_ylenh, 'thuoc') = 'thuoc';

SELECT
    concat(XMLCAST(XMLAGG(XMLELEMENT(
            e,('  + '
                || TO_CHAR(thoigianylenh, 'DD/MM/YYYY HH24:MI')
                || ' Số lượng truyền: '
                || soml_truyendich
                || ' ml'
                || '; Tốc độ: '
                || tocdo_truyendich
                || ' ('
                ||
               CASE
                   WHEN loai_tocdo_ylenh = 'ml/gio' THEN
                       'ml/giờ'
                   ELSE
                       'giọt/phút'
                   END
                || ')'
                || CASE
                       WHEN lieudung IS NULL THEN ' '
                       ELSE '; Liều dùng: ' || lieudung
                   END
                || ' '
                || ghichu
                || '<br/>')
        )
        ORDER BY
                thoigianylenh
        ).extract('//text()') AS VARCHAR2(4000)), '')
INTO v_groupylenh_truyendich
FROM
    cmu_ylenhthuoc_theogio ylenh
WHERE
        dvtt = p_dvtt
  AND id_dieutri = p_id_dieutri
  AND stt_toathuoc = p_stt_toathuoc
  AND sovaovien = p_sovaovien
  AND ( sovaovien_dt = p_sovaovien_dt
    OR p_sovaovien_dt = '-1' )
  AND loai_ylenh = 'truyendich';

IF v_groupylenh IS NOT NULL THEN
        v_result := ' Thời gian thực hiện: <br/>'
                    || v_groupylenh
                    || chr(10);
END IF;

    IF v_groupylenh_truyendich IS NOT NULL THEN
        v_result := v_result
                    || ' Thời gian thực hiện truyền dịch: <br/>'
                    || v_groupylenh_truyendich;
END IF;

RETURN v_result;
END;