CREATE OR REPLACE FUNCTION "CLS_TTPT_VLTL_CN_KQ_F_SVV" (
    p_sophieu              IN                     VARCHAR2,
    p_dvtt                 IN                     VARCHAR2,
    p_madv                 IN                     NUMBER,
    p_chandoan             IN                     VARCHAR2,
    p_noitru               IN                     NUMBER,
    p_ma_kham_benh         IN                     VARCHAR2,
    p_stt_<PERSON><PERSON>           IN                     VARCHAR2,
    p_stt_dotdieutri       IN                     VARCHAR2,
    p_stt_dieutri          IN                     VARCHAR2,
    p_phuongphapvocam      IN                     VARCHAR2,
    p_phuongphappttt       IN                     VARCHAR2,
    p_trinhtupttt          IN                     CLOB,
    p_catchisau7ngay       IN                     CLOB,
    p_bacsipttt            IN                     VARCHAR2,
    p_bacsigayme           IN                     VARCHAR2,
    p_ngaygiopttt          IN                     TIMESTAMP DEFAULT SYSDATE,
    p_taibien              IN                     NUMBER,
    p_tuvong               IN                     NUMBER,
    p_sovaovien            IN                     NUMBER,
    p_sovaovien_noi        IN                     NUMBER,
    p_sovaovien_dt_noi     IN                     NUMBER,
    p_nguoithuchien        IN                     NUMBER,
    p_mabenhlytruocttpt    IN                     NUMBER DEFAULT NULL,
    p_maben<PERSON><PERSON>uttpt      IN                     NUMBER DEFAULT NULL,
    p_trinhtupttt_xml5     IN                     CLOB,
    p_mabacsipttt          IN                     VARCHAR2,
    p_ma_bacsigayme        IN                     VARCHAR2,
    p_ma_ktv_gayme         IN                     VARCHAR2,
    p_ma_phumo_vongtrong   IN                     VARCHAR2,
    p_ma_phumo_vongngoai   IN                     VARCHAR2,
    p_ma_dcvongtrong       IN                     VARCHAR2,
    p_ma_dcvongngoai       IN                     VARCHAR2,
    p_luotdottpt           IN                     CLOB,
    p_mota_dienbien_benh   VARCHAR2,
    p_ppvc                 NUMBER DEFAULT 0,
    v_ngaygiopttt_kt       IN                     VARCHAR2 DEFAULT '',
    p_vetthuongtaiphat     NUMBER DEFAULT 0
) RETURN NUMBER IS

    vcount             NUMBER(10) DEFAULT 0;
    v_thamso960481     VARCHAR2(5) := his_manager.dm_tsdv_sl_mtso(p_dvtt, '960481');
    v_khoa             NUMBER(10) := 0;
    p_ngaygiopttt_kt   TIMESTAMP DEFAULT to_timestamp(v_ngaygiopttt_kt, 'yyyy-mm-dd hh24:mi:ss');
    v_return           NUMBER;
    p_mabenhnhan       NUMBER;
BEGIN
    IF v_thamso960481 = 1 THEN
        IF p_noitru = 0 THEN
SELECT
    COUNT(1)
INTO v_khoa
FROM
    kb_phieuthanhtoan
WHERE
    dvtt = p_dvtt
  AND ma_kham_benh = p_ma_kham_benh
  AND sovaovien = p_sovaovien
  AND khoa_thanhtoan = 1;

ELSE
SELECT
    COUNT(1)
INTO v_khoa
FROM
    noitru_phieuthanhtoan
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien_noi
  AND sovaovien_dt = p_sovaovien_dt_noi
  AND khoa_thanhtoan = 1;

END IF;

        IF v_khoa > 0 THEN
            RETURN -1;
END IF;
END IF;

    IF p_noitru = 0 THEN
SELECT
    mabenhnhan
INTO p_mabenhnhan
FROM
    his_manager.kb_cd_dichvu
WHERE
    so_phieu_dichvu = p_sophieu
  AND dvtt = p_dvtt
  AND ma_kham_benh = p_ma_kham_benh
  AND sovaovien = p_sovaovien;

UPDATE his_manager.kb_cd_dichvu
SET
    trang_thai_dichvu = 1,
    ngay_tra_ketqua = nvl(p_ngaygiopttt_kt, SYSDATE)
WHERE
    so_phieu_dichvu = p_sophieu
  AND dvtt = p_dvtt
  AND ma_kham_benh = p_ma_kham_benh
  AND sovaovien = p_sovaovien;

UPDATE kb_cd_dichvu_ct
SET
    chandoan = p_chandoan,
    phuongphap_tt_pt = p_phuongphappttt,
    phuongphap_vocam = p_phuongphapvocam,
    bacsi_pttt = p_bacsipttt,
    bacsi_gayme = p_bacsigayme,
    catchi_sau7ngay = p_catchisau7ngay,
    trinhtu_tt_pt = p_trinhtupttt,
    ngay_gio_pttt = p_ngaygiopttt
--					case when p_ngaygiopttt < NGAY_CHI_DINH_CT then
--						NGAY_CHI_DINH_CT + interval '15' minute
--					else p_ngaygiopttt end
        ,
    da_chan_doan = 1,
    taibien = p_taibien,
    tuvong = p_tuvong,
    nguoi_thuc_hien = p_nguoithuchien,
    mabenhly_truocttpt = p_mabenhlytruocttpt,
    mabenhly_sauttpt = p_mabenhlysauttpt,
    trinhtu_pttt_xml5 = p_trinhtupttt_xml5,
    ma_bs_pttp = p_mabacsipttt,
    ma_bs_gayme = p_ma_bacsigayme,
    ma_ktv_gayme = p_ma_ktv_gayme,
    ma_phu_mo_vongtrong = p_ma_phumo_vongtrong,
    ma_phu_mo_vongngoai = p_ma_phumo_vongngoai,
    ma_bs_dc_vongngoai = p_ma_dcvongngoai,
    ma_bs_dc_vongtrong = p_ma_dcvongtrong,
    luotdo_ttpt = p_luotdottpt,
    pp_vo_cam = p_ppvc,
    ngay_gio_pttt_kt = nvl(p_ngaygiopttt_kt, SYSDATE),
    vet_thuong_tp = p_vetthuongtaiphat
WHERE
    so_phieu_dichvu = p_sophieu
  AND dvtt = p_dvtt
  AND ma_dv = p_madv
  AND ma_kham_benh = p_ma_kham_benh
  AND sovaovien = p_sovaovien;

vcount := SQL%rowcount;
        cmu_taoallbc(p_dvtt, p_sovaovien);
ELSE
SELECT
    mabenhnhan
INTO p_mabenhnhan
FROM
    his_manager.noitru_cd_dichvu
WHERE
    so_phieu_dichvu = p_sophieu
  AND dvtt = p_dvtt
  AND sovaovien = p_sovaovien_noi;

UPDATE noitru_cd_dichvu
SET
    trang_thai_dichvu = 1,
    ngay_tra_ketqua = nvl(p_ngaygiopttt_kt, SYSDATE)
WHERE
    so_phieu_dichvu = p_sophieu
  AND dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri
  AND sovaovien = p_sovaovien_noi
  AND sovaovien_dt = p_sovaovien_dt_noi;

UPDATE noitru_cd_dichvu_ct
SET
    chandoan = p_chandoan,
    phuongphap_tt_pt = p_phuongphappttt,
    phuongphap_vocam = p_phuongphapvocam,
    bacsi_pttt = p_bacsipttt,
    bacsi_gayme = p_bacsigayme,
    catchi_sau7ngay = p_catchisau7ngay,
    trinhtu_tt_pt = p_trinhtupttt,
    ngay_gio_pttt = p_ngaygiopttt
--                    case when p_ngaygiopttt < NGAY_CHI_DINH_CT then
--						NGAY_CHI_DINH_CT + interval '15' minute
--					else p_ngaygiopttt end
        ,
    da_chan_doan = 1,
    taibien = p_taibien,
    tuvong = p_tuvong,
    nguoi_thuc_hien = p_nguoithuchien,
    noitru_cd_dichvu_ct.mabenhly_truocttpt = p_mabenhlytruocttpt,
    noitru_cd_dichvu_ct.mabenhly_sauttpt = p_mabenhlysauttpt,
    trinhtu_pttt_xml5 = p_trinhtupttt_xml5,
    ma_bs_pttp = p_mabacsipttt,
    ma_bs_gayme = p_ma_bacsigayme,
    ma_ktv_gayme = p_ma_ktv_gayme,
    ma_phu_mo_vongtrong = p_ma_phumo_vongtrong,
    ma_phu_mo_vongngoai = p_ma_phumo_vongngoai,
    ma_bs_dc_vongngoai = p_ma_dcvongngoai,
    ma_bs_dc_vongtrong = p_ma_dcvongtrong,
    luotdo_ttpt = p_luotdottpt,
    mota_dienbenh_benh = p_mota_dienbien_benh,
    pp_vo_cam = p_ppvc,
    ngay_gio_pttt_kt = nvl(p_ngaygiopttt_kt, SYSDATE),
    vet_thuong_tp = p_vetthuongtaiphat
WHERE
    so_phieu_dichvu = p_sophieu
  AND dvtt = p_dvtt
  AND ma_dv = p_madv
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri
  AND sovaovien = p_sovaovien_noi
  AND sovaovien_dt = p_sovaovien_dt_noi;

vcount := SQL%rowcount;
        cmu_taosolieu_allbc_v3(p_dvtt, p_sovaovien_noi, p_sovaovien_dt_noi);
END IF;

DELETE FROM cmu_thoigian_nhanvien
WHERE
    dvtt = p_dvtt
  AND sovaovien =
      CASE
          WHEN p_noitru = 1 THEN
              p_sovaovien
          ELSE
              p_sovaovien_noi
          END
  AND noitru = p_noitru
  AND sophieu = p_sophieu
  AND ma_dv = p_madv;

v_return := cmu_thoigian_nhanvien_ins(p_dvtt, p_mabacsipttt,
        CASE
            WHEN p_noitru = 0 THEN
                p_sovaovien
            ELSE p_sovaovien_noi
        END, p_mabenhnhan, p_noitru, TO_CHAR(p_ngaygiopttt, 'DD/MM/YYYY HH24:MI'), TO_CHAR(nvl(p_ngaygiopttt_kt, SYSDATE), 'DD/MM/YYYY HH24:MI'
        ), p_sophieu, p_madv, 'TTPT');

    IF p_ma_ktv_gayme IS NOT NULL THEN
        v_return := cmu_thoigian_nhanvien_ins(p_dvtt, p_ma_ktv_gayme,
            CASE
                WHEN p_noitru = 0 THEN
                    p_sovaovien
                ELSE p_sovaovien_noi
            END, p_mabenhnhan, p_noitru, TO_CHAR(p_ngaygiopttt, 'DD/MM/YYYY HH24:MI'), TO_CHAR(nvl(p_ngaygiopttt_kt, SYSDATE), 'DD/MM/YYYY HH24:MI'
            ), p_sophieu, p_madv, 'TTPT');
END IF;

    IF p_ma_bacsigayme IS NOT NULL THEN
        v_return := cmu_thoigian_nhanvien_ins(p_dvtt, p_ma_bacsigayme,
            CASE
                WHEN p_noitru = 0 THEN
                    p_sovaovien
                ELSE p_sovaovien_noi
            END, p_mabenhnhan, p_noitru, TO_CHAR(p_ngaygiopttt, 'DD/MM/YYYY HH24:MI'), TO_CHAR(nvl(p_ngaygiopttt_kt, SYSDATE), 'DD/MM/YYYY HH24:MI'
            ), p_sophieu, p_madv, 'TTPT');
END IF;

    IF p_ma_phumo_vongtrong IS NOT NULL THEN
        v_return := cmu_thoigian_nhanvien_ins(p_dvtt, p_ma_phumo_vongtrong,
            CASE
                WHEN p_noitru = 0 THEN
                    p_sovaovien
                ELSE p_sovaovien_noi
            END, p_mabenhnhan, p_noitru, TO_CHAR(p_ngaygiopttt, 'DD/MM/YYYY HH24:MI'), TO_CHAR(nvl(p_ngaygiopttt_kt, SYSDATE), 'DD/MM/YYYY HH24:MI'
            ), p_sophieu, p_madv, 'TTPT');
END IF;

    IF p_ma_phumo_vongngoai IS NOT NULL THEN
        v_return := cmu_thoigian_nhanvien_ins(p_dvtt, p_ma_phumo_vongngoai,
            CASE
                WHEN p_noitru = 0 THEN
                    p_sovaovien
                ELSE p_sovaovien_noi
            END, p_mabenhnhan, p_noitru, TO_CHAR(p_ngaygiopttt, 'DD/MM/YYYY HH24:MI'), TO_CHAR(nvl(p_ngaygiopttt_kt, SYSDATE), 'DD/MM/YYYY HH24:MI'
            ), p_sophieu, p_madv, 'TTPT');
END IF;

    IF p_ma_dcvongngoai IS NOT NULL THEN
        v_return := cmu_thoigian_nhanvien_ins(p_dvtt, p_ma_dcvongngoai,
            CASE
                WHEN p_noitru = 0 THEN
                    p_sovaovien
                ELSE p_sovaovien_noi
            END, p_mabenhnhan, p_noitru, TO_CHAR(p_ngaygiopttt, 'DD/MM/YYYY HH24:MI'), TO_CHAR(nvl(p_ngaygiopttt_kt, SYSDATE), 'DD/MM/YYYY HH24:MI'
            ), p_sophieu, p_madv, 'TTPT');
END IF;

    IF p_ma_dcvongtrong IS NOT NULL THEN
        v_return := cmu_thoigian_nhanvien_ins(p_dvtt, p_ma_dcvongtrong,
            CASE
                WHEN p_noitru = 0 THEN
                    p_sovaovien
                ELSE p_sovaovien_noi
            END, p_mabenhnhan, p_noitru, TO_CHAR(p_ngaygiopttt, 'DD/MM/YYYY HH24:MI'), TO_CHAR(nvl(p_ngaygiopttt_kt, SYSDATE), 'DD/MM/YYYY HH24:MI'
            ), p_sophieu, p_madv, 'TTPT');
END IF;

RETURN vcount;
END;