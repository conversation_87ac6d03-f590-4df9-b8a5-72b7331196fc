<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@page contentType="text/html" pageEncoding="UTF-8"  %>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="google-site-verification" content="u6uNEfD4cb3gidezi4r_6aI8Wb1E07-ufBeCQpvmlqQ" />
    <title>Hệ thống chăm sóc sức khỏe</title>
    <link rel="icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
    <link rel="shortcut icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
    <!-- jQuery file -->
    <link href="<c:url value="/resources/css/divheader.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/css/style_new.css" />" rel="stylesheet"/>
    <!-- Jquery -->
    <link rel="stylesheet" href="<c:url value="/resources/css/jquery-ui-redmond.1.9.1.css" />" />
    <script src="<c:url value="/resources/js/jquery.min.1.8.3.js" />"></script>
    <script src="<c:url value="/resources/js/jquery-ui.1.9.1.js" />"></script>
    <!-- Grid -->
    <link href="<c:url value="/resources/jqueryui/themes/redmond/jquery-ui.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/jqgrid/css/ui.jqgrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/jqgrid/js/i18n/grid.locale-en.js" />"></script>
    <script src="<c:url value="/resources/jqgrid/js/jquery.jqGrid.src.js" />"></script>
    <script src="<c:url value="/resources/js/common_function.js" />"></script>
    <script src="<c:url value="/resources/js/export_file.js" />"></script>
    <script src="<c:url value="/resources/js/jquery.inputmask.bundle.min.js" />"></script>
    <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
    <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/dialog/jquery.alerts.1.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/js/datetimepicker.js" />"></script>
    <link rel="stylesheet" href="<c:url value="/resources/css/datetimepicker.css" />" />
    <link href="<c:url value="/resources/dialog/jquery.alerts.1.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/dialog/jquery.alerts.js" />"></script>
    <link href="<c:url value="/resources/bootstrap/css/select2.min.css"/>" rel="stylesheet">
    <script src="<c:url value="/resources/bootstrap/js/select2.min.js"/>"></script>
    <script>
        //CMU 17/10/2017
        $(function() {
            $(":input").inputmask();
            $("#tungay").inputmask({
                mask: "1/2/y h:s",
                placeholder: "dd/mm/yyyy hh:mm",
                alias: "datetime",
                hourFormat: "24"
            });
            $("#denngay").inputmask({
                mask: "1/2/y h:s",
                placeholder: "dd/mm/yyyy hh:mm",
                alias: "datetime",
                hourFormat: "24"
            });
            $("#tungay").datetimepicker({
                dateFormat: "dd/mm/yy"
            });
            $("#denngay").datetimepicker({
                dateFormat: "dd/mm/yy"
            });

            $("#tungay").val("${ngayhientai}" + " 00:00");
            $("#denngay").val("${ngayhientai}" + " 23:59");
            $("#inbaocao").click(function(evt) {
                inbaocao();
            });
            function inbaocao()
            {
                var arr = [$('#tungay').val(), $("#denngay").val(), $("#nhanvien").val(), 'Từ ngày '+$('#tungay').val() + " đến ngày "+$("#denngay").val(), "${Sess_User}", $("#cachlay").val()];
                var param = ['tungay', 'denngay', 'nhanvien', 'tieude2', 'nguoilapbieu', 'cachlay'];
                var url = "cmu_injasper?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=xlsx&jasper=rp_bcvienphi_nhathuoc_ct"
                $(location).attr('href', url);
            }
            $("#inbaocaotonghoppdf").click(function(evt) {
                var arr = [$('#tungay').val(), $("#denngay").val(), $("#nhanvien").val(), 'Từ ngày '+$('#tungay').val() + " đến ngày "+$("#denngay").val(), "${Sess_User}", $("#cachlay").val(), $("#hinhthuc_thanhtoan").val()];
                var param = ['tungay', 'denngay', 'nhanvien', 'tieude2', 'nguoilapbieu', 'cachlay', 'hinhthuctt'];
                if("${Sess_DVTT}" == '96161') {
                    var url_96161 = "cmu_injasper?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=" + $('#loai_file').val() + "&jasper=rp_bcvienphi_nhathuoc_tonghop_96161"
                    $(location).attr('href', url_96161);
                } else {
                    var url = "cmu_injasper?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf&jasper=rp_bcvienphi_nhathuoc_tonghop"
                    $(location).attr('href', url);
                }
            });
            $("#inbknhanvien").click(function(evt) {
                var arr = [$('#tungay').val(), $("#denngay").val(), $("#nhanvien").val(), 'Từ ngày '+$('#tungay').val() + " đến ngày "+$("#denngay").val(), "${Sess_User}", $("#cachlay").val()];
                var param = ['tungay', 'denngay', 'nhanvien', 'tieude2', 'nguoilapbieu', 'cachlay'];
                var url = "cmu_injasper?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=xlsx&jasper=rp_bcvienphi_nhanvien"
                $(location).attr('href', url);
            });
            $("#inbaocaotonghopxls").click(function(evt) {
                var t_ngay = $("#tungay").val().split(" ")
                var d_ngay = $("#denngay").val().split(" ")
                var arr = t_ngay[0].split("/");
                var arr1 = d_ngay[0].split("/");
                var tungay = arr[2] + "-" + arr[1] + "-" + arr[0] + " " + t_ngay[1]+":00"+ "!!!" + $("#hinhthucthanhtoan").val();
                var denngay = arr1[2] + "-" + arr1[1] + "-" + arr1[0] + " " + d_ngay[1]+":59";
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#htbaocao").val();
                var manhanvien = $("#nhanvien").val();
                var tennhanvien = $("#nhanvien option:selected").text();
                var arr = [tungay, denngay, dvtt, noitru, manhanvien, tennhanvien];
                var url = "cmu_intonghopthuchi_tonghop?type=xls&url=" + convertArray(arr);
                $(location).attr('href', url);
            });
            $.ajax({
                method: "GET",
                url: "cmu_getlist?url=" + convertArray(["${Sess_DVTT}", "CMU_GETNHANVIEN"]),
                success: function (data) {
                    data.forEach(function (_obj) {
                        console.log("obj", _obj)
                        $("#nhanvien").append("<option value=" + _obj.MA_NHANVIEN + ">" + _obj.TEN_NHANVIEN+ "</option>");
                    })
                    $("#nhanvien").select2();
                }
            })
        });
        //CMU END 17/102017
    </script>
</head>
<body>
<div id="panel_all">
    <%@include file="../../../../resources/Theme/include_pages/menu.jsp"%>
    <div id="panelwrap">
        <div class="center_content">
            <div class="panel_with_title">
                <div class="panel_title">Bảng kê thu tiền nhà thuốc</div>
                <div class="panel_body">
                    <table width="990" align="center" id="table_bd">
                        <tr>
                            <td align="center">
                                <form name="form1" method="post" action="">
                                    <table class="tb-view" border="0">
                                        <tr>
                                            <td width="80" align="left">Từ ngày</td>
                                            <td width="200" align="left"><input type="text" name="tungay" id="tungay" data-inputmask="'alias': 'date'" style=" width: 190px;" /></td>
                                            <td width="80" align="left">Cách lấy</td>
                                            <td width="200" align="left">
                                                <select name="cachlay"  id="cachlay" class="form-control">
                                                    <option value ="1">Theo ngày xuất dược</option>
                                                    <option value ="0">Theo ngày thu kế toán</option>
                                                </select>
                                            </td>
                                            <c:if test="${Sess_DVTT == '96161'}">
                                                <td width="80" align="left">Loại file</td>
                                                <td width="200" align="left">
                                                    <select name="loai_file"  id="loai_file"  style="width: 190px">
                                                        <option value ="pdf">Pdf</option>
                                                        <option value ="xlsx">Excel</option>
                                                    </select>
                                                </td>
                                            </c:if>
                                        </tr>
                                        <tr>
                                            <td align="left">Đến ngày</td>
                                            <td align="left"><input type="text" name="denngay" id="denngay" data-inputmask="'alias': 'date'" style=" width: 190px;" /></td>
                                            <td align="left">Nhân viên</td>
                                            <td align="left">
                                                <select name="nhanvien"  id="nhanvien"  style="width: 190px">
                                                    <option value ="-1">-Tất cả nhân viên -</option>
                                                    <c:forEach var="i" items="${nhanvien}">
                                                        <option value="${i.MA_NHANVIEN}">${i.TEN_NHANVIEN}</option>
                                                    </c:forEach>
                                                </select>
                                            </td>
                                            <c:if test="${Sess_DVTT == '96161'}">
                                                <td width="80" align="left">Hình thức thanh toán</td>
                                                <td width="200" align="left">
                                                    <select name="loai_file"  id="hinhthuc_thanhtoan"  style="width: 190px">
                                                        <option value ="0">Tiền mặt</option>
                                                        <option value ="1">Chuyển khoản</option>
                                                    </select>
                                                </td>
                                            </c:if>
                                        </tr>
                                        <tr>
                                            <td align="center" colspan="4">
                                                <input type="button" name="inbaocao" id="inbaocao" value="In bảng kê" class="button_shadow" style="width: 120px;" />
                                                <input type="button" name="inbaocaotonghoppdf" id="inbaocaotonghoppdf" value="In bảng kê TH (pdf)" class="button_shadow" style="width: 150px;" />
                                                <input type="button" name="inbknhanvien" id="inbknhanvien" value="In bảng kê nhân viên" class="button_shadow" style="width: 150px;" />
                                            </td>
                                        </tr>
                                    </table>
                                </form>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>