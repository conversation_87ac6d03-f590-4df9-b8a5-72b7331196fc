<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="cls_xn_all" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" whenResourceMissingType="Error" uuid="0f5e3019-005e-44fe-bb8f-29ae14f10109">
	<property name="ireport.zoom" value="1.4641000000000044"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="soyte" class="java.lang.String"/>
	<parameter name="benhvien" class="java.lang.String"/>
	<parameter name="dvtt" class="java.lang.String"/>
	<parameter name="mabenhnhan" class="java.lang.String"/>
	<parameter name="magiay" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="text_date" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="text_month" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="text_year" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="sobenhan" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="gioitinh" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="tennguoibenh" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="tuoi" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="khoa" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_dotdieutri" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_benhan" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="sovaovien" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="chandoan" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_todieutri" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString language="plsql">
		<![CDATA[{call CMU_KYTHUATPHCN_PROCED($P{dvtt},$P{sovaovien},$P{stt_benhan},$P{stt_dotdieutri},$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="NGAY_TAO_PHIEU" class="java.lang.String"/>
	<field name="PHONG" class="java.lang.String"/>
	<field name="GIUONG" class="java.lang.String"/>
	<field name="NGUOITHUCHIEN" class="java.lang.String"/>
	<field name="BACSICHIDINH" class="java.lang.String"/>
	<field name="THOIGIAN" class="java.lang.String"/>
	<field name="THOIGIANPHUT" class="java.lang.String"/>
	<field name="DIENBIENBENH" class="java.lang.String"/>
	<field name="TENDICHVU" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="210" splitType="Stretch">
			<textField>
				<reportElement x="0" y="20" width="190" height="20" uuid="bf002bb6-f816-4a93-92a0-feb2a2ee4d12"/>
				<textElement verticalAlignment="Top" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{soyte}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="40" width="190" height="20" uuid="867d6065-44c4-4b22-a0a5-4f4a81bc84eb"/>
				<textElement verticalAlignment="Top" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{benhvien}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="208" y="20" width="191" height="40" uuid="45e2c42f-894e-4cef-abae-aa9e9bc6c881"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="true"/>
					<paragraph lineSpacing="1_1_2"/>
				</textElement>
				<text><![CDATA[PHIẾU THỰC HIỆN KỸ THUẬT 
PHỤC HỒI CHỨC NĂNG]]></text>
			</staticText>
			<staticText>
				<reportElement x="415" y="20" width="140" height="20" uuid="a74db1e8-89fa-478d-a7a9-ca1528a9cae9"/>
				<textElement verticalAlignment="Top">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[MS: 41/BV-01]]></text>
			</staticText>
			<textField>
				<reportElement x="415" y="40" width="140" height="20" uuid="5c4c762f-4b08-4e76-83ab-f18a60474908"/>
				<textElement verticalAlignment="Top" markup="none">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["Số vào viện: " + $P{sobenhan}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="110" width="339" height="20" uuid="9385dcbc-b425-4d99-b09e-55ad6ba93b75"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["Họ tên người bệnh: " + $P{tennguoibenh}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="339" y="110" width="102" height="20" uuid="3ff583c2-ef51-44c8-922c-bcdcda38e405"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["Tuổi: " + $P{tuoi}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="470" y="110" width="29" height="20" uuid="e65e88bf-d914-4c7a-ac02-561f509cd843"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[Nam]]></text>
			</staticText>
			<textField>
				<reportElement x="456" y="112" width="15" height="15" uuid="6c2c9dab-1edf-4b16-adb5-17e987218644"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{gioitinh}.equals( "1" ) ? "x" : ""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="528" y="110" width="27" height="20" uuid="95ff8a89-87e9-469b-8fc2-21801331121e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[Nữ]]></text>
			</staticText>
			<textField>
				<reportElement x="513" y="112" width="15" height="15" uuid="79294430-b8f4-4df1-93fb-a96a45c94303"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{gioitinh}.equals( "0" ) ? "x" : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="130" width="287" height="20" uuid="66ad3b1e-c5e2-49ed-8778-5540457d8a2e"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["Khoa: " + $P{khoa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="287" y="130" width="112" height="20" uuid="74a96b4d-099d-463f-a6ac-8fd4cabc748c"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHONG} == null ? "Buồng: " : "Buồng: " + $F{PHONG}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="399" y="130" width="129" height="20" uuid="55915627-fbe2-4584-9eec-eaf34bd895b1"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIUONG} == null ? "Giường: " : "Giường: " + $F{GIUONG}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="190" y="0" width="209" height="20" uuid="61ab74fa-332f-41d3-8027-1c186a32bedd"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Mẫu phiếu số 3)]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="170" width="67" height="40" isPrintWhenDetailOverflows="true" uuid="9a443665-693f-43ea-83fa-cd9661076bd8"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Ngày giờ]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="60" width="190" height="20" uuid="c5f59caf-7a99-43ad-b9fa-566cafc235f7"/>
				<textElement verticalAlignment="Top" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{khoa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="80" width="95" height="20" uuid="9c87d388-2c57-4d40-8963-016bfbf97d6c"/>
				<textElement verticalAlignment="Top" markup="none">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHONG} == null ? "Buồng: " : "Buồng: " + $F{PHONG}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="95" y="80" width="95" height="20" uuid="db5abcef-0de7-456b-b99b-79ffe9dbe8e3"/>
				<textElement verticalAlignment="Top" markup="none">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{GIUONG} == null ? "Giường: " : "Giường: " + $F{GIUONG}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="150" width="555" height="20" uuid="31808184-9c6c-4e0e-aecb-122385fbff25"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["Chẩn đoán: " + $P{chandoan}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="67" y="170" width="131" height="40" isPrintWhenDetailOverflows="true" uuid="f892b1ee-3908-4f77-a182-f57d193c70ac"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Mô tả diễn biến bệnh, 
tật/các vấn đề cần PHCN]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="198" y="170" width="110" height="40" isPrintWhenDetailOverflows="true" uuid="6b9333c2-c2b8-43e2-9949-486a967350e2"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Tên dịch vụ kỹ thuật
PHCN]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="308" y="170" width="65" height="40" isPrintWhenDetailOverflows="true" uuid="9ff85693-28f5-42b4-8efe-d93c55c23a69"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Thời gian 
thực hiện 
(phút)]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="373" y="170" width="68" height="40" isPrintWhenDetailOverflows="true" uuid="ebd9f164-98d0-43a8-8722-424d32b5dc12"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Người thực hiện]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="441" y="170" width="58" height="40" isPrintWhenDetailOverflows="true" uuid="c082b983-beb8-457c-bc12-a1d149bd9cc6"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[BS chỉ định]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="499" y="170" width="56" height="40" isPrintWhenDetailOverflows="true" uuid="0972175d-7421-4a75-b9bf-c6badbd160b8"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Xác nhận 
của NB/ 
người nhà]]></text>
			</staticText>
			<textField>
				<reportElement x="208" y="60" width="191" height="20" uuid="2708592c-ef2b-4b65-9b08-accacba835f9"/>
				<textElement textAlignment="Center" verticalAlignment="Top" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Tờ điều trị: " + $P{stt_todieutri}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="20">
			<frame>
				<reportElement x="0" y="0" width="555" height="20" uuid="515547e9-89a2-4f95-9218-014051d3649b"/>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="67" height="20" uuid="ba0d9582-8ac2-4d7b-af99-3b97a73b57c1"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{THOIGIAN}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="67" y="0" width="131" height="20" uuid="d0c64754-df3b-4016-8908-53aa475bcf9a"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{DIENBIENBENH}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="198" y="0" width="110" height="20" uuid="a47dab34-6864-40a1-81a6-d40ab6bebf45"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{TENDICHVU}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="308" y="0" width="65" height="20" uuid="11f235c2-7926-4f91-a5eb-3734ee6ced93"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{THOIGIANPHUT}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="373" y="0" width="68" height="20" uuid="3c567c49-d26d-439b-843a-1c79d2f77870"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{NGUOITHUCHIEN}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="441" y="0" width="58" height="20" uuid="831b93e1-90a1-4b28-86ea-9ea6144e11ef"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{BACSICHIDINH}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="499" y="0" width="56" height="20" uuid="6766f09b-5d4c-429e-88cb-f90fa75743d8"/>
					<box>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11"/>
					</textElement>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
