create or replace FUNCTION HIS_MANAGER."CMU_CHAMSOCCAP1_CONFIG_GET" (
    p_dvtt   IN       VARCHAR2,
    p_loai   IN       VARCHAR2
) RETURN SYS_REFCURSOR IS
    cur SYS_REFCURSOR;
BEGIN
OPEN cur FOR SELECT
                     cf.id,
                     cf.dvtt,
                     gr.lo<PERSON>,
                     cf.<PERSON><PERSON><PERSON><PERSON>,
                     cf.ng<PERSON><PERSON><PERSON>,
                     cf.ng<PERSON><PERSON><PERSON>,
                     cf.tt_chuyen
                 FROM
                     "CMU_CHAMSOCCAP1_CONFIG" cf
                     INNER JOIN "CMU_CHAMSOCCAP1_GROUP_CONFIG" gr on gr.dvtt = cf.dvtt and gr.id = cf.loai
                 WHERE
                     cf.dvtt = p_dvtt
                     AND ( cf.loai = p_loai
                           OR p_loai = '-1' )
                ORDER BY cf.stt;

RETURN cur;
END;