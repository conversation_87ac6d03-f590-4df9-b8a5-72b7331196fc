<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rp_phieuxetnghiemhoasinhnuoctieu" pageWidth="595" pageHeight="842" columnWidth="575" leftMargin="0" rightMargin="20" topMargin="0" bottomMargin="0" uuid="048c5d59-838d-4731-832b-c83a480b78ef">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="363"/>
	<property name="ireport.y" value="228"/>
	<style name="Title" forecolor="#FFFFFF" fontName="Times New Roman" fontSize="50" isBold="false" pdfFontName="Times-Bold"/>
	<style name="SubTitle" forecolor="#CCCCCC" fontName="Times New Roman" fontSize="18" isBold="false" pdfFontName="Times-Roman"/>
	<style name="Column header" forecolor="#666666" fontName="Times New Roman" fontSize="14" isBold="true" pdfFontName="Times-Roman"/>
	<style name="Detail" mode="Transparent" fontName="Times New Roman" pdfFontName="Times-Roman"/>
	<style name="Row" mode="Transparent" fontName="Times New Roman" pdfFontName="Times-Roman">
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style mode="Opaque" backcolor="#F0EFEF"/>
		</conditionalStyle>
	</style>
	<parameter name="madonvi" class="java.lang.String"/>
	<parameter name="makhambenh" class="java.lang.String"/>
	<parameter name="tenbenhvien" class="java.lang.String"/>
	<parameter name="hoten" class="java.lang.String"/>
	<parameter name="tuoi" class="java.lang.String"/>
	<parameter name="gioitinh" class="java.lang.String"/>
	<parameter name="diachi_bn" class="java.lang.String"/>
	<parameter name="chandoan" class="java.lang.String"/>
	<parameter name="ngayxetnghiem" class="java.lang.String"/>
	<parameter name="bsdieutri" class="java.lang.String"/>
	<parameter name="sophieuthanhtoan" class="java.lang.String"/>
	<parameter name="noitru" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_benhan" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_dotdieutri" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_dieutri" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="sophieuxn" class="java.lang.String"/>
	<parameter name="capcuu" class="java.lang.String"/>
	<parameter name="thuong" class="java.lang.String"/>
	<parameter name="sothebaohiem" class="java.lang.String"/>
	<parameter name="ngaydieutri" class="java.lang.String"/>
	<parameter name="soyte" class="java.lang.String"/>
	<parameter name="khoa" class="java.lang.String"/>
	<parameter name="giuong" class="java.lang.String"/>
	<parameter name="buong" class="java.lang.String"/>
	<parameter name="hthi" class="java.lang.String"/>
	<parameter name="mabenhnhan" class="java.lang.String"/>
	<parameter name="loaixetnghiem" class="java.lang.String"/>
	<parameter name="bsphutrach" class="java.lang.String"/>
	<parameter name="maxetnghiem" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{call CMU_INPHIEUXETNGHIEM_V2($P{madonvi},$P{sophieuxn},$P{noitru},$P{makhambenh},$P{stt_benhan},$P{stt_dotdieutri},$P{stt_dieutri},'HSNT',$P{ORACLE_REF_CURSOR})}]]>
	</queryString>
	<field name="dvtt" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="sophieu" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="noitru" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="mota_loai_xetnghiem" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ten_xetnghiem" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="dvnghiepvu_xetnghiem" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="chisobinhthuong" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ket_qua" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ket_luan_tong" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="nguoi_lay_mau" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ngay_lay_mau" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="sapxep" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="nhom_xetnghiem" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="TT" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="HH_ABO" class="java.lang.String"/>
	<field name="HH_Rh" class="java.lang.String"/>
	<field name="nguoi_chi_dinh" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="tuoi" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="kq_ABO" class="java.lang.String"/>
	<field name="kq_Rh" class="java.lang.String"/>
	<field name="NGAYGIOLAYMAU" class="java.lang.String"/>
	<field name="NGAYCHIDINH" class="java.lang.String"/>
	<field name="BINHTHUONG" class="java.lang.String"/>
	<field name="NGAYTRAKETQUA" class="java.lang.String"/>
	<field name="thamsocmu" class="java.lang.String"/>
	<field name="NGUOI_THUC_HIEN" class="java.lang.String"/>
	<field name="nguoilaymau_cmu" class="java.lang.String"/>
	<field name="nguoigiaomau_cmu" class="java.lang.String"/>
	<field name="benhpham_cmu" class="java.lang.String"/>
	<field name="solanin" class="java.lang.Integer"/>
	<field name="TEN_MAY_XN" class="java.lang.String"/>
	<field name="CANHBAO_LAMSANG" class="java.lang.String"/>
	<field name="ngthuchien_in" class="java.lang.String"/>
	<field name="ngaylaynhanmau" class="java.lang.String"/>
	<field name="ANCHUKY" class="java.lang.String"/>
	<variable name="count page" class="java.lang.Integer" resetType="Page" calculation="DistinctCount">
		<variableExpression><![CDATA[$V{PAGE_NUMBER}]]></variableExpression>
	</variable>
	<group name="g_nhom">
		<groupExpression><![CDATA[$F{nhom_xetnghiem}]]></groupExpression>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="200" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="365" y="88" width="28" height="20" uuid="067c2aa7-b4ff-4651-b2c3-a505a58d291b"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("")? $P{sothebaohiem}.substring(00,02):$P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="480" y="68" width="55" height="20" uuid="f9d9db95-2f0a-4896-87ad-87ce73cf6ba3"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{gioitinh}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="393" y="68" width="59" height="20" uuid="4b15a1c4-81f2-48cd-806a-407043c462e6"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tuoi}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="210" y="46" width="44" height="20" uuid="d7e0304b-8497-474d-b6cf-787b8ba8d750"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Thường:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="355" y="109" width="38" height="20" uuid="8390a99d-1f52-4b67-898e-571e608903a1"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Buồng:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="4" width="175" height="17" uuid="a6e72dc0-225a-4939-801f-23a96aeb54c5"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{soyte}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="46" y="88" width="259" height="20" uuid="d391dbf8-1952-497c-8d7a-3b401d729a9f"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{diachi_bn}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="2" y="68" width="98" height="20" uuid="f7fd8948-ee54-48d7-b5fa-b7c0e4c45b5b"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Họ tên người bệnh:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="420" y="88" width="23" height="20" uuid="b4425b31-c287-45c3-b7cc-8ecac504d01f"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("")? $P{sothebaohiem}.substring(03,05):$P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="2" y="88" width="44" height="20" uuid="cc2c9f95-9d12-4504-877d-899048e58101"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Địa chỉ:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="305" y="88" width="60" height="20" uuid="e0cabe1d-fdfd-4eb2-bb36-ebafe76026b9"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Số thẻ BHYT:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="64" y="129" width="473" height="20" isPrintWhenDetailOverflows="true" uuid="28290fe6-cec3-4f34-9ee3-2709ce4d6198">
					<printWhenExpression><![CDATA[!$P{madonvi}.equals("96170")]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{chandoan}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="175" y="4" width="250" height="20" uuid="73873755-a8b4-440a-b25d-aba10bb85a6f"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="16" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[PHIẾU KẾT QUẢ XÉT NGHIỆM]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="365" y="68" width="28" height="20" uuid="08004453-f888-4cb0-a17f-c79c558962cf"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Tuổi:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="2" y="129" width="62" height="20" uuid="6ceb5147-f2e7-4010-8849-3dcf69904a4f"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Chẩn đoán:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="100" y="68" width="263" height="20" uuid="88f5cbe1-6c1e-4477-b2b7-0c562ab02e02"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{hoten}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="290" y="46" width="44" height="20" uuid="665631c2-ddf8-4b6d-a160-80438733c9b4"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Cấp cứu:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="254" y="46" width="24" height="20" uuid="108b172a-5482-413a-aed2-de8421458b57"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{capcuu}.equals("1")? "": "X"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="175" y="24" width="250" height="20" uuid="613051aa-9923-4741-a9c0-9f24b0f02c0d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="16" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[NƯỚC TIỂU]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="443" y="88" width="27" height="20" uuid="38d62b8d-e45a-4955-9a6d-a2c6771b10a6"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("")? $P{sothebaohiem}.substring(05,8):$P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="502" y="88" width="35" height="20" uuid="1d25164e-4978-4444-b306-e42d037f0ddc"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("")? $P{sothebaohiem}.substring(11,15):$P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="452" y="68" width="28" height="20" uuid="653786e5-b266-4eba-9149-ccebbb3b6996"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Giới:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="393" y="88" width="27" height="20" uuid="2af2b0cd-3644-449f-8484-941f5c4fb274"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("")? $P{sothebaohiem}.substring(02,03):$P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="441" y="109" width="39" height="20" uuid="d0e2bb58-0952-4dfd-90ec-183cb1c04192"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Giường:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="334" y="46" width="21" height="20" forecolor="#000000" backcolor="#FFFFFF" uuid="047434e8-93af-4679-a53c-be5913075fdc"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="1.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{capcuu}.equals("1")? "X": ""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="471" y="88" width="31" height="20" uuid="dad193e5-5a37-4b7f-9f1c-566bbcf7a703"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="0.0" lineStyle="Double"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="1.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[!$P{sothebaohiem}.equals("")? $P{sothebaohiem}.substring(8,11):$P{sothebaohiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="35" y="109" width="192" height="20" uuid="7728da4b-c2f2-4e9c-b7e0-8b4aed262689"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{khoa}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="2" y="109" width="33" height="20" uuid="ba04360a-e536-4eb6-9b1a-b9ab2738d53f"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Khoa:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="22" width="175" height="20" uuid="438d5704-23b6-4834-ace3-aefa189a493a"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenbenhvien}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="458" y="35" width="75" height="11" forecolor="#000000" uuid="debbb0ae-64bb-4d1a-9472-2362eadf8e32"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{mabenhnhan}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="459" y="4" width="70" height="11" forecolor="#000000" uuid="d237ec76-83c2-4be4-9c96-705d29c6e393"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="6" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Mã bệnh nhân]]></text>
			</staticText>
			<componentElement>
				<reportElement x="460" y="15" width="70" height="20" uuid="8425060b-6d39-49f3-8185-1dd23b8b8303"/>
				<jr:barbecue xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" type="Code128" drawText="false" checksumRequired="true" barHeight="45">
					<jr:codeExpression><![CDATA[$P{mabenhnhan}]]></jr:codeExpression>
					<jr:applicationIdentifierExpression><![CDATA[$P{mabenhnhan}]]></jr:applicationIdentifierExpression>
				</jr:barbecue>
			</componentElement>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="480" y="109" width="57" height="20" uuid="*************-40fd-9731-2378c4d9e0c9"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{giuong}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="393" y="109" width="48" height="20" uuid="d2b24ff5-9790-40fa-926f-a8d2e4a9c237"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{buong}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="1" y="149" width="107" height="17" forecolor="#000000" uuid="86628fd2-5ecd-4946-83c2-bde63b8c69af"/>
				<box leftPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Bác sĩ điều trị:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="108" y="149" width="429" height="17" forecolor="#000000" uuid="67aa6ad8-4f7e-4bb8-83bf-1b5097c1c88e">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nguoi_chi_dinh}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="108" y="166" width="159" height="17" forecolor="#000000" uuid="05f44a50-8a66-459f-baa7-3e82ad92d918">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ngay_lay_mau}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="290" y="109" width="65" height="20" forecolor="#000000" uuid="a94aa510-6a2d-44d1-ab53-3f8c49aac696">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Nước tiểu"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="227" y="109" width="63" height="20" forecolor="#000000" uuid="d91ae47b-5b71-419a-af46-d190e07dda1e"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Bệnh phẩm:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="-2" y="183" width="110" height="17" forecolor="#000000" uuid="85f0c2a7-0f70-463a-853a-c0e917561e9a"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Người nhận mẫu:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="267" y="183" width="98" height="17" forecolor="#000000" uuid="5d93bffb-64a0-4343-ba8e-ab53ce8b76ed"/>
				<box leftPadding="2"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Người giao mẫu:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="365" y="183" width="170" height="17" forecolor="#000000" uuid="41436219-05bd-4fa9-ad3b-7a74d9dafc1f">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nguoigiaomau_cmu}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="108" y="183" width="159" height="17" forecolor="#000000" uuid="0acc89d9-20c2-42ba-87ba-df35dda634a2">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nguoilaymau_cmu}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="1" y="166" width="107" height="17" forecolor="#000000" uuid="1ddd6e7a-f4f1-455e-b8a0-22640566e80d"/>
				<box leftPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["- Ngày giờ nhận mẫu:"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="267" y="166" width="98" height="17" forecolor="#000000" uuid="bb101983-dcb5-497e-8ade-e31b76df45fd"/>
				<box leftPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Ngày giờ lấy mẫu:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="3" y="44" width="97" height="21" isRemoveLineWhenBlank="true" uuid="57b90fac-15a0-419d-8cbe-841d66d49f5b"/>
				<box>
					<pen lineWidth="1.0" lineStyle="Dashed"/>
					<topPen lineWidth="1.0" lineStyle="Dashed"/>
					<leftPen lineWidth="1.0" lineStyle="Dashed"/>
					<bottomPen lineWidth="1.0" lineStyle="Dashed"/>
					<rightPen lineWidth="1.0" lineStyle="Dashed"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{maxetnghiem}.equals( "1" )?"":$F{solanin}<=1 ?"":"BẢN SAO"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="365" y="166" width="156" height="17" forecolor="#000000" uuid="ed562f18-2f9d-478c-bdd6-db0666d97046">
					<printWhenExpression><![CDATA[!$P{madonvi}.substring(0,2).equals("14")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ngaylaynhanmau}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="20" splitType="Stretch">
			<staticText>
				<reportElement x="430" y="0" width="106" height="20" forecolor="#000000" uuid="984b0659-54d4-4c98-8c9b-46960dad36c3"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[THIẾT BỊ]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="175" y="0" width="95" height="20" forecolor="#000000" uuid="2b53c437-e342-410b-970d-4080215b0866"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[KẾT QUẢ	]]></text>
			</staticText>
			<staticText>
				<reportElement x="270" y="0" width="110" height="20" forecolor="#000000" uuid="918eb254-63ee-4e6f-9728-cd857b73add5"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[KHOẢNG THAM CHIẾU]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="175" height="20" forecolor="#000000" uuid="bf9fe1e6-6195-452e-8f3d-5ef4fe9eba88"/>
				<box topPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[XÉT NGHIỆM]]></text>
			</staticText>
			<staticText>
				<reportElement x="380" y="0" width="50" height="20" forecolor="#000000" uuid="77eeca3c-1cc0-4ccb-a156-480bffa48b5c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isBold="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[ĐƠN VỊ]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="175" height="20" forecolor="#000000" uuid="57c7979b-bd61-437a-b81f-a308b1c4663d"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
					<paragraph leftIndent="5"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ten_xetnghiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="175" y="0" width="30" height="20" forecolor="#000000" uuid="0916f73f-3925-4309-bd45-c7c5dfff4b63"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineWidth="0.0" lineStyle="Dashed"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="true" isUnderline="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BINHTHUONG}.equals("-1")? "(L)":""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="430" y="0" width="106" height="20" uuid="ae4d6403-0296-468b-80d2-c31ba16154f1"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TEN_MAY_XN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="240" y="0" width="30" height="20" forecolor="#000000" uuid="3170fd8d-282e-4c82-9ce0-5b355aaf2bf9"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
					<rightPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="true" isUnderline="false" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[(
    (
        $F{BINHTHUONG}.equals("0")  
    )
    ? "(H)" : ""
) + ($F{CANHBAO_LAMSANG}.equals("1") ?"*":"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="270" y="0" width="110" height="20" forecolor="#000000" uuid="d17ce309-c460-457e-928b-41250a378ba6"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{chisobinhthuong}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="380" y="0" width="50" height="20" forecolor="#000000" uuid="955121f5-048b-42a5-82f4-ab1e07f9584c"/>
				<box>
					<pen lineWidth="0.5" lineStyle="Solid"/>
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<leftPen lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
					<rightPen lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dvnghiepvu_xetnghiem}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="175" y="0" width="95" height="20" forecolor="#000000" uuid="86957e21-1d3b-4ef4-828c-de9f7c9964c0"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="0.5" lineStyle="Solid"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="12" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BINHTHUONG}.equals("-1")? "<style size='11' isBold='true' pdfFontName='Times New Roman'>"+$F{ket_qua}+"</style>":$F{BINHTHUONG}.equals("0")? "<style size='11' isBold='true' pdfFontName='Times New Roman'>"+$F{ket_qua}+"</style>":$F{ket_qua}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<lastPageFooter>
		<band height="15">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="16" y="0" width="72" height="15" uuid="4a9d9bec-eba9-4356-9567-d158f3f68f41"/>
				<textElement verticalAlignment="Middle" rotation="None">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang "+$V{PAGE_NUMBER}+"/"+$V{count page}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="459" y="0" width="80" height="15" isRemoveLineWhenBlank="true" uuid="a58c4bd9-e7da-4c99-b123-620d36052075"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{maxetnghiem}.equals( "1" )?"":$F{solanin}<=1 ?"":"In lần "+($F{solanin}-1)]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="161" y="0" width="254" height="15" forecolor="#000000" uuid="55b7e0de-bf58-40f1-8b9e-3782cd81f557"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{maxetnghiem}.equals( "1" )?"":$F{solanin}<=1 ?"":"Người in bản sao: "+ $P{bsphutrach}]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
	<summary>
		<band height="100" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="310" y="0" width="227" height="17" uuid="5d19af86-011b-4e05-a4dd-d7bd4e364dd4"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isItalic="true" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dvtt}.substring(0,2).equals("14")?"......giờ......ngày......tháng......năm......":$F{NGAYTRAKETQUA}]]></textFieldExpression>
			</textField>
			<break>
				<reportElement x="0" y="99" width="575" height="1" uuid="063c83b5-9463-49c9-afa0-06de08032126"/>
			</break>
			<textField isBlankWhenNull="true">
				<reportElement x="310" y="17" width="227" height="23" forecolor="#000000" uuid="2875ff86-951f-4a76-8619-bdb82823bce8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dvtt}.equals("96014") ? "TRƯỞNG KHOA XÉT NGHIỆM" : "KÝ DUYỆT KẾT QUẢ"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="259" height="20" forecolor="#000000" uuid="e5c9a08b-6d34-48b7-88f3-73059e510ca5"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Người thực hiện: "+ $F{ngthuchien_in}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="76" y="20" width="181" height="15" uuid="108b6874-195a-40aa-aa05-b521da2bd9ce"/>
				<box>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="76" y="35" width="180" height="15" uuid="3bfb7d2f-4037-4f0c-9c14-c7969f053531"/>
				<box>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="76" y="50" width="180" height="15" uuid="0e5ab7f7-17ef-460d-8f86-b675855ad130"/>
				<box>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="Times New Roman"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="-1" y="35" width="77" height="15" forecolor="#000000" uuid="54e6741b-cddb-4f38-a790-2c71328eb750"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Ý kiến bác sĩ:"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="20" width="76" height="15" forecolor="#000000" uuid="fb46de39-54e7-4bd0-9640-86f6e8232a99"/>
				<textElement textAlignment="Right" verticalAlignment="Bottom">
					<font fontName="Times New Roman" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Lưu ý: "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="311" y="60" width="226" height="20" forecolor="#000000" uuid="0ba10c90-2558-4c5e-a0c0-dbae5bdcde75">
					<printWhenExpression><![CDATA[$F{ANCHUKY}.equals("0")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Bottom">
					<font fontName="Times New Roman" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGUOI_THUC_HIEN}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement x="16" y="65" width="128" height="20" uuid="4f7f3142-da4c-406f-976d-a0f718a1bd35"/>
				<staticText>
					<reportElement x="0" y="0" width="128" height="20" forecolor="#FFFFFF" uuid="b9cebdfd-57a3-4495-bdf6-665960bc6b8f"/>
					<textElement>
						<font fontName="Times New Roman"/>
					</textElement>
					<text><![CDATA[KYXEMKQ]]></text>
				</staticText>
			</frame>
		</band>
	</summary>
</jasperReport>
