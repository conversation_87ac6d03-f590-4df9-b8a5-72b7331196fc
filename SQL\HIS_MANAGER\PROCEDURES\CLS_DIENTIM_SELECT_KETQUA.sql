create or replace PROCEDURE               "HIS_MANAGER"."CLS_DIENTIM_SELECT_KETQUA" (
 p_SO_PHIEU_CDHA in varchar2,
 p_DVTT in varchar2,
 p_MA_CDHA in number,
 p_noitru in number,
 p_MA_KHAM_BENH in  varchar2,
 p_stt_benhan in varchar2,
 p_stt_dotdieutri in varchar2,
 p_stt_dieutri in varchar2, cur OUT SYS_REFCURSOR
)
IS
v_thamsocmu varchar2(50) default '0'; -- CMU VNPTHIS-4985
-- VNPTHIS-4697 23/11/2017 thêm
v_bacsidieutri varchar2(500) default '';
v_thamso_bacsidieutri int(2) default 0;
-- VNPTHIS-4697 23/11/2017 thêm
    v_thamso208 number(1) default '0';
	    v_sobenhan varchar2(500);
        v_thamso960616 number(10) := cmu_tsdv(p_dvtt, 960616, 0);
BEGIN
begin
select t.sobenhan into v_sobenhan
from his_manager.noitru_benhan t
where dvtt = p_DVTT and t.stt_benhan = p_stt_benhan and rownum = 1;
exception when no_data_found then v_sobenhan:= ' ';
end;
begin
select mota_thamso into v_thamso208 FROM HIS_FW.DM_THAMSO_DONVI where dvtt=p_dvtt and ma_thamso = 208;
exception when no_data_found then
        v_thamso208:='0';
end;
-- VNPTHIS-4697 23/11/2017 thêm tham số bác sĩ điều trị
begin
select mota_thamso into v_thamso_bacsidieutri
from his_fw.dm_thamso_donvi where dvtt= p_dvtt and ma_thamso = 94310;
exception when no_data_found then
      v_thamso_bacsidieutri := 0;
end;

begin
    if p_noitru = 0 then
      if v_thamso_bacsidieutri = 1 then
select cdanh.mota_chucdanh || '. ' || nv.ten_nhanvien into v_bacsidieutri
from kb_cd_cdha cd
         left join his_fw.dm_nhanvien nv on cd.bac_si_dieu_tri =  nv.ma_nhanvien
         left join his_fw.dm_chucdanh_nhanvien cdanh on nv.chucdanh_nhanvien = cdanh.ma_chucdanh
where cd.so_phieu_cdha = p_SO_PHIEU_CDHA and cd.dvtt = p_dvtt
  and cd.ma_kham_benh = p_ma_kham_benh;
else
select cdanh.mota_chucdanh || '. ' || nv.ten_nhanvien into v_bacsidieutri
from kb_cd_cdha cd
         left join his_fw.dm_nhanvien nv on cd.NGUOI_CHI_DINH =  nv.ma_nhanvien
         left join his_fw.dm_chucdanh_nhanvien cdanh on nv.chucdanh_nhanvien = cdanh.ma_chucdanh
where cd.so_phieu_cdha = p_SO_PHIEU_CDHA and cd.dvtt = p_dvtt
  and cd.ma_kham_benh = p_ma_kham_benh;
end if;
else
      if v_thamso_bacsidieutri = 1 then
select cdanh.mota_chucdanh || '. ' || nv.ten_nhanvien into v_bacsidieutri
from NOITRU_CD_CDHA cd
         left join his_fw.dm_nhanvien nv on cd.bac_si_dieu_tri =  nv.ma_nhanvien
         left join his_fw.dm_chucdanh_nhanvien cdanh on nv.chucdanh_nhanvien = cdanh.ma_chucdanh
where cd.so_phieu_cdha = p_SO_PHIEU_CDHA and cd.dvtt = p_dvtt
  and cd.stt_benhan = p_stt_benhan and cd.stt_dotdieutri = p_stt_dotdieutri
  and cd.stt_dieutri = p_stt_dieutri;
else
select cdanh.mota_chucdanh || '. ' || nv.ten_nhanvien into v_bacsidieutri
from NOITRU_CD_CDHA cd
         left join his_fw.dm_nhanvien nv on cd.NGUOI_CHI_DINH =  nv.ma_nhanvien
         left join his_fw.dm_chucdanh_nhanvien cdanh on nv.chucdanh_nhanvien = cdanh.ma_chucdanh
where cd.so_phieu_cdha = p_SO_PHIEU_CDHA and cd.dvtt = p_dvtt
  and cd.stt_benhan = p_stt_benhan and cd.stt_dotdieutri = p_stt_dotdieutri
  and cd.stt_dieutri = p_stt_dieutri;
end if;
end if;
exception when no_data_found then
    v_bacsidieutri:=' ';
end;
  -- VNPTHIS-4697 23/11/2017 thêm bác sĩ điều trị
  -- CMU  VNPTHIS-4985
BEGIN
select MOTA_THAMSO into v_thamsocmu from HIS_FW.DM_THAMSO_DONVI
where dvtt=P_DVTT and MA_THAMSO = 96103;
EXCEPTION when no_data_found then v_thamsocmu:='0';
END;
  -- END CMU   VNPTHIS-4985	  
  if p_noitru = 0 then
    open cur for select ct.SO_PHIEU_CDHA, ct.DVTT, ct.MA_CDHA,MO_TA, KET_QUA, BACSI_CHIDINH, BACSI_THUCHIEN, nvl(CHANDOAN, '') LOIDANBACSI, MA_MAUSIEUAM, MAU_SIEUAM,
                        --b.ten_cdha,
                        case when v_thamso208 in (0,4) and SUDUNG_TT37=0 and trunc(cd.ngay_tao) >= to_date('2018-07-15','yyyy-mm-dd') then
                                 case when NVL(TEN_TT15,' ')=' ' then b.ten_cdha else TEN_TT15 end else b.ten_cdha end as ten_cdha,
                        EXTRACT(day FROM cd.ngay_chi_dinh) as ngay_cd, EXTRACT(month FROM cd.ngay_chi_dinh) as thang_cd, EXTRACT(year FROM cd.ngay_chi_dinh) as nam_cd,
                        EXTRACT(day FROM ct.ngay_thuc_hien) as ngay_kq,  EXTRACT(month FROM ct.ngay_thuc_hien) as  thang_kq,  EXTRACT(year FROM ct.ngay_thuc_hien) as nam_kq, v_bacsidieutri as BACSI_DIEUTRI -- VNPTHIS-4697
                              ,to_char(ct.ngay_thuc_hien,'dd/mm/yyyy HH24:MI') ngaygio,
                        TO_CHAR(ct.ngay_thuc_hien,'HH') || ' giờ ' || to_char(ct.ngay_thuc_hien,'MI') || ' phút,' AS GIO_PHUT,
                        'Ngày ' || TO_CHAR(ct.ngay_thuc_hien,'dd') || ' tháng ' || to_char(ct.ngay_thuc_hien,'mm') || ' năm ' || to_char(ct.ngay_thuc_hien,'yyyy')  AS NGAY_THANG,
                        cdanh.mota_chucdanh || '. ' || nv.ten_nhanvien BACSIDIEUTRI,
                        0 NOITRU,
                        v_thamso960616 ANCHUKY
                 from KB_CD_CDHA_CT ct,cls_cdha b, kb_cd_cdha cd,
                      his_fw.dm_nhanvien           nv,
                      his_fw.dm_chucdanh_nhanvien  cdanh
                 where ct.DVTT=p_DVTT
                   and ct.SO_PHIEU_CDHA=p_SO_PHIEU_CDHA
                   and ct.MA_CDHA=p_MA_CDHA
                   and ct.MA_KHAM_BENH = p_MA_KHAM_BENH
                   and b.DVTT=p_DVTT
                   and b.MA_CDHA=p_MA_CDHA
                   AND cd.BAC_SI_DIEU_TRI = nv.ma_nhanvien
                   AND nv.chucdanh_nhanvien = cdanh.ma_chucdanh
                   and cd.dvtt = p_dvtt
                   and cd.so_phieu_cdha = ct.so_phieu_cdha;
else
    --CMU VNPTHIS-4985
    if v_thamsocmu != '0' then
            open cur for select ct.SO_PHIEU_CDHA, ct.DVTT, ct.MA_CDHA,MO_TA,
                                KET_QUA,
                                BACSI_CHIDINH,BACSI_THUCHIEN,
                                cdanh.mota_chucdanh || '. ' || nv.ten_nhanvien BACSIDIEUTRI, --CMU 17/10/2017
                                1 NOITRU,
                                nvl(CHANDOAN, '') LOIDANBACSI,
                                MA_MAUSIEUAM, MAU_SIEUAM,--b.ten_cdha,
                                case when v_thamso208 in (0,4) and SUDUNG_TT37=0 and trunc(cd.ngay_tao) >= to_date('2018-07-15','yyyy-mm-dd') then
                                         case when NVL(TEN_TT15,' ')=' ' then b.ten_cdha else TEN_TT15 end else b.ten_cdha end || decode(ct.ghichu_tencdha, null, '', '[' || ct.ghichu_tencdha || ']') as ten_cdha,
                                EXTRACT(day FROM cd.ngay_chi_dinh) as ngay_cd,
                                EXTRACT(month FROM cd.ngay_chi_dinh) as thang_cd, EXTRACT(year FROM cd.ngay_chi_dinh) as nam_cd,
                                EXTRACT(day FROM ct.ngay_thuc_hien) as ngay_kq,
                                EXTRACT(month FROM ct.ngay_thuc_hien) as  thang_kq,
                                EXTRACT(year FROM ct.ngay_thuc_hien) as nam_kq,
                                TO_CHAR(ct.ngay_thuc_hien,'HH') || ' giờ ' || to_char(ct.ngay_thuc_hien,'MI') || ' phút,' AS GIO_PHUT
                                 ,to_char(ct.ngay_thuc_hien,'dd/mm/yyyy HH24:MI') ngaygio,
                                'Ngày ' || TO_CHAR(ct.ngay_thuc_hien,'dd') || ' tháng ' || to_char(ct.ngay_thuc_hien,'mm') || ' năm ' || to_char(ct.ngay_thuc_hien,'yyyy')  AS NGAY_THANG,
                                v_thamso960616 ANCHUKY
                         from
                             NOITRU_CD_CDHA_CHI_TIET ct,
                             cls_cdha b,
                             NOITRU_CD_CDHA cd,
                             his_fw.dm_nhanvien           nv,
                             his_fw.dm_chucdanh_nhanvien  cdanh
                         where ct.DVTT=p_DVTT
                           and ct.SO_PHIEU_CDHA=p_SO_PHIEU_CDHA
                           and ct.MA_CDHA=p_MA_CDHA
                           and ct.stt_benhan = p_stt_benhan
                           and ct.stt_dotdieutri=p_stt_dotdieutri
                           and ct.stt_dieutri = p_stt_dieutri
                           AND cd.BAC_SI_DIEU_TRI = nv.ma_nhanvien
                           AND nv.chucdanh_nhanvien = cdanh.ma_chucdanh
                           and b.DVTT=p_DVTT
                           and b.MA_CDHA=p_MA_CDHA
                           and cd.dvtt = p_dvtt
                           and cd.so_phieu_cdha = ct.so_phieu_cdha;
--CMU END VNPTHIS-4985
ELSE
    open cur for select ct.SO_PHIEU_CDHA, ct.DVTT, ct.MA_CDHA,MO_TA, KET_QUA, BACSI_CHIDINH,BACSI_THUCHIEN, nvl(CHANDOAN, '') LOIDANBACSI, MA_MAUSIEUAM, MAU_SIEUAM,
                        --b.ten_cdha,
                        case when v_thamso208 in (0,4) and SUDUNG_TT37=0 and trunc(cd.ngay_tao) >= to_date('2018-07-15','yyyy-mm-dd') then
                                 case when NVL(TEN_TT15,' ')=' ' then b.ten_cdha else TEN_TT15 end else b.ten_cdha end || decode(ct.ghichu_tencdha, null, '', '[' || ct.ghichu_tencdha || ']') as ten_cdha,
                        EXTRACT(day FROM cd.ngay_chi_dinh) as ngay_cd, EXTRACT(month FROM cd.ngay_chi_dinh) as thang_cd, EXTRACT(year FROM cd.ngay_chi_dinh) as nam_cd,
                        EXTRACT(day FROM ct.ngay_thuc_hien) as ngay_kq,  EXTRACT(month FROM ct.ngay_thuc_hien) as  thang_kq,  EXTRACT(year FROM ct.ngay_thuc_hien) as nam_kq, v_bacsidieutri as BACSI_DIEUTRI -- VNPTHIS-4697
                         ,to_char(ct.ngay_thuc_hien,'dd/mm/yyyy HH24:MI') ngaygio,
                        TO_CHAR(ct.ngay_thuc_hien,'HH') || ' giờ ' || to_char(ct.ngay_thuc_hien,'MI') || ' phút,' AS GIO_PHUT,
                        'Ngày ' || TO_CHAR(ct.ngay_thuc_hien,'dd') || ' tháng ' || to_char(ct.ngay_thuc_hien,'mm') || ' năm ' || to_char(ct.ngay_thuc_hien,'yyyy')  AS NGAY_THANG,
                        v_sobenhan as sobenhan,
                        cdanh.mota_chucdanh || '. ' || nv.ten_nhanvien BACSIDIEUTRI,
                        v_thamso960616 ANCHUKY
                 from NOITRU_CD_CDHA_CHI_TIET ct,cls_cdha b, NOITRU_CD_CDHA cd,
                      his_fw.dm_nhanvien           nv,
                      his_fw.dm_chucdanh_nhanvien  cdanh
                 where ct.DVTT=p_DVTT
                   and ct.SO_PHIEU_CDHA=p_SO_PHIEU_CDHA
                   and ct.MA_CDHA=p_MA_CDHA AND cd.BAC_SI_DIEU_TRI = nv.ma_nhanvien
                   AND nv.chucdanh_nhanvien = cdanh.ma_chucdanh
                   and ct.stt_benhan = p_stt_benhan
                   and ct.stt_dotdieutri=p_stt_dotdieutri
                   and ct.stt_dieutri = p_stt_dieutri
                   and b.DVTT=p_DVTT
                   and b.MA_CDHA=p_MA_CDHA
                   and cd.dvtt = p_dvtt
                   and cd.so_phieu_cdha = ct.so_phieu_cdha;
END IF;
end if;
END;