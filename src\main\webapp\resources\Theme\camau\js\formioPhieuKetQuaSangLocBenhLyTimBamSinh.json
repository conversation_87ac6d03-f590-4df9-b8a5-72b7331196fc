{"display": "form", "components": [{"label": "ID", "key": "ID", "type": "hidden", "input": true}, {"label": "Cân nặng", "key": "CAN_NANG", "type": "hidden", "input": true}, {"label": "<PERSON><PERSON><PERSON>", "key": "GIO_SINH", "type": "hidden", "input": true}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON><PERSON> t<PERSON>", "customClass": "mr-2 formio-css-datetime formio-css-suffix formio-js-validate-1 luulog", "key": "NGAY_TAO_PHIEU", "type": "datetime", "format": "dd/MM/yyyy HH:mm", "validate": {"required": true, "customMessage": "<PERSON><PERSON><PERSON> t<PERSON>o phi<PERSON>u là bắt buộc"}, "timePicker": {"showMeridian": false}, "widget": {"enableTime": true, "format": "dd/MM/yyyy HH:mm", "time_24hr": true}}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "<PERSON><PERSON><PERSON><PERSON> th<PERSON> hi<PERSON>n", "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 luulog", "tableView": true, "dataSrc": "json", "template": "<span>{{ item.tennhanvien }}</span>", "validate": {"required": true, "customMessage": "<PERSON><PERSON><PERSON><PERSON> thực hiện là bắt buộc"}, "validateWhenHidden": false, "key": "NGUOI_THUC_HIEN", "type": "select", "input": true}], "width": 4, "size": "md", "currentWidth": 4}, {"components": [{"label": "<PERSON><PERSON><PERSON> s<PERSON> ký duy<PERSON>t kết quả", "widget": "<PERSON><PERSON><PERSON>", "customClass": "lu<PERSON><PERSON>", "tableView": true, "dataSrc": "json", "template": "<span>{{ item.TEXT }}</span>", "validate": {"required": true, "customMessage": "<PERSON><PERSON><PERSON> sĩ ký duyệt kết quả là bắt buộc"}, "validateWhenHidden": false, "key": "TRUONG_KHOA", "type": "select", "input": true}], "width": 4, "size": "md", "currentWidth": 4}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b>THÔNG TIN CÁ NHÂN</b>", "input": false}, {"label": "Columns", "columns": [{"components": [{"label": "Tuổi thai: tu<PERSON><PERSON>", "customClass": "mr-2 luulog", "tableView": true, "validateWhenHidden": false, "key": "TUOI_THAI_TUAN", "type": "number", "input": true}], "width": 3, "size": "md", "currentWidth": 3}, {"components": [{"label": "Tuổi thai: ngày", "customClass": "mr-2 luulog", "tableView": true, "validateWhenHidden": false, "key": "TUOI_THAI_NGAY", "type": "number", "input": true}], "width": 3, "size": "md", "currentWidth": 3}, {"components": [{"label": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 formio-css-datetime formio-css-suffix luulog", "key": "NGAY_SANG_LOC", "type": "datetime", "format": "dd/MM/yyyy", "enableTime": false}], "width": 3, "size": "md", "currentWidth": 3}, {"components": [{"label": "<PERSON><PERSON> hồ sơ", "customClass": "lu<PERSON><PERSON>", "tableView": true, "validateWhenHidden": false, "key": "MA_HO_SO", "type": "textfield", "input": true}], "width": 3, "size": "md", "currentWidth": 3}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b>KẾT QUẢ</b>", "input": false}, {"label": "Columns", "columns": [{"components": [{"label": "<PERSON><PERSON><PERSON> qu<PERSON>", "hideLabel": true, "widget": "<PERSON><PERSON><PERSON>", "customClass": "mr-2 formio-css-selection luulog", "tableView": true, "data": {"values": [{"label": "<PERSON><PERSON>", "value": "AM_TINH"}, {"label": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "value": "DUONG_TINH"}]}, "validateWhenHidden": false, "key": "KET_QUA", "type": "select", "input": true}], "width": 3, "size": "md", "currentWidth": 3}], "key": "columns", "type": "columns", "customClass": "ml-0 mr-0"}, {"label": "Data Grid", "hideLabel": true, "customClass": "formio-css-grid", "reorder": false, "addAnother": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "addAnotherPosition": "bottom", "layoutFixed": false, "enableRowGroups": false, "initEmpty": false, "tableView": false, "defaultValue": [{"SANG_LOC_DI_TAT_BAM_SINH": "<PERSON><PERSON> bão hòa oxygen ở tay phải", "SANG_LOC_DI_TAT_BAM_SINH_L1": "", "SANG_LOC_DI_TAT_BAM_SINH_L2": "", "SANG_LOC_DI_TAT_BAM_SINH_L3": ""}, {"SANG_LOC_DI_TAT_BAM_SINH": "<PERSON><PERSON> bão hòa oxygen ở chân", "SANG_LOC_DI_TAT_BAM_SINH_L1": "", "SANG_LOC_DI_TAT_BAM_SINH_L2": "", "SANG_LOC_DI_TAT_BAM_SINH_L3": ""}, {"SANG_LOC_DI_TAT_BAM_SINH": "<PERSON><PERSON><PERSON> (giữa tay và chân)", "SANG_LOC_DI_TAT_BAM_SINH_L1": "", "SANG_LOC_DI_TAT_BAM_SINH_L2": "", "SANG_LOC_DI_TAT_BAM_SINH_L3": ""}], "validateWhenHidden": false, "key": "BANG_KET_QUA", "type": "datagrid", "input": true, "disableAddingRemovingRows": true, "components": [{"label": "<b><PERSON><PERSON><PERSON> lọc dị tật b<PERSON><PERSON> sinh</b>", "customClass": "formio-css-disable-none-bottom", "tableView": true, "validateWhenHidden": false, "key": "SANG_LOC_DI_TAT_BAM_SINH", "type": "textfield", "input": true, "disabled": true}, {"label": "<b>Lần 1</b>", "suffix": "%", "customClass": "formio-css-suffix", "tableView": true, "validateWhenHidden": false, "key": "SANG_LOC_DI_TAT_BAM_SINH_L1", "type": "number", "input": true}, {"label": "<b>Lần 2</b>", "suffix": "%", "customClass": "formio-css-suffix", "tableView": true, "validateWhenHidden": false, "key": "SANG_LOC_DI_TAT_BAM_SINH_L2", "type": "number", "input": true}, {"label": "<b>Lần 3</b>", "suffix": "%", "customClass": "formio-css-suffix", "tableView": true, "validateWhenHidden": false, "key": "SANG_LOC_DI_TAT_BAM_SINH_L3", "type": "number", "input": true}]}, {"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b><PERSON><PERSON> ngh<PERSON></b>", "input": false}, {"label": "<PERSON><PERSON> nghị", "hideLabel": true, "customClass": "formio-css-textarea formio-js-scale-textarea luulog", "applyMaskOn": "change", "autoExpand": false, "tableView": true, "validate": {"maxLength": 2900, "customMessage": "<PERSON><PERSON><PERSON> văn bản không đư<PERSON><PERSON> có quá 2900 ký tự"}, "validateWhenHidden": false, "key": "DE_NGHI", "type": "textarea", "input": true}, {"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "<b><PERSON><PERSON><PERSON> ý:</b>", "input": false}, {"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "- <PERSON><PERSON><PERSON><PERSON> pháp này chỉ mang tính chất tầm so<PERSON>t, kh<PERSON>ng phải là chẩn đoán.", "input": false}, {"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "- Chỉ phát hiện một số bệnh tim bẩm sinh nặng phụ thuộc ống động mạch cần phải can thiệp cấp cứ<PERSON> như: <PERSON><PERSON> chứ<PERSON>, chuyển vị đại động mạch, thân chung động mạch, thiể<PERSON> sản tim tr<PERSON>i, hẹp động mạch chủ ...", "input": false}, {"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "- Tầm soát có thể âm tính giả hoặc dương tính giả do thời điểm tầm soát trước 24 giờ tuổi, trẻ bị lạnh, qu<PERSON><PERSON> khó<PERSON>.", "input": false}, {"label": "Titles", "customClass": "formio-css-p", "type": "htmlelement", "tag": "p", "content": "- Phương pháp tầm soát này không phát hiện đư<PERSON><PERSON> những bệnh tim bẩm sinh không phụ thuộc ống động mạch.", "input": false}]}