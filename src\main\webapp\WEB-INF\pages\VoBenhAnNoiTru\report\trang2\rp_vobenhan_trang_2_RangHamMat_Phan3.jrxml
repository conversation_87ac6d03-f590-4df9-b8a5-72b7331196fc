<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rp_vobenhan_trang_2" language="groovy" pageWidth="595" pageHeight="842" columnWidth="580" leftMargin="10" rightMargin="5" topMargin="10" bottomMargin="10" uuid="513ef620-30fb-4b13-96b1-94f754855041">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="158"/>
	<subDataset name="dataset1" uuid="7aa11af8-2d37-4137-b19e-c09052de7e0d"/>
	<parameter name="HINHANHVV" class="java.lang.String"/>
	<queryString>
		<![CDATA[select 1 from dual]]>
	</queryString>
	<field name="ID" class="java.math.BigDecimal"/>
	<field name="DVTT" class="java.lang.String"/>
	<field name="LYDOVAOVIEN" class="java.lang.String"/>
	<field name="BENHSU" class="java.lang.String"/>
	<field name="TIENSUBANTHAN" class="java.lang.String"/>
	<field name="BOXDDDIUNG" class="java.lang.String"/>
	<field name="DDDIUNG" class="java.lang.String"/>
	<field name="BOXDDTHUOCLA" class="java.lang.String"/>
	<field name="DDTHUOCLA" class="java.lang.String"/>
	<field name="BOXDDMATUY" class="java.lang.String"/>
	<field name="DDMATUY" class="java.lang.String"/>
	<field name="BOXDDTHUOCLAO" class="java.lang.String"/>
	<field name="DDTHUOCLAO" class="java.lang.String"/>
	<field name="BOXDDRUOUBIA" class="java.lang.String"/>
	<field name="DDRUOUBIA" class="java.lang.String"/>
	<field name="BOXDDKHAC" class="java.lang.String"/>
	<field name="DDKHAC" class="java.lang.String"/>
	<field name="TIENSUGIADINH" class="java.lang.String"/>
	<field name="KHAMTOANTHAN" class="java.lang.String"/>
	<field name="MACH" class="java.lang.String"/>
	<field name="NHIETDO" class="java.lang.String"/>
	<field name="HUYETAPTREN" class="java.lang.String"/>
	<field name="HUYETAPDUOI" class="java.lang.String"/>
	<field name="NHIPTHO" class="java.lang.String"/>
	<field name="CANNANG" class="java.lang.String"/>
	<field name="CHIEUCAO" class="java.lang.String"/>
	<field name="BMI" class="java.lang.String"/>
	<field name="TUANHOAN" class="java.lang.String"/>
	<field name="HOHAP" class="java.lang.String"/>
	<field name="TIEUHOA" class="java.lang.String"/>
	<field name="TIETNIEUSINHDUC" class="java.lang.String"/>
	<field name="THANKINH" class="java.lang.String"/>
	<field name="XUONGKHOP" class="java.lang.String"/>
	<field name="TMH" class="java.lang.String"/>
	<field name="RHM" class="java.lang.String"/>
	<field name="MAT" class="java.lang.String"/>
	<field name="NOITIET" class="java.lang.String"/>
	<field name="CANLAMSANG" class="java.lang.String"/>
	<field name="TOMTATBA" class="java.lang.String"/>
	<field name="BENHCHINH" class="java.lang.String"/>
	<field name="BENHPHU" class="java.lang.String"/>
	<field name="PHANBIET" class="java.lang.String"/>
	<field name="TIENLUONG" class="java.lang.String"/>
	<field name="HUONGDIEUTRI" class="java.lang.String"/>
	<field name="NV_LAMBENHAN" class="java.lang.String"/>
	<field name="NGAY_LAMBENHAN" class="java.lang.String"/>
	<field name="ANCHUKY" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="75">
			<staticText>
				<reportElement x="0" y="0" width="580" height="15" uuid="e30263f8-0a70-4b95-a0b4-8a269ddaca1e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[4. Các xét nghiệm cận lâm sàng cần làm:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="15" width="580" height="60" isPrintWhenDetailOverflows="true" uuid="da991d06-afa4-4af7-93fa-3cd3fb622b3c"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{CANLAMSANG} != null && !$F{CANLAMSANG}.isEmpty()) ? $F{CANLAMSANG} : ""]]></textFieldExpression>
			</textField>
		</band>
		<band height="75">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="15" width="580" height="60" isPrintWhenDetailOverflows="true" uuid="96377861-b532-4f1a-8025-524231dc20f5"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TOMTATBA}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="0" width="580" height="15" uuid="c9cd858c-9ca2-43cd-b272-ad7e9d00de80"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[5. Tóm tắt bệnh án:]]></text>
			</staticText>
		</band>
	</detail>
	<summary>
		<band height="447">
			<staticText>
				<reportElement x="261" y="375" width="319" height="15" uuid="da73a898-32d1-4ef8-8614-bf3076a9ed30"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Ký, ghi rõ họ tên)]]></text>
			</staticText>
			<staticText>
				<reportElement x="261" y="360" width="319" height="15" uuid="0dcb7ea2-f562-4bea-b685-ff7c8115caac"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Bác sĩ làm bệnh án]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="261" y="432" width="319" height="15" uuid="18e5e201-1a24-4364-9c9c-6324a1377bde">
					<printWhenExpression><![CDATA[$F{ANCHUKY}.equals("0")]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12" isItalic="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Họ và tên: "+$F{NV_LAMBENHAN}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="261" y="345" width="319" height="15" uuid="048ec256-5241-4639-abd5-27b332b7a957"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="12" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAY_LAMBENHAN}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="15" width="580" height="60" uuid="0189ae3c-5d14-4e71-9f98-863db51c412b"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["+ Bệnh chính:"+(($F{BENHCHINH} != null && !$F{BENHCHINH}.isEmpty())?$F{BENHCHINH}:"")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="0" width="580" height="15" uuid="0dc96fae-25d7-459c-9d31-b4b084ae84a4"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<text><![CDATA[IV. Chẩn đoán vào khoa điều trị]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="135" width="580" height="60" uuid="b7e946ad-c812-465e-a9aa-2b67b6a1cf29"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["+ Phân biệt:"+(($F{PHANBIET} != null && !$F{PHANBIET}.isEmpty())?$F{PHANBIET}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="75" width="580" height="60" uuid="3c0bcd6f-0064-413a-950b-bf07fe1b1b3c"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["+ Bệnh kèm theo" + "<i>(nếu có):</i>" + (($F{BENHPHU} != null && !$F{BENHPHU}.isEmpty())?$F{BENHPHU}:"")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="195" width="580" height="15" uuid="4e690c1c-8475-4e8d-9195-27ee6cb07752"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<text><![CDATA[V. TIÊN LƯỢNG]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="210" width="580" height="60" uuid="0294b8d7-560a-4f8b-8816-a0d8eff7c295"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{TIENLUONG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="285" width="580" height="60" uuid="512f65bd-441f-402a-bc1f-8f9c31ab60b9"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="9" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HUONGDIEUTRI}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="270" width="580" height="15" uuid="48c31beb-7af2-44e4-8cc0-73b6148522a7"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<text><![CDATA[VI. HƯỚNG ĐIỀU TRỊ]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
