function getBAPHATHAIJSON () {
    let loaiBA = 'PHATHAI'
    let form;
    let formTongket;
    let keyMauHSBANGOAITRUPHCN = "MAUHSPHATHAI";
    let keyMauHSBANGOAITRUPHCNTongket = "MAUHSBAPHATHAITONGKET";
    let formioMauHSBA;
    let formioMauHSBATongket;

    return {
        script: {},
        scriptTongket: {},
        initObjectFormPage2: function() {
            return getJSONObjectForm([
                {
                    "collapsible": true,
                    "key": "p-lydovaovien",
                    "type": "panel",
                    "label": "Lý do vào viện",
                    "title": "BỆNH ÁN VÀ HỎI BỆNH",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap",
                    "components": [
                        {
                            "label": "Titles",
                            "type": "htmlelement",
                            "tag": "p",
                            "content": "<b>LÝ DO PHÁ THAI</b>",
                            "input": false
                        },
                        {
                            "label": "<PERSON>ý do phá thai",
                            "key": "LYDOPHATHAI",
                            "type": "textarea",
                            others: {
                                "hideLabel": true,
                            },
                            "validate": {
                                "maxLength": 3000,
                                "required": true
                            }
                        },
                        {
                            "label": "Titles",
                            "type": "htmlelement",
                            "tag": "p",
                            "content": "</br><b>TIỀN SỬ</b></br></br>1. Tiền sử sản phụ khoa",
                            "input": false
                        },
                        {
                            "label": "Columns",
                            "columns": [
                                {
                                    "components": [
                                        {
                                            "label": "PARA",
                                            "customClass": "mr-2",
                                            "key": "PARA",
                                            "type": "textfield",
                                        }
                                    ],
                                    "width": 2, "size": "md", "currentWidth": 2
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Số con hiện có",
                                            "customClass": "mr-2",
                                            "key": "SOCONHIENCO",
                                            "type": "number",
                                        }
                                    ],
                                    "width": 2, "size": "md", "currentWidth": 2
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Trai",
                                            "customClass": "mr-2",
                                            "key": "TRAI",
                                            "type": "number",
                                        }
                                    ],
                                    "width": 1, "size": "md", "currentWidth": 1
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Gái",
                                            "customClass": "mr-2",
                                            "key": "GAI",
                                            "type": "number",
                                        }
                                    ],
                                    "width": 1, "size": "md", "currentWidth": 1
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Đã phẫu thuật lấy thai",
                                            "customClass": "mr-2 formio-css-suffix",
                                            "key": "DAPTLAYTHAI",
                                            "type": "number",
                                            others: {
                                                "suffix": "lần",
                                            },
                                        }
                                    ],
                                    "width": 3, "size": "md", "currentWidth": 3
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Năm phẫu thuật lần cuối",
                                            "customClass": "",
                                            "key": "NAMPTLANCUOI",
                                            "type": "number",
                                        }
                                    ],
                                    "width": 3, "size": "md", "currentWidth": 3
                                },
                            ],
                            "key": "columns",
                            "type": "columns",
                            "customClass": "ml-0 mr-0 formio-css-baseline"
                        },
                        {
                            "label": "Columns",
                            "columns": [
                                {
                                    "components": [
                                        {
                                            "label": "Các phẫu thuật TC khác",
                                            "customClass": "mr-2",
                                            "key": "CACPTTCKHAC",
                                            "type": "textfield",
                                        }
                                    ],
                                    "width": 5, "size": "md", "currentWidth": 5
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Năm",
                                            "customClass": "mr-2",
                                            "key": "NAMCACPTTCKHAC",
                                            "type": "number",
                                        }
                                    ],
                                    "width": 1, "size": "md", "currentWidth": 1
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Số lần đã phá thai lần",
                                            "customClass": "mr-2 formio-css-suffix",
                                            "key": "SOLANDAPHATHAI",
                                            "type": "number",
                                            others: {
                                                "suffix": "lần",
                                            },
                                        }
                                    ],
                                    "width": 3, "size": "md", "currentWidth": 3
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Lần gần nhất: Tháng",
                                            "customClass": "mr-2",
                                            "key": "THANGPHAGANHAT",
                                            "type": "number",
                                        }
                                    ],
                                    "width": 2, "size": "md", "currentWidth": 2
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Năm",
                                            "customClass": "",
                                            "key": "NAMPHAGANHAT",
                                            "type": "number",
                                        }
                                    ],
                                    "width": 1, "size": "md", "currentWidth": 1
                                },
                            ],
                            "key": "columns",
                            "type": "columns",
                            "customClass": "ml-0 mr-0 formio-css-baseline"
                        },
                        {
                            "label": "Biện pháp TT đang sử dụng khi có thai lần này",
                            "customClass": "",
                            "key": "BIENPHAPTTLANNAY",
                            "type": "select",
                            others: {
                                "data": {
                                    "values": [
                                        {
                                            "label": "DCTC",
                                            "value": "1",
                                        },
                                        {
                                            "label": "Thuốc tiêm",
                                            "value": "2",
                                        },
                                        {
                                            "label": "Thuốc uống",
                                            "value": "3",
                                        },
                                        {
                                            "label": "Que cấy",
                                            "value": "4",
                                        },
                                        {
                                            "label": "Bao cao su",
                                            "value": "5",
                                        },
                                        {
                                            "label": "Thuốc tránh thai khẩn cấp",
                                            "value": "6",
                                        },
                                        {
                                            "label": "Biện pháp khác",
                                            "value": "7",
                                        },
                                        {
                                            "label": "Không sử dụng biện pháp nào",
                                            "value": "8",
                                        }
                                    ]
                                },
                            }
                        },
                        {
                            "label": "Columns",
                            "columns": [
                                {
                                    "components": [
                                        {
                                            "label": "2. Tiền sử bệnh",
                                            "customClass": "mr-2",
                                            "key": "TIENSUBENH",
                                            "type": "textarea",
                                            others: {
                                                "tooltip": "bệnh tám thần, tuần hoàn, hô hấp, tiêu hóa, tiết niệu, dị ứng",
                                            }
                                        },
                                    ],
                                    "width": 9, "size": "md", "currentWidth": 9
                                },
                                {
                                    "components": [
                                        {
                                            "label": "3. Tình trạng hôn nhân",
                                            "customClass": "",
                                            "key": "TINHTRANGHONNHAN",
                                            "type": "select",
                                            others: {
                                                "data": {
                                                    "values": [
                                                        {
                                                            "label": "Có chồng",
                                                            "value": "1",
                                                        },
                                                        {
                                                            "label": "Không có chồng",
                                                            "value": "2",
                                                        },
                                                    ]
                                                },
                                            }
                                        }
                                    ],
                                    "width": 3, "size": "md", "currentWidth": 3
                                },
                            ],
                            "key": "columns",
                            "type": "columns",
                            "customClass": "ml-0 mr-0 formio-css-baseline"
                        },
                    ]
                },
                {
                    "collapsible": true,
                    "key": "p-khambenh",
                    "type": "panel",
                    "label": "Khám bệnh",
                    "title": "KHÁM BỆNH",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap",
                    "components": [
                        {
                            "label": "left",
                            "columns": [
                                {
                                    "components": [
                                        {
                                            "label": "1. Toàn thân",
                                            "key": "KHAMTOANTHAN",
                                            "type": "textarea",
                                            "input": true,
                                            "validate": {
                                                "maxLength": 3000,
                                                "required": true
                                            }
                                        },
                                        {
                                            "label": "2. Các bộ phận",
                                            "key": "CACBOPHAN",
                                            "type": "textarea",
                                            "input": true,
                                            "validate": {
                                                "maxLength": 3000,
                                            }
                                        },
                                        {
                                            "label": "Titles",
                                            "type": "htmlelement",
                                            "tag": "p",
                                            "content": "3. Khám phụ khoa",
                                            "input": false
                                        },
                                        {
                                            "label": "Columns",
                                            "columns": [
                                                {
                                                    "components": [
                                                        {
                                                            "label": "Ngày đầu kỳ kinh cuối cùng",
                                                            "customClass": "mr-2",
                                                            "key": "NGAYDAUKYKINHCUOI",
                                                            "type": "datetime",
                                                            others: {
                                                                "format": "dd/MM/yyyy",
                                                                "enableTime": false
                                                            }
                                                        },
                                                    ],
                                                    "width": 3, "size": "md", "currentWidth": 3
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "Âm hộ",
                                                            "customClass": "mr-2",
                                                            "key": "AMHO",
                                                            "type": "textfield",
                                                        }
                                                    ],
                                                    "width": 3, "size": "md", "currentWidth": 3
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "Âm đạo",
                                                            "customClass": "mr-2",
                                                            "key": "AMDAO",
                                                            "type": "textfield",
                                                        }
                                                    ],
                                                    "width": 3, "size": "md", "currentWidth": 3
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "Cổ tử cung",
                                                            "customClass": "",
                                                            "key": "COTUCUNG",
                                                            "type": "textfield",
                                                        }
                                                    ],
                                                    "width": 3, "size": "md", "currentWidth": 3
                                                },
                                            ],
                                            "key": "columns",
                                            "type": "columns",
                                            "customClass": "ml-0 mr-0 formio-css-baseline"
                                        },
                                        {
                                            "label": "Columns",
                                            "columns": [
                                                {
                                                    "components": [
                                                        {
                                                            "label": "Tử cung",
                                                            "customClass": "mr-2",
                                                            "key": "TUCUNG",
                                                            "type": "textfield",
                                                            others: {
                                                                "tooltip": "kích thước, mật độ, tư thế",
                                                            },
                                                        },
                                                    ],
                                                    "width": 4, "size": "md", "currentWidth": 4
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "Phần phụ phải",
                                                            "customClass": "mr-2",
                                                            "key": "PHANPHUPHAI",
                                                            "type": "textfield",
                                                        },
                                                    ],
                                                    "width": 4, "size": "md", "currentWidth": 4
                                                },
                                                {
                                                    "components": [
                                                        {
                                                            "label": "Phần phụ trái",
                                                            "key": "PHANPHUTRAI",
                                                            "type": "textfield",
                                                        },
                                                    ],
                                                    "width": 4, "size": "md", "currentWidth": 4
                                                },
                                            ],
                                            "key": "columns",
                                            "type": "columns",
                                            "customClass": "ml-0 mr-0 formio-css-baseline"
                                        },
                                        {
                                            "label": "4. Các xét nghiệm cần làm",
                                            "key": "CACXNCANLAM",
                                            "type": "textarea",
                                            "input": true,
                                            "validate": {
                                                "maxLength": 3000,
                                            }
                                        },
                                    ],
                                    "width": 8, "size": "md", "currentWidth": 8
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Tabs",
                                            "components": [
                                                {
                                                    "label": "Chỉ số sinh tồn",
                                                    "key": "sinhhieu",
                                                    "components": [
                                                        {
                                                            "label": "chisocothe",
                                                            "columns": [
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "Mạch",
                                                                            "customClass": "pr-2",
                                                                            "validate": {
                                                                                "min": 0,
                                                                                "max": 200,
                                                                                required: true
                                                                            },
                                                                            "key": "MACH",
                                                                            "type": "number",
                                                                        }
                                                                    ],
                                                                    "width": 4,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "size": "md",
                                                                    "currentWidth": 4
                                                                },
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "Nhiệt độ",
                                                                            "customClass": "pr-2",
                                                                            "validate": {
                                                                                "min": 35,
                                                                                "max": 43,
                                                                                required: true
                                                                            },
                                                                            "key": "NHIETDO",
                                                                            "type": "number",
                                                                        }
                                                                    ],
                                                                    "width": 4,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "size": "md",
                                                                    "currentWidth": 4
                                                                },
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "Nhịp thở",
                                                                            "customClass": "pr-2",
                                                                            "validate": {
                                                                                "min": 0,
                                                                                "max": 200,
                                                                                required: true
                                                                            },
                                                                            "key": "NHIPTHO",
                                                                            "type": "number",
                                                                        }
                                                                    ],
                                                                    "width": 4,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "size": "md",
                                                                    "currentWidth": 4
                                                                },

                                                            ],
                                                            "customClass": "ml-0 mr-0",
                                                            "key": "chisocothe",
                                                            "type": "columns",
                                                        },
                                                        {
                                                            "label": "chisocothe2",
                                                            "columns": [
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "Huyết áp trên",
                                                                            "customClass": "pr-2",
                                                                            "validate": {
                                                                                "min": 0,
                                                                                // "max": 200,
                                                                                required: true
                                                                            },
                                                                            "key": "HUYETAPTREN",
                                                                            "type": "number",
                                                                        }
                                                                    ],
                                                                    "width": 6,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "size": "md",
                                                                    "currentWidth": 6
                                                                },
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "Huyết áp dưới",
                                                                            "customClass": "pr-2",
                                                                            "validate": {
                                                                                "min": 0,
                                                                                // "max": 300,
                                                                                required: true
                                                                            },
                                                                            "key": "HUYETAPDUOI",
                                                                            "type": "number",
                                                                        }
                                                                    ],
                                                                    "width": 6,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "size": "md",
                                                                    "currentWidth": 6
                                                                },
                                                            ],
                                                            "customClass": "ml-0 mr-0",
                                                            "key": "chisocothe2",
                                                            "type": "columns",
                                                        },
                                                        {
                                                            "label": "chisocothe3",
                                                            "columns": [
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "Cân nặng(kg)",
                                                                            "customClass": "pr-2",
                                                                            "validate": {
                                                                                "min": 0,
                                                                                "max": 400,
                                                                                required: true
                                                                            },
                                                                            "key": "CANNANG",
                                                                            "type": "number",
                                                                        }
                                                                    ],
                                                                    "width": 4,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "size": "md",
                                                                    "currentWidth": 4
                                                                },
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "Chiều cao(cm)",
                                                                            "customClass": "pr-2",
                                                                            "validate": {
                                                                                "min": 1,
                                                                                "max": 400,
                                                                                required: true
                                                                            },
                                                                            "key": "CHIEUCAO",
                                                                            "type": "number",
                                                                        }
                                                                    ],
                                                                    "width": 4,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "size": "md",
                                                                    "currentWidth": 4
                                                                },
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "BMI",
                                                                            "key": "BMI",
                                                                            others: {
                                                                                "disabled": true,
                                                                                "attributes": {
                                                                                    "readonly": "true"
                                                                                },
                                                                            },
                                                                            "type": "number",
                                                                        }
                                                                    ],
                                                                    "size": "md",
                                                                    "width": 4,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "currentWidth": 4
                                                                }

                                                            ],
                                                            "customClass": "ml-0 mr-0",
                                                            "key": "chisocothe3",
                                                            "type": "columns",
                                                        },

                                                    ]
                                                }
                                            ],
                                            "customClass": "hsba-tabs-wrap pl-3",
                                            "key": "tabs",
                                            "type": "tabs",
                                        },
                                    ],
                                    "width": 4, "size": "md", "customClass": "pl-2", "currentWidth": 4
                                },
                            ],
                            "customClass": "ml-0 mr-0",
                            "key": "kb-column",
                            "type": "columns",
                            "input": false,
                            "tableView": false
                        },
                    ]
                },
                {
                    "collapsible": true,
                    "key": "p-ketluan",
                    "type": "panel",
                    "label": "Kết luận",
                    "title": "KẾT LUẬN",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap",
                    "components": [
                        {
                            "label": "Columns",
                            "columns": [
                                {
                                    "components": [
                                        {
                                            "label": "Chẩn đoán: Tuổi thai",
                                            "customClass": "mr-2 formio-css-suffix",
                                            "key": "TUOITHAI",
                                            "type": "number",
                                            others: {
                                                "suffix": "tuần",
                                            },
                                        }
                                    ],
                                    "width": 3, "size": "md", "currentWidth": 3
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Phương pháp phá thai",
                                            "customClass": "",
                                            "key": "KLPPPHATHAI",
                                            "type": "textarea",
                                            "validate": {
                                                "maxLength": 3000,
                                                "required": true
                                            }
                                        }
                                    ],
                                    "width": 9, "size": "md", "currentWidth": 9
                                },
                            ],
                            "key": "columns",
                            "type": "columns",
                            "customClass": "ml-0 mr-0 formio-css-baseline"
                        },
                    ]
                },
                getObjectThoigianBacsilambenhanFormio()
            ])
        },
        initObjectFormPage3: function() {
            let objTongKet = getObjectThoigianTongketFormio();
            objTongKet.components[0].columns[0].components.push({
                "label": "Lãnh đạo đơn vị",
                "key": "LANHDAODONVI",
                "type": "select",
                options: [],
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 35,
                    "data": {
                        "values": singletonObject.danhsachtatcanhanvien
                    },
                },
                validate: {
                    required: true,
                }
            });
            return getJSONObjectForm([
                {
                    "collapsible": true,
                    "key": "p-tongketdieutri",
                    "type": "panel",
                    "label": "TỔNG KẾT BỆNH ÁN",
                    "title": "TỔNG KẾT BỆNH ÁN",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap",
                    components: [
                        {
                            "label": "Columns",
                            "columns": [
                                {
                                    "components": [
                                        {
                                            "label": "1. Chẩn đoán: Thai",
                                            "customClass": "mr-2 formio-css-suffix",
                                            "key": "THAI",
                                            "type": "number",
                                            others: {
                                                "suffix": "tuần",
                                            },
                                        }
                                    ],
                                    "width": 3, "size": "md", "currentWidth": 3
                                },
                                {
                                    "components": [
                                        {
                                            "label": "2. Phương pháp phá thai",
                                            "customClass": "mr-2",
                                            "key": "TKPPPHATHAI",
                                            "type": "select",
                                            others: {
                                                "data": {
                                                    "values": [
                                                        {
                                                            "label": "Phương pháp hút chân không",
                                                            "value": "1",
                                                        },
                                                        {
                                                            "label": "Bằng thuốc",
                                                            "value": "2",
                                                        },
                                                        {
                                                            "label": "Nong và gắp",
                                                            "value": "3",
                                                        },
                                                        {
                                                            "label": "P. pháp Khác",
                                                            "value": "4",
                                                        },
                                                    ]
                                                },
                                            }
                                        }
                                    ],
                                    "width": 3, "size": "md", "currentWidth": 3
                                },
                                {
                                    "components": [
                                        {
                                            "label": "3. Tình trạng sau khi thực hiện",
                                            "customClass": "mr-2",
                                            "key": "TTSAUKHIPHA",
                                            "type": "select",
                                            others: {
                                                "data": {
                                                    "values": [
                                                        {
                                                            "label": "Tốt",
                                                            "value": "1",
                                                        },
                                                        {
                                                            "label": "Chưa tốt",
                                                            "value": "2",
                                                        },
                                                    ]
                                                },
                                            }
                                        }
                                    ],
                                    "width": 3, "size": "md", "currentWidth": 3
                                },
                                {
                                    "components": [
                                        {
                                            "label": "4. Tai biến",
                                            "customClass": "",
                                            "key": "TAIBIEN",
                                            "type": "select",
                                            others: {
                                                "data": {
                                                    "values": [
                                                        {
                                                            "label": "Không",
                                                            "value": "1",
                                                        },
                                                        {
                                                            "label": "Có",
                                                            "value": "2",
                                                        },
                                                    ]
                                                },
                                            }
                                        }
                                    ],
                                    "width": 3, "size": "md", "currentWidth": 3
                                },
                            ],
                            "key": "columns",
                            "type": "columns",
                            "customClass": "ml-0 mr-0 formio-css-baseline"
                        },
                        {
                            "label": "Columns",
                            "columns": [
                                {
                                    "components": [
                                        {
                                            "label": "5. Thời gian ở cơ sở thực hiện kỹ thuật phá thai: tổng số",
                                            "customClass": "mr-2 formio-css-suffix",
                                            "key": "TSTHOIGIANOCOSOTH",
                                            "type": "number",
                                            others: {
                                                "suffix": "giờ",
                                            },
                                        }
                                    ],
                                    "width": 3, "size": "md", "currentWidth": 3
                                },
                                {
                                    "components": [
                                        {
                                            "label": "6. Ra về/ Nhập viện điều trị/ Chuyển tuyến",
                                            "customClass": "mr-2",
                                            "key": "RAVENHAPVIENCHTUYEN",
                                            "type": "select",
                                            others: {
                                                "data": {
                                                    "values": [
                                                        {
                                                            "label": "Ra về",
                                                            "value": "1",
                                                        },
                                                        {
                                                            "label": "Nhập viện",
                                                            "value": "2",
                                                        },
                                                        {
                                                            "label": "Chuyển tuyến",
                                                            "value": "3",
                                                        },
                                                    ]
                                                },
                                            }
                                        }
                                    ],
                                    "width": 3, "size": "md", "currentWidth": 3
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Chuyển tuyến lúc",
                                            "customClass": "mr-2",
                                            "key": "CHUYENTUYENLUC",
                                            "type": "datetime",
                                            "format": "dd/MM/yyyy HH:mm",
                                            "enableTime": true,
                                            others: {
                                                "timePicker": {
                                                    "showMeridian": false
                                                },
                                                "widget": {
                                                    "enableTime": true,
                                                    "format": "dd/MM/yyyy HH:mm",
                                                    "time_24hr": true
                                                }
                                            }
                                        }
                                    ],
                                    "width": 3, "size": "md", "currentWidth": 3
                                },
                                {
                                    "components": [

                                    ],
                                    "width": 3, "size": "md", "currentWidth": 3
                                },
                            ],
                            "key": "columns",
                            "type": "columns",
                            "customClass": "ml-0 mr-0 formio-css-baseline"
                        },
                        {
                            "label": "Columns",
                            "columns": [
                                {
                                    "components": [
                                        {
                                            "label": "Lý do nhập viện",
                                            "customClass": "mr-2",
                                            "key": "LYDONHAPVIEN",
                                            "type": "textfield",
                                        }
                                    ],
                                    "width": 6, "size": "md", "currentWidth": 6
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Lý do chuyển tuyến",
                                            "customClass": "",
                                            "key": "LYDOCHUYENTUYEN",
                                            "type": "textfield",
                                        }
                                    ],
                                    "width": 6, "size": "md", "currentWidth": 6
                                },
                            ],
                            "key": "columns",
                            "type": "columns",
                            "customClass": "ml-0 mr-0 formio-css-baseline"
                        },
                        {
                            "label": "7. Biện pháp tránh thai sau phá thai",
                            "customClass": "",
                            "key": "BIENPHAPTRANHSAUKHIPHA",
                            "type": "select",
                            others: {
                                "data": {
                                    "values": [
                                        {
                                            "label": "DCTC",
                                            "value": "1",
                                        },
                                        {
                                            "label": "Thuốc tiêm",
                                            "value": "2",
                                        },
                                        {
                                            "label": "Thuốc uống",
                                            "value": "3",
                                        },
                                        {
                                            "label": "Que cấy",
                                            "value": "4",
                                        },
                                        {
                                            "label": "Bao cao su",
                                            "value": "5",
                                        },
                                        {
                                            "label": "Thuốc tránh thai khẩn cấp",
                                            "value": "6",
                                        },
                                        {
                                            "label": "Biện pháp khác",
                                            "value": "7",
                                        },
                                        {
                                            "label": "Không sử dụng biện pháp nào",
                                            "value": "8",
                                        }
                                    ]
                                },
                            }
                        },
                        {
                            "label": "8. Khám lại bất thường",
                            "customClass": "",
                            "key": "KLBATTHUONG",
                            "type": "textarea",
                            "validate": {
                                "maxLength": 3000,
                            }
                        },
                        {
                            "label": "9. Khám lại theo hẹn",
                            "customClass": "",
                            "key": "KLTHEOHEN",
                            "type": "textarea",
                            "validate": {
                                "maxLength": 3000,
                            }
                        },
                        {
                            "label": "10. Kết luận",
                            "customClass": "",
                            "key": "KETLUAN",
                            "type": "textarea",
                            "validate": {
                                "maxLength": 3000,
                                "required": true
                            }
                        },
                    ]
                },
                objTongKet
            ]);
        },
        callbackAfterLoad: function (instance, callBack) {
            form = instance;
            let bacsilambenhanElement = form.getComponent('MABACSILAMBENHAN');
            let bmiElement = form.getComponent('BMI');
            let cannangElement = form.getComponent('CANNANG');
            let chieucaoElement = form.getComponent('CHIEUCAO');

            $("#"+getIdElmentFormio(form,'MAKHOA')).change(function() {
                if(!$(this).val()) {
                    return;
                }
                getBacsiByKhoaFormio($(this).val(), bacsilambenhanElement)
            })

            $("#"+getIdElmentFormio(form,'CANNANG')).change(function() {
                if(!$(this).val() || !chieucaoElement.getValue()) {
                    return;
                }
                bmiElement.setValue((chieucaoElement.getValue()/Math.pow($(this).val()/100, 2)).toFixed(2))
            })

            $("#"+getIdElmentFormio(form,'CHIEUCAO')).change(function() {
                if(!$(this).val() || !cannangElement.getValue()) {
                    return;
                }
                bmiElement.setValue((cannangElement.getValue()/Math.pow($(this).val()/100, 2)).toFixed(2))
            })

            let idWrap = "hsba_vba_trang2-tab";
            showLoaderIntoWrapId(idWrap)
            getThongtinBenhan(thongtinhsba.thongtinbn.VOBENHAN[0].ID, loaiBA, function(dataTrang2) {
                hideLoaderIntoWrapId(idWrap)
                delete dataTrang2.ID;
                let formData = JSON.parse(dataTrang2.THONGTIN_PHATHAI);
                getBacsiByKhoaFormio(dataTrang2.MAKHOA, bacsilambenhanElement);
                let dataCallBack = form.submission =  {
                    data: {
                        ...formData
                    }
                };
                callBack && callBack(dataCallBack);
            }, function() {
                hideLoaderIntoWrapId(idWrap)
                notifiToClient("Red", "Lỗi load thông tin bệnh án")
            });
        },
        save: function(element, callBackSave, callBackLog) {
            let idButton = element.id;
            // form.emit("checkValidity");
            // console.log(form.submission.data)
            // if (!form.checkValidity(null, false, null, true)) {
            //     hideSelfLoading(idButton);
            //     return;
            // }
            let dataSubmit = form.submission.data;
            dataSubmit.ID = thongtinhsba.thongtinbn.VOBENHAN[0].ID;
            let ngayba = moment(dataSubmit.NGAYBSLAMBENHAN)
            dataSubmit.NGAYLAMBENHAN = "Ngày " + ngayba.format("DD") + " tháng " + ngayba.format("MM") + " năm " + ngayba.format("YYYY");
            dataSubmit.BACSILAMBENHAN = getTextSelectedFormio(form.getComponent('MABACSILAMBENHAN'));
            dataSubmit.MAKHOA = getTextSelectedFormio(form.getComponent('MAKHOA')).split(" - ")[0];

            $.post("cmu_post_CMU_VBA_PHATHAI_UPD", {
                url: [
                    dataSubmit.ID,
                    singletonObject.dvtt,
                    JSON.stringify(dataSubmit),
                ].join("```")
            }).done(function (data) {
                if (data > 0) {
                    !callBackSave && notifiToClient("Green", MESSAGEAJAX.EDIT_SUCCESS);
                    updateNgaylamVaBSHSBA({
                        ...dataSubmit,
                        NGAYBA: ngayba.format("DD/MM/YYYY"),
                    }, function () {
                        callBackSave && callBackSave({keyword: "Người làm bệnh án"});
                        let dsBacSi = singletonObject.danhsachnhanvien;
                        let ngaybaOld = moment(dataOld.NGAYBSLAMBENHAN);
                        dataOld.BACSILAMBENHAN = getTenBacSiLog(dsBacSi, dataOld.MABACSILAMBENHAN);
                        dataOld.GIAMDOCBENHVIEN = getTenBacSiLog(dsBacSi, dataOld.GIAMDOCBENHVIEN);
                        dataOld.NGAYLAMBENHAN = "Ngày " + ngaybaOld.format("DD") +
                                                " tháng " + ngaybaOld.format("MM") +
                                                " năm " + ngaybaOld.format("YYYY");
                        dataSubmit.BACSILAMBENHAN = getTextSelectedFormio(form.getComponent('MABACSILAMBENHAN')).split(" - ")[1];
                        callBackLog && callBackLog(dataSubmit);
                    });
                    !callBackSave && hideSelfLoading(idButton);
                }
                else {
                    notifiToClient('Red', MESSAGEAJAX.ERROR);
                }
            }).fail(function () {
                notifiToClient("Red", MESSAGEAJAX.FAIL)
            }).always(function () {
                hideSelfLoading(idButton);
            });
        },
        callbackAfterLoadTongket: function (instance, callBack) {
            formTongket = instance;
            let bacsiketthucBAElement = formTongket.getComponent('MABACSIDIEUTRI');

            let idWrap = "hsba_vba_trang3-tab";
            $("#"+getIdElmentFormio(formTongket,'MAKHOA_KETHUC')).change(function() {
                if(!$(this).val()) {
                    return;
                }
                getBacsiByKhoaFormio($(this).val(), bacsiketthucBAElement)
            })

            showLoaderIntoWrapId(idWrap)
            getThongtinTongket(thongtinhsba.thongtinbn.VOBENHAN[0].ID, loaiBA, function(dataTrang3) {
                hideLoaderIntoWrapId(idWrap)
                dataTrang3.MAKHOA_KETHUC = dataTrang3.MAKHOA_KETHUC? dataTrang3.MAKHOA_KETHUC: singletonObject.makhoa;

                let danhsachkhoa;
                formTongket.getComponent('MAKHOA_KETHUC', function(component) {
                    danhsachkhoa = component.component.data.values;
                });
                let formData = JSON.parse(dataTrang3.BENHAN_PHATHAI);
                let dataCallBack = formTongket.submission =  {
                    data: {
                        ...formData
                    }
                };
                callBack && callBack(dataCallBack);
                getBacsiByKhoaFormio(dataTrang3.MAKHOA_KETHUC, bacsiketthucBAElement);

            }, function() {
                hideLoaderIntoWrapId(idWrap)
                notifiToClient("Red", "Lỗi load thông tin bệnh án")
            });
        },
        saveTongket: function(element, callBackSave, callBackLog) {
            let idButton = element.id;
            formTongket.emit("checkValidity");
            if (!formTongket.checkValidity(null, false, null, true)) {
                hideSelfLoading(idButton);
                return;
            }
            let dataSubmit = formTongket.submission.data;
            dataSubmit.id = thongtinhsba.thongtinbn.VOBENHAN[0].ID;
            let ngayba = moment(dataSubmit.NGAY_TONGKET)
            dataSubmit.ngayTongKet =  ngayba.format("DD/MM/YYYY HH:mm");
            dataSubmit.bacSiDieuTri =  getTextSelectedFormio(formTongket.getComponent('MABACSIDIEUTRI')).split(" - ")[1];
            dataSubmit.nguoiGiaoHoSo = getTextSelectedFormio(formTongket.getComponent('MANHANVIEN_GIAOHOSO'));
            dataSubmit.nguoiNhanHoSo = getTextSelectedFormio(formTongket.getComponent('MANHANVIEN_NHANHOSO'));

            $.post("cmu_post_CMU_VBA_PHATHAI_TONGKET_UPD", {
                url: [
                    thongtinhsba.thongtinbn.VOBENHAN[0].ID,
                    singletonObject.dvtt,
                    JSON.stringify(dataSubmit),
                ].join("```")
            }).done(function (data) {
                if (data > 0) {
                    !callBackSave && notifiToClient("Green", MESSAGEAJAX.EDIT_SUCCESS)
                    updateThongtinPage3(dataSubmit, function () {
                        callBackSave && callBackSave({keyword: "Bác sĩ điều trị"});
                        dataSubmit.NGUOIGIAO_HOSO = dataSubmit.nguoiGiaoHoSo;
                        dataSubmit.NGUOINHAN_HOSO = dataSubmit.nguoiNhanHoSo;
                        dataSubmit.KHOA_KETHUC = getTextSelectedFormio(formTongket.getComponent('MAKHOA_KETHUC'));
                        callBackLog && callBackLog(dataSubmit);
                    });
                    hideSelfLoading(idButton);
                }
                else {
                    notifiToClient('Red', MESSAGEAJAX.ERROR);
                }
            }).fail(function () {
                notifiToClient("Red", MESSAGEAJAX.FAIL)
            }).always(function () {
                hideSelfLoading(idButton);
            });
        },
        saveThongtinHC: function(element) {
            var idButton = element.id;
            showSelfLoading(idButton);
            var dataSubmit = convertDataFormToJson("formHsbatthcqlnb");
            updateQuanlynbvaChandoan(dataSubmit, function() {
                hideSelfLoading(idButton);
                notifiToClient("Green", "Lưu thành công")
            }, function() {
                hideSelfLoading(idButton);
                notifiToClient("Red", "Lỗi lưu thông tin")
            });
        },
        loadThongtinPage1: function() {
            var idWrap = "hsba_vba_trang1-tab";
            showLoaderIntoWrapId(idWrap)
            getThongtinPage1Benhan(thongtinhsba.thongtinbn.VOBENHAN[0].ID, function(_) {

                hideLoaderIntoWrapId(idWrap)
            }, function() {
                hideLoaderIntoWrapId(idWrap)
                notifiToClient("Red", "Lỗi load thông tin")
            });
        },
        copyChidinhCLS: function(cls) {
            console.log("form.submission", form.submission)
            form.submission = {
                data: {
                    ...form.submission.data,
                    CLS: cls
                }
            }
        },

        getInfoMauHSBA: function() {
            this.extendFunctionMau();
            return {
                keyMauHSBA: keyMauHSBANGOAITRUPHCN,
                insertMau: "insertMauHSBANGOAITRUPHCN",
                editMau: "editMauHSBANGOAITRUPHCN",
                selectMau: "selectMauHSBANGOAITRUPHCN",
                getdataMau: "getdataMauHSBANGOAITRUPHCN",
                formioValidate: "formioHSBANGOAITRUPHCNValidate",
            };
        },
        extendFunctionMau: function() {
            var self = this
            $.extend({
                insertMauHSBANGOAITRUPHCN: function () {
                    self.generateFormMauHSBA({})
                },
                editMauHSBANGOAITRUPHCN: function (rowSelect) {
                    var json = JSON.parse(rowSelect.NOIDUNG);
                    var dataMau = {}
                    json.forEach(function(item) {
                        dataMau[item.key] = item.value
                    })

                    self.generateFormMauHSBA({
                        ID: rowSelect.ID,
                        TENMAU: rowSelect.TENMAU,
                        ...dataMau
                    })
                },
                selectMauHSBANGOAITRUPHCN: function (rowSelect) {
                    var json = JSON.parse(rowSelect.NOIDUNG);
                    var dataMau = {
                        ...form.submission.data,
                    }
                    json.forEach(function(item) {
                        dataMau[item.key] = item.value
                    })
                    form.submission = {
                        data: {
                            ...dataMau
                        }
                    }
                    $("#modalMauChungJSON").modal("hide");
                },
                getdataMauHSBANGOAITRUPHCN: function () {
                    var objectNoidung = [];
                    self.getObjectMauHSBA().forEach(function(item) {
                        if(item.key != 'ID' && item.key != 'TENMAU') {
                            objectNoidung.push({
                                "label": item.label,
                                "value": formioMauHSBA.submission.data[item.key],
                                "key": item.key,
                            })
                        }
                    })
                    return {
                        ID: formioMauHSBA.submission.data.ID,
                        TENMAU: formioMauHSBA.submission.data.TENMAU,
                        NOIDUNG: JSON.stringify(objectNoidung),
                        KEYMAUCHUNG: keyMauHSBANGOAITRUPHCN
                    };
                },
                formioHSBANGOAITRUPHCNValidate: function() {
                    formioMauHSBA.emit("checkValidity");
                    if (!formioMauHSBA.checkValidity(null, false, null, true)) {
                        return false;
                    }
                    return true;
                }
            })
        },
        generateFormMauHSBA: function(dataForm) {
            var self = this;
            var jsonForm = getJSONObjectForm(self.getObjectMauHSBA());
            Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
                jsonForm,{}
            ).then(function(form) {
                formioMauHSBA = form;
                formioMauHSBA.submission = {
                    data: {
                        ...dataForm
                    }
                }
            });
        },
        getObjectMauHSBA: function() {
            return getObjectMauHSBAPAGE2_NTC();
        },

        getInfoMauHSBATongket: function() {
            this.extendFunctionMauTongket();
            return {
                keyMauHSBA: keyMauHSBANGOAITRUPHCNTongket,
                insertMau: "insertMauHSBANGOAITRUPHCNTongket",
                editMau: "editMauHSBANGOAITRUPHCNTongket",
                selectMau: "selectMauHSBANGOAITRUPHCNTongket",
                getdataMau: "getdataMauHSBANGOAITRUPHCNTongket",
                formioValidate: "formioHSBANGOAITRUPHCNTongketValidate",
            };
        },
        extendFunctionMauTongket: function() {
            var self = this
            $.extend({
                insertMauHSBANGOAITRUPHCNTongket: function () {
                    self.generateFormMauHSBATongket({})
                },
                editMauHSBANGOAITRUPHCNTongket: function (rowSelect) {
                    var json = JSON.parse(rowSelect.NOIDUNG);
                    var dataMau = {}
                    json.forEach(function(item) {
                        dataMau[item.key] = item.value
                    })

                    self.generateFormMauHSBATongket({
                        ID: rowSelect.ID,
                        TENMAU: rowSelect.TENMAU,
                        ...dataMau
                    })
                },
                selectMauHSBANGOAITRUPHCNTongket: function (rowSelect) {
                    var json = JSON.parse(rowSelect.NOIDUNG);
                    var dataMau = {
                        ...formTongket.submission.data,
                    }
                    json.forEach(function(item) {
                        dataMau[item.key] = item.value
                    })
                    formTongket.submission = {
                        data: {
                            ...dataMau
                        }
                    }
                    $("#modalMauChungJSON").modal("hide");
                },
                getdataMauHSBANGOAITRUPHCNTongket: function () {
                    var objectNoidung = [];
                    self.getObjectMauHSBATongket().forEach(function(item) {
                        if(item.key != 'ID' && item.key != 'TENMAU') {
                            objectNoidung.push({
                                "label": item.label,
                                "value": formioMauHSBATongket.submission.data[item.key],
                                "key": item.key,
                            })
                        }
                    })
                    return {
                        ID: formioMauHSBATongket.submission.data.ID,
                        TENMAU: formioMauHSBATongket.submission.data.TENMAU,
                        NOIDUNG: JSON.stringify(objectNoidung),
                        KEYMAUCHUNG: keyMauHSBANGOAITRUPHCNTongket
                    };
                },
                formioHSBANGOAITRUPHCNTongketValidate: function() {
                    formioMauHSBATongket.emit("checkValidity");
                    if (!formioMauHSBATongket.checkValidity(null, false, null, true)) {
                        return false;
                    }
                    return true;
                }
            })
        },
        generateFormMauHSBATongket: function(dataForm) {
            var self = this;
            var jsonForm = getJSONObjectForm(self.getObjectMauHSBATongket());
            Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
                jsonForm,{}
            ).then(function(form) {
                formioMauHSBATongket = form;
                formioMauHSBATongket.submission = {
                    data: {
                        ...dataForm
                    }
                }
            });
        },
        getObjectMauHSBATongket: function() {
            return getObjectMauTongket();
        }
    }
}