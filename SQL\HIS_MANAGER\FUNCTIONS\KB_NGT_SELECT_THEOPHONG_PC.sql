create or replace FUNCTION "HIS_MANAGER"."KB_NGT_SELECT_THEOPHONG_PC"(p_ma_phong_benh IN NUMBER,
                                                                    p_trang_thai    IN NUMBER,
                                                                    p_dvtt          IN VARCHAR2,
                                                                    p_dskhamngoailoc67 in number DEFAULT  0,
                                                                    p_mi<PERSON><PERSON>_bob<PERSON><PERSON><PERSON> in number DEFAULT  0)
  RETURN SYS_REFCURSOR IS
  v_ngayhientai        DATE;
  v_thanghientai       NUMBER(10);
  v_namhientai         NUMBER(10);
  v_batbuocdongtien    NUMBER(10) DEFAULT 0;
  cur SYS_REFCURSOR;
  v_chuyephong_kobh_30 VARCHAR2(10); -- AGG Quí 07/09/2017 điều chỉnh DS BN theo phòng có tham số, xét thêm điều kiện BN chuyển phòng chưa đóng tiền
  v_bhyt_traituyen_vankham VARCHAR2(10) := his_fw.cmu_tsdv(p_dvtt, '96126', '0');
	v_thamso960516 VARCHAR2(10) := his_fw.cmu_tsdv(p_dvtt, '960516', '0');
    v_thamso_960558 varchar2(10) := his_fw.cmu_tsdv(p_dvtt, '960558', '0');
  v_goisohinhanh number(1);
  v_ts_capcuukham VARCHAR2(10); -- Hoành STG cho phép bệnh nhân cấp cứu khám khi chưa đóng tiền công khám 30/06/2018
  v_thamso_ds_bant_mantinh number(1) default 0;
BEGIN
    v_thamso_ds_bant_mantinh := to_number(his_manager.get_tsdv(p_dvtt, 9401022, '0'));
    v_ngayhientai := trunc(sysdate);
    v_thanghientai := extract(MONTH FROM v_ngayhientai);
    v_namhientai := extract(YEAR FROM v_ngayhientai);

    /*select \*+ RESULT_SET *\ mota_thamso into v_batbuocdongtien
    from his_fw.dm_thamso_donvi
    where dvtt = p_dvtt and ma_thamso = 70;*/
    -- AGG Quí 07/09/2017
BEGIN
SELECT mota_thamso
INTO v_chuyephong_kobh_30
FROM his_fw.dm_thamso_donvi
WHERE ma_thamso = 89194
  AND dvtt = p_dvtt;
EXCEPTION
      WHEN no_data_found THEN
      v_chuyephong_kobh_30 := '0';
END;
BEGIN
SELECT mota_thamso
INTO v_goisohinhanh
FROM his_fw.dm_thamso_donvi
WHERE ma_thamso = 91100
  AND dvtt = p_dvtt;
EXCEPTION
      WHEN no_data_found THEN
      v_goisohinhanh := '0';
END;
    -- AGG Quí 07/09/2017

BEGIN
SELECT mota_thamso
INTO v_batbuocdongtien
FROM his_fw.dm_thamso_donvi
WHERE dvtt = p_dvtt
  AND ma_thamso = 70;
EXCEPTION
      WHEN no_data_found THEN
      v_batbuocdongtien := 0;
END;

    -- Hoành STG 30/06/2018
BEGIN
SELECT mota_thamso
INTO v_ts_capcuukham
FROM his_fw.dm_thamso_donvi
WHERE ma_thamso = 94434
  AND dvtt = p_dvtt;
EXCEPTION
      WHEN OTHERS THEN
        v_ts_capcuukham := '0';
END;
    -- Hoành STG 30/06/2018

    IF v_chuyephong_kobh_30 = '1' AND v_thamso960516 = '0'
    THEN
      OPEN cur FOR
SELECT
    tn.ID_TIEPNHAN,
    tn.DVTT,
    tn.STT_HANGNGAY,
    tn.MA_BENH_NHAN,
    tn.MA_DV,
    tn.THOI_GIAN_TIEP_NHAN,
    tn.TI_LE_MIEN_GIAM,
    tn.TRANG_THAI,
    tn.CO_BAO_HIEM,
    to_char(bn.ngay_sinh, 'dd/mm/yyyy')         AS NGAY_SINH,
    tn.TUOI,
    tn.THANG
    --,case when tn.THANG='0' then hienthi_tuoi_benhnhan(bn.NGAY_SINH) else '0' end as TUOI,
    --case when tn.TUOI='0' then hienthi_tuoi_benhnhan(bn.NGAY_SINH) else '0' end as THANG
        ,
    decode(tn.TUOI + tn.THANG,
           0,
           trunc(tn.THOI_GIAN_TIEP_NHAN) - bn.ngay_sinh,
           0)                                   AS NGAY,
    -- Sang STG - 11/10/2017: Them ngay tuoi TE
    decode(tn.UU_TIEN, 1, 'true', 'false')      AS UU_TIEN,
    tn.CAPCUU,
    decode(tn.KSK, 1, 'true', 'false')          AS KSK,
    tn.DUNG_TUYEN,
    tn.THANG,
    tn.TIEPNHAN_QUADAI108,
    tn.TT_CONG_KHAM,
    tn.THANH_TOAN,
    tn.SO_THE_BHYT,
    tn.NGAY_BATDAU,
    tn.NGAY_HETHAN,
    tn.THANH_TOAN_YC,
    DOUUTIEN,
    decode(tn.CANHBAO, 1, 'true', 'false')      AS CANHBAO,
    dt.TEN_DOI_TUONG_BHYT,
    bn.TEN_BENH_NHAN,
    bn.TEN_BENH_NHAN                            AS TEN_BENH_NHAN_HT,
    bn.DIA_CHI,
    bn.so_dien_thoai                  AS so_dien_thoai,
    decode(bn.GIOI_TINH, 1, 'true', 'false')    AS GIOI_TINH,
    tnp.SO_TIEP_NHAN_PB,
    tnp.MA_PHONG_BENH,
    NVL2(BANT.ketthuc_benhan,1,tn.BA_NGOAITRU) BA_NGOAITRU,
    tn.tt_dungtuyen,
    tn.bhyt_noitinh,
    tn.sovaovien,
    AGG_NGT_CHECK_CHUYENPHONG_FF(tn.MA_BENH_NHAN,
                                 tn.sovaovien,
                                 tn.DVTT)        AS TT_CHUYEN_PHONG,
    CASE
        WHEN substr(p_dvtt, 1, 2) = '89'
            THEN
            0
        ELSE
            ptt.ketoanxacnhan
        END                                         AS ketoanxacnhan,
    ptt.da_xuat_thuoc_vt
    --Begin AGG Khang 5/5/2017 select them thong tin
        ,
    bn.te_giaykhaisinh,
    bn.te_giaychungsinh,
    bn.te_giaytokhac
    --End AGG Khang 5/5/2017 select them thong tin
    -- Begin AGG - Tâm 0918197999 31/05/2017: lấy thông tin Dịch vụ khám để hiện trên app Gọi số phòng khám
        ,
    dv.ten_dv
    -- End AGG - Tâm 0918197999 31/05/2017: lấy thông tin Dịch vụ khám để hiện trên app Gọi số phòng khám
        ,
    CASE
        WHEN (nvl(ptt.phongketoan_inbangke, 0) = 1 AND
              nvl(ptt.khoa_thanhtoan, 0) = 1)
            THEN
            1
        ELSE
            0
        END                                         AS khoa_thanhtoan,
    to_char(TN.ngay_mien_cung_ct, 'dd/mm/yyyy') AS ngay_mien_cung_ct,
    tn.NOIDANGKY_KCB -- CMU VNPTHIS-4879
    --begin HGI Ngân 24/4/2018 thêm trạng thái phiếu xét nghiệm
        , case when exists (select SO_PHIEU_XN from HIS_MANAGER.KB_CD_XET_NGHIEM xn
                            where xn.dvtt=tn.dvtt and xn.MA_KHAM_BENH = concat('kb_',tn.id_tiepnhan) and xn.MABENHNHAN = tn.MA_BENH_NHAN
                              and xn.TRANG_THAI_XET_NGHIEM=1 and xn.DAIN=1) then 1 else 0 end TT_PXN
    --end HGI Ngân 24/4/2018 thêm trạng thái phiếu xét nghiệm
        ,to_char( tnp.thoi_gian_kham_benh, 'HH24:MI' ) as tg_khambenh,
    to_char( tn.thoi_gian_tiep_nhan, 'HH24:MI' ) as tg_tiepnhan,
    to_char(nvl(tn.ngay_batdau,''),'dd/mm/yyyy') as ngay_bd_bh,
    to_char(nvl(tn.ngay_hethan,''),'dd/mm/yyyy') as ngay_hh_bh,
    to_char(nvl(tn.kb_thoidiem_5nam_lientuc,''),'dd/mm/yyyy') as ngay_5nam,
    decode(v_goisohinhanh,3,NGT_LAYSTT_HINHANH(tn.dvtt,tn.id_tiepnhan),0) as HINHANHTN
        , nvl(NOIGIOITHIEU,'') NOIGIOITHIEU
        , nvl(TENNOIGIOITHIEU,'') TENNOIGIOITHIEU,nvl(bn.hien_namsinh,0) as chinamsinh
        ,dv.KYHIEUDV, tn.khambenh_online,
    tn.cuocgoi_manguoinhan,nvl(tn.BN_DATLICH_KGG,0) as BN_DATLICH_KGG
        , nvl(tn.tt_datlich_kgg,tnp.SO_TIEP_NHAN_PB) as TT_DATLICH_KGG,
    tnp.TRANG_THAI_BN
FROM kb_tiep_nhan tn
         LEFT JOIN kb_phieuthanhtoan ptt
                   ON tn.dvtt = ptt.dvtt
                       AND tn.id_tiepnhan = ptt.id_tiepnhan
                       AND tn.sovaovien = ptt.sovaovien
         LEFT JOIN kb_chuyenPK_thutien_tt37 tt37
                   ON tt37.sovaovien = tn.sovaovien
                       AND tt37.dvtt = tn.dvtt
                       AND tt37.ma_phong_benh = p_ma_phong_benh
         LEFT JOIN his_public_list.dm_doi_tuong_bhyt dt
                   ON tn.BAKYTUDAU = dt.MA_DOI_TUONG_BHYT
         LEFT JOIN  HIS_MANAGER.vienphingoaitru_lantamung lantt
                    on lantt.sovaovien=tn.sovaovien and lantt.TT_LANTT=1 and tn.CO_BAO_HIEM=0
         left join HIS_MANAGER.NGOAITRU_BENHAN_TONGHOP BANT ON
    BANT.DVTT = tn.dvtt and BANT.MABENHNHAN = tn.MA_BENH_NHAN and bant.KETTHUC_BENHAN = 0
        , dm_dich_vu_kham dv,
     tiep_nhan_phong_benh tnp, his_public_list.dm_benh_nhan bn
WHERE tn.NGAY_TIEP_NHAN = v_ngayhientai
  AND tn.THANG_HT = v_thanghientai
  AND tn.NAM = v_namhientai
  and 1=(case tn.CO_BAO_HIEM when 1 then 1 else case p_dskhamngoailoc67 when 1 then case  when lantt.sovaovien is not null then 1 else 0 end else 1 end end)
  AND tnp.NGAYTIEPNHAN = v_ngayhientai
  AND tnp.THANGTIEPNHAN = v_thanghientai
  AND tnp.NAMTIEPNHAN = v_namhientai
  AND tn.MA_DV = dv.MA_DV
  AND tn.DVTT = p_dvtt
  AND tnp.DVTT = p_dvtt
  AND dv.DVTT = p_dvtt
  AND tnp.MA_PHONG_BENH = p_ma_phong_benh
  AND tnp.TRANG_THAI_BN = p_trang_thai
  AND ((1=decode(p_mienphi_bobatdongtien,1,tn.MIENPHI,0) --and dv.TRANGTHAI = 1
           ) or (tn.TI_LE_MIEN_GIAM > 0 AND dv.KHAM_DV = 0 --AND dv.TRANGTHAI = 1
           ) OR tn.tien_cong_kham = 0 OR
       (v_bhyt_traituyen_vankham = '1' AND TN.SO_THE_BHYT IS NOT NULL) OR -- TRAI TUYẾN CÓ BH VẪN KHÁM
       (nvl(tt37.tt_thutienvienphi, 1) = 1 AND tn.THANH_TOAN = 1 AND
        nvl(tn.TI_LE_MIEN_GIAM, 0) = 0 --AND dv.TRANGTHAI = 1
           AND
        dv.KHAM_DV = 0) OR (tn.THANH_TOAN = 0 AND dv.GIA_DV = 0 AND
                            nvl(tn.TI_LE_MIEN_GIAM, 0) = 0 /*AND dv.TRANGTHAI = 1*/ AND dv.KHAM_DV = 0) OR
       (tn.TI_LE_MIEN_GIAM > 0 /*AND dv.TRANGTHAI = 1*/ AND
        dv.KHAM_DV = 1 AND tn.THANH_TOAN_YC = 1) OR
       (nvl(tn.TI_LE_MIEN_GIAM, 0) = 0 AND tn.thanh_toan = 1 AND
        dv.KHAM_DV = 1 AND tn.TT_CK_DV_KBH = 1 ) -- CMU NGUYEN THEM BN KBH KHAM DICH VU
    OR (tn.capcuu = 1 and v_ts_capcuukham = '1' and tn.tien_cong_kham != 0) -- Hoành STG 30/06/2018
    or (tn.thanh_toan = 0 and tnp.TRANG_THAI_BN = 3 and v_thamso_960558 = '1') -- Toan CMU
    )
  AND tn.ID_TIEPNHAN = tnp.ID_TIEPNHAN
  AND tn.MA_BENH_NHAN = bn.MA_BENH_NHAN
GROUP BY tn.ID_TIEPNHAN,
         tn.DVTT,
         tn.STT_HANGNGAY,
         tn.MA_BENH_NHAN,
         tn.MA_DV,
         tn.THOI_GIAN_TIEP_NHAN,
         tn.TI_LE_MIEN_GIAM,
         tn.TRANG_THAI,
         tn.CO_BAO_HIEM,
         bn.ngay_sinh,
         tn.TUOI,
         tn.THANG,
         tn.UU_TIEN,
         tn.CAPCUU,
         tn.KSK,
         tn.DUNG_TUYEN,
         tn.THANG,
         tn.TIEPNHAN_QUADAI108,
         tn.TT_CONG_KHAM,
         tn.THANH_TOAN,
         tn.SO_THE_BHYT,
         tn.NGAY_BATDAU,
         tn.NGAY_HETHAN,
         tn.THANH_TOAN_YC,
         DOUUTIEN,
         tn.CANHBAO,
         dt.TEN_DOI_TUONG_BHYT,
         bn.TEN_BENH_NHAN,
         bn.DIA_CHI,
         bn.so_dien_thoai,
         bn.GIOI_TINH,
         tnp.SO_TIEP_NHAN_PB,
         tnp.MA_PHONG_BENH,
         NVL2(BANT.ketthuc_benhan,1,tn.BA_NGOAITRU),
         tn.tt_dungtuyen,
         tn.bhyt_noitinh,
         tn.sovaovien,
         bn.NGAY_SINH,
         ptt.ketoanxacnhan,
         ptt.da_xuat_thuoc_vt
         --Begin AGG Khang 5/5/2017 select them thong tin
        ,
         bn.te_giaykhaisinh,
         bn.te_giaychungsinh,
         bn.te_giaytokhac
         --End AGG Khang 5/5/2017 select them thong tin
         -- Begin AGG - Tâm 0918197999 31/05/2017: lấy thông tin Dịch vụ khám để hiện trên app Gọi số phòng khám
        ,
         DV.ten_dv,
         ptt.phongketoan_inbangke,
         ptt.khoa_thanhtoan -- AGG Quí 17/06/2017 lấy thêm khóa thanh toán
         -- End AGG - Tâm 0918197999 31/05/2017: lấy thông tin Dịch vụ khám để hiện trên app Gọi số phòng khám
        ,
         TN.ngay_mien_cung_ct,
         tn.NOIDANGKY_KCB -- CMU VNPTHIS-4879
        ,tnp.thoi_gian_kham_benh
        ,  NOIGIOITHIEU
        ,  TENNOIGIOITHIEU,tn.kb_thoidiem_5nam_lientuc,bn.hien_namsinh,dv.KYHIEUDV, tn.khambenh_online,
         tn.cuocgoi_manguoinhan, tn.tt_datlich_kgg
        ,TN.BN_DATLICH_KGG,
         tnp.TRANG_THAI_BN
ORDER BY tn.UU_TIEN DESC, tnp.SO_TIEP_NHAN_PB ASC;
ELSE
      IF v_batbuocdongtien = 0
      THEN
    --HGI Ngân 09/09/2018 thêm danh sách bệnh nhân BANT mãn tính


    if p_trang_thai = 8 and v_thamso_ds_bant_mantinh = 1 then
      OPEN cur FOR
SELECT
    '' ID_TIEPNHAN,
    p_dvtt DVTT,
    0 STT_HANGNGAY,
    '' MA_BENH_NHAN,
    0 MA_DV,
    '' THOI_GIAN_TIEP_NHAN,
    0 TI_LE_MIEN_GIAM,
    0 TRANG_THAI,
    0 CO_BAO_HIEM,
    to_char(bn.ngay_sinh, 'dd/mm/yyyy')         AS NGAY_SINH,
    0 as TUOI,
    0 as THANG
    --,case when tn.THANG='0' then hienthi_tuoi_benhnhan(bn.NGAY_SINH) else '0' end as TUOI,
    --case when tn.TUOI='0' then hienthi_tuoi_benhnhan(bn.NGAY_SINH) else '0' end as THANG
        , 0 AS NGAY,
    -- Sang STG - 11/10/2017: Them ngay tuoi TE
    0   AS UU_TIEN,
    0 as CAPCUU,
    'false'        AS KSK,
    1 as DUNG_TUYEN,
    0 as THANG,
    0 TIEPNHAN_QUADAI108,
    0 TT_CONG_KHAM,
    0 THANH_TOAN,
    '' SO_THE_BHYT,
    '' NGAY_BATDAU,
    '' NGAY_HETHAN,
    0 THANH_TOAN_YC,
    0 DOUUTIEN,
    'false'    AS CANHBAO,
    '' TEN_DOI_TUONG_BHYT,
    bn.TEN_BENH_NHAN,
    bn.TEN_BENH_NHAN                            AS TEN_BENH_NHAN_HT,
    bn.DIA_CHI,
    bn.so_dien_thoai                  AS so_dien_thoai,
    decode(bn.GIOI_TINH, 1, 'true', 'false')    AS GIOI_TINH,
    0 SO_TIEP_NHAN_PB,
    0 MA_PHONG_BENH,
    0 BA_NGOAITRU,
    1 tt_dungtuyen,
    1 bhyt_noitinh,
    0 sovaovien,
    0       AS TT_CHUYEN_PHONG,
    0                        AS ketoanxacnhan,
    0 da_xuat_thuoc_vt
    --Begin AGG Khang 5/5/2017 select them thong tin
        ,
    bn.te_giaykhaisinh,
    bn.te_giaychungsinh,
    bn.te_giaytokhac
    --End AGG Khang 5/5/2017 select them thong tin
    -- Begin AGG - Tâm 0918197999 31/05/2017: lấy thông tin Dịch vụ khám để hiện trên app Gọi số phòng khám
        ,
    '' ten_dv,
    0                      AS khoa_thanhtoan
    -- AGG Quí 07/09/2017 điều chỉnh lại khóa thanh toán
    -- End AGG - Tâm 0918197999 31/05/2017: lấy thông tin Dịch vụ khám để hiện trên app Gọi số phòng khám
    --Ends AGG Khang 12/12/2016 Th�m tr?ng th�i chuy?n ph�ng v� format code
        ,
    '' AS ngay_mien_cung_ct,
    '' NOIDANGKY_KCB -- CMU VNPTHIS-4879
    --begin HGI Ngân 24/4/2018 thêm trạng thái phiếu xét nghiệm
        , 0 TT_PXN
    --end HGI Ngân 24/4/2018 thêm trạng thái phiếu xét nghiệm
        ,'' as tg_khambenh,
    '' as tg_tiepnhan,
    0 as HINHANHTN
        , 0 as MANTINH,
    0 as khambenh_online,
    '' as cuocgoi_manguoinhan,0 as BN_DATLICH_KGG
        ,' ' as TT_DATLICH_KGG

from (select distinct mabenhnhan
      from (select ba.mabenhnhan,
                   ct.ngay_ra_toa,
                   max(ct.so_ngay_uong) over (partition by ct.ma_toa_thuoc, ct.sovaovien) as max_so_ngay_uong
            from his_manager.ngoaitru_benhan_tonghop ba
                     join his_manager.kb_chi_tiet_toa_thuoc ct on ba.dvtt = ct.dvtt and ba.sovaovien = ct.sovaovien
            where ba.dvtt = p_dvtt and ct.dvtt = p_dvtt
              and nvl(ba.ketthuc_benhan, 0) = 0   -- Chua ket thuc benh an
              and ba.loaibenhan = 'ngoaitru'
              and ct.ma_phong_chidinh = p_ma_phong_benh) t  -- Lay theo phong benh
      where trunc(ngay_ra_toa + max_so_ngay_uong) = trunc(v_ngayhientai)
     ) bant
         left join (select t.ma_benh_nhan
                    from his_manager.kb_tiep_nhan t
                    where t.dvtt=p_dvtt and trunc(t.ngay_tiep_nhan)=trunc(v_ngayhientai)) tn
                   on bant.mabenhnhan = tn.ma_benh_nhan
         join his_public_list.dm_benh_nhan bn on bant.MABENHNHAN = bn.MA_BENH_NHAN
where  tn.ma_benh_nhan is null  -- Benh nhan chua tiep nhan hom nay
--and tt.ngay_ra_toa+stg_songaythuoc_f(p_dvtt,tt.ma_toa_thuoc,bant.MABENHNHAN)=v_ngayhientai
--and  tt.ngay_ra_toa= (select max(tt1.ngay_ra_toa) from  his_manager.kb_toa_thuoc tt1 where tt1.dvtt=p_dvtt and tt1.mabenhnhan=bant.MABENHNHAN)
GROUP BY
    bn.ngay_sinh,
    bn.TEN_BENH_NHAN,
    bn.DIA_CHI,
    bn.so_dien_thoai,
    bn.GIOI_TINH,
    bn.te_giaykhaisinh,
    bn.te_giaychungsinh,
    bn.te_giaytokhac

;
-- HGI Ngân
else
      --Begin AGG Khang 12/12/2016 Th�m tr?ng th�i chuy?n ph�ng v� format code
      OPEN cur FOR
SELECT
    tn.ID_TIEPNHAN,
    tn.DVTT,
    tn.STT_HANGNGAY,
    tn.MA_BENH_NHAN,
    tn.MA_DV,
    tn.THOI_GIAN_TIEP_NHAN,
    tn.TI_LE_MIEN_GIAM,
    tn.TRANG_THAI,
    tn.CO_BAO_HIEM,
    to_char(bn.ngay_sinh, 'dd/mm/yyyy')         AS NGAY_SINH,
    tn.TUOI,
    tn.THANG
    --,case when tn.THANG='0' then hienthi_tuoi_benhnhan(bn.NGAY_SINH) else '0' end as TUOI,
    --case when tn.TUOI='0' then hienthi_tuoi_benhnhan(bn.NGAY_SINH) else '0' end as THANG
        ,
    decode(tn.TUOI + tn.THANG,
           0,
           trunc(tn.THOI_GIAN_TIEP_NHAN) - bn.ngay_sinh,
           0)                                   AS NGAY,
    -- Sang STG - 11/10/2017: Them ngay tuoi TE
    decode(tn.UU_TIEN, 1, 'true', 'false')      AS UU_TIEN,
    tn.CAPCUU,
    decode(tn.KSK, 1, 'true', 'false')          AS KSK,
    tn.DUNG_TUYEN,
    tn.THANG,
    tn.TIEPNHAN_QUADAI108,
    tn.TT_CONG_KHAM,
    tn.THANH_TOAN,
    tn.SO_THE_BHYT,
    tn.NGAY_BATDAU,
    tn.NGAY_HETHAN,
    tn.THANH_TOAN_YC,
    DOUUTIEN,
    decode(tn.CANHBAO, 1, 'true', 'false')      AS CANHBAO,
    dt.TEN_DOI_TUONG_BHYT,
    bn.TEN_BENH_NHAN,
    bn.TEN_BENH_NHAN                            AS TEN_BENH_NHAN_HT,
    bn.DIA_CHI,
    bn.so_dien_thoai                  AS so_dien_thoai,
    decode(bn.GIOI_TINH, 1, 'true', 'false')    AS GIOI_TINH,
    tnp.SO_TIEP_NHAN_PB,
    tnp.MA_PHONG_BENH,
    NVL2(BANT.ketthuc_benhan,1,tn.BA_NGOAITRU) BA_NGOAITRU,
    tn.tt_dungtuyen,
    tn.bhyt_noitinh,
    tn.sovaovien,
    AGG_NGT_CHECK_CHUYENPHONG_FF(tn.MA_BENH_NHAN,
                                 tn.sovaovien,
                                 tn.DVTT)        AS TT_CHUYEN_PHONG,
    CASE
        WHEN substr(p_dvtt, 1, 2) = '89'
            THEN
            0
        ELSE
            ptt.ketoanxacnhan
        END                                         AS ketoanxacnhan,
    ptt.da_xuat_thuoc_vt
    --Begin AGG Khang 5/5/2017 select them thong tin
        ,
    bn.te_giaykhaisinh,
    bn.te_giaychungsinh,
    bn.te_giaytokhac
    --End AGG Khang 5/5/2017 select them thong tin
    -- Begin AGG - Tâm 0918197999 31/05/2017: lấy thông tin Dịch vụ khám để hiện trên app Gọi số phòng khám
        ,
    dv.ten_dv
    -- End AGG - Tâm 0918197999 31/05/2017: lấy thông tin Dịch vụ khám để hiện trên app Gọi số phòng khám
        ,
    CASE
        WHEN (nvl(ptt.phongketoan_inbangke, 0) = 1 AND
              nvl(ptt.khoa_thanhtoan, 0) = 1)
            THEN
            1
        ELSE
            0
        END                                         AS khoa_thanhtoan
    -- AGG Quí 07/09/2017 điều chỉnh lại khóa thanh toán sau khi in bảng kê
        ,
    to_char(TN.ngay_mien_cung_ct, 'dd/mm/yyyy') AS ngay_mien_cung_ct,
    tn.NOIDANGKY_KCB -- CMU VNPTHIS-4879
    --begin HGI Ngân 24/4/2018 thêm trạng thái phiếu xét nghiệm
        , case when exists (select SO_PHIEU_XN from HIS_MANAGER.KB_CD_XET_NGHIEM xn
                            where xn.dvtt=tn.dvtt and xn.MA_KHAM_BENH = concat('kb_',tn.id_tiepnhan) and xn.MABENHNHAN = tn.MA_BENH_NHAN
                              and xn.TRANG_THAI_XET_NGHIEM=1 and xn.DAIN=1) then 1 else 0 end TT_PXN
    --end HGI Ngân 24/4/2018 thêm trạng thái phiếu xét nghiệm
        ,to_char( tnp.thoi_gian_kham_benh, 'HH24:MI' ) as tg_khambenh,
    to_char( tn.thoi_gian_tiep_nhan, 'HH24:MI' ) as tg_tiepnhan,
    decode(v_goisohinhanh,3,NGT_LAYSTT_HINHANH(tn.dvtt,tn.id_tiepnhan),0) as HINHANHTN
        , case when exists(select 1 from NGOAITRU_BENHAN_TONGHOP bant where tn.MA_BENH_NHAN=bant.MABENHNHAN and bant.dvtt = p_dvtt
                                                                        and bant.ketthuc_benhan = 0 and bant.loaibenhan = 'ngoaitru') then 1 else 0 end as MANTINH

        , nvl(NOIGIOITHIEU,'') NOIGIOITHIEU
        , nvl(TENNOIGIOITHIEU,'') TENNOIGIOITHIEU,
    to_char(nvl(tn.ngay_batdau,''),'dd/mm/yyyy') as ngay_bd_bh,
    to_char(nvl(tn.ngay_hethan,''),'dd/mm/yyyy') as ngay_hh_bh,
    to_char(nvl(tn.kb_thoidiem_5nam_lientuc,''),'dd/mm/yyyy') as ngay_5nam,nvl(bn.hien_namsinh,0) as chinamsinh
        ,dv.KYHIEUDV, tn.khambenh_online,
    tn.cuocgoi_manguoinhan, nvl(tn.BN_DATLICH_KGG,0) as BN_DATLICH_KGG
        , nvl(tn.tt_datlich_kgg,tnp.SO_TIEP_NHAN_PB) as TT_DATLICH_KGG,
    tnp.TRANG_THAI_BN
FROM kb_tiep_nhan tn
         LEFT JOIN kb_phieuthanhtoan ptt
                   ON tn.dvtt = ptt.dvtt
                       AND tn.id_tiepnhan = ptt.id_tiepnhan
                       AND tn.sovaovien = ptt.sovaovien
         LEFT JOIN kb_chuyenPK_thutien_tt37 tt37
                   ON tt37.sovaovien = tn.sovaovien
                       AND tt37.dvtt = tn.dvtt
         LEFT JOIN his_public_list.dm_doi_tuong_bhyt dt
                   ON tn.BAKYTUDAU = dt.MA_DOI_TUONG_BHYT
         LEFT JOIN  HIS_MANAGER.vienphingoaitru_lantamung lantt
                    on lantt.sovaovien=tn.sovaovien and lantt.TT_LANTT=1 and tn.CO_BAO_HIEM=0
         left join HIS_MANAGER.NGOAITRU_BENHAN_TONGHOP BANT ON
    BANT.DVTT = tn.dvtt and BANT.MABENHNHAN = tn.MA_BENH_NHAN and bant.KETTHUC_BENHAN = 0
        , dm_dich_vu_kham dv,
     tiep_nhan_phong_benh tnp, his_public_list.dm_benh_nhan bn
WHERE tn.NGAY_TIEP_NHAN = v_ngayhientai
  AND tn.THANG_HT = v_thanghientai
  AND tn.NAM = v_namhientai
  and 1=(case tn.CO_BAO_HIEM when 1 then 1 else case p_dskhamngoailoc67 when 1 then case  when lantt.sovaovien is not null then 1 else 0 end else 1 end end)
  AND tnp.NGAYTIEPNHAN = v_ngayhientai
  AND tnp.THANGTIEPNHAN = v_thanghientai
  AND tnp.NAMTIEPNHAN = v_namhientai
  AND tn.MA_DV = dv.MA_DV
  AND tn.DVTT = p_dvtt
  AND tnp.DVTT = p_dvtt
  AND dv.DVTT = p_dvtt
  AND tnp.MA_PHONG_BENH = p_ma_phong_benh
  AND tnp.TRANG_THAI_BN = p_trang_thai
  AND ((1=decode(p_mienphi_bobatdongtien,1,tn.MIENPHI,0) --and dv.TRANGTHAI = 1
           ) or (tn.TI_LE_MIEN_GIAM > 0 AND dv.KHAM_DV = 0 /*AND dv.TRANGTHAI = 1*/) OR tn.tien_cong_kham = 0 OR
       (v_bhyt_traituyen_vankham = '1' AND TN.SO_THE_BHYT IS NOT NULL) OR -- TRAI TUYẾN CÓ BH VẪN KHÁM
       (nvl(tt37.tt_thutienvienphi, 1) = 1 AND tn.THANH_TOAN = 1 AND
        nvl(tn.TI_LE_MIEN_GIAM, 0) = 0 /*AND dv.TRANGTHAI = 1*/ AND
        dv.KHAM_DV = 0) OR (tn.THANH_TOAN = 0 AND dv.GIA_DV = 0 AND
                            nvl(tn.TI_LE_MIEN_GIAM, 0) = 0 /*AND dv.TRANGTHAI = 1*/ AND dv.KHAM_DV = 0) OR
       (tn.TI_LE_MIEN_GIAM > 0 /*AND dv.TRANGTHAI = 1*/ AND
        dv.KHAM_DV = 1 AND tn.THANH_TOAN_YC = 1) OR
       (nvl(tn.TI_LE_MIEN_GIAM, 0) = 0 AND tn.thanh_toan = 1 AND
        dv.KHAM_DV = 1 AND tn.TT_CK_DV_KBH = 1) -- CMU NGUYEN THEM BN KBH KHAM DICH VU
    OR (tn.capcuu = 1 and v_ts_capcuukham = '1' and tn.tien_cong_kham != 0) -- Hoành STG 30/06/2018
    or (tn.thanh_toan = 0 and tnp.TRANG_THAI_BN = 3 and v_thamso_960558 = '1') -- Toan CMU
    )
  AND tn.ID_TIEPNHAN = tnp.ID_TIEPNHAN
  AND tn.MA_BENH_NHAN = bn.MA_BENH_NHAN
GROUP BY tn.ID_TIEPNHAN,
         tn.DVTT,
         tn.STT_HANGNGAY,
         tn.MA_BENH_NHAN,
         tn.MA_DV,
         tn.THOI_GIAN_TIEP_NHAN,
         tn.TI_LE_MIEN_GIAM,
         tn.TRANG_THAI,
         tn.CO_BAO_HIEM,
         bn.ngay_sinh,
         tn.TUOI,
         tn.THANG,
         tn.UU_TIEN,
         tn.CAPCUU,
         tn.KSK,
         tn.DUNG_TUYEN,
         tn.THANG,
         tn.TIEPNHAN_QUADAI108,
         tn.TT_CONG_KHAM,
         tn.THANH_TOAN,
         tn.SO_THE_BHYT,
         tn.NGAY_BATDAU,
         tn.NGAY_HETHAN,
         tn.THANH_TOAN_YC,
         DOUUTIEN,
         tn.CANHBAO,
         dt.TEN_DOI_TUONG_BHYT,
         bn.TEN_BENH_NHAN,
         bn.DIA_CHI,
         bn.so_dien_thoai,
         bn.GIOI_TINH,
         tnp.SO_TIEP_NHAN_PB,
         tnp.MA_PHONG_BENH,
         NVL2(BANT.ketthuc_benhan,1,tn.BA_NGOAITRU),
         tn.tt_dungtuyen,
         tn.bhyt_noitinh,
         tn.sovaovien,
         bn.NGAY_SINH,
         ptt.ketoanxacnhan,
         ptt.da_xuat_thuoc_vt
         --Begin AGG Khang 5/5/2017 select them thong tin
        ,
         bn.te_giaykhaisinh,
         bn.te_giaychungsinh,
         bn.te_giaytokhac
         --End AGG Khang 5/5/2017 select them thong tin
         -- Begin AGG - Tâm 0918197999 31/05/2017: lấy thông tin Dịch vụ khám để hiện trên app Gọi số phòng khám
        ,
         DV.ten_dv,
         ptt.phongketoan_inbangke,
         ptt.khoa_thanhtoan -- AGG Quí 17/06/2017 lấy thêm khóa thanh toán
         -- End AGG - Tâm 0918197999 31/05/2017: lấy thông tin Dịch vụ khám để hiện trên app Gọi số phòng khám
        ,
         TN.ngay_mien_cung_ct,
         tn.NOIDANGKY_KCB -- CMU VNPTHIS-4879
        ,tnp.thoi_gian_kham_benh
        ,case when exists(select 1 from NGOAITRU_BENHAN_TONGHOP bant where tn.MA_BENH_NHAN=bant.MABENHNHAN and bant.dvtt = p_dvtt
                                                                       and bant.ketthuc_benhan = 0 and bant.loaibenhan = 'ngoaitru') then 1 else 0 end
        ,  NOIGIOITHIEU
        ,  TENNOIGIOITHIEU,tn.kb_thoidiem_5nam_lientuc,bn.hien_namsinh  ,dv.KYHIEUDV, tn.khambenh_online,
         tn.cuocgoi_manguoinhan,tn.TT_DATLICH_KGG
        ,TN.BN_DATLICH_KGG,
         tnp.TRANG_THAI_BN
ORDER BY tn.UU_TIEN DESC, tnp.SO_TIEP_NHAN_PB ASC;
end if;
ELSE
    --HGI Ngân 09/09/2018 thêm danh sách bệnh nhân BANT mãn tính

    if p_trang_thai = 8 and v_thamso_ds_bant_mantinh = 1 then
      OPEN cur FOR
SELECT
    '' ID_TIEPNHAN,
    p_dvtt DVTT,
    0 STT_HANGNGAY,
    '' MA_BENH_NHAN,
    0 MA_DV,
    '' THOI_GIAN_TIEP_NHAN,
    0 TI_LE_MIEN_GIAM,
    0 TRANG_THAI,
    0 CO_BAO_HIEM,
    to_char(bn.ngay_sinh, 'dd/mm/yyyy')         AS NGAY_SINH,
    0 as TUOI,
    0 as THANG
    --,case when tn.THANG='0' then hienthi_tuoi_benhnhan(bn.NGAY_SINH) else '0' end as TUOI,
    --case when tn.TUOI='0' then hienthi_tuoi_benhnhan(bn.NGAY_SINH) else '0' end as THANG
        , 0 AS NGAY,
    -- Sang STG - 11/10/2017: Them ngay tuoi TE
    0   AS UU_TIEN,
    0 as CAPCUU,
    'false'        AS KSK,
    1 as DUNG_TUYEN,
    0 as THANG,
    0 TIEPNHAN_QUADAI108,
    0 TT_CONG_KHAM,
    0 THANH_TOAN,
    '' SO_THE_BHYT,
    '' NGAY_BATDAU,
    '' NGAY_HETHAN,
    0 THANH_TOAN_YC,
    0 DOUUTIEN,
    'false'    AS CANHBAO,
    '' TEN_DOI_TUONG_BHYT,
    bn.TEN_BENH_NHAN,
    bn.TEN_BENH_NHAN                            AS TEN_BENH_NHAN_HT,
    bn.DIA_CHI,
    bn.so_dien_thoai                  AS so_dien_thoai,
    decode(bn.GIOI_TINH, 1, 'true', 'false')    AS GIOI_TINH,
    0 SO_TIEP_NHAN_PB,
    0 MA_PHONG_BENH,
    0 BA_NGOAITRU,
    1 tt_dungtuyen,
    1 bhyt_noitinh,
    0 sovaovien,
    0       AS TT_CHUYEN_PHONG,
    0                        AS ketoanxacnhan,
    0 da_xuat_thuoc_vt
    --Begin AGG Khang 5/5/2017 select them thong tin
        ,
    bn.te_giaykhaisinh,
    bn.te_giaychungsinh,
    bn.te_giaytokhac
    --End AGG Khang 5/5/2017 select them thong tin
    -- Begin AGG - Tâm 0918197999 31/05/2017: lấy thông tin Dịch vụ khám để hiện trên app Gọi số phòng khám
        ,
    '' ten_dv,
    0                      AS khoa_thanhtoan
    -- AGG Quí 07/09/2017 điều chỉnh lại khóa thanh toán
    -- End AGG - Tâm 0918197999 31/05/2017: lấy thông tin Dịch vụ khám để hiện trên app Gọi số phòng khám
    --Ends AGG Khang 12/12/2016 Th�m tr?ng th�i chuy?n ph�ng v� format code
        ,
    '' AS ngay_mien_cung_ct,
    '' NOIDANGKY_KCB -- CMU VNPTHIS-4879
    --begin HGI Ngân 24/4/2018 thêm trạng thái phiếu xét nghiệm
        , 0 TT_PXN
    --end HGI Ngân 24/4/2018 thêm trạng thái phiếu xét nghiệm
        ,'' as tg_khambenh,
    '' as tg_tiepnhan,
    0 as HINHANHTN
        , 0 as MANTINH,
    0 as khambenh_online,
    '' as cuocgoi_manguoinhan ,0 as BN_DATLICH_KGG
        , ' ' as TT_DATLICH_KGG

from (select distinct mabenhnhan
      from (select ba.mabenhnhan,
                   ct.ngay_ra_toa,
                   max(ct.so_ngay_uong) over (partition by ct.ma_toa_thuoc, ct.sovaovien) as max_so_ngay_uong
            from his_manager.ngoaitru_benhan_tonghop ba
                     join his_manager.kb_chi_tiet_toa_thuoc ct on ba.dvtt = ct.dvtt and ba.sovaovien = ct.sovaovien
            where ba.dvtt = p_dvtt and ct.dvtt = p_dvtt
              and nvl(ba.ketthuc_benhan, 0) = 0
              and ba.loaibenhan = 'ngoaitru'
              and ct.ma_phong_chidinh = p_ma_phong_benh) t
      where trunc(ngay_ra_toa + max_so_ngay_uong) = trunc(v_ngayhientai)
     ) bant
         left join (select t.ma_benh_nhan
                    from his_manager.kb_tiep_nhan t
                    where t.dvtt=p_dvtt and trunc(t.ngay_tiep_nhan)=trunc(v_ngayhientai)) tn
                   on bant.mabenhnhan = tn.ma_benh_nhan
         join his_public_list.dm_benh_nhan bn on bant.MABENHNHAN = bn.MA_BENH_NHAN
where  tn.ma_benh_nhan is null  -- Benh nhan chua tiep nhan hom nay
--and tt.ngay_ra_toa+stg_songaythuoc_f(p_dvtt,tt.ma_toa_thuoc,bant.MABENHNHAN)=v_ngayhientai
--and  tt.ngay_ra_toa= (select max(tt1.ngay_ra_toa) from  his_manager.kb_toa_thuoc tt1 where tt1.dvtt=p_dvtt and tt1.mabenhnhan=bant.MABENHNHAN)

GROUP BY
    bn.ngay_sinh,
    bn.TEN_BENH_NHAN,
    bn.DIA_CHI,
    bn.so_dien_thoai,
    bn.GIOI_TINH,
    bn.te_giaykhaisinh,
    bn.te_giaychungsinh,
    bn.te_giaytokhac


;
-- HGI Ngân
else

      OPEN cur FOR
SELECT
    tn.ID_TIEPNHAN,
    tn.DVTT,
    tn.STT_HANGNGAY,
    tn.MA_BENH_NHAN,
    tn.MA_DV,
    tn.THOI_GIAN_TIEP_NHAN,
    tn.TI_LE_MIEN_GIAM,
    tn.TRANG_THAI,
    tn.CO_BAO_HIEM,
    to_char(bn.ngay_sinh, 'dd/mm/yyyy')         AS NGAY_SINH,
    tn.TUOI,
    tn.THANG
    --,case when tn.THANG='0' then hienthi_tuoi_benhnhan(bn.NGAY_SINH) else '0' end as TUOI,
    --case when tn.TUOI='0' then hienthi_tuoi_benhnhan(bn.NGAY_SINH) else '0' end as THANG
        ,
    decode(tn.TUOI + tn.THANG,
           0,
           trunc(tn.THOI_GIAN_TIEP_NHAN) - bn.ngay_sinh,
           0)                                   AS NGAY,
    -- Sang STG - 11/10/2017: Them ngay tuoi TE
    decode(tn.UU_TIEN, 1, 'true', 'false')      AS UU_TIEN,
    tn.CAPCUU,
    decode(tn.KSK, 1, 'true', 'false')          AS KSK,
    tn.DUNG_TUYEN,
    tn.THANG,
    tn.TIEPNHAN_QUADAI108,
    tn.TT_CONG_KHAM,
    tn.THANH_TOAN,
    tn.SO_THE_BHYT,
    tn.NGAY_BATDAU,
    tn.NGAY_HETHAN,
    tn.THANH_TOAN_YC,
    DOUUTIEN,
    decode(tn.CANHBAO, 1, 'true', 'false')      AS CANHBAO,
    dt.TEN_DOI_TUONG_BHYT,
    bn.TEN_BENH_NHAN,
    bn.TEN_BENH_NHAN                            AS TEN_BENH_NHAN_HT,
    bn.DIA_CHI,
    bn.so_dien_thoai                  AS so_dien_thoai,
    decode(bn.GIOI_TINH, 1, 'true', 'false')    AS GIOI_TINH,
    tnp.SO_TIEP_NHAN_PB,
    tnp.MA_PHONG_BENH,
    NVL2(BANT.ketthuc_benhan,1,tn.BA_NGOAITRU) BA_NGOAITRU,
    tn.tt_dungtuyen,
    tn.bhyt_noitinh,
    tn.sovaovien,
    AGG_NGT_CHECK_CHUYENPHONG_FF(tn.MA_BENH_NHAN,
                                 tn.sovaovien,
                                 tn.DVTT)        AS TT_CHUYEN_PHONG,
    CASE
        WHEN substr(p_dvtt, 1, 2) = '89'
            THEN
            0
        ELSE
            ptt.ketoanxacnhan
        END                                         AS ketoanxacnhan,
    ptt.da_xuat_thuoc_vt
    --Begin AGG Khang 5/5/2017 select them thong tin
        ,
    bn.te_giaykhaisinh,
    bn.te_giaychungsinh,
    bn.te_giaytokhac
    --End AGG Khang 5/5/2017 select them thong tin
    -- Begin AGG - Tâm 0918197999 31/05/2017: lấy thông tin Dịch vụ khám để hiện trên app Gọi số phòng khám
        ,
    dv.ten_dv,
    CASE
        WHEN (nvl(ptt.phongketoan_inbangke, 0) = 1 AND
              nvl(ptt.khoa_thanhtoan, 0) = 1)
            THEN
            1
        ELSE
            0
        END                                         AS khoa_thanhtoan
    -- AGG Quí 07/09/2017 điều chỉnh lại khóa thanh toán
    -- End AGG - Tâm 0918197999 31/05/2017: lấy thông tin Dịch vụ khám để hiện trên app Gọi số phòng khám
    --Ends AGG Khang 12/12/2016 Th�m tr?ng th�i chuy?n ph�ng v� format code
        ,
    to_char(TN.ngay_mien_cung_ct, 'dd/mm/yyyy') AS ngay_mien_cung_ct,
    tn.NOIDANGKY_KCB -- CMU VNPTHIS-4879
    --begin HGI Ngân 24/4/2018 thêm trạng thái phiếu xét nghiệm
        , case when exists (select SO_PHIEU_XN from HIS_MANAGER.KB_CD_XET_NGHIEM xn
                            where xn.dvtt=tn.dvtt and xn.MA_KHAM_BENH = concat('kb_',tn.id_tiepnhan) and xn.MABENHNHAN = tn.MA_BENH_NHAN
                              and xn.TRANG_THAI_XET_NGHIEM=1 and xn.DAIN=1) then 1 else 0 end TT_PXN
    --end HGI Ngân 24/4/2018 thêm trạng thái phiếu xét nghiệm
        ,to_char( tnp.thoi_gian_kham_benh, 'HH24:MI' ) as tg_khambenh,
    to_char( tn.thoi_gian_tiep_nhan, 'HH24:MI' ) as tg_tiepnhan,
    decode(v_goisohinhanh,3,NGT_LAYSTT_HINHANH(tn.dvtt,tn.id_tiepnhan),0) as HINHANHTN
        , case when exists(select 1 from NGOAITRU_BENHAN_TONGHOP bant where tn.MA_BENH_NHAN=bant.MABENHNHAN and bant.dvtt = p_dvtt
                                                                        and bant.ketthuc_benhan = 0 and bant.loaibenhan = 'ngoaitru') then 1 else 0 end as MANTINH
        , nvl(NOIGIOITHIEU,'') NOIGIOITHIEU
        , nvl(TENNOIGIOITHIEU,'') TENNOIGIOITHIEU,
    to_char(nvl(tn.ngay_batdau,''),'dd/mm/yyyy') as ngay_bd_bh,
    to_char(nvl(tn.ngay_hethan,''),'dd/mm/yyyy') as ngay_hh_bh,
    to_char(nvl(tn.kb_thoidiem_5nam_lientuc,''),'dd/mm/yyyy') as ngay_5nam,nvl(bn.hien_namsinh,0) as chinamsinh ,dv.KYHIEUDV, tn.khambenh_online,
    tn.cuocgoi_manguoinhan, nvl(tn.BN_DATLICH_KGG,0) as BN_DATLICH_KGG
        , nvl(tn.tt_datlich_kgg,tnp.SO_TIEP_NHAN_PB) as TT_DATLICH_KGG,
    tnp.TRANG_THAI_BN
FROM kb_tiep_nhan tn
         LEFT JOIN kb_phieuthanhtoan ptt
                   ON tn.dvtt = ptt.dvtt
                       AND tn.id_tiepnhan = ptt.id_tiepnhan
                       AND tn.sovaovien = ptt.sovaovien
         LEFT JOIN his_public_list.dm_doi_tuong_bhyt dt
                   ON tn.BAKYTUDAU = dt.MA_DOI_TUONG_BHYT
         LEFT JOIN  HIS_MANAGER.vienphingoaitru_lantamung lantt
                    on lantt.sovaovien=tn.sovaovien and lantt.TT_LANTT=1 and tn.CO_BAO_HIEM=0
         left join HIS_MANAGER.NGOAITRU_BENHAN_TONGHOP BANT ON
    BANT.DVTT = tn.dvtt and BANT.MABENHNHAN = tn.MA_BENH_NHAN and bant.KETTHUC_BENHAN = 0
        , dm_dich_vu_kham dv,
     tiep_nhan_phong_benh tnp, his_public_list.dm_benh_nhan bn
WHERE tn.NGAY_TIEP_NHAN = v_ngayhientai
  AND tn.THANG_HT = v_thanghientai
  AND tn.NAM = v_namhientai
  and 1=(case tn.CO_BAO_HIEM when 1 then 1 else case p_dskhamngoailoc67 when 1 then case  when lantt.sovaovien is not null then 1 else 0 end else 1 end end)
  AND tnp.NGAYTIEPNHAN = v_ngayhientai
  AND tnp.THANGTIEPNHAN = v_thanghientai
  AND tnp.NAMTIEPNHAN = v_namhientai
  AND tn.MA_DV = dv.MA_DV
  AND tn.DVTT = p_dvtt
  AND tnp.DVTT = p_dvtt
  AND dv.DVTT = p_dvtt
  AND tnp.MA_PHONG_BENH = p_ma_phong_benh
  AND tnp.TRANG_THAI_BN = p_trang_thai
  --AND dv.TRANGTHAI = 1
  -- and ((tn.TI_LE_MIEN_GIAM > 0 and dv.KHAM_DV = 0 and dv.TRANGTHAI = 1) or
  -- (tn.THANH_TOAN = 1 and ifnull(tn.TI_LE_MIEN_GIAM ,0) = 0 and dv.TRANGTHAI = 1 and dv.KHAM_DV = 0) or
  -- (tn.THANH_TOAN=0 and dv.GIA_DV=0 and ifnull(tn.TI_LE_MIEN_GIAM ,0) = 0 and dv.TRANGTHAI = 1 and dv.KHAM_DV = 0) or
  -- (tn.TI_LE_MIEN_GIAM > 0 and dv.TRANGTHAI = 1 and dv.KHAM_DV = 1 and tn.THANH_TOAN_YC = 1))
  AND tn.ID_TIEPNHAN = tnp.ID_TIEPNHAN
  AND tn.MA_BENH_NHAN = bn.MA_BENH_NHAN
GROUP BY tn.ID_TIEPNHAN,
         tn.DVTT,
         tn.STT_HANGNGAY,
         tn.MA_BENH_NHAN,
         tn.MA_DV,
         tn.THOI_GIAN_TIEP_NHAN,
         tn.TI_LE_MIEN_GIAM,
         tn.TRANG_THAI,
         tn.CO_BAO_HIEM,
         bn.ngay_sinh,
         tn.TUOI,
         tn.THANG,
         tn.UU_TIEN,
         tn.CAPCUU,
         tn.KSK,
         tn.DUNG_TUYEN,
         tn.THANG,
         tn.TIEPNHAN_QUADAI108,
         tn.TT_CONG_KHAM,
         tn.THANH_TOAN,
         tn.SO_THE_BHYT,
         tn.NGAY_BATDAU,
         tn.NGAY_HETHAN,
         tn.THANH_TOAN_YC,
         DOUUTIEN,
         tn.CANHBAO,
         dt.TEN_DOI_TUONG_BHYT,
         bn.TEN_BENH_NHAN,
         bn.DIA_CHI,
         bn.so_dien_thoai,
         bn.GIOI_TINH,
         tnp.SO_TIEP_NHAN_PB,
         tnp.MA_PHONG_BENH,
         NVL2(BANT.ketthuc_benhan,1,tn.BA_NGOAITRU),
         tn.tt_dungtuyen,
         tn.bhyt_noitinh,
         tn.sovaovien,
         ptt.ketoanxacnhan,
         ptt.da_xuat_thuoc_vt
         --Begin AGG Khang 5/5/2017 select them thong tin
        ,
         bn.te_giaykhaisinh,
         bn.te_giaychungsinh,
         bn.te_giaytokhac
         --End AGG Khang 5/5/2017 select them thong tin
         -- Begin AGG - Tâm 0918197999 31/05/2017: lấy thông tin Dịch vụ khám để hiện trên app Gọi số phòng khám
        ,
         DV.ten_dv,
         ptt.phongketoan_inbangke,
         ptt.khoa_thanhtoan -- AGG Quí 17/06/2017 lấy thêm khóa thanh toán
         -- End AGG - Tâm 0918197999 31/05/2017: lấy thông tin Dịch vụ khám để hiện trên app Gọi số phòng khám
         --, bn.NGAY_SINH
        ,
         TN.ngay_mien_cung_ct,
         tn.NOIDANGKY_KCB -- CMU VNPTHIS-4879
        ,tnp.thoi_gian_kham_benh
        ,case when exists(select 1 from NGOAITRU_BENHAN_TONGHOP bant where tn.MA_BENH_NHAN=bant.MABENHNHAN and bant.dvtt = p_dvtt
                                                                       and bant.ketthuc_benhan = 0 and bant.loaibenhan = 'ngoaitru') then 1 else 0 end
        ,  NOIGIOITHIEU
        ,  TENNOIGIOITHIEU,tn.kb_thoidiem_5nam_lientuc,bn.hien_namsinh ,dv.KYHIEUDV, tn.khambenh_online,
         tn.cuocgoi_manguoinhan	, tn.TT_DATLICH_KGG
        ,TN.BN_DATLICH_KGG,
         tnp.TRANG_THAI_BN
ORDER BY tn.UU_TIEN DESC, tnp.SO_TIEP_NHAN_PB ASC;
END IF;
END IF;
END IF;
RETURN cur;
END;
