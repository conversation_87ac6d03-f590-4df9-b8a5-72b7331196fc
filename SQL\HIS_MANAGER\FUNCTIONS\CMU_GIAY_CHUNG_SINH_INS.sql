CREATE OR REPLACE FUNCTION cmu_giay_chung_sinh_ins (
    p_dvtt          VARCHAR2,
    p_sovaovien     VARCHAR2,
    p_makhoa        VARCHAR2,
    p_idchungsinh   VARCHAR2,
    p_ngaycap       VARCHAR2,
    p_truongkhoa    VARCHAR2,
    p_xmldata       CLOB
) RETURN NUMBER AS

    v_dakyso         NUMBER := 0;
    v_thamso960599   NUMBER(10) := cmu_tsdv(p_dvtt, 960599, 0);
BEGIN
SELECT
    COUNT(1)
INTO v_dakyso
FROM
    cmu_giay_chung_sinh
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND id_chungsinh = p_idchungsinh
  AND ( ( matruongkhoa IS NOT NULL
    AND v_thamso960599 = 1 )
    OR mabgd IS NOT NULL );

IF v_dakyso > 0 THEN
        RETURN -1;
END IF;
DELETE FROM cmu_giay_chung_sinh
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND id_chungsinh = p_idchungsinh;

INSERT INTO cmu_giay_chung_sinh (
    dvtt,
    id_chungsinh,
    sovaovien,
    makhoa,
    ngaychungsinh,
    xmldata,
    matruongkhoa
) VALUES (
             p_dvtt,
             p_idchungsinh,
             p_sovaovien,
             p_makhoa,
             TO_DATE(p_ngaycap, 'DD/MM/YYYY'),
             p_xmldata,
             CASE WHEN v_thamso960599 = 1 then null else p_truongkhoa end
         );

RETURN SQL%rowcount;
END;