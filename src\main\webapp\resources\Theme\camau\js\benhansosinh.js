function getBASOSINHJSON() {
    var form;
    var formTongket;
    var keyMauHSBASOSINH = "MAUHSBASOSINH";
    var keyMauHSBASOSINHTongket = "MAUHSBASOSINHTONGKET";
    var formioMauHSBA;
    var formioMauHSBATongket;
    return {
        script: {},
        scriptTongket: {},
        initObjectFormPage1: function(edit, hidden) {
            return getJSONObjectForm([
                {
                    "tag": "label",
                    "content": "",
                    "refreshOnChange": false,
                    "key": "htmllabelcanhbao",
                    "type": "htmlelement",
                    others: {
                        "hidden": true,
                    }
                },
                {
                    "key": "p-chandoan",
                    "type": "tabs",
                    "customClass": "hsba-tabs-wrap",
                    "components": [
                        {
                            "label": "THÔNG TIN HÀNH CHÍNH",
                            "key": "tabThongTinHanhChinh",
                            "components": [
                                getObjectThongTinHanhChinhVBA(edit, hidden),
                            ]
                        },
                        {
                            "label": "QUẢN LÝ NGƯỜI BỆNH",
                            "key": "tabQuanLyNguoiBenh",
                            "components": [
                                getObjectQuanLyNguoiBenhVBAT1_1(edit, hidden),
                            ]
                        },
                        {
                            "label": "CHẨN ĐOÁN",
                            "key": "tabChanDoan",
                            "components": [
                                getObjectChanDoanVBAT1_2(edit, hidden),
                            ]
                        },
                        {
                            "label": "TÌNH TRẠNG RA VIỆN",
                            "key": "tabTinhTrangRaVien",
                            "components": [
                                getObjectTinhTrangRaVienVBAT1_2(edit, hidden),
                            ]
                        }
                    ]
                },
            ])
        },
        initObjectFormPage2: function () {
            return getJSONObjectForm([
                {
                    "collapsible": true,
                    "key": "p-lydovaovien",
                    "type": "panel",
                    "label": "Lý do vào viện",
                    "title": "BỆNH ÁN VÀ HỎI BỆNH",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap",
                    "components": [
                        {
                            "label": "Lý do vào viện",
                            "key": "LYDOVAOVIEN",
                            "type": "textarea",
                            rows: 2,
                            "validate": {
                                "minLength": 5,
                                "maxLength": 3000,
                                "required": true
                            }
                        },
                        {
                            "label": "Diễn biến bệnh của sơ sinh",
                            "key": "HOIBENH",
                            "type": "textarea",
                            "validate": {
                                "minLength": 5,
                                "maxLength": 3000,
                                "required": true
                            }
                        },
                        {
                            label: "tinhhinhsanphu",
                            key: "colTinhhinhsanphu",
                            columns: [
                                {
                                    "components": [
                                        {
                                            "label": "Tình trạng ối",
                                            others: {
                                                "data": {
                                                    "values": [
                                                        {
                                                            "label": "Ối còn",
                                                            "value": "1"
                                                        },
                                                        {
                                                            "label": "Ối vỡ",
                                                            "value": "2"
                                                        },

                                                    ]
                                                },
                                            },
                                            key: "TINHTRANGOI",
                                            "customClass": "pr-2",
                                            "type": "select",
                                        },
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },

                                {
                                    "components": [
                                        {
                                            "label": "Ối còn",
                                            others: {
                                                "data": {
                                                    "values": [
                                                        {
                                                            "label": "1.Ối phồng",
                                                            "value": "1"
                                                        },
                                                        {
                                                            "label": "2. Ối dẹp",
                                                            "value": "2"
                                                        },
                                                        {
                                                            "label": "3. Ối quả lê",
                                                            "value": "3"
                                                        },
                                                    ]
                                                },
                                                "customConditional": "show = data.TINHTRANGOI == 1;",
                                            },
                                            key: "TINHTRANGOI_CON",
                                            "customClass": "pr-2",
                                            "type": "select",
                                        },
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Ối vỡ lúc",
                                            "key": "OIVONGAY",
                                            "type": "datetime",
                                            format: "dd/MM/yyyy HH:mm",
                                            enableTime: true,
                                            customClass: "pr-2",
                                            others: {
                                                "customConditional": "show = data.TINHTRANGOI == 2;",
                                            }
                                        },
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Ối vỡ",
                                            others: {
                                                "data": {
                                                    "values": [
                                                        {
                                                            "label": "1. Tự nhiên",
                                                            "value": "1"
                                                        },
                                                        {
                                                            "label": "2. Bấm ối",
                                                            "value": "2"
                                                        },
                                                    ]
                                                },
                                                defaultValue: "1",
                                                "customConditional": "show = data.TINHTRANGOI == 2;",
                                            },
                                            key: "TINHTRANGOI_VO",
                                            "customClass": "pr-2",
                                            "type": "select",
                                        }, 
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                // {
                                //     "components": [
                                //         {
                                //             "label": "Ối vỡ lúc",
                                //             "key": "OIVONGAY",
                                //             "type": "datetime",
                                //             customClass: "pr-2",
                                //             enableTime: true,
                                //         },
                                //     ],
                                //     "width": 6,
                                //     "size": "md",
                                // },
                            ],
                            "customClass": "ml-0 mr-0",
                            "type": "columns",
                        },

                        {
                            label: "tinhhinhsanphu2",
                            key: "colTinhhinhsanphu2",
                            columns: [
                                {
                                    "components": [
                                        {
                                            "label": "Màu sắc",
                                            "key": "MAUSAC",
                                            "type": "textfield",
                                            customClass: "pr-2",
                                        },
                                    ],
                                    "width": 4,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Cách đẻ",
                                            others: {
                                                "data": {
                                                    "values": [
                                                        {
                                                            "label": "Đẻ thường",
                                                            "value": "1"
                                                        },
                                                        {
                                                            "label": "Can thiệp",
                                                            "value": "2"
                                                        },
                                                    ]
                                                },
                                            },
                                            key: "DETHUONG",
                                            "customClass": "pr-2",
                                            "type": "select",
                                        },
                                    ],
                                    "width": 4,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Thời gian",
                                            "key": "DENGAY",
                                            "type": "datetime",
                                            customClass: "pr-2",
                                            enableTime: true,
                                        },
                                    ],
                                    "width": 4,
                                    "size": "md",
                                },
                            ],
                            "customClass": "ml-0 mr-0",
                            "type": "columns",
                        },
                        {
                            "label": "Lý do can thiệp",
                            "key": "LYDOCANTHIEP",
                            "type": "textarea",
                            "validate": {
                                "minLength": 5,
                                "maxLength": 3000,
                                "required": true
                            },
                            others: {
                                "customConditional": "show = data.DETHUONG == 2;",
                            }
                        },
                        {
                            "label": "Lấy thông tin trẻ từ bệnh án mẹ",
                            "customClass": "text-right form-control-sm line-height-1",
                            "key": "layThongTinDacDiemCon",
                            "type": "button",
                            others: {
                                "leftIcon": "fa fa-ellipsis-v",
                                "action": "event",
                                "showValidations": false,
                                "event": "layThongTinDacDiemConAction",
                                "type": "button",
                            }

                        },
                        {
                            label: "tinhhinhsanphu2",
                            key: "colTinhhinhsanphu2",
                            columns: [
                                {
                                    "components": [
                                        JSONRadio("Tình trạng sơ sinh khi ra đời", "RADOIKHOCNGAY", "", {
                                            others: {
                                                "optionsLabelPosition": "right",
                                                "inline": true,
                                                "tableView": false,
                                                "values": [
                                                    {
                                                        "label": "Khóc ngay",
                                                        "value": "1",
                                                        "shortcut": ""
                                                    },
                                                    {
                                                        "label": "Ngạt",
                                                        "value": "2",
                                                        "shortcut": ""
                                                    },
                                                    {
                                                        "label": "Khác",
                                                        "value": "3",
                                                        "shortcut": ""
                                                    }
                                                ],
                                            }
                                        }),
                                    ],
                                    "width": 4,
                                    "size": "md",
                                },
                            ],
                            "customClass": "ml-0 mr-0",
                            "type": "columns",
                        },
                        {
                            "label": "Ngoại viện",
                            "key": "BOXNGOAIVIEN",
                            "type": "checkbox",
                        },
                        {
                            "html": "<p>Họ tên người đỡ đẻ</p>",
                            "label": "Tên người đỡ đẻ",
                            "refreshOnChange": false,
                            "key": "NGUOIDODE",
                            "type": "content",
                            "input": false,
                            "tableView": false
                        },
                        getBacsiAllKhoaFormio("MAKHOADODE", "HOTENNGUOIDODE"),
                        {
                            label: "apgar",
                            key: "colApgar",
                            columns: [
                                {
                                    "components": [
                                        {
                                            "label": "Apgar 1p",
                                            "key": "APGAR1PHUT",
                                            "type": "textfield",
                                            customClass: "pr-2",
                                        },
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Apgar 5p",
                                            "key": "APGAR5PHUT",
                                            "type": "textfield",
                                            customClass: "pr-2",
                                        },
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Apgar 10p",
                                            "key": "APGAR10PHUT",
                                            "type": "textfield",
                                            customClass: "pr-2",
                                        },
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Cân nặng (g)",
                                            "key": "APGARCANNANG",
                                            "type": "textfield",
                                            customClass: "pr-2",
                                        },
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                            ],
                            "customClass": "ml-0 mr-0",
                            "type": "columns",
                        },
                        {
                            "label": "Tình trạng dinh dưỡng sau sinh",
                            "key": "TINHTRANGDINHDUONGSAUSINH",
                            "type": "textarea",
                            "rows": 2,
                            "validate": {
                                "required": true
                            },
                        },
                        {
                            "label": "Phương pháp hồi sinh sau đẻ",
                            "columns": [
                                {
                                    "components": [
                                        {
                                            "label": "Hút dịch",
                                            "key": "HUTDICH",
                                            "type": "checkbox",
                                        }
                                    ],
                                    "width": 4,
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Xoa bóp tim",
                                            "key": "XOABOPTIM",
                                            "type": "checkbox",
                                        }
                                    ],
                                    "width": 4,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Thở O2",
                                            "key": "THOO2",
                                            "type": "checkbox",
                                        }
                                    ],
                                    "size": "md",
                                    "width": 4,
                                }
                            ],
                            "customClass": "ml-0 mr-0",
                            "key": "PHUONGPHAPHOISINH",
                            "type": "columns",
                        },
                        {
                            "label": "Phương pháp hồi sinh sau đẻ 2",
                            "columns": [
                                {
                                    "components": [
                                        {
                                            "label": "Đặt nội khí quản",
                                            "key": "DATNOIKHIQUAN",
                                            "type": "checkbox",
                                        }
                                    ],
                                    "width": 4,
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Bóp bóng O2",
                                            "key": "BOPBONGO2",
                                            "type": "checkbox",
                                        }
                                    ],
                                    "width": 4,
                                    "size": "md",
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Khác",
                                            "key": "PHUONGPHAPKHAC",
                                            "type": "checkbox",
                                        }
                                    ],
                                    "size": "md",
                                    "width": 4,
                                }
                            ],
                            "customClass": "ml-0 mr-0",
                            "key": "PHUONGPHAPHOISINH2",
                            "type": "columns",
                        },


                    ]
                },
                {
                    "collapsible": true,
                    "key": "p-khambenh",
                    "type": "panel",
                    "label": "Khám bệnh",
                    "title": "KHÁM BỆNH",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap",
                    "components": [
                        {
                            "html": "<p>Họ tên người chuyển sơ sinh</p>",
                            "label": "Tên người chuyển sơ sinh",
                            "refreshOnChange": false,
                            "key": "NGUOICHUYENSOSINH",
                            "type": "content",
                            "input": false,
                            "tableView": false
                        },
                        getBacsiAllKhoaFormio("MAKHOACHUYENSOSINH", "HOTENNGUOICHUYENSOSINH"),

                        {
                            "label": "Dị tật/ Có hậu môn",
                            "columns": [
                                {
                                    "components": [
                                        {
                                            "label": "Dị tật bẩm sinh",
                                            "key": "DITATBAMSINH",
                                            "type": "checkbox",
                                        }
                                    ],
                                    "width": 4,
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Có hậu môn",
                                            "key": "COHAUMON",
                                            "type": "checkbox",
                                        }
                                    ],
                                    "width": 4,
                                    "size": "md",
                                },
                            ],
                            "customClass": "ml-0 mr-0",
                            "key": "DITATHAUMON",
                            "type": "columns",
                        },
                        {
                            "label": "Cụ thể dị tật",
                            "key": "CUTHEDITAT",
                            "type": "textarea",
                            "validate": {
                                "minLength": 5,
                                "maxLength": 3000,
                                "required": true
                            },
                            others: {
                                "customConditional": "show = !!data.DITATBAMSINH;",
                            }
                        },


                        {
                            "label": "left",
                            "columns": [
                                {
                                    "components": [
                                        {
                                            "label": "Tình hình sơ sinh",
                                            others: {
                                                "tooltip": "Tình hình sơ sinh khi vào khoa",
                                            },
                                            "key": "TINHHINHSOSINHKHIVAOKHOA",
                                            "type": "textarea",
                                            "rows": 2,
                                            "input": true,
                                            "validate": {
                                                "minLength": 5,
                                                "maxLength": 3000,
                                                "required": true
                                            }
                                        },
                                        {
                                            "label": "Tình trạng toàn thân",
                                            "key": "TINHTRANGTOANTHAN",
                                            "type": "textarea",
                                            "rows": 2,
                                            "validate": {
                                                "minLength": 5,
                                                "maxLength": 3000,
                                            }
                                        },
                                        JSONRadio("Màu da", "HONGHAO", "", {
                                            others: {
                                                "optionsLabelPosition": "right",
                                                "inline": true,
                                                "labelPosition": "left-left",
                                                "optionsLabelPosition": "right",
                                                "tableView": false,
                                                "values": [
                                                    {
                                                        "label": "Hồng hào",
                                                        "value": "1",
                                                        "shortcut": ""
                                                    },
                                                    {
                                                        "label": "Xanh tái",
                                                        "value": "2",
                                                        "shortcut": ""
                                                    },
                                                    {
                                                        "label": "Vàng",
                                                        "value": "3",
                                                        "shortcut": ""
                                                    },
                                                    {
                                                        "label": "Tím",
                                                        "value": "4",
                                                        "shortcut": ""
                                                    },
                                                    {
                                                        "label": "Khác",
                                                        "value": "5",
                                                        "shortcut": ""
                                                    },
                                                ],
                                            }
                                        }),
                                        {
                                            "label": "Màu sắc da",
                                            "key": "MAUSACDA",
                                            "hideLabel": true,
                                            "type": "textarea",
                                            "rows": 2,
                                        },
                                    ],
                                    "width": 8,
                                    "offset": 0,
                                    "push": 0,
                                    "pull": 0,
                                    "size": "md",
                                    "currentWidth": 8
                                },
                                {
                                    "components": [
                                        {
                                            "label": "Tabs",
                                            "components": [
                                                {
                                                    "label": "Chỉ số sơ sanh",
                                                    "key": "sinhhieu",
                                                    "components": [
                                                        {
                                                            "label": "chisososanh1",
                                                            "columns": [
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "Cân nặng (gr)",
                                                                            "customClass": "pr-2",
                                                                            "validate": {
                                                                                "min": 0,
                                                                                required: true
                                                                            },
                                                                            "key": "CANNANG",
                                                                            "type": "number",
                                                                        }
                                                                    ],
                                                                    "width": 4,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "size": "md",
                                                                    "currentWidth": 4
                                                                },
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "Chiều dài (cm)",
                                                                            "customClass": "pr-2",
                                                                            "validate": {
                                                                                "min": 0,
                                                                                required: true
                                                                            },
                                                                            "key": "CHIEUCAO",
                                                                            "type": "number",
                                                                        }
                                                                    ],
                                                                    "width": 4,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "size": "md",
                                                                    "currentWidth": 4
                                                                },
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "Vòng đầu (cm)",
                                                                            "validate": {
                                                                                "min": 0,
                                                                                required: true
                                                                            },
                                                                            "key": "VONGDAU",
                                                                            "type": "number",
                                                                        }
                                                                    ],
                                                                    "size": "md",
                                                                    "width": 4,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "currentWidth": 4
                                                                }
                                                            ],
                                                            "customClass": "ml-0 mr-0",
                                                            "key": "chisososanh1",
                                                            "type": "columns",
                                                        },
                                                        {
                                                            "label": "chisososanh2",
                                                            "columns": [
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "Nhiệt độ",
                                                                            "customClass": "pr-2",
                                                                            "validate": {
                                                                                "min": 35,
                                                                                "max": 43,
                                                                                required: true
                                                                            },
                                                                            "key": "NHIETDO",
                                                                            "type": "number",
                                                                        }
                                                                    ],
                                                                    "width": 6,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "size": "md",
                                                                    "currentWidth": 6
                                                                },
                                                                {
                                                                    "components": [
                                                                        {
                                                                            "label": "Nhịp thở",
                                                                            "customClass": "pr-2",
                                                                            "validate": {
                                                                                "min": 0,
                                                                                "max": 200,
                                                                                required: true
                                                                            },
                                                                            "key": "NHIPTHO",
                                                                            "type": "number",
                                                                        }
                                                                    ],
                                                                    "width": 6,
                                                                    "offset": 0,
                                                                    "push": 0,
                                                                    "pull": 0,
                                                                    "size": "md",
                                                                    "currentWidth": 6
                                                                },
                                                            ],
                                                            "customClass": "ml-0 mr-0",
                                                            "key": "chisosausinh2",
                                                            "type": "columns",
                                                        },

                                                    ]
                                                }
                                            ],
                                            "customClass": "hsba-tabs-wrap pl-3",
                                            "key": "tabs",
                                            "type": "tabs",
                                        },
                                    ],
                                    "width": 4,
                                    "offset": 0,
                                    "push": 0,
                                    "pull": 0,
                                    "size": "md",
                                    customClass: "pl-2",
                                    "currentWidth": 4
                                },

                            ],
                            "customClass": "ml-0 mr-0",
                            "key": "chiso-column",
                            "type": "columns",
                            "input": false,
                            "tableView": false
                        },


                        {
                            "label": "Các cơ quan khác",
                            "key": "COQUANKHAC",
                            columns: [
                                {
                                    "components": [
                                        {
                                            "label": "Nhịp thở (lần/ phút)",
                                            "key": "NHIPTHOHOHAP",
                                            "type": "textfield",
                                            customClass: "pr-2",
                                            enableTime: true,
                                        },
                                    ],
                                    "width": 6,
                                    "size": "md",
                                },
                                {
                                    "components": [

                                        {
                                            "label": "Nhịp tim (lần/ phút)",
                                            "key": "NHIPTIM",
                                            "type": "textfield",
                                            "validate": {
                                                "required": true
                                            }
                                        },
                                    ],
                                    "width": 6,
                                    "size": "md",
                                },
                            ],
                            "customClass": "ml-0 mr-0",
                            "type": "columns",
                        },
                        {
                            "label": "Nghe phổi",
                            "key": "NGHEPHOI",
                            "type": "textarea",
                            "validate": {
                                "required": true
                            }
                        },
                        {
                            "label": "Chỉ số Silverman",
                            "key": "CHISO",
                            others: {
                                "attributes": {
                                    "readonly": "true"
                                },
                            },
                            "type": "number",
                        },
                        {
                            label: "giannolongnguc",
                            key: "colGiannolongnguc",
                            columns: [
                                {
                                    "components": [
                                        {
                                            "html": "<p>Sự giãn nở lồng ngực</p>",
                                            "label": "Giãn nở lồng ngực",
                                            "refreshOnChange": false,
                                            "key": "GIANNOLONGNGUC",
                                            "type": "content",
                                            "input": false,
                                            "tableView": false
                                        },
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [

                                        {
                                            "label": "Điều hòa",
                                            "key": "BOXGNDIEUHOA",
                                            "properties": {
                                                "value": "1123123"
                                            },
                                            "type": "checkbox",
                                            "customClass": "pr-2",
                                        }
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [

                                        {
                                            "label": "Xê dịch n.thở",
                                            "key": "BOXGNNHIPTHO",
                                            others: {
                                                "tooltip": "Xê dịch nhịp thở với di động bụng",
                                            },
                                            "type": "checkbox",
                                            "customClass": "pr-2",
                                        }
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [

                                        {
                                            "label": "Không di động",
                                            "key": "BOXGNDIDONG",
                                            others: {
                                                "tooltip": "Không di động ngực bụng",
                                            },
                                            "type": "checkbox",
                                            "customClass": "pr-2",
                                        }
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                            ],
                            "customClass": "ml-0 mr-0",
                            "type": "columns",
                        },
                        {
                            label: "cokeocoliensuon",
                            key: "colCokeocoliensuon",
                            columns: [
                                {
                                    "components": [
                                        {
                                            "html": "<p>Co kéo cơ liên sườn</p>",
                                            "label": "Co kéo cơ",
                                            "refreshOnChange": false,
                                            "key": "COKEOCOLIENSUON",
                                            "type": "content",
                                            "input": false,
                                            "tableView": false
                                        },
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [

                                        {
                                            "label": "Không",
                                            "key": "BOXGNCOKEOCO1",
                                            "type": "checkbox",
                                            "customClass": "pr-2",
                                        }
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [

                                        {
                                            "label": "Có ít",
                                            "key": "BOXGNCOKEOCO2",
                                            "type": "checkbox",
                                            "customClass": "pr-2",
                                        }
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [

                                        {
                                            "label": "Thấy rõ",
                                            "key": "BOXGNCOKEOCO3",
                                            "type": "checkbox",
                                            "customClass": "pr-2",
                                        }
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                            ],
                            "customClass": "ml-0 mr-0",
                            "type": "columns",
                        },
                        {
                            label: "cokeomuiuc",
                            key: "colCokeomuiuc",
                            columns: [
                                {
                                    "components": [
                                        {
                                            "html": "<p>Co kéo mũi ức</p>",
                                            "label": "Co kéo mũi ức",
                                            "refreshOnChange": false,
                                            "key": "COKEOMUIUC",
                                            "type": "content",
                                            "input": false,
                                            "tableView": false
                                        },
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [

                                        {
                                            "label": "Không",
                                            "key": "BOXGNCOKEOMUI1",
                                            "type": "checkbox",
                                            "customClass": "pr-2",
                                        }
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [

                                        {
                                            "label": "Có ít",
                                            "key": "BOXGNCOKEOMUI2",
                                            "type": "checkbox",
                                            "customClass": "pr-2",
                                        }
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [

                                        {
                                            "label": "Thấy rõ",
                                            "key": "BOXGNCOKEOMUI3",
                                            "type": "checkbox",
                                            "customClass": "pr-2",
                                        }
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                            ],
                            "customClass": "ml-0 mr-0",
                            "type": "columns",
                        },
                        {
                            label: "dapcanhmui",
                            key: "colDapcanhmui",
                            columns: [
                                {
                                    "components": [
                                        {
                                            "html": "<p>Đập cánh mũi</p>",
                                            "label": "Đập cánh mũi",
                                            "refreshOnChange": false,
                                            "key": "DAPCANHMUI",
                                            "type": "content",
                                            "input": false,
                                            "tableView": false
                                        },
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [

                                        {
                                            "label": "Không",
                                            "key": "BOXGNDAPCANHMUI1",
                                            "type": "checkbox",
                                            "customClass": "pr-2",
                                        }
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [

                                        {
                                            "label": "Nhẹ",
                                            "key": "BOXGNDAPCANHMUI2",
                                            "type": "checkbox",
                                            "customClass": "pr-2",
                                        }
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [

                                        {
                                            "label": "Rõ",
                                            "key": "BOXGNDAPCANHMUI3",
                                            "type": "checkbox",
                                            "customClass": "pr-2",
                                        }
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                            ],
                            "customClass": "ml-0 mr-0",
                            "type": "columns",
                        },
                        {
                            label: "renri",
                            key: "colRenri",
                            columns: [
                                {
                                    "components": [
                                        {
                                            "html": "<p>Rên rỉ</p>",
                                            "label": "Rên rỉ",
                                            "refreshOnChange": false,
                                            "key": "RENRI",
                                            "type": "content",
                                            "input": false,
                                            "tableView": false
                                        },
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [

                                        {
                                            "label": "Không",
                                            "key": "BOXGNRENRI1",
                                            "type": "checkbox",
                                            "customClass": "pr-2",
                                        }
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [

                                        {
                                            "label": "Nghe bằng ống nghe",
                                            "key": "BOXGNRENRI2",
                                            "type": "checkbox",
                                            "customClass": "pr-2",
                                        }
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                                {
                                    "components": [

                                        {
                                            "label": "Tai thường nghe rõ",
                                            "key": "BOXGNRENRI3",
                                            "type": "checkbox",
                                            "customClass": "pr-2",
                                        }
                                    ],
                                    "width": 3,
                                    "size": "md",
                                },
                            ],
                            "customClass": "ml-0 mr-0",
                            "type": "columns",
                        },


                        {
                            "label": "Bụng",
                            "key": "BUNG",
                            "type": "textarea",
                            "rows": 2,
                            "validate": {
                                "required": true
                            },
                        },
                        {
                            "label": "Cơ quan sinh dục ngoài",
                            "key": "COQUANSINHDUCNGOAI",
                            "type": "textarea",
                            "rows": 2,
                            "validate": {
                                "required": true
                            },
                        },
                        {
                            "label": "Xương khớp",
                            "key": "XUONGKHOP",
                            "type": "textarea",
                            "rows": 2,
                            "validate": {
                                "required": true
                            },
                        },
                        {
                            "label": "Phản xạ",
                            "key": "PHANXA",
                            "type": "textarea",
                            "rows": 2,
                            "validate": {
                                "required": true
                            },
                        },
                        {
                            "label": "Trương lực cơ",
                            "key": "TRUONGLUCCO",
                            "type": "textarea",
                            "rows": 2,
                            "validate": {
                                "required": true
                            },
                        },

                        {
                            "label": "Các xét nghiệm cận lâm sàng cần làm",
                            "key": "CLS",
                            "type": "textarea",
                            "rows": 2,
                            validate: {
                                required: true
                            }
                        },
                        {
                            "label": "Tóm tắt bệnh án",
                            "key": "TOMTATBENHAN",
                            "type": "textarea",
                            "rows": 2,
                            validate: {
                                required: true
                            }
                        },
                        {
                            "label": "Chỉ định theo dõi",
                            "key": "CHIDINHTHEODOI",
                            "type": "textarea",
                            "rows": 2,
                            validate: {
                                required: true
                            }
                        }
                    ]
                },

                getObjectThoigianBacsilambenhanFormio()
            ])
        },
        initObjectFormPage3: function () {
            return getJSONObjectForm([
                {
                    "collapsible": true,
                    "key": "p-tongketdieutri",
                    "type": "panel",
                    "label": "TỔNG KẾT BỆNH ÁN",
                    "title": "TỔNG KẾT BỆNH ÁN",
                    "collapsed": false,
                    "input": false,
                    "tableView": false,
                    "customClass": "hsba-tabs-wrap",
                    components: [
                        {
                            "label": "Quá trình bệnh lý và diễn biến lâm sàng",
                            "key": "quaTrinhBenhLy",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "minLength": 5,
                                "maxLength": 3000,
                                required: true,
                            }
                        },
                        {
                            "label": "Copy cận lâm sàng",
                            "customClass": "text-right form-control-sm line-height-1",
                            "key": "copytomtatcls",
                            "type": "button",
                            others: {
                                "leftIcon": "fa fa-ellipsis-v",
                                "action": "event",
                                "showValidations": false,
                                "event": "openmodalcopytomtatcls",
                                "type": "button",
                            }

                        },
                        {
                            "label": "Tóm tắt kết quả xét nghiệm cận lâm sàng có giá trị chẩn đoán",
                            "key": "tomTatKetQuaXNCLS",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "minLength": 5,
                                "maxLength": 3000,
                                required: true,
                            }
                        },
                        {
                            "label": "Phương pháp điều trị",
                            "key": "phuongPhapDieuTri",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "minLength": 5,
                                "maxLength": 3000,
                                required: true,
                            }
                        },
                        {
                            "label": "Tình trạng người bệnh ra viện",
                            "key": "tinhTrangNguoiBenhRaVien",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "minLength": 5,
                                "maxLength": 3000,
                                required: true,
                            }
                        },
                        {
                            "label": "Hướng điều trị và các chế độ tiếp theo",
                            "key": "huongDieuTriVaCacCheDo",
                            "type": "textarea",
                            rows: 2,
                            validate: {
                                "minLength": 5,
                                "maxLength": 3000,
                                required: true,
                            }
                        },
                    ]
                },
                getObjectThoigianTongketFormio()
            ]);
        },
        callbackAfterLoad: function (instance, callBack) {
            form = instance;
            initGridThongTinTreEm();
            var bacsidodeElement = form.getComponent('HOTENNGUOIDODE');
            var bacsichuyensosinhElement = form.getComponent('HOTENNGUOICHUYENSOSINH');
            var bacsilambenhanElement = form.getComponent('MABACSILAMBENHAN');

            var chisoElement = form.getComponent('CHISO');

            var boxdhElement = form.getComponent('BOXGNDIEUHOA');
            var nhipthoElement = form.getComponent('BOXGNNHIPTHO');
            var didongElement = form.getComponent('BOXGNDIDONG');
            var keoco1Element = form.getComponent('BOXGNCOKEOCO1');
            var keoco2Element = form.getComponent('BOXGNCOKEOCO2');
            var keoco3Element = form.getComponent('BOXGNCOKEOCO3');
            var keomui1Element = form.getComponent('BOXGNCOKEOMUI1');
            var keomui2Element = form.getComponent('BOXGNCOKEOMUI2');
            var keomui3Element = form.getComponent('BOXGNCOKEOMUI3');
            var canhmui1Element = form.getComponent('BOXGNDAPCANHMUI1');
            var canhmui2Element = form.getComponent('BOXGNDAPCANHMUI2');
            var canhmui3Element = form.getComponent('BOXGNDAPCANHMUI3');
            var renri1Element = form.getComponent('BOXGNRENRI1');
            var renri2Element = form.getComponent('BOXGNRENRI2');
            var renri3Element = form.getComponent('BOXGNRENRI3');
            var boxdhValue, keoco1Value, keomui1Value, canhmui1Value, renri1Value;
            var nhipthoValue, keoco2Value, keomui2Value, canhmui2Value, renri2Value;
            var didongValue, keoco3Value, keomui3Value, canhmui3Value, renri3Value;
            boxdhValue = keoco1Value = keomui1Value = canhmui1Value = renri1Value = 0;
            nhipthoValue = keoco2Value = keomui2Value = canhmui2Value = renri2Value = 1;
            didongValue = keoco3Value = keomui3Value = canhmui3Value = renri3Value = 2;

            var dataSil = form.submission.data;
            var valueSil = dataSil.CHISO ? dataSil.CHISO : "0";

            $("#" + getIdElmentFormio(form, 'MAKHOADODE')).change(function () {
                if (!$(this).val()) {
                    return;
                }
                getAllNhanvienByKhoaFormio($(this).val(), bacsidodeElement)
            })

            $("#" + getIdElmentFormio(form, 'MAKHOACHUYENSOSINH')).change(function () {
                if (!$(this).val()) {
                    return;
                }

                getAllNhanvienByKhoaFormio($(this).val(), bacsichuyensosinhElement)
            })

            $("#" + getIdElmentFormio(form, 'MAKHOA')).change(function () {
                if (!$(this).val()) {
                    return;
                }
                getBacsiByKhoaFormio($(this).val(), bacsilambenhanElement)
            })
            console.log("id", "#" + getIdElmentFormio(form, 'BOXGNDIEUHOA') + " input")
            $("#" + boxdhElement.id + " input").change(function () {
                if (boxdhElement.getValue()){
                    chisoElement.setValue(chisoElement.getValue() + boxdhValue);
                }else{
                    chisoElement.setValue(chisoElement.getValue() - boxdhValue);
                }
            })
            $("#" + nhipthoElement.id + " input").change(function () {
                if (nhipthoElement.getValue()){
                    chisoElement.setValue(chisoElement.getValue() + nhipthoValue);
                }else{
                    chisoElement.setValue(chisoElement.getValue() - nhipthoValue);
                }
            })
            $("#" + didongElement.id + " input").change(function () {
                if (didongElement.getValue()){
                    chisoElement.setValue(chisoElement.getValue() + didongValue);
                }else{
                    chisoElement.setValue(chisoElement.getValue() - didongValue);
                }
            })
            $("#" + keoco1Element.id + " input").change(function () {
                if (keoco1Element.getValue()){
                    chisoElement.setValue(chisoElement.getValue() + keoco1Value);
                }else{
                    chisoElement.setValue(chisoElement.getValue() - keoco1Value);
                }
            })
            $("#" + keoco2Element.id + " input").change(function () {
                if (keoco2Element.getValue()){
                    chisoElement.setValue(chisoElement.getValue() + keoco2Value);
                }else{
                    chisoElement.setValue(chisoElement.getValue() - keoco2Value);
                }
            })
            $("#" + keoco3Element.id + " input").change(function () {
                if (keoco3Element.getValue()){
                    chisoElement.setValue(chisoElement.getValue() + keoco3Value);
                }else{
                    chisoElement.setValue(chisoElement.getValue() - keoco3Value);
                }
            })
            $("#" + keomui1Element.id + " input").change(function () {
                if (keomui1Element.getValue()){
                    chisoElement.setValue(chisoElement.getValue() + keomui1Value);
                }else{
                    chisoElement.setValue(chisoElement.getValue() - keomui1Value);
                }
            })
            $("#" + keomui2Element.id + " input").change(function () {
                if (keomui2Element.getValue()){
                    chisoElement.setValue(chisoElement.getValue() + keomui2Value);
                }else{
                    chisoElement.setValue(chisoElement.getValue() - keomui2Value);
                }
            })
            $("#" + keomui3Element.id + " input").change(function () {
                if (keomui3Element.getValue()){
                    chisoElement.setValue(chisoElement.getValue() + keomui3Value);
                }else{
                    chisoElement.setValue(chisoElement.getValue() - keomui3Value);
                }
            })
            $("#" + canhmui1Element.id + " input").change(function () {
                if (canhmui1Element.getValue()){
                    chisoElement.setValue(chisoElement.getValue() + canhmui1Value);
                }else{
                    chisoElement.setValue(chisoElement.getValue() - canhmui1Value);
                }
            })
            $("#" + canhmui2Element.id + " input").change(function () {
                if (canhmui2Element.getValue()){
                    chisoElement.setValue(chisoElement.getValue() + canhmui2Value);
                }else{
                    chisoElement.setValue(chisoElement.getValue() - canhmui2Value);
                }
            })
            $("#" + canhmui3Element.id + " input").change(function () {
                if (canhmui3Element.getValue()){
                    chisoElement.setValue(chisoElement.getValue() + canhmui3Value);
                }else{
                    chisoElement.setValue(chisoElement.getValue() - canhmui3Value);
                }
            })
            $("#" + renri1Element.id + " input").change(function () {
                if (renri1Element.getValue()){
                    chisoElement.setValue(chisoElement.getValue() + renri1Value);
                }else{
                    chisoElement.setValue(chisoElement.getValue() - renri1Value);
                }
            })
            $("#" + renri2Element.id + " input").change(function () {
                if (renri2Element.getValue()){
                    chisoElement.setValue(chisoElement.getValue() + renri2Value);
                }else{
                    chisoElement.setValue(chisoElement.getValue() - renri2Value);
                }
            })
            $("#" + renri3Element.id + " input").change(function () {
                if (renri3Element.getValue()){
                    chisoElement.setValue(chisoElement.getValue() + renri3Value);
                }else{
                    chisoElement.setValue(chisoElement.getValue() - renri3Value);
                }
            })

            var idWrap = "hsba_vba_trang2-tab";
            showLoaderIntoWrapId(idWrap)
            getThongtinBenhan(thongtinhsba.thongtinbn.VOBENHAN[0].ID, 'SOSINH', function (dataTrang2) {
                hideLoaderIntoWrapId(idWrap)
                delete dataTrang2.ID;
                instance.on('layThongTinDacDiemConAction', function(click) {
                    reloadDSThongTinTreSoSinh(dataTrang2.SOBENHAN_HOSOME, dataTrang2.SOVAOVIEN_ME);
                    $("#modalFormThongTinTreSoSinhBenhAnSoSinh").modal("show");
                });
                dataTrang2.HUTDICH = dataTrang2.HUTDICH == "X";
                dataTrang2.XOABOPTIM = dataTrang2.XOABOPTIM == "X";
                dataTrang2.THOO2 = dataTrang2.THOO2 == "X";
                dataTrang2.DATNOIKHIQUAN = dataTrang2.DATNOIKHIQUAN == "X";
                dataTrang2.BOPBONGO2 = dataTrang2.BOPBONGO2 == "X";
                dataTrang2.PHUONGPHAPKHAC = dataTrang2.PHUONGPHAPKHAC == "X";

                dataTrang2.DITATBAMSINH = dataTrang2.DITATBAMSINH == "X";
                dataTrang2.COHAUMON = dataTrang2.COHAUMON == "X";
                dataTrang2.BOXNGOAIVIEN = dataTrang2.BOXNGOAIVIEN == "X";

                dataTrang2.OIVONGAY = dataTrang2.OIVONGAY ? moment(dataTrang2.OIVONGAY, ['DD/MM/YYYY HH:mm']).toISOString() : "";
                dataTrang2.DENGAY = dataTrang2.DENGAY ? moment(dataTrang2.DENGAY, ['DD/MM/YYYY HH:mm']).toISOString() : "";
                dataTrang2.HOTENNGUOIDODE = dataTrang2.HOTENNGUOIDODE ? dataTrang2.HOTENNGUOIDODE.split(" - ")[0] : "";
                dataTrang2.HOTENNGUOICHUYENSOSINH = dataTrang2.HOTENNGUOICHUYENSOSINH ? dataTrang2.HOTENNGUOICHUYENSOSINH.split(" - ")[0] : "";

                dataTrang2.MAKHOADODE = dataTrang2.MAKHOADODE ? dataTrang2.MAKHOADODE : singletonObject.makhoa;
                dataTrang2.MAKHOACHUYENSOSINH = dataTrang2.MAKHOACHUYENSOSINH ? dataTrang2.MAKHOACHUYENSOSINH : singletonObject.makhoa;
                dataTrang2.MAKHOA = dataTrang2.MAKHOA ? dataTrang2.MAKHOA : singletonObject.makhoa;

                if (dataTrang2.SILVERMAN_SCORE){
                    console.log(dataTrang2.SILVERMAN_SCORE)
                    var tesst = JSON.parse(dataTrang2.SILVERMAN_SCORE)
                    dataTrang2.BOXGNDIEUHOA = tesst.BOXGNDIEUHOA;
                    dataTrang2.BOXGNNHIPTHO = tesst.BOXGNNHIPTHO;
                    dataTrang2.BOXGNDIDONG = tesst.BOXGNDIDONG;
                    dataTrang2.BOXGNCOKEOCO1 = tesst.BOXGNCOKEOCO1;
                    dataTrang2.BOXGNCOKEOCO2 = tesst.BOXGNCOKEOCO2;
                    dataTrang2.BOXGNCOKEOCO3 = tesst.BOXGNCOKEOCO3;
                    dataTrang2.BOXGNCOKEOMUI1 = tesst.BOXGNCOKEOMUI1;
                    dataTrang2.BOXGNCOKEOMUI2 = tesst.BOXGNCOKEOMUI2;
                    dataTrang2.BOXGNCOKEOMUI3 = tesst.BOXGNCOKEOMUI3;
                    dataTrang2.BOXGNDAPCANHMUI1 = tesst.BOXGNDAPCANHMUI1;
                    dataTrang2.BOXGNDAPCANHMUI2 = tesst.BOXGNDAPCANHMUI2;
                    dataTrang2.BOXGNDAPCANHMUI3 = tesst.BOXGNDAPCANHMUI3;
                    dataTrang2.BOXGNRENRI1 = tesst.BOXGNRENRI1;
                    dataTrang2.BOXGNRENRI2 = tesst.BOXGNRENRI2;
                    dataTrang2.BOXGNRENRI3 = tesst.BOXGNRENRI3;
                }

                dataTrang2.CHISO = dataTrang2.CHISO;
                dataTrang2.NGAYBSLAMBENHAN = (dataTrang2.NGAYBSLAMBENHAN ? moment(dataTrang2.NGAYBSLAMBENHAN) : moment()).toISOString()
                getAllNhanvienByKhoaFormio(dataTrang2.MAKHOADODE, bacsidodeElement);
                getAllNhanvienByKhoaFormio(dataTrang2.MAKHOACHUYENSOSINH, bacsichuyensosinhElement);
                getBacsiByKhoaFormio(dataTrang2.MAKHOA, bacsilambenhanElement);

                var dataCallBack = form.submission =  {
                    data: {
                        ...dataTrang2
                    }
                };
                callBack && callBack(dataCallBack);
            }, function () {
                hideLoaderIntoWrapId(idWrap)
                notifiToClient("Red", "Lỗi load thông tin bệnh án")
            });
        },
        save: function (element, callBackSave, callBackLog) {
            var idButton = element.id;
            form.emit("checkValidity");
            if (!form.checkValidity(null, false, null, true)) {
                hideSelfLoading(idButton);
                return;
            }
            var dataSubmit = form.submission.data;
            delete dataSubmit.copyclstdt
            dataSubmit.ID = thongtinhsba.thongtinbn.VOBENHAN[0].ID;
            var chiso2 = '{\"BOXGNDIEUHOA\":' + dataSubmit.BOXGNDIEUHOA +',\"BOXGNCOKEOCO1\":' + dataSubmit.BOXGNCOKEOCO1 +',\"BOXGNCOKEOMUI1\":' + dataSubmit.BOXGNCOKEOMUI1 +',\"BOXGNDAPCANHMUI1\":' + dataSubmit.BOXGNDAPCANHMUI1 +',\"BOXGNRENRI1\":' + dataSubmit.BOXGNRENRI1 +',\"BOXGNNHIPTHO\":' + dataSubmit.BOXGNNHIPTHO +',\"BOXGNCOKEOCO2\":' + dataSubmit.BOXGNCOKEOCO2 +',\"BOXGNCOKEOMUI2\":' + dataSubmit.BOXGNCOKEOMUI2 +',\"BOXGNDAPCANHMUI2\":' + dataSubmit.BOXGNDAPCANHMUI2 +',\"BOXGNRENRI2\":' + dataSubmit.BOXGNRENRI2 +',\"BOXGNDIDONG\":' + dataSubmit.BOXGNDIDONG +',\"BOXGNCOKEOCO3\":' + dataSubmit.BOXGNCOKEOCO3 +',\"BOXGNCOKEOMUI3\":' + dataSubmit.BOXGNCOKEOMUI3 +',\"BOXGNDAPCANHMUI3\":' + dataSubmit.BOXGNDAPCANHMUI3 +',\"BOXGNRENRI3\":' + dataSubmit.BOXGNRENRI3 +'}'

            var ngayba = moment(dataSubmit.NGAYBSLAMBENHAN)
            dataSubmit.HOTENNGUOIDODE = getTextSelectedFormio(form.getComponent('HOTENNGUOIDODE'));
            dataSubmit.HOTENNGUOICHUYENSOSINH = getTextSelectedFormio(form.getComponent('HOTENNGUOICHUYENSOSINH'));
            dataSubmit.NGAYLAMBENHAN = "Ngày " + ngayba.format("DD") + " tháng " + ngayba.format("MM") + " năm " + ngayba.format("YYYY");
            dataSubmit.BACSILAMBENHAN = getTextSelectedFormio(form.getComponent('MABACSILAMBENHAN')).split(" - ")[1];

            var jsonData = {
                id: dataSubmit.ID,
                lyDoVaoVien: dataSubmit.LYDOVAOVIEN,
                hoiBenh: dataSubmit.HOIBENH,
                oiVoNgay: dataSubmit.OIVONGAY ? moment(dataSubmit.OIVONGAY).format("DD/MM/YYYY HH:mm") : "",
                mauSac: dataSubmit.MAUSAC,
                deThuong: dataSubmit.DETHUONG,
                deNgay: dataSubmit.DENGAY ? moment(dataSubmit.DENGAY).format("DD/MM/YYYY HH:mm") : "",
                lyDoCanThiep: dataSubmit.LYDOCANTHIEP,
                raDoiKhocNgay: dataSubmit.RADOIKHOCNGAY,
                hoTenNguoiDoDe: dataSubmit.HOTENNGUOIDODE,
                apgar1Phut: dataSubmit.APGAR1PHUT,
                apgar5Phut: dataSubmit.APGAR5PHUT,
                apgar10Phut: dataSubmit.APGAR10PHUT,
                apgarCanNang: dataSubmit.APGARCANNANG,
                tinhTrangDinhDuongSauSinh: dataSubmit.TINHTRANGDINHDUONGSAUSINH,
                hutDich: dataSubmit.HUTDICH ? "X" : "",
                datNoiKhiQuan: dataSubmit.DATNOIKHIQUAN ? "X" : "",
                xoaBopTim: dataSubmit.XOABOPTIM ? "X" : "",
                bopBongO2: dataSubmit.BOPBONGO2 ? "X" : "",
                thoO2: dataSubmit.THOO2 ? "X" : "",
                phuongPhapKhac: dataSubmit.PHUONGPHAPKHAC ? "X" : "",
                hoTenNguoiChuyenSoSinh: dataSubmit.HOTENNGUOICHUYENSOSINH,
                diTatBamSinh: dataSubmit.DITATBAMSINH ? "X" : "",
                coHauMon: dataSubmit.COHAUMON ? "X" : "",
                cuTheDiTat: dataSubmit.CUTHEDITAT,
                tinhHinhSoSinhKhiVaoKhoa: dataSubmit.TINHHINHSOSINHKHIVAOKHOA,
                canNang: dataSubmit.CANNANG,
                chieuCao: dataSubmit.CHIEUCAO,
                vongDau: dataSubmit.VONGDAU,
                tinhTrangToanThan: dataSubmit.TINHTRANGTOANTHAN,
                nhietDo: dataSubmit.NHIETDO,
                nhipTho: dataSubmit.NHIPTHO,
                hongHao: dataSubmit.HONGHAO,
                mauSacDa: dataSubmit.MAUSACDA,
                nhipThoHoHap: dataSubmit.NHIPTHOHOHAP,
                nghePhoi: dataSubmit.NGHEPHOI,
                nhipTim: dataSubmit.NHIPTIM,
                bung: dataSubmit.BUNG,
                coQuanSinhDucNgoai: dataSubmit.COQUANSINHDUCNGOAI,
                xuongKhop: dataSubmit.XUONGKHOP,
                phanXa: dataSubmit.PHANXA,
                truongLucCo: dataSubmit.TRUONGLUCCO,
                cls: dataSubmit.CLS,
                tomtatBenhAn: dataSubmit.TOMTATBENHAN,
                chiDinhTheoDoi: dataSubmit.CHIDINHTHEODOI,
                ngay    : dataSubmit.NGAYLAMBENHAN,
                hoTenBacSi   : dataSubmit.BACSILAMBENHAN,
                chiSo   : dataSubmit.CHISO,
                silvermanScore   : chiso2,
                boxNgoaiVien: dataSubmit.BOXNGOAIVIEN ? "X" : "",
                tinhTrangOi: dataSubmit.TINHTRANGOI,
                tinhTrangOiCon: dataSubmit.TINHTRANGOI_CON,
                tinhTrangOiVo: dataSubmit.TINHTRANGOI_VO,

            };

            $.ajax({
                type: "POST",
                url: "BenhAn_SoSinh_Insert",
                dataType: "json",
                contentType: 'application/json',
                data: JSON.stringify(jsonData),
                success: function (data) {
                    if (data.SUCCESS == 1) {
                        !callBackSave && notifiToClient("Green", "Lưu thành công");
                        updateNgaylamVaBSHSBA({
                            ...dataSubmit,
                            NGAYBA: ngayba.format("DD/MM/YYYY"),
                        }, function () {
                            $.post("cmu_post", {
                                url: [
                                    dataSubmit.ID,
                                    dataSubmit.TINHTRANGOI,
                                    dataSubmit.TINHTRANGOI_CON ,
                                    dataSubmit.TINHTRANGOI_VO,
                                    dataSubmit.MAKHOADODE,
                                    dataSubmit.MAKHOACHUYENSOSINH,
                                    "UPDATE_OIVO_BASOSINH"
                                ].join("```")
                            }).done(function(data) {
                                console.log(data)
                            }).fail(function() {
                                notifiToClient("Red", "Lỗi dữ liệu")
                            });
                            var dsBacSi = singletonObject.danhsachnhanvien;
                            callBackSave && callBackSave({keyword: "Bác sỹ làm bệnh án"});
                            dataOld.NGAYBSLAMBENHAN = moment(dataOld.NGAYBSLAMBENHAN).format("DD/MM/YYYY HH:mm");
                            dataOld.NGAYLAMBENHAN = dataOld.NGAY_LAMBENHAN;
                            dataOld.BACSILAMBENHAN = dataOld.NV_LAMBENHAN;
                            dataOld.HOTENNGUOIDODE = getTenBacSiLog(dsBacSi, dataOld.HOTENNGUOIDODE)
                            dataOld.HOTENNGUOICHUYENSOSINH = getTenBacSiLog(dsBacSi, dataOld.HOTENNGUOICHUYENSOSINH)
                            dataSubmit.HOTENNGUOIDODE = getTenBacSiLog(dsBacSi, dataSubmit.HOTENNGUOIDODE)
                            dataSubmit.HOTENNGUOICHUYENSOSINH = getTenBacSiLog(dsBacSi, dataSubmit.HOTENNGUOICHUYENSOSINH)
                            callBackLog && callBackLog(dataSubmit);
                        });
                    } else {
                        notifiToClient("Red", "Lỗi lưu thông tin")

                    }
                    hideSelfLoading(idButton);
                },
                error: function (error) {
                    notifiToClient("Red", "Lỗi lưu thông tin")
                    hideSelfLoading(idButton);
                }
            });
        },
        callbackAfterLoadTongket: function (instance) {
            formTongket = instance;
            var bacsiketthucBAElement = formTongket.getComponent('MABACSIDIEUTRI');
            var idWrap = "hsba_vba_trang3-tab";
            $("#" + getIdElmentFormio(formTongket, 'MAKHOA_KETHUC')).change(function () {
                if (!$(this).val()) {
                    return;
                }
                getBacsiByKhoaFormio($(this).val(), bacsiketthucBAElement)
            })
            instance.on('openmodalcopytomtatcls', function(click) {
                addTextTitleModal("titleModalTomtatketCLSDieutri")
                $("#modalTomtatketCLSDieutri").modal("show");
                $(document).trigger("reloadDSTomtatCLS");
                $("#tomtatketCLSDieutriTabs").attr("data-function-copy", "copyTomtatKetquaCLSPage3")
            });
            showLoaderIntoWrapId(idWrap)
            getThongtinTongket(thongtinhsba.thongtinbn.VOBENHAN[0].ID, 'SOSINH', function (dataTrang3) {
                hideLoaderIntoWrapId(idWrap)
                formTongket.getComponent('MANHANVIEN_GIAOHOSO', function(component) {
                    danhsachnhanvien = component.component.data.values;
                });
                var NGUOIGIAO_HOSO = danhsachnhanvien.find(opt => opt.label.includes(dataTrang3.NGUOIGIAO_HOSO));
                var NGUOINHAN_HOSO = danhsachnhanvien.find(opt => opt.label.includes(dataTrang3.NGUOINHAN_HOSO));
                dataTrang3.MANHANVIEN_GIAOHOSO = NGUOIGIAO_HOSO ? NGUOIGIAO_HOSO.value: "";
                dataTrang3.MANHANVIEN_NHANHOSO = NGUOINHAN_HOSO ? NGUOINHAN_HOSO.value: "";
                dataTrang3.MAKHOA_KETHUC = dataTrang3.MAKHOA_KETHUC ? dataTrang3.MAKHOA_KETHUC : singletonObject.makhoa;
                formTongket.submission = {
                    data: {
                        ...dataTrang3,
                        quaTrinhBenhLy: dataTrang3.QUATRINH_BENHLY,
                        tomTatKetQuaXNCLS: dataTrang3.TOMTAT_KETQUA,
                        phuongPhapDieuTri: dataTrang3.PHUONGPHAP_DIEUTRI,
                        tinhTrangNguoiBenhRaVien: dataTrang3.TINHTRANG_RAVIEN,
                        huongDieuTriVaCacCheDo: dataTrang3.HUONG_DIEUTRI,
                        soToXQuang: dataTrang3.SOTO_XQUANG,
                        soToCTScanner: dataTrang3.SOTO_CTSCANNER,
                        soToSieuAm: dataTrang3.SOTO_SIEUAM,
                        soToXetNghiem: dataTrang3.SOTO_XETNGHIEM,
                        soToKhac: dataTrang3.SOTO_KHAC,
                        toanBoHoSo: dataTrang3.SOTO_TOANBOHS,
                        loaiGiayToKhac: dataTrang3.LOAI_GIAYTO_KHAC,
                        MAKHOA_KETHUC: dataTrang3.MAKHOA_KETHUC,
                        MABACSIDIEUTRI: dataTrang3.MABACSIDIEUTRI,
                        NGAY_TONGKET: (dataTrang3.NGAY_TONGKET_DATETIME ? moment(dataTrang3.NGAY_TONGKET_DATETIME, ['DD/MM/YYYY HH:mm']) : moment()).toISOString(),
                    }
                };
                getBacsiByKhoaFormio(dataTrang3.MAKHOA_KETHUC, bacsiketthucBAElement);

            }, function () {
                hideLoaderIntoWrapId(idWrap)
                notifiToClient("Red", "Lỗi load thông tin bệnh án")
            });
        },
        saveTongket: function (element, callBackSave) {
            var idButton = element.id;
            formTongket.emit("checkValidity");
            if (!formTongket.checkValidity(null, false, null, true)) {
                hideSelfLoading(idButton);
                return;
            }
            var dataSubmit = formTongket.submission.data;
            dataSubmit.id = thongtinhsba.thongtinbn.VOBENHAN[0].ID;
            dataSubmit.chiTietThuThuatPhauThuat = "";
            dataSubmit.giaiPhauBenh = "";
            dataSubmit.thuThuatPhauThuat = "";
            dataSubmit.benh = "";
            var ngayba = moment(dataSubmit.NGAY_TONGKET)
            dataSubmit.ngayTongKet = ngayba.format("DD/MM/YYYY HH:mm");
            dataSubmit.bacSiDieuTri = getTextSelectedFormio(formTongket.getComponent('MABACSIDIEUTRI')).split(" - ")[1];
            dataSubmit.nguoiGiaoHoSo = getTextSelectedFormio(formTongket.getComponent('MANHANVIEN_GIAOHOSO'));
            dataSubmit.nguoiNhanHoSo = getTextSelectedFormio(formTongket.getComponent('MANHANVIEN_NHANHOSO'));
            $.ajax({
                url: "TongKetBenhAn_Update",
                type: 'POST',
                data: JSON.stringify(dataSubmit),
                contentType: 'application/json',
                success: function (data) {
                    if (data.SUCCESS == 1) {
                        !callBackSave && notifiToClient("Green", "Lưu thành công")
                        updateThongtinPage3(dataSubmit, function () {
                            callBackSave && callBackSave({keyword: "Bác sĩ điều trị"});
                        });
                        !callBackSave && hideSelfLoading(idButton);
                        var Logbandau = []
                        var Logmoi = []
                        var newdata = {};
                        var dataSubmitnew = formTongket.submission.data;

                        dataSubmitnew.BACSIDIEUTRI = dataSubmitnew.bacSiDieuTri;
                        dataSubmitnew.BENH = dataSubmitnew.benh;
                        dataSubmitnew.CHITIET_THUTHUAT_PHAUTHUAT = dataSubmitnew.chiTietThuThuatPhauThuat;
                        dataSubmitnew.GIAIPHAU_BENH = dataSubmitnew.giaiPhauBenh;
                        dataSubmitnew.HUONG_DIEUTRI = dataSubmitnew.huongDieuTriVaCacCheDo;
                        // dataSubmitnew.ID = dataSubmitnew.id;
                        dataSubmitnew.LOAI_GIAYTO_KHAC = dataSubmitnew.loaiGiayToKhac;
                        var ngaytknew = moment(dataSubmitnew.NGAY_TONGKET)
                        dataSubmitnew.NGAY_TONGKET = "Ngày " + ngaytknew.format("DD") + " tháng " + ngaytknew.format("MM") + " năm " + ngaytknew.format("YYYY");
                        dataSubmitnew.NGUOIGIAO_HOSO = dataSubmitnew.nguoiGiaoHoSo;
                        dataSubmitnew.NGUOINHAN_HOSO = dataSubmitnew.nguoiNhanHoSo;
                        dataSubmitnew.PHUONGPHAP_DIEUTRI = dataSubmitnew.phuongPhapDieuTri;
                        dataSubmitnew.QUATRINH_BENHLY = dataSubmitnew.quaTrinhBenhLy;
                        dataSubmitnew.SOTO_CTSCANNER = dataSubmitnew.soToCTScanner;
                        dataSubmitnew.SOTO_KHAC = dataSubmitnew.soToKhac;
                        dataSubmitnew.SOTO_SIEUAM = dataSubmitnew.soToSieuAm;
                        dataSubmitnew.SOTO_XQUANG = dataSubmitnew.soToXQuang;
                        dataSubmitnew.SOTO_XETNGHIEM = dataSubmitnew.soToXetNghiem;
                        dataSubmitnew.THUTHUAT_PHAUTHUAT = dataSubmitnew.thuThuatPhauThuat;
                        dataSubmitnew.TINHTRANG_RAVIEN = dataSubmitnew.tinhTrangNguoiBenhRaVien;
                        dataSubmitnew.SOTO_TOANBOHS = dataSubmitnew.toanBoHoSo;
                        dataSubmitnew.TOMTAT_KETQUA = dataSubmitnew.tomTatKetQuaXNCLS;
                        // dataSubmitnew.NGAY_TONGKET_DATE = dataSubmitnew.ngayTongKet;
                        // dataSubmit.ngayTongKet =  ngayba.format("DD/MM/YYYY HH:mm");

                        assignNonNullValuesBA(newdata,dataSubmitnew);
                        var diffObject = findDifferencesBetweenObjects(oldDataTrang3, newdata);
                        for (let key in diffObject) {
                            if (keyLuuLogTrang3.hasOwnProperty(key)) {
                                Logbandau.push(getLabelValueBATrang3(key, oldDataTrang3))
                                Logmoi.push(getLabelValueBATrang3(key, newdata))
                            }
                        }
                        if (Logbandau.length != 0 || Logmoi.length != 0){
                            luuLogHSBATheoBN({
                                SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                LOAI: LOGHSBALOAI.SOSINH.KEY,
                                NOIDUNGBANDAU: Logbandau.join(";"),
                                NOIDUNGMOI: Logmoi.join(";"),
                                USERID: singletonObject.userId,
                                ACTION: LOGHSBAACTION.EDIT.KEY,
                            })
                        }
                    } else {
                        notifiToClient("Red", "Lưu thông tin bệnh án không thành công")
                    }
                    hideSelfLoading(idButton);
                },
                error: function (error) {
                    notifiToClient("Red", "Lỗi lưu thông tin")
                    hideSelfLoading(idButton);
                }
            })

        },
        callbackAfterLoadPage1: function (instance, callBack) {
            formPage1 = instance;
            var idWrap = "hsba_vba_trang1-tab";
            showLoaderIntoWrapId(idWrap);
            var dataTrang1 = thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1.INFO;
            const promises = [
                actionLoadObjectQuanLyNguoiBenhVBAT1_1(formPage1, dataTrang1),
                actionLoadObjectChanDoanVBAT1_2(formPage1, dataTrang1),
                actionLoadObjectTinhTrangRaVienVBAT1_1(formPage1, dataTrang1)
            ];
            Promise.all(promises)
                .then(results => {
                    var dataCallBack = formPage1.submission =  {
                        data: {
                            ...dataTrang1
                        }
                    };
                    callBack && callBack(dataCallBack);
                    hideLoaderIntoWrapId(idWrap)
                })
                .catch(error => {
                    console.error("An error occurred:", error);
                    hideLoaderIntoWrapId(idWrap);
                });
        },
        savePage1: function(element, callBackLog) {
            var idButton = element.id;
            formPage1.emit("checkValidity");
            if (!formPage1.checkValidity(null, false, null, true)) {
                hideSelfLoading(idButton);
                return;
            }
            var dataSubmit = formPage1.submission.data;
            var dataTrang1 = thongtinhsba.thongtinbn.VOBENHAN[0].TRANG1;
            var tthcCha = {
                hoTenCha: dataSubmit.HOTENCHA,
                ngaySinhCuaCha: dataSubmit.NGAYSINH_CUACHA ? moment(dataSubmit.NGAYSINH_CUACHA).format('YYYY-MM-DD') : '',
                maNgheNghiepCuaCha: dataSubmit.MA_NGHENGHIEP_CUACHA,
                ngheNghiepCuaCha: getTenNgheNghiep(dataSubmit.MA_NGHENGHIEP_CUACHA)
            };
            var tthcMe = {
                hoTenMe: dataSubmit.HOTENME,
                ngaySinhCuaMe: dataSubmit.NGAYSINHCUAME ? moment(dataSubmit.NGAYSINHCUAME).format('YYYY-MM-DD') : '',
                maNgheNghiepCuaMe: dataSubmit.MA_NGHENGHIEP_CUAME,
                ngheNghiepCuaMe: getTenNgheNghiep(dataSubmit.MA_NGHENGHIEP_CUAME),
                nhomMauMe: dataSubmit.NHOMMAU_CUAME,
                soLanDe: dataSubmit.SOLANDE,
                tienThaiPara: dataSubmit.TIENTHAI_PARA ? dataSubmit.TIENTHAI_PARA.split('-').map(s => s.trim()).join('-') : ''
            };

            dataTrang1.INFO.CHUYENKHOASONGAY0 = dataSubmit.CHUYENKHOASONGAY0;
            dataTrang1.INFO.CHUYENKHOATHOIGIAN0 = moment(dataSubmit.CHUYENKHOATHOIGIAN0).format("DD/MM/YYYY HH:mm:ss");
            dataTrang1.INFO.ICD_KHOADT = dataSubmit.ICD_KHOADT;
            dataTrang1.INFO.ICD_KHOADT_TEN = dataSubmit.ICD_KHOADT_TEN;

            // Thêm
            dataTrang1.INFO.NOIGIOITHIEU = dataSubmit.NOIGIOITHIEU;
            dataTrang1.INFO.VAOVIENLANTHU = dataSubmit.VAOVIENLANTHU;
            dataTrang1.INFO.TAIBIEN_BIENCHUNG = dataSubmit.TAIBIEN_BIENCHUNG;
            dataTrang1.INFO.THUTHUAT_PHAUTHUAT = dataSubmit.THUTHUAT_PHAUTHUAT
            dataTrang1.INFO.GIAIPHAUBENH = dataSubmit.GIAIPHAUBENH;
            dataTrang1.INFO.NN_TUVONG = dataSubmit.NN_TUVONG;
            dataTrang1.INFO.KHOANGTG_TUVONG = dataSubmit.KHOANGTG_TUVONG;
            dataTrang1.INFO.KHAMNGHIEM = dataSubmit.KHAMNGHIEM == true ? 1 : 0;
            dataTrang1.INFO.ICD_GIAIPHAU = dataSubmit.ICD_GIAIPHAU;
            dataTrang1.INFO.TEN_ICD_GIAIPHAU = "";

            dataTrang1.INFO.TTHC_CHA = JSON.stringify(tthcCha);
            dataTrang1.INFO.TTHC_ME = JSON.stringify(tthcMe);

            luuThongTinVBATrang1();

            dataOld.KHAMNGHIEM ? dataOld.KHAMNGHIEM = "Có" : dataOld.KHAMNGHIEM = "Không"
            dataOld.GIAIPHAUBENH == 1 ? dataOld.GIAIPHAUBENH = "Lành tính" : (dataOld.GIAIPHAUBENH == 2 ? dataOld.GIAIPHAUBENH = "Nghi ngờ" : (dataOld.GIAIPHAUBENH == 3 ? dataOld.GIAIPHAUBENH = "Ác tính" : ""))
            dataOld.NN_TUVONG == 1 ? dataOld.NN_TUVONG = "Do bệnh" : (dataOld.NN_TUVONG == 2 ? dataOld.NN_TUVONG = "Do tai biến điều trị" : (dataOld.NN_TUVONG == 3 ? dataOld.NN_TUVONG = "Khác" : ""))
            dataOld.KHOANGTG_TUVONG == 1 ? dataOld.KHOANGTG_TUVONG = "Trong 24 giờ vào viện" : (dataOld.KHOANGTG_TUVONG == 2 ? dataOld.KHOANGTG_TUVONG = "Sau 24 giờ vào viện" : "")
            dataOld.TAIBIEN_BIENCHUNG == 1 ? dataOld.TAIBIEN_BIENCHUNG = "Tai biến" : (dataOld.TAIBIEN_BIENCHUNG == 2 ? dataOld.TAIBIEN_BIENCHUNG = "Biến chứng" : "")
            dataSubmit.TAIBIEN_BIENCHUNG == 1 ? dataSubmit.TAIBIEN_BIENCHUNG = "Tai biến" : (dataSubmit.TAIBIEN_BIENCHUNG == 2 ? dataSubmit.TAIBIEN_BIENCHUNG = "Biến chứng" : "")
            dataSubmit.KHAMNGHIEM ? dataSubmit.KHAMNGHIEM = "Có" : dataSubmit.KHAMNGHIEM = "Không"
            dataSubmit.GIAIPHAUBENH == 1 ? dataSubmit.GIAIPHAUBENH = "Lành tính" : (dataSubmit.GIAIPHAUBENH == 2 ? dataSubmit.GIAIPHAUBENH = "Nghi ngờ" : (dataSubmit.GIAIPHAUBENH == 3 ? dataSubmit.GIAIPHAUBENH = "Ác tính" : ""))
            dataSubmit.NN_TUVONG == 1 ? dataSubmit.NN_TUVONG = "Khác" : (dataSubmit.NN_TUVONG == 2 ? dataSubmit.NN_TUVONG = "Do tai biến điều trị" : (dataSubmit.NN_TUVONG == 3 ? dataSubmit.NN_TUVONG = "Khác" : ""))
            dataSubmit.KHOANGTG_TUVONG == 1 ? dataSubmit.KHOANGTG_TUVONG = "Trong 24 giờ vào viện" : (dataSubmit.KHOANGTG_TUVONG == 2 ? dataSubmit.KHOANGTG_TUVONG = "Sau 24 giờ vào viện" : "")
            dataSubmit.CHUYENKHOATHOIGIAN0 = dataSubmit.CHUYENKHOATHOIGIAN0? moment(dataSubmit.CHUYENKHOATHOIGIAN0).format("DD/MM/YYYY HH:mm:ss"): "";
            callBackLog && callBackLog(dataSubmit);

            reloadFormVBAPage1(0, 0, idButton);
        },
        saveThongtinHC: function (element) {
            var idButton = element.id;
            showSelfLoading(idButton);
            var dataSubmit = convertDataFormToJson("formHsbatthcqlnb");
            updateQuanlynbvaChandoan(dataSubmit, function () {
                hideSelfLoading(idButton);
                notifiToClient("Green", "Lưu thành công")
            }, function () {
                hideSelfLoading(idButton);
                notifiToClient("Red", "Lỗi lưu thông tin")
            });
        },
        loadThongtinPage1: function () {
            var idWrap = "hsba_vba_trang1-tab";
            showLoaderIntoWrapId(idWrap)
            getThongtinPage1Benhan(thongtinhsba.thongtinbn.VOBENHAN[0].ID, function (response) {

                hideLoaderIntoWrapId(idWrap)
            }, function () {
                hideLoaderIntoWrapId(idWrap)
                notifiToClient("Red", "Lỗi load thông tin")
            });
        },
        copyChidinhCLS: function (cls) {
            console.log("form.submission", form.submission)
            form.submission = {
                data: {
                    ...form.submission.data,
                    CLS: cls
                }
            }
        },
        getInfoMauHSBA: function() {
            this.extendFunctionMau();
            return {
                keyMauHSBA: keyMauHSBASOSINH,
                insertMau: "insertMauHSBASOSINH",
                editMau: "editMauHSBASOSINH",
                selectMau: "selectMauHSBASOSINH",
                getdataMau: "getdataMauHSBASOSINH",
                formioValidate: "formioHSBASOSINHValidate",
            };
        },
        extendFunctionMau: function() {
            var self = this
            $.extend({
                insertMauHSBASOSINH: function () {
                    self.generateFormMauHSBA({})
                },
                editMauHSBASOSINH: function (rowSelect) {
                    var json = JSON.parse(rowSelect.NOIDUNG);
                    var dataMau = {}
                    json.forEach(function(item) {
                        dataMau[item.key] = item.value
                    })

                    self.generateFormMauHSBA({
                        ID: rowSelect.ID,
                        TENMAU: rowSelect.TENMAU,
                        ...dataMau
                    })
                },
                selectMauHSBASOSINH: function (rowSelect) {
                    var json = JSON.parse(rowSelect.NOIDUNG);
                    var dataMau = {
                        ...form.submission.data,
                    }
                    json.forEach(function(item) {
                        dataMau[item.key] = item.value
                    })
                    form.submission = {
                        data: {
                            ...dataMau
                        }
                    }
                    $("#modalMauChungJSON").modal("hide");
                },
                getdataMauHSBASOSINH: function () {
                    var objectNoidung = [];
                    self.getObjectMauHSBA().forEach(function(item) {
                        if(item.key != 'ID' && item.key != 'TENMAU') {
                            objectNoidung.push({
                                "label": item.label,
                                "value": formioMauHSBA.submission.data[item.key],
                                "key": item.key,
                            })
                        }
                    })
                    return {
                        ID: formioMauHSBA.submission.data.ID,
                        TENMAU: formioMauHSBA.submission.data.TENMAU,
                        NOIDUNG: JSON.stringify(objectNoidung),
                        KEYMAUCHUNG: keyMauHSBASOSINH
                    };
                },
                formioHSBASOSINHValidate: function() {
                    formioMauHSBA.emit("checkValidity");
                    if (!formioMauHSBA.checkValidity(null, false, null, true)) {
                        return false;
                    }
                    return true;
                }
            })
        },
        generateFormMauHSBA: function(dataForm) {
            var self = this;
            var jsonForm = getJSONObjectForm(self.getObjectMauHSBA());
            Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
                jsonForm,{}
            ).then(function(form) {
                formioMauHSBA = form;
                formioMauHSBA.submission = {
                    data: {
                        ...dataForm
                    }
                }
            });
        },
        getObjectMauHSBA: function() {
            return getObjectMauHSBASOSINHPAGE2();
        },

        getInfoMauHSBATongket: function() {
            this.extendFunctionMauTongket();
            return {
                keyMauHSBA: keyMauHSBASOSINHTongket,
                insertMau: "insertMauHSBASOSINHTongket",
                editMau: "editMauHSBASOSINHTongket",
                selectMau: "selectMauHSBASOSINHTongket",
                getdataMau: "getdataMauHSBASOSINHTongket",
                formioValidate: "formioHSBASOSINHTongketValidate",
            };
        },
        extendFunctionMauTongket: function() {
            var self = this
            $.extend({
                insertMauHSBASOSINHTongket: function () {
                    self.generateFormMauHSBATongket({})
                },
                editMauHSBASOSINHTongket: function (rowSelect) {
                    var json = JSON.parse(rowSelect.NOIDUNG);
                    var dataMau = {}
                    json.forEach(function(item) {
                        dataMau[item.key] = item.value
                    })

                    self.generateFormMauHSBATongket({
                        ID: rowSelect.ID,
                        TENMAU: rowSelect.TENMAU,
                        ...dataMau
                    })
                },
                selectMauHSBASOSINHTongket: function (rowSelect) {
                    var json = JSON.parse(rowSelect.NOIDUNG);
                    var dataMau = {
                        ...formTongket.submission.data,
                    }
                    json.forEach(function(item) {
                        dataMau[item.key] = item.value
                    })
                    formTongket.submission = {
                        data: {
                            ...dataMau
                        }
                    }
                    $("#modalMauChungJSON").modal("hide");
                },
                getdataMauHSBASOSINHTongket: function () {
                    var objectNoidung = [];
                    self.getObjectMauHSBATongket().forEach(function(item) {
                        if(item.key != 'ID' && item.key != 'TENMAU') {
                            objectNoidung.push({
                                "label": item.label,
                                "value": formioMauHSBATongket.submission.data[item.key],
                                "key": item.key,
                            })
                        }
                    })
                    return {
                        ID: formioMauHSBATongket.submission.data.ID,
                        TENMAU: formioMauHSBATongket.submission.data.TENMAU,
                        NOIDUNG: JSON.stringify(objectNoidung),
                        KEYMAUCHUNG: keyMauHSBASOSINHTongket
                    };
                },
                formioHSBASOSINHTongketValidate: function() {
                    formioMauHSBATongket.emit("checkValidity");
                    if (!formioMauHSBATongket.checkValidity(null, false, null, true)) {
                        return false;
                    }
                    return true;
                }
            })
        },
        generateFormMauHSBATongket: function(dataForm) {
            var self = this;
            var jsonForm = getJSONObjectForm(self.getObjectMauHSBATongket());
            Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
                jsonForm,{}
            ).then(function(form) {
                formioMauHSBATongket = form;
                formioMauHSBATongket.submission = {
                    data: {
                        ...dataForm
                    }
                }
            });
        },
        getObjectMauHSBATongket: function() {
            return getObjectMauSOSINHTongket();
        }
    }

    function initGridThongTinTreEm() {
        var list = $("#listFormThongTinTreSoSinhBenhAnSoSinh");
        if(!list[0].grid) {
            $(list).jqGrid({
                datatype: "local",
                loadonce: false,
                height: 100,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "Thời gian", name: 'THOIGIANDE', index: 'THOIGIANDE', width: 150},
                    {label: "Cân nặng", name: 'CANNANG', index: 'CANNANG', width: 80},
                    {label: "Giới tính", name: 'GIOITINH', index: 'GIOITINH', hidden: true},
                    {label: "Giới tính", name: 'GIOI_TINH_HT', index: 'GIOI_TINH_HT', width: 150, formatter: function (cellValue, options, rowObject) {
                            return rowObject.GIOITINH == 1 ? "Nam" : "Nữ";
                        }},
                    {label: "APGAR 1 phút", name: 'APGAR1PHUT', index: 'APGAR1PHUT', width: 150},
                    {label: "APGAR 5 phút", name: 'APGAR5PHUT', index: 'APGAR5PHUT', width: 150},
                    {label: "APGAR 10 phút", name: 'APGAR10PHUT', index: 'APGAR10PHUT', width: 150},
                    {label: "Vòng đầu",name: 'VONGDAU', index: 'VONGDAU', width: 200}

                ],
                rowNum: 1000000,
                caption: "Danh sách trẻ em",
                onRightClickRow: function(id) {
                    if (id) {
                        $.contextMenu({
                            selector: '#listFormThongTinTreSoSinhBenhAnSoSinh tr',
                            reposition: false,
                            callback: function (key, options) {
                                if(key == 'copy') {
                                    var rowSelected = getThongtinRowSelected("listFormThongTinTreSoSinhBenhAnSoSinh");
                                    var dataForm = form.submission.data;
                                    dataForm.DENGAY = moment(rowSelected.THOIGIANDE, ['DD/MM/YYYY HH:mm:SS']).toISOString();
                                    dataForm.APGAR1PHUT = rowSelected.APGAR1PHUT;
                                    dataForm.APGAR5PHUT = rowSelected.APGAR5PHUT;
                                    dataForm.APGAR10PHUT = rowSelected.APGAR10PHUT;
                                    dataForm.APGARCANNANG = rowSelected.CANNANG;
                                    form.submission = {
                                        data: {
                                            ...dataForm
                                        }
                                    }
                                    $("#modalFormThongTinTreSoSinhBenhAnSoSinh").modal("hide");
                                }
                            },
                            items: {
                                "copy": {name: '<p><i class="fa fa-copy text-primary" aria-hidden="true"></i> Copy</p>'},
                            }
                        });
                    }
                }
            });
        }
    }

    function reloadDSThongTinTreSoSinh(soBenhAnMe, soVaoVienMe) {
        var params = {
            soBenhAn: soBenhAnMe,
            soVaoVien: soVaoVienMe
        }
        var url = 'get-dacdiem-tre-sosinh?' + $.param(params);
        $("#listFormThongTinTreSoSinhBenhAnSoSinh").jqGrid('setGridParam', {
            datatype: 'json',
            url: url
        }).trigger('reloadGrid');
    }
}