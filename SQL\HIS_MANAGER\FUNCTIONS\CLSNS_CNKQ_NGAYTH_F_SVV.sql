CREATE OR REPLACE FUNCTION "CLSNS_CNKQ_NGAYTH_F_SVV" (
    p_so_phieu_cdha        VARCHAR2,
    p_dvtt                 VARCHAR2,
    p_ma_cdha              NUMBER,
    p_ket_qua              CLOB,
    p_ket_luan             CLOB,
    p_bacsi_chidinh        VARCHAR2,
    p_bacsi_thuchien       VARCHAR2,
    p_chandoan             VARCHAR2,
    p_ma_noisoi            NUMBER,
    p_lydo_nhapvien        VARCHAR2,
    p_tiensubenh           VARCHAR2,
    p_may                  VARCHAR2,
    p_thuoc                VARCHAR2,
    p_denghi               CLOB,
    p_ns_chuandoan         NUMBER,
    p_ns_dieutri           NUMBER,
    p_capcuu               NUMBER,
    p_ns_laydivat          NUMBER,
    p_sinhthiet            NUMBER,
    p_clotest              NUMBER,
    p_giauphau_benh        NUMBER,
    p_noitru               IN                     NUMBER,
    p_ma_kham_benh         IN                     VARCHAR2,
    p_stt_benhan           IN                     VARCHAR2,
    p_stt_dotdieutri       IN                     VARCHAR2,
    p_stt_dieutri          IN                     VARCHAR2,
    p_ngaythuchien         IN                     DATE,
    p_sovaovien            IN                     NUMBER,
    p_sovaovien_noi        IN                     NUMBER,
    p_sovaovien_dt_noi     IN                     NUMBER,
    p_nguoithuchien        IN                     NUMBER,
    p_duongvao             IN                     VARCHAR2,
    p_mota_xml5            IN                     CLOB,
    p_ketqua_xml5          IN                     CLOB,
    p_ngaygioktthuchien    IN                     DATE,
    p_mabenhly_truoccdha   IN                     VARCHAR2,
    p_mabenhly_saucdha     IN                     VARCHAR2,
    p_chandoan_truoccdha   IN                     VARCHAR2,
    p_chandoan_saucdha     IN                     VARCHAR2,
    p_thoigianbatdaucls    IN                     VARCHAR2,
    p_maicd                IN                     VARCHAR2,
    p_tenicd               IN                     VARCHAR2,
    p_mabenhly             IN                     VARCHAR2
) RETURN NUMBER IS

    vcount                     NUMBER(10);
    v_maunoisoi                VARCHAR2(200);
    v_kiemtratontai            NUMBER(10);
    v_mabenhnhan               NUMBER(11);
    v_thamsocmu_ngaythuchien   VARCHAR(50) := his_manager.dm_tsdv_sl_mtso(p_dvtt, '960479');
    v_thamso960481             VARCHAR2(5) := his_manager.dm_tsdv_sl_mtso(p_dvtt, '960481');
    v_khoa                     NUMBER(10) := 0;
    v_thoigianbatdaucls        DATE DEFAULT TO_DATE(p_thoigianbatdaucls, 'dd/mm/yyyy hh24:mi:ss');
    v_return number;
BEGIN
    IF v_thamso960481 = 1 THEN
        IF p_noitru = 0 THEN
SELECT
    COUNT(1)
INTO v_khoa
FROM
    kb_phieuthanhtoan
WHERE
    dvtt = p_dvtt
  AND ma_kham_benh = p_ma_kham_benh
  AND sovaovien = p_sovaovien
  AND khoa_thanhtoan = 1;

ELSE
SELECT
    COUNT(1)
INTO v_khoa
FROM
    noitru_phieuthanhtoan
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien_noi
  AND sovaovien_dt = p_sovaovien_dt_noi
  AND khoa_thanhtoan = 1;

END IF;

        IF v_khoa > 0 THEN
            RETURN -1;
END IF;
END IF;

    v_kiemtratontai := 0;
    vcount := 0;
BEGIN
SELECT
    nvl(ten_maunoisoi, '')
INTO v_maunoisoi
FROM
    cls_maunoisoi
WHERE
    dvtt = p_dvtt
  AND ma_maunoisoi = p_ma_noisoi;

EXCEPTION
        WHEN no_data_found THEN
            v_maunoisoi := '';
END;

    IF p_noitru = 0 THEN
SELECT
    mabenhnhan
INTO v_mabenhnhan
FROM
    kb_kham_benh
WHERE
    dvtt = p_dvtt
  AND ma_kham_benh = p_ma_kham_benh
  AND sovaovien = p_sovaovien;

UPDATE kb_cd_cdha
SET
    trang_thai_chuan_doan = 1,
    ngay_tra_ketqua = p_ngaythuchien
WHERE
    so_phieu_cdha = p_so_phieu_cdha
  AND dvtt = p_dvtt
  AND ma_kham_benh = p_ma_kham_benh
  AND sovaovien = p_sovaovien;

UPDATE kb_cd_cdha_ct
SET
    ket_qua = p_ket_qua,
    mo_ta = p_ket_luan,
    bacsi_chidinh = p_bacsi_chidinh,
    bacsi_thuchien = p_bacsi_thuchien,
    chandoan = p_chandoan,
    da_chan_doan = 1,
    ma_mausieuam = p_ma_noisoi,
    mau_sieuam = v_maunoisoi,
    ngay_thuc_hien =
        CASE
            WHEN p_ngaythuchien < ngay_chi_dinh_ct THEN
                ngay_chi_dinh_ct + INTERVAL '15' MINUTE
            ELSE
                p_ngaythuchien
            END,
    ngay_gio_cdha_kt = p_ngaygioktthuchien,
    nguoi_thuc_hien = p_nguoithuchien,
    loidanbacsi = p_denghi,
    mo_ta_xml5 = p_mota_xml5,
    ket_qua_xml5 = p_ketqua_xml5,
    mabenhly_truoccdha = DECODE(p_mabenhly_truoccdha, '', NULL, to_number(p_mabenhly_truoccdha)),
    mabenhly_saucdha = DECODE(p_mabenhly_saucdha, '', NULL, to_number(p_mabenhly_saucdha)),
    chandoan_truoccdha = p_chandoan_truoccdha,
    chandoan_saucdha = p_chandoan_saucdha,
    ngay_th_yl = v_thoigianbatdaucls,
    ma_icd = p_maicd,
    ten_icd = p_tenicd,
    ma_benh_ly_theo_icd = p_mabenhly
WHERE
    so_phieu_cdha = p_so_phieu_cdha
  AND dvtt = p_dvtt
  AND ma_cdha = p_ma_cdha
  AND sovaovien = p_sovaovien;

SELECT
    COUNT(ma_cdha)
INTO v_kiemtratontai
FROM
    kb_cd_cdha_ct_ns
WHERE
    so_phieu_cdha = p_so_phieu_cdha
  AND dvtt = p_dvtt
  AND ma_cdha = p_ma_cdha;

IF v_kiemtratontai = 0 THEN
            INSERT INTO kb_cd_cdha_ct_ns (
                so_phieu_cdha,
                dvtt,
                ma_cdha,
                lydo_nhapvien,
                may,
                thuoc,
                denghi,
                ns_chuandoan,
                ns_dieutri,
                capcuu,
                ns_laydivat,
                sinhthiet,
                clotest,
                giauphau_benh,
                tiensubenh,
                mabenhnhan,
                sovaovien,
                ngay_tao,
                duong_vao
            ) VALUES (
                p_so_phieu_cdha,
                p_dvtt,
                p_ma_cdha,
                p_lydo_nhapvien,
                p_may,
                p_thuoc,
                p_denghi,
                p_ns_chuandoan,
                p_ns_dieutri,
                p_capcuu,
                p_ns_laydivat,
                p_sinhthiet,
                p_clotest,
                p_giauphau_benh,
                p_tiensubenh,
                v_mabenhnhan,
                p_sovaovien,
                trunc(p_ngaythuchien),
                p_duongvao
            );

ELSE
UPDATE kb_cd_cdha_ct_ns
SET
    lydo_nhapvien = p_lydo_nhapvien,
    may = p_may,
    thuoc = p_thuoc,
    denghi = p_denghi,
    ns_chuandoan = p_ns_chuandoan,
    ns_dieutri = p_ns_dieutri,
    capcuu = p_capcuu,
    ns_laydivat = p_ns_laydivat,
    sinhthiet = p_sinhthiet,
    clotest = p_clotest,
    giauphau_benh = p_giauphau_benh,
    tiensubenh = p_tiensubenh,
    duong_vao = p_duongvao
WHERE
    so_phieu_cdha = p_so_phieu_cdha
  AND dvtt = p_dvtt
  AND ma_cdha = p_ma_cdha;

END IF;

UPDATE his_manager.checkbox_cdha_insert
SET
    da_luu = 1
WHERE
    so_phieu_cdha = p_so_phieu_cdha
  AND ma_cdha = p_ma_cdha
  AND dvtt = p_dvtt;

vcount := SQL%rowcount;
ELSE
UPDATE noitru_cd_cdha
SET
    trang_thai_chuan_doan = 1,
    ngay_chuan_doan = p_ngaythuchien --TGDEV:36575 thêm ngày thực hiện cdha
        ,
    ngay_tra_ketqua = p_ngaythuchien
WHERE
    so_phieu_cdha = p_so_phieu_cdha
  AND dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri
  AND sovaovien = p_sovaovien_noi
  AND sovaovien_dt = p_sovaovien_dt_noi;

UPDATE noitru_cd_cdha_chi_tiet
SET
    ket_qua = p_ket_qua,
    mo_ta = p_ket_luan,
    bacsi_chidinh = p_bacsi_chidinh,
    bacsi_thuchien = p_bacsi_thuchien,
    chandoan = p_chandoan,
    da_chan_doan = 1,
    ma_mausieuam = p_ma_noisoi,
    mau_sieuam = v_maunoisoi,
    ngay_thuc_hien =
        CASE
            WHEN p_ngaythuchien < ngay_chi_dinh_ct THEN
                ngay_chi_dinh_ct + INTERVAL '15' MINUTE
            ELSE
                p_ngaythuchien
            END,
    ngay_gio_cdha_kt = p_ngaygioktthuchien,
    nguoi_thuc_hien = p_nguoithuchien,
    loidanbacsi = p_denghi,
    mo_ta_xml5 = p_mota_xml5,
    ket_qua_xml5 = p_ketqua_xml5,
    mabenhly_truoccdha = DECODE(p_mabenhly_truoccdha, '', NULL, to_number(p_mabenhly_truoccdha)),
    mabenhly_saucdha = DECODE(p_mabenhly_saucdha, '', NULL, to_number(p_mabenhly_saucdha)),
    chandoan_truoccdha = p_chandoan_truoccdha,
    chandoan_saucdha = p_chandoan_saucdha,
    ngay_th_yl = v_thoigianbatdaucls,
    ma_icd = p_maicd,
    ten_icd = p_tenicd,
    ma_benh_ly_theo_icd = p_mabenhly
WHERE
    so_phieu_cdha = p_so_phieu_cdha
  AND dvtt = p_dvtt
  AND ma_cdha = p_ma_cdha
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri
  AND stt_dieutri = p_stt_dieutri
  AND sovaovien = p_sovaovien_noi
  AND sovaovien_dt = p_sovaovien_dt_noi;

SELECT
    COUNT(ma_cdha)
INTO v_kiemtratontai
FROM
    noitru_cd_cdha_chi_tiet_ns
WHERE
    so_phieu_cdha = p_so_phieu_cdha
  AND dvtt = p_dvtt
  AND ma_cdha = p_ma_cdha
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri;

IF v_kiemtratontai = 0 THEN
            INSERT INTO noitru_cd_cdha_chi_tiet_ns (
                so_phieu_cdha,
                dvtt,
                ma_cdha,
                lydo_nhapvien,
                may,
                thuoc,
                denghi,
                ns_chuandoan,
                ns_dieutri,
                capcuu,
                ns_laydivat,
                sinhthiet,
                clotest,
                giauphau_benh,
                tiensubenh,
                stt_dieutri,
                stt_benhan,
                stt_dotdieutri,
                sovaovien,
                sovaovien_dt,
                duong_vao
            ) VALUES (
                p_so_phieu_cdha,
                p_dvtt,
                p_ma_cdha,
                p_lydo_nhapvien,
                p_may,
                p_thuoc,
                p_denghi,
                p_ns_chuandoan,
                p_ns_dieutri,
                p_capcuu,
                p_ns_laydivat,
                p_sinhthiet,
                p_clotest,
                p_giauphau_benh,
                p_tiensubenh,
                p_stt_dieutri,
                p_stt_benhan,
                p_stt_dotdieutri,
                p_sovaovien_noi,
                p_sovaovien_dt_noi,
                p_duongvao
            );

ELSE
UPDATE noitru_cd_cdha_chi_tiet_ns
SET
    lydo_nhapvien = p_lydo_nhapvien,
    may = p_may,
    thuoc = p_thuoc,
    denghi = p_denghi,
    ns_chuandoan = p_ns_chuandoan,
    ns_dieutri = p_ns_dieutri,
    capcuu = p_capcuu,
    ns_laydivat = p_ns_laydivat,
    sinhthiet = p_sinhthiet,
    clotest = p_clotest,
    giauphau_benh = p_giauphau_benh,
    tiensubenh = p_tiensubenh,
    duong_vao = p_duongvao
WHERE
    so_phieu_cdha = p_so_phieu_cdha
  AND dvtt = p_dvtt
  AND ma_cdha = p_ma_cdha
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri
  AND stt_dieutri = p_stt_dieutri;

END IF;

SELECT
    mabenhnhan
INTO v_mabenhnhan
FROM
    noitru_cd_cdha_chi_tiet
WHERE
    so_phieu_cdha = p_so_phieu_cdha
  AND dvtt = p_dvtt
  AND ma_cdha = p_ma_cdha
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri
  AND sovaovien = p_sovaovien_noi
  AND sovaovien_dt = p_sovaovien_dt_noi;

UPDATE his_manager.checkbox_cdha_insert
SET
    da_luu = 1
WHERE
    so_phieu_cdha = p_so_phieu_cdha
  AND ma_cdha = p_ma_cdha
  AND dvtt = p_dvtt;

vcount := SQL%rowcount;
END IF;
    
    v_return:=cmu_thoigian_nhanvien_ins(p_dvtt, p_nguoithuchien,
        CASE
            WHEN p_noitru = 0 THEN
                p_sovaovien
            ELSE p_sovaovien_noi
        END, v_mabenhnhan, p_noitru, TO_CHAR(v_thoigianbatdaucls, 'DD/MM/YYYY HH24:MI'), TO_CHAR(p_ngaythuchien, 'DD/MM/YYYY HH24:MI'
        ), p_so_phieu_cdha, p_ma_cdha, 'NS');

RETURN vcount;
END;