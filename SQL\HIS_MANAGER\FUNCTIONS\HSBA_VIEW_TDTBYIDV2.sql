CREATE OR REPLACE 
PROCEDURE             "HSBA_VIEW_TDTBYIDV2" (
 p_dvtt     IN VARCHAR2,
 p_id_dieutri IN VARCHAR2,
 p_sovaovien IN VARCHAR2,
 p_sovaovien_dt  IN VARCHAR2,
 cur               OUT SYS_REFCURSOR
) IS
  V_THAMSO_94300           VARCHAR2(1) := cmu_tsdv(p_dvtt, 94300, 0);
  V_THAMSO_86979           VARCHAR2(1) := cmu_tsdv(p_dvtt, 86979, 0);
  v_kiemtrathuoc           number(11) default 0;
  V_THAMSO_86971           VARCHAR2(1) := cmu_tsdv(p_dvtt, 86971, 0);
  v_thamso_bo_phieuhoantra number(1) default '0';
  v_svv                    number(11);
  v_svv_dt                 number(11);
  v_disable_auto           number(10) := cmu_tsdv(p_dvtt, 96206, 0);
  v_disable_auto_ttpt      number(10) := cmu_tsdv(p_dvtt, 96212, 0);
	v_thamso960616 					 number(10) := cmu_tsdv(p_dvtt, 960616, 0);
	v_stt_dieutri varchar2(255):='';
	v_stt_benhan varchar2(255):='';
	v_stt_dotdieutri varchar2(255):='';
	v_phongbenh varchar2(255);
	v_tenkhoa varchar2(255);
	v_chandoankhoa varchar2(255);
	v_sogiuong varchar2(255);
	v_makhoa varchar2(255);
	v_ngaygiolap varchar2(255);
BEGIN


begin
select pb.TEN_PHONG, gb.TENGIUONG into v_phongbenh, v_sogiuong
from CMU_HSBA_PHONGBENH pb
         inner join CMU_HSBA_PHONGBENH_GB gb on pb.dvtt = GB.dvtt
    and pb."ID" = gb.MA_PHONG
where gb.dvtt = p_Dvtt and GB.SOVAOVIEN = p_sovaovien
  and rownum <=1;
exception
	when no_data_found then v_phongbenh:=null;v_sogiuong:=null;
end;


SELECT STT_DIEUTRI,STT_BENHAN,STT_DOTDIEUTRI, pb.TEN_PHONGBAN, KHOALAP,
       to_char(ngaygiolap_tdt, 'DD/MM/YYYY HH24:MI')
into v_stt_dieutri, v_stt_benhan, v_stt_dotdieutri, v_tenkhoa, v_makhoa,
    v_ngaygiolap
FROM NOITRU_DIEUTRI dt
         inner join HIS_FW.dm_phongban pb on dt.dvtt = pb.MA_DONVI and dt.KHOALAP = pb.MA_PHONGBAN
where dvtt = p_Dvtt and SOVAOVIEN =p_sovaovien and ID_DIEUTRI = p_id_dieutri and SOVAOVIEN_DT = p_sovaovien_dt;

select
    case when ICD_KHOADIEUTRI IS NOT NULL THEN
             ICD_KHOADIEUTRI || ' - '|| TENICD_KHOADIEUTRI
         ELSE '' END INTO v_chandoankhoa
from NOITRU_LOGKHOAPHONG
where dvtt = p_dvtt
  and sovaovien = to_number(p_sovaovien)
  and SOVAOVIEN_DT = to_number(p_sovaovien_dt)
  and STT_LOGKHOAPHONG = (
    select max(STT_LOGKHOAPHONG) from NOITRU_LOGKHOAPHONG
    where dvtt = p_dvtt
      and sovaovien = to_number(p_sovaovien)
      and SOVAOVIEN_DT = to_number(p_sovaovien_dt)
    --and MAPHONGBAN = v_makhoa
)
--and MAPHONGBAN = v_makhoa
;




OPEN cur FOR
SELECT p_dvtt,
       b.stt_benhan STT_BENHAN,
       b.stt_dotdieutri,
       b.stt_dieutri,
       to_char(b.ngaygiolap_tdt, 'DD/MM/YYYY HH24:MI') AS ngaygio,
       nvl(b.tdt_ylenh, ' ') AS Y_LENH,
       CASE
           WHEN nvl(b.MACH, ' ') = ' ' THEN
               ' '
           ELSE
               ' - Mạch: ' || b.MACH || ' lần/phút,'
           END ||
           --
       CASE
           WHEN nvl(b.NHIETDO, ' ') = ' ' THEN
               ' '
           ELSE
               CHR(10) || ' - Nhiệt độ: ' || b.NHIETDO || ' ᵒC,'
           END ||
           --
       CASE
           WHEN nvl(b.HUYETAPTREN || '/' || b.HUYETAPDUOI, ' ') = '/' THEN
               ' '
           ELSE
               CHR(10) || ' - Huyết áp: ' || b.HUYETAPTREN || '/' || b.HUYETAPDUOI ||
               ' mmHg,'
           END ||
           --
       CASE
           WHEN nvl(b.NHIPTHO, ' ') = ' ' THEN
               ' '
           ELSE
               CHR(10) || ' - Nhịp thở: ' || b.NHIPTHO || ' lần/phút,'
           END ||
           --
       CASE
           WHEN nvl(b.CHIEUCAO, ' ') = ' ' THEN
               ' '
           ELSE
               CHR(10) || ' - Chiều cao: ' || b.CHIEUCAO || ' cm,'
           END ||
       CASE
           WHEN nvl(b.CANNANG, ' ') = ' ' THEN
               ' '
           ELSE
               CHR(10) || ' - Cân nặng: ' || b.CANNANG || ' kg,'
           END ||
           --BMI
       CASE
           WHEN b.BMI != null and b.CHIEUCAO != null and b.CANNANG != null THEN
        CHR(10) || ' - BMI: ' || b.BMI || ','
        ELSE
        ' '
END ||
             --SPO2
			 CASE
					 WHEN nvl(b.SPO2, ' ') = ' ' THEN
                        ' '
					 ELSE
                        CHR(10) || ' - SPO2: ' || b.SPO2 || ' %,'
END ||
			 --
			 --Diễn biến bệnh
			 CASE
			 WHEN nvl(b.tdt_dienbienbenh, ' ') = ' ' THEN
			 ' '
			 ELSE
			 concat(CHR(10), b.tdt_dienbienbenh)
END ||
			 CASE WHEN b.TEN_BENHPHU is not null or b.ICD_DIEUTRI is not null or  b.TENICD_DIEUTRI is not null then
			 case
			 when p_dvtt in 96029 then
			 CHR(10) || ' - Δ: '
			 else
			 CHR(10) || ' - Chẩn đoán: '
end || b.ICD_DIEUTRI || ' - ' || b.TENICD_DIEUTRI || ';'||
			 b.TEN_BENHPHU || chr(10)
END  AS DIEN_BIEN_BENH,
       b.ngaygiolap_tdt,
       c.ten_nhanvien_cd ten_nhanvien,
			 B.sovaovien,
			 B.sovaovien_Dt,
			 B.ID_DIEUTRI,
			 B.DVTT,
			 v_phongbenh PHONGBENH,
       v_sogiuong SOGIUONG,
			 pb.ten_phongban khoa,
			 CASE WHEN v_chandoankhoa IS NOT NULL then v_chandoankhoa 
			 else b.ICD_DIEUTRI || ' - ' || b.TENICD_DIEUTRI end CHANDOAN,
			 v_thamso960616 ANCHUKY
 FROM NOITRU_DIEUTRI b
  JOIN his_fw.dm_nhanvien_cd c ON b.tdt_nguoilap = c.ma_nhanvien
	join HIS_FW.DM_PHONGBAN pb on b.khoalap = pb.ma_phongban
	WHERE b.SOVAOVIEN = p_SOVAOVIEN AND SOVAOVIEN_DT = p_sovaovien_dt 
	AND ID_DIEUTRI = p_id_dieutri
	AND b.DVTT = p_dvtt 
;
END;
