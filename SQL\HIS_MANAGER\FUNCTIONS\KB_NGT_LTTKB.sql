create or replace FUNCTION "HIS_MANAGER"."KB_NGT_LTTKB" (
    p_makhambenh    IN              VARCHAR2,
    p_dvtt          IN              VARCHAR2,
    p_sovaovien     IN              NUMBER,
    p_maphongkham   IN              NUMBER
) RETURN SYS_REFCURSOR IS
    cur              SYS_REFCURSOR;
    v_thamso_89022   VARCHAR2(50) DEFAULT '0';
BEGIN
    v_thamso_89022 := get_tsdv(p_dvtt, 89022, '0');
    IF v_thamso_89022 = 2 THEN
        OPEN cur FOR SELECT
                                                   ma_benh_ly,
                                                   ma_huong_dieu_tri,
                                                   CAST(mach AS NUMBER(10, 0)) AS mach,
                                                   CAST(huyetapthap AS NUMBER(10, 0)) AS huyetapthap,
                                                   CAST(huyetapcao AS NUMBER(10, 0)) AS huyetapcao,
                                                   CAST(nhieptho AS NUMBER(10, 0)) AS nhieptho,
                                                   n<PERSON><PERSON><PERSON>,
                                                   ch<PERSON><PERSON><PERSON>,
                                                   CASE kb.cannang
                                                       WHEN ' ' THEN
                                                           NULL
                                                       ELSE
                                                           kb.cannang
                                                       END AS cannang,
                                                   kb.t<PERSON>,
                                                   chuan<PERSON><PERSON><PERSON><PERSON>,
                                                   TO_CHAR(ngay_hen, 'yyyy-mm-dd') AS ngay_hen,
                                                   TO_CHAR(ngay_hen, 'AM') AS buoi_hen,
                                                   tnp.icd_benhphu_pk        AS benh_phu,
                                                   creatinin,
                                                   bmi,
                                                   dothanhthai,
                                                   nhandinh_bmi,
                                                   nhandinh_dothanhthai,
                                                   icd,
                                                   nvl(tngt, 0) <PERSON> tngt,
                                                   chandoan_tuyentruoc,
                                                   nvl(lydo_chuyen, 0) AS lydo_chuyen,
                                                   tainanthuongtich,
                                                   nvl(chandoan_yhct, '') AS chandoan_yhct,
                                                   nvl(chandoan_nguyennhan, '') AS chandoan_nguyennhan,
                                                   TO_CHAR(ngay_kb, 'YYYY-MM-DD') AS ngay_kb,
                                                   nvl(ten_benh_theobs, '') AS ten_benh_theobs,
                                                   CASE kb.vongbung
                                                       WHEN ' ' THEN
                                                           NULL
                                                       ELSE
                                                           kb.vongbung
                                                       END AS vongbung,
                                                   bn.nhommau,
                                                   bn.khangthe,
                                                   tk.soluutru_taikham,
                                                   kb.loidankham             AS loidankham,
                                                   tn.icd_chan_don_vao,
                                                   tn.chan_doan_vao,
                                                   tn.ma_benh_ly_vao,
                                                   kb.ma_benh_yhct           AS mabenhyhct,
                                                   kb.ma_kem_ten_benh_yhct   AS tenbenhyhct,
                                                   abs(nvl(tn.ngay_hethan-trunc(sysdate)+1,0)) SONGAYCONBHYT,
                                                   kb.GIAI_DOAN_BENH,
                                                   kb.MA_BENH_YHCT,
                                                   kb.MA_KEM_TEN_BENH_YHCT,
                                                   kb.PP_DIEUTRI
                     FROM
                         kb_kham_benh                   kb
                             INNER JOIN kb_tiep_nhan                   tn ON tn.dvtt = kb.dvtt
                             AND tn.sovaovien = kb.sovaovien
                             INNER JOIN tiep_nhan_phong_benh           tnp ON tnp.sovaovien = kb.sovaovien
                             AND tnp.dvtt = kb.dvtt, his_public_list.dm_benh_nhan   bn
                                                         LEFT JOIN tgg_hentaikham                 tk ON bn.ma_benh_nhan = tk.mabenhnhan
                         AND tk.sovaovien = p_sovaovien
                     WHERE
                                                   kb.sovaovien = p_sovaovien
                       AND tnp.sovaovien = p_sovaovien
                       AND tnp.ma_phong_benh = p_maphongkham
                       AND ma_kham_benh = p_makhambenh
                       AND kb.dvtt = p_dvtt
                       AND bn.ma_benh_nhan = kb.mabenhnhan
                       AND ROWNUM = 1
                     UNION ALL
                     SELECT
                         ma_benh_ly,
                         ma_huong_dieu_tri,
                         CAST(mach AS NUMBER(10, 0)) AS mach,
                         CAST(huyetapthap AS NUMBER(10, 0)) AS huyetapthap,
                         CAST(huyetapcao AS NUMBER(10, 0)) AS huyetapcao,
                         CAST(nhieptho AS NUMBER(10, 0)) AS nhieptho,
                         nhietdo,
                         chieucao,
                         cannang,
                         trieuchungls,
                         chuandoansobo,
                         TO_CHAR(ngay_hen, 'yyyy-mm-dd') AS ngay_hen,
                         TO_CHAR(ngay_hen, 'AM') AS buoi_hen,
                         benh_phu,
                         creatinin,
                         bmi,
                         dothanhthai,
                         nhandinh_bmi,
                         nhandinh_dothanhthai,
                         icd,
                         nvl(tngt, 0) AS tngt,
                         chandoan_tuyentruoc,
                         nvl(lydo_chuyen, 0) AS lydo_chuyen,
                         tainanthuongtich,
                         nvl(chandoan_yhct, '') AS chandoan_yhct,
                         nvl(chandoan_nguyennhan, '') AS chandoan_nguyennhan,
                         TO_CHAR(ngay_kb, 'YYYY-MM-DD') AS ngay_kb,
                         nvl(ten_benh_theobs, '') AS ten_benh_theobs,
                         '' AS vongbung,
                         '' AS nhommau,
                         0 AS khangthe,
                         tk.soluutru_taikham,
                         '' AS loidankham,
                         '' icdvao,
                         '' chandoanvao,
                         NULL,
                         '' AS mabenhyhct,
                         '' AS tenbenhyhct,
                         0 SONGAYCONBHYT,
                         null GIAI_DOAN_BENH,
                         null MA_BENH_YHCT,
                         null MA_KEM_TEN_BENH_YHCT,
                         null PP_DIEUTRI
                     FROM
                                                   log_kb_kham_benh   kb
                                                       LEFT JOIN tgg_hentaikham     tk ON kb.mabenhnhan = tk.mabenhnhan
                                                       AND kb.sovaovien = tk.sovaovien
                     WHERE
                                                   kb.ma_kham_benh = p_makhambenh
                       AND kb.dvtt = p_dvtt
                       AND kb.sovaovien = p_sovaovien;

ELSE
        OPEN cur FOR SELECT
                         ma_benh_ly,
                         ma_huong_dieu_tri,
                         CAST(mach AS NUMBER(10, 0)) AS mach,
                         CAST(huyetapthap AS NUMBER(10, 0)) AS huyetapthap,
                         CAST(huyetapcao AS NUMBER(10, 0)) AS huyetapcao,
                         CAST(nhieptho AS NUMBER(10, 0)) AS nhieptho,
                         nhietdo,
                         chieucao,
                         CASE kb.cannang
                             WHEN ' ' THEN
                                 NULL
                             ELSE
                                 kb.cannang
                             END AS cannang,
                         trieuchungls,
                         chuandoansobo,
                         TO_CHAR(ngay_hen, 'yyyy-mm-dd') AS ngay_hen,
                         TO_CHAR(ngay_hen, 'AM') AS buoi_hen,
                         benh_phu,
                         creatinin,
                         bmi,
                         dothanhthai,
                         nhandinh_bmi,
                         nhandinh_dothanhthai,
                         icd,
                         nvl(tngt, 0) AS tngt,
                         chandoan_tuyentruoc,
                         nvl(lydo_chuyen, 0) AS lydo_chuyen,
                         tainanthuongtich,
                         nvl(chandoan_yhct, '') AS chandoan_yhct,
                         nvl(chandoan_nguyennhan, '') AS chandoan_nguyennhan,
                         TO_CHAR(ngay_kb, 'YYYY-MM-DD') AS ngay_kb,
                         nvl(ten_benh_theobs, '') AS ten_benh_theobs,
                         CASE kb.vongbung
                             WHEN ' ' THEN
                                 NULL
                             ELSE
                                 kb.vongbung
                             END AS vongbung,
                         bn.nhommau,
                         bn.khangthe,
                         tk.soluutru_taikham,
                         kb.loidankham             AS loidankham,
                         DECODE(kb.ketqua_dieutri, '0', '1', kb.ketqua_dieutri) AS ketqua_dieutri,
                         tn.icd_chan_don_vao,
                         tn.chan_doan_vao,
                         tn.ma_benh_ly_vao,
                         kb.ma_benh_yhct           AS mabenhyhct,
                         kb.ma_kem_ten_benh_yhct   AS tenbenhyhct,
                         abs(nvl(tn.ngay_hethan-trunc(sysdate)+1,0)) SONGAYCONBHYT,
                         kb.GIAI_DOAN_BENH,
                         kb.MA_BENH_YHCT,
                         kb.MA_KEM_TEN_BENH_YHCT,
                         kb.PP_DIEUTRI
                     FROM
                         kb_kham_benh                   kb
                             JOIN kb_tiep_nhan                   tn ON tn.dvtt = kb.dvtt
                             AND tn.sovaovien = kb.sovaovien, his_public_list.dm_benh_nhan   bn
                                                                  LEFT JOIN tgg_hentaikham                 tk ON bn.ma_benh_nhan = tk.mabenhnhan
                         AND tk.sovaovien = p_sovaovien
                     WHERE
                         ma_kham_benh = p_makhambenh
                       AND kb.dvtt = p_dvtt
                       AND kb.sovaovien = p_sovaovien
                       AND bn.ma_benh_nhan = kb.mabenhnhan
                     UNION ALL
                     SELECT
                         ma_benh_ly,
                         ma_huong_dieu_tri,
                         CAST(mach AS NUMBER(10, 0)) AS mach,
                         CAST(huyetapthap AS NUMBER(10, 0)) AS huyetapthap,
                         CAST(huyetapcao AS NUMBER(10, 0)) AS huyetapcao,
                         CAST(nhieptho AS NUMBER(10, 0)) AS nhieptho,
                         nhietdo,
                         chieucao,
                         cannang,
                         trieuchungls,
                         chuandoansobo,
                         TO_CHAR(ngay_hen, 'yyyy-mm-dd') AS ngay_hen,
                         TO_CHAR(ngay_hen, 'AM') AS buoi_hen,
                         benh_phu,
                         creatinin,
                         bmi,
                         dothanhthai,
                         nhandinh_bmi,
                         nhandinh_dothanhthai,
                         icd,
                         nvl(tngt, 0) AS tngt,
                         chandoan_tuyentruoc,
                         nvl(lydo_chuyen, 0) AS lydo_chuyen,
                         tainanthuongtich,
                         nvl(chandoan_yhct, '') AS chandoan_yhct,
                         nvl(chandoan_nguyennhan, '') AS chandoan_nguyennhan,
                         TO_CHAR(ngay_kb, 'YYYY-MM-DD') AS ngay_kb,
                         nvl(ten_benh_theobs, '') AS ten_benh_theobs,
                         '' AS vongbung,
                         '' AS nhommau,
                         0 AS khangthe,
                         tk.soluutru_taikham,
                         '' AS loidankham,
                         '' AS ketqua_dieutri,
                         '' icdvao,
                         '' chandoanvao,
                         NULL,
                         '' AS mabenhyhct,
                         '' AS tenbenhyhct,
                         0 SONGAYCONBHYT,
                         null GIAI_DOAN_BENH,
                         null MA_BENH_YHCT,
                         null MA_KEM_TEN_BENH_YHCT,
                         null PP_DIEUTRI
                     FROM
                         log_kb_kham_benh   kb
                             LEFT JOIN tgg_hentaikham     tk ON kb.mabenhnhan = tk.mabenhnhan
                             AND kb.sovaovien = tk.sovaovien
                     WHERE
                         kb.ma_kham_benh = p_makhambenh
                       AND kb.dvtt = p_dvtt
                       AND kb.sovaovien = p_sovaovien;

END IF;

RETURN cur;
END;