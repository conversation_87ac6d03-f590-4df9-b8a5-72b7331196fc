
CREATE TABLE "HIS_MANAGER"."CMU_THEODOICHIPHINOITRU_96019"
(	"DVTT" VARCHAR2(255 BYTE),
     "SOVAOVIEN" NUMBER(18,0),
     "SOVAOVIEN_DT" NUMBER(18,0),
     "<PERSON><PERSON><PERSON><PERSON><PERSON>HA<PERSON>" VARCHAR2(255 BYTE),
     "TENBE<PERSON>HNHAN" VARCHAR2(500 BYTE),
     "DIACHI" VARCHAR2(500 BYTE),
     "MABENHAN" VARCHAR2(255 BYTE),
     "SOTHEBHYT" VARCHAR2(255 BYTE),
     "TYLEBH" VARCHAR2(255 BYTE),
     "NGAYBATDAU" DATE,
     "NGAYHETHAN" DATE,
     "NGAY<PERSON>OVIEN" DATE,
     "TONGCHIPHI" NUMBER(18,4),
     "CONGKHAM" NUMBER(18,4),
     "XETNGHIEM" NUMBER(18,4),
     "CD<PERSON>" NUMBER(18,4),
     "TTPT" NUMBER(18,4),
     "GIUONG" NUMBER(18,4),
     "<PERSON><PERSON><PERSON><PERSON>VI<PERSON>" DATE,
     "MAPHONGBAN" VARCHAR2(255 BYTE),
     "TENPHONGBAN" VARCHAR2(255 BYTE),
     "TT_PHIEUTHANHTOAN" NUMBER(2,0),
     "TONGTAMUNG" NUMBER(18,4),
     "CHIPHI_TTNGOAITRU" NUMBER(18,4),
     "NGAYSINH" DATE,
     "THUOC" NUMBER(18,4),
     "XETNGHIEM_XHH" NUMBER(18,4),
     "CDHA_XHH" NUMBER(18,4),
     "TTPT_XHH" NUMBER(18,4),
     "SOTIENCHUYENCONHO" NUMBER(18,4),
     "GIOITINH" VARCHAR2(2 BYTE),
     "HINHTHUCKETTHUC" VARCHAR2(4 BYTE),
     "BANT" VARCHAR2(2 BYTE),
     "DATHANHTOAN" VARCHAR2(2 BYTE),
     "CHIPHIDATHANHTOAN" NUMBER(18,4),
     "TONGCHIHOANTRA" NUMBER(18,4),
     "TUOI" VARCHAR2(10 BYTE),
     "THANG" VARCHAR2(10 BYTE),
     "ICD" VARCHAR2(2000 BYTE),
     "MALOAI_KCB" VARCHAR2(5 BYTE),
     "TONGCHIPHI_PHATSINH" NUMBER(18,4),
     "TONGSONGAYDIEUTRI" VARCHAR2(10 BYTE),
     "NOIDANGKY_KCB" VARCHAR2(255 BYTE),
     "NOITINH" VARCHAR2(255 BYTE),
     "T_BHYT" NUMBER(18,4),
     "TT_HOANTATKHAM" VARCHAR2(255 BYTE),
     "TT_SAISOT" NUMBER(18,4) DEFAULT 0,
     "SOBENHAN" VARCHAR2(255 BYTE),
     "SOLUUTRU_RAVIEN" VARCHAR2(255 BYTE),
     "SOCHUYENTUYEN" VARCHAR2(255 BYTE),
     "CONGVIENCHUC" VARCHAR2(255 BYTE),
     "THANHTHI_NONGTHON" VARCHAR2(255 BYTE),
     "NGHENGHIEP" VARCHAR2(255 BYTE),
     "NOIGIOITHIEU" VARCHAR2(500 BYTE),
     "TUVONGTRONG24H" VARCHAR2(255 BYTE),
     "CHANDOANTUYEN_DUOI" VARCHAR2(1000 BYTE),
     "CHANDOAN_GPB" VARCHAR2(1000 BYTE),
     "KETQUADIEUTRI" VARCHAR2(255 BYTE),
     "MAICDRAVIEN" VARCHAR2(255 BYTE),
     "TINHTRANGRAVIEN" VARCHAR2(255 BYTE),
     "CHANDOANVAOVIEN" VARCHAR2(1000 BYTE),
     "NGAYGIORAVIEN" DATE,
     "MAKHOA_CDNHAPVIEN" VARCHAR2(255 BYTE),
     "TEN_KHOACDNHAPVIEN" VARCHAR2(500 BYTE)
)
CREATE INDEX "HIS_MANAGER"."CMU_96019_NGAYVAOVIEN" ON "HIS_MANAGER"."CMU_THEODOICHIPHINOITRU_96019" ("DVTT", "NGAYVAOVIEN")
CREATE INDEX "HIS_MANAGER"."CMU_96019_PT_UNIQUE" ON "HIS_MANAGER"."CMU_THEODOICHIPHINOITRU_96019" ("SOVAOVIEN_DT")
CREATE INDEX "HIS_MANAGER"."CMU_96019_SOVAOVIEN" ON "HIS_MANAGER"."CMU_THEODOICHIPHINOITRU_96019" ("SOVAOVIEN")
CREATE INDEX "HIS_MANAGER"."CMU_THEODOI_96019_ID3" ON "HIS_MANAGER"."CMU_THEODOICHIPHINOITRU_96019" ("DVTT", "TT_PHIEUTHANHTOAN")