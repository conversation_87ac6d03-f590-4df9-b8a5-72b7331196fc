CREATE OR REPLACE FUNCTION "CLS_DT_CNKQ_BSTT_SVV" (
    p_so_phieu_cdha        VARCHAR2,
    p_dvtt                 VARCHAR2,
    p_ma_cdha              NUMBER,
    p_ket_qua              CLOB,
    p_ket_luan             CLOB,
    p_bacsi_chidinh        VARCHAR2,
    p_bacsi_thuchien       VARCHAR2,
    p_loidan<PERSON><PERSON>i          VARCHAR2,
    p_ma_dientim           NUMBER,
    p_noitru               IN                     NUMBER,
    p_ma_kham_benh         IN                     VARCHAR2,
    p_stt_benhan           IN                     VARCHAR2,
    p_stt_dotdieutri       IN                     VARCHAR2,
    p_stt_dieutri          IN                     VARCHAR2,
--  p_ngaythuchien in date
    v_ngaythuchien         IN                     VARCHAR2,
    p_sovaovien            IN                     NUMBER,
    p_sovaovien_noi        IN                     NUMBER,
    p_sovaovien_dt_noi     IN                     NUMBER,
    p_nguoithuchien        IN                     NUMBER,
    p_mota_xml5            IN                     CLOB,
    p_ketqua_xml5          IN                     CLOB,
    p_mabenhly_truoccdha   IN                     VARCHAR2,
    p_mabenhly_saucdha     IN                     VARCHAR2,
    p_chandoan_truoccdha   IN                     VARCHAR2,
    p_chandoan_saucdha     IN                     VARCHAR2,
    p_thoigianbatdaucls    IN                     VARCHAR2,
    p_maicd                IN                     VARCHAR2,
    p_tenicd               IN                     VARCHAR2,
    p_mabenhly             IN                     VARCHAR2
) RETURN VARCHAR2 IS

    v_maudientim               VARCHAR2(200);
    p_ngaythuchien             TIMESTAMP;
    v_thamsocmu_ngaythuchien   VARCHAR(50) := his_manager.dm_tsdv_sl_mtso(p_dvtt, '960479');
    v_thamso960481             VARCHAR2(5) := his_manager.dm_tsdv_sl_mtso(p_dvtt, '960481');
    v_khoa                     NUMBER(10) := 0;
    v_tenbacsinguoithuchien    VARCHAR2(500);
    v_thoigianbatdaucls        DATE DEFAULT TO_DATE(p_thoigianbatdaucls, 'dd/mm/yyyy hh24:mi:ss');
    p_mabenhnhan               NUMBER;
    v_return                   NUMBER;
BEGIN
SELECT
    CASE
        WHEN mota_chucdanh IS NOT NULL THEN
            mota_chucdanh
                || '. '
                || ten_nhanvien
        ELSE
            ten_nhanvien
        END ten_nhanvien
INTO v_tenbacsinguoithuchien
FROM
    his_fw.dm_nhanvien            nv,
    his_fw.dm_chucdanh_nhanvien   cd
WHERE
    nv.ma_nhanvien = p_nguoithuchien
  AND nv.chucdanh_nhanvien = cd.ma_chucdanh;

IF v_thamso960481 = 1 THEN
        IF p_noitru = 0 THEN
SELECT
    COUNT(1)
INTO v_khoa
FROM
    kb_phieuthanhtoan
WHERE
    dvtt = p_dvtt
  AND ma_kham_benh = p_ma_kham_benh
  AND sovaovien = p_sovaovien
  AND khoa_thanhtoan = 1;

ELSE
SELECT
    COUNT(1)
INTO v_khoa
FROM
    noitru_phieuthanhtoan
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien_noi
  AND sovaovien_dt = p_sovaovien_dt_noi
  AND khoa_thanhtoan = 1;

END IF;

        IF v_khoa > 0 THEN
            RETURN -1;
END IF;
END IF;

BEGIN
SELECT
    nvl(ten_maudientim, '')
INTO v_maudientim
FROM
    cls_maudientim
WHERE
    dvtt = p_dvtt
  AND ma_maudientim = p_ma_dientim;

EXCEPTION
        WHEN no_data_found THEN
            v_maudientim := '';
END;

    p_ngaythuchien := to_timestamp(v_ngaythuchien, 'YYYY-MM-DD HH24:MI:SS');
    IF p_noitru = 0 THEN
UPDATE kb_cd_cdha
SET
    trang_thai_chuan_doan = 1,
    ngay_tra_ketqua = p_ngaythuchien
WHERE
    so_phieu_cdha = p_so_phieu_cdha
  AND dvtt = p_dvtt
  AND ma_kham_benh = p_ma_kham_benh
  AND sovaovien = p_sovaovien;

UPDATE kb_cd_cdha_ct
SET
    ket_qua = p_ket_qua,
    mo_ta = p_ket_luan,
    bacsi_chidinh = p_bacsi_chidinh,
    bacsi_thuchien = DECODE(v_thamsocmu_ngaythuchien, '1', v_tenbacsinguoithuchien, p_bacsi_thuchien),
    chandoan = p_loidanbacsi,
    da_chan_doan = 1,
    ma_mausieuam = p_ma_dientim,
    mau_sieuam = v_maudientim,
    ngay_thuc_hien = p_ngaythuchien,
    nguoi_thuc_hien = p_nguoithuchien,
    loidanbacsi = p_loidanbacsi,
    mo_ta_xml5 = p_mota_xml5,
    ket_qua_xml5 = p_ketqua_xml5,
    mabenhly_truoccdha = DECODE(p_mabenhly_truoccdha, '', NULL, to_number(p_mabenhly_truoccdha)),
    mabenhly_saucdha = DECODE(p_mabenhly_saucdha, '', NULL, to_number(p_mabenhly_saucdha)),
    chandoan_truoccdha = p_chandoan_truoccdha,
    chandoan_saucdha = p_chandoan_saucdha,
    ngay_th_yl = v_thoigianbatdaucls,
    ma_icd = p_maicd,
    ten_icd = p_tenicd,
    ma_benh_ly_theo_icd = p_mabenhly
WHERE
    so_phieu_cdha = p_so_phieu_cdha
  AND dvtt = p_dvtt
  AND ma_cdha = p_ma_cdha
  AND ma_kham_benh = p_ma_kham_benh
  AND sovaovien = p_sovaovien;

SELECT
    mabenhnhan
INTO p_mabenhnhan
FROM
    kb_cd_cdha_ct
WHERE
    so_phieu_cdha = p_so_phieu_cdha
  AND dvtt = p_dvtt
  AND ma_cdha = p_ma_cdha
  AND ma_kham_benh = p_ma_kham_benh
  AND sovaovien = p_sovaovien;

ELSE
UPDATE noitru_cd_cdha
SET
    trang_thai_chuan_doan = 1,
    ngay_tra_ketqua = p_ngaythuchien
WHERE
    so_phieu_cdha = p_so_phieu_cdha
  AND dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri
  AND sovaovien = p_sovaovien_noi
  AND sovaovien_dt = p_sovaovien_dt_noi;

UPDATE noitru_cd_cdha_chi_tiet
SET
    ket_qua = p_ket_qua,
    mo_ta = p_ket_luan,
    bacsi_chidinh = p_bacsi_chidinh,
    bacsi_thuchien = DECODE(v_thamsocmu_ngaythuchien, '1', v_tenbacsinguoithuchien, p_bacsi_thuchien),
    chandoan = p_loidanbacsi,
    da_chan_doan = 1,
    ma_mausieuam = p_ma_dientim,
    mau_sieuam = v_maudientim,
    ngay_thuc_hien = p_ngaythuchien,
    nguoi_thuc_hien = p_nguoithuchien,
    loidanbacsi = p_loidanbacsi,
    mo_ta_xml5 = p_mota_xml5,
    ket_qua_xml5 = p_ketqua_xml5,
    mabenhly_truoccdha = DECODE(p_mabenhly_truoccdha, '', NULL, to_number(p_mabenhly_truoccdha)),
    mabenhly_saucdha = DECODE(p_mabenhly_saucdha, '', NULL, to_number(p_mabenhly_saucdha)),
    chandoan_truoccdha = p_chandoan_truoccdha,
    chandoan_saucdha = p_chandoan_saucdha,
    ngay_th_yl = v_thoigianbatdaucls,
    ma_icd = p_maicd,
    ten_icd = p_tenicd,
    ma_benh_ly_theo_icd = p_mabenhly
WHERE
    so_phieu_cdha = p_so_phieu_cdha
  AND dvtt = p_dvtt
  AND ma_cdha = p_ma_cdha
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri
  AND sovaovien = p_sovaovien_noi
  AND sovaovien_dt = p_sovaovien_dt_noi;

SELECT
    mabenhnhan
INTO p_mabenhnhan
FROM
    noitru_cd_cdha_chi_tiet
WHERE
    so_phieu_cdha = p_so_phieu_cdha
  AND dvtt = p_dvtt
  AND ma_cdha = p_ma_cdha
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri
  AND sovaovien = p_sovaovien_noi
  AND sovaovien_dt = p_sovaovien_dt_noi;

END IF;

    v_return := cmu_thoigian_nhanvien_ins(p_dvtt, p_nguoithuchien,
        CASE
            WHEN p_noitru = 0 THEN
                p_sovaovien
            ELSE p_sovaovien_noi
        END, p_mabenhnhan, p_noitru, TO_CHAR(v_thoigianbatdaucls, 'DD/MM/YYYY HH24:MI'), TO_CHAR(p_ngaythuchien, 'DD/MM/YYYY HH24:MI'
        ), p_so_phieu_cdha, p_ma_cdha, 'DT');

RETURN v_maudientim;
END;