
$(function (){
    var thongTinKyThuatPHCNMoiNhat = {};
    var thongTinKyThuatPHCNTruocChinhSua = {};
    var formKyThuatPHCN;
    var selectedSttDieuTri;

    $("#ttchamsoc-phieukhac").click(function () {
        instanceGridKyThuatPHCN();
        reloadDSKyThuatPHCN();
        showFormKyThuatPHCN();
    });
    $("#kythuatphcn_lammoi").click(function () {
        reloadDSKyThuatPHCN();
    });

    $(".themkythuatphcn").click(function () {
        $("#modalFormKyThuatPHCN").modal("show");
        addTextTitleModal("titleFormKyThuatPHCN", " Phiếu thực hiện kỹ thuật phục hồi chức năng");
        showFormKyThuatPHCN();
        $("#kythuatphcn_luu").attr("data-action", "THEM");
    });

    $("#kythuatphcn_luu").click(function () {
        var btnAction = $('#kythuatphcn_luu').attr("data-action");
        if (btnAction == "THEM"){
            themKyThuatPHCN();
        } else {
            updateKyThuatPHCN();
        }
    });
    $("#xemkythuatphcn").click(function(){
        $.get("cmu_getlist?url=" + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, 'CMU_GET_KYTHUATPHCN'])).done(function(data) {
            if(data.length > 0) {
                var res = data[0];
                if(res.KEYSIGN) {
                    getFilesign769(
                        "PHIEU_NOITRU_KYTHUATPHCN",
                        1,
                        -1,//singletonObject.userId,
                        singletonObject.dvtt,
                        thongtinhsba.thongtinbn.SOVAOVIEN,
                        thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                        -1,
                        function(data) {
                            // hideLoaderIntoWrapId("hsba_list_todieutri");
                            if(data.length > 0) {
                                getCMUFileSigned769(data[0].KEYMINIO,"pdf")
                            } else {
                                notifiToClient("Red", "Lỗi lấy dữ liệu ký số");
                            }
                        }
                    )
                } else {
                    xemKyThuatPHCN();
                }
            } else {
            }
        }).fail(function() {
            notifiToClient("Red", "Lỗi lấy dữ liệu");
        })
    })
    $("#kythuatphcn_kyso").click(function(){
        var params = {
            mabenhnhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
            tennguoibenh: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
            chandoan: getChuanDoanKTPHCN(),
            sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
            sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
            gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
            tuoi: thongtinhsba.thongtinbn.TUOI,
            khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
            stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
            stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
            stt_todieutri: selectedSttDieuTri
        }
        var url = 'cmu_in_cmu_kithuatphcn?type=pdf&' + $.param(params);
        previewAndSignPdfDefaultModal({
            url: url,
            idButton: 'kythuatphcn_kyso_action',
        }, function(){
            $("#kythuatphcn_kyso_action").click(function() {
                kySoChung({
                    dvtt: singletonObject.dvtt,
                    userId: singletonObject.userId,
                    url: $('#iframePreviewAndSign').attr('src'),
                    loaiGiay: "PHIEU_NOITRU_KYTHUATPHCN",
                    maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                    soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                    soPhieuDichVu: 1,
                    soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                    soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                    keyword: "Số vào viện",
                    fileName: "Phiếu thực hiện kỹ thuật phục hồi chức năng: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                }, function(dataKySo) {
                    $("#modalPreviewAndSignPDF").modal("hide");
                    reloadDSKyThuatPHCN();
                });
            });
        });
    })
    $("#kythuatphcn_huykyso").click(function() {
        confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
            huykysoFilesign769("PHIEU_NOITRU_KYTHUATPHCN", 1, singletonObject.userId, singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                    reloadDSKyThuatPHCN();
                })
        }, function () {

        })
    })

    $('#view_single_kythuatphcn').click(function () {
        xemKyThuatPHCN(thongTinKyThuatPHCNMoiNhat)
    })

    $('#edit_single_kythuatphcn').click(function () {
        showUpdateKyThuatPHCN(thongTinKyThuatPHCNMoiNhat)
    });

    $('#delete_single_kythuatphcn').click(function () {
        deleteKyThuatPHCN(thongTinKyThuatPHCNMoiNhat)
    })

    function showFormKyThuatPHCN() {
        var jsonForm = getJSONObjectForm([
            {
                "label": "Người nhà",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Ngày giờ thực hiện",
                                "key": "THOIGIAN",
                                "type": "datetime",
                                format: "dd/MM/yyyy HH:mm",
                                "customClass": "pr-2",
                                enableTime: true,
                            },
                        ],
                        "width": 4,
                    },
                    {
                        "components": [
                            {
                                "label": "Thời gian thực hiện (phút)",
                                "key": "THOIGIANPHUT",
                                "type": "number",
                                "customClass": "pr-2",
                            },
                        ],
                        "width": 4,
                    },
                    {
                        "components": [
                            {
                                "label": "Tờ điều trị",
                                others: {
                                    "data": {
                                        "values": getDanhSachSttDieuTri()
                                    },
                                },
                                "key": "STT_DIEUTRI",
                                "type": "select",
                                "validate": {
                                    required: true,
                                }
                            },
                        ],
                        "width": 4,
                    },
                ],
                "customClass": "ml-0 mr-0",
                "key": "THONGTINNGUOINHANHANVIEN",
                "type": "columns",
            },
            {
                "label": "Diễn biến bệnh, tật/ các vấn đề cần PHCN",
                "key": "DIENBIENBENH",
                "type": "textarea",
            },
            {
                "label": "Tên dịch vụ kỹ thuật PHCN",
                "key": "TENDICHVU",
                "type": "textarea",
            },
            {
                "label": "Người nhà",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Người thực hiện",
                                others: {
                                    "data": {
                                        "values": singletonObject.danhsachtatcanhanvien
                                    },
                                    // defaultValue: singletonObject.userId,
                                },
                                "customClass": "pr-2",
                                "key": "NGUOITHUCHIEN",
                                "type": "select",
                            },
                        ],
                        "width": 6,
                    },
                    {
                        "components": [
                            {
                                "label": "Bác sĩ chỉ định",
                                others: {
                                    "data": {
                                        "values": singletonObject.danhsachnhanvienFormio
                                    },
                                    defaultValue: singletonObject.userId,
                                },
                                "key": "BACSICHIDINH",
                                "type": "select",
                                "validate": {
                                    required: true,
                                }
                            },
                        ],
                        "width": 6,
                    },

                ],
                "customClass": "ml-0 mr-0",
                "key": "THONGTINNGUOINHANHANVIEN",
                "type": "columns",
            },
        ])
        Formio.createForm(document.getElementById('formNhapKyThuatPHCN'),
            jsonForm,{}
        ).then(function(form) {
            formKyThuatPHCN = form;
        });
    }

    function getDanhSachSttDieuTri() {
        const danhSachDieuTri = $.ajax({
            url: "cmu_getlist?url=" + convertArray([
                thongtinhsba.thongtinbn.STT_BENHAN,
                0,
                -1,
                singletonObject.dvtt,
                "HSBA_TODIEUTRI_CMU_SEL"
            ]),
            async: false
        }).responseText;

        const danhSachDieuTriJson = JSON.parse(danhSachDieuTri);
        if (danhSachDieuTriJson && danhSachDieuTriJson.length > 0) {
            return danhSachDieuTriJson.map(dt => {
                return {
                    "label": dt.STT_DIEUTRI + ' - ' + dt.TEN_NHANVIEN + ' - ' + dt.NGAYGIO,
                    "value": dt.STT_DIEUTRI.toString()
                };
            });
        }
        return [];
    }

    function reloadDSKyThuatPHCN(){
        var url = "cmu_getlist?url=" + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, "CMU_GET_KYTHUATPHCN"]);
        $.get(url).done(function(data){
            if (data && data.length > 0) {
                $("#data_kythuatphcn").html(thongtinhsba.thongtinbn.TEN_PHONGBAN + ' - ' + data[0].NGAY_TAO_PHIEU);
                selectedSttDieuTri = data[0].STT_DIEUTRI;
                thongTinKyThuatPHCNMoiNhat = data[0];
                if (data[0].KEYSIGN){
                    $('#edit_single_kythuatphcn').css('visibility', 'hidden');
                    $('#delete_single_kythuatphcn').css('visibility', 'hidden');
                }else{
                    $('#handle_icon_kythuatphcn').css('visibility', 'unset');
                    $('#view_single_kythuatphcn').css('visibility', 'unset');
                    $('#edit_single_kythuatphcn').css('visibility', 'unset');
                    $('#delete_single_kythuatphcn').css('visibility', 'unset');
                }
            } else  {
                $("#data_kythuatphcn").html('Không có dữ liệu');
                $('#handle_icon_kythuatphcn').css('visibility', 'hidden');
            }
        });
        $("#list_kythuatphcn").jqGrid('setGridParam', {
            datatype: 'json',
            url: url
        }).trigger('reloadGrid')
        hideLoaderIntoWrapId("list_ttcs-bdcdct-wrap");
        disablePHCNButtons(true);
        // showFormKyThuatPHCN();
        checkKysokythuatphcn();
    }

    function disablePHCNButtons(disabled) {
        $("#xemkythuatphcn").prop('disabled', disabled);
        $("#kythuatphcn_kyso").prop('disabled', disabled);
    }

    function themKyThuatPHCN() {
        showSelfLoading("kythuatphcn_luu");
        formKyThuatPHCN.emit("checkValidity");
        if (!formKyThuatPHCN.checkValidity(null, false, null, true)) {
            hideSelfLoading("kythuatphcn_luu");
            return;
        }
        var actionUrl;
        var url;
        var dataSubmit = formKyThuatPHCN.submission.data;
        actionUrl = "cmu_post";
        url = [
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.MA_BENH_NHAN,
            moment(dataSubmit.THOIGIAN).format("DD/MM/YYYY HH:mm"),
            dataSubmit.THOIGIANPHUT,
            dataSubmit.DIENBIENBENH,
            dataSubmit.TENDICHVU,
            dataSubmit.NGUOITHUCHIEN,
            dataSubmit.BACSICHIDINH,
            singletonObject.userId,
            dataSubmit.STT_DIEUTRI,
            "CMU_KYTHUATPHCN_INSERT"
        ];

        $.post(actionUrl, {
            url: url.join('```')
        }).done(function (data) {
            if(data > 0){
                notifiToClient('Green', 'Thêm phiếu thành công');
                $("#modalFormKyThuatPHCN").modal("hide");
                // luuLogHSBAChinhSuaFormio({}, {...dataSubmit},"PHIEUDUYETPTDVU", keyLuuLogKyThuatPHCN);
                var noidung = ["Số phiếu:"+ data]
                for (const key in dataSubmit) {
                    noidung.push(formKyThuatPHCN.getComponent(key).label.trim(":") + ": " + getValueOfFormIO(formKyThuatPHCN.getComponent(key)));
                }
                var dataLog = {
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: LOGHSBALOAI.KYTHUATPHCN.KEY,
                    NOIDUNGBANDAU: "",
                    NOIDUNGMOI: noidung.join("; "),
                    USERID: singletonObject.userId,
                    ACTION: LOGHSBAACTION.INSERT.KEY,
                }
                luuLogHSBATheoBN(dataLog);
            } else {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }
        }).fail(function(error) {
            notifiToClient("Red",MESSAGEAJAX.ERROR);
        }).always(function() {
            hideSelfLoading("kythuatphcn_luu");
            reloadDSKyThuatPHCN();
        });
    }

    function updateKyThuatPHCN() {
        showSelfLoading("kythuatphcn_luu");
        formKyThuatPHCN.emit("checkValidity");
        if (!formKyThuatPHCN.checkValidity(null, false, null, true)) {
            hideSelfLoading("kythuatphcn_luu");
            return;
        }
        var actionUrl;
        var url;
        var dataSubmit = formKyThuatPHCN.submission.data;
        actionUrl = "cmu_post";
        url = [
            thongTinKyThuatPHCNTruocChinhSua.ID,
            singletonObject.dvtt,
            moment(dataSubmit.THOIGIAN).format("DD/MM/YYYY HH:mm"),
            dataSubmit.THOIGIANPHUT,
            dataSubmit.DIENBIENBENH,
            dataSubmit.TENDICHVU,
            dataSubmit.NGUOITHUCHIEN,
            dataSubmit.BACSICHIDINH,
            dataSubmit.STT_DIEUTRI,
            "CMU_KYTHUATPHCN_UPDATE"
        ];
        $.post(actionUrl, {
            url: url.join('```')
        }).done(function (data) {
            if(data > 0){
                notifiToClient('Green', 'Cập nhật phiếu thành công');
                $("#modalFormKyThuatPHCN").modal("hide");
                // luuLogHSBAChinhSuaFormio({...thongTinKyThuatPHCNTruocChinhSua}, {...dataSubmit},"PHIEUDUYETPTDVU", keyLuuLogKyThuatPHCN);
                var noidungold = []
                var noidungnew = []
                var luutru = ""

                dataSubmit.NGAY_TAO_PHIEU = moment(dataSubmit.NGAY_TAO_PHIEU).format("DD/MM/YYYY")
                dataSubmit.THOIGIANXACNHAN = moment(dataSubmit.THOIGIANXACNHAN).format("DD/MM/YYYY HH:mm")
                var diffObject = findDifferencesBetweenObjects(thongTinKyThuatPHCNTruocChinhSua, dataSubmit);
                for (const key in diffObject) {
                    try {
                        luutru = formKyThuatPHCN.getComponent(key).label
                        if (luutru) {
                            noidungold.push(luutru.trim(":") + ": " + thongTinKyThuatPHCNTruocChinhSua[key]);
                            noidungnew.push(luutru.trim(":") + ": " + getValueOfFormIO(formKyThuatPHCN.getComponent(key)));
                        }
                    } catch (error) {
                        // console.log("Error: ", key);
                    }



                }
                var dataLog = {
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: LOGHSBALOAI.KYTHUATPHCN.KEY,
                    NOIDUNGBANDAU: noidungold.join("; "),
                    NOIDUNGMOI: noidungnew.join("; "),
                    USERID: singletonObject.userId,
                    ACTION: LOGHSBAACTION.EDIT.KEY,
                }
                luuLogHSBATheoBN(dataLog);
            } else {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }
        }).fail(function(error) {
            notifiToClient("Red",MESSAGEAJAX.ERROR);
        }).always(function() {
            hideSelfLoading("kythuatphcn_luu");
            reloadDSKyThuatPHCN();
        });
    }

    function instanceGridKyThuatPHCN(){
        if (!$("#list_kythuatphcn")[0].grid) {
            $("#list_kythuatphcn").jqGrid({
                datatype: "local",
                loadonce: false,
                height: 100,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {
                        name: "KYSO",
                        label: "Ký số",
                        align: 'left',
                        width: 100,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.KEYSIGN) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: green">Đã ký</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: red">Chưa ký</span>';
                            }
                        }
                    },
                    {label: 'ID',name: 'ID', index: 'ID', width: 50, align: 'center'},
                    {label: 'STT điều trị',name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 50, align: 'center'},
                    {label: 'Thời gian',name: 'THOIGIAN', index: 'THOIGIAN', width: 250, align: 'center'},
                    {label: 'Thời gian thực hiện',name: 'THOIGIANPHUT', index: 'THOIGIANPHUT', width: 250, align: 'center', hidden: true},
                    {label: 'Diễn biến bệnh',name: 'DIENBIENBENH', index: 'DIENBIENBENH', width: 250, align: 'center', hidden: true},
                    {label: 'Tên dịch vụ',name: 'TENDICHVU', index: 'TENDICHVU', width: 250, align: 'center', hidden: true},
                    {label: 'NGUOITHUCHIEN',name: 'NGUOITHUCHIEN', index: 'NGUOITHUCHIEN', width: 250, align: 'center', hidden: true},
                    {label: 'BACSICHIDINH',name: 'BACSICHIDINH', index: 'BACSICHIDINH', width: 250, align: 'center', hidden: true},
                    {label: 'Tên người thực hiện',name: 'TENNGUOITHUCHIEN', index: 'TENNGUOITHUCHIEN', width: 250, align: 'center'},
                    {label: 'Tên BS chỉ định',name: 'TENBACSICHIDINH', index: 'TENBACSICHIDINH', width: 250, align: 'center'},
                    {label: 'Ngày tạo phiếu',name: 'NGAY_TAO_PHIEU', index: 'NGAY_TAO_PHIEU', width: 250, align: 'center', hidden: true},
                    {name: "KEYSIGN", label: "KEYSIGN", align: 'center', width: 150, hidden: true},
                ],
                rowNum: 1000000,
                caption: "Danh sách phiếu thực hiện kỹ thuật phục hồi chức năng",
                onSelectRow: function (id) {
                    if (id) {
                        var ret = $("#list_kythuatphcn").jqGrid('getRowData', id);
                        selectedSttDieuTri = ret.STT_DIEUTRI;
                        if (selectedSttDieuTri) {
                            disablePHCNButtons(false);
                        } else {
                            disablePHCNButtons(true);
                        }
                    }
                },
                onRightClickRow: function (id1) {
                    if (id1) {
                        var ret = getThongtinRowSelected("list_kythuatphcn");
                        var items = {}
                        $.contextMenu('destroy', '#list_kythuatphcn tr');
                        if (ret.KEYSIGN) {
                            items = {}
                        } else {
                            items = {
                                "sua": {name: '<i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Sửa</p>'},
                                "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'},
                            }
                        }
                        $.contextMenu({
                            selector: '#list_kythuatphcn tr',
                            callback: function (key, options) {
                                var id = $("#list_kythuatphcn").jqGrid('getGridParam', 'selrow');
                                var ret = $("#list_kythuatphcn").jqGrid('getRowData', id);
                                var params = {
                                    mabenhnhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                                    tennguoibenh: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                    chandoan: getChuanDoanKTPHCN(),
                                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                    gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                                    tuoi: thongtinhsba.thongtinbn.TUOI,
                                    khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                                    stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                                    stt_todieutri: selectedSttDieuTri
                                }
                                var url = 'cmu_in_cmu_kithuatphcn?type=pdf&' + $.param(params);
                                if (key == "kyso") {
                                    // if(ret.BAC_SI != singletonObject.userId) {
                                    //     return notifiToClient("Red", MESSAGEAJAX.PERMISSION);
                                    // }
                                    thongTinKyThuatPHCNTruocChinhSua = ret
                                    previewAndSignPdfDefaultModal({
                                        url: url,
                                        idButton: 'phieukythuatphcn_kysogd_action',
                                    }, function(){

                                    });
                                }
                                if (key == "huykyso") {
                                    // if(ret.DIEU_DUONG != singletonObject.userId) {
                                    //     return notifiToClient("Red", MESSAGEAJAX.PERMISSION);
                                    // }
                                    confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                        huykysoFilesign769("PHIEU_NOITRU_KYTHUATPHCN", ret.ID, singletonObject.userId, singletonObject.dvtt,
                                            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                                                reloadDSKyThuatPHCN();
                                            })
                                    }, function () {

                                    })
                                }
                                if (key == "xem") {
                                    xemKyThuatPHCN(ret);
                                }
                                if (key== "sua"){
                                    showUpdateKyThuatPHCN(ret)
                                }
                                if (key == "xoa") {
                                    deleteKyThuatPHCN(ret)
                                }
                            },
                            items: items
                        });
                    }
                }

            });
            $("#list_kythuatphcn").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
        }
    }

    function xemKyThuatPHCN(){
        $.get("cmu_getlist?url=" + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, 'CMU_GET_KYTHUATPHCN'])).done(function(data) {
            if(data.length > 0) {
                var res = data[0];
                if(res.KEYSIGN) {
                    getFilesign769(
                        "PHIEU_NOITRU_KYTHUATPHCN",
                        1,
                        -1,//singletonObject.userId,
                        singletonObject.dvtt,
                        thongtinhsba.thongtinbn.SOVAOVIEN,
                        thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                        -1,
                        function(data) {
                            // hideLoaderIntoWrapId("hsba_list_todieutri");
                            if(data.length > 0) {
                                getCMUFileSigned769(data[0].KEYMINIO,"pdf")
                            } else {
                                notifiToClient("Red", "Lỗi lấy dữ liệu ký số");
                            }
                        }
                    )
                } else {
                    var params = {
                        mabenhnhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                        tennguoibenh: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                        chandoan: getChuanDoanKTPHCN(),
                        sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                        sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                        gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                        tuoi: thongtinhsba.thongtinbn.TUOI,
                        khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                        stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                        stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                        stt_todieutri: selectedSttDieuTri
                    }
                    var url = 'cmu_in_cmu_kithuatphcn?type=pdf&' + $.param(params);
                    var idFrame = "preview_kithuatphcn";

                    previewPdfDefaultModal(url, idFrame);
                }
            } else {
            }
        }).fail(function() {
            notifiToClient("Red", "Lỗi lấy dữ liệu");
        })
    }

    function getIdDieuTri() {
        const danhSachToDieuTri = $.ajax({
            url: "cmu_getlist?url=" + convertArray([
                thongtinhsba.thongtinbn.STT_BENHAN,
                0,
                -1,
                singletonObject.dvtt,
                "HSBA_TODIEUTRI_CMU_SEL"
            ]),
            async: false
        }).responseText;

        const danhSachToDieuTriJson = JSON.parse(danhSachToDieuTri);
        if (danhSachToDieuTriJson && danhSachToDieuTriJson.length > 0) {
            const toDieuTri = danhSachToDieuTriJson.find(tdt => {
                return tdt.STT_DIEUTRI == selectedSttDieuTri;
            });

            if (toDieuTri) {
                return toDieuTri.ID_DIEUTRI;
            }
        }
        return '';
    }

    function getChuanDoanKTPHCN() {
        const danhSachChuanDoan = $.ajax({
            url: "cmu_getlist?url=" + convertArray([
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                getIdDieuTri(),
                "CMU_TDT_ICD_LIST"
            ]),
            async: false
        }).responseText;

        const danhSachChuanDoanJson = JSON.parse(danhSachChuanDoan);
        if (danhSachChuanDoanJson && danhSachChuanDoanJson.length > 0) {
            const chuanDoanChinh = danhSachChuanDoanJson.find(cd => {
                return cd.BENHCHINH == 1;
            });
            if (chuanDoanChinh) {
                const benhChinh = '(' + chuanDoanChinh.ICD + ')' + chuanDoanChinh.TENBENH;
                const dsBenhPhu = danhSachChuanDoanJson.filter(cd => {
                    return cd.BENHCHINH == 0;
                }).map(cd => {
                    return cd.ICD + '-' + cd.TENBENH;
                }).join('; ');

                return benhChinh + '; ' + dsBenhPhu;
            }
        }
        return '';
    }

    function showUpdateKyThuatPHCN(ret){
        $("#modalFormKyThuatPHCN").modal("show");
        $("#kythuatphcn_luu").attr("data-action", "CAP_NHAT");
        addTextTitleModal('titleFormKyThuatPHCN', "Phiếu thực hiện kỹ thuật phục hồi chức năng");
        thongTinKyThuatPHCNTruocChinhSua = ret
        formKyThuatPHCN.submission =  {
            data: {
                ...ret,
                THOIGIAN:  (ret.THOIGIAN? moment(ret.THOIGIAN, ['DD/MM/YYYY HH:mm']): moment()).toISOString(),
            }
        };
    }

    function deleteKyThuatPHCN(ret){
        var maGiay = ret.ID;
        thongTinKyThuatPHCNTruocChinhSua = ret
        confirmToClient("Bạn có chắc chắn muốn xóa phiếu này?", function() {
            var arr = [maGiay, singletonObject.dvtt]
            var url = "cmu_post_CMU_KYTHUATPHCN_DELETE";
            $.post(url, {
                url: arr.join("```")
            }).done(function (data) {
                if (data === "1") {
                    notifiToClient("Green", MESSAGEAJAX.DEL_SUCCESS)
                    reloadDSKyThuatPHCN();
                    var formData = { ...formKyThuatPHCN.submission.data}
                    var noidung = ["Số phiếu:"+ thongTinKyThuatPHCNTruocChinhSua.ID]
                    for (const key in formData) {
                        try {
                            var label = formKyThuatPHCN.getComponent(key).label;
                            if (label) {
                                noidung.push(formKyThuatPHCN.getComponent(key).label + ": " + getValueOfFormIO(formKyThuatPHCN.getComponent(key)));
                            }
                        } catch (error) {
                            // console.log("Error: ", error);
                        }
                    }
                    var dataLog = {
                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                        LOAI: LOGHSBALOAI.KYTHUATPHCN.KEY,
                        NOIDUNGBANDAU: noidung.join("; "),
                        NOIDUNGMOI: "",
                        USERID: singletonObject.userId,
                        ACTION: LOGHSBAACTION.DELETE.KEY,
                    }
                    luuLogHSBATheoBN(dataLog);

                } else {
                    notifiToClient("Red", MESSAGEAJAX.ERROR)
                }
            }).fail(function() {
                notifiToClient("Red", MESSAGEAJAX.ERROR)
            })
        }, function () {

        })
    }

    function checkKysokythuatphcn(){
        $.get("cmu_getlist?url=" + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, "CMU_GET_KYTHUATPHCN"])).done(function(data) {
            if(data.length > 0) {
                var res = data[0];
                if(res.KEYSIGN) {
                    $("#kythuatphcn_kyso").hide()
                    $(".themkythuatphcn").hide()
                    $("#kythuatphcn_huykyso").show()
                } else {
                    $("#kythuatphcn_kyso").show()
                    $(".themkythuatphcn").show()
                    $("#kythuatphcn_huykyso").hide()
                }
                $("#xemkythuatphcn").show()

            }else{
                $("#kythuatphcn_kyso").hide()
                $("#xemkythuatphcn").hide()
                $("#kythuatphcn_huykyso").hide()
            }
        }).fail(function() {
            notifiToClient("Red", "Lỗi lấy dữ liệu");
        })
    }

    // Bổ sung Mẫu chuẩn bị
    $("#mauChuanBiPhieuKyThuatPHCN").click(function() {
        let element = $("#mau_danhsachmaujson_wrap");
        element.attr("function-add", 'insertMauCBPhieuKyThuatPHCN');
        element.attr("function-chinhsua", 'editMauCBPhieuKyThuatPHCN');
        element.attr("function-select", 'selectMauCBPhieuKyThuatPHCN');
        element.attr("function-getdata", 'getdataMauCBPhieuKyThuatPHCN');
        element.attr("function-validate", 'formioCBPhieuKyThuatPHCNValidate');
        element.attr("data-key", 'MAUCBPHIEUKYTHUATPHCN');
        $("#modalMauChungJSON").modal("show");
        $.loadDanhSachMauChungJSON('MAUCBPHIEUKYTHUATPHCN')
    }); $.extend({
        insertMauCBPhieuKyThuatPHCN: function () {
            generateFormMauCBPhieuKyThuatPHCN({})
        },
        editMauCBPhieuKyThuatPHCN: function (rowSelect) {
            let json = JSON.parse(rowSelect.NOIDUNG);
            let dataMau = {}
            json.forEach(function(item) {
                dataMau[item.key] = item.value
            })
            generateFormMauCBPhieuKyThuatPHCN({
                ID: rowSelect.ID,
                TENMAU: rowSelect.TENMAU,
                ...dataMau
            })
        },
        selectMauCBPhieuKyThuatPHCN: function (rowSelect) {
            let json = JSON.parse(rowSelect.NOIDUNG);
            json.forEach(function(item) {
                $(`#formNhapKyThuatPHCN [name="data[${item.key}]"]`).val(item.value)
                formKyThuatPHCN.data[item.key] = item.value
            })
            $("#modalMauChungJSON").modal("hide");
        },
        getdataMauCBPhieuKyThuatPHCN: function () {
            let objectNoidung = [];
            getObjectMauCBPhieuKyThuatPHCN().forEach(function(item) {
                if (item.key !== 'ID' && item.key !== 'TENMAU') {
                    objectNoidung.push({
                        "label": item.label,
                        "value": formioMauHSBA.submission.data[item.key],
                        "key": item.key,
                    })
                }
            })
            return {
                ID: formioMauHSBA.submission.data.ID,
                TENMAU: formioMauHSBA.submission.data.TENMAU,
                NOIDUNG: JSON.stringify(objectNoidung),
                KEYMAUCHUNG: 'MAUCBPHIEUKYTHUATPHCN'
            };
        },
        formioCBPhieuKyThuatPHCNValidate: function() {
            formioMauHSBA.emit("checkValidity");
            return formioMauHSBA.checkValidity(null, false, null, true);

        },
    });

    function generateFormMauCBPhieuKyThuatPHCN(dataForm) {
        let jsonForm = getJSONObjectForm(getObjectMauCBPhieuKyThuatPHCN());
        Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
            jsonForm,{}
        ).then(function(form) {
            formioMauHSBA = form;
            formioMauHSBA.submission = { data: { ...dataForm }}
        });
    }

    function getObjectMauCBPhieuKyThuatPHCN() {
        return [
            {
                "label": "ID",
                "key": "ID",
                "type": "textfield",
                others: {
                    hidden: true
                }
            },
            {
                "label": "Tên mẫu",
                "key": "TENMAU",
                "type": "textarea",
                validate: {
                    required: true
                },
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Thời gian thực hiện (phút)",
                "key": "THOIGIANPHUT",
                "type": "textfield",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Diễn biến bệnh, tật/ các vấn đề cần PHCN",
                "key": "DIENBIENBENH",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Tên dịch vụ kỹ thuật PHCN",
                "key": "TENDICHVU",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            }
        ];
    }

})