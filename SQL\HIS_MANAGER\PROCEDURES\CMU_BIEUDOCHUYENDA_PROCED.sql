CREATE OR REPLACE 
PROCEDURE  HIS_MANAGER.CMU_BIEUDOCHUYENDA_PROCED(
    p_id                IN      NUMBER,
    p_dvtt              IN     VARCHAR2,
    p_so_vao_vien      IN     NUMBER,
    cur OUT SYS_REFCURSOR
) AS
v_timthai clob;
v_CTC clob;
v_machhuyetap clob;
v_concctc clob;
BEGIN
begin
select IMAGE into v_timthai
from CMU_BIEUDOCHUYENDA_CHART
where dvtt = p_Dvtt and ID_BIEUDO = p_id and TYPECHART = 'TIMTHAI';
exception
	when no_data_found then v_timthai:=null;
end;

begin
select IMAGE into v_CTC
from CMU_BIEUDOCHUYENDA_CHART
where dvtt = p_Dvtt and ID_BIEUDO = p_id and TYPECHART = 'CTC';
exception
	when no_data_found then v_CTC:=null;
end;

begin
select IMAGE into v_machhuyetap
from CMU_BIEUDOCHUYENDA_CHART
where dvtt = p_Dvtt and ID_BIEUDO = p_id and TYPECHART = 'MACHHUYETAP';
exception
	when no_data_found then v_machhuyetap:=null;
end;

begin
select IMAGE into v_concctc
from CMU_BIEUDOCHUYENDA_CHART
where dvtt = p_Dvtt and ID_BIEUDO = p_id and TYPECHART = 'CONCCTC';
exception
	when no_data_found then v_concctc:=null;
end;

delete from CMU_BIEUDOCHUYENDA_CHART
where dvtt = p_Dvtt and ID_BIEUDO = p_id;

open cur for
select
    phieu.ID,
    phieu.DVTT,
    phieu.SOVAOVIEN,
    phieu.MA_BENH_NHAN,
    phieu.PARA,
    TO_CHAR(phieu.NGAY_GHI_BIEU_DO, 'dd/MM/yyyy') NGAY_GHI_BIEU_DO,
    phieu.OI_DA_VO,

    TO_CHAR(phieu.NGAY_TAO_PHIEU, 'dd/MM/yyyy') NGAY_TAO_PHIEU,
    phieu.NGUOI_TAO,
    nt.SOBENHAN MABENHAN,
    TO_CHAR(nt.NGAYNHAPVIEN, 'HH24:MI DD/MM/YYYY') NGAY_NHAP_VIEN,
    nt.TENKHOA_NHAPVIENVAOKHOA KHOA,
    bn.TEN_BENH_NHAN,
    v_timthai NHIPTIMTHAI,
    v_CTC DOMOCTC,
    v_machhuyetap MACHHUYTAP,
    v_concctc CONCCTC,
    MA_DIEU_DUONG,
    MA_BAC_SI,
    SANHTHUONG,
    CANNANG,
    GIOITINH,
    APGAR01,
    APGAR05,
    APGAR10,
    SONHAU,
    COTUCUNG,
    AMDAO,
    TANGSINHMON,
    to_char(NGAY_GIO_SINH, 'DD/MM/YYYY HH24:MI') NGAY_GIO_SINH,
    bacsi.TEN_NHANVIEN_CD bacsi,
    DD.TEN_NHANVIEN_CD DIEUDUONG,
    NT.TEN_NHANVIEN_CD NGUOI_TAO
FROM CMU_BIEUDOCHUYENDA phieu
         LEFT JOIN his_public_list.dm_benh_nhan bn ON phieu.MA_BENH_NHAN = bn.ma_benh_nhan
         LEFT JOIN his_manager.noitru_benhan nt ON phieu.MA_BENH_NHAN = nt.MABENHNHAN AND phieu.SOVAOVIEN = nt.SOVAOVIEN
         left join HIS_FW.DM_NHANVIEN_CD BACSI on phieu.MA_BAC_SI = bacsi.MA_NHANVIEN
         left join HIS_FW.DM_NHANVIEN_CD DD on phieu.MA_DIEU_DUONG = DD.MA_NHANVIEN
         left join HIS_FW.DM_NHANVIEN_CD NT on phieu.NGUOI_TAO = NT.MA_NHANVIEN
WHERE phieu.ID = p_id AND phieu.dvtt = p_dvtt AND phieu.SOVAOVIEN = p_so_vao_vien
order by phieu.NGAY_TAO_PHIEU;
END;
