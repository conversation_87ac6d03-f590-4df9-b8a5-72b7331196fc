function xemPhieuPhanLoaiNB(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkPhanLoaiNguoiBenh = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_PHANLOAINB"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieuphanloainb-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlPhanLoaiNB(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "<PERSON><PERSON><PERSON><PERSON> định - <PERSON><PERSON> loại người bệnh - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "PHANLOAINGUOIBENH",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_PLNB_341",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkPhanLoaiNguoiBenh.push(objTemp);
                        } else {
                            notifiToClient("Red", "Nhận định - Phân loại người bệnh - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieuphanloainb-tab").hide();
            }
        });
    });
}

function xemPhieuBanGiaoBS(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkBanGiaoBS = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_PHIEUBANGIAOBS"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieubangiaobs-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlBanGiaoBS(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Bàn giao người bệnh chuyển khoa (dành cho bác sĩ) - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "BANGIAOBACSI",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_BANGIAONGUOIBENH_340",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkBanGiaoBS.push(objTemp);
                        } else {
                            notifiToClient("Red", "Bàn giao người bệnh chuyển khoa (dành cho bác sĩ) - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieubangiaobs-tab").hide();
            }
        });
    });
}

function xemPhieuDeXuatPTTT(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkDeXuatPTTT = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_DEXUATPTTT"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieudexuatpttt-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlDeXuatPTTT(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu đề xuất phẫu thuật, thủ thuật theo yêu cầu - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "DEXUATPTTT",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "RPT_SCAN",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkDeXuatPTTT.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu đề xuất phẫu thuật, thủ thuật theo yêu cầu - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieudexuatpttt-tab").hide();
            }
        });
    });
}

function xemGiayChungNhan(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkGiayChungNhan = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.STT_BENHAN, itemDDT.MABENHNHAN, "-1", "HSBA_DSTTPT_TONGHOP"]),
            type: "GET",
        }).done(function (dataPhieu) {
            console.log(dataPhieu)
            if(dataPhieu.length > 0) {
                $("#hsba-giaychungnhan-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var arr = [
                        item.MA_DV,
                        item.SO_PHIEU_DICHVU,
                        '',
                        singletonObject.dvtt,
                        1,
                        thongtinhsba.thongtinbn.STT_BENHAN,
                        item.STT_DOTDIEUTRI,
                        item.STT_DIEUTRI,
                        item.SOVAOVIEN,
                        thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                        thongtinhsba.thongtinbn.TUOI_HT,
                        thongtinhsba.thongtinbn.GIOI_TINH_HT]
                    var url = "inphieuchungnhanttpt?url=" + convertArray(arr) +'&&viewPDF=1';
                    var arrTemp = [];
                    getFilesign769("PHIEU_NOITRU_GIAYCHUNGNHAN_PTV", item.SO_PHIEU_DICHVU, -1, singletonObject.dvtt,
                        itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, -1, function(dataKySo) {
                            if (dataKySo.length > 0) {
                                arrTemp.push(dataKySo[0])
                            }
                        });
                    getFilesign769("PHIEU_NOITRU_GIAYCHUNGNHAN_TRUONGKHOA", item.SO_PHIEU_DICHVU, -1, singletonObject.dvtt,
                        itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, -1, function(dataKySo) {
                            if (dataKySo.length > 0) {
                                arrTemp.push(dataKySo[0])
                            }
                        });
                    getFilesign769("PHIEU_NOITRU_GIAYCHUNGNHAN_BGD", item.SO_PHIEU_DICHVU, -1, singletonObject.dvtt,
                        itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, -1, function(dataKySo) {
                            if (dataKySo.length > 0) {
                                arrTemp.push(dataKySo[0])
                            }
                        });
                    var maxCreateDate = null;
                    var maxCreateDataObject = null;
                    if(arrTemp.length > 0) {
                        $.each(arrTemp, function(index, dataObject) {
                            var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                            if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                                maxCreateDate = createDate;
                                maxCreateDataObject = dataObject;
                            }
                        });
                        getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                            url = pdfData;
                            thongtinhsba.thongtinbn.linkGiayChungNhan.push({
                                "url": [url],
                                "name": "Giấy chứng nhận: " + item.SO_PHIEU_DICHVU,
                                "keyEMR": "RPT_SCAN",
                                "key": "GIAYCHUNGNHAN",
                                "idPhieu": maxCreateDataObject.ID,
                            });
                        }).catch(error => {
                            thongtinhsba.thongtinbn.linkGiayChungNhan.push({
                                "url": [url],
                                "name": "Giấy chứng nhận: " + item.SO_PHIEU_DICHVU,
                                "key": "GIAYCHUNGNHAN",
                            });
                        });
                    } else {
                        thongtinhsba.thongtinbn.linkGiayChungNhan.push({
                            "url": [url],
                            "name": "Giấy chứng nhận: " + item.SO_PHIEU_DICHVU,
                            "key": "GIAYCHUNGNHAN",
                        });
                    }
                });
            } else {
                $("#hsba-giaychungnhan-tab").hide();
            }
        });
    });
}

function xemPhieuBanGiaoDD(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkBanGiaoDD = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt,itemDDT.MABENHNHAN, itemDDT.SOVAOVIEN,
                itemDDT.SOVAOVIEN_DT, itemDDT.STT_BENHAN, 'CMU_BAN_GIAO_CHUYEN_KHOA_LIST']),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieubangiaodd-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var formData = JSON.parse(item.DATA_DIEU_DUONG);
                    var params = {
                        dvtt: singletonObject.dvtt,
                        HO_TEN: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                        TUOI: thongtinhsba.thongtinbn.TUOI,
                        GIOI_TINH: thongtinhsba.thongtinbn.GIOI_TINH === 1 ?'NAM':'NU',
                        MS:  thongtinhsba.thongtinbn.MABENHNHAN,
                        SO_VAO_VIEN:  thongtinhsba.thongtinbn.SOBENHAN,
                        MA_NGUOI_BENH:  thongtinhsba.thongtinbn.MABENHNHAN,
                        ...item,
                        ...formData,
                        ...formData.TAI_LIEU_BAN_GIAO,
                        NGAY_GIO_CHUYEN: "Ngày giờ chuyển: " + moment(formData.NGAY_GIO_CHUYEN).utcOffset("+07:00").format("DD/MM/YYYY HH:mm"),
                        BS_CHI_DINH_CHUYEN: _.get(formData, 'BS_CHI_DINH_CHUYEN.TEN_NHANVIEN', "" ),
                        CHUYEN_TU: _.get(formData, 'CHUYEN_TU.TENKHOA', "" ),
                        CHUYEN_DEN: _.get(formData, 'CHUYEN_DEN.TENKHOA', "" ),
                        DT_TM_NGOAI_BIEN_NGAY_DAT: formData.DT_TM_NGOAI_BIEN_NGAY_DAT? moment(formData.DT_TM_NGOAI_BIEN_NGAY_DAT).utcOffset("+07:00").format("DD/MM/YYYY HH:mm"): "",
                        DT_TM_TT_NGAY_DAT: formData.DT_TM_TT_NGAY_DAT? moment(formData.DT_TM_TT_NGAY_DAT).utcOffset("+07:00").format("DD/MM/YYYY HH:mm"): "",
                        DT_DM_NGAY_DAT: formData.DT_DM_NGAY_DAT? moment(formData.DT_DM_NGAY_DAT).utcOffset("+07:00").format("DD/MM/YYYY HH:mm"): "",
                        ONG_THONG_TIEU_NGAY_DAT: formData.ONG_THONG_TIEU_NGAY_DAT? moment(formData.ONG_THONG_TIEU_NGAY_DAT).utcOffset("+07:00").format("DD/MM/YYYY HH:mm"): "",
                        BANG_VET_THUONG_NGAY_CAT_CHI: formData.BANG_VET_THUONG_NGAY_CAT_CHI? moment(formData.BANG_VET_THUONG_NGAY_CAT_CHI).utcOffset("+07:00").format("DD/MM/YYYY"): "",
                        THUOC_DT_TRONG_NGAY_LUC: formData.THUOC_DT_TRONG_NGAY_LUC? moment(formData.THUOC_DT_TRONG_NGAY_LUC).utcOffset("+07:00").format("DD/MM/YYYY HH:mm"): "",
                        THUOC_CAN_SD_LUC: formData.THUOC_CAN_SD_LUC? moment(formData.THUOC_CAN_SD_LUC).utcOffset("+07:00").format("DD/MM/YYYY HH:mm"): "",
                        DIEU__DUONG_CHUYEN: formData.DIEU_DUONG_CHUYEN.TENNHANVIEN,
                        DIEU_DUONG_NHAN: formData.DIEU_DUONG_NHAN.TENNHANVIEN,
                    }
                    var url = 'cmu_in_rp_phieu_ban_giao_dd?type=pdf&' + $.param(params);
                    var arrTemp = [];
                    getFilesign769("PHIEU_NOITRU_BANGIAO_DDKHOA", item.ID, -1, singletonObject.dvtt,
                        itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, -1, function(dataKySo) {
                            if (dataKySo.length > 0) {
                                arrTemp.push(dataKySo[0])
                            }
                        });
                    getFilesign769("PHIEU_NOITRU_BANGIAO_DDKHOANHAN", item.ID, -1, singletonObject.dvtt,
                        itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, -1, function(dataKySo) {
                            if (dataKySo.length > 0) {
                                arrTemp.push(dataKySo[0])
                            }
                        });
                    var maxCreateDate = null;
                    var maxCreateDataObject = null;
                    if(arrTemp.length > 0) {
                        $.each(arrTemp, function(index, dataObject) {
                            var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                            if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                                maxCreateDate = createDate;
                                maxCreateDataObject = dataObject;
                            }
                        });
                        getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                            url = pdfData;
                            thongtinhsba.thongtinbn.linkBanGiaoDD.push({
                                "url": [url],
                                "name": "Bàn giao người bệnh chuyển khoa (điều dưỡng) " + item.ID,
                                "keyEMR": "BM_PHIEU_BANGIAONGUOIBENH_340",
                                "key": "BANGIAODIEUDUONG",
                                "idPhieu": maxCreateDataObject.ID,
                            });
                        }).catch(error => {
                            thongtinhsba.thongtinbn.linkBanGiaoDD.push({
                                "url": [url],
                                "name": "Bàn giao người bệnh chuyển khoa (điều dưỡng) " + item.ID,
                                "key": "BANGIAODIEUDUONG",
                            });
                        });
                    } else {
                        thongtinhsba.thongtinbn.linkBanGiaoDD.push({
                            "url": [url],
                            "name": "Bàn giao người bệnh chuyển khoa (điều dưỡng) " + item.ID,
                            "key": "BANGIAODIEUDUONG",
                        });
                    }
                });
            } else {
                $("#hsba-phieubangiaodd-tab").hide();
            }
        });
    });
}

function xemPhieuTruyenMau(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkTruyenMau = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_DSBENHNHAN_TRUYENMAU_SVV"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieutruyenmau-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var arr = [singletonObject.dvtt, itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, item.IDTRUYENMAU, thongtinhsba.thongtinbn.TEN_BENH_NHAN, thongtinhsba.thongtinbn.GIOI_TINH == 1? "Nam": "Nữ", " "];
                    var param = ['dvtt', 'sovaovien', 'sovaovien_dt', 'id', 'hoten', 'gioitinh', "giuong"];
                    var url = "cmu_injasper?url=" + convertArray(arr) + "&param=" + convertArray(param) + "&loaifile=pdf&jasper=rp_phieutruyenmau_khoalamsang";
                    var arrTemp = [];
                    getFilesign769("PHIEU_NOITRU_PHIEUTRUYENMAU_KHOALAMSANG_DIEUDUONG", item.IDTRUYENMAU, -1, singletonObject.dvtt,
                        itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, -1, function(dataKySo) {
                            if (dataKySo.length > 0) {
                                arrTemp.push(dataKySo[0])
                            }
                        });
                    getFilesign769("PHIEU_NOITRU_PHIEUTRUYENMAU_KHOALAMSANG_BACSI", item.IDTRUYENMAU, -1, singletonObject.dvtt,
                        itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, -1, function(dataKySo) {
                            if (dataKySo.length > 0) {
                                arrTemp.push(dataKySo[0])
                            }
                        });
                    var maxCreateDate = null;
                    var maxCreateDataObject = null;
                    if(arrTemp.length > 0) {
                        $.each(arrTemp, function(index, dataObject) {
                            var createDate = moment(dataObject.CREATE_DATE_STRING, "DD/MM/YYYY HH:mm:ss");
                            if (maxCreateDate === null || createDate.isAfter(maxCreateDate)) {
                                maxCreateDate = createDate;
                                maxCreateDataObject = dataObject;
                            }
                        });
                        getCMUFileSigned769GetLink(maxCreateDataObject.KEYMINIO, 'pdf').then(pdfData => {
                            url = pdfData;
                            thongtinhsba.thongtinbn.linkTruyenMau.push({
                                "url": [url],
                                "name": "Phiếu truyền máu" + item.ID,
                                "keyEMR": "BM_PHIEU_TRUYENMAU_101",
                                "key": "PHIEUTRUYENMAU",
                                "idPhieu": maxCreateDataObject.ID,
                            });
                        }).catch(error => {
                            thongtinhsba.thongtinbn.linkTruyenMau.push({
                                "url": [url],
                                "name": "Phiếu truyền máu" + item.ID,
                                "key": "PHIEUTRUYENMAU",
                            });
                        });
                    } else {
                        thongtinhsba.thongtinbn.linkTruyenMau.push({
                            "url": [url],
                            "name": "Phiếu truyền máu" + item.ID,
                            "key": "PHIEUTRUYENMAU",
                        });
                    }
                });
            } else {
                $("#hsba-phieutruyenmau-tab").hide();
            }
        });
    });
}

function xemPhieuTheoDoiGiucSanh(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkTheoDoiGiucSanh = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_THEODOIGIUCSANH"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieutdgiucsanh-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                var params = {
                    ID: '-1'
                }
                getUrlTheoDoiGiucSanh(params).then(objReturn => {
                    if (objReturn.isError == 0) {
                        var objTemp = {
                            "url": [objReturn.url],
                            "name": "Phiếu theo dõi giục sanh - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            "key": "THEODOIGIUCSANH",
                        }
                        if (objReturn.kySo == 1) {
                            objTemp = {
                                ...objTemp,
                                "keyEMR": "RPT_SCAN",
                                "idPhieu": objReturn.idKySo,
                            }
                        }
                        thongtinhsba.thongtinbn.linkTheoDoiGiucSanh.push(objTemp);
                    } else {
                        notifiToClient("Red", "Phiếu theo dõi giục sanh - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                    }
                }).catch(error => {
                    // notifiToClient("Red", error.message || "Lỗi không xác định");
                });
            } else {
                $("#hsba-phieutdgiucsanh-tab").hide();
            }
        });
    });
}

function xemPhieuTheoDoi6hSauDe(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkTheoDoi6hSauDe = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_THEODOI6HSAUDE"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieutd6hsaude-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                var params = {
                    ID: -1
                }
                getUrlTD6hSauDe(params).then(objReturn => {
                    if (objReturn.isError == 0) {
                        var objTemp = {
                            "url": [objReturn.url],
                            "name": "Phiếu theo dõi bà mẹ và trẻ sơ sinh 6h sau đẻ - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            "key": "THEODOI6HSAUDE",
                        }
                        if (objReturn.kySo == 1) {
                            objTemp = {
                                ...objTemp,
                                "keyEMR": "RPT_SCAN",
                                "idPhieu": objReturn.idKySo,
                            }
                        }
                        thongtinhsba.thongtinbn.linkTheoDoi6hSauDe.push(objTemp);
                    } else {
                        notifiToClient("Red", "Phiếu theo dõi bà mẹ và trẻ sơ sinh 6h sau đẻ - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                    }
                }).catch(error => {
                    // notifiToClient("Red", error.message || "Lỗi không xác định");
                });
            } else {
                $("#hsba-phieutd6hsaude-tab").hide();
            }
        });
    });
}

function xemPhieuTheoDoiTienSanGiat(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkTheoDoiTienSanGiat = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_THEODOITIENSANGIAT"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieutdtiensangiat-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                var params = {
                    ID: -1
                }
                getUrlTDTienSanGiat(params).then(objReturn => {
                    if (objReturn.isError == 0) {
                        var objTemp = {
                            "url": [objReturn.url],
                            "name": "Phiếu theo dõi tiền sản giật - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            "key": "THEODOITIENSANGIAT",
                        }
                        if (objReturn.kySo == 1) {
                            objTemp = {
                                ...objTemp,
                                "keyEMR": "RPT_SCAN",
                                "idPhieu": objReturn.idKySo,
                            }
                        }
                        thongtinhsba.thongtinbn.linkTheoDoiTienSanGiat.push(objTemp);
                    } else {
                        notifiToClient("Red", "Phiếu theo dõi tiền sản giật - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                    }
                }).catch(error => {
                    // notifiToClient("Red", error.message || "Lỗi không xác định");
                });
            } else {
                $("#hsba-phieutdtiensangiat-tab").hide();
            }
        });
    });
}

function xemPhieuThuPhanUngThuoc(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkThuPhanUngThuoc = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.MABENHNHAN, "CMU_DSPHIEU_PHAN_UNG_THUOC_GET"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieuthuphanungthuoc-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ma_benh_nhan: thongtinhsba.thongtinbn.MABENHNHAN,
                        sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                        mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                        GIOI_TINH1: thongtinhsba.thongtinbn.GIOI_TINH_HT
                    }
                    var url = 'cmu_in_cmu_phieuthuphanungthuoc?type=pdf&' + $.param(params);
                    getFilesign769(
                        "PHIEU_NOITRU_PHANUNGTHUOC",
                        item.ID,
                        -1,//singletonObject.userId,
                        singletonObject.dvtt,
                        itemDDT.SOVAOVIEN,
                        itemDDT.SOVAOVIEN_DT,
                        -1,
                        function(dataKySo) {
                            if(dataKySo.length > 0) {
                                getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                    url = pdfData;
                                    thongtinhsba.thongtinbn.linkThuPhanUngThuoc.push({
                                        "url": [url],
                                        "name": "Phiếu thử phản ứng thuốc " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                        "keyEMR": "RPT_SCAN",
                                        "key": "PHIEUTHUPHANUNGTHUOC",
                                        "idPhieu": dataKySo[0].ID,
                                    });
                                }).catch(error => {
                                    thongtinhsba.thongtinbn.linkThuPhanUngThuoc.push({
                                        "url": [url],
                                        "name": "Phiếu thử phản ứng thuốc " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                        "key": "PHIEUTHUPHANUNGTHUOC",
                                    });
                                });
                            } else {
                                thongtinhsba.thongtinbn.linkThuPhanUngThuoc.push({
                                    "url": [url],
                                    "name": "Phiếu thử phản ứng thuốc" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                    "key": "PHIEUTHUPHANUNGTHUOC",
                                });
                            }
                        }
                    )
                });
            } else {
                $("#hsba-phieuthuphanungthuoc-tab").hide();
            }
        });
    });
}

function xemPhieuPCA(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkPCA = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, "CMU_GET_PHIEU_PCA"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieupca-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                        NGAY_TAO_PHIEU: item.NGAY_TAO_PHIEU,
                    }
                    getUrlPhieuPCA(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu PCA - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "PHIEUPCA",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "RPT_SCAN",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkPCA.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu PCA - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieupca-tab").hide();
            }
        });
    });
}

function xemBienBanHop(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkBienBanHop = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_BIENBANHOP"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-bienbanhop-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                        NGAY_TAO_PHIEU: item.NGAY_TAO_PHIEU,
                    }
                    getUrlBienBanHop(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Biên bản họp - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "BIENBANHOP",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "RPT_SCAN",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkBienBanHop.push(objTemp);
                        } else {
                            notifiToClient("Red", "Biên bản họp - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-bienbanhop-tab").hide();
            }
        });
    });
}


function xemCamKetDTXaTri(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkCamKetDTXaTri = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_CAMKETDTXATRI"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieucamketdtxatri-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlCamKetDTXaTri(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu cam kết chấp thuận điều trị bằng xạ trị - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "CAMKETDTXATRI",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_CAMKETCHUNG_146",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkCamKetDTXaTri.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu cam kết chấp thuận điều trị bằng xạ trị - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieucamketdtxatri-tab").hide();
            }
        });
    });
}

function xemCamKetDTHoaXaTri(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkCamKetDTHoaXaTri = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_CAMKETDTHOAXATRI"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieucamketdthoaxatri-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlCamKetDTHoaXaTri(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu cam kết chấp thuận điều trị bằng hóa trị - xạ trị - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "CAMKETDTHOAXATRI",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_CAMKETCHUNG_146",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkCamKetDTHoaXaTri.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu cam kết chấp thuận điều trị bằng hóa trị - xạ trị - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieucamketdthoaxatri-tab").hide();
            }
        });
    });
}

function xemGiayCamDoanSuDung(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkGiayCamDoanSuDung = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_GIAYCAMDOANSUDUNG"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-giaycamdoansudung-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlGiayCDSuDung(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu cam đoan (Sử dụng cho bệnh nhân dưới 06 tuổi miễn phí, viện phí và người bệnh có thẻ BHYT) - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "GIAYCAMDOANSUDUNG",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "RPT_SCAN",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkGiayCamDoanSuDung.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu cam đoan (Sử dụng cho bệnh nhân dưới 06 tuổi miễn phí, viện phí và người bệnh có thẻ BHYT) - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-giaycamdoansudung-tab").hide();
            }
        });
    });
}

function xemPhieuDYXNHIVGhiTen(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkDYXetNghiemHIVGhiTen = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_DONGYXETNGHIEMHIV"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieudyxnhivghiten-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlDongYXNHIVGhiTen(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu đồng ý xét nghiệm HIV ghi tên - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "DONGYXETNGHIEMHIVGHITEN",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "RPT_SCAN",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkDYXetNghiemHIVGhiTen.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu đồng ý xét nghiệm HIV ghi tên - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieudyxnhivghiten-tab").hide();
            }
        });
    });
}

function xemPhieuDangKyKBTheoYC(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkDangKyKBTheoYC = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_DANGKYKBTHEOYC"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieudangkykbtheoyc-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlDangKyKBTheoYC(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu đăng ký khám chữa bệnh theo yêu cầu - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "DANGKYKBTHEOYC",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "RPT_SCAN",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkDangKyKBTheoYC.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu đăng ký khám chữa bệnh theo yêu cầu - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieudangkykbtheoyc-tab").hide();
            }
        });
    });
}

function xemPhieuKiemTiemChungTreSS(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkKiemTiemChungTreSS = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, "CMU_KTTIEMCHUNGTRESOSINH_GET"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieukiemtiemchungtress-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var url = 'cmu_in_cmu_bangkiemtruoctiemdoivoitresosinh?type=pdf&' + $.param({
                        dvtt: singletonObject.dvtt,
                        id: item.ID
                    });
                    getFilesign769(
                        "PHIEU_NOITRU_KIEMTRUOCTIEMCHUNGTRESOSINH",
                        item.ID,
                        -1,//singletonObject.userId,
                        singletonObject.dvtt,
                        itemDDT.SOVAOVIEN,
                        itemDDT.SOVAOVIEN_DT,
                        -1,
                        function(dataKySo) {
                            if(dataKySo.length > 0) {
                                getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                    url = pdfData;
                                    thongtinhsba.thongtinbn.linkKiemTiemChungTreSS.push({
                                        "url": [url],
                                        "name": "Phiếu kiểm trước tiêm chủng đối với trẻ sơ sinh " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                        "keyEMR": "RPT_SCAN",
                                        "key": "KIEMTIEMCHUNGTRESS",
                                        "idPhieu": dataKySo[0].ID,
                                    });
                                }).catch(error => {
                                    thongtinhsba.thongtinbn.linkKiemTiemChungTreSS.push({
                                        "url": [url],
                                        "name": "Phiếu kiểm trước tiêm chủng đối với trẻ sơ sinh " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                        "key": "KIEMTIEMCHUNGTRESS",
                                    });
                                });
                            } else {
                                thongtinhsba.thongtinbn.linkKiemTiemChungTreSS.push({
                                    "url": [url],
                                    "name": "Phiếu kiểm trước tiêm chủng đối với trẻ sơ sinh " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                    "key": "KIEMTIEMCHUNGTRESS",
                                });
                            }
                        }
                    )
                });
            } else {
                $("#hsba-phieukiemtiemchungtress-tab").hide();
            }
        });
    });
}

function xemPhieuKeHoachChamSoc(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkKeHoachChamSoc = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, "CMU_KEHOACHCHAMSOC_GET"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieukehoachchamsoc-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlKeHoachChamSoc(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu kế hoạch chăm sóc - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "KEHOACHCHAMSOC",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "RPT_SCAN",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkKeHoachChamSoc.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu kế hoạch chăm sóc - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieukehoachchamsoc-tab").hide();
            }
        });
    });
}

function xemPhieuCSC1Khac(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkCSC1Khac = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_PHIEUCHAMSOCCAP1"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieucsc1khac-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlPhieuCSC1Khac(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu chăm sóc cấp 1 - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "CHAMSOCCAP1KHAC",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "RPT_SCAN",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkCSC1Khac.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu chăm sóc cấp 1 - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieucsc1khac-tab").hide();
            }
        });
    });
}

function xemPhieuChamSocCap1(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkPCS1 = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, "1", itemDDT.MABENHNHAN, itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, "CMU_LANCHAMSOCCAP_SEL_V2"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieucsc1-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID_CHAM_SOC_CAP_1: item.ID_CHAM_SOC_CAP_1,
                        ID_CHI_TIET: -1,
                    }
                    getUrlPCSC1(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu chăm sóc cấp 1" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "CHAMSOCCAP1",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_PHIEUCHAMSOC_13",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkPCS1.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu chăm sóc cấp 1 - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });


                    // $.get("cmu_getlist?url=" + convertArray([item.ID_CHAM_SOC_CAP_1, "CMU_LCSC1_CHITIET_SEL24ROW_V2"])).done(function(data) {
                    //     if (data.length > 0) {
                    //         var dsPdf = [];
                    //         for(var p = 0; p < Math.ceil(data.length/24); p++){
                    //             var params = {
                    //                 dvtt: singletonObject.dvtt,
                    //                 mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    //                 ngayvaovien: thongtinhsba.thongtinbn.NGAYGIO_NHAPVIEN ? thongtinhsba.thongtinbn.NGAYGIO_NHAPVIEN : thongtinhsba.thongtinbn.NGAYNHAPVIEN,
                    //                 ngaydautien: data[0].ONLY_NGAY,
                    //                 id_phieu: item.ID_CHAM_SOC_CAP_1,
                    //                 page: p,
                    //             };
                    //             for(var i = 0; i < 24; i++) {
                    //                 var item2 = data[i + p * 24];
                    //                 if (item2) {
                    //                     var object = {};
                    //                     $.each(item2, function(key, value) {
                    //                         object[key.toLowerCase() + "_" + (i+1)] = value;
                    //                     });
                    //                     params = {
                    //                         ...params,
                    //                         ...object
                    //                     };
                    //                 }
                    //             }
                    //             console.log("params", params)
                    //             var url = 'cmu_in_rp_chamsoccap1?type=pdf&' + $.param(params);
                    //             dsPdf.push({
                    //                 "url": [url],
                    //                 "name": "Phiếu chăm sóc cấp 1" + " trang " + (p+1)
                    //             });
                    //         }
                    //         loadAndCombinePDFs(dsPdf).then(data => {
                    //             getFilesign769(
                    //                 "PHIEU_NOITRU_CHAMSOCCAP1",
                    //                 item.ID_CHAM_SOC_CAP_1,
                    //                 -1,
                    //                 singletonObject.dvtt,
                    //                 itemDDT.SOVAOVIEN,
                    //                 itemDDT.SOVAOVIEN_DT,
                    //                 -1,
                    //                 function(dataKySo) {
                    //                     if(dataKySo.length > 0) {
                    //                         getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                    //                             url = pdfData;
                    //                             thongtinhsba.thongtinbn.linkPCS1.push({
                    //                                 "url": [url],
                    //                                 "name": "Phiếu chăm sóc cấp 1" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    //                                 "keyEMR": "BM_PHIEU_PHIEUCHAMSOC_13",
                    //                                 "key": "CHAMSOCCAP1",
                    //                                 "idPhieu": dataKySo[0].ID,
                    //                             });
                    //                         }).catch(error => {
                    //                             thongtinhsba.thongtinbn.linkPCS1.push({
                    //                                 "url": [url],
                    //                                 "name": "Phiếu chăm sóc cấp 1" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    //                                 "key": "CHAMSOCCAP1",
                    //                             });
                    //                         });
                    //                     } else {
                    //                         thongtinhsba.thongtinbn.linkPCS1.push({
                    //                             "url": [url],
                    //                             "name": "Phiếu chăm sóc cấp 1" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    //                             "key": "CHAMSOCCAP1",
                    //                         });
                    //                     }
                    //                 }
                    //             )
                    //         }).catch(error => {
                    //             notifiToClient("Red", "Có lỗi xảy ra:", error);
                    //         });
                    //     }
                    // });

                });
            } else {
                $("#hsba-phieucsc1-tab").hide();
            }
        });
    });
}

// function xemPhieuChamSocCap2(dsDotDieuTri) {
//     thongtinhsba.thongtinbn.linkPCS2 = [];
//     dsDotDieuTri.forEach(function (itemDDT) {
//         $.ajax({
//             url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.MABENHNHAN, itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, "CMU_LANCHAMSOCCAP2_SEL"]),
//             type: "GET",
//         }).done(function (dataPhieu) {
//             if(dataPhieu.length > 0) {
//                 $("#hsba-phieucsc2-tab").show();
//                 $("#wrap_Menu_PhieuKhac").show();
//                 dataPhieu.forEach(function (item) {
//                     $.get("cmu_getlist?url=" + convertArray([item.ID_CHAM_SOC_CAP_2, "CMU_LCSC2_CHITIET_SEL24ROW"])).done(function(data) {
//                         if (data.length > 0) {
//                             var dsPdf = [];
//                             for(var p = 0; p < Math.ceil(data.length/3); p++){
//                                 var params = {
//                                     dvtt: singletonObject.dvtt,
//                                     mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
//                                     ngayvaovien: thongtinhsba.thongtinbn.NGAYGIO_NHAPVIEN,
//                                     ngaydautien: data[0].ONLY_NGAY,
//                                     id_phieu: item.ID_CHAM_SOC_CAP_1,
//                                     page: p,
//                                 };
//                                 for(var i = 0; i < 3; i++) {
//                                     var item2 = data[i + p * 3];
//                                     if (item2) {
//                                         var object = {};
//                                         $.each(item2, function(key, value) {
//                                             object[key.toLowerCase() + "_" + (i+1)] = value;
//                                         });
//                                         params = {
//                                             ...params,
//                                             ...object
//                                         };
//                                     }
//                                 }
//                                 console.log("params", params)
//                                 var url = 'cmu_in_rp_chamsoccap2?type=pdf&' + $.param(params);
//                                 dsPdf.push({
//                                     "url": [url],
//                                     "name": "Phiếu chăm sóc cấp 2" + " trang " + (p+1)
//                                 });
//                             }
//                             loadAndCombinePDFs(dsPdf).then(data => {
//                                 getFilesign769(
//                                     "PHIEU_NOITRU_CHAMSOCCAP2",
//                                     item.ID_CHAM_SOC_CAP_1,
//                                     -1,
//                                     singletonObject.dvtt,
//                                     itemDDT.SOVAOVIEN,
//                                     itemDDT.SOVAOVIEN_DT,
//                                     -1,
//                                     function(dataKySo) {
//                                         if(dataKySo.length > 0) {
//                                             getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
//                                                 url = pdfData;
//                                                 thongtinhsba.thongtinbn.linkPCS2.push({
//                                                     "url": [url],
//                                                     "name": "Phiếu chăm sóc cấp 2" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
//                                                     "keyEMR": "BM_PHIEU_PHIEUCHAMSOC_13",
//                                                     "key": "CHAMSOCCAP2",
//                                                     "idPhieu": dataKySo[0].ID,
//                                                 });
//                                             }).catch(error => {
//                                                 thongtinhsba.thongtinbn.linkPCS2.push({
//                                                     "url": [url],
//                                                     "name": "Phiếu chăm sóc cấp 2" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
//                                                     "key": "CHAMSOCCAP2",
//                                                 });
//                                             });
//                                         } else {
//                                             thongtinhsba.thongtinbn.linkPCS2.push({
//                                                 "url": [url],
//                                                 "name": "Phiếu chăm sóc cấp 2" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
//                                                 "key": "CHAMSOCCAP2",
//                                             });
//                                         }
//                                     }
//                                 )
//                             }).catch(error => {
//                                 notifiToClient("Red", "Có lỗi xảy ra:", error);
//                             });
//                         }
//                     });
//
//                 });
//             } else {
//                 $("#hsba-phieucsc2-tab").hide();
//             }
//         });
//     });
// }

function xemPhieuChamSocCap2(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkPCS2 = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.MABENHNHAN, itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, "CMU_LANCHAMSOCCAP2_SEL"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieucsc2-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID_CHAM_SOC_CAP_2: item.ID_CHAM_SOC_CAP_2,
                        ID_CHI_TIET: -1,
                    }
                    getUrlPCSC2(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu chăm sóc cấp 2" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "CHAMSOCCAP2",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_PHIEUCHAMSOC_13",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkPCS2.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu chăm sóc cấp 2 - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });


                    // $.get("cmu_getlist?url=" + convertArray([item.ID_CHAM_SOC_CAP_2, "CMU_LCSC2_CHITIET_SEL24ROW"])).done(function(data) {
                    //     if (data.length > 0) {
                    //         var dsPdf = [];
                    //         for(var p = 0; p < Math.ceil(data.length/3); p++){
                    //             var params = {
                    //                 dvtt: singletonObject.dvtt,
                    //                 mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                    //                 ngayvaovien: thongtinhsba.thongtinbn.NGAYGIO_NHAPVIEN,
                    //                 ngaydautien: data[0].ONLY_NGAY,
                    //                 id_phieu: item.ID_CHAM_SOC_CAP_1,
                    //                 page: p,
                    //             };
                    //             for(var i = 0; i < 3; i++) {
                    //                 var item2 = data[i + p * 3];
                    //                 if (item2) {
                    //                     var object = {};
                    //                     $.each(item2, function(key, value) {
                    //                         object[key.toLowerCase() + "_" + (i+1)] = value;
                    //                     });
                    //                     params = {
                    //                         ...params,
                    //                         ...object
                    //                     };
                    //                 }
                    //             }
                    //             console.log("params", params)
                    //             var url = 'cmu_in_rp_chamsoccap2?type=pdf&' + $.param(params);
                    //             dsPdf.push({
                    //                 "url": [url],
                    //                 "name": "Phiếu chăm sóc cấp 2" + " trang " + (p+1)
                    //             });
                    //         }
                    //         loadAndCombinePDFs(dsPdf).then(data => {
                    //             getFilesign769(
                    //                 "PHIEU_NOITRU_CHAMSOCCAP2",
                    //                 item.ID_CHAM_SOC_CAP_1,
                    //                 -1,
                    //                 singletonObject.dvtt,
                    //                 itemDDT.SOVAOVIEN,
                    //                 itemDDT.SOVAOVIEN_DT,
                    //                 -1,
                    //                 function(dataKySo) {
                    //                     if(dataKySo.length > 0) {
                    //                         getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                    //                             url = pdfData;
                    //                             thongtinhsba.thongtinbn.linkPCS2.push({
                    //                                 "url": [url],
                    //                                 "name": "Phiếu chăm sóc cấp 2" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    //                                 "keyEMR": "BM_PHIEU_PHIEUCHAMSOC_13",
                    //                                 "key": "CHAMSOCCAP2",
                    //                                 "idPhieu": dataKySo[0].ID,
                    //                             });
                    //                         }).catch(error => {
                    //                             thongtinhsba.thongtinbn.linkPCS2.push({
                    //                                 "url": [url],
                    //                                 "name": "Phiếu chăm sóc cấp 2" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    //                                 "key": "CHAMSOCCAP2",
                    //                             });
                    //                         });
                    //                     } else {
                    //                         thongtinhsba.thongtinbn.linkPCS2.push({
                    //                             "url": [url],
                    //                             "name": "Phiếu chăm sóc cấp 2" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                    //                             "key": "CHAMSOCCAP2",
                    //                         });
                    //                     }
                    //                 }
                    //             )
                    //         }).catch(error => {
                    //             notifiToClient("Red", "Có lỗi xảy ra:", error);
                    //         });
                    //     }
                    // });

                });
            } else {
                $("#hsba-phieucsc2-tab").hide();
            }
        });
    });
}

function xemCamKetChuyenCSKham(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkChuyenCSKhamBenh = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_CAMKETCHUYENCSKB"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieuckchuyencosokb-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlCKChuyenCSKham(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu cam kết chuyển cơ sở khám bệnh, chữa bệnh - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "CHUYENCOSOKHAMBENH",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_CAMKETCHUNG_146",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkChuyenCSKhamBenh.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu cam kết chuyển cơ sở khám bệnh, chữa bệnh - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieuckchuyencosokb-tab").hide();
            }
        });
    });
}

function xemPhieuCungCapTTNB(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkCungCapThongTinNguoiBenh = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_CCTTNGUOIBENH"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieucungcapttnb-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlCungCapTTNB(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu cung cấp thông tin về người bệnh (tại khoa hồi sức tích cực) - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "CUNGCAPTHONGTINNGUOIBENH",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_CAMKETCHUNG_146",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkCungCapThongTinNguoiBenh.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu cung cấp thông tin về người bệnh (tại khoa hồi sức tích cực) - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieucungcapttnb-tab").hide();
            }
        });
    });
}

function xemPhieuKhamBenhTheoYC(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkKhamBenhTheoYC = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_KHAMBENHTHEOYC"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieukhambenhtheoyc-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlKhamBenhTheoYC(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu khám chữa bệnh theo yêu cầu - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "KHAMBENHTHEOYC",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_CAMKETCHUNG_146",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkKhamBenhTheoYC.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu khám chữa bệnh theo yêu cầu - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieukhambenhtheoyc-tab").hide();
            }
        });
    });
}

function xemPhieuScan(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkScanHoSo = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_NOITRU_SCAN_HOSO"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    if (item.BASE64.includes("application/pdf;base64")){
                        thongtinhsba.thongtinbn.linkScanHoSo.push({
                            "url": [item.BASE64],
                            "name": "Tài liệu Scan" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                            "key": "PHIEUSCAN",
                        });
                    }
                });
                thongtinhsba.thongtinbn.linkScanHoSo.length > 0 ? $("#hsba-phieuscan-tab").show() : $("#hsba-phieuscan-tab").hide()
            } else {
                $("#hsba-phieuscan-tab").hide();
            }
        });
    });
}

function xemCamKetTuChoiSDDV(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkCamKetTuChoiSDDV = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_CAMKETTUCHOISDDV"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieucktuchoiSDDV-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlCamKetTuChoiSDDV(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu cam kết từ chối sử dụng dịch vụ khám bệnh, chữa bệnh - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "CAMKETTUCHOISDDV",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_CAMKETCHUNG_146",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkCamKetTuChoiSDDV.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu cam kết từ chối sử dụng dịch vụ khám bệnh, chữa bệnh - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieucktuchoiSDDV-tab").hide();
            }
        });
    });
}

function xemPhieuTomTatHSBA(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkTomTatHSBA = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_TOMTATHSBA"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieutomtathsba-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlTomTatHSBA(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu tóm tắt hồ sơ bệnh án - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "TOMTATHSBA",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_CAMKETCHUNG_146",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkTomTatHSBA.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu tóm tắt hồ sơ bệnh án - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieutomtathsba-tab").hide();
            }
        });
    });
}

function xemCamKetRaVienKBS(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkCamKetRaVienKhongTheoBS = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_CAMKETRAVIENKHONGBS"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieuckravienkhongbs-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlCamKetRaVienKBS(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu cam kết ra viện không theo chỉ định bác sĩ - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "RAVIENKHONGTHEOBS",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_CAMKETCHUNG_146",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkCamKetRaVienKhongTheoBS.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu cam kết ra viện không theo chỉ định bác sĩ - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieuckravienkhongbs-tab").hide();
            }
        });
    });
}

function xemTDSuDungKhangSinhDP(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkTheoDoiSDKhangSinhDP = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_THEODOISDKSINHDP"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieutdsdkhangsinhdp-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlTheoDoiSDKhangSinhDP(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu theo dõi sử dụng kháng sinh dự phòng - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "THEODOISDKHANGSINHDP",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "RPT_SCAN",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkTheoDoiSDKhangSinhDP.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu theo dõi sử dụng kháng sinh dự phòng - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieutdsdkhangsinhdp-tab").hide();
            }
        });
    });
}

function xemCamKetPhauThuatGMHS(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkCamKetPhauThuatGMHS = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_CAMKETPHAUTHUATGMHS"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieuckphauthuatgmhs-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlCamKetPhauThuatGMHS(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Cam kết chấp thuận phẫu thuật, thủ thuật và gây mê hồi sức - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "CAMKETPHAUTHUATGMHS",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "BM_PHIEU_CAMKETCHUNG_146",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkCamKetPhauThuatGMHS.push(objTemp);
                        } else {
                            notifiToClient("Red", "Cam kết chấp thuận phẫu thuật, thủ thuật và gây mê hồi sức - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieuckphauthuatgmhs-tab").hide();
            }
        });
    });
}

function xemPhieuKyThuatPHCN(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkKyThuatPHCN = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_KYTHUATPHCN"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieukythuatphcn-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlKyThuatPHCN(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu thực hiện kỹ thuật phục hồi chức năng - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "KYTHUATPHCN",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "PHIEUTHUCHIEN_KYTHUAT_PHCN_A4",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkKyThuatPHCN.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu thực hiện kỹ thuật phục hồi chức năng - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieukythuatphcn-tab").hide();
            }
        });
    });
}

function xemPhieuLuongGiaHDCN(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkLuongGiaHDCN = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_LUONGGIAHDCN"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieuluonggiahdcn-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlLuongGiaHDCN(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu lượng giá hoạt động chức năng và sự tham gia - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "LUONGIAHDCN",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "PHIEULUONGGIA_HDCN_A4",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkLuongGiaHDCN.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu lượng giá hoạt động chức năng và sự tham gia - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieuluonggiahdcn-tab").hide();
            }
        });
    });
}

function xemPhieuKhamChiDinhPHCN(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkKhamChiDinhPHCN = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_KHAMCHIDINHPHCN"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieukhamchidinhphcn-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        ID: item.ID,
                    }
                    getUrlKhamChiDinhPHCN(params).then(objReturn => {
                        if (objReturn.isError == 0) {
                            var objTemp = {
                                "url": [objReturn.url],
                                "name": "Phiếu khám và chỉ định phục hồi chức năng - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                "key": "KHAMCHIDINHPHCN",
                            }
                            if (objReturn.kySo == 1) {
                                objTemp = {
                                    ...objTemp,
                                    "keyEMR": "PHIEUKHAMVACHIDINH_PHCN_A4",
                                    "idPhieu": objReturn.idKySo,
                                }
                            }
                            thongtinhsba.thongtinbn.linkKhamChiDinhPHCN.push(objTemp);
                        } else {
                            notifiToClient("Red", "Phiếu khám và chỉ định phục hồi chức năng - " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ": " + objReturn.message);
                        }
                    }).catch(error => {
                        // notifiToClient("Red", error.message || "Lỗi không xác định");
                    });
                });
            } else {
                $("#hsba-phieukhamchidinhphcn-tab").hide();
            }
        });
    });
}

function xemPhieuTTCamKetChung(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkTTCamKetChung = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.MABENHNHAN, itemDDT.SOVAOVIEN, itemDDT.SOVAOVIEN_DT, thongtinhsba.thongtinbn.STT_BENHAN, "CMU_TT_VA_CKC_NHAP_VIEN_LIST"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieuttcamketchung-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var formData = JSON.parse(item.DATA);
                    var text_date_part = item.NGAYTAO.split(" ")[0].split("/");
                    var text_date = text_date_part[0];
                    var text_month = text_date_part[1];
                    var text_year = text_date_part[2];
                    var params = {
                        dvtt: singletonObject.dvtt,
                        HO_TEN: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                        TUOI: thongtinhsba.thongtinbn.TUOI,
                        GIOI_TINH: thongtinhsba.thongtinbn.GIOI_TINH === 1 ?'NAM':'NU',
                        MS:  item.STT_BENHAN+"."+item.ID,
                        SO_VAO_VIEN:  thongtinhsba.thongtinbn.SOVAOVIEN,
                        MA_NGUOI_BENH:  thongtinhsba.thongtinbn.MABENHNHAN,
                        SDT:  thongtinhsba.thongtinbn.SO_DIEN_THOAI,
                        ...item,
                        ...formData,
                        NGUOI_CCTT: formData.NGUOI_CCTT.tennhanvien,
                        DIA_CHI: thongtinhsba.thongtinbn.DIA_CHI,
                        NGAYTAO: 'Ngày '+ text_date + " tháng " + text_month + " năm " + text_year,
                    }
                    var url = 'cmu_in_rp_phieu_tt_ckc_nhap_vien_nt?type=pdf&' + $.param(params);
                    getFilesign769(
                        "PHIEU_NOITRU_CUNGCAPTHONGTIN",
                        item.ID,
                        -1,
                        singletonObject.dvtt,
                        itemDDT.SOVAOVIEN,
                        itemDDT.SOVAOVIEN_DT,
                        -1,
                        function(dataKySo) {
                            if(dataKySo.length > 0) {
                                getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                    url = pdfData;
                                    thongtinhsba.thongtinbn.linkTTCamKetChung.push({
                                        "url": [url],
                                        "name": "Phiếu thông tin và cam kết chung nhập viện" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                        "keyEMR": "BM_PHIEU_CAMKETCHUNG_146",
                                        "key": "TTCAMKETCHUNG",
                                        "idPhieu": dataKySo[0].ID,
                                    });
                                }).catch(error => {
                                    thongtinhsba.thongtinbn.linkTTCamKetChung.push({
                                        "url": [url],
                                        "name": "Phiếu thông tin và cam kết chung nhập viện" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                        "key": "TTCAMKETCHUNG",
                                    });
                                });
                            } else {
                                thongtinhsba.thongtinbn.linkTTCamKetChung.push({
                                    "url": [url],
                                    "name": "Phiếu thông tin và cam kết chung nhập viện" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                    "key": "TTCAMKETCHUNG",
                                });
                            }
                        }
                    )
                });
            } else {
                $("#hsba-phieuttcamketchung-tab").hide();
            }
        });
    });
}

function xemPhieuNhanDinhBanDau(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkNhanDinhBanDau = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_NHANDINHBANDAU"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieunhandinhbandau-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        magiay: item.ID,
                        mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                        khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                        diachi: thongtinhsba.thongtinbn.DIA_CHI,
                        sdt: thongtinhsba.thongtinbn.SO_DIEN_THOAI,
                        tenbenhnhan: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                        chandoan: thongtinhsba.thongtinbn.TENBENHCHINH_NHAPVIEN,
                        sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                        sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                        gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                        namsinh: thongtinhsba.thongtinbn.NGAY_SINH.split("/")[2],
                        tuoi: thongtinhsba.thongtinbn.TUOI,
                        stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                        stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                    }
                    var url = 'cmu_in_cmu_nhandinhbandau?type=pdf&' + $.param(params);
                    getFilesign769(
                        "PHIEU_NOITRU_NHANDINHBANDAU",
                        item.ID,
                        -1,
                        singletonObject.dvtt,
                        itemDDT.SOVAOVIEN,
                        itemDDT.SOVAOVIEN_DT,
                        -1,
                        function(dataKySo) {
                            if(dataKySo.length > 0) {
                                getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                    url = pdfData;
                                    thongtinhsba.thongtinbn.linkNhanDinhBanDau.push({
                                        "url": [url],
                                        "name": "Phiếu nhận định ban đầu vào viện tại khoa nội trú" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                        "keyEMR": "BM_PHIEU_PLNB_341",
                                        "key": "NHANDINHBANDAU",
                                        "idPhieu": dataKySo[0].ID,
                                    });
                                }).catch(error => {
                                    thongtinhsba.thongtinbn.linkNhanDinhBanDau.push({
                                        "url": [url],
                                        "name": "Phiếu nhận định ban đầu vào viện tại khoa nội trú" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                        "key": "NHANDINHBANDAU",
                                    });
                                });
                            } else {
                                thongtinhsba.thongtinbn.linkNhanDinhBanDau.push({
                                    "url": [url],
                                    "name": "Phiếu nhận định ban đầu vào viện tại khoa nội trú" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                    "key": "NHANDINHBANDAU",
                                });
                            }
                        }
                    )
                });
            } else {
                $("#hsba-phieunhandinhbandau-tab").hide();
            }
        });
    });
}

function xemPhieuTuVanHDNhapVien(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkTuVanHDNhapVien = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_TVHDNHAPVIEN"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieutuvanhdnhapvien-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        magiay: item.ID,
                        mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                        tennguoibenh: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                        sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                        sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                        gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                        tuoi: thongtinhsba.thongtinbn.TUOI,
                        khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                        stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                        stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                    }
                    var url = 'cmu_in_cmu_tv_hd_nhapvien?type=pdf&' + $.param(params);
                    getFilesign769(
                        "PHIEU_NOITRU_TVHDNHAPVIEN",
                        item.ID,
                        -1,
                        singletonObject.dvtt,
                        itemDDT.SOVAOVIEN,
                        itemDDT.SOVAOVIEN_DT,
                        -1,
                        function(dataKySo) {
                            if(dataKySo.length > 0) {
                                getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                    url = pdfData;
                                    thongtinhsba.thongtinbn.linkTuVanHDNhapVien.push({
                                        "url": [url],
                                        "name": "Phiếu tư vấn, hướng dẫn khi nhập viện" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                        "keyEMR": "BM_PHIEU_TUVAN_338",
                                        "key": "TUVANHDNHAPVIEN",
                                        "idPhieu": dataKySo[0].ID,
                                    });
                                }).catch(error => {
                                    thongtinhsba.thongtinbn.linkTuVanHDNhapVien.push({
                                        "url": [url],
                                        "name": "Phiếu tư vấn, hướng dẫn khi nhập viện" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                        "key": "TUVANHDNHAPVIEN",
                                    });
                                });
                            } else {
                                thongtinhsba.thongtinbn.linkTuVanHDNhapVien.push({
                                    "url": [url],
                                    "name": "Phiếu tư vấn, hướng dẫn khi nhập viện" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                    "key": "TUVANHDNHAPVIEN",
                                });
                            }
                        }
                    )
                });
            } else {
                $("#hsba-phieutuvanhdnhapvien-tab").hide();
            }
        });
    });
}

function xemPhieuTuVanHDVaoVien(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkTuVanHDVaoVien = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_TVHDVAOVIEN"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieutuvanhdvaovien-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        magiay: item.ID,
                        mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                        tennguoibenh: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                        sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                        sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                        gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                        tuoi: thongtinhsba.thongtinbn.TUOI,
                        khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                        stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                        stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                    }
                    var url = 'cmu_in_cmu_tv_hd_vaovien?type=pdf&' + $.param(params);
                    getFilesign769(
                        "PHIEU_NOITRU_TVHDVAOVIEN",
                        item.ID,
                        -1,
                        singletonObject.dvtt,
                        itemDDT.SOVAOVIEN,
                        itemDDT.SOVAOVIEN_DT,
                        -1,
                        function(dataKySo) {
                            if(dataKySo.length > 0) {
                                getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                    url = pdfData;
                                    thongtinhsba.thongtinbn.linkTuVanHDVaoVien.push({
                                        "url": [url],
                                        "name": "Phiếu tư vấn, hướng dẫn khi vào viện" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                        "keyEMR": "BM_PHIEU_TUVAN_338",
                                        "key": "TUVANHDVAOVIEN",
                                        "idPhieu": dataKySo[0].ID,
                                    });
                                }).catch(error => {
                                    thongtinhsba.thongtinbn.linkTuVanHDVaoVien.push({
                                        "url": [url],
                                        "name": "Phiếu tư vấn, hướng dẫn khi vào viện" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                        "key": "TUVANHDVAOVIEN",
                                    });
                                });
                            } else {
                                thongtinhsba.thongtinbn.linkTuVanHDVaoVien.push({
                                    "url": [url],
                                    "name": "Phiếu tư vấn, hướng dẫn khi vào viện" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                    "key": "TUVANHDVAOVIEN",
                                });
                            }
                        }
                    )
                });
            } else {
                $("#hsba-phieutuvanhdvaovien-tab").hide();
            }
        });
    });
}

function xemPhieuTuVanHDRaVien(dsDotDieuTri) {
    thongtinhsba.thongtinbn.linkTuVanHDRaVien = [];
    dsDotDieuTri.forEach(function (itemDDT) {
        $.ajax({
            url: "cmu_getlist?url=" + convertArray([singletonObject.dvtt, itemDDT.SOVAOVIEN, "CMU_GET_TVHDRAVIEN"]),
            type: "GET",
        }).done(function (dataPhieu) {
            if(dataPhieu.length > 0) {
                $("#hsba-phieutuvanhdravien-tab").show();
                $("#wrap_Menu_PhieuKhac").show();
                dataPhieu.forEach(function (item) {
                    var params = {
                        magiay: item.ID,
                        mabenhnhan: thongtinhsba.thongtinbn.MABENHNHAN,
                        tennguoibenh: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                        sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                        sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                        gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                        tuoi: thongtinhsba.thongtinbn.TUOI,
                        khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                        stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                        stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                    }
                    var url = 'cmu_in_cmu_tv_hd_ravien?type=pdf&' + $.param(params);
                    getFilesign769(
                        "PHIEU_NOITRU_TVHDRAVIEN",
                        item.ID,
                        -1,
                        singletonObject.dvtt,
                        itemDDT.SOVAOVIEN,
                        itemDDT.SOVAOVIEN_DT,
                        -1,
                        function(dataKySo) {
                            if(dataKySo.length > 0) {
                                getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf').then(pdfData => {
                                    url = pdfData;
                                    thongtinhsba.thongtinbn.linkTuVanHDRaVien.push({
                                        "url": [url],
                                        "name": "Phiếu tư vấn, hướng dẫn trước khi ra viện" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                        "keyEMR": "BM_PHIEU_TUVAN_338",
                                        "key": "TUVANHDRAVIEN",
                                        "idPhieu": dataKySo[0].ID,
                                    });
                                }).catch(error => {
                                    thongtinhsba.thongtinbn.linkTuVanHDRaVien.push({
                                        "url": [url],
                                        "name": "Phiếu tư vấn, hướng dẫn trước khi ra viện" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                        "key": "TUVANHDRAVIEN",
                                    });
                                });
                            } else {
                                thongtinhsba.thongtinbn.linkTuVanHDRaVien.push({
                                    "url": [url],
                                    "name": "Phiếu tư vấn, hướng dẫn trước khi ra viện" + thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                    "key": "TUVANHDRAVIEN",
                                });
                            }
                        }
                    )
                });
            } else {
                $("#hsba-phieutuvanhdravien-tab").hide();
            }
        });
    });
}