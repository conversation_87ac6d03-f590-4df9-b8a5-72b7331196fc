create or replace FUNCTION HIS_MANAGER."CMU_CSC2_DATALIST_ITEM_INS" (
    p_dvtt       IN           VARCHAR2,
    p_loai       IN           VARCHAR2,
    p_ten        IN           VARCHAR2,
		p_mota       IN           VARCHAR2,
    p_khoatao    IN           VARCHAR2,
    p_nguoitao   IN           VARCHAR2
) RETURN VARCHAR2 IS
    v_checkexist   NUMBER := 0;
    v_result       VARCHAR2(10) := '-1';
BEGIN
SELECT
    COUNT(*)
INTO v_checkexist
FROM
    cmu_datalist_item_csc2
WHERE
        dvtt = p_dvtt
  AND loai_item = p_loai
  AND ten_item = p_ten
  AND khoa_tao = p_khoatao;

IF v_checkexist > 0 THEN
        RETURN '-2';
END IF;
INSERT INTO cmu_datalist_item_csc2 (
    dvtt,
    loai_item,
    ten_item,
    MOTA_ITEM,
    khoa_tao,
    nguoi_tao
) VALUES (
             p_dvtt,
             p_loai,
             p_ten,
             p_mota,
             p_khoatao,
             p_nguoitao
         ) RETURNING id INTO v_result;

RETURN v_result;
END;