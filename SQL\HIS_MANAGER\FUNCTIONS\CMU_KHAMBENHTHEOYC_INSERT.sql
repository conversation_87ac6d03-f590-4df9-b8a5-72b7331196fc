create or replace FUNCTION      HIS_MANAGER."CMU_KHAMBENHTHEOYC_INSERT" (
    p_dvtt                IN VARCHAR2,
    p_so_vao_vien         IN NUMBER,
    p_ma_<PERSON><PERSON>_<PERSON>han        IN NUMBER,
    p_daidien          IN VARCHAR2,
    p_<PERSON><PERSON><PERSON>   IN VARCHAR2,
    p_tuoi           IN NUMBER,
    p_gioitinh           IN NUMBER,
    p_cmnd           IN VARCHAR,
    p_coquanc<PERSON>           IN VARCHAR,
    p_dantoc           IN VARCHAR,
    p_ngoaikieu           IN NUMBER,
    p_nghenghiep           IN VARCHAR,
    p_noilamviec           IN VARCHAR,
    p_diachi           IN VARCHAR,
    p_k<PERSON><PERSON><PERSON>tin           IN VARCHAR,
    p_sotienung           IN VARCHAR,
    p_sotienchu           IN VARCHAR,
    p_bacsicb           IN NUMBER,
    p_giamdocbv           IN NUMBER,
    p_ngaytao             IN VARCHAR2,
    p_nguoi_tao           IN NUMBER
)
return number IS
    v_id CMU_KHAMBENHTHEOYC.id%TYPE;
    v_ngaytao   DATE := TO_DATE(p_ngaytao, 'dd/mm/yyyy');
BEGIN
INSERT INTO CMU_KHAMBENHTHEOYC
(
    DVTT,
    SOVAOVIEN,
    MA_BENH_NHAN,
    DAIDIEN,
    TENDAIDIEN,
    TUOI,
    GIOI_TINH,
    CMND,
    COQUANCAP,
    DANTOC,
    NGOAIKIEU,
    NGHENGHIEP,
    NOILAMVIEC,
    DIACHI,
    KHICANBAOTIN,
    SOTIENUNG,
    SOTIENBANGCHU,
    BACSICHUABENH,
    GIAMDOCBV,
    NGAY_TAO_PHIEU,
    NGUOI_TAO
)
VALUES
    (
        p_dvtt,
        p_so_vao_vien,
        p_ma_benh_nhan,
        p_daidien,
        p_tendaidien,
        p_tuoi,
        p_gioitinh,
        p_cmnd,
        p_coquancap,
        p_dantoc,
        p_ngoaikieu,
        p_nghenghiep,
        p_noilamviec,
        p_diachi,
        p_khicanbaotin,
        p_sotienung,
        p_sotienchu,
        p_bacsicb,
        p_giamdocbv,
        v_ngaytao,
        p_nguoi_tao
    )
    RETURNING id INTO v_id;

RETURN v_id;
END;