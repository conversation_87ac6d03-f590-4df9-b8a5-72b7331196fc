<%@ page import="l2.ThamSoManager" %>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@page contentType="text/html" pageEncoding="UTF-8" session="true" autoFlush="false" buffer="20000kb" %>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="google-site-verification" content="u6uNEfD4cb3gidezi4r_6aI8Wb1E07-ufBeCQpvmlqQ"/>
    <title>Hệ thống chăm sóc sức khỏe</title>
    <link rel="icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
    <link rel="shortcut icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>

    <%--Bootstrap--%>
    <link rel="stylesheet" href="<c:url value="/resources/font-awesome-4.7.0/css/font-awesome.min.css"/>">
    <link href="<c:url value="/resources/bootstrap-4.1.3/dist/css/bootstrap.min.css" />" rel="stylesheet"/>

    <!-- jQuery file -->
    <link href="<c:url value="/resources/css/divheader.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/css/style_new.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/css/magiczoomplus.css" />" rel="stylesheet"/>

    <!--Jquery-->
    <link rel="stylesheet" href="<c:url value="/resources/css/jquery-ui-redmond.1.9.1.css" />"/>
    <script src="<c:url value="/resources/js/jquery.min.1.8.3.js" />"></script>
    <script src="<c:url value="/resources/js/jquery-ui.1.9.1.js" />"></script>
    <!--Grid-->
    <link href="<c:url value="/resources/jqgrid/css/ui.jqgrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/jqgrid/js/i18n/grid.locale-en.js" />"></script>
    <script src="<c:url value="/resources/jqgrid/js/jquery.jqGrid.src.js" />"></script>
    <script src="<c:url value="/resources/js/common_function.js?timestamp=${System.currentTimeMillis()}" />"></script>
    <script src="<c:url value="/resources/js/jquery.inputmask.bundle.min.js" />"></script>
    <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
    <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/dialog/jquery.alerts.1.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/dialog/jquery.alerts.js" />"></script>
    <script src="<c:url value="/resources/js/read_file.js" />"></script>
    <link href="<c:url value="/resources/dialog/jBox.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/dialog/jBox.js" />"></script>
    <script src="<c:url value="/resources/camau/popper.js" />"></script>
    <script src="<c:url value="/resources/bootstrap-4.4.1-dist/js/bootstrap.min.js"/>" ></script>


    <link href="<c:url value="/resources/combogrid/css/smoothness/jquery.ui.combogrid.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/combogrid/plugin/jquery.ui.combogrid-1.6.3.js" />"></script>
    <script src="<c:url value="/resources/ckeditor/ckeditor.js" />"></script>
    <script src="<c:url value="/resources/ckeditor/adapters/jquery.js" />"></script>
    <script src="<c:url value="/resources/webcam/say-cheese.js" />"></script>
    <link href="<c:url value="/resources/webcam/pygments.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/webcam/say-cheese.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
    <script src="<c:url value="/resources/js/magiczoomplus.js" />"></script>
    <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/blockUI/jquery.blockUI.js" />"></script>
    <script src="<c:url value="/resources/js/jquery-confirm.min.js" />"></script>
    <link href="<c:url value="/resources/css/jquery-confirm.min.css" />" rel="stylesheet"/>
    <link href="<c:url value="/resources/camau/css/custom.css" />" rel="stylesheet"/>
    <script src="<c:url value="/resources/camau/smartca769.js" />"></script>
    <script src="<c:url value="/resources/camau/material/moment.js" />"></script>
    <script src="<c:url value="/resources/camau/js/kiemtradulieuthoigiankham.js?timestamp=${System.currentTimeMillis()}" />"></script>

    <style>
        .width1 {
            width: 200px;
        }

        .width2 {
            width: 550px;
        }

        .width3 {
            width: 150px;
        }

        span.cellWithoutBackground {
            background-image: none;
            margin-right: -2px;
            margin-left: -2px;
            height: 14px;
            padding: 4px;
        }
    </style>
    <script>
        var sovaovien;
        var sovaovien_noi;
        var sovaovien_dt_noi;
        var THAMSO_828327 = "<%= ThamSoManager.instance(session).getThamSoString(ThamSoManager.THAMSO_828327, "0")%>";
        var giopttt_timer_is_on = false;
        var giopttt_timer;

        var ngay_chi_dinh;
        var ngaychidinh;
        var giochidinh;
        var tatAutoTime = "<%= ThamSoManager.instance(session).getThamSoString(ThamSoManager.THAMSO_28084881,"0")%>";
        var SESS_PHONG_BAN = "<%= session.getAttribute("Sess_PhongBan").toString()%>";
        var SESS_USER_ID = "<%= session.getAttribute("Sess_UserID").toString()%>";
        var THAMSO_828449 = "<%= ThamSoManager.instance(session).getThamSoString("828449", "0")%>";
        var THAMSO_960625 = "<%= ThamSoManager.instance(session).getThamSoString("960625", "0")%>";
        var THAMSO_960626 = "<%= ThamSoManager.instance(session).getThamSoString("960626", "0")%>";

        function checkNguoiDocKetQua() {
            var _selector = $('#cboNguoiDocKetQua');
            var countOption =  _selector.find("option[value='"+SESS_USER_ID+"']").length;
            return !!!_selector.val() || countOption==0 //|| _selector.find(':selected').data("khoa") != SESS_PHONG_BAN
                ;
        }

        function setNguoiDocKetQua(maBacSi) {
            var _selector = $('#cboNguoiDocKetQua');
            _selector.val(maBacSi||SESS_USER_ID);
        }

        function loadBacSiTheoKhoa(strListMaKhoa = "-1") {
            // Fetch the preselected item, and add to the control
            var _selector = $('#cboNguoiDocKetQua');
            $.ajax({
                type: 'GET',
                url: 'cmu_getlist?url='+convertArray(["${Sess_DVTT}",strListMaKhoa,"SEL_LIST_BAC_SI_THEO_KHOA"]),
            }).then(function (data) {
                if(jQuery.isArray(data) ) {
                    _selector.empty();
                    // $('#cboDoiTuongTiepNhan').append($('<option>', { value: '0', text : 'Khác', color: '' }));
                    _selector.append($('<option>', {
                        value: SESS_USER_ID,
                        text : "${Sess_User}",
                        "data-khoa": SESS_PHONG_BAN
                    }));
                    $.each(data, function (i, item) {
                        if(!!item && item.MA_NHANVIEN && item.TEN_NHANVIEN && item.MA_PHONGBAN && item.MA_NHANVIEN != SESS_USER_ID) {
                            _selector.append($('<option>', {
                                value: item.MA_NHANVIEN,
                                text : item.TEN_NHANVIEN,
                                "data-khoa": item.MA_PHONGBAN
                            }));
                        }
                    });
                    _selector.val(SESS_USER_ID);
                    if(!!!_selector.val()) {
                        // jAlert("Tài khoản đăng nhập không thuộc khoa đã cấu hình", "Cảnh báo");
                    }
                }
            }).always(function( data, textStatus, jqXHR ) {

            });
        }

        function luuNguoiDocKetQua(_soPhieu, _soVaoVien, _soVaoVienDt = 0, _noiTru = 0, _listMaCls = "-1") {
            $.ajax({
                type: 'POST',
                url: 'update-thong-tin-theo-benh-nhan',
                async: false,
                data: {
                    P_LOG_USERID: "${Sess_UserID}",
                    p_dvtt: "${Sess_DVTT}",
                    p_soPhieu: _soPhieu,
                    p_soVaoVien: _soVaoVien,
                    p_soVaoVienDt: _soVaoVienDt,
                    p_noiTru: _noiTru,
                    p_sessKhoaId: SESS_PHONG_BAN,
                    p_nguoiDocKetQua: $('#cboNguoiDocKetQua').val(),
                    p_listMaCls: _listMaCls,
                    p_loai_cls: 'TTPT',
                    action: "UPD_CLS_NGUOI_DOC_KET_QUA"
                }
            }).fail(function(data){
                jAlert("Lỗi cập nhật người đọc kết quả", "Cảnh báo");
            }).then(function (data) {
                console.log(data);
            }).always(function( data, textStatus, jqXHR ) {

            });
        }

        function getNguoiDocKetQua(_soPhieu, _soVaoVien, _soVaoVienDt = 0, _noiTru = 0, _listMaCls = "-1") {
            $.ajax({
                type: 'GET',
                url: 'select-thong-tin-theo-benh-nhan',
                data: {
                    P_LOG_USERID: "${Sess_UserID}",
                    p_dvtt: "${Sess_DVTT}",
                    p_soPhieu: _soPhieu,
                    p_soVaoVien: _soVaoVien,
                    p_soVaoVienDt: _soVaoVienDt,
                    p_noiTru: _noiTru,
                    p_listMaCls: _listMaCls,
                    p_loai_cls: 'TTPT',
                    action: "SEL_CLS_NGUOI_DOC_KET_QUA"
                }
            }).then(function (data) {
                if(!!data && !!data[0]){
                    var obj = data[0];
                    setNguoiDocKetQua(obj.MA_NHANVIEN);
                }
            }).always(function( data, textStatus, jqXHR ) {

            });
        }

        function showUploadDialog() {
            var madv = $("#madv").val();
            var sophieu = $("#sophieu").val();
            var noitru = $("#noitru").val();
            var makhambenh = $("#makhambenh").val();
            var sttbenhan = $("#sttbenhan").val();
            var sttdotdieutri = $("#sttdotdieutri").val();
            var sttdieutri = $("#sttdieutri").val();

            var postData = {
                maDichVu: madv,
                soPhieu: sophieu,
                noiTru: noitru,
                maKhamBenh: makhambenh,
                sttBenhAn: sttbenhan,
                sttDotDieuTri: sttdotdieutri,
                sttDieuTri: sttdieutri
            };
            if (madv != "" && sophieu != "") {
                var params = {
                    URL: "ttpt_insert_file",
                    DATA: postData
                }
                dialog_upload.data(params).dialog('open');
            }
        }

        function addZero(i) {
            if (i < 10) {
                i = "0" + i;
            }
            return i;
        }

        function showtime_giopttt() {
            var thoigian = new Date();
            var gio = addZero(thoigian.getHours());
            var phut = addZero(thoigian.getMinutes());
            var giay = addZero(thoigian.getSeconds());
            /*if(showtime_giopttt_cancel !== 1) {
             $('#giopttt').val(gio + ":" + phut + ":" + giay);
             }
             t = setTimeout(showtime_giopttt, 1000);*/
            $('#giopttt').val(gio + ":" + phut + ":" + giay);
            giopttt_timer = setTimeout(showtime_giopttt, 1000);
            giopttt_timer_is_on = true;
        }

        function stopGioPtttTimer() {
            clearTimeout(giopttt_timer);
            giopttt_timer_is_on = false;
        }

        function gioPtttTimerChange() {
            if("${Sess_DVTT}"=="14017"){
                stopGioPtttTimer();
            }
            else if (tatAutoTime == 1) {
                stopGioPtttTimer();
            }
            else if (giopttt_timer_is_on || ($("#giopttt").data('da-thuc-hien') && ${capnhat_cls_timer_off} == 1))
                stopGioPtttTimer();
            else
                showtime_giopttt();
        }

        $(function () {
            $("#giopttt").click(function (evt) {
                gioPtttTimerChange();
            });
            lichmo_dialog = $("#dialog_lichmo").dialog({
                autoOpen: false,
                width: 1000,
                modal: true,
                resizable: false,
                position: {my: "center", at: "center", of: window}
            });
            $("#inphieugaymehoisuc").click(function (evt) {
                if ($("#hoten").val() != "") {
                    var hoten = $("#hoten").val();
                    var tuoi = $("#tuoi").val();
                    var gioitinh = $("#gioitinh").val();
                    var id1 = $("#list_xn_bhyt").jqGrid('getGridParam', 'selrow');
                    var ret = $("#list_xn_bhyt").jqGrid('getRowData', id1);
                    var ten_dichvu = ret.TEN_DV;
                    var ma_dv      = ret.MA_DV;
                    var chandoan = $("#_chandoan").val();
                    var khoa = $("#tenkhoa").val();
                    var sovaovien = $("#sovaovien").val();
                    if (noitru == "1") {
                        sovaovien = sovaovien_noi;
                    }
                    var phieu_dv = $("#sophieu").val();

                    var url = "inphieugaymehoisuc?hoten=" + hoten + '&tuoi=' + tuoi + '&gioitinh=' + gioitinh + '&ten_dichvu=' + ten_dichvu + '&chandoan=' + encodeURIComponent(chandoan) + '&khoa=' + khoa + '&sovaovien=' + sovaovien + '&phieu_dv=' + phieu_dv + '&ma_dv=' + ma_dv;
                    if (ten_dichvu) {
                        $(location).attr('href', url);
                    } else jAlert("Chưa chọn dịch vụ", "Cảnh báo");
                }
            });
            dalBBduyetphauthuat = $("#dalBBduyetphauthuat").dialog({
                autoOpen: false,
                width: 1000,
                modal: true,
                resizable: false,
                position: {my: "center", at: "center", of: window}
            });
            $("#bt_chuanbiphauthuat").click(function(evt){
                chuanbiphauthuat_dialog.dialog("open");
            });

            $("#bt_lichphauthuat").click(function (evt) {
                lichphauthuat_dialog.dialog("open");

            });
            $("#bt_kiemkeantoan").click(function (evt) {
                PhongPhauThuat_KiemKeAnToanPhauThuat_dialog.dialog("open");

            });
            $("#bt_khamtienme").click(function(evt){
                khamtienme_dialog.dialog("open");
            });
            $("#bt_sauphauthuat").click(function(evt){
                bieumausaumo_dialog.dialog("open");
            });
            function getObjectBBDuyetPhauThuat() {
                var sophieu = $("#sophieu").val();
                var madichvu = $("#madv").val();
                var noitru = $("#noitru").val();
                var thilucmatphai = $("#thilucmatphai").val();
                var thilucmattrai = $("#thilucmattrai").val();
                var nhanapmatphai = $("#nhanapmatphai").val();
                var nhanapmattrai = $("#nhanapmattrai").val();
                var cdphauthuatphoihop = $("#cdphauthuatphoihop").val();
                var phauthuatphoihop = $("#phauthuatphoihop").val();
                var phauthuatvienphu = $("#phauthuatvienphu").val();
                var gayte_gayme = $("#gayte_gayme").prop('checked') == true ? "1" : "0";
                var ykientruongkhoa = $("#ykientruongkhoa").val();
                var arr = [sophieu, madichvu, noitru, thilucmatphai, thilucmattrai, nhanapmatphai, nhanapmattrai, cdphauthuatphoihop, phauthuatphoihop, phauthuatvienphu, gayte_gayme, ykientruongkhoa, "0"];
                return arr;
            }
            $("#luu_bbduyet").click(function (evt) {
                var madichvu = $("#madv").val();
                if (madichvu != "") {
                    var url = "luuBBduyetphauthuat?url=" + convertArray(getObjectBBDuyetPhauThuat());
                    $.ajax({
                        url: url
                    }).done(function (data) {
                        if (data == "1") {
                            jAlert("Lưu thành công", "Thông báo");
                        }
                    });
                } else {
                    jAlert("Chưa chọn dịch vụ", "Thông báo");
                }
            });

            $("#inphieu_bbduyet").click(function (evt) {
                var madichvu = $("#madv").val();
                if (madichvu != "") {
                    var maBHYT = $("#sothebhyt").val();
                    var hoten = $("#hoten").val();
                    var tuoi = $("#tuoi").val();
                    var gioitinh = $("#gioitinh").val();
                    var diachi = $("#diachi").val();
                    // var lydovaovien = $("#_chandoan").val();
                    var chandoan = $("#_chandoan").val();
                    var mabenhnhan = $("#mabenhnhan").val();
                    var arr = [maBHYT, hoten, tuoi, gioitinh, diachi, chandoan, mabenhnhan, "0"];
                    var url = "inphieuBBduyetphauthuat?url=" + convertArray(getObjectBBDuyetPhauThuat()) + "&thongtin=" + convertArray(arr);
                    $(location).attr('href', url);
                } else {
                    jAlert("Chưa chọn dịch vụ", "Thông báo");
                }
            });

            $("#bbduyetphauthuat").click(function (evt) {
                var madichvu = $("#madv").val();
                if (madichvu != "") {
                    var urlget = "layBBDuyetPhauThuat?url=" + convertArray(getObjectBBDuyetPhauThuat());
                    $.getJSON(urlget, function (result) {
                        $.each(result, function (i, field) {
                            $("#thilucmatphai").val(field.THILUCMATPHAI);
                            $("#thilucmattrai").val(field.THILUCMATTRAI);
                            $("#nhanapmatphai").val(field.NHANAPMATPHAI);
                            $("#nhanapmattrai").val(field.NHANAPMATTRAI);
                            $("#cdphauthuatphoihop").val(field.CDPHAUTHUATPHOIHOP);
                            $("#phauthuatphoihop").val(field.PHAUTHUATPHOIHOP);
                            $("#phauthuatvienphu").val(field.PHAUTHUATVIENPHU);
                            $("#gayte_gayme").prop("checked", field.GAYTE_GAYME == "1" ? true : false);
                            $("#ykientruongkhoa").val(field.YKIENTRUONGKHOA);
                        });
                    });
                    dalBBduyetphauthuat.dialog("open");
                } else {
                    jAlert("Chưa chọn dịch vụ", "Thông báo");
                }
            });

            $("#lichmo").click(function (evt) {
                var id1 = $("#list_xn_bhyt").jqGrid('getGridParam', 'selrow');
                var id2 = $("#list_benhnhan").jqGrid('getGridParam', 'selrow');
                if (id1) {
                    var ret1 = $("#list_benhnhan").jqGrid('getRowData', id2);
                    var noitru = $("#noitru").val();
                    if (noitru == "0") {
                        $("#sovaovien_lm").val(sovaovien);
                        nghiepvu = "ngoaitru_toadichvu";
                    } else {
                        $("#sovaovien_lm").val(sovaovien_noi);
                        nghiepvu = "noitru_toadichvu";
                    }
                    ;
                    $("#sttbenhan_lm").val(ret1.STT_BENHAN);
                    $("#sttdieutri_lm").val(ret1.STT_DOTDIEUTRI);
                    $("#sttdotdieutri_lm").val(ret1.STT_DOTDIEUTRI);
                    var ret = $("#list_xn_bhyt").jqGrid('getRowData', id1);
                    $("#tendv_lm").val(ret.TEN_DV);
                    $("#madv_lm").val(ret.MA_DV);
                    $("#maphongban").val(ret1.MA_PHONGBAN);
                    lichmo_dialog.dialog("open");


                    var sophieu = $("#sophieu").val();
                    var sttbenhan = $("#sttbenhan").val();
                    var sttdotdieutri = $("#sttdotdieutri").val();
                    var sttdieutri = $("#sttdieutri").val();
                    var makhambenh = $("#makhambenh").val();
                    var madv = $("#madv").val();
                    var dvtt = "${Sess_DVTT}";
                    var arrkt = [$("#sovaovien_lm").val(), sophieu, dvtt, madv]
                    var urlkt = "kiemtra_dalenlich?url=" + convertArray(arrkt);
                    $.ajax({
                        url: urlkt
                    }).done(function (data) {
                        if (data == "1") {

                            toggle("xem");
                        } else toggle("load");
                    });
                    var arr = [sophieu, noitru, sttbenhan, sttdotdieutri, sttdieutri, makhambenh, madv, dvtt, "0", sovaovien, sovaovien_noi, sovaovien_dt_noi];
                    var url3 = "pttt_select_ketqua_svv?url=" + convertArray(arr);
                    $.getJSON(url3, function (result) {
                        $.each(result, function (i, field) {
                            $("#frm_kq_id_ekip").val(field.ID_EKIP);
                            $("#frm_kq_so_phieu_dichvu").val(field.SO_PHIEU_DICHVU);
                            $("#sophieudv_lm").val(field.SO_PHIEU_DICHVU);
                            $("#frm_kq_ma_dv").val(field.MA_DV);
                            $("#frm_kq_mabenhnhan").val(field.MABENHNHAN);
                            $("#frm_kq_sovaovien").val(field.SOVAOVIEN);
                            $("#frm_kq_sovaovien_dt").val(field.SOVAOVIEN_DT);
                            $("#frm_kq_noitru").val(field.NOITRU);
                            $("#frm_kq_id_dieutri").val(field.ID_DIEUTRI);
                            $("#id_dieutri_lm").val(field.ID_DIEUTRI);
                            $("#sophieudv_lm").val(field.SO_PHIEU_DICHVU);
                        })
                    })
                } else jAlert("Chưa chọn dịch vụ chỉ định!", "Cảnh báo");
            });

            $("#ngaythuchien_cls").datepicker();
            $("#ngaythuchien_cls").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngaythuchien_cls").val("${ngayhientai}");
            $("#giothuchien_cls").val("${giohientai}");
            $("#ngaychidinh_cls").datepicker();
            $("#ngaychidinh_cls").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngaychidinh_cls").val("${ngayhientai}");
            $("#giochidinh_cls").val("${giohientai}");

            $(":input").inputmask();
            $("#ngaythuchien").datepicker();
            $("#ngaypttt").datepicker();
            $("#ngaypttt").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngaypttt").val("${ngayhientai}");
            $("#thoigiankt").datepicker();
            $("#thoigiankt").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngaythuchien").datepicker("option", "dateFormat", "dd/mm/yy");
            $("#ngaythuchien").val("${ngayhientai}");
            // HPG--Timkiem benh nhan CLS tu ngay den ngay
            if ("${timkiem_cls}" == "1") {
                $(".hpg_tmp").show();
                $("#tungay").datepicker();
                $("#tungay").datepicker("option", "dateFormat", "dd/mm/yy");
                $("#tungay").val("${ngayhientai}");
                $("#tungay").change(function (evt) {
                    reload_grid();
                });
            } else $(".hpg_tmp").hide();
            //--
            // ĐắkLắk: tùy chọn hiển thị lọc theo khoa, phòng, đối tượng
            if ("${choloctheokhoaphong_capnhatketquacls}" == "1")
                $(".dlk_tmp").show();
            else $(".dlk_tmp").hide();
            // End ĐắkLắk
            if ("${hienthi_mausac_bn}" == "1")
                $(".ghichutrangthai").show();
            else
                $(".ghichutrangthai").hide();
            //-------
            if ("${hienthi_them_cls}" == "1")
                $(".hpg_hienthithem").show();
            else
                $(".hpg_hienthithem").hide();
            //--End HPG
            $('textarea#trinhtupttt').ckeditor();
            $('textarea#catchisau7ngay').ckeditor();
            $("#cmuphanphoinguonthu").hide();
            $("#cmuphanphoinguonthu_v2").hide();
            $("#list_benhnhan").jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 345,
                width: 300,
                colNames: ["STT", "Mã y tế", "Họ tên", "TENBENHNHAN", "Tuổi", "Nội trú", "gioitinh", "diachi", "sobhyt", "SO_PHIEU", "MA_KHAM_BENH", "stt_benhan", "stt_dotdieutri",
                    "stt_dieutri", "NGUOI_CHI_DINH", "PHONG_CHI_DINH", "MA_PHONG_XN", "KET_LUAN_TONG", "SOPHIEUTHANHTOAN", "CAPCUU", "tenkhoa", "SOVAOVIEN", "SOVAOVIEN_NOI", "SOVAOVIEN_DT_NOI", "DA_THANH_TOAN", "CAPCUU", "CHUANDOANICD", "NGAY_CHI_DINH", "SOTHEBHYT"],
                colModel: [
                    {name: 'STT_HANGNGAY', index: 'STT_HANGNGAY', width: 50, align: 'center', sorttype: 'int'},
                    {name: 'MABENHNHAN', index: 'MABENHNHAN', hidden: false, width: 100},
                    {
                        name: 'TENBENHNHAN_HT',
                        index: 'TENBENHNHAN_HT',
                        width: 200,
                        formatter: function (cellvalue, options, rowObject) {
                            var color;
                            var color_text;
                            if (rowObject.CAPCUU.toString() === "1") {
                                color = 'red';
                                color_text = 'white;weightfont:bold';
                            } else if (rowObject.DA_THANH_TOAN == "1") {
                                color = '#009900';
                                color_text = 'white';
                            } else {
                                if ("${hienthi_mausac_bn}" == "0") {
                                    color = 'white';
                                    color_text = 'black';
                                }
                            }

                            if ("${hienthi_mausac_bn}" == "1") {

                                var CAPCUU =   rowObject["CAPCUU"];
                                var cobhyt = "";
                                var chuadongvp = "";
                                var tuoi = "";
                                cobhyt = rowObject.SOTHEBHYT == null? 0: rowObject.SOTHEBHYT.toString().length;
                                tuoi = rowObject["TUOI"];
                                chuadongvp = rowObject["DA_THANH_TOAN"];

                                if (cobhyt < 1 && tuoi.indexOf("tháng") == -1) {
                                    color_text = '#bf00ff;fontweight:bold'
                                } else if (tuoi.indexOf("tháng") != -1) {
                                    color_text = '#00ff00;fontweight:bold'

                                } else if (cobhyt < 1 && chuadongvp == "1") {
                                    color_text = '#EE7600;fontweight:bold'

                                } else {
                                    color="white";
                                    color_text = 'black;fontweight:bold'

                                }
                            }

                            return '<span class="cellWithoutBackground" style="background-color:' + color + ';color:' + color_text + '">' + cellvalue + '</span>';
                        }
                    },
                    {
                        name: 'TENBENHNHAN',
                        index: 'TENBENHNHAN',
                        width: 200,
                        hidden: true,
                        sorttype: 'string',
                        searchoptions: {
                            dataInit: function (el) {
                                setTimeout(function () {
                                    $(el).focus().trigger({type: 'keypress', charCode: 13});
                                }, 20);
                            }
                        }
                    },
                    {name: 'TUOI', index: 'TUOI', width: 50, align: 'right'},
                    {name: 'NOITRU', index: 'NOITRU', width: 50, align: 'center'},
                    {name: 'GIOITINH', index: 'GIOITINH', hidden: true},
                    {name: 'DIACHI', index: 'DIACHI', hidden: true},
                    {name: 'SOTHEBHYT', index: 'SOTHEBHYT', hidden: true},
                    {name: 'SO_PHIEU', index: 'SO_PHIEU', hidden: true},
                    {name: 'MA_KHAM_BENH', index: 'MA_KHAM_BENH', hidden: true},
                    {name: 'STT_BENHAN', index: 'STT_BENHAN', hidden: true},
                    {name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', hidden: true},
                    {name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', hidden: true},
                    {name: 'NGUOI_CHI_DINH', index: 'NGUOI_CHI_DINH', hidden: true},
                    {name: 'PHONG_CHI_DINH', index: 'PHONG_CHI_DINH', hidden: true},
                    {name: 'MA_PHONG_DICHVU', index: 'MA_PHONG_XN', hidden: true},
                    {name: 'KET_QUA', index: 'KET_LUAN_TONG', hidden: true},
                    {name: 'SOPHIEUTHANHTOAN', index: 'SOPHIEUTHANHTOAN', hidden: true},
                    {name: 'CAPCUU', index: 'CAPCUU', hidden: true},
                    {name: 'TENKHOA', index: 'TENKHOA', hidden: true},
                    {name: 'SOVAOVIEN', index: 'SOVAOVIEN', hidden: true},
                    {name: 'SOVAOVIEN_NOI', index: 'SOVAOVIEN_NOI', hidden: true},
                    {name: 'SOVAOVIEN_DT_NOI', index: 'SOVAOVIEN_DT_NOI', hidden: true},
                    {name: 'DA_THANH_TOAN', index: 'DA_THANH_TOAN', hidden: true},
                    {name: 'CAPCUU', index: 'CAPCUU', hidden: true},
                    // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: thêm thông tin khoa chỉ định, chẩn đoán icd
                    {name: 'CHUANDOANICD', index: 'CHUANDOANICD', hidden: true},
                    // End ĐắkLắk
                    {name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', hidden: true},
                    {name: 'SOTHEBHYT', index: 'SOTHEBHYT', hidden: true},
                ],
                caption: "Danh sách bệnh nhân",
                ignoreCase: true,
                rowNum: 1000000,
                gridComplete: function () {
                    var c = $("#list_benhnhan").getGridParam("records");
                    $("#list_benhnhan").jqGrid('setCaption', "Danh sách bệnh nhân (" + c + " bệnh nhân)");

                },
                onSelectRow: function (id) {
                    if (id) {
                        var ret = $("#list_benhnhan").jqGrid('getRowData', id);
                        $("#mabenhnhan").val(ret.MABENHNHAN);
                        $("#hoten").val(ret.TENBENHNHAN);
                        $("#tuoi").val(ret.TUOI);
                        $("#gioitinh").val(ret.GIOITINH.toString());
                        $("#diachi").val(ret.DIACHI);
                        $("#sothebhyt").val(ret.SOTHEBHYT);
                        $("#sophieu").val(ret.SO_PHIEU);
                        $("#makhambenh").val(ret.MA_KHAM_BENH);
                        $("#noitru").val(ret.NOITRU);
                        $("#sttbenhan").val(ret.STT_BENHAN);
                        $("#sttdotdieutri").val(ret.STT_DOTDIEUTRI);
                        $("#sttdieutri").val(ret.STT_DIEUTRI);
                        $("#ketqua").val(ret.KET_QUA);
                        $("#sophieuthanhtoan").val(ret.SOPHIEUTHANHTOAN);
                        $("#sovaovien").val(ret.SOVAOVIEN);
                        $("#tenkhoa").val(ret.TENKHOA);
                        sovaovien = ret.SOVAOVIEN;
                        sovaovien_noi = ret.SOVAOVIEN_NOI;
                        sovaovien_dt_noi = ret.SOVAOVIEN_DT_NOI;
                        $("#mabenhnhan_lm").val(ret.MABENHNHAN);
                        $("#hoten_lm").val(ret.TENBENHNHAN);
                        $("#tuoi_lm").val(ret.TUOI);
                        $("#gioitinh_lm").val(ret.GIOITINH.toString());
                        $("#sovaovien_dt_lm").val(ret.SOVAOVIEN_DT_NOI);
                        $("#noitru_lm").val(ret.NOITRU);
                        $("#id_dieutri_lm").val(ret.ID_DIEUTRI);
                        ngay_chi_dinh = ret.NGAY_CHI_DINH;
                        if(ngay_chi_dinh !== null){
                            var ngaygio_chichinh_cls = ngay_chi_dinh.split(" ");
                            ngaychidinh = ngaygio_chichinh_cls[0];
                            giochidinh = ngaygio_chichinh_cls[1];
                            $("#ngaychidinh_cls").val(ngaychidinh);
                            $("#giochidinh_cls").val(giochidinh);
                        }

                        // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: thêm thông tin khoa chỉ định, chẩn đoán icd
                        $("#_chandoan").val(ret.CHUANDOANICD);
                        //$("#chandoan").val(ret.CHUANDOANICD);
                        // End ĐắkLắk

                        var dathuchien = $("#daxetnghiem").prop('checked');
                        var idath;
                        if (dathuchien == true) {
                            idath = 1;
                        } else {
                            idath = 0;
                        }
                        var arr = [ret.NOITRU, ret.SO_PHIEU, ret.STT_BENHAN, ret.STT_DOTDIEUTRI, ret.STT_DIEUTRI, "${Sess_DVTT}", sovaovien, sovaovien_noi, sovaovien_dt_noi, idath];
                        var url = "ttpt_hienthi_chitiet_svv?url=" + convertArray(arr);
                        $("#list_xn_bhyt").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
                        //HPG Hiển thị thông tin thêm về bệnh nhân
                        var hpg_STT_BENHAN = "0";
                        var hpg_STT_DIEUTRI = "0";
                        var hpg_STT_DOTDIEUTRI = "0";
                        if (ret.STT_DIEUTRI != "")
                            hpg_STT_BENHAN = ret.STT_BENHAN;
                        if (ret.STT_DIEUTRI != "")
                            hpg_STT_DOTDIEUTRI = ret.STT_DOTDIEUTRI;
                        if (ret.STT_DIEUTRI != "")
                            hpg_STT_DIEUTRI = ret.STT_DIEUTRI;
                        var arr1 = [ret.MA_KHAM_BENH, ret.SO_PHIEU, "${Sess_DVTT}", ret.NOITRU, hpg_STT_BENHAN, hpg_STT_DOTDIEUTRI, hpg_STT_DIEUTRI, 1]
                        var url1 = "hpg_thongtin_mo_rong_bn_cls?url=" + convertArray(arr1);

                        if ("${hienthi_them_cls}" == "1") {
                            hienthi_them_cls(url1);
                        }
                        var url3 = "hpg_pttt_trangthai_thanhtoan?url=" + convertArray(arr1);
                        if ("${suaketqua_cls}" == "1" && kiemtra_tt_cls(url3) > 0)
                            $("#list_xn_bhyt").jqGrid().setColProp('KET_QUA', {editable: false});
                        else $("#list_xn_bhyt").jqGrid().setColProp('KET_QUA', {editable: true});
                        //--End HPG
                        $("#cmuphanphoinguonthu").hide();
                        $("#cmuphanphoinguonthu_v2").hide();
                        $.post('cmu_post_CMU_TONGTIEN_MONEY_NOIDUNG', {
                            url: ["${Sess_DVTT}",  sovaovien == 0? sovaovien_noi: sovaovien, sovaovien_dt_noi? sovaovien_dt_noi: 0, ret.SO_PHIEU].join('```')
                        }).done(function (data) {
                            if(data == 0) {
                                $("#vnptmoney").hide();
                            } else {
                                $("#vnptmoney").show();
                                $("#vnptmoney").html("Đã thanh toán qua VNPT-MONEY: "+ data);
                            }
                        })
                        $.post('cmu_post_CMU_TONGTIEN_BANK_NOIDUNG', {
                            url: ["${Sess_DVTT}",
                                sovaovien == 0? sovaovien_noi: sovaovien, sovaovien_dt_noi? sovaovien_dt_noi: 0, ret.SO_PHIEU, 'BIDV'].join('```')
                        }).done(function (data) {
                            if(data == 0) {
                                $("#bidv").hide();
                            } else {
                                $("#bidv").show();
                                $("#bidv").html("Đã thanh toán qua BIDV: "+ new Intl.NumberFormat().format(data));
                            }
                        })
                        $.post('cmu_post_CMU_TONGTIEN_BANK_NOIDUNG', {
                            url: ["${Sess_DVTT}",
                                sovaovien == 0? sovaovien_noi: sovaovien, sovaovien_dt_noi? sovaovien_dt_noi: 0, ret.SO_PHIEU, 'VIETINBANK'].join('```')
                        }).done(function (data) {
                            if(data == 0) {
                                $("#vietinbank").hide();
                            } else {
                                $("#vietinbank").show();
                                $("#vietinbank").html("Đã thanh toán qua VIETINBANK: "+ new Intl.NumberFormat().format(data));
                            }
                        })
                    }
                },
                onRightClickRow: function (id1) {
                    if (id1) {
                        $('#list_benhnhan').jqGrid('setSelection', id1);
                        //alert(id1);

                        $.contextMenu({
                            selector: '#list_benhnhan tr',
                            callback: function (key, options) {
                                if (key == "huyketqua") {
                                    var id = $("#list_benhnhan").jqGrid('getGridParam', 'selrow');
                                    var ret = $("#list_benhnhan").jqGrid('getRowData', id);
                                    var arr = ["${Sess_DVTT}", ret.NOITRU, ret.SO_PHIEU, ret.MA_KHAM_BENH, ret.STT_BENHAN, ret.STT_DOTDIEUTRI, ret.STT_DIEUTRI, 0];
                                    if ("${Sess_Admin}" != "0") {
                                        jConfirm('Xác nhận hủy kết quả  ?', 'Thông báo', function (r) {
                                            if (r.toString() == "true") {
                                                if (ret.SO_PHIEU != "") {
                                                    var url = "huyketquattpt?url=" + convertArray(arr);
                                                    $.ajax({
                                                        url: url
                                                    }).always(function () {
                                                        var arr1 = ["${Sess_DVTT}", "Hủy kết quả TTPT-VLTL cho bệnh nhân " + ret.TEN_BENH_NHAN + " với phiếu TTPT-VLTL " + ret.SO_PHIEU, "${Sess_UserID}" + "-" + "${Sess_User}", "Hủy kết quả TTPT-VLTL"];
                                                        $.post("lichsusudung_insert", {url: convertArray(arr1)});
                                                        $("#lammoi").click();
                                                    });
                                                } else {
                                                    jAlert("Chọn phiếu để hủy", 'Thông báo');
                                                }
                                            }
                                        });
                                    } else {
                                        jAlert("Chỉ Admin mới có quyền hủy!", 'Thông báo');
                                    }
                                }
                                if (key == "goiso") {
                                    var id = $("#list_benhnhan").jqGrid('getGridParam', 'selrow');
                                    var ret = $("#list_benhnhan").jqGrid('getRowData', id);
                                    var chuoi = ${Sess_PhongDuocSet} +"|" + ret.STT_HANGNGAY.toString().replace('<span class="cellWithoutBackground" style="background-color:white;color:black">', '').replace('</span>', '') + "|" + ret.TENBENHNHAN + "|" + ret.CAPCUU;
                                    saveTextAsFile(chuoi);
                                }
                            },
                            items: {
                                "huyketqua": {name: "Hủy kết quả"},
                                "goiso": {
                                    name: "<span style='color:red'>Gọi số bệnh nhân</span>", icon: "goiso"
                                }
                            }
                        });
                    }
                }
                // End ĐắkLắk
            });
            $("#list_hinhanhttpt").jqGrid({
                datatype: "local",
                loadonce: true,
                //height: 235,
                height: 292, // ĐắkLắk (An Giang yêu cầu) - Ninh 09/12/2016: điều chỉnh độ cao lưới
                width: 290,
                colNames: ["SO_PHIEU_DICHVU", "DVTT", "idauto", "MA_DV", "Hình ảnh phẫu thuật,thủ thuật"],
                colModel: [
                    {name: 'SO_PHIEU_DICHVU', index: 'SO_PHIEU_DICHVU', hidden: true},
                    {name: 'DVTT', index: 'DVTT', hidden: true},
                    {name: 'STT', index: 'STT', hidden: true},
                    {name: 'MA_DV', index: 'MA_DV', hidden: true},
                    {name: 'HINHANH', index: 'HINHANH', width: 80, formatter: imageFormat}
                ],
                rowNum: 200000
            });
            function cmu_laynhanvienttpt(sovaovien,sovaovien_dt,sophieu,madv) {
                $.get("cmu_getlist?url="+convertArray(["${Sess_DVTT}",sovaovien,sovaovien_dt,sophieu,madv,'CMU_DSEKIP_BS'])
                ).done(function(data) {
                    if(data.length > 0) {
                        var vitri = data[0].VITRI.split(";");
                        var tennhanvien = data[0].TENBACSI.split(";");
                        var manhanvien = data[0].MABACSI.split(";");
                        var cchn = data[0].DSBACSI.split(";");
                        vitri.forEach(function(value, index){
                            $("#"+vitri[index]).val(tennhanvien[index])
                            $("#"+vitri[index]+"_cchn").val(cchn[index])
                            $("#ma_"+vitri[index]+"_cchn").val(manhanvien[index])
                        })
                    }

                })
            }

            function imageFormat(cellvalue, options, rowObject) {
                return '<img src="' + cellvalue + '" width="100px" height="80px" />';
            }

            $("#list_benhnhan").jqGrid('filterToolbar', {
                stringResult: true,
                searchOnEnter: false,
                defaultSearch: "cn"
            });
            $("#list_xn_bhyt").jqGrid({
                datatype: "local",
                loadonce: true,
                height: 250,
                width: 665,
                shrinkToFit: false,
                colNames: ["MA_DV", "Dịch vụ", "Kết quả", "Trangthai", "Ma", "DVT", "Ngày chỉ định", "Người thực hiện",
                    "Tên người thực hiện", "MA_BS_TTPT", "MA_BS_GAYME", "SO_LUONG", "STT_MAMAY"
                    , "NGAY_GIO_CHI_DINH"
                ],
                colModel: [
                    {name: 'MA_DV', index: 'MA_DV', hidden: true},
                    {name: 'TEN_DV', index: 'TEN_DV', width: 290},
                    {name: 'KET_QUA', index: 'KET_QUA', width: 65, editable: true, edittype: 'text'},
                    {name: 'TT_BHYT_CHI', index: 'TT_BHYT_CHI', hidden: true},
                    {name: 'MABAOCAO', index: 'MABAOCAO', hidden: true},
                    {name: 'DVT_DV', index: 'DVT_DV', hidden: true},
                    {name: 'NGAY_CHI_DINH_CT', index: 'NGAY_CHI_DINH_CT', width: 130},
                    {name: 'NGUOI_THUC_HIEN', index: 'NGUOI_THUC_HIEN', width: 100},
                    {name: 'TEN_NGUOI_THUC_HIEN', index: 'TEN_NGUOI_THUC_HIEN', width: 160},
                    {name: 'MA_BS_PTTP', index: 'MA_BS_PTTP', hidden: false},
                    {name: 'MA_BS_GAYME', index: 'MA_BS_GAYME', hidden: false},
                    {name: 'SO_LUONG', index: 'SO_LUONG', hidden: true},
                    {name: 'STT_MAMAY', index: 'STT_MAMAY', hidden: true}
                    ,{name: 'NGAY_GIO_CHI_DINH', index: 'NGAY_GIO_CHI_DINH', hidden: true}
                ],
                caption: "Yêu cầu dịch vụ",
                cellEdit: true,
                cellsubmit: 'clientArray',
                ondblClickRow: function (id) {
                    if (id) {
                        dialog_capnhatketquattpt.dialog("open");
                        var ret = $("#list_xn_bhyt").jqGrid('getRowData', id);
                        $("#madv").val(ret.MA_DV);
                        $("#nguoithuchien").val(ret.NGUOI_THUC_HIEN);
                        var sophieu = $("#sophieu").val();
                        var noitru = $("#noitru").val();
                        var sttbenhan = $("#sttbenhan").val();
                        var sttdotdieutri = $("#sttdotdieutri").val();
                        var sttdieutri = $("#sttdieutri").val();
                        var makhambenh = $("#makhambenh").val();
                        $("#ngaychidinh_kq").val(ngaychidinh);
                        $("#giochidinh_kq").val(giochidinh);
                        var madv = $("#madv").val();
                        var dvtt = "${Sess_DVTT}";
                        $("#phuongphappttt").val(ret.TEN_DV);
                        // ĐắkLắk (An Giang yêu cầu) - Ninh 09/12/2016: view thông tin hành chánh của BN lên form nhập kết quả
                        $("#hoten_ct").val($("#hoten").val());
                        $("#tuoi_ct").val($("#tuoi").val());
                        $("#gioitinh_ct").val($("#gioitinh").val() == "true" ? "Nam" : "Nữ");
                        $("#mabenhnhan_ct").val($("#mabenhnhan").val());
                        $("#tenkhoa_ct").val($("#tenkhoa").val());
                        $("#sothebhyt_ct").val($("#sothebhyt").val());
                        $("#chandoan").val($("#_chandoan").val());
                        $("#chonppvocam").val(0);
                        $("#trinhtupttt").val("");
                        $("#phuongphappttt").val("");
                        $("#phuongphapvocam").val("");
                        $("#bacsipttt").val("");
                        $("#bacsipttt_1").val("");
                        $("#bacsipttt_2").val("");
                        $("#bacsigayme").val("");
                        $("#bacsigayme_1").val("");
                        $("#dcvongtrong").val("");
                        $("#dcvongngoai").val("");
                        $("#catchisau7ngay").val("");
                        // End ĐắkLắk
                        var arr = [sophieu, noitru, sttbenhan, sttdotdieutri, sttdieutri, makhambenh, madv, dvtt, "0", sovaovien, sovaovien_noi, sovaovien_dt_noi];
                        var url3 = "pttt_select_ketqua_svv?url=" + convertArray(arr);
                        $("#bacsipttt").val("");
                        $("#bacsipttt_1").val("");
                        $("#bacsipttt_2").val("");
                        $("#bacsigayme").val("");
                        $("#bacsigayme_1").val("");
                        $("#dcvongtrong").val("");
                        $("#dcvongngoai").val("");
                        var idBn = $("#list_benhnhan").jqGrid('getGridParam', 'selrow');
                        var retBN = $("#list_benhnhan").jqGrid('getRowData', idBn);
                        $("#cmuchandoan_truocttpt").val("");
                        $("#cmuchandoan_truocttpt").val(retBN.CHUANDOANICD);
                        // Thêm mã bác sĩ TTPT để lấy DS_CCHN lên XML3
                        //$("#ma_bacsipttt").val(ret.MA_BS_PTTP);
                        //$("#ma_bacsigayme").val(ret.MA_BS_GAYME);

                        var ngaycd = ret.NGAY_CHI_DINH_CT.split(" ");
                        $("#ngaycd_pttt").val(ngaycd[0]);
                        $("#giocd_pttt").val(ngaycd[1]);
                        cmu_laynhanvienttpt(noitru==1?sovaovien_noi:sovaovien,noitru==1?sovaovien_dt_noi:0,sophieu,madv);
                        $.getJSON(url3, function (result) {
                            $.each(result, function (i, field) {
                                $("#ma_pp_vo_cam").val(field.PPVC);

                                if (field.CHANDOANSAUPTTT != null && field.CHANDOANSAUPTTT.toString() != "") {
                                    $("#chandoan").val(field.CHANDOANSAUPTTT);
                                }
                                //$("#chandoan").val(field.CHANDOANSAUPTTT);

                                $("#phuongphappttt").val(field.PHUONGPHAP_TT_PT == null ? ret.TEN_DV : field.PHUONGPHAP_TT_PT);
                                $("#phuongphapvocam").val(field.PHUONGPHAP_VOCAM);
                                $("#chonppvocam").val(field.PPVC);
                                if (field.BACSI_PTTT != null) {
                                    var danhsachBacsi = field.BACSI_PTTT.split(":");
                                    $("#bacsipttt").val(danhsachBacsi[0]);
                                    $("#bacsipttt_1").val(danhsachBacsi[1]);
                                    $("#bacsipttt_2").val(danhsachBacsi[2]);
                                }
                                if (field.BACSI_GAYME != null) {
                                    var danhsachGayme = field.BACSI_GAYME.split(":");
                                    $("#bacsigayme").val(danhsachGayme[0]);
                                    $("#bacsigayme_1").val(danhsachGayme[1]);
                                    $("#dcvongtrong").val(danhsachGayme[2]);
                                    $("#dcvongngoai").val(danhsachGayme[3]);
                                }
                                $("#ma_bacsipttt_1").val(field.MA_PHU_MO_VONGTRONG);
                                $("#ma_bacsipttt_2").val(field.MA_PHU_MO_VONGNGOAI);
                                $("#ma_bacsigayme_1").val(field.MA_KTV_GAYME);
                                $("#ma_dcvongtrong").val(field.MA_BS_DC_VONGTRONG);
                                $("#ma_dcvongngoai").val(field.MA_BS_DC_VONGNGOAI);

                                $("#ma_bacsipttt").val(field.MA_BS_PTTP);
                                $("#ma_bacsigayme").val(field.MA_BS_GAYME);
                                //$("#bacsipttt").val(field.BACSI_PTTT);
                                //$("#bacsigayme").val(field.BACSI_GAYME);
                                $("#catchisau7ngay").val(field.CATCHI_SAU7NGAY);
                                $("#trinhtupttt").val(field.TRINHTU_TT_PT);
                                if($("#daxetnghiem").prop("checked") == true) {
                                    $("#ngaypttt").val(field.NGAYPTTT_1);
                                    $("#giopttt").val(field.GIOPTTT_1);
                                } else {
                                    $("#ngaypttt").val("${ngayhientai}");
                                    $("#giopttt").val("${giohientai}");
                                }

                                $("#giopttt").data('da-thuc-hien', field.DA_CHAN_DOAN == 1)
                                if (field.GIOPTTT_1 == null || field.DA_CHAN_DOAN != 1) {
                                    if(tatAutoTime == 1) {
                                        var ngayHienTai = new Date();
                                        var gioHienTai = addZero(ngayHienTai.getHours());
                                        var phutHienTai = addZero(ngayHienTai.getMinutes());
                                        var giayHienTai = addZero(ngayHienTai.getSeconds());
                                        $('#giopttt').val(gioHienTai + ":" + phutHienTai + ":" + giayHienTai);
                                    } else
                                        showtime_giopttt();
                                } else {
                                    stopGioPtttTimer();
                                    $("#giopttt").val(field.GIOPTTT_1);
                                }
                                if (field.TAIBIEN == "1") {
                                    $("#cb_gaymehoisuc").prop("checked", true);
                                    $("#cb_nhiemkhuan").prop("checked", false);
                                    $("#cb_khac").prop("checked", false);
                                } else if (field.TAIBIEN == "2") {
                                    $("#cb_nhiemkhuan").prop("checked", true);
                                    $("#cb_gaymehoisuc").prop("checked", false);
                                    $("#cb_khac").prop("checked", false);
                                } else if (field.TAIBIEN == "3") {
                                    $("#cb_khac").prop("checked", true);
                                    $("#cb_nhiemkhuan").prop("checked", false);
                                    $("#cb_gaymehoisuc").prop("checked", false);
                                } else {
                                    $("#cb_khac").prop("checked", false);
                                    $("#cb_nhiemkhuan").prop("checked", false);
                                    $("#cb_gaymehoisuc").prop("checked", false);
                                }

                                if (field.TUVONG == "1") {
                                    $("#cb_trenban").prop("checked", true);
                                    $("#cb_trong24gio").prop("checked", false);
                                } else if (field.TUVONG == "2") {
                                    $("#cb_trong24gio").prop("checked", true);
                                    $("#cb_trenban").prop("checked", false);
                                } else {
                                    $("#cb_trong24gio").prop("checked", false);
                                    $("#cb_trenban").prop("checked", false);
                                }
                                $("#chkVetThuongTaiPhat").setCheckBoxValue(field.VET_THUONG_TP);
                                $("#thoigiankt").val(field.NGAYPTTT_KT1);
                                $("#giokt").val(field.GIOPTTT_KT1);
                            });
                        });
                        var arr = [sophieu, dvtt, madv, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
                        var url = 'pttt_danhsach_hinhanh?url=' + convertArray(arr);
                        $("#list_hinhanhttpt").jqGrid('setGridParam', {
                            datatype: 'json',
                            url: url
                        }).trigger('reloadGrid');
                        var ngay_chi_dinh_ct = ret.NGAY_GIO_CHI_DINH;
                        if (ngay_chi_dinh_ct !== null) {
                            var ngaygio_chichinh_cls_ct = ngay_chi_dinh_ct.split(" ");
                            var ngaychidinh_ct = ngaygio_chichinh_cls_ct[0];
                            var giochidinh_ct = ngaygio_chichinh_cls_ct[1];
                            $("#ngaychidinh_kq").val(ngaychidinh_ct);
                            $("#giochidinh_kq").val(giochidinh_ct);
                        }
                    }
                    getNguoiDocKetQua(sophieu, noitru==1?sovaovien_noi:sovaovien, sovaovien_dt_noi, noitru, madv);
                },
                onCellSelect: function (id) {
                    if (id) {
                        var ret = $("#list_xn_bhyt").jqGrid('getRowData', id);
                        checkEkipThietLap(ret.MA_DV);
                        checkEkipThietLap_v2(ret.MA_DV);
                    }

                },
                // onSelectRow: function (id) {
                //     if (id) {
                //         alert("qưeqweq");
                //     }
                // },
                // onRightClickRow: function (id2) {
                //     alert("onRightClickRow:");
                // },
                beforeSaveCell: function (rowid, name, val, iRow, iCol) {
                }
                ,
                afterSaveCell: function (rowid, name, val, iRow, iCol) {
                    var ret = $("#list_xn_bhyt").jqGrid('getRowData', rowid);
                    var arr = [$("#sophieu").val(), ret.MA_DV, val.replace(/\+/g, "%2B"), "${Sess_DVTT}", $("#noitru").val(), $("#makhambenh").val(),
                        $("#sttbenhan").val(), $("#sttdotdieutri").val(), $("#sttdieutri").val(), "0"];
                    var url = "ttpt_update_ketqua_chitiet";
                    $.post(url, {
                        url: convertArray(arr)
                    }).done(function () {
                        var sophieu = $("#sophieu").val();
                        var noitru = $("#noitru").val();
                        var sttbenhan = $("#sttbenhan").val();
                        var sttdotdieutri = $("#sttdotdieutri").val();
                        var sttdieutri = $("#sttdieutri").val();
                        var dathuchien = $("#daxetnghiem").prop('checked');
                        var idath;
                        if (dathuchien == true) {
                            idath = 1;
                        } else {
                            idath = 0;
                        }
                        var arr1 = [noitru, sophieu, sttbenhan, sttdotdieutri, sttdieutri, "${Sess_DVTT}", sovaovien, sovaovien_noi, sovaovien_dt_noi, idath];
                        var url1 = "ttpt_hienthi_chitiet_svv?url=" + convertArray(arr1);
                        $("#list_xn_bhyt").jqGrid('setGridParam', {datatype: 'json', url: url1}).trigger('reloadGrid');
                    });
                }
            });
            $("#lammoi").click(function (evt) {
                reload_grid();
            });
            $("#div_list_ttpt").click(function (evt) {
                var id = $("#list_xn_bhyt").jqGrid('getGridParam', 'selrow');
                var ret = $("#list_xn_bhyt").jqGrid('getRowData', id);
                $("#madv").val(ret.MA_DV);
                $("#sttMayTTPT").val(ret.STT_MAMAY);
            });
            $("#luuthongtin").click(function (evt) {
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var ketluan = $("#ketqua").val();
                var ngaygioth_cls = "";
                if ($("#ngaythuchien_cls").val() != '' && $("#giothuchien_cls").val() != '') {
                    ngaygioth_cls = convertStr_MysqlDate($("#ngaythuchien_cls").val()) + " " + $("#giothuchien_cls").val();
                }
                var arr2 = [dvtt, noitru, sophieu, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, ketluan, "1", sovaovien, sovaovien_noi, sovaovien_dt_noi, ngaygioth_cls, "${Sess_UserID}"];
                var url2 = "ttpt_update_trangthai_svv";
                var arr1 = [makhambenh, sophieu, "${Sess_DVTT}", noitru, sttbenhan, sttdotdieutri, sttdieutri, 0]
                var url1 = "hpg_pttt_trangthai_thanhtoan?url=" + convertArray(arr1);
                if ("${suaketqua_cls}" == "1" && kiemtra_tt_cls(url1) > 0) {
                    jAlert("Bệnh nhân đã thanh toán viện phí. Không được thay đổi kết quả.", 'Cảnh báo');
                    return;
                } else
                    $.post(url2, {
                        url: convertArray(arr2)
                    }).done(function () {
                        luuNguoiDocKetQua(sophieu, noitru==1?sovaovien_noi:sovaovien, sovaovien_dt_noi, noitru);
                        reload_grid();
                        jAlert("Cập nhật thành công", 'Thông báo');
                    });
            });

            $("#luufile").click(function (evt) {
                if ($("#madv").val() == "")
                    jAlert("Vui lòng chọn dịch vụ để thêm ảnh!", "Cảnh báo");
                else
                    showUploadDialog();
            });

            $("#luufileanh").click(function (evt) {
                showUploadDialog();
            });

            $("#inphieu").click(function (evt) {
                var madv = $("#madv").val();
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var sovaovien = $("#sovaovien").val();
                var hotenbenhnhan = $("#hoten").val();
                var tuoi = $("#tuoi").val();
                var gioitinh = $("#gioitinh").val();
                if (gioitinh.toString() == "true") {
                    gioitinh = "Nam";
                } else {
                    gioitinh = "Nữ";
                }
                if (madv != "") {
                    //mặc đinh cho in không hình
                    var arr = [madv, sophieu, makhambenh, dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, sovaovien, hotenbenhnhan, tuoi, gioitinh, "0"];
                    var url = "inphieuthuthuatphauthuat_hinhmau?url=" + convertArray(arr);
                    //HPG--- Xem truc tiep bao cao
                    if ("${xemtructiep_bc}" == "1") {
                        var redirectWindow = window.open(url, '_blank');
                        redirectWindow.location;
                        return false;

                    } else
                        //-------End
                        $(location).attr('href', url);
                } else {
                    jAlert("Vui lòng chọn dịch vụ để kết quả", 'Cảnh báo');
                }
            });

            function decodeHTMLEntities (str) {
                if(str && typeof str === 'string') {
                    // strip script/html tags
                    str = str.replace(/<script[^>]*>([\S\s]*?)<\/script>/gmi, '');
                    str = str.replace(/<\/?\w(?:[^"'>]|"[^"]*"|'[^']*')*>/gmi, '');
                    //element.innerHTML = str;
                    //str = element.textContent;
                    //element.textContent = '';
                }

                return str;
            }

            loadBacSiTheoKhoa(SESS_PHONG_BAN);
            $("#luu_tt").click(function (evt) {

                var ketQuaKiemTra = THAMSO_828449=="1"?kiemTraThoiGianYLenhHopLe():"1";
                if(ketQuaKiemTra!="1"){
                    jAlert(ketQuaKiemTra, 'Thông báo');
                    return false;
                }
                if (checkNguoiDocKetQua()) {
                    jAlert("Người đọc kết quả không hợp lệ", "Cảnh báo");
                    return false;
                }
                if ($("#ngaypttt").val() == '' || $("#giopttt").val() == '') {
                    jAlert("Ngày giờ thủ thuật/phẫu thuật không được trống");
                    return false;
                }
                if(!$("#ma_pp_vo_cam").val()) {
                    jAlert("Vui lòng chon mã phương pháp vô cảm", "Cảnh báo");
                    return false;
                }

                var ngaygiocd_pttt = new Date(($("#ngaycd_pttt").val() + ' ' + $("#giocd_pttt").val()).replace(/(.*)\/(.*)\/(.*)/, "$2/$1/$3")); //ouput: "MM/dd/yyyy HH:mm:ss");
                var ngaygioth_pttt = new Date(($("#ngaypttt").val() + ' ' + $("#giopttt").val()).replace(/(.*)\/(.*)\/(.*)/, "$2/$1/$3"));; //ouput: "MM/dd/yyyy HH:mm:ss");
                var regEx = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
                if(isNaN(ngaygioth_pttt.getTime())){
                    jAlert("Ngày thực hiện không hợp lệ.");
                    return;
                }
                if(ngaygioth_pttt < ngaygiocd_pttt){
                    jAlert('Ngày PTTT phải nhỏ hơn ngày chỉ định', 'Thông báo');
                    return;
                }
                var ngayketthucpttt = $("#thoigiankt").val() + " " + $("#giokt").val();//tvh thêm ngaykt pttt
                var ngaythuchien = $("#ngaypttt").val() + " " + $("#giopttt").val();
                var ngayThuchienArr = $("#ngaypttt").val().split("/");
                var gioThucHienArr = $("#giopttt").val().split(":");
                var ngayGioChiDinhArr = ngay_chi_dinh.split(" ");
                var ngayChiDinhArr = ngayGioChiDinhArr[0].split("/");
                var gioChiDinhArr = ngayGioChiDinhArr[1].split(":");
                var dateNgayThucHien = new  Date(ngayThuchienArr[2],ngayThuchienArr[1],ngayThuchienArr[0],gioThucHienArr[0],gioThucHienArr[1],gioThucHienArr[2]);
                var dateNgayChiDinh = new Date(ngayChiDinhArr[2],ngayChiDinhArr[1],ngayChiDinhArr[0],gioChiDinhArr[0],gioChiDinhArr[1],gioChiDinhArr[2]);
                if ("${thamso_82860467}" == "1" && (dateNgayThucHien < dateNgayChiDinh)) {
                    jAlert("Thời gian cập nhật kết quả nhỏ hơn thời gian chỉ định", "Cảnh báo")
                } else if ("${Sess_Admin}"  == "0" && "${Sess_DVTT}" != "96025" && $("#nguoithuchien").val() != "0" && (((($("#nguoithuchien").val().indexOf(";")) < 0 ? ($("#nguoithuchien").val() + ";") : $("#nguoithuchien").val())).indexOf("${Sess_UserID}" + ';')) < 0 && $("#nguoithuchien").val().trim() != "") {
                    jAlert("Bạn không thể chỉnh sửa KQ thủ thuật phẫu thuật của nhân viên khác!", 'Thông báo');
                } else if (THAMSO_828327 == "1" && $("#ma_bacsipttt").val() == "") {
                    jAlert("Vui lòng nhập Phẫu thuật/thủ thuật viên!", 'Thông báo');
                } else {
                    var ngayChiDinh = $('#ngaychidinh_kq').val();
                    var gioChiDinh = $('#giochidinh_kq').val();
                    var thoiGianChiDinh = ngayChiDinh + ' ' + gioChiDinh
                    var ngayTHYL = $('#ngaypttt').val();
                    var gioTHYL = $('#giopttt').val();
                    var thoiGianTHYL = ngayTHYL + ' ' + gioTHYL;
                    var ngayKQ = $('#thoigiankt').val();
                    var gioKQ = $('#giokt').val();
                    var thoiGianKQ = ngayKQ + ' ' + gioKQ;

                    var momentChiDinh = moment(thoiGianChiDinh, ['DD/MM/YYYY HH:mm:ss']);
                    var momentThucHienYLenh = moment(thoiGianTHYL, ['DD/MM/YYYY HH:mm:ss']);
                    var momentKetQua = moment(thoiGianKQ, ['DD/MM/YYYY HH:mm:ss']);

                    if(momentThucHienYLenh.diff(momentChiDinh, 'minutes') < 1){
                        jAlert('THỜI GIAN THỰC HIỆN Y LỆNH : '+thoiGianTHYL+'<br> KHÔNG ĐƯỢC NHỎ HƠN HOẶC BẰNG'+'<br>THỜI GIAN CHỈ ĐỊNH : ' + thoiGianChiDinh , 'Thông báo');
                        return;
                    }

                    if(momentKetQua.diff(momentThucHienYLenh, 'minutes') < 1){
                        jAlert('THỜI GIAN KẾT QUẢ : '+thoiGianKQ+'<br> KHÔNG ĐƯỢC NHỎ HƠN HOẶC BẰNG'+'<br>THỜI GIAN THỰC HIỆN Y LỆNH : ' + thoiGianTHYL , 'Thông báo');
                        return;
                    }

                    if(momentKetQua.diff(momentThucHienYLenh, 'minutes') < 5){
                        jAlert('THỜI GIAN THỰC HIỆN Y LỆNH: '+thoiGianTHYL+'<br> ĐẾN GIỜ '+'<br>THỜI GIAN KẾT QUẢ : ' + thoiGianKQ + " KHÔNG ĐƯỢC NHỎ HƠN 5 PHÚT", 'Thông báo');
                        return;
                    }

                    if("${Sess_DVTT}" == '96014'){
                        var ten_dv = $('#phuongphappttt').val();
                        if(ten_dv.toLowerCase().indexOf("điện châm") !== -1 && (momentKetQua.diff(momentThucHienYLenh, 'minutes') < 25)){
                            jAlert('THỜI GIAN TRẢ KẾT QUẢ KHÔNG ĐƯỢC NHỎ HƠN 25 PHÚT', 'Thông báo');
                            return;
                        } else if (ten_dv.toLowerCase().indexOf("sóng ngắn") !== -1 && (momentKetQua.diff(momentThucHienYLenh, 'minutes') < 20)){
                            jAlert('THỜI GIAN TRẢ KẾT QUẢ KHÔNG ĐƯỢC NHỎ HƠN 20 PHÚT', 'Thông báo');
                            return;
                        } else if (ten_dv.toLowerCase().indexOf("xoa bóp") !== -1 && (momentKetQua.diff(momentThucHienYLenh, 'minutes') < 30)){
                            jAlert('THỜI GIAN TRẢ KẾT QUẢ KHÔNG ĐƯỢC NHỎ HƠN 30 PHÚT', 'Thông báo');
                            return;
                        }
                    }

                    var madv = $("#madv").val();
                    var sophieu = $("#sophieu").val();
                    var makhambenh = $("#makhambenh").val();
                    var dvtt = "${Sess_DVTT}";
                    var noitru = $("#noitru").val();
                    var sttbenhan = $("#sttbenhan").val();
                    var sttdotdieutri = $("#sttdotdieutri").val();
                    var sttdieutri = $("#sttdieutri").val();
                    var chandoan = $("#chandoan").val();
                    var phuongphappttt = $("#phuongphappttt").val();
                    var phuongphapvocam = $("#phuongphapvocam").val();
                    var PPVC = $("#ma_pp_vo_cam").val();
                    var bacsipttt = $("#bacsipttt").val() + ":" + $("#bacsipttt_1").val() + ":" + $("#bacsipttt_2").val();
                    var bacsigayme = $("#bacsigayme").val() + ":" + $("#bacsigayme_1").val() + ":" + $("#dcvongtrong").val() + ":" + $("#dcvongngoai").val();
                    var catchisau7ngay = $("#catchisau7ngay").val().trim();
                    var trinhtupttt = $("#trinhtupttt").val().trim();
                    var trinhtupttt_xml5 =$('<textarea />').html(trinhtupttt).text();
                    var trinhtupttt_xml5_luu = decodeHTMLEntities(trinhtupttt_xml5);
                    if(trinhtupttt_xml5_luu.length > 4000) {
                        jAlert("Tường trình thủ thuật phẫu thuật không được quá 4000 ký tự", "Cảnh báo");
                        return false;
                    }
                    var ngaypttt = convertStr_MysqlDate($("#ngaypttt").val());
                    var nguoithuchien = "${Sess_UserID}";
                    var giopttt = $("#giopttt").val();
                    var taibien = "0";
                    var tuvong = "0";
                    var check = $("#cb_gaymehoisuc").prop("checked");
                    if (check == true) {
                        taibien = "1";
                    }
                    check = $("#cb_nhiemkhuan").prop("checked");
                    if (check == true) {
                        taibien = "2";
                    }
                    check = $("#cb_khac").prop("checked");
                    if (check == true) {
                        taibien = "3";
                    }
                    check = $("#cb_trenban").prop("checked");
                    if (check == true) {
                        var tuvong = "1";
                    }
                    check = $("#cb_trong24gio").prop("checked");
                    if (check == true) {
                        var tuvong = "2";
                    }
                    if($('#phuongphapvocam').val() == "") {
                        jAlert("Vui lòng chọn PP vô cảm");
                        return false;
                    }
                    var ngaygiopttt = ngaypttt + " " + giopttt;
                    var ngaygioth_ct = "";
                    if ($("#ngaypttt").val() != '' && $("#giopttt").val() != '') {
                        ngaygioth_ct = convertStr_MysqlDate($("#ngaypttt").val()) + " " + $("#giopttt").val();
                    }
                    var ngayketpt = convertStr_MysqlDate($("#thoigiankt").val()) + " " + $("#giokt").val();
                    var ma_bacsipttt = $("#ma_bacsipttt").val();
                    var ma_phumo_vongtrong = $("#ma_bacsipttt_1").val();
                    var ma_phumo_vongngoai = $("#ma_bacsipttt_2").val();
                    var ma_bacsigayme = $("#ma_bacsigayme").val();
                    var ma_ktv_gayme = $("#ma_bacsigayme_1").val();
                    var ma_dcvongtrong = $("#ma_dcvongtrong").val();
                    var ma_dcvongngoai = $("#ma_dcvongngoai").val();
                    if(ma_bacsipttt == "" || $("#bacsipttt").val() == "") {
                        jAlert("Vui lòng chọn Phẫu thuật/thủ thuật viên");
                        return false;
                    }

                    if(ma_phumo_vongtrong == "" && $("#bacsipttt_1").val() != "") {
                        jAlert("Vui lòng chọn Bác sĩ gây mê/tê:");
                        return false;
                    }

                    if(ma_phumo_vongngoai == "" && $("#bacsipttt_2").val() != "") {
                        jAlert("Vui lòng chọn Bác sĩ gây mê/tê:");
                        return false;
                    }

                    if(ma_bacsigayme == "" && $("#bacsigayme").val() != "") {
                        jAlert("Vui lòng chọn Bác sĩ gây mê/tê:");
                        return false;
                    }

                    if(ma_ktv_gayme == "" && $("#bacsigayme_1").val() != "") {
                        jAlert("Vui lòng chọn KTV gây mê/tê:");
                        return false;
                    }

                    if(ma_dcvongtrong == "" && $("#dcvongtrong").val() != "") {
                        jAlert("Vui lòng chọn DC vòng trong:");
                        return false;
                    }

                    if(ma_dcvongngoai == "" && $("#dcvongngoai").val() != "") {
                        jAlert("Vui lòng chọn DC vòng ngoài:");
                        return false;
                    }
                    if(THAMSO_960625 == "1" && typeof cmuKiemtratungThoigianCLSTheoNV == 'function' &&  !cmuKiemtratungThoigianCLSTheoNV({
                        dvtt: "${Sess_DVTT}",
                        sovaovien: $("#noitru").val()==1?sovaovien_noi:sovaovien,
                        userId: [$("#bacsipttt").val(), $("#bacsipttt_1").val(),
                            $("#bacsipttt_2").val(), $("#bacsigayme").val(),
                            $("#bacsigayme_1").val(), $("#dcvongtrong").val(),  $("#dcvongngoai").val()
                        ].join(","),
                        thoigianbd: momentThucHienYLenh.format('DD/MM/YYYY HH:mm'),
                        thoigiankt: momentKetQua.format('DD/MM/YYYY HH:mm'),
                        loaikythuat: "KTTTPT"
                    })) {
                        return false;
                    }

                    var thoigiankt = convertStr_MysqlDate($("#thoigiankt").val());
                    var giokt = $("#giokt").val();
                    var ngaygiopttt_kt = thoigiankt + " " + giokt;
                    if (madv != "") {
                        luuNguoiDocKetQua(sophieu, noitru==1?sovaovien_noi:sovaovien, sovaovien_dt_noi, noitru, madv);
                        //---------------
                        if ("${suaketqua_cls}" == "1") {
                            var arr1 = [makhambenh, sophieu, "${Sess_DVTT}", noitru, sttbenhan, sttdotdieutri, sttdieutri, 0];
                            var url1 = "hpg_pttt_trangthai_thanhtoan?url=" + convertArray(arr1);
                            $.ajax({
                                url: url1
                            }).done(function (data) {
                                if (data == "2") {
                                    jAlert("Bệnh nhân đã thanh toán viện phí. Không được thay đổi kết quả.", 'Cảnh báo');
                                    return;
                                } else {
                                    var ktrathoigian = $.ajax({type: "POST", url: "cmu_post", async: false, //Chỉ định CLS Viện Phí lấy giá trên grid. cho phép sửa giá
                                        data: {url: ["${Sess_DVTT}", $("#sophieu").val(), ngayketpt,
                                                sovaovien_noi == 0? sovaovien:sovaovien_noi, noitru,'CMU_KTRATHOI_CDVAKQ'].join('```')}
                                    }).responseText;
                                    if(ktrathoigian == 1) {
                                        jAlert("Thời gian thực hiện không được dưới 5 phút", 'Cảnh báo');
                                        return false;
                                    }
                                    $.post("ttpt_capnhat_ketqua_svv", {
                                        madv: madv,
                                        sophieu: sophieu,
                                        makhambenh: makhambenh,
                                        dvtt: dvtt,
                                        noitru: noitru,
                                        sttbenhan: sttbenhan,
                                        sttdotdieutri: sttdotdieutri,
                                        sttdieutri: sttdieutri,
                                        chandoan: chandoan,
                                        phuongphappttt: phuongphappttt,
                                        phuongphapvocam: phuongphapvocam,
                                        bacsipttt: bacsipttt,
                                        bacsigayme: bacsigayme,
                                        catchisau7ngay: catchisau7ngay,
                                        trinhtupttt: trinhtupttt,
                                        ngaygiopttt: ngaygiopttt,
                                        taibien: taibien,
                                        tuvong: tuvong,
                                        sovaovien: sovaovien,
                                        sovaovien_noi: sovaovien_noi,
                                        sovaovien_dt_noi: sovaovien_dt_noi,
                                        nguoithuchien: nguoithuchien,
                                        ma_bacsittpt : ma_bacsipttt,
                                        ma_phumo_vongtrong : ma_phumo_vongtrong,
                                        ma_phumo_vongngoai : ma_phumo_vongngoai,
                                        ma_bacsigayme : ma_bacsigayme,
                                        ma_ktv_gayme : ma_ktv_gayme,
                                        ma_dcvongtrong : ma_dcvongtrong,
                                        ma_dcvongngoai : ma_dcvongngoai,
                                        trinhtupttt_xml5: trinhtupttt_xml5_luu
                                        ,ngaygiopttt_kt: ngaygiopttt_kt
                                        ,PPVC: PPVC
                                        ,vetThuongTaiPhat: $("#chkVetThuongTaiPhat").getCheckBoxValue()
                                    }).done(function () {
                                        reload_grid();
                                        jAlert("Cập nhật thành công", 'Thông báo');

                                    });
                                }
                            });
                            var dscchn = '';
                            var manhanvien = '';
                            var tennhan = '';
                            var vitri = '';
                            if($("#bacsipttt").val().trim() != '') {
                                dscchn+=$("#bacsipttt_cchn").val()+';'
                                manhanvien+=$("#ma_bacsipttt_cchn").val()+';'
                                tennhan+=$("#bacsipttt").val()+';'
                                vitri += 'bacsipttt;';
                            }
                            if($("#bacsipttt_1").val().trim() != '') {
                                dscchn+=$("#bacsipttt_1_cchn").val()+';'
                                manhanvien+=$("#ma_bacsipttt_1_cchn").val()+';'
                                tennhan+=$("#bacsipttt_1").val()+';'
                                vitri += 'bacsipttt_1;';
                            }
                            if($("#bacsipttt_2").val().trim() != '') {
                                dscchn+=$("#bacsipttt_2_cchn").val()+';'
                                manhanvien+=$("#ma_bacsipttt_2_cchn").val()+';'
                                tennhan+=$("#bacsipttt_2").val()+';'
                                vitri += 'bacsipttt_2;';
                            }
                            if($("#bacsigayme").val().trim() != '') {
                                dscchn+=$("#bacsigayme_cchn").val()+';'
                                manhanvien+=$("#ma_bacsigayme_cchn").val()+';'
                                tennhan+=$("#bacsigayme").val()+';'
                                vitri += 'bacsigayme;';
                            }
                            if($("#bacsigayme_1").val().trim() != '') {
                                dscchn+=$("#bacsigayme_1_cchn").val()+';'
                                manhanvien+=$("#ma_bacsigayme_1_cchn").val()+';'
                                tennhan+=$("#bacsigayme_1").val()+';'
                                vitri += 'bacsigayme_1;';
                            }
                            if($("#dcvongtrong").val().trim() != '') {
                                dscchn+=$("#dcvongtrong_cchn").val()+';'
                                manhanvien+=$("#ma_dcvongtrong_cchn").val()+';'
                                tennhan+=$("#dcvongtrong").val()+';'
                                vitri += 'dcvongtrong;';
                            }
                            if($("#dcvongngoai").val().trim() != '') {
                                dscchn+=$("#dcvongngoai_cchn").val()+';'
                                manhanvien+=$("#ma_dcvongngoai_cchn").val()+';'
                                tennhan+=$("#dcvongngoai").val()+';'
                                vitri += 'dcvongngoai;';
                            }
                            if($("#cmupt_pm4").val().trim() != '') {
                                dscchn+=$("#cmupt_pm4_cnhh").val()+';'
                                manhanvien+=$("#ma_cmupt_pm4_cnhh").val()+';'
                                tennhan+=$("#cmupt_pm4").val()+';'
                                vitri += 'cmupt_pm4;';
                            }
                            $.post("cmu_post", {
                                url: ["${Sess_DVTT}",madv,sophieu,noitru==1?sovaovien_noi:sovaovien,noitru==1?sovaovien_dt_noi:0,dscchn
                                    ,manhanvien,tennhan,vitri,'CMU_LUUCCHN_EKIP'].join("```")
                            })
                        } else   //---End
                            var ktrathoigian = $.ajax({type: "POST", url: "cmu_post", async: false, //Chỉ định CLS Viện Phí lấy giá trên grid. cho phép sửa giá
                                data: {url: ["${Sess_DVTT}", $("#sophieu").val(),ngayketpt,
                                        sovaovien_noi == 0? sovaovien:sovaovien_noi, noitru,'CMU_KTRATHOI_CDVAKQ'].join('```')}
                            }).responseText;
                        if(ktrathoigian == 1) {
                            jAlert("Thời gian thực hiện không được dưới 5 phút", 'Cảnh báo');
                            return false;
                        }
                        $.post("ttpt_capnhat_ketqua_svv", {
                            madv: madv,
                            sophieu: sophieu,
                            makhambenh: makhambenh,
                            dvtt: dvtt,
                            noitru: noitru,
                            sttbenhan: sttbenhan,
                            sttdotdieutri: sttdotdieutri,
                            sttdieutri: sttdieutri,
                            chandoan: chandoan,
                            phuongphappttt: phuongphappttt,
                            phuongphapvocam: phuongphapvocam,
                            bacsipttt: bacsipttt,
                            bacsigayme: bacsigayme,
                            catchisau7ngay: catchisau7ngay,
                            trinhtupttt: trinhtupttt,
                            ngaygiopttt: ngaygiopttt,
                            taibien: taibien,
                            tuvong: tuvong,
                            sovaovien: sovaovien,
                            sovaovien_noi: sovaovien_noi,
                            sovaovien_dt_noi: sovaovien_dt_noi,
                            nguoithuchien: nguoithuchien,
                            ma_bacsittpt : ma_bacsipttt,
                            ma_phumo_vongtrong : ma_phumo_vongtrong,
                            ma_phumo_vongngoai : ma_phumo_vongngoai,
                            ma_bacsigayme : ma_bacsigayme,
                            ma_ktv_gayme : ma_ktv_gayme,
                            ma_dcvongtrong : ma_dcvongtrong,
                            ma_dcvongngoai : ma_dcvongngoai,
                            trinhtupttt_xml5: trinhtupttt_xml5_luu
                            ,ngaygiopttt_kt: ngaygiopttt_kt
                            ,PPVC: PPVC
                            ,vetThuongTaiPhat: $("#chkVetThuongTaiPhat").getCheckBoxValue()
                        }).done(function () {
                            reload_grid();
                            jAlert("Cập nhật thành công", 'Thông báo');
                        });
                        var dscchn = '';
                        var manhanvien = '';
                        var tennhan = '';
                        var vitri = '';
                        if($("#bacsipttt").val().trim() != '') {
                            dscchn+=$("#bacsipttt_cchn").val()+';'
                            manhanvien+=$("#ma_bacsipttt_cchn").val()+';'
                            tennhan+=$("#bacsipttt").val()+';'
                            vitri += 'bacsipttt;';
                        }
                        if($("#bacsipttt_1").val().trim() != '') {
                            dscchn+=$("#bacsipttt_1_cchn").val()+';'
                            manhanvien+=$("#ma_bacsipttt_1_cchn").val()+';'
                            tennhan+=$("#bacsipttt_1").val()+';'
                            vitri += 'bacsipttt_1;';
                        }
                        if($("#bacsipttt_2").val().trim() != '') {
                            dscchn+=$("#bacsipttt_2_cchn").val()+';'
                            manhanvien+=$("#ma_bacsipttt_2_cchn").val()+';'
                            tennhan+=$("#bacsipttt_2").val()+';'
                            vitri += 'bacsipttt_2;';
                        }
                        if($("#bacsigayme").val().trim() != '') {
                            dscchn+=$("#bacsigayme_cchn").val()+';'
                            manhanvien+=$("#ma_bacsigayme_cchn").val()+';'
                            tennhan+=$("#bacsigayme").val()+';'
                            vitri += 'bacsigayme;';
                        }
                        if($("#bacsigayme_1").val().trim() != '') {
                            dscchn+=$("#bacsigayme_1_cchn").val()+';'
                            manhanvien+=$("#ma_bacsigayme_1_cchn").val()+';'
                            tennhan+=$("#bacsigayme_1").val()+';'
                            vitri += 'bacsigayme_1;';
                        }
                        if($("#dcvongtrong").val().trim() != '') {
                            dscchn+=$("#dcvongtrong_cchn").val()+';'
                            manhanvien+=$("#ma_dcvongtrong_cchn").val()+';'
                            tennhan+=$("#dcvongtrong").val()+';'
                            vitri += 'dcvongtrong;';
                        }
                        if($("#dcvongngoai").val().trim() != '') {
                            dscchn+=$("#dcvongngoai_cchn").val()+';'
                            manhanvien+=$("#ma_dcvongngoai_cchn").val()+';'
                            tennhan+=$("#dcvongngoai").val()+';'
                            vitri += 'dcvongngoai;';
                        }
                        if($("#cmupt_pm4").val().trim() != '') {
                            dscchn+=$("#cmupt_pm4_cnhh").val()+';'
                            manhanvien+=$("#ma_cmupt_pm4_cnhh").val()+';'
                            tennhan+=$("#cmupt_pm4").val()+';'
                            vitri += 'cmupt_pm4;';
                        }
                        $.post("cmu_post", {
                            url: ["${Sess_DVTT}",madv,sophieu,noitru==1?sovaovien_noi:sovaovien,noitru==1?sovaovien_dt_noi:0,dscchn
                                ,manhanvien,tennhan,vitri,'CMU_LUUCCHN_EKIP'].join("```")
                        })

                    }
                }
            });
            function kiemTraThoiGianYLenhHopLe() {
                var objThoiGianChiDinhChiTiet = {name: "Thời gian chỉ định", value: $("#ngaychidinh_kq").val() + " " + $("#giochidinh_kq").val()};
                var objThoiGianThucHienYLenh = {name: "Thời gian thực hiện Y lệnh", value: $("#ngaypttt").val() + " " + $("#giopttt").val()};
                var objThoiGianKetQua = {name: "Thời gian kết quả", value: $("#thoigiankt").val() + " " + $("#giokt").val()};
                //ThoiGianKetQua > ThoiGianThucHienYLenh > ThoiGianChiDinhChiTiet
                var objCompare = validateAndCompareDatesToMinute(objThoiGianKetQua,objThoiGianThucHienYLenh,objThoiGianChiDinhChiTiet);
                if ((objCompare.errorCode=="-1" || objCompare.errorCode=="-2") && objCompare.objects.length>0) {
                    if(objCompare.errorCode=="-1") {
                        return "Lỗi định dạng " + objCompare.objects[0].name;
                    } else {
                        return "Lỗi " + objCompare.objects[0].name + " phải sau " + objCompare.objects[1].name + " tính đến phút";
                    }
                }
                return "1"; //Hợp lệ
            }
            $("#inphieu_ttpt").click(function (evt) {
                var madv = $("#madv").val();
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var sovaovien = $("#sovaovien").val();
                var hotenbenhnhan = $("#hoten").val();
                var tuoi = $("#tuoi").val();
                var gioitinh = $("#gioitinh").val();
                if (gioitinh.toString() == "true") {
                    gioitinh = "Nam";
                } else {
                    gioitinh = "Nữ";
                }
                if (madv != "") {
                    //In phiếu có hình cho tham số cuối = 1
                    var arr = [madv, sophieu, makhambenh, dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri,
                        sovaovien, hotenbenhnhan, tuoi, gioitinh, "1"];
                    var url = "inphieuthuthuatphauthuat_hinhmau?url=" + convertArray(arr);
                    //HPG--- Xem truc tiep bao cao
                    if ("${xemtructiep_bc}" == "1") {
                        var redirectWindow = window.open(url, '_blank');
                        redirectWindow.location;
                        return false;

                    } else
                        //-------End
                        $(location).attr('href', url);
                } else {
                    jAlert("Vui lòng chọn dịch vụ để kết quả", 'Cảnh báo');
                }
            });
            $("#inphieu_ttpt_khonghinhmau").click(function (evt) {
                var madv = $("#madv").val();
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var sovaovien = $("#sovaovien").val();
                var hotenbenhnhan = $("#hoten").val();
                var tuoi = $("#tuoi").val();
                var gioitinh = $("#gioitinh").val();
                if (gioitinh.toString() == "true") {
                    gioitinh = "Nam";
                } else {
                    gioitinh = "Nữ";
                }
                if (madv != "") {
                    //In phiếu không có hình cho tham số cuối = 0
                    var arr = [madv, sophieu, makhambenh, dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, sovaovien, hotenbenhnhan, tuoi, gioitinh, "0"];
                    var url = "inphieuthuthuatphauthuat_hinhmau?url=" + convertArray(arr);
                    //HPG--- Xem truc tiep bao cao
                    if ("${xemtructiep_bc}" == "1") {
                        var redirectWindow = window.open(url, '_blank');
                        redirectWindow.location;
                        return false;

                    } else
                        //-------End
                        $(location).attr('href', url);
                } else {
                    jAlert("Vui lòng chọn dịch vụ để kết quả", 'Cảnh báo');
                }
            });
            // KGG thêm in phiếu chứng nhận thủ thuật
            $("#inphieuchungnhan").click(function (evt) {
                var madv = $("#madv").val();
                var sophieu = $("#sophieu").val();
                var makhambenh = $("#makhambenh").val();
                var dvtt = "${Sess_DVTT}";
                var noitru = $("#noitru").val();
                var sttbenhan = $("#sttbenhan").val();
                var sttdotdieutri = $("#sttdotdieutri").val();
                var sttdieutri = $("#sttdieutri").val();
                var sovaovien = $("#sovaovien").val();
                var hotenbenhnhan = $("#hoten").val();
                var tuoi = $("#tuoi").val();
                var gioitinh = $("#gioitinh").val();
                if (gioitinh.toString() == "true") {
                    gioitinh = "Nam";
                } else {
                    gioitinh = "Nữ";
                }
                if (madv != "") {
                    var arr = [madv, sophieu, makhambenh, dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, sovaovien, hotenbenhnhan, tuoi, gioitinh];
                    var url = "inphieuchungnhanttpt?url=" + convertArray(arr);
                    $(location).attr('href', url);
                } else {
                    jAlert("Vui lòng chọn dịch vụ để kết quả", 'Cảnh báo');
                }
            });
            // KGG thêm in phiếu chứng nhận thủ thuật
            $("#ingiaycamdoan").click(function (evt) {
                if ($("#hoten").val() != "") {
                    var mabenhnhan = $("#mabenhnhan").val();
                    var sophieuthanhtoan = $("#sophieuthanhtoan").val();
                    var arr = [mabenhnhan, sophieuthanhtoan];
                    if('<%= ThamSoManager.instance(session).getThamSoString("960551","0")%>' != '0'){
                        var url = "ingiaycamdoan?url=" + convertArray(arr);
                    } else {
                        var url = "ingiaycamdoan?url=" + convertArray(arr)+"&viewPDF=1";
                    }
                    $(location).attr('href', url);
                }
            });
            // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT
            $("#phongban").change(function (evt) {
                var url = "layphongbenh_theokhoaxn?khoa=" + $("#phongban").val() + "&dvtt=${Sess_DVTT}";
                $.ajax({
                    url: url
                }).done(function (data) {
                    if (data) {
                        $("#phongbenh").empty();
                        $.each(data, function (i) {
                            $("<option value='" + data[i].MA_PHONG_BENH + "'>" + data[i].TEN_PHONG_BENH + "</option>").appendTo("#phongbenh");
                        });
                    }
                });
                $("#phongbenh").val(-1);
                reload_grid();
            });
            $("#phongbenh").change(function (evt) {
                reload_grid();
            });
            $("#doituong").change(function (evt) {
                reload_grid();
            });
            // End ĐắkLắk
            $("#daxetnghiem").change(function (evt) {
                reload_grid();
            });
            $("#ngaythuchien").change(function (evt) {
                reload_grid();
            });
            $("#luuMayTTPT").click(function (evt) {
                var sophieu = $("#sophieu").val();
                var noitru = $("#noitru").val();
                var madv = $("#madv").val();
                if (sophieu != "" && madv != "" && (sovaovien != "" || sovaovien_noi != "")) {
                    $.post("luu-may-ttpt", {
                        sophieu: sophieu,
                        madv: madv,
                        noitru: noitru,
                        sovaovien: sovaovien,
                        sovaovien_noi: sovaovien_noi,
                        sovaovien_dt_noi: sovaovien_dt_noi,
                        stt_may_ttpt: ($("#sttMayTTPT").val())
                    }).done(function (data) {
                        if (data == "1") {
                            jAlert("Cập nhật thành công!", "Thông báo");
                        } else {
                            jAlert("Vui lòng kiểm tra lại thông tin!", "Thông báo");
                        }
                    }).fail(function () {
                        jAlert("Vui lòng thử lại!", "Thông báo");
                    });
                }
            });
            reload_grid();
            $('.dropdown-menu p').click(function () {
                var attrId = $(this).attr('data-id');
                if($(this).attr('data-id') == 'capnhatekip') {
                    return opencmuekipt();
                }
                if(attrId == 'capnhatsinhthuong') {
                    return openModalSinhThuong();
                }
                $("#" + attrId).click();
            });

        });

        function reload_grid() {
            var ngay = convertStr_MysqlDate($("#ngaythuchien").val());
            var dvtt = "${Sess_DVTT}";
            var phong = "${Sess_Phong}";
            // ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT
            var phongban = $("#phongban").val();
            var phongbenh = $("#phongbenh").val();
            var doituong = $("#doituong").val();
            // End ĐắkLắk
            var daxetnghiem = $("#daxetnghiem").prop('checked');
            if (daxetnghiem == true) {
                daxn = 1;
            } else {
                daxn = 0;
            }
            var arr = [dvtt, ngay, phong, daxn, phongban, phongbenh, doituong];
            if ("${timkiem_cls}" == "1") {
                var tungay = convertStr_MysqlDate($("#tungay").val());
                arr = [dvtt, tungay, ngay, phong, daxn, phongban, phongbenh, doituong];
            }
            var url = 'ttpt_ds_benhnhan_cothamso?url=' + convertArray(arr);
            $("#list_benhnhan").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');

        };

        function hienthi_them_cls(url) {
            $.ajax({
                url: url
            }).done(function (data) {
                if (data.length > 0) {
                    $('#trieuchungls').val(data[0].TRIEUCHUNGLS);
                    $('#benhtheobs').val(data[0].TEN_BENH_THEOBS);
                    $('#ngaythuchien_cls').val(data[0].NGAY_THUC_HIEN);
                    $('#ngaythuchien_cls').val((data[0].NGAY_TRA_KETQUA == null ? "${ngayhientai}" : data[0].NGAY_TRA_KETQUA));
                    $('#giothuchien_cls').val((data[0].GIO_TRA_KETQUA == null ? "${giohientai}" : data[0].GIO_TRA_KETQUA));
                    if (data[0].TT_THANHTOAN == "0")
                        $('#tt_thanhtoan').val("Chưa thanh toán");
                    else  $('#tt_thanhtoan').val("Đã thanh toán");
                } else {
                    $('#trieuchungls').val("");
                    $('#benhtheobs').val("");
                    //$('#ngaythuchien_cls').val("");
                    $('#ngaythuchien_cls').val("${ngayhientai}");
                    $('#giothuchien_cls').val("${giohientai}");
                    $('#tt_thanhtoan').val("");
                }
            });
        };

        function kiemtra_tt_cls(url) {
            var x = 0;
            $.ajax({
                url: url,
                async: false
            }).done(function (data) {
                if (data == "2")
                    x = 1;
                else x = 0;
            });
            return x;
        };

    </script>

</head>
<body>
<div id="panel_all">
    <%@include file="../../../resources/Theme/include_pages/menu.jsp" %>
    <div id="panelwrap">
        <%@include file="UploadfileTTPT.jsp" %>
        <div class="center_content">
            <form id="form1" name="form1" method="post" action="">
                <table width="990">
                    <tr>
                        <td width="302"  valign="top">
                            <table width="302">
                                <tr class="hpg_tmp">
                                    <td width="60px"><span style="width:60px; display:inline-block;">Từ Ngày </span>
                                    </td>
                                    <td><input type="text" name="tungay" id="tungay"/></td>
                                </tr>
                                <tr>
                                    <td width="60px"><span style="width:60px; display:inline-block;"><span
                                            class="hpg_tmp">Đến </span>Ngày  </span>

                                    </td>
                                    <td>
                                        <input type="text" name="ngaythuchien" id="ngaythuchien" class="form-control form-control-sm"/>

                                    </td>
                                </tr>
                                <!-- ĐắkLắk (An Giang, Cà Mau yêu cầu) - Ninh 09/12/2016: lọc danh sách theo khoa, phòng chỉ định, đối tượng BHYT -->
                                <tr class="dlk_tmp ">
                                    <td>Khoa</td>
                                    <td><select name="phongban" id="phongban" class="form-control form-control-sm mt-1">
                                        <c:forEach var="i" items="${phongban}">
                                            <option value="${i.ma_phongban}">${i.ten_phongban}</option>
                                        </c:forEach>
                                    </select>
                                    </td>
                                </tr>
                                <tr class="dlk_tmp">
                                    <td>Phòng</td>
                                    <td><select name="phongbenh" id="phongbenh" class="form-control form-control-sm  mt-1">
                                        <c:forEach var="i" items="${phongbenh}">
                                            <option value="${i.ma_phong_benh}">${i.ten_phong_benh}</option>
                                        </c:forEach>
                                    </select></td>
                                </tr>
                                <tr class="dlk_tmp">
                                    <td>Đối tượng</td>
                                    <td><select name="doituong" id="doituong" class="form-control form-control-sm  mt-1">
                                        <option value="-1">--Tất cả--</option>
                                        <option value="1">Có BHYT</option>
                                        <option value="0">Không BHYT</option>
                                    </select></td>
                                </tr>
                                <!-- End ĐắkLắk -->
                                <tr>
                                    <td colspan="2"><label class="form-label mt-1"><input type="checkbox" name="daxetnghiem" id="daxetnghiem">
                                        Đã thực hiện</label></td>
                                </tr>
                                <tr>
                                    <td colspan="2">
                                        <button class="btn btn-success  mt-1 mb-1 form-control" id="lammoi" type="button">
                                            <i class="fa fa-refresh"></i> Làm mới
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="2">
                                        <table id="list_benhnhan"></table>
                                    </td>
                                </tr>
                                <tr id="ghichutrangthai">
                                    <td colspan="2" style="padding-top:10px">
                                        <label style="color:red;font-weight: normal;">BN cấp cứu</label>
                                        <label style="color:#bf00ff;margin-left:20px;font-weight: normal;">Viện
                                            phí</label>
                                        <label style="color:#00ff00;margin-left:20px;font-weight: normal;">BN < 6
                                            tuổi</label>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="2" style="padding-top:5px"><label
                                            style="color:#EE7600;margin-left:20px;font-weight: normal;">Bệnh nhân BHYT
                                        chưa đóng VP</label></td>
                                </tr>
                            </table>
                        </td>
                        <td width="676" valign="top">
                            <table width="674">
                                <tr>
                                    <td width="110">Họ tên
                                        <input name="sophieu" type="hidden" id="sophieu"/>
                                        <input name="makhambenh" type="hidden" id="makhambenh"/>
                                        <input name="noitru" type="hidden" id="noitru"/></td>
                                    <input name="madv" type="hidden" id="madv"/></td>
                        <input name="sovaovien" type="hidden" id="sovaovien"/></td>
                        <td width="552"><input name="hoten" type="text" disabled="disabled" class="width1"
                                               id="hoten" style="color: red; font-weight: bold"/>
                            Tuổi
                            <input name="tuoi" type="text" disabled="disabled" class="width3" id="tuoi"
                                   style="width:50px"/>
                            <select name="gioitinh" id="gioitinh" disabled="disabled">
                                <option value="true">Nam</option>
                                <option value="false">Nữ</option>
                            </select>
                            Mã y tế
                            <input name="mabenhnhan" type="text" disabled="disabled" class="width3"
                                   id="mabenhnhan" style="width:100px"/>
                        </td>
                    </tr>
                    <tr>
                        <td>Khoa</td>
                        <td><input name="tenkhoa" type="text" disabled="disabled" class="form-control form-control-sm"
                                   id="tenkhoa"/></td>
                    </tr>
                    <tr>
                        <td>Địa chỉ</td>
                        <td><input name="diachi" type="text" disabled="disabled" class="form-control form-control-sm"
                                   id="diachi"/></td>
                    </tr>
                    <tr>
                        <td>Số thẻ BHYT</td>
                        <td><input name="sothebhyt" type="text" disabled="disabled" class="form-control form-control-sm"
                                   id="sothebhyt"/></td>
                    </tr>
                    <tr class="hpg_hienthithem">
                        <td>Triệu chứng</td>
                        <td>
                            <input name="trieuchungls" type="text" disabled="disabled" class="form-control form-control-sm"
                                   id="trieuchungls"/>
                        </td>
                    </tr>
                    <tr class="hpg_hienthithem">
                        <td>Bệnh tật</td>
                        <td>
                            <input name="benhtheobs" type="text" disabled="disabled" class="form-control form-control-sm"
                                   id="benhtheobs"/>
                        </td>
                    </tr>
                    <tr class="hpg_hienthithem">
                        <td>Ngày giờ thực hiện</td>
                        <td>
                            <div class="form-group row" style="margin: 0">

                                <div class="col-sm-4">
                                    <input name="ngaythuchien_cls" type="text" id="ngaythuchien_cls" size="10" class="form-control form-control-sm"
                                           data-inputmask="'alias': 'date'"/>
                                </div>
                                <div class="col-sm-4">
                                    <input name="giothuchien_cls" type="text" id="giothuchien_cls" size="10"  class="form-control form-control-sm  ml-1"
                                           data-inputmask="'alias': 'hh:mm:ss'"/>
                                </div>
                            </div>



                        </td>
                    </tr>
                    <tr class="hpg_hienthithem">
                        <td>
                            Thanh toán
                        </td>
                        <td>
                            <input name="tt_thanhtoan" type="text" disabled="disabled" class="form-control form-control-sm"
                                   id="tt_thanhtoan"/>
                        </td>
                    </tr>
                    <!-- ĐắkLắk (Cà Mau yêu cầu) - Ninh 09/12/2016: thêm thông tin chẩn đoán icd -->
                    <tr>
                        <td>Chẩn đoán</td>
                        <td><input name="_chandoan" type="text" disabled="disabled" class="form-control form-control-sm"
                                   id="_chandoan"/></td>
                    </tr>
                    <tr>
                        <td>Chọn máy</td>
                        <td>
                            <div class="form-group row" style="margin: 5px 0 0 0">
                                <div class="col-md-7">
                                    <select name="sttMayTTPT" id="sttMayTTPT" class="form-control form-control-sm mt-1">
                                        <option value="0" selected>--- Chọn máy TPTT ---</option>
                                        <c:forEach var="e" items="${dsMayTTPT}">
                                            <option value="${e.STT}">${e.TEN_MAY}</option>
                                        </c:forEach>
                                    </select>
                                </div>
                                <div class="col-md-5">
                                    <button class="btn btn-success ml-1" id="luuMayTTPT" type="button">
                                        <i class="fa fa-check"></i> Lưu máy thực hiện
                                    </button>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <!-- End ĐắkLắk -->
                    <tr>
                        <td>Ngày giờ chỉ định</td>
                        <td>
                            <div class="form-group row" style="margin: 5px 0 0 0">
                                <div class="col-md-4">
                                    <input name="ngaychidinh_cls" type="text" class="form-control form-control" disabled="disabled" id="ngaychidinh_cls" size="10"
                                           data-inputmask="'alias': 'date'"/>
                                </div>
                                <div class="col-md-4">
                                    <input name="giochidinh_cls" type="text"  class="form-control form-control  ml-1" disabled="disabled" id="giochidinh_cls" size="10"
                                           data-inputmask="'alias': 'hh:mm:ss'"/>
                                </div>

                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <p id="vnptmoney" style="color:#21409A;;font-weight: bold;"></p>
                            <p id="bidv" style="color:#219a5f;;font-weight: bold;margin-top: 10px"></p>
                            <p id="vietinbank" style="color:#216b9a;;font-weight: bold;margin-top: 10px"></p>
                        </td>
                    </tr>
                    <tr>
                        <td>Kết quả tổng thể</td>
                        <td><textarea name="ketqua" rows="2" class="form-control form-control" id="ketqua"></textarea></td>
                    </tr>
                    <tr>
                        <td colspan="2" align="center">
                            <div class="form-group row" style="margin: 5px 0 0 0">
                                <div class="col-md-8">
                                    <button class="btn btn-success ml-1" id="luufile" type="button">
                                        <i class="fa fa-cloud-upload"></i> Upload file
                                    </button>
                                    <button class="btn btn-success ml-1" id="luuthongtin" type="button">
                                        <i class="fa fa-check-circle"></i> Lưu thông tin
                                    </button>
                                    <button class="btn btn-info ml-1" id="inphieu" type="button">
                                        <i class="fa fa-print"></i> In phiếu
                                    </button>

                                </div>
                                <div class="col-md-4">
                                    <div class="input-group-prepend">
                                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <i class="fa fa-list"></i> Khác
                                        </button>
                                        <div class="dropdown-menu">
                                            <p class="dropdown-item" data-id="ingiaycamdoan"><i class="fa fa-print"></i>  In giấy cam đoan</p>
                                            <p class="dropdown-item" data-id="lamdungcu"> <i class="fa fa-flask"></i> Làm dụng cụ</p>
                                            <p class="dropdown-item" data-id="inphieugaymehoisuc" ><i class="fa fa-print"></i> Phiếu gây mê hồi sức</p>
                                            <p class="dropdown-item" data-id="bbduyetphauthuat"><i class="fa fa-print"></i> BB duyệt phẩu thuật</p>
                                            <p class="dropdown-item" data-id="lichmo"><i class="fa fa-calendar"></i> Lịch phẩu thuật</p>
                                            <p class="dropdown-item" data-id="capnhatekip"><i class="fa fa-address-card"></i> Cập nhật EKIP</p>
                                            <p class="dropdown-item" data-id="capnhatsinhthuong"><i class="fa fa-id-badge"></i> Cập nhật sinh thường</p>
                                        </div>
                                    </div>
                                </div>


                            </div>
                            <div style="display: none">
                                <input type="button" name="ingiaycamdoan" id="ingiaycamdoan"
                                       value="In giấy cam đoan" class="button_shadow" style="width: 130px"/>
                                <input name="sttbenhan" type="hidden" id="sttbenhan"/>
                                <input name="sttdotdieutri" type="hidden" id="sttdotdieutri"/>
                                <input name="sttdieutri" type="hidden" id="sttdieutri"/>
                                <input name="sophieuthanhtoan" type="hidden" id="sophieuthanhtoan"/>
                                <input name="nguoithuchien" type="hidden" id="nguoithuchien"/>
                                <!--VNPT VLG-->
                                <input type="button" name="lamdungcu" id="lamdungcu" value="Làm dụng cụ"
                                       class="button_shadow" style="width: 140px"/>
                                <input type="button" name="inphieugaymehoisuc" id="inphieugaymehoisuc"
                                       value="Phiếu gây mê hồi sức"
                                       class="button_shadow" style="width: 150px"/>
                                <input type="button" name="bbduyetphauthuat" id="bbduyetphauthuat" value="BB duyệt phẩu thuật" class="button_shadow" style="width: auto"/>
                                <input type="button" name="lichmo" id="lichmo" value="Lịch phẩu thuật"
                                       class="button_shadow" style="width: auto"/>
                                <input onclick="opencmuekipt()" type="button" id="cmuthemtienphauthuat"
                                       value="Cập nhật EKIP" class="button_shadow" style="width: 140px"/>
                            </div>

                            <!--CMU-->
                            <div class="form-group row" style="margin: 5px 0 0 0">
                                <div class="col-md-12">
                                    <button onclick="open_cmu_ppnt()" class="btn btn-default ml-1" id="cmuphanphoinguonthu" type="button">
                                        <i class="fa fa-money"></i> Phân phối nguồn thu
                                    </button>
                                    <button  onclick="open_cmu_ppnt_v2()" class="btn btn-success ml-1" id="cmuphanphoinguonthu_v2" type="button">
                                        <i class="fa fa-money"></i> Phân phối nguồn thu V2
                                    </button>
                                </div>
                            </div>
                            <!--CMU-->
                        </td>
                    </tr>
                    <tr style="display:none">
                        <td colspan="2">
                            <input type="button" name="bt_lichphauthuat" id="bt_lichphauthuat"
                                   value="Lập lịch phẫu thuật" class="button_shadow"
                                   style="width: 140px"/>
                            <input type="button" name="bt_khamtienme" id="bt_khamtienme"
                                   value="Khám tiền mê"
                                   class="button_shadow"
                                   style="width: 160px"/>
                            <input type="button" name="bt_inlichphauthuat" id="bt_inlichphauthuat"
                                   value="In lịch phẩu thuật"
                                   class="button_shadow"
                                   style="width: 160px"/>
                            <input type="button" name="bt_chuanbiphauthuat" id="bt_chuanbiphauthuat" value="Chuẩn bị phẫu thuật" class="button_shadow" style="width: 160px"/>
                            <input type="button" name="bt_kiemkeantoan" id="bt_kiemkeantoan"
                                   value="Kiểm kê an toàn phẩu thuật" class="button_shadow"
                                   style="width: 200px"/>
                            <input type="button" name="bt_sauphauthuat" id="bt_sauphauthuat" value="Biểu mẫu sau mổ" class="button_shadow" style="width: 160px"/>
                        </td>
                    </tr>

                    <tr>
                        <td colspan="2">
                            <div id="div_list_ttpt">
                                <table id="list_xn_bhyt"></table>
                            </div>
                        </td>
                    </tr>
                </table>
                </td>
                </tr>
                </table>
            </form>
        </div> <!--end of center_content-->
        <%@include file="../../../resources/Theme/include_pages/footer.jsp"%>
        <jsp:include page="Capnhatketqua_TTPT_AGG.jsp" />
        <jsp:include page="../camau/canlamsang/cmuphauthuat.jsp" />
        <jsp:include page="../camau/canlamsang/cmuekip_ppnt.jsp" />
        <jsp:include page="../camau/canlamsang/cmuekip_ppnt_v2.jsp" />
        <jsp:include page="../camau/formsinhthuong.jsp" />
        <jsp:include page="lichmo.jsp" />
        <div id="dalBBduyetphauthuat" style="display: none" title="Biên bản duyệt phẩu thuật">
            <form id="frmBBduyetphauthuat" name="frmBBduyetphauthuat" method="post" action="">
                <table width="100%" style="font-size: 13px">
                    <tr>
                        <td width="20%">Thị lực</td>
                        <td width="5%"><b>MP</b></td>
                        <td width="35%"><input name="thilucmatphai" type="text" id="thilucmatphai" style="width: 100%"/></td>
                        <td width="5%"><b>MT</b></td>
                        <td width="35%"><input name="thilucmattrai" type="text" id="thilucmattrai" style="width: 100%"/></td>
                    </tr>
                    <tr>
                        <td width="20%">Nhãn áp</td>
                        <td width="5%"><b>MP</b></td>
                        <td width="35%"><input name="nhanapmatphai" type="text" id="nhanapmatphai" style="width: 100%"/></td>
                        <td width="5%"><b>MT</b></td>
                        <td width="35%"><input name="nhanapmattrai" type="text" id="nhanapmattrai" style="width: 100%"/></td>
                    </tr>
                    <tr>
                        <td width="20%">Chỉ định phẩu thuật phối hợp</td>
                        <td width="80%" colspan="4"><input name="cdphauthuatphoihop" type="text" id="cdphauthuatphoihop" style="width: 100%"/></td>
                    </tr>
                    <tr>
                        <td width="20%">Phẩu thuật phối hợp</td>
                        <td width="80%" colspan="4"><input name="phauthuatphoihop" type="text" id="phauthuatphoihop" style="width: 100%"/></td>
                    </tr>
                    <tr>
                        <td width="20%">Phẩu thuật viên phụ</td>
                        <td width="80%" colspan="4"><input name="phauthuatvienphu" type="text" id="phauthuatvienphu" style="width: 100%"/></td>
                    </tr>
                    <tr>
                        <td width="20%">Theo dõi gây tê/gây mê</td>
                        <td width="80%" colspan="4"><input name="gayte_gayme" type="checkbox" id="gayte_gayme"/></td>
                    </tr>
                    <tr>
                        <td width="20%">Ý kiến trưởng khoa</td>
                        <td width="80%" colspan="4"><input name="ykientruongkhoa" type="text" id="ykientruongkhoa" style="width: 100%"/></td>
                    </tr>
                    <tr>
                        <td width="100%" colspan="5" align="center">
                            <input type="button" style="width: 80px" name="luu_bbduyet" id="luu_bbduyet" value="Lưu" class="button_shadow"/>
                            <%--<input type="button" style="width: 80px" name="sua_bbduyet" id="sua_bbduyet" value="Sửa" class="button_shadow"/>--%>
                            <input type="button" style="width: 80px" name="inphieu_bbduyet" id="inphieu_bbduyet" value="In phiếu" class="button_shadow"/>
                        </td>
                    </tr>
                </table>
            </form>
        </div>
        <%-- <%@include file="vlg_duyen_phieulamdungcu.jsp"%> --%>
        <jsp:include page="../tuyenhai/phauthuat/PhongPhauThuat_ChuanBi.jsp" />
        <jsp:include page="../tuyenhai/phauthuat/PhongPhauThuat_LichPT.jsp" />
        <jsp:include page="../tuyenhai/phauthuat/PhongPhauThuat_KiemKeAnToanPhauThuat.jsp" />
        <jsp:include page="../tuyenhai/phauthuat/PhongPhauthuat_TienMe.jsp" />
        <jsp:include page="../tuyenhai/phauthuat/PhongPhauThuat_BieuMauSauMo.jsp"/>
    </div>
</div>
</body>
</html>
<script>
    // VNPT VLG
    $("#lamdungcu").click(function (evt) {
        dialog_lamdungcu.dialog("open");
        //var ret = $("#list_xn_bhyt").jqGrid('getRowData', id);
        //$("#madv").val(ret.MA_DV);
        var sophieu = $("#sophieu").val();
        var noitru = $("#noitru").val();
        var sttbenhan = $("#sttbenhan").val();
        var sttdotdieutri = $("#sttdotdieutri").val();
        var sttdieutri = $("#sttdieutri").val();
        var makhambenh = $("#makhambenh").val();
        var madv = $("#madv").val();
        var dvtt = "${Sess_DVTT}";
        var arr = [sophieu, noitru, sttbenhan, sttdotdieutri, sttdieutri, makhambenh, madv, dvtt, "0", sovaovien, sovaovien_noi, sovaovien_dt_noi];
        var url3 = "pttt_select_ketqua_svv?url=" + convertArray(arr);
        $.getJSON(url3, function (result) {
            $.each(result, function (i, field) {
                $("#chandoan").val(field.CHANDOANSAUPTTT);
                $("#phuongphappttt").val(field.PHUONGPHAP_TT_PT);
                $("#phuongphapvocam").val(field.PHUONGPHAP_VOCAM);
                $("#bacsipttt").val(field.BACSI_PTTT);
                $("#bacsigayme").val(field.BACSI_GAYME);
                $("#catchisau7ngay").val(field.CATCHI_SAU7NGAY);
                $("#trinhtupttt").val(field.TRINHTU_TT_PT);
                //$("#ngaypttt").val(field.NGAYPTTT_1);
                //$("#giopttt").val(field.GIOPTTT_1);
                $("#giopttt").data('da-thuc-hien', field.DA_CHAN_DOAN == 1)
                if (field.GIOPTTT_1 == null || field.DA_CHAN_DOAN != 1) {
                    if(tatAutoTime == 1) {
                        var ngayHienTai = new Date();
                        var gioHienTai = addZero(ngayHienTai.getHours());
                        var phutHienTai = addZero(ngayHienTai.getMinutes());
                        var giayHienTai = addZero(ngayHienTai.getSeconds());
                        $('#giopttt').val(gioHienTai + ":" + phutHienTai + ":" + giayHienTai);
                    } else
                        showtime_giopttt();
                } else {
                    stopGioPtttTimer();
                    $("#giopttt").val(field.GIOPTTT_1);
                }
                if (field.TAIBIEN == "1") {
                    $("#cb_gaymehoisuc").prop("checked", true);
                    $("#cb_nhiemkhuan").prop("checked", false);
                    $("#cb_khac").prop("checked", false);
                } else if (field.TAIBIEN == "2") {
                    $("#cb_nhiemkhuan").prop("checked", true);
                    $("#cb_gaymehoisuc").prop("checked", false);
                    $("#cb_khac").prop("checked", false);
                } else if (field.TAIBIEN == "3") {
                    $("#cb_khac").prop("checked", true);
                    $("#cb_nhiemkhuan").prop("checked", false);
                    $("#cb_gaymehoisuc").prop("checked", false);
                } else {
                    $("#cb_khac").prop("checked", false);
                    $("#cb_nhiemkhuan").prop("checked", false);
                    $("#cb_gaymehoisuc").prop("checked", false);
                }

                if (field.TUVONG == "1") {
                    $("#cb_trenban").prop("checked", true);
                    $("#cb_trong24gio").prop("checked", false);
                } else if (field.TUVONG == "2") {
                    $("#cb_trong24gio").prop("checked", true);
                    $("#cb_trenban").prop("checked", false);
                } else {
                    $("#cb_trong24gio").prop("checked", false);
                    $("#cb_trenban").prop("checked", false);
                }
            });
        });
        var arr = [sophieu, dvtt, madv, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
        var url = 'pttt_danhsach_hinhanh?url=' + convertArray(arr);
        $("#list_hinhanhttpt").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');

    });
</script>
