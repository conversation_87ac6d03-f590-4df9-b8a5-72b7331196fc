create or replace FUNCTION "NOITRU_KT_CLS" (
    p_dvtt           IN               VARCHAR2,
    p_sovaovien      IN               NUMBER,
    p_sovaovien_dt   IN               NUMBER,
    p_ngayra         VARCHAR2 DEFAULT NULL,
    p_stt_dieu_tri   VARCHAR2 DEFAULT NULL
) RETURN SYS_REFCURSOR IS

    cur                SYS_REFCURSOR;
    v_ngayvao          TIMESTAMP;
    v_ngayvao_text     VARCHAR2(255);
    v_ngayra           TIMESTAMP := TO_DATE(p_ngayra, 'dd/mm/yyyy hh24:mi:ss');
    v_ylenhcongkham    DATE;
    v_bant             NUMBER := 0;
    v_sovaovienngoai   NUMBER;
    v_stt_dotdieutri   NUMBER;
    v_mabenhnhan       NUMBER;
    v_tungay           DATE;
    v_denngay          DATE;
    p_stt_benhan       VARCHAR2(255);
    p_stt_dotdieutri   VARCHAR2(255);
    v_sothebhyt        VARCHAR2(255);
    v_thamso_960592    NUMBER(6) := cmu_tsdv(p_dvtt, 960592, 0);
    v_thamso_960618    NUMBER(6) := cmu_tsdv(p_dvtt, 960618, 0);
    v_thamso_960619    NUMBER(6) := cmu_tsdv(p_dvtt, 960619, 0);
BEGIN
BEGIN
SELECT
    ngayvao,
    TO_CHAR(ngayvao, 'dd/mm/yyyy hh24:mi:ss'),
    stt_dotdieutri,
    mabenhnhan
INTO
    v_ngayvao,
    v_ngayvao_text,
    v_stt_dotdieutri,
    v_mabenhnhan
FROM
    noitru_dotdieutri
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND sovaovien_dt = p_sovaovien_dt
  AND sobaohiemyte IS NOT NULL;

EXCEPTION
        WHEN no_data_found THEN
            NULL;
END;

    IF p_dvtt = '96175' THEN
UPDATE noitru_dieutri
SET
    ngaygiolap_tdt = ngaygiolap,
    ngaygiolap_pcs = ngaygiolap
WHERE
    dvtt = 96175
  AND sovaovien = p_sovaovien
  AND ( ngaygiolap_tdt IS NULL
    OR ngaygiolap_pcs IS NULL );

END IF;

SELECT
    bant,
    nvl(sovaovien_ngt, 0)
INTO
    v_bant,
    v_sovaovienngoai
FROM
    noitru_benhan
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien;

IF v_bant = 1 THEN
SELECT
    MIN(cd.ngay_chi_dinh)
INTO v_ylenhcongkham
FROM
    noitru_cd_dichvu      cd
        JOIN noitru_cd_dichvu_ct   cdct ON cd.dvtt = cdct.dvtt
        AND cd.sovaovien = cdct.sovaovien
        AND cd.so_phieu_dichvu = cdct.so_phieu_dichvu
        AND cdct.dvtt = p_dvtt
        AND cdct.sovaovien = p_sovaovien
        AND cdct.sovaovien_dt = p_sovaovien_dt
WHERE
    cd.dvtt = p_dvtt
  AND cd.sovaovien = p_sovaovien
  AND cd.sovaovien_dt = p_sovaovien_dt
  AND ma_loai_dichvu = 'E';

ELSE
        IF v_sovaovienngoai != 0 AND v_stt_dotdieutri = 1 THEN
SELECT
    ngay_gio_kham
INTO v_ylenhcongkham
FROM
    his_manager.kb_kham_benh
WHERE
    dvtt = p_dvtt
  AND sovaovien = v_sovaovienngoai;

SELECT
    thoi_gian_tiep_nhan
INTO v_ngayvao
FROM
    his_manager.kb_tiep_nhan
WHERE
    dvtt = p_dvtt
  AND sovaovien = v_sovaovienngoai;

ELSE
            v_ylenhcongkham := v_ngayvao;
END IF;
END IF;

SELECT
    ngaybatdau_thebhyt,
    ngayhethan_thebhyt,
    stt_benhan,
    stt_dotdieutri,
    sobaohiemyte
INTO
    v_tungay,
    v_denngay,
    p_stt_benhan,
    p_stt_dotdieutri,
    v_sothebhyt
FROM
    noitru_dotdieutri
WHERE
    dvtt = p_dvtt
  AND sovaovien = p_sovaovien
  AND sovaovien_dt = p_sovaovien_dt;

DELETE FROM cmu_empty_dien_bien_y_lenh_tmp
WHERE
    dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan;

IF p_stt_dieu_tri IS NOT NULL and v_thamso_960619 = 1 THEN
        INSERT INTO cmu_empty_dien_bien_y_lenh_tmp (
            stt_benhan,
            dvtt,
            stt_dieutri
        )
SELECT
    p_stt_benhan   AS stt_benhan,
    p_dvtt         AS dvtt,
    TRIM(regexp_substr(p_stt_dieu_tri, '[^,]+', 1, level)) AS stt_dieutri
FROM
    dual
    CONNECT BY
                level <= regexp_count(p_stt_dieu_tri, ',') + 1;

END IF;

OPEN cur FOR SELECT
                    cls.*--, TEN_NGUOI_THUC_HIEN
                FROM
                    (
                        SELECT
                            '-CDHA' loai,
                            ct.stt_dieutri,
                            ha.ten_cdha ten_cls,
                            ct.mo_ta,
                            ct.nguoi_thuc_hien,
                            TO_CHAR(ct.ngay_chi_dinh_ct, 'dd/mm/yyyy hh24:mi') ngay_chi_dinh_ct,
                            TO_CHAR(ct.ngay_thuc_hien, 'dd/mm/yyyy hh24:mi') ngay_thuc_hien_ct,
                            nv.ten_nhanvien_cd
                            || ' ('
                            || chungchi_hanhnghe
                            || ')' ten_nguoi_thuc_hien,
                            CASE
                                    WHEN ct.da_chan_doan = 0
                                         OR ct.mo_ta IS NULL
                                         OR ct.nguoi_thuc_hien IS NULL THEN
                                        'Chưa thực hiện CĐHA; '
                                    WHEN nv.chungchi_hanhnghe IS NULL THEN
                                        'Người thực hiện chưa có CCHN; '
                                    WHEN ct.ngay_thuc_hien < ct.ngay_chi_dinh_ct THEN
                                        'Ngày thực hiện trước ngày chỉ định; '
                                END
                            ||
                                CASE
                                    WHEN ct.ngay_chi_dinh_ct < v_ngayvao THEN
                                        'Ngày chỉ định trước ngày vào viện: '
                                        || v_ngayvao_text
                                        || '; '
                                    WHEN p_ngayra IS NOT NULL
                                         AND ct.ngay_chi_dinh_ct >= v_ngayra THEN
                                        'Ngày chỉ định sau ngày ra viện; '
                                END
                            ||
                                CASE
                                    WHEN v_ylenhcongkham IS NOT NULL
                                         AND trunc(ngay_chi_dinh_ct, 'MI') < trunc(v_ylenhcongkham, 'MI') THEN
                                        'Ngày chỉ định trước y lệnh công khám: ' || TO_CHAR(v_ylenhcongkham, 'DD/MM/YYYY HH24:MI'
                                        )
                                END
                            ||
                                CASE
                                    WHEN ct.ngay_thuc_hien IS NULL THEN
                                        'Ngày kết quả rỗng; '
                                END
                            ||
                                CASE
                                    WHEN ct.ngay_th_yl IS NULL THEN
                                        'Ngày thực hiện y lệnh rỗng; '
                                END
                            ||
                                CASE
                                    WHEN ct.ngay_th_yl <= v_ngayvao THEN
                                        'Ngày thực hiện trước hoặc bằng ngày vào viện '
                                        || v_ngayvao_text
                                        || ', ngày thực hiện: '
                                        || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                                        || ' ; '
                                END
                            ||
                                CASE
                                    WHEN ct.ngay_thuc_hien <= v_ngayvao THEN
                                        'Ngày kết quả trước hoặc bằng ngày vào viện '
                                        || v_ngayvao_text
                                        || ', ngày kết quả: '
                                        || TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI')
                                        || ' ; '
                                END
                            ||
                                CASE
                                    WHEN p_ngayra IS NOT NULL
                                         AND ct.ngay_th_yl >= v_ngayra THEN
                                        'Ngày thực hiện lớn hơn hoặc bằng ngày ra viện '
                                        || p_ngayra
                                        || ', ngày thực hiện: '
                                        || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                                        || ' ; '
                                END
                            ||
                                CASE
                                    WHEN p_ngayra IS NOT NULL
                                         AND ct.ngay_thuc_hien >= v_ngayra THEN
                                        'Ngày kết quả lớn hơn hoặc bằng ngày ra viện '
                                        || p_ngayra
                                        || ', ngày kết quả: '
                                        || TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI')
                                        || ' ; '
                                END
                            ||
                                CASE
                                    WHEN trunc(ct.ngay_th_yl, 'MI') <= trunc(ct.ngay_chi_dinh_ct, 'MI') THEN
                                        'Ngày thực hiện nhỏ hơn hoặc bằng ngày chỉ định '
                                        || TO_CHAR(ct.ngay_chi_dinh_ct, 'DD/MM/YYYY HH24:MI')
                                        || ', ngày thực hiện: '
                                        || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                                        || ' ; '
                                END
                            ||
                                CASE
                                    WHEN ( CAST(ct.ngay_thuc_hien AS DATE) - CAST(ct.ngay_th_yl AS DATE) ) * 1440 < 5 THEN
                                        'Ngày thực hiện đến thời gian kết quả nhỏ hơn 5 phút '
                                        || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                                        || ', ngày kết quả: '
                                        || TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI')
                                        || ' ; '
                                END
                                || DECODE(v_thamso_960618, '1',
                                        CASE WHEN signkcb.KEYSIGN IS NULL THEN 'Phiếu chưa thực hiện ký số' || ' ; ' ELSE ' ; ' END, ' ; ')
                            ghi_chu
                        FROM
                            his_manager.noitru_cd_cdha_chi_tiet   ct
                            LEFT JOIN smartca_signed_kcb              signkcb ON ct.dvtt = signkcb.dvtt
                                                             AND ct.sovaovien = signkcb.sovaovien
                                                             AND signkcb.so_phieu_dv = ct.SO_PHIEU_CDHA
                                                             AND signkcb.status = 0
                            JOIN cls_cdha                              ha ON ct.dvtt = ha.dvtt
                                                AND ct.ma_cdha = ha.ma_cdha
                            LEFT JOIN his_fw.dm_nhanvien_cd                 nv ON ct.nguoi_thuc_hien = nv.ma_nhanvien
                        WHERE
                            ct.dvtt = p_dvtt
                            AND ct.sovaovien = p_sovaovien
                            AND ct.sovaovien_dt = p_sovaovien_dt
            --AND ha.koyeucau_kq = 0
                            AND ct.bhytkchi = 0
                            AND ( ct.da_chan_doan = 0
                                  OR ct.nguoi_thuc_hien IS NULL
                                  OR ct.mo_ta IS NULL
                                  OR ct.ngay_thuc_hien < ct.ngay_chi_dinh_ct
                                  OR ct.ngay_chi_dinh_ct < v_ngayvao
                                  OR ( p_ngayra IS NOT NULL
                                       AND ct.ngay_chi_dinh_ct >= v_ngayra )
                                  OR nv.chungchi_hanhnghe IS NULL
                                  OR ( v_ylenhcongkham IS NOT NULL
                                       AND trunc(ngay_chi_dinh_ct, 'MI') < trunc(v_ylenhcongkham, 'MI') )
                                  OR ( ( CAST(ct.ngay_thuc_hien AS DATE) - CAST(ct.ngay_th_yl AS DATE) ) * 1440 < 5 )
                                  OR trunc(ct.ngay_th_yl, 'MI') <= trunc(ct.ngay_chi_dinh_ct, 'MI')
                                  OR ct.ngay_th_yl IS NULL
                                  OR ct.ngay_thuc_hien IS NULL
                                  OR ct.ngay_thuc_hien <= v_ngayvao
                                  OR ct.ngay_th_yl <= v_ngayvao
                                  OR ( p_ngayra IS NOT NULL
                                       AND ct.ngay_th_yl >= v_ngayra )
                                  OR ( p_ngayra IS NOT NULL
                                       AND ct.ngay_thuc_hien >= v_ngayra ) )
                        UNION ALL
                        SELECT
                            '-DVKT',
                            ct.stt_dieutri,
                            dm.ten_dv,
                            ct.mo_ta,
                            ct.nguoi_thuc_hien,
                            TO_CHAR(ct.ngay_chi_dinh_ct, 'dd/mm/yyyy hh24:mi') ngay_chi_dinh_ct,
                            TO_CHAR(ct.ngay_gio_pttt, 'dd/mm/yyyy hh24:mi') ngay_thuc_hien_ct,
                            nv.ten_nhanvien_cd
                            || ' ('
                            || chungchi_hanhnghe
                            || ')' ten_nguoi_thuc_hien,
                            CASE
                                    WHEN ct.da_chan_doan = 0
                                         OR ct.ma_bs_pttp IS NULL THEN
                                        'Chưa thực hiện DVKT; '
                                    WHEN nv.chungchi_hanhnghe IS NULL THEN
                                        'Người thực hiện chưa có CCHN; '
                                    WHEN ct.ngay_gio_pttt < ct.ngay_chi_dinh_ct THEN
                                        'Ngày thực hiện trước ngày chỉ định; '
                                END
                            ||
                                CASE
                                    WHEN ct.ngay_chi_dinh_ct < v_ngayvao THEN
                                        'Ngày chỉ định trước ngày vào viện: '
                                        || v_ngayvao_text
                                        || '; '
                                    WHEN p_ngayra IS NOT NULL
                                         AND ct.ngay_chi_dinh_ct >= v_ngayra THEN
                                        'Ngày chỉ định sau ngày ra viện; '
                                END
                            ||
                                CASE
                                    WHEN v_ylenhcongkham IS NOT NULL
                                         AND ngay_chi_dinh_ct < v_ylenhcongkham THEN
                                        'Ngày chỉ định trước y lệnh công khám: ' || TO_CHAR(v_ylenhcongkham, 'DD/MM/YYYY HH24:MI'
                                        )
                                END
                            ||
                                CASE
                                    WHEN ct.ngay_gio_pttt_kt IS NULL
                                         AND dm.loai_dv NOT IN (
                                        'THO_OXY',
                                        'MAU'
                                    ) THEN
                                        'Ngày kết quả rỗng; '
                                END
                            ||
                                CASE
                                    WHEN ct.ngay_gio_pttt IS NULL THEN
                                        'Ngày thực hiện y lệnh rỗng; '
                                END
                            ||
                                CASE
                                    WHEN ct.ngay_gio_pttt <= v_ngayvao THEN
                                        'Ngày thực hiện trước hoặc bằng ngày vào viện '
                                        || v_ngayvao_text
                                        || ', ngày thực hiện: '
                                        || TO_CHAR(ct.ngay_gio_pttt, 'DD/MM/YYYY HH24:MI')
                                        || ' ; '
                                END
                            ||
                                CASE
                                    WHEN ct.ngay_gio_pttt_kt <= v_ngayvao THEN
                                        'Ngày kết quả trước hoặc bằng ngày vào viện '
                                        || v_ngayvao_text
                                        || ', ngày kết quả: '
                                        || TO_CHAR(ct.ngay_gio_pttt_kt, 'DD/MM/YYYY HH24:MI')
                                        || ' ; '
                                END
                            ||
                                CASE
                                    WHEN p_ngayra IS NOT NULL
                                         AND ct.ngay_gio_pttt >= v_ngayra THEN
                                        'Ngày thực hiện lớn hơn hoặc bằng ngày ra viện '
                                        || p_ngayra
                                        || ', ngày thực hiện: '
                                        || TO_CHAR(ct.ngay_gio_pttt, 'DD/MM/YYYY HH24:MI')
                                        || ' ; '
                                END
                            ||
                                CASE
                                    WHEN p_ngayra IS NOT NULL
                                         AND ct.ngay_gio_pttt_kt >= v_ngayra THEN
                                        'Ngày kết quả lớn hơn hoặc bằng ngày ra viện '
                                        || p_ngayra
                                        || ', ngày kết quả: '
                                        || TO_CHAR(ct.ngay_gio_pttt_kt, 'DD/MM/YYYY HH24:MI')
                                        || ' ; '
                                END
                            ||
                                CASE
                                    WHEN trunc(ct.ngay_gio_pttt, 'MI') <= trunc(ct.ngay_chi_dinh_ct, 'MI') THEN
                                        'Ngày thực hiện nhỏ hơn hoặc bằng ngày chỉ định '
                                        || TO_CHAR(ct.ngay_chi_dinh_ct, 'DD/MM/YYYY HH24:MI')
                                        || ', ngày thực hiện: '
                                        || TO_CHAR(ct.ngay_gio_pttt, 'DD/MM/YYYY HH24:MI')
                                        || ' ; '
                                END
                            ||
                                CASE
                                    WHEN ( CAST(ct.ngay_gio_pttt_kt AS DATE) - CAST(ct.ngay_gio_pttt AS DATE) ) * 1440 < 5
                                         AND dm.loai_dv NOT IN (
                                        'THO_OXY',
                                        'MAU'
                                    ) THEN
                                        'Ngày thực hiện đến thời gian kết quả nhỏ hơn 5 phút '
                                        || TO_CHAR(ct.ngay_gio_pttt, 'DD/MM/YYYY HH24:MI')
                                        || ', ngày kết quả: '
                                        || TO_CHAR(ct.ngay_gio_pttt_kt, 'DD/MM/YYYY HH24:MI')
                                        || ' ; '
                                END
                            ||
                                CASE
                                    WHEN dm.loai_dv IN (
                                        'TT',
                                        'PT',
                                        'VLTL'
                                    )
                                         AND nvl(ct.pp_vo_cam, '0') NOT IN (
                                        1,
                                        2,
                                        3,
                                        4
                                    ) THEN
                                        'Phương pháp vô cảm trống; '
                                END
                            ghi_chu
                        FROM
                            his_manager.noitru_cd_dichvu_ct   ct
                            JOIN his_manager.dm_dich_vu_kham       dm ON dm.dvtt = ct.dvtt
                                                                   AND dm.ma_dv = ct.ma_dv
                            LEFT JOIN his_fw.dm_nhanvien_cd             nv ON ct.ma_bs_pttp = nv.ma_nhanvien
                        WHERE
                            dm.loai_dv IN (
                                'TT',
                                'PT',
                                'VLTL',
                                'THO_OXY',
                                'MAU'
                            )
                            AND dm.dvtt = p_dvtt
                            AND ct.dvtt = p_dvtt
                            AND ct.sovaovien = p_sovaovien
                            AND ct.sovaovien_dt = p_sovaovien_dt
            --AND dm.koyeucau_kq = 0
                            AND ct.bhytkchi = 0
                            AND ( ct.da_chan_doan = 0
                                  OR ct.ma_bs_pttp IS NULL
                                  OR ct.ngay_gio_pttt < ct.ngay_chi_dinh_ct
                                  OR ct.ngay_chi_dinh_ct < v_ngayvao
                                  OR ( p_ngayra IS NOT NULL
                                       AND ct.ngay_chi_dinh_ct >= v_ngayra )
                                  OR nv.chungchi_hanhnghe IS NULL
                                  OR ( v_ylenhcongkham IS NOT NULL
                                       AND trunc(ngay_chi_dinh_ct, 'MI') < trunc(v_ylenhcongkham, 'MI') )
                                  OR ( ( CAST(ct.ngay_gio_pttt_kt AS DATE) - CAST(ct.ngay_gio_pttt AS DATE) ) * 1440 < 5
                                       AND dm.loai_dv NOT IN (
                                'THO_OXY',
                                'MAU'
                            ) )
                                  OR trunc(ct.ngay_gio_pttt, 'MI') <= trunc(ct.ngay_chi_dinh_ct, 'MI')
                                  OR ct.ngay_gio_pttt IS NULL
                                  OR ( ct.ngay_gio_pttt_kt IS NULL
                                       AND dm.loai_dv NOT IN (
                                'THO_OXY',
                                'MAU'
                            ) )
                                  OR ct.ngay_gio_pttt_kt <= v_ngayvao
                                  OR ct.ngay_gio_pttt <= v_ngayvao
                                  OR ( p_ngayra IS NOT NULL
                                       AND ct.ngay_gio_pttt >= v_ngayra )
                                  OR ( p_ngayra IS NOT NULL
                                       AND ct.ngay_gio_pttt_kt >= v_ngayra )
                                  OR ( dm.loai_dv IN (
                                'TT',
                                'PT',
                                'VLTL'
                            )
                                       AND nvl(ct.pp_vo_cam, '0') NOT IN (
                                1,
                                2,
                                3,
                                4
                            ) ) )
                        UNION ALL
                        SELECT
                            '-XN',
                            ct.stt_dieutri,
                            xn.ten_xetnghiem,
                            ct.ket_qua,
                            ct.nguoi_thuc_hien,
                            TO_CHAR(ct.ngay_chi_dinh_ct, 'dd/mm/yyyy hh24:mi') ngay_chi_dinh_ct,
                            TO_CHAR(ct.ngay_thuc_hien, 'dd/mm/yyyy hh24:mi') ngay_thuc_hien_ct,
                            nv.ten_nhanvien_cd
                            || ' ('
                            || chungchi_hanhnghe
                            || ')' ten_nguoi_thuc_hien,
                            CASE
                                    WHEN ct.da_xet_nghiem = 0
                                         OR ( ct.ket_qua IS NULL
                                              AND nvl(xn.co_dulieucon, 0) = 0 )
                                         OR ct.nguoi_thuc_hien IS NULL
                                         OR TO_CHAR(ct.ngay_thuc_hien, 'hh24:mi:ss') = '00:00:00' THEN
                                        'Chưa thực hiện XN; '
                                    WHEN nv.chungchi_hanhnghe IS NULL THEN
                                        'Người thực hiện chưa có CCHN; '
                                    WHEN ct.ngay_thuc_hien < ct.ngay_chi_dinh_ct THEN
                                        'Ngày thực hiện trước ngày chỉ định; '
                                END
                            ||
                                CASE
                                    WHEN ct.ngay_chi_dinh_ct < v_ngayvao THEN
                                        'Ngày chỉ định trước ngày vào viện: '
                                        || v_ngayvao_text
                                        || '; '
                                    WHEN p_ngayra IS NOT NULL
                                         AND ct.ngay_chi_dinh_ct >= v_ngayra THEN
                                        'Ngày chỉ định sau ngày ra viện; '
                                END
                            || ct.so_phieu_xn
                            || ';'
                            ||
                                CASE
                                    WHEN v_ylenhcongkham IS NOT NULL
                                         AND trunc(ngay_chi_dinh_ct, 'MI') < trunc(v_ylenhcongkham, 'MI') THEN
                                        'Ngày chỉ định trước y lệnh công khám: ' || TO_CHAR(v_ylenhcongkham, 'DD/MM/YYYY HH24:MI'
                                        )
                                END
                            ||
                                CASE
                                    WHEN ct.ngay_thuc_hien IS NULL THEN
                                        'Ngày kết quả rỗng; '
                                END
                            ||
                                CASE
                                    WHEN ct.ngay_th_yl IS NULL THEN
                                        'Ngày thực hiện y lệnh rỗng; '
                                END
                            ||
                                CASE
                                    WHEN ct.ngay_th_yl <= v_ngayvao THEN
                                        'Ngày thực hiện trước hoặc bằng ngày vào viện '
                                        || v_ngayvao_text
                                        || ', ngày thực hiện: '
                                        || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                                        || ' ; '
                                END
                            ||
                                CASE
                                    WHEN ct.ngay_thuc_hien <= v_ngayvao THEN
                                        'Ngày kết quả trước hoặc bằng ngày vào viện '
                                        || v_ngayvao_text
                                        || ', ngày kết quả: '
                                        || TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI')
                                        || ' ; '
                                END
                            ||
                                CASE
                                    WHEN p_ngayra IS NOT NULL
                                         AND ct.ngay_th_yl >= v_ngayra THEN
                                        'Ngày thực hiện lớn hơn hoặc bằng ngày ra viện '
                                        || p_ngayra
                                        || ', ngày thực hiện: '
                                        || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                                        || ' ; '
                                END
                            ||
                                CASE
                                    WHEN p_ngayra IS NOT NULL
                                         AND ct.ngay_thuc_hien >= v_ngayra THEN
                                        'Ngày kết quả lớn hơn hoặc bằng ngày ra viện '
                                        || p_ngayra
                                        || ', ngày kết quả: '
                                        || TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI')
                                        || ' ; '
                                END
                            ||
                                CASE
                                    WHEN trunc(ct.ngay_th_yl, 'MI') <= trunc(ct.ngay_chi_dinh_ct, 'MI') THEN
                                        'Ngày thực hiện nhỏ hơn hoặc bằng ngày chỉ định '
                                        || TO_CHAR(ct.ngay_chi_dinh_ct, 'DD/MM/YYYY HH24:MI')
                                        || ', ngày thực hiện: '
                                        || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                                        || ' ; '
                                END
                            ||
                                CASE
                                    WHEN ( CAST(ct.ngay_thuc_hien AS DATE) - CAST(ct.ngay_th_yl AS DATE) ) * 1440 < 5 THEN
                                        'Ngày thực hiện đến thời gian kết quả nhỏ hơn 5 phút '
                                        || TO_CHAR(ct.ngay_th_yl, 'DD/MM/YYYY HH24:MI')
                                        || ', ngày kết quả: '
                                        || TO_CHAR(ct.ngay_thuc_hien, 'DD/MM/YYYY HH24:MI')
                                        || ' ; '
                                END
                                || DECODE(v_thamso_960618, '1',
                                        CASE WHEN signkcb.KEYSIGN IS NULL THEN 'Phiếu chưa thực hiện ký số' || ' ; ' ELSE ' ; ' END, ' ; ')
                            ghi_chu
                        FROM
                            his_manager.noitru_cd_xet_nghiem_ct   ct
                            LEFT JOIN smartca_signed_kcb              signkcb ON ct.dvtt = signkcb.dvtt
                                                             AND ct.sovaovien = signkcb.sovaovien
                                                             AND signkcb.so_phieu_dv = ct.SO_PHIEU_XN
                                                             AND signkcb.status = 0
                            JOIN cls_xetnghiem                         xn ON ct.dvtt = xn.dvtt
                                                     AND ct.ma_xet_nghiem = xn.ma_xetnghiem
                            LEFT JOIN his_fw.dm_nhanvien_cd                 nv ON ct.nguoi_thuc_hien = nv.ma_nhanvien
                        WHERE
                            ct.dvtt = p_dvtt
                            AND ct.sovaovien = p_sovaovien
                            AND ct.sovaovien_dt = p_sovaovien_dt
                            AND xn.cap_xn = 1
                            AND ct.bhytkchi = 0
            --and xn.koyeucau_kq = 0
                            AND nvl(ct.ket_qua, ' ') != 'CHỜ KẾT QUẢ'
                            AND ( ct.da_xet_nghiem = 0
                                  OR ct.nguoi_thuc_hien IS NULL
                                  OR ( ct.ket_qua IS NULL
                                       AND nvl(xn.co_dulieucon, 0) = 0
                                       AND ct.ma_xet_nghiem NOT IN (
                                SELECT DISTINCT
                                    id_chisocha
                                FROM
                                    his_manager.noitru_cd_xet_nghiem_ct
                                WHERE
                                    dvtt = p_dvtt
                                    AND sovaovien = p_sovaovien
                                    AND sovaovien_dt = p_sovaovien_dt
                                    AND id_chisocha IS NOT NULL
                                    AND ket_qua IS NOT NULL
                            ) )
                                  OR ct.ngay_thuc_hien < ct.ngay_chi_dinh_ct
                                  OR ct.ngay_chi_dinh_ct < v_ngayvao
                                  OR ( p_ngayra IS NOT NULL
                                       AND ct.ngay_chi_dinh_ct >= v_ngayra )
                                  OR nv.chungchi_hanhnghe IS NULL
                                  OR ( v_ylenhcongkham IS NOT NULL
                                       AND trunc(ngay_chi_dinh_ct, 'MI') < trunc(v_ylenhcongkham, 'MI') )
                                  OR ( ( CAST(ct.ngay_thuc_hien AS DATE) - CAST(ct.ngay_th_yl AS DATE) ) * 1440 < 1 )
                                  OR trunc(ct.ngay_th_yl, 'MI') <= trunc(ct.ngay_chi_dinh_ct, 'MI')
                                  OR ct.ngay_th_yl IS NULL
                                  OR ct.ngay_thuc_hien IS NULL
                                  OR ct.ngay_thuc_hien <= v_ngayvao
                                  OR ct.ngay_th_yl <= v_ngayvao
                                  OR ( p_ngayra IS NOT NULL
                                       AND ct.ngay_th_yl >= v_ngayra )
                                  OR ( p_ngayra IS NOT NULL
                                       AND ct.ngay_thuc_hien >= v_ngayra ) )
                        UNION ALL
                        SELECT
                            '-GIORAVIEN',
                            '' stt_dieutri,
                            '' ten_xetnghiem,
                            '' ket_qua,
                            0 nguoi_thuc_hien,
                            TO_CHAR(v_ngayvao, 'dd/mm/yyyy hh24:mi') ngay_chi_dinh_ct,
                            p_ngayra ngay_thuc_hien_ct,
                            '' ten_nguoi_thuc_hien,
                            'Ngày vào viện lớn hơn ngày ra viện '
                        FROM
                            dual
                        WHERE
                            v_ngayvao > v_ngayra
                        UNION ALL
                        SELECT DISTINCT
                            '-Toa thuốc',
                            ct.stt_dieutri,
                            'Tờ điều trị',
                            '' ket_qua,
                            NULL nguoi_thuc_hien,
                            TO_CHAR(tt.ngaygioratoa, 'dd/mm/yyyy hh24:mi') ngay_chi_dinh_ct,
                            '' ngay_thuc_hien_ct,
                            nv.ten_nhanvien_cd
                            || ' ('
                            || chungchi_hanhnghe
                            || ')' ten_nguoi_thuc_hien,
                            CASE
                                WHEN ct.ngay_gio_ke_thucte > v_ngayra        THEN
                                    'Ngày y lệnh (Tờ điều trị -'
                                    || dt.stt_dieutri
                                    || ':'
                                    || TO_CHAR(ct.ngay_gio_ke_thucte, 'dd/mm/yyyy hh24:mi')
                                    || ' lớn hơn ngày ra viện : '
                                    || TO_CHAR(v_ngayra, 'dd/mm/yyyy hh24:mi')
                                    || ') '
                                    || ct.ten_vat_tu
                                WHEN ct.ngay_gio_ke_thucte < v_ylenhcongkham THEN
                                    'Ngày y lệnh (Tờ điều trị -'
                                    || dt.stt_dieutri
                                    || ':'
                                    || TO_CHAR(ct.ngay_gio_ke_thucte, 'dd/mm/yyyy hh24:mi')
                                    || ' trước y lệnh thời gian khám : '
                                    || TO_CHAR(v_ylenhcongkham, 'dd/mm/yyyy hh24:mi')
                                    || ') '
                                    || ct.ten_vat_tu
                            END ghi_chu
            --AND count(1) over (PARTITION BY ct.STT_DIEUTRI, ct.mavattu) <= 1
                        FROM
                            his_manager.noitru_ct_toa_thuoc   ct
                            JOIN noitru_toa_thuoc                  tt ON ct.dvtt = tt.dvtt
                                                        AND ct.id_dieutri = tt.id_dieutri
                                                        AND ct.ma_toa_thuoc = tt.ma_toa_thuoc
                                                        AND ct.stt_benhan = tt.stt_benhan
                                                        AND ct.stt_dotdieutri = tt.stt_dotdieutri
                                                        AND tt.dvtt = p_dvtt
                                                        AND tt.sovaovien = p_sovaovien
                                                        AND tt.sovaovien_dt = p_sovaovien_dt
                            JOIN noitru_dieutri                    dt ON tt.dvtt = dt.dvtt
                                                      AND tt.sovaovien = dt.sovaovien
                                                      AND tt.sovaovien_dt = dt.sovaovien_dt
                                                      AND tt.id_dieutri = dt.id_dieutri
                                                      AND dt.dvtt = p_dvtt
                                                      AND dt.sovaovien = p_sovaovien
                                                      AND dt.sovaovien_dt = p_sovaovien_dt
                            JOIN his_fw.dm_nhanvien_cd             nv ON dt.tdt_nguoilap = nv.ma_nhanvien
                        WHERE
                            ct.dvtt = p_dvtt
                            AND ct.sovaovien = p_sovaovien
                            AND ct.sovaovien_dt = p_sovaovien_dt
                            AND ct.so_luong_thuc_linh > 0
                            AND ( ( ( ct.maloaivattu LIKE 'TH%'
                                      OR ct.maloaivattu LIKE '%DICHTRUYEN%'
                                      OR ct.maloaivattu IN (
                                'DONGY_CP',
                                'DD',
                                'KS'
                            )
                                      OR ct.maloaivattu LIKE '%YHCT%' )
                                    AND nghiep_vu NOT IN (
                                'noitru_toathuoc',
                                'noitru_toaquaybanthuocbv',
                                'noitru_toamuangoai'
                            )
                                    AND nvl(ct.ghi_chu_ct_toa_thuoc, '0') NOT LIKE '%CD_%' )
                                  OR nghiep_vu IN (
                                'noitru_toathuoc'
                            ) )
                            AND ( ct.ngay_gio_ke_thucte > v_ngayra
                                  OR ct.ngay_gio_ke_thucte < v_ylenhcongkham )
                        UNION ALL
                        SELECT DISTINCT
                            '-Tờ điều trị',
                            dt.stt_dieutri,
                            'Tờ điều trị',
                            '' ket_qua,
                            NULL nguoi_thuc_hien,
                            TO_CHAR(dt.NGAYGIOLAP_TDT, 'dd/mm/yyyy hh24:mi') ngay_chi_dinh_ct,
                            '' ngay_thuc_hien_ct,
                            nv.ten_nhanvien_cd
                            || ' ('
                            || chungchi_hanhnghe
                            || ')' ten_nguoi_thuc_hien,
                            'Y lệnh hoặc diễn biến bệnh không được để trống' ghi_chu
                        FROM  noitru_dieutri dt
                            JOIN his_fw.dm_nhanvien_cd nv ON dt.tdt_nguoilap = nv.ma_nhanvien
                        WHERE
                            dt.dvtt = p_dvtt
                            AND dt.sovaovien = p_sovaovien
                            AND dt.sovaovien_dt = p_sovaovien_dt
                            AND dt.stt_dieutri IN (SELECT yl.STT_DIEUTRI FROM CMU_EMPTY_DIEN_BIEN_Y_LENH_TMP yl
                                                   WHERE yl.STT_BENHAN = p_stt_benhan AND yl.DVTT = p_dvtt)
                        UNION ALL
                        SELECT
                            '-Toa thuốc',
                            ct.stt_dieutri,
                            ct.ten_vat_tu,
                            '' ket_qua,
                            NULL nguoi_thuc_hien,
                            TO_CHAR(ct.ngay_gio_ke_thucte, 'dd/mm/yyyy hh24:mi') ngay_chi_dinh_ct,
                            '' ngay_thuc_hien_ct,
                            nv.ten_nhanvien_cd
                            || ' ('
                            || chungchi_hanhnghe
                            || ')' ten_nguoi_thuc_hien,
                            CASE
                                WHEN ct.ngay_th_ylenh IS NULL THEN
                                    'Chưa thực hiện y lệnh'
                                WHEN trunc(ct.ngay_th_ylenh, 'MI') <= trunc(v_ngayvao, 'MI') THEN
                                    'Ngày thực hiện nhỏ hơn hoặc bằng ngày vào'
                                WHEN trunc(ct.ngay_th_ylenh, 'MI') > trunc(v_ngayra, 'MI') THEN
                                    'Ngày thực hiện lớn hơn ngày ra viện'
                                WHEN ct.ngay_th_ylenh IS NOT NULL
                                     AND trunc(ct.ngay_th_ylenh, 'MI') <= trunc(ct.ngay_gio_ke_thucte, 'MI') THEN
                                    'Ngày thực hiện nhỏ hơn ngày ra y lệnh'
                            END AS ghi_chu
            --AND count(1) over (PARTITION BY ct.STT_DIEUTRI, ct.mavattu) <= 1
                        FROM
                            his_manager.noitru_ct_toa_thuoc   ct
                            JOIN noitru_dieutri                    dt ON ct.dvtt = dt.dvtt
                                                      AND ct.stt_benhan = dt.stt_benhan
                                                      AND ct.stt_dotdieutri = dt.stt_dotdieutri
                                                      AND ct.stt_dieutri = dt.stt_dieutri
                                                      AND ct.sovaovien = dt.sovaovien
                                                      AND ct.sovaovien_dt = dt.sovaovien_dt
                                                      AND ct.id_dieutri = dt.id_dieutri
                                                      AND dt.dvtt = p_dvtt
                                                      AND dt.sovaovien = p_sovaovien
                                                      AND dt.sovaovien_dt = p_sovaovien_dt
                            JOIN his_fw.dm_nhanvien_cd             nv ON dt.tdt_nguoilap = nv.ma_nhanvien
                        WHERE
                            ct.dvtt = p_dvtt
                            AND ct.sovaovien = p_sovaovien
                            AND ct.sovaovien_dt = p_sovaovien_dt
                            AND ct.so_luong_thuc_linh > 0
                            AND ( ( ( ct.maloaivattu LIKE 'TH%'
                                      OR ct.maloaivattu LIKE '%DICHTRUYEN%'
                                      OR ct.maloaivattu IN (
                                'DONGY_CP',
                                'DD',
                                'KS'
                            )
                                      OR ct.maloaivattu LIKE '%YHCT%' )
                                    AND nghiep_vu NOT IN (
                                'noitru_toathuoc',
                                'noitru_toaquaybanthuocbv',
                                'noitru_toamuangoai'
                            )
                                    AND nvl(ct.ghi_chu_ct_toa_thuoc, '0') NOT LIKE '%CD_%' )
                                  OR nghiep_vu IN (
                                'noitru_toathuoc'
                            ) )
                            AND ( ct.ngay_th_ylenh IS NULL
                                  OR trunc(ct.ngay_th_ylenh, 'MI') <= trunc(v_ngayvao, 'MI')
                                  OR trunc(ct.ngay_th_ylenh, 'MI') > trunc(v_ngayra, 'MI')
                                  OR ( ct.ngay_th_ylenh IS NOT NULL
                                       AND trunc(ct.ngay_th_ylenh, 'MI') <= trunc(ct.ngay_gio_ke_thucte, 'MI') ) )
                        UNION ALL
                        SELECT DISTINCT
                            '-Giường bệnh',
                            ct.stt_loggiuongbenh,
                            'Giường bệnh',
                            '' ket_qua,
                            NULL nguoi_thuc_hien,
                            TO_CHAR(ct.ngayvao, 'dd/mm/yyyy hh24:mi') ngay_chi_dinh_ct,
                            '' ngay_thuc_hien_ct,
                            nv.ten_nhanvien_cd
                            || ' ('
                            || chungchi_hanhnghe
                            || ')' ten_nguoi_thuc_hien,
                            CASE
                                WHEN ct.ngayvao < tt.ngayvao THEN
                                    'Ngày cấp giường: '
                                    || TO_CHAR(ct.ngayvao, 'dd/mm/yyyy hh24:mi')
                                    || ' nhỏ hơn ngày vào viện : '
                                    || TO_CHAR(tt.ngayvao, 'dd/mm/yyyy hh24:mi')
                                WHEN ct.ngayra > v_ngayra    THEN
                                    'Ngày cấp giường: '
                                    || TO_CHAR(ct.ngayra, 'dd/mm/yyyy hh24:mi')
                                    || ' lớn hơn ngày ra viện : '
                                    || TO_CHAR(v_ngayra, 'dd/mm/yyyy hh24:mi')
                                WHEN ct.ngayvao > v_ngayra   THEN
                                    'Ngày cấp giường: '
                                    || TO_CHAR(ct.ngayvao, 'dd/mm/yyyy hh24:mi')
                                    || ' lớn hơn ngày ra viện : '
                                    || TO_CHAR(v_ngayra, 'dd/mm/yyyy hh24:mi')
                                WHEN ct.ngayvao > ct.ngayra  THEN
                                    'Ngày cấp giường: '
                                    || TO_CHAR(ct.ngayvao, 'dd/mm/yyyy hh24:mi')
                                    || ' lớn hơn ngày ra  : '
                                    || TO_CHAR(ct.ngayra, 'dd/mm/yyyy hh24:mi')
                            END ghi_chu
                        FROM
                            his_manager.noitru_loggiuongbenh   ct
                            JOIN noitru_dotdieutri                  tt ON ct.dvtt = tt.dvtt
                                                         AND ct.stt_benhan = tt.stt_benhan
                                                         AND ct.stt_dotdieutri = tt.stt_dotdieutri
                                                         AND tt.dvtt = p_dvtt
                                                         AND tt.sovaovien = p_sovaovien
                                                         AND tt.sovaovien_dt = p_sovaovien_dt
                            JOIN his_fw.dm_nhanvien_cd              nv ON ct.nhanvienlap = nv.ma_nhanvien
                        WHERE
                            ct.dvtt = p_dvtt
                            AND ct.sovaovien = p_sovaovien
                            AND ct.sovaovien_dt = p_sovaovien_dt
                            AND ( ct.ngayvao < tt.ngayvao
                                  OR ct.ngayra > v_ngayra
                                  OR ct.ngayvao > v_ngayra
                                  OR ct.ngayvao > ct.ngayra )
                        UNION ALL
                        SELECT DISTINCT
                            '-Thông tin hành chính',
                            ' ',
                            'Cân nặng',
                            '' ket_qua,
                            NULL nguoi_thuc_hien,
                            ' ' ngay_chi_dinh_ct,
                            ' ' ngay_thuc_hien_ct,
                            ' ',
                            'Thiếu cân nặng' ghi_chu
                        FROM
                            noitru_dotdieutri
                        WHERE
                            dvtt = p_dvtt
                            AND sovaovien = p_sovaovien
                            AND sovaovien_dt = p_sovaovien_dt
                            AND cannang_xml IS NULL
                        UNION ALL
                        SELECT DISTINCT
                            '-Thông tin hành chính',
                            NULL,
                            'Lý do vào viện',
                            '' ket_qua,
                            NULL nguoi_thuc_hien,
                            NULL ngay_chi_dinh_ct,
                            '' ngay_thuc_hien_ct,
                            '',
                            'Thiếu lý do vào viện' ghi_chu
                        FROM
                            his_manager.noitru_benhan
                        WHERE
                            dvtt = p_dvtt
                            AND sovaovien = p_sovaovien
                            AND lydo_trangthai_bn_nhapvien IS NULL
                        UNION ALL
                        SELECT DISTINCT
                            '-Thông tin hành chính',
                            NULL,
                            'CMND/CCCD',
                            '' ket_qua,
                            NULL nguoi_thuc_hien,
                            NULL ngay_chi_dinh_ct,
                            '' ngay_thuc_hien_ct,
                            '',
                            'CMND/CCCD sai định dạng (9 hoặc 12 kí tự)' ghi_chu
                        FROM
                            his_public_list.dm_benh_nhan
                        WHERE
                            ma_benh_nhan = v_mabenhnhan
                            AND cmt_benhnhan IS NOT NULL
                            AND ( length(cmt_benhnhan) != 9
                                  AND length(cmt_benhnhan) != 12 )
                        UNION ALL
                        SELECT
                            'Bệnh án',
                            '' stt_dieutri,
                            '' ten_xetnghiem,
                            '' ket_qua,
                            0 nguoi_thuc_hien,
                            TO_CHAR(v_ngayvao, 'dd/mm/yyyy hh24:mi') ngay_chi_dinh_ct,
                            p_ngayra ngay_thuc_hien_ct,
                            '' ten_nguoi_thuc_hien,
                            CASE
                                    WHEN phuongphapdieutri IS NULL THEN
                                        'Phương pháp điều trị không được để trống; '
                                END
                            ||
                                CASE
                                    WHEN tongket_quatrinhbenhly_ls IS NULL THEN
                                        'Quá trình bệnh lý không được để trống; '
                                END
                            ||
                                CASE
                                    WHEN tomtat_xn_cls IS NULL THEN
                                        'Tóm tắt kết quả xét nghiệm cận lâm sàng không được để trống; '
                                END
                            AS ghichu
                        FROM
                            noitru_benhan
                        WHERE
                            dvtt = p_dvtt
                            AND sovaovien = p_sovaovien
                            AND ( tongket_quatrinhbenhly_ls IS NULL
                                  OR tomtat_xn_cls IS NULL
                                  OR phuongphapdieutri IS NULL )
                        UNION ALL
                        SELECT
                            'Giấy chứng sinh',
                            '' stt_dieutri,
                            '' ten_xetnghiem,
                            '' ket_qua,
                            0 nguoi_thuc_hien,
                            NULL ngay_chi_dinh_ct,
                            '' ngay_thuc_hien_ct,
                            '' ten_nguoi_thuc_hien,
                            'Mã thẻ tạm trống' AS ghichu
                        FROM
                            his_ytcs.kb_ss_thong_tin   a
                            INNER JOIN his_ytcs.ds_chung_sinh     b ON a.id_ss_thong_tin = b.id_ss_thong_tin
                        WHERE
                            a.dvtt = p_dvtt
                            AND a.sovaovien_dt = p_sovaovien
                            AND a.ma_benh_nhan = v_mabenhnhan
                            AND b.ma_the_tam IS NULL
                        UNION ALL
                        SELECT
                            'Viện phí',
                            '' stt_dieutri,
                            noidung   ten_xetnghiem,
                            '' ket_qua,
                            0 nguoi_thuc_hien,
                            ngay      ngay_chi_dinh_ct,
                            'Hạn BHYT: '
                            || ngaybatdau_thebhyt
                            || ' - '
                            || ngayhethan_thebhyt ngay_thuc_hien_ct,
                            '' ten_nguoi_thuc_hien,
                            'Dịch vụ BHYT thanh toán không nằm trong hạn thẻ BHYT; Số phiếu: '
                            || sophieu
                            || '; Mã DV: '
                            || ma_dv
                        FROM
                            (
                                SELECT
                                    stt_toathuoc         sophieu,
                                    id_dieutri,
                                    mavattu              ma_dv,
                                    ten_vat_tu           noidung,
                                    ngoai_danh_muc       bhyt,
                                    stt_dieutri,
                                    so_luong_thuc_linh   soluong,
                                    dongia_ban_bh        don_gia_bhyt,
                                    dongia_ban_bv        don_gia_kbhyt,
                                    sovaovien,
                                    sovaovien_dt,
                                    'Ngày y lệnh: ' || TO_CHAR(ngay_gio_ke_thucte, 'DD/MM/YYYY') ngay,
                                    CASE
                                        WHEN ngoai_danh_muc = 1 THEN
                                            1
                                        ELSE
                                            CASE
                                                WHEN trunc(ngay_gio_ke_thucte) BETWEEN v_tungay AND v_denngay + INTERVAL '15' DAY
                                                THEN
                                                    1
                                                ELSE
                                                    0
                                            END
                                    END trangthai,
                                    trunc(ngay_gio_ke_thucte) ngaydate,
                                    'THUOC' loai,
                                    2 stt_order,
                                    TO_CHAR(v_tungay, 'DD/MM/YYYY') ngaybatdau_thebhyt,
                                    TO_CHAR(v_denngay, 'DD/MM/YYYY') ngayhethan_thebhyt
                                FROM
                                    noitru_ct_toa_thuoc
                                WHERE
                                    dvtt = p_dvtt
                                    AND stt_benhan = p_stt_benhan
                                    AND stt_dotdieutri = p_stt_dotdieutri
                                    AND v_thamso_960592 = 1
                                    AND nghiep_vu IN (
                                        'noitru_toathuoc',
                                        'noitru_toavattu'
                                    )
                                    AND so_luong_thuc_linh > 0
                                    AND v_sothebhyt IS NOT NULL
                                UNION ALL
                                SELECT
                                    so_phieu_xn        sophieu,
                                    id_dieutri,
                                    ct.ma_xet_nghiem   ma_dv,
                                    nvl(xn.ten_xetnghiem, xn.ten_hien_thi) noidung,
                                    bhytkchi           bhyt,
                                    stt_dieutri,
                                    soluong,
                                    don_gia_bhyt       don_gia_bhyt,
                                    don_gia_ko_bhyt    don_gia_kbhyt,
                                    sovaovien,
                                    sovaovien_dt,
                                    'Ngày y lệnh: ' || TO_CHAR(ngay_chi_dinh_ct, 'DD/MM/YYYY') ngay,
                                    CASE
                                        WHEN bhytkchi = 1 THEN
                                            1
                                        ELSE
                                            CASE
                                                WHEN trunc(ngay_chi_dinh_ct) BETWEEN v_tungay AND v_denngay + INTERVAL '15' DAY THEN
                                                    1
                                                ELSE
                                                    0
                                            END
                                    END trangthai,
                                    trunc(ngay_chi_dinh_ct) ngaydate,
                                    'XN' loai,
                                    3 stt_order,
                                    TO_CHAR(v_tungay, 'DD/MM/YYYY') ngaybatdau_thebhyt,
                                    TO_CHAR(v_denngay, 'DD/MM/YYYY') ngayhethan_thebhyt
                                FROM
                                    noitru_cd_xet_nghiem_ct   ct
                                    JOIN cls_xetnghiem             xn ON ct.dvtt = xn.dvtt
                                                             AND ct.ma_xet_nghiem = xn.ma_xetnghiem
                                WHERE
                                    ct.dvtt = p_dvtt
                                    AND stt_benhan = p_stt_benhan
                                    AND stt_dotdieutri = p_stt_dotdieutri
                                    AND v_thamso_960592 = 1
                                    AND id_chisocha IS NULL
                                    AND v_sothebhyt IS NOT NULL
                                UNION ALL
                                SELECT
                                    so_phieu_cdha     sophieu,
                                    id_dieutri,
                                    ct.ma_cdha        ma_dv,
                                    nvl(xn.ten_cdha, xn.ten_hien_thi) noidung,
                                    bhytkchi          bhyt,
                                    stt_dieutri,
                                    so_luong,
                                    don_gia_bhyt      don_gia_bhyt,
                                    don_gia_ko_bhyt   don_gia_kbhyt,
                                    sovaovien,
                                    sovaovien_dt,
                                    'Ngày y lệnh: ' || TO_CHAR(ngay_chi_dinh_ct, 'DD/MM/YYYY') ngay,
                                    CASE
                                        WHEN bhytkchi = 1 THEN
                                            1
                                        ELSE
                                            CASE
                                                WHEN trunc(ngay_chi_dinh_ct) BETWEEN v_tungay AND v_denngay + INTERVAL '15' DAY THEN
                                                    1
                                                ELSE
                                                    0
                                            END
                                    END trangthai,
                                    trunc(ngay_chi_dinh_ct) ngaydate,
                                    'CDHA' loai,
                                    4 stt_order,
                                    TO_CHAR(v_tungay, 'DD/MM/YYYY') ngaybatdau_thebhyt,
                                    TO_CHAR(v_denngay, 'DD/MM/YYYY') ngayhethan_thebhyt
                                FROM
                                    noitru_cd_cdha_chi_tiet   ct
                                    JOIN cls_cdha                  xn ON ct.dvtt = xn.dvtt
                                                        AND ct.ma_cdha = xn.ma_cdha
                                WHERE
                                    ct.dvtt = p_dvtt
                                    AND stt_benhan = p_stt_benhan
                                    AND v_thamso_960592 = 1
                                    AND stt_dotdieutri = p_stt_dotdieutri
                                    AND v_sothebhyt IS NOT NULL
                                UNION ALL
                                SELECT
                                    so_phieu_dichvu   sophieu,
                                    id_dieutri,
                                    ct.ma_dv          ma_dv,
                                    nvl(xn.ten_dv, xn.ten_hien_thi) noidung,
                                    bhytkchi          bhyt,
                                    stt_dieutri,
                                    so_luong,
                                    don_gia_bhyt      don_gia_bhyt,
                                    don_gia_ko_bhyt   don_gia_kbhyt,
                                    sovaovien,
                                    sovaovien_dt,
                                    'Ngày y lệnh: ' || TO_CHAR(ngay_chi_dinh_ct, 'DD/MM/YYYY') ngay,
                                    CASE
                                        WHEN bhytkchi = 1 THEN
                                            1
                                        ELSE
                                            CASE
                                                WHEN trunc(ngay_chi_dinh_ct) BETWEEN v_tungay AND v_denngay + INTERVAL '15' DAY THEN
                                                    1
                                                ELSE
                                                    0
                                            END
                                    END trangthai,
                                    trunc(ngay_chi_dinh_ct) ngaydate,
                                    'TTPT' loai,
                                    5 stt_order,
                                    TO_CHAR(v_tungay, 'DD/MM/YYYY') ngaybatdau_thebhyt,
                                    TO_CHAR(v_denngay, 'DD/MM/YYYY') ngayhethan_thebhyt
                                FROM
                                    noitru_cd_dichvu_ct   ct
                                    JOIN dm_dich_vu_kham       xn ON ct.dvtt = xn.dvtt
                                                               AND ct.ma_dv = xn.ma_dv
                                WHERE
                                    ct.dvtt = p_dvtt
                                    AND stt_benhan = p_stt_benhan
                                    AND v_thamso_960592 = 1
                                    AND stt_dotdieutri = p_stt_dotdieutri
                                    AND v_sothebhyt IS NOT NULL
                                UNION ALL
                                SELECT
                                    log.stt_loggiuongbenh,
                                    to_number(log.stt_logkhoaphong),
                                    giuong.magiuongbenh,
                                    giuong.tengiuongbenh,
                                    log.ngoai_danh_muc,
                                    log.stt_logkhoaphong,
                                    CASE nam_cung_giuong
                                        WHEN 1 THEN
                                            log.songaydieutri / (
                                                CASE
                                                    WHEN log.so_nguoi_nam = 2 THEN
                                                        0.5
                                                    ELSE
                                                        CASE
                                                            WHEN log.so_nguoi_nam >= 3 THEN
                                                                0.3
                                                            ELSE
                                                                1
                                                        END
                                                END
                                            )
                                        ELSE
                                            log.songaydieutri
                                    END AS songaydieutri,
                                    log.dongia,
                                    log.dongia,
                                    log.sovaovien,
                                    log.sovaovien_dt,
                                    'Ngày y lệnh: '
                                    || TO_CHAR(log.ngayvao, 'DD/MM/YYYY') ngay,
                                    CASE
                                        WHEN log.ngoai_danh_muc = 1 THEN
                                            1
                                        ELSE
                                            CASE
                                                WHEN trunc(log.ngayvao) BETWEEN v_tungay AND v_denngay + INTERVAL '15' DAY THEN
                                                    1
                                                ELSE
                                                    0
                                            END
                                    END trangthai,
                                    trunc(log.ngayvao) ngaydate,
                                    'GIUONG' loai,
                                    1 stt_order,
                                    TO_CHAR(v_tungay, 'DD/MM/YYYY') ngaybatdau_thebhyt,
                                    TO_CHAR(v_denngay, 'DD/MM/YYYY') ngayhethan_thebhyt
                                FROM
                                    noitru_loggiuongbenh   log,
                                    dm_giuongbenh          giuong,
                                    dm_phong_benh          phong
                                WHERE
                                    log.dvtt = p_dvtt
                                    AND giuong.dvtt = p_dvtt
                                    AND log.stt_benhan = p_stt_benhan
                                    AND log.stt_dotdieutri = p_stt_dotdieutri
                                    AND log.magiuongbenh = giuong.magiuongbenh
                                    AND log.sovaovien = p_sovaovien
                                    AND log.sovaovien_dt = p_sovaovien_dt
                                    AND v_thamso_960592 = 1
                                    AND giuong.ma_phong_benh = phong.ma_phong_benh
                                    AND v_sothebhyt IS NOT NULL
                                UNION ALL
                                SELECT
                                    'CONGKHAM' sophieu,
                                    1,
                                    xn.ma_dv            ma_dv,
                                    nvl(xn.ten_dv, xn.ten_hien_thi) noidung,
                                    congkham_dichvu     bhyt,
                                    '1' stt_dieutri,
                                    1 so_luong,
                                    congkham_ngoaitru   don_gia_bhyt,
                                    congkham_ngoaitru   don_gia_kbhyt,
                                    sovaovien,
                                    0 sovaovien_dt,
                                    'Ngày y lệnh: ' || TO_CHAR(ngay_nhapvien, 'DD/MM/YYYY') ngay,
                                    CASE
                                        WHEN congkham_dichvu = 1 THEN
                                            1
                                        ELSE
                                            CASE
                                                WHEN trunc(ngay_nhapvien) BETWEEN v_tungay AND v_denngay + INTERVAL '15' DAY THEN
                                                    1
                                                ELSE
                                                    0
                                            END
                                    END trangthai,
                                    trunc(ngay_nhapvien) ngaydate,
                                    'CONGKHAM' loai,
                                    0 stt_order,
                                    TO_CHAR(v_tungay, 'DD/MM/YYYY') ngaybatdau_thebhyt,
                                    TO_CHAR(v_denngay, 'DD/MM/YYYY') ngayhethan_thebhyt
                                FROM
                                    noitru_benhan     ct
                                    JOIN dm_dich_vu_kham   xn ON ct.dvtt = xn.dvtt
                                                               AND ct.ma_dv_kham_ngoaitru = xn.ma_dv
                                WHERE
                                    ct.dvtt = p_dvtt
                                    AND stt_benhan = p_stt_benhan
                                    AND v_sothebhyt IS NOT NULL
                                    AND v_thamso_960592 = 1
                                ORDER BY
                                    ngaydate,
                                    stt_order
                            ) a
                        WHERE
                            trangthai = 0
                            AND v_sothebhyt IS NOT NULL
                            AND v_thamso_960592 = 1
                    ) cls;

RETURN cur;
END;