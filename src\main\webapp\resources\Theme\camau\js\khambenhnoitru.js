var thongtinhsba = {
    todieutri: [],
    thuoc: [],
    vattuyte: [],
    phieuchamsoc: [],
    phieuchucnangsong: [],
    xetnghiem: [],
    cdha: [],
    ttpt: [],
    thongtinbn: {}
}
function load_dsbenhnhan(SOVAOVIEN_DT = null) {
    var url = getUrlLoadDSBenhnhan();
    $("#list_benhnhan").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
    resetDataBN();

    if (SOVAOVIEN_DT) {
        $("#list_benhnhan").on("jqGridAfterGridComplete", function () {
            var row = $("#list_benhnhan").find("tr.jqgrow").filter(function () {
                return $(this).find("td").text().indexOf(SOVAOVIEN_DT) !== -1;
            });
            if (row.length > 0) {
                row.trigger("click");
                setTimeout(() => {
                    row.trigger("dblclick");
                }, 100);
            }
            $(this).off("jqGridAfterGridComplete");
        });
    }
}

function getUrlLoadDSBenhnhan() {
    if(singletonObject.bant == 1) {
        return 'noitru_danhsachbenhnhan_bant?'+$.param({
            phongban: singletonObject.makhoa,
            tt: $("#trangthai_bn").val(),
            dvtt: singletonObject.dvtt,
            tungay: moment().format("YYYY-MM-DD"),
            denngay: moment().format("YYYY-MM-DD"),
            phongkham: $("#tenphongbenh").val()? $("#tenphongbenh").val(): 0,
        })
    }
    return  "noitru_danhsachbenhnhantheokhoa?phongban="+singletonObject.makhoa+"&tt=" + $("#trangthai_bn").val() + "&dvtt="+singletonObject.dvtt;
}
function getThongtinBnSelected() {
    var id = $("#list_benhnhan").jqGrid("getGridParam", "selrow");
    var rowData = $("#list_benhnhan").jqGrid("getRowData", id);
    return rowData;
}

function getThongtinBNChitiet(callbackDone, callbackFail) {
    showLoaderIntoWrapId("content-thontinhc");
    var url = "noitru_chitiet_dotdieutri";
    var retBenhnhan = getThongtinBnSelected();
    $.post(url, {
        dvtt: singletonObject.dvtt,
        stt_benhan: retBenhnhan.STT_BENHAN,
        stt_dotdieutri: retBenhnhan.STT_DOTDIEUTRI
    }).done(function (data) {
        thongtinhsba.thongtinbn['DSDOTDIEUTRI'] = [];
        thongtinhsba.thongtinbn = { ...thongtinhsba.thongtinbn, ...data }
        if(typeof callbackDone === 'function') {
            callbackDone();
        }
        if(data.MAPHONGBAN != singletonObject.makhoa) {
            $("#hsba_action_ctrvck_wrap").hide()
            $(".hidechuyenkhoa").hide()
        } else {
            $("#hsba_action_ctrvck_wrap").show()
            $(".hidechuyenkhoa").show()
        }
        $.post("noitru_ttbenhnhan", {
            mabenhnhan: retBenhnhan.MA_BENH_NHAN
        }).done(function (dataBenhnhan) {
            thongtinhsba.thongtinbn = { ...thongtinhsba.thongtinbn, ...dataBenhnhan }
            $.post("noitru_songay_conbhyt", {
                url: convertArray([singletonObject.dvtt, retBenhnhan.STT_BENHAN, retBenhnhan.STT_DOTDIEUTRI])
            }).done(function (songayconBHYT) {
                thongtinhsba.thongtinbn['songayconBHYT'] = songayconBHYT
            });

            $.ajax({
                url: "lay_id_nhankhau?mabenhnhan=" + retBenhnhan.MA_BENH_NHAN
            }).done(function (dt) {
                thongtinhsba.thongtinbn['IDNHANKHAU'] = dt;
            })
            var resDanhsachDDT = $.ajax({
                url: "noitru_danhsachdotdieutri?stt_benhan="+retBenhnhan.STT_BENHAN+"&dvtt="+singletonObject.dvtt,
                async: false,
                method: "GET"
            }).responseText;
            resDanhsachDDT = JSON.parse(resDanhsachDDT);
            thongtinhsba.thongtinbn['DSDOTDIEUTRI'] = resDanhsachDDT;
            displayThongtinBNChitiet();
            noitruTaoBangke(thongtinhsba.thongtinbn, loadthongtinchiphibenhnhan)
        }).always(function() {
            hideLoaderIntoWrapId("content-thontinhc");
        })
    }).fail(function() {
        notifiToClient("Red", "Lỗi lấy thông tin bệnh nhân");
        hideLoaderIntoWrapId("content-thontinhc");
        if(typeof callbackFail === 'function') {
            callbackFail();
        }
    })
}
function clearThongtinBNChitiet() {
    $(".hsba-thongtin").html("")
}

function displayThongtinBNChitiet() {
    clearThongtinBNChitiet();
    var dataBN  = thongtinhsba.thongtinbn;
    $("#hsba_hoten2").html(dataBN.TEN_BENH_NHAN)
    $("#hsba_sobenhan2").html(dataBN.SOBENHAN)
    $("#hsba_mayte2").html(dataBN.MA_BENH_NHAN)
    $("#hsba_gioitinh").html(dataBN.GIOI_TINH == 1? "Nam": "Nữ")
    $("#hsba_namsinh").html(dataBN.NGAY_SINH)
    var tendantoc = "";
    singletonObject.danhsachdantoc.forEach(function (item) {
        if(item.MA_DANTOC == dataBN.MA_DANTOC) {
            tendantoc = item.TEN_DANTOC;
        }
    })
    $("#hsba_dantoc").html(tendantoc)
    $("#hsba_diachi").html(dataBN.DIA_CHI)
    $("#hsba_sdt").html(dataBN.SO_DIEN_THOAI)
    $("#hsba_cccd").html(dataBN.CMT_BENHNHAN)
    $("#hsba_doituong").html(dataBN.COBHYT == 1? "BHYT": "Thu phí")
    $("#hsba_sothebhyt").html(dataBN.SOBAOHIEMYTE)
    $("#hsba_muchuong").html(dataBN.TYLEBAOHIEM + "%")
    $("#hsba_nguoilienhe").html(dataBN.NGUOI_LIEN_HE)
    $("#hsba_cccd").html(dataBN.CMT_BENHNHAN)
    if(dataBN.SOBAOHIEMYTE != null && dataBN.SOBAOHIEMYTE != "") {
        $("#hsba_hanthe").html( dataBN.NGAYBATDAU_THEBHYT + " - " + dataBN.NGAYHETHAN_THEBHYT)
        $("#hsba_noidangky").html(dataBN.NOIDANGKYBANDAU + " - " + dataBN.TENNOIDANGKYBANDAU)
    }

    $("#hsba_trangthainhapvien").html("<span style='color: green'>" +
        (dataBN.CAPCUU == 1? "Cấp cứu":  dataBN.THONGTUYEN_BHXH_XML4210 == 1? "Thông tuyến" : dataBN.DUNGTUYEN == 1? "Đúng tuyến": "Trái tuyến") + "</span>" )
    $("#hsba_doituongbhyt").html(dataBN.MADOITUONG)

    $("#hsba_makhuvuc").html(dataBN.MA_KHUVUC)

    $("#hsba_lydonhapvien").html(dataBN.LYDO_TRANGTHAI_BN_NHAPVIEN)
    $("#hsba_icdnhapvien").html(dataBN.ICD_NHAPVIEN + " - " + dataBN.TENBENHCHINH_NHAPVIEN)
    $("#hsba_ngaynhapvien").html(dataBN.NGAYTIEPNHAN6556 + " "+ dataBN.GIOTIEPNHAN6556)
    $("#hsba_ngayvaokhoa").html(dataBN.NGAYTIEPNHAN6556 + " "+ dataBN.GIOTIEPNHAN6556)
    $("#hsba_tthc_qlnbngayvaokhoa").html(dataBN.NGAYTIEPNHAN6556 + " "+ dataBN.GIOTIEPNHAN6556);
    $("#hsba_khoanhapvien").html(dataBN.TENKHOA_KHOACHIDINHNHAPVIEN)
    $("#hsba_miencungchitra").html(dataBN.BAOHIEMYTE5NAM == 1? "<span style='color:red'>Có</span>": "Không")
    $("#hsba_ngaymiencungchitra").html(dataBN.NGAY_MIEN_CUNG_CT)
    $("#hsba_ngaymiencungchitra_kgt").html(dataBN.NT_THOIDIEM_MCCT_KH_GIAY)
    $("#hsba_tthc_tntt").html(dataBN.TENTAINANTHUONGTICH)
    var giaytote = "";
    if(dataBN.TE_GIAYCHUNGSINH != null && dataBN.TE_GIAYCHUNGSINH != "") {
        giaytote = dataBN.TE_GIAYCHUNGSINH
    }
    if(dataBN.TE_GIAYKHAISINH != null && dataBN.TE_GIAYKHAISINH != "") {
        giaytote = dataBN.TE_GIAYKHAISINH
    }
    if(dataBN.TE_GIAYTOKHAC != null && dataBN.TE_GIAYTOKHAC != "") {
        giaytote = dataBN.TE_GIAYTOKHAC
    }
    $("#hsba_giaytote").html(giaytote)
    $("#hsba_ngay5nam").html(dataBN.NT_THOIDIEM_5NAM_LIENTUC)
    $(".hsba-congkham").show();

    $("#hsba_khoa_tencongkham").html("")
    var tenphongphong = '';
    singletonObject.danhsachphongban.forEach(function(item) {
        if(item.MAKHOA == dataBN.MA_PHONG_BAN_CK || dataBN.KHOANGOAITRU_NHAPVIEN == item.MAKHOA) {
            tenphongphong = item.TENKHOA;
        }
    })
    if(dataBN.TEN_DV) {
        $("#hsba_khoa_tencongkham").html(tenphongphong + ": " + dataBN.TEN_DV)
    }

    if(dataBN.STT_DOTDIEUTRI > 1) {
        $(".hsba-congkham").hide();
    }
}

function resetData() {
    $("#idbacsidieutri").val("");
    $("#bacsidieutri").val("").trigger("change");
    $("#titleTodieutri").html("");
    var listTdt = $("#list_todieutri");
    if(listTdt[0].grid) {
        listTdt.jqGrid("clearGridData");
    }
    var listThuoc = $("#list_thuoc");
    if(listThuoc[0].grid) {
        listThuoc.jqGrid("clearGridData");

    }
    var listPhieuchamsoc = $("#list_phieuchamsoc");
    if(listPhieuchamsoc[0].grid) {
        listPhieuchamsoc.jqGrid("clearGridData");
    }
    var listXetnghiem = $("#list_xetnghiem");
    if(listXetnghiem[0].grid) {
        listXetnghiem.jqGrid("clearGridData");
    }
    var listCdha = $("#list_chandoanhinhanh");
    if(listCdha[0].grid) {
        listCdha.jqGrid("clearGridData");
    }
    var listTtpt = $("#list_ttpt");
    if(listTtpt[0].grid) {
        listTtpt.jqGrid("clearGridData");
    }

}
function resetDataBN() {
    thongtinhsba = {
        todieutri: [],
        thuoc: [],
        vattuyte: [],
        phieuchamsoc: [],
        phieuchucnangsong: [],
        xetnghiem: [],
        cdha: [],
        ttpt: [],
        thongtinbenhnhan: {}
    }
}

function loadBsTheokhoaDieutri(id, makhoa, optionAll, mabs) {
    var url = "laybacsi_theokhoa?khoa=" + makhoa;
    $.ajax({
        url: url
    }).done(function (data) {
        if (data) {
            $("#"+ id).empty();
            if(optionAll) {
                $("<option value='-1'>Tất cả</option>").appendTo("#"+id);
            }
            $.each(data, function (i) {
                if (i == 0)
                    $("#id"+ id).val(data[i].MA_NHANVIEN);
                $("<option value='" + data[i].MA_NHANVIEN + "'>" + data[i].TEN_NHANVIEN + "</option>").appendTo("#"+id);
            });
        }
        $("#" + id).select2({ width: '100%' });
        if(mabs != ""){
            $("#" + id).val(mabs).trigger("change");
        }
        $("#" + id).on('select2:open', function (e) {
            if($('.modal:visible').length > 0) {
                var zIndex = 1040 + 10 * $('.modal:visible').length + 2;
                $('.select2-container--open').css('z-index', zIndex);
            }

        });
    });
}

function loadDDTheokhoaDieutri(id, makhoa, optionAll, mabs) {
    var url = "cmu_list_NOT_SEL_YTADDUONG_THEOKHOA?url=" + convertArray([makhoa]);
    $.ajax({
        url: url
    }).done(function (data) {
        if (data) {
            $("#"+ id).empty();
            if(optionAll) {
                $("<option value='-1'>Tất cả</option>").appendTo("#"+id);
            }
            $.each(data, function (i) {
                if (i == 0)
                    $("#id"+ id).val(data[i].MA_NHANVIEN);
                $("<option value='" + data[i].MA_NHANVIEN + "'>" + data[i].TEN_NHANVIEN + "</option>").appendTo("#"+id);
            });
        }
        $("#" + id).select2({ width: '100%' });
        if(mabs != ""){
            $("#" + id).val(mabs).trigger("change");
        }
    });
}

function loadDsPhongBan(id, makhoa, optionAll) {
    $("#" + id).html('')
    singletonObject.danhsachphongban.forEach(function(obj) {
        if(optionAll){
            $("#" + id).append("<option value='"+obj.MAKHOA+"'>"+obj.TENKHOA+"</option>")
        } else {
            if(obj.MAKHOA != 0){
                $("#" + id).append("<option value='"+obj.MAKHOA+"'>"+obj.TENKHOA+"</option>")
            }
        }
    })
    if(makhoa != -1) {
        $("#" + id).select2({ width: '100%' });
        $("#" + id).val(makhoa).trigger("change");
    }

}

function loadDsToDieutri() {
    if($("#list_todieutri")[0].grid) {
        var url = 'cmu_getlist?url=' + convertArray([thongtinhsba.thongtinbn.STT_BENHAN,
            $("#ttin_dieutri_khoa").val(),
            $("#ttin_dieutri_bs").val(),
            singletonObject.dvtt,
            "HSBA_TODIEUTRI_CMU_SEL"]);
        $("#list_todieutri").jqGrid('setGridParam', {
            datatype: 'json',
            url: url
        }).trigger('reloadGrid');
    }

}

function loadDsThuocVattuTonghop(maloai, idgrid) {
    var url = 'cmu_list_HSBA_DSTHUOC_TONGHOP?url='
        + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.STT_BENHAN,
            thongtinhsba.thongtinbn.MA_BENH_NHAN
            , maloai])
    loadDataGridGroupBy($("#"+idgrid), url);
}

function loadDsXetnghiemTonghop() {
    var url = 'cmu_list_HSBA_DSXN_TONGHOP?url='
        + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.STT_BENHAN,
            thongtinhsba.thongtinbn.MA_BENH_NHAN]);
    loadDataGridGroupBy($("#list_xetnghiem"), url);
}

function loadDsCDHATonghop() {
    var url = 'cmu_list_HSBA_DSCDHA_TONGHOP?url='
        + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.STT_BENHAN,
            thongtinhsba.thongtinbn.MA_BENH_NHAN])
    loadDataGridGroupBy($("#list_chandoanhinhanh"), url);
}

function loadDsTTPTTonghop() {
    var loai = $("#hsba-tonghop-loaittpt").val();
    var url = 'cmu_list_HSBA_DSTTPT_TONGHOP?url='
        + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.STT_BENHAN,
            thongtinhsba.thongtinbn.MA_BENH_NHAN, loai])
    loadDataGridGroupBy($("#list_ttpt"), url);
}

function loadthongtinchiphibenhnhan() {
    $.post("noitru_select_tientamung_conlai", {
        dvtt: singletonObject.dvtt,
        stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
        stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI
    }).done(function(data) {
        $("#hsba_tongchiphi").html(Number(data.CHIPHI_NGUOIBENH).toLocaleString("de-DE"));
        $("#hsba_tongtamung").html(Number(data.TONGTIEN_TAMUNG).toLocaleString("de-DE"));
        var text = Number(data.TONGTIEN_CONLAI) > 0 ? " (Thừa tiền)" : Number(data.TONGTIEN_CONLAI) < 0 ? " (Thiếu tiền)" : "";
        $("#hsba_tongcandoi").html(Number(data.TONGTIEN_CONLAI).toLocaleString("de-DE") + text);
    })
}

function hsbaKhoitaobenhan() {
    addTextTitleModal("titleHsbaTaobenhan", "Tạo bệnh án");
    $("#modalHsbaTaobenhan").modal("show")
    initSelect2IfnotIntance("hsba-taoba-khoa", singletonObject.danhsachphongban, "MAKHOA",
        "TENKHOA", true, false,singletonObject.makhoa, true);

    $("#hsba-taoba-loaibenhan").html("");
    singletonObject.danhsachloaibenhan.forEach(function(obj) {
        $("#hsba-taoba-loaibenhan").append("<option value='" + obj.LOAIBENHAN + "'>" + obj.TENLOAIBENHAN + "</option>")
    })
}
$(function() {
    $(".input-date").inputmask({
        mask: "1/2/y h:s",
        placeholder: "dd/mm/yyyy hh:mm",
        alias: "datetime",
        hourFormat: "24"
    });
    $(".input-date").datetimepicker({dateFormat: "dd/mm/yy"});
    $(".input-date").val(singletonObject.ngayhientai + " 00:00");

    $(".input-date-second").inputmask({
        mask: "1/2/y h:s:s",
        placeholder: "dd/mm/yyyy hh:mm:ss",
        alias: "datetime",
        hourFormat: "24"
    });
    $(".input-date-second").datetimepicker({
        dateFormat: "dd/mm/yy",
        timeFormat: 'HH:mm:ss',
        showSecond: true,
    });
    $(".input-date-second").val(singletonObject.ngayhientai + " 00:00:00");

    $(".input-only-date").inputmask({
        mask: "1/2/y",
        placeholder: "dd/mm/yyyy",
        alias: "datetime",
        hourFormat: "24"
    });
    $(".input-only-date").datepicker({dateFormat: "dd/mm/yy"});
    $(".input-only-date").val(singletonObject.ngayhientai);

    $(".input-only-time").inputmask({
        mask: "h:s",
        placeholder: "hh:mm",
        alias: "datetime",
        hourFormat: "24"
    });
    $(".input-only-time-second").inputmask({
        mask: "h:s:s",
        placeholder: "hh:mm:ss",
        alias: "datetime",
        hourFormat: "24"
    });
    $(".unbinddbclick").unbind("dblclick");

    $("#hsba_tabs").tabs();
    $("#list_benhnhan").jqGrid({
        url: getUrlLoadDSBenhnhan(),
        datatype: "json",
        loadonce: true,
        height: 400,
        width: null,
        shrinkToFit: false,
        colModel: [
            {label: "Số BA",name: 'SOBENHAN', index: 'SOBENHAN',width: 100,fixed: true},
            {label: "Mã BN", name: 'MA_BENH_NHAN_HT', index: 'MA_BENH_NHAN_HT',align: "center", fixed: true, width: 100, formatter: function (cellvalue, options, rowObject) {
                    var stringIcon = "";
                    if(rowObject.EMR == 1 ) {
                        stringIcon = ' <i style="font-weight: bold" class="fa fa-certificate text-primary" aria-hidden="true"></i>'
                    }
                    return rowObject.MA_BENH_NHAN + stringIcon;
                }
            },
            {label: "Mã BN", name: 'MA_BENH_NHAN', index: 'MA_BENH_NHAN',hidden: true},
            {label: "GRV/GCT", name: 'GRV_HT', index: 'GRV_HT',align: "center", fixed: true, width: 100,
                cellattr: function (cellvalue, options, rowObject) {
                    var classString = 'class="cellWithoutBackground"';
                    var color = 'red';

                    switch(rowObject.GRV) {
                        case '1':
                            grvctht = 'GRV (Trưởng khoa chưa ký)';
                            color = 'red';
                            break;
                        case '2':
                            grvctht = 'GRV (BGĐ chưa ký)';
                            color = 'red';
                            break;
                        case '3':
                            grvctht = 'GRV (Đã ký)';
                            color = 'green';
                            break;
                        case '4':
                            grvctht = 'GCT (BGĐ chưa ký)';
                            color = 'red';
                            break;
                        case '5':
                            grvctht = 'GCT (Đã ký)';
                            color = 'green';
                            break;
                        default:
                            color = 'red';
                            break;
                    }
                    var styleString = 'style="white-space: nowrap; white-space: normal;font-weight:bold ;color:' + color + '"';
                    return classString + ' ' + styleString;
                }
            },
            {label: "GRV/GCT", name: 'GRV', index: 'GRV',align: "center", hidden: true},
            {label: "Bệnh án", name: 'MABACSILAMBENHAN', index: 'MABACSILAMBENHAN',align: "center", fixed: true, width: 100, cellattr: function (rowId, tv, rawObject, cm, rdata) {
                    var color = "green";
                    var fontWeight = "600";
                    if(!rawObject.MABACSILAMBENHAN) {
                        return 'style="font-weight: ' + fontWeight + '; color: red;"';
                    }
                    return 'style="font-weight: ' + fontWeight + '; color: ' + color + ';"';
                }, formatter: function (cellvalue, options, rowObject) {
                    if(!cellvalue) {
                        return "Chưa làm BA";
                    }
                    return "Đã làm BA";
                }
            },
            {label: "Họ tên", name: 'TEN_BENH_NHAN_HT', index: 'TEN_BENH_NHAN_HT', fixed: true, width: 350, searchoptions: {dataInit: function (el) {
                        setTimeout(function () {
                            $(el).focus().trigger({type: 'keypress', charCode: 13});
                        }, 20);
                    }
                }, formatter: function (cellvalue, options, rowObject) {
                    var color;
                    var color_text;
                    if (rowObject.MAX_NGAY_LAP == '1') {
                        color = '#6eac2c';
                        color_text = 'white';
                    } else {
                        color = 'transparent';
                        color_text = 'black';
                    }
                    var khoa = "";
                    if($("#trangthai_bn").val() == 8) {
                        singletonObject.danhsachphongban.forEach(function (item) {
                            if(item.MAKHOA == rowObject.KHOAPHONG_CHUYENDI) {
                                khoa = " ("+item.TENKHOA+")";
                            }
                        })
                    }
                    var tranBH = Math.max(rowObject.T_CHIPHICAO_BV, rowObject.T_CHIPHICAO_PB_BH);
                    if(rowObject.T_CHIPHICAO_BV > 0 &&rowObject.TONGTIEN_BH >= tranBH){
                        var title = 'Cảnh báo: Tổng chi phí của bệnh nhân đã vượt trần ' + tranBH + '.';
                        return '<span class="cellWithoutBackground" style="position: relative; background-color:' + color + ';font-weight:bold ;color:' + color_text + '">' + cellvalue + khoa + '<img title="' + title + '" src="/web_his/resources/images/danger.png" style="position: absolute; top: 0; right: 0; height: 20px;"/></span>';
                    }
                    return '<span class="cellWithoutBackground" style="background-color:' + color + ';font-weight:bold ;color:' + color_text + '">' + cellvalue + khoa + '</span>';
                }
            },
            {label: "Phòng", name: 'TEN_PHONG', index: 'TEN_PHONG', fixed: true, width: 80},
            {label: "Giường", name: 'SOGIUONG', index: 'SOGIUONG',align: "center", fixed: true, width: 80},
            {label: "TEN_BENH_NHAN", name: 'TEN_BENH_NHAN', index: 'TEN_BENH_NHAN', width: 200, hidden: true},
            {label: "Ngày nhập viện", name: 'NGAYGIO_NHAPVIEN', index: 'NGAYGIO_NHAPVIEN', width: 180, align: "center", fixed: true},
            {label: "Ngày vào khoa", name: 'NGAYVAOKHOA', index: 'NGAYVAOKHOA', width: 180, align: "center", fixed: true},
            {label: "UU_TIEN", name: 'UU_TIEN', index: 'UU_TIEN', hidden: true},
            {label: "Tuổi",name: 'TUOI', index: 'TUOI', width: 150, align: "center", fixed: true, hidden: true},
            {label: "Tuổi",name: 'TUOI_HT', index: 'TUOI_HT', width: 100, align: "center", fixed: true,
                formatter: function (cellvalue, options, rowObject) {
                    var tuoiHt = rowObject.TUOI == 0? (rowObject.THANG > 0? rowObject.THANG + ' tháng': rowObject.NGAY + ' ngày'): rowObject.TUOI + ' tuổi';
                    return tuoiHt;
                }
            },
            {label: "Tháng", name: 'THANG', index: 'THANG', width: 150, align: "center", fixed: true, hidden: true},
            {label: 'NGAY', name: 'NGAY', index: 'NGAY', width: 35, align: "center", fixed: true, hidden: true},
            {label: 'GIOI_TINH', name: 'GIOI_TINH', index: 'GIOI_TINH', hidden: true, width: 30, fixed: true, formatter: 'checkbox', formatoptions: {value: 'true:false'}, align: "center"},
            {label: 'Giới tính', name: 'GIOI_TINH_HT', index: 'GIOI_TINH_HT', width: 80, align: "center", formatter: function (cellvalue, options, rowObject) {

                    return rowObject.GIOI_TINH == 1? "Nam": "Nữ";
                }
            },
            {label: 'STT_BENHAN', name: 'STT_BENHAN', index: 'STT_BENHAN', hidden: true},
            {label: 'SOBENHAN_TT', name: 'SOBENHAN_TT', index: 'SOBENHAN_TT', hidden: true},
            {label: 'STT_DOTDIEUTRI', name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', hidden: true},
            {label: 'STT_LOGKHOAPHONG', name: 'STT_LOGKHOAPHONG', index: 'STT_LOGKHOAPHONG', hidden: true},
            {label: 'DIA_CHI', name: 'DIA_CHI', index: 'DIA_CHI', hidden: true},
            {label: 'BHYT', name: "SOBAOHIEMYTE", index: "SOBAOHIEMYTE", width: 150},
            {label: 'MADOITUONG', name: 'MADOITUONG', index: 'MADOITUONG', hidden: true},
            {label: 'TYLEBAOHIEM', name: 'TYLEBAOHIEM', index: 'TYLEBAOHIEM', hidden: true},
            {label: 'NOIDANGKYBANDAU', name: 'NOIDANGKYBANDAU', index: 'NOIDANGKYBANDAU', hidden: true},
            {label: 'NGAYBATDAU_THEBHYT', name: 'NGAYBATDAU_THEBHYT', index: 'NGAYBATDAU_THEBHYT', hidden: true},
            {label: 'NGAYHETHAN_THEBYT', name: 'NGAYHETHAN_THEBYT', index: 'NGAYHETHAN_THEBYT', width: 60, hidden: true},
            {label: 'DUNGTUYEN', name: 'DUNGTUYEN', index: 'DUNGTUYEN', width: 60, hidden: true},
            {label: 'ICD_NHAPVIEN', name: 'ICD_NHAPVIEN', index: 'ICD_NHAPVIEN', width: 150, hidden: true},
            {label: 'Chẩn đoán', name: 'ICD_HT', index: 'ICD_HT', width: 150},
            {label: 'TENBENHPHU_NHAPVIEN', name: 'TENBENHPHU_NHAPVIEN', index: 'TENBENHPHU_NHAPVIEN', width: 60, hidden: true},
            {label: 'HINHTHUCNHAPVIEN', name: 'HINHTHUCNHAPVIEN', index: 'HINHTHUCNHAPVIEN', width: 60, hidden: true},
            {label: 'NOIGIOITHIEU', name: 'NOIGIOITHIEU', index: 'NOIGIOITHIEU', width: 60, hidden: true},
            {label: 'LYDO_TRANGTHAI_BN_NHAPVIEN', name: 'LYDO_TRANGTHAI_BN_NHAPVIEN', index: 'LYDO_TRANGTHAI_BN_NHAPVIEN', width: 60, hidden: true},
            {label: 'KHOACHIDINHNHAPVIEN', name: 'KHOACHIDINHNHAPVIEN', index: 'KHOACHIDINHNHAPVIEN', hidden: true},
            {label: 'NHAPVIENVAOKHOA', name: 'NHAPVIENVAOKHOA', index: 'NHAPVIENVAOKHOA', hidden: true},
            {label: 'TENGIUONGBENH', name: 'TENGIUONGBENH', index: 'TENGIUONGBENH', hidden: true},
            {label: 'KHOAPHONG_CHUYENDI', name: 'KHOAPHONG_CHUYENDI', index: 'KHOAPHONG_CHUYENDI', hidden: true},
            {label: 'CAPCUU', name: 'CAPCUU', index: 'CAPCUU', hidden: true},
            {label: 'ICD_KHOADIEUTRI', name: 'ICD_KHOADIEUTRI', index: 'ICD_KHOADIEUTRI', hidden: true},
            {label: 'TENICD_KHOADIEUTRI', name: 'TENICD_KHOADIEUTRI', index: 'TENICD_KHOADIEUTRI', hidden: true},
            {label: 'STT_LOGGIUONGBENH', name: 'STT_LOGGIUONGBENH', index: 'STT_LOGGIUONGBENH', hidden: true},
            {label: 'NGAY_VAO', name: 'NGAY_VAO', index: 'NGAY_VAO', hidden: true},
            {label: 'CHANDOAN_NGUYENNHAN', name: 'CHANDOAN_NGUYENNHAN', index: 'CHANDOAN_NGUYENNHAN', hidden: true},
            {label: 'MAKB_NHAPVIEN', name: 'MAKB_NHAPVIEN', index: 'MAKB_NHAPVIEN', hidden: true},
            {label: 'SOVAOVIEN', name: 'SOVAOVIEN', index: 'SOVAOVIEN', hidden: true},
            {label: 'SOVAOVIEN_DT', name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', hidden: true},
            {label: 'HOTRO_TIENAN', name: 'HOTRO_TIENAN', index: 'HOTRO_TIENAN', hidden: true},
            {label: 'MAX_NGAY_LAP', name: 'MAX_NGAY_LAP', index: 'MAX_NGAY_LAP', hidden: true},
            {label: 'Cân đối chi phí', name: 'CANDOICHIPHI', index: 'CANDOICHIPHI', width: 150,
                formatter: 'integer',formatoptions: {thousandsSeparator: ",", decimalPlaces: 0},
            },
            {label: 'Tổng chi phí', name: 'TONGCHIPHI', index: 'TONGCHIPHI', width: 150,
                formatter: 'integer',formatoptions: {thousandsSeparator: ",", decimalPlaces: 0},
            },
            {label: 'Tiền tạm ứng', name: 'SOTIENTAMUNG', index: 'SOTIENTAMUNG', width: 150,formatter: 'integer',formatoptions: {thousandsSeparator: ",", decimalPlaces: 0}},
            {label: 'TEN_PHONGBAN', name: 'TEN_PHONGBAN', index: 'TEN_PHONGBAN', width: 70, hidden: true},
            {label: 'THOIGIANCHUYENDEN', name: 'THOIGIANCHUYENDEN', index: 'THOIGIANCHUYENDEN', width: 70, hidden: true},
            {label: 'BENH_GUI', name: 'BENH_GUI', index: 'BENH_GUI', width: 0, hidden: true},
            {label: 'T_CHIPHICAO_BV', name: 'T_CHIPHICAO_BV', index: 'T_CHIPHICAO_BV', width: 0, hidden: true},
            {label: 'T_CHIPHICAO_PB_BH', name: 'T_CHIPHICAO_PB_BH', index: 'T_CHIPHICAO_PB_BH', width: 0, hidden: true},
            {label: 'T_CHIPHICAO_PB_BH', name: 'TONGTIEN_BH', index: 'TONGTIEN_BH', width: 0, hidden: true},
            {label: 'SOVAOVIEN_NGT', name: 'SOVAOVIEN_NGT', index: 'SOVAOVIEN_NGT', width: 70, hidden: true},
            {label: 'KHOAPHONG_CHUYENDI', name: 'KHOAPHONG_CHUYENDI', index: 'KHOAPHONG_CHUYENDI', width: 70, hidden: true},
            {label: 'NGAY_RAVIEN', name: 'NGAY_RAVIEN', index: 'NGAY_RAVIEN', width: 70, hidden: true},
            {label: 'EMR', name: 'EMR', index: 'EMR', width: 70, hidden: true},
        ],
        rowNum: 100000,
        ignoreCase: true,
        caption: "Danh sách bệnh nhân",
        afterInsertRow: function(rowid, aData) {
        },
        onSelectRow: function (id) {
            $("#hsba_tabs").tabs("option", "disabled", [1]);
            resetDataBN();
        },
        footerrow: true,
        gridComplete: function () {
            var grid = $("#list_benhnhan");
            var sl = grid.getGridParam("records");
            sl = sl + " bệnh nhân";
            grid.jqGrid("footerData", "set", {TEN_BENH_NHAN_HT: sl});

            var dataGrid = getAllRowDataJqgrid("list_benhnhan")
            var listSovaovien = [];
            dataGrid.forEach(function (item) {
                listSovaovien.push(item.SOVAOVIEN_DT)
            })
            if(listSovaovien.length >0 & !singletonObject.callInit)  {
                getICDHT(listSovaovien);
            }
        },
        onRightClickRow: function (id) {
            if(id) {
                var rowData = getThongtinBnSelected("list_benhnhan");
                $.contextMenu('destroy', '#list_benhnhan tr');
                var items = {
                    "kiemtradulieu": {name: '<p><i class="fa fa-braille text-primary" aria-hidden="true"></i> Kiểm tra dữ liệu</p>'},
                    "dangkylichmo": {name: '<p><i class="fa fa-list text-primary" aria-hidden="true"></i> Đăng ký lịch mổ</p>'},
                    "tt50homnay": {name: '<p><i class="fa fa-file-pdf-o text-primary" aria-hidden="true"></i> Xem TT50</p>'},
                    "xembangke": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem bảng kê</p>'},
                    "logchuyenkhoa": {name: '<p><i class="fa fa-history text-primary" aria-hidden="true"></i> Lịch sử chuyển khoa</p>'},
                    "emr": {name: '<p><i class="fa fa-certificate text-primary" aria-hidden="true"></i> Bệnh án điện tử</p>'},
                    "huyemr": {name: '<p><i class="fa fa-window-close-o text-danger" aria-hidden="true"></i> Hủy bệnh án điện tử</p>'},
                    "xemqr": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem QR</p>'},
                    "capgiuongtangthem": {name: '<p><i class="fa fa-bed text-primary" aria-hidden="true"></i> Dịch vụ tăng thêm</p>'},
                    // "xembangkevp": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem bảng kê viện phí</p>'},
                };
                if(rowData.KHOAPHONG_CHUYENDI) {
                    items = {
                        ...items,
                        "goilaihsba": {name: '<p><i class="fa fa-undo text-primary" aria-hidden="true"></i> Gọi lại HSBA</p>'}
                    }
                    delete items.dangkylichmo;
                }
                if(rowData.EMR == 0) {
                    delete items.huyemr
                }
                if(rowData.EMR == 1) {
                    delete items.emr
                }
                if(rowData.GRV == 3) {
                    items = {
                        ...items,
                        "xemgrv": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem giấy ra viện</p>'},
                    }
                }
                if(rowData.GRV == 5) {
                    items = {
                        ...items,
                        "xemgct": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem giấy chuyển tuyến</p>'},
                    }
                }
                $.contextMenu({
                    selector: '#list_benhnhan tr',
                    reposition : false,
                    callback: function (key, options) {
                        var idWrap = "list_benhnhan_wrap"
                        var rowData = getThongtinRowSelected("list_benhnhan");
                        if(key == 'kiemtradulieu') {
                            setTimeout(function() {
                                showLoaderIntoWrapId(idWrap)
                                kiemtradulieuXuatvien({
                                    ...rowData,
                                    NGAYRAVIEN: moment().format("DD/MM/YYYY HH:mm:ss")
                                })
                                hideLoaderIntoWrapId(idWrap)
                            })

                        }
                        if(key == 'dangkylichmo') {
                            setTimeout(function() {
                                showLoaderIntoWrapId(idWrap)
                                var data = {
                                    action: 'them',
                                    ngayhientai: singletonObject.ngayhientai,
                                    admin: singletonObject.admin,
                                    dvtt: singletonObject.dvtt,
                                    makhoa: singletonObject.makhoa,
                                    maphong: singletonObject.maphong,
                                    userid: singletonObject.userId,
                                    sovaovien: rowData.SOVAOVIEN,
                                    sovaovien_dt: rowData.SOVAOVIEN_DT,
                                    stt_benhan: rowData.STT_BENHAN,
                                    stt_dotdieutri: rowData.STT_DOTDIEUTRI,
                                    ten_phongban: rowData.TEN_PHONGBAN,
                                    ma_benh_nhan: rowData.MA_BENH_NHAN,
                                    icd: rowData.ICD_HT,
                                    ten_benh_nhan: rowData.TEN_BENH_NHAN,
                                    tuoi: rowData.TUOI,
                                    dia_chi: rowData.DIA_CHI,
                                    gioi_tinh: rowData.GIOI_TINH_HT,
                                    sobenhan: rowData.SOBENHAN,
                                    danhsachnhanvienkhoa: singletonObject.danhsachnhanvienkhoa,
                                    danhsachnhanvien: singletonObject.danhsachnhanvien,
                                }
                                initDangKyLichMo(data);
                                hideLoaderIntoWrapId(idWrap);
                            })

                        }
                        if (key == "xembangke") {

                            showLoaderIntoWrapId(idWrap)
                            noitruTaoBangke(rowData, function(data) {
                                if(data && data.ERROR) {
                                    notifiToClient("Red", "Lỗi tạo bảng kê")
                                    return hideLoaderIntoWrapId(idWrap)
                                }
                                xembangkenoitru({
                                    ...rowData
                                }, function() {
                                    hideLoaderIntoWrapId(idWrap)
                                }, function () {
                                    hideLoaderIntoWrapId(idWrap)
                                }, 0)
                            })


                        }
                        if (key == "xembangkevp") {
                            showLoaderIntoWrapId(idWrap)
                            noitruTaoBangke(rowData,function(data) {
                                if(data && data.ERROR) {
                                    notifiToClient("Red", "Lỗi tạo bảng kê")
                                    return hideLoaderIntoWrapId(idWrap)
                                }
                                xembangkenoitru({
                                    ...rowData,
                                }, function() {
                                    hideLoaderIntoWrapId(idWrap)
                                }, function () {
                                    hideLoaderIntoWrapId(idWrap)
                                }, 1)
                            })

                        }
                        if(key == "tt50homnay") {
                            $("#titleModalFormThongtu50").html(rowData.TEN_BENH_NHAN + " - Số bệnh án: " + rowData.SOBENHAN + " - Thông tư 50" );
                            $("#modalFormThongtu50").modal("show");
                        }
                        if (key == "goilaihsba") {
                            var url = "noitru_chuyenkhoa_goilaihsba";
                            var arr = [rowData.STT_LOGKHOAPHONG, singletonObject.dvtt,
                                rowData.STT_BENHAN, rowData.STT_DOTDIEUTRI,
                                singletonObject.makhoa, rowData.KHOAPHONG_CHUYENDI];
                            showLoaderIntoWrapId(idWrap)
                            $.post(url, {url: convertArray(arr)}).done(function (dt) {
                                if (dt == "1") {
                                    notifiToClient("Green","Gọi lại hồ sơ bệnh án thành công");
                                    load_dsbenhnhan();
                                } else {
                                    notifiToClient("Red","Khoa nội trú đã nhận bệnh nhân, vui lòng liên hệ khoa chuyển đi!");
                                }
                            }).fail(function () {
                                notifiToClient("Red","Gọi lại hồ sơ bệnh án không thành công");
                            }).always(function () {
                                hideLoaderIntoWrapId(idWrap)
                            })
                        }
                        if(key == 'logchuyenkhoa') {
                            $("#modalLogThongtinchuyenkhoa").modal("show");
                        }
                        if(key == 'huyemr') {
                            capnhatEMR(rowData, 0)
                        }
                        if(key == 'emr') {
                            capnhatEMR(rowData, 1)
                        }
                        if(key == 'xemqr') {
                            var params = {
                                sobenhan: rowData.SOBENHAN,
                                mabenhnhan: rowData.MA_BENH_NHAN,
                                tenbenhnhan: rowData.TEN_BENH_NHAN,
                                ngaynhapvien: rowData.NGAYGIO_NHAPVIEN,
                            }
                            var url = 'cmu_in_cmu_barcode?type=pdf&' + $.param(params);
                            previewPdfDefaultModal(url, "preview_inbarcode");
                        }

                        if(key == 'xemgrv') {
                            getUrlGiayRaVien({
                                dvtt: singletonObject.dvtt,
                                SOVAOVIEN: rowData.SOVAOVIEN,
                                SOVAOVIEN_DT: rowData.SOVAOVIEN_DT
                            }).then(objReturn => {
                                previewPdfDefaultModal(objReturn.url, "preview_ingrv");
                            })

                        }
                        if (key == 'xemgct') {
                            getUrlGiayChuyenTuyen({
                                dvtt: singletonObject.dvtt,
                                SOVAOVIEN: rowData.SOVAOVIEN,
                                SOVAOVIEN_DT: rowData.SOVAOVIEN_DT
                            }).then(objReturn => {
                                previewPdfDefaultModal(objReturn.url, "preview_ingrv");
                            })
                        }

                        if (key == 'capgiuongtangthem') {
                            $("#modalDSGiuongbenhTangthem").modal("show")
                        }
                    },
                    items: items
                });
            }
        },
        ondblClickRow: function (id) {
            openBenhan()
        }
    });
    $("#list_benhnhan").bind("touchstart", function (e) {
        var $this = $(this), now = new Date().getTime(),
            lastTouchTime = $this.data("lastTouchTime") || now + 1,
            timeInterval = now - lastTouchTime;
        if (timeInterval < 500 && timeInterval > 0) {
            var $tr = $(e.target).closest("tr.jqgrow");
            if ($tr.length > 0) {
                openBenhan()
            } else {

            }
        }
        $this.data("lastTouchTime", now);
    });

    $("#list_benhnhan").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
    $("#loaddsbenhnhan").click(function() {
        singletonObject.callInit = false;
        load_dsbenhnhan();
    })

    $("#thuoc").click(function() {
        loadDsThuocVattuTonghop($("#hsba-tonghop-nghiepvutoathuoc").val(), "list_thuoc")
    })
    $("#hsba-tonghop-thuoc-lammoi").click(function() {
        loadDsThuocVattuTonghop($("#hsba-tonghop-nghiepvutoathuoc").val(), "list_thuoc")
    })
    $("#xetnghiem").click(function() {
        loadDsXetnghiemTonghop()
    })
    $("#chandoanhinhanh").click(function() {
        loadDsCDHATonghop()
    })
    $("#ttpt").click(function() {
        loadDsTTPTTonghop()
    })

    $("#hsba-tonghop-ttpt-lammoi").click(function() {
        loadDsTTPTTonghop();
    })
    $("#khoabs").change(function() {
        loadBsTheokhoaDieutri('bacsidieutri', $("#khoabs").val(), false, singletonObject.userId);

    })
    $("#ttin_dieutri_khoa").change(function() {
        loadBsTheokhoaDieutri('ttin_dieutri_bs', $("#ttin_dieutri_khoa").val(), true, "");
    })
    $("#bacsidieutri").change(function() {
        $("#idbacsidieutri").val($(this).val())
    })
    $("#hsba-taoba-khoa").change(function() {
        loadBsTheokhoaDieutri('hsba-taoba-bacsi', $("#hsba-taoba-khoa").val(), false, "");
    })
    $("#hsba_khoitaobenhan").click(function() {
        var idButton = this.id
        showSelfLoading(idButton)
        if(!$("#hsbaFormkhoitaobenhan").valid()) {
            hideSelfLoading(idButton)
            return;
        }
        $.post("cmu_post_KHOITAO_VOBENHAN_MOI", {
            url: [
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                thongtinhsba.thongtinbn.MA_BENH_NHAN,
                thongtinhsba.thongtinbn.SOBENHAN,
                $("#hsba-taoba-loaibenhan").val(),
                singletonObject.makhoa,
                singletonObject.userId,
                $("#hsba-taoba-bacsi").val()
            ].join("```")
        }).done(function(data) {
            hideSelfLoading(idButton)
            $("#modalHsbaTaobenhan").modal("hide")
            initThongtinBenhnhan();
            if(data <= 0) {
                notifiToClient("Green", "Khởi tạo bệnh án không thành công");
            } else {
                notifiToClient("Green", "Tạo bệnh án thành công");
                luuLogHSBATheoBN({
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: LOGHSBALOAI.BENHAN.KEY,
                    NOIDUNGBANDAU: "",
                    NOIDUNGMOI: "Khởi tạo bệnh án số " + data
                        + " loại " + $("#hsba-taoba-loaibenhan").val()
                        + ", người tạo " + $("#hsba-taoba-bacsi").val(),
                    USERID: singletonObject.userId,
                    ACTION: LOGHSBAACTION.INSERT.KEY
                });
                getThongtinVoBA()
                $.post("cmu_post_CMU_HSBA_TTHC_UPD", {
                    url: [
                        singletonObject.dvtt,
                        data,
                        thongtinhsba.thongtinbn.SOVAOVIEN,
                        thongtinhsba.thongtinbn.SOBENHAN,
                        thongtinhsba.thongtinbn.MA_BENH_NHAN
                    ].join("```")
                })
            }
        }).fail(function() {
            hideSelfLoading(idButton)
            notifiToClient("Red", "Lỗi tạo bệnh án");
        })
    })
    $("#hsbaFormkhoitaobenhan").validate({})

    $("#hsba_dongkhoitaobenhan").click(function() {
        $("#modalHsbaTaobenhan").modal("hide");
        clearDataBNReloadList();
    });

    $('#ttchamsoc-phieukhac').click(function() {
        getLatestPhieuCk();
        loadDsPhieusoketTonghop();
        loadDsPhieusoketTonghop();
        loadDsPhieuhoichanTonghop();
        phieuChuanDoanNguyenNhanTuVongJS.reloadDSPhieuChuanDoanNguyenNhanTuVong();
        baoCaoTuVongJS.reloadDSBaoCaoTuVong();
        phieuKetQuaSangLocBenhLyTimBamSinhJS.reloadDSPhieuKetQuaSangLocBenhLyTimBamSinh();
        phieuTinhDichDoJS.reloadDSPhieuTinhDichDo();
        giayCamKetNamVienDTriNTruJS.reloadDSGiayCamKetNamVienDTriNTru();
        phieuKiemTraBenhAnJS.reloadDSPhieuKiemTraBenhAn();
    });

    $("#ttchamsoc-cacphieukhac").click(function() {
        reloadDsPhieuLoetTiDe();
        // phieuBangTheoDoiCSMeVaSoSinh6HDauSauDeJS.loadDSPhieuTheoDoi6hSauDe();
        phieuBieuDoChuyenDaJS.globalLoadDSBieuDoChuyenDa();
    });

    $("#tdt_thomay_capnhat").click(function(){
        $("#tdt_thomay_thomay").val(todieutriObject.THO_MAY);
        $("#tdt_thomay_chedo").val(todieutriObject.THOMAY_CHEDO);
        $("#tdt_thomay_tanso").val(todieutriObject.THOMAY_TANSO);
        $("#tdt_thomay_ie").val(todieutriObject.THOMAY_IE);
        $("#tdt_thomay_fio2").val(todieutriObject.THOMAY_FIO2);
        $("#tdt_thomay_trigger").val(todieutriObject.THOMAY_PHANTRAM_TRIGGER);
        $("#tdt_thomay_pcpspeep").val(todieutriObject.THOMAY_PC_PS_PEEP);
        $("#tdt_thomay_map").val(todieutriObject.THOMAY_MAP);
        $("#tdt_thomay_mve").val(todieutriObject.THOMAY_MVE);
        $("#modalThoMay").modal("show");
    });

    $("#tdt_thomay_luu").click(function(){
        var idButton = this.id;
        showSelfLoading(idButton);
        $.post("cmu_post", {
            url: [singletonObject.dvtt,
                thongtinhsba.thongtinbn.STT_BENHAN,
                todieutriObject.STT_DOTDIEUTRI,
                todieutriObject.STT_DIEUTRI,
                $("#tdt_thomay_thomay").val(),
                $("#tdt_thomay_chedo").val(),
                $("#tdt_thomay_tanso").val(),
                $("#tdt_thomay_ie").val(),
                $("#tdt_thomay_fio2").val(),
                $("#tdt_thomay_trigger").val(),
                $("#tdt_thomay_pcpspeep").val(),
                $("#tdt_thomay_map").val(),
                $("#tdt_thomay_mve").val(),
                "CMU_TODIEUTRI_THOMAY_UPD"].join("```")
        }).done(function(data) {
            hideSelfLoading(idButton);
            if(data == "1") {
                notifiToClient("Green", "Cập nhật thông tin thở máy thành công");
                $("#modalThoMay").modal("hide");
                todieutriObject.THO_MAY = $("#tdt_thomay_thomay").val();
                todieutriObject.THOMAY_CHEDO = $("#tdt_thomay_chedo").val();
                todieutriObject.THOMAY_TANSO = $("#tdt_thomay_tanso").val();
                todieutriObject.THOMAY_IE = $("#tdt_thomay_ie").val();
                todieutriObject.THOMAY_FIO2 = $("#tdt_thomay_fio2").val();
                todieutriObject.THOMAY_PHANTRAM_TRIGGER = $("#tdt_thomay_trigger").val();
                todieutriObject.THOMAY_PC_PS_PEEP = $("#tdt_thomay_pcpspeep").val();
                todieutriObject.THOMAY_MAP = $("#tdt_thomay_map").val();
                todieutriObject.THOMAY_MVE = $("#tdt_thomay_mve").val();
                loadDataThoMay();
            } else {
                notifiToClient("Red", "Cập nhật thông tin thở máy không thành công");
            }
        }).fail(function() {
            hideSelfLoading(idButton);
            notifiToClient("Red", "Cập nhật lỗi, Vui lòng liên hệ IT");
        });
    });

    $("#tdt_thocpap_capnhat").click(function(){
        $("#tdt_thocpap_p").val(todieutriObject.THOCPAP_P);
        $("#tdt_thocpap_fio2").val(todieutriObject.THOCPAP_FIO2);
        $("#modalThoCPAP").modal("show");
    });

    $("#tdt_thocpap_luu").click(function(){
        var idButton = this.id;
        showSelfLoading(idButton);
        $.post("cmu_post", {
            url: [
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.STT_BENHAN,
                todieutriObject.STT_DOTDIEUTRI,
                todieutriObject.STT_DIEUTRI,
                $("#tdt_thocpap_p").val(),
                $("#tdt_thocpap_fio2").val(),
                "CMU_TODIEUTRI_THOCPAP_UPD"
            ].join("```")
        }).done(function(data) {
            hideSelfLoading(idButton);
            if (data == "1") {
                notifiToClient("Green", "Cập nhật thông tin thở CPAP thành công");
                $("#modalThoCPAP").modal("hide");
                todieutriObject.THOCPAP_P = $("#tdt_thocpap_p").val();
                todieutriObject.THOCPAP_FIO2 = $("#tdt_thocpap_fio2").val();
                loadDataThoCPAP();
            }
            else {
                notifiToClient("Red", "Cập nhật thông tin thở CPAP không thành công");
            }
        }).fail(function() {
            hideSelfLoading(idButton);
            notifiToClient("Red", "Cập nhật lỗi, Vui lòng liên hệ IT");
        });
    });

    $("#tdt_thooxy_capnhat").click(function(){
        $("#tdt_thooxy_soluong").val(todieutriObject.THOOXY_SOLUONG);
        $("#tdt_thooxy_loaitd").val(todieutriObject.THOOXY_LOAITD);
        $("#modalThoOXY").modal("show");
    });

    $("#tdt_thooxy_luu").click(function(){
        var idButton = this.id;
        showSelfLoading(idButton);
        $.post("cmu_post", {
            url: [
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.STT_BENHAN,
                todieutriObject.STT_DOTDIEUTRI,
                todieutriObject.STT_DIEUTRI,
                $("#tdt_thooxy_soluong").val(),
                $("#tdt_thooxy_loaitd").val(),
                "CMU_TODIEUTRI_THOOXY_UPD_V2"
            ].join("```")
        }).done(function(data) {
            hideSelfLoading(idButton);
            if (data == "1") {
                notifiToClient("Green", "Cập nhật thông tin thở OXY thành công");
                $("#modalThoOXY").modal("hide");
                todieutriObject.THOOXY_SOLUONG = $("#tdt_thooxy_soluong").val();
                todieutriObject.THOOXY_LOAITD = $("#tdt_thooxy_loaitd").val();
                loadDataThoOXY();
            }
            else {
                notifiToClient("Red", "Cập nhật thông tin thở OXY không thành công");
            }
        }).fail(function() {
            hideSelfLoading(idButton);
            notifiToClient("Red", "Cập nhật lỗi, Vui lòng liên hệ IT");
        });
    });

    $("#hsba-tthc-tab").click(function() {
        $("#hsba_vba_trang1").click();
    });

    $(document).on('show.bs.modal', '.modal', function() {
        var zIndex = 1040 + 10 * $('.modal:visible').length;
        $(this).css('z-index', zIndex);
        setTimeout(() => $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack'));
    });

    setTimeout(function() {
        initData();
    })
    function getICDHT(listSovaovien) {
        var grid = $("#list_benhnhan");
        var ids = grid.jqGrid('getDataIDs');
        var idWrap = 'list_benhnhan_wrap'
        showLoaderIntoWrapId(idWrap);
        $.get("cmu_list_CMU_DSLAYICD_MOINHAT?url="+
            convertArray([singletonObject.dvtt, listSovaovien.join(";")])).done(function (data) {
            for (var i = 0; i < ids.length; i++) {
                var rowId = ids[i];
                var rowData = grid.jqGrid('getRowData', rowId);

                data.forEach(function(obj) {
                    if(rowData.SOVAOVIEN == obj.SOVAOVIEN && rowData.SOVAOVIEN_DT == obj.SOVAOVIEN_DT) {

                        grid.jqGrid("setCell", rowId, "ICD_HT", obj.TENBENH);
                        grid.jqGrid("setCell", rowId, "NGAYGIO_NHAPVIEN", obj.NGAYGIO_NHAPVIEN);
                        grid.jqGrid("setCell", rowId, "SOGIUONG", obj.SOGIUONG);
                        grid.jqGrid("setCell", rowId, "TEN_PHONG", obj.TEN_PHONG);
                        var grvctht = '';
                        var attrColorGRVCT = {
                            background: 'transparent',
                            color: 'white',
                            'font-weight': 600
                        }
                        switch(obj.GRV) {
                            case '1':
                                grvctht = 'GRV (Trưởng khoa chưa ký)';
                                attrColorGRVCT.color = 'red';
                                break;
                            case '2':
                                grvctht = 'GRV (BGĐ chưa ký)';
                                attrColorGRVCT.color = 'red';
                                break;
                            case '3':
                                grvctht = 'GRV (Đã ký)';
                                attrColorGRVCT.color = 'green';
                                break;
                            case '4':
                                grvctht = 'GCT (BGĐ chưa ký)';
                                attrColorGRVCT.color = 'red';
                                break;
                            case '5':
                                grvctht = 'GCT (Đã ký)';
                                attrColorGRVCT.color = 'green';
                                break;
                            default:
                                grvctht = '';
                                break;
                        }
                        grid.jqGrid("setCell", rowId, "GRV_HT", grvctht, attrColorGRVCT);
                        grid.jqGrid("setCell", rowId, "GRV", obj.GRV);

                        var attrColor = {
                            background: 'transparent',
                            color: 'black'
                        }
                        if(obj.CANDOICHIPHI < 0)  {
                            attrColor.background = 'red';
                            attrColor.color = 'white';
                            attrColor['font-weight'] = '600';
                        }
                        var attrColorBA = {
                            color: 'green',
                            'font-weight': '600'
                        }
                        if(!obj.MABACSILAMBENHAN) {
                            attrColorBA.color = 'red';
                        }
                        grid.jqGrid("setCell", rowId, "MABACSILAMBENHAN", obj.MABACSILAMBENHAN, attrColorBA);
                        grid.jqGrid("setCell", rowId, "CANDOICHIPHI", obj.CANDOICHIPHI, attrColor);
                        grid.jqGrid("setCell", rowId, "TONGCHIPHI", obj.TONGCHIPHI);
                        grid.jqGrid("setCell", rowId, "SOTIENTAMUNG", obj.TONGTAMUNG);
                        grid.jqGrid("setCell", rowId, "NGAY_RAVIEN", obj.NGAY_RAVIEN);
                    }
                })

            }

        }).always(function() {
            hideLoaderIntoWrapId(idWrap);
            singletonObject.callInit = true;
        })
    }

    function initThongtinBenhnhan() {
        showLoaderIntoWrapId("list_benhnhan_wrap");
        getThongtinBNChitiet(function() {
            hideLoaderIntoWrapId("list_benhnhan_wrap");
            $("#hsba_tabs").tabs("enable", 1);
            $("#hsba_tabs").tabs("option", "active", 1);
            $("#hsba-tthc-tab").click();
            $("#hsba_hoten").html(thongtinhsba.thongtinbn.TEN_BENH_NHAN)
            $("#hsba_sobenhan").html(thongtinhsba.thongtinbn.SOBENHAN)
            $("#hsba_mabenhnhan").html(thongtinhsba.thongtinbn.MA_BENH_NHAN)
            $("#hsba_ngayvaovien").html(thongtinhsba.thongtinbn.NGAYGIO_NHAPVIEN)
            // $("#hsba_ngayvaokhoa").html(thongtinhsba.thongtinbn.THOIGIANCHUYENDEN)
            instanceGridTodieutri();
            instanceGridThuoc();
            instanceGridPhieuchamsoc();
            instanceGridXetnghiem();
            instanceGridCdha();
            instanceGridTTPT();
            initDSDenghitamung();
            initDSTamung();
            initGiuongBenh();
            initGridPhieuchucnangsong();
            loadTTPhieuDanhGiaDinhDuong();
            loadPhieuKhamCk();
            resetData();
            $.post("noitru_danhsach_giuongbenh", {
                dvtt: singletonObject.dvtt,
                maphongban: singletonObject.makhoa,
                maphongbenh: singletonObject.maphongbenh,
                sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                sovaovien_dt: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            }).done(function(data) {
                singletonObject.danhsachgiuongbenh = data;
            })

            $.post("noitru_danhsach_giuongbenh_banngay", {
                dvtt: singletonObject.dvtt,
                maphongban: singletonObject.makhoa,
                maphongbenh: singletonObject.maphongbenh,
                sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                sovaovien_dt: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            }).done(function(data) {
                singletonObject.danhsachgiuongbenhbanngay = data;
                $(".giuongbenhbanngay-show").hide();
                if (singletonObject.danhsachgiuongbenhbanngay.length > 0) {
                    $(".giuongbenhbanngay-show").show();
                }
            })
        }, function() {
            hideLoaderIntoWrapId("list_benhnhan_wrap");
        });

    }
    function instanceGridTodieutri() {
        if(!$("#list_todieutri")[0].grid) {
            $("#list_todieutri").jqGrid({
                url: '',
                datatype: "local",
                loadonce: true,
                height: 450,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "ID_DIEUTRI", name: 'ID_DIEUTRI', index: 'ID_DIEUTRI', width: 10, hidden: true},
                    {label: "SOVAOVIEN_DT", name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', width: 10, hidden: true},
                    {label: "SOVAOVIEN_", name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 10, hidden: true},
                    {label: "STT", name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 40, align: "center"},
                    {label: "Thời gian", name: 'NGAYGIO', index: 'NGAYGIO', width: 110, hidden: true},
                    {label: "Thời gian", name: 'NGAYGIO_HT', index: 'NGAYGIO_HT', width: 110,
                        align: "center",
                        formatter: function (cellvalue, options, rowObject) {
                            var iconSign = "";
                            var moikham =  rowObject.YLENHMOIKHAM != 0 ? "<strong class='text-primary'>Mời khám</strong>":"";
                            if(!rowObject.KEYSIGN) {
                                iconSign = "<p style='text-align: center;font-size: 20px;margin-bottom: 4px;margin-top:4px'><i class='fa fa-exclamation-circle text-danger'></i></p>"
                            }
                            if(rowObject.KEYSIGN) {
                                iconSign = "<p style='text-align: center;font-size: 20px;margin-bottom: 4px;margin-top:4px'><i class='fa fa-check-square-o text-primary'></i></p>"
                            }
                            var phauthuat = '';
                            if(rowObject.PHAUTHUAT > 0) {
                                phauthuat = "<p class='text-primary' style='text-align: center;font-weight: bold;margin-bottom: 4px;margin-top:4px'> Phẫu thuật</p>"
                            }
                            return iconSign+'<p style="margin-bottom: 4px">' + rowObject.NGAYGIO + '</p>' + phauthuat + moikham;
                        },
                        cellattr: function (rowId, val, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';

                        }
                    },
                    {
                        label: "Trạng thái",
                        name: 'TRANG_THAI_HT',
                        index: 'TRANG_THAI_HT',
                        align: 'center',
                        width: 70,
                        cellattr: function (cellvalue, options, rowObject) {
                            var color = "black";
                            var fontWeight = "500";
                            if(rowObject.TRANG_THAI.toString() === "2") {
                                fontWeight = 900;
                                color = 'red';
                            }
                            if(rowObject.TRANG_THAI.toString() === "3") {
                                fontWeight = '900';
                                color = 'blue';
                            }
                            if (rowObject.TRANG_THAI.toString() === "4") {
                                fontWeight =  900;
                                color = 'CC00CC';
                            }
                            return 'style="font-weight: ' + fontWeight + '; color: ' + color + ';white-space: normal;"';
                        },
                        formatter: function (cellvalue, options, rowObject) {
                            var text = "";
                            if (rowObject.TRANG_THAI.toString() === "2") {
                                text = "Đã lãnh thuốc"
                            } else if (rowObject.TRANG_THAI.toString() === "3") {
                                text = "Cập nhật hoàn trả";
                            } else if (rowObject.TRANG_THAI.toString() === "4") {
                                text = "Đã hoàn trả"
                            } else {
                                text = "Mới lập"
                            }
                            return text;
                        }
                    },
                    {label: "Diễn biến",name: 'DIEN_BIEN_BENH', index: 'DIEN_BIEN_BENH', width: 200,
                        cellattr: function (rowId, val, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';

                        }
                    },
                    {label: "Y lệnh",name: 'Y_LENH', index: 'Y_LENH', width: 240,
                        cellattr: function (rowId, val, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';

                        }
                    },
                    {label: "Bác sĩ điều trị",name: 'TEN_NHANVIEN', index: 'TEN_NHANVIEN', width: 120,
                        cellattr: function (rowId, val, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';

                        }},

                    {label: "Khoa", name: 'TEN_PHONGBAN', index: 'TEN_PHONGBAN', width: 150,
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "Người tạo",name: 'TEN_NHANVIEN_CD', index: 'TEN_NHANVIEN_CD', width: 120,
                        cellattr: function (rowId, val, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';

                        }
                    },
                    {label: "NGUOITAO",name: 'NGUOITAO', index: 'NGUOITAO', hidden: true},
                    {label: "KHOALAP", name: 'KHOALAP', index: 'KHOALAP', hidden: true},

                    {label: "Đợt",name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', width: 50},
                    {label: "STT_BENHAN", name: 'STT_BENHAN', index: 'STT_BENHAN', width: 120, hidden: true},
                    {label: "TRANG_THAI", name: 'TRANG_THAI', index: 'TRANG_THAI', width: 120, hidden: true},
                    {label: "KEYSIGN", name: 'KEYSIGN', index: 'KEYSIGN', width: 120, hidden: true},
                    {label: "PHAUTHUAT", name: 'PHAUTHUAT', index: 'PHAUTHUAT', width: 120, hidden: true},
                    {label: "TDT_NGUOILAP", name: 'TDT_NGUOILAP', index: 'TDT_NGUOILAP', width: 120, hidden: true},
                    {label: "YLENHMOIKHAM", name: 'YLENHMOIKHAM', index: 'YLENHMOIKHAM', width: 120, hidden: true},

                ],
                rowNum: 1000,
                caption: "",
                gridComplete: function() {
                    if ("0" == "1" && laySoDotDieuTri() > 1) {
                        $.contextMenu({
                            selector: '#hsba_list_phieudieutri tr.jqgrow',
                            build: function ($trigger, e) {
                                var rowId = $(e.target).closest("tr.jqgrow").attr("id");
                                if ($.inArray(rowId, $("#hsba_list_phieudieutri").getGridParam('selarrrow')) == -1)
                                    $('#hsba_list_phieudieutri').setSelection(rowId, true);
                                return {
                                    callback: function (key, options) {
                                        var ids = $("#hsba_list_phieudieutri").getGridParam('selarrrow');
                                        var rowsData = [];
                                        var dsIdDieuTri = [];
                                        ids.forEach(function(id, index) {
                                            rowsData.push($("#hsba_list_phieudieutri").getRowData(id));
                                            dsIdDieuTri.push($("#hsba_list_phieudieutri").getCell(id, "ID_DIEUTRI"))
                                        });
                                        if (key == "chuyenphieudieutri") {
                                            showDialogChuyenPhieuDieuTri(rowsData, dsIdDieuTri);
                                        }
                                    },
                                    items: {
                                        chuyenphieudieutri: {
                                            name: "Chuyển phiếu điều trị"
                                        }
                                    }
                                };
                            }
                        });
                    }
                },
                onRightClickRow: function(id) {
                    if (id) {
                        $.contextMenu('destroy', '#list_todieutri tr');
                        var rowData = getThongtinRowSelected("list_todieutri");
                        var items = {
                            "chinhsua": {name: '<p><i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Chỉnh sửa</p>'},
                            "thaydoibsdieutri": {name: '<p><i class="fa fa-address-card-o text-primary" aria-hidden="true"></i> Thay đổi BS điều trị</p>'},
                            "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                            "xemchitiet": {name: '<p><i class="fa fa-list-ul text-primary" aria-hidden="true"></i> Xem chi tiết</p>'},
                            "xoa": {name: '<p><i class="fa fa-trash-o red-text" aria-hidden="true"></i> Xóa</p>'}
                        }
                        if(rowData.TRANG_THAI == 1) {
                            items = {
                                "bosungvattu": {name: '<p><i class="fa fa-calendar-plus-o text-primary" aria-hidden="true"></i> Bổ sung vật tư</p>'},
                                ...items,
                            }
                        }

                        if(rowData.TRANG_THAI == 2) {
                            items = {
                                "capnhathoantra": {name: '<p><i class="fa fa-minus-square-o text-primary" aria-hidden="true"></i> Cập nhật hoàn trả</p>'},
                                ...items,
                            }
                        }
                        if(rowData.TRANG_THAI == 3) {
                            items = {
                                "chitiethoantra": {name: '<p><i class="fa fa-bars text-primary" aria-hidden="true"></i> Chi tiết hoàn trả</p>'},
                                "huycapnhathoantra": {name: '<p><i class="fa fa-eraser text-primary" aria-hidden="true"></i> Hủy hoàn trả</p>'},
                                ...items,
                            }
                        }
                        if(rowData.TRANG_THAI == 4) {
                            items = {
                                "chitiethoantra": {name: '<p><i class="fa fa-bars text-primary" aria-hidden="true"></i> Chi tiết hoàn trả</p>'},
                                ...items,
                            }
                        }
                        if ( thongtinhsba.thongtinbn.DSDOTDIEUTRI.length > 1) {
                            items = {
                                ...items,
                                "chuyendotdieutri": {name: '<p><i class="fa fa-exchange text-primary" aria-hidden="true"></i> Chuyển đợt điều trị</p>'},
                            }
                        }
                        if(rowData.PHAUTHUAT > 0 ) {
                            delete items.chinhsua
                        }
                        if(!rowData.KEYSIGN) {
                            items = {
                                "kyso": {name: '<p><i class="fa fa-key text-primary" aria-hidden="true"></i> Ký số tất cả</p>'},
                                ...items
                            }
                        }
                        if(rowData.KEYSIGN) {
                            items = {
                                ...items,
                                "huykyso": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số</p>'},
                            }
                        }
                        $.contextMenu({
                            selector: '#list_todieutri tr',
                            reposition : false,
                            callback: function (key, options) {

                                if (key == "chinhsua") {
                                    var id = $("#list_todieutri").jqGrid('getGridParam', 'selrow');
                                    var rowData = $("#list_todieutri").jqGrid('getRowData', id);
                                    if(rowData.KHOALAP != singletonObject.makhoa) {
                                        return notifiToClient("Red", "Bạn không có quyền chỉnh sửa phiếu điều trị này");
                                    }
                                    showLoaderIntoWrapId("hsba_list_todieutri");
                                    $.get("cmu_list_HSBA_CMU_TDT_GETBYID?url="+convertArray([
                                        singletonObject.dvtt,
                                        rowData.ID_DIEUTRI,
                                        rowData.SOVAOVIEN,
                                        rowData.SOVAOVIEN_DT
                                    ])).always(function (data) {
                                        hideLoaderIntoWrapId("hsba_list_todieutri");
                                    }).done(function (resData) {
                                        var data = resData[0];
                                        todieutriObject = data;
                                        if(data.TDT_NGUOILAP != singletonObject.userId && singletonObject.userId != data.NGUOITAO) {
                                            return notifiToClient("Red", "Bạn không có quyền chỉnh sửa phiếu điều trị này");
                                        }
                                        todieutriObject['NGAYGIO'] = rowData.NGAYGIO
                                        $("#ttdtFormthongtinsinhhieu .clear-text").val("")
                                        showModalTodieutri(thongtinhsba.thongtinbn)
                                        $("#tdt_inputnhiptho").val(data.NHIPTHO);
                                        $("#tdt_inputmach").val(data.MACH);
                                        $("#tdt_inputchieucao").val(data.CHIEUCAO);
                                        $("#tdt_inputnhietdo").val(data.NHIETDO);
                                        $("#tdt_inputhuyetap_tren").val(data.HUYETAPTREN);
                                        $("#tdt_inputhuyetap_duoi").val(data.HUYETAPDUOI);
                                        $("#tdt_inputcannang").val(data.CANNANG);
                                        $("#tdt_inputspo2").val(data.SPO2);
                                        $("#tdt_ylenh").val(data.TDT_YLENH);
                                        $("#tdt_dienbien").val(data.TDT_DIENBIENBENH);
                                        $("#tdt_giaidoanbenh").val(data.GIAI_DOAN_BENH);
                                        $("#tdt_input_icd_ma_yhct").val(data.MA_BENH_YHCT);
                                        loadDataThoMay();
                                        loadDataThoCPAP();
                                        loadDataThoOXY();
                                        var time = moment().format('HH') + ":"+moment().format('mm');
                                        $("#tdt_xn_giochidinh").val(time);
                                        $("#tdt_cdha_giochidinh").val(time);
                                        $("#tdt_ttpt_giochidinh").val(time);
                                        $("#tdt-dieutri").click()
                                        checkKysotodieutri();
                                    }).fail(function (data) {
                                        notifiToClient("Red", "Lỗi lấy dữ liệu phiếu điều trị");
                                    })

                                }
                                if (key == "xoa") {
                                    var id = $("#list_todieutri").jqGrid('getGridParam', 'selrow');
                                    var rowData = $("#list_todieutri").jqGrid('getRowData', id);
                                    if(rowData.KHOALAP != singletonObject.makhoa) {
                                        return notifiToClient("Red", "Bạn không có quyền xóa  phiếu điều trị này");
                                    }
                                    showLoaderIntoWrapId("hsba_list_todieutri");
                                    $.get("cmu_list_HSBA_CMU_TDT_GETBYID?url="+convertArray([
                                        singletonObject.dvtt,
                                        rowData.ID_DIEUTRI,
                                        rowData.SOVAOVIEN,
                                        rowData.SOVAOVIEN_DT
                                    ])).always(function (data) {
                                        hideLoaderIntoWrapId("hsba_list_todieutri");
                                    }).done(function (resData) {
                                        var data = resData[0];
                                        todieutriObject = data;
                                        if(data.TDT_NGUOILAP != singletonObject.userId && singletonObject.userId != data.NGUOITAO) {
                                            return notifiToClient("Red", "Bạn không có quyền chỉnh sửa phiếu điều trị này");
                                        }
                                        var confirmModal = $.confirm({
                                            title: 'Xác nhận!',
                                            type: 'orange',
                                            content: "Bạn có chắc chắn muốn xóa phiếu điều trị này?",
                                            buttons: {
                                                warning: {
                                                    btnClass: 'btn-warning',
                                                    text: "Tiếp tục",
                                                    action: function(){
                                                        confirmModal.close();
                                                        showLoaderIntoWrapId("hsba_list_todieutri");
                                                        xoaphieudieutri();
                                                    }
                                                },
                                                cancel: function () {

                                                }
                                            }
                                        });

                                    }).fail(function (data) {
                                        notifiToClient("Red", "Lỗi lấy dữ liệu phiếu điều trị");
                                    })

                                }
                                if(key == 'capnhathoantra') {
                                    var id = $("#list_todieutri").jqGrid('getGridParam', 'selrow');
                                    if (id) {

                                        var ret = $("#list_todieutri").jqGrid('getRowData', id);
                                        if(ret.KHOALAP != singletonObject.makhoa) {
                                            return notifiToClient("Red", "Bạn không có quyền cập nhật hoàn trả phiếu điều trị này");
                                        }
                                        var confirmModal = $.confirm({
                                            title: 'Xác nhận!',
                                            type: 'orange',
                                            content: "Bạn đã chắc chắn cập nhật hoàn trả?",
                                            buttons: {
                                                warning: {
                                                    btnClass: 'btn-warning',
                                                    text: "Tiếp tục",
                                                    action: function(){
                                                        confirmModal.close();
                                                        var idList = "hsba_list_todieutri"
                                                        showLoaderIntoWrapId(idList);
                                                        try {
                                                            var resthanhtoan = $.ajax({
                                                                url: "cmu_post",
                                                                type: 'POST',
                                                                async: false,
                                                                data: {
                                                                    url: [
                                                                        singletonObject.dvtt,
                                                                        ret.MA_TOA_THUOC,
                                                                        ret.STT_DIEUTRI,
                                                                        ret.SOVAOVIEN,
                                                                        ret.SOVAOVIEN_DT,
                                                                        'CMU_KIEMTRATHUOCTHANHTOAN'
                                                                    ].join('```')
                                                                }
                                                            }).responseText;
                                                            if (resthanhtoan != "0") {
                                                                notifiToClient("Red","Thuốc đã thanh toán. Không thể cập nhật");
                                                                hideLoaderIntoWrapId(idList);
                                                            }
                                                            var url = "noitru_update_tt_phieudt";
                                                            var arr = [ret.STT_DIEUTRI, singletonObject.dvtt, ret.STT_DIEUTRI, ret.STT_BENHAN, ret.STT_DOTDIEUTRI, "3"];
                                                            var resPhieudt = $.ajax({
                                                                url: url,
                                                                type: 'POST',
                                                                async: false,
                                                                data: {
                                                                    url: convertArray(arr)
                                                                }
                                                            }).responseText;
                                                            if (resPhieudt == "100") {
                                                                notifiToClient("Red","Đã chốt số liệu dược. Không thể cập nhật");
                                                            } else if (resPhieudt == "0") {
                                                                notifiToClient("Green","Cập nhật trạng thái thành công");
                                                                openModalhoantrathuoc(ret.ID_DIEUTRI, true)
                                                            } else {
                                                                notifiToClient("Red","Không thể cập nhật trạng thái của phiếu do khoa dược chưa duyệt phiếu dự trù");
                                                            }
                                                            loadDsToDieutri()
                                                            hideLoaderIntoWrapId(idList);
                                                        } catch (e) {
                                                            console.log("e", e)
                                                            notifiToClient("Red", "Lỗi cập nhật hoàn trả");
                                                            hideLoaderIntoWrapId(idList);
                                                        }
                                                    }
                                                },
                                                cancel: function () {
                                                }
                                            }
                                        });

                                    }
                                }
                                if(key == 'huycapnhathoantra') {
                                    var id = $("#list_todieutri").jqGrid('getGridParam', 'selrow');
                                    if (!id) {
                                        return false;
                                    }
                                    var ret = $("#list_todieutri").jqGrid('getRowData', id);
                                    if(ret.KHOALAP != singletonObject.makhoa) {
                                        return notifiToClient("Red", "Bạn không có quyền cập nhật phiếu của khoa khác");
                                    }
                                    var confirmModal = $.confirm({
                                        title: 'Xác nhận!',
                                        type: 'orange',
                                        content: "Bạn đã chắc chắn muốn hủy hoàn trả?",
                                        buttons: {
                                            warning: {
                                                btnClass: 'btn-warning',
                                                text: "Tiếp tục",
                                                action: function(){
                                                    confirmModal.close();
                                                    var idList = "hsba_list_todieutri"
                                                    showLoaderIntoWrapId(idList);
                                                    try {
                                                        var url = "noitru_huy_tt_phieudtsai";
                                                        var arr = [ret.STT_DIEUTRI, singletonObject.dvtt, ret.STT_DIEUTRI, ret.STT_BENHAN, ret.STT_DOTDIEUTRI, "2"];
                                                        var resPhieudt = $.ajax({
                                                            url: url,
                                                            type: 'POST',
                                                            async: false,
                                                            data: {
                                                                url: convertArray(arr)
                                                            }
                                                        }).responseText;
                                                        if (resPhieudt == "100") {
                                                            notifiToClient("Red","Đã chốt số liệu dược. Không thể cập nhật");
                                                        } else if (resPhieudt == "0") {
                                                            notifiToClient("Green","Cập nhật trạng thái thành công");
                                                        } else {
                                                            notifiToClient("Red","Thuốc đã được tổng hợp hoàn trả hoặc đang chờ duyệt. Không thể cập nhật");
                                                        }
                                                        loadDsToDieutri()
                                                        hideLoaderIntoWrapId(idList);

                                                    } catch (e) {
                                                        console.log("e", e)
                                                        notifiToClient("Red", "Lỗi cập nhật hoàn trả");
                                                        hideLoaderIntoWrapId(idList);
                                                    }
                                                }
                                            },
                                            cancel: function () {
                                            }
                                        }
                                    });
                                }
                                if(key == 'xem') {
                                    xemToDieuTriLe();
                                }
                                if(key == 'chitiethoantra') {
                                    var rowData = getThongtinRowSelected("list_todieutri")
                                    if(rowData.KHOALAP != singletonObject.makhoa) {
                                        return notifiToClient("Red", "Bạn không có quyền cập nhật hoàn trả phiếu điều trị này");
                                    }
                                    openModalhoantrathuoc(rowData.ID_DIEUTRI, true)
                                }
                                if(key == 'thaydoibsdieutri') {
                                    var rowData = getThongtinRowSelected("list_todieutri")
                                    if(rowData.KHOALAP != singletonObject.makhoa || singletonObject.admin != 1) {
                                        return notifiToClient("Red", "Bạn không có quyền cập nhật phiếu điều trị này");
                                    }
                                    if(rowData.STT_DOTDIEUTRI != thongtinhsba.thongtinbn.STT_DOTDIEUTRI) {
                                        return notifiToClient("Red", "Phiếu điều trị này đã kết thúc không thể chỉnh sửa");
                                    }
                                    initSelect2IfnotIntance("tdt-khoabs-change", singletonObject.danhsachphongban, "MAKHOA", "TENKHOA",
                                        true, false, singletonObject.makhoa, true)
                                    $("#modalThaydoibsdieutri").modal("show")
                                }

                                if(key == 'bosungvattu') {
                                    var rowData = getThongtinRowSelected("list_todieutri")
                                    if(rowData.KHOALAP != singletonObject.makhoa) {
                                        return notifiToClient("Red", "Bạn không có quyền chỉnh sửa phiếu điều trị này");
                                    }
                                    showLoaderIntoWrapId("hsba_list_todieutri");
                                    $.get("cmu_list_HSBA_CMU_TDT_GETBYID?url="+convertArray([
                                        singletonObject.dvtt,
                                        rowData.ID_DIEUTRI,
                                        rowData.SOVAOVIEN,
                                        rowData.SOVAOVIEN_DT
                                    ])).always(function (data) {
                                        hideLoaderIntoWrapId("hsba_list_todieutri");
                                    }).done(function (resData) {
                                        todieutriObject = resData[0];
                                        todieutriObject['NGAYGIO'] = rowData.NGAYGIO
                                        $("#ttdtFormthongtinsinhhieu .clear-text").val("")
                                        $("#titleModalToavattunoitru").html(thongtinhsba.thongtinbn.TEN_BENH_NHAN +
                                            " - Số bệnh án: " + thongtinhsba.thongtinbn.SOBENHAN
                                            + " - Tờ điều trị số: "+ todieutriObject.STT_DIEUTRI + " - " + todieutriObject.NGAYGIO);
                                        $("#modalToavattunoitru").modal("show")
                                    }).fail(function (data) {
                                        notifiToClient("Red", "Lỗi lấy dữ liệu phiếu điều trị");
                                    })
                                }

                                if(key == 'chuyendotdieutri') {
                                    var rowData = getThongtinRowSelected("list_todieutri")
                                    $("#tdt-dotdieutri-hientai").val(rowData.STT_DOTDIEUTRI)
                                    $("#tdt-list-dotdieutri").empty()
                                    thongtinhsba.thongtinbn.DSDOTDIEUTRI.forEach(function (item) {
                                        if(item.STT_DOTDIEUTRI != rowData.STT_DOTDIEUTRI) {
                                            $("#tdt-list-dotdieutri").append("<option value='"+item.STT_DOTDIEUTRI+"'>"+item.STT_DOTDIEUTRI+"</option>")
                                        }
                                    })
                                    $("#modalChuyenphieudieutri").modal("show")
                                    addTextTitleModal("titleModalChuyenphieudieutri", "Số phiếu: "+ rowData.STT_DIEUTRI + " - "+ rowData.NGAYGIO)
                                }
                                if(key == 'xemchitiet') {
                                    $("#modalXemchitietTodieutri").modal("show")
                                    var rowData = getThongtinRowSelected("list_todieutri")
                                    addTextTitleModal("titleModalXemchitietTodieutri", "Số phiếu: "+ rowData.STT_DIEUTRI + " - "+ rowData.NGAYGIO)
                                }

                                if(key == 'kyso') {
                                    var rowData = getThongtinRowSelected("list_todieutri");
                                    if(rowData.TDT_NGUOILAP != singletonObject.userId) {
                                        return notifiToClient("Red", "Bạn không có quyền ký số phiếu điều trị này");
                                    }

                                    $.loadDSTatcaPhieuTDT(rowData);
                                }
                                if(key == 'huykyso') {
                                    var rowData = getThongtinRowSelected("list_todieutri");
                                    var idWrapper = "hsba_list_todieutri";
                                    showLoaderIntoWrapId(idWrapper);
                                    $.get("cmu_list_HSBA_CMU_TDT_GETBYID?url="+convertArray([
                                        singletonObject.dvtt,
                                        rowData.ID_DIEUTRI,
                                        rowData.SOVAOVIEN,
                                        rowData.SOVAOVIEN_DT
                                    ])).always(function (data) {
                                        hideLoaderIntoWrapId(idWrapper);
                                    }).done(function (resData) {
                                        var data = resData[0];
                                        todieutriObject = data;
                                        if(data.TDT_NGUOILAP != singletonObject.userId) {
                                            return notifiToClient("Red", "Bạn không có quyền hủy kyý số phiếu điều trị này");
                                        }
                                        confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                            showLoaderIntoWrapId(idWrapper)
                                            huykysoFilesign769("TODIEUTRI_NOITRU",
                                                rowData.ID_DIEUTRI, singletonObject.userId, singletonObject.dvtt,
                                                rowData.SOVAOVIEN, rowData.SOVAOVIEN_DT, -1, function(data) {

                                                    $("#ttin_dieutri_btnlammoi").click();
                                                    if(data && data.ERROR) {
                                                        notifiToClient("Red", "Hủy ký số thất bại");
                                                    }
                                                    hideLoaderIntoWrapId(idWrapper)

                                                })
                                        })
                                    }).fail(function (data) {
                                        notifiToClient("Red", "Lỗi lấy dữ liệu phiếu điều trị");
                                    })
                                }
                            },
                            items: items
                        });

                    }

                }
            });
            $("#list_todieutri").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
        }

    }
    function instanceGridThuoc() {
        if(!$("#list_thuoc")[0].grid) {
            $("#list_thuoc").jqGrid({
                //url: 'chitiettoathuocngoatru?matt=' + $("#lskb_matoathuoc").val() + "&nghiepvu=ngoaitru_toathuoc&dvtt=96145",
                datatype: "local",
                loadonce: false,
                height: 450,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "STT_DOTDIEUTRI", name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', width: 10},
                    {label: "TEN_PHONGBAN", name: 'TEN_PHONGBAN', index: 'TEN_PHONGBAN', width: 10},
                    {label: "NGAYLAP", name: 'NGAYLAP', index: 'NGAYLAP', width: 10},
                    {label: "STT_DIEUTRI", name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 10},
                    {label: "Toa", name: 'MOTA_NGHIEPVU', index: 'MOTA_NGHIEPVU', width: 100},
                    {label: "Ngày y lệnh", name: 'NGAY_YL', index: 'NGAY_YL', width: 120},
                    {label: "Tên thương mại", name: 'TEN_VAT_TU', index: 'TEN_VAT_TU', width: 100},
                    {label: "Hoạt chất", name: 'HOAT_CHAT', index: 'HOAT_CHAT', width: 130},
                    {label: "DVT", name: 'DVT', index: 'DVT', width: 50},
                    {label: "Số ngày uống", name: 'SO_NGAY_UONG', index: 'SO_NGAY_UONG', width: 60},
                    {label: "Sáng", name: 'SANG_UONG', index: 'SANG_UONG', width: 40},
                    {label: "Trưa", name: 'TRUA_UONG', index: 'TRUA_UONG', width: 40},
                    {label: "Chiều", name: 'CHIEU_UONG', index: 'CHIEU_UONG', width: 40},
                    {label: "Tối", name: 'TOI_UONG', index: 'TOI_UONG', width: 40},
                    {label: "Số lượng", name: "SO_LUONG", index: "SO_LUONG", width: 60},
                    {label: "Cách dùng", name: 'CACH_SU_DUNG', index: 'CACH_SU_DUNG', width: 100},
                    {
                        label: "Đơn giá",
                        name: 'DONGIA_BAN_BH',
                        index: 'DONGIA_BAN_BH',
                        width: 80,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ","}
                    },
                    {
                        label: "Thành tiền",
                        name: 'THANHTIEN_THUOC',
                        index: 'THANHTIEN_THUOC',
                        width: 80,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ","}
                    },
                    {label: "Ghi chú", name: 'GHI_CHU_CT_TOA_THUOC', index: 'GHI_CHU_CT_TOA_THUOC', width: 130},
                    {label: "Bác sĩ thêm thuốc", name: 'TEN_NHANVIEN', index: 'TEN_NHANVIEN', width: 120},
                    {label: "MAVATTU", name: 'MAVATTU', index: 'MAVATTU', hidden: true},
                    {label: "MAKHOVATTU", name: 'MAKHOVATTU', index: 'MAKHOVATTU', hidden: true},
                    {label: "DONGIA_BAN_BV", name: 'DONGIA_BAN_BV', index: 'DONGIA_BAN_BV', hidden: true},
                    {label: "MA_TOA_THUOC", name: 'MA_TOA_THUOC', index: 'MA_TOA_THUOC', hidden: true},
                    {label: "STT_TOATHUOC", name: 'STT_TOATHUOC', index: 'STT_TOATHUOC', hidden: true},
                    {label: "NGHIEP_VU", name: 'NGHIEP_VU', index: 'NGHIEP_VU', hidden: true},
                ],
                caption: "Danh sách thuốc",
                rowNum: 1000,
                grouping: true,
                groupingView: {
                    groupField: ["STT_DOTDIEUTRI", "TEN_PHONGBAN", "NGAYLAP", "STT_DIEUTRI"],
                    groupColumnShow: [false, false, false, false],
                    groupText: ['<b>Đợt: {0}</b>', '<b>Khoa lập phiếu: {0}</b>', '<b>Thời gian: {0}</b>', "<b>PHIẾU: {0}</b>"],
                    groupCollapse: false
                },
                onSelectRow: function (id) {
                },
                onRightClickRow: function (id1) {
                    $.contextMenu({
                        selector: '#list_thuoc tr',
                        callback: function (key, options) {
                            if (key == "chuyenduoc"){
                                var rowData = getThongtinRowSelected("list_thuoc")
                                if(singletonObject.admin != 1){
                                    return notifiToClient("Red", MESSAGEAJAX.PERMISSION);
                                }
                                if(rowData.NGHIEP_VU == 'noitru_toamuangoai'){
                                    return notifiToClient("Red", "Nghiệp vụ này không thể chuyển");
                                }
                                if(rowData.DATHANHTOAN == '1'){
                                    return notifiToClient("Red", "Thuốc/vật tư đã thanh toán");
                                }
                                $("#modalChuyenNghiepvuduoc").modal("show")
                            }
                        },
                        items: {
                            "chuyenduoc": {name: '<p><i class="fa fa-random text-primary" aria-hidden="true"></i> Chuyển nghiệp vụ dược</p>'},
                        }
                    });
                }
            });
        }
    }
    function instanceGridPhieuchamsoc() {
        if(!$("#list_phieuchamsoc")[0].grid) {
            $("#list_phieuchamsoc").jqGrid({
                datatype: "local",
                loadonce: false,
                height: 400,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {
                        name: "KYSO",
                        label: "Ký số",
                        align: 'left',
                        width: 100,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.KEYSIGN) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: green">Đã ký</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: red">Chưa ký</span>';
                            }
                        }
                    },
                    {label: "ID", name: 'ID', index: 'ID', width: 10, hidden: true},
                    {label: "DVTT",name: 'DVTT', index: 'DVTT', width: 10, hidden: true},
                    {label: "SOVAOVIEN",name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 10, hidden: true},
                    {label: "ID_DIEUTRI",name: 'ID_DIEUTRI', index: 'ID_DIEUTRI', width: 10, hidden: true},
                    {label: "Khoa", name: 'KHOA', index: 'KHOA', width: 130},
                    {label: "Ngày giờ", name: 'PCS_NGAYGIOLAP', index: 'PCS_NGAYGIOLAP', width: 130},
                    {label: "Nhịp thở",name: 'NHIPTHO', index: 'NHIPTHO', width: 80},
                    {label: "Nhiệt độ",name: 'NHIETDO', index: 'NHIETDO', width: 80},
                    {label: "Huyết áp",name: 'HUYET_AP', index: 'HUYET_AP', width: 80},
                    {label: "Chiều cao",name: 'CHIEUCAO', index: 'CHIEUCAO', width: 80},
                    {label: "Cân nặng",name: 'CANNANG', index: 'CANNANG', width: 80},
                    {label: "Mạch",name: 'MACH', index: 'MACH', width: 80},
                    {label: "Diễn biến",name: 'PCS_THEODOIDIENBIEN', index: 'PCS_THEODOIDIENBIEN', width: 80},
                    {label: "Thực hiện y lệnh",name: 'PCS_THUCHIENYLENH_CS', index: 'PCS_THUCHIENYLENH_CS', width: 80},
                    {label: "Người lập",name: 'PCS_NGUOILAP', index: 'PCS_NGUOILAP', width: 80},
                    {label: "Ngày tạo",name: 'NGAYTAO', index: 'NGAYTAO', width: 80},
                    {label: "MA_NHANVIEN",name: 'MA_NHANVIEN', index: 'MA_NHANVIEN', width: 10, hidden: true},
                    {name: "KEYSIGN", label: "KEYSIGN", hidden:true},
                ],
                rowNum: 1000000,
                caption: "Danh sách Phiếu chăm sóc",
                onRightClickRow: function (id1) {
                    if (id1) {
                        $("#list_phieuchamsoc").jqGrid('setSelection', id1);
                        $.contextMenu('destroy', '#list_phieuchamsoc tr');
                        var ret = getThongtinRowSelected("list_phieuchamsoc");
                        var items = {
                            "copy": {name: '<p><i class="fa fa-bolt text-primary" aria-hidden="true"></i> Copy dữ liệu</p>'},
                            "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                        }
                        if (ret.KEYSIGN) {
                            items = {
                                ...items,
                                "huykyso": {name: '<p style="color:red"><i class="fa fa-key text-danger" aria-hidden="true"></i> Huỷ ký số</p>'},
                            }
                        } else {
                            items = {
                                "kyso": {name: '<p><i class="fa fa-key text-primary" aria-hidden="true"></i> Ký số</p>'},
                                ...items,
                                "capnhat": {name: '<p><i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Chỉnh sửa</p>'},
                                "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'},
                            }
                        }

                        var dataBN = thongtinhsba.thongtinbn;
                        var tenkhoa = "";
                        singletonObject.danhsachphongban.forEach(function(obj) {
                            if(obj.MAKHOA == singletonObject.makhoa) {
                                tenkhoa = obj.TENKHOA;
                            }
                        })
                        var arr = [
                            dataBN.TEN_BENH_NHAN,
                            dataBN.TUOI == 0 ? dataBN.THANG == 0? dataBN.NGAY + " ngày": dataBN.THANG + " tháng": dataBN.TUOI,
                            singletonObject.dvtt,
                            tenkhoa,
                            dataBN.ICD_NHAPVIEN + "- " + dataBN.TENBENHCHINH_NHAPVIEN,
                            dataBN.GIOI_TINH == 1? "Nam": "Nữ",
                            dataBN.SOVAOVIEN,
                            dataBN.SOBENHAN,
                            ret.ID
                        ];
                        var param = ['hovaten',  'tuoi', 'dvtt', 'ten_phongban', 'chandoan', 'gioitinh', 'sovaovien', 'sobenhan', 'idpcs'];
                        var url = "cmu_injasper?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=pdf&jasper=rp_phieuchamsoc_npcs_v2";
                        $.contextMenu({
                            selector: '#list_phieuchamsoc tr',
                            callback: function (key, options) {
                                if (key == "capnhat") {
                                    if (singletonObject.userId != ret.MA_NHANVIEN) {
                                        notifiToClient("Red", "Bạn không có quyền ký số cho phiếu này");
                                    } else {
                                        showLoaderIntoWrapId("phieuchamsoc-wrap");
                                        $.get("cmu_list_cmu_hsba_dsiddt?url="+
                                            convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT]))
                                            .done(function(data) {
                                                hideLoaderIntoWrapId("phieuchamsoc-wrap");
                                                var hap = ret.HUYET_AP.split("/")
                                                $("#phieucs_inputmach").val(ret.MACH);
                                                $("#cmu_pcs_id").val(ret.ID);
                                                $("#phieucs_inputnhietdo").val(ret.NHIETDO);
                                                $("#phieucs_inputhuyetap_tren").val(hap[0]);
                                                if(hap.length > 1) {
                                                    $("#phieucs_inputhuyetap_duoi").val(hap[1]);
                                                }
                                                $("#phieucs_inputnhiptho").val(ret.NHIPTHO);
                                                $("#phieucs_inputcannang").val(ret.CANNANG);
                                                $("#phieucs_inputchieucao").val(ret.CHIEUCAO);
                                                $("#phieucs_dienbien").val(ret.PCS_THEODOIDIENBIEN);
                                                $("#phieucs_ylenh").val(ret.PCS_THUCHIENYLENH_CS);
                                                $("#phieucs_ngaygioplap").val(ret.PCS_NGAYGIOLAP);
                                                $("#modalPhieuchamsoc").modal("show")
                                                addTextTitleModal("titlePhieuchamsoc", "Phiếu chăm sóc");
                                            })
                                    }
                                }
                                if (key == "xoa") {
                                    if (singletonObject.userId == ret.MA_NHANVIEN) {
                                        $.confirm({
                                            title: 'Xác nhận!',
                                            type: 'orange',
                                            content: 'Bạn có chắc chắn muốn xóa phiếu chăm sóc này?',
                                            buttons: {
                                                warning: {
                                                    btnClass: 'btn-warning',
                                                    text: "Tiếp tục",
                                                    action: function(){
                                                        showLoaderIntoWrapId("phieuchamsoc-wrap");
                                                        $.post("cmu_post", {
                                                            url: [singletonObject.dvtt, ret.ID, "CMU_HSBA_PCS_DEL"].join("```")
                                                        }).done(function() {
                                                            luuLogHSBATheoBN({
                                                                SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                                LOAI: LOGHSBALOAI.PHIEUCHAMSOC.KEY,
                                                                NOIDUNGBANDAU: $.getContentLogPhieuchamsoc(ret),
                                                                NOIDUNGMOI: "",
                                                                USERID: singletonObject.userId,
                                                                ACTION: LOGHSBAACTION.DELETE.KEY,
                                                            })
                                                            notifiToClient("Green", "Xóa thành công")
                                                            loadDsPhieuchamsocTonghop()
                                                        }).fail(function() {
                                                            jAlert("Xóa thất bại")
                                                            notifiToClient("Red", "Xóa thất bại")
                                                        }).always(function() {
                                                            hideLoaderIntoWrapId("phieuchamsoc-wrap");
                                                        })
                                                    }
                                                },
                                                cancel: function () {
                                                    hideSelfLoading(idButton)
                                                }
                                            }
                                        });
                                    }
                                }
                                if (key == "copy") {
                                    cloneYlenhPhieuchamsoc(ret)
                                }
                                if (key == "kyso"){
                                    if (singletonObject.userId != ret.MA_NHANVIEN) {
                                        notifiToClient("Red", "Bạn không có quyền ký số cho phiếu này");
                                    } else {
                                        kySoChung({
                                            dvtt: singletonObject.dvtt,
                                            userId: singletonObject.userId,
                                            url: url,
                                            loaiGiay: "PHIEU_NOITRU_PHIEUCHAMSOC",
                                            maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                                            soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                                            soPhieuDichVu: ret.ID,
                                            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                            keyword: "Điều dưỡng ký tên",
                                            fileName: "Phiếu chăm sóc " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + ' - ' + ret.PCS_NGAYGIOLAP,
                                        }, function(dataKySo) {
                                            $("#ttchamsoc-phieuchamsoc").click();
                                        });
                                    }
                                }
                                if (key == "huykyso"){
                                    if (singletonObject.userId != ret.MA_NHANVIEN) {
                                        notifiToClient("Red", "Bạn không có quyền ký số cho phiếu này");
                                    } else {
                                        confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                            huykysoFilesign769("PHIEU_NOITRU_PHIEUCHAMSOC", ret.ID, singletonObject.userId, singletonObject.dvtt,
                                                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                                                    $("#ttchamsoc-phieuchamsoc").click();
                                                })
                                        }, function () {

                                        })
                                    }
                                }
                                if (key == "xem"){
                                    getFilesign769("PHIEU_NOITRU_PHIEUCHAMSOC", ret.ID, -1, singletonObject.dvtt,
                                        thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(dataKySo) {
                                            if (dataKySo.length > 0) {
                                                getCMUFileSigned769(dataKySo[0].KEYMINIO, "pdf")
                                            } else {
                                                previewPdfDefaultModal(url,"preview-phieuchamsoc")
                                            }
                                        });
                                }
                            },
                            items: items
                        });
                    }
                }
            })
        }
    }
    function getJqGridKETQUAStyle(rowId, cellValue, rowObject) {
        return "style='text-align:center; font-weight: bold; color:" + getJqGridKETQUAColor(rowId, cellValue, rowObject) + "; background-color: " + gẹtqGridKETQUABackgroundColor(rowId, cellValue, rowObject) + ";'";
    }
    function isKQBatThuong(ketqua, KetQuaBT) {
        var xBTs = KetQuaBT.toString().split(';');
        for (var i = 0; i < xBTs.length; i++) {
            var xBT = xBTs[i];
            if (xBT.toUpperCase() == ketqua.toUpperCase())
                return true;
        }
        return false;
    }
    function getJqGridKETQUAColor(rowId, cellValue, rowObject) {
        var color = 'back';
        var CONST_KETQUA_CS_COLOR = {
            DUOI_CSBT: 'blue',
            TREN_CSBT: 'red',
            TRONG_CSBT: 'black',
            KETQUA_BATTHUONG: 'green'}
        if (cellValue === null || cellValue == "") color = CONST_KETQUA_CS_COLOR.TRONG_CSBT; // normal
        else if (!(!isNaN(cellValue) && cellValue.toString().split('.').length <= 2)) {
            // khong phai so hop le
            if (rowObject.KQBATTHUONG === undefined || rowObject.KQBATTHUONG === null
                || rowObject.KQBATTHUONG.trim() == "") color = CONST_KETQUA_CS_COLOR.TRONG_CSBT;
            else if (isKQBatThuong(cellValue, rowObject.KQBATTHUONG)) {
                color = CONST_KETQUA_CS_COLOR.KETQUA_BATTHUONG;
            } else {
                color = CONST_KETQUA_CS_COLOR.TRONG_CSBT;
            }
        }
        else {
            var c_top = rowObject.CSBT_TREN;
            var c_bottom = rowObject.CSBT_DUOI;
            if (c_top === null || c_bottom === null) color = CONST_KETQUA_CS_COLOR.TRONG_CSBT; // normal
            else {
                if (parseFloat(cellValue) < parseFloat(c_bottom)) color = CONST_KETQUA_CS_COLOR.DUOI_CSBT; // lower
                else if (parseFloat(cellValue) > parseFloat(c_top)) color = CONST_KETQUA_CS_COLOR.TREN_CSBT; // higher
                else color = CONST_KETQUA_CS_COLOR.TRONG_CSBT; // normal
            }
        }
        //
        return color;
    }
    function gẹtqGridKETQUABackgroundColor(rowId, cellValue, rowObject) {
        var color = 'white';
        if (cellValue === null || cellValue == "" || rowObject.TIENSU == "" || rowObject === null) color = 'white'; // normal
        else {
            if(rowObject.KET_QUA && rowObject.TIENSU){
                var chenhlech = (rowObject.KET_QUA - rowObject.TIENSU)/ rowObject.TIENSU * 100;
                if(chenhlech > 20 || chenhlech < -20){
                    color = 'yellow'
                }
            }
        }
        return color;
    }
    function instanceGridXetnghiem() {
        if(!$("#list_xetnghiem")[0].grid) {
            $("#list_xetnghiem").jqGrid({
                datatype: "local",
                loadonce: false,
                height: 450,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "STT_DOTDIEUTRI",name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', width: 10, hidden: true},
                    {label: "NGAY_CHI_DINH",name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', width: 10, hidden: true},
                    {label: "STT_DIEUTRI",name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 10, hidden: true},
                    {label: "SOBENHAN_TT",name: 'SOBENHAN_TT', index: 'SOBENHAN_TT', width: 10, hidden: true},
                    {label: "SOBENHAN",name: 'SOBENHAN', index: 'SOBENHAN', width: 10, hidden: true},
                    {label: "TEN_LOAI_XETNGHIEM",name: 'TEN_LOAI_XETNGHIEM', index: 'TEN_LOAI_XETNGHIEM', width: 10, hidden: true},
                    {label: "ICD_KHOADIEUTRI",name: 'ICD_KHOADIEUTRI', index: 'ICD_KHOADIEUTRI', width: 10, hidden: true},
                    {label: "TENICD_KHOADIEUTRI",name: 'TENICD_KHOADIEUTRI', index: 'TENICD_KHOADIEUTRI', width: 10, hidden: true},
                    {label: "SO_PHIEU_XN",name: 'SO_PHIEU_XN', index: 'SO_PHIEU_XN', width: 100, hidden: true},
                    {label: "Tên xét nghiệm",name: 'TEN_XETNGHIEM', index: 'TEN_XETNGHIEM', width: 270,
                        cellattr: function (rowId, cellValue, rowObject) {
                            if (rowObject.INDAM != 0) {
                                return 'style="font-weight:bold;color: blue"';
                            }
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "Số lượng",name: 'SO_LUONG', index: 'SO_LUONG', width: 50},
                    {
                        label: "Đơn giá",
                        name: 'DON_GIA',
                        index: 'DON_GIA',
                        width: 100,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}
                    },
                    {
                        label: "Thành tiền",
                        name: 'THANH_TIEN',
                        index: 'THANH_TIEN',
                        width: 100,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}
                    },
                    {
                        label: "BHYT không chi",
                        name: 'BHYTKCHI',
                        index: 'BHYTKCHI',
                        width: 110,
                        align: 'center',
                        formatter: 'checkbox',
                        formatoptions: {value: 'true:false'}
                    },
                    {label: "Kết quả",name: 'KET_QUA', index: 'KET_QUA', width: 100,
                        cellattr: function (rowId, cellValue, rowObject) {
                            return getJqGridKETQUAStyle(rowId, cellValue, rowObject);
                        }
                    },
                    {label: "CSBT",name: 'CSBT', index: 'CSBT', width: 100},
                    {label: "Đơn vị tính",name: 'DONVI', index: 'DONVI', width: 100},
                    {label: "Nguời chỉ định",name: 'NGUOI_CHI_DINH', index: 'NGUOI_CHI_DINH', width: 100},
                    {label: "STT_BENHAN",name: 'STT_BENHAN', index: 'STT_BENHAN', width: 10, hidden: true},
                    {label: "SOVAOVIEN",name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 10, hidden: true},
                    {label: "SOVAOVIEN_DT",name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', width: 10, hidden: true},
                    {label: "KQBATTHUONG",name: 'KQBATTHUONG', index: 'KQBATTHUONG', width: 10, hidden: true},
                    {label: "CSBT_TREN",name: 'CSBT_TREN', index: 'CSBT_TREN', width: 10, hidden: true},
                    {label: "CSBT_DUOI",name: 'CSBT_DUOI', index: 'CSBT_DUOI', width: 10, hidden: true},
                    {label: "KEYSIGN",name: 'KEYSIGN', index: 'KEYSIGN', hidden: true},
                    {label: "KEYSIGN_KYXEM",name: 'KEYSIGN_KYXEM', index: 'KEYSIGN_KYXEM', hidden: true},
                    {label: "KY_HIEU_PHIEU",name: 'KY_HIEU_PHIEU', index: 'KY_HIEU_PHIEU', hidden: true},
                    {label: "SO_PHIEU_DV",name: 'SO_PHIEU_DV', index: 'SO_PHIEU_DV', hidden: true},
                    {label: "Bác sĩ điều trị",name: 'BAC_SI_DIEU_TRI', index: 'BAC_SI_DIEU_TRI', width: 150}
                ],
                caption: "Danh sách xét nghiệm",
                rowNum: 100000,
                grouping: true,
                groupingView: {
                    groupField: ["STT_DOTDIEUTRI", "NGAY_CHI_DINH", "STT_DIEUTRI", "TEN_LOAI_XETNGHIEM"],
                    groupColumnShow: [false, false, false, false, false],
                    groupText: [
                        '<b>Đợt: {0}</b>', '<b>Ngày lập: {0}</b>',
                        '<b>Phiếu: {0}</b>', '<b>{0}</b>'],
                    groupCollapse: false
                },
                onLeftClickRow: function (id) {

                }
                ,
                onRightClickRow: function (id1) {
                    if(id1) {
                        var rowData = getThongtinRowSelected("list_xetnghiem");
                        var items = {
                        }
                        $.contextMenu('destroy', '#list_xetnghiem tr');
                        if (rowData.KEYSIGN_KYXEM){
                            items = {
                                "xemkyxem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                                "huykyxem": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký xem</p>'},
                                ...items
                            }
                        } else if (rowData.KEYSIGN) {
                            items = {
                                "xemketqua": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem KQ ký số và ký xem</p>'},
                                ...items
                            }
                        } else {
                            items = {}
                        }
                        $.contextMenu({
                            selector: '#list_xetnghiem tr',
                            reposition : false,
                            callback: function (key, options) {
                                var rowData = getThongtinRowSelected("list_xetnghiem");
                                if (key == "xemkyxem") {

                                    getFilesign769(
                                        "PHIEUKQ_XETNGHIEM_KYXEM",
                                        rowData.SO_PHIEU_DV,
                                        -1,//singletonObject.userId,
                                        singletonObject.dvtt,
                                        thongtinhsba.thongtinbn.SOVAOVIEN,
                                        thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                        // rowData.MA_LOAI_XETNGHIEM,
                                        -1,
                                        function(data) {
                                            if(data.length > 0) {
                                                getCMUFileSigned769(data[0].KEYMINIO,"pdf")
                                            } else {
                                                notifiToClient("Red", "Lỗi xem phiếu")
                                            }
                                        }
                                    )
                                }
                                if(key == 'xemketqua') {
                                    getFilesign769(
                                        rowData.KY_HIEU_PHIEU,
                                        rowData.SO_PHIEU_DV,
                                        -1,
                                        singletonObject.dvtt,
                                        rowData.SOVAOVIEN,
                                        rowData.SOVAOVIEN_DT,
                                        -1,
                                        function(dataFile) {
                                            console.log(dataFile)
                                            var urlKeyMinio = getCMUFileSigned769GetLinkV2(dataFile[0].KEYMINIO, 'pdf');
                                            previewAndSignPdfDefaultModal({
                                                url: urlKeyMinio,
                                                idButton: 'xn_kyxem_action',
                                            }, function(){
                                                $("#xn_kyxem_action").click(function() {
                                                    var idButton = this.id;
                                                    showSelfLoading(idButton)
                                                    kySoChung({
                                                        dvtt: singletonObject.dvtt,
                                                        userId: singletonObject.userId,
                                                        url: urlKeyMinio,
                                                        loaiGiay: "PHIEUKQ_XETNGHIEM_KYXEM",
                                                        maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                                                        soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                                                        soPhieuDichVu: rowData.SO_PHIEU_XN,
                                                        soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                        soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                                        keyword: "Ngày ban hành",
                                                        y1: 95,
                                                        y2: 30,
                                                        x1: 20,
                                                        x: 160,
                                                        fileName: "Ký xem phiếu xét nghiệm: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Số phiếu: " + rowData.SO_PHIEU_XN,
                                                    }, function(dataKySo) {
                                                        hideSelfLoading(idButton)
                                                        $("#modalPreviewAndSignPDF").modal("hide");
                                                        loadDsXetnghiemTonghop();
                                                    });
                                                });
                                            });
                                        })
                                }
                                if (key == "huykyxem") {
                                    confirmToClient("Bạn có chắc sẽ hủy ký xem phiếu này?", function() {
                                        huykysoFilesign769("PHIEUKQ_XETNGHIEM_KYXEM", rowData.SO_PHIEU_XN, singletonObject.userId, singletonObject.dvtt,
                                            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                                                loadDsXetnghiemTonghop();
                                            })
                                    }, function () {

                                    })
                                }
                            },
                            items: items
                        });

                    }
                }
            });
        }
    }
    function instanceGridCdha() {
        var list = $("#list_chandoanhinhanh");
        if(!list[0].grid) {
            list.jqGrid({
                url: "",
                datatype: "local",
                loadonce: false,
                height: 450,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "STT_DOTDIEUTRI",name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', width: 10, hidden: true},
                    {label: "NGAY_CHI_DINH",name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', width: 10, hidden: true},
                    {label: "STT_DIEUTRI",name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 10, hidden: true},
                    {label: "SOBENHAN_TT",name: 'SOBENHAN_TT', index: 'SOBENHAN_TT', width: 10, hidden: true},
                    {label: "SOBENHAN",name: 'SOBENHAN', index: 'SOBENHAN', width: 10, hidden: true},
                    {label: "ICD_KHOADIEUTRI",name: 'ICD_KHOADIEUTRI', index: 'ICD_KHOADIEUTRI', width: 10, hidden: true},
                    {label: "TENICD_KHOADIEUTRI",name: 'TENICD_KHOADIEUTRI', index: 'TENICD_KHOADIEUTRI', width: 10, hidden: true},
                    {label: "TEN_CDHA",name: 'TEN_CDHA', index: 'TEN_CDHA', width: 200, hidden: true},
                    {label: "Tên chẩn đoán",name: 'TEN_CDHA_HT', index: 'TEN_CDHA_HT', width: 200
                        , formatter: function (cellvalue, options, rowObject) {
                            var iconSign = ""
                            if(!rowObject.KEYSIGN && rowObject.DA_CHAN_DOAN == 1) {
                                iconSign = "<p style='text-align: center;font-size: 20px;margin-bottom: 4px;margin-top:4px'><i class='fa fa-exclamation-circle text-danger'></i></p>"
                            }
                            if(rowObject.KEYSIGN) {
                                iconSign = "<i class='fa fa-check-square-o text-primary'></i>"
                                if(!rowObject.KEYSIGNCONFIRM) {
                                    iconSign += "<i class='fa fa-exclamation-triangle text-warning'></i>"
                                }
                                iconSign = "<p style='text-align: center;font-size: 20px;margin-bottom: 4px;margin-top:4px'>"+iconSign+"</p>"
                            }

                            return iconSign+'<p style="margin-bottom: 4px">' + rowObject.TEN_CDHA + '</p>';
                        }
                    },
                    {label: "Số lượng",name: 'SO_LUONG', index: 'SO_LUONG', width: 100},
                    {   label: "Đơn giá",
                        name: 'DON_GIA',
                        index: 'DON_GIA',
                        width: 100,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}
                    },
                    {
                        label: "Thành tiền",
                        name: 'THANH_TIEN',
                        index: 'THANH_TIEN',
                        width: 100,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}
                    },
                    {
                        label: "BHYT không chi",
                        name: 'BHYTKCHI',
                        index: 'BHYTKCHI',
                        width: 110,
                        align: 'center',
                        formatter: 'checkbox',
                        formatoptions: {value: 'true:false'}
                    },
                    {label: "Kết quả",name: 'KET_QUA', index: 'KET_QUA', width: 100 },
                    {label: "Người chỉ định",name: 'NGUOI_CHI_DINH', index: 'NGUOI_CHI_DINH', width: 100},
                    {label: "MABENHNHAN",name: 'MABENHNHAN', index: 'MABENHNHAN', hidden: true},
                    {label: "SO_PHIEU_CDHA",name: 'SO_PHIEU_CDHA', index: 'SO_PHIEU_CDHA', hidden: true},
                    {label: "MA_CDHA",name: 'MA_CDHA', index: 'MA_CDHA', hidden: true},
                    {label: "STT_BENHAN",name: 'STT_BENHAN', index: 'STT_BENHAN', hidden: true},
                    {label: "SOVAOVIEN",name: 'SOVAOVIEN', index: 'SOVAOVIEN', hidden: true},
                    {label: "SOVAOVIEN_DT",name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', hidden: true},
                    {label: "MOTA_LOAI_CDHA",name: 'MOTA_LOAI_CDHA', index: 'MOTA_LOAI_CDHA', with: 50},
                    {label: "Bác sĩ điều trị",name: 'BAC_SI_DIEU_TRI', index: 'BAC_SI_DIEU_TRI', width: 150},
                    {label: "DA_CHAN_DOAN",name: 'DA_CHAN_DOAN', index: 'DA_CHAN_DOAN', hidden: true},
                    {label: "KEYSIGN",name: 'KEYSIGN', index: 'KEYSIGN', hidden: true},
                    {label: "KEYSIGNCONFIRM",name: 'KEYSIGNCONFIRM', index: 'KEYSIGNCONFIRM', hidden: true},
                    {label: "KEYSIGN_KYXEM",name: 'KEYSIGN_KYXEM', index: 'KEYSIGN_KYXEM', hidden: true},
                ],
                caption: "Danh sách chẩn đoán hình ảnh",
                rowNum: 1000,
                grouping: true,
                groupingView: {
                    groupField: ["STT_DOTDIEUTRI", "NGAY_CHI_DINH", "STT_DIEUTRI"],
                    groupColumnShow: [false, false, false, false],
                    groupText: ['<b>Đợt: {0}</b>', '<b>Ngày lập: {0}</b>', '<b>Phiếu: {0}</b>'],
                    groupCollapse: false
                },
                onLeftClickRow: function (id) {
                },
                onRightClickRow: function (id) {
                    if(id) {
                        var rowData = getThongtinRowSelected("list_chandoanhinhanh");
                        // $.contextMenu('destroy');
                        // console.log("rowData", rowData)
                        var items = {
                        }
                        $.contextMenu('destroy', '#list_chandoanhinhanh tr');
                        if (rowData.KEYSIGN_KYXEM){
                            items = {
                                "xemkyxem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                                "huykyxem": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký xem</p>'},
                            }
                        } else if (rowData.KEYSIGN) {
                            items = {
                                "xemketqua": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem KQ ký số và ký xem</p>'},
                            }
                        }
                        if(rowData.KEYSIGN) {
                            $.contextMenu({
                                selector: '#list_chandoanhinhanh tr',
                                reposition : false,
                                callback: function (key, options) {
                                    if (key == "xemkyxem") {
                                        getFilesign769(
                                            "PHIEUKQ_CDHA_KYXEM",
                                            rowData.SO_PHIEU_CDHA,
                                            -1,//singletonObject.userId,
                                            singletonObject.dvtt,
                                            thongtinhsba.thongtinbn.SOVAOVIEN,
                                            thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                            -1,
                                            function(data) {
                                                if(data.length > 0) {
                                                    getCMUFileSigned769(data[0].KEYMINIO,"pdf")
                                                } else {
                                                    notifiToClient("Red", "Lỗi xem phiếu")
                                                }
                                            }
                                        )
                                    }
                                    if(key == 'xemketqua') {
                                        if(['NS', 'SA'].indexOf(rowData.MOTA_LOAI_CDHA) == -1) {
                                            var kyhieuphieu = "PHIEUKQ_CT_XQUANG_MRI";
                                            if(rowData.MOTA_LOAI_CDHA == 'TDCN') {
                                                kyhieuphieu =  "PHIEUKQ_DIENTIM";
                                            }
                                            getFilesign769(
                                                kyhieuphieu,
                                                rowData.SO_PHIEU_CDHA,
                                                -1,
                                                singletonObject.dvtt,
                                                rowData.SOVAOVIEN,
                                                rowData.SOVAOVIEN_DT,
                                                rowData.MA_CDHA,
                                                function(dataFile) {
                                                    console.log(dataFile[0].KEYMINIO)
                                                    var urlKeyMinio = getCMUFileSigned769GetLinkV2(dataFile[0].KEYMINIO, 'pdf');
                                                    previewAndSignPdfDefaultModal({
                                                        url: urlKeyMinio,
                                                        idButton: 'cdha_kyxem_action',
                                                    }, function(){
                                                        $("#cdha_kyxem_action").click(function() {
                                                            var idButton = this.id;
                                                            showSelfLoading(idButton)
                                                            kySoChung({
                                                                dvtt: singletonObject.dvtt,
                                                                userId: singletonObject.userId,
                                                                url: urlKeyMinio,
                                                                loaiGiay: "PHIEUKQ_CDHA_KYXEM",
                                                                maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                                                                soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                                                                soPhieuDichVu: rowData.SO_PHIEU_CDHA,
                                                                maDichVu: rowData.MA_CDHA,
                                                                soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                                soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                                                keyword: 'Kết luận',
                                                                y1: -30,
                                                                y2: -95,
                                                                x1: 20,
                                                                x: 160,
                                                                fileName: "Ký xem phiếu chẩn đoán hình ảnh: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Số phiếu: " + rowData.SO_PHIEU_CDHA,
                                                            }, function(dataKySo) {
                                                                hideSelfLoading(idButton)
                                                                $("#modalPreviewAndSignPDF").modal("hide");
                                                                loadDsCDHATonghop();
                                                            });
                                                        });
                                                    });
                                                })
                                        } else {
                                            var urlKeyMinio = "download-result-ris-pacs?maPhieuChiDinh="+rowData.SO_PHIEU_CDHA+"&maDichVu="+rowData.MA_CDHA
                                            previewAndSignPdfDefaultModal({
                                                url: urlKeyMinio,
                                                idButton: 'cdha_kyxem_action',
                                            }, function(){
                                                $("#cdha_kyxem_action").click(function() {
                                                    var idButton = this.id;
                                                    showSelfLoading(idButton)
                                                    kySoChung({
                                                        dvtt: singletonObject.dvtt,
                                                        userId: singletonObject.userId,
                                                        url: urlKeyMinio,
                                                        loaiGiay: "PHIEUKQ_CDHA_KYXEM",
                                                        maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                                                        soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
                                                        soPhieuDichVu: rowData.SO_PHIEU_CDHA,
                                                        maDichVu: rowData.MA_CDHA,
                                                        soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                                        soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
                                                        keyword: 'Tái khám mang theo phiếu này',
                                                        x1: 20,
                                                        x: 140,
                                                        fileName: "Ký xem phiếu chẩn đoán hình ảnh: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Số phiếu: " + rowData.SO_PHIEU_CDHA,
                                                    }, function(dataKySo) {
                                                        hideSelfLoading(idButton)
                                                        $("#modalPreviewAndSignPDF").modal("hide");
                                                        loadDsCDHATonghop();
                                                    });
                                                });
                                            });
                                        }
                                    }
                                    if (key == "huykyxem") {
                                        confirmToClient("Bạn có chắc sẽ hủy ký xem phiếu này?", function() {
                                            huykysoFilesign769("PHIEUKQ_CDHA_KYXEM",
                                                rowData.SO_PHIEU_CDHA,
                                                singletonObject.userId, singletonObject.dvtt,
                                                thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, rowData.MA_CDHA, function(data) {
                                                    loadDsCDHATonghop();
                                                })
                                        }, function () {

                                        })
                                    }
                                },
                                items: items
                            });
                        }

                    }
                }
            });
        }
    }
    function instanceGridTTPT() {
        if(!$("#list_ttpt")[0].grid) {
            $("#list_ttpt").jqGrid({
                datatype: "local",
                loadonce: false,
                height: 450,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {label: "MABENHNHAN", name: 'MABENHNHAN', index: 'MABENHNHAN', hidden: true},
                    {label: "STT_DOTDIEUTRI", name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', width: 10},
                    {label: "MA_LOAI_DICHVU", name: 'MA_LOAI_DICHVU', index: 'MA_LOAI_DICHVU', hidden: true},
                    {label: "NGAY_CHI_DINH", name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', width: 10},
                    {label: "CHUYEN_KHOA", name: 'CHUYEN_KHOA', index: 'CHUYEN_KHOA', hidden: true},
                    {label: "CHI_TIET_CHUYEN_KHOA",name: 'CHI_TIET_CHUYEN_KHOA', index: 'CHI_TIET_CHUYEN_KHOA', hidden: true},
                    {label: "STT_DIEUTRI", name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', width: 10},
                    {label: "SOBENHAN_TT", name: 'SOBENHAN_TT', index: 'SOBENHAN_TT', width: 10, hidden: true},
                    {label: "SOBENHAN", name: 'SOBENHAN', index: 'SOBENHAN', width: 10, hidden: true},
                    {label: "ICD_KHOADIEUTRI", name: 'ICD_KHOADIEUTRI', index: 'ICD_KHOADIEUTRI', width: 10, hidden: true},
                    {label: "TENICD_KHOADIEUTRI",name: 'TENICD_KHOADIEUTRI', index: 'TENICD_KHOADIEUTRI', width: 10, hidden: true},
                    {label: "Tên dịch vụ", name: 'TEN_DV', index: 'TEN_DV', width: 200, hidden: true},
                    {label: "BHYTKCHI", name: 'BHYTKCHI', index: 'BHYTKCHI', width: 200, hidden: true},
                    {label: "Tên DV", name: 'TEN_DV_HT', index: 'TEN_DV_HT', width: 215,
                        formatter: function (cellvalue, options, rowObject) {
                            var ekip = '';
                            var tienekip = '';
                            var dathuchien = '';
                            if(rowObject.DACOEKIP > 0) {
                                ekip = "<p class='text-success font-weight-bold m-0'> <i class='fa fa-users'></i> Đã có ekip</p>";
                            }
                            if(rowObject.TIENEKIP == 1) {
                                tienekip = "<p class='text-warning font-weight-bold m-0'> <i class='fa fa-money'></i> Tính tiền ekip</p>";
                            }
                            if(rowObject.DA_CHAN_DOAN == 1) {
                                dathuchien = " <p class='text-primary font-weight-bold m-0 mt-1'> <i class='fa fa-check-circle'></i> Đã thực hiện</p>";
                            }
                            return rowObject.TEN_DV + ekip + tienekip + dathuchien;
                        },
                        cellattr: function (rowId, tv, rawObject, cm, rdata) {
                            return 'style="white-space: normal;"';
                        }
                    },
                    {label: "Số lượng", name: 'SO_LUONG', index: 'SO_LUONG', width: 100},
                    {
                        label: "Đơn giá",
                        name: 'DON_GIA',
                        index: 'DON_GIA',
                        width: 100,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}
                    },
                    {
                        label: "Thành tiền",
                        name: 'THANH_TIEN',
                        index: 'THANH_TIEN',
                        width: 100,
                        align: 'right',
                        formatter: 'integer',
                        formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}
                    },
                    {
                        label: "BHYT không chi",
                        name: 'BHYTKCHI_HT',
                        index: 'BHYTKCHI_HT',
                        width: 110,
                        align: 'center',
                        formatter: 'checkbox',
                        formatoptions: {value: 'true:false'}
                    },
                    {label: "Kết quả",name: 'KET_QUA', index: 'KET_QUA', width: 100},
                    {label: "Người chỉ định", name: 'NGUOI_CHI_DINH', index: 'NGUOI_CHI_DINH', width: 100},
                    {label: "STT_BENHAN", name: 'STT_BENHAN', index: 'STT_BENHAN', hidden: true},
                    {label: "MA_DV", name: 'MA_DV', index: 'MA_DV', hidden: true},
                    {label: "SO_PHIEU_DICHVU", name: 'SO_PHIEU_DICHVU', index: 'SO_PHIEU_DICHVU', hidden: true},
                    {label: "SOVAOVIEN",name: 'SOVAOVIEN', index: 'SOVAOVIEN', hidden: true},
                    {label: "SOVAOVIEN_DT",name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', hidden: true},
                    {label: "BAC_SI_DIEU_TRI",name: 'BAC_SI_DIEU_TRI', index: 'BAC_SI_DIEU_TRI', width: 150},
                    {label: "DACOEKIP",name: 'DACOEKIP', index: 'DACOEKIP', width: 150},
                    {label: "TIENEKIP",name: 'TIENEKIP', index: 'TIENEKIP', width: 150},
                    {label: "DA_CHAN_DOAN",name: 'DA_CHAN_DOAN', index: 'DA_CHAN_DOAN', width: 150},
                    {label: "ID_DIEUTRI",name: 'ID_DIEUTRI', index: 'ID_DIEUTRI', width: 150},
                    {label: "NGAY_CHI_DINH_CT", name: 'NGAY_CHI_DINH_CT', index: 'NGAY_CHI_DINH_CT', hidden: true},
                    {label: "PHONG_CHI_DINH", name: 'PHONG_CHI_DINH', index: 'PHONG_CHI_DINH', hidden: true},
                ],
                caption: "Danh sách thủ thuật phẫu thuật",
                rowNum: 1000,
                grouping: true,
                groupingView: {
                    groupField: ["STT_DOTDIEUTRI", "NGAY_CHI_DINH", "STT_DIEUTRI"],
                    groupColumnShow: [false, false, false, false],
                    groupText: ['<b>Đợt: {0}</b>', '<b>Ngày lập: {0}</b>', '<b>Phiếu: {0}</b>'],
                    groupCollapse: false
                },
                onLeftClickRow: function (id) {
                },
                onRightClickRow: function (id) {
                    if(id) {
                        $.contextMenu({
                            selector: '#list_ttpt tr',
                            reposition : false,
                            callback: function (key, options) {
                                var rowSelected = getThongtinRowSelected("list_ttpt")
                                var idWrap = "ttpt-tab";
                                if(key == "tuongtrinh") {
                                    if(rowSelected.MA_LOAI_DICHVU == 'TT') {
                                        $.openModalTuongTrinhTT(idWrap, rowSelected)
                                    } else {
                                        if(rowSelected.DACOEKIP == 0) {
                                            return notifiToClient("Red", "Vui lòng cập nhật EKIP trước khi thực hiện tường trình")
                                        }
                                        $.openModalTuongTrinhPT(idWrap, rowSelected)
                                    }
                                }
                                if(key == "capnhatsinhthuong") {
                                    $.instanceGridTreSoRau();
                                    $.showFormSinhThuong();
                                    $.showFormTreEm();
                                    $.showFormRauSo();
                                    $.reloadSinhThuong();
                                    $.reloadDSTreEm();
                                    $.reloadDSSoRau();
                                    $("#modalFormSinhThuong").modal("show")
                                }
                            },
                            items: {
                                "tuongtrinh": {name: '<p><i class="fa fa-file-text text-primary" aria-hidden="true"></i> Tường trình</p>'},
                                "capnhatsinhthuong": {name: '<p><i class="fa fa-file-text text-primary" aria-hidden="true"></i> Cập nhật sinh thường</p>'},

                            }
                        });
                    }
                }
            });
        }
    }

    function initData() {
        $.get("cmu_list_STG_TAM_select_phong_ban?url="+convertArray([singletonObject.dvtt])).done(function(data) {
            singletonObject.danhsachphongban = data;
            $("#khoabs").html("")
            $("#ttin_dieutri_khoa").html("")
            data.forEach(function(obj){
                $("#khoabs").append("<option value='"+obj.MAKHOA+"'>"+obj.TENKHOA+"</option>")
                $("#ttin_dieutri_khoa").append("<option value='"+obj.MAKHOA+"'>"+obj.TENKHOA+"</option>")
            })
            $("#khoabs").select2();
            $("#ttin_dieutri_khoa").select2();
        })
        $.get("cmu_list_KB_NOT_ds_phongkhamnoitru?url="+convertArray([singletonObject.makhoa])).done(function(data) {
            singletonObject.danhsachphongbenh = data;
            $("#tenphongbenh").html("")
            if(singletonObject.bant == 1) {
                $("#tenphongbenh").append("<option value='0'>Tất cả</option>")
            }
            data.forEach(function(obj){
                $("#tenphongbenh").append("<option value='"+obj.MA_PHONG_BENH+"'>"+obj.TEN_PHONG_BENH+"</option>")
            })
        })
        $.get("cmu_list_KHAMBENH_NT_SELECT_LTTPTVLTL?url="+convertArray([singletonObject.dvtt])).done(function(data) {
            singletonObject.danhsachloaittpt = data;
            $("#tdt_ttpt_loaittpt").html("")
            $("#hsba-tonghop-loaittpt").html("");
            $("#hsba-tonghop-loaittpt").append("<option value='-1'>Tất cả</option>")
            data.forEach(function(obj){
                $("#tdt_ttpt_loaittpt").append("<option value='"+obj.MA_LOAI_DICH_VU+"'>"+obj.TEN_LOAI_DICH_VU+"</option>")
                $("#hsba-tonghop-loaittpt").append("<option value='"+obj.MA_LOAI_DICH_VU+"'>"+obj.TEN_LOAI_DICH_VU+"</option>")
            })
        })
        $.get("cmu_list_KHAMBENH_NT_SELECT_LTTPTVLTL?url="+convertArray([singletonObject.dvtt])).done(function(data) {
            singletonObject.danhsachloaittpt = data;
            $("#tdt_ttpt_loaittpt").html("")
            data.forEach(function(obj){
                $("#tdt_ttpt_loaittpt").append("<option value='"+obj.MA_LOAI_DICH_VU+"'>"+obj.TEN_LOAI_DICH_VU+"</option>")
            })
        })
        $.get("cmu_list_KB_NOI_khoaphong_GET_khoduoc?url="+convertArray([singletonObject.dvtt, singletonObject.makhoa, singletonObject.bant == 1? "ba_ngoaitru_toathuoc":"noitru_toathuoc"])).done(function(data) {
            singletonObject.danhsachkhothuocBHYT = data;
        })
        $.get("cmu_list_KB_NOI_khoaphong_GET_khoduoc?url="+convertArray([singletonObject.dvtt, singletonObject.makhoa, singletonObject.bant == 1? "ba_ngoaitru_toavattu":"noitru_toavattu"])).done(function(data) {
            singletonObject.danhsachkhovattu = data;
        })
        $.get("cmu_list_KB_NOI_khoaphong_GET_khoduoc?url="+convertArray([singletonObject.dvtt, singletonObject.makhoa, singletonObject.bant == 1? "ba_ngoaitru_toamienphi":"noitru_toamienphi"])).done(function(data) {
            singletonObject.danhsachkhomienphi = data;
        })
        $.get("cmu_list_KB_NOI_khoaphong_GET_khoduoc?url="+convertArray([singletonObject.dvtt, singletonObject.makhoa, singletonObject.bant == 1? "ba_ngoaitru_toaquaybanthuocbv":"noitru_toaquaybanthuocbv"])).done(function(data) {
            singletonObject.danhsachkhomuataiquay = data;
        })
        $.get("cmu_list_KB_NOI_khoaphong_GET_khoduoc?url="+convertArray([singletonObject.dvtt, singletonObject.makhoa, singletonObject.bant == 1? "ba_ngoaitru_toadongy":"noitru_toadongy"])).done(function(data) {
            singletonObject.danhsachkhodongy = data;
        })
        $.get("cmu_list_KB_NOI_khoaphong_GET_khoduoc?url="+convertArray([singletonObject.dvtt, singletonObject.makhoa, singletonObject.bant == 1? "ba_ngoaitru_toadichvu":"noitru_toadichvu"])).done(function(data) {
            singletonObject.danhsachkhodichvu = data;
        })
        $.get("cmu_list_CMU_LAYDM_NGHENGHIEP?url=").done(function(data) {
            singletonObject.danhsachnghenghiep = data;
            $("#tthc_nghenghiep").html("")
            data.forEach(function(obj){
                $("#tthc_nghenghiep").append("<option value='"+obj.MA_NGHE_NGHIEP+"'>"+ obj.MA_NGHE_NGHIEP_130 + " - " +obj.TEN_NGHE_NGHIEP+"</option>")
            })
            singletonObject.danhsachnghenghiepFormio = data.map(function(object) {
                return {
                    label: object.TEN_NGHE_NGHIEP,
                    value: object.MA_NGHE_NGHIEP,
                }
            });
            $("#tthc_nghenghiep").select2({ width: '100%',
                dropdownParent: $("#tthc_nghenghiep").parent()
            })
        })
        $.get("cmu_list_CMU_LAYDM_DANTOC?url=").done(function(data) {
            singletonObject.danhsachdantoc = data;
            $("#tthc_dantoc").html("")
            data.forEach(function(obj){
                $("#tthc_dantoc").append("<option value='"+obj.MA_DANTOC+"'>"+ obj.MA_DANTOC + " - " +obj.TEN_DANTOC+"</option>")
            })
            $("#tthc_dantoc").select2({ width: '100%',
                dropdownParent: $("#tthc_dantoc").parent()
            })
        })
        $.get("cmu_list_LAYDSNHANVIEN_THUTRUONGDONVI?url="+convertArray([singletonObject.dvtt])).done(function(data) {
            singletonObject.danhsachthutruong = data;
            $("#tthc_xv_giamdoc").html("<option value='-1'></option>")
            data.forEach(function(obj){
                $("#tthc_xv_giamdoc").append("<option value='"+obj.MA_BAC_SI+"'>"+ obj.CHUCDANH_HOTEN +"</option>")
            })
            $("#tthc_xv_giamdoc").select2({ width: '100%',
                dropdownParent: $("#tthc_xv_giamdoc").parent()
            });
        })
        $.get("cmu_list_LAYDSNHANVIEN_TRUONGKHOA?url="+convertArray([singletonObject.dvtt, singletonObject.makhoa])).done(function(data) {
            singletonObject.danhsachtruongkhoa = data;
            $("#tthc_xv_truongkhoa").html("<option value='-1'></option>")
            data.forEach(function(obj){
                $("#tthc_xv_truongkhoa").append("<option value='"+obj.MA_BAC_SI+"'>"+ obj.CHUCDANH_HOTEN +"</option>")
            })
            $("#tthc_xv_truongkhoa").select2({ width: '100%',
                dropdownParent: $("#tthc_xv_truongkhoa").parent()
            });
        })
        $.get("cmu_list_KB_NGT_SELECT_BV_CHUYENTUYEN?url="+convertArray([singletonObject.dvtt])).done(function(data) {
            singletonObject.danhsachbvchuyentuyen = data;
            $("#tthc_ct_benhvien").html("<option value=''>Chọn bệnh viện</option>")
            data.forEach(function(obj){
                $("#tthc_ct_benhvien").append("<option value='"+obj.DONVI_CHUYEN+"'>"+ obj.DONVI_CHUYEN + " - " +obj.TEN_DONVICHUYEN+"</option>");
            })
            $("#tthc_ct_benhvien").css("width", "100%").select2()
        })
        $.get("cmu_list_SEL_DM_XANG_DAU?url="+convertArray([singletonObject.dvtt])).done(function(data) {
            singletonObject.danhsachxangdau = data;
            $("#tthc_ct_loaixangdau").html("<option value=''>Chọn loại xăng dầu</option>")
            data.forEach(function(obj){
                $("#tthc_ct_loaixangdau").append("<option value='"+obj.MA+"'>"+ obj.MA + " - " +obj.TEN+"</option>");
            })
        })
        $.get("cmu_list_CMU_DSNHANVIENTOANBV?url="+convertArray([singletonObject.dvtt])).done(function(data){
            singletonObject.danhsachnhanvien = data;
            singletonObject.danhsachnhanvienFormio = data.map(function(object) {
                return {
                    label: object.TEN_NHANVIEN + "(" + object.TEN_PHONGBAN + ")",
                    value: object.MA_NHANVIEN,
                }
            });
        });
        $.get("cmu_list_CMU_DSNHANVIENTOANBV_V2?url="+convertArray([singletonObject.dvtt])).done(function(data){
            singletonObject.danhsachtatcanhanvien = data.map(function(object) {
                return {
                    label: object.TEN_NHANVIEN + "(" + object.TEN_PHONGBAN + ")",
                    value: object.MA_NHANVIEN.toString(),
                    tennhanvien: object.TEN_NHANVIEN,
                }
            });
        });
        $.get("cmu_list_CMU_DSNHANVIEN?url="+convertArray([singletonObject.makhoa])).done(function(data){
            singletonObject.danhsachnhanvienkhoa = data.filter(function(obj){
                return obj.MANHANVIEN != 0;
            });
        });
        $.get("cmu_list_CMU_GET_DMLIETKE?url="+convertArray(['dm_quan_he'])).done(function(data){
            singletonObject.danhsachquanhe = data;
            $("#tthc_tv_quanhenguoichet").html("<option value=''>Chọn quan hệ</option>")
            data.forEach(function(obj){
                $("#tthc_tv_quanhenguoichet").append("<option value='"+obj.ID+"'>"+ obj.ID + " - " +obj.NAME+"</option>")
            })
        });
        $.get("cmu_list_CMU_GET_DMLIETKE?url="+convertArray(['dm_nguyen_nhan_tu_vong'])).done(function(data){
            singletonObject.danhsachnguyennhantv = data;
            $("#tthc_tv_nguyennhantv").html("<option value=''>Chọn nguyên nhân</option>")
            data.forEach(function(obj){
                $("#tthc_tv_nguyennhantv").append("<option value='"+obj.ID+"'>"+ obj.ID + " - " +obj.NAME+"</option>")
            })
        });
        $.get("cmu_list_CMU_GET_DMLIETKE?url="+convertArray(['dm_noi_tu_vong'])).done(function(data){
            singletonObject.danhsachnnoituvong = data;
            $("#tthc_tv_noituvong").html("<option value=''>Chọn nơi tử vong</option>")
            data.forEach(function(obj){
                $("#tthc_tv_noituvong").append("<option value='"+obj.ID+"'>"+ obj.ID + " - " +obj.NAME+"</option>")
            })
        });
        $.post("cmu_post_cmu_tsdv", {
            url: [singletonObject.dvtt,  82816, 0].join('```')
        }).done(function(data) {
            singletonObject.thamso82816 = data;
        })
        $.post("cmu_post_cmu_tsdv", {
            url: [singletonObject.dvtt,  42001, 0].join('```')
        }).done(function(data) {
            singletonObject.thamso42001 = data;
        })
        $.post("cmu_post_cmu_tsdv", {
            url: [singletonObject.dvtt,  960518, 0].join('```')
        }).done(function(data) {
            singletonObject.thamso960518 = data;
        })
        $.post("cmu_post_cmu_tsdv", {
            url: [singletonObject.dvtt,  960484, 0].join('```')
        }).done(function(data) {
            singletonObject.thamso960484 = data;
        })
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, 'CMU_GET_MATINH']))
            .done(function(data){
                singletonObject.danhsachtinh = data;
            })
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt,1, 'QCT_GET_DANH_SACH_CHECK']))
            .done(function(data){
                singletonObject.danhsachquyenchungtu = data;
                $("#tthc_tv_quyengiaychungtu").html("")
                data.forEach(function(obj) {
                    $("#tthc_tv_quyengiaychungtu").append("<option value='"+obj.MAQUYEN+"'>"+obj.TENQUYEN+"</option>")

                })
                $("#tthc_tv_quyengiaychungtu").val("")
            })
        $.get("cmu_list_CMU_HSBA_GETLOAIBA_KHOA?url="+convertArray([singletonObject.dvtt, singletonObject.makhoa])).done(function(data){
            singletonObject.danhsachloaibenhan = data;

        });
        hideSelfLoadingByClass("btn-loading");
        $("#hsba_tabs").tabs("option", "disabled", [1]);
        loadThongtinPhongcls();
        $("#ttin_dieutri_bs").html("<option value='-1'>Tất cả</option>")
        $("#tdt_inputten_icd").combogrid({
            url: 'laydanhmucicd',
            debug: true,
            width: "615px",
            colModel: [
                {'columnName': 'ICD', 'label': 'ICD', 'width': '20', 'align': 'left'},
                {'columnName': 'MO_TA_BENH_LY', 'label': 'Mô tả bệnh lý', 'width': '80', 'align': 'left'},
                {'columnName': 'MA_BENH_LY', 'label': 'mabenhly', hidden: true}
            ],
            select: function (event, ui) {
                $("#tdt_inputten_icd").val( ui.item.MO_TA_BENH_LY);
                $("#tdt_inputicd").val(ui.item.ICD);
                return false;
            }
        });
        $("#tdt_xn_danhgiavs").select2();
        $.get("cmu_list_DS_TINH_TT30?url="+convertArray([singletonObject.dvtt])).done(function(data){
            singletonObject.danhsachtinhtt30 = data;
            initSelect2IfnotIntance('tthc_matinh30', data, 'MA_TINH', 'TEN_TINH', 0, 1, "", 1);
        });
        initSelect2IfnotIntance('tthc_mahuyen30', [], 'MA_HUYEN', 'TEN_HUYEN', 0, 1, "", 1);
        initSelect2IfnotIntance('tthc_maxa30', [], 'MA_XA', 'TEN_XA', 0, 1, "", 1);
        if(singletonObject.bant == 1) {
            setNghiepvutoaBANT("tdt_nghiepvutoathuoc")
            setNghiepvutoaBANT("tdt_toacu_nghiepvu")
            setNghiepvutoaBANT("hsba-tonghop-nghiepvutoathuoc")
            setNghiepvutoaBANT("hsba-lichsu-nghiepvutoathuoc")
            setNghiepvutoaBANT("tdt_hoantra_nghiepvutoathuoc")
            setNghiepvutoaBANT("tdt_nghiepvutoavattuyte")
            $("#tdt_nghiepvutoathuoc").val("ba_ngoaitru_toathuoc")
            $("#tdt_nghiepvutoathuoc").val("ba_ngoaitru_toathuoc")
            $("#tdt_nghiepvutoavattuyte option[value='ba_ngoaitru_toathuoc']").remove()
            $("#tdt_hoantra_nghiepvutoathuoc option[value='ba_ngoaitru_toathuoc']").remove()
            $("#tdt_hoantra_nghiepvutoathuoc option[value='ba_ngoaitru_toamuangoai']").remove()
        }

        $.get("cmu_list_LAYDSTNTT?url="+convertArray([singletonObject.dvtt])).done(function(data) {
            //singletonObject.danhsachthutruong = data;
            data.forEach(function(obj){
                $("#tthc_tntt").append("<option value='"+obj.MA_TAINAN+"'>"+ obj.TENNHOM_TAINAN +"</option>")
            })
            $("#tthc_tntt").select2();
        })
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt,"CMU_CHIPHI_TANGTHEM"])).done(function(data) {
            singletonObject.danhsachgiuongbenhtangthem = data;
            var $select = $("#vp_gb_tengiuongtangthem")
            data.forEach(function(_obj){
                $select.append("<option value='"+_obj.GHICHU_PHUTHU+"'>"+_obj.TEN_LOAI_PHUTHU+ " - " + _obj.DON_GIA_PHUTHU +"</option>")
            })
            $select.select2({ width: '100%',
                dropdownParent: $select.parents('.modal').length == 0? $select.parent():  $select.parents('.modal')
            });
        })
    }

    function loadThongtinPhongcls() {
        var url = "noitru_xetnghiem_select_dsphongxn";
        $.post(url, {dvtt: singletonObject.dvtt}).done(function (data) {
            if (data) {
                $("#tdt_xn_phongxetnghiem").empty();
                $("#tdt_xn_phongxetnghiem_goidichvu").empty();
                $.each(data, function (i) {
                    $("<option value='" + data[i].MA_PHONG_BENH + "'>" + data[i].TEN_PHONG_BENH + "</option>").appendTo("#tdt_xn_phongxetnghiem");
                    $("<option value='" + data[i].MA_PHONG_BENH + "'>" + data[i].TEN_PHONG_BENH + "</option>").appendTo("#tdt_xn_phongxetnghiem_goidichvu");
                });
            }
        });
        var urlCdha = "noitru_cdha_select_dsphongxn";
        $.post(urlCdha, {dvtt: singletonObject.dvtt}).done(function (data) {
            if (data) {
                $("#tdt_cdha_phongcdha").empty();
                $("#tdt_cdha_phongcdha_goidichvu").empty();
                $.each(data, function (i) {
                    $("<option value='" + data[i].MA_PHONG_BENH + "'>" + data[i].TEN_PHONG_BENH + "</option>").appendTo("#tdt_cdha_phongcdha");
                    $("<option value='" + data[i].MA_PHONG_BENH + "'>" + data[i].TEN_PHONG_BENH + "</option>").appendTo("#tdt_cdha_phongcdha_goidichvu");
                });
            }
        });
        var urlTtpt = "noitru_ttpt_select_dsphongxn";
        $.post(urlTtpt, {dvtt: singletonObject.dvtt , phongban: singletonObject.makhoa}).done(function (data) {
            if (data) {
                $("#tdt_ttpt_phongttpt").empty();
                $("#tdt_ttpt_phongttpt_goidichvu").empty();
                $("#lanphauthuat_dvkt_ttpt_phongttpt").empty();
                $.each(data, function (i) {
                    $("<option value='" + data[i].MA_PHONG_BENH + "'>" + data[i].TEN_PHONG_BENH + "</option>").appendTo("#tdt_ttpt_phongttpt");
                    $("<option value='" + data[i].MA_PHONG_BENH + "'>" + data[i].TEN_PHONG_BENH + "</option>").appendTo("#tdt_ttpt_phongttpt_goidichvu");
                    $("<option value='" + data[i].MA_PHONG_BENH + "'>" + data[i].TEN_PHONG_BENH + "</option>").appendTo("#lanphauthuat_dvkt_ttpt_phongttpt");
                });
            }
        });
    }

    function getThongTinHanhChinhVoBA(id) {
        var {responseText: resThongTinHanhChinh} = $.ajax({
            url: "cmu_list_CMU_GET_VOBENHAN_HANHCHINH?url=" + convertArray([singletonObject.dvtt, id]),
            type: "GET",
            async: false,
        });
        return resThongTinHanhChinh
    }

    function getThongtinVoBA() {
        var resThongtinba = $.ajax({
            url: "cmu_list_CMU_HSBA_GETBENHAN?url="+convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN]),
            type: "GET",
            async: false,
        }).responseText;
        resThongtinba = JSON.parse(resThongtinba);
        thongtinhsba.thongtinbn["VOBENHAN"] = resThongtinba;
        thongtinhsba.thongtinbn.VOBENHAN.forEach(function(obj) {
            var resThongTinHanhChinh = getThongTinHanhChinhVoBA(obj.ID)
            obj["HANHCHINH"] = JSON.parse(resThongTinHanhChinh)[0];
        });
        return resThongtinba;
    }

    function openBenhan() {
        resetDataBN();
        try {
            var idWrap = "list_benhnhan_wrap"
            thongtinhsba.thongtinbn = getThongtinBnSelected();
            showLoaderIntoWrapId(idWrap);
            $.loadKetquakysoPacs()
            var resThongtinba = getThongtinVoBA()
            if(thongtinhsba.thongtinbn.SOBENHAN == null || thongtinhsba.thongtinbn.SOBENHAN == ''){
                return $.confirm({
                    title: 'Xác nhận!',
                    type: 'orange',
                    content: 'Bệnh nhân chưa tạo số bệnh án, bạn có muốn tạo số bệnh án cho bệnh nhân này không?',
                    buttons: {
                        warning: {
                            btnClass: 'btn-warning',
                            text: "Tạo bệnh án",
                            action: function(){

                                var url =  singletonObject.bant == 1? "noitru_khambenh_taosobenhan_ngt": "noitru_khambenh_taosobenhan";
                                $.post(url, {
                                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                                    dvtt: singletonObject.dvtt
                                }).done(function (data) {
                                    thongtinhsba.thongtinbn.SOBENHAN = data;
                                    var id = $("#list_benhnhan").jqGrid("getGridParam", "selrow");
                                    $('#list_benhnhan').jqGrid('setRowData', id, thongtinhsba.thongtinbn);
                                    if(resThongtinba.length == 0) {
                                        return hsbaKhoitaobenhan();
                                    }
                                    initThongtinBenhnhan();
                                }).fail(function() {
                                    notifiToClient("Red", "Lỗi tạo số bệnh án!")
                                }).always(function () {
                                    hideLoaderIntoWrapId(idWrap);
                                })
                            }
                        },
                        cancel: function () {
                            hideLoaderIntoWrapId(idWrap);
                        }
                    }
                });
            }
            hideLoaderIntoWrapId(idWrap);
            if(resThongtinba.length == 0) {
                return hsbaKhoitaobenhan();
            }
            initThongtinBenhnhan();
        } catch (e) {
            console.log("Lỗi lấy thông tin bệnh nhân: " + e)
            notifiToClient("Red", "Lỗi lấy thông tin bệnh nhân!")
            hideLoaderIntoWrapId(idWrap);
        }
    }
    function setNghiepvutoaBANT(idElement) {
        var $element = $("#"+idElement);
        $element.empty();
        $element.append("<option value='ba_ngoaitru_toathuoc'>Toa thuốc</option>");
        $element.append("<option value='ba_ngoaitru_toavattu'>Toa vật tư</option>");
        $element.append("<option value='ba_ngoaitru_toadichvu'>Toa dịch vụ</option>");
        $element.append("<option value='ba_ngoaitru_toaquaybanthuocbv'>Toa mua tại BV - Thu phí</option>");
        $element.append("<option value='ba_ngoaitru_toadongy'>Toa đông y</option>");
        $element.append("<option value='ba_ngoaitru_toamienphi'>Toa miễn phí</option>");
        $element.append("<option value='ba_ngoaitru_toamuangoai'>Toa mua ngoài</option>");
    }

    function loadDataThoMay() {
        var thomay = [];
        todieutriObject.THO_MAY && thomay.push("Thở máy: " + todieutriObject.THO_MAY);
        todieutriObject.THOMAY_CHEDO && thomay.push("Chế độ: " + todieutriObject.THOMAY_CHEDO);
        todieutriObject.THOMAY_TANSO && thomay.push("Tần số: " + todieutriObject.THOMAY_TANSO);
        todieutriObject.THOMAY_IE && thomay.push("I/E: " + todieutriObject.THOMAY_IE);
        todieutriObject.THOMAY_FIO2 && thomay.push("FiO2: " + todieutriObject.THOMAY_FIO2);
        todieutriObject.THOMAY_PHANTRAM_TRIGGER && thomay.push("%Trigger: " + todieutriObject.THOMAY_PHANTRAM_TRIGGER);
        todieutriObject.THOMAY_PC_PS_PEEP && thomay.push("PC/PS/PEEP: " + todieutriObject.THOMAY_PC_PS_PEEP);
        todieutriObject.THOMAY_MAP && thomay.push("MAP: " + todieutriObject.THOMAY_MAP);
        todieutriObject.THOMAY_MVE && thomay.push("MVe: " + todieutriObject.THOMAY_MVE);
        $("#tdt_thomay").html(thomay.join(", "));
    }

    function loadDataThoCPAP() {
        var thocpap = [];
        todieutriObject.THOCPAP_P && thocpap.push("P: " + todieutriObject.THOCPAP_P);
        todieutriObject.THOCPAP_FIO2 && thocpap.push("FiO2: " + todieutriObject.THOCPAP_FIO2);
        $("#tdt_thocpap").html(thocpap.join(", "));
    }

    function loadDataThoOXY() {
        var thooxy = [];
        todieutriObject.THOOXY_SOLUONG && thooxy.push("Số lượng: " + todieutriObject.THOOXY_SOLUONG);
        todieutriObject.THOOXY_LOAITD && thooxy.push("Loại: " + loaiThoOxy(todieutriObject.THOOXY_LOAITD));
        $("#tdt_thooxy").html(thooxy.join(", "));
    }

    function loaiThoOxy(loaiInput) {
        return ({
            "0": "lít/phút",
            "1": "lít/giờ",
        })[loaiInput] ?? loaiInput
    }

    function capnhatEMR(rowData, trangthai) {
        showLoaderIntoWrapId("list_benhnhan_wrap")
        $.post("cmu_post_cmu_update_emr", {
            url: [singletonObject.dvtt, rowData.STT_BENHAN, trangthai].join('```'),
        }).done(function(data) {
            if(data > 0) {
                notifiToClient("Green", MESSAGEAJAX.EDIT_SUCCESS)
                $("#loaddsbenhnhan").click();
            } else {
                notifiToClient("Red", MESSAGEAJAX.FAIL)
            }
        }).fail(function() {
            notifiToClient("Red", MESSAGEAJAX.ERROR)
        }).always(function() {
            hideLoaderIntoWrapId("list_benhnhan_wrap")
        })
    }

    function xemToDieuTriLe() {
        var rowData = getThongtinRowSelected("list_todieutri")
        showLoaderIntoWrapId("hsba_list_todieutri");
        if(rowData.KEYSIGN) {
            getFilesign769(
                "TODIEUTRI_NOITRU",
                rowData.ID_DIEUTRI,
                -1,//singletonObject.userId,
                singletonObject.dvtt,
                thongtinhsba.thongtinbn.SOVAOVIEN,
                rowData.SOVAOVIEN_DT,
                -1,
                function(data) {
                    hideLoaderIntoWrapId("hsba_list_todieutri");
                    if(data.length > 0) {
                        getCMUFileSigned769(data[0].KEYMINIO,"pdf")
                    } else {
                        notifiToClient("Red", "Lỗi lấy dữ liệu ký số");
                    }
                }
            )
        } else {

            $.get("cmu_list_HSBA_CMU_TDT_GETBYID?url="+convertArray([
                singletonObject.dvtt,
                rowData.ID_DIEUTRI,
                rowData.SOVAOVIEN,
                rowData.SOVAOVIEN_DT
            ])).always(function (data) {
                hideLoaderIntoWrapId("hsba_list_todieutri");
            }).done(function (resData) {
                var data = resData[0];
                previewPdfDefaultModal(getUrlToDieutriById(rowData),'tdt-iframe-preview');

            }).fail(function (data) {
                notifiToClient("Red", "Lỗi lấy dữ liệu phiếu điều trị");
            })
        }
    }
})