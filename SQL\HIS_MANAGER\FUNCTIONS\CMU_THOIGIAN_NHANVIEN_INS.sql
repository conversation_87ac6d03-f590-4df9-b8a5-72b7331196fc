create or replace FUNCTION CMU_THOIGIAN_NHANVIEN_INS(
P_DVTT VARCHAR2,
P_MANHANVIEN NUMBER ,
P_SOVAOVIEN NUMBER ,
P_MABENHNHAN NUMBER ,
P_NOITRU NUMBER ,
P_THOIGIAN_BD VARCHAR2,
P_THOIGIAN_KT VARCHAR2 ,
P_SOPHIEU VARCHAR2,
P_MA_DV VARCHAR2,
P_LOAI VARCHAR2
) RETURN  VARCHAR2
IS
v_exist number;
BEGIN
select count(1) into v_exist  from CMU_THOIGIAN_NHANVIEN
where dvtt =p_dvtt and sovaovien = p_sovaovien
  and noitru = p_noitru and SOPHIEU = P_SOPHIEU
  and MA_DV = P_MA_DV;
if v_exist > 0 then
update CMU_THOIGIAN_NHANVIEN
set THOIGIAN_BD = TO_DATE(P_THOIGIAN_BD, 'DD/MM/YYYY HH24:MI'), THOIGIAN_KT = TO_DATE(P_THOIGIAN_KT, 'DD/MM/YYYY HH24:MI'),
    MANHANVIEN= P_MANHANVIEN
where dvtt =p_dvtt and sovaovien = p_sovaovien
  and noitru = p_noitru and SOPHIEU = P_SOPHIEU
  and MA_DV = P_MA_DV;
else
		insert into HIS_MANAGER.CMU_THOIGIAN_NHANVIEN (
			 "DVTT"  ,
			 "MANHANVIEN",
			 "SOVAOVIEN",
			 "MABENHNHAN",
			 "NOITRU",
			 "THOIGIAN_BD",
			 "THOIGIAN_KT",
			 "SOPHIEU",
			 "MA_DV",
			 "LOAI",
			 "NGAYTAO"
		) values (
			P_DVTT ,
			P_MANHANVIEN  ,
			P_SOVAOVIEN  ,
			P_MABENHNHAN  ,
			P_NOITRU  ,
			TO_DATE(P_THOIGIAN_BD, 'DD/MM/YYYY HH24:MI'),
			TO_DATE(P_THOIGIAN_KT, 'DD/MM/YYYY HH24:MI') ,
			P_SOPHIEU ,
			P_MA_DV ,
			P_LOAI,
			SYSDATE
		);
end if;
	RETURN sql%rowcount;
END;