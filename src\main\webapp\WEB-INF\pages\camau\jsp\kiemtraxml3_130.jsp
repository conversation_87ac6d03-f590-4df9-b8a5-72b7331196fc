<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@page contentType="text/html" pageEncoding="UTF-8"  %>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
  <meta name="google-site-verification" content="u6uNEfD4cb3gidezi4r_6aI8Wb1E07-ufBeCQpvmlqQ" />
  <title>Hệ thống chăm sóc sức khỏe</title>
  <link rel="icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
  <link rel="shortcut icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>

  <!-- jQuery file -->

  <link href="<c:url value="/resources/css/divheader.css" />" rel="stylesheet"/>
  <link href="<c:url value="/resources/css/style_new.css" />" rel="stylesheet"/>

  <!--Jquery-->
  <link rel="stylesheet" href="<c:url value="/resources/css/jquery-ui-redmond.1.9.1.css" />" />
  <script src="<c:url value="/resources/js/jquery.min.1.8.3.js" />"></script>
  <script src="<c:url value="/resources/js/jquery-ui.1.9.1.js" />"></script>


  <%--<script src="<c:url value="/resources/js/jquery.jqGrid.src.multicol.js" />"></script>--%>


  <link href="<c:url value="/resources/jqgrid/css/ui.jqgrid.css" />" rel="stylesheet"/>
  <script src="<c:url value="/resources/jqgrid/js/i18n/grid.locale-en.js" />"></script>
  <script src="<c:url value="/resources/jqgrid/js/jquery.jqGrid.src.js" />"></script>

  <!-- This is the Javascript file of jqGrid -->
  <script src="<c:url value="/resources/js/common_function.js" />"></script>
  <script src="<c:url value="/resources/js/jquery.inputmask.bundle.min.js" />"></script>
  <script src="<c:url value="/resources/blockUI/jquery.blockUI.js" />"></script>
  <script src="<c:url value="/resources/dialog/jquery.alerts.js" />"></script>
  <link href="<c:url value="/resources/dialog/jquery.alerts.1.css" />" rel="stylesheet"/>
  <link href="<c:url value="/resources/combogrid/css/smoothness/jquery.ui.combogrid.css" />" rel="stylesheet"/>
  <script src="<c:url value="/resources/combogrid/plugin/jquery.ui.combogrid-1.6.3.js" />"></script>
  <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
  <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>
  <script src="<c:url value="/resources/bootstrap/js/bootstrap.min.js" />"></script>
  <link href="<c:url value="/resources/bootstrap/css/bootstrap.css" />" rel="stylesheet"/>
  <link href="<c:url value="/resources/dialog/jBox.css" />" rel="stylesheet"/>
  <script src="<c:url value="/resources/dialog/jBox.js" />"></script>
  <script src="<c:url value="/resources/js/datetimepicker.js"/>"></script>
  <link rel="stylesheet" href="<c:url value="/resources/css/datetimepicker.css"/>" />
  <script type="text/javascript" src="<c:url value="/resources/bootstrap/js/bootstrap-multiselect.js" />"></script>
  <link rel="stylesheet" href="<c:url value="/resources/bootstrap/css/bootstrap-multiselect.css" />"/>
  <script type="text/javascript" language="javascript" src="//cdnjs.cloudflare.com/ajax/libs/jszip/2.5.0/jszip.min.js"></script>
  <script type="text/javascript" language="javascript" src="//cdn.rawgit.com/bpampuch/pdfmake/0.1.26/build/pdfmake.min.js"></script>
  <script type="text/javascript" language="javascript" src="//cdn.rawgit.com/bpampuch/pdfmake/0.1.26/build/vfs_fonts.js"></script>
  <script src="<c:url value="/resources/js/jquery-confirm.min.js" />"></script>
  <link href="<c:url value="/resources/css/jquery-confirm.min.css" />" rel="stylesheet"/>
  <script src="<c:url value="/resources/camau/material/moment.js" />"></script>
  <script src="<c:url value="/resources/camau/js/kiemtradulieuthoigiankham.js?timestamp=${System.currentTimeMillis()}" />"></script>
  <style type="text/css">
    .jqgrid-cell-error{
      background-color: red;
      white-space: pre-wrap !important;
      text-align: center !important;
      color: white
    }
    .ui-jqgrid-view .ui-jqgrid .ui-jqgrid-htable th.ui-th-column-header {
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      padding-top: 1px;
      height: 19px
    }
    .ui-jqgrid .ui-jqgrid-view {
      z-index: 0 !important;
    }

  </style>

</head>
<body>
<div id="panel_all">
  <%@include file="../../../../resources/Theme/include_pages/menu.jsp"%>
  <div id="panelwrap">
    <div class="center_content">
      <div class="panel_with_title">
        <div class="panel_title">Kiểm tra XML3 130</div>
        <div class="panel_body">
          <form action="" method="get">
            <table width="82%" border="0" align="center">
              <tr>
                <td width="100">Ngày</td>
                <td width="150"><input type="text" name="tungay" id="tungay" data-inputmask="'alias': 'date'" style="width: 150px" autocomplete="off"/></td>
                <td width="100">Khoa điều trị</td>
                <td width="120">
                  <select name="khoa" class="width1" id="khoa" style="width: 100%">
                    <option selected value="-1">---Tất cả---</option>
                  </select>
                </td>
                <td width="100">Nội trú/Ngoại trú</td>
                <td width="120">
                  <select name="hinhthuc" class="width1" id="hinhthuc" style="width: 60%">
                    <option value="0">Nội trú</option>
                    <option value="1">Ngoại trú</option>
                  </select>
                </td>
              </tr>
              <tr style="">
                <td width="100">Đến ngày</td>
                <td width="150"><input type="text" name="denngay" id="denngay" data-inputmask="'alias': 'date'" style="width: 150px" autocomplete="off"/></td>
              </tr>
              <tr>
                <td colspan="4" align="center">
                  <input type="button" name="xembc" id="xembc" value="Xem báo cáo" class="button_shadow" style="width:110px"/>
                  <input type="button" name="inbc" id="inbc" value="In báo cáo" class="button_shadow" style="width:110px"/>
                  <input type="button" name="xuatbaocao" id="xuatbaocao" value="Xuất báo cáo" class="button_shadow" style="width:110px"/></td>
                </td>
                <td>
                  <a href="cmu_funs_map_bacsi" class="button_shadow" style="width:200px; float:right">Map Công khám - Bác sĩ</a>
                </td>
              </tr>
            </table>
          </form>
        </div>
      </div>
      <div id="tab_tabds">
        <ul >
          <li class="active"><a href="#tabcongkham">CÔNG KHÁM</a></li>
          <li onclick="onClick()"><a href="#tabthuoc">THUỐC</a></li>
          <li onclick="onClick()"><a href="#tabvattu">VẬT TƯ</a></li>
          <li onclick="onClick()"><a href="#tabxquang">CDHA</a></li>
          <li onclick="onClick()"><a href="#tabxetnghiem">XÉT NGHIỆM</a></li>
          <li onclick="onClick()"><a href="#tabthuthuatphauthuat">THỦ THUẬT PHẪU THUẬT</a></li>
          <li onclick="onClick()"><a href="#tabgiuongbenh">GIƯỜNG BỆNH</a></li>
          <li onclick="onClick()"><a href="#tabtrunggioylenh">KIỂM TRA THỜI GIAN Y LỆNH</a></li>
          <li onclick="onClick()"><a href="#tabtrungthoigianthuchien">THỜI GIAN THỰC HIỆN</a></li>
        </ul>
        <div id="tabcongkham" class="tabcontent">
          <div id="div_tabcongkham" ><table id="list_tabcongkham"></table></div>
        </div>

        <div id="tabthuoc" class="tabcontent">
          <div id="div_list_tabthuoc"> <table id="list_tabthuoc"></table></div>
        </div>
        <div id="tabvattu" class="tabcontent">
          <div id="div_list_vattu"> <table id="list_vattu"></table></div>
        </div>
        <div id="tabxquang" class="tabcontent">
          <div id="div_list_tabcdha"> <table id="list_tabcdha"></table></div>
        </div>
        <div id="tabxetnghiem" class="tabcontent">
          <div id="div_list_tabxetnghiem"> <table id="list_tabxetnghiem"></table></div>
        </div>
        <div id="tabthuthuatphauthuat" class="tabcontent">
          <div id="div_list_tabthuthuatphauthuat"> <table id="list_tabthuthuatphauthuat"></table></div>
        </div>
        <div id="tabgiuongbenh" class="tabcontent">
          <div id="div_list_tabgiuongbenh"> <table id="list_tabgiuongbenh"></table></div>
        </div>
        <div id="tabtrunggioylenh" class="tabcontent">
          <div id="div_list_tabtrunggioylenh"> <table id="list_tabtrunggioylenh"></table></div>
        </div>
        <div id="tabtrungthoigianthuchien" class="tabcontent">
          <div id="div_list_tabtrungthoigianthuchien"> <table id="list_tabtrungthoigianthuchien"></table></div>
        </div>
      </div>
    </div> <!--end of center_content-->
    <div id="dialog_mapthemdinhmuc" style="display: none">
      <table width="100%" border="0">
        <tr>
          <td style="text-align: center;">
            <select name="dsmay_xn_them" id="ds_bacsicongkham" class="dsbacsi">
            </select>
          </td>

        </tr>
        <tr>
          <td>
            <table id="cmu_ds_bacsi"></table>
          </td>
        </tr>
        <tr>
          <td colspan="2" style="text-align: center;"> <input name="cmu_luubacsi" type="button"
                                                              id="cmu_luubacsi" value="Lưu"
                                                              class="button_shadow" style="width: 140px">
          </td>
        </tr>
      </table>
    </div>
    <div id="dialog_bacsi" style="display: none">
      <table width="100%" border="0">
        <tr>
          <td style="text-align: center;">
            <select name="dsmay_xn_them" id="ds_bacsicongkham_thuoc" class="dsbacsi">
            </select>
          </td>

        </tr>
        <tr>
          <td colspan="2" style="text-align: center;"> <input name="cmu_luubacsi_thuoc" type="button"
                                                              id="cmu_luubacsi_thuoc" value="Lưu"
                                                              class="button_shadow" style="width: 140px">
          </td>
        </tr>
      </table>
    </div>
    <div id="dialog_bacsi_cdha" style="display: none">
      <table width="100%" border="0">
        <tr>
          <td style="text-align: center;">
            <select  id="ds_bacsi" class="dsbacsi">
            </select>
          </td>

        </tr>
        <tr>
          <td colspan="2" style="text-align: center;">
            <input type="button" id="cmu_luubacsi_cdha_ktv" value="Lưu" class="button_shadow" style="width: 140px">
            <input type="button"  id="cmu_luubacsi_cdha_nguoidoc" value="Lưu" class="button_shadow" style="width: 140px">
          </td>
        </tr>
      </table>
    </div>
    <div id="dialog_bacsi_xn" style="display: none">
      <table width="100%" border="0">
        <tr>
          <td style="text-align: center;">
            <select  id="ds_bacsi_xn" class="dsbacsi">
            </select>
          </td>

        </tr>
        <tr>
          <td colspan="2" style="text-align: center;">
            <input  type="button" id="cmu_luubacsi_xn_ktv" value="Lưu" class="button_shadow" style="width: 140px">
            <input  type="button" id="cmu_luubacsi_xn_nguoidoc" value="Lưu" class="button_shadow" style="width: 140px">
          </td>
        </tr>
      </table>
    </div>
    <div id="dialog_capnhatthoigian_ylenh" style="display: none">
      <table width="100%" border="0">
        <tr>
          <td style="text-align: center;">
            Thời gian y lệnh
          </td>
          <td style="text-align: center;">
            <input name="tungay" type="text" style="width:200px" id="ylenh" data-inputmask="'alias': 'date'" />
          </td>
        </tr>
        <tr>
          <td colspan="2" style="text-align: center;"> <input name="cmu_luugioylenh" type="button"
                                                              id="cmu_luugioylenh" value="Lưu"
                                                              class="button_shadow" style="width: 140px">
          </td>
        </tr>
      </table>
    </div>
    <div id="dialog_capnhatthoigian_thuchien_ylenh" style="display: none">
      <table width="100%" border="0">
        <tr>
          <td style="text-align: center;">
            Thời gian TH y lệnh
          </td>
          <td style="text-align: center;">
            <input name="tungay" type="text" style="width:200px" id="thuchien_ylenh" data-inputmask="'alias': 'date'" />
          </td>
        </tr>
        <tr>
          <td colspan="2" style="text-align: center;"> <input name="cmu_luugio_thuchien_ylenh" type="button"
                                                              id="cmu_luugio_thuchien_ylenh" value="Lưu"
                                                              class="button_shadow" style="width: 140px">
          </td>
        </tr>
      </table>
    </div>
    <div id="dialog_capnhatthoigianhtkham" style="display: none">
      <table width="100%" border="0">
        <tr>
          <td style="text-align: center;">
            Thời gian hoàn tất khám
          </td>
          <td style="text-align: center;">
            <input type="text" style="width:100px" id="ngayhoantatkham" readonly />
            <input type="text" style="width:200px" id="cmuthoaigianhtkham" />
          </td>
        </tr>
        <tr>
          <td colspan="2" style="text-align: center;"> <input name="cmu_luugioylenh" type="button"
                                                              id="cmu_luuthoigianhtkham" value="Lưu"
                                                              class="button_shadow" style="width: 140px">
          </td>
        </tr>
      </table>
    </div>
    <div id="dialog_capnhatthoiylenh" style="display: none">
      <table width="100%" border="0">
        <tr>
          <td style="text-align: center;">
            Thời gian y lệnh
          </td>
          <td style="text-align: center;">
            <input type="text" style="width:100px" id="ngayhoantatkham_2" readonly />
            <input type="text" style="width:200px" id="cmuthoigianylenh" />
          </td>
        </tr>
        <tr>
          <td colspan="2" style="text-align: center;"> <input name="cmu_luugioylenh" type="button"
                                                              id="cmu_luuthoigianylenh" value="Lưu"
                                                              class="button_shadow" style="width: 140px">
          </td>
        </tr>
      </table>
    </div>
    <div id="dialog_capnhatthoigian_ketqua" style="display: none">
      <table width="100%" border="0">
        <tr>
          <td style="text-align: center;">
            Thời gian kết quả
          </td>
          <td style="text-align: center;">
            <input name="tungay" type="text" style="width:200px" id="ketqua" data-inputmask="'alias': 'date'" />
          </td>
        </tr>
        <tr>
          <td colspan="2" style="text-align: center;"> <input name="cmu_luugioketqua" type="button"
                                                              id="cmu_luugioketqua" value="Lưu"
                                                              class="button_shadow" style="width: 140px">
          </td>
        </tr>
      </table>
    </div>
    <div id="dialog_capnhatthoigian_capgiuong" style="display: none">
      <table width="100%" border="0">
        <tr>
          <td style="text-align: center;">
            Giờ vào
          </td>
          <td style="text-align: center;">
            <input name="tungay" type="text" style="width:100px" id="giuong_tungay" data-inputmask="'alias': 'date'" />
          </td>
          <td style="text-align: center;">
            Giờ ra
          </td>
          <td style="text-align: center;">
            <input name="denngay" type="text" style="width:100px" id="giuong_denngay" data-inputmask="'alias': 'date'" />
          </td>
        </tr>
        <tr>
          <td colspan="4" style="text-align: center;"> <input name="cmu_luugioketqua" type="button"
                                                              id="cmu_luugiocapgiuong" value="Lưu"
                                                              class="button_shadow" style="width: 140px">
          </td>
        </tr>
      </table>
    </div>
    <div id="dialog_capnhatthoigian_capsogiuong" style="display: none">
      <table width="100%" border="0">
        <tr>
          <td style="text-align: center;">
            Số giường
          </td>
          <td style="text-align: center;">
            <input type="number" style="width:100px" id="giuong_sogiuong"  />
          </td>
        </tr>
        <tr>
          <td colspan="4" style="text-align: center;"> <input name="cmu_luusogiuong" type="button"
                                                              id="cmu_luusogiuong" value="Lưu"
                                                              class="button_shadow" style="width: 140px">
          </td>
        </tr>
      </table>
    </div>
    <div id="dialog_capnhatnguoicap_giuong" style="display: none">
      <table width="100%" border="0">
        <tr>
          <td style="text-align: center;">
            Nhân viên
          </td>
          <td style="text-align: center;">
            <select  id="ds_nhanviencapgiuong" class="dsbacsi">
            </select>
          </td>
        </tr>
        <tr>
          <td colspan="4" style="text-align: center;"> <input name="cmu_luunguoicapgiuong" type="button"
                                                              id="cmu_luunguoicapgiuong" value="Lưu"
                                                              class="button_shadow" style="width: 140px">
          </td>
        </tr>
      </table>
    </div>
    <div id="dialog_capnhatngaythuoc" style="display: none">
      <table width="100%" border="0">
        <tr>
          <td style="text-align: center;">
            Ngày uống
          </td>
          <td style="text-align: center;">
            <input name="thuoc_ngayuong" type="number" style="width:50px" id="thuoc_ngayuong"  />
          </td>
          <td style="text-align: center;">
            Sáng
          </td>
          <td style="text-align: center;">
            <input name="thuoc_sanguong" type="number" style="width:50px" id="thuoc_sanguong"  />
          </td>
          <td style="text-align: center;">
            Trưa
          </td>
          <td style="text-align: center;">
            <input name="thuoc_truauong" type="number" style="width:50px" id="thuoc_truauong"  />
          </td>
          <td style="text-align: center;">
            Chiều
          </td>
          <td style="text-align: center;">
            <input name="thuoc_chieuuong" type="number" style="width:50px" id="thuoc_chieuuong"  />
          </td>
          <td style="text-align: center;">
            Tối
          </td>
          <td style="text-align: center;">
            <input name="thuoc_toiuong" type="number" style="width:50px" id="thuoc_toiuong"  />
          </td>
          <td style="text-align: center;">
            Dạng thuốc
          </td>
          <td style="text-align: center;">
            <input name="thuoc_dangthuoc" type="text" style="width:100px" id="thuoc_dangthuoc"  />
          </td>
        </tr>
        <tr>
          <td colspan="10" style="text-align: center;"> <input name="cmu_luungaythuoc" type="button"
                                                               id="cmu_luungaythuoc" value="Lưu"
                                                               class="button_shadow" style="width: 140px">
          </td>
        </tr>
      </table>
    </div>
    <div id="dialog_capnhatthoigian_ylenhthuoc" style="display: none">
      <table width="100%" border="0">
        <tr>
          <td style="text-align: center;">
            Thời gian y lệnh thuốc
          </td>
          <td style="text-align: center;">
            <input name="thoigianylenhthuoc" type="text" style="width:200px" id="thoigianylenhthuoc" data-inputmask="'alias': 'date'" />
          </td>
        </tr>
        <tr>
          <td colspan="2" style="text-align: center;"> <input name="cmu_luuthoigianylenhthuoc" type="button"
                                                              id="cmu_luuthoigianylenhthuoc" value="Lưu"
                                                              class="button_shadow" style="width: 140px">
          </td>
        </tr>
      </table>
    </div>

  </div>
</div>
</div>

</body>
<script>
  var dialog_mapthemdinhmuc = new jBox('Modal', {
    title: "Cập nhật bác sĩ",
    overlay: false,
    content: $('#dialog_mapthemdinhmuc'),
    position: {
      y: 'center'
    },
    draggable: 'title'
  });

  var dialog_capnhatthoigianhtkham = new jBox('Modal', {
    title: "Cập nhật thời gian hoàn tất khám",
    overlay: false,
    content: $('#dialog_capnhatthoigianhtkham'),
    position: {
      y: 'center'
    },
    draggable: 'title'
  });
  var dialog_capnhatylenhtkham = new jBox('Modal', {
    title: "Cập nhật thời gian khám",
    overlay: false,
    content: $('#dialog_capnhatthoiylenh'),
    position: {
      y: 'center'
    },
    draggable: 'title'
  });

  var dialog_bacsi= new jBox('Modal', {
    title: "Cập nhật bác sĩ thuốc",
    overlay: false,
    content: $('#dialog_bacsi'),
    position: {
      y: 'center'
    },
    width: 400,
    height: 200,
    draggable: 'title'
  });
  var dialog_bacsi_cdha= new jBox('Modal', {
    title: "Cập nhật người thực hiện/đọc kết quả",
    overlay: false,
    content: $('#dialog_bacsi_cdha'),
    position: {
      y: 'center'
    },
    width: 400,
    height: 200,
    draggable: 'title'
  });
  var dialog_bacsi_xn= new jBox('Modal', {
    title: "Cập nhật người thực hiện/đọc kết quả",
    overlay: false,
    content: $('#dialog_bacsi_xn'),
    position: {
      y: 'center'
    },
    width: 400,
    height: 200,
    draggable: 'title'
  });
  var dialog_ylenh= new jBox('Modal', {
    title: "Cập nhật thời gian y lệnh",
    overlay: false,
    content: $('#dialog_capnhatthoigian_ylenh'),
    position: {
      y: 'center'
    },
    width: 400,
    height: 100,
    draggable: 'title'
  });
  var dialog_th_ylenh= new jBox('Modal', {
    title: "Cập nhật thời gian thực hiện y lệnh",
    overlay: false,
    content: $('#dialog_capnhatthoigian_thuchien_ylenh'),
    position: {
      y: 'center'
    },
    width: 400,
    height: 100,
    draggable: 'title'
  });
  var dialog_ketqua= new jBox('Modal', {
    title: "Cập nhật thời gian kết quả",
    overlay: false,
    content: $('#dialog_capnhatthoigian_ketqua'),
    position: {
      y: 'center'
    },
    width: 400,
    height: 100,
    draggable: 'title'
  });
  var dialog_capgiuong = new jBox('Modal', {
    title: "Cập nhật thời gian cấp giường ",
    overlay: false,
    content: $('#dialog_capnhatthoigian_capgiuong'),
    position: {
      y: 'center'
    },
    width: 400,
    height: 100,
    draggable: 'title'
  });
  var dialog_capsogiuong = new jBox('Modal', {
    title: "Cập nhật cấp số giường",
    overlay: false,
    content: $('#dialog_capnhatthoigian_capsogiuong'),
    position: {
      y: 'center'
    },
    width: 400,
    height: 100,
    draggable: 'title'
  });

  var dialog_nguoicapgiuong = new jBox('Modal', {
    title: "Cập nhật người cấp",
    overlay: false,
    content: $('#dialog_capnhatnguoicap_giuong'),
    position: {
      y: 'center'
    },
    width: 400,
    height: 250,
    draggable: 'title'
  });
  var dialog_capnhatngaythuoc = new jBox('Modal', {
    title: "Cập nhật ngày thuốc ",
    overlay: false,
    content: $('#dialog_capnhatngaythuoc'),
    position: {
      y: 'center'
    },
    width: 800,
    height: 100,
    draggable: 'title'
  });
  var dialog_capnhatthoigian_ylenhthuoc = new jBox('Modal', {
    title: "Cập nhật thời gian y lệnh thuốc",
    overlay: false,
    content: $('#dialog_capnhatthoigian_ylenhthuoc'),
    position: {
      y: 'center'
    },
    width: 800,
    height: 100,
    draggable: 'title'
  });
  var sovaovien;
  var sovaovien_dt;
  var sophieu_cd;
  var ma_dv;
  var loai_dv;
  var tableDVKT = "";
  var tenbenhvien;
  function listTabCongkhamInit(){
    $("#list_tabcongkham").jqGrid({
      url: '',
      datatype: "local",
      shrinkToFit: false,
      ignoreCase: true,
      autowidth: true,
      loadonce: true,
      colNames: ["TRẠNG THÁI","MÃ BN", "TÊN BỆNH NHÂN",
        "GIỜ Y LỆNH", "NGÀY VÀO VIỆN", "NGÀY RA VIỆN",
        "MÃ ĐKKCB",
        "MÃ LOẠI KCB",
        "MÃ ĐỐI TƯỢNG",
        "CÔNG KHÁM","ĐƠN GIÁ", "BÁC SĨ",
        "CHỨNG CHI HÀNH NGHỀ","MÃ KHOA", "NOITRU", "SOVAOVIEN", "SOVAOVIEN_DT", "MA_PHONG_BENH_CD_B3",
        "MA_BAC_SI",
        "MA_LOAIKCB_XML",
        "MA_DOI_TUONG_KCB",
      ],
      colModel: [
        {name: 'LOI', index: 'LOI', width: 150, formatter: function (cellvalue, options, rowObject) {
            var color = 'white';
            var color_text = 'black';
            var loi = "OK"

            if (cellvalue == "SAICONGKHAM") {
              color = 'red';
              color_text = 'white';
              loi = "SAICONGKHAM"
            } else {
              if (cellvalue != 'OK') {
                color = 'red';
                color_text = 'white';
                loi = cellvalue
              }
            }

            return '<span class="cellWithoutBackground" style="background-color:' + color + ';color:' + color_text + ';white-space: pre-wrap !important;">' + loi + '</span>';
          }
        },
        {name: 'MABENHNHAN', index: 'MABENHNHAN', width: 80},
        {name: 'TENBENHNHAN', index: 'TENBENHNHAN', width: 180},
        {name: 'NGAYGIO_YLENH', index: 'NGAYGIO_YLENH', width: 135},
        {name: 'NGAY_VAO_VIEN', index: 'NGAY_VAO_VIEN', width: 135},
        {name: 'NGAY_RA_VIEN', index: 'NGAY_RA_VIEN', width: 135},
        {name: 'MA_DKBD', index: 'MA_DKBD', width: 130},
        {name: 'MA_LOAIKCB_XML_HT', index: 'MA_LOAIKCB_XML_HT', width: 135, formatter: function (cellvalue, options, rowObject) {
            var objectKCB = {
              '1': 'Khám bệnh',
              '2': 'Điều trị ngoại trú',
              '3': 'Điều trị nội trú.',
              '4': 'Điều trị nội trú ban ngày.',
              '5': 'Điều trị ngoại trú các bệnh mạn tính dài ngày liên tục trong năm (có khám bệnh và lĩnh thuốc)',
              '6': 'Điều trị lưu tại Trạm Y tế tuyến xã, Phòng khám đa khoa khu vực.',
              '7': 'Nhận thuốc theo hẹn (không khám bệnh).',
              '8': 'Điều trị ngoại trú các bệnh mạn tính dài ngày liên tục trong năm (có thực hiện các dịch vụ kỹ thuật và/hoặc được sử dụng thuốc)',
              '9': 'Điều trị nội trú dưới 04 (bốn) giờ.',
              '10': 'Các trường hợp khác.',
            }

            return rowObject.MA_LOAIKCB_XML + " - " + objectKCB[rowObject.MA_LOAIKCB_XML];
          }
        },
        {name: 'MA_DOI_TUONG_KCB_HT', index: 'MA_DOI_TUONG_KCB_HT', width: 135, formatter: function (cellvalue, options, rowObject) {
            var objectMadoituong = {
              '7.1':'Người bệnh đến cơ sở khám bệnh, chữa bệnh lĩnh thuốc',
              '7':'Lĩnh thuốc ngoài viện do trường hợp bất khả kháng',
              '3.4':'Trường hợp tự đi khám bệnh, chữa bệnh ngoại trú tại cơ sở khám bệnh, chữa bệnh tuyến trung ương....',
              '7.3':'Cơ sở khám bệnh, chữa bệnh chuyển thuốc cho cơ sở khám bệnh, chữa bệnh khác',
              '1.3':'Các trường hợp quy định tại các điểm a, b khoản 4 Điều 6 Thông tư số 30/2020/TT-BYT',
              '1.5':'Trường hợp quy định tại khoản 6 Điều 6 Thông tư số 30/2020/TT-BYT',
              '3.7':'Các trường hợp khác không thuộc một trong các mã từ mã 3.1 đến mã 3.5 ....',
              '7':'Lĩnh thuốc ngoài viện do trường hợp bất khả kháng - CV3153',
              '2':'Cấp cứu',
              '3.1':'Các trường hợp tự đi khám bệnh, chữa bệnh được chỉ định điều trị nội trú, nội trú ban ngày tại các bệnh viện tuyến trung ương theo quy định tại điểm a khoản 3 Điều 22 Luật bảo hiểm y tế',
              '7.4':'Cơ sở khám bệnh, chữa bệnh chuyển thuốc đến cho người nhận',
              '1.4':'Các trường hợp quy định tại khoản 5 Điều 6 Thông tư số 30/2020/TT-BYT',
              '1.1':'Trường hợp quy định tại khoản 1 Điều 6 Thông tư số 30/2020/TT-BYT',
              '1.2':'Các trường hợp quy định tại khoản 2 Điều 6 Thông tư số 30/2020/TT-BYT',
              '7.2':'Người bệnh ủy quyền cho người khác đến cơ sở khám bệnh, chữa bệnh lĩnh thuốc',
              '1.7':'Trường hợp quy định tại khoản 8 Điều 6 Thông tư số 30/2020/TT-BYT',
              '1.8':'Các trường hợp KBCB theo quy định đối với người mắc bệnh lao theo quy định tại khoản 2 Điều 6 Thông tư số 04/2016/TT-BYT',
              '1.6':'Trường hợp quy định tại khoản 7 Điều 6 Thông tư số 30/2020/TT-BYT',
              '1.9':'Các trường hợp khám bệnh, chữa bệnh theo quy định đối với người mắc bệnh HIV/AIDS',
              '3.3':'Các trường hợp tự đi khám bệnh, chữa bệnh tại các bệnh viện tuyến huyện trên phạm vi cả nước theo quy định tại điểm c khoản 3 Điều 22 Luật bảo hiểm y tế...',
              '3.2':'Các trường hợp tự đi khám bệnh, chữa bệnh được chỉ định điều trị nội trú, nội trú ban ngày tại các bệnh viện tuyến tỉnh theo quy định tại điểm b khoản 3 và khoản 6 Điều 22 Luật bảo hiểm y tế',
              '3.5':'Trường hợp tự đi khám bệnh, chữa bệnh ngoại trú tại cơ sở khám bệnh, chữa bệnh tuyến tỉnh...',
              '8':'Thu hồi đề nghị thanh toán',
              '9':'Khám bệnh, chữa bệnh dịch vụ',
              '1.10':'Các trường hợp khám bệnh, chữa bệnh theo quy định đối với người mắc bệnh COVID-19',
              '3.6':'Các trường hợp quy định tại khoản 5 Điều 22 luật bảo hiểm y tế tự đi khám bệnh, chữa bệnh không đúng tuyến'

            }
            return rowObject.MA_DOI_TUONG_KCB + " - " + objectMadoituong[rowObject.MA_DOI_TUONG_KCB];
          }
        },
        {name: 'CONGKHAM', index: 'CONGKHAM', width: 180},
        {name: 'DON_GIA', index: 'DON_GIA', width: 80},
        {name: 'BACSI', index: 'BACSI', width: 100},
        {name: 'CHUNGCHIHANHNGHE', index: 'CHUNGCHIHANHNGHE', width: 180},
        {name: 'MAKHOA', index: 'MAKHOA', width: 100},
        {name: 'NOITRU', index: 'NOITRU', width: 100, hidden: true},
        {name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 100, hidden: true},
        {name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', width: 100, hidden: true},
        {name: 'MA_PHONG_BENH_CD_B3', index: 'MA_PHONG_BENH_CD_B3', width: 100, hidden: true},
        {name: 'MA_BAC_SI', index: 'MA_BAC_SI', width: 100, hidden: true},
        {name: 'MA_DOI_TUONG_KCB', index: 'MA_DOI_TUONG_KCB', width: 100, hidden: true},
        {name: 'MA_LOAIKCB_XML', index: 'MA_LOAIKCB_XML', width: 100, hidden: true},

      ],
      rowNum: 10000,
      height: '500',
      footerrow: true,
      rownumbers: true,
      rownumWidth: 25,
      onRightClickRow: function (id1) {
        if (id1) {
          var ret = $("#list_tabcongkham").jqGrid('getRowData', id1);
          console.log("ret", ret);
          $.contextMenu({
            selector: '#list_tabcongkham tr',
            callback: function (key, options) {
              if (key == "capnhat") {
                var arr = ["${Sess_DVTT}", sovaovien,$("#hinhthuc").val()];
                console.log("convertArray(arr)", convertArray(arr))
                var url = "cmu_list_cmu_dsbs_congkham?url=" + convertArray(arr);
                $("#cmu_ds_bacsi").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');

                dialog_mapthemdinhmuc.open();
              }
              var idSelect = $("#list_tabcongkham").jqGrid ('getGridParam', 'selrow');
              ret = $("#list_tabcongkham").jqGrid('getRowData', idSelect);
              if (key == "capnhatngayhtkham" && ret.NOITRU ==0 ) {

                var ngayravien = ret.NGAY_RA_VIEN;
                $("#ngayhoantatkham").val(ngayravien.split(" ")[0]);
                $("#cmuthoaigianhtkham").val(ngayravien.split(" ")[1])

                dialog_capnhatthoigianhtkham.open();
              }
              if (key == "capnhatthoigianylenh" && ret.NOITRU ==0 ) {
                var ngayylenh = ret.NGAYGIO_YLENH;
                $("#ngayhoantatkham_2").val(ngayylenh.split(" ")[0]);
                $("#cmuthoigianylenh").val(ngayylenh.split(" ")[1])

                dialog_capnhatylenhtkham.open();
              }
            },
            items: {
              "capnhat": {name: "Cập nhật bác sĩ"},
              "capnhatngayhtkham": {name: "Cập nhật thời gian hoàn tất khám"},
              "capnhatthoigianylenh": {name: "Cập nhật thời gian y lệnh"},
            }
          });
        }
      },
      onSelectRow: function(id) {
        var ret = $("#list_tabcongkham").jqGrid('getRowData', id);
        sovaovien= ret.SOVAOVIEN;
        tenbenhvien= ret.TENBENHNHAN;
        console.log("ret", ret);
      }
    });
    $("#list_tabcongkham").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
  }

  function listTabXQUANGInit(){
    $("#list_tabcdha").jqGrid({
      url: '',
      datatype: "local",
      shrinkToFit: false,
      autowidth: true,
      ignoreCase: true,
      loadonce: true,
      colNames:["TRẠNG THÁI","MÃ BN", "TÊN BỆNH NHÂN", "NGÀY VÀO VIỆN", "NGÀY RA VIỆN", "NGÀY Y LỆNH", "NGÀY TH Y LỆNH", "NGÀY KẾT QUẢ", "TÊN DỊCH VỤ","ĐƠN GIÁ",
        "BÁC SĨ CHỈ ĐỊNH", "NGƯỜI THỰC HIỆN", "NGƯỜI ĐỌC KẾT QUẢ", "DS CCHN","MÃ KHOA","NOITRU", "SOVAOVIEN",
        "SOVAOVIEN_DT", "SOPHIEU_CHIDINH", "MA_DICH_VU",
        "LOAI_KYTHUAT","MA_BAC_SI_CD", "MA_BAC_SI_DOCKQ"
      ],
      colModel: [
        {name: 'LOI', index: 'LOI', width: 150},
        {name: 'MABENHNHAN', index: 'MABENHNHAN', width: 80},
        {name: 'TENBENHNHAN', index: 'TENBENHNHAN', width: 180},
        {name: 'NGAY_VAO_VIEN', index: 'NGAY_VAO_VIEN', width: 135},
        {name: 'NGAY_RA_VIEN', index: 'NGAY_RA_VIEN', width: 135},
        {name: 'NGAYYLENH', index: 'NGAYYLENH', width: 135},
        {name: 'NGAY_TH_YLENH', index: 'NGAY_TH_YLENH', width: 135},
        {name: 'NGAYKETQUA', index: 'NGAYKETQUA', width: 135},
        {name: 'CONGKHAM', index: 'CONGKHAM', width: 180},
        {name: 'DON_GIA', index: 'DON_GIA', width: 80},
        {name: 'BACSI_CHIDINH', index: 'BACSI_CHIDINH', width: 150},
        {name: 'KYTHUATVIEN', index: 'KYTHUATVIEN', width: 150},
        {name: 'BACSI', index: 'BACSI', width: 150},
        {name: 'CHUNGCHIHANHNGHE', index: 'CHUNGCHIHANHNGHE', width: 180},
        {name: 'MAKHOA', index: 'MAKHOA', width: 100},
        {name: 'NOITRU', index: 'NOITRU', width: 100, hidden: true},
        {name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 100, hidden: true},
        {name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', width: 100, hidden: true},
        {name: 'SOPHIEU_CHIDINH', index: 'SOPHIEU_CHIDINH', width: 100, hidden: true},
        {name: 'MA_DICH_VU', index: 'MA_DICH_VU', width: 100, hidden: true},
        {name: 'LOAI_KYTHUAT', index: 'LOAI_KYTHUAT', width: 100, hidden: true},
        {name: 'MA_BAC_SI_CD', index: 'MA_BAC_SI_CD', width: 100, hidden: true},
        {name: 'MA_BAC_SI_DOCKQ', index: 'MA_BAC_SI_DOCKQ', width: 100, hidden: true},

      ],
      rowNum: 10000,
      height: '500',
      footerrow: true,
      rownumbers: true,
      rownumWidth: 25,
      userDataOnFooter: true,
      gridview : false,
      afterInsertRow: function(rowid, aData) {
        if (!!aData.LOI && aData.LOI != 'OK') {
          $('#list_tabcdha').setCell(rowid, 'LOI','','jqgrid-cell-error');
        }
      },
      loadComplete: function () {

      },
      onRightClickRow: function (id1) {
        if (id1) {
          var ret = $("#list_tabcdha").jqGrid('getRowData', id1);
          console.log("ret", ret);
          $.contextMenu({
            selector: '#list_tabcdha tr',
            callback: function (key, options) {
              if (key == "capnhatylenh") {
                //Kiểm tra khóa đã khám - ra viện khóa bảng kê
                var ktraAjax = $.ajax({type: "POST", url: "cmu_post_cmu_kttrangthai_cls", async: false,
                  data:  {url: ["${Sess_DVTT}", ret.SOVAOVIEN, ret.SOVAOVIEN, ret.SOVAOVIEN_DT, ret.NOITRU].join('```')}
                });
                if(ktraAjax.status != 200 || ktraAjax.responseText > 0){
                  var alert = ktraAjax.status != 200 ? ("Lỗi: " + ktraAjax.statusText) : "Bệnh nhân đã khóa dữ liệu, không thể thao tác.";
                  jAlert(alert);
                  return;
                }//
                dialog_ylenh.open();
              }

              if (key == "capnhatthuchienylenh") {
                //Kiểm tra khóa đã khám - ra viện khóa bảng kê
                var ktraAjax = $.ajax({type: "POST", url: "cmu_post_cmu_kttrangthai_cls", async: false,
                  data:  {url: ["${Sess_DVTT}", ret.SOVAOVIEN, ret.SOVAOVIEN, ret.SOVAOVIEN_DT, ret.NOITRU].join('```')}
                });
                if(ktraAjax.status != 200 || ktraAjax.responseText > 0){
                  var alert = ktraAjax.status != 200 ? ("Lỗi: " + ktraAjax.statusText) : "Bệnh nhân đã khóa dữ liệu, không thể thao tác.";
                  jAlert(alert);
                  return;
                }//
                dialog_th_ylenh.open();
              }

              if (key == "capnhatketqua") {
                //Kiểm tra khóa đã khám - ra viện khóa bảng kê
                var ktraAjax = $.ajax({type: "POST", url: "cmu_post_cmu_kttrangthai_cls", async: false,
                  data:  {url: ["${Sess_DVTT}", ret.SOVAOVIEN, ret.SOVAOVIEN, ret.SOVAOVIEN_DT, ret.NOITRU].join('```')}
                });
                if(ktraAjax.status != 200 || ktraAjax.responseText > 0){
                  var alert = ktraAjax.status != 200 ? ("Lỗi: " + ktraAjax.statusText) : "Bệnh nhân đã khóa dữ liệu, không thể thao tác.";
                  jAlert(alert);
                  return;
                }//
                dialog_ketqua.open();
              }
              if (key == "capnhatbsdoc") {
                $("#cmu_luubacsi_cdha_ktv").hide()
                $("#cmu_luubacsi_cdha_nguoidoc").show()
                dialog_bacsi_cdha.open();
              }
              if (key == "capnhatkythuatvien") {
                $("#cmu_luubacsi_cdha_ktv").show()
                $("#cmu_luubacsi_cdha_nguoidoc").hide()
                dialog_bacsi_cdha.open();
              }
            },
            items: {
              "capnhatylenh": {name: "Cập nhật thời gian y lệnh"},
              "capnhatthuchienylenh": {name: "Cập nhật thời gian thực hiện y lệnh"},
              "capnhatketqua": {name: "Cập nhật thời gian kết quả"},
              "capnhatkythuatvien": {name: "Cập nhật người thực hiện"},
              "capnhatbsdoc": {name: "Cập nhật người đọc kết quả"},
            }
          });
        }
      },
      onSelectRow: function(id) {
        var ret = $("#list_tabcdha").jqGrid('getRowData', id);
        sovaovien= ret.SOVAOVIEN;
        sophieu_cd= ret.SOPHIEU_CHIDINH;
        ma_dv = ret.MA_DICH_VU;
        loai_dv = 'CDHA'
        tableDVKT = 'list_tabcdha'

      }
    });
    $("#list_tabcdha").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
  }

  function listTabXNInit(){
    $("#list_tabxetnghiem").jqGrid({
      url: '',
      datatype: "local",
      shrinkToFit: false,
      autowidth: true,
      ignoreCase: true,
      loadonce: true,
      colNames:["TRẠNG THÁI","MÃ BN", "TÊN BỆNH NHÂN", 'NGÀY VÀO VIỆN', "NGÀY RA VIỆN", "NGÀY Y LỆNH", "NGÀY TH Y LỆNH",
        "NGÀY KẾT QUẢ", "TÊN DỊCH VỤ","ĐƠN GIÁ","BÁC SĨ CHỈ ĐỊNH", "NGƯỜI THỰC HIỆN", "NGƯỜI ĐỌC KẾT QUẢ", "DS CNHN","MÃ KHOA","NOITRU", "SOVAOVIEN", "SOVAOVIEN_DT",
        "SOPHIEU_CHIDINH", "MA_DICH_VU","LOAI_KYTHUAT","MA_BAC_SI_CD", "MA_BAC_SI_DOCKQ"],
      colModel: [
        {name: 'LOI', index: 'LOI', width: 150},
        {name: 'MABENHNHAN', index: 'MABENHNHAN', width: 80},
        {name: 'TENBENHNHAN', index: 'TENBENHNHAN', width: 180},
        {name: 'NGAY_VAO_VIEN', index: 'NGAY_VAO_VIEN', width: 135},
        {name: 'NGAY_RA_VIEN', index: 'NGAY_RA_VIEN', width: 135},
        {name: 'NGAYYLENH', index: 'NGAYYLENH', width: 135},
        {name: 'NGAY_TH_YLENH', index: 'NGAY_TH_YLENH', width: 135},
        {name: 'NGAYKETQUA', index: 'NGAYKETQUA', width: 135},
        {name: 'CONGKHAM', index: 'CONGKHAM', width: 180},
        {name: 'DON_GIA', index: 'DON_GIA', width: 80},
        {name: 'BACSI_CHIDINH', index: 'BACSI_CHIDINH', width: 100},
        {name: 'KYTHUATVIEN', index: 'KYTHUATVIEN', width: 100},
        {name: 'BACSI', index: 'BACSI', width: 100},
        {name: 'CHUNGCHIHANHNGHE', index: 'CHUNGCHIHANHNGHE', width: 180},
        {name: 'MAKHOA', index: 'MAKHOA', width: 100},
        {name: 'NOITRU', index: 'NOITRU', width: 100, hidden: true},
        {name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 100, hidden: true},
        {name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', width: 100, hidden: true},
        {name: 'SOPHIEU_CHIDINH', index: 'SOPHIEU_CHIDINH', width: 100, hidden: true},
        {name: 'MA_DICH_VU', index: 'MA_DICH_VU', width: 100, hidden: true},
        {name: 'LOAI_KYTHUAT', index: 'LOAI_KYTHUAT', width: 100, hidden: true},
        {name: 'MA_BAC_SI_CD', index: 'MA_BAC_SI_CD', width: 100, hidden: true},
        {name: 'MA_BAC_SI_DOCKQ', index: 'MA_BAC_SI_DOCKQ', width: 100, hidden: true},
      ],
      rowNum: 10000,
      height: '500',
      footerrow: true,
      rownumbers: true,
      rownumWidth: 25,
      userDataOnFooter: true,
      loadComplete: function () {

      },
      gridview : false,
      afterInsertRow: function(rowid, aData) {
        if (!!aData.LOI && aData.LOI != 'OK') {
          $('#list_tabxetnghiem').setCell(rowid, 'LOI','','jqgrid-cell-error');
        }
      },
      onRightClickRow: function (id1) {
        if (id1) {
          var ret = $("#list_tabxetnghiem").jqGrid('getRowData', id1);
          $.contextMenu({
            selector: '#list_tabxetnghiem tr',
            callback: function (key, options) {
              if (key == "capnhatylenh") {
                //Kiểm tra khóa đã khám - ra viện khóa bảng kê
                var ktraAjax = $.ajax({type: "POST", url: "cmu_post_cmu_kttrangthai_cls", async: false,
                  data:  {url: ["${Sess_DVTT}", ret.SOVAOVIEN, ret.SOVAOVIEN, ret.SOVAOVIEN_DT, ret.NOITRU].join('```')}
                });
                if(ktraAjax.status != 200 || ktraAjax.responseText > 0){
                  var alert = ktraAjax.status != 200 ? ("Lỗi: " + ktraAjax.statusText) : "Bệnh nhân đã khóa dữ liệu, không thể thao tác.";
                  jAlert(alert);
                  return;
                }//
                dialog_ylenh.open();
              }
              if (key == "capnhatthuchienylenh") {
                //Kiểm tra khóa đã khám - ra viện khóa bảng kê
                var ktraAjax = $.ajax({type: "POST", url: "cmu_post_cmu_kttrangthai_cls", async: false,
                  data:  {url: ["${Sess_DVTT}", ret.SOVAOVIEN, ret.SOVAOVIEN, ret.SOVAOVIEN_DT, ret.NOITRU].join('```')}
                });
                if(ktraAjax.status != 200 || ktraAjax.responseText > 0){
                  var alert = ktraAjax.status != 200 ? ("Lỗi: " + ktraAjax.statusText) : "Bệnh nhân đã khóa dữ liệu, không thể thao tác.";
                  jAlert(alert);
                  return;
                }//
                dialog_th_ylenh.open();
              }
              if (key == "capnhatketqua") {
                //Kiểm tra khóa đã khám - ra viện khóa bảng kê
                var ktraAjax = $.ajax({type: "POST", url: "cmu_post_cmu_kttrangthai_cls", async: false,
                  data:  {url: ["${Sess_DVTT}", ret.SOVAOVIEN, ret.SOVAOVIEN, ret.SOVAOVIEN_DT, ret.NOITRU].join('```')}
                });
                if(ktraAjax.status != 200 || ktraAjax.responseText > 0){
                  var alert = ktraAjax.status != 200 ? ("Lỗi: " + ktraAjax.statusText) : "Bệnh nhân đã khóa dữ liệu, không thể thao tác.";
                  jAlert(alert);
                  return;
                }//
                dialog_ketqua.open();
              }
              if (key == "capnhatbsdoc") {
                $("#cmu_luubacsi_xn_ktv").hide()
                $("#cmu_luubacsi_xn_nguoidoc").show()
                dialog_bacsi_xn.open();
              }
              if (key == "capnhatkythuatvien") {
                $("#cmu_luubacsi_xn_ktv").show()
                $("#cmu_luubacsi_xn_nguoidoc").hide()
                dialog_bacsi_xn.open();
              }
            },
            items: {
              "capnhatylenh": {name: "Cập nhật thời gian y lệnh"},
              "capnhatthuchienylenh": {name: "Cập nhật thời gian thực hiện y lệnh"},
              "capnhatketqua": {name: "Cập nhật thời gian kết quả"},
              "capnhatkythuatvien": {name: "Cập nhật người thực hiện"},
              "capnhatbsdoc": {name: "Cập nhật người đọc kết quả"},
            }
          });
        }
      },
      onSelectRow: function(id) {
        var ret = $("#list_tabxetnghiem").jqGrid('getRowData', id);
        sovaovien= ret.SOVAOVIEN;
        sophieu_cd= ret.SOPHIEU_CHIDINH;
        ma_dv = ret.MA_DICH_VU;
        loai_dv = 'XETNGHIEM'
        tableDVKT = 'list_tabxetnghiem'

      }
    });
    $("#list_tabxetnghiem").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
  }

  function listTabThuthuatphauthuatInit(){
    $("#list_tabthuthuatphauthuat").jqGrid({
      url: '',
      datatype: "local",
      shrinkToFit: false,
      autowidth: true,
      ignoreCase: true,
      loadonce: true,
      colNames:["TRẠNG THÁI","MÃ BN", "TÊN BỆNH NHÂN", "NGÀY VÀO VIỆN", "NGÀY RA VIỆN", "NGÀY Y LỆNH", "NGÀY TH Y LỆNH",
        "NGÀY KẾT QUẢ", "TÊN DỊCH VỤ","ĐƠN GIÁ", "BÁC SĨ TH", "CNHN BSTH","MÃ KHOA","NOITRU", "SOVAOVIEN", "SOVAOVIEN_DT",
        "SOPHIEU_CHIDINH", "MA_DICH_VU", "LOAI_KYTHUAT", "MA_BAC_SI_CD", "MA_BAC_SI_DOCKQ"],
      colModel: [
        {name: 'LOI', index: 'LOI', width: 150},
        {name: 'MABENHNHAN', index: 'MABENHNHAN', width: 80},
        {name: 'TENBENHNHAN', index: 'TENBENHNHAN', width: 180},
        {name: 'NGAY_VAO_VIEN', index: 'NGAY_VAO_VIEN', width: 135},
        {name: 'NGAY_RA_VIEN', index: 'NGAY_RA_VIEN', width: 135},
        {name: 'NGAYYLENH', index: 'NGAYYLENH', width: 135},
        {name: 'NGAY_TH_YLENH', index: 'NGAY_TH_YLENH', width: 135},
        {name: 'NGAYKETQUA', index: 'NGAYKETQUA', width: 135},
        {name: 'CONGKHAM', index: 'CONGKHAM', width: 180},
        {name: 'DON_GIA', index: 'DON_GIA', width: 80},
        {name: 'BACSI', index: 'BACSI', width: 100},
        {name: 'CHUNGCHIHANHNGHE', index: 'CHUNGCHIHANHNGHE', width: 180},
        {name: 'MAKHOA', index: 'MAKHOA', width: 100},
        {name: 'NOITRU', index: 'NOITRU', width: 100, hidden: true},
        {name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 100, hidden: true},
        {name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', width: 100, hidden: true},
        {name: 'SOPHIEU_CHIDINH', index: 'SOPHIEU_CHIDINH', width: 100, hidden: true},
        {name: 'MA_DICH_VU', index: 'MA_DICH_VU', width: 100, hidden: true},
        {name: 'LOAI_KYTHUAT', index: 'LOAI_KYTHUAT', width: 100, hidden: true},
        {name: 'MA_BAC_SI_CD', index: 'MA_BAC_SI_CD', width: 100, hidden: true},
        {name: 'MA_BAC_SI_DOCKQ', index: 'MA_BAC_SI_DOCKQ', width: 100, hidden: true},
      ],
      rowNum: 10000,
      height: '500',
      footerrow: true,
      rownumbers: true,
      rownumWidth: 25,
      userDataOnFooter: true,
      loadComplete: function () {

      },
      gridview : false,
      afterInsertRow: function(rowid, aData) {
        if (!!aData.LOI && aData.LOI != 'OK') {
          $('#list_tabthuthuatphauthuat').setCell(rowid, 'LOI','','jqgrid-cell-error');
        }
      },
      onRightClickRow: function (id1) {
        if (id1) {
          var ret = $("#list_tabthuthuatphauthuat").jqGrid('getRowData', id1);
          console.log("ret", ret);
          $.contextMenu({
            selector: '#list_tabthuthuatphauthuat tr',
            callback: function (key, options) {
              if (key == "capnhatylenh") {
                //Kiểm tra khóa đã khám - ra viện khóa bảng kê
                var ktraAjax = $.ajax({type: "POST", url: "cmu_post_cmu_kttrangthai_cls", async: false,
                  data:  {url: ["${Sess_DVTT}", ret.SOVAOVIEN, ret.SOVAOVIEN, ret.SOVAOVIEN_DT, ret.NOITRU].join('```')}
                });
                if(ktraAjax.status != 200 || ktraAjax.responseText > 0){
                  var alert = ktraAjax.status != 200 ? ("Lỗi: " + ktraAjax.statusText) : "Bệnh nhân đã khóa dữ liệu, không thể thao tác.";
                  jAlert(alert);
                  return;
                }//
                dialog_ylenh.open();
              }
              if (key == "capnhatthuchienylenh") {
                //Kiểm tra khóa đã khám - ra viện khóa bảng kê
                var ktraAjax = $.ajax({type: "POST", url: "cmu_post_cmu_kttrangthai_cls", async: false,
                  data:  {url: ["${Sess_DVTT}", ret.SOVAOVIEN, ret.SOVAOVIEN, ret.SOVAOVIEN_DT, ret.NOITRU].join('```')}
                });
                if(ktraAjax.status != 200 || ktraAjax.responseText > 0){
                  var alert = ktraAjax.status != 200 ? ("Lỗi: " + ktraAjax.statusText) : "Bệnh nhân đã khóa dữ liệu, không thể thao tác.";
                  jAlert(alert);
                  return;
                }//
                dialog_th_ylenh.open();
              }
              if (key == "capnhatketqua") {
                //Kiểm tra khóa đã khám - ra viện khóa bảng kê
                var ktraAjax = $.ajax({type: "POST", url: "cmu_post_cmu_kttrangthai_cls", async: false,
                  data:  {url: ["${Sess_DVTT}", ret.SOVAOVIEN, ret.SOVAOVIEN, ret.SOVAOVIEN_DT, ret.NOITRU].join('```')}
                });
                if(ktraAjax.status != 200 || ktraAjax.responseText > 0){
                  var alert = ktraAjax.status != 200 ? ("Lỗi: " + ktraAjax.statusText) : "Bệnh nhân đã khóa dữ liệu, không thể thao tác.";
                  jAlert(alert);
                  return;
                }//
                dialog_ketqua.open();
              }
            },
            items: {
              "capnhatylenh": {name: "Cập nhật thời gian y lệnh"},
              "capnhatthuchienylenh": {name: "Cập nhật thời gian thực hiện y lệnh"},
              "capnhatketqua": {name: "Cập nhật thời gian kết quả"},
            }
          });
        }
      },
      onSelectRow: function(id) {
        var ret = $("#list_tabthuthuatphauthuat").jqGrid('getRowData', id);
        sovaovien= ret.SOVAOVIEN;
        sophieu_cd= ret.SOPHIEU_CHIDINH;
        ma_dv = ret.MA_DICH_VU;
        loai_dv = 'TTPT'
        tableDVKT = 'list_tabthuthuatphauthuat'

      }
    });
    $("#list_tabthuthuatphauthuat").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
  }

  function listVtytInit() {
    $("#list_vattu").jqGrid({
      url: '',
      datatype: "local",
      shrinkToFit: false,
      autowidth: true,
      ignoreCase: true,
      loadonce: true,
      colNames:  ["MÃ BN", "TÊN BỆNH NHÂN", "NGÀY RA VIỆN", "NGÀY Y LỆNH", "TÊN DỊCH VỤ",
        "ĐƠN GIÁ","SỐ LƯỢNG","TỶ LỆ TT", "BÁC SĨ", "QĐ", "MÃ BÁO CÁO",
        "SỐ CÔNG VĂN",  "CNHN BSTH","MÃ KHOA","SOVAOVIEN"],
      colModel: [
        {name: 'MABENHNHAN', index: 'MABENHNHAN', width: 80},
        {name: 'TENBENHNHAN', index: 'TENBENHNHAN', width: 180},
        {name: 'NGAY_RA_VIEN', index: 'NGAY_RA_VIEN', width: 100},
        {name: 'NGAYYLENH', index: 'NGAYYLENH', width: 100},
        {name: 'CONGKHAM', index: 'CONGKHAM', width: 180},
        {name: 'DON_GIA', index: 'DON_GIA', width: 80},
        {name: 'SOLUONG', index: 'SOLUONG', width: 80},
        {name: 'TYLE_TT', index: 'TYLE_TT', width: 60},
        {name: 'BACSI', index: 'BACSI', width: 100},
        {name: 'QUYETDINH', index: 'QUYETDINH', width: 100},
        {name: 'MABAOCAO', index: 'MABAOCAO', width: 100},
        {name: 'SOCONGVAN', index: 'SOCONGVAN', width: 50},
        {name: 'CHUNGCHIHANHNGHE', index: 'CHUNGCHIHANHNGHE', width: 180},
        {name: 'MAKHOA', index: 'MAKHOA', width: 100},
        {name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 100, hidden: true}
      ],
      rowNum: 10000,
      height: '500',
      footerrow: true,
      rownumbers: true,
      rownumWidth: 25,
      userDataOnFooter: true,
      grouping: true,
      loadComplete: function () {
      }
    });
    $("#list_vattu").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
  }
  function listGiuongInit() {
    $("#list_tabgiuongbenh").jqGrid({
      url: '',
      datatype: "local",
      shrinkToFit: false,
      autowidth: true,
      ignoreCase: true,
      loadonce: true,
      colNames: ["TRẠNG THÁI","MÃ BN", "TÊN BỆNH NHÂN",  "NGÀY VÀO VIỆN", "NGÀY RA VIỆN", "SỐ GIƯỜNG", "NGÀY GIỜ VÀO", "NGÀY GIỜ RA", "GIƯỜNG","ĐƠN GIÁ", "SỐ LƯỢNG",
        "TỶ LỆ TT","MÃ KHOA", "NHÂN VIÊN","SOVAOVIEN", "SOVAOVIEN_DT", "SOPHIEU_CHIDINH"],
      colModel: [
        {name: 'LOI', index: 'LOI', width: 150},
        {name: 'MABENHNHAN', index: 'MABENHNHAN', width: 80},
        {name: 'TENBENHNHAN', index: 'TENBENHNHAN', width: 180},
        {name: 'NGAY_VAO_VIEN', index: 'NGAY_VAO_VIEN', width: 135},
        {name: 'NGAY_RA_VIEN', index: 'NGAY_RA_VIEN', width: 135},
        {name: 'MA_GIUONG_B3', index: 'MA_GIUONG_B3', width: 135},
        {name: 'NGAYYLENH', index: 'NGAYYLENH', width: 135},
        {name: 'NGAYKETQUA', index: 'NGAYKETQUA', width: 135},
        {name: 'CONGKHAM', index: 'CONGKHAM', width: 180},
        {name: 'DON_GIA', index: 'DON_GIA', width: 80},
        {name: 'SO_LUONG', index: 'SO_LUONG', width: 100},
        {name: 'TYLE_TT', index: 'TYLE_TT', width: 60},
        {name: 'MAKHOA', index: 'MAKHOA', width: 100},
        {name: 'BACSI_CHIDINH', index: 'BACSI_CHIDINH', width: 100},
        {name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 100, hidden: true},
        {name: 'SOVAOVIEN_DT', index: 'SOVAOVIEN_DT', width: 100, hidden: true},
        {name: 'SOPHIEU_CHIDINH', index: 'SOPHIEU_CHIDINH', width: 100, hidden: true},

      ],
      rowNum: 10000,
      height: '500',
      footerrow: true,
      rownumbers: true,
      rownumWidth: 25,
      userDataOnFooter: true,
      gridview : false,
      afterInsertRow: function(rowid, aData) {
        if (!!aData.LOI && aData.LOI != 'OK') {
          $('#list_tabgiuongbenh').setCell(rowid, 'LOI','','jqgrid-cell-error');
        }
      },
      loadComplete: function () {
        var $self = $(this);
        var count_dv = $self.getGridParam("reccount");
        $self.jqGrid("footerData", "set", {CONGKHAM: "Tổng cộng: " + count_dv});
      },
      onRightClickRow: function (id1) {
        if (id1) {
          var ret = $("#list_tabgiuongbenh").jqGrid('getRowData', id1);
          console.log("ret", ret);
          $.contextMenu({
            selector: '#list_tabgiuongbenh tr',
            callback: function (key, options) {
              if (key == "capnhat") {

                dialog_capgiuong.open();
              }
              if (key == "capnhatsoguong") {

                dialog_capsogiuong.open();
              }
              if(key == 'nguoicap') {
                dialog_nguoicapgiuong.open();
              }
            },
            items: {
              "nguoicap": {name: "Cập nhật người cấp giường"},
              "capnhat": {name: "Cập nhật giờ cấp giường "},
              "capnhatsoguong": {name: "Cập nhật số cấp giường"},
            }
          });
        }
      },
      onSelectRow: function(id) {
        var ret = $("#list_tabgiuongbenh").jqGrid('getRowData', id);
        sovaovien= ret.SOVAOVIEN;
        console.log("ret", ret);
      }
    });
    $("#list_tabgiuongbenh").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
  }
  function listBntrathuocInit() {
    $("#list_tabthuoc").jqGrid({
      url: '',
      datatype: "local",
      shrinkToFit: false,
      autowidth: true,
      ignoreCase: true,
      loadonce: true,
      colNames: ["TRẠNG THÁI","MÃ BN", "TÊN BỆNH NHÂN", "NGÀY VÀO VIỆN", "NGÀY RA VIỆN",
        "NGÀY Y LỆNH",
        "NGÀY THỰC HIỆN Y LỆNH",
        "TÊN DỊCH VỤ",
        "ĐƠN GIÁ","SỐ LƯỢNG","TỶ LỆ TT", "Ngày uống", "BÁC SĨ", "MÃ ĐƯỜNG DÙNG","SỐ ĐĂNG KÝ", "QĐ", "MÃ BÁO CÁO",
        "GÓI THẦU", "NHÓM THẦU", "CNHN BSTH","MÃ KHOA","SOVAOVIEN", "STT_TOATHUOC_NGOAI", "SOVAOVIEN_DT_NOI",
        "STT_TOATHUOC", "MA_BAC_SI"],
      colModel: [
        {name: 'LOI', index: 'LOI', width: 80, cellattr: function (rowId, cellValue, rowObject) {
            if (cellValue != 'OK') {
              return "style='text-align:center; font-weight: bold; color:white;white-space: pre-wrap !important;'";
            }
            return "";
          }},
        {name: 'MABENHNHAN', index: 'MABENHNHAN', width: 80},
        {name: 'TENBENHNHAN', index: 'TENBENHNHAN', width: 180},
        {name: 'NGAY_VAO_VIEN', index: 'NGAY_VAO_VIEN', width: 100},
        {name: 'NGAY_RA_VIEN', index: 'NGAY_RA_VIEN', width: 100},
        {name: 'NGAYYLENH', index: 'NGAYYLENH', width: 100},
        {name: 'NGAYTHYLENH', index: 'NGAYTHYLENH', width: 100},
        {name: 'CONGKHAM', index: 'CONGKHAM', width: 180},
        {name: 'DON_GIA', index: 'DON_GIA', width: 80},
        {name: 'SOLUONG', index: 'SOLUONG', width: 80},
        {name: 'TYLE_TT', index: 'TYLE_TT', width: 60},
        {name: 'LIEU_DUNG', index: 'LIEU_DUNG', width: 160},
        {name: 'BACSI', index: 'BACSI', width: 100},
        {name: 'MADUONGDUNG', index: 'MADUONGDUNG', width: 100},
        {name: 'SODANGKY', index: 'SODANGKY', width: 100},
        {name: 'QUYETDINH', index: 'QUYETDINH', width: 100},
        {name: 'MABAOCAO', index: 'MABAOCAO', width: 100},
        {name: 'GOITHAU', index: 'GOITHAU', width: 50},
        {name: 'NHOMTHAU', index: 'NHOMTHAU', width: 50},
        {name: 'CHUNGCHIHANHNGHE', index: 'CHUNGCHIHANHNGHE', width: 180},
        {name: 'MAKHOA', index: 'MAKHOA', width: 100},
        {name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 100, hidden: true},
        {name: 'STT_TOATHUOC_NGOAI', index: 'STT_TOATHUOC_NGOAI', width: 100, hidden: true},
        {name: 'SOVAOVIEN_DT_NOI', index: 'SOVAOVIEN_DT_NOI', width: 100, hidden: true},
        {name: 'STT_TOATHUOC', index: 'STT_TOATHUOC', width: 100, hidden: true},
        {name: 'MA_BAC_SI', index: 'MA_BAC_SI', width: 100, hidden: true},
      ],
      rowNum: 100000,
      height: '500',
      footerrow: true,
      rownumbers: true,
      rownumWidth: 25,
      userDataOnFooter: true,
      afterInsertRow: function(rowid, aData) {
        console.log("aData", aData.LOI != 'OK', aData.LOI);
        if (aData.LOI && aData.LOI != 'OK') {
          console.log("tét", aData.LOI);
          $('#list_tabthuoc').setCell(rowid, 'LOI','','jqgrid-cell-error');
        }
      },
      loadComplete: function () {

      }
      , onRightClickRow: function (id1) {
        if (id1) {
          var ret = $("#list_tabthuoc").jqGrid('getRowData', id1);
          console.log("ret", ret);
          $.contextMenu({
            selector: '#list_tabthuoc tr',
            callback: function (key, options) {
              if (key == "capnhatbacsi") {
                dialog_bacsi.open();
              }
              var id= $("#list_tabthuoc").jqGrid('getGridParam', 'selrow');
              var ret = $("#list_tabthuoc").jqGrid('getRowData', id);
              if (key == "capnhatngayuong") {
                if (ret.SOVAOVIEN_DT_NOI == 0) {
                  $("#thuoc_ngayuong").val(0);
                  $("#thuoc_sanguong").val(0);
                  $("#thuoc_truauong").val(0);
                  $("#thuoc_chieuuong").val(0);
                  $("#thuoc_toiuong").val(0);
                  $("#thuoc_dangthuoc").val("");
                  dialog_capnhatngaythuoc.open();
                } else {
                  jAlert("Chỉ chỉnh bệnh nhân ngoại trú")
                }

              }
              if (key == "capnhatngayylenh") {
                $("#thoigianylenhthuoc").val(ret.NGAYYLENH);
                dialog_capnhatthoigian_ylenhthuoc.open();
              }
            },
            items: {
              "capnhatbacsi": {name: "Cập nhật bác sĩ"},
              "capnhatngayuong": {name: "Cập nhật ngày uống"},
              "capnhatngayylenh": {name: "Cập nhật ngày y lệnh"},
            }
          });
        }
      }
    });
    $("#list_tabthuoc").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
  }
  function listTabTrunggioylenhInit(){
    $("#list_tabtrunggioylenh").jqGrid({
      url: '',
      datatype: "local",
      shrinkToFit: false,
      ignoreCase: true,
      autowidth: true,
      loadonce: true,
      colModel: [

        {label: "MÃ BN", name: 'MABENHNHAN', index: 'MABENHNHAN', width: 80},
        {label: "TÊN BỆNH NHÂN",name: 'TENBENHNHAN', index: 'TENBENHNHAN', width: 180},
        {label: "TÊN DỊCH VỤ",name: 'TEN_DICH_VU', index: 'TEN_DICH_VU', width: 180},
        {label: "GIỜ Y LỆNH",name: 'NGAYYLENH', index: 'NGAYYLENH', width: 135},
        {label: "Bác sĩ chỉ định",name: 'TEN_NHANVIEN_CD', index: 'TEN_NHANVIEN_CD', width: 150},
        {label: "MA_BAC_SI_CD",name: 'MA_BAC_SI_CD', index: 'MA_BAC_SI_CD', width: 150, hidden: true},
        {label: "SOVAOVIEN",name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 100, hidden: true},
        {label: "LOAI_KYTHUAT",name: 'LOAI_KYTHUAT', index: 'LOAI_KYTHUAT', width: 100, hidden: true},
        {label: "NGAY_YL",name: 'NGAY_YL', index: 'NGAY_YL', width: 100, hidden: true},
      ],
      rowNum: 10000,
      height: '500',
      footerrow: true,
      rownumbers: true,
      rownumWidth: 25,
      userDataOnFooter: true,
      loadComplete: function () {
      },
      onRightClickRow: function (id1) {
        if (id1) {
          var ret = $("#list_tabtrunggioylenh").jqGrid('getRowData', id1);
          $.contextMenu({
            selector: '#list_tabtrunggioylenh tr',
            callback: function (key, options) {
              var idSelect = $("#list_tabtrunggioylenh").jqGrid ('getGridParam', 'selrow');
              ret = $("#list_tabtrunggioylenh").jqGrid('getRowData', idSelect);
              if (key == "xemchitiet") {
                cmuNgoaitruktdulieutgkhamRavien({
                  dvtt: "${Sess_DVTT}",
                  sovaovien: ret.SOVAOVIEN,
                  userId: ret.MA_BAC_SI_CD,
                  thoigian: ret.NGAY_YL,
                  loaikythuat: ret.LOAI_KYTHUAT
                })
              }
            },
            items: {
              "xemchitiet": {name: "Xem chi tiết"}
            }
          });
        }
      },
      onSelectRow: function(id) {
        var ret = $("#list_tabcongkham").jqGrid('getRowData', id);
        sovaovien= ret.SOVAOVIEN;
        tenbenhvien= ret.TENBENHNHAN;
        console.log("ret", ret);
      }
    });
    $("#list_tabtrunggioylenh").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
  }
  function listTabTrunggiothuchienInit(){
    $("#list_tabtrungthoigianthuchien").jqGrid({
      url: '',
      datatype: "local",
      shrinkToFit: false,
      ignoreCase: true,
      autowidth: true,
      loadonce: true,
      colModel: [

        {label: "MÃ BN", name: 'MABENHNHAN', index: 'MABENHNHAN', width: 80},
        {label: "TÊN BỆNH NHÂN",name: 'TENBENHNHAN', index: 'TENBENHNHAN', width: 180},
        {label: "NGÀY RA VIỆN",name: 'NGAY_RA_VIEN', index: 'NGAY_RA_VIEN', width: 180},
        {label: "TÊN DỊCH VỤ",name: 'TEN_DICH_VU', index: 'TEN_DICH_VU', width: 180},
        {label: "GIỜ BẮT ĐẦU",name: 'NGAYGIO_BD', index: 'NGAYGIO_BD', width: 135},
        {label: "GIỜ KẾT THÚC",name: 'NGAYGIO_KT', index: 'NGAYGIO_KT', width: 135},
        {label: "BÁC SĨ THỰC HIỆN ",name: 'TEN_NHANVIEN_CD', index: 'TEN_NHANVIEN_CD', width: 150},
        {label: "MA_BAC_SI_CD",name: 'MA_BAC_SI', index: 'MA_BAC_SI', width: 150, hidden: true},
        {label: "SOVAOVIEN",name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 100, hidden: true},
        {label: "LOAI_KYTHUAT",name: 'LOAI_KYTHUAT', index: 'LOAI_KYTHUAT', width: 100, hidden: true},
        {label: "NGAY_YL",name: 'NGAY_YL', index: 'NGAY_YL', width: 100, hidden: true},
      ],
      rowNum: 10000,
      height: '500',
      footerrow: true,
      rownumbers: true,
      rownumWidth: 25,
      userDataOnFooter: true,
      loadComplete: function () {
      },
      onRightClickRow: function (id1) {
        if (id1) {
          var ret = $("#list_tabtrungthoigianthuchien").jqGrid('getRowData', id1);
          $.contextMenu({
            selector: '#list_tabtrungthoigianthuchien tr',
            callback: function (key, options) {
              var idSelect = $("#list_tabtrungthoigianthuchien").jqGrid ('getGridParam', 'selrow');
              ret = $("#list_tabtrungthoigianthuchien").jqGrid('getRowData', idSelect);
              if (key == "xemchitiet") {
                cmuNgoaitruktdulieutgthuchienCLS({
                  dvtt: "${Sess_DVTT}",
                  sovaovien: ret.SOVAOVIEN,
                  userId: ret.MA_BAC_SI,
                  thoigianbd: ret.NGAYGIO_BD,
                  thoigiankt: ret.NGAYGIO_KT,
                  loaikythuat: ret.LOAI_KYTHUAT
                })
              }
            },
            items: {
              "xemchitiet": {name: "Xem chi tiết"}
            }
          });
        }
      },
      onSelectRow: function(id) {
        var ret = $("#list_tabtrungthoigianthuchien").jqGrid('getRowData', id);
        sovaovien= ret.SOVAOVIEN;
        tenbenhvien= ret.TENBENHNHAN;
        console.log("ret", ret);
      }
    });
    $("#list_tabtrungthoigianthuchien").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
  }
  function onClick() {
    var tab = $("#tab_tabds").tabs("option", "active");
    if (tab == 3) {
      listTabXQUANGInit();
    } else if (tab == 4) {
      listTabXNInit()
    } else if (tab == 5) {
      listTabThuthuatphauthuatInit()
    }
    else if (tab == 6) {
      listGiuongInit()
    } else if (tab == 7) {
      listTabTrunggioylenhInit()
    }
    else if (tab == 1) {
      listBntrathuocInit();
    }
    else if (tab == 2) {
      listVtytInit()
    } else if (tab == 8) {
      listTabTrunggiothuchienInit()
    }
  }
  $(function () {
    $("#inbc").hide();
    $("#loai_dv_lable").hide();
    $("#loai_dv_div").hide();
    $(":input").inputmask();
    $("#ylenh").inputmask({
      mask: "1/2/y h:s",
      placeholder: "dd/mm/yyyy hh:mm",
      alias: "datetime",
      hourFormat: "24"
    });
    $("#thuchien_ylenh").inputmask({
      mask: "1/2/y h:s",
      placeholder: "dd/mm/yyyy hh:mm",
      alias: "datetime",
      hourFormat: "24"
    });
    $("#ketqua").inputmask({
      mask: "1/2/y h:s",
      placeholder: "dd/mm/yyyy hh:mm",
      alias: "datetime",
      hourFormat: "24"
    });
    $("#thoigianylenhthuoc").datetimepicker({
      dateFormat: "dd/mm/yy"
    });
    $("#thoigianylenhthuoc").inputmask({
      mask: "1/2/y h:s",
      placeholder: "dd/mm/yyyy hh:mm",
      alias: "datetime",
      hourFormat: "24"
    });
    $('#giuong_denngay').inputmask("hh:mm:ss", {
              placeholder: "HH:MM:SS",
              insertMode: false,
              showMaskOnHover: false,
              hourFormat: 24
            }
    );
    $('#giuong_tungay').inputmask("hh:mm:ss", {
              placeholder: "HH:MM:SS",
              insertMode: false,
              showMaskOnHover: false,
              hourFormat: 24
            }
    );
    $("#tungay").datepicker();
    $("#tungay").datepicker("option", "dateFormat", "dd/mm/yy");
    $("#denngay").datepicker();
    $("#denngay").datepicker("option", "dateFormat", "dd/mm/yy");
    $("#ylenh").datetimepicker({
      dateFormat: "dd/mm/yy"
    });
    $("#thuchien_ylenh").datetimepicker({
      dateFormat: "dd/mm/yy"
    });
    $("#ketqua").datetimepicker({
      dateFormat: "dd/mm/yy"
    });
    $('#cmuthoaigianhtkham').inputmask("hh:mm", {
              placeholder: "HH:MM",
              insertMode: false,
              showMaskOnHover: false,
              hourFormat: 24
            }
    );
    $('#cmuthoigianylenh').inputmask("hh:mm", {
              placeholder: "HH:MM",
              insertMode: false,
              showMaskOnHover: false,
              hourFormat: 24
            }
    );

    $("#cmuthoaigianhtkham").timepicker({

    })
    var t = new Date();
    $("#tungay").val(t.getDate() +"/"+ (t.getMonth()+1 < 10? "0"+ (t.getMonth()+1): t.getMonth()+1)+ "/"+ t.getFullYear());
    $("#denngay").val(t.getDate() +"/"+ (t.getMonth()+1 < 10? "0"+ (t.getMonth()+1): t.getMonth()+1)+ "/"+ t.getFullYear());
    $("#tab_tabds").tabs();
    $("#cmu_luubacsi").click(function() {
      var id = $("#cmu_ds_bacsi").jqGrid('getGridParam', 'selrow');
      var ret = $("#cmu_ds_bacsi").jqGrid('getRowData', id);

      var idcongkham = $("#list_tabcongkham").jqGrid('getGridParam', 'selrow');
      var retcongkham = $("#list_tabcongkham").jqGrid('getRowData', idcongkham);
      $.post('cmu_post', {
        url: [
          "${Sess_DVTT}",
          $("#ds_bacsicongkham").val(),
          $("#hinhthuc").val() ==1? ret.SOVAOVIEN:retcongkham.SOVAOVIEN,
          ret.MAPHONGKHAM,
          ret.STT_PHONGBENH
          ,$("#hinhthuc").val(),
          ret.LANCHUYEN,
          "${Sess_User}"+"-"+"${Sess_UserID}",
          'CMU_UPDATE_BACSI_PB'
        ].join('```')
      }).done(function(data) {
        jAlert("Cập nhật thành công vui lòng tạo lại bảng kê.")
      })
    })

    $("#cmu_luubacsi_thuoc").click(function() {

      var idcongkham = $("#list_tabthuoc").jqGrid('getGridParam', 'selrow');
      var retcongkham = $("#list_tabthuoc").jqGrid('getRowData', idcongkham);
      $.post('cmu_post', {
        url: [
          "${Sess_DVTT}",
          $("#ds_bacsicongkham_thuoc").val(),
          retcongkham.SOVAOVIEN,
          retcongkham.SOVAOVIEN_DT_NOI,
          retcongkham.STT_TOATHUOC_NGOAI
          ,$("#hinhthuc").val(),
          "${Sess_User}"+"-"+"${Sess_UserID}",
          'CMU_UPDATE_BACSI_THUOC'
        ].join('```')
      }).done(function(data) {
        jAlert("Cập nhật thành công vui lòng tạo lại bảng kê.")
      })
    })
    $("#cmu_luugiocapgiuong").click(function() {
      var idcongkham = $("#list_tabgiuongbenh").jqGrid('getGridParam', 'selrow');
      var retcongkham = $("#list_tabgiuongbenh").jqGrid('getRowData', idcongkham);
      if($("#giuong_tungay").val() != '' && $("#giuong_denngay").val() != '') {
        $.post('cmu_post', {
          url: [
            "${Sess_DVTT}",
            retcongkham.SOVAOVIEN,
            retcongkham.SOVAOVIEN_DT,
            $("#giuong_tungay").val(),
            $("#giuong_denngay").val(),
            retcongkham.SOPHIEU_CHIDINH,
            "${Sess_User}"+"-"+"${Sess_UserID}",
            'CMU_UPDATE_GIUONGBENH'
          ].join('```')
        }).done(function(data) {

          if(data == -1) {
            jAlert("Giờ vào lớn hơn hoặc bằng ngày ra")
          } else if (data == 1) {
            jAlert("Bệnh nhân đã khóa thanh toán, vui lòng mở khóa")
          }
          else {
            jAlert("Cập nhật thành công vui lòng tạo lại bảng kê.")
          }

        })
      } else {
        jAlert("Vui lòng nhập giờ vào, giờ ra.")
      }

    })
    $("#cmu_luusogiuong").click(function() {
      var idcongkham = $("#list_tabgiuongbenh").jqGrid('getGridParam', 'selrow');
      var retcongkham = $("#list_tabgiuongbenh").jqGrid('getRowData', idcongkham);
      if($("#giuong_sogiuong").val() != "") {
        $.post('cmu_post', {
          url: [
            "${Sess_DVTT}",
            retcongkham.SOVAOVIEN,
            retcongkham.SOVAOVIEN_DT,
            $("#giuong_sogiuong").val(),
            retcongkham.SOPHIEU_CHIDINH,
            "${Sess_User}"+"-"+"${Sess_UserID}",
            'CMU_UPDATE_SOGIUONGBENH'
          ].join('```')
        }).done(function(data) {

          jAlert("Cập nhật thành công vui lòng tạo lại bảng kê.")

        })
      } else {
        jAlert("Vui lòng nhập số giường")
      }

    })
    $("#cmu_luunguoicapgiuong").click(function() {
      var idcongkham = $("#list_tabgiuongbenh").jqGrid('getGridParam', 'selrow');
      var retcongkham = $("#list_tabgiuongbenh").jqGrid('getRowData', idcongkham);
      if($("#ds_nhanviencapgiuong").val() != "") {
        $.post('cmu_post', {
          url: [
            "${Sess_DVTT}",
            retcongkham.SOVAOVIEN,
            retcongkham.SOVAOVIEN_DT,
            $("#ds_nhanviencapgiuong").val(),
            retcongkham.SOPHIEU_CHIDINH,
            "${Sess_User}"+"-"+"${Sess_UserID}",
            'CMU_UPDATE_NGUOICAPGIUONG'
          ].join('```')
        }).done(function(data) {

          jAlert("Cập nhật thành công vui lòng tạo lại bảng kê.")

        })
      } else {
        jAlert("Vui lòng chọn người cấp giường")
      }
    })
    $("#cmu_luugioylenh").click(function() {
      var id= $("#"+tableDVKT).jqGrid('getGridParam', 'selrow');
      var ret = $("#"+tableDVKT).jqGrid('getRowData', id);
      if(!cmuNgoaitruktdulieutgkhamRavien({
        dvtt: "${Sess_DVTT}",
        sovaovien: ret.SOVAOVIEN,
        userId: ret.MA_BAC_SI_CD,
        thoigian: moment($("#ylenh").val(),['DD/MM/YYYY HH:mm']).format('YYYYMMDDHHmm'),
        loaikythuat: ret.LOAI_KYTHUAT
      })) {
        return false;
      }
      $.post('cmu_post', {
        url: [
          "${Sess_DVTT}",
          $("#ylenh").val(),
          sovaovien,
          sophieu_cd,
          ma_dv
          ,$("#hinhthuc").val(),
          loai_dv,
          "${Sess_User}"+"-"+"${Sess_UserID}",
          'CMU_UPDATE_NGAYYLENH_XML3'
        ].join('```')
      }).done(function(data) {
        jAlert("Cập nhật thành công vui lòng tạo lại bảng kê.")
      })
    })
    $("#cmu_luubacsi_xn_ktv").click(function() {
      var id = $("#list_tabxetnghiem").jqGrid('getGridParam', 'selrow');
      var ret = $("#list_tabxetnghiem").jqGrid('getRowData', id);
      if($("#ds_bacsi_xn").val() == '') {
        return jAlert("Vui lòng chọn bác sĩ");
      }
      if(!ret.SOPHIEU_CHIDINH) {
        return jAlert("Vui lòng chọn bệnh nhân cần chỉnh sửa");
      }
      $.post('cmu_post', {
        url: [
          "${Sess_DVTT}",
          ret.SOVAOVIEN,
          ret.SOPHIEU_CHIDINH,
          ret.MA_DICH_VU,
          $("#ds_bacsi_xn").val(),
          1,
          "${Sess_User}"+"-"+"${Sess_UserID}",
          'CMU_UPDATE_XNKTV_XML3'
        ].join('```')
      }).done(function(data) {
        jAlert("Cập nhật thành công vui lòng tạo lại bảng kê.")
      })
    })
    $("#cmu_luubacsi_xn_nguoidoc").click(function() {
      var id = $("#list_tabxetnghiem").jqGrid('getGridParam', 'selrow');
      var ret = $("#list_tabxetnghiem").jqGrid('getRowData', id);
      if($("#ds_bacsi_xn").val() == '') {
        return jAlert("Vui lòng chọn bác sĩ");
      }
      if(!ret.SOPHIEU_CHIDINH) {
        return jAlert("Vui lòng chọn bệnh nhân cần chỉnh sửa");
      }
      $.post('cmu_post', {
        url: [
          "${Sess_DVTT}",
          ret.SOVAOVIEN,
          ret.SOPHIEU_CHIDINH,
          ret.MA_DICH_VU,
          $("#ds_bacsi_xn").val(),
          0,
          "${Sess_User}"+"-"+"${Sess_UserID}",
          'CMU_UPDATE_XNKTV_XML3'
        ].join('```')
      }).done(function(data) {
        jAlert("Cập nhật thành công vui lòng tạo lại bảng kê.")
      })
    })
    $("#cmu_luubacsi_cdha_ktv").click(function() {
      var id = $("#list_tabcdha").jqGrid('getGridParam', 'selrow');
      var ret = $("#list_tabcdha").jqGrid('getRowData', id);
      if($("#ds_bacsi").val() == '') {
        return jAlert("Vui lòng chọn bác sĩ");
      }
      if(!ret.SOPHIEU_CHIDINH) {
        return jAlert("Vui lòng chọn bệnh nhân cần chỉnh sửa");
      }
      $.post('cmu_post', {
        url: [
          "${Sess_DVTT}",
          ret.SOVAOVIEN,
          ret.SOPHIEU_CHIDINH,
          ret.MA_DICH_VU,
          $("#ds_bacsi").val(),
          1,
          "${Sess_User}"+"-"+"${Sess_UserID}",
          'CMU_UPDATE_CDHAKTV_XML3'
        ].join('```')
      }).done(function(data) {
        jAlert("Cập nhật thành công vui lòng tạo lại bảng kê.")
      })
    })
    $("#cmu_luubacsi_cdha_nguoidoc").click(function() {
      var id = $("#list_tabcdha").jqGrid('getGridParam', 'selrow');
      var ret = $("#list_tabcdha").jqGrid('getRowData', id);
      if($("#ds_bacsi").val() == '') {
        return jAlert("Vui lòng chọn bác sĩ");
      }
      if(!ret.SOPHIEU_CHIDINH) {
        return jAlert("Vui lòng chọn bệnh nhân cần chỉnh sửa");
      }
      $.post('cmu_post', {
        url: [
          "${Sess_DVTT}",
          ret.SOVAOVIEN,
          ret.SOPHIEU_CHIDINH,
          ret.MA_DICH_VU,
          $("#ds_bacsi").val(),
          0,
          "${Sess_User}"+"-"+"${Sess_UserID}",
          'CMU_UPDATE_CDHAKTV_XML3'
        ].join('```')
      }).done(function(data) {
        jAlert("Cập nhật thành công vui lòng tạo lại bảng kê.")
      })
    })
    $("#cmu_luugioylenh").click(function() {
      $.post('cmu_post', {
        url: [
          "${Sess_DVTT}",
          $("#ylenh").val(),
          sovaovien,
          sophieu_cd,
          ma_dv
          ,$("#hinhthuc").val(),
          loai_dv,
          "${Sess_User}"+"-"+"${Sess_UserID}",
          'CMU_UPDATE_NGAYYLENH_XML3'
        ].join('```')
      }).done(function(data) {
        jAlert("Cập nhật thành công vui lòng tạo lại bảng kê.")
      })
    })

    $("#cmu_luugioketqua").click(function() {
      var id= $("#"+tableDVKT).jqGrid('getGridParam', 'selrow');
      var ret = $("#"+tableDVKT).jqGrid('getRowData', id);
      if(!cmuNgoaitruktdulieutgkhamRavien({
        dvtt: "${Sess_DVTT}",
        sovaovien: ret.SOVAOVIEN,
        userId: ret.MA_BAC_SI_DOCKQ,
        thoigian: moment($("#ketqua").val(),['DD/MM/YYYY HH:mm']).format('YYYYMMDDHHmm'),
        loaikythuat: ret.LOAI_KYTHUAT
      })) {
        return false;
      }
      $.post('cmu_post', {
        url: [
          "${Sess_DVTT}",
          $("#ketqua").val(),
          sovaovien,
          sophieu_cd,
          ma_dv
          ,$("#hinhthuc").val(),
          loai_dv,
          "${Sess_User}"+"-"+"${Sess_UserID}",
          'CMU_UPDATE_KETQUA_XML3'
        ].join('```')
      }).done(function(data) {
        jAlert("Cập nhật thành công vui lòng tạo lại bảng kê.")
      })
    })
    $("#cmu_luugio_thuchien_ylenh").click(function() {
      var id= $("#"+tableDVKT).jqGrid('getGridParam', 'selrow');
      var ret = $("#"+tableDVKT).jqGrid('getRowData', id);
      <%--if(!cmuNgoaitruktdulieutgkhamRavien({--%>
      <%--  dvtt: "${Sess_DVTT}",--%>
      <%--  sovaovien: ret.SOVAOVIEN,--%>
      <%--  userId: ret.MA_BAC_SI_DOCKQ,--%>
      <%--  thoigian: moment($("#thuchien_ylenh").val(),['DD/MM/YYYY HH:mm']).format('YYYYMMDDHHmm'),--%>
      <%--  loaikythuat: ret.LOAI_KYTHUAT--%>
      <%--})) {--%>
      <%--  return false;--%>
      <%--}--%>
      $.post('cmu_post', {
        url: [
          "${Sess_DVTT}",
          $("#thuchien_ylenh").val(),
          sovaovien,
          sophieu_cd,
          ma_dv
          ,$("#hinhthuc").val(),
          loai_dv,
          "${Sess_User}"+"-"+"${Sess_UserID}",
          'CMU_UPDATE_NGAYTH_YLENH_XML3'
        ].join('```')
      }).done(function(data) {
        jAlert("Cập nhật thành công vui lòng tạo lại bảng kê.")
      })
    })
    $("#cmu_luuthoigianhtkham").click(function() {
      $.post('cmu_post', {
        url: [
          "${Sess_DVTT}",
          $("#ngayhoantatkham").val() + " " + $("#cmuthoaigianhtkham").val(),
          sovaovien,
          "${Sess_User}"+"-"+"${Sess_UserID}",
          tenbenhvien,
          'CMU_UPDATE_NGAYHTK_XML3'
        ].join('```')
      }).done(function(data) {
        jAlert("Cập nhật thành công vui lòng tạo lại bảng kê.")
      })
    })
    $("#cmu_luuthoigianylenh").click(function() {
      var idcongkham = $("#list_tabcongkham").jqGrid('getGridParam', 'selrow');
      var retcongkham = $("#list_tabcongkham").jqGrid('getRowData', idcongkham);
      $.post('cmu_post', {
        url: [
          "${Sess_DVTT}",
          $("#ngayhoantatkham_2").val() + " " + $("#cmuthoigianylenh").val(),
          sovaovien,
          "${Sess_User}"+"-"+"${Sess_UserID}",
          tenbenhvien,
          retcongkham.MA_PHONG_BENH_CD_B3,
          'CMU_UPDATE_NGAYYLENHKHAM_XML3'
        ].join('```')
      }).done(function(data) {
        jAlert("Cập nhật thành công vui lòng tạo lại bảng kê.")
      })
    })
    $("#cmu_ds_bacsi").jqGrid({
      url: '',
      datatype: "local",
      shrinkToFit: false,
      ignoreCase: true,
      loadonce: true,
      colNames:["MÃ NHÂN VIÊN", "TÊN NHÂN VIÊN", "DỊCH VỤ", "PHÒNG CHỈ ĐỊNH", "CNHN BSTH","SOVAOVIEN","MAPHONGKHAM", "STT_PHONGBENH", "LANCHUYEN"],
      colModel: [
        {name: 'MA_NHANVIEN', index: 'MA_NHANVIEN', width: 80},
        {name: 'TEN_NHANVIEN', index: 'TEN_NHANVIEN', width: 180},
        {name: 'TENDICHVU', index: 'TENDICHVU', width: 100},
        {name: 'TENPHONGBAN', index: 'TENPHONGBAN', width: 100},
        {name: 'CHUNGCHIHANHNGHE', index: 'CHUNGCHIHANHNGHE', width: 180},
        {name: 'SOVAOVIEN', index: 'SOVAOVIEN', width: 100, hidden: true},
        {name: 'MAPHONGKHAM', index: 'MAPHONGKHAM', width: 100, hidden: true},
        {name: 'STT_PHONGBENH', index: 'STT_PHONGBENH', width: 100, hidden: true},
        {name: 'LANCHUYEN', index: 'LANCHUYEN', width: 100, hidden: true}
      ],
      rowNum: 10000,
      height: '200',
      width:'700',
      footerrow: true,
      rownumbers: true,
      rownumWidth: 25,
      userDataOnFooter: true,
      loadComplete: function () {
      }
    });
    $.get("cmu_getlist?url="+convertArray(["${Sess_DVTT}",'CMU_DSNHANVIENTOANBV']))
            .done(function(data){
              data.forEach(function(object) {
                $(".dsbacsi").append("<option value='"+object.MA_NHANVIEN+"\'>"+object.TEN_NHANVIEN+"</option>")
              })
              $('.dsbacsi').multiselect({
                includeSelectAllOption: true,
                enableFiltering: true,
                maxHeight: 200,//AGG DUY cập nhật lại chiều cao
                buttonWidth: 200,//AGG DUY cập nhật lại chiều cao
                numberDisplayed: 2,
                nSelectedText: 'selected',
                nonSelectedText: 'Chọn Bác Sĩ'
              });

            })
    'use strict';
    listTabCongkhamInit();
    $("#xembc").click(function (evt) {
      //Kiểm tra thao tác ngoài giờ
      var ktraAjax = $.ajax({type: "POST", url: "cmu_post_cmu_ktra_ngoaigio", async: false,
        data:  {url: [dvtt, $("#tungay").val() + " 00:00:00", $("#denngay").val() + " 00:00:00"].join('```')}
      });
      if(ktraAjax.status != 200 || ktraAjax.responseText > 0){
        if(ktraAjax.responseText == 1){
          jAlert("Vui lòng chọn thời gian <= 7 ngày");
          return;
        }
        if(ktraAjax.responseText == 2){
          jAlert("Xem > 1 ngày chỉ xem được ngoài giờ hành chính");
          return;
        }
      }//


      var tab = $("#tab_tabds").tabs("option", "active");
      if (tab == 0) {
        var arr = ["${Sess_DVTT}", $("#tungay").val(), $("#denngay").val(), $("#khoa").val(),
          $("#hinhthuc").val()];
        var url = "cmu_list_f_cmuktraxml3_congkham130?url=" + convertArray(arr);
        $("#list_tabcongkham").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
      } else if (tab == 1) {
        var arr = ["${Sess_DVTT}", $("#tungay").val(), $("#denngay").val(), $("#khoa").val(),
          $("#hinhthuc").val()];
        var url = "cmu_list_f_cmuktraxml4750_thuoc?url=" + convertArray(arr);
        $("#list_tabthuoc").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
      }else if(tab == 2){
        var arr = ["${Sess_DVTT}", $("#tungay").val(), $("#denngay").val(), $("#khoa").val(),
          $("#hinhthuc").val()];
        var url = "cmu_list_f_cmuktraxml3_vtyt?url=" + convertArray(arr);
        $("#list_vattu").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
      }else if(tab == 3){
        var arr = ["${Sess_DVTT}", $("#tungay").val(), $("#denngay").val(), $("#khoa").val(),
          $("#hinhthuc").val(),"CDHA"];
        var url = "cmu_list_f_cmuktraxml4750_dvkt?url=" + convertArray(arr);
        $("#list_tabcdha").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
      }
      else if(tab == 4){
        var arr = ["${Sess_DVTT}", $("#tungay").val(), $("#denngay").val(), $("#khoa").val(),
          $("#hinhthuc").val(),"XETNGHIEM"];
        var url = "cmu_list_f_cmuktraxml4750_dvkt?url=" + convertArray(arr);
        $("#list_tabxetnghiem").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
      }
      else if(tab == 5){
        var arr = ["${Sess_DVTT}", $("#tungay").val(), $("#denngay").val(), $("#khoa").val(),
          $("#hinhthuc").val(),"TTPT"];
        var url = "cmu_list_f_cmuktraxml4750_dvkt?url=" + convertArray(arr);
        $("#list_tabthuthuatphauthuat").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
      } else if(tab == 7){
        if($("#hinhthuc").val() == 0) {
          return jAlert("Chữa hỗ trợ kiểm tra nội trú")
        }
        var arr = ["${Sess_DVTT}", $("#tungay").val(), $("#denngay").val(), $("#khoa").val(),
          $("#hinhthuc").val()];
        var url = "cmu_list_f_cmuktraxml3_gioylenh?url=" + convertArray(arr);
        $("#list_tabtrunggioylenh").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
      } else if(tab == 8){
        var arr = ["${Sess_DVTT}", $("#tungay").val(), $("#denngay").val(), $("#khoa").val(),
          $("#hinhthuc").val()];
        var url = "cmu_list_f_cmuktraxml3_trunggioth?url=" + convertArray(arr);
        $("#list_tabtrungthoigianthuchien").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
      } else {
        var arr = ["${Sess_DVTT}", $("#tungay").val(), $("#denngay").val(), $("#khoa").val(),
          $("#hinhthuc").val(),"GIUONG"];
        var url = "cmu_list_f_cmuktraxml3_dvkt?url=" + convertArray(arr);
        $("#list_tabgiuongbenh").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
      }
    });
//                 var trangthai = $("#tabs_ttpt").tabs("option", "active");
    $("#inbc").click(function (evt) {
      var tab = $("#tab_tabds").tabs("option", "active");

    });
    $("#xuatbaocao").click(function (evt) {
      //Kiểm tra thao tác ngoài giờ
      var ktraAjax = $.ajax({type: "POST", url: "cmu_post_cmu_ktra_ngoaigio", async: false,
        data:  {url: [dvtt, $("#tungay").val() + " 00:00:00", $("#denngay").val() + " 00:00:00"].join('```')}
      });
      if(ktraAjax.status != 200 || ktraAjax.responseText > 0){
        if(ktraAjax.responseText == 1){
          jAlert("Vui lòng chọn thời gian <= 7 ngày");
          return;
        }
        if(ktraAjax.responseText == 2){
          jAlert("Xem > 1 ngày chỉ xem được ngoài giờ hành chính");
          return;
        }
      }//
      var tab = $("#tab_tabds").tabs("option", "active");
      console.log('tab',tab);
      //return false;
      var $grid;
      var nameEx;
      if (tab == 0) {
        /*var loai = $('input[name=loaibaocao]:checked').val();
        if(loai == 'icd_nghenghiep'){
          var arr = ["${Sess_DVTT}", $("#tungay").val(), $("#denngay").val(), $("#khoa").val(), $("#phong").val(),
            $("#benhphu").val(), $("#hinhthuc").val()];
          var param = ['dvtt', 'tungay', 'denngay','khoa','phong',
            'benhphu', 'hinhthuc'];
          var url = " cmu_report_rp_thongke_icd_nghenghiep?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=xlsx";
          $(location).attr('href', url);
        }else if(loai == 'icd_tuoi'){
          var arr = ["${Sess_DVTT}", $("#tungay").val(), $("#denngay").val(), $("#khoa").val(), $("#phong").val(),
            $("#benhphu").val(), $("#hinhthuc").val()];
          var param = ['dvtt', 'tungay', 'denngay','khoa','phong',
            'benhphu', 'hinhthuc'];
          var url = " cmu_report_rp_thongke_icd_tuoi?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=xlsx";
          $(location).attr('href', url);
        }*/
        var params = {
          tungay: $("#tungay").val(),
          denngay: $("#denngay").val(),
          khoa: $("#khoa").val(),
          hinhthuc: $("#hinhthuc").val(),
          loai: "CONGKHAM"
        }
        var url = "cmu_in_rp_xml3_130?type=xlsx&" + $.param(params);
        $(location).attr('href', url);
      }else if (tab ==1)  {
        <%--var arr = ["${Sess_DVTT}", $("#tungay").val(), $("#denngay").val(), $("#khoa").val(), $("#phong").val(),--%>
        <%--  $("#hinhthuc").val(), $("#loai_dv").val()];--%>
        <%--var param = ['dvtt', 'tungay', 'denngay','khoa','phong', 'hinhthuc', 'loaidv'];--%>
        <%--var url = " cmu_report_rp_thongke_dvkt?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=xlsx";--%>


        var params = {
          tungay: $("#tungay").val(),
          denngay: $("#denngay").val(),
          khoa: $("#khoa").val(),
          hinhthuc: $("#hinhthuc").val(),
          loai: "THUOC"
        }
        var url = "cmu_in_rp_xml3_130?type=xlsx&" + $.param(params);
        $(location).attr('href', url);
      }else if (tab ==2)  {
        var arr = ["${Sess_DVTT}", $("#tungay").val(), $("#denngay").val(), $("#khoa").val(), $("#phong").val(),
          $("#hinhthuc").val()];
        var param = ['dvtt', 'tungay', 'denngay','khoa','phong', 'hinhthuc'];
        var url = " cmu_report_rp_thongke_vtyt?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=xlsx";
        $(location).attr('href', url);
      }else if (tab ==3)  {
        <%--var arr = ["${Sess_DVTT}", $("#tungay").val(), $("#denngay").val(), $("#khoa").val(), $("#phong").val()];--%>
        <%--var param = ['dvtt', 'tungay', 'denngay','khoa','phong'];--%>
        <%--var url = " cmu_report_rp_thongke_giuong?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=xlsx";--%>

        var params = {
          tungay: $("#tungay").val(),
          denngay: $("#denngay").val(),
          khoa: $("#khoa").val(),
          hinhthuc: $("#hinhthuc").val(),
          loai: "CDHA"
        }
        var url = "cmu_in_rp_xml3_130?type=xlsx&" + $.param(params);
        $(location).attr('href', url);
      }else if (tab ==4)  {
        <%--var arr = ["${Sess_DVTT}", $("#tungay").val(), $("#denngay").val(), $("#khoa").val(), $("#phong").val()];--%>
        <%--var param = ['dvtt', 'tungay', 'denngay','khoa','phong'];--%>
        <%--var url = " cmu_report_rp_thongke_bnhoantra?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=xlsx";--%>
        <%--$(location).attr('href', url);--%>
        var params = {
          tungay: $("#tungay").val(),
          denngay: $("#denngay").val(),
          khoa: $("#khoa").val(),
          hinhthuc: $("#hinhthuc").val(),
          loai: "XETNGHIEM"
        }
        var url = "cmu_in_rp_xml3_130?type=xlsx&" + $.param(params);
        $(location).attr('href', url);
      }else if (tab ==5)  {
        <%--var arr = ["${Sess_DVTT}", $("#tungay").val(), $("#denngay").val(), $("#khoa").val(), $("#phong").val()];--%>
        <%--var param = ['dvtt', 'tungay', 'denngay','khoa','phong'];--%>
        <%--var url = " cmu_report_rp_thongke_bnhoantra?url=" + convertArray(arr)+"&param="+ convertArray(param)+"&loaifile=xlsx";--%>
        <%--$(location).attr('href', url);--%>
        var params = {
          tungay: $("#tungay").val(),
          denngay: $("#denngay").val(),
          khoa: $("#khoa").val(),
          hinhthuc: $("#hinhthuc").val(),
          loai: "TTPT"
        }
        var url = "cmu_in_rp_xml3_130?type=xlsx&" + $.param(params);
        $(location).attr('href', url);
      }

    });
    var dvtt = "${Sess_DVTT}";
    var select = '';
    $.ajax({
      method: "GET",
      url: "cmu_getlist?url=" + convertArray([dvtt, 'noitru_khoanoitru_select']),
      success: function (data) {
        data.forEach(function (_obj) {
          if (_obj.MA_PHONGBAN != -1) {
            if (_obj.MA_PHONGBAN == "${Sess_PhongBan}"){
              $("#khoa").append("<option selected value=" + _obj.MA_PHONGBAN + ">" + _obj.TEN_PHONGBAN + "</option>");
            } else
              $("#khoa").append("<option value=" + _obj.MA_PHONGBAN + ">" + _obj.TEN_PHONGBAN + "</option>");
          }
        });
        loadPhong();
      }
    });
    $("#khoa").change(function (evt) {
      loadPhong();
    });
    function loadPhong(){
      $.ajax({
        method: "GET",
        url: "layphongbenh_theokhoa?khoa=" + $("#khoa").val() + "&dvtt=${Sess_DVTT}",
        success: function (data) {
          $("#phong").html(' ')
          if ($("#khoa").val() != "-1") {
            $("<option selected value='-1'>---Tất cả---</option>").appendTo("#phong");
            data.forEach(function (i) {
              $("<option value='" + i.MA_PHONG_BENH + "'>" + i.TEN_PHONG_BENH + "</option>").appendTo("#phong");
            });
          } else {
            $("<option selected value='-1'>-- Tất cả phòng --</option>").appendTo("#phong");
          }
        }
      });
    }
    $('input[name=loaibaocao]').change(function(){
      var loai = $('input[name=loaibaocao]:checked').val();
      if(loai == 'icd_nghenghiep'){
        $("#div_list_icd_nghenghiep").show();
        $("#div_list_icd").hide();
      }else if(loai == 'icd_tuoi'){
        $("#div_list_icd_nghenghiep").hide();
        $("#div_list_icd").show();
      }
    });

    $("#cmu_luungaythuoc").click(function() {
      if ($("#thuoc_ngayuong").val() == '' || $("#thuoc_ngayuong").val() <= 0) {
        jAlert("Vui lòng nhập ngày uống lớn hơn 0");
        return;
      }
      if ($("#thuoc_sanguong").val() == '' || $("#thuoc_truauong").val() == '' ||  $("#thuoc_toiuong").val() == '' || $("#thuoc_chieuuong").val() == '') {
        jAlert("Vui lòng nhập đầy đủ thông tin: Sáng, trưa, chiều, tối");
        return;
      }
      if ($("#thuoc_sanguong").val() <0 || $("#thuoc_truauong").val()  <0 ||  $("#thuoc_toiuong").val()  <0 || $("#thuoc_chieuuong").val()  <0 ) {
        jAlert("Sáng, trưa, chiều, tối phải lớn hơn hoặc bằng 0");
        return;
      }
      if (!$("#thuoc_dangthuoc").val()) {
        jAlert("Vui lòng nhập dạng thuốc");
        return;
      }
      var id= $("#list_tabthuoc").jqGrid('getGridParam', 'selrow');
      var ret = $("#list_tabthuoc").jqGrid('getRowData', id);
      $.post('cmu_post', {
        url: [
          "${Sess_DVTT}",
          ret.SOVAOVIEN,
          ret.STT_TOATHUOC,
          $("#thuoc_ngayuong").val(),
          $("#thuoc_sanguong").val(),
          $("#thuoc_truauong").val(),
          $("#thuoc_chieuuong").val(),
          $("#thuoc_toiuong").val(),
          $("#thuoc_dangthuoc").val(),
          "${Sess_User}"+"-"+"${Sess_UserID}",
          ret.TENBENHNHAN + " - " + ret.MABENHNHAN,
          'CMU_UPDATE_NGAYUONG_TOATHUOC'
        ].join('```')
      }).done(function(data) {
        if (data == 1)  {
          jAlert("Bệnh nhân đã khóa thanh toán, vui lòng mở khóa");
          return false;
        }
        jAlert("Cập nhật thành công vui lòng tạo lại bảng kê.")
      })

    })
    $("#cmu_luuthoigianylenhthuoc").click(function() {
      var id= $("#list_tabthuoc").jqGrid('getGridParam', 'selrow');
      var ret = $("#list_tabthuoc").jqGrid('getRowData', id);
      if(!cmuNgoaitruktdulieutgkham({
        dvtt: "${Sess_DVTT}",
        sovaovien: ret.SOVAOVIEN,
        userId: ret.MA_BAC_SI,
        thoigian: $("#thoigianylenhthuoc").val()
      })) {
        return false;
      }
      if((moment($("#thoigianylenhthuoc").val(), ['DD/MM/YYYY HH:mm']).format("DD/MM/YYYY") !=
              moment(ret.NGAYYLENH, ['DD/MM/YYYY HH:mm']).format("DD/MM/YYYY")) && ("${Sess_DVTT}" != '96172' && "${Sess_DVTT}" != '96155')) {
        jAlert("Vui lòng nhập đúng định dạng ngày tháng");
        return false;
      }
      $.post('cmu_post', {
        url: [
          "${Sess_DVTT}",
          ret.SOVAOVIEN,
          ret.STT_TOATHUOC,
          $("#thoigianylenhthuoc").val(),
          "${Sess_User}"+"-"+"${Sess_UserID}",
          ret.TENBENHNHAN + " - " + ret.MABENHNHAN,
          ret.SOVAOVIEN_DT_NOI,
          'CMU_UPDATE_NGAYYLENH_TOATHUOC'
        ].join('```')
      }).done(function(data) {
        if (data == 1)  {
          jAlert("Bệnh nhân đã khóa thanh toán, vui lòng mở khóa");
          return false;
        }
        jAlert("Cập nhật thành công vui lòng tạo lại bảng kê.")
      })

    })

  });
</script>
</html>