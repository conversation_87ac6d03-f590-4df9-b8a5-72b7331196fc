package dm_phuongxa;

import VSC.jdbc.JdbcTemplate;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.List;

public class GopXaPhuongDAOImp implements GopXaPhuongDAO{
    @Autowired
    @Resource(name = "dataSourceMNG")
    DataSource dataSourceMNG;

    @Override
    public List laydanhmuc(String sql) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSourceMNG);
        return jdbcTemplate.queryForList(sql);
    }

    @Override
    public List listGopXaPhuong(String madonvi, String madonvi_moi, int rows, int page) {
        String sql ="call fn_ds_chuyen_doi_don_vi(?,?,?,?)#c,s,s,s,s"; // #c trả ra loại cursor là 1 list, s là kiểu dữ liệu của tham số truyền vào
        JdbcTemplate jdbcTemplate = new JdbcTemplate(this.dataSourceMNG);
        return jdbcTemplate.queryForList(sql, new Object[] {madonvi,madonvi_moi,rows,page});
    }

    @Override
    public Long saveGopXaPhuong(JSONObject data) {
        String sql = "call fn_save_chuyen_doi_don_vi(?)" +
                "#l,s";

        String stringData = data.toString();
        Object[] parameters = {stringData};
        return convertToInt(sql,parameters);
    }

    @Override
    public Long deleteGopXaPhuong(String id) {
        String sql = "call fn_del_chuyen_doi_don_vi(?)" +
                "#l,s";
        Object[] parameters = {id};
        return convertToInt(sql,parameters);
    }

    public long convertToInt(String sql,Object[] parameters) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(this.dataSourceMNG);
        long status = jdbcTemplate.queryForObject(sql, parameters, Long.class);
        return status;
    }
}
