CREATE OR REPLACE FUNCTION dsbn_gchungsinh_mobile_bgd (
    p_dvtt          IN              VARCHAR2,
    p_ngaybatdau    IN              VARCHAR2,
    p_ngayketthuc   IN              VARCHAR2,
    p_trangthai     IN              VARCHAR2,
    p_chucvu        IN              VARCHAR2,
    p_manhanvien    IN              VARCHAR2
) RETURN SYS_REFCURSOR IS

    cur              SYS_REFCURSOR;
    v_ngaykethuc     DATE := TO_DATE(p_ngayketthuc || ' 23:59:59', 'DD/MM/YYYY HH24:MI:SS');
    v_ngaybatdau     DATE := TO_DATE(p_ngaybatdau || ' 00:00:00', 'DD/MM/YYYY HH24:MI:SS');
    v_tennhanvien    VARCHAR2(500);
    v_thamso960599   NUMBER(10) := his_fw.cmu_tsdv(p_dvtt, 960599, '0');
BEGIN
    IF v_thamso960599 = 0 THEN
        RETURN dsbn_gchungsinh_mobile_bgdv2(p_dvtt, p_ngay<PERSON><PERSON>u, p_ngayketthuc, p_trangtha<PERSON>, p_chucvu, p_manhanvien);
END IF;

SELECT
    ten_nhanvien_cd
INTO v_tennhanvien
FROM
    his_fw.dm_nhanvien_cd
WHERE
    ma_nhanvien = p_manhanvien;

OPEN cur FOR SELECT
                    bn.ma_benh_nhan,
                    ba.stt_benhan,
                    ba.sobenhan,
                    ddt.stt_dotdieutri,
                    bn.ten_benh_nhan,
                    bn.gioi_tinh,
                    ba.sovaovien,
                    ddt.sovaovien_dt,
                    EXTRACT(YEAR FROM trunc(ba.ngaynhapvien)) - EXTRACT(YEAR FROM trunc(bn.ngay_sinh)) AS tuoi,
                    cmu_hienthi_thangv2(bn.ngay_sinh, ba.ngaynhapvien, p_dvtt) thang,
                    cmu_hienthi_ngayv2(bn.ngay_sinh, ba.ngaynhapvien, p_dvtt) ngay,
                    bn.dia_chi,
                    giuong.stt_buong    ten_phong,
                    giuong.stt_giuong   sogiuong,
                    'NOITRU_GIAYCHUNGSINH' ky_hieu_phieu,
                    grv.id_chungsinh    so_phieu_dv,
                    '-1' ma_dich_vu,
                    'kyso/get-file-minio' urlgetpdf,
                    'Thủ trưởng cơ sở y tế' keyword,
                    'Thủ trưởng cơ sở y tế' keywordcomment,
                    '20' cx1,
                    '160' cx,
                    '-40' cy1,
                    '-105' cy2,
                    '0' x1,
                    '140' x,
                    '0' y1,
                    '-65' cy2,
                    'NOITRU_GIAYCHUNGSINH_BGD' kyhieukyso,
                    '-1' id_dieutri,
                    '1' visibletype,
                    v_tennhanvien       tennhanvien,
                    grv.ngaychungsinh   ngayravien,
                    'rp_giaychungsinh_mobile' jasper,
                    TO_CHAR(grv.ngaychungsinh, 'DD/MM/YYYY HH24:MI') ngayrv,
                    pban.ten_phongban,
                    '13' fontsize,
                    CASE
                        WHEN p_trangthai = 1 THEN
                            cmu_getminiokey(p_dvtt, grv.sovaovien, 'NOITRU_GIAYCHUNGSINH_BGD', grv.id_chungsinh, '-1')
                        ELSE
                            minio.keyminio
END keyminio,
                    cmu_getsignkey(p_dvtt, grv.sovaovien, 'NOITRU_GIAYCHUNGSINH_BGD', grv.id_chungsinh, '-1') keysign
                FROM
                    cmu_giay_chung_sinh                     grv
                    JOIN noitru_benhan                           ba ON grv.dvtt = ba.dvtt
                                             AND grv.sovaovien = ba.sovaovien
                    JOIN smartca_signed_kcb                      sign ON grv.dvtt = sign.dvtt
                                                    AND grv.sovaovien = sign.sovaovien
                                                    AND sign.status = 0
                                                    AND sign.ky_hieu_phieu = 'NOITRU_GIAYCHUNGSINH'
                                                    AND sign.so_phieu_dv = TO_CHAR(grv.id_chungsinh)
                    JOIN his_manager.smartca_signed_file_minio   minio ON sign.dvtt = minio.dvtt
                                                                        AND sign.keysign = minio.keysign
                    JOIN noitru_dotdieutri                       ddt ON ddt.stt_benhan = ba.stt_benhan
                                                  AND ddt.dvtt = ba.dvtt
                    LEFT JOIN cmu_sobuonggiuong                       giuong ON ddt.dvtt = giuong.dvtt
                                                          AND ddt.stt_benhan = giuong.stt_benhan
                                                          AND giuong.stt_dotdieutri = ddt.stt_dotdieutri
                    JOIN his_public_list.dm_benh_nhan            bn ON ba.mabenhnhan = bn.ma_benh_nhan
                    JOIN his_fw.dm_phongban                      pban ON pban.ma_phongban = grv.makhoa
                    JOIN cmu_khoakyso                            khoaks ON khoaks.dvtt = grv.dvtt
                                                AND khoaks.makhoa = grv.makhoa
                                                AND khoaks.manhanvien = p_manhanvien
                                                AND khoaks.loai = 'BGD'
                WHERE
                    grv.dvtt = p_dvtt
                    AND grv.ngaychungsinh BETWEEN v_ngaybatdau AND v_ngaykethuc
                    AND ( ( p_trangthai = 1
                            AND mabgd IS NOT NULL )
                          OR ( mabgd IS NULL
                               AND p_trangthai = 0 ) )
                    AND matruongkhoa IS NOT NULL
                ORDER BY
                    ngayravien DESC;

RETURN cur;
END;