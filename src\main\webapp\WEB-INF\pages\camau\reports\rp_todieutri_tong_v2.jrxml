<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="TODIEUTRI" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" whenResourceMissingType="Error" uuid="0f5e3019-005e-44fe-bb8f-29ae14f10109">
	<property name="ireport.zoom" value="1.3660269107301417"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="tenbenhvien" class="java.lang.String"/>
	<parameter name="ten_phongban" class="java.lang.String"/>
	<parameter name="hovaten" class="java.lang.String"/>
	<parameter name="tuoi" class="java.lang.String"/>
	<parameter name="gioitinh" class="java.lang.String"/>
	<parameter name="sogiuong" class="java.lang.String"/>
	<parameter name="buong" class="java.lang.String"/>
	<parameter name="chandoan" class="java.lang.String"/>
	<parameter name="tensoyte" class="java.lang.String"/>
	<parameter name="sovaovien" class="java.lang.String"/>
	<parameter name="sophieu" class="java.lang.String"/>
	<parameter name="dvtt" class="java.lang.String"/>
	<parameter name="stt_benhan" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="stt_dotdieutri" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="id_dieutri" class="java.lang.String"/>
	<parameter name="sovaovien_dt" class="java.lang.String"/>
	<parameter name="sobenhan" class="java.lang.String"/>
	<parameter name="soyte" class="java.lang.String"/>
	<parameter name="makhoa" class="java.lang.String"/>
	<parameter name="userid" class="java.lang.String"/>
	<parameter name="FILE_1" class="java.lang.String"/>
	<parameter name="nguoiin" class="java.lang.String"/>
	<queryString language="plsql">
		<![CDATA[{
	call HSBA_TODIEUTRI_CMU_PROV2(
		$P{stt_benhan},
		$P{makhoa},
		$P{userid},
		$P{dvtt},
		$P{ORACLE_REF_CURSOR}
	)
}]]>
	</queryString>
	<field name="stt_dieutri" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="dvtt" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="stt_benhan" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="stt_dotdieutri" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ngaygio" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="Y_LENH" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="DIEN_BIEN_BENH" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ten_nhanvien" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="PHONGBENH" class="java.lang.String"/>
	<field name="SOGIUONG" class="java.lang.String"/>
	<field name="ID_DIEUTRI" class="java.lang.String"/>
	<field name="KEYSIGN" class="java.lang.String"/>
	<field name="TENBS" class="java.lang.String"/>
	<field name="NGAY_KY" class="java.lang.String"/>
	<field name="CHANDOAN" class="java.lang.String"/>
	<field name="CHANDOANPHANBIET" class="java.lang.String"/>
	<field name="anchuky" class="java.lang.String"/>
	<field name="anhchuky" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="95" splitType="Stretch">
			<staticText>
				<reportElement mode="Transparent" x="413" y="16" width="68" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="7703de91-898a-47cd-86a0-b3a8a03f2db9"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Số vào viện:]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="0" y="16" width="230" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="f0c48e80-7d18-49f4-a649-632ef235e480"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tenbenhvien}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="36" y="55" width="181" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="a0adb478-141e-4382-ab1b-aaf700cf8766"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{ten_phongban}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="481" y="16" width="74" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="97aac8a5-3a5a-4553-85d3-5e0421a00b62"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{sobenhan}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="41" width="96" height="13" uuid="b168425a-7d90-40d6-9637-b31cba329986"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[- Họ tên người bệnh:]]></text>
			</staticText>
			<staticText>
				<reportElement x="360" y="41" width="30" height="13" uuid="d5bbc1cc-f9ee-42a8-8a44-c2b83398af4b"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Tuổi:]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="55" width="35" height="13" uuid="1f300484-8453-4a3e-87ee-9f1ef0337e1e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[- Khoa:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="98" y="41" width="262" height="13" uuid="d924e2d2-1cd5-4681-8a62-dca3e5f1aa1e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{hovaten}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="390" y="41" width="71" height="13" uuid="942d4194-5b23-48d3-bc7c-fb8fb4319415"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{tuoi}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="422" y="55" width="133" height="13" isPrintWhenDetailOverflows="true" uuid="eaffd7e6-629c-4caa-a9e1-d46e3d847e80"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{SOGIUONG}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="1" y="69" width="554" height="13" isPrintWhenDetailOverflows="true" uuid="e54aa044-e8a6-467b-a20c-c2a0efd1949c"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["- Chẩn đoán: " + $F{CHANDOAN}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="230" y="0" width="183" height="32" uuid="ded79c50-5116-4636-b6a9-d4e953b03c52"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="13" isBold="true"/>
				</textElement>
				<text><![CDATA[PHIẾU THEO DÕI ĐIỀU TRỊ]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement mode="Transparent" x="461" y="41" width="94" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="fb4a594a-ece5-4fc0-8e16-625f1297ee13"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["Giới tính: " +$P{gioitinh}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="0" y="0" width="230" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="1c9d9496-dac9-4148-a093-29e4c4ea8871"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{soyte}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="413" y="0" width="142" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="82acb37e-77b0-4ae7-ad1b-8fc2b72e537f"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[MS: 36/BV2
]]></text>
			</staticText>
			<staticText>
				<reportElement x="217" y="55" width="37" height="13" uuid="ba96b0e5-7075-4c64-add5-fdd1862dedbb"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Phòng:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="254" y="55" width="126" height="13" isPrintWhenDetailOverflows="true" uuid="9839ca9b-abce-4755-a4b7-8ab043ca7c19"/>
				<textElement verticalAlignment="Middle" markup="html">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{PHONGBENH}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="380" y="55" width="39" height="13" uuid="f4ba5bbe-551e-46e7-9da3-78428906d3bf"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Giường:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="1" y="82" width="553" height="13" isPrintWhenDetailOverflows="true" uuid="cb5cb8e4-67a7-4540-aae5-c8e1bae3d9ae"/>
				<textElement verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA["- Chẩn đoán phân biệt: " + $F{CHANDOANPHANBIET}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band height="13">
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="554" height="13" uuid="fc72f107-b8bc-48e3-a6d3-d1a980ef5197">
					<printWhenExpression><![CDATA[$V{PAGE_NUMBER} > 1]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{hovaten} + " - "+ $P{sobenhan}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="24" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="60" height="24" uuid="8196e62c-8f7a-4e83-9be5-363d7aaad10b"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[NGÀY GIỜ]]></text>
			</staticText>
			<staticText>
				<reportElement x="60" y="0" width="215" height="24" uuid="4905128b-5a9c-43b3-a9c6-d62b1c18c5f0"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[DIỄN BIẾN BỆNH]]></text>
			</staticText>
			<staticText>
				<reportElement x="275" y="0" width="280" height="24" uuid="d656f0d2-e37c-4843-9da9-bbacf9ca5bf0"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[CHỈ ĐỊNH]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="90" splitType="Stretch">
			<frame>
				<reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="0" width="555" height="90" isPrintInFirstWholeBand="true" uuid="99359e4f-4cd6-4e19-9cbf-f1711043b446"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<line>
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="59" y="0" width="1" height="90" isPrintWhenDetailOverflows="true" uuid="ab02e830-62e0-423a-b3db-a212d889ca91"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<line>
					<reportElement positionType="FixRelativeToBottom" stretchType="RelativeToBandHeight" x="59" y="0" width="1" height="90" isPrintWhenDetailOverflows="true" uuid="b710b819-2d58-4323-a7eb-d07e755a0bb4"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<line>
					<reportElement positionType="FixRelativeToBottom" stretchType="RelativeToBandHeight" x="275" y="0" width="1" height="90" isPrintWhenDetailOverflows="true" uuid="37b6165a-af9a-4119-be27-dc0409cfb88c"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<line>
					<reportElement positionType="Float" stretchType="RelativeToBandHeight" x="275" y="0" width="1" height="90" isPrintWhenDetailOverflows="true" uuid="3537116d-d160-4534-be6c-16e9d4b767c7"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<frame>
					<reportElement stretchType="RelativeToTallestObject" isPrintRepeatedValues="false" x="276" y="0" width="279" height="90" uuid="67ae7a7b-ceea-43b2-abf5-8983629dd27e"/>
					<subreport>
						<reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="0" width="279" height="90" isRemoveLineWhenBlank="true" isPrintWhenDetailOverflows="true" uuid="96e74ae3-01da-42aa-8dc8-34626c08a0c2"/>
						<subreportParameter name="stt_benhan">
							<subreportParameterExpression><![CDATA[$P{stt_benhan}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="KEYSIGN">
							<subreportParameterExpression><![CDATA[$F{KEYSIGN}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="gioitinh">
							<subreportParameterExpression><![CDATA[$P{gioitinh}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="tenbenhvien">
							<subreportParameterExpression><![CDATA[$P{tenbenhvien}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="soyte">
							<subreportParameterExpression><![CDATA[$P{soyte}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="tensoyte">
							<subreportParameterExpression><![CDATA[$P{tensoyte}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="makhoa">
							<subreportParameterExpression><![CDATA[$P{makhoa}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="userid">
							<subreportParameterExpression><![CDATA[$P{userid}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="TENBS">
							<subreportParameterExpression><![CDATA[$F{TENBS}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="chandoan">
							<subreportParameterExpression><![CDATA[$P{chandoan}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="stt_dotdieutri">
							<subreportParameterExpression><![CDATA[$F{stt_dotdieutri}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="ten_phongban">
							<subreportParameterExpression><![CDATA[$P{ten_phongban}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="ten_nhanvien">
							<subreportParameterExpression><![CDATA[$F{ten_nhanvien}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="tuoi">
							<subreportParameterExpression><![CDATA[$P{tuoi}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="sogiuong">
							<subreportParameterExpression><![CDATA[$P{sogiuong}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="buong">
							<subreportParameterExpression><![CDATA[$P{buong}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="sophieu">
							<subreportParameterExpression><![CDATA[$P{sophieu}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="hovaten">
							<subreportParameterExpression><![CDATA[$P{hovaten}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="FILE_1">
							<subreportParameterExpression><![CDATA[$P{FILE_1}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="NGAY_KY">
							<subreportParameterExpression><![CDATA[$F{NGAY_KY}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="sovaovien_dt">
							<subreportParameterExpression><![CDATA[$P{sovaovien_dt}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="sovaovien">
							<subreportParameterExpression><![CDATA[$P{sovaovien}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="dvtt">
							<subreportParameterExpression><![CDATA[$P{dvtt}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="stt_dieutri">
							<subreportParameterExpression><![CDATA[$F{stt_dieutri}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="sobenhan">
							<subreportParameterExpression><![CDATA[$P{sobenhan}]]></subreportParameterExpression>
						</subreportParameter>
						<subreportParameter name="id_dieutri">
							<subreportParameterExpression><![CDATA[$F{ID_DIEUTRI}]]></subreportParameterExpression>
						</subreportParameter>
						<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
						<subreportExpression><![CDATA[$P{FILE_1}]]></subreportExpression>
					</subreport>
				</frame>
				<frame>
					<reportElement positionType="Float" x="0" y="0" width="60" height="90" isPrintInFirstWholeBand="true" uuid="617d53b3-5ad1-4328-bb13-1c0f10a8259e"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="60" height="90" uuid="1d7fd9a4-453e-4337-9869-05ba833640d1"/>
						<box topPadding="5"/>
						<textElement textAlignment="Center" verticalAlignment="Top">
							<font fontName="Times New Roman" size="11" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{ngaygio}]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement x="60" y="0" width="215" height="90" uuid="5f41d931-7872-47df-b321-e48427ba7847"/>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="215" height="20" uuid="f8c34703-da9e-4556-92ea-51b0f114e79f"/>
						<box topPadding="5" leftPadding="3" rightPadding="3"/>
						<textElement verticalAlignment="Top" markup="styled">
							<font fontName="Times New Roman" size="11" isBold="false"/>
							<paragraph lineSpacing="Single"/>
						</textElement>
						<textFieldExpression><![CDATA[$F{DIEN_BIEN_BENH}]]></textFieldExpression>
					</textField>
					<frame>
						<reportElement positionType="Float" x="0" y="20" width="215" height="70" uuid="7c476af6-5e56-4f1c-95fc-f2600fd16a24"/>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="26" y="20" width="148" height="13" isPrintWhenDetailOverflows="true" uuid="aac6a3ea-4956-428e-b706-0862440db061">
								<printWhenExpression><![CDATA[$F{KEYSIGN} != null && $F{anchuky}.equals("0")]]></printWhenExpression>
							</reportElement>
							<textElement textAlignment="Left" verticalAlignment="Bottom">
								<font fontName="Times New Roman" size="9" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["Ký bởi: " + $F{TENBS}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="26" y="33" width="148" height="13" isPrintWhenDetailOverflows="true" uuid="8b631e66-66e1-4d81-b096-08219309bda1">
								<printWhenExpression><![CDATA[$F{KEYSIGN} != null && $F{anchuky}.equals("0")]]></printWhenExpression>
							</reportElement>
							<textElement textAlignment="Left" verticalAlignment="Bottom">
								<font fontName="Times New Roman" size="9" isBold="false" isStrikeThrough="false"/>
							</textElement>
							<textFieldExpression><![CDATA["Ngày ký: "+ $F{NGAY_KY}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="4" y="57" width="190" height="13" isPrintWhenDetailOverflows="true" uuid="8e38fd5d-9a79-4910-ae53-d7a1a600b2c6">
								<printWhenExpression><![CDATA[$F{anchuky}.equals("0")]]></printWhenExpression>
							</reportElement>
							<textElement textAlignment="Center" verticalAlignment="Bottom">
								<font fontName="Times New Roman" size="11" isBold="true"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{ten_nhanvien}]]></textFieldExpression>
						</textField>
						<image scaleImage="FillFrame" hAlign="Center">
							<reportElement x="156" y="13" width="20" height="20" uuid="3d20ab43-3ca1-44d5-8731-07aa9c5d5319">
								<printWhenExpression><![CDATA[$F{KEYSIGN} != null && $F{anchuky}.equals("0")]]></printWhenExpression>
							</reportElement>
							<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.getInstance(
new SimpleJasperReportsContext()).loadAwtImageFromBytes(Base64.getDecoder().decode("iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAABvElEQVR4nO3U3UrCYBjAcS+gAy0XdjAs925uO+huBGfnnYkmQX7kxwQvoNqJdAmFSEFQptP5RdDRBg26jig6euKxrFdbVjo66oGHuZP/j9cX5vH8z1+OcFv0y0M1hU/X48SoMFK/ZMoDFaRh2ZZvigF3472SJfVVkAYqICIPy5YrJyFGhgl3C5bYK4HUx/1AxIGaXDguGAVL7BZB7OFOIIudgLzGzbBRgDACNNIrfb6Dpari9x0pKXz+JM538ma4k4cRQCPdgi3r0/GDCOPTFNOnxcCrxWxGiwRmxvWcKbTzICBAI4ZTvKr4vUeKhfHx4juiTnHSypq8vg9CG5dG8p/jOPi30PGvEILxZtbkWzngddwPhO/sO8e/OsE0Qi4wnjFJMwsEAQrh2zPiTncwvcva1n2okb4j1xkYARRCWjk7+F18FrKibcH6eQq4RgY4BCiEa/4i7oRgPHi2A6GrNHCN9ATCXWftoL4737cGEe9B1GLrSQhd7kHoam8KSVt4J3PF35FKhGFriccNBChkvZ54XDg+nrXqtsjWEk9jJFhPPK8eRzddib8jh9siexJ/YE/jD/jb1fj/eN7mBQZhd5OoxlU0AAAAAElFTkSuQmCC")
)]]></imageExpression>
						</image>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="4" y="0" width="190" height="13" isPrintWhenDetailOverflows="true" uuid="6e0157c4-c2fe-4295-99e9-4af944834402"/>
							<textElement textAlignment="Center" verticalAlignment="Bottom">
								<font fontName="Times New Roman" size="11" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["Bác sĩ điều trị"]]></textFieldExpression>
						</textField>
						<image scaleImage="FillFrame" hAlign="Center">
							<reportElement positionType="Float" isPrintRepeatedValues="false" x="31" y="13" width="139" height="57" uuid="51a6cc9e-ae58-48d9-a598-ce15b9fe2d99">
								<printWhenExpression><![CDATA[$F{anhchuky} != null && $F{anchuky}.equals("1")]]></printWhenExpression>
							</reportElement>
							<imageExpression><![CDATA[net.sf.jasperreports.engine.util.JRImageLoader.getInstance(
new SimpleJasperReportsContext()).loadAwtImageFromBytes(Base64.getDecoder().decode($F{anhchuky})
)]]></imageExpression>
						</image>
					</frame>
				</frame>
			</frame>
		</band>
	</detail>
	<columnFooter>
		<band height="1" splitType="Stretch">
			<line>
				<reportElement x="1" y="0" width="554" height="1" uuid="a5a49d9d-888f-479c-ba3b-5aae544db025"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</line>
		</band>
	</columnFooter>
	<pageFooter>
		<band height="13">
			<textField>
				<reportElement x="198" y="0" width="56" height="12" uuid="499b19b2-168e-4657-91ee-f188920a8735"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["Trang "+ $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="0" y="0" width="187" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="4cb4c09b-1384-4adb-84ce-3a9aa687bc98"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{ten_phongban}]]></textFieldExpression>
			</textField>
			<textField pattern="&apos;Ngày giờ in:&apos; dd/MM/YYYY HH:mm:ss">
				<reportElement x="264" y="0" width="126" height="13" uuid="90497273-376a-4f87-b041-f8d0667bee98"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[new Date()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="390" y="0" width="165" height="13" uuid="55f632e3-afc2-479e-91be-9b59a47eae04"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="8" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{nguoiin}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band/>
	</summary>
</jasperReport>
