var thongtinhsba = {
    todieutri: [],
    xetnghiem: [],
    thongtinbn: {}
}
var formKetQuaDienTim, luuLogOld, dvtt;
var keyLuuLogDienTim = {
    "NGAY_TH_YL": "Thời gian bắt đầu",
    "NGAY_GIO_TH": "Thời gian thực hiện",
    "KET_QUA": "Kết quả",
    "MO_TA": "Kết luận",
    "LOIDANBACSI": "Lời dặn bác sĩ",
}

// Hệ thống quản lý gợi ý cho "Lời dặn bác sĩ"
var LoiDanBacSiSuggestions = {
    storageKey: 'dientim_loidanbacsi_suggestions',
    maxSuggestions: 20, // Giới hạn số lượng gợi ý lưu trữ

    // L<PERSON>y danh sách gợi ý từ localStorage
    getSuggestions: function() {
        try {
            var suggestions = localStorage.getItem(this.storageKey);
            return suggestions ? JSON.parse(suggestions) : [];
        } catch (e) {
            console.error('Lỗi khi lấy gợi ý:', e);
            return [];
        }
    },

    // Lưu gợi ý mới
    addSuggestion: function(text) {
        if (!text || text.trim().length < 3) return; // Chỉ lưu text có ít nhất 3 ký tự

        text = text.trim();
        var suggestions = this.getSuggestions();

        // Loại bỏ gợi ý trùng lặp (không phân biệt hoa thường)
        suggestions = suggestions.filter(function(item) {
            return item.text.toLowerCase() !== text.toLowerCase();
        });

        // Thêm gợi ý mới vào đầu danh sách
        suggestions.unshift({
            text: text,
            timestamp: new Date().getTime()
        });

        // Giới hạn số lượng gợi ý
        if (suggestions.length > this.maxSuggestions) {
            suggestions = suggestions.slice(0, this.maxSuggestions);
        }

        try {
            localStorage.setItem(this.storageKey, JSON.stringify(suggestions));
        } catch (e) {
            console.error('Lỗi khi lưu gợi ý:', e);
        }
    },

    // Khởi tạo autocomplete cho textarea "Lời dặn bác sĩ"
    initAutocomplete: function() {
        var self = this;

        // Thêm một số gợi ý mặc định nếu chưa có
        var defaultSuggestions = [
            'Tái khám sau 1 tuần',
            'Uống thuốc đúng giờ theo chỉ định',
            'Nghỉ ngơi đầy đủ, tránh căng thẳng',
            'Theo dõi huyết áp thường xuyên',
            'Chế độ ăn ít muối, ít béo',
            'Tập thể dục nhẹ nhàng',
            'Không hút thuốc, uống rượu bia',
            'Uống đủ nước, ít nhất 2 lít/ngày'
        ];

        var currentSuggestions = this.getSuggestions();
        if (currentSuggestions.length === 0) {
            defaultSuggestions.forEach(function(suggestion) {
                self.addSuggestion(suggestion);
            });
        }

        // Tìm textarea "Lời dặn bác sĩ"
        function findTextarea() {
            var $textarea = $('textarea[name="data[LOIDANBACSI]"]');
            if ($textarea.length === 0) {
                $textarea = $('#formKetQua textarea').last();
            }
            return $textarea;
        }

        // Tạo dropdown custom
        function createCustomDropdown() {
            var $textarea = findTextarea();
            if ($textarea.length === 0) {
                console.log('Không tìm thấy textarea LOIDANBACSI');
                return;
            }

            console.log('✅ Tạo autocomplete cho textarea:', $textarea);

            // Xóa dropdown cũ
            $('.loidanbacsi-dropdown').remove();

            // Tạo dropdown mới
            var $dropdown = $('<div class="loidanbacsi-dropdown"></div>');
            $dropdown.css({
                position: 'absolute',
                backgroundColor: '#ffffff',
                border: '2px solid #007bff',
                borderRadius: '4px',
                maxHeight: '200px',
                overflowY: 'auto',
                zIndex: 9999,
                display: 'none',
                boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
                minWidth: '300px'
            });

            // Thêm vào container của textarea
            var $container = $textarea.closest('div, td, form');
            if ($container.length === 0) {
                $container = $textarea.parent();
            }

            // Đảm bảo container có position relative
            if ($container.css('position') === 'static') {
                $container.css('position', 'relative');
            }

            $container.append($dropdown);

            // Hàm tính toán vị trí chính xác
            function getDropdownPosition() {
                var textareaOffset = $textarea.offset();
                var textareaHeight = $textarea.outerHeight();
                var textareaWidth = $textarea.outerWidth();
                var windowScrollTop = $(window).scrollTop();
                var windowHeight = $(window).height();

                // Vị trí fixed = offset - scroll
                var fixedTop = textareaOffset.top - windowScrollTop + textareaHeight + 5;
                var fixedLeft = textareaOffset.left;

                // Kiểm tra nếu dropdown sẽ bị tràn ra ngoài màn hình
                var dropdownHeight = 200; // Ước tính
                if (fixedTop + dropdownHeight > windowHeight) {
                    // Hiển thị phía trên textarea
                    fixedTop = textareaOffset.top - windowScrollTop - dropdownHeight - 5;
                }

                // Đảm bảo không bị tràn trái/phải
                var windowWidth = $(window).width();
                var dropdownWidth = Math.max(textareaWidth, 300);
                if (fixedLeft + dropdownWidth > windowWidth) {
                    fixedLeft = windowWidth - dropdownWidth - 10;
                }

                return {
                    top: Math.max(5, fixedTop), // Không cho âm
                    left: Math.max(5, fixedLeft), // Không cho âm
                    width: dropdownWidth
                };
            }

            // Hàm hiển thị dropdown
            function showDropdown() {
                var suggestions = self.getSuggestions();
                var value = $textarea.val().toLowerCase();

                // Lọc gợi ý
                var filtered = suggestions.filter(function(s) {
                    return value === '' || s.text.toLowerCase().includes(value);
                });

                if (filtered.length > 0) {
                    $dropdown.empty();

                    // Header
                    var $header = $('<div>💡 Gợi ý lời dặn bác sĩ</div>');
                    $header.css({
                        padding: '8px 12px',
                        backgroundColor: '#e3f2fd',
                        fontWeight: 'bold',
                        borderBottom: '1px solid #1976d2',
                        color: '#1976d2',
                        fontSize: '13px'
                    });
                    $dropdown.append($header);

                    // Các gợi ý
                    filtered.slice(0, 6).forEach(function(suggestion, index) {
                        var $item = $('<div></div>');
                        $item.html('<span style="color: #666; font-size: 12px;">' + (index + 1) + '.</span> ' + suggestion.text);
                        $item.css({
                            padding: '8px 12px',
                            cursor: 'pointer',
                            borderBottom: '1px solid #f0f0f0',
                            fontSize: '13px',
                            lineHeight: '1.3'
                        });

                        $item.hover(
                            function() {
                                $(this).css('backgroundColor', '#f5f5f5');
                            },
                            function() {
                                $(this).css('backgroundColor', 'white');
                            }
                        );

                        $item.click(function() {
                            $textarea.val(suggestion.text);
                            $textarea.focus();
                            $dropdown.hide();
                            self.addSuggestion(suggestion.text);
                            console.log('✅ Đã chọn:', suggestion.text);
                        });

                        $dropdown.append($item);
                    });

                    // Sử dụng position fixed để tránh vấn đề scroll
                    $dropdown.css({
                        position: 'fixed'
                    });

                    var pos = getDropdownPosition();
                    $dropdown.css({
                        top: pos.top + 'px',
                        left: pos.left + 'px',
                        width: pos.width + 'px',
                        display: 'block'
                    });

                } else {
                    $dropdown.hide();
                }
            }

            // Gắn sự kiện
            $textarea.off('.loidanbacsi').on('focus.loidanbacsi click.loidanbacsi', function() {
                setTimeout(showDropdown, 50);
            });

            $textarea.on('input.loidanbacsi', function() {
                showDropdown();
            });

            // Ẩn dropdown khi cần
            $(document).off('click.loidanbacsi scroll.loidanbacsi').on('click.loidanbacsi', function(e) {
                if (!$(e.target).closest($textarea).length &&
                    !$(e.target).closest($dropdown).length) {
                    $dropdown.hide();
                }
            }).on('scroll.loidanbacsi', function() {
                if ($dropdown.is(':visible')) {
                    // Cập nhật vị trí khi scroll
                    var pos = getDropdownPosition();
                    $dropdown.css({
                        top: pos.top + 'px',
                        left: pos.left + 'px'
                    });
                }
            });

            $(window).off('resize.loidanbacsi').on('resize.loidanbacsi', function() {
                $dropdown.hide();
            });

            console.log('✅ Autocomplete đã được khởi tạo thành công!');
        }

        // Khởi tạo ngay nếu có textarea
        createCustomDropdown();

        // Theo dõi thay đổi DOM để khởi tạo lại khi cần
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    var $textarea = findTextarea();
                    if ($textarea.length > 0 && !$textarea.data('autocomplete-initialized')) {
                        $textarea.data('autocomplete-initialized', true);
                        setTimeout(createCustomDropdown, 100);
                    }
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        return this;
    }
};
$(function() {
    var matoathuoc;
    var flag_noitru = "-1";
    var bienLast;
    var selfRowId;
    var listXNSelected = [];
    var allDataXN = [];
    // $("#ngaythuchien").datepicker();
    // $("#ngaythuchien").datepicker("option", "dateFormat", "dd/mm/yy");
    var today = new Date();
    var formattedDate = today.toLocaleDateString('vi-VN');
    $(".input-date").inputmask({
        mask: "1/2/y h:s",
        placeholder: "dd/mm/yyyy hh:mm",
        alias: "datetime",
        hourFormat: "24"
    });
    $(".input-date").datetimepicker({dateFormat: "dd/mm/yy"});
    $(".input-date").val(formattedDate + " 00:00");

    $(".input-date-second").inputmask({
        mask: "1/2/y h:s:s",
        placeholder: "dd/mm/yyyy hh:mm:ss",
        alias: "datetime",
        hourFormat: "24"
    });
    $(".input-date-second").datetimepicker({
        dateFormat: "dd/mm/yy",
        timeFormat: 'HH:mm:ss',
        showSecond: true,
    });
    $(".input-date-second").val(singletonObject.ngayhientai + " 00:00:00");

    $(".input-only-date").inputmask({
        mask: "1/2/y",
        placeholder: "dd/mm/yyyy",
        alias: "datetime",
        hourFormat: "24"
    });
    $(".input-only-date").datepicker({dateFormat: "dd/mm/yy"});
    $(".input-only-date").val(formattedDate);

    $(".input-only-time").inputmask({
        mask: "h:s",
        placeholder: "hh:mm",
        alias: "datetime",
        hourFormat: "24"
    });
    $(".input-only-time-second").inputmask({
        mask: "h:s:s",
        placeholder: "hh:mm:ss",
        alias: "datetime",
        hourFormat: "24"
    });
    $(".unbinddbclick").unbind("dblclick");
    $("#hsba_tabs").tabs();

    $("#list_benhnhan").jqGrid({
        url: "",
        datatype: "json",
        loadonce: true,
        height: 400,
        width: null,
        shrinkToFit: false,
        colModel: [
            {label: 'STT', name: 'STT_HANGNGAY', index: 'STT_HANGNGAY', width: 50, sorttype: 'int'},
            {label: "KEYSIGN", name: 'KEYSIGN', index: 'KEYSIGN', hidden: true},
            {label: "Ký số", name: 'KEYSIGN_HT', index: 'KEYSIGN_HT', width: 80, formatter: function (cellvalue, options, rowObject) {
                    return rowObject.KEYSIGN ? "Đã ký" : "Chưa ký";
                },
                cellattr: function (rowId, tv, rawObject, cm, rdata) {
                    return rawObject.KEYSIGN ? 'style="color:green; font-weight:bold"' : 'style="color:red; font-weight:bold"';
                }
            },
            {label: "Mã y tế", name: 'MABENHNHAN', index: 'MABENHNHAN',align: "center", fixed: true, width: 200},
            {label: "Họ tên", name: 'TENBENHNHAN_HT', index: 'TENBENHNHAN_HT', fixed: true, width: 400,
                formatter: function (cellvalue, options, rowObject) {
                    var color;
                    var color_text;
                    if (rowObject.DA_THANH_TOAN == "1") {
                        color = '#009900';
                        color_text = 'white';
                    }   //END VTU:25/10/2016
                    else {
                        if (singletonObject.hienThiMauSacBN == "0")
                        {
                            color = 'white';
                            color_text = 'black';
                        }
                    }
                    return '<span class="cellWithoutBackground" style="font-weight:bold ;background-color:' + color + ';color:' + color_text + '">' + cellvalue + '</span>';
                }
            },
            {label: 'TENBENHNHAN', name: 'TENBENHNHAN', index: 'TENBENHNHAN', width: 200, hidden: true, sorttype: 'string', searchoptions: {dataInit: function (el) {
                        setTimeout(function () {
                            $(el).focus().trigger({type: 'keypress', charCode: 13});
                        }, 20);
                    }
                }
            },
            {label: 'Tuổi', name: 'TUOI', index: 'TUOI', width: 100, align: 'right'},
            {label: 'Nội trú', name: 'NOITRU', index: 'NOITRU', width: 100, align: 'center', hidden: true},
            {label: "Nội trú", name: 'NOITRU_HT', index: 'NOITRU_HT', fixed: true, width: 100, align: 'center',
                formatter: function (cellvalue, options, rowObject) {
                    return rowObject.NOITRU ? 'X' : '';
                }
            },
            {label: 'gioitinh', name: 'GIOITINH', index: 'GIOITINH', hidden: true},
            {label: "Giới tính", name: 'GIOITINH_HT', index: 'GIOITINH_HT', fixed: true, width: 100,
                formatter: function (cellvalue, options, rowObject) {
                    return rowObject.GIOITINH.toString() == 'true' ? 'Nam' : 'Nữ';
                }
            },
            {label: 'diachi', name: 'DIACHI', index: 'DIACHI', hidden: true},
            {label: 'sobhyt', name: 'SOTHEBHYT', index: 'SOTHEBHYT', hidden: true},
            {label: 'Số phiếu', name: 'SO_PHIEU', index: 'SO_PHIEU' , width: 200},
            {label: 'MA_KHAM_BENH', name: 'MA_KHAM_BENH', index: 'MA_KHAM_BENH', hidden: true},
            {label: 'stt_benhan', name: 'STT_BENHAN', index: 'STT_BENHAN', hidden: true},
            {label: 'stt_dotdieutri', name: 'STT_DOTDIEUTRI', index: 'STT_DOTDIEUTRI', hidden: true},
            {label: 'stt_dieutri', name: 'STT_DIEUTRI', index: 'STT_DIEUTRI', hidden: true},
            {label: 'NGUOI_CHI_DINH', name: 'NGUOI_CHI_DINH', index: 'NGUOI_CHI_DINH', hidden: true},
            {label: 'BACSI_THUCHIEN', name: 'BACSI_THUCHIEN', index: 'BACSI_THUCHIEN', hidden: true},
            {label: 'PHONG_CHI_DINH', name: 'PHONG_CHI_DINH', index: 'PHONG_CHI_DINH', hidden: true},
            {label: 'MA_PHONG_CDHA', name: 'MA_PHONG_CDHA', index: 'MA_PHONG_CDHA', hidden: true},
            {label: 'Kết quá CDHA', name: 'KET_QUA_CDHA', index: 'KET_QUA_CDHA', hidden: true},
            {label: 'NGAY_CHI_DINH', name: 'NGAY_CHI_DINH', index: 'NGAY_CHI_DINH', hidden: true},
            {label: 'Tên chẩn đoán', name: 'TEN_CDHA', index: 'TEN_CDHA', width: 400},
            {label: 'MA_CDHA', name: 'MA_CDHA', index: 'MA_CDHA', width: 100, hidden: true},
            {label: 'KET_QUA', name: 'KET_QUA', index: 'KET_QUA', width: 100, hidden: true},
            {label: 'MO_TA', name: 'MO_TA', index: 'MO_TA', width: 100, hidden: true},
            {label: 'LOIDANBACSI', name: 'LOIDANBACSI', index: 'LOIDANBACSI', width: 100, hidden: true},
            {label: 'MA_MAUSIEUAM', name: 'MA_MAUSIEUAM', index: 'MA_MAUSIEUAM', width: 100, hidden: true},
            {label: 'MAU_SIEUAM', name: 'MAU_SIEUAM', index: 'MAU_SIEUAM', width: 100, hidden: true},
            {label: 'DA_CHAN_DOAN', name: 'DA_CHAN_DOAN', index: 'DA_CHAN_DOAN', width: 100, hidden: true},
            {label: 'NGAY_TH_YL', name: 'NGAY_TH_YL', index: 'NGAY_TH_YL', width: 100, hidden: true},
            {label: 'NGAY_GIO_TH', name: 'NGAY_GIO_TH', index: 'NGAY_GIO_TH', width: 100, hidden: true},
            {label: 'DVT_CDHA', name: 'DVT_CDHA', index: 'DVT_CDHA', width: 100, hidden: true},
            {label: 'TRANGTHAI_BHYT', name: 'TRANGTHAI_BHYT', index: 'TRANGTHAI_BHYT', width: 100, hidden: true},
            {label: 'MOTA_CDHA', name: 'MOTA_CDHA', index: 'MOTA_CDHA', width: 100, hidden: true},
            {label: 'STT_MAYCDHA', name: 'STT_MAYCDHA', index: 'STT_MAYCDHA', width: 100, hidden: true},
            {label: 'KYTHUATVIEN', name: 'KYTHUATVIEN', index: 'KYTHUATVIEN', width: 100, hidden: true},
            {label: 'DON_GIA', name: 'DON_GIA', index: 'DON_GIA', width: 100, hidden: true},
            {label: 'MA_ICD', name: 'MA_ICD', index: 'MA_ICD', width: 100, hidden: true},
            {label: 'TEN_ICD', name: 'TEN_ICD', index: 'TEN_ICD', width: 100, hidden: true},
            {label: 'MABENHLY_TRUOCCDHA', name: 'MABENHLY_TRUOCCDHA', index: 'MABENHLY_TRUOCCDHA', width: 100, hidden: true},
            {label: 'MABENHLY_SAUCDHA', name: 'MABENHLY_SAUCDHA', index: 'MABENHLY_SAUCDHA', width: 100, hidden: true},
            {label: 'CHANDOAN_TRUOCCDHA', name: 'CHANDOAN_TRUOCCDHA', index: 'CHANDOAN_TRUOCCDHA', width: 100, hidden: true},
            {label: 'CHANDOAN_SAUCDHA', name: 'CHANDOAN_SAUCDHA', index: 'CHANDOAN_SAUCDHA', width: 100, hidden: true},
            {label: 'cannang', name: 'CANNANG', index: 'CANNANG', hidden: true},
            {label: 'chieucao', name: 'CHIEUCAO', index: 'CHIEUCAO', hidden: true},
            {label: 'khoa', name: 'KHOA', index: 'KHOA', hidden: true},
            {label: 'Buong', name: 'BUONG', index: 'BUONG', hidden: true},
            {label: 'Giuong', name: 'GIUONG', index: 'GIUONG', hidden: true},
            {label: 'chuandoanicd', name: 'CHUANDOANICD', index: 'CHUANDOANICD', hidden: true},
            {label: 'SOVAOVIEN', name: 'SOVAOVIEN', index: 'SOVAOVIEN', hidden: true},
            {label: 'SOVAOVIEN_NOI', name: 'SOVAOVIEN_NOI', index: 'SOVAOVIEN_NOI', hidden: true},
            {label: 'SOVAOVIEN_DT_NOI', name: 'SOVAOVIEN_DT_NOI', index: 'SOVAOVIEN_DT_NOI', hidden: true},
            {label: 'DA_THANH_TOAN', name: 'DA_THANH_TOAN', index: 'DA_THANH_TOAN', hidden: true}
            , {label: 'CHUANDOANICD', name: 'CHUANDOANICD', index: 'CHUANDOANICD', hidden: true}
            , {label: 'Khoa', name: 'TENKHOA', index: 'TENKHOA', width: 300}
            , {label: 'CAPCUU', name: 'CAPCUU', index: 'CAPCUU', hidden: true}
            , {label: 'MOTA_CDHA', name: 'MOTA_CDHA', index: 'MOTA_CDHA', hidden: true},
            {label: 'sophieuthanhtoan', name: 'SOPHIEUTHANHTOAN', index: 'SOPHIEUTHANHTOAN', hidden: true},
            {label: 'tilemiengiam', name: 'TI_LE_MIEN_GIAM', index: 'TI_LE_MIEN_GIAM', hidden: true},
            {label: 'ngay_kb', name: 'NGAY_KB', index: 'NGAY_KB', hidden: true},
            {label: 'CO_BHYT', name: 'CO_BHYT', index: 'CO_BHYT', hidden: true},
            {label: 'SOBENHAN', name: 'SOBENHAN', index: 'SOBENHAN', hidden: true},
            {label: 'SOBENHAN_TT', name: 'SOBENHAN_TT', index: 'SOBENHAN_TT', hidden: true},
            {label: 'ICD', name: 'ICD', index: 'ICD', hidden: true},
            {label: 'NGUOI_THUC_HIEN', name: 'NGUOI_THUC_HIEN', index: 'NGUOI_THUC_HIEN', hidden: true},
            {label: 'NGAY_CHI_DINH_CT', name: 'NGAY_CHI_DINH_CT', index: 'NGAY_CHI_DINH_CT', hidden: true},
            {label: 'BACSI_CHIDINH', name: 'BACSI_CHIDINH', index: 'BACSI_CHIDINH', hidden: true}
        ],
        rowNum: 100000,
        ignoreCase: true,
        caption: "Danh sách bệnh nhân",
        onSelectRow: function (id) {
            $("#hsba_tabs").tabs("option", "disabled", [1]);
        },
        footerrow: true,
        beforeRequest: function () {
            showLoaderIntoWrapId("list_benhnhan_wrap");
        },
        loadComplete: function () {
            hideLoaderIntoWrapId("list_benhnhan_wrap");
        },
        gridComplete: function () {
            var sl = $("#list_benhnhan").jqGrid("getGridParam", "records");
            $("#list_benhnhan").jqGrid("footerData", "set", {
                TENBENHNHAN_HT: "Tổng: " + sl + " bệnh nhân",
            });

            if (singletonObject.hienThiMauSacBN == "1")
            {
                var rows = $("#list_benhnhan").getDataIDs();
                for (var i = 0; i < rows.length; i++)
                {
                    var CAPCUU = $("#list_benhnhan").jqGrid('getRowData')[i]["CAPCUU"];
                    //var SOTHEBHYT = $("#list_benhnhan").jqGrid('getRowData')[i]["SOTHEBHYT"].toString().length;
                    var CO_BHYT = $("#list_benhnhan").jqGrid('getRowData')[i]["CO_BHYT"].toString().length;
                    var tuoi = $("#list_benhnhan").jqGrid('getRowData')[i]["TUOI"];
                    var thanhtoan = $("#list_benhnhan").jqGrid('getRowData')[i]["DA_THANH_TOAN"];
                    if (CAPCUU === "1") {
                        $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {
                            color: 'red',
                            weightfont: 'bold'
                        });
                    } else if (CO_BHYT == 0&& thanhtoan == 0) {//CMU:26/10/2017
                        $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {
                            color: '#bf00ff',
                            weightfont: 'bold'
                        });
                    } else if (CO_BHYT == 0 && thanhtoan == 1) {
                        $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {
                            color: '##EE7600',
                            weightfont: 'bold'
                        });
                    } else if (tuoi.indexOf("tháng") != -1) {
                        $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {
                            color: '#00ff00',
                            weightfont: 'bold'
                        });
                    } else {
                        $("#list_benhnhan").jqGrid('setRowData', rows[i], false, {
                            color: 'black',
                            weightfont: 'bold',
                            background: 'white'
                        });
                    }
                }
            }

        },
        onRightClickRow: function (id) {
            if(id) {
                var rowData = getThongtinRowSelected("list_benhnhan");
                $.contextMenu('destroy', '#list_benhnhan tr');
                var items = {
                    "goiso": {name: '<p><i class="fa fa-volume-up text-primary" aria-hidden="true"></i> Gọi số bệnh nhân</p>'},
                    "huycdhachitiet": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Hủy kết quả</p>'},
                };
                $.contextMenu({
                    selector: '#list_benhnhan tr',
                    reposition : false,
                    callback: function (key, options) {
                        var idWrap = "list_benhnhan_wrap"
                        if (key == "goiso") {
                            var chuoi = "";
                            if(singletonObject.dvtt.slice(0,2) == "96"){
                                chuoi = singletonObject.phongDuocSet + "|" + rowData.STT_HANGNGAY + "|" + rowData.TENBENHNHAN + "|" + "0" + "|" + "CDHA" + "|" + "0";
                            } else {
                                chuoi = singletonObject.phongDuocSet + "|" + rowData.STT_HANGNGAY.toString().replace('<span class="cellWithoutBackground" style="background-color:white;color:black">', '').replace('</span>', '')+ "|" + rowData.TENBENHNHAN + "|" + rowData.CAPCUU;
                            }
                            if (singletonObject.kgggoiso == '3') {
                                var chuoihinhanh = goisolayhinhanh($("#list_benhnhan"), id);
                                saveTextAsFile(chuoihinhanh);
                            } else {
                                saveTextAsFile(chuoi);
                            }
                            saveTextAsFile(chuoi);
                        }
                        if (key == "huycdhachitiet") {
                            if(singletonObject.dvtt == 96004 && singletonObject.userId != "1344516") {
                                return jAlert("Bạn không có quyền hủy kết quả xét nghiệm, vui lòng liên hệ Minh", 'Thông báo');
                            }
                            var retbn = getThongtinRowSelected("list_benhnhan");
                            var DVTT = singletonObject.dvtt;
                            var SO_PHIEU = retbn.SO_PHIEU;
                            var SO_PHIEU_W_CT = SO_PHIEU + ';' + retbn.MA_CDHA;
                            var NOITRU = flag_noitru;
                            var MABENHNHAN = retbn.MABENHNHAN;
                            var MA_KHAM_BENH = retbn.MA_KHAM_BENH;
                            var STT_BENHAN = retbn.STT_BENHAN;
                            var STT_DOTDIEUTRI = retbn.STT_DOTDIEUTRI;
                            var STT_DIEUTRI = retbn.STT_DIEUTRI;
                            var TEN_BENH_NHAN = retbn.TENBENHNHAN;

                            var arr_ct = [singletonObject.dvtt, NOITRU, SO_PHIEU, MA_KHAM_BENH, STT_BENHAN, STT_DOTDIEUTRI, STT_DIEUTRI, 0];
                            if (singletonObject.admin != "0" || (retbn.NGUOI_THUC_HIEN == singletonObject.userId && singletonObject.tsktvhuyKQ == "0") || (retbn.NGUOI_THUC_HIEN == singletonObject.userId && retbn.NGAY_THUC_HIEN == singletonObject.ngayhientai && singletonObject.tsktvhuyKQ == "1")) {
                                var risarr = [SO_PHIEU, MABENHNHAN, NOITRU, "TDCN", "0"];
                                var urlarr = "ris_kiemtra_trangthai_cachup";
                                $.post(urlarr, {
                                    url: convertArray(risarr)
                                }).done(function (data) {
                                    if (data != "0") {
                                        return jAlert("Có ca chụp đang thực hiện bởi RIS. Không thể hủy phiếu. Vui lòng liên hệ khoa CDHA...", "Thông báo");
                                    } else {
                                        confirmToClient("Xác nhận hủy kết quả ?", function() {
                                            if (retbn.SO_PHIEU != "") {
                                                var url = "huyketquacdha?url=" + convertArray(arr_ct);//var url = "huyketquacdha?url=" + convertArray(arr);
                                                $.ajax({
                                                    url: url
                                                }).done(function (data) {
                                                    if(data == "RIS.1"){
                                                        jAlert("Trạng thái phiếu hiện tại trên RIS không cho phép hủy kết quả CĐHA của phiếu chỉ định này", "Thông báo");
                                                    } else if (data == "ERRLOGIN") {
                                                        jAlert("Xác thực đăng nhập RIS Connector thất bại, Vui lòng kiểm tra lại thông tin cấu hình kết nối RIS", "Thông báo");
                                                    } else if (data == "RISFAIL") {
                                                        jConfirm("Hủy kết quả CĐHA của phiếu trên RIS thất bại. Bạn có muốn tiếp tục?", "Thông báo", function (r) {
                                                            if (r.toString() == "true"){
                                                                $.post("ris_update_huytrangthai", {
                                                                    url: convertArray(arr_ct)
                                                                }).always(function() {
                                                                    var arr1 = [singletonObject.dvtt, "Hủy kết quả CDHA cho bệnh nhân " + TEN_BENH_NHAN + " với phiếu CDHA " + SO_PHIEU_W_CT, singletonObject.userId + "-" + singletonObject.user, "Hủy kết quả CDHA"];
                                                                    $.post("lichsusudung_insert", {url: convertArray(arr1)});
                                                                    jAlert("Hủy kết quả thành công", "Thông báo");
                                                                    notifiToClient("Green", "Hủy kết quả thành công");
                                                                    loadGridBenhNhan();
                                                                });
                                                            }
                                                        });
                                                    } else if(data == "SUCCESS"){
                                                        var arr1 = [singletonObject.dvtt, "Hủy kết quả CDHA cho bệnh nhân " + TEN_BENH_NHAN + " với phiếu CDHA " + SO_PHIEU_W_CT, singletonObject.userId + "-" + singletonObject.user, "Hủy kết quả CDHA"];
                                                        $.post("lichsusudung_insert", {url: convertArray(arr1)});
                                                        notifiToClient("Green", "Hủy kết quả thành công");
                                                        loadGridBenhNhan();
                                                    }
                                                });
                                            } else {
                                                jAlert("Chọn phiếu để hủy", 'Thông báo');
                                            }
                                        }, function () {

                                        })
                                    }
                                });
                            } else {
                                jAlert("Chỉ Admin hoặc bác sĩ thực hiện mới có quyền hủy!", 'Thông báo');
                            }
                        }
                    },
                    items: items
                });
            }
        },
        ondblClickRow: function (id) {
            if (singletonObject.khoaKham == "0") {
                var rowData = getThongtinRowSelected("list_benhnhan");
                flag_noitru = rowData.NOITRU;
                getThongTinBenhNhan(rowData);
                listXNSelected = [];
                allDataXN = [];
            }
        }
    });
    $("#list_benhnhan").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});

    $("#list_dslichsudt").jqGrid({
        datatype: "local",
        loadonce: true,
        height: 450,
        width: null,
        shrinkToFit: false,
        colNames: ["Ngày", "Đơn vị", "Chẩn đoán", "Tên điện tim", "Số lượng", "Đơn giá", "Thành tiền", "BHYT không chi", "Kết quả", "Kết luận",
            "sophieu", "stt_benhan","Kết quả"],
        colModel: [
            {name: 'NGAY_THUC_HIEN', index: 'NGAY_THUC_HIEN', width: 100},
            {name: 'TEN_DONVI', index: 'TEN_DONVI', width: 250},
            {name: 'CHANDOAN', index: 'CHANDOAN', width: 150},
            {name: 'TEN_CDHA', index: 'TEN_CDHA', width: 200},
            {name: 'SO_LUONG', index: 'SO_LUONG', width: 100, hidden: true},
            {name: 'DON_GIA', index: 'DON_GIA', width: 100, align: 'right', formatter: 'integer', formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}, hidden: true},
            {name: 'THANH_TIEN', index: 'THANH_TIEN', width: 100, align: 'right', formatter: 'integer', formatoptions: {decimalSeparator: ".", thousandsSeparator: ",", decimalPlaces: 2}, hidden: true},
            {name: 'BHYTKCHI', index: 'BHYTKCHI', width: 110, align: 'center', formatter: 'checkbox', formatoptions: {value: 'true:false'}, hidden: true},
            {name: 'KET_QUA', index: 'KET_QUA', width: 100, hidden: true},
            {name: 'MO_TA', index: 'MO_TA', width: 200},
            {name: 'SO_PHIEU_XN', index: 'SO_PHIEU_XN', width: 100, hidden: true},
            {name: 'STT_BENHAN', index: 'STT_BENHAN', width: 100, hidden: true},
            {name: 'KET_QUA', index: 'KET_QUA', width: 200}
        ],
        caption: "Lịch sử điện tim",
        rowNum: 100000
    });

    $("#dt_luumayth").click(function (evt) {
        var retbn = getThongtinRowSelected("list_benhnhan");
        var sophieu = retbn.SO_PHIEU;
        var noitru = retbn.NOITRU;
        var makhambenh = retbn.MA_KHAM_BENH;
        var sttbenhan = retbn.STT_BENHAN;
        var sttdotdieutri = retbn.STT_DOTDIEUTRI;
        var sttdieutri = retbn.STT_DIEUTRI;
        var macdha = retbn.MA_CDHA;
        if (sophieu != "" && macdha != "" && (retbn.SOVAOVIEN != "" || retbn.SOVAOVIEN_NOI != "")) {
            $.post("luu_maycdha_vaobangchitiet", {
                sophieu: sophieu, macdha: macdha, noitru: noitru,
                sttbenhan: sttbenhan, sttdotdieutri: sttdotdieutri, sttdieutri: sttdieutri,
                makhambenh: makhambenh, sovaovien: retbn.SOVAOVIEN,
                sovaovien_noi: retbn.SOVAOVIEN_NOI,
                sovaovien_dt_noi: retbn.SOVAOVIEN_DT_NOI,
                stt_may_cdha: formKetQuaDienTim.submission.data.STT_MAYCDHA
            }).done(function (data) {
                if (data == "1") {
                    notifiToClient("Green", "Cập nhật thành công");
                } else {
                    notifiToClient("Red", "Vui lòng kiểm tra lại thông tin");
                }
            }).fail(function () {
                notifiToClient("Red", "Vui lòng thử lại");
            });
        }
    });

    setTimeout(function() {
        initDataNotAsync();
        initDataAsync();

        // Khởi tạo autocomplete cho "Lời dặn bác sĩ"
        LoiDanBacSiSuggestions.initAutocomplete();
    });

    $("#xn_khoa").change(function() {
        $.get("cmu_list_KB_NOT_ds_phongkhamnoitru?url="+convertArray([$("#xn_khoa").val()])).done(function(data) {
            $("#xn_phong").html("")
            initSelect2IfnotIntance('xn_phong', data, 'MA_PHONG_BENH', 'TEN_PHONG_BENH', 0, 1, "", 1);
        })
    });

    $("#xn_lammoi").click(function() {
        loadGridBenhNhan();
    });
    $("#xn_trangthai").change(function() {
        loadGridBenhNhan();
    });

    $("#hsba_tab0_header").click(function() {
        $("#xn_lammoi").click();
    });
    // $('textarea#dt_ketqua').ckeditor();

    $("#xn_luuthongtin").click(function (evt) {
        var retbn = getThongtinRowSelected("list_benhnhan");
        var editorKey = Object.keys(CKEDITOR.instances).find(key => key.includes("KET_QUA"));
        if (editorKey) {
            formKetQuaDienTim.submission.data.KET_QUA = CKEDITOR.instances[editorKey].getData();
        }
        var dataForm = formKetQuaDienTim.submission.data
        if (dataForm.NGUOI_THUC_HIEN == "" || dataForm.NGUOI_THUC_HIEN == null) {
            return notifiToClient("Red", "Người đọc kết quả không hợp lệ");;
        }
        // if (dataForm.NGUOI_THUC_HIEN != "0" && dataForm.NGUOI_THUC_HIEN != singletonObject.userId && dataForm.NGUOI_THUC_HIEN != "" && $("#xn_trangthai").val() == "1") {
        //     return notifiToClient("Red", "Bạn không thể chỉnh sửa KQ điện tim của nhân viên khác!");
        // }
        if (singletonObject.nhapketluan_cls == "1" && (dataForm.MO_TA == "" || dataForm.MO_TA == null)) { // 20171102 NAN VINHVT HISHD-21197 ADD
            return notifiToClient("Red", "Chưa nhập kết luận cho bệnh nhân!");
        } else {
            var sophieu = retbn.SO_PHIEU;
            var makhambenh = retbn.MA_KHAM_BENH;
            var noitru = retbn.NOITRU;
            var sttbenhan = retbn.STT_BENHAN;
            var sttdotdieutri = retbn.STT_DOTDIEUTRI;
            var sttdieutri = retbn.STT_DIEUTRI;
            var macdha = retbn.MA_CDHA;
            var dvtt = singletonObject.dvtt;
            var ketqua = dataForm.KET_QUA;
            var ketluan = dataForm.MO_TA;
            var bacsichidinh = dataForm.BACSI_CHIDINH;
            var nguoithuchien = dataForm.NGUOI_THUC_HIEN;
            var kythuatvien = dataForm.KYTHUATVIEN;
            var loidanbacsi = dataForm.LOIDANBACSI;
            var maudientim = dataForm.MAU_DIEN_TIM;
            var ngaygioth_ct = moment(dataForm.NGAY_GIO_TH).format("DD/MM/YYYY HH:mm:ss");
            var ngay_cd= retbn.NGAY_CHI_DINH;
            var thoiGianBatDauCls= moment(dataForm.NGAY_TH_YL).format("DD/MM/YYYY HH:mm:ss");

            if (retbn.SOTHEBHYT !== ''){
                if(moment(thoiGianBatDauCls).diff(moment(ngay_cd), 'minutes') < 1){
                    return notifiToClient("Red","Thời gian thực hiện y lệnh: " + thoiGianBatDauCls + " không được nhỏ hơn hoặc bằng thời gian chỉ định: " + ngay_cd);
                }
                if(moment(ngaygioth_ct).diff(moment(thoiGianBatDauCls), 'minutes') < 1){
                    return notifiToClient("Red","Thời gian kết quả: " + ngaygioth_ct + " không được nhỏ hơn hoặc bằng thời gian thực hiện y lệnh: " + thoiGianBatDauCls);
                }

                if(moment(ngaygioth_ct).diff(moment(thoiGianBatDauCls), 'minutes') < 5){
                    return notifiToClient("Red","Thời gian thực hiện y lệnh: " + thoiGianBatDauCls + " đến thời gian kết quả: " + ngaygioth_ct + " KHÔNG ĐƯỢC NHỎ HƠN 5 PHÚT");
                }
            }

            var arrLogNew = [];
            var arrLogOld = [];
            var luuLogNew = formKetQuaDienTim.submission.data;
            var diffLog = getLogHSBAChinhSua(luuLogOld, luuLogNew, keyLuuLogDienTim);
            if (diffLog.length > 0) {
                arrLogOld.push(diffLog[0]);
                arrLogNew.push(diffLog[1]);
            }

            if(nguoithuchien == "" || nguoithuchien == 0){
                return notifiToClient("Red", "Chưa chọn người thực hiện")
            }
            if(kythuatvien == "" || kythuatvien == 0){
                return notifiToClient("Red", "Chưa chọn kỹ thuật viên thực hiện")
            }

            if(maudientim == "0" || maudientim == "") {
                return notifiToClient("Red", "Chưa chọn mẫu điện tim")
            } else if (macdha == "") {
                return notifiToClient("Red", "Chưa chọn điện tim để thực hiện")
            } else if (sophieu != "") {
                var thamso960543;
                var url = 'laythamso_donvi_motthamso?mathamso=960543';
                $.post(url).done(function(data) {
                    thamso960543 = data
                });
                $.post('cmu_post', {
                    url: [
                        sophieu, singletonObject.dvtt, macdha, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri,
                        retbn.SOVAOVIEN, retbn.SOVAOVIEN_NOI, retbn.SOVAOVIEN_DT_NOI,
                        'CMU_CHECKMAY_CDHA_SA'
                    ].join('```')
                }).done(function(data) {
                    if(thamso960543 == 1 && data > 0){
                        jAlert("Vui lòng Lưu máy thực hiện trước khi Lưu thông tin", "Cảnh báo");
                    } else if(ketluan.length < 5){
                        jAlert("Kết luận quá ngắn, vui lòng nhập nhiều hơn 5 ký tự", "Cảnh báo");
                    } else {
                        ngaygioth_ct = moment(dataForm.NGAY_GIO_TH).format("YYYY-MM-DD HH:mm:ss");
                        $.post('cmu_post', {
                            url: [singletonObject.dvtt, sophieu, macdha, ngaygioth_ct,'CMU_KTRA_GIOTHU_DIENTIM'].join("```")
                        }).then(function(data) {
                            if(data == 1) {
                                jAlert("Ngày giờ thực hiện không được nhỏ hơn ngày giờ chỉ định. Ngày chỉ định:" + ngay_cd)
                            } else {
                                var ktrathoigian = $.ajax({type: "POST", url: "cmu_post", async: false, //Chỉ định CLS Viện Phí lấy giá trên grid. cho phép sửa giá
                                    data: {url: [singletonObject.dvtt, sophieu,moment(dataForm.NGAY_GIO_TH).format("YYYY-MM-DD HH:mm:ss"),
                                            retbn.SOVAOVIEN_NOI == 0? retbn.SOVAOVIEN:retbn.SOVAOVIEN_NOI, noitru,'CMU_KTRATHOI_CDVAKQ'].join('```')}
                                }).responseText;
                                if(ktrathoigian == 1) {
                                    jAlert("Thời gian thực hiện không được dưới 5 phút", 'Cảnh báo');
                                    return false;
                                }
                                luuNguoiDocKetQua(sophieu, noitru == 1 ? retbn.SOVAOVIEN_NOI:retbn.SOVAOVIEN, retbn.SOVAOVIEN_DT_NOI, noitru, macdha);
                                $.post("capnhatketqua_dientim_svv", {
                                    sophieu: sophieu,
                                    macdha: macdha,
                                    dvtt: dvtt,
                                    ketqua: ketqua,
                                    ketluan: ketluan,
                                    bacsichidinh: getTextSelectedFormio(formKetQuaDienTim.getComponent('NGUOI_CHI_DINH')),
                                    bacsithuchien: getTextSelectedFormio(formKetQuaDienTim.getComponent('NGUOI_THUC_HIEN_CK')),
                                    loidanbacsi: loidanbacsi,
                                    maudientim: maudientim,
                                    noitru: noitru,
                                    sttbenhan: sttbenhan,
                                    sttdotdieutri: sttdotdieutri,
                                    sttdieutri: sttdieutri,
                                    makhambenh: makhambenh,
                                    sovaovien: retbn.SOVAOVIEN,
                                    sovaovien_noi: retbn.SOVAOVIEN_NOI,
                                    sovaovien_dt_noi: retbn.SOVAOVIEN_DT_NOI,
                                    nguoithuchien: dataForm.NGUOI_THUC_HIEN,
                                    ngaygioth_ct: ngaygioth_ct,
                                    mota_xml5: ketluan,
                                    ketqua_xml5: ketqua,
                                    maBenhLyTruocCdha: dataForm.MABENHLY_TRUOCCDHA,
                                    maBenhLySauCdha: dataForm.MABENHLY_SAUCDHA,
                                    chanDoanTruocCdha: dataForm.CHANDOAN_TRUOCCDHA,
                                    chanDoanSauCdha: dataForm.CHANDOAN_SAUCDHA
                                    ,thoiGianBatDauCls: thoiGianBatDauCls
                                    , maIcd: dataForm.MA_ICD,
                                    tenIcd: dataForm.TEN_ICD,
                                    maBenhLy:  dataForm.MA_BENH_LY_THEO_ICD
                                })
                                    .done(function (datares) {
                                        if (datares == -1) {
                                            jAlert("Dữ liệu bệnh nhân đã khóa không thể chỉnh sửa, vui lòng liên hệ admin.", 'Cảnh báo');
                                            return false;
                                        }
                                        $.post("luu_maycdha_vaobangchitiet", {
                                            sophieu: sophieu, macdha: macdha, noitru: noitru,
                                            sttbenhan: sttbenhan, sttdotdieutri: sttdotdieutri, sttdieutri: sttdieutri,
                                            makhambenh: makhambenh, sovaovien: retbn.SOVAOVIEN,
                                            sovaovien_noi: retbn.SOVAOVIEN_NOI,
                                            sovaovien_dt_noi: retbn.SOVAOVIEN_DT_NOI,
                                            stt_may_cdha: formKetQuaDienTim.submission.data.STT_MAYCDHA
                                        }).done(function (data) {
                                            if (data == "1") {
                                                // notifiToClient("Green", "Cập nhật thành công");
                                            } else {
                                                notifiToClient("Red", "Lỗi lưu máy");
                                            }
                                        }).fail(function () {
                                            notifiToClient("Red", "Lỗi lưu máy");
                                        });
                                        $.post('cmu_post', {
                                            url: [
                                                singletonObject.dvtt, sophieu, macdha,
                                                retbn.SOVAOVIEN == 0? retbn.SOVAOVIEN_NOI: retbn.SOVAOVIEN, retbn.SOVAOVIEN_DT_NOI,
                                                dataForm.KYTHUATVIEN, dataForm.NGUOI_THUC_HIEN,
                                                'CMU_UPDATE_KTV_XQUANG'
                                            ].join('```')
                                        })

                                        luuLogHSBATheoBN({
                                            SOVAOVIEN: noitru == 0 ? retbn.SOVAOVIEN : retbn.SOVAOVIEN_NOI,
                                            LOAI: LOGHSBALOAI.CDHA.KEY,
                                            NOIDUNGBANDAU: arrLogOld.join("; "),
                                            NOIDUNGMOI: arrLogNew.join("; "),
                                            USERID: singletonObject.userId,
                                            ACTION: LOGHSBAACTION.EDIT.KEY,
                                            NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                                        });
                                        // loadGridBenhNhan();

                                        // Lưu gợi ý "Lời dặn bác sĩ" vào localStorage
                                        if (loidanbacsi && loidanbacsi.trim().length > 0) {
                                            LoiDanBacSiSuggestions.addSuggestion(loidanbacsi);
                                        }

                                        notifiToClient("Green", "Cập nhật thành công");
                                    });
                            }
                        })
                    }
                });
            }
        }
    });

    $("#dt_inphieucd").click(function (evt) {
        var retbn = getThongtinRowSelected("list_benhnhan");
        if (retbn.NOITRU == "0") {
            var makhambenh = retbn.MA_KHAM_BENH;
            var sophieu = retbn.SO_PHIEU;
            var bhytkhongchi = retbn.CO_BHYT == 1 ? 0 : 1;
            var arr = [makhambenh, bhytkhongchi, sophieu, singletonObject.dvtt, "0", "1", retbn.NOITRU];
            //Ðo?n mã c?a BDH
            var hoten = retbn.TENBENHNHAN;
            var tuoi = retbn.TUOI;
            var phai = retbn.GIOITINH;
            if (phai.toString() == "true") {
                phai = "Nam";
            } else {
                phai = "Nữ";
            }
            var gioitinh = phai;
            var diachi = retbn.DIACHI;
            var bschidinh = retbn.NGUOI_CHI_DINH;
            var sothebaohiem = "";
            //Ki?m tra b?nh nhân có BHYT
            if (bhytkhongchi == "0")
                sothebaohiem = $("#sothebhyt").val();
            var maphong = retbn.MA_PHONG_CDHA;
            if (singletonObject.thanhtoannhieunac == "1" && bhytkhongchi == "0") {
                var url_taobk = "taobangke_truocin?makb=" + makhambenh + "&dvtt=" + singletonObject.dvtt + "&sophieu=0";
                $.ajax({
                    url: url_taobk
                }).done(function (data) {
                    if (singletonObject.dvtt.indexOf("52") == 0 || singletonObject.dvtt == "82023") {
                        var url = "laykyhieubaocaophongcdha?maphongcdha=" + maphong;
                        $.ajax({
                            url: url
                        }).done(function (data) {
                            //1:PXQ
                            if (data == "1" && sophieu != "") {
                                arr = ["", hoten, diachi, tuoi, gioitinh, makhambenh, sophieu, "0",
                                    singletonObject.dvtt, "0", "", "", "", "", "", "", sothebaohiem, "", bschidinh, ""];
                                url = "bdh_inketquaxquang?url=" + convertArray(arr);
                                previewPdfDefaultModal(url, "inketquadientim");
                            } else {
                                url = "inphieucdha?url=" + convertArray(arr);
                                previewPdfDefaultModal(url, "inketquadientim");
                            }
                        });
                    } else {
                        url = "inphieucdha?url=" + convertArray(arr);
                        previewPdfDefaultModal(url, "inketquadientim");
                        // if ("${taibaocaovemay}" == "1") {
                        //     var redirectWindow = window.open(url, '_blank');
                        //     redirectWindow.location;
                        //     return false;
                        // } else
                        //     previewPdfDefaultModal(url, "inketquadientim");
                    }
                });
            } else {
                if (singletonObject.dvtt.indexOf("52") == 0 || singletonObject.dvtt == "82023") {
                    var url = "laykyhieubaocaophongcdha?maphongcdha=" + maphong;
                    $.ajax({
                        url: url
                    }).done(function (data) {
                        if (data == "1" && sophieu != "") {
                            arr = ["", hoten, diachi, tuoi, gioitinh, makhambenh, sophieu, "0",
                                singletonObject.dvtt, "0", "", "", "", "", "", "", sothebaohiem, "", bschidinh, ""];
                            url = "bdh_inketquaxquang?url=" + convertArray(arr);
                            previewPdfDefaultModal(url, "inketquadientim");
                        } else {
                            url = "inphieucdha?url=" + convertArray(arr);
                            previewPdfDefaultModal(url, "inketquadientim");
                        }
                    });
                } else {
                    url = "inphieucdha?url=" + convertArray(arr);
                    previewPdfDefaultModal(url, "inketquadientim");
                    // if ("${taibaocaovemay}" == "1") {
                    //     var redirectWindow = window.open(url, '_blank');
                    //     redirectWindow.location;
                    //     return false;
                    // } else
                    //     $(location).attr('href', url);
                }
            }
        }else{
            var sophieucdha = retbn.SO_PHIEU;
            var bhytkhongchi = retbn.CO_BHYT == 1 ? 0 : 1;
            var sobenhantt = retbn.SOBENHAN_TT;
            var sobenhan = retbn.SOBENHAN;
            var icd_khoadt = retbn.ICD;
            var ten_khoadt = retbn.CHUANDOANICD.replace(retbn.ICD + ' - ', '');
            var soba;
            if (sobenhan != "")
                soba = sobenhan;
            else
                soba = sobenhantt;
            var arr = [retbn.MABENHNHAN, bhytkhongchi, sophieucdha, singletonObject.dvtt, soba, retbn.STT_BENHAN, retbn.STT_DOTDIEUTRI, retbn.STT_DIEUTRI, icd_khoadt, ten_khoadt, "", "0"
                , retbn.SOVAOVIEN_NOI, retbn.SOVAOVIEN_DT_NOI, 0, retbn.NOITRU];
            var url = "noitru_inphieucdha_svv?url=" + convertArray(arr);
            previewPdfDefaultModal(url, "inketquadientim");
        }
    });

    $("#dt_inphieu").click(async function() {
        var url = await getLinkInPhieuKetQua();
        previewPdfDefaultModal(url, "inketquadientim");
    });

    $("#dt_huykyso").click(function() {
        var dataForm = getThongtinRowSelected("list_benhnhan");
        // if(singletonObject.userId != dataForm.MA_BS_DOC_KQ) {
        //     return notifiToClient("Red", "Bạn không thể hủy ký số phiếu kết quả xét nghiệm của người khác");
        // }
        confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
            huykysoFilesign769("PHIEUKQ_DIENTIM", dataForm.SO_PHIEU, singletonObject.userId, singletonObject.dvtt,
                dataForm.NOITRU == 0 ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI, dataForm.SOVAOVIEN_DT_NOI ? dataForm.SOVAOVIEN_DT_NOI : 0, -1, function(data) {
                    showOrHideByClass("hsba_tab1","kyso","chuakyshow")
                    getThongTinBenhNhan(dataForm);
                    luuLogHSBATheoBN({
                        SOVAOVIEN: dataForm.NOITRU == "0" ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI,
                        LOAI: LOGHSBALOAI.CDHA.KEY,
                        NOIDUNGBANDAU: "",
                        NOIDUNGMOI: "Huỷ ký số phiếu kết quả điện tim " + dataForm.SO_PHIEU,
                        USERID: singletonObject.userId,
                        ACTION: LOGHSBAACTION.EDIT.KEY,
                        NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                    });
                })
        }, function () {

        })
    });

    $("#dt_ekip").click(function(){
        var retbn = $("#list_benhnhan").jqGrid ('getGridParam', 'selrow');
        var ret = getThongtinRowSelected("list_benhnhan");

        if (!!retbn){
            $("#modalFormEkipDienTim").modal("show")
            $("#dt_ekiptenbn").val(ret.TENBENHNHAN)
            var dsBacSi = singletonObject.danhsachnhanvien;
            initSelect2IfnotIntance("dt_ekipbacsi", dsBacSi, 'MA_NHANVIEN', 'TEN_NHANVIEN', 0, 0, singletonObject.userId);
            initSelect2IfnotIntance("dt_ekipdungcu", dsBacSi, 'MA_NHANVIEN', 'TEN_NHANVIEN', 0, 0, singletonObject.userId);
            $("#dt_ekipbacsi").select2();
            $("#dt_ekipdungcu").select2();

            var url2 = "cmu_getlist?url="+convertArray([singletonObject.dvtt,
                ret.SOVAOVIEN_NOI == 0? ret.SOVAOVIEN:ret.SOVAOVIEN_NOI ,ret.SO_PHIEU,ret.MA_CDHA,"CMU_GETEKIPPT"]);
            $.ajax({
                url: url2
            }).done(function(data) {
                if(data.length > 0) {
                    $("#dt_ekipbacsi").val(data[0].PM1).select2();
                    $("#dt_ekipdungcu").val(data[0].PM2).select2();
                    var t = new Date(data[0].NGAYTHUHIEN)
                    $("#dt_ekipthoigian").val((t.getDate() < 10? "0"+t.getDate():t.getDate())+"/"+ (t.getMonth()+1 < 10 ? "0"+(t.getMonth()+1): t.getMonth()+1 ) + "/" + t.getFullYear()
                        + " " + (t.getHours() < 10? "0" + t.getHours() : t.getHours())
                        +":"+ (t.getMinutes() < 10? "0" + t.getMinutes() : t.getMinutes()))
                }

            })
        }
    });

    $("#ekipdientim_luu").click(function(){
        var ret = getThongtinRowSelected("list_benhnhan");
        var timetest = $("#dt_ekipthoigian").val().split(" ")
        var cmungaygb = timetest[0].split("/")
        var dsBacSi = singletonObject.danhsachnhanvien;
        $.post('cmu_post', {
            url: [
                singletonObject.dvtt,
                ret.NOITRU==1?ret.SOVAOVIEN_NOI:ret.SOVAOVIEN,
                ret.SO_PHIEU,
                ret.MABENHNHAN,
                ret.TENBENHNHAN,
                ret.MA_CDHA,
                cmungaygb[2]+"-"+cmungaygb[1]+"-"+cmungaygb[0] + " " + timetest[1] +":00",
                ret.TEN_CDHA,
                ret.PHONG_CHI_DINH,
                singletonObject.maphongbenh,
                getTenBacSiLog(dsBacSi, $("#dt_ekipbacsi").val()),
                '',
                '',
                getTenBacSiLog(dsBacSi, $("#dt_ekipdungcu").val()),
                $("#dt_ekipbacsi").val(),
                $("#dt_ekipdungcu").val(),
                '',
                '',
                'DIENTIM',
                'CMU_LUUEKIPSA'
            ].join('```')
        }).done(function(data) {
            if(data == '0') {
                notifiToClient("Green", "Cập nhật ekip thành công")
            }
            else if(data == -1) {
                notifiToClient("Red", "Đã khóa số liệu bệnh nhân, vui lòng admin để mở khóa")
            } else {
                notifiToClient("Red", "Đã có lỗi xảy ra vui lòng thao tác lại")
            }
        }).fail(function() {
            notifiToClient("Red", "Đã có lỗi xảy ra vui lòng thao tác lại")
        })
    });

    $("#ekipdientim_huy").click(function(){
        var ret = getThongtinRowSelected("list_benhnhan");
        var timetest = $("#dt_ekipthoigian").val().split(" ")
        var cmungaygb = timetest[0].split("/")
        var dsBacSi = singletonObject.danhsachnhanvien;
        confirmToClient("Bạn có chắc chắn muốn hủy ekip ?", function() {
            $.post('cmu_post', {
                url: [
                    singletonObject.dvtt,
                    ret.NOITRU==1?ret.SOVAOVIEN_NOI:ret.SOVAOVIEN,
                    ret.SO_PHIEU,
                    ret.MABENHNHAN,
                    ret.TENBENHNHAN,
                    ret.MA_CDHA,
                    cmungaygb[2]+"-"+cmungaygb[1]+"-"+cmungaygb[0] + " " + timetest[1] +":00",
                    ret.TEN_CDHA,
                    ret.PHONG_CHI_DINH,
                    singletonObject.maphongbenh,
                    getTenBacSiLog(dsBacSi, $("#dt_ekipbacsi").val()),
                    '',
                    '',
                    getTenBacSiLog(dsBacSi, $("#dt_ekipdungcu").val()),
                    '',
                    '',
                    '',
                    '',
                    '',
                    'CMU_HUYEKIPSA'
                ].join('```')
            }).done(function(data) {
                if(data == '0') {
                    notifiToClient("Green", "Hủy ekip thành công")
                }
                else if(data == -1) {
                    notifiToClient("Red", "Đã khóa số liệu bệnh nhân, vui lòng admin để mở khóa")
                } else {
                    notifiToClient("Red", "Đã có lỗi xảy ra vui lòng thao tác lại")
                }
            }).fail(function() {
                notifiToClient("Red", "Đã có lỗi xảy ra vui lòng thao tác lại")
            })
        }, function () {

        })
    });

    // $("#dt_hinhanh").click(function(){
    //     $("#modalFormHinhAnhDienTim").modal("show")
    //     hinhAnhDT();
    // });

    $("#dt_kysosm").click(async function(){
        var url = await getLinkInPhieuKetQua();
        formKetQuaDienTim.emit("checkValidity");
        if (!formKetQuaDienTim.checkValidity(null, false, null, true)) {
            return notifiToClient("Red", "Vui lòng cập nhật đầy đủ thông tin");
        }

        var dataForm = getThongtinRowSelected("list_benhnhan");
        // if(singletonObject.userId != dataForm.MA_BS_DOC_KQ) {
        //     return notifiToClient("Red", "Bạn không thể ký số phiếu kết quả điện tim của người khác");
        // }
        previewAndSignPdfDefaultModal({
            url: url,
            idButton: 'kysoketquadt',
        }, function(){
            $("#kysoketquadt").click(function() {
                kySoChung({
                    dvtt: singletonObject.dvtt,
                    userId: singletonObject.userId,
                    url: url,
                    noitru: dataForm.NOITRU,
                    loaiGiay: "PHIEUKQ_DIENTIM",
                    maBenhNhan: dataForm.MABENHNHAN,
                    soBenhAn: dataForm.SOBENHAN ? dataForm.SOBENHAN : "",
                    soPhieuDichVu: dataForm.SO_PHIEU,
                    soVaoVien: dataForm.SOVAOVIEN_NOI == 0 ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI,
                    soVaoVienDT: dataForm.SOVAOVIEN_DT_NOI ? dataForm.SOVAOVIEN_DT_NOI : 0,
                    keyword: "BÁC SĨ CHUYÊN KHOA",
                    fileName: "Phiếu kết quả điện tim " + dataForm.SO_PHIEU + " - " + dataForm.TENBENHNHAN,
                }, function(dataKySo) {
                    luuLogHSBATheoBN({
                        SOVAOVIEN: dataForm.NOITRU == "0" ? dataForm.SOVAOVIEN : dataForm.SOVAOVIEN_NOI,
                        LOAI: LOGHSBALOAI.CDHA.KEY,
                        NOIDUNGBANDAU: "",
                        NOIDUNGMOI: "Ký số phiếu kết quả điện tim " + dataForm.SO_PHIEU,
                        USERID: singletonObject.userId,
                        ACTION: LOGHSBAACTION.EDIT.KEY,
                        NGOAI: dataForm.NOITRU == "0" ? 1 : 0
                    });
                    showOrHideByClass("hsba_tab1","chuakyshow","kyso")
                    $("#modalPreviewAndSignPDF").modal("hide");
                    getThongTinBenhNhan(dataForm);
                });
            });
        });
    });

    $("#dt_kysotoken").click(async function(){
        var url = await getLinkInPhieuKetQua();
        doSignPlugin(url);
    });

    $("#dt_xemkysosm").click(function(){
        inkysmartdientim();
    });

    $("#dt_xemkysotoken").click(function(){
        inKyToken();
    });

    $("#dt_lichsu").click(function(){
        var retbn = getThongtinRowSelected("list_benhnhan");
        $("#modalDSLichSuDT").modal("show")
        var url = "cmu_danhsach_lichsu_cdha?mabenhnhan=" + retbn.MABENHNHAN + "&type=TDCN";
        $("#list_dslichsudt").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
    });

    var dataURL = "";

    $('#hinhanhdt_luu').on('click', function (evt) {
        var retbn = getThongtinRowSelected("list_benhnhan");

        var macdha = retbn.MA_CDHA;
        var sophieu = retbn.SO_PHIEU;
        var noitru = retbn.NOITRU;
        var makhambenh = retbn.MA_KHAM_BENH;
        var sttbenhan = retbn.STT_BENHAN;
        var sttdotdieutri = retbn.STT_DOTDIEUTRI;
        var sttdieutri = retbn.STT_DIEUTRI;
        var imageInput = document.getElementById("hinhanhdt_duyet");
        var file = imageInput.files[0];
        if (!file) {
            return notifiToClient("Red", "Vui lòng up ảnh");
        }
        if (dataURL != "" && sophieu != "" && macdha != "") {
            $.post("luuhinhanh_sieuam", {
                sophieu: sophieu,
                macdha: macdha,
                dvtt: singletonObject.dvtt,
                hinh: dataURL,
                noitru: noitru,
                sttbenhan: sttbenhan,
                sttdotdieutri: sttdotdieutri,
                sttdieutri: sttdieutri,
                makhambenh: makhambenh
            }).done(function () {
                    notifiToClient("Green", "Lưu hình ảnh thành công")
                hinhAnhDT();
                }).fail(function () {
                notifiToClient("Red", "Lưu hình ảnh thất bại")
            });
        }
    });
    $('#hinhanhdt_duyet').on('change', function (evt) {
        var input = evt.target;
        if(this.files[0].size > 5097152) {
            jAlert("File quá lớn (tối đa 5MB)");
            this.value = "";
        }
        var bg_preview = document.getElementById('say-cheese-snapshotsdt').childNodes[0];
        var bg_file = document.getElementById('hinhanhdt_duyet').files[0];

        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.readAsDataURL(this.files[0]);
            reader.onload = function () {
                dataURL = reader.result;
                bg_preview.src = reader.result;
            };
            reader.onerror = function (error) {
                console.log('Error: ', error);
            };
        }
    });
    var rotate = 0;
    $("#hinhanhdt_xoa").click(function (evt) {
        var retbn = getThongtinRowSelected("list_benhnhan");
        var selectedRowId = $("#list_benhnhan").jqGrid('getGridParam', 'selrow');
        var imageInput = document.getElementById("hinhanhdt_duyet");
        var file = imageInput.files[0];
        if (!file) {
            return notifiToClient("Red", "Vui lòng up ảnh");
        }
        if (retbn.KEYSIGN){
            return notifiToClient("Red", "Đã ký số không thể xóa !!!");
        }
        var macdha = retbn.MA_CDHA;
        var sophieu = retbn.SO_PHIEU;
        var noitru = retbn.NOITRU;
        var makhambenh = retbn.MA_KHAM_BENH;
        if (selectedRowId && macdha) {
            confirmToClient("Bạn có chắc chắn muốn xóa ảnh này?", function() {
                $.post("cmu_post",
                    {
                        url: [sophieu,singletonObject.dvtt, macdha, $("#snapshot-sttdt").val(),noitru,"CLS_SIEUAM_DELETE_HINHANH_F"].join('```')
                    }).done(function () {
                    notifiToClient("Green", "Xóa thành công");
                    hinhAnhDT()
                });
            }, function () {

            })
        } else {
            notifiToClient("Red", "Chưa chọn bệnh nhân để thực hiện");
        }
    });
    $("#dt_xemanh").click(function (evt) {
        var imageInput = document.getElementById("hinhanhdt_duyet");
        var file = imageInput.files[0];
        if (!file) {
            return notifiToClient("Red", "Vui lòng up ảnh");
        }
        $("#modalHienThiHinhAnh").modal("show");
        $('#modalImage').attr('src', dataURL);
    })
    // $(document).on('click', '#snapshot-rotate', function(evt) {
    $(document).on('click', '#snapshot-rotate', function(evt) {
        var canvas = document.getElementById("canvas");
        var imageSrc = document.getElementById("preview-imagedt");
        var ctx = canvas.getContext("2d");
        var imageInput = document.getElementById("hinhanhdt_duyet");
        var file = imageInput.files[0];
        rotate += 90;
        var angle = rotate;
        if (!file) {
            return notifiToClient("Red", "Vui lòng up ảnh");
        }
        const reader = new FileReader();
        reader.onload = function (e) {
            const img = new Image();
            img.onload = function () {
                // Calculate canvas size after rotation
                var radians = (Math.PI / 180) * angle;
                var sin = Math.abs(Math.sin(radians));
                var cos = Math.abs(Math.cos(radians));
                var newWidth = Math.ceil(img.width * cos + img.height * sin);
                var newHeight = Math.ceil(img.width * sin + img.height * cos);

                canvas.width = newWidth;
                canvas.height = newHeight;

                // Move the origin to the center and rotate
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.translate(newWidth / 2, newHeight / 2);
                ctx.rotate(radians);

                // Draw the image
                ctx.drawImage(img, -img.width / 2, -img.height / 2);

                // Get the rotated image as base64
                const base64Image = canvas.toDataURL("image/jpeg");
                imageSrc.src = base64Image;
                dataURL = base64Image;

            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    });

    function hinhAnhDT(){
        var retbn = getThongtinRowSelected("list_benhnhan");

        var macdha = retbn.MA_CDHA;
        var sophieu = retbn.SO_PHIEU;
        var noitru = retbn.NOITRU;
        var makhambenh = retbn.MA_KHAM_BENH;
        var sttbenhan = retbn.STT_BENHAN;
        var sttdotdieutri = retbn.STT_DOTDIEUTRI;
        var sttdieutri = retbn.STT_DIEUTRI;
        var arr = [sophieu, singletonObject.dvtt, macdha, noitru, makhambenh, sttbenhan, sttdotdieutri, sttdieutri, "0"];
        var url = 'sieuam_danhsach_hinhanh?url=' + convertArray(arr);
        var imageSrc = document.getElementById("preview-imagedt");
        // imageSrc.src = "resources/webcam/camera_png.jpg";
        var imageInput = document.getElementById("hinhanhdt_duyet");
        imageInput.value = "";
        dataURL = "";
        $('#snapshot-sttdt').val("");
        $.get(url).done(function(data){
            if (data && data.length > 0){
                data.forEach(function(obj) {
                    $('#preview-imagedt').attr('src', obj.HINHANH);
                    dataURL = obj.HINHANH;
                    $('#snapshot-sttdt').val( obj.STT);
                    const url = obj.HINHANH;
                    fetch(url)
                        .then(res => res.blob())
                        .then(blob => {
                            var file = new File([blob], "dt.jpg",{ type: "image/jpeg" })
                            const dataTransfer = new DataTransfer();
                            dataTransfer.items.add(file);
                            imageInput.files = dataTransfer.files;
                        })
                    $("#hinhanhdt_xoa").show()
                })
            } else {
                imageSrc.src = "resources/webcam/camera_png.jpg";
                $("#hinhanhdt_xoa").hide()
            }
        })
    }

    function goisolayhinhanh(mgrid, cur_id) {
        var IDS = mgrid.jqGrid("getDataIDs");
        var count = 5 + parseInt(cur_id - 1);
        var chuoitong = "";
        for (var i = parseInt(cur_id - 1); i < count; i++) {
            var id = IDS[i];
            var ret = mgrid.jqGrid('getRowData', id);
            if (ret.STT_HANGNGAY == undefined || ret.STT_HANGNGAY == null){
                break;
            }
            var chuoi = singletonObject.tenphongbenh + "|" + ret.STT_HANGNGAY + "|" + ret.TENBENHNHAN + "|" + "0" + "|" + " " + "|" + singletonObject.dvtt + "|" + "" + "|" + " ";
            chuoitong = chuoitong + chuoi + "@";
        }
        return chuoitong;
    }

    function loadGridBenhNhan(rowId = null) {
        try {
            $("#hsba_tabs").tabs("option", "disabled", [1]);
            var ngay = convertStr_MysqlDate($("#xn_ngaychidinh").val());
            var dvtt = singletonObject.dvtt;
            var phong = singletonObject.maphongbenh;
            var phongban = Number($("#xn_khoa").val()) ? $("#xn_khoa").val() : -1;
            var phongbenh = $("#xn_phong").val() ? $("#xn_phong").val() : -1;
            var doituong = $("#xn_doituong").val();
            var loaidt = $("#xn_loaidt").val();
            var daxetnghiem = $("#xn_trangthai").val();
            var arr, url;
            if (singletonObject.locDsBnTheoPhong == "1") {
                arr = [dvtt, ngay, phong, daxetnghiem, phong, maphongcdha];
                url = 'bdh_dientim_ds_benhnhan_cothamso?url=' + convertArray(arr);
            } else {
                // arr = [dvtt, ngay, phong, daxetnghiem, phongban, phongbenh, doituong, loaidt, ngay, "0"];
                // url = "dientim_ds_benhnhan_cothamso?url=" + convertArray(arr);
                arr = [dvtt, ngay, daxetnghiem, phongban, phongbenh, doituong, singletonObject.thuchienclsphaidongtienvienphi, loaidt, singletonObject.BN_MIENPHI_KBH_KHHONGCANDONGTIEN, ngay];
                url = "cmu_list_CMU_DSBN_DT_EMRV2?url=" + convertArray(arr);
            }
            $("#list_benhnhan").jqGrid('setGridParam', {datatype: 'json', url: url}).trigger('reloadGrid');
            if (rowId) {
                $("#list_benhnhan").jqGrid("setSelection", rowId);
                setTimeout(function () {
                    $("#list_benhnhan tr[id='" + rowId + "']").trigger("dblclick");
                }, 1000);
            }
        } catch (error) {
        }
    }

    async function initDataAsync() {
        showLoaderIntoWrapId("hsba_tabs");
        try {
            const danhsachphongbanPromise = $.get("cmu_list_STG_TAM_select_phong_ban?url=" + convertArray([singletonObject.dvtt]));
            // const danhsachphongbenhPromise = $.get("cmu_list_KB_NOT_ds_phongkhamnoitru?url=" + convertArray([singletonObject.makhoa]));
            singletonObject.danhsachphongban = await danhsachphongbanPromise;
            // singletonObject.danhsachphongbenh = await danhsachphongbenhPromise;
            singletonObject.danhsachphongbanFormio = singletonObject.danhsachphongban.map(function(object) {
                return {
                    label: object.TENKHOA,
                    value: object.MAKHOA,
                }
            })
            initSelect2IfnotIntance('xn_khoa', singletonObject.danhsachphongban, 'MAKHOA', 'TENKHOA', 0, 1, "", 1);
            $("#xn_khoa").change();
            hideSelfLoadingByClass("btn-loading");
            $("#hsba_tabs").tabs("option", "disabled", [1]);
            loadGridBenhNhan();
            hideLoaderIntoWrapId("hsba_tabs");
        } catch (error) {
            hideLoaderIntoWrapId("hsba_tabs");
        }
    }

    function initDataNotAsync() {
        $.get("cmu_list_CMU_DSNHANVIENTOANBV?url="+convertArray([singletonObject.dvtt])).done(function(data){
            singletonObject.danhsachnhanvien = data;
            singletonObject.danhsachnhanvienFormio = data.map(function(object) {
                return {
                    label: object.TEN_NHANVIEN,
                    value: object.MA_NHANVIEN.toString(),
                }
            });
        });
        $.get("cmu_list_CMU_DSNHANVIENTOANBV_V2?url="+convertArray([singletonObject.dvtt])).done(function(data){
            singletonObject.danhsachtatcanhanvien = data.map(function(object) {
                return {
                    label: object.TEN_NHANVIEN,
                    value: object.MA_NHANVIEN.toString(),
                    tennhanvien: object.TEN_NHANVIEN,
                }
            });
        });
        $.get("cmu_list_CMU_DSNHANVIEN?url="+convertArray([singletonObject.makhoa])).done(function(data){
            singletonObject.danhsachnhanvienkhoa = data.filter(function(obj){
                return obj.MANHANVIEN != 0;
            });
            singletonObject.danhsachnhanvienkhoaFormio = singletonObject.danhsachnhanvienkhoa.map(function(object) {
                return {
                    label: object.TENNHANVIEN,
                    value: object.MANHANVIEN.toString(),
                }
            });
        });
        $.get("danhsachphongban?madv="+singletonObject.dvtt).done(function(data){
            singletonObject.danhsachtatcaphongban = data.filter(function(obj){
                return obj.HOAT_DONG == 1;
            });
            singletonObject.danhsachtatcaphongbanFormio = singletonObject.danhsachtatcaphongban.map(function(object) {
                return {
                    label: object.TEN_PHONGBAN,
                    value: object.MA_PHONGBAN.toString()
                }
            });
        });
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, "CLS_CDHA_SELECT_MAUDIENTIM_F"])).done(function(data){
            // singletonObject.danhsachmaudt = data.filter(function(obj){
            //     return obj.HOATDONG == 1;
            // });
            singletonObject.danhsachmaudtFormio = data.map(function(object) {
                return {
                    label: object.TEN_MAUDIENTIM,
                    value: object.MA_MAUDIENTIM.toString(),
                }
            });
        });
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, '', "DANHSACH_MAYCDHA_DMCDHA_NS"])).done(function(data){
            singletonObject.danhsachmaycdha = data
            singletonObject.danhsachmaycdhaFormio = data.map(function(object) {
                return {
                    label: object.TEN_MAY,
                    value: object.STT.toString(),
                }
            });
        });
    }

    function getThongTinBenhNhan(rowData, isloadGridDienTim = true) {
        var sovaovien = rowData.SOVAOVIEN != '0' ? rowData.SOVAOVIEN : rowData.SOVAOVIEN_NOI;
        var ret = getThongtinRowSelected("list_benhnhan");
        try {
            showLoaderIntoWrapId("hsba_tabs");
            // if (rowData.NGAY_CHI_DINH_CT) {
            //     rowData.NGAY_CHI_DINH_CT_FORM = rowData.NGAY_CHI_DINH_CT
            // }
            // if (rowData.TINH_TIEN_EKIP) {
            //     rowData.TINH_TIEN_EKIP = rowData.TINH_TIEN_EKIP == 1 ? true : false
            // }

            var editorKey = CKEDITOR.instances ? Object.keys(CKEDITOR.instances).find(key => key.includes("KET_QUA")) : '';
            if (editorKey && CKEDITOR.instances[editorKey]) {
                CKEDITOR.instances[editorKey].destroy();
            }

            $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, rowData.SO_PHIEU, rowData.MA_CDHA, sovaovien, rowData.SOVAOVIEN_DT_NOI, rowData.STT_BENHAN, rowData.NOITRU, "CMU_DT_EMR_GET_INFOV2"])).done(function(dataInfo){
                if(dataInfo.length == 0) {
                    return notifiToClient("Red", "Không tìm thấy thông tin bệnh nhân");
                }
                $.get("cmu_list_CMU_HSBA_GETDEF?url="+convertArray([singletonObject.dvtt, sovaovien, rowData.SOVAOVIEN_DT_NOI])).done(function(dataDef) {
                    if(dataDef.length > 0) {
                        thongtinhsba.thongtinbn.infoDienTim.CANNANG = dataDef[0].CANNANG;
                        thongtinhsba.thongtinbn.infoDienTim.CHIEUCAO = dataDef[0].CHIEUCAO;
                    }
                })
                thongtinhsba.thongtinbn.infoDienTim = dataInfo[0]
                hinhAnhDT();
                rowData = {...rowData, ...dataInfo[0]};
                var convertedObj = convertNumericToString(rowData);
                genFormKetQua(convertedObj);
                matoathuoc = rowData.NOITRU == '1' ? (ret.SO_PHIEU.split('.')[2]).split('_')[0] : ret.MA_KHAM_BENH.replace("kb_", "tt_");
                setTimeout(function() {
                    var dataSub = formKetQuaDienTim.submission.data;
                    // Object.keys(rowData).forEach(function (value) {
                    //     $("#formPhieucbtienphau [data-name='" + value + "']").text(rowData[value])
                    // })
                    formKetQuaDienTim.submission = {
                        data: {
                            ...dataSub,
                            MAU_DIEN_TIM: rowData.MA_MAUSIEUAM,
                            MABENHLY_TRUOCCDHA: rowData.MABENHLY_TRUOCCDHA,
                            MABENHLY_SAUCDHA: rowData.MABENHLY_SAUCDHA,
                            CHANDOAN_TRUOCCDHA: rowData.CHANDOAN_TRUOCCDHA,
                            CHANDOAN_SAUCDHA: rowData.CHANDOAN_SAUCDHA,
                            MA_BENH_LY_THEO_ICD: rowData.MA_BENH_LY_THEO_ICD,
                            NGAY_TH_YL: rowData.DA_CHAN_DOAN == 1 ? ((rowData.NGAY_TH_YL ? moment(rowData.NGAY_TH_YL, ['DD/MM/YYYY HH:mm']): moment()).toISOString()) : moment().toISOString(),
                            NGAY_GIO_TH: (rowData.NGAY_GIO_TH? moment(rowData.NGAY_GIO_TH, ['DD/MM/YYYY HH:mm']): moment()).toISOString(),
                        }
                    }
                    rowData.NGAY_TH_YL = rowData.DA_CHAN_DOAN == 1 ? ((rowData.NGAY_TH_YL ? moment(rowData.NGAY_TH_YL, ['DD/MM/YYYY HH:mm']): moment()).toISOString()) : moment().toISOString()
                    rowData.NGAY_GIO_TH = (rowData.NGAY_GIO_TH? moment(rowData.NGAY_GIO_TH, ['DD/MM/YYYY HH:mm']): moment()).toISOString()
                    luuLogOld = rowData

                    if (rowData.KEYSIGN){
                        $(".shownutanh_dt").hide()
                        showOrHideByClass("hsba_tab1","chuakyshow","kyso")
                    } else {
                        $(".shownutanh_dt").show()
                        showOrHideByClass("hsba_tab1","kyso","chuakyshow")
                    }
                }, 500)
            })

            $.post('cmu_post_CMU_TONGTIEN_MONEY_NOIDUNG', {
                url: [singletonObject.dvtt,  ret.SOVAOVIEN == 0? ret.SOVAOVIEN_NOI: ret.SOVAOVIEN, ret.SOVAOVIEN_DT_NOI? ret.SOVAOVIEN_DT_NOI: 0, ret.SO_PHIEU].join('```')
            }).done(function (data) {
                if(data == 0) {
                    $("#vnptmoney").hide();
                } else {
                    $("#vnptmoney").show();
                    $("#vnptmoney").html("Đã thanh toán qua VNPT-MONEY: "+ data);
                }
            })
            $.post('cmu_post_CMU_TONGTIEN_BANK_NOIDUNG', {
                url: [singletonObject.dvtt,
                    ret.SOVAOVIEN == 0? ret.SOVAOVIEN_NOI: ret.SOVAOVIEN, ret.SOVAOVIEN_DT_NOI? ret.SOVAOVIEN_DT_NOI: 0, ret.SO_PHIEU, 'BIDV'].join('```')
            }).done(function (data) {
                if(data == 0) {
                    $("#bidv").hide();
                } else {
                    $("#bidv").show();
                    $("#bidv").html("Đã thanh toán qua BIDV: "+ new Intl.NumberFormat().format(data));
                }
            })
            $.post('cmu_post_CMU_TONGTIEN_BANK_NOIDUNG', {
                url: [singletonObject.dvtt,
                    ret.SOVAOVIEN == 0? ret.SOVAOVIEN_NOI: ret.SOVAOVIEN, ret.SOVAOVIEN_DT_NOI? ret.SOVAOVIEN_DT_NOI: 0, ret.SO_PHIEU, 'VIETINBANK'].join('```')
            }).done(function (data) {
                if(data == 0) {
                    $("#vietinbank").hide();
                } else {
                    $("#vietinbank").show();
                    $("#vietinbank").html("Đã thanh toán qua VIETINBANK: "+ new Intl.NumberFormat().format(data));
                }
            })
        } catch (error) {
            hideLoaderIntoWrapId("hsba_tabs")
        }
    }

    function hienthi_them_cls(url) {
        $.ajax({
            url: url
        }).done(function (data) {
            if (data.length > 0) {
                // STG
                if (data[0].NGAY_THUC_HIEN == null) {
                    $("#ngaythuchien_cls").val(singletonObject.ngayhientai);
                } else {
                    $("#ngaythuchien_cls").val(data[0].NGAY_THUC_HIEN);
                }

                if (data[0].GIO_TRA_KETQUA == null || !$("#dathuchien").prop('checked')) {
                    if (!giothuchien_cls_timer_is_on) {
                        if(tatAutoTime == 1) {
                            var ngayHienTai = new Date();
                            var gioHienTai = addZero(ngayHienTai.getHours());
                            var phutHienTai = addZero(ngayHienTai.getMinutes());
                            var giayHienTai = addZero(ngayHienTai.getSeconds());
                            $('#giothuchien_cls').val(gioHienTai + ":" + phutHienTai + ":" + giayHienTai);
                        } else
                            showtime_giothuchien_cls();
                    }
                } else {
                    stopGioThucHienClsTimer();
                    $("#giothuchien_cls").val(data[0].GIO_TRA_KETQUA);
                }
                // STG
                if (data[0].TT_THANHTOAN == "0")
                    $('#tt_thanhtoan').val("Chưa thanh toán");
                else
                    $('#tt_thanhtoan').val("Đã thanh toán");
            } else {
                $('#trieuchungls').val("");
                $('#benhtheobs').val("");
                $('#ngaythuchien_cls').val("");
                $('#tt_thanhtoan').val("");
            }
        });
    }

    function genFormKetQua(objData, dataSelected = false) {
        var jsonForm = getJSONObjectForm([
            // {
            //     label: "xn",
            //     key: "xn",
            //     columns: [
            //         {
            //             "components": [
            //                 {
            //                     "label": "Họ tên",
            //                     "key": "TENBENHNHAN",
            //                     "type": "textfield",
            //                     "customClass": "pr-2",
            //                     others: {
            //                         "disabled": true,
            //                     }
            //                 },
            //             ],
            //             "width": 2,
            //             "size": "md",
            //         },
            //         {
            //             "components": [
            //                 {
            //                     "label": "Số vào viện",
            //                     "key": "SOVAOVIEN",
            //                     "type": "textfield",
            //                     "customClass": "pr-2",
            //                     others: {
            //                         "disabled": true,
            //                     }
            //                 },
            //             ],
            //             "width": 2,
            //             "size": "md",
            //         },
            //         {
            //             "components": [
            //                 {
            //                     "label": "Tuổi",
            //                     "key": "TUOI",
            //                     "type": "textfield",
            //                     "customClass": "pr-2",
            //                     others: {
            //                         "disabled": true,
            //                     }
            //                 },
            //             ],
            //             "width": 1,
            //             "size": "md",
            //         },
            //         {
            //             "components": [
            //                 {
            //                     "label": "Giới tính",
            //                     "key": "GIOITINH_HT",
            //                     "type": "textfield",
            //                     "customClass": "pr-2",
            //                     others: {
            //                         "disabled": true,
            //                     },
            //                 },
            //             ],
            //             "width": 1,
            //             "size": "md",
            //         },
            //         {
            //             "components": [
            //                 {
            //                     "label": "Mã y tế",
            //                     "key": "MABENHNHAN",
            //                     "type": "number",
            //                     "customClass": "pr-2",
            //                     others: {
            //                         "disabled": true,
            //                     }
            //                 },
            //             ],
            //             "width": 2,
            //             "size": "md",
            //         },
            //         {
            //             "components": [
            //                 {
            //                     "label": "BHYT",
            //                     "customClass": "pr-2",
            //                     "key": "SOTHEBHYT",
            //                     "type": "textfield",
            //                     others: {
            //                         "disabled": true,
            //                     }
            //                 }
            //             ],
            //             "width": 2,
            //             "size": "md",
            //         },
            //         {
            //             "components": [
            //                 {
            //                     "label": "Khoa",
            //                     "customClass": "pr-2",
            //                     "key": "TENKHOA",
            //                     "type": "textfield",
            //                     others: {
            //                         "disabled": true,
            //                     }
            //                 }
            //             ],
            //             "width": 2,
            //             "size": "md",
            //         },
            //     ],
            //     "customClass": "ml-0 mr-0",
            //     "type": "columns",
            // },
            // {
            //     label: "xn",
            //     key: "xn",
            //     columns: [
            //         {
            //             "components": [
            //                 {
            //                     "label": "Cân nặng (kg)",
            //                     "key": "CANNANG",
            //                     "type": "textfield",
            //                     "customClass": "pr-2",
            //                     others: {
            //                         "disabled": true,
            //                     }
            //                 },
            //             ],
            //             "width": 3,
            //             "size": "md",
            //         },
            //         {
            //             "components": [
            //                 {
            //                     "label": "Chiều cao (cm)",
            //                     "key": "CHIEUCAO",
            //                     "type": "textfield",
            //                     "customClass": "pr-2",
            //                     others: {
            //                         "disabled": true,
            //                     }
            //                 },
            //             ],
            //             "width": 3,
            //             "size": "md",
            //         },
            //         {
            //             "components": [
            //                 {
            //                     "label": "Buồng",
            //                     "key": "BUONG",
            //                     "type": "textfield",
            //                     "customClass": "pr-2",
            //                     others: {
            //                         "disabled": true,
            //                     }
            //                 },
            //             ],
            //             "width": 3,
            //             "size": "md",
            //         },
            //         {
            //             "components": [
            //                 {
            //                     "label": "Giường",
            //                     "key": "GIUONG",
            //                     "type": "textfield",
            //                     "customClass": "pr-2",
            //                     others: {
            //                         "disabled": true,
            //                     },
            //                 },
            //             ],
            //             "width": 3,
            //             "size": "md",
            //         },
            //     ],
            //     "customClass": "ml-0 mr-0",
            //     "type": "columns",
            // },
            // {
            //     label: "xn",
            //     key: "xn",
            //     columns: [
            //         {
            //             "components": [
            //                 {
            //                     "label": "Địa chỉ",
            //                     "key": "DIACHI",
            //                     "type": "textfield",
            //                     "customClass": "pr-2",
            //                     others: {
            //                         "disabled": true,
            //                     }
            //                 },
            //             ],
            //             "width": 4,
            //             "size": "md",
            //         },
            //         {
            //             "components": [
            //                 {
            //                     "label": "Chẩn đoán",
            //                     "key": "CHUANDOANICD",
            //                     "type": "textfield",
            //                     "customClass": "pr-2",
            //                     others: {
            //                         "disabled": true,
            //                     }
            //                 },
            //             ],
            //             "width": 4,
            //             "size": "md",
            //         },
            //         {
            //             "components": [
            //                 {
            //                     "label": "Ngày giờ chỉ định",
            //                     "key": "NGAY_CHI_DINH",
            //                     "type": "textfield",
            //                     "customClass": "pr-2",
            //                     others: {
            //                         "disabled": true,
            //                     }
            //                 },
            //             ],
            //             "width": 4,
            //             "size": "md",
            //         },
            //     ],
            //     "customClass": "ml-0 mr-0",
            //     "type": "columns",
            // },
            {
                label: "xn",
                key: "xn",
                columns: [
                    {
                        "components": [
                            {
                                "label": "Bác sĩ điều trị",
                                "customClass": "pr-2",
                                "key": "NGUOI_CHI_DINH",
                                "type": "select",
                                others: {
                                    "data": {
                                        "values": singletonObject.danhsachtatcanhanvien
                                    },
                                    "disabled": true,
                                }
                            }
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Bác sĩ chuyên khoa",
                                "customClass": "pr-2",
                                "key": "NGUOI_THUC_HIEN_CK",
                                "type": "select",
                                others: {
                                    "data": {
                                        "values": singletonObject.danhsachtatcanhanvien
                                    },
                                    "defaultValue": singletonObject.userId,
                                }
                            }
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Thời gian thực hiện y lệnh",
                                "customClass": "pr-2",
                                "key": "NGAY_TH_YL",
                                "type": "datetime",
                                enableTime: true,
                                others: {
                                    "disabled": objData.KEYSIGN ? true : dataSelected,
                                },
                            }
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Thời gian kết quả",
                                "customClass": "pr-2",
                                "key": "NGAY_GIO_TH",
                                "type": "datetime",
                                enableTime: true,
                                others: {
                                    "disabled": objData.KEYSIGN ? true : dataSelected,
                                },
                            }
                        ],
                        "width": 3,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "xn",
                key: "xn",
                columns: [
                    {
                        "components": [
                            {
                                label: "",
                                key: "wrap_benhchinh",
                                columns: [
                                    {
                                        "components": [
                                            {
                                                "tag": "label",
                                                "content": "Chẩn đoán CLS",
                                                "refreshOnChange": false,
                                                "key": "htmllabel_benhchinh",
                                                "type": "htmlelement",
                                            },
                                        ],
                                        "width": 12,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "",
                                                "key": "MA_ICD",
                                                "type": "textfield",
                                                customClass: "pr-2",
                                                others: {
                                                    "placeholder": "ICD",
                                                    "disabled": objData.KEYSIGN ? true : dataSelected,
                                                }
                                            },
                                        ],
                                        "width": 2,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "",
                                                "key": "TEN_ICD",
                                                "type": "textfield",
                                                customClass: "pr-2",
                                                others: {
                                                    "placeholder": "Tên bệnh chính",
                                                    "disabled": objData.KEYSIGN ? true : dataSelected,
                                                },
                                            },
                                        ],
                                        "width": 10,
                                        "size": "md",
                                    },

                                ],
                                "customClass": "ml-0 mr-0 mt-1",
                                "type": "columns",
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                    // {
                    //     "components": [
                    //         {
                    //             "label": "Chẩn đoán CLS",
                    //             "customClass": "pr-2",
                    //             "key": "MA_ICD",
                    //             "type": "textfield",
                    //             others: {
                    //                 "disabled": objData.KEYSIGN ? true : dataSelected,
                    //             },
                    //         }
                    //     ],
                    //     "width": 1,
                    //     "size": "md",
                    // },
                    // {
                    //     "components": [
                    //         {
                    //             "label": "",
                    //             "customClass": "pr-2",
                    //             "key": "TEN_ICD",
                    //             "type": "textfield",
                    //             others: {
                    //                 "disabled": objData.KEYSIGN ? true : dataSelected,
                    //             },
                    //         }
                    //     ],
                    //     "width": 2,
                    //     "size": "md",
                    // },
                    {
                        "components": [
                            {
                                label: "",
                                key: "wrap_benhchinh",
                                columns: [
                                    {
                                        "components": [
                                            {
                                                "tag": "label",
                                                "content": "Chọn máy",
                                                "refreshOnChange": false,
                                                "key": "htmllabel_benhchinh",
                                                "type": "htmlelement",
                                            },
                                        ],
                                        "width": 12,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "",
                                                "customClass": "pr-2",
                                                "key": "STT_MAYCDHA",
                                                "type": "select",
                                                others: {
                                                    "data": {
                                                        "values": singletonObject.danhsachmaycdhaFormio
                                                    },
                                                    "disabled": objData.KEYSIGN ? true : dataSelected,
                                                },
                                            }
                                        ],
                                        "width": 10,
                                        "size": "md",
                                    },
                                    {
                                        "components": [
                                            {
                                                "label": "Lưu máy",
                                                "customClass": "w-100 custom-bottom pr-2",
                                                "key": "copyclstdt",
                                                "type": "button",
                                                others: {
                                                    "leftIcon": "fa fa-save",
                                                    "action": "event",
                                                    "theme": "primary",
                                                    "showValidations": false,
                                                    "event": "luumayth",
                                                    "type": "button",
                                                    "disabled": objData.KEYSIGN ? true : dataSelected,
                                                }
                                            },
                                        ],
                                        "width": 2,
                                        "size": "md",
                                    },

                                ],
                                "customClass": "ml-0 mr-0 mt-1",
                                "type": "columns",
                            },
                        ],
                        "width": 6,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                label: "xn",
                key: "xn",
                columns: [
                    {
                        "components": [
                            {
                                "label": "Người đọc kết quả",
                                "customClass": "pr-2",
                                "key": "NGUOI_THUC_HIEN",
                                "type": "select",
                                others: {
                                    "data": {
                                        "values": singletonObject.danhsachtatcanhanvien
                                    },
                                    "defaultValue": singletonObject.userId,
                                    "disabled": objData.KEYSIGN ? true : dataSelected,
                                },
                            }
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Kỹ thuật viên",
                                "customClass": "pr-2",
                                "key": "KYTHUATVIEN",
                                "type": "select",
                                others: {
                                    "data": {
                                        "values": singletonObject.danhsachnhanvienkhoaFormio
                                    },
                                    "disabled": objData.KEYSIGN ? true : dataSelected,
                                },
                            }
                        ],
                        "width": 3,
                        "size": "md",
                    },
                    {
                        "components": [
                            {
                                "label": "Mẫu điện tim",
                                "customClass": "pr-2",
                                "key": "MAU_DIEN_TIM",
                                "type": "select",
                                others: {
                                    "data": {
                                        "values": singletonObject.danhsachmaudtFormio
                                    },
                                    "disabled": objData.KEYSIGN ? true : dataSelected,
                                },
                            }
                        ],
                        "width": 6,
                        "size": "md",
                    },
                ],
                "customClass": "ml-0 mr-0",
                "type": "columns",
            },
            {
                "label": "Kết quả",
                "customClass": "pr-2",
                "key": "KET_QUA",
                "type": "textarea",
                others: {
                    "disabled": objData.KEYSIGN ? true : dataSelected,
                },
            },
            {
                "label": "Kết luận",
                "customClass": "pr-2",
                "key": "MO_TA",
                "type": "textarea",
                others: {
                    "disabled": objData.KEYSIGN ? true : dataSelected,
                },
            },
            {
                "label": "Lời dặn bác sĩ",
                "customClass": "pr-2",
                "key": "LOIDANBACSI",
                "type": "textarea",
                others: {
                    "disabled": objData.KEYSIGN ? true : dataSelected,
                },
            },
        ])
        Formio.createForm(document.getElementById('formKetQua'),
            jsonForm,
            {},
        ).then(function(form) {
            formKetQuaDienTim = form;
            var tenBenhchinhElement = form.getComponent('TEN_ICD');
            var icdBenhchinhElement = form.getComponent('MA_ICD');
            setTimeout(() => {
                $('textarea[name="data[KET_QUA]"]').ckeditor();

                // Thêm dữ liệu mẫu nếu chưa có
                addSampleSuggestions();

                // Thêm autocomplete cho trường "Lời dặn bác sĩ"
                setupLoiDanBacSiAutocomplete(form);
            }, 500);
            $("#"+getIdElmentFormio(form,'MA_ICD')).on('keypress', function(event) {
                var mabenhICD = $(this).val();
                if(event.keyCode == 13 && mabenhICD != "") {
                    mabenhICD = mabenhICD.toUpperCase();
                    getMotabenhly(mabenhICD, function(data) {
                        var splitIcd = data.split("!!!")
                        tenBenhchinhElement.setValue(splitIcd[1]);
                        icdBenhchinhElement.setValue(mabenhICD)
                    })
                }
            })
            combgridTenICD(getIdElmentFormio(form,'TEN_ICD'), function(item) {
                icdBenhchinhElement.setValue(item.ICD);
                tenBenhchinhElement.setValue(item.MO_TA_BENH_LY);
            });
            form.on('luumayth', function(click) {
                var retbn = getThongtinRowSelected("list_benhnhan");
                var sophieu = retbn.SO_PHIEU;
                var noitru = retbn.NOITRU;
                var makhambenh = retbn.MA_KHAM_BENH;
                var sttbenhan = retbn.STT_BENHAN;
                var sttdotdieutri = retbn.STT_DOTDIEUTRI;
                var sttdieutri = retbn.STT_DIEUTRI;
                var macdha = retbn.MA_CDHA;
                if (sophieu != "" && macdha != "" && (retbn.SOVAOVIEN != "" || retbn.SOVAOVIEN_NOI != "")) {
                    $.post("luu_maycdha_vaobangchitiet", {
                        sophieu: sophieu, macdha: macdha, noitru: noitru,
                        sttbenhan: sttbenhan, sttdotdieutri: sttdotdieutri, sttdieutri: sttdieutri,
                        makhambenh: makhambenh, sovaovien: retbn.SOVAOVIEN,
                        sovaovien_noi: retbn.SOVAOVIEN_NOI,
                        sovaovien_dt_noi: retbn.SOVAOVIEN_DT_NOI,
                        stt_may_cdha: formKetQuaDienTim.submission.data.STT_MAYCDHA
                    }).done(function (data) {
                        if (data == "1") {
                            notifiToClient("Green", "Cập nhật thành công");
                        } else {
                            notifiToClient("Red", "Vui lòng kiểm tra lại thông tin");
                        }
                    }).fail(function () {
                        notifiToClient("Red", "Vui lòng thử lại");
                    });
                }
            });
            var initializedFields = {};
            form.on('change', function(event) {
                if (!event.changed && objData.MA_MAUSIEUAM) {
                    return;
                }
                let fieldKey = event.changed.component.key
                if (!initializedFields[fieldKey] && objData.MA_MAUSIEUAM) {
                    initializedFields[fieldKey] = true;
                    return;
                }
                if (event.changed && event.changed.component.key === 'MAU_DIEN_TIM') {
                    var id = formKetQuaDienTim.submission.data.MAU_DIEN_TIM;
                    if (id !== "0") {
                        var url = "select_maudientim_theoma?ma=" + id + "&dvtt=" + singletonObject.dvtt;
                        $.ajax({
                            url: url
                        }).done(function (data) {
                            let contentKey = Object.keys(CKEDITOR.instances).find(key => key.includes("KET_QUA"));
                            if (contentKey) {
                                CKEDITOR.instances[contentKey].setData(data[0]["NOIDUNG"])
                                form.submission = {
                                    data: {
                                        ...formKetQuaDienTim.submission.data,
                                        MO_TA: data[0]["KET_LUAN"]
                                    },
                                }
                            }
                        });
                    }
                }
            });

            // const key = 'loidanHistory';
            //
            // const loidanComp = form.getComponent('LOIDANBACSI');
            //
            // loidanComp.on('focus', () => {
            //     const history = JSON.parse(localStorage.getItem(key) || '[]');
            //     console.log('Gợi ý hiển thị:', history);
            //
            //     // TODO: chỗ này bạn có thể hiển thị dropdown gợi ý tùy chỉnh
            // });
            //
            // loidanComp.on('blur', () => {
            //     const value = loidanComp.getValue();
            //     if (value) {
            //         let history = JSON.parse(localStorage.getItem(key) || '[]');
            //         if (!history.includes(value)) {
            //             history.push(value);
            //             localStorage.setItem(key, JSON.stringify(history));
            //         }
            //     }
            // });

            objData.SOVAOVIEN = objData.SOVAOVIEN == 0 ? objData.SOVAOVIEN_NOI : objData.SOVAOVIEN
            objData.NGUOI_THUC_HIEN_CK = objData.NGUOI_THUC_HIEN
            form.submission = {
                data: objData,
            }
            $(".dt-thongtin").each(function () {
                let key = $(this).data("key");
                if (objData[key]) {
                    $(this).text(objData[key]);
                }
            });
            luuLogOld = objData;
            $("#hsba_tabs").tabs("enable", 1);
            $("#hsba_tab1_header").click();
            hideLoaderIntoWrapId("hsba_tabs");
        });
    }

    function convertNumericToString(obj) {
        const newObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                if (typeof obj[key] === 'number') {
                    newObj[key] = obj[key].toString();
                } else {
                    newObj[key] = obj[key];
                }
            }
        }
        return newObj;
    }

    function luuNguoiDocKetQua(_soPhieu, _soVaoVien, _soVaoVienDt = 0, _noiTru = 0, _listMaCls = "-1") {
        $.post("cmu_post", {
            url: [singletonObject.userId, singletonObject.dvtt,
                _soPhieu,
                _soVaoVien,
                _soVaoVienDt,
                _noiTru,
                singletonObject.makhoa,
                formKetQuaDienTim.submission.data.NGUOI_THUC_HIEN,
                _listMaCls,
                'CDHA',
                'UPD_CLS_NGUOI_DOC_KET_QUA'].join("```")
        })
    }

    async function getLinkInPhieuKetQua() {
        return new Promise((resolve, reject) => {
            var retbn = getThongtinRowSelected("list_benhnhan");
            var dataForm = formKetQuaDienTim.submission.data
            getFilesign769(
                "PHIEUKQ_DIENTIM",
                retbn.SO_PHIEU,
                -1,
                singletonObject.dvtt,
                retbn.NOITRU == "0" ? retbn.SOVAOVIEN : retbn.SOVAOVIEN_NOI,
                retbn.SOVAOVIEN_DT_NOI ? retbn.SOVAOVIEN_DT_NOI : 0,
                -1,
                async function (dataKySo) {
                    if (dataKySo.length > 0) {
                        try{
                            var pdfData = await getCMUFileSigned769GetLink(dataKySo[0].KEYMINIO, 'pdf')
                            resolve(pdfData);
                        } catch (e){
                            console.log("Lỗi: " + e);
                            reject(e);
                        }
                    } else {
                        var selectedRowId = $("#list_benhnhan").jqGrid('getGridParam', 'selrow');
                        if (!selectedRowId) {
                            return notifiToClient("Red", "Vui lòng chọn bệnh nhân chẩn đoán");
                        }

                        var mabenhnhan = retbn.MABENHNHAN;
                        var hoten = retbn.TENBENHNHAN;
                        var diachi = retbn.DIACHI;
                        var tuoi = retbn.TUOI;
                        var phai = retbn.GIOITINH;
                        if (phai == "true") {
                            phai = "Nam";
                        } else {
                            phai = "Nữ";
                        }
                        var sophieu = retbn.SO_PHIEU;
                        var makhambenh = retbn.MA_KHAM_BENH;
                        var macdha = retbn.MA_CDHA;
                        var dvtt = singletonObject.dvtt;
                        var noitru = retbn.NOITRU;
                        var sttbenhan = retbn.STT_BENHAN;
                        var sttdotdieutri = retbn.STT_DOTDIEUTRI;
                        var sttdieutri = retbn.STT_DIEUTRI;
                        var cannang = thongtinhsba.thongtinbn.infoDienTim.CANNANG;
                        var chieucao = thongtinhsba.thongtinbn.infoDienTim.CHIEUCAO;
                        var khoa = retbn.TENKHOA;
                        var giuong = thongtinhsba.thongtinbn.infoDienTim.GIUONG;
                        var buong = thongtinhsba.thongtinbn.infoDienTim.BUONG;
                        var sothebaohiem = retbn.SOTHEBHYT;
                        var chandoan = retbn.CHUANDOANICD;
                        var yeucaukiemtra = "";
                        var bacsidieutri = getTextSelectedFormio(formKetQuaDienTim.getComponent('NGUOI_CHI_DINH'));
                        var bacsichuyenkhoa = getTextSelectedFormio(formKetQuaDienTim.getComponent('NGUOI_THUC_HIEN'));
                        var ngayth = moment(dataForm.NGAY_GIO_TH).format("DD/MM/YYYY")
                        var gioth = moment(dataForm.NGAY_GIO_TH).format("HH:mm:ss")
                        // var bacsichuyenkhoa = $("#bacsichuyenkhoa option:selected").text();
                        var solan = retbn.STT_HANGNGAY;
                        var tencdha = thongtinhsba.thongtinbn.infoDienTim.TEN_CDHA;
                        var motacdha = $("#dt_loaimauin").val();
                        var type = '2'
                        // var type = $("#loaiin").val();

                        if (sophieu != "" && macdha != "") {
                            let rowData = getThongtinRowSelected("list_benhnhan");
                            yeucaukiemtra = yeucaukiemtra ? yeucaukiemtra + ". " + rowData.TEN_CDHA : rowData.TEN_CDHA;

                            var arr = [mabenhnhan, hoten, diachi, tuoi, phai, makhambenh, sophieu, macdha,
                                dvtt, noitru, sttbenhan, sttdotdieutri, sttdieutri, cannang, chieucao,
                                khoa, giuong, buong, sothebaohiem, chandoan, yeucaukiemtra,
                                bacsidieutri, bacsichuyenkhoa, solan, tencdha, "0", motacdha, retbn.SOVAOVIEN
                                , retbn.SOVAOVIEN_NOI
                                , retbn.SOVAOVIEN_DT_NOI, 0, type, ngayth, gioth == "" ? "00:00:00" : gioth, "0"];
                            var url = "inketquadientim_svv?url=" + convertArray(arr);
                            resolve(url);
                        }
                        reject("Lỗi in phiếu");
                    }
                }
            )
        })
    }

    function getLastIDBn(){
        var rowId = $("#list_benhnhan").jqGrid('getGridParam', 'selrow');
        return rowId;
    }

    function inkysmartdientim() {
        var retbn = getThongtinRowSelected("list_benhnhan");
        var Sess_UserID = singletonObject.userId
        var sovaovien_t = retbn.SOVAOVIEN == 0 ? retbn.SOVAOVIEN_NOI: retbn.SOVAOVIEN;
        dvtt = singletonObject.dvtt;
        var mabn = retbn.MABENHNHAN;
        $.get("cmu_getlist?url="+convertArray([dvtt, sovaovien_t, 0,'PHIEUDIENTIM', retbn.SO_PHIEU,'CMU_SMARTCA_INCLS_NGOAI']))
            .done(function(data){

                if (data.length > 0 && data[0].TRANID_KHOA != null) {
                    $.ajax({
                        type: "POST",
                        url: "cmu-smartca-get-information",
                        dataType: "json",
                        contentType: 'application/json',
                        data: JSON.stringify({
                            tranId: data[0].TRANID_KHOA,
                            userId: userId,
                        }),
                        success: function (resfile) {
                            console.log("data", resfile)
                            var pdf = 'data:application/pdf;base64,' + resfile.DATA.content.documents[0].dataSigned;
                            var link = document.createElement('a');
                            link.href = pdf;
                            var t = new Date();

                            link.download=   resfile.DATA.content.tranCode+ ".pdf";
                            link.click();
                        },
                        error: function () {
                        }
                    });
                    clearInterval(i);
                } else {
                    notifiToClient("Red", "Phiếu chưa được ký");
                }
            })
    }

    function doSignPlugin(url, typeSign) {
        var x = new XMLHttpRequest();
        x.onload = function() {
            // Create a form
            var reader = new FileReader();
            reader.readAsDataURL(x.response);
            reader.onloadend = function() {

                var base64data = reader.result.replace("data:application/pdf;base64,", "");
                console.log("base64data", base64data)
                var sigOptions = null;
                sigOptions = new PdfSigner();
                sigOptions.AdvancedCustom = true;
                SignAdvanced(base64data, 'pdf', sigOptions, typeSign);
            }

        };
        x.responseType = 'blob';    // <-- This is necessary!
        x.open('GET', url, true);
        x.send();
    }
    function SignAdvanced(data, type, sigOption, typeSign) {
        var dataJS = {};
        var arrData = [];
        // 1
        dataJS.data = data;
        dataJS.type = type;
        dataJS.sigOptions = JSON.stringify(sigOption);
        var jsData = "";
        jsData += JSON.stringify(dataJS);
        //
        arrData.push(jsData);
        var serial = "";
        vnpt_plugin.signArrDataAdvanced(arrData, serial, true, showMessageCDHAKQ);
    }

    function inKyToken() {
        var retbn = getThongtinRowSelected("list_benhnhan");
        $.get("cmu_getlist?url="+convertArray([singletonObject.dvtt, retbn.SOVAOVIEN, retbn.SO_PHIEU+"_"+ retbn.MA_CDHA +"_KQ",'CMU_DOWNLOAD_CLS_TOKEN']))
            .done(function(data){
                if (data.length > 0 ) {
                    var pdf = 'data:application/pdf;base64,' + data[0].FILE_SIGNED;
                    var link = document.createElement('a');
                    link.href = pdf;
                    var t = new Date();

                    link.download=   retbn.SOVAOVIEN+".pdf";
                    link.click();
                } else {
                    notifiToClient("Red", "Phiếu chỉ định chưa được kí số")
                }
            })
    }

    // Hàm thiết lập autocomplete cho trường "Lời dặn bác sĩ"
    function setupLoiDanBacSiAutocomplete(form) {
        console.log('🔧 Bắt đầu thiết lập autocomplete cho LOIDANBACSI');

        try {
            // Lấy element của trường LOIDANBACSI
            var loidanElement = form.getComponent('LOIDANBACSI');
            console.log('📋 Component LOIDANBACSI:', loidanElement);

            if (!loidanElement) {
                console.warn('⚠️ Không tìm thấy component LOIDANBACSI');
                return;
            }

            // Tạo datalist cho autocomplete
            var datalistId = LoiDanBacSiSuggestions.createDatalist();
            console.log('📝 Đã tạo datalist với ID:', datalistId);

            // Thử nhiều cách tìm textarea element
            var attempts = 0;
            var maxAttempts = 10;

            function findAndSetupTextarea() {
                attempts++;
                console.log('🔍 Lần thử', attempts, '- Tìm textarea...');

                // Thử nhiều selector khác nhau
                var selectors = [
                    'textarea[name="data[LOIDANBACSI]"]',
                    'textarea[data-key="LOIDANBACSI"]',
                    '#formKetQua textarea[placeholder*="Lời dặn"]',
                    '#formKetQua .formio-component-LOIDANBACSI textarea',
                    '#formKetQua textarea:contains("LOIDANBACSI")',
                    'textarea'  // Fallback - tìm tất cả textarea
                ];

                var $textarea = null;
                var usedSelector = '';

                for (var i = 0; i < selectors.length; i++) {
                    $textarea = $(selectors[i]);
                    if ($textarea.length > 0) {
                        usedSelector = selectors[i];
                        console.log('✅ Tìm thấy textarea với selector:', usedSelector, '- Số lượng:', $textarea.length);
                        break;
                    }
                }

                // Nếu tìm thấy nhiều textarea, tìm cái đúng
                if ($textarea && $textarea.length > 1) {
                    console.log('🔍 Có nhiều textarea, đang tìm cái đúng...');
                    $textarea.each(function(index, element) {
                        var $el = $(element);
                        var name = $el.attr('name');
                        var placeholder = $el.attr('placeholder');
                        var parentClass = $el.parent().attr('class');

                        console.log('Textarea', index, ':', {
                            name: name,
                            placeholder: placeholder,
                            parentClass: parentClass
                        });

                        // Tìm textarea có liên quan đến LOIDANBACSI
                        if (name && name.includes('LOIDANBACSI')) {
                            $textarea = $el;
                            console.log('✅ Chọn textarea có name chứa LOIDANBACSI');
                            return false; // break
                        }
                    });
                }

                if ($textarea && $textarea.length > 0) {
                    console.log('🎯 Đang thiết lập autocomplete cho textarea...');

                    // Thêm thuộc tính list để kết nối với datalist
                    $textarea.attr('list', datalistId);

                    // Thêm placeholder gợi ý
                    var currentPlaceholder = $textarea.attr('placeholder') || '';
                    if (!currentPlaceholder.includes('gợi ý')) {
                        $textarea.attr('placeholder', 'Nhập lời dặn bác sĩ... (sẽ hiển thị gợi ý từ các lần nhập trước)');
                    }

                    // Thêm class để styling
                    $textarea.addClass('loidanbacsi-autocomplete');

                    // Xử lý sự kiện focus để cập nhật datalist
                    $textarea.off('focus.autocomplete').on('focus.autocomplete', function() {
                        console.log('👆 Focus vào textarea LOIDANBACSI');
                        LoiDanBacSiSuggestions.createDatalist();
                    });

                    // Xử lý sự kiện input để lọc gợi ý theo thời gian thực
                    $textarea.off('input.autocomplete').on('input.autocomplete', function() {
                        var currentValue = $(this).val().toLowerCase();
                        console.log('⌨️ Input changed:', currentValue);
                        if (currentValue.length >= 2) {
                            updateFilteredSuggestions(datalistId, currentValue);
                        }
                    });

                    // Thêm sự kiện click để hiển thị tất cả gợi ý
                    $textarea.off('click.autocomplete').on('click.autocomplete', function() {
                        console.log('👆 Click vào textarea LOIDANBACSI');
                        LoiDanBacSiSuggestions.createDatalist();
                        // Trigger focus để hiển thị dropdown
                        $(this).focus();
                    });

                    console.log('✅ Đã thiết lập autocomplete thành công cho trường Lời dặn bác sĩ');
                    console.log('📊 Số gợi ý hiện có:', LoiDanBacSiSuggestions.getSuggestions().length);

                    return true; // Thành công
                } else {
                    console.warn('⚠️ Không tìm thấy textarea LOIDANBACSI - Lần thử:', attempts);

                    if (attempts < maxAttempts) {
                        setTimeout(findAndSetupTextarea, 500); // Thử lại sau 500ms
                    } else {
                        console.error('❌ Đã thử', maxAttempts, 'lần nhưng không tìm thấy textarea LOIDANBACSI');
                        // Debug: In ra tất cả textarea có trong form
                        console.log('🔍 Debug - Tất cả textarea trong #formKetQua:');
                        $('#formKetQua textarea').each(function(i, el) {
                            console.log('Textarea', i, ':', {
                                name: $(el).attr('name'),
                                id: $(el).attr('id'),
                                class: $(el).attr('class'),
                                placeholder: $(el).attr('placeholder')
                            });
                        });
                    }
                    return false;
                }
            }

            // Bắt đầu tìm textarea
            setTimeout(findAndSetupTextarea, 1000);

        } catch (error) {
            console.error('❌ Lỗi khi thiết lập autocomplete:', error);
        }
    }

    // Hàm cập nhật gợi ý được lọc
    function updateFilteredSuggestions(datalistId, searchText) {
        console.log('🔍 Lọc gợi ý với từ khóa:', searchText);

        var suggestions = LoiDanBacSiSuggestions.getSuggestions();
        var datalist = document.getElementById(datalistId);

        if (!datalist) {
            console.warn('⚠️ Không tìm thấy datalist với ID:', datalistId);
            return;
        }

        // Xóa các option cũ
        datalist.innerHTML = '';

        // Lọc và thêm các gợi ý phù hợp
        var filteredSuggestions = suggestions.filter(function(suggestion) {
            return suggestion.text.toLowerCase().indexOf(searchText) !== -1;
        });

        console.log('📋 Tìm thấy', filteredSuggestions.length, 'gợi ý phù hợp');

        // Giới hạn số lượng gợi ý hiển thị
        filteredSuggestions.slice(0, 10).forEach(function(suggestion) {
            var option = document.createElement('option');
            option.value = suggestion.text;
            datalist.appendChild(option);
        });
    }

    // Hàm thêm dữ liệu mẫu để test (chỉ dùng khi chưa có dữ liệu)
    function addSampleSuggestions() {
        var currentSuggestions = LoiDanBacSiSuggestions.getSuggestions();

        if (currentSuggestions.length === 0) {
            console.log('📝 Thêm dữ liệu mẫu cho autocomplete...');

            var sampleSuggestions = [
                'Tái khám sau 1 tuần',
                'Uống thuốc đúng giờ, đủ liều theo chỉ định',
                'Nghỉ ngơi, tránh căng thẳng và stress',
                'Ăn nhạt, hạn chế muối và đồ chiên rán',
                'Tập thể dục nhẹ nhàng, đi bộ 30 phút/ngày',
                'Theo dõi huyết áp hàng ngày',
                'Đến viện ngay nếu có triệu chứng bất thường',
                'Kiêng rượu bia và thuốc lá',
                'Uống đủ nước, ít nhất 2 lít/ngày',
                'Ngủ đủ giấc 7-8 tiếng/ngày'
            ];

            sampleSuggestions.forEach(function(suggestion) {
                LoiDanBacSiSuggestions.addSuggestion(suggestion);
            });

            console.log('✅ Đã thêm', sampleSuggestions.length, 'gợi ý mẫu');
        }
    }

    // Hàm debug để kiểm tra tính năng autocomplete
    function debugAutocomplete() {
        console.log('🔧 === DEBUG AUTOCOMPLETE LOIDANBACSI ===');
        console.log('📊 Số gợi ý hiện có:', LoiDanBacSiSuggestions.getSuggestions().length);
        console.log('📋 Danh sách gợi ý:', LoiDanBacSiSuggestions.getSuggestions());

        // Kiểm tra datalist
        var datalist = document.getElementById('loidanbacsi-suggestions');
        console.log('📝 Datalist element:', datalist);
        if (datalist) {
            console.log('📝 Số options trong datalist:', datalist.children.length);
        }

        // Kiểm tra textarea
        var textareas = $('textarea[name="data[LOIDANBACSI]"]');
        console.log('📝 Textarea LOIDANBACSI:', textareas.length, textareas);

        if (textareas.length > 0) {
            var textarea = textareas.first();
            console.log('📝 Textarea attributes:', {
                list: textarea.attr('list'),
                placeholder: textarea.attr('placeholder'),
                class: textarea.attr('class')
            });
        }

        // Kiểm tra tất cả textarea trong form
        var allTextareas = $('#formKetQua textarea');
        console.log('📝 Tất cả textarea trong form:', allTextareas.length);
        allTextareas.each(function(i, el) {
            console.log('Textarea', i, ':', {
                name: $(el).attr('name'),
                placeholder: $(el).attr('placeholder'),
                list: $(el).attr('list')
            });
        });

        console.log('🔧 === KẾT THÚC DEBUG ===');
    }

    // Thêm hàm debug vào window để có thể gọi từ console
    window.debugAutocomplete = debugAutocomplete;
    window.LoiDanBacSiSuggestions = LoiDanBacSiSuggestions;

    console.log('🚀 Tính năng autocomplete đã được tải');
    console.log('💡 Sử dụng debugAutocomplete() trong console để kiểm tra');
})