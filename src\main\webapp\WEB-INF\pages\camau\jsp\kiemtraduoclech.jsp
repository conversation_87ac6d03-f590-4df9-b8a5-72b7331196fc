<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form"%>
<%@page contentType="text/html" pageEncoding="UTF-8"  %>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
  <meta name="google-site-verification" content="u6uNEfD4cb3gidezi4r_6aI8Wb1E07-ufBeCQpvmlqQ" />
  <title>Hệ thống chăm sóc sức khỏe</title>
  <link rel="icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>
  <link rel="shortcut icon" href="<c:url value="/resources/images/favicon.ico" />" type="image/x-icon"/>

  <!-- jQuery file -->

  <link href="<c:url value="/resources/css/divheader.css" />" rel="stylesheet"/>
  <link href="<c:url value="/resources/css/style_new.css" />" rel="stylesheet"/>

  <!--Jquery-->
  <link rel="stylesheet" href="<c:url value="/resources/css/jquery-ui-redmond.1.9.1.css" />" />
  <script src="<c:url value="/resources/js/jquery.min.1.8.3.js" />"></script>
  <script src="<c:url value="/resources/js/jquery-ui.1.9.1.js" />"></script>


  <%--<script src="<c:url value="/resources/js/jquery.jqGrid.src.multicol.js" />"></script>--%>

  <!--Grid-->
  <!--        <link href="<c:url value="/resources/jqgrid/css/ui.jqgrid.css" />" rel="stylesheet"/>
        <script src="<c:url value="/resources/jqgrid/js/i18n/grid.locale-en.js" />"></script>
        <script src="<c:url value="/resources/jqgrid_5/src/jquery.jqGrid.js" />"></script>-->

  <link href="<c:url value="/resources/jqgrid_5/css/ui.jqgrid.css" />" rel="stylesheet"/>
  <script src="<c:url value="/resources/V3/jqgrid/js/jquery.jqGrid.js" />"></script>
  <script src="<c:url value="/resources/jqgrid/js/i18n/grid.locale-en.js" />"></script>


  <!-- This is the Javascript file of jqGrid -->
  <script src="<c:url value="/resources/js/common_function.js" />"></script>
  <script src="<c:url value="/resources/js/jquery.inputmask.bundle.min.js" />"></script>
  <script src="<c:url value="/resources/blockUI/jquery.blockUI.js" />"></script>
  <script src="<c:url value="/resources/dialog/jquery.alerts.js" />"></script>
  <link href="<c:url value="/resources/dialog/jquery.alerts.1.css" />" rel="stylesheet"/>
  <link href="<c:url value="/resources/combogrid/css/smoothness/jquery.ui.combogrid.css" />" rel="stylesheet"/>
  <script src="<c:url value="/resources/combogrid/plugin/jquery.ui.combogrid-1.6.3.js" />"></script>
  <script src="<c:url value="/resources/contextmenu/jquery.contextMenu.js" />"></script>
  <link href="<c:url value="/resources/contextmenu/jquery.contextMenu.css" />" rel="stylesheet"/>
  <script src="<c:url value="/resources/bootstrap/js/bootstrap.min.js" />"></script>
  <link href="<c:url value="/resources/bootstrap/css/bootstrap.css" />" rel="stylesheet"/>
  <link href="<c:url value="/resources/dialog/jBox.css" />" rel="stylesheet"/>
  <script src="<c:url value="/resources/dialog/jBox.js" />"></script>
  <script type="text/javascript" src="<c:url value="/resources/bootstrap/js/bootstrap-multiselect.js" />"></script>
  <link rel="stylesheet" href="<c:url value="/resources/bootstrap/css/bootstrap-multiselect.css" />"/>
  <script type="text/javascript" language="javascript" src="//cdnjs.cloudflare.com/ajax/libs/jszip/2.5.0/jszip.min.js"></script>
  <script type="text/javascript" language="javascript" src="//cdn.rawgit.com/bpampuch/pdfmake/0.1.26/build/pdfmake.min.js"></script>
  <script type="text/javascript" language="javascript" src="//cdn.rawgit.com/bpampuch/pdfmake/0.1.26/build/vfs_fonts.js"></script>
  <script src="<c:url value="/resources/blockUI/jquery.blockUI.js" />"></script>
  <style type="text/css">
    .ui-jqgrid-view .ui-jqgrid .ui-jqgrid-htable th.ui-th-column-header {
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      padding-top: 1px;
      height: 19px
    }
    .ui-jqgrid .ui-jqgrid-view {
      z-index: 0 !important;
    }
  </style>

</head>
<body>
<div id="panel_all">
  <%@include file="../../../../resources/Theme/include_pages/menu.jsp"%>
  <div id="panelwrap">
    <div class="center_content">
      <div class="panel_with_title">
        <div class="panel_title">Kiểm tra dược</div>
        <a id='dwnldLnk' href="javascript:void(0)" download='o ficheirinho de tostas.pdf' style="display:none;" ></a>
        <div class="panel_body">
          <form action="" method="get">
            <table width="82%" border="0" align="center">
              <tr>
                <td width="100">Từ Ngày</td>
                <td width="150"><input type="text" name="tungay" id="tungay" data-inputmask="'alias': 'date'" style="width: 150px" autocomplete="off"/></td>
                <td width="100">Đến ngày</td>
                <td width="150"><input type="text" name="denngay" id="denngay" data-inputmask="'alias': 'date'" style="width: 150px" autocomplete="off"/></td>
                <td width="250">
                  <input type="button" name="xembc" id="xembc" value="Xem báo cáo" class="button_shadow" style="width:110px"/>
                  <input type="button" name="chinhdulieu" id="chinhdulieu" value="Chỉnh dữ liệu dược" class="button_shadow" style="width:140px"/>
                </td>
              </tr>
            </table>
          </form>
        </div>
      </div>
      <div id="tab_tabds">

        <ul >
          <li class="active"><a href="#tabcongkham">Danh sách dược/vật tư</a></li>
        </ul>
        <div id="tabcongkham" class="tabcontent">
          <div id="div_tabcongkham" ><table id="list_tabcongkham"></table></div>
        </div>
      </div> <!--end of center_content-->


    </div>
  </div>
</div>

</body>
<script>
  var dialog_mapthemdinhmuc = new jBox('Modal', {
    title: "Cập nhật bác sĩ",
    overlay: false,
    content: $('#dialog_mapthemdinhmuc'),
    position: {
      y: 'center'
    },
    draggable: 'title'
  });
  var dialog_bacsi= new jBox('Modal', {
    title: "Cập nhật bác sĩ thuốc",
    overlay: false,
    content: $('#dialog_bacsi'),
    position: {
      y: 'center'
    },
    width: 400,
    height: 200,
    draggable: 'title'
  });
  var sovaovien;
  var sovaovien_dt;
  var pdf;
  function listTabCongkhamInit(){
    $("#list_tabcongkham").jqGrid({
      url: '',
      datatype: "local",
      shrinkToFit: false,
      ignoreCase: true,
      autowidth: true,
      loadonce: true,
      colNames: ["MÃ VẬT TƯ","MÃ KHO", "Số nhập kho chi tiết", "Ngày lệch", "SL TỒN",
      ],
      colModel: [
        {name: 'MAVATTU', index: 'MAVATTU', width: 80},
        {name: 'MAKHOVATTU', index: 'MAKHOVATTU', width: 180},
        {name: 'SONHAPKHOCHITIET', index: 'SONHAPKHOCHITIET', width: 180},
        {name: 'NGAYSAU', index: 'NGAYSAU', width: 120},
        {name: 'SL_TON', index: 'SL_TON', width: 180},

      ],
      rowNum: 10000,
      height: '500',
      footerrow: true,
      rownumbers: true,
      rownumWidth: 25,
      userDataOnFooter: true,
      loadComplete: function () {
      },

      onSelectRow: function(id) {
        var ret = $("#list_tabcongkham").jqGrid('getRowData', id);
        sovaovien= ret.SOVAOVIEN;
        sovaovien_dt= ret.SOVAOVIEN_DT;
        pdf= ret.TENBENHNHAN+"_"+ret.MABENHNHAN;
        console.log("ret", ret);
      }
    });
    $("#list_tabcongkham").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});



  }
  $(function () {
    $("#inbc").hide();
    $("#loai_dv_lable").hide();
    $("#loai_dv_div").hide();
    $(":input").inputmask();
    $("#tungay").datepicker();
    $("#tungay").datepicker("option", "dateFormat", "dd/mm/yy");
    $("#denngay").datepicker();
    $("#denngay").datepicker("option", "dateFormat", "dd/mm/yy");
    var t = new Date();
    $("#tungay").val(t.getDate() +"/"+ (t.getMonth()+1 < 10? "0"+ (t.getMonth()+1): t.getMonth()+1)+ "/"+ t.getFullYear());
    $("#denngay").val(t.getDate() +"/"+ (t.getMonth()+1 < 10? "0"+ (t.getMonth()+1): t.getMonth()+1)+ "/"+ t.getFullYear());
    $("#tab_tabds").tabs();

    'use strict';
    listTabCongkhamInit();
    $("#xembc").click(function (evt) {
      var url1 = 'cmu_getlist?url=' +convertArray(["${Sess_DVTT}", $("#tungay").val(), $("#denngay").val() , "CMU_KT_DULIEUDUOC_KHONGRATOA"]);
      $("#list_tabcongkham").jqGrid('setGridParam', {datatype: 'json', url: url1}).trigger('reloadGrid')

    });
    $("#chinhdulieu").click(function() {
      var data = $("#list_tabcongkham").getGridParam('data');
      if (data.length > 0) {
        $.blockUI({
          css: {zIndex: 9999999},
          message: '<h3>Đang xử lý ...</h3>'
        });
        data.forEach(function(obj) {
          $.ajax({
            method : "post",
            url : "cmu_post?url="+convertArray(["${Sess_DVTT}",obj.SL_TON,
              obj.NGAYSAU, obj.SONHAPKHOCHITIET, obj.MAKHOVATTU,"CMU_CHINH_DULIEUDUOC_KRATOA"]),
            success: function(data) {
              if (data == 1) {
                jAlert("Vui lòng chốt dữ liệu!")
              } else {
                jAlert("Điều chỉnh thành công!")
              }
              $.unblockUI();
            },
            error: function() {
              jAlert("Đã có lỗi!")
              $.unblockUI();
            }
          })
        })

      }

    })

    $("#inbc").click(function (evt) {
      var tab = $("#tab_tabds").tabs("option", "active");

    });
    var dvtt = "${Sess_DVTT}";
    var select = '';



  });
</script>
</html>