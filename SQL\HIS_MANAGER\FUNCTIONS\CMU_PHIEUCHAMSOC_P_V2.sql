CREATE OR REPLACE
PROCEDURE              "CMU_PHIEUCHAMSOC_P_V2" (
    p_dvtt        IN            VARCHAR2,
    p_sovaovien   IN            VARCHAR2,
    p_id          IN            VARCHAR2,
    cur           OUT           SYS_REFCURSOR
) IS
v_sttbenhan varchar2(255):='';
v_giuong varchar2(255):='';
v_buong varchar2(255):='';
v_thamso960616 					 number(10) := cmu_tsdv(p_dvtt, 960616, 0);
BEGIN

SELECT
    STT_BENHAN into v_sttbenhan
FROM NOITRU_BENHAN
WHERE SOVAOVIEN = p_sovaovien and dvtt = p_dvtt;

begin
select STT_BUONG, STT_GIUONG
into v_buong, v_giuong
from CMU_SOBUONGGIUONG
WHERE DVTT = P_DVTT AND
    STT_BENHAN = v_sttbenhan
  and STT_DOTDIEUTRI = 1 and rownum<=1;
exception
when no_data_found then v_giuong:=null;
end;

OPEN cur FOR SELECT
                     pcs.id,
                     pcs.id_dieutri,
                     pcs.dvtt,
                     pb.ten_phongban   khoa,
                     pcs.mabenhnhan,
                     pcs.sovaovien,
                     pcs.sovaovien_dt,
                     TO_CHAR(ngaytao, 'DD/MM/YYYY HH24:MI:SS') ngaytao,
                     TO_CHAR(pcs_ngaygiolap, 'DD/MM/YYYY') pcs_ngaylap,
                     TO_CHAR(pcs_ngaygiolap, 'HH24:MI') pcs_giolap,
                     pcs_theodoidienbien
                     ||
                         CASE
                             WHEN pcs.mach IS NOT NULL THEN
                                 '<br/>- Mạch:' || pcs.mach ||' lần/phút'
                             ELSE
                                 ''
END
||
                         CASE
                             WHEN pcs.huyetapcao IS NOT NULL THEN
                                 '<br/>- Huyết áp:'
                                 || pcs.huyetapcao
                                 || '/'
                                 || pcs.huyetapthap
                                 || ' mmHg'
                             ELSE
                                 ''
END
||
                         CASE
                             WHEN pcs.nhietdo IS NOT NULL THEN
                                 '<br/>- Nhiệt độ:' || pcs.nhietdo || ' °C'
                             ELSE
                                 ''
END
||
                         CASE
                             WHEN pcs.nhiptho IS NOT NULL THEN
                                 '<br/>- Nhịp thở:'
                                 || pcs.nhiptho
                                 || ' lần/phút'
                             ELSE
                                 ''
END
||
CASE
                             WHEN pcs.cannang IS NOT NULL THEN
                                 '<br/>- Cân nặng:' || pcs.cannang ||' kg'
                             ELSE
                                 ''
END
||
CASE
                             WHEN pcs.chieucao IS NOT NULL THEN
                                 '<br/>- Chiều cao:' || pcs.chieucao ||' cm'
                             ELSE
                                 ''
END
                     pcs_theodoidienbien,
                     pcs.pcs_thuchienylenh_cs,
                     nv.ten_nhanvien,
                     pcs.mach,
                     pcs.nhiptho,
                     pcs.nhietdo,
                     pcs.huyetapcao
                     || '/'
                     || pcs.huyetapthap huyet_ap,
                     pcs.chieucao,
                     pcs.cannang,
                     nv.ma_nhanvien,
			--dt.STT_DIEUTRI,
                     pcs_ngaygiolap    ngaylap,
                     pcs_nguoilap,
                     signkcb.keysign   keysign,
										 to_char(signkcb.ngay_ky, 'DD/MM/YYYY HH24:MI:SS') ngay_ky,
										 v_buong BUONG,
										 v_giuong GIUONG,
										 v_thamso960616 ANCHUKY,
										 CMU_GET_CHUKYNHANVIEN(
											pcs.pcs_nguoilap
										 ) ANHCHUKY
                 FROM
                     noitru_phieuchamsoc_nan   pcs
                     INNER JOIN his_fw.dm_phongban        pb ON pcs.dvtt = pb.ma_donvi
                                                         AND pcs.khoa = pb.ma_phongban
                     INNER JOIN his_fw.dm_nhanvien        nv ON pcs.pcs_nguoilap = nv.ma_nhanvien
		--inner join NOITRU_DIEUTRI dt on pcs.dvtt = dt.dvtt and pcs.id_dieutri = dt.id_dieutri
										 LEFT JOIN smartca_signed_kcb        signkcb ON pcs.dvtt = signkcb.dvtt
                                                             AND pcs.sovaovien = signkcb.sovaovien
                                                             AND signkcb.so_phieu_dv = pcs.id
                                                             AND signkcb.status = 0 AND signkcb.dvtt = p_Dvtt
																														 and signkcb.sovaovien=p_sovaovien
                                                             AND signkcb.ky_hieu_phieu IN (
                         'PHIEU_NOITRU_PHIEUCHAMSOC'
                     )
                 WHERE
                     pcs.dvtt = p_dvtt
                     AND pcs.sovaovien = p_sovaovien
                     AND ( pcs.id = p_id
                           OR p_id = '-1' )
                 ORDER BY
                     ngaylap ASC;

END;
