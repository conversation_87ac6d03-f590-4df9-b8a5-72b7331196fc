create or replace PROCED<PERSON><PERSON> "HIS_MANAGER"."CMU_PHIEU_KIEMTRA_BENHAN_P" (
    p_ID in NUMBER,
    CUR out SYS_REFCURSOR
)
IS

BEGIN
    OPEN CUR FOR
    SELECT
        TO_CHAR(TO_TIMESTAMP_TZ(
            JSON_VALUE(CK.DATA_PHIEU, '$.NGAY_TAO_PHIEU'), 'YYYY-MM-DD"T"HH24:MI:SS.FFTZH:TZM'), 'DD'
        ) AS DD_TAO_PHIEU,
        TO_CHAR(TO_TIMESTAMP_TZ(
            JSON_VALUE(CK.DATA_PHIEU, '$.NGAY_TAO_PHIEU'), 'YYYY-MM-DD"T"HH24:MI:SS.FFTZH:TZM'), 'MM'
        ) AS MM_TAO_PHIEU,
        TO_CHAR(TO_TIMESTAMP_TZ(
            JSON_VALUE(CK.DATA_PHIEU, '$.NGAY_TAO_PHIEU'), 'YYYY-MM-DD"T"HH24:MI:SS.FFTZH:TZM'), 'YYYY'
        ) AS YY_TAO_PHIEU,
        TO_CHAR(TO_TIMESTAMP_TZ(
            JSON_VALUE(CK.DATA_PHIEU, '$.NGAY_TAO_PHIEU'), 'YYYY-MM-DD"T"HH24:MI:SS.FFTZH:TZM'), 'HH24'
        ) AS HH_TAO_PHIEU,
        TO_CHAR(TO_TIMESTAMP_TZ(
            JSON_VALUE(CK.DATA_PHIEU, '$.NGAY_TAO_PHIEU'), 'YYYY-MM-DD"T"HH24:MI:SS.FFTZH:TZM'), 'MI'
        ) AS MI_TAO_PHIEU,
        JSON_VALUE(CK.DATA_PHIEU, '$.NGUOI_KIEM_TRA.tennhanvien') AS NGUOI_KIEM_TRA,

        JSON_VALUE(CK.DATA_PHIEU, '$.KHOA.TENKHOA') AS KHOA,
        BN.TEN_BENH_NHAN AS HO_TEN_NGUOI_BENH,
        BN.TUOI AS TUOI,
        CASE BN.GIOI_TINH WHEN 0 THEN 'Nữ' ELSE 'Nam' END AS GIOI_TINH,
        JSON_VALUE(CK.DATA_PHIEU, '$.SO_THE_BHYT') AS SO_THE_BHYT,
        JSON_VALUE(CK.DATA_PHIEU, '$.SO_BA') AS SO_BA,
        JSON_VALUE(CK.DATA_PHIEU, '$.MA_BENH') AS MA_BENH,
        TO_CHAR(TO_TIMESTAMP_TZ(
            JSON_VALUE(CK.DATA_PHIEU, '$.NGAY_VAO_VIEN'), 'YYYY-MM-DD"T"HH24:MI:SS.FFTZH:TZM'), 'HH24:MI DD/MM/YYYY'
        ) AS NGAY_VAO_VIEN,
        TO_CHAR(TO_TIMESTAMP_TZ(
            JSON_VALUE(CK.DATA_PHIEU, '$.NGAY_RA'), 'YYYY-MM-DD"T"HH24:MI:SS.FFTZH:TZM'), 'HH24:MI DD/MM/YYYY'
        ) AS NGAY_RA,
        TO_CHAR(
            TRUNC(TO_TIMESTAMP_TZ(JSON_VALUE(CK.DATA_PHIEU, '$.NGAY_RA'), 'YYYY-MM-DD"T"HH24:MI:SS.FFTZH:TZM'))
                - TRUNC(TO_TIMESTAMP_TZ(JSON_VALUE(CK.DATA_PHIEU, '$.NGAY_VAO_VIEN'), 'YYYY-MM-DD"T"HH24:MI:SS.FFTZH:TZM')) + 1
        ) AS TS_NGAY_DIEU_TRI,
        JSON_VALUE(CK.DATA_PHIEU, '$.BS_DIEU_TRI.tennhanvien') AS BS_DIEU_TRI
    FROM "HIS_MANAGER"."CMU_PHIEU_KIEMTRA_BENHAN" CK
        INNER JOIN HIS_PUBLIC_LIST.DM_BENH_NHAN BN
            ON CK.MA_BENHNHAN = BN.MA_BENH_NHAN
    WHERE CK.ID = p_ID;
END;