CREATE OR REPLACE
PROCEDURE  HIS_MANAGER.PHIEU_KTAT_PHAU_THUAT(
	p_dvtt        IN VARCHAR2,
  p_idPhieuKTPT IN NUMBER,
  CUR           OUT SYS_REFCURSOR
)
IS
v_thoigianbatdau date;
v_thoigianme date;
v_thoigianketthuc date;
v_idlanpt number;
v_bsgayme varchar2(255):='';
	v_bsphauthuat varchar2(255):='';
    v_thamso960616 number(10) := cmu_tsdv(p_dvtt, 960616, 0);
BEGIN

select ID_LANPHAUTHUAT into v_idlanpt from BANGKIEM_ANTOAN_PHAUTHUAT
where id_bang_kiem_an_toan_pt = p_idPhieuKTPT and dvtt = p_Dvtt;

select max(thoi_gian_block), min(thoi_gian_block)
into v_thoigianketthuc,v_thoigianme
from cmu_lpt_gaymehoisuc_ct
where dvtt = p_Dvtt and ID_LANPHAUTHUAT = v_idlanpt;


begin
select TEN_NHANVIEN_CD into v_bsphauthuat
from CMU_LANPHAUTHUAT_EKIP ekip
         JOIN HIS_FW.DM_NHANVIEN_CD nv on EKIP.MANHANVIEN = nv.MA_NHANVIEN
where dvtt = p_dvtt and ID_LANPHAUTHUAT = v_idlanpt and VITRI = 'BSPHAUTHUAT'
  AND ID_LANPHAUTHUAT_EKIP_TONG = (select MIN("ID") from CMU_LANPHAUTHUAT_EKIP_TONG where dvtt = p_dvtt and ID_LANPHAUTHUAT = v_idlanpt);
exception
			when no_data_found then v_bsphauthuat:=null;
end;
begin
select TEN_NHANVIEN_CD into v_bsgayme
from CMU_LANPHAUTHUAT_EKIP ekip
         JOIN HIS_FW.DM_NHANVIEN_CD nv on EKIP.MANHANVIEN = nv.MA_NHANVIEN
where dvtt = p_dvtt and ID_LANPHAUTHUAT = v_idlanpt and VITRI = 'BSGAYME'
  AND ID_LANPHAUTHUAT_EKIP_TONG = (select MIN("ID") from CMU_LANPHAUTHUAT_EKIP_TONG where dvtt = p_dvtt and ID_LANPHAUTHUAT = v_idlanpt);
exception
			when no_data_found then v_bsgayme:=null;
end;


OPEN CUR FOR
select ktpt.id_bang_kiem_an_toan_pt,
       ktpt.dvtt,
       ktpt.mabenhnhan,
       bn.Ten_Benh_Nhan,
       bn.gioi_tinh,
       to_char(extract(year from bn.ngay_sinh)) as nam_sinh,
       ktpt.sovaovien,
       ktpt.sobenhan,
       ktpt.sovaovien_dt,
       to_char(v_thoigianme, 'DD/MM/YYYY HH24:MI') THOI_GIAN_GAY_ME_VC,
       v_bsphauthuat HOTEN_BACSI_PHAUTHUAT,
       v_bsgayme HOTEN_BACSI_GAYME,

       to_char(v_thoigianketthuc, 'DD/MM/YYYY HH24:MI') THOIGIAN_ROIPHONG_PHAUTHUAT,
       'Ngày '|| to_char(v_thoigianketthuc, 'DD') || ' tháng ' ||  to_char(v_thoigianketthuc, 'MM') || ' năm '
           ||  to_char(v_thoigianketthuc, 'YYYY') NGAYTHANGNAM,
       to_char(ktpt.create_date, 'dd/mm/yyyy') ngay_tao_phieu,
       ktpt.user_id,
       cd.mota_chucdanh || '. ' || nv.ten_nhanvien as nguoi_tao_phieu,
       ktpt.ma_phongban,
       pb.ten_phongban,
       lpt.ASA XEP_LOAI_ASA,
       lpt.HINHTHUC_PHAUTHUAT LOAI_PHAU_THUAT,
       CASE WHEN lpt.LOAI_PHAU_THUAT = 1 THEN 'Dạ dày đầy'
            WHEN lpt.LOAI_PHAU_THUAT = 2 THEN 'Cấp cứu'
            WHEN lpt.LOAI_PHAU_THUAT = 3 THEN 'Phiên'
            WHEN lpt.LOAI_PHAU_THUAT = 4 THEN 'Theo yêu cầu'
            ELSE 'Chưa xác định' END PHAN_LOAI_PT,
       lpt.PHUONG_PHAP_PHAU_THUAT,
       lpt.PHUONG_PHAP_VO_CAM,

       JSON_VALUE(ktpt.truoc_gayme_vocam, '$.XACNHANLAITENNGUOIBENH' RETURNING VARCHAR2) XACNHANLAITENNGUOIBENH,
       JSON_VALUE(ktpt.truoc_gayme_vocam, '$.XACNHANLAIPHUONGPHAPPHAUTHUAT' RETURNING VARCHAR2) XACNHANLAIPHUONGPHAPPHAUTHUAT,
       JSON_VALUE(ktpt.truoc_gayme_vocam, '$.XACNHANDONGYPHAUTHUAT' RETURNING VARCHAR2) XACNHANDONGYPHAUTHUAT,
       JSON_VALUE(ktpt.truoc_gayme_vocam, '$.VUNGMODUOCDANHDAU' RETURNING VARCHAR2) VUNGMODUOCDANHDAU,
       JSON_VALUE(ktpt.truoc_gayme_vocam, '$.KIEMTRATHUOCVATHIETBI' RETURNING VARCHAR2) KIEMTRATHUOCVATHIETBI,
       JSON_VALUE(ktpt.truoc_gayme_vocam, '$.KIEMTRAMAYDOBAOHOAOXY' RETURNING VARCHAR2) KIEMTRAMAYDOBAOHOAOXY,
       JSON_VALUE(ktpt.truoc_gayme_vocam, '$.TIENSUDIDUNG' RETURNING VARCHAR2) TIENSUDIDUNG,
       JSON_VALUE(ktpt.truoc_gayme_vocam, '$.DATNOIKHIQUANKHO' RETURNING VARCHAR2) DATNOIKHIQUANKHO,
       JSON_VALUE(ktpt.truoc_gayme_vocam, '$.DUONGTHOKHONGUYCOSAC' RETURNING VARCHAR2) DUONGTHOKHONGUYCOSAC,
       JSON_VALUE(ktpt.truoc_gayme_vocam, '$.NGUYCOMATMAU' RETURNING VARCHAR2) NGUYCOMATMAU,
       nv1.TEN_NHANVIEN_CD TENNGUOITHUCHIEN1,


       JSON_VALUE(ktpt.truoc_khi_rach_da, '$.PHAUTHUATVIENXACNHAN' RETURNING VARCHAR2) PHAUTHUATVIENXACNHAN,
       JSON_VALUE(ktpt.truoc_khi_rach_da, '$.GAYMEXACNHAN' RETURNING VARCHAR2) GAYMEXACNHAN,
       JSON_VALUE(ktpt.truoc_khi_rach_da, '$.PHUMOXACNHAN' RETURNING VARCHAR2) PHUMOXACNHAN,
       JSON_VALUE(ktpt.truoc_khi_rach_da, '$.DUNGXACNHAN' RETURNING VARCHAR2) DUNGXACNHAN,
       JSON_VALUE(ktpt.truoc_khi_rach_da, '$.XACNHANDUNGBENHNHAN' RETURNING VARCHAR2) XACNHANDUNGBENHNHAN,
       JSON_VALUE(ktpt.truoc_khi_rach_da, '$.XACNHANDUNGVITRI' RETURNING VARCHAR2) XACNHANDUNGVITRI,
       JSON_VALUE(ktpt.truoc_khi_rach_da, '$.XACNHANDUNGPPPHAUTHUAT' RETURNING VARCHAR2) XACNHANDUNGPPPHAUTHUAT,
       JSON_VALUE(ktpt.truoc_khi_rach_da, '$.NGUYCOCAODIENBIENBATNGO' RETURNING VARCHAR2) NGUYCOCAODIENBIENBATNGO,
       JSON_VALUE(ktpt.truoc_khi_rach_da, '$.TIENLUONGTHOIGIANPHAUTHUAT' RETURNING VARCHAR2) TIENLUONGTHOIGIANPHAUTHUAT,
       JSON_VALUE(ktpt.truoc_khi_rach_da, '$.DUBAOLUONGMAUMAT' RETURNING VARCHAR2) DUBAOLUONGMAUMAT,
       JSON_VALUE(ktpt.truoc_khi_rach_da, '$.DUNGCUDACBIET' RETURNING VARCHAR2) DUNGCUDACBIET,
       JSON_VALUE(ktpt.truoc_khi_rach_da, '$.LUUYTINHTRANGNGUOIBENH' RETURNING VARCHAR2) LUUYTINHTRANGNGUOIBENH,
       JSON_VALUE(ktpt.truoc_khi_rach_da, '$.DUNGCUTRIETKHUAN' RETURNING VARCHAR2) DUNGCUTRIETKHUAN,
       JSON_VALUE(ktpt.truoc_khi_rach_da, '$.DUNGCUHUHONG' RETURNING VARCHAR2) DUNGCUHUHONG,
       JSON_VALUE(ktpt.truoc_khi_rach_da, '$.KHANGSINHDUPHONG' RETURNING VARCHAR2) KHANGSINHDUPHONG,
       JSON_VALUE(ktpt.truoc_khi_rach_da, '$.XETNGHIEMHINHANHTRUNGBAY' RETURNING VARCHAR2) XETNGHIEMHINHANHTRUNGBAY,
       nv2.TEN_NHANVIEN_CD TENNGUOITHUCHIEN2,

       JSON_VALUE(ktpt.truoc_khi_roi_phong_pt, '$.DOCLAIPPPHAUTHUAT' RETURNING VARCHAR2) DOCLAIPPPHAUTHUAT,
       JSON_VALUE(ktpt.truoc_khi_roi_phong_pt, '$.DUNGCUDU' RETURNING VARCHAR2) DUNGCUDU,
       JSON_VALUE(ktpt.truoc_khi_roi_phong_pt, '$.DAO' RETURNING VARCHAR2) DAO,
       JSON_VALUE(ktpt.truoc_khi_roi_phong_pt, '$.KIM' RETURNING VARCHAR2) KIM,
       JSON_VALUE(ktpt.truoc_khi_roi_phong_pt, '$.GAC' RETURNING VARCHAR2) GAC,
       JSON_VALUE(ktpt.truoc_khi_roi_phong_pt, '$.DANNHANBENHPHAM' RETURNING VARCHAR2) DANNHANBENHPHAM,
       JSON_VALUE(ktpt.truoc_khi_roi_phong_pt, '$.COHUHONGDUNGCU' RETURNING VARCHAR2) COHUHONGDUNGCU,
       JSON_VALUE(ktpt.truoc_khi_roi_phong_pt, '$.CHAMSOC' RETURNING VARCHAR2) CHAMSOC,
       JSON_VALUE(ktpt.truoc_khi_roi_phong_pt, '$.CHITIETHOISUCCHAMSOC' RETURNING VARCHAR2) CHITIETHOISUCCHAMSOC,
       nv3.TEN_NHANVIEN_CD TENNGUOITHUCHIEN3,
       v_thamso960616 ANCHUKY

from BANGKIEM_ANTOAN_PHAUTHUAT ktpt
         join CMU_LANPHAUTHUAT lpt on ktpt.dvtt = lpt.dvtt and ktpt.id_lanphauthuat = lpt.id
         INNER JOIN HIS_PUBLIC_LIST.DM_BENH_NHAN bn
                    ON bn.MA_BENH_NHAN = ktpt.Mabenhnhan
         INNER JOIN HIS_FW.DM_PHONGBAN pb
                    ON pb.ma_donvi = ktpt.dvtt
                        and pb.Ma_Phongban = ktpt.Ma_Phongban
         inner join HIS_FW.DM_NHANVIEN nv
                    on nv.ma_nhanvien = ktpt.user_id
         left join HIS_FW.DM_CHUCDANH_NHANVIEN cd
                   on cd.ma_chucdanh = nv.chucdanh_nhanvien
         left join HIS_FW.DM_NHANVIEN_CD nv1
                   on nv1.ma_nhanvien = JSON_VALUE(ktpt.truoc_gayme_vocam, '$.NGUOITHUCHIEN1' RETURNING VARCHAR2)
         left join HIS_FW.DM_NHANVIEN_CD nv2
                   on nv2.ma_nhanvien = JSON_VALUE(ktpt.truoc_khi_rach_da, '$.NGUOITHUCHIEN2' RETURNING VARCHAR2)
         left join HIS_FW.DM_NHANVIEN_CD nv3
                   on nv3.ma_nhanvien = JSON_VALUE(ktpt.truoc_khi_roi_phong_pt, '$.NGUOITHUCHIEN3' RETURNING VARCHAR2)
where ktpt.id_bang_kiem_an_toan_pt = p_idPhieuKTPT and ktpt.dvtt = p_Dvtt ;
END;
