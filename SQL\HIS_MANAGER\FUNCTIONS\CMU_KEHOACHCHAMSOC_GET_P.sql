create or replace PROCEDURE "HIS_MANAGER"."CMU_KEHOACHCHAMSOC_GET_P" (
    p_dvtt          IN       VARCHAR2,
    p_id            IN       VARCHAR2,
    p_sovaovien     IN       VARCHAR2,
    cur             OUT      SYS_REFCURSOR
) IS
BEGIN
OPEN cur FOR SELECT
                     bn.ten_benh_nhan,
                     pba.ten_phongban ten_khoa_lap_phieu,
                     ct.sobenhan,
                     nvl(ct.so_phieu, ' ') so_phieu,
                     bn.tuoi,
                     CASE
                         WHEN bn.gioi_tinh = 1 THEN
                             'Nam'
                         ELSE
                             'Nữ'
END gioi_tinh,
                     cmu_geticd_moinhat(p_dvtt, ct.sovaovien, ct.sovaovien_dt) chan_doan,
                     nvl(ct.so_giuong, ' ') so_giuong,
                     TO_CHAR(ct.ngay_lap_phieu, 'dd/mm/yyyy hh24:mi') ngay_gio,
                     nvl(ct.tinh_trang_nguoi_benh, ' ') tinh_trang_nguoi_benh,
                     nvl(ct.ke_hoach_cham_soc, ' ') ke_hoach_cham_soc,
                     nvl(ct.thuc_hien_cham_soc, ' ') thuc_hien_cham_soc,
                     nvl(ct.luong_gia, ' ') luong_gia,
                     nv.ten_nhanvien ten_nguoi_lap_phieu
                 FROM
                     his_manager.cmu_kehoachchamsoc   ct
                     INNER JOIN his_fw.dm_phongban               pba ON ct.khoa_lap_phieu = pba.ma_phongban
                     INNER JOIN his_public_list.dm_benh_nhan     bn ON ct.mabenhnhan = bn.ma_benh_nhan
                     INNER JOIN his_fw.dm_nhanvien               nv ON ct.nguoi_lap_phieu = nv.ma_nhanvien
                 WHERE
                     ct.dvtt = p_dvtt
                     AND ct.sovaovien = p_sovaovien
                     AND (ct.id = p_id or p_id = '-1');

END;