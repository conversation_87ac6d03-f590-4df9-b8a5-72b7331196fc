CREATE SEQUENCE his_manager.iseq$$_153781
    INCREMENT BY 1
    START WITH 1
    MINVALUE 1
    MAXVALUE 9999999999999999999999999999
    NOCYCLE
  NOORDER
  CACHE 20
/
CREATE TABLE his_manager.dm_donvi_chuyen_doi
(id                             NUMBER DEFAULT "HIS_MANAGER"."ISEQ$$_153781".nextval NOT NULL,
 madonvi                        VARCHAR2(10 CHAR) NOT NULL,
 madonvi_moi                    VARCHAR2(10 CHAR) NOT NULL,
 ngay_hieu_luc                  VARCHAR2(12 BYTE) NOT NULL,
 ngay_het_hieu_luc              VARCHAR2(12 BYTE),
 ghi_chu                        VARCHAR2(200 CHAR))
    SEGMENT CREATION IMMEDIATE
  PCTFREE     10
  INITRANS    1
  MAXTRANS    255
  TABLESPACE  dbhis
  STORAGE   (
    INITIAL     65536
    NEXT        1048576
    MINEXTENTS  1
    MAXEXTENTS  2147483645
  )
  NOCACHE
  MONITORING
  NOPARALLEL
  LOGGING
/





-- Indexes for DM_DONVI_CHUYEN_DOI

CREATE UNIQUE INDEX uq_donvi_hieuluc ON his_manager.dm_donvi_chuyen_doi
  (
    madonvi                         ASC,
    ngay_hieu_luc                   ASC
  )
  PCTFREE     10
  INITRANS    2
  MAXTRANS    255
  TABLESPACE  dbhis
  STORAGE   (
    INITIAL     65536
    NEXT        1048576
    MINEXTENTS  1
    MAXEXTENTS  2147483645
  )
NOPARALLEL
LOGGING
/



-- Constraints for DM_DONVI_CHUYEN_DOI

ALTER TABLE his_manager.dm_donvi_chuyen_doi
    ADD PRIMARY KEY (id)
    USING INDEX
  PCTFREE     10
  INITRANS    2
  MAXTRANS    255
  TABLESPACE  dbhis
  STORAGE   (
    INITIAL     65536
    NEXT        1048576
    MINEXTENTS  1
    MAXEXTENTS  2147483645
  )
/


-- End of DDL Script for Table HIS_MANAGER.DM_DONVI_CHUYEN_DOI

