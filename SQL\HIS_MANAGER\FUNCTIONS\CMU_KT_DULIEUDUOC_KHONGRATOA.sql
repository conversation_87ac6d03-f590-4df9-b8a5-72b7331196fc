CREATE OR REPLACE
FUNCTION               "CMU_KT_DULIEUDUOC_KHONGRATOA" ( p_dvtt in varchar2,
 p_tungay in varchar2,
 p_denngay in varchar2)
return SYS_REFCURSOR
IS
 cur SYS_REFCURSOR;
BEGIN
open cur for with xnt as (
			select a.*,
			ROW_NUMBER () OVER (PARTITION BY SONHAPKHOCHITIET, DVTT, MAKHOVATTU ORDER BY ngaybiendong) rn
			from HIS_MANAGER.DC_TB_XNT_BIENDONG a
				where dvtt=p_dvtt
				and NGAYBIENDONG BETWEEN TO_DATE(p_tungay, 'DD/MM/YYYY') AND TO_DATE(p_denngay, 'DD/MM/YYYY')
				--AND SONHAPKHOCHITIET = 734580
				order by ngaybiendong
		)
select TO_CHAR(a.ngaybiendong, 'DD/MM/YYYY') ng<PERSON><PERSON>,b.ngaybiendong ngaytruoc,b.sl_ton, a.ma<PERSON><PERSON>, a.makhovattu,a.sonhapkhochitiet
from  xnt a join xnt b on a.rn = b.rn + 1
    AND b.sonhapkhochitiet = a.sonhapkhochitiet and a.makhovattu = b.makhovattu
where
    a.sL_ton != b.sL_ton + a.sl_nhap + a.sl_chuyenden-a.sl_chuyendi - a.sl_xuat_nv3_ngoai-a.sl_xuat_nv3_noi - a.sl_xuat_ko_nv3
;

return cur;
END;
