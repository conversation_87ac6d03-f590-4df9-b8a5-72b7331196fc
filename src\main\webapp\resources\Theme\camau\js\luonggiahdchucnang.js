$(function (){
    var thongTinLuongGiaHDChucNangMoiNhat = {};
    var thongTinLuongGiaHDChucNangTruocChinhSua = {};
    var formLuongGiaHDChucNang;

    $("#ttchamsoc-phieukhac").click(function () {
        instanceGridLuongGiaHDChucNang();
        reloadDSLuongGiaHDChucNang();
        showFormLuongGiaHDChucNang();
    });
    $("#luonggiahdchucnang_lammoi").click(function () {
        reloadDSLuongGiaHDChucNang();
    });

    $(".themluonggiahdchucnang").click(function () {
        $("#modalFormLuongGiaHDChucNang").modal("show");
        addTextTitleModal("titleFormLuongGiaHDChucNang", " Phiếu lượng giá hoạt động chức năng và sự tham gia");
        showFormLuongGiaHDChucNang();
        $("#luonggiahdchucnang_luu").attr("data-action", "THEM");
    });

    $("#luonggiahdchucnang_luu").click(function () {
        var btnAction = $('#luonggiahdchucnang_luu').attr("data-action");
        if (btnAction == "THEM"){
            themLuongGiaHDChucNang();
        } else {
            updateLuongGiaHDChucNang();
        }
    });
    $(document).on('click', '#luonggiahdcn_kyso_action', function() {
        kySoChung({
            dvtt: singletonObject.dvtt,
            userId: singletonObject.userId,
            url: $('#iframePreviewAndSign').attr('src'),
            loaiGiay: "PHIEU_NOITRU_LUONGGIAHDCN",
            maBenhNhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
            soBenhAn: thongtinhsba.thongtinbn.STT_BENHAN,
            soPhieuDichVu: thongTinLuongGiaHDChucNangTruocChinhSua.ID,
            soVaoVien: thongtinhsba.thongtinbn.SOVAOVIEN,
            soVaoVienDT: thongtinhsba.thongtinbn.SOVAOVIEN_DT,
            keyword: "và ghi rõ họ",
            fileName: "Phiếu lượng giá hoạt động chức năng và sự tham gia: " + thongtinhsba.thongtinbn.TEN_BENH_NHAN + " - Mã phiếu: " + thongTinLuongGiaHDChucNangTruocChinhSua.ID,
        }, function(dataKySo) {
            $("#modalPreviewAndSignPDF").modal("hide");
            reloadDSLuongGiaHDChucNang();
        });
    });

    $('#view_single_luonggiahdchucnang').click(function () {
        xemLuongGiaHDChucNang(thongTinLuongGiaHDChucNangMoiNhat)
    })

    $('#edit_single_luonggiahdchucnang').click(function () {
        showUpdateLuongGiaHDChucNang(thongTinLuongGiaHDChucNangMoiNhat)
    });

    $('#delete_single_luonggiahdchucnang').click(function () {
        deleteLuongGiaHDChucNang(thongTinLuongGiaHDChucNangMoiNhat)
    })

    function showFormLuongGiaHDChucNang() {
        var optionsDanToc = singletonObject.danhsachdantoc.filter(function(object) {
            return object.MA_DANTOC != 0
        }).map(function(object) {
            return {
                label: object.MA_DANTOC + " - "+ object.TEN_DANTOC,
                value: object.MA_DANTOC
            }
        })
        var optionsNgheNghiep = singletonObject.danhsachnghenghiep.filter(function(object) {
            return object.MA_NGHE_NGHIEP != 0
        }).map(function(object) {
            return {
                label: object.MA_NGHE_NGHIEP + " - "+ object.TEN_NGHE_NGHIEP,
                value: object.MA_NGHE_NGHIEP
            }
        })
        var jsonForm = getJSONObjectForm([
            {
                "label": "Người nhà",
                "columns": [
                    {
                        "components": [
                            {
                                "label": "Ngày tạo phiếu",
                                "key": "NGAY_TAO_PHIEU",
                                "type": "datetime",
                                format: "dd/MM/yyyy",
                                enableTime: false,
                                customClass: "pr-2",
                                minDate: moment(thongtinhsba.thongtinbn.NGAY_VAO_VIEN, ['DD/MM/YYYY']).format("YYYY-MM-DD"),
                                maxDate: moment().format("YYYY-MM-DD"),
                                "validate": {
                                    "required": true
                                },
                            },
                        ],
                        "width": 6,
                    },
                ],
                "customClass": "ml-0 mr-0",
                "key": "THONGTINNGUOINHANHANVIEN",
                "type": "columns",
            },
            ...(singletonObject.dvtt === "96029" ? [
                {
                    "label": "Người nhà",
                    "columns": [
                        {
                            "components": [
                                {
                                    "label": "Vận động và di chuyển",
                                    "key": "VAN_DONG",
                                    "type": "select",
                                    customClass: "pr-2",
                                    others: {
                                        "tooltip": "thay đổi vị trí, tư thế; di chuyển độc lập hay cần dụng cụ trợ giúp, người trợ giúp; đi lại....",
                                        "data": {
                                            "values": [
                                                {
                                                    "label": "Bệnh nhân không thể thay đổi tư thế từ nằm sang ngồi được mà cần sự trợ giúp của người chăm sóc hoàn toàn.",
                                                    "value": "Bệnh nhân không thể thay đổi tư thế từ nằm sang ngồi được mà cần sự trợ giúp của người chăm sóc hoàn toàn."
                                                },
                                                {
                                                    "label": "Bệnh nhân không thể tự di chuyển được mà cần dụng cụ trợ giúp bằng xe lăn và người chăm sóc.",
                                                    "value": "Bệnh nhân không thể tự di chuyển được mà cần dụng cụ trợ giúp bằng xe lăn và người chăm sóc."
                                                },
                                                {
                                                    "label": "Bệnh nhân có thể tự di chuyển được nhờ dụng cụ trợ giúp bằng gậy bốn chân.",
                                                    "value": "Bệnh nhân có thể tự di chuyển được nhờ dụng cụ trợ giúp bằng gậy bốn chân."
                                                },
                                                {
                                                    "label": "Bệnh nhân có thể tự di chuyển được nhờ dụng cụ trợ giúp bằng khung tập đi.",
                                                    "value": "Bệnh nhân có thể tự di chuyển được nhờ dụng cụ trợ giúp bằng khung tập đi."
                                                },
                                                {
                                                    "label": "Bệnh nhân tự di chuyển được nhưng còn khó khăn, chậm chạp.",
                                                    "value": "Bệnh nhân tự di chuyển được nhưng còn khó khăn, chậm chạp."
                                                }
                                            ]
                                        },
                                    },
                                },
                            ],
                            "width": 6,
                        },
                        {
                            "components": [
                                {
                                    "label": "Chức năng sinh hoạt hàng ngày",
                                    "key": "SINH_HOAT",
                                    "type": "select",
                                    customClass: "pr-2",
                                    others: {
                                        "tooltip": "mức độ độc lập trong hoạt động: ăn uống; tắm rửa; vệ sinh cá nhân; mặc quần áo; sử dụng nhà vệ sinh; kiểm soát đại tiện - tiểu tiện...",
                                        "data": {
                                            "values": [
                                                {
                                                    "label": "Bệnh nhân không thể tự chăm sóc bản thân được mà cần người trợ giúp hoàn toàn.",
                                                    "value": "Bệnh nhân không thể tự chăm sóc bản thân được mà cần người trợ giúp hoàn toàn."
                                                },
                                                {
                                                    "label": "Bệnh nhân có thể tự ăn uống, tắm rửa,vệ sinh cá nhân,mặc quần áo được nhưng cần người chăm sóc hỗ trợ chuẩn bị bữa ăn sẵn và chuẩn bị sẵn quần áo.",
                                                    "value": "Bệnh nhân có thể tự ăn uống, tắm rửa,vệ sinh cá nhân,mặc quần áo được nhưng cần người chăm sóc hỗ trợ chuẩn bị bữa ăn sẵn và chuẩn bị sẵn quần áo."
                                                },
                                                {
                                                    "label": "Bệnh nhân có thể tự ăn uống, tắm rửa,vệ sinh cá nhân, mặc quần áo và sử dụng nhà vệ sinh một cách độc lập nhưng còn hơi khó khăn, chậm chạp.",
                                                    "value": "Bệnh nhân có thể tự ăn uống, tắm rửa,vệ sinh cá nhân, mặc quần áo và sử dụng nhà vệ sinh một cách độc lập nhưng còn hơi khó khăn, chậm chạp."
                                                }
                                            ]
                                        },
                                    },
                                },
                            ],
                            "width": 6,
                        },

                    ],
                    "customClass": "ml-0 mr-0",
                    "key": "THONGTINNGUOINHANHANVIEN",
                    "type": "columns",
                },
                {
                    "label": "Người nhà",
                    "columns": [
                        {
                            "components": [
                                {
                                    "label": "Nhận thức, giao tiếp",
                                    "key": "NHAN_THUC",
                                    "type": "select",
                                    customClass: "pr-2",
                                    others: {
                                        "tooltip": "định hướng; tập trung chú ý; trí nhớ; thờ ơ lãng quên; chức năng điều hành; giao tiếp, ngôn ngữ...",
                                        "data": {
                                            "values": [
                                                {
                                                    "label": "Bệnh nhân lơ mơ không giao tiếp được.",
                                                    "value": "Bệnh nhân lơ mơ không giao tiếp được."
                                                },
                                                {
                                                    "label": "Bệnh nhân mất tập trung, trí nhớ kém hay thờ ơ lãng quên.",
                                                    "value": "Bệnh nhân mất tập trung, trí nhớ kém hay thờ ơ lãng quên."
                                                },
                                                {
                                                    "label": "Bệnh nhân tỉnh táo nhưng không thể giao tiếp bằng ngôn ngữ được.",
                                                    "value": "Bệnh nhân tỉnh táo nhưng không thể giao tiếp bằng ngôn ngữ được."
                                                },
                                                {
                                                    "label": "Bệnh nhân tỉnh táo tiếp xúc tốt, giao tiếp bình thường.",
                                                    "value": "Bệnh nhân tỉnh táo tiếp xúc tốt, giao tiếp bình thường."
                                                }
                                            ]
                                        },
                                    },
                                },
                            ],
                            "width": 6,
                        },
                        {
                            "components": [
                                {
                                    "label": "Các chức năng khác",
                                    "key": "CHUC_NANG_KHAC",
                                    "type": "select",
                                    customClass: "pr-2",
                                    others: {
                                        "tooltip": "rối loạn nuốt, tiết niệu, sinh dục, da, các giác quan…….",
                                        "data": {
                                            "values": [
                                                {
                                                    "label": "Bệnh nhân rối loạn nuốt cho ăn quan sol dạ dày,tiêu tiểu không tự chủ, da nhợt nhạt.",
                                                    "value": "Bệnh nhân rối loạn nuốt cho ăn quan sol dạ dày,tiêu tiểu không tự chủ, da nhợt nhạt."
                                                },
                                                {
                                                    "label": "Bệnh nhân nuốt hơi khó, ăn, uống dễ bị sặc, tiêu tiểu lúc kiểm soát được lúc không.",
                                                    "value": "Bệnh nhân nuốt hơi khó, ăn, uống dễ bị sặc, tiêu tiểu lúc kiểm soát được lúc không."
                                                },
                                                {
                                                    "label": "Bệnh nhân nuốt được nhưng đôi khi ăn, uống dễ bị sặc.",
                                                    "value": "Bệnh nhân nuốt được nhưng đôi khi ăn, uống dễ bị sặc."
                                                },
                                                {
                                                    "label": "Bệnh nhân ăn, uống bình thường, tiêu tiểu tự chủ, các chức năng khác chưa ghi nhận bất thường.",
                                                    "value": "Bệnh nhân ăn, uống bình thường, tiêu tiểu tự chủ, các chức năng khác chưa ghi nhận bất thường."
                                                }
                                            ]
                                        },
                                    },
                                },
                            ],
                            "width": 6,
                        },

                    ],
                    "customClass": "ml-0 mr-0",
                    "key": "THONGTINNGUOINHANHANVIEN",
                    "type": "columns",
                },
                {
                    "label": "Sự tham gia các hoạt động trong gia đình và xã hội",
                    "key": "THAM_GIA_HOAT_DONG",
                    "type": "select",
                    customClass: "pr-2",
                    others: {
                        "tooltip": "chuẩn bị bữa ăn, công việc nội trợ, dọn dẹp nơi sinh hoạt/nhà cửa, đi chợ, mua sắm, tham gia hoạt động xã hội ….",
                        "data": {
                            "values": [
                                {
                                    "label": "Bệnh nhân không còn tham gia các hoạt động trong gia đình và xã hội.",
                                    "value": "Bệnh nhân không còn tham gia các hoạt động trong gia đình và xã hội."
                                },
                                {
                                    "label": "Bệnh nhân có thể tham gia phụ giúp chuẩn bị bữa ăn, dọn dẹp vệ sinh nhà cửa được nhưng còn khó khăn, chậm chạp.",
                                    "value": "Bệnh nhân có thể tham gia phụ giúp chuẩn bị bữa ăn, dọn dẹp vệ sinh nhà cửa được nhưng còn khó khăn, chậm chạp."
                                },
                                {
                                    "label": "Bệnh nhân có thể tham gia các buổi tập nhóm và các buổi trồng, chăm sóc cây ở vườn trị liệu nhưng còn hơi khó khăn, di chuyển hơi chậm chạp.",
                                    "value": "Bệnh nhân có thể tham gia các buổi tập nhóm và các buổi trồng, chăm sóc cây ở vườn trị liệu nhưng còn hơi khó khăn, di chuyển hơi chậm chạp."
                                },
                                {
                                    "label": "Bệnh nhân tham gia được tất cả các hoạt động trong bệnh viện(tự chuẩn bị bữa ăn, tự tắm rửa, dọn dẹp vệ sinh phòng bệnh...).",
                                    "value": "Bệnh nhân tham gia được tất cả các hoạt động trong bệnh viện(tự chuẩn bị bữa ăn, tự tắm rửa, dọn dẹp vệ sinh phòng bệnh...)."
                                }
                            ]
                        },
                    },
                },
                {
                    "label": "Yếu tố môi trường",
                    "key": "MOI_TRUONG",
                    "type": "select",
                    customClass: "pr-2",
                    others: {
                        "tooltip": "Đánh giá tiếp cận môi trường của trẻ khuyết tật/NB: tình trạng nơi sinh hoạt/ điều trị, nhà vệ sinh, dụng cụ PHCN đang sử dụng; sự hỗ trợ và quan tâm của những người xung quanh; thái độ và cách ứng xử của gia đình, xã hội...",
                        "data": {
                            "values": [
                                {
                                    "label": "Nơi điều trị có lối đi bằng phẳng, nền được lót gạch bằng phẳng, không khí  thoáng mát, sạch sẽ, có nhà vệ sinh đầy đủ tiện nghi giành cho người khuyết tật, có nhiều dụng cụ PHCN, được Y Bác sỹ và người chăm sóc luôn quan tâm giúp đở.",
                                    "value": "Nơi điều trị có lối đi bằng phẳng, nền được lót gạch bằng phẳng, không khí  thoáng mát, sạch sẽ, có nhà vệ sinh đầy đủ tiện nghi giành cho người khuyết tật, có nhiều dụng cụ PHCN, được Y Bác sỹ và người chăm sóc luôn quan tâm giúp đở."
                                }
                            ]
                        },
                    },
                },
                {
                    "label": "Yếu tố cá nhân",
                    "key": "CA_NHAN",
                    "type": "select",
                    customClass: "pr-2",
                    others: {
                        "tooltip": "tình trạng hôn nhân, trình độ học vấn, tình trạng việc làm, thể lực, tâm lý, sở thích, lối sống, thói quen, kỹ năng xử lý tình huống, tính cách,…",
                        "data": {
                            "values": [
                                {
                                    "label": "Bệnh nhân sống cùng cha mẹ.",
                                    "value": "Bệnh nhân sống cùng cha mẹ."
                                },
                                {
                                    "label": "Bệnh nhân sống cùng vợ và các con.",
                                    "value": "Bệnh nhân sống cùng vợ và các con."
                                },
                                {
                                    "label": "Bệnh nhân sống cùng chồng và các con.",
                                    "value": "Bệnh nhân sống cùng chồng và các con."
                                },
                                {
                                    "label": "Bệnh nhân hiện tại không còn làm việc do bệnh tật.",
                                    "value": "Bệnh nhân hiện tại không còn làm việc do bệnh tật."
                                },
                                {
                                    "label": "Bệnh nhân thích xem tivi, điện thoại.",
                                    "value": "Bệnh nhân thích xem tivi, điện thoại."
                                },
                                {
                                    "label": "Bệnh nhân thích uống cà phê, trò chuyện với các bệnh nhân trọng bệnh viện.",
                                    "value": "Bệnh nhân thích uống cà phê, trò chuyện với các bệnh nhân trọng bệnh viện."
                                },
                                {
                                    "label": "Bệnh nhân ít nói khó tiếp xúc.",
                                    "value": "Bệnh nhân ít nói khó tiếp xúc."
                                },
                                {
                                    "label": "Bệnh nhân sống vui vẻ lạc quan.",
                                    "value": "Bệnh nhân sống vui vẻ lạc quan."
                                }
                            ]
                        },
                    },
                },
            ] : [
                {
                    "label": "Người nhà",
                    "columns": [
                        {
                            "components": [
                                {
                                    "label": "Vận động và di chuyển",
                                    "key": "VAN_DONG",
                                    "type": "textarea",
                                    customClass: "pr-2",
                                    others: {
                                        "tooltip": "thay đổi vị trí, tư thế; di chuyển độc lập hay cần dụng cụ trợ giúp, người trợ giúp; đi lại....",
                                    },
                                },
                            ],
                            "width": 6,
                        },
                        {
                            "components": [
                                {
                                    "label": "Chức năng sinh hoạt hàng ngày",
                                    "key": "SINH_HOAT",
                                    "type": "textarea",
                                    customClass: "pr-2",
                                    others: {
                                        "tooltip": "mức độ độc lập trong hoạt động: ăn uống; tắm rửa; vệ sinh cá nhân; mặc quần áo; sử dụng nhà vệ sinh; kiểm soát đại tiện - tiểu tiện...",
                                    },
                                },
                            ],
                            "width": 6,
                        },

                    ],
                    "customClass": "ml-0 mr-0",
                    "key": "THONGTINNGUOINHANHANVIEN",
                    "type": "columns",
                },
                {
                    "label": "Người nhà",
                    "columns": [
                        {
                            "components": [
                                {
                                    "label": "Nhận thức, giao tiếp",
                                    "key": "NHAN_THUC",
                                    "type": "textarea",
                                    customClass: "pr-2",
                                    others: {
                                        "tooltip": "định hướng; tập trung chú ý; trí nhớ; thờ ơ lãng quên; chức năng điều hành; giao tiếp, ngôn ngữ...",
                                    },
                                },
                            ],
                            "width": 6,
                        },
                        {
                            "components": [
                                {
                                    "label": "Các chức năng khác",
                                    "key": "CHUC_NANG_KHAC",
                                    "type": "textarea",
                                    customClass: "pr-2",
                                    others: {
                                        "tooltip": "rối loạn nuốt, tiết niệu, sinh dục, da, các giác quan…….",
                                    },
                                },
                            ],
                            "width": 6,
                        },

                    ],
                    "customClass": "ml-0 mr-0",
                    "key": "THONGTINNGUOINHANHANVIEN",
                    "type": "columns",
                },
                {
                    "label": "Sự tham gia các hoạt động trong gia đình và xã hội",
                    "key": "THAM_GIA_HOAT_DONG",
                    "type": "textarea",
                    customClass: "pr-2",
                    others: {
                        "tooltip": "chuẩn bị bữa ăn, công việc nội trợ, dọn dẹp nơi sinh hoạt/nhà cửa, đi chợ, mua sắm, tham gia hoạt động xã hội ….",
                    },
                },
                {
                    "label": "Yếu tố môi trường",
                    "key": "MOI_TRUONG",
                    "type": "textarea",
                    customClass: "pr-2",
                    others: {
                        "tooltip": "Đánh giá tiếp cận môi trường của trẻ khuyết tật/NB: tình trạng nơi sinh hoạt/ điều trị, nhà vệ sinh, dụng cụ PHCN đang sử dụng; sự hỗ trợ và quan tâm của những người xung quanh; thái độ và cách ứng xử của gia đình, xã hội...",
                    },
                },
                {
                    "label": "Yếu tố cá nhân",
                    "key": "CA_NHAN",
                    "type": "textarea",
                    customClass: "pr-2",
                    others: {
                        "tooltip": "tình trạng hôn nhân, trình độ học vấn, tình trạng việc làm, thể lực, tâm lý, sở thích, lối sống, thói quen, kỹ năng xử lý tình huống, tính cách,…",
                    },
                },
            ]),
        ])
        Formio.createForm(document.getElementById('formNhapLuongGiaHDChucNang'),
            jsonForm,{}
        ).then(function(form) {
            formLuongGiaHDChucNang = form;
        });
    }

    function reloadDSLuongGiaHDChucNang(){
        var url = "cmu_getlist?url=" + convertArray([singletonObject.dvtt, thongtinhsba.thongtinbn.SOVAOVIEN, "CMU_GET_LUONGGIAHDCN"]);
        $.get(url).done(function(data){
            if (data && data.length > 0) {
                $("#data_luonggiahdchucnang").html(thongtinhsba.thongtinbn.TEN_PHONGBAN + ' - ' + data[0].NGAY_TAO_PHIEU);
                thongTinLuongGiaHDChucNangMoiNhat = data[0];
                if (data[0].KEYSIGN){
                    $('#edit_single_luonggiahdchucnang').css('visibility', 'hidden');
                    $('#delete_single_luonggiahdchucnang').css('visibility', 'hidden');
                }else{
                    $('#handle_icon_luonggiahdchucnang').css('visibility', 'unset');
                    $('#view_single_luonggiahdchucnang').css('visibility', 'unset');
                    $('#edit_single_luonggiahdchucnang').css('visibility', 'unset');
                    $('#delete_single_luonggiahdchucnang').css('visibility', 'unset');
                }
            } else  {
                $("#data_luonggiahdchucnang").html('Không có dữ liệu');
                $('#handle_icon_luonggiahdchucnang').css('visibility', 'hidden');
            }
        });
        $("#list_luonggiahdchucnang").jqGrid('setGridParam', {
            datatype: 'json',
            url: url
        }).trigger('reloadGrid')
        hideLoaderIntoWrapId("list_ttcs-bdcdct-wrap");
    }

    function themLuongGiaHDChucNang() {
        showSelfLoading("luonggiahdchucnang_luu");
        formLuongGiaHDChucNang.emit("checkValidity");
        if (!formLuongGiaHDChucNang.checkValidity(null, false, null, true)) {
            hideSelfLoading("luonggiahdchucnang_luu");
            return;
        }
        var actionUrl;
        var url;
        var dataSubmit = formLuongGiaHDChucNang.submission.data;
        actionUrl = "cmu_post";
        url = [
            singletonObject.dvtt,
            thongtinhsba.thongtinbn.SOVAOVIEN,
            thongtinhsba.thongtinbn.MA_BENH_NHAN,
            dataSubmit.VAN_DONG,
            dataSubmit.SINH_HOAT,
            dataSubmit.NHAN_THUC,
            dataSubmit.CHUC_NANG_KHAC,
            dataSubmit.THAM_GIA_HOAT_DONG,
            dataSubmit.MOI_TRUONG,
            dataSubmit.CA_NHAN,
            moment(dataSubmit.NGAY_TAO_PHIEU).format("DD/MM/YYYY"),
            singletonObject.userId,
            "CMU_LUONGGIAHDCN_INSERT"
        ];

        $.post(actionUrl, {
            url: url.join('```')
        }).done(function (data) {
            if(data > 0){
                var noidung = ["Số phiếu:"+ data]
                for (const key in dataSubmit) {
                    noidung.push(formLuongGiaHDChucNang.getComponent(key).label.trim(":") + ": " + getValueOfFormIO(formLuongGiaHDChucNang.getComponent(key)));
                }
                var dataLog = {
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: LOGHSBALOAI.LUONGGIAHDCN.KEY,
                    NOIDUNGBANDAU: "",
                    NOIDUNGMOI: noidung.join("; "),
                    USERID: singletonObject.userId,
                    ACTION: LOGHSBAACTION.INSERT.KEY,
                }
                luuLogHSBATheoBN(dataLog);
                notifiToClient('Green', 'Thêm phiếu thành công');
                $("#modalFormLuongGiaHDChucNang").modal("hide");
            } else {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }
        }).fail(function(error) {
            notifiToClient("Red",MESSAGEAJAX.ERROR);
        }).always(function() {
            hideSelfLoading("luonggiahdchucnang_luu");
            setTimeout(function(){
                reloadDSLuongGiaHDChucNang();
            })
        });
    }

    function updateLuongGiaHDChucNang() {
        showSelfLoading("luonggiahdchucnang_luu");
        formLuongGiaHDChucNang.emit("checkValidity");
        if (!formLuongGiaHDChucNang.checkValidity(null, false, null, true)) {
            hideSelfLoading("luonggiahdchucnang_luu");
            return;
        }
        var actionUrl;
        var url;
        var dataSubmit = formLuongGiaHDChucNang.submission.data;
        actionUrl = "cmu_post";
        url = [
            thongTinLuongGiaHDChucNangTruocChinhSua.ID,
            singletonObject.dvtt,
            dataSubmit.VAN_DONG,
            dataSubmit.SINH_HOAT,
            dataSubmit.NHAN_THUC,
            dataSubmit.CHUC_NANG_KHAC,
            dataSubmit.THAM_GIA_HOAT_DONG,
            dataSubmit.MOI_TRUONG,
            dataSubmit.CA_NHAN,
            moment(dataSubmit.NGAY_TAO_PHIEU).format("DD/MM/YYYY"),
            "CMU_LUONGGIAHDCN_UPDATE"
        ];
        $.post(actionUrl, {
            url: url.join('```')
        }).done(function (data) {
            if(data > 0){
                var noidungold = []
                var noidungnew = []
                var luutru = ""
                dataSubmit.NGAY_TAO_PHIEU = moment(dataSubmit.NGAY_TAO_PHIEU).format("DD/MM/YYYY")
                var diffObject = findDifferencesBetweenObjects(thongTinLuongGiaHDChucNangTruocChinhSua, dataSubmit);
                for (const key in diffObject) {
                    luutru = formLuongGiaHDChucNang.getComponent(key).label
                    noidungold.push(luutru.trim(":") + ": " + thongTinLuongGiaHDChucNangTruocChinhSua[key]);
                    noidungnew.push(luutru.trim(":") + ": " + getValueOfFormIO(formLuongGiaHDChucNang.getComponent(key)));
                }
                var dataLog = {
                    SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                    LOAI: LOGHSBALOAI.LUONGGIAHDCN.KEY,
                    NOIDUNGBANDAU: noidungold.join("; "),
                    NOIDUNGMOI: noidungnew.join("; "),
                    USERID: singletonObject.userId,
                    ACTION: LOGHSBAACTION.EDIT.KEY,
                }
                luuLogHSBATheoBN(dataLog);
                notifiToClient('Green', 'Cập nhật phiếu thành công');
                $("#modalFormLuongGiaHDChucNang").modal("hide");
            } else {
                notifiToClient("Red",MESSAGEAJAX.ERROR);
            }
        }).fail(function(error) {
            notifiToClient("Red",MESSAGEAJAX.ERROR);
        }).always(function() {
            hideSelfLoading("luonggiahdchucnang_luu");
            setTimeout(function(){
                reloadDSLuongGiaHDChucNang();
            })
        });
    }

    function instanceGridLuongGiaHDChucNang(){
        if (!$("#list_luonggiahdchucnang")[0].grid) {
            $("#list_luonggiahdchucnang").jqGrid({
                datatype: "local",
                loadonce: false,
                height: 100,
                width: null,
                shrinkToFit: false,
                colModel: [
                    {
                        name: "KYSO",
                        label: "Ký số",
                        align: 'left',
                        width: 100,
                        formatter: function (cellValue, options, rowData) {
                            if (rowData.KEYSIGN) {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: green">Đã ký</span>';
                            } else {
                                return '<span class="cellWithoutBackground" style="font-weight:bold ;color: red">Chưa ký</span>';
                            }
                        }
                    },
                    {label: 'ID',name: 'ID', index: 'ID', width: 50, align: 'center'},
                    {name: "VAN_DONG", label: "Vận động di chuyển", align: 'center', width: 150},
                    {name: "SINH_HOAT", label: "Chức năng sinh hoạt", align: 'center', width: 80},
                    {name: "NHAN_THUC", label: "Nhận thức, giao tiếp", align: 'center', width: 150, hidden: true},
                    {name: "CHUC_NANG_KHAC", label: "Chức năng khác", align: 'center', width: 150, hidden: true},
                    {name: "THAM_GIA_HOAT_DONG", label: "Tham gia hoạt động", align: 'center', width: 150, hidden: true},
                    {name: "MOI_TRUONG", label: "Yếu tố môi trường", align: 'center', width: 150, hidden: true},
                    {name: "CA_NHAN", label: "Yếu tố cá nhân", align: 'center', width: 150, hidden: true},
                    {name: "TENNGUOITHUCHIEN", label: "Người thực hiện", align: 'center', width: 250},
                    {label: 'Ngày tạo phiếu',name: 'NGAY_TAO_PHIEU', index: 'NGAY_TAO_PHIEU', width: 250, align: 'center'},
                    {name: "KEYSIGN", label: "KEYSIGN", align: 'center', width: 150, hidden: true},
                ],
                rowNum: 1000000,
                caption: "Danh sách phiếu lượng giá hoạt động chức năng và sự tham gia",
                onRightClickRow: function (id1) {
                    if (id1) {
                        var ret = getThongtinRowSelected("list_luonggiahdchucnang");
                        var items = {
                            "xem": {name: '<p><i class="fa fa-eye text-primary" aria-hidden="true"></i> Xem</p>'},
                        }
                        $.contextMenu('destroy', '#list_luonggiahdchucnang tr');
                        if (ret.KEYSIGN) {
                            items = {
                                ...items,
                                "huykyso": {name: '<p><i class="fa fa-key text-danger" aria-hidden="true"></i> Hủy ký số</p>'},
                            }
                        } else {
                            items = {
                                "kyso": {name: '<p><i class="fa fa-key text-success" aria-hidden="true"></i> Ký số</p>'},
                                ...items,
                                "sua": {name: '<i class="fa fa-pencil-square-o text-primary" aria-hidden="true"></i> Sửa</p>'},
                                "xoa": {name: '<p><i class="fa fa-trash-o text-danger" aria-hidden="true"></i> Xóa</p>'},
                            }
                        }
                        $.contextMenu({
                            selector: '#list_luonggiahdchucnang tr',
                            callback: function (key, options) {
                                var id = $("#list_luonggiahdchucnang").jqGrid('getGridParam', 'selrow');
                                var ret = $("#list_luonggiahdchucnang").jqGrid('getRowData', id);
                                var params = {
                                    magiay: ret.ID,
                                    mabenhnhan: thongtinhsba.thongtinbn.MA_BENH_NHAN,
                                    tennguoibenh: thongtinhsba.thongtinbn.TEN_BENH_NHAN,
                                    sobenhan: thongtinhsba.thongtinbn.SOBENHAN,
                                    sovaovien: thongtinhsba.thongtinbn.SOVAOVIEN,
                                    gioitinh: thongtinhsba.thongtinbn.GIOI_TINH,
                                    tuoi: thongtinhsba.thongtinbn.TUOI,
                                    khoa: thongtinhsba.thongtinbn.TEN_PHONGBAN,
                                    stt_benhan: thongtinhsba.thongtinbn.STT_BENHAN,
                                    stt_dotdieutri: thongtinhsba.thongtinbn.STT_DOTDIEUTRI,
                                }
                                var url = 'cmu_in_cmu_phieuluonggiahdchucnang?type=pdf&' + $.param(params);
                                if (key == "kyso") {
                                    thongTinLuongGiaHDChucNangTruocChinhSua = ret
                                    previewAndSignPdfDefaultModal({
                                        url: url,
                                        idButton: 'luonggiahdcn_kyso_action',
                                    }, function(){

                                    });
                                }
                                if (key == "huykyso") {
                                    // if(ret.BAC_SI != singletonObject.userId) {
                                    //     return notifiToClient("Red", MESSAGEAJAX.PERMISSION);
                                    // }
                                    confirmToClient("Bạn có chắc sẽ hủy ký số phiếu này?", function() {
                                        huykysoFilesign769("PHIEU_NOITRU_LUONGGIAHDCN", ret.ID, singletonObject.userId, singletonObject.dvtt,
                                            thongtinhsba.thongtinbn.SOVAOVIEN, thongtinhsba.thongtinbn.SOVAOVIEN_DT, -1, function(data) {
                                                reloadDSLuongGiaHDChucNang();
                                            })
                                    }, function () {

                                    })
                                }
                                if (key == "xem") {
                                    xemLuongGiaHDChucNang(ret);
                                }
                                if (key== "sua"){
                                    showUpdateLuongGiaHDChucNang(ret)
                                }
                                if (key == "xoa") {
                                    deleteLuongGiaHDChucNang(ret)
                                }
                            },
                            items: items
                        });
                    }
                }

            });
            $("#list_luonggiahdchucnang").jqGrid('filterToolbar', {stringResult: true, searchOnEnter: false, defaultSearch: "cn"});
        }
    }

    function xemLuongGiaHDChucNang(ret){
        var params = {
            ID: ret.ID,
        }
        getUrlLuongGiaHDCN(params).then(objReturn => {
            if (objReturn.isError == 0) {
                previewPdfDefaultModal(objReturn.url, 'preview_phieuluonggiahdchucnang');
            } else {
                notifiToClient("Red", objReturn.message);
            }
        }).catch(error => {
            notifiToClient("Red", error.message || "Lỗi không xác định");
        });
    }

    function showUpdateLuongGiaHDChucNang(ret){
        $("#modalFormLuongGiaHDChucNang").modal("show");
        $("#luonggiahdchucnang_luu").attr("data-action", "CAP_NHAT");
        addTextTitleModal('titleFormLuongGiaHDChucNang', "Phiếu lượng giá hoạt động chức năng và sự tham gia");
        thongTinLuongGiaHDChucNangTruocChinhSua = ret
        formLuongGiaHDChucNang.submission =  {
            data: {
                ...ret,
                NGAY_TAO_PHIEU:  (ret.NGAY_TAO_PHIEU? moment(ret.NGAY_TAO_PHIEU, ['DD/MM/YYYY']): moment()).toISOString(),
            }
        };
    }

    function deleteLuongGiaHDChucNang(ret){
        var maGiay = ret.ID;
        thongTinLuongGiaHDChucNangTruocChinhSua = ret
        confirmToClient("Bạn có chắc chắn muốn xóa phiếu này?", function() {
            var arr = [maGiay, singletonObject.dvtt]
            var url = "cmu_post_CMU_LUONGGIAHDCN_DELETE";
            $.post(url, {
                url: arr.join("```")
            }).done(function (data) {
                if (data === "1") {
                    var formData = { ...formLuongGiaHDChucNang.submission.data}
                    var noidung = ["Số phiếu:"+ thongTinLuongGiaHDChucNangTruocChinhSua.ID]
                    for (const key in formData) {
                        try {
                            var label = formLuongGiaHDChucNang.getComponent(key).label;
                            if (label) {
                                noidung.push(formLuongGiaHDChucNang.getComponent(key).label + ": " + getValueOfFormIO(formLuongGiaHDChucNang.getComponent(key)));
                            }
                        } catch (error) {
                            // console.log("Error: ", error);
                        }
                    }
                    var dataLog = {
                        SOVAOVIEN: thongtinhsba.thongtinbn.SOVAOVIEN,
                        LOAI: LOGHSBALOAI.LUONGGIAHDCN.KEY,
                        NOIDUNGBANDAU: noidung.join("; "),
                        NOIDUNGMOI: "",
                        USERID: singletonObject.userId,
                        ACTION: LOGHSBAACTION.DELETE.KEY,
                    }
                    luuLogHSBATheoBN(dataLog);                    reloadDSLuongGiaHDChucNang();
                    notifiToClient("Green", MESSAGEAJAX.DEL_SUCCESS)

                } else {
                    notifiToClient("Red", MESSAGEAJAX.ERROR)
                }
            }).fail(function() {
                notifiToClient("Red", MESSAGEAJAX.ERROR)
            })
        }, function () {

        })
    }

    // Bổ sung Mẫu chuẩn bị
    $("#mauChuanBiPhieuLuongGiaHDChucNang").click(function() {
        let element = $("#mau_danhsachmaujson_wrap");
        element.attr("function-add", 'insertMauCBPhieuLuongGiaHDChucNang');
        element.attr("function-chinhsua", 'editMauCBPhieuLuongGiaHDChucNang');
        element.attr("function-select", 'selectMauCBPhieuLuongGiaHDChucNang');
        element.attr("function-getdata", 'getdataMauCBPhieuLuongGiaHDChucNang');
        element.attr("function-validate", 'formioCBPhieuLuongGiaHDChucNangValidate');
        element.attr("data-key", 'MAUCBPHIEULUONGGIAHDCHUCNANG');
        $("#modalMauChungJSON").modal("show");
        $.loadDanhSachMauChungJSON('MAUCBPHIEULUONGGIAHDCHUCNANG')
    }); $.extend({
        insertMauCBPhieuLuongGiaHDChucNang: function () {
            generateFormMauCBPhieuLuongGiaHDChucNang({})
        },
        editMauCBPhieuLuongGiaHDChucNang: function (rowSelect) {
            let json = JSON.parse(rowSelect.NOIDUNG);
            let dataMau = {}
            json.forEach(function(item) {
                dataMau[item.key] = item.value
            })
            generateFormMauCBPhieuLuongGiaHDChucNang({
                ID: rowSelect.ID,
                TENMAU: rowSelect.TENMAU,
                ...dataMau
            })
        },
        selectMauCBPhieuLuongGiaHDChucNang: function (rowSelect) {
            let json = JSON.parse(rowSelect.NOIDUNG);
            json.forEach(function(item) {
                $(`#formNhapLuongGiaHDChucNang [name="data[${item.key}]"]`).val(item.value)
                formLuongGiaHDChucNang.data[item.key] = item.value
            })
            $("#modalMauChungJSON").modal("hide");
        },
        getdataMauCBPhieuLuongGiaHDChucNang: function () {
            let objectNoidung = [];
            getObjectMauCBPhieuLuongGiaHDChucNang().forEach(function(item) {
                if (item.key !== 'ID' && item.key !== 'TENMAU') {
                    objectNoidung.push({
                        "label": item.label,
                        "value": formioMauHSBA.submission.data[item.key],
                        "key": item.key,
                    })
                }
            })
            return {
                ID: formioMauHSBA.submission.data.ID,
                TENMAU: formioMauHSBA.submission.data.TENMAU,
                NOIDUNG: JSON.stringify(objectNoidung),
                KEYMAUCHUNG: 'MAUCBPHIEULUONGGIAHDCHUCNANG'
            };
        },
        formioCBPhieuLuongGiaHDChucNangValidate: function() {
            formioMauHSBA.emit("checkValidity");
            return formioMauHSBA.checkValidity(null, false, null, true);

        },
    });

    function generateFormMauCBPhieuLuongGiaHDChucNang(dataForm) {
        let jsonForm = getJSONObjectForm(getObjectMauCBPhieuLuongGiaHDChucNang());
        Formio.createForm(document.getElementById('formChiTietMauChungJSON'),
            jsonForm,{}
        ).then(function(form) {
            formioMauHSBA = form;
            formioMauHSBA.submission = { data: { ...dataForm }}
        });
    }

    function getObjectMauCBPhieuLuongGiaHDChucNang() {
        return [
            {
                "label": "ID",
                "key": "ID",
                "type": "textfield",
                others: {
                    hidden: true
                }
            },
            {
                "label": "Tên mẫu",
                "key": "TENMAU",
                "type": "textarea",
                validate: {
                    required: true
                },
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Vận động và di chuyển",
                "key": "VAN_DONG",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Chức năng sinh hoạt hàng ngày",
                "key": "SINH_HOAT",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Nhận thức, giao tiếp",
                "key": "NHAN_THUC",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Các chức năng khác",
                "key": "CHUC_NANG_KHAC",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Sự tham gia các hoạt động trong gia đình và xã hội",
                "key": "THAM_GIA_HOAT_DONG",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Yếu tố môi trường",
                "key": "MOI_TRUONG",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            },
            {
                "label": "Yếu tố cá nhân",
                "key": "CA_NHAN",
                "type": "textarea",
                others: {
                    "labelPosition": "left-left",
                    "labelWidth": 10
                }
            }
        ];
    }
})