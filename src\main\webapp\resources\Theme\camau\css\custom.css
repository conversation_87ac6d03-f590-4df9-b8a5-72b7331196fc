
.cyan-text{
    color: #0097A7;
}
.red-text {
    color:#FF5252;
}
.c-content-padding {padding: 7px}
.c-card-header {
    border-bottom: 1px solid #009688;
    text-transform: uppercase;
    font-family: Roboto,Helvetica Neue,sans-serif;
    padding: 7px;
    font-size: 15px;
}
.c-card-header .md-title{
    font-size: 17px;
}
.c-input-height {
    height: 35px
}
.c-sub-title {
    font-weight: bold;
    margin-top: 10px;
}
md-divider.c-dashed {
    border-top-style: dashed;
}
input.form-control:disabled{
    background-color: inherit;
    opacity: 1;
    border: none;
    border-bottom: 1px dashed #ccc;
}
input.form-control[readonly]{
    background-color: inherit;
    opacity: 1;
    border: none;
    border-bottom: 1px dashed #ccc;
}
.dash-bt-border {
    padding-bottom: 4px;
    border-bottom: 1px dashed #ccc;
}
.ui-widget-content {
    background: rgb(246, 248, 252) !important;
}
.card {
    border: 1px solid rgb(238, 238, 238) !important;
    border-radius: 0.5rem !important;
}
.dropdown-menu p {
    margin-bottom: 4px;
    cursor: pointer;
}

.dropdown-menu p:hover {
    color: #fff;
    background-color: #28a745
}

@keyframes spinner-border {
    to { transform: rotate(360deg); }
}

.spinner-border {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    vertical-align: text-bottom;
    border: 0.25em solid white;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border .75s linear infinite;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: 0.2em;
}

@keyframes spinner-grow {
    0% {
        transform: scale(0);
    }
    50% {
        opacity: 1;
    }
}

.spinner-grow {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    vertical-align: text-bottom;
    background-color: white;

    border-radius: 50%;
    opacity: 0;
    animation: spinner-grow .75s linear infinite;
}

.spinner-grow-sm {
    width: 1rem;
    height: 1rem;
}
.ui-widget {
    font-size: 13px !important;
}
.search-list {
    margin-bottom: 30px;
    width: auto;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    border-radius: 10px;
    overflow: hidden;
    margin-left: auto;
    margin-right: auto;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}
.search-list .nav-item {
    background-color: rgba(92,97,242,0.1);
}
.search-list li:first-child {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
}
.search-list li:last-child {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
}
.nav-pills .nav-link, .nav-pills .show>.nav-link {
    color: #007bff;
}
.wrap-white {
    background: white;
}
.ui-state-highlight, .ui-widget-content .ui-state-highlight, .ui-widget-header .ui-state-highlight {
    background: #f8da4e 50% 50% repeat-x !important;
}
.wrap-full {
    overflow-y: scroll;
    max-height: calc(100vh - 50px);
    height: calc(100vh - 50px);
}

.text-header-table {
    font-size: 12px;
}
.jqgroup b{
    font-size: 13px !important;
}
label.is-invalid {
    color: red;
    font-size: 11px;
}
.v-menu-tab .nav-link {
    margin-top: 6px;
    margin-bottom: 6px;
    border: 1px solid #007bff;
    background: #007bff12;
}
.nav-pills .nav-link.active, .nav-pills .show>.nav-link {
    color: #fff;
    background-color: #007bff;
}
.pt-2px {
    padding-top: 2px !important;
}
.input-group label.is-invalid {
    position: absolute;
    bottom: -23px;
    display: block;
}
.wrap-jqgrid .ui-jqgrid tr.jqgrow {
    font-size: 13px !important;
}
.visually-hidden {
    display: none;
}

.field-required:after {
    position: relative;
    z-index: 10;
}
.field-required:after, .tab-error::after {
    content: " *";
    color: #eb0000;
}
.hsba-tabs-wrap .card-header {
    background: #007bff12;
    border-bottom: 1px solid #007bff;
}
.hsba-tabs-wrap .card {
    border: 1px solid #007bff !important;
}
.hsba-tabs-wrap .nav-link.active {
    color: white;
    border-color: #007bff;
    background-color: #007bff;
}

.modal-header--sticky {
    position: sticky;
    top: 0;
    background-color: inherit;
    z-index: 1055;
}

.modal-footer--sticky {
    position: sticky;
    bottom: 0;
    background-color: inherit;
    z-index: 1055;
}
.wrap-select2 {
    position: relative;
}
.wrap-select2 label.is-invalid {
    position: absolute;
    bottom: 0px;
}
.fa-question-sign:before {
    content: "\f29c";

}
.bi::before, [class*=" bi-"]::before, [class^=bi-]::before {
    font-family: inherit !important;
}
.context-menu-list {
    background: #fff !important;
    border: none !important;
}
.context-menu-item {
    background: white !important;
}
.context-menu-item.hover {
    cursor: pointer;
    background-color: #007bff78 !important;

}
.fa-plus:before {
    content: "\f196" !important;
}
.fa-calendar:before {
    content: "\f073" !important;
}
.background_new {
    background-color: #f5f5f5;
}
.minH100 {
    min-height: 100%;
}
.header {
    background-color: #f6f8fc
}
#table_header {
    background-color: #f6f8fc !important;
}
.card-giuong {
    border: 1px solid #007bff;
    border-radius: 5px;
    padding: 6px;
    height: 80px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
}
.card-giuong-active {
    background: #007bff1c;
}
.btn-xemba {
    border-radius: 0px 5px 5px 0px;
}
.wrap-ylenhhuy {
    padding: 8px;
    border: 1px solid #28a745;
    border-radius: 4px;
}
.card-header-custom {
    color: #fff;
    background-color: #007bffbf;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
}
.glyphicon {
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.glyphicon-backward:before {
    content: "\f04a";
}
.glyphicon-step-backward:before {
    content: "\f048";
}
.glyphicon-forward:before {
    content: "\f04e";
}
.glyphicon-step-forward:before {
    content: "\f051";
}
.formio-component-form .form-control {
    height: calc(1.8125rem + 2px);
    padding: 0.25rem 0.5rem;
    font-size: .875rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

.formio-component-form .input-group-text {
    height: calc(1.8125rem + 2px);
    padding: 0.25rem 0.5rem;
    font-size: .875rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}


.hsba-tabs-wrap .card-title {
    color: #007bff;
    font-weight: 600;
}
.hsba-tabs-wrap .formio-collapse-icon {
    color: #007bff !important;
    position: relative;
    top: -1px;
}
.hsba-tabs-wrap .card-body {
    background: #f5f5f5;
    border-radius: 12px;
}
#hsbaFormTrang2 .formio-error-wrapper {
    background-color: transparent;
    border-color: transparent;
    padding: 0;
}
#hsbaFormTrang3 .formio-error-wrapper {
    background-color: transparent;
    border-color: transparent;
    padding: 0;
}
.form-group-flex {
    display: flex;
    flex-direction: row;
    align-items: center; /* Căn giữa theo chiều dọc (vertical alignment) */
    margin-bottom: 10px;
}

.form-group-flex label {
    margin-right: 10px; /* Khoảng cách giữa label và input */
    flex-shrink: 0; /* Không cho phép thu nhỏ label */
    text-align: left;
    min-width: 100px;
}

/*input[type="checkbox"], input[type="radio"]{*/
/*    margin-right: 10px;*/
/*    height: 20px;*/
/*    width: 20px;*/
/*    margin-bottom: 10px;*/
/*}*/
.line-horizontal {
    margin-top: -1em;
    margin-bottom: 0.5em;
}
#ghms-table-thuoc .table td, .table th {
    border-top: unset;
}
#ghms-table-thuoc  .table {
    margin-bottom: 0px !important;
}
.choices__item.choices__item--selectable {
    text-wrap: wrap;
}
.choices__list--dropdown .choices__item--selectable, .choices__list[aria-expanded] .choices__item--selectable {
    padding-right: 30px !important;
}
.pt-6px {
    padding-top:6px;
}
.input-line {
    border-width: 0px 0px 0px 0px;
    border-bottom: 1px solid;
    border-color: #adadad;
    background-color: #ffffff;
    color: #141c80;
    border-style: dashed;
    /*font-weight: bold;*/
    margin-left: 10px;
    /*font-family: Verdana, Arial, Helvetica, sans-serif;*/
    /*width: calc(60%+ 8 * ((20vw - 320px) / 960));*/
}

input.input-line:focus{
    outline: 0px;
    outline-style:outset;
    /*box-shadow: 0px 1px #77d5f7;*/
}

.form-group-flex-vba {
    display: flex;
    flex-direction: row;
    align-items: center; /* Căn giữa theo chiều dọc (vertical alignment) */
    /*flex-wrap: wrap;*/
}

.form-group-flex-vba label {
    margin-right: 10px; /* Khoảng cách giữa label và input */
    flex-shrink: 0; /* Không cho phép thu nhỏ label */
    text-align: left;
    min-width: fit-content;
}

.ml-100 {
    margin-left: 100px;
}
.ml-10 {
    margin-left: 10px;
}
.ml-15 {
    margin-left: 15px;
}
.checkbox-radio-vba {
    margin-right: 5px;
    height: 20px;
    width: 20px;
    margin-bottom: 10px;
}

.formio-error-wrapper {
    background-color: transparent;
    border-color: transparent;
    padding: 0;
}
.formio-error-wrapper {
    background-color: transparent;
    border-color: transparent;
    padding: 0;
}
.pr-20{
    padding-right: 20px;
}

.formio-component-form .textarea-h-100 .form-control {
    min-height: 100% !important;
}

.formio-component-htmlelement-mb-0 label {
    padding-top: calc(0.375rem + 1px);
    padding-bottom: calc(0.375rem + 1px);
    margin-bottom: 0;
    font-size: inherit;
    line-height: 1.5;
}

.formio-component-dataGrid .formio-button-remove-row .bi-x-circle:before {
    content: "Xoá";
    visibility: visible;
}
.element-center {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding-top: 24px;
}

#lanphauthuatEkipFormioWrap .choices__list--dropdown .choices__list,
#lanphauthuatEkipFormioWrap.choices__list[aria-expanded] .choices__list {
    max-height: 230px !important;
}

.choices__list {
    font-weight: 600;
}
.choices__item:hover {
    background-color: #007bff !important; /* change this to your preferred color */
    color: white; /* change this to your preferred color */
}
.element-center-left {
    display: flex;
    align-items: center;
    height: 100%;
    padding-top: 24px;
}
.kblabel{
    margin-left: 0.5em;
    display: inline;
    padding: .2em .6em .3em;
    font-weight: bold;
    font-size: 75%;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em;
}
.kblabel-conthuoc {
    background-color: #9900FF;
}
.kblabel-uutien {
    background-color: #ff0000;
}
.kblabel-bant {
    background-color: #009900;
}
.kblabel-vp {
    background-color: #800000;
}
.kblabel-ksk {
    color: black;
    background-color: #ffff00;
}
.kblabel-chuyenphong {
    color: black;
    background-color: #00FFFF;
}
.kblabel-chokq-cls {
    background-color: #0000ff;
}
.kblabel-patient-vncare {
    background-color: #FFA500;
}
.input-text-bold .form-control {
    font-weight: bold;
}
.formio-css-suffix .input-group div[ref="suffix"] {
    height: calc(1.8125rem + 2px);
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 0;
    font-size: inherit;
}

.formio-css-collap div[aria-expanded="false"] {
    border-bottom: none;
    border-radius: 5px;
}

.formio-css-datetime .input-group input[type="text"] {
    border-top-left-radius: 0.2rem;
    border-bottom-left-radius: 0.2rem;
}

.formio-css-card-body .card-body {
    background: none;
}

.formio-css-textarea textarea[type="text"] {
    overflow: hidden;
    min-height: calc(1.8125rem + 2px) !important;
}

.formio-css-selection .selection {
    border-radius: .35rem;
}

.formio-css-p p {
    padding-top: calc(.375rem + 1px);
    padding-bottom: calc(.375rem + 1px);
    margin-bottom: 0;
    font-size: inherit;
    line-height: 1.5;
}

.formio-css-disable-input input.form-control:disabled {
    background-color: #e9ecef;
    opacity: 1;
    border: 1px solid #ced4da;
}

.formio-css-disable-none-bottom input.form-control:disabled {
    border-bottom: none;
}

.formio-css-grid .datagrid-table,
.formio-css-grid .datagrid-table td,
.formio-css-grid .datagrid-table th {
    border: 1px solid #ced4da !important;
    font-weight: normal;
}

.formio-css-grid .mb-2 {
    margin-bottom: 0 !important;
}

.formio-css-grid-no-header .datagrid-table thead {
    display: none;
}

.formio-css-suffix .input-group div[ref="suffix"] {
    height: calc(1.8125rem + 2px);
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 0;
    font-size: inherit;
}

.formio-css-prefix .input-group div[ref="prefix"] {
    height: calc(1.8125rem + 2px);
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: 0;
    border-left: 1px solid #ced4da;
    font-size: inherit;
}

.formio-css-input-text-center input {
    text-align: center;
}

.formio-css-no-margin {
    margin-bottom: unset !important;
}

.formio-css-not-display,
.formio-css-disable-datetime input[disabled] ~ .input-group-text {
    display: none !important;
}

.formio-css-baseline [ref^="column-"] {
    align-self: baseline;
}

#modalLanChamSocCap1ChiTiet .datagrid-overflow-visible .datagrid-table, #modalLanChamSocCap2ChiTiet .datagrid-overflow-visible .datagrid-table {
    overflow: visible !important;
}
#modalLanChamSocCap1ChiTiet .formio-component-datagrid, #modalLanChamSocCap2ChiTiet .formio-component-datagrid {
    overflow-x: unset !important;
    overflow-y: unset !important;
}

.select2-container--open .select2-dropdown--below {
    z-index: 9999;
    display: block;
}

#phieuKiemTraBenhAn .header-content .datagrid-table thead tr th {
    vertical-align: top;
    align-items: center;
    text-align: center;
}

#phieuKiemTraBenhAn .no-bottom .datagrid-table,
#phieuKiemTraBenhAn .no-bottom .datagrid-table td,
#phieuKiemTraBenhAn .no-bottom .datagrid-table th {
    border-bottom: none !important;
    border-top: 1px solid #ced4da !important;
    border-left: 1px solid #ced4da !important;
    border-right: 1px solid #ced4da !important;
    font-weight: normal;
}

#phieuKiemTraBenhAn .no-top-bottom .datagrid-table,
#phieuKiemTraBenhAn .no-top-bottom .datagrid-table td,
#phieuKiemTraBenhAn .no-top-bottom .datagrid-table th {
    border-bottom: none !important;
    border-top: none !important;
    border-left: 1px solid #ced4da !important;
    border-right: 1px solid #ced4da !important;
    font-weight: normal;
}

#phieuKiemTraBenhAn .resize-col tbody td:nth-child(1) {
    width: 10%;
}
#phieuKiemTraBenhAn .resize-col tbody td:nth-child(2) {
    width: 60%;
}
#phieuKiemTraBenhAn .resize-col tbody td:nth-child(3) {
    width: 10%;
}
#phieuKiemTraBenhAn .resize-col tbody td:nth-child(4) {
    width: 10%;
}
#phieuKiemTraBenhAn .resize-col tbody td:nth-child(5) {
    width: 10%;
}
