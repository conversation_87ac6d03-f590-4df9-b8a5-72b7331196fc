<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:task="http://www.springframework.org/schema/task"
       xmlns:rabbit="http://www.springframework.org/schema/rabbit" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
      http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd
      http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-4.0.xsd
      http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
      http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd
      http://www.springframework.org/schema/rabbit http://www.springframework.org/schema/rabbit/spring-rabbit.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd">
    <aop:aspectj-autoproxy />
    <context:component-scan base-package="SQLQuery"/>
    <context:component-scan base-package="vncare.**"/>
    <context:component-scan base-package="config"/>
    <context:component-scan base-package="KhamBenhOnLine"/>
    <context:component-scan base-package="API_Lienthong.**"/>
    <context:component-scan base-package="CallAPIDialog"/>
    <context:component-scan base-package="VSC.ytcs.BAOCAO_YTCS.*"/>
    <context:component-scan base-package="congdulieuKSK"/>
    <context:component-scan base-package="VSC.ytcs.YTCS_API"/>
    <context:component-scan base-package="VSC.ytcs.TT37"/>
    <context:component-scan base-package="VSC.ytcs.HOSOSUCKHOE_HMIS"/>
    <context:component-scan base-package="lichtruc"/>
    <context:component-scan base-package="BaoCao7980"/>
    <context:component-scan base-package="RabbitMQ"/>
    <context:component-scan base-package="RabbitMQ.dao"/>
    <context:component-scan base-package="PHANQUYEN_DSBENHNHAN_NOITRU"/>
    <context:component-scan base-package="GIUONGNGOAITRU"/>
    <context:component-scan base-package="CONNECTWAREHOUSE"/>
    <context:component-scan base-package="BAOCAO_BHPVI"/>
    <context:component-scan base-package="WS_CONG_TTTT"/>
    <context:component-scan base-package="DANHMUC_MAY_XN_CDHA_TTPT"/>
    <context:component-scan base-package="DANHMUC_HESO_NGAY_AP_DUNG"/>
    <context:component-scan base-package="ris.connector.client"/>
    <context:component-scan base-package="ris.connector.client.authentication"/>
    <context:component-scan base-package="ris.connector.client.client"/>
    <context:component-scan base-package="ris.connector.client.dto"/>
    <context:component-scan base-package="ris.connector.client.dto.request"/>
    <context:component-scan base-package="ris.connector.client.dto.response"/>
    <context:component-scan base-package="ris.connector.client.exception"/>
    <context:component-scan base-package="ris.connector.client.util"/>
    <context:component-scan base-package="ris.connector.client.viewimages"/>
    <context:component-scan base-package="check_dangnhap_ris"/>
    <context:component-scan base-package="VSC.ytcs.tiemchung.APIKehoachTiemChung"/>
    <context:component-scan base-package="io.swagger.client"/>
    <context:component-scan base-package="tiepnhan"/>
    <context:component-scan base-package="tts"/>
    <context:component-scan base-package="tts.*"/>
    <context:component-scan base-package="nhanvien"/>
    <context:component-scan base-package="dangnhap"/>
    <context:component-scan base-package="chucvu"/>
    <context:component-scan base-package="chucdanh"/>
    <context:component-scan base-package="khambenh"/>
    <context:component-scan base-package="thamsohethong"/>
    <context:component-scan base-package="duoc"/>
    <context:component-scan base-package="ttpt_vatlytrilieu"/>
    <context:component-scan base-package="xetnghiem"/>
    <context:component-scan base-package="chandoanhinhanh"/>
    <context:component-scan base-package="xuatthuocvattu"/>
    <context:component-scan base-package="phanquyen"/>
    <context:component-scan base-package="vienphi"/>
    <context:component-scan base-package="baocao"/>
    <context:component-scan base-package="dieutrichuyenkhoa"/>
    <context:component-scan base-package="capnhatcanlamsang"/>
    <context:component-scan base-package="dichvu"/>
    <context:component-scan base-package="lichsusudung"/>
    <context:component-scan base-package="tiepnhannoitru"/>
    <context:component-scan base-package="khambenhnoitru"/>
    <context:component-scan base-package="thietlapdonvi"/>
    <context:component-scan base-package="trogiup"/>
    <context:component-scan base-package="phanquyentruycapdonvi"/>
    <context:component-scan base-package="DANH_MUC_HOI_CHAN"/>
    <context:component-scan base-package="upload"/>
    <context:component-scan base-package="baocao_chung"/>
    <context:component-scan base-package="Noitiepnhan"/>
    <context:component-scan base-package="tintuc"/>
    <context:component-scan base-package="dmchuanbenhvien"/>
    <context:component-scan base-package="DANH_MUC_HOI_CHAN"/>
    <context:component-scan base-package="dmhethongquanly"/>
    <context:component-scan base-package="BaocaoBHXH"/>
    <context:component-scan base-package="BaocaoSYT"/>
    <context:component-scan base-package="mausieuam"/>
    <context:component-scan base-package="maunoisoi"/>
    <context:component-scan base-package="dmdonvi"/>
    <context:component-scan base-package="dmphongban"/>
    <context:component-scan base-package="Khamsuckhoe"/>
    <context:component-scan base-package="dmchuyenvien"/>
    <context:component-scan base-package="dmBVthuocchuan"/>
    <context:component-scan base-package="dmthamsodonvi"/>
    <context:component-scan base-package="Baocaochotsolieu"/>
    <context:component-scan base-package="noitruvienphi"/>
    <context:component-scan base-package="Baocaonoitru"/>
    <context:component-scan base-package="noitruduoc"/>
    <context:component-scan base-package="dmphongkham"/>
    <context:component-scan base-package="vnptpay.**"/>
    <context:component-scan base-package="hosobenhan"/>
    <context:component-scan base-package="khoaphongsudungkho"/>
    <context:component-scan base-package="khoitaodonvi"/>
    <context:component-scan base-package="Example"/>
    <context:component-scan base-package="dm_giuong"/>
    <context:component-scan base-package="exportxml"/>
    <context:component-scan base-package="phuthu"/>
    <context:component-scan base-package="angiang"/>
    <context:component-scan base-package="hosobenhan_ngoaitru"/>
    <context:component-scan base-package="toathuocmau"/>
    <context:component-scan base-package="baocao_hungyen"/>
    <context:component-scan base-package="baocao_khaibao_sudung"/>
    <context:component-scan base-package="thongbao"/>
    <context:component-scan base-package="dmgoidichvumau"/>
    <context:component-scan base-package="haiphong"/>
    <context:component-scan base-package="kiengiang"/>
    <context:component-scan base-package="daklak"/>
    <context:component-scan base-package="dmdc"/>
    <context:component-scan base-package="danhmuc.**"/>
    <context:component-scan base-package="SaiGon"/>
    <context:component-scan base-package="quangtri"/>
    <context:component-scan base-package="haugiang"/>
    <context:component-scan base-package="binhduong"/>
    <context:component-scan base-package="binhphuoc"/>
    <context:component-scan base-package="binhphuoc.noitruduoc"/>
    <context:component-scan base-package="nghean"/>
    <context:component-scan base-package="camau"/>
    <context:component-scan base-package="danhmuc"/>
    <context:component-scan base-package="bte"/>
    <context:component-scan base-package="BenTre"/>
    <context:component-scan base-package="vungtau"/>
    <context:component-scan base-package="DANH_MUC_MAU"/>
    <context:component-scan base-package="ThuocBaoChe"/>
    <context:component-scan base-package="CHUYENNGAY_THANH_TOAN_BHYT"/>
    <context:component-scan base-package="baocaoxuatnhapthuoc"/>
    <!-- VNPT Soc Trang -->
    <context:component-scan base-package="soctrang"/>
    <context:component-scan base-package="soctrang.Khambenhnoitru"/> <!-- VNPT Soc Trang -->
    <!--<context:component-scan base-package="soctrang.baocao"/>--> <!-- VNPT Soc Trang -->
    <context:component-scan base-package="soctrang.noitruduoc"/> <!-- VNPT Soc Trang -->
    <!-- VNPT Soc Trang -->
    <!-- begin VSC base-packet-->
    <context:component-scan base-package="hosobenhan_noitru"/>
    <context:component-scan base-package="dm_dantoc"/>
    <context:component-scan base-package="dm_nghenghiep"/>
    <context:component-scan base-package="dm_phuongxa"/>
    <context:component-scan base-package="dmThonXom"/>
    <context:component-scan base-package="dm_quanhuyen"/>
    <context:component-scan base-package="dm_tinhthanh"/>
    <context:component-scan base-package="VSC.benhan"/>
    <context:component-scan base-package="VSC.benhan_truyennhiem"/>
    <context:component-scan base-package="VSC.benhan_huyethoc_truyenmau"/>
    <context:component-scan base-package="VSC.benhan_huyethoc_truyenmau.khambenh"/>
    <context:component-scan base-package="VSC.benhan_huyethoc_truyenmau.kbcq"/>
    <context:component-scan base-package="VSC.benhan_huyethoc_truyenmau.ttba"/>
    <context:component-scan base-package="Kiemtrabaocaongoaitru"/>
    <context:component-scan base-package="Hosobenhanngoaitru"/>
    <context:component-scan base-package="thao"/>
    <context:component-scan base-package="bacgiang"/>
    <context:component-scan base-package="vinhlong"/>
    <context:component-scan base-package="dm_phanquyen_nvdc"/>
    <context:component-scan base-package="hatinh"/>
    <context:component-scan base-package="langson"/>
    <context:component-scan base-package="thaibinh"/>
    <context:component-scan base-package="binhdinh"/>
    <context:component-scan base-package="VSC.jreport"/>
    <context:component-scan base-package="VSC.AjaxJson"/>
    <context:component-scan base-package="importFromFile"/>
    <context:component-scan base-package="qldiaphuong"/>
    <context:component-scan base-package="qldiaphuongmoi"/>
    <context:component-scan base-package="lis"/>
    <context:component-scan base-package="Chotsolieu"/>
    <context:component-scan base-package="ImportXLS"/>
    <context:component-scan base-package="Hethongbatso1080"/>
    <context:component-scan base-package="KhaibaoDM"/>
    <context:component-scan base-package="dmtheBHYT"/>
    <!-- Vsc Ytcs -->
    <context:component-scan base-package="VSC.ytcs.danso"/>
    <context:component-scan base-package="VSC.ytcs.danhmuc"/>
    <context:component-scan base-package="VSC.ytcs.khambenh"/>
    <context:component-scan base-package="VSC.ytcs.benhnhan"/>
    <context:component-scan base-package="VSC.ytcs.tiemchung"/>
    <context:component-scan base-package="VSC.ytcs.benhnhan"/>
    <context:component-scan base-package="VSC.ytcs.tiemchung"/>
    <context:component-scan base-package="VSC.ytcs.chamsocsuckhoe.tntt"/>
    <context:component-scan base-package="VSC.ytcs.benhnhanlao"/>
    <context:component-scan base-package="VSC.ytcs.chamsocsuckhoe.qlbnkln"/>
    <context:component-scan base-package="VSC.ytcs.chamsocsuckhoe.qlbnln"/>
    <context:component-scan base-package="VSC.ytcs.chamsocsuckhoe.qlbnln2"/>
    <context:component-scan base-package="VSC.ytcs.chamsocsuckhoe.hiv"/>
    <context:component-scan base-package="VSC.ytcs.chamsocsuckhoe.huyethoc"/>
    <context:component-scan base-package="VSC.ytcs.chamsocsuckhoe.benhtamthan"/>
    <context:component-scan base-package="VSC.ytcs.chamsocsuckhoe.quanlycando"/>
    <context:component-scan base-package="VSC.ytcs.chamsocsuckhoe.vitamina"/>
    <!--    <context:component-scan base-package="VSC.ytcs.benhlaothongtin"/>-->
    <context:component-scan base-package="VSC.ytcs.ttgdsk"/>
    <context:component-scan base-package="VSC.ytcs.chamsocsuckhoe.benhsotret"/>
    <context:component-scan base-package="VSC.ytcs.chamsocsuckhoe.tuvong"/>
    <context:component-scan base-package="VSC.ytcs.baocao"/>
    <context:component-scan base-package="VSC.ytcs.dichvu_dc"/>
    <context:component-scan base-package="VSC.ytcs.duoc"/>
    <context:component-scan base-package="VSC.ytcs.hethong"/>
    <context:component-scan base-package="VSC.ytcs.hethong.phanquyen"/>
    <context:component-scan base-package="VSC.ytcs.antoanthucpham"/>
    <context:component-scan base-package="VSC.ytcs.DichVuKhamDV"/>
    <context:component-scan base-package="VSC.ytcs.UongVitamin"/>
    <context:component-scan base-package="VSC.ytcs.quytrinhHIV"/>
    <context:component-scan base-package="CHUYEN_BHYT_KBHYT"/>
    <context:component-scan base-package="DANH_MUC_DOITUONG_BC"/>
    <context:component-scan base-package="DANHMUC_GIA_APDUNG"/>
    <context:component-scan base-package="Xuatnhapton_chitiet"/>
    <context:component-scan base-package="DynamicReport"/>
    <context:component-scan base-package="vinhphuc"/>
    <context:component-scan base-package="lis"/>
    <context:component-scan base-package="dm_benh_ly"/>
    <context:component-scan base-package="version"/>
    <context:component-scan base-package="Lichsusudungthuoc"/>
    <context:component-scan base-package="chuyentuyenSXH"/>
    <context:component-scan base-package="tuyenhai"/>
    <context:component-scan base-package="dmhoidongkiemnhap"/>
    <context:component-scan base-package="HoaDonDienTu"/>
    <context:component-scan base-package="HOSOSUCKHOE"/>
    <context:component-scan base-package="HosobenhanVHM"/>
    <context:component-scan base-package="visinh.rest"/>
    <context:component-scan base-package="visinh.dao"/>
    <context:component-scan base-package="dm_checkbox_cdha"/>
    <context:component-scan base-package="PhacDoDieuTri"/>
    <context:component-scan base-package="luutruhsba"/>
    <context:component-scan base-package="gdyk"/>
    <context:component-scan base-package="namdinh"/>
    <context:component-scan base-package="VSC.ytcs.chamsocsuckhoe.phong"/>
    <context:component-scan base-package="VSC.ytcs.chamsocsuckhoe.treemtiepxuc_inh"/>
    <context:component-scan base-package="com.vnpt.signservice.client"/>
    <context:component-scan base-package="SignPDF"/>
    <!-- Vsc Ytcs -->
    <!-- end VSC base-packet-->
    <!--tuyenhai-->
    <context:component-scan base-package="L2_BanSoKhamBenh"/>
    <context:component-scan base-package="binhphuoc.L2"/>
    <context:component-scan base-package="BASanKhoa"/>
    <context:component-scan base-package="binhphuoc.TreSoSinh"/>
    <context:component-scan base-package="dmhoidongkiemnhap"/>
    <context:component-scan base-package="l2"/>
    <context:component-scan base-package="setting"/>
    <context:component-scan base-package="l2.duoc"/>
    <context:component-scan base-package="DmTuongTacThuoc"/>
    <context:component-scan base-package="phanquyen.duocvattu"/>
    <context:component-scan base-package="dm_map_cdha_mota"/>
    <context:component-scan base-package="dinhmuc"/>
    <context:component-scan base-package="danhmuc"/>
    <context:component-scan base-package="lichhenkhambenh"/>
    <context:component-scan base-package="SonLa"/>
    <context:component-scan base-package="lamdong"/>
    <context:component-scan base-package="quanlydongmau"/>
    <context:component-scan base-package="angiang"/>
    <context:component-scan base-package="SoTayBacSi"/>
    <context:component-scan base-package="baocaoCLSLaoCai"/>
    <context:component-scan base-package="apihis_labsoft"/>
    <!-- TGG-->
    <context:component-scan base-package="ThongKeHoatDongKCB"/>
    <context:component-scan base-package="BaoCheThuoc"/>
    <context:component-scan base-package="duoc_v20_client.*"/>
    <!-- TGG-->
    <context:component-scan base-package="MauDienBien"/>
    <context:component-scan base-package="hosobenhan_dientu"/>
    <context:component-scan base-package="template"/>
    <bean id="q_duyetphieuDAO" class="quangtri.qti_duyetphieuDAOImp"/>
    <context:annotation-config/>
    <context:component-scan base-package="giaychungsinh"/>
    <context:component-scan base-package="mauphieuin"/>
    <context:component-scan base-package="logAction"/>
    <context:component-scan base-package="VoBenhAnNoiTru"/>
    <context:component-scan base-package="socket"/>
    <context:component-scan base-package="smartca.**"/>
    <context:component-scan base-package="loghethong.controller"/>
    <context:component-scan base-package="donthuocdientu"/>
    <context:component-scan base-package="bieudochuyenda"/>
    <context:component-scan base-package="PhaThuoc"/>
    <bean
            class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="prefix">
            <value>/WEB-INF/pages/</value>
        </property>
        <property name="suffix">
            <value>.jsp</value>
        </property>
    </bean>
    <bean id="VoBenhAnNoiTruDAO" class="VoBenhAnNoiTru.VoBenhAnNoiTruDAOImp"/>
    <bean id="HsbaDienTuDAO" class="hosobenhan_dientu.HsbaDienTuDAOImp"/>
    <bean id="KSKDao" class="congdulieuKSK.KSKDaoImp"/>
    <bean id="LienThongKhoDuocTuyenTrenDAO" class="API_Lienthong.Service.LienThongKhoDuocTuyenTrenDAOImp"/>
    <bean id="ApiKhamBenhOnlineDAO" class="KhamBenhOnLine.ApiKhamBenhOnLineDAOImp"/>
    <bean id="Check_dangnhap_risDAO" class="check_dangnhap_ris.Check_dangnhap_risDAOImp"/>
    <bean id="bgg_baocaotamungDAO" class="bacgiang.bgg_baocaotamungDAOImp"/>
    <bean id="hoadonbanhangDAO" class="binhphuoc.hoadonbanhangDAOImp"/>
    <bean id="Body" class="io.swagger.client.model.Body"/>
    <bean id="TiKhonApi" class="io.swagger.client.api.TiKhonApi"/>
    <bean id="ITngApi" class="io.swagger.client.api.ITngApi"/>
    <bean id="LchSTimApi" class="io.swagger.client.api.LchSTimApi"/>
    <bean id="NVHnhChnhApi" class="io.swagger.client.api.NVHnhChnhApi"/>
    <bean id="VacxinApi" class="io.swagger.client.api.VacxinApi"/>
    <bean id="DnTcApi" class="io.swagger.client.api.DnTcApi"/>
    <bean id="CSTimChngApi" class="io.swagger.client.api.CSTimChngApi"/>
    <bean id="ApiClient" class="io.swagger.client.ApiClient"/>
    <bean id="ToKenApi_V20" class="io.swagger.client.api_v20.ToKenApi_V20"/>
    <bean id="ToKenApi_Dhis2" class="io.swagger.client.api_dhis2.ToKenApi_Dhis2"/>
    <bean id="Api_Ytcs_Dhis2DAO" class="VSC.ytcs.YTCS_API.Api_Ytcs_Dhis2DAOImp"/>
    <bean id="Api_Ytcs_V20DAO" class="VSC.ytcs.YTCS_API.Api_Ytcs_V20DAOImp"/>
    <bean id="LchSTimApi_V20" class="io.swagger.client.api_v20.LchSTimApi_V20"/>
    <bean id="hoidongkiemnhapDAO" class="dmhoidongkiemnhap.hoidongkiemnhapDAOImp"/>
    <bean id="DinhMucNhomVatTuDAO" class="dinhmuc.DinhMucNhomVatTuDAOImp"/>
    <bean id="PhacDoDieuTriDAO" class="PhacDoDieuTri.PhacDoDieuTriDAOImp"/>
    <bean id="ChotsolieuDAO" class="Chotsolieu.ChotsolieuDAOImp"/>
    <bean id="nhanvienDAO" class="nhanvien.NhanvienDAOImp"/>
    <bean id="ExDAO" class="Example.ExDAOImp"/>
    <bean id="userDAO" class="dangnhap.UserDAOImp"/>
    <bean id="chucdanhDAO" class="chucdanh.ChucdanhDAOImp"/>
    <bean id="chucvuDAO" class="chucvu.ChucvuDAOImp"/>
    <bean id="tiepnhanDAO" class="tiepnhan.TiepnhanDAOImp"/>
    <bean id="khambenhDAO" class="khambenh.KhambenhDAOImp"/>
    <bean id="ttptDAO" class="ttpt_vatlytrilieu.TTPTDAOImp"/>
    <bean id="xetnghiemDAO" class="xetnghiem.XetnghiemDAOImp"/>
    <bean id="cdhaDAO" class="chandoanhinhanh.CDHADAOImp"/>
    <bean id="phanquyenDAO" class="phanquyen.PhanquyenDAOImp"/>
    <bean id="loaiduocDAO" class="duoc.loaiduocDAOImp"/>
    <bean id="khoduocDAO" class="duoc.khoduocDAOImp"/>
    <bean id="chuyenkhoDAO" class="duoc.chuyenkhoDAOImp"/>
    <bean id="nghiepvuduocDAO" class="duoc.nghiepvuduocDAOImp"/>
    <bean id="nhomduocDAO" class="duoc.nhomduocDAOImp"/>
    <bean id="nuocsxDAO" class="duoc.nuocsxDAOImp"/>
    <bean id="nhaccDAO" class="duoc.nhaccDAOImp"/>
    <bean id="nhasxDAO" class="duoc.nhasxDAOImp"/>
    <bean id="vattuDAO" class="duoc.vattuDAOImp"/>
    <bean id="nhapkhotunhaccDAO" class="duoc.nhapkhotunhaccDAOImp"/>
    <bean id="duyetphieuDAO" class="duoc.duyetphieuDAOImp"/>
    <bean id="xuatthuocDAO" class="xuatthuocvattu.XuatthuocDAOImp"/>
    <bean id="vienphiDAO" class="vienphi.VienphiDAOImp"/>
    <bean id="baocaoxuatduocDAO" class="baocao.BaocaoxuatduoctheonghiepvuDAOImp"/>
    <bean id="chuyentuyenDAO" class="khambenh.ChuyentuyenDAOImp"/>
    <bean id="xemtonkhoduocDAO" class="duoc.XemtonkhoduocDAOImp"/>
    <bean id="lichsukhamDAO" class="khambenh.LichsukhamDAOImp"/>
    <bean id="dieutrichuyenkhoaDAO" class="dieutrichuyenkhoa.DieutrichuyenkhoaDAOImp"/>
    <bean id="capnhatxetnghiemDAO" class="capnhatcanlamsang.CapnhatXetnghiemDAOImp"/>
    <bean id="capnhatCDHADAO" class="capnhatcanlamsang.CapnhatCDHADAOImp"/>
    <bean id="capnhatTTPTDAO" class="capnhatcanlamsang.CapnhatTTPTDAOImp"/>
    <bean id="capnhatTTPTEkipDAO" class="capnhatcanlamsang.CapnhatTTPTEkipDAOImp"/>
    <bean id="kiemtrabangkeDAO" class="baocao.KiemtrabangkeDAOImp"/>
    <bean id="dmxetnghiemDAO" class="xetnghiem.DM_XETNGHIEMDAOImp"/>
    <bean id="dmcdhaDAO" class="chandoanhinhanh.DM_CDHADAOImp"/>
    <bean id="DICHVUDAO" class="dichvu.DICHVUDAOImp"/>
    <bean id="thongketonghopchiphiDAO" class="baocao.ThongketonghopchiphiDAOImp"/>
    <bean id="BaocaokcbBHYTngoaitruDAO" class="baocao.BaocaokbcBHYTngoaitruDAOImp"/>
    <bean id="Baocaochiphikcbnghiepvu3DAO" class="baocao.Baocaochiphikcbnghiepvu3DAOImp"/>
    <bean id="LichsusudungDAO" class="lichsusudung.LichsusudungDAOImp"/>
    <bean id="TiepnhannhapvienDAO" class="tiepnhannoitru.TiepnhannhapvienDAOImp"/>
    <bean id="KhambenhnoitruDAO" class="khambenhnoitru.KhambenhnoitruDAOImp"/>
    <bean id="KhamBenhNoiTruDialogDAO" class="khambenhnoitru.KhamBenhNoiTruDialogDAOImp"/>
    <bean id="KhambenhnoitrudaukyDAO" class="khambenhnoitru.KhambenhnoitrudaukyDAOImp"/>
    <bean id="ThietlapdonviDAO" class="thietlapdonvi.ThietlapdonviDAOImp"/>
    <bean id="BaocaotiepnhanDAO" class="baocao.BaocaotiepnhanDAOImp"/>
    <bean id="DanhsachbenhnhantheottkhamDAO" class="baocao.DanhsachbenhnhantheottkhamDAOImp"/>
    <bean id="BaocaosoluongthuckhamDAO" class="baocao.BaocaosoluongthuckhamDAOImp"/>
    <bean id="PhanquyendonviDAO" class="phanquyentruycapdonvi.PhanquyendonviDAOImp"/>
    <bean id="BaithuocdongyDAO" class="duoc.BaithuocdongyDAOImp"/>
    <bean id="BaocaochuyentuyenDAO" class="baocao.BaocaochuyentuyenDAOImp"/>
    <bean id="BaocaongayDAO" class="baocao.BaocaongayDAOImp"/>
    <bean id="SessionFilter" class="dangnhap.SessionFilter"/>
    <bean id="KiemtraduocDAO" class="duoc.KiemtraduocDAOImp"/>
    <bean id="TrogiupDAO" class="trogiup.TrogiupDAOImp"/>
    <bean id="NoisoiDAO" class="capnhatcanlamsang.NoisoiDAOImp"/>
    <bean id="SieuamDAO" class="capnhatcanlamsang.SieuamDAOImp"/>
    <bean id="Baocaomau14DAO" class="baocao.Baocaomau14DAOImp"/>
    <bean id="Baocao_chungDAO" class="baocao_chung.Baocao_chungDAOImp"/>
    <bean id="noitiepnhanDAO" class="Noitiepnhan.noitiepnhanDAOImp"/>
    <bean id="tintucDAO" class="tintuc.tintucDAOImp"/>
    <bean id="chuanbenhvienDAO" class="dmchuanbenhvien.chuanbenhvienDAOImp"/>
    <bean id="hethongquanlyDAO" class="dmhethongquanly.hethongquanlyDAOImp"/>
    <bean id="Baocao_BHXHDAO" class="BaocaoBHXH.Baocao_BHXHDAOImp"/>
    <bean id="BaocaoSYTDAO" class="BaocaoSYT.BaocaoSYTDAOImp"/>
    <bean id="DanhsachbenhnhansudungthuocBHYTDAO" class="baocao.DanhsachbenhnhansudungthuocBHYTDAOImp"/>
    <bean id="DanhsachbenhnhantapVLTLDAO" class="baocao.DanhsachbenhnhantapVLTLDAOImp"/>
    <bean id="BaocaobacsikhamtheodichvuDAO" class="baocao.BaocaobacsikhamtheodichvuDAOImp"/>
    <bean id="MausieuamDAO" class="mausieuam.MausieuamDAOImp"/>
    <bean id="MaunoisoiDAO" class="maunoisoi.MaunoisoiDAOImp"/>
    <bean id="NhanvekhoDAO" class="duoc.NhanvekhoDAOImp"/>
    <bean id="donviDAO" class="dmdonvi.donviDAOImp"/>
    <bean id="phongbanDAO" class="dmphongban.phongbanDAOImp"/>
    <bean id="KhamsuckhoeDAO" class="Khamsuckhoe.KhamsuckhoeDAOImp"/>
    <bean id="BaocaokhamchuabenhtheonhieutieuchiDAO" class="baocao.BaocaokhamchuabenhtheonhieutieuchiDAOImp"/>
    <bean id="chuyenvienDAO" class="dmchuyenvien.chuyenvienDAOImp"/>
    <bean id="ChinhsuathuoctraingayDAO" class="khambenh.ChinhsuathuoctraingayDAOImp"/>
    <bean id="bvthuocchuanDAO" class="dmBVthuocchuan.bvthuocchuanDAOImp"/>
    <bean id="HoatdongkhamchuabenhDAO" class="baocao.HoatdongkhamchuabenhDAOImp"/>
    <bean id="CanhbaoduocvattuhethanDAO" class="duoc.CanhbaoduocvattuhethanDAOImp"/>
    <bean id="TainangiaotrongDAO" class="khambenh.TainangiaotrongDAOImp"/>
    <bean id="TrathuocvekhoDAO" class="duoc.TrathuocvekhoDAOImp"/>
    <bean id="thamsodonviDAO" class="dmthamsodonvi.thamsodonviDAOImp"/>
    <bean id="BaocaochotsolieuDAO" class="Baocaochotsolieu.BaocaochotsolieuDAOImp"/>
    <bean id="BaocaosoluongchuyenvaokhoDAO" class="baocao.BaocaosoluongchuyenvaokhoDAOImp"/>
    <bean id="PhieudieutriDAO" class="khambenh.PhieudieutriDAOImp"/>
    <bean id="bhxh_kiemtrathamdinhDAO" class="BaocaoBHXH.bhxh_kiemtrathamdinhDAOImp"/>
    <bean id="ThongkexuatduoctheosophieuDAO" class="xuatthuocvattu.ThongkexuatduoctheosophieuDAOImp"/>
    <bean id="baocaokhambenhDAO" class="baocao_chung.baocaokhambenhDAOImp"/>
    <bean id="PhieunhapvienDAO" class="khambenh.PhieunhapvienDAOImp"/>
    <bean id="vienphinoitruDAO" class="noitruvienphi.VienphinoitruDAOImp"/>
    <bean id="nhapthuoctuphieusaiDao" class="khambenhnoitru.NhapthuoctuphieusaiDAOImp"/>
    <bean id="ChuyentuyennoitruDAO" class="khambenhnoitru.ChuyentuyennoitruDAOImp"/>
    <bean id="ChuyenkhoaDAO" class="khambenhnoitru.ChuyenkhoaDAOImp"/>
    <bean id="XuatvienDAO" class="khambenhnoitru.XuatvienDAOImp"/>
    <bean id="CanlamsangDAO" class="khambenhnoitru.CanlamsangDAOImp"/>
    <bean id="SobenhanDAO" class="Baocaonoitru.SobenhanDAOImp"/>
    <bean id="duocdutruDAO" class="noitruduoc.DuocdutruDAOImp"/>
    <bean id="duochoantraDAO" class="noitruduoc.DuochoantraDAOImp"/>
    <bean id="dsbn_chuyentukhoaccDAO" class="Baocaonoitru.dsbn_chuyentukhoaccDAOImp"/>
    <bean id="dsbn_dieutritaikhoaDAO" class="Baocaonoitru.dsbn_dieutritaikhoaDAOImp"/>
    <bean id="dsbn_ravienDAO" class="Baocaonoitru.dsbn_ravienDAOImp"/>
    <bean id="dsbn_chuaravienDAO" class="Baocaonoitru.dsbn_chuaravienDAOImp"/>
    <bean id="phongkhamDAO" class="dmphongkham.phongkhamDAOImp"/>
    <bean id="KhamdakhoaDAO" class="khambenhnoitru.KhamdakhoaDAOImp"/>
    <bean id="HoichanDAO" class="khambenhnoitru.HoichanDAOImp"/>
    <bean id="TiensubenhsuDAO" class="khambenhnoitru.TiensubenhsuDAOImp"/>
    <bean id="hosobenhanDAO" class="hosobenhan.hosobenhanDAOImp"/>
    <bean id="hotronoitruDAO" class="Baocaonoitru.hotronoitruDAOImp"/>
    <bean id="TuvongDAO" class="khambenhnoitru.TuvongDAOImp"/>
    <bean id="khoaphongsdkhoDAO" class="khoaphongsudungkho.khoaphongsdkhoDAOImp"/>
    <bean id="bhxh_bieumaubaocaoDAO" class="BaocaoBHXH.bhxh_bieumaubaocaoDAOImp"/>
    <bean id="baocaonoitrubhytDAO" class="Baocaonoitru.baocaonoitrubhytDAOImp"/>
    <bean id="dm_giuongDAO" class="dm_giuong.dm_giuongDAOImp"/>
    <bean id="BaocaoXML_BYT_DAO" class="exportxml.BaocaoXML_BYT_DAOImp"/>
    <bean id="bhxh_2348_bieumaubaocaoDAO" class="BaocaoBHXH.bhxh_2348_bieumaubaocaoDAOImp"/>
    <bean id="baocaokhambenh_2348_DAO" class="BaocaoSYT.baocaokhambenh_2348_DAOImp"/>
    <bean id="khoxl_nghiepvuDAO" class="duoc.khoxl_nghiepvuDAOImp"/>
    <bean id="dm_giuongbenhDAO" class="dm_giuong.dm_giuongbenhDAOImp"/>
    <bean id="DienTimDAO" class="capnhatcanlamsang.DienTimDAOImp"/>
    <bean id="MaudientimDAO" class="mausieuam.MaudientimDAOImp"/>
    <bean id="PhuthuDAO" class="phuthu.PhuthuDAOImp"/>
    <bean id="MauxquangDAO" class="mausieuam.MauxquangDAOImp"/>
    <bean id="MauxetnghiemDAO" class="mausieuam.MauxetnghiemDAOImp"/>
    <bean id="Hosobenhan_ngoaitruDAO" class="hosobenhan_ngoaitru.Hosobenhan_ngoaitruDAOImp"/>
    <bean id="PhieukhambenhvaovienDAO" class="tiepnhannoitru.PhieukhambenhvaovienDAOImp"/>
    <bean id="SoylenhDAO" class="Baocaonoitru.SoylenhDAOImp"/>
    <bean id="TamungngoaitruDAO" class="vienphi.TamungngoaitruDAOImp"/>
    <bean id="baocaovienphithuBHYTDAO" class="angiang.baocaovienphithuBHYTDAOImp"/>
    <bean id="ToathuocmauDAO" class="toathuocmau.ToathuocmauDAOImp"/>
    <bean id="MauphauthuatthuthuatDAO" class="mausieuam.MauphauthuatthuthuatDAOImp"/>
    <bean id="sotheodoiDAO" class="duoc.sotheodoiDAOImp"/>
    <bean id="xemthutienvienphiDAO" class="vienphi.xemthutienvienphiDAOImp"/>
    <bean id="XQuang" class="capnhatcanlamsang.XQuangDAOImp"/>
    <bean id="baocaohungyenDAO" class="baocao_hungyen.baocaohungyenDAOImp"/>
    <bean id="ds_nhapvienDAO" class="angiang.ds_nhapvienDAOImp"/>
    <bean id="AGGxemthutienvienphiDAO" class="angiang.AGGxemthutienvienphiDAOImp"/>
    <bean id="ds_khamdieutri_captoachoveDAO" class="angiang.ds_khamdieutri_captoachoveDAOImp"/>
    <bean id="bieu02_KBDAO" class="angiang.bieu02_KBDAOImp"/>
    <bean id="bieu04_PTTTDAO" class="angiang.bieu04_PTTTDAOImp"/>
    <bean id="baocaokhaibaosudungDAO" class="baocao_khaibao_sudung.baocaokhaibaosudungDAOImp"/>
    <bean id="ThongbaoDAO" class="thongbao.ThongbaoDAOImp"/>
    <bean id="baocaotonghopbhytDAO" class="Baocaonoitru.baocaotonghopbhytDAOImp"/>
    <bean id="dsbn_namvienDAO" class="angiang.dsbn_namvienDAOImp"/>
    <bean id="dsbn_chuyenkhoaDAO" class="angiang.dsbn_chuyenkhoaDAOImp"/>
    <bean id="TainanthuongtichDAO" class="khambenh.TainanthuongtichDAOImp"/>
    <bean id="NhapkhohoantranoitruDAO" class="duoc.NhapkhohoantranoitruDAOImp"/>
    <bean id="DichvumauDAO" class="dmgoidichvumau.DichvumauDAOImp"/>
    <bean id="BaocaoxuatduoctheokhoaDAO" class="angiang.BaocaoxuatduoctheokhoaDAOImp"/>
    <bean id="BaocaoxuatnhapthuocDAO" class="baocaoxuatnhapthuoc.BaocaoxuatnhapthuocDAOImp"/>
    <!-- VNPT Soc Trang -->
    <bean id="ThongkebenhnoitrutheonhomDAO" class="soctrang.ThongkebenhnoitrutheonhomDAOImp"/>
    <bean id="BaocaothkcbngoaitruDAO" class="soctrang.BaocaothkcbngoaitruDAOImp"/>
    <bean id="BangkekcbtheokhoaDAO" class="soctrang.BangkekcbtheokhoaDAOImp"/>
    <bean id="bangkexuatthuocvtytkhambenhngoaitruDAO" class="soctrang.bangkexuatthuocvtytkhambenhngoaitruDAOImp"/>
    <bean id="bangkenhaptraDAO" class="soctrang.bangkenhaptraDAOImp"/>
    <bean id="BaocaochiphiduocDAO" class="soctrang.BaocaochiphiduocDAOImp"/>
    <bean id="BaocaothongkecanlamsangDAO" class="soctrang.BaocaothongkecanlamsangDAOImp"/>
    <bean id="bangkecactoavattuDAO" class="soctrang.bangkecactoavattuDAOImp"/>
    <bean id="PhongbenhDAO" class="soctrang.PhongbenhDAOImp"/>
    <bean id="STG_KhambenhnoitruDAO" class="soctrang.Khambenhnoitru.STG_KhambenhnoitruDAOImp"/> <!-- VNPT Soc Trang -->
    <bean id="STG_DuochoantraDAO" class="soctrang.noitruduoc.STG_DuochoantraDAOImp"/> <!-- VNPT Soc Trang -->
    <bean id="STG_phongbanDAO" class="soctrang.dmphongban.STG_phongbanDAOImp"/>
    <bean id="XuatthuocSTGDAO" class="soctrang.Xuatthuocvattu.XuatthuocSTGDAOImp"/>
    <bean id="STG_ChuyentuyennoitruDAO" class="soctrang.Khambenhnoitru.STG_ChuyentuyennoitruDAOImp"/>
    <bean id="DanhMucKHHGDDAO" class="VSC.ytcs.danhmuc.DanhMucKHHGDDAOImp"/>
    <!--<bean id="xuatthuochgg" class="haugiang.HGITHDXuatthuocDAOImp" />-->
    <!-- VNPT Soc Trang -->
    <!-- VNPT Soc Trang -->
    <!-- VNPT Ca Mau -->
    <!--tuyen hai-->
    <bean id="BanSoKhamBenhDAO" class="L2_BanSoKhamBenh.BanSoKhamBenhDAOImp"/>
    <bean id="TongHopTheoMaPTTTDAO" class="binhphuoc.L2.TongHopTheoMaPTTTDAOImp"/>
    <bean id="TreSoSinhKhoaSanDAO" class="binhphuoc.TreSoSinh.TreSoSinhKhoaSanDAOImp"/>
    <bean id="ThanhToanThatThuDAO" class="binhphuoc.L2.ThanhToanThatThuDAOImp"/>
    <bean id="HoanTraDuNoDAO" class="binhphuoc.L2.HoanTraDuNoDAOImp"/>
    <bean id="BTE_Baocao25aDAO" class="baocao.BTE_Baocao25DAOImp"/>
    <bean id="TongHopEkipDAO" class="binhphuoc.L2.TongHopEkipDAOImp"/>


    <bean id="PhanquyenkhoaphongDAO" class="camau.PhanquyenkhoaphongDAOImp"/>
    <bean id="ReportCMUDAO" class="camau.tienich.ReportCMUDAOImp"/>
    <bean id="TienichCMUDAO" class="camau.tienich.TienichCMUDAOImp"/>
    <bean id="VienphiCMUDAO" class="camau.vienphi.VienphiCMUDAOImp"/>
    <bean id="VienphinoitruCMUDAO" class="camau.vienphinoitru.VienphinoitruCMUDAOImp"/>
    <bean id="DuocCMUDAO" class="camau.duoc.DuocCMUDAOImp"/>
    <bean id="CMUCLSDAO" class="camau.canlamsang.CMUCLSDAOImp"/>
    <bean id="bcxuattramxaDAO" class="camau.duoc.bcxuattramxaDAOImp"/>
    <bean id="SotonghopthuocListExcelView_Unicode" class="Baocaonoitru.SotonghopthuocListExcelView_Unicode"/>
    <!-- End VNPT Ca Mau -->
    <bean id="KGGRavienngoaitruDAO" class="kiengiang.KGGRavienngoaitruDAOImp"/>
    <bean id="KGGBaocaoListExcelView_Unicode" class="kiengiang.KGGBaocaoListExcelView_Unicode"/>
    <bean id="BaocaoKCBListExcelViewType" class="baocao.BaocaoKCBListExcelViewType"/>
    <bean id="KGGBaocaophuthuDAO" class="kiengiang.KGGBaocaophuthuDAOImp"/>
    <bean id="KGGDanhmucDAO" class="kiengiang.KGGDanhmucDAOImp"/>
    <bean id="DMDCDAO" class="dmdc.DMDCDAOImp"/>
    <bean id="IDanhMucViTriThuThuatPhauThuat" class="danhmuc.DanhMucViTriThuThuatPhauThuatObject"/>
    <bean id="DMDCPHONGBANDAO" class="dmdc.DMDCPHONGBANDAOImp"/>
    <!-- begin VSC imp -->
    <bean id="Hosobenhan_noitruDAO" class="hosobenhan_noitru.Hosobenhan_noitruDAOImp"/>
    <bean id="DanTocDAO" class="dm_dantoc.DanTocDAOImp"/>
    <bean id="ThonXomDAO" class="dmThonXom.ThonXomDAOImp"/>
    <bean id="XaPhuongDAO" class="dm_phuongxa.XaPhuongDAOImp"/>
    <bean id="GopXaPhuongDAO" class="dm_phuongxa.GopXaPhuongDAOImp"/>
    <bean id="QuanHuyenDAO" class="dm_quanhuyen.QuanHuyenDAOImp"/>
    <bean id="TinhThanhDAO" class="dm_tinhthanh.TinhThanhDAOImp"/>
    <bean id="NgheNghiepDAO" class="dm_nghenghiep.NgheNghiepDAOImp"/>
    <bean id="BenhAnSoSinh_DAO" class="VSC.benhan.BenhAnSoSinh_DAOImp"/>
    <bean id="benhAnSanKhoa_DAO" class="VSC.benhan.BenhAnSanKhoa_DAOImp"/>
    <bean id="benhAnSanKhoaChiTiet_DAO" class="VSC.benhan.BenhAnSanKhoaChiTiet_DAOImp"/>
    <bean id="benhAnSanKhoaKhamBenh_DAO" class="VSC.benhan.BenhAnSanKhoaKhamBenh_DAOImp"/>
    <bean id="benhan_truyennhiemDAO" class="VSC.benhan_truyennhiem.BenhAn_TruyenNhiemDAOImp"/>
    <bean id="benhan_truyennhiemkhambenhDAO" class="VSC.benhan_truyennhiem.BenhAn_TruyenNhiemKhamBenhDAOImp"/>
    <bean id="tongket_benhanDAO" class="VSC.tongket_benhan.TongKet_BenhAnDAOImp"/>
    <bean id="HPG_BaocaoXML_BYT_DAO" class="haiphong.HPG_BaocaoXML_BYT_DAOImp"/>
    <bean id="benhan_huyethocDAO" class="VSC.benhan_huyethoc_truyenmau.BenhAn_HuyetHocDAOImp"/>
    <bean id="benhAn_HuyetHocKhamBenhCoQuanDAO"
          class="VSC.benhan_huyethoc_truyenmau.kbcq.BenhAn_HuyetHocKhamBenhCoQuanDAOImp"/>
    <bean id="benhAn_HuyetHocKhamBenhDAO" class="VSC.benhan_huyethoc_truyenmau.khambenh.BenhAn_HuyetHocKhamBenhDAOImp"/>
    <bean id="benhAn_HuyetHocKhamBenhTTBADAO"
          class="VSC.benhan_huyethoc_truyenmau.ttba.BenhAn_HuyetHocKhamBenhTTBADAOImp"/>
    <bean id="BenhAn_TuyenXaPhuongDAO" class="VSC.benhan_tuyenxaphuong.BenhAn_TuyenXaPhuongDAOImp"/>
    <bean id="KGGBaocaotamungDAO" class="kiengiang.KGGBaocaotamungDAOImp"/>
    <bean id="baocaoTamUngDAO" class="nghean.baocaoTamUngDAOImp"/>
    <bean id="BenhAn_MatTreEmDAO" class="VSC.benhanmat_mattrem.BenhAn_MatTreEmDAOImp"/>
    <bean id="CoSoTuTrucDAO" class="daklak.CoSoTuTrucDAOImp"/>
    <bean id="dlk_ThongkehoantraDAO" class="daklak.dlk_ThongkehoantraDAOImp"/>
    <bean id="BenhAnChuyenKhoaBong_DAO" class="VSC.benhan.BenhAnChuyenKhoaBong_DAOImp"/>
    <bean id="BenhAnChuyenKhoaBong_KhamBenh_DAO" class="VSC.benhan.BenhAnChuyenKhoaBong_KhamBenh_DAOImp"/>
    <bean id="BenhAn_MatTreEm_TongKetDAO" class="VSC.benhanmat_mattrem.tongket.BenhAn_MatTreEm_TongKetDAOImp"/>
    <bean id="Code_HCM_VienphiQuyenBienlaiDAO" class="SaiGon.Code_HCM_VienphiQuyenBienlaiDAOImp"/>
    <bean id="VienphiQuyenBienlaiDAO" class="SaiGon.Code_HCM_VienphiQuyenBienlaiDAOImp"/>
    <bean id="dlk_VienphiDAO" class="daklak.dlk_VienphiDAOImp"/>
    <bean id="dlk_BaocaosudungtutrucDAO" class="daklak.dlk_BaocaosudungtutrucDAOImp"/>
    <bean id="BenhAnChuyenKhoaMatBanPhanTruoc_DAO" class="VSC.benhan.BenhAnChuyenKhoaMatBanPhanTruoc_DAOImp"/>
    <bean id="BenhAnChuyenKhoaMatBanPhanTruocToanThan_DAO"
          class="VSC.benhan.BenhAnChuyenKhoaMatBanPhanTruocToanThan_DAOImp"/>
    <bean id="BenhAnChuyenKhoaMatBanPhanTruocTongKet_DAO"
          class="VSC.benhan.BenhAnChuyenKhoaMatBanPhanTruocTongKet_DAOImp"/>
    <bean id="ChiPhiVanChuyenDAO" class="nghean.ChiPhiVanChuyenDAOImp"/>
    <bean id="CongNoDAO" class="nghean.CongNoDAOImp"/>
    <bean id="TheodoibenhnhannoitruDAO" class="nghean.TheodoibenhnhannoitruDAOImp"/>
    <bean id="VienPhiNopTienVaoQuy" class="nghean.VienPhiNopTienVaoQuyDAOImp"/>
    <bean id="Vienphingoaitru_baocaotheolydpDAO" class="nghean.Vienphingoaitru_baocaotheolydoDAOImp"/>
    <bean id="NAN_UtilityDAO" class="nghean.NAN_UtilityDAOImp"/>
    <bean id="NAN_ChuyentuyenDAO" class="nghean.NAN_ChuyentuyenDAOImp"/>
    <bean id="BaoCaoKKBDAO" class="nghean.BaoCaoKKBDAOImp"/>
    <bean id="KiemtrabaocaongoaitruDAO" class="Kiemtrabaocaongoaitru.KiemtrabaocaongoaitruDAOImp"/>
    <bean id="DMDCVATTUSOYTEDAO" class="dmdc.DMDCVATTUSOYTEDAOImp"/>
    <bean id="DMDC_SYT_DVKT_DAO" class="dmdc.DMDC_SYT_DVKT_DAOImp"/>
    <bean id="Vienphi_benhanDAO" class="vienphi.Vienphi_benhanDAOImp"/>
    <bean id="HosobenhanngoaitruDAO" class="Hosobenhanngoaitru.HosobenhanngoaitruDAOImp"/>
    <bean id="dlk_BaocaosudungthuocDAO" class="daklak.dlk_BaocaosudungthuocDAOImp"/>
    <bean id="dlk_DuocDAO" class="daklak.dlk_DuocDAOImp"/>
    <bean id="DMThongbaoDAO" class="thao.DMThongbaoDAOImp"/>
    <bean id="QuanhuyenDAO" class="thao.QuanhuyenDAOImp"/>
    <bean id="xemtonkhoduocVLGDAO" class="vinhlong.xemtonkhoduocVLGDAOImp"/>
    <bean id="vlg_duyen_phieutheodoidieutrihangngayDAO" class="vinhlong.vlg_duyen_phieutheodoidieutrihangngayDAOImp"/>
    <bean id="vlg_soKetDotDieuTriDAO" class="vinhlong.soKetDotDieuTriDAOImp"/>
    <bean id="BienbanduocvattuhongvoDAO" class="duoc.BienbanduocvattuhongvoDAOImp"/>
    <bean id="dm_phanquyen_nvdc_DAO" class="dm_phanquyen_nvdc.dm_phanquyen_nvdc_DAOImp"/>
    <bean id="ThuocVatTuCLSDAO" class="nghean.ThuocVatTuCLSDAOImp"/>
    <bean id="Baocaosdthuoc_khoaphongDAO" class="nghean.Baocaosdthuoc_khoaphongDAOImp"/>
    <bean id="dlk_GiayChungSinhDAO" class="daklak.dlk_GiayChungSinhDAOImp"/>
    <bean id="phieulamdungcuchinhhinhDAO" class="vinhlong.phieulamdungcuchinhhinhDAOImp"/>
    <bean id="langsonDAO" class="langson.langsonDAOImp"/>
    <bean id="BaocaogiaobannoitruDAO" class="thaibinh.BaocaogiaobannoitruDAOImp"/>
    <bean id="BaocaotinhtrangdonthuocDAO" class="nghean.BaocaotinhtrangdonthuocDAOImp"/>
    <bean id="HPG_DanhMucDAO" class="haiphong.HPG_DanhMucDAOImp"/>
    <bean id="dmthietbicosoDAO" class="VSC.ytcs.danhmuc.dmthietbicosoDAOImp"/>

    <bean id="BTE_BaocaoKCBListExcelView" class="baocao.BTE_BaocaoKCBListExcelView"></bean>
    <bean id="BTE_BaocaoKCBListExcelView_Unicode" class="baocao.BTE_BaocaoKCBListExcelView_Unicode"></bean>
    <bean id="BaocaoPhieuDuocChuaGuiExcelView_Unicode" class="baocao.BaocaoPhieuDuocChuaGuiExcelView_Unicode"></bean>
    <bean id="ChuyenkhoadichvukhamDAO" class="thao.ChuyenkhoadichvukhamDAOImp"></bean>
    <bean id="baocaoduocDAO" class="hatinh.baocaoduocDAOImp"></bean>
    <bean id="hth_dsbn_namvienDAO" class="hatinh.hth_dsbn_namvienDAOImp"></bean>
    <bean id="DM_XN_DC_DAO" class="dmdc.DM_XN_DC_DAOImp"/>
    <bean id="DM_DVK_DC_DAO" class="dmdc.DM_DVK_DC_DAOImp"/>
    <bean id="DM_CDHA_DC_DAO" class="dmdc.DM_CDHA_DC_DAOImp"/>
    <bean id="DM_VATTU_THUOC_DUNGCHUNG_DAO" class="dmdc.DM_VATTU_THUOC_DUNGCHUNG_DAOImp"/>
    <bean id="XoaDuocVattuThuaDAO" class="nghean.XoaDuocVattuThuaDAOImp"/>
    <bean id="HTHSoylenhDAO" class="hatinh.HTHSoylenhDAOImp"></bean>
    <bean id="dlk_thuchiencls_theokhoaDAO" class="daklak.dlk_thuchiencls_theokhoaDAOImp"/>
    <bean id="dlk_bieu04PTTT_TheokhoaDAO" class="daklak.dlk_bieu04PTTT_TheokhoaDAOImp"/>
    <bean id="CongnobaocaoDAO" class="angiang.CongnobaocaoDAOImp"/>
    <bean id="KiemtraphieudutruDAO" class="angiang.KiemtraphieudutruDAOImp"/>
    <bean id="Baocao2021khongbhDAO" class="angiang.Baocao2021khongbhDAOImp"/>
    <bean id="SokhambenhDAO" class="khambenh.SokhambenhDAOImp"/>
    <!--Bổ sung 19/05/2016-->
    <bean id="TOOLANHXADMDAO" class="nghean.TOOLANHXADMDAOImp"/>
    <bean id="XuatXMLBHYTDAO" class="BaocaoBHXH.XuatXMLBHYTDAOImp"/>
    <bean id="KhambenhBANTDAO" class="khambenhnoitru.KhambenhBANTDAOImp"/>
    <bean id="VienphiBANTDAO" class="noitruvienphi.VienphiBANTDAOImp"/>
    <bean id="XuatduocBANTDAO" class="xuatthuocvattu.XuatduocBANTDAOImp"/>
    <bean id="LienthongkhoduocDAO" class="duoc.LienthongkhoduocDAOImp"/>
    <bean id="AGG_dmDAO" class="angiang.AGG_dmDAOImp"/>
    <bean id="Thongke_Tinhhinh_GuiBHXHDAO" class="BaocaoBHXH.Thongke_Tinhhinh_GuiBHXHDAOImp"/>
    <bean id="XuatduocbanleDAO" class="xuatthuocvattu.XuatduocbanleDAOImp"/>
    <bean id="BaocaoKCBnoitrutheonhieutieuDAO" class="Baocaonoitru.BaocaoKCBnoitrutheonhieutieuDAOImp"/>
    <bean id="Hethongbatso1080DAO" class="Hethongbatso1080.Hethongbatso1080DAOImp"/>
    <bean id="ImportXLS" class="ImportXLS.ImportXLSDAOImp">
        <property name="dataSource" ref="dataSourcePL"/>
    </bean>
    <bean id="dlgKhamChuyenKhoaMatDAO" class="khambenh.dlgKhamChuyenKhoaMatDAOImp"/>
    <bean id="KhaibaoDMDAO" class="KhaibaoDM.KhaibaoDMDAOImp"/>
    <bean id="DanhMucXangDauDAO" class="danhmuc.DanhMucXangDauDAOImp"/>

    <!-- Vsc Ytcs -->
    <bean id="QLhokhauDAO" class="VSC.ytcs.danso.QLhokhauDAOImp"/>
    <bean id="QLnhankhauDAO" class="VSC.ytcs.danso.QLnhankhauDAOImp"/>
    <bean id="QLnhankhauDAO1" class="VSC.ytcs.danso.QLnhankhauDAOImp1"/>
    <bean id="QLgiaychungsinhDAO" class="VSC.ytcs.danso.QLgiaychungsinhDAOImp"/>
    <bean id="TraCuuNhanKhauDAO" class="VSC.ytcs.danso.TraCuuNhanKhauDAOImp"/>
    <bean id="TraCuuNhanKhauBenhNhanDAO" class="VSC.ytcs.danso.TraCuuNhanKhauBenhNhanDAOImp"/>
    <bean id="BenhNhankskDAO" class="VSC.ytcs.khambenh.BenhNhankskDAOImp"/>
    <bean id="DmDiaPhuongDAO" class="VSC.ytcs.danhmuc.DmDiaPhuongDAOImp"/>
    <bean id="DmDungChungDAO" class="VSC.ytcs.danhmuc.DmDungChungDAOImp"/>
    <bean id="DanhSachKhamBenhDAO" class="VSC.ytcs.khambenh.DanhSachKhamBenhDAOImp"/>
    <bean id="KhamBenhTytDAO" class="VSC.ytcs.khambenh.KhamBenhTytDAOImp"/>
    <bean id="BenhNhanDAO" class="VSC.ytcs.benhnhan.BenhNhanDAOImp"/>
    <bean id="tiemchungtreemDAO" class="VSC.ytcs.tiemchung.tiemchungtreemDAOImp"/>
    <bean id="TaiNanThuongTichDAO" class="VSC.ytcs.chamsocsuckhoe.tntt.TaiNanThuongTichDAOImp"/>
    <bean id="BenhTamThanDAO" class="VSC.ytcs.chamsocsuckhoe.benhtamthan.BenhTamThanDAOImp"/>
    <bean id="QLhokhauctvsDAO" class="VSC.ytcs.danso.QLhokhauctvsDAOImp"/>
    <bean id="MapDanhMucDAO" class="VSC.ytcs.danhmuc.MapDanhMucDAOImp"/>
    <bean id="BenhNhanLaoDAO" class="VSC.ytcs.benhnhanlao.BenhNhanLaoDAOImp"/>
    <bean id="QuanLyBenhNhanKhongLayNhiemDAO"
          class="VSC.ytcs.chamsocsuckhoe.qlbnkln.QuanLyBenhNhanKhongLayNhiemDAOImp"/>
    <bean id="QuanLyBenhNhanLayNhiemDAO"
          class="VSC.ytcs.chamsocsuckhoe.qlbnln.QuanLyBenhNhanLayNhiemDAOImp"/>
    <bean id="QuanLyBenhNhanLayNhiem2DAO"
          class="VSC.ytcs.chamsocsuckhoe.qlbnln2.QuanLyBenhNhanLayNhiem2DAOImp"/>
    <bean id="DmBenhDAO" class="VSC.ytcs.danhmuc.DmBenhDAOImp"/>
    <bean id="DmNoiPhatHienDAO" class="VSC.ytcs.danhmuc.DmNoiPhatHienDAOImp"/>
    <bean id="HivDAO" class="VSC.ytcs.chamsocsuckhoe.hiv.HivDAOImp"/>
    <bean id="HuyetHocDAO" class="VSC.ytcs.chamsocsuckhoe.huyethoc.HuyetHocDAOImp"/>
    <!--<bean id="QuanLySinhSanChamSocDao" class="VSC.ytcs.khambenh.QuanLySinhSanChamSocDAOImp" />-->
    <!--    <bean id="BenhLaoThongTinDAO" class="VSC.ytcs.benhlaothongtin.BenhLaoThongTinDAOImp"/>-->
    <bean id="QuanLyCanDoDAO" class="VSC.ytcs.chamsocsuckhoe.quanlycando.QuanLyCanDoDAOImp"/>
    <bean id="TTGDSKDAO" class="VSC.ytcs.ttgdsk.TTGDSKDAOImp"/>
    <bean id="VitaminADAO" class="VSC.ytcs.chamsocsuckhoe.vitamina.VitaminADAOImp"/>
    <bean id="BenhSotRetDAO" class="VSC.ytcs.chamsocsuckhoe.benhsotret.BenhSotRetDAOImp"/>
    <bean id="TuVongVSCDAO" class="VSC.ytcs.chamsocsuckhoe.tuvong.TuVongVSCDAOImp"/>
    <bean id="baocaodonviDAO" class="VSC.ytcs.baocao.baocaodonviDAOImp"/>
    <bean id="dmkehoachDAO" class="VSC.ytcs.danhmuc.dmkehoachDAOImp"/>
    <bean id="dmvacxinDAO" class="VSC.ytcs.danhmuc.dmvacxinDAOImp"/>
    <bean id="dmdiadiemtiemDAO" class="VSC.ytcs.danhmuc.dmdiadiemtiemDAOImp"/>
    <bean id="Duoc_DutruthuocDAO" class="VSC.ytcs.duoc.Duoc_DutruthuocDAOImp"/>
    <bean id="DmTrieuChungDAO" class="VSC.ytcs.danhmuc.DmTrieuChungDAOImp"/>
    <bean id="CheckoutbytDao" class="VSC.ytcs.khambenh.CheckoutbytDaoImp"/>
    <bean id="nhomndDAO" class="VSC.ytcs.hethong.nhomndDAOImp"/>
    <bean id="PhanquyentytDAO" class="VSC.ytcs.hethong.phanquyen.PhanquyentytDAOImp"/>
    <bean id="dichvu_dcDAO" class="VSC.ytcs.dichvu_dc.dichvu_dcDAOImp"/>
    <bean id="menuhethongDAO" class="VSC.ytcs.hethong.menuhethongDAOImp"/>
    <bean id="TKwebserviceDAO" class="VSC.ytcs.hethong.TKwebserviceDAOImp"/>
    <bean id="TKNgoDocThucPhamDAO" class="VSC.ytcs.antoanthucpham.TKNgoDocThucPhamDAOImp"/>
    <bean id="dlqDonThuocTheoBenhDAO" class="VSC.ytcs.khambenh.dlqQlyDonThuocTheoBenhDAOImp"/>
    <bean id="DichVuKhamDVDAO" class="VSC.ytcs.DichVuKhamDV.DichVuKhamDVDAOImp"/>
    <bean id="TonghopsolieuDao" class="VSC.ytcs.khambenh.TonghopsolieuDaoImp"/>
    <bean id="KeHoachUongVitaminDAO" class="VSC.ytcs.UongVitamin.KeHoachUongVitaminDAOImp"/>
    <bean id="KHUongVitaminDAO" class="VSC.ytcs.danhmuc.KHUongVitaminDAOImp"/>
    <bean id="dmkehoachcdDAO" class="VSC.ytcs.chamsocsuckhoe.quanlycando.dmkehoachcdDAOImp"/>
    <bean id="khamlamsangDAO" class="VSC.ytcs.tiemchung.khamlamsangDAOImp"/>
    <bean id="tudongbhxhDAO" class="VSC.ytcs.hethong.tudongbhxhDAOImp"/>
    <bean id="dlqQlyKhamThaiDAO" class="VSC.ytcs.khambenh.dlqQlyKhamThaiDAOImp"/>
    <bean id="dlqQlyPhaThaiDAO" class="VSC.ytcs.khambenh.dlqQlyPhaThaiDAOImp"/>
    <bean id="QuanLyPhaThaiTaiBienDao" class="VSC.ytcs.khambenh.QuanLyPhaThaiTaiBienDAOImp"/>
    <bean id="dlqQlySinhNoDAO" class="VSC.ytcs.khambenh.dlqQlySinhNoDAOImp"/>
    <bean id="QuanLySinhSanTaiBienDao" class="VSC.ytcs.khambenh.QuanLySinhSanTaiBienDAOImp"/>
    <bean id="QuanLySinhSanXetNghiemDao" class="VSC.ytcs.khambenh.QuanLySinhSanXetNghiemDAOImp"/>
    <bean id="QuanLySinhSanChamSocDao" class="VSC.ytcs.khambenh.QuanLySinhSanChamSocDAOImp"/>
    <bean id="dlqQlyKhhgdDAO" class="VSC.ytcs.khambenh.dlqQlyKhhgdDAOImp"/>
    <bean id="QuanLyKhhgdTaiBienDAO" class="VSC.ytcs.khambenh.QuanLyKhhgdTaiBienDAOImp"/>
    <bean id="dlgKhamYHCTDAO" class="VSC.ytcs.khambenh.dlgKhamYHCTDAOImp"/>
    <bean id="dlqTamThanDAO" class="VSC.ytcs.khambenh.dlgTamThanDAOImp"/>
    <bean id="dlqSotRetDAO" class="VSC.ytcs.khambenh.dlgSotRetDAOImp"/>
    <bean id="dlqLaoDAO" class="VSC.ytcs.khambenh.dlgLaoDAOImp"/>
    <bean id="dlqHuyetHocDAO" class="VSC.ytcs.khambenh.dlgHuyetHocDAOImp"/>
    <bean id="dlqHivDAO" class="VSC.ytcs.khambenh.dlgHivDAOImp"/>
    <bean id="dlqPhongDAO" class="VSC.ytcs.khambenh.dlqPhongDAOImp"/>
    <bean id="dlqQlyPhuKhoaDAO" class="VSC.ytcs.khambenh.dlqQlyPhuKhoaDAOImp"/>
    <bean id="hgi_tinhhinhsudungquyenhoadon_Dao" class="haugiang.vienphingoaitru.hgi_tinhhinhsudungquyenhoadon_DaoImp"/>
    <bean id="DmKhamchuyenkhoaDAO" class="VSC.ytcs.danhmuc.DmKhamchuyenkhoaDAOImp"/>

    <bean id="QLDiaPhuongDAO" class="qldiaphuong.QLDiaPhuongDAOImp"/>
    <bean id="QLDiaPhuongMoiDAO" class="qldiaphuongmoi.QLDiaPhuongMoiDAOImp"/>
    <bean id="ImportDiaPhuongDAO" class="importFromFile.ImportDiaPhuongDAOImp"/>
    <bean id="NhacLichCanDoDAO" class="VSC.ytcs.chamsocsuckhoe.quanlycando.NhacLichCanDoDAOImp"/>
    <bean id="bcCTVSDAO" class="VSC.ytcs.baocao.bcCTVSDAOImp"/>
    <bean id="DmCongTacVienDAO" class="VSC.ytcs.danhmuc.DmCongTacVienDAOImp"/>
    <bean id="CreateHDDTDao" class="VSC.ytcs.khambenh.CreateHDDTDaoImp"/>
    <bean id="ThuVienPhiDAO" class="angiang.ThuVienPhiImp"/>
    <bean id="KGGVienphinoitruDAO" class="kiengiang.KGGVienphinoitruDAOImp"/>
    <bean id="benhnhanPhongDAO" class="VSC.ytcs.chamsocsuckhoe.phong.benhnhanPhongDAOImp"/>
    <!-- End Vsc Ytcs -->
    <!-- end VSC imp -->
    <!--Binhduong-->
    <bean id="CLSDAO" class="binhduong.CLSDAOImp"/>
    <bean id="BaoCaoNghiOm04GCNDAO" class="binhduong.BaoCaoNghiOm04GCNDAOImp"/>
    <bean id="theBHYTDAO" class="dmtheBHYT.theBHYTDAOImp"/>
    <!--Binhduong-->
    <!-- CMU -->
    <bean id="KGGBangkeChungDAO" class="kiengiang.KGGBangkeChungDAOImp"/>
    <!-- CMU -->
    <!-- TGG-->
    <bean id="ThongKeHoatDongKCBDAO" class="ThongKeHoatDongKCB.ThongKeHoatDongKCBDAOImp"/>
    <!-- TGG-->
    <!--Bacgiang-->
    <!--end BacGiang-->
    <bean id="HPG_CapnhatXetnghiemDAO" class="haiphong.HPG_CapnhatXetnghiemDAOImp"/>
    <bean id="vlg_dmthuocmuangoaiDAO" class="vinhlong.vlg_dmthuocmuangoaiDAOImp"/>
    <bean id="BPC_DuocdutruDAO" class="binhphuoc.noitruduoc.BPC_DuocdutruDAOImp"/>
    <!--KGG thêm xét nghiệm-->
    <bean id="KGGXetnghiemDAO" class="kiengiang.KGGXetnghiemDAOImp"/>
    <bean id="bc_cls_DAO" class = "kiengiang.lis.bc_cls_DAOImp"/>
    <!--KGG thêm xét nghiệm END-->


    <bean id="TanggiamkiemkeDAO" class="duoc.TanggiamkiemkeDAOImp"/>
    <bean id="baocaoduoctbhDAO" class="thaibinh.baocaoduoctbhDAOImp"/>
    <bean id="CT_XemtonkhoduocDAO" class="Xuatnhapton_chitiet.CT_XemtonkhoduocDAOImp"/>
    <!--Begin AGG Bổ sung 28/02/2017-->
    <bean id="AGG_baocaotuanLXNDAO" class="angiang.AGG_baocaotuanLXNDAOImp"/>
    <!--End AGG Bổ sung 28/02/2017-->

    <bean id="DynamicReport" class="DynamicReport.DynamicReportImp"/>
    <bean id="ts_ytncskcanhanDAO" class="VSC.ytcs.khambenh.ts_ytncskcanhanDAOImp"/>
    <bean id="ts_benhtatdiungDAO" class="VSC.ytcs.khambenh.ts_benhtatdiungDAOImp"/>
    <bean id="ts_khamlsvaclsDAO" class="VSC.ytcs.khambenh.ts_khamlsvaclsDAOImp"/>
    <bean id="lichsunhankhautachhoDAO" class="VSC.ytcs.danso.lichsunhankhautachhoDAOImp"/>
    <!-- VNPT Quang Tri START -->
    <bean id="dichVuVatTuDAO" class="quangtri.QTI_DichVuVatTuDAOImp"/>
    <bean id="CanhBaoBacSiDAO" class="quangtri.QTI_CanhBaoBacSiDAOImp"/>
    <!-- VNPT Quang Tri END -->
    <bean id="TracuulichsuDAO" class="VSC.ytcs.khambenh.TracuulichsuDAOImp"/>
    <bean id="GiayChungSinhDAO" class="giaychungsinh.GiayChungSinhDAOImpl"/>
    <bean id="quytrinhHIVDAO" class="VSC.ytcs.quytrinhHIV.quytrinhHIVDAOImp"/>
    <bean id="Checkbox_cdhaDAO" class="dm_checkbox_cdha.Checkbox_cdhaDAOImp"/>
    <bean id="Map_cdha_motaDAO" class="dm_map_cdha_mota.Map_cdha_motaDAOImp"/>
    <bean id="KhamChuyenKhoaLaoDAO" class="Khamsuckhoe.KhamChuyenKhoaLaoDAOImp"/>
    <bean id="dataSourceBATSO"
          class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="com.mysql.jdbc.Driver"/>
        <property name="url"
                  value="****************************************************************************************"/>
        <property name="username" value="khoamap"/>
        <property name="password" value="Qlbv@2015"/>
    </bean>

    <bean id="dataSourceBATSO1080"
          class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="com.mysql.jdbc.Driver"/>
        <property name="url"
                  value="***********************************************************************************"/>
        <property name="username" value="khoamap"/>
        <property name="password" value="Qlbv@2015"/>
    </bean>

    <bean id="dataSourceKIEMTRATHE"
          class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="net.sourceforge.jtds.jdbc.Driver"/>
        <property name="url"
                  value="************************************************************************************************;"/>
        <property name="username" value="sa"/>
        <property name="password" value="kcb#2015@"/>
    </bean>

    <bean id="dataSourceSMS"
          class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="net.sourceforge.jtds.jdbc.Driver"/>
        <property name="url"
                  value="jdbc:jtds:sqlserver://***********:1433;databaseName=SMS3G_3;sendStringParametersAsUnicode=false;"/>
        <property name="username" value="sa"/>
        <property name="password" value="Vnpttg@2015"/>
    </bean>

    <bean id="dataSourceSMS_ORC"
          class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="oracle.jdbc.pool.OracleDataSource"/>
        <property name="url"
                  value="*********************************************= ***********)(protocol=tcp)(port=1521))(connect_data=(service_name= omctgg)))"/>
        <property name="username" value="OMC_NEW"/>
        <property name="password" value="omcdata#1"/>
    </bean>
    <bean id="HttpClientAPIComponent" class="tienich.HttpClientAPIComponent"/>

    <bean id="dataSourceFW" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${db.dataSourceClassName}"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_fw"/>
        <property name="password" value="${db.pass}"/>
        <property name="validationQuery" value="SELECT 1 FROM DUAL"/>
        <property name="timeBetweenEvictionRunsMillis" value="30000"/>
        <property name="maxActive" value="100"/>
        <property name="minIdle" value="10"/>
        <property name="maxWait" value="10000"/>
        <property name="initialSize" value="10"/>
        <property name="removeAbandonedTimeout" value="60"/>
        <property name="removeAbandoned" value="true"/>
        <property name="logAbandoned" value="true"/>
        <property name="minEvictableIdleTimeMillis" value="30000"/>
        <property name="defaultAutoCommit" value="true"/>
    </bean>
    <!--<bean id="dataSourceDMDC"
          class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="oracle.jdbc.pool.OracleDataSource"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_category"/>
        <property name="password" value="${db.pass}"/>
    </bean>-->
    <bean id="dataSourceDMDC" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${db.dataSourceClassName}"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_category"/>
        <property name="password" value="${db.pass}"/>
        <property name="validationQuery" value="SELECT 1 FROM DUAL"/>
        <property name="timeBetweenEvictionRunsMillis" value="30000"/>
        <property name="maxActive" value="100"/>
        <property name="minIdle" value="10"/>
        <property name="maxWait" value="10000"/>
        <property name="initialSize" value="10"/>
        <property name="removeAbandonedTimeout" value="60"/>
        <property name="removeAbandoned" value="true"/>
        <property name="logAbandoned" value="true"/>
        <property name="minEvictableIdleTimeMillis" value="30000"/>
        <property name="defaultAutoCommit" value="true"/>
    </bean>

    <!--<bean id="dataSourceYTCS"
          class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="oracle.jdbc.pool.OracleDataSource"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_ytcs"/>
        <property name="password" value="${db.pass}"/>
    </bean>-->

    <bean id="dataSourceYTCS" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${db.dataSourceClassName}"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_ytcs"/>
        <property name="password" value="${db.pass}"/>
        <property name="validationQuery" value="SELECT 1 FROM DUAL"/>
        <property name="timeBetweenEvictionRunsMillis" value="30000"/>
        <property name="maxActive" value="100"/>
        <property name="minIdle" value="10"/>
        <property name="maxWait" value="10000"/>
        <property name="initialSize" value="10"/>
        <property name="removeAbandonedTimeout" value="60"/>
        <property name="removeAbandoned" value="true"/>
        <property name="logAbandoned" value="true"/>
        <property name="minEvictableIdleTimeMillis" value="30000"/>
        <property name="defaultAutoCommit" value="true"/>
    </bean>

    <!--<bean id="dataSource_synchronization"
          class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="oracle.jdbc.pool.OracleDataSource"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_synchronization"/>
        <property name="password" value="${db.pass}"/>
    </bean>-->

    <bean id="dataSource_synchronization" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${db.dataSourceClassName}"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_synchronization"/>
        <property name="password" value="${db.pass}"/>
        <property name="validationQuery" value="SELECT 1 FROM DUAL"/>
        <property name="timeBetweenEvictionRunsMillis" value="30000"/>
        <property name="maxActive" value="100"/>
        <property name="minIdle" value="10"/>
        <property name="maxWait" value="10000"/>
        <property name="initialSize" value="10"/>
        <property name="removeAbandonedTimeout" value="60"/>
        <property name="removeAbandoned" value="true"/>
        <property name="logAbandoned" value="true"/>
        <property name="minEvictableIdleTimeMillis" value="30000"/>
        <property name="defaultAutoCommit" value="true"/>
    </bean>

    <!--<bean id="dataSourceFW"
          class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="oracle.jdbc.pool.OracleDataSource"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_fw"/>
        <property name="password" value="${db.pass}"/>
    </bean>-->
    <!--<bean id="dataSourcePL"
          class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="oracle.jdbc.pool.OracleDataSource"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_public_list"/>
        <property name="password" value="${db.pass}"/>
    </bean>-->
    <bean id="dataSourcePL" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${db.dataSourceClassName}"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_public_list"/>
        <property name="password" value="${db.pass}"/>
        <property name="validationQuery" value="SELECT 1 FROM DUAL"/>
        <property name="timeBetweenEvictionRunsMillis" value="30000"/>
        <property name="maxActive" value="100"/>
        <property name="minIdle" value="10"/>
        <property name="maxWait" value="10000"/>
        <property name="initialSize" value="10"/>
        <property name="removeAbandonedTimeout" value="60"/>
        <property name="removeAbandoned" value="true"/>
        <property name="logAbandoned" value="true"/>
        <property name="minEvictableIdleTimeMillis" value="30000"/>
        <property name="defaultAutoCommit" value="true"/>
    </bean>

    <!--<bean id="dataSourceMNG"
    class="org.springframework.jdbc.datasource.DriverManagerDataSource" >
    <property name="driverClassName" value="oracle.jdbc.pool.OracleDataSource" />
    <property name="url" value="${db.url}" />
    <property name="username" value="his_manager" />
    <property name="password" value="${db.pass}" />
    </bean>-->
    <bean id="dataSourceMNG" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${db.dataSourceClassName}"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_manager"/>
        <property name="password" value="${db.pass}"/>
        <property name="validationQuery" value="SELECT 1 FROM DUAL"/>
        <property name="timeBetweenEvictionRunsMillis" value="30000"/>
        <property name="maxActive" value="100"/>
        <property name="minIdle" value="10"/>
        <property name="maxWait" value="10000"/>
        <property name="initialSize" value="10"/>
        <property name="removeAbandonedTimeout" value="60"/>
        <property name="removeAbandoned" value="true"/>
        <property name="logAbandoned" value="true"/>
        <property name="minEvictableIdleTimeMillis" value="30000"/>
        <property name="defaultAutoCommit" value="true"/>
    </bean>

    <!--    cau hinh slave 25-->
    <!--<bean id="dataSource_baocao"
          class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="oracle.jdbc.pool.OracleDataSource"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_reports"/>
        <property name="password" value="${db.pass}"/>
    </bean>-->
    <bean id="dataSource_baocao" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${db.dataSourceClassName}"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_reports"/>
        <property name="password" value="${db.pass}"/>
        <property name="validationQuery" value="SELECT 1 FROM DUAL"/>
        <property name="timeBetweenEvictionRunsMillis" value="30000"/>
        <property name="maxActive" value="100"/>
        <property name="minIdle" value="10"/>
        <property name="maxWait" value="10000"/>
        <property name="initialSize" value="10"/>
        <property name="removeAbandonedTimeout" value="60"/>
        <property name="removeAbandoned" value="true"/>
        <property name="logAbandoned" value="true"/>
        <property name="minEvictableIdleTimeMillis" value="30000"/>
        <property name="defaultAutoCommit" value="true"/>
    </bean>

    <!--<bean id="dataSource_news"
          class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="oracle.jdbc.pool.OracleDataSource"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_news"/>
        <property name="password" value="${db.pass}"/>
    </bean>-->

    <bean id="dataSource_news" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${db.dataSourceClassName}"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_news"/>
        <property name="password" value="${db.pass}"/>
        <property name="validationQuery" value="SELECT 1 FROM DUAL"/>
        <property name="timeBetweenEvictionRunsMillis" value="30000"/>
        <property name="maxActive" value="100"/>
        <property name="minIdle" value="10"/>
        <property name="maxWait" value="10000"/>
        <property name="initialSize" value="10"/>
        <property name="removeAbandonedTimeout" value="60"/>
        <property name="removeAbandoned" value="true"/>
        <property name="logAbandoned" value="true"/>
        <property name="minEvictableIdleTimeMillis" value="30000"/>
        <property name="defaultAutoCommit" value="true"/>
    </bean>

    <!--<bean id="dataSource48_logtruycap"
          class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="oracle.jdbc.pool.OracleDataSource"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_logtruycap"/>
        <property name="password" value="${db.pass}"/>
    </bean>-->

    <bean id="dataSource48_logtruycap" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${db.dataSourceClassName}"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_logtruycap"/>
        <property name="password" value="${db.pass}"/>
        <property name="validationQuery" value="SELECT 1 FROM DUAL"/>
        <property name="timeBetweenEvictionRunsMillis" value="30000"/>
        <property name="maxActive" value="100"/>
        <property name="minIdle" value="10"/>
        <property name="maxWait" value="10000"/>
        <property name="initialSize" value="10"/>
        <property name="removeAbandonedTimeout" value="60"/>
        <property name="removeAbandoned" value="true"/>
        <property name="logAbandoned" value="true"/>
        <property name="minEvictableIdleTimeMillis" value="30000"/>
        <property name="defaultAutoCommit" value="true"/>
    </bean>

    <!--<bean id="dataSource_xetnghiem"
          class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="oracle.jdbc.pool.OracleDataSource"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_xetnghiem"/>
        <property name="password" value="${db.pass}"/>
    </bean>-->

    <bean id="dataSource_xetnghiem" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${db.dataSourceClassName}"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_xetnghiem"/>
        <property name="password" value="${db.pass}"/>
        <property name="validationQuery" value="SELECT 1 FROM DUAL"/>
        <property name="timeBetweenEvictionRunsMillis" value="30000"/>
        <property name="maxActive" value="100"/>
        <property name="minIdle" value="10"/>
        <property name="maxWait" value="10000"/>
        <property name="initialSize" value="10"/>
        <property name="removeAbandonedTimeout" value="60"/>
        <property name="removeAbandoned" value="true"/>
        <property name="logAbandoned" value="true"/>
        <property name="minEvictableIdleTimeMillis" value="30000"/>
        <property name="defaultAutoCommit" value="true"/>
    </bean>

    <!--<bean id="dataSource_medicine"
          class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="oracle.jdbc.pool.OracleDataSource"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_medicine"/>
        <property name="password" value="${db.pass}"/>
    </bean>-->

    <bean id="dataSourceMNG_HBN"
          class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="oracle.jdbc.pool.OracleDataSource"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_manager"/>
        <property name="password" value="${db.pass}"/>
    </bean>

    <bean id="dataSource_medicine" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
        <property name="driverClassName" value="${db.dataSourceClassName}"/>
        <property name="url" value="${db.url}"/>
        <property name="username" value="his_medicine"/>
        <property name="password" value="${db.pass}"/>
        <property name="validationQuery" value="SELECT 1 FROM DUAL"/>
        <property name="timeBetweenEvictionRunsMillis" value="30000"/>
        <property name="maxActive" value="100"/>
        <property name="minIdle" value="10"/>
        <property name="maxWait" value="10000"/>
        <property name="initialSize" value="10"/>
        <property name="removeAbandonedTimeout" value="60"/>
        <property name="removeAbandoned" value="true"/>
        <property name="logAbandoned" value="true"/>
        <property name="minEvictableIdleTimeMillis" value="30000"/>
        <property name="defaultAutoCommit" value="true"/>
    </bean>

    <!-- VNPT Soc Trang -->
    <bean id="BaocaoKCBListExcelView_Unicode_stg"
          class="soctrang.BaocaoKCBListExcelView_Unicode_stg">
    </bean>
    <!-- VNPT Soc Trang -->
    <bean id="HTHSoylenhListExcelView_Unicode"
          class="hatinh.HTHSoylenhListExcelView_Unicode">
    </bean>
    <bean id="BaocaoKCBListExcelView"
          class="baocao.BaocaoKCBListExcelView">
    </bean>
    <bean id="BaocaoTNTTListExcelView_Unicode"
          class="baocao.BaocaoTNTTListExcelView_Unicode">
    </bean>
    <bean id="baocao.BaocaoKCBListExcelView_Unicode"
          class="baocao.BaocaoKCBListExcelView_Unicode">
    </bean>
    <bean id="SoylenhListExcelView_Unicode"
          class="Baocaonoitru.SoylenhListExcelView_Unicode">
    </bean>
    <bean id="BaocaoKCBListExcelView_Unicode_19"
          class="baocao.BaocaoKCBListExcelView_Unicode_19">
    </bean>
    <bean id="BaocaoKCBListExcelView_Unicode_20"
          class="baocao.BaocaoKCBListExcelView_Unicode_20">
    </bean>
    <bean id="BaocaoKCBListExcelView_Unicode_21"
          class="baocao.BaocaoKCBListExcelView_Unicode_21">
    </bean>
    <bean id="BaocaoKCBListExcelView_Unicode_79"
          class="baocao.BaocaoKCBListExcelView_Unicode_79">
    </bean>
    <bean id="BaocaoKCBListExcelView_Unicode_80"
          class="baocao.BaocaoKCBListExcelView_Unicode_80">
    </bean>
    <bean id="BaocaoKCBListExcelView_Unicode_7980"
          class="baocao.BaocaoKCBListExcelView_Unicode_7980">
    </bean>
    <bean id="Chungtubanhang_ListExcelView"
          class="vienphi.Chungtubanhang_ListExcelView_Unicode">
    </bean>
    <bean id="SoXetNghiemListExcelView" class="haugiang.SoXetNghiemListExcelView"></bean>
    <bean id="ExcelExtension"
          class="soctrang.ExcelExtension">
    </bean>
    <bean id="messageSource" class="org.springframework.context.support.ResourceBundleMessageSource">
        <property name="basename" value="i18n.languages"/>
        <property name="defaultEncoding" value="UTF-8"/>
    </bean>
    <bean class="org.springframework.web.servlet.view.XmlViewResolver">
        <property name="location">
            <value>/WEB-INF/spring-excel-views.xml</value>
        </property>
        <property name="order" value="0"/>
    </bean>
    <bean id="dbProperties"
          class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="location">
            <value>/WEB-INF/config_db.properties</value>
        </property>
    </bean>

    <bean id="sessionFactory"
          class="org.springframework.orm.hibernate4.LocalSessionFactoryBean">
        <property name="dataSource" ref="dataSourceMNG_HBN"/>
        <property name="packagesToScan">
            <list>
                <value>chuyentuyenSXH</value>
                <value>BASanKhoa</value>
                <value>visinh.domain</value>
                <value>phanquyen.duocvattu.domain</value>
                <value>lichhenkhambenh.domain</value>
<!--                <value>luutruhsba.domain</value>-->
                <value>duoc.vattu_muangoai.domain</value>
                <value>gdyk.domain</value>
                <value>RabbitMQ.domain</value>
                <value>duoc_v20_client.domain</value>
                <value>API_Lienthong.Domain</value>
            </list>
        </property>
        <property name="hibernateProperties">
            <props>
                <prop key="hibernate.dialect">org.hibernate.dialect.Oracle12cDialect</prop>
                <prop key="hibernate.current_session_context_class">thread</prop>
                <prop key="hibernate.show_sql">true</prop>
                <prop key="hibernate.format_sql">false</prop>
                <prop key="hibernate.hbm2ddl.auto">update</prop>
            </props>
        </property>
    </bean>

    <bean id="LogNgoaiTruDAO" class="log_thongtin.dao.LogNgoaiTruDAOImp"/>
    <bean id="LogNgoaiTruNoiDungDAO" class="log_thongtin.dao.LogNgoaiTruNoiDungDAOImp"/>
    <bean id="KhamBenhDAO" class="log_thongtin.dao.ngoaitru.KhamBenhDAOImp"/>
    <bean id="ToaThuocDAO" class="log_thongtin.dao.ngoaitru.ToaThuocDAOImp"/>
    <bean id="ChiTietToaThuocDAO" class="log_thongtin.dao.ngoaitru.ChiTietToaThuocDAOImp"/>
    <bean id="ThanhToanDAO" class="log_thongtin.dao.ngoaitru.ThanhToanDAOImp"/>
    <bean id="TiepNhanDAO" class="log_thongtin.dao.ngoaitru.TiepNhanDAOImp"/>
    <bean id="TTHCBenhNhanDAO" class="log_thongtin.dao.TTHCBenhNhanDAOImp"/>
    <bean id="ChuyenVienTayChanMiengDAO" class="khambenh.ChuyenVienTayChanMiengDAOImp"/>
    <bean id="ChuyenTuyenSXHDAO" class="chuyentuyenSXH.dao.ChuyenTuyenSXHDAOImp"/>
    <bean id="BASanKhoaDAO" class="BASanKhoa.dao.BASanKhoaDAOImp"/>
    <bean id="BASanKhoaChiTietDAO" class="BASanKhoa.dao.BASanKhoaChiTietDAOImp"/>
    <bean id="HoSoBenhAnDAO" class="BASanKhoa.dao.HoSoBenhAnDAOImp"/>
    <bean id="L2DAO" class="l2.L2DAOImp"/>
    <bean id="L2DuocDAO" class="l2.duoc.L2DuocDAOImplement"/>
    <bean id="WebserviceHDDTDAO" class="HoaDonDienTu.WebserviceHDDTDAOImp"/>
    <bean id="PhanQuyenCapNhatDAO" class="phanquyen.duocvattu.dao.PhanQuyenCapNhatDAOImp"/>
    <!--Tuyen Hai-->
    <bean id="KhamBenhPhauThuatDAO" class="tuyenhai.phauthuat.dao.KhamBenhPhauThuatDAOImp"/>
    <bean id="PhauThuatHoiChanDAO" class="tuyenhai.phauthuat.dao.PhauThuatHoiChanDAOImp"/>
    <bean id="TuVanGiaiThichDAO" class="tuyenhai.phauthuat.dao.TuVanGiaiThichDAOImp"/>
    <bean id="CamKetPhauThuatDAO" class="tuyenhai.phauthuat.dao.CamKetPhauThuatDAOImp"/>
    <bean id="PhongPhauThuatDAO" class="tuyenhai.phauthuat.dao.PhongPhauThuatDAOImp"/>
    <bean id="LichPhauThuatDAO" class="tuyenhai.phauthuat.dao.LichPhauThuatDAOImp"/>
    <bean id="LichHenKhamBenhDAO" class="lichhenkhambenh.dao.LichHenKhamBenhDAOImp"/>
    <bean id="NanBaoCaoKcbKhoaPhongDAO" class="nghean.NanBaoCaoKcbKhoaPhongDAOImp"/>
    <bean id="AOPSmartCAService" class="smartca.service.AOPSmartCAService"/>
    <bean id="SmartCAManager" class="smartca.service.SmartCAManager"/>
    <bean id="LogHeThongAOP" class="loghethong.LogHeThongAOP"/>
    <bean id="BaocaoYTCSDAO" class="VSC.ytcs.baocao.BaocaoYTCSDAOImp"/>
    <bean id="ytcsDongBoDuLieuDAO" class="VSC.ytcs.BAOCAO_YTCS.DONGBODULIEU.ytcsDongBoDuLieuDAOImp"/>
    <bean id="Api_YtcsDAO" class="VSC.ytcs.YTCS_API.Api_YtcsDAOImp"/>
    <bean id="TT37DAO" class="VSC.ytcs.TT37.TT37DAOImp"/>

    <tx:annotation-driven transaction-manager="transactionManager"/>
    <task:annotation-driven scheduler="scheduler"/>
    <task:scheduler id="scheduler" pool-size="15"/>
    <mvc:annotation-driven/>
    <bean id="transactionManager"
          class="org.springframework.orm.hibernate4.HibernateTransactionManager">
        <property name="sessionFactory" ref="sessionFactory"/>
    </bean>
    <bean id="jdbcTransactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager"
          scope="singleton">
        <property name="dataSource" ref="dataSourceMNG_HBN"/>
    </bean>

    <mvc:interceptors>
        <bean class="dangnhap.SessionValidator"/>
    </mvc:interceptors>
    <mvc:resources mapping="/resources/**" location="/resources/Theme/"/>
    <!-- Hiếu CMU 23/11/2017 -->
    <mvc:resources mapping="/report/**" location="/resources/Report/"/>
    <!-- Hiếu CMU 23/11/2017 -->
    <mvc:annotation-driven/>
    <mvc:view-controller path="/" view-name="index"/>
    <mvc:view-controller path="/error_page" view-name="error_page"/>
    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver"/>
    <bean id="localeResolver"
          class="MvcConfiguration.CustomSessionLocaleResolver">
        <property name="defaultLocale" value="vi_VN"/>
    </bean>

    <rabbit:annotation-driven/>

    <rabbit:connection-factory id="connectionFactory" host="************" port="5672" username="guest"
                               password="guest"/>

    <rabbit:admin connection-factory="connectionFactory"/>

    <rabbit:template id="amqpTemplate" connection-factory="connectionFactory"/>
    <rabbit:queue id="TAOPHIEUXN_QUEUE" name="TAOPHIEUXN_QUEUE" durable="true" auto-delete="false"/>
    <rabbit:queue id="THEMCHIDINH_QUEUE" name="THEMCHIDINH_QUEUE" durable="true" auto-delete="false"/>
    <rabbit:queue id="XOACHIDINH_QUEUE" name="XOACHIDINH_QUEUE" durable="true" auto-delete="false"/>
    <rabbit:queue id="THANHTOAN_QUEUE" name="THANHTOAN_QUEUE" durable="true" auto-delete="false"/>
    <rabbit:queue id="HUYTHANHTOAN_QUEUE" name="HUYTHANHTOAN_QUEUE" durable="true" auto-delete="false"/>
    <rabbit:queue id="XOAPHIEUXN_QUEUE" name="XOAPHIEUXN_QUEUE" durable="true" auto-delete="false"/>
    <rabbit:direct-exchange id="TAOPHIEUXN_EXCHANGE" name="TAOPHIEUXN_EXCHANGE" durable="true" auto-delete="false">
        <rabbit:bindings>
            <rabbit:binding queue="TAOPHIEUXN_QUEUE" key="KEY_CREATE.@@.##.DR1"/>
        </rabbit:bindings>
    </rabbit:direct-exchange>
    <rabbit:direct-exchange id="THEMCHIDINH_EXCHANGE" name="THEMCHIDINH_EXCHANGE" durable="true" auto-delete="false">
        <rabbit:bindings>
            <rabbit:binding queue="THEMCHIDINH_QUEUE" key="KEY_ADD.@@.##.DR1"/>
        </rabbit:bindings>
    </rabbit:direct-exchange>
    <rabbit:direct-exchange id="XOACHIDINH_EXCHANGE" name="XOACHIDINH_EXCHANGE" durable="true" auto-delete="false">
        <rabbit:bindings>
            <rabbit:binding queue="XOACHIDINH_QUEUE" key="KEY_DELETE.@@.##.DR1"/>
        </rabbit:bindings>
    </rabbit:direct-exchange>
    <rabbit:direct-exchange id="THANHTOAN_EXCHANGE" name="THANHTOAN_EXCHANGE" durable="true" auto-delete="false">
        <rabbit:bindings>
            <rabbit:binding queue="THANHTOAN_QUEUE" key="KEY_PAY.@@.##.DR1"/>
        </rabbit:bindings>
    </rabbit:direct-exchange>
    <rabbit:direct-exchange id="HUYTHANHTOAN_EXCHANGE" name="HUYTHANHTOAN_EXCHANGE" durable="true" auto-delete="false">
        <rabbit:bindings>
            <rabbit:binding queue="HUYTHANHTOAN_QUEUE" key="KEY_CANCELPAY.@@.##.DR1"/>
        </rabbit:bindings>
    </rabbit:direct-exchange>
    <rabbit:direct-exchange id="XOAPHIEUXN_EXCHANGE" name="XOAPHIEUXN_EXCHANGE" durable="true" auto-delete="false">
        <rabbit:bindings>
            <rabbit:binding queue="XOAPHIEUXN_QUEUE" key="KEY_DELETEPHIEU.@@.##.DR1"/>
        </rabbit:bindings>
    </rabbit:direct-exchange>
    <bean id="rabbitListenerContainerFactory"
          class="org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory">
        <property name="connectionFactory" ref="connectionFactory"/>
        <property name="concurrentConsumers" value="1"/>
        <property name="maxConcurrentConsumers" value="10"/>
    </bean>

    <bean id="minioClient" class="integration.minio.MinIOClient">
        <property name="endpoint" value="${minio.endpoint}" />
        <property name="accessKey" value="${minio.accessKey}" />
        <property name="secret" value="${minio.secret}" />
        <property name="bucket" value="${minio.bucket}" />
    </bean>
</beans>