<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<script>
    var dialog_capnhatketquattpt;
    $(function () {
        dialog_capnhatketquattpt = $("#dialog_capnhatketquattpt").dialog({
            autoOpen: false,
            width: 1000,
            position: {my: "center top", at: "center top", of: window}
        });
        var url_gio = "noitru_ngaygio_chuyenkhoa";
        $.post(url_gio).done(function (data) {
            $("#ngaypttt").val(data.ngaycv);
            $("#giopttt").val(data.giocv);
        });
        $("#bt_ttpt_dong").click(function (evt) {
            dialog_capnhatketquattpt.dialog("close");
        });
        $("#ngaypttt").keypress(function (evt) {
            if (evt.keyCode == 13) {
                $("#giopttt").focus();
            }
        });
        $("#giopttt").keypress(function (evt) {
            if (evt.keyCode == 13) {
                $("#chandoan").focus();
            }
        });
        $("#chandoan").keypress(function (evt) {
            if (evt.keyCode == 13) {
                $("#phuongphappttt").focus();
            }
        });
        $("#phuongphappttt").keypress(function (evt) {
            if (evt.keyCode == 13) {
                $("#phuongphapvocam").focus();
            }
        });
        $("#phuongphapvocam").keypress(function (evt) {
            if (evt.keyCode == 13) {
                $("#bacsipttt").focus();
            }
        });
        $("#bacsipttt").keypress(function (evt) {
            if($("#bacsipttt").val() == "" ){
                $("#ma_bacsipttt").val("");
            }
            if (evt.keyCode == 13) {
                $("#bacsigayme").focus();
            }
        });

        $("#bacsigayme").keypress(function (evt) {
            if($("#bacsigayme").val() == "" ){
                $("#ma_bacsigayme").val("");
            }
            if (evt.keyCode == 13) {
                $("#catchisau7ngay").focus();
            }
        });

        $("#bacsigayme").keyup(function (evt) {
            if($("#bacsigayme").val() == "" ){
                $("#ma_bacsigayme").val("");
            }
        });

        $("#bacsigayme_1").keypress(function (evt) {
            if($("#bacsigayme_1").val() == "" ){
                $("#ma_bacsigayme_1").val("");
            }
        });

        $("#bacsigayme_1").keyup(function (evt) {
            if($("#bacsigayme_1").val() == "" ){
                $("#ma_bacsigayme_1").val("");
            }
        });

        $("#dcvongtrong").keypress(function (evt) {
            if($("#dcvongtrong").val() == "" ){
                $("#ma_dcvongtrong").val("");
            }
        });

        $("#dcvongtrong").keyup(function (evt) {
            if($("#dcvongtrong").val() == "" ){
                $("#ma_dcvongtrong").val("");
            }
        });

        $("#dcvongngoai").keypress(function (evt) {
            if($("#dcvongngoai").val() == "" ){
                $("#ma_dcvongngoai").val("");
            }
        });

        $("#dcvongngoai").keyup(function (evt) {
            if($("#dcvongngoai").val() == "" ){
                $("#ma_dcvongngoai").val("");
            }
        });

        $("#bacsipttt_1").keypress(function (evt) {
            if($("#bacsipttt_1").val() == "" ){
                $("#ma_bacsipttt_1").val("");
            }
        });

        $("#bacsipttt_1").keyup(function (evt) {
            if($("#bacsipttt_1").val() == "" ){
                $("#ma_bacsipttt_1").val("");
            }
        });

        $("#bacsipttt_2").keypress(function (evt) {
            if($("#bacsipttt_2").val() == "" ){
                $("#ma_bacsipttt_2").val("");
            }
        });

        $("#bacsipttt_2").keyup(function (evt) {
            if($("#bacsipttt_2").val() == "" ){
                $("#ma_bacsipttt_2").val("");
            }
        });


        $("#cb_gaymehoisuc").change(function (evt) {
            var check = $("#cb_gaymehoisuc").prop("checked");
            if (check == true) {
                $("#cb_nhiemkhuan").prop("checked", false);
                $("#cb_khac").prop("checked", false);
            }
        });
        $("#cb_nhiemkhuan").change(function (evt) {
            var check = $("#cb_nhiemkhuan").prop("checked");
            if (check == true) {
                $("#cb_gaymehoisuc").prop("checked", false);
                $("#cb_khac").prop("checked", false);
            }
        });
        $("#cb_khac").change(function (evt) {
            var check = $("#cb_khac").prop("checked");
            if (check == true) {
                $("#cb_gaymehoisuc").prop("checked", false);
                $("#cb_nhiemkhuan").prop("checked", false);
            }
        });
        $("#cb_trenban").change(function (evt) {
            var check = $("#cb_trenban").prop("checked");
            if (check == true) {
                $("#cb_trong24gio").prop("checked", false);
            }
        });
        $("#cb_trong24gio").change(function (evt) {
            var check = $("#cb_trong24gio").prop("checked");
            if (check == true) {
                $("#cb_trenban").prop("checked", false);
            }
        });
        $("#maupttt").combogrid({
            url: 'select_maupttt_theodvtt',
            debug: true,
            width: "670px",
            colModel: [{'columnName': 'MA_MAUPTT', 'label': 'MA_MAUPTT', hidden: true},
                {'columnName': 'TEN_MAUPTTT', 'width': '100%', 'label': 'Tên Mẫu PTTT', 'align': 'left'},
                {'columnName': 'NOIDUNG','label': 'NOIDUNG', hidden: true}
            ],
            select: function (event, ui) {
                $("#maupttt").val(ui.item.TEN_MAUPTTT);
                $("#trinhtupttt").val(ui.item.NOIDUNG);
                return false;
            }
        });
        //dc vong trong
        $("#dcvongtrong").combogrid({
            url: 'noitru_hoichan_timkiem_bacsi?url='+"${Sess_DVTT}",
            debug: true,
            width: "300px",
            colModel: [{'columnName': 'MA_NHANVIEN', 'label': 'MA_NHANVIEN', hidden: true},
                {'columnName': 'TEN_NHANVIEN', 'width': '100%', 'label': 'Tên Mẫu PTTT', 'align': 'left'}
            ],
            select: function (event, ui) {
                $("#ma_dcvongtrong").val(ui.item.MA_NHANVIEN);
                $("#dcvongtrong").val(ui.item.TEN_NHANVIEN);
                cmu_laycchn_bacsi(ui.item.MA_NHANVIEN,'#dcvongtrong_cchn');
                return false;
            }
        });
        //dc vong ngoai
        $("#dcvongngoai").combogrid({
            url: 'noitru_hoichan_timkiem_bacsi?url='+"${Sess_DVTT}",
            debug: true,
            width: "300px",
            colModel: [{'columnName': 'MA_NHANVIEN', 'label': 'MA_NHANVIEN', hidden: true},
                {'columnName': 'TEN_NHANVIEN', 'width': '100%', 'label': 'Tên Mẫu PTTT', 'align': 'left'}
            ],
            select: function (event, ui) {
                $("#ma_dcvongngoai").val(ui.item.MA_NHANVIEN);
                $("#dcvongngoai").val(ui.item.TEN_NHANVIEN);
                cmu_laycchn_bacsi(ui.item.MA_NHANVIEN,'#dcvongngoai_cchn');
                return false;
            }
        });
        //bacsigayme
        $("#bacsigayme").combogrid({
            url: 'noitru_hoichan_timkiem_bacsi?url='+"${Sess_DVTT}",
            debug: true,
            width: "300px",
            colModel: [{'columnName': 'MA_NHANVIEN', 'label': 'MA_NHANVIEN', hidden: true},
                {'columnName': 'TEN_NHANVIEN', 'width': '100%', 'label': 'Tên Mẫu PTTT', 'align': 'left'}
            ],
            select: function (event, ui) {
                $("#ma_bacsigayme").val(ui.item.MA_NHANVIEN);
                $("#bacsigayme").val(ui.item.TEN_NHANVIEN);
                cmu_laycchn_bacsi(ui.item.MA_NHANVIEN,'#bacsigayme_cchn');
                return false;
            }
        });
        //bacsigayme_1
        $("#bacsigayme_1").combogrid({
            url: 'noitru_hoichan_timkiem_bacsi?url='+"${Sess_DVTT}",
            debug: true,
            width: "300px",
            colModel: [{'columnName': 'MA_NHANVIEN', 'label': 'MA_NHANVIEN', hidden: true},
                {'columnName': 'TEN_NHANVIEN', 'width': '100%', 'label': 'Tên Mẫu PTTT', 'align': 'left'}
            ],
            select: function (event, ui) {
                $("#ma_bacsigayme_1").val(ui.item.MA_NHANVIEN);
                $("#bacsigayme_1").val(ui.item.TEN_NHANVIEN);
                cmu_laycchn_bacsi(ui.item.MA_NHANVIEN,'#bacsigayme_1_cchn');
                return false;
            }
        });
        //bacsipttt
        $("#bacsipttt").combogrid({
            url: 'noitru_hoichan_timkiem_bacsi?url='+"${Sess_DVTT}",
            debug: true,
            width: "300px",
            colModel: [{'columnName': 'MA_NHANVIEN', 'label': 'MA_NHANVIEN', hidden: true},
                {'columnName': 'TEN_NHANVIEN', 'width': '100%', 'label': 'Tên Mẫu PTTT', 'align': 'left'}
            ],
            select: function (event, ui) {
                $("#ma_bacsipttt").val(ui.item.MA_NHANVIEN);
                $("#bacsipttt").val(ui.item.TEN_NHANVIEN);
                cmu_laycchn_bacsi(ui.item.MA_NHANVIEN,'#bacsipttt_cchn');
                return false;
            },
            open: function( event, ui ) {
                $("#ma_bacsipttt").val("");
            },
            close: function( event, ui ) {
                if($("#ma_bacsipttt").val() == "") {
                    $("#bacsipttt").val("");
                }
            },
            change: function( event, ui ) {
                if(!ui.item) {
                    $("#ma_bacsipttt").val("");
                    $("#bacsipttt").val("");
                }
            },
            search: function( event, ui ) {
                if(!ui.item && $("#ma_bacsipttt").val() != "") {
                    $("#ma_bacsipttt").val("");
                }
            }
        });
        //bacsipttt_1
        $("#bacsipttt_1").combogrid({
            url: 'noitru_hoichan_timkiem_bacsi?url='+"${Sess_DVTT}",
            debug: true,
            width: "300px",
            colModel: [{'columnName': 'MA_NHANVIEN', 'label': 'MA_NHANVIEN', hidden: true},
                {'columnName': 'TEN_NHANVIEN', 'width': '100%', 'label': 'Tên Mẫu PTTT', 'align': 'left'}
            ],
            select: function (event, ui) {
                $("#ma_bacsipttt_1").val(ui.item.MA_NHANVIEN);
                $("#bacsipttt_1").val(ui.item.TEN_NHANVIEN);
                cmu_laycchn_bacsi(ui.item.MA_NHANVIEN,'#bacsipttt_1_cchn');
                return false;
            }
        });
        //bacsipttt_2
        $("#bacsipttt_2").combogrid({
            url: 'noitru_hoichan_timkiem_bacsi?url='+"${Sess_DVTT}",
            debug: true,
            width: "300px",
            colModel: [{'columnName': 'MA_NHANVIEN', 'label': 'MA_NHANVIEN', hidden: true},
                {'columnName': 'TEN_NHANVIEN', 'width': '100%', 'label': 'Tên Mẫu PTTT', 'align': 'left'}
            ],
            select: function (event, ui) {
                $("#ma_bacsipttt_2").val(ui.item.MA_NHANVIEN);
                $("#bacsipttt_2").val(ui.item.TEN_NHANVIEN);
                cmu_laycchn_bacsi(ui.item.MA_NHANVIEN,'#bacsipttt_2_cchn');
                return false;
            }
        });
        function cmu_laycchn_bacsi(manhanvien,id) {

            $.get("cmu_getlist?url="+convertArray(["${Sess_DVTT}",manhanvien,'CMU_LAYCCHN'])
            ).done(function(data) {
                if(data.length > 0) {
                    $(id).val(data[0].CCHN);
                    $("#"+id.replace("#",'ma_')).val(manhanvien);
                }

            })
        }
        $("#chonppvocam").on("change", function (e){
            var optionSelected = $("option:selected", this).text();
            var valueSelected = this.value;
            if (valueSelected != 0){
                var ht = $("#phuongphapvocam").val();
                if (ht.trim() == ""){
                    ht = optionSelected;
                }else{
                    ht = ht+", "+optionSelected;
                }
                $("#phuongphapvocam").val(ht);
            }
        });
        $("#cmuluu_tt_trinhtu").click(function() {
            var madv = $("#madv").val();
            var sophieu = $("#sophieu").val();
            var dvtt = "${Sess_DVTT}";
            var trinhtupttt = $("#trinhtupttt").val().trim();
            $.post("cmu_post", {
                url: ["${Sess_DVTT}",madv,sophieu,trinhtupttt,'CMU_LUUTRINHTU_TTPT'].join("```")
            }).done(function(data){
                if(data == 0) {
                    jAlert("Lưu thành công.")
                }
                if(data == -1) {
                    jAlert("Bệnh nhân đã bị khóa dữ liệu vui lòng mở khóa.")
                }
            })
        })
    });

</script>

<style>
    #dialog_capnhatketquattpt tr td {
        font-size: 12px;
    }
    .width1{
        width: 200px;
    }
    .width2{
        width: 545px;
    }
    .width3{
        width: 150px;
    }
    .width4{
        width: 325px;
    }
    .width5{
        width: 810px;
    }
    .width6{
        width:500px;
    }
    legend{
        font-size: 12px;
        color:red;
    }
    .width100{
        width:100%;
    }
    .width50{
        width:50%;
    }
</style>
<div id="dialog_capnhatketquattpt" style="display: none; font-size: 12px; background-color: #E3F2E1" title="Kết quả thủ thuật phẫu thuật">
    <form id="ketquattpt" name="ketquattpt" method="post" action="">
        <!-- ĐắkLắk (An Giang yêu cầu) - Ninh 09/12/2016: view thông tin hành chánh của BN lên form nhập kết quả -->
        <div>
            <fieldset>
                <table width="100%">
                    <tr>
                        <td colspan="4"><b>Thông tin hành chính của bệnh nhân agg:</b></td>
                    </tr>
                    <tr>
                        <td width="10%">Họ tên</td>
                        <td width="40%"><input name="hoten_ct" type="text" disabled="disabled" id="hoten_ct" style="width: 355px; color: red; font-weight: bold" /></td>
                        <td>Tuổi
                            <input name="tuoi_ct" type="text" disabled="disabled" id="tuoi_ct" style="width: 80px" />
                            Giới tính
                            <input name="gioitinh_ct" type="text" disabled="disabled" id="gioitinh_ct" style="width: 70px" />
                            Mã y tế
                            <input name="mabenhnhan_ct" type="text" disabled="disabled" id="mabenhnhan_ct" style="width: 170px" />
                        </td>
                    <tr>
                        <td>Khoa</td>
                        <td><input name="tenkhoa_ct" type="text" disabled="disabled" style="width: 355px" id="tenkhoa_ct" /></td>
                        <td>Số thẻ BHYT
                            <input name="sothebhyt_ct" type="text" disabled="disabled" style="width: 381px" id="sothebhyt_ct" /></td>
                    </tr>
                    <tr>
                        <td>Thời gian chỉ định</td>
                        <td colspan="2"><input name="ngaychidinh_kq" type="text" disabled="disabled" id="ngaychidinh_kq" size="10"
                                               data-inputmask="'alias': 'date'"/>
                            <input name="giochidinh_kq" type="text" disabled="disabled" id="giochidinh_kq" size="10"
                                   data-inputmask="'alias': 'hh:mm:ss'"/></td>
                    </tr>
                </table>
            </fieldset>
        </div>
        <!-- End ĐắkLắk -->
        <div>
            <table width="100%" border="0">
                <tr>
                    <td>Giờ chỉ đinh:</td>
                    <td colspan="2">
                        <input name="ngaycd_pttt" id="ngaycd_pttt" data-inputmask="'alias': 'date'"  style="width:120px" disabled/>
                        <input name="giocd_pttt" id="giocd_pttt" data-inputmask="'alias': 'hh:mm:ss'" style="width:121px" disabled/>
                    </td>
                    <td width="91" align="right">Tai biến:</td>
                    <td width="400"><label><input type="checkbox" name="cb_gaymehoisuc" id="cb_gaymehoisuc" />
                        Gây mê hồi sức</label>
                        <label style="padding:5px"><input type="checkbox" name="cb_nhiemkhuan" id="cb_nhiemkhuan" />
                            Nhiễm khuẩn </label>
                        <label style="padding:5px"><input type="checkbox" name="cb_khac" id="cb_khac" />
                            Khác</label></td>
                </tr>
                <tr>
                    <td width="167">Thời gian thực hiện Y lệnh:</td>
                    <td colspan="2">
                        <input name="ngaypttt" id="ngaypttt" data-inputmask="'alias': 'date'"  style="width:120px"/>
                        <input name="giopttt" id="giopttt" data-inputmask="'alias': 'hh:mm:ss'" style="width:121px"/>
                    </td>
                    <td align="right">Tử vong:</td>
                    <td><label><input type="checkbox" name="cb_trenban" id="cb_trenban" />
                        Trên bàn phẫu thuật</label>
                        <label style="padding:5px"><input type="checkbox" name="cb_trong24gio" id="cb_trong24gio" />
                            Trong 24 giờ</label></td>
                </tr>
                <tr id="textthoigiankt">
                    <td width="167">Thời gian kết quả:</td>
                    <td colspan="2"><input name="thoigiankt" id="thoigiankt" data-inputmask="'alias': 'date'"  style="width:127px"/>
                        <input name="giokt" id="giokt" data-inputmask="'alias': 'hh:mm:ss'" style="width:127px"/></td>
                    </td>
                </tr>
                <tr>
                    <td width="167">Chẩn đoán trước TT/PP :</td>
                    <td colspan="4"><input name="cmuchandoan_truocttpt" readonly="true" type="text" class="width100" id="cmuchandoan_truocttpt" /></td>
                </tr>
                <tr>
                    <td width="167">Chẩn đoán sau TT/PP :</td>
                    <td colspan="4"><input name="chandoan" type="text" class="width100" id="chandoan" /></td>
                </tr>
                <tr>
                    <td width="167">Phương pháp PT/TT:</td>
                    <td colspan="4"><input name="phuongphappttt" type="text" class="width100" id="phuongphappttt" /></td>
                </tr>
                <tr>
                    <td width="167">Mã phương pháp vô cảm (CV130) <span style="color: red">(*):</span></td>
                    <td colspan="4">
                        <select name="ma_pp_vo_cam" id="ma_pp_vo_cam" class="width100">
                            <option value="">Chọn mã phương pháp</option>
                            <option value="1">Gây mê</option>
                            <option value="2">Gây tê</option>
                            <option value="3">Châm tê</option>
                            <option value="4">Các phương pháp vô cảm khác</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td width="167">Chọn Phương pháp vô cảm:</td>
                    <td colspan="4">
                        <c:choose>
                            <c:when test="${THAMSO_44311001 == '1'}">
                                <select name="chonppvocam" type="text" class="width100" id="chonppvocam">
                                    <c:forEach var="i" items="${listPhuongPhapVoCam}">
                                        <option value="${i.ma_phuongphap}"> ${i.ten_phuongphap}</option>
                                    </c:forEach>
                                </select>
                            </c:when>
                            <c:otherwise>
                                <select id="chonppvocam" style="width: 266px;">
                                    <option value="0">-- Chọn loại PP vô cảm --</option>
                                    <option value="1">Gây mê tĩnh mạch</option>
                                    <option value="2">Gây mê nội khí quản</option>
                                    <option value="3">Gây mê tại chỗ</option>
                                    <option value="4">Gây tê tủy sống</option>
                                    <option value="5">Gây tê ngoài màng cứng</option>
                                    <option value="6">Gây mê mask</option>
                                    <option value="7">Gây tê cơ bậc thang</option>
                                    <option value="8">Tiền mê + gây tê tại chỗ</option>
                                    <option value="9">Tê tại chỗ</option>
                                    <option value="10">Tê tùng nách</option>
                                    <option value="11">Tê tùng đoàn</option>
                                    <option value="12">Bình thường</option>
                                    <option value="13">Không nhỏ tê và gây tê</option>
                                    <option value="14">Nhỏ tê hoặc bôi tê tại chổ</option>
                                    <option value="15">Tê cạnh nhãn cầu</option>
                                    <option value="16">Tê hậu nhãn cầu</option>
                                </select>
                            </c:otherwise>
                        </c:choose>
                    </td>
                </tr>
                <tr>
                    <td width="167">Phương pháp vô cảm:</td>
                    <td colspan="4"><input name="phuongphapvocam" type="text" class="width100" id="phuongphapvocam" /></td>
                </tr>
                <tr>
                    <td width="167">Phẫu thuật/thủ thuật viên:</td>
                    <td colspan="4">
                        <input name="ma_bacsipttt" type="text" style="width: 66px" id="ma_bacsipttt" disabled/>
                        <input name="bacsipttt" type="text" id="bacsipttt" style="width: 200px"/>
                        <input name="bacsipttt_cchn" type="hidden" class="width100" id="bacsipttt_cchn" />
                    </td>
                </tr>
                <tr>
                    <td width="167">Phụ mổ vòng trong:</td>
                    <td colspan="2">
                        <input name="ma_bacsipttt_1" type="text" style="width: 66px;" id="ma_bacsipttt_1" disabled/>
                        <input name="bacsipttt_1" type="text" style="width: 200px" id="bacsipttt_1" />
                        <input name="bacsipttt_1_cchn" type="hidden" class="width100" id="bacsipttt_1_cchn" />
                    </td>
                    <td width="100">Phụ mổ vòng ngoài:</td>
                    <td colspan="2">
                        <input name="ma_bacsipttt_2" type="text" style="width: 66px;" id="ma_bacsipttt_2" disabled/>
                        <input name="bacsipttt_2" type="text" style="width: 328px;" id="bacsipttt_2" />
                        <input name="bacsipttt_2_cchn" type="hidden" class="width100" id="bacsipttt_2_cchn" />
                    </td>
                </tr>
                <tr>
                    <td width="167">Bác sĩ gây mê/tê:</td>
                    <td colspan="2">
                        <input name="ma_bacsigayme" type="text" style="width: 66px;" id="ma_bacsigayme" disabled/>
                        <input name="bacsigayme" type="text" style="width: 200px;" id="bacsigayme" />
                        <input name="bacsigayme_cchn" type="hidden" class="width100" id="bacsigayme_cchn" />
                    </td>
                    <td width="100">KTV gây mê/tê:</td>
                    <td colspan="2">
                        <input name="ma_bacsigayme_1" type="text" style="width: 66px;" id="ma_bacsigayme_1" disabled/>
                        <input name="bacsigayme_1" type="text" style="width: 328px;" id="bacsigayme_1" />
                        <input name="bacsigayme_1_cchn" type="hidden" class="width100" id="bacsigayme_1_cchn" />
                        <input name="cmupt_pm4_cnhh" type="hidden" class="width100" id="cmupt_pm4_cnhh" />
                    </td>
                </tr>
                <tr>
                    <td width="167">DC vòng trong:</td>
                    <td colspan="2">
                        <input name="ma_dcvongtrong" type="text" style="width: 66px;" id="ma_dcvongtrong" disabled/>
                        <input name="dcvongtrong" type="text" style="width: 200px" id="dcvongtrong" />
                        <input name="dcvongtrong_cchn" type="hidden" class="width100" id="dcvongtrong_cchn" />
                    </td>
                    <td width="100">DC vòng ngoài:</td>
                    <td colspan="2">
                        <input name="ma_dcvongngoai" type="text" style="width: 66px;" id="ma_dcvongngoai" disabled/>
                        <input name="dcvongngoai" type="text" style="width: 328px" id="dcvongngoai" />
                        <input name="dcvongngoai_cchn" type="hidden" class="width100" id="dcvongngoai_cchn" />
                        <input name="ma_bacsipttt_cchn" type="hidden" class="width100" id="ma_bacsipttt_cchn" />
                        <input name="ma_bacsipttt_1_cchn" type="hidden" class="width100" id="ma_bacsipttt_1_cchn" />
                        <input name="ma_bacsipttt_2_cchn" type="hidden" class="width100" id="ma_bacsipttt_2_cchn" />
                        <input name="ma_bacsigayme_cchn" type="hidden" class="width100" id="ma_bacsigayme_cchn" />
                        <input name="ma_bacsigayme_1_cchn" type="hidden" class="width100" id="ma_bacsigayme_1_cchn" />
                        <input name="ma_dcvongtrong_cchn" type="hidden" class="width100" id="ma_dcvongtrong_cchn" />
                        <input name="ma_dcvongngoai_cchn" type="hidden" class="width100" id="ma_dcvongngoai_cchn" />
                        <input name="ma_cmupt_pm4_cnhh" type="hidden" class="width100" id="ma_cmupt_pm4_cnhh" />
                    </td>
                </tr>
                <tr  style="display:none" >
                    <td colspan="5"><textarea name="catchisau7ngay" cols="80" rows="4" class="width100" id="catchisau7ngay">
                        </textarea></td>
                </tr>
                <tr>
                    <td colspan="2"><b>Mẫu trình tự thủ thuật phẫu thuật</b></td>
                    <td colspan="3"><input name="maupttt" class="width100" id="maupttt" ></td>
                </tr>
                <tr>
                    <td colspan="1">Người đọc kết quả</td>
                    <td colspan="4">
                        <select id="cboNguoiDocKetQua">
                        </select>
                        <label style="padding:5px" for="chkVetThuongTaiPhat"> Vết thương tái phát: </label><input type="checkbox" name="chkVetThuongTaiPhat" id="chkVetThuongTaiPhat"/>
                    </td>
                </tr>
                <tr>
                    <td colspan="5"><textarea name="trinhtupttt" cols="80" rows="7" class="width100" id="trinhtupttt"></textarea></td>
                </tr>
                <tr>
                    <td colspan="5" align="center"><input name="luu_tt" type="button" class="button_shadow" id="luu_tt" value="Lưu"/>
                        <input name="cmuluu_tt_trinhtu" style="width:176px" type="button" class="button_shadow" id="cmuluu_tt_trinhtu" value="Lưu Trình Tự thủ thuật"/>
                        <input name="inphieu_ttpt" type="button" class="button_shadow" id="inphieu_ttpt" value="In phiếu"/>
                        <input name="inphieu_ttpt_khonghinhmau" type="button" class="button_shadow" id="inphieu_ttpt_khonghinhmau" value="In phiếu không hình" style="width: 160px"/>
                        <!-- KGG thêm in phiếu chứng nhận thủ thuật -->
                        <input name="inphieuchungnhan" type="button" class="button_shadow" id="inphieuchungnhan" value="In phiếu chứng nhận" style="width: 160px"/>
                        <input type="button" name="luufileanh" id="luufileanh" value="Chọn file" class="button_shadow"/>
                        <input type="button" name="bt_ttpt_dong" id="bt_ttpt_dong" value="Đóng" class="button_shadow"/></td>
                </tr>
            </table>
        </div>
    </form>
</div>