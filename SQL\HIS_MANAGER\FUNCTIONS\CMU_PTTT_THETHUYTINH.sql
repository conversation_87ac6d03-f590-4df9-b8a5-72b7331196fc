create or replace PROCEDURE "HIS_MANAGER"."CMU_PTTT_THETHUYTINH" (
    p_so_phieu_dichvu   IN                  VARCHAR2,
    p_dvtt              IN                  VARCHAR2,
    p_ma_dv             IN                  NUMBER,
    p_noitru            IN                  NUMBER,
    p_ma_kham_benh      IN                  VARCHAR2,
    p_stt_<PERSON>han        IN                  VARCHAR2,
    p_stt_dotdieutri    IN                  VARCHAR2,
    p_stt_dieutri       IN                  VARCHAR2,
    cur                 OUT                 SYS_REFCURSOR
) IS

    p_tengiuong      VARCHAR2(500) DEFAULT ' ';
    p_hinhanh1       CLOB := '';
    p_hinhanh2       CLOB := '';
    v_thamso960524   NUMBER := his_fw.cmu_tsdv(p_dvtt, 960524, 0);
    v_sogiuong       VARCHAR2(1000);
    v_sobuong        VARCHAR2(1000);
    v_thamso960616 number(10) := cmu_tsdv(p_dvtt, 960616, 0);
BEGIN
    IF p_noitru = 0 THEN
BEGIN
SELECT
    hinhanh
INTO p_hinhanh1
FROM
    kb_cd_dichvu_images
WHERE
    dvtt = p_dvtt
  AND so_phieu_dichvu = p_so_phieu_dichvu
  AND ma_dv = p_ma_dv
  AND stt = 1;

EXCEPTION
            WHEN no_data_found THEN
                p_hinhanh1 := '';
END;

BEGIN
SELECT
    hinhanh
INTO p_hinhanh2
FROM
    kb_cd_dichvu_images
WHERE
    dvtt = p_dvtt
  AND so_phieu_dichvu = p_so_phieu_dichvu
  AND ma_dv = p_ma_dv
  AND stt = 2;

EXCEPTION
            WHEN no_data_found THEN
                p_hinhanh2 := '';
END;

OPEN cur FOR SELECT
                        kb.icd
                        || ' - '
                        || kb.ten_benh_theobs
                        || kb.benh_phu AS chandoantruocpttt,
                        ct.chandoan        AS chandoansaupttt,
                        ct.phuongphap_tt_pt,
                        CASE ct.phuongphap_vocam
                            WHEN '1'   THEN
                                'Khác'
                            WHEN '2'   THEN
                                'Gây mê tĩnh mạch'
                            WHEN '3'   THEN
                                'Gây mê nội khí quản'
                            WHEN '4'   THEN
                                'Gây tê tại chổ'
                            WHEN '5'   THEN
                                'Tiền mê + gây mê tại chổ'
                            WHEN '6'   THEN
                                'Gây tê tủy sống'
                            WHEN '7'   THEN
                                'Mê Mask'
                            WHEN '8'   THEN
                                'Tiền mê + tê tại chỗ'
                            ELSE
                                ct.phuongphap_vocam
END AS phuongphap_vocam,
                        ldv.ten_loaihinh   AS ten_loai_dich_vu,
                        CASE
                            WHEN p_dvtt = 96066 THEN
                                ct.bacsi_pttt
                            ELSE
                                substrb(ct.bacsi_pttt, 1, instrb(ct.bacsi_pttt, ':', 1) - 1)
END AS bacsi_pttt,
                        CASE
                            WHEN p_dvtt = 96066 THEN
                                ''
                            ELSE
                                substrb(ct.bacsi_pttt, instrb(ct.bacsi_pttt, ':', 1) + 1, instrb(ct.bacsi_pttt, ':', - 1) - instrb
                                (ct.bacsi_pttt, ':', 1) - 1)
END AS bacsi_pttt_1,
                        CASE
                            WHEN p_dvtt = 96066 THEN
                                ''
                            ELSE
                                substrb(ct.bacsi_pttt, instrb(ct.bacsi_pttt, ':', - 1) + 1, lengthb(ct.bacsi_pttt) - instrb(ct.bacsi_pttt
                                , ':', - 1))
END AS bacsi_pttt_2,
                        CASE
                            WHEN p_dvtt = 96066 THEN
                                ct.bacsi_gayme
                            ELSE
                                substrb(ct.bacsi_gayme, 1, instrb(ct.bacsi_gayme, ':', 1, 1) - 1)
END AS bacsi_gayme,
                        substrb(ct.bacsi_gayme, instrb(ct.bacsi_gayme, ':', 1, 1) + 1, instrb(ct.bacsi_gayme, ':', 1, 2) - instrb
                        (ct.bacsi_gayme, ':', 1, 1) - 1) AS bacsi_gayme_1,
                        substrb(ct.bacsi_gayme, instrb(ct.bacsi_gayme, ':', 1, 2) + 1, instrb(ct.bacsi_gayme, ':', 1, 3) - instrb
                        (ct.bacsi_gayme, ':', 1, 2) - 1) AS dc_vongtrong,
                        substrb(ct.bacsi_gayme, instrb(ct.bacsi_gayme, ':', 1, 3) + 1, lengthb(ct.bacsi_gayme) - 1) AS dc_vongngoai
                        ,
                        kb.mabenhnhan      AS mabenhnhan,
                        kb.ma_kham_benh    AS sobenhan,
                        ct.catchi_sau7ngay,
                        ct.trinhtu_tt_pt,
                        EXTRACT(HOUR FROM
                            CASE
                                WHEN v_thamso960524 = 1 THEN
                                    cd.ngay_chi_dinh
                                ELSE
                                    ct.ngay_gio_pttt
                            END
                        ) AS giopttt,
                        EXTRACT(MINUTE FROM
                            CASE
                                WHEN v_thamso960524 = 1 THEN
                                    cd.ngay_chi_dinh
                                ELSE
                                    ct.ngay_gio_pttt
                            END
                        ) AS phutpttt,
                        EXTRACT(DAY FROM
                            CASE
                                WHEN v_thamso960524 = 1 THEN
                                    cd.ngay_chi_dinh
                                ELSE
                                    ct.ngay_gio_pttt
                            END
                        ) AS ngaypttt,
                        EXTRACT(MONTH FROM
                            CASE
                                WHEN v_thamso960524 = 1 THEN
                                    cd.ngay_chi_dinh
                                ELSE
                                    ct.ngay_gio_pttt
                            END
                        ) AS thangpttt,
                        EXTRACT(YEAR FROM
                            CASE
                                WHEN v_thamso960524 = 1 THEN
                                    cd.ngay_chi_dinh
                                ELSE
                                    ct.ngay_gio_pttt
                            END
                        ) AS nampttt,
                        ' ' AS tenkhoa_nhapvienvaokhoa,
                        ' ' AS tengiuongbenh,
                        TO_CHAR(
                            CASE
                                WHEN v_thamso960524 = 1 THEN
                                    ct.ngay_chi_dinh_ct
                                ELSE
                                    ct.ngay_gio_pttt
                            END, 'DD/MM/YYYY') AS ngaypttt_1,
                        TO_CHAR(
                            CASE
                                WHEN v_thamso960524 = 1 THEN
                                    ct.ngay_chi_dinh_ct
                                ELSE
                                    ct.ngay_gio_pttt
                            END, 'HH24:MI:SS') AS giopttt_1,
                        'TRÌNH TỰ THỦ THUẬT PHẪU THUẬT' AS tieude,
                        EXTRACT(HOUR FROM ct.ngay_gio_pttt_kt) AS giopttt_kt,
                        EXTRACT(MINUTE FROM ct.ngay_gio_pttt_kt) AS phutpttt_kt,
                        EXTRACT(DAY FROM ct.ngay_gio_pttt_kt) AS ngaypttt_kt,
                        EXTRACT(MONTH FROM ct.ngay_gio_pttt_kt) AS thangpttt_kt,
                        EXTRACT(YEAR FROM ct.ngay_gio_pttt_kt) AS nampttt_kt,
                        taibien,
                        tuvong, -- KGG Thêm Th?i gian ti?p nh?n
                        EXTRACT(HOUR FROM tn.thoi_gian_tiep_nhan) AS gio,
                        EXTRACT(MINUTE FROM tn.thoi_gian_tiep_nhan) AS phut,
                        EXTRACT(DAY FROM tn.thoi_gian_tiep_nhan) AS ngay,
                        EXTRACT(MONTH FROM tn.thoi_gian_tiep_nhan) AS thang,
                        EXTRACT(YEAR FROM tn.thoi_gian_tiep_nhan) AS nam,
                        regexp_replace(regexp_replace(p_hinhanh1, 'data:image/jpeg;base64,', ''), 'data:image/png;base64,', '') hinhanh1
                        ,
                        regexp_replace(regexp_replace(p_hinhanh2, 'data:image/jpeg;base64,', ''), 'data:image/png;base64,', '') hinhanh2
                        ,
                        '' sogiuong,
                        '' sobuong,
                        '' dan_luu,
                        '' bac,
                        '' khac,
                        '' ngay_cat_chi,
                        '' ngay_rut,
                        '' phanloaivetmo,
                        '' sophongmo,
                        v_thamso960616 ANCHUKY
                    FROM
                        kb_cd_dichvu_ct      ct
                        INNER JOIN kb_cd_dichvu         cd ON ct.dvtt = cd.dvtt
                                                      AND ct.sovaovien = cd.sovaovien
                                                      AND ct.so_phieu_dichvu = cd.so_phieu_dichvu,
                        kb_kham_benh         kb,
                        dm_dich_vu_kham      dv,
                        dm_loaihinh_dichvu   ldv,
                        kb_tiep_nhan         tn
                    WHERE
                        ct.dvtt = p_dvtt
                        AND ct.so_phieu_dichvu = p_so_phieu_dichvu
                        AND kb.ma_kham_benh = ct.ma_kham_benh
                        AND kb.dvtt = p_dvtt
                        AND ct.ma_dv = p_ma_dv
                        AND dv.ma_nhom_dv = ldv.ma_loaihinh
                        AND dv.ma_dv = p_ma_dv
                        AND dv.dvtt = p_dvtt
                        AND ct.ma_kham_benh = p_ma_kham_benh
                        AND tn.dvtt = p_dvtt
                        AND tn.id_tiepnhan = kb.id_tiepnhan;

ELSE
BEGIN
SELECT
    hinhanh
INTO p_hinhanh1
FROM
    noitru_cd_pttt_thuchien_images
WHERE
    dvtt = p_dvtt
  AND so_phieu_dichvu = p_so_phieu_dichvu
  AND ma_dv = p_ma_dv
  AND stt = 1;

EXCEPTION
            WHEN no_data_found THEN
                p_hinhanh1 := '';
END;

BEGIN
SELECT
    hinhanh
INTO p_hinhanh2
FROM
    noitru_cd_pttt_thuchien_images
WHERE
    dvtt = p_dvtt
  AND so_phieu_dichvu = p_so_phieu_dichvu
  AND ma_dv = p_ma_dv
  AND stt = 2;

EXCEPTION
            WHEN no_data_found THEN
                p_hinhanh2 := '';
END;

OPEN cur FOR SELECT
                        bn.ten_benh_nhan,
                        bn.tuoi,
                        CASE
                            WHEN bn.gioi_tinh = '1' THEN
                                'Nam'
                            ELSE
                                'Nữ'
END gioi_tinh,
                        CASE
                            WHEN tenchandoan_truocpt IS NOT NULL THEN
                                tenchandoan_truocpt
                            ELSE
                                nvl(dt.icd_dieutri, '')
                                || '-'
                                || dt.tenicd_dieutri
                                || dt.ten_benhphu
END AS chandoantruocpttt,
                        nvl(ct.chandoan, ' ')         AS chandoan,
                        EXTRACT(HOUR FROM ba.ngaynhapvien) AS gio,
                        EXTRACT(MINUTE FROM ba.ngaynhapvien) AS phut,
                        EXTRACT(DAY FROM ba.ngaynhapvien) AS ngay,
                        EXTRACT(MONTH FROM ba.ngaynhapvien) AS thang,
                        EXTRACT(YEAR FROM ba.ngaynhapvien) AS nam,
                        TO_CHAR(ba.ngaynhapvien, 'dd/mm/yyyy hh24:mi') ngaynhapvien,
                        EXTRACT(HOUR FROM
                            CASE
                                WHEN v_thamso960524 = 1 THEN
                                    ct.ngay_chi_dinh_ct
                                ELSE
                                    ct.ngay_gio_pttt
                            END
                        ) AS giopttt,
                        EXTRACT(MINUTE FROM
                            CASE
                                WHEN v_thamso960524 = 1 THEN
                                    ct.ngay_chi_dinh_ct
                                ELSE
                                    ct.ngay_gio_pttt
                            END
                        ) AS phutpttt,
                        EXTRACT(DAY FROM
                            CASE
                                WHEN v_thamso960524 = 1 THEN
                                    ct.ngay_chi_dinh_ct
                                ELSE
                                    ct.ngay_gio_pttt
                            END
                        ) AS ngaypttt,
                        EXTRACT(MONTH FROM
                            CASE
                                WHEN v_thamso960524 = 1 THEN
                                    ct.ngay_chi_dinh_ct
                                ELSE
                                    ct.ngay_gio_pttt
                            END
                        ) AS thangpttt,
                        EXTRACT(YEAR FROM
                            CASE
                                WHEN v_thamso960524 = 1 THEN
                                    ct.ngay_chi_dinh_ct
                                ELSE
                                    ct.ngay_gio_pttt
                            END
                        ) AS nampttt,
                        CASE
                            WHEN v_thamso960524 = 1 THEN
                                TO_CHAR(ct.ngay_chi_dinh_ct, 'dd/mm/yyyy hh24:mi')
                            ELSE
                                TO_CHAR(ct.ngay_gio_pttt, 'dd/mm/yyyy hh24:mi')
END ngaygiopttt,
                        CASE
                            WHEN p_dvtt = 96066 THEN
                                ct.bacsi_pttt
                            ELSE
                                substrb(ct.bacsi_pttt, 1, instrb(ct.bacsi_pttt, ':', 1) - 1)
END AS bacsi_pttt,
                        ct.phuongphap_vocam,
                        JSON_VALUE(ct.json_thongtinchung, '$.LOAITHUOCTE') loaithuocte,
                        JSON_VALUE(ct.json_tuongtrinh, '$.CODINHNHANCAU_1."Vành mi"') vanhmi_1,
                        JSON_VALUE(ct.json_tuongtrinh, '$.CODINHNHANCAU_1."Chỉ cơ trực"') chicotruc_1,
                        JSON_VALUE(ct.json_tuongtrinh, '$.TINHTRANGT3_2') tinhtrangt3_2,
                        JSON_VALUE(ct.json_tuongtrinh, '$.MOKMRIA_3') mokmria_3,
                        JSON_VALUE(ct.json_tuongtrinh, '$.KINHTUYEN_3')
                        || ' giờ' kinhtuyen_3,
                        JSON_VALUE(ct.json_tuongtrinh, '$.MOVAOTP_4."Giác mạc"') giac_mac_4,
                        JSON_VALUE(ct.json_tuongtrinh, '$.MOVAOTP_4."Vùng rìa"') vung_ria_4,
                        JSON_VALUE(ct.json_tuongtrinh, '$.MOVAOTP_4."Củng mạc"') cung_mac_4,
                        JSON_VALUE(ct.json_tuongtrinh, '$.KINHTUYEN_4')
                        || ' giờ' kinhtuyen_4,
                        JSON_VALUE(ct.json_tuongtrinh, '$.KICHTHUOC_4')
                        || ' mm' kichthuoc_4,
                        JSON_VALUE(ct.json_tuongtrinh, '$.NHUOMBAO_5') nhuombao_5,
                        JSON_VALUE(ct.json_tuongtrinh, '$.ITEM6_6."Xé bao trước T3"') xebaotruoct3_6,
                        JSON_VALUE(ct.json_tuongtrinh, '$.ITEM6_6."Mở bao hình tem thư"') mobaohinhtemthu_6,
                        JSON_VALUE(ct.json_tuongtrinh, '$.TACHNHAN_7') tachnhan_7,
                        JSON_VALUE(ct.json_tuongtrinh, '$.XOAYNHAN_7') xoaynhan_7,
                        JSON_VALUE(ct.json_tuongtrinh, '$.DAYNHANRANGOAI_8') daynhanrangoai_8,
                        JSON_VALUE(ct.json_tuongtrinh, '$.CHATNHAY_8') chatnhay_8,
                        '9. Tán nhân: năng lượng '
                        || nvl(JSON_VALUE(ct.json_tuongtrinh, '$.NANGLUONG_9'), '.....')
                        || ' %, lực hút '
                        || nvl(JSON_VALUE(ct.json_tuongtrinh, '$.LUCHUT_9'), '.....')
                        || ' mmHg, tốc độ dòng chảy '
                        || nvl(JSON_VALUE(ct.json_tuongtrinh, '$.TOCDODONGCHAY_9'), '.....') item9_9,
                        JSON_VALUE(ct.json_tuongtrinh, '$.HUTCHATT3_10."IA"') IA_10,
                        JSON_VALUE(ct.json_tuongtrinh, '$.HUTCHATT3_10."Kim hai nòng"') KIMHAINONG_10,
                        JSON_VALUE(ct.json_tuongtrinh, '$.DATIOL_11."Mềm"') MEM_11,
                        JSON_VALUE(ct.json_tuongtrinh, '$.DATIOL_11."Cứng"') CUNG_11,
                        JSON_VALUE(ct.json_tuongtrinh, '$.DATIOL_11."Đặt bằng pince"') DATBANGPINCE_11,
                        JSON_VALUE(ct.json_tuongtrinh, '$.DATIOL_11."Bằng súng"') BANGSUNG_11,
                        JSON_VALUE(ct.json_tuongtrinh, '$.DATIOL_11."Đặt trong túi bao"') DATTRONGTUIBAO_11,
                        JSON_VALUE(ct.json_tuongtrinh, '$.DATIOL_11."Rãnh thể mi"') RANHTHEMI_11,
                        JSON_VALUE(ct.json_tuongtrinh, '$.CODINHIOL_11."Củng mạc"') CUNGMAC_11,
                        JSON_VALUE(ct.json_tuongtrinh, '$.RACHBAOSAU_12') RACHBAOSAU_12,
                        'Vị trí rách ' || nvl(JSON_VALUE(ct.json_tuongtrinh, '$.VITRIRACH_12'), '.....') || ' Kích thước ' || nvl(JSON_VALUE(ct.json_tuongtrinh, '$.KICHTHUOC_12'), '.....') VITRIRACH_KICHTHUOC_12,
                        JSON_VALUE(ct.json_tuongtrinh, '$.CATBAOSAU_12') CATBAOSAU_12,
                        JSON_VALUE(ct.json_tuongtrinh, '$.PHUONGPHAPCATBAOSAU_12."Cắt bằng kéo"') CATBANGKEO_12,
                        JSON_VALUE(ct.json_tuongtrinh, '$.PHUONGPHAPCATBAOSAU_12."Máy cắt DK"') MAYCATDK_12,
                        JSON_VALUE(ct.json_tuongtrinh, '$.CATMONGMATNGOAIVI_12') CATMONGMATNGOAIVI_12,
                        'Vị trí ' || nvl(JSON_VALUE(ct.json_tuongtrinh, '$.VITRI_12'), '.....') VITRICAT_12,
                        JSON_VALUE(ct.json_tuongtrinh, '$.PHUCHOIVETMO_12."Bơm phù"') BOMPHU_12,
                        JSON_VALUE(ct.json_tuongtrinh, '$.PHUCHOIVETMO_12."Khâu vắt"') KHAUVAT_12,
                        JSON_VALUE(ct.json_tuongtrinh, '$.PHUCHOIVETMO_12."Khâu mũi rời"') KHAUMUIROI_12,
                        nvl(JSON_VALUE(ct.json_tuongtrinh, '$.SOMUI_12'), '.....') || ' mũi' SOMUI_12,
                        'Loại chỉ ' || nvl(JSON_VALUE(ct.json_tuongtrinh, '$.LOAICHI_12'), '.....') || '/0' LOAICHI_12,
                        'Diễn biến khác: ' || nvl(JSON_VALUE(ct.json_tuongtrinh, '$.DIENBIENKHAC_12'), '.....') DIENBIENKHAC_12,
                        JSON_VALUE(ct.json_tuongtrinh, '$.TIEMMAT_12') TIEMMAT_12,
                        JSON_VALUE(ct.json_tuongtrinh, '$.DUOIKMCNC_12') DUOIKMCNC_12,
                        'Loại thuốc: ' || nvl(JSON_VALUE(ct.json_tuongtrinh, '$.LOAITHUOC_12'), '.....') LOAITHUOC_12,
                        'Tra mắt: ' || nvl(JSON_VALUE(ct.json_tuongtrinh, '$.TRAMAT_12'), '.....') TRAMAT_12,
                        'Băng: ' || nvl(JSON_VALUE(ct.json_tuongtrinh, '$.BANG_12'), '.....') BANG_12,
                        pban.ten_phongban   tenkhoa_nhapvienvaokhoa,
                        ' ' AS tengiuongbenh,
                        ct.phuongphap_tt_pt,
                        CASE ct.phuongphap_vocam
                            WHEN '1'   THEN
                                'Khác'
                            WHEN '2'   THEN
                                'Gây mê tĩnh mạch'
                            WHEN '3'   THEN
                                'Gây mê nội khí quản'
                            WHEN '4'   THEN
                                'Gây tê tại chổ'
                            WHEN '5'   THEN
                                'Tiền mê + gây mê tại chổ'
                            WHEN '6'   THEN
                                'Gây tê tủy sống'
                            WHEN '7'   THEN
                                'Mê Mask'
                            WHEN '8'   THEN
                                'Tiền mê + tê tại chỗ'
                            ELSE
                                ct.phuongphap_vocam
END AS phuongphap_vocam,
                        ldv.ten_loaihinh    AS ten_loai_dich_vu,
                        CASE
                            WHEN p_dvtt = 96066 THEN
                                ct.bacsi_pttt
                            ELSE
                                substrb(ct.bacsi_pttt, 1, instrb(ct.bacsi_pttt, ':', 1) - 1)
END AS bacsi_pttt,
                        CASE
                            WHEN p_dvtt = 96066 THEN
                                ''
                            ELSE
                                substrb(ct.bacsi_pttt, instrb(ct.bacsi_pttt, ':', 1) + 1, instrb(ct.bacsi_pttt, ':', - 1) - instrb
                                (ct.bacsi_pttt, ':', 1) - 1)
END AS bacsi_pttt_1,
                        CASE
                            WHEN p_dvtt = 96066 THEN
                                ''
                            ELSE
                                substrb(ct.bacsi_pttt, instrb(ct.bacsi_pttt, ':', - 1) + 1, lengthb(ct.bacsi_pttt) - instrb(ct.bacsi_pttt
                                , ':', - 1))
END AS bacsi_pttt_2,
                        CASE
                            WHEN p_dvtt = 96066 THEN
                                ct.bacsi_gayme
                            ELSE
                                substrb(ct.bacsi_gayme, 1, instrb(ct.bacsi_gayme, ':', 1, 1) - 1)
END AS bacsi_gayme,
                        substrb(ct.bacsi_gayme, instrb(ct.bacsi_gayme, ':', 1, 1) + 1, instrb(ct.bacsi_gayme, ':', 1, 2) - instrb
                        (ct.bacsi_gayme, ':', 1, 1) - 1) AS bacsi_gayme_1,
                        substrb(ct.bacsi_gayme, instrb(ct.bacsi_gayme, ':', 1, 2) + 1, instrb(ct.bacsi_gayme, ':', 1, 3) - instrb
                        (ct.bacsi_gayme, ':', 1, 2) - 1) AS dc_vongtrong,
                        substrb(ct.bacsi_gayme, instrb(ct.bacsi_gayme, ':', 1, 3) + 1, lengthb(ct.bacsi_gayme) - 1) AS dc_vongngoai
                        ,
                        ba.mabenhnhan       AS mabenhnhan,
                        nvl(ba.sobenhan, ba.sobenhan_ngt) AS sobenhan,
                        ct.catchi_sau7ngay,
                        ct.trinhtu_tt_pt,
                        'TRÌNH TỰ THỦ THUẬT PHẪU THUẬT' AS tieude,
                        taibien,
                        tuvong,
                        regexp_replace(regexp_replace(p_hinhanh1, 'data:image/jpeg;base64,', ''), 'data:image/png;base64,', '') hinhanh1
                        ,
                        regexp_replace(regexp_replace(p_hinhanh2, 'data:image/jpeg;base64,', ''), 'data:image/png;base64,', '') hinhanh2
                        ,
                        v_sogiuong          AS sogiuong,
                        v_sobuong           AS sobuong,
                        ct.dan_luu,
                        ct.bac,
                        ct.khac,
                        TO_CHAR(ct.ngay_cat_chi, 'DD/MM/YYYY') ngay_cat_chi,
                        TO_CHAR(ct.ngay_rut, 'DD/MM/YYYY') ngay_rut,
                        CASE
                            WHEN phanloaivetmo = 1 THEN
                                'Sạch'
                            WHEN phanloaivetmo = 2 THEN
                                'Sạch-nhiễm'
                            WHEN phanloaivetmo = 3 THEN
                                'Nhiễm'
                            WHEN phanloaivetmo = 4 THEN
                                'Bẩn'
                            WHEN phanloaivetmo = 5 THEN
                                'Bẩn/Nhiễm trùng = ô nhiễm, nhiễm trùng nặng'
                            ELSE
                                NULL
END phanloaivetmo,
                        '- Phẩu thuật viên chính: ' || nvl(bspt.TEN_NHANVIEN_CD, '') || '<br/>' ||
                        '- Bác sĩ gây mê: ' || nvl(bsgm.TEN_NHANVIEN_CD, '') || '<br/>' ||
                        CASE WHEN p_dvtt = '96161' THEn '- KTV gây mê: ' || nvl(ktvgm.TEN_NHANVIEN_CD, '') else '' end danhsachekiptrai,
                        '- Phẫu thuật viên phụ: ' || nvl(pmvt.TEN_NHANVIEN_CD, '') || '<br/>' ||
                        CASE WHEN p_dvtt = '96161' THEn '- Phẫu thuật viên phụ 2: ' || nvl(dc.TEN_NHANVIEN_CD, '') || '<br/>' else '' end danhsachekipphai,
                        v_thamso960616 ANCHUKY
                    FROM
                        noitru_cd_dichvu_ct            ct
                        left join his_fw.dm_nhanvien_cd   bspt ON ct.MA_BS_PTTP = bspt.ma_nhanvien
                        left join his_fw.dm_nhanvien_cd   bsgm ON ct.MA_BS_GAYME = bsgm.ma_nhanvien
                        left join his_fw.dm_nhanvien_cd   ktvgm ON ct.MA_KTV_GAYME = ktvgm.ma_nhanvien
                        left join his_fw.dm_nhanvien_cd   dc ON ct.MA_BS_DC_VONGTRONG = dc.ma_nhanvien
                        left join his_fw.dm_nhanvien_cd   pmvt ON ct.MA_PHU_MO_VONGTRONG = pmvt.ma_nhanvien
                        ,
                        dm_dich_vu_kham                dv,
                        noitru_benhan                  ba,
                        dm_loaihinh_dichvu             ldv,
                        noitru_cd_dichvu               cd,
                        dm_phong_benh                  pbenh,
                        his_fw.dm_phongban             pban, noitru_dieutri                 dt
                        LEFT JOIN his_public_list.dm_benh_nhan   bn ON dt.mabenhnhan = bn.ma_benh_nhan
                    WHERE
                        ct.dvtt = p_dvtt
                        AND ct.stt_benhan = ba.stt_benhan
                        AND cd.dvtt = p_dvtt
                        AND cd.so_phieu_dichvu = ct.so_phieu_dichvu
                        AND cd.sovaovien = ct.sovaovien
                        AND cd.sovaovien_dt = ct.sovaovien_dt
                        AND pbenh.ma_phong_benh = cd.phong_chi_dinh
                        AND pbenh.ma_phong_ban = pban.ma_phongban
                        AND ba.dvtt = p_dvtt
                        AND ct.stt_benhan = p_stt_benhan
                        AND ct.stt_dotdieutri = p_stt_dotdieutri
                        AND ct.stt_dieutri = p_stt_dieutri
                        AND ct.so_phieu_dichvu = p_so_phieu_dichvu
                        AND ct.ma_dv = p_ma_dv
                        AND ct.sovaovien = dt.sovaovien
                        AND ct.sovaovien_dt = dt.sovaovien_dt
                        AND ct.id_dieutri = dt.id_dieutri
                        AND dv.ma_nhom_dv = ldv.ma_loaihinh
                        AND dv.dvtt = p_dvtt
                        AND dv.ma_dv = p_ma_dv;

END IF;
END;