# Hướng dẫn Debug tính năng Autocomplete

## Bước 1: Mở trang điện tim
1. <PERSON><PERSON><PERSON> cập vào trang điện tim trong hệ thống
2. Chọn một bệnh nhân để xem form kết quả

## Bước 2: Mở Developer Tools
1. <PERSON>hấn F12 hoặc Ctrl+Shift+I
2. <PERSON><PERSON><PERSON><PERSON> sang tab Console

## Bước 3: Kiểm tra tính năng đã load
Trong console, gõ:
```javascript
debugAutocomplete()
```

Bạn sẽ thấy thông tin debug như:
- Số gợi ý hiện có
- Danh sách gợi ý
- Thông tin về datalist và textarea

## Bước 4: Kiểm tra thủ công
### Kiểm tra localStorage:
```javascript
console.log(localStorage.getItem('dientim_loidanbacsi_suggestions'))
```

### Thêm gợi ý thủ công:
```javascript
LoiDanBacSiSuggestions.addSuggestion('Test gợi ý thủ công')
```

### Kiểm tra datalist:
```javascript
console.log(document.getElementById('loidanbacsi-suggestions'))
```

### Tìm textarea:
```javascript
console.log($('textarea[name="data[LOIDANBACSI]"]'))
```

## Bước 5: Test tính năng
1. Click vào trường "Lời dặn bác sĩ"
2. Bắt đầu nhập một vài ký tự
3. Kiểm tra xem có dropdown gợi ý xuất hiện không

## Các vấn đề thường gặp

### 1. Không tìm thấy textarea
**Nguyên nhân:** Form chưa được render hoặc selector không đúng
**Giải pháp:** Đợi form load xong, kiểm tra lại selector

### 2. Datalist không hiển thị
**Nguyên nhân:** Trình duyệt không hỗ trợ hoặc CSS che khuất
**Giải pháp:** Kiểm tra trình duyệt, xem CSS

### 3. Gợi ý không được lưu
**Nguyên nhân:** localStorage bị vô hiệu hóa
**Giải pháp:** Bật localStorage trong trình duyệt

## Thông tin debug chi tiết

### Console messages cần chú ý:
- `🚀 Tính năng autocomplete đã được tải`
- `🔧 Bắt đầu thiết lập autocomplete cho LOIDANBACSI`
- `✅ Đã thiết lập autocomplete thành công`

### Nếu thấy lỗi:
- `⚠️ Không tìm thấy component LOIDANBACSI`
- `❌ Đã thử X lần nhưng không tìm thấy textarea`

## Cách khắc phục

### 1. Nếu không tìm thấy component:
```javascript
// Kiểm tra form có tồn tại không
console.log(formKetQuaDienTim)

// Kiểm tra tất cả components
if (formKetQuaDienTim) {
    console.log(formKetQuaDienTim.components)
}
```

### 2. Nếu không tìm thấy textarea:
```javascript
// Tìm tất cả textarea
console.log($('#formKetQua textarea'))

// Kiểm tra form đã render chưa
setTimeout(() => {
    console.log('Textarea sau 5s:', $('#formKetQua textarea'))
}, 5000)
```

### 3. Force setup lại:
```javascript
// Gọi lại hàm setup
if (typeof setupLoiDanBacSiAutocomplete === 'function' && formKetQuaDienTim) {
    setupLoiDanBacSiAutocomplete(formKetQuaDienTim)
}
```

## Test hoàn chỉnh

Chạy script này để test toàn bộ:
```javascript
// Test script hoàn chỉnh
function testAutocompleteComplete() {
    console.log('=== BẮT ĐẦU TEST AUTOCOMPLETE ===')
    
    // 1. Kiểm tra object tồn tại
    console.log('1. LoiDanBacSiSuggestions:', typeof LoiDanBacSiSuggestions)
    
    // 2. Thêm test data
    LoiDanBacSiSuggestions.addSuggestion('Test suggestion 1')
    LoiDanBacSiSuggestions.addSuggestion('Test suggestion 2')
    
    // 3. Kiểm tra localStorage
    console.log('2. LocalStorage:', localStorage.getItem('dientim_loidanbacsi_suggestions'))
    
    // 4. Tạo datalist
    var datalistId = LoiDanBacSiSuggestions.createDatalist()
    console.log('3. Datalist ID:', datalistId)
    console.log('4. Datalist element:', document.getElementById(datalistId))
    
    // 5. Tìm textarea
    var textarea = $('textarea[name="data[LOIDANBACSI]"]')
    console.log('5. Textarea found:', textarea.length)
    
    if (textarea.length > 0) {
        // 6. Setup thủ công
        textarea.attr('list', datalistId)
        console.log('6. Đã setup list attribute')
        
        // 7. Test focus
        textarea.focus()
        console.log('7. Đã focus vào textarea')
    }
    
    console.log('=== KẾT THÚC TEST ===')
}

// Chạy test
testAutocompleteComplete()
```
