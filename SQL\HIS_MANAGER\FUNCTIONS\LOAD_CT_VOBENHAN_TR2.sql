create or replace FUNCTION his_manager.load_ct_vobenhan_tr2 (
    p_id NUMBER,
    p_loaibenhan VARCHAR2
) RETURN SYS_REFCURSOR IS

    cur                 SYS_REFCURSOR;
    p_sql               CLOB;
    v_nv_lambenhan      VARCHAR2(100);
    v_ngay_lambenhan    VARCHAR2(50);
    v_dieutri_tungay    VARCHAR2(100) DEFAULT '';
    v_dieutri_denngay   VARCHAR2(100) DEFAULT '';
    v_icd_ravien        VARCHAR2(500) DEFAULT '';
    v_ten_icd_ravien    VARCHAR2(500) DEFAULT '';
    v_ts_vba_12         VARCHAR2(10) DEFAULT '';
    p_dvtt              VARCHAR2(5);
    v_mabenhnhan        NUMBER;
    v_tenbenhnhan       VARCHAR2(500) DEFAULT '';
    v_id_vbame          NUMBER := 0;
    v_sovaovien_me      NUMBER;
    v_sobenhan_me       VARCHAR2(100);
    v_loaibame          VARCHAR2(250);
    p_sql_hosome        VARCHAR2(4000);
    v_tinhtrangoi       VARCHAR2(10);
    v_tinhtrangoi_con   VARCHAR2(10);
    v_tinhtrangoi_vo    VARCHAR2(10);
    v_vooingay          VARCHAR2(100);
    v_mausacnuocoi      VARCHAR2(250);
    v_phuongphapde varchar2(10);
    v_lydocanthiep varchar2(1000);
    v_tennguoidode varchar2(300);
    v_makhoatheodoi varchar2(100);
    v_thamso960616 number(10);
BEGIN
BEGIN
SELECT
    vba.dvtt,
    vba.mabenhnhan
INTO
    p_dvtt,
    v_mabenhnhan
FROM
    his_manager.noitru_vobenhan vba
WHERE
    vba.id = p_id
  AND ROWNUM <= 1;

EXCEPTION
        WHEN no_data_found THEN
            p_dvtt := '';
END;

BEGIN
SELECT
    vba.ten_benh_nhan,
    nvl(vbame.id, 0),
    vbame.id_vba,
    gcs.sovaovien,
    vba.sobenhan_hosome
INTO
    v_tenbenhnhan,
    v_id_vbame,
    v_loaibame,
    v_sovaovien_me,
    v_sobenhan_me
FROM
    his_public_list.dm_benh_nhan    vba
        LEFT JOIN his_manager.cmu_ma_gcsguibhxh   gcs ON gcs.id_giaychungsinh = vba.id_giaychungsinh
        LEFT JOIN his_manager.noitru_vobenhan     vbame ON vbame.mabenhan = vba.sobenhan_hosome
        AND vbame.dvtt = gcs.dvtt
        AND vbame.sovaovien = gcs.sovaovien
        AND vbame.DELETED = 0
WHERE
    ma_benh_nhan = v_mabenhnhan;

EXCEPTION
        WHEN no_data_found THEN
            v_tenbenhnhan := '';
            v_id_vbame := 0;
            v_loaibame := NULL;
            v_sovaovien_me := NULL;
            v_sobenhan_me := NULL;
END;

    v_ts_vba_12 := his_manager.func_tham_so_vba(p_dvtt, 12);
BEGIN
SELECT
    TO_CHAR(DECODE(TO_CHAR(ba.nhapvientuphanhe_ngoaitru), '1', tiepnhan.thoi_gian_tiep_nhan, ba.ngaynhapvien), 'DD/MM/YYYY'
    ),
    CASE
        WHEN ddt.tt_dotdieutri = 3
            OR ddt.tt_dotdieutri = 6 THEN
            TO_CHAR(xv.thoigianxuatvien, 'DD/MM/YYYY')
        WHEN ddt.tt_dotdieutri = 4 THEN
            TO_CHAR(cv.thoigianchuyenvien, 'DD/MM/YYYY')
        WHEN ddt.tt_dotdieutri = 5 THEN
            TO_CHAR(tv.thoigiantuvong, 'DD/MM/YYYY')
        WHEN ddt.tt_dotdieutri = 7 THEN
            TO_CHAR(ddt.ngayra, 'DD/MM/YYYY')
        ELSE
            ' '
        END,
    CASE
        WHEN ddt.tt_dotdieutri = 3
            OR ddt.tt_dotdieutri = 6 THEN
            nvl(xv.icd_benhchinh, ' ') -- Xuất viện
        WHEN ddt.tt_dotdieutri = 4 THEN
            nvl(cv.icd_benhchinh, ' ') -- chuyển viện
        WHEN ddt.tt_dotdieutri = 5 THEN
            nvl(tv.icd_benhchinh, ' ') -- tử vong
        WHEN ddt.tt_dotdieutri = 7 THEN
            nvl(ba.icd_nhapvien, ' ') -- kết thúc đợt điều trị
        ELSE
            NULL
        END,
    CASE
        WHEN ddt.tt_dotdieutri = 3
            OR ddt.tt_dotdieutri = 6 THEN
            nvl(xv.ten_benhchinh, ' ') -- Xuất viện
        WHEN ddt.tt_dotdieutri = 4 THEN
            nvl(cv.ten_benhchinh, ' ') -- chuyển viện
        WHEN ddt.tt_dotdieutri = 5 THEN
            nvl(tv.ten_benhchinh, ' ') -- tử vong
        WHEN ddt.tt_dotdieutri = 7 THEN
            nvl(ba.tenbenhchinh_nhapvien, ' ') -- kết thúc đợt điều trị
        ELSE
            NULL
        END
INTO
    v_dieutri_tungay,
    v_dieutri_denngay,
    v_icd_ravien,
    v_ten_icd_ravien
FROM
    his_manager.noitru_vobenhan              vba
        JOIN his_manager.noitru_benhan                ba ON ba.sovaovien = vba.sovaovien
        JOIN his_manager.noitru_dotdieutri            ddt ON ddt.sovaovien = vba.sovaovien
        AND ddt.sovaovien_dt = vba.sovaovien_dt
        LEFT JOIN his_manager.kb_tiep_nhan                 tiepnhan ON tiepnhan.dvtt = ba.dvtt
        AND 'kb_' || tiepnhan.id_tiepnhan = ba.makhambenhngoaitru_nhapvien
        LEFT JOIN his_manager.noitru_xuatvien              xv ON xv.sovaovien_dt = ddt.sovaovien_dt
        AND xv.dvtt = ddt.dvtt
        LEFT JOIN his_manager.noitru_chuyentuyenbenhnhan   cv ON cv.sovaovien_dt = ddt.sovaovien_dt
        AND cv.dvtt = ddt.dvtt
        LEFT JOIN his_manager.noitru_tuvong                tv ON tv.sovaovien_dt = ddt.sovaovien_dt
        AND tv.dvtt = ddt.dvtt
WHERE
    vba.id = p_id;

EXCEPTION
        WHEN no_data_found THEN
            v_dieutri_tungay := NULL;
            v_dieutri_denngay := NULL;
            v_icd_ravien := NULL;
            v_ten_icd_ravien := NULL;
END;

BEGIN
SELECT
    concat(concat(cd.mota_chucdanh, '. '), nv.ten_nhanvien),
    CASE
        WHEN instr(cha.id_vba, 'YHCT') > 0 THEN
            CASE
                WHEN v_ts_vba_12 = '1' THEN
                    TO_CHAR(DECODE(TO_CHAR(ba.nhapvientuphanhe_ngoaitru), '1', tn.thoi_gian_tiep_nhan, ba.ngaynhapvien), 'HH24 "Giờ" MI "phút, ngày" DD "tháng" MM "năm" YYYY'
                    )
                ELSE
                    TO_CHAR(cha.time_create, 'HH24 "Giờ" MI "phút, ngày" DD "tháng" MM "năm" YYYY')
                END
        ELSE
            CASE
                WHEN v_ts_vba_12 = '1' THEN
                    TO_CHAR(DECODE(TO_CHAR(ba.nhapvientuphanhe_ngoaitru), '1', tn.thoi_gian_tiep_nhan, ba.ngaynhapvien), '"Ngày" DD "tháng" MM "năm" YYYY'
                    )
                ELSE
                    TO_CHAR(cha.time_create, '"Ngày" DD "tháng" MM "năm" YYYY')
                END
        END AS ngay_lam_benh_an
INTO
    v_nv_lambenhan,
    v_ngay_lambenhan
FROM
    his_manager.noitru_vobenhan   cha
        INNER JOIN his_manager.noitru_benhan     ba ON ba.dvtt = cha.dvtt
        AND ba.sovaovien = cha.sovaovien
        INNER JOIN his_fw.dm_nhanvien            nv ON nv.ma_nhanvien = cha.manv_lambenhan
        INNER JOIN his_fw.dm_chucdanh_nhanvien   cd ON cd.ma_chucdanh = nv.chucdanh_nhanvien
        LEFT JOIN his_manager.kb_tiep_nhan      tn ON tn.dvtt = ba.dvtt
        AND tn.sovaovien = ba.sovaovien_ngt
WHERE
    cha.id = p_id;

EXCEPTION
        WHEN no_data_found THEN
            v_nv_lambenhan := '';
            v_ngay_lambenhan := '';
END;

    v_thamso960616 := cmu_tsdv(p_dvtt, 960616, 0);

    IF p_loaibenhan = 'SANKHOA' THEN
        OPEN cur FOR SELECT
                                                   TO_CHAR(t.kinhcuoi_tungay, '"Ngày" DD "tháng" MM "năm" YYYY') AS kinhcuoi_tungay_varchar,
                                                   TO_CHAR(t.kinhcuoi_tungay, 'DD/MM/YYYY') AS kinhcuoi_tungay_date,
                                                   TO_CHAR(t.kinhcuoi_denngay, '"Ngày" DD "tháng" MM "năm" YYYY') AS kinhcuoi_denngay_varchar,
                                                   TO_CHAR(t.kinhcuoi_denngay, 'DD/MM/YYYY') AS kinhcuoi_denngay_date,
                                                   TO_CHAR(t.ngaygio_chuyenda, 'HH24 "giờ" MI "phút, ngày" DD "tháng" MM "năm" YYYY') AS ngaygio_chuyenda_varchar
                                                       ,
                                                   TO_CHAR(t.ngaygio_chuyenda, 'DD/MM/YYYY HH24:MI:SS') AS ngaygio_chuyenda_date,
                                                   TO_CHAR(t.ngaygio_oivo, 'HH24 "giờ" MI "phút, ngày" DD "tháng" MM "năm" YYYY') AS ngaygio_oivo_varchar,
                                                   TO_CHAR(t.ngaygio_oivo, 'DD/MM/YYYY HH24:MI:SS') AS ngaygio_oivo_date,
                                                   TO_CHAR(t.ngay_du_sinh, 'DD/MM/YYYY') AS ngaydusinh_date,
                                                   t.*,
                                                   ts.*,
                                                   v_nv_lambenhan     AS nv_lambenhan,
                                                   v_ngay_lambenhan   AS ngay_lambenhan,
                                                   v_tenbenhnhan      ten_benh_nhan,
                                                   nvbs.TEN_NHANVIEN_CD TENBACSILAMBENHAN,
                                                   v_thamso960616 ANCHUKY
                     FROM
                                                   his_manager.vba_sankhoa                   t
                                                       LEFT JOIN his_manager.ba_sankhoa_tien_su_san_khoa   ts ON ts.id_benhan = p_id
                                                       AND ts.dvtt = t.dvtt
                                                       LEFT JOIN HIS_FW.DM_NHANVIEN_CD nvbs ON TO_CHAR(nvbs.MA_NHANVIEN) = TO_CHAR(MABACSILAMBENHAN)
                     WHERE
                                                   t.id = p_id
                     ORDER BY
                                                   ts.so_lan_co_thai;

ELSIF p_loaibenhan = 'NGOAITRURHM' THEN
BEGIN
OPEN cur FOR SELECT
                             rhm.*,
                             nvl(v_dieutri_tungay, TO_CHAR(rhm.dieutri_tungay, 'DD/MM/YYYY')) AS dieutritungay,
                             nvl(v_dieutri_denngay, TO_CHAR(rhm.dieutri_denngay, 'DD/MM/YYYY')) AS dieutridenngay,
                             v_nv_lambenhan     AS nv_lambenhan,
                             v_ngay_lambenhan   AS ngay_lambenhan,
                             rhm.giamdoc        AS giamdocbenhvien,
                             v_thamso960616 ANCHUKY
                         FROM
                             his_manager.vba_ngoaitrurhm rhm
                         WHERE
                             rhm.id = p_id
                             AND ROWNUM <= 1;

EXCEPTION
            WHEN no_data_found THEN
                OPEN cur FOR SELECT
                                 NULL
                             FROM
                                 dual;

END;
    ELSIF p_loaibenhan = 'PHCN_NHI' THEN
BEGIN
OPEN cur FOR SELECT
                             ba.thongtin_phcn_nhi_clob,
--               v_nv_lambenhan as NV_LAMBENHAN,
                             concat(concat(cd.mota_chucdanh, '. '), nv.ten_nhanvien) AS nv_lambenhan,
                             v_ngay_lambenhan AS ngay_lambenhan,
                             ba.mabacsilambenhan,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnLyDoVaoVienNhi' RETURNING VARCHAR2), ' ') AS lydo_vaovien
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnQuaTrinhBenhLyNhi' RETURNING VARCHAR2), ' ') AS quatrinh_benhly
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.tienSuSanKhoa' RETURNING VARCHAR2), ' ') AS nhi_tiensu_sankhoa
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiTsMeTuoiMe' RETURNING VARCHAR2), ' ') AS nhi_tuoime_khisinh
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiTsMeTinhTrangSK' RETURNING VARCHAR2), ' ') AS nhi_ttme_khimangthai
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiTsConConThu' RETURNING VARCHAR2), ' ') AS nhi_conthu
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiTsConTuoiThai' RETURNING VARCHAR2), ' ') AS nhi_tuoi_thai
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhitienSuThai' RETURNING VARCHAR2), ' ') AS nhi_tinh_trang_con
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiCanNangKhiSinh' RETURNING VARCHAR2), ' ') AS nhi_cannangcon_khisinh
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiTTCanNangKhiSinh' RETURNING VARCHAR2), ' ') AS nhi_tt_cannangcon
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiTTKSDeThuong' RETURNING VARCHAR2), ' ') AS nhi_tinhtrang_khisinh
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiTTSS' RETURNING VARCHAR2), ' ') AS nhi_tinhtrang_sausinh
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiTiemPhongVacxin' RETURNING VARCHAR2), ' ') AS nhi_tiemphong_vacxin
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiSoConTrongGiaDinh' RETURNING VARCHAR2), ' ') AS nhi_socontrong_giadinh
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiSoTreBatThuong' RETURNING VARCHAR2), ' ') AS nhi_sotre_batthuong
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.gdCoNhiemChatDocDaCam' RETURNING VARCHAR2), ' ') AS nhi_gd_conguoi_nhiem_cdmdc
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiTheTrangChung' RETURNING VARCHAR2), ' ') AS nhi_tinhtrang_chung
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiCanNang' RETURNING VARCHAR2), ' ') AS cannang_rp
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiChieuCao' RETURNING VARCHAR2), ' ') AS chieucao_rp
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiVongDau' RETURNING VARCHAR2), ' ') AS vongdau_rp
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiNhietDo' RETURNING VARCHAR2), ' ') AS nhietdo_rp
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiMach' RETURNING VARCHAR2), ' ') AS mach_rp,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiHuyetApTren' RETURNING VARCHAR2), ' ') AS huyetaptren_rp
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiHuyetApDuoi' RETURNING VARCHAR2), ' ') AS huyetapduoi_rp
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiNhipTho' RETURNING VARCHAR2), ' ') AS nhiptho_rp
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiTamThanThanKinh' RETURNING VARCHAR2), ' ') AS cq_tamthan_thankinh
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiHeCoXuongKhop' RETURNING VARCHAR2), ' ') AS cq_cxk_cotsong
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiCacChuyenMonKhac' RETURNING VARCHAR2), ' ') AS cq_chuyenkhoa_khac
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiXetNghiemCls' RETURNING VARCHAR2), ' ') AS xetnghiem_cls
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiTomTatBenhAn' RETURNING VARCHAR2), ' ') AS tomtat_benhan
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiBenhAnMaBenhTatChinh' RETURNING VARCHAR2), ' ') AS
                             mabenhtat_chinh,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiBenhAnTenBenhTatChinh' RETURNING VARCHAR2), ' ')
                             AS tenbenhtat_chinh,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiBenhAnMaBenhKemTheo' RETURNING VARCHAR2), ' ') AS
                             mabenhtat_kemtheo,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiBenhAnTenBenhKemTheo' RETURNING VARCHAR2), ' ') AS
                             tenbenhtat_kemtheo,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiBenhAnMaBenhPhanBiet' RETURNING VARCHAR2), ' ') AS
                             mabenh_phanbiet,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiBenhAnTenBenhPhanBiet' RETURNING VARCHAR2), ' ')
                             AS tenbenh_phanbiet,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiChiDinhDieuTriCanThiep' RETURNING VARCHAR2), ' '
                             ) AS nhi_chidinh_canthiep_phcn,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiDieuTriBlKemTheo' RETURNING VARCHAR2), ' ') AS nhi_dieutri_benhly_kemtheo
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiCheDoCsNguoiBenh' RETURNING VARCHAR2), ' ') AS nhi_chedo_chamsoc_nguoibenh
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.phcnNhiHoiNhapXaHoi' RETURNING VARCHAR2), ' ') AS nhi_hoinhap_xahoi
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_nhi_clob, '$.ngayKhamBenh' RETURNING VARCHAR2), ' ') AS ngaybslambenhan,
                             v_thamso960616 ANCHUKY
                         FROM
                             his_manager.vba_phcn_nhi      ba
                             JOIN his_manager.noitru_vobenhan   cha ON cha.id = ba.id
                             JOIN his_fw.dm_nhanvien            nv ON nv.ma_nhanvien = cha.manv_lambenhan
                             JOIN his_fw.dm_chucdanh_nhanvien   cd ON cd.ma_chucdanh = nv.chucdanh_nhanvien
                         WHERE
                             ba.id = p_id;

END;
    ELSIF p_loaibenhan = 'PHCN_BANT' THEN
BEGIN
OPEN cur FOR SELECT
                             thongtin_phcn_bant_clob AS thongtin_benhan,
                             TO_CHAR(ngay_khambenh, 'dd/mm/yyyy') AS ngay_kham_benh,
                             TO_CHAR(ngay_khambenh, '"Ngày" dd "tháng" mm "năm" yyyy') AS ngay_kham_benh_char,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.lyDoNhapVien' RETURNING VARCHAR2), ' ') AS lydovaovien
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.quaTrinhBenhLy' RETURNING VARCHAR2), ' ') AS benhsu,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.diUng' RETURNING VARCHAR2), ' ') AS diung,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.banThan' RETURNING VARCHAR2), ' ') AS tiensubanthan,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.giaDinh' RETURNING VARCHAR2), ' ') AS tiensugiadinh,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.khamBenhToanThan' RETURNING VARCHAR2), ' ') AS khamtoanthan
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.cacBoPhan' RETURNING VARCHAR2), ' ') AS cacbophan,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.Mach' RETURNING VARCHAR2), ' ') AS mach,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.nhietDo' RETURNING VARCHAR2), ' ') AS nhietdo,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.huyetApTren' RETURNING VARCHAR2), ' ') AS huyetaptren,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.huyetApThap' RETURNING VARCHAR2), ' ') AS huyetapduoi,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.nhipTho' RETURNING VARCHAR2), ' ') AS nhiptho,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.canNang' RETURNING VARCHAR2), ' ') AS cannang,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.Bmi' RETURNING VARCHAR2), ' ') AS bmi,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.tomTatKetQuaCanLamSang' RETURNING VARCHAR2), ' ') AS tomtatketquacls
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.chanDoanBanDau' RETURNING VARCHAR2), ' ') AS chandoan_bandau
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.daXuLy' RETURNING VARCHAR2), ' ') AS daxuly,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.chanDoanRaVien' RETURNING VARCHAR2), ' ') AS chandoan_ravien
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.tenChanDoanRaVien' RETURNING VARCHAR2), ' ') AS ten_chandoan_ravien
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.chanDoanRaVien' RETURNING VARCHAR2), ' ') AS chandoan_ravien
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.chanDoanRaVien1' RETURNING VARCHAR2), ' ') AS chandoan_ravien_1
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.chanDoanRaVien2' RETURNING VARCHAR2), ' ') AS chandoan_ravien_2
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.chanDoanRaVien3' RETURNING VARCHAR2), ' ') AS chandoan_ravien_3
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.chanDoanRaVien4' RETURNING VARCHAR2), ' ') AS chandoan_ravien_4
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.dieuTriNgoaiTruTuNgay' RETURNING VARCHAR2), ' ') AS dt_ngoaitru_tungay
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.dieuTriNgoaiTruDenNgay' RETURNING VARCHAR2), ' ') AS dt_ngoaitru_denngay
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.giamDocBenhVien' RETURNING VARCHAR2), ' ') AS giamdoc_bv
                             ,
                             nvl(JSON_VALUE(ba.thongtin_phcn_bant_clob, '$.bacSyKhamBenh' RETURNING VARCHAR2), ' ') AS bacsy_khambenh,
                             v_thamso960616 ANCHUKY
                         FROM
                             his_manager.vba_phcn_bant ba
                         WHERE
                             ba.id = p_id;

END;
    ELSIF p_loaibenhan = 'NGOAITRU_CHUNG' THEN
BEGIN
OPEN cur FOR SELECT
                             thongtin_ngtru_chung_clob AS thongtin_benhan,
                             TO_CHAR(ngay_khambenh, 'dd/mm/yyyy') AS ngay_kham_benh,
                             TO_CHAR(ba.ngaybslambenhan, 'dd/mm/yyyy') AS ngaybslambenhan,
                             TO_CHAR(ngay_khambenh, '"Ngày" dd "tháng" mm "năm" yyyy') AS ngay_kham_benh_char,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.lyDoNhapVien' RETURNING VARCHAR2), '') AS lydovaovien
                             ,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.quaTrinhBenhLy' RETURNING VARCHAR2), '') AS benhsu,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.diUng' RETURNING VARCHAR2), '') AS diung,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.banThan' RETURNING VARCHAR2), '') AS tiensubanthan,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.giaDinh' RETURNING VARCHAR2), '') AS tiensugiadinh,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.khamBenhToanThan' RETURNING VARCHAR2), '') AS khamtoanthan
                             ,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.cacBoPhan' RETURNING VARCHAR2), '') AS cacbophan,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.Mach' RETURNING VARCHAR2), '') AS mach,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.nhietDo' RETURNING VARCHAR2), '') AS nhietdo,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.huyetApTren' RETURNING VARCHAR2), '') AS huyetaptren
                             ,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.huyetApThap' RETURNING VARCHAR2), '') AS huyetapduoi
                             ,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.nhipTho' RETURNING VARCHAR2), '') AS nhiptho,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.canNang' RETURNING VARCHAR2), '') AS cannang,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.chieuCao' RETURNING VARCHAR2), '') AS chieucao,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.Bmi' RETURNING VARCHAR2), '') AS bmi,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.tomTatKetQuaCanLamSang' RETURNING VARCHAR2), '') AS tomtatketquacls
                             ,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.chanDoanBanDau' RETURNING VARCHAR2), '') AS chandoan_bandau
                             ,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.daXuLy' RETURNING VARCHAR2), '') AS daxuly,
                             nvl(v_ten_icd_ravien, nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.tenChanDoanRaVien' RETURNING VARCHAR2
                             ), '')) AS ten_chandoan_ravien,
                             nvl(v_icd_ravien, nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.chanDoanRaVien' RETURNING VARCHAR2
                             ), '')) AS chandoan_ravien,
                             nvl(substr(nvl(v_icd_ravien, JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.chanDoanRaVien' RETURNING VARCHAR2
                             )), 1, 1), '') AS chandoan_ravien_1,
                             nvl(substr(nvl(v_icd_ravien, JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.chanDoanRaVien' RETURNING VARCHAR2
                             )), 2, 1), '') AS chandoan_ravien_2,
                             nvl(DECODE(TO_CHAR(length(nvl(v_icd_ravien, JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.chanDoanRaVien'
                             RETURNING VARCHAR2)))), '3', substr(nvl(v_icd_ravien, JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.chanDoanRaVien'
                             RETURNING VARCHAR2)), 3, 1), '5', substr(nvl(v_icd_ravien, JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.chanDoanRaVien'
                             RETURNING VARCHAR2)), 3, 2), '4', substr(nvl(v_icd_ravien, JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.chanDoanRaVien'
                             RETURNING VARCHAR2)), 3, 1), ' '), '') AS chandoan_ravien_3,
                             nvl(DECODE(TO_CHAR(length(nvl(v_icd_ravien, JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.chanDoanRaVien'
                             RETURNING VARCHAR2)))), '3', ' ', '5', substr(nvl(v_icd_ravien, JSON_VALUE(ba.thongtin_ngtru_chung_clob
                             , '$.chanDoanRaVien' RETURNING VARCHAR2)), 5, 1), '4', substr(nvl(v_icd_ravien, JSON_VALUE(ba.thongtin_ngtru_chung_clob
                             , '$.chanDoanRaVien' RETURNING VARCHAR2)), 4, 1), ''), '') AS chandoan_ravien_4
               --             , NVL(JSON_VALUE(BA.THONGTIN_NGTRU_CHUNG_CLOB,  '$.chanDoanRaVien1' RETURNING VARCHAR2), ' ')  AS CHANDOAN_RAVIEN_1
               --             , NVL(JSON_VALUE(BA.THONGTIN_NGTRU_CHUNG_CLOB,  '$.chanDoanRaVien2' RETURNING VARCHAR2), ' ')  AS CHANDOAN_RAVIEN_2
               --             , NVL(JSON_VALUE(BA.THONGTIN_NGTRU_CHUNG_CLOB,  '$.chanDoanRaVien3' RETURNING VARCHAR2), ' ')  AS CHANDOAN_RAVIEN_3
               --             , NVL(JSON_VALUE(BA.THONGTIN_NGTRU_CHUNG_CLOB,  '$.chanDoanRaVien4' RETURNING VARCHAR2), ' ')  AS CHANDOAN_RAVIEN_4
                             ,
                             nvl(v_dieutri_tungay, nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.dieuTriNgoaiTruTuNgay' RETURNING
                             VARCHAR2), '')) AS dt_ngoaitru_tungay,
                             nvl(v_dieutri_denngay, nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.dieuTriNgoaiTruDenNgay' RETURNING
                             VARCHAR2), '')) AS dt_ngoaitru_denngay,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.maKhoaGiamDoc' RETURNING VARCHAR2), '') AS makhoagiamdoc
                             ,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.giamDocBenhVien' RETURNING VARCHAR2), '') AS giamdoc_bv
                             ,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.bacSyKhamBenh' RETURNING VARCHAR2), '') AS bacsy_khambenh
                             ,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.chucDanhBenhVien' RETURNING VARCHAR2), 'Giám đốc bệnh viện'
                             ) AS chucdanh_benhvien,
                             nvl(JSON_VALUE(ba.thongtin_ngtru_chung_clob, '$.chucDanhKhoa' RETURNING VARCHAR2), 'Bác sỹ làm bệnh án'
                             ) AS chuc_danh_khoa,
                             v_thamso960616 ANCHUKY
                         FROM
                             his_manager.vba_ngoaitru_chung ba
                         WHERE
                             ba.id = p_id;

END;
    ELSIF p_loaibenhan = 'NGOAITRUTMH' THEN
BEGIN
            p_sql := 'SELECT BA.*, :v_dieutri_tungay  AS DIEUTRITUNGAY, NVL(:v_dieutri_denngay, TO_CHAR(BA.DIEUTRIDENNGAY, ''DD/MM/YYYY'')) AS DIEUTRIDENNGAY,
                         NVL(SUBSTR(BA.ICD_BENHCHINH, 1, 1), '' '') AS ICD_BENHCHINH_1,
                         NVL(SUBSTR(BA.ICD_BENHCHINH, 2, 1), '' '') AS ICD_BENHCHINH_2,
                         NVL(DECODE(TO_CHAR(LENGTH(BA.ICD_BENHCHINH)),
                                    ''3'',
                                    SUBSTR(BA.ICD_BENHCHINH, 3, 1),
                                    ''5'',
                                    SUBSTR(BA.ICD_BENHCHINH, 3, 2),
                                    ''4'',
                                    SUBSTR(BA.ICD_BENHCHINH, 3, 1),
                                    '' ''),
                             '' '') AS ICD_BENHCHINH_3,
                         NVL(DECODE(TO_CHAR(LENGTH(BA.ICD_BENHCHINH)),
                                    ''3'',
                                    '' '',
                                    ''5'',
                                    SUBSTR(BA.ICD_BENHCHINH, 5, 1),
                                    ''4'',
                                    SUBSTR(BA.ICD_BENHCHINH, 4, 1),
                                    '' ''),
                             '' '') AS ICD_BENHCHINH_4,
                         NVL(SUBSTR(BA.ICD_BENHPHU, 1, 1), '' '') AS ICD_BENHPHU_1,
                         NVL(SUBSTR(BA.ICD_BENHPHU, 2, 1), '' '') AS ICD_BENHPHU_2,
                         NVL(DECODE(TO_CHAR(LENGTH(BA.ICD_BENHPHU)),
                                    ''3'',
                                    SUBSTR(BA.ICD_BENHPHU, 3, 1),
                                    ''5'',
                                    SUBSTR(BA.ICD_BENHPHU, 3, 2),
                                    ''4'',
                                    SUBSTR(BA.ICD_BENHPHU, 3, 1),
                                    '' ''),
                             '' '') AS ICD_BENHPHU_3,
                         NVL(DECODE(TO_CHAR(LENGTH(BA.ICD_BENHPHU)),
                                    ''3'',
                                    '' '',
                                    ''5'',
                                    SUBSTR(BA.ICD_BENHPHU, 5, 1),
                                    ''4'',
                                    SUBSTR(BA.ICD_BENHPHU, 4, 1),
                                    '' ''),
                             '' '') AS ICD_BENHPHU_4,
                         CONCAT(CONCAT(CD.MOTA_CHUCDANH, ''.''), NV.TEN_NHANVIEN) AS NV_LAMBENHAN,
                          :v_ngay_lambenhan AS NGAY_LAMBENHAN,
                          :v_thamso960616 ANCHUKY
                    FROM HIS_MANAGER.VBA_NGOAITRUTMH BA
                    JOIN HIS_MANAGER.NOITRU_VOBENHAN CHA
                      ON CHA.ID = BA.ID
                    JOIN HIS_FW.DM_NHANVIEN NV
                      ON NV.MA_NHANVIEN = CHA.MANV_LAMBENHAN
                    JOIN HIS_FW.DM_CHUCDANH_NHANVIEN CD
                      ON CD.MA_CHUCDANH = NV.CHUCDANH_NHANVIEN
                   WHERE BA.ID = :pId'
            ;
OPEN cur FOR p_sql
                USING v_dieutri_tungay, v_dieutri_denngay, v_ngay_lambenhan, v_thamso960616, p_id;

EXCEPTION
            WHEN no_data_found THEN
                OPEN cur FOR SELECT
                                 NULL
                             FROM
                                 dual;

END;
    ELSIF p_loaibenhan = 'SOSINH' THEN
        IF v_id_vbame <> 0 THEN
BEGIN
                p_sql_hosome := 'SELECT CASE WHEN NVL(TO_NUMBER(BA.BOXOIPHONG),0) + NVL(TO_NUMBER(BA.BOXOIDET),0) + NVL(TO_NUMBER(BA.BOXOIQUALE),0) > 0 THEN 1 '
                                || ' WHEN NVL(TO_NUMBER(BA.BOXTUNHIEN),0) + NVL(TO_NUMBER(BA.BOXBAMOI), 0) > 0 THEN 2 ELSE null END, '
                                || ' CASE WHEN BA.BOXOIPHONG = 1 THEN 1 WHEN BA.BOXOIDET = 1 then 2 WHEN BA.BOXOIQUALE = 1 THEN 3 ELSE null END, '
                                || ' CASE WHEN BA.BOXTUNHIEN = 1 THEN 1 WHEN BA.BOXBAMOI = 1 THEN 2 ELSE null END, '
                                || ' TO_CHAR(BA.NGAYGIO_OIVO, ''DD/MM/YYYY HH24:MI''), '
                                || ' BA.MAUSACNUOCOI, '
                                || ' JSON_VALUE(TINHTRANG_SANPHU_SAUDE,  ''$.phuongPhapDe'' RETURNING VARCHAR2), '
                                || ' JSON_VALUE(TINHTRANG_SANPHU_SAUDE,  ''$.lyDoCanThiep'' RETURNING VARCHAR2), '
                                || ' nv.TEN_NHANVIEN, '
                                || ' nv.MA_PHONGBAN '
                                || ' FROM HIS_MANAGER.VBA_'
                                || v_loaibame
                                || ' BA '
                                || ' JOIN HIS_MANAGER.VOBENHAN_TONGKETBA TK on BA.ID = TK.ID_HSBA '
                                || ' LEFT JOIN HIS_FW.DM_NHANVIEN nv on JSON_VALUE(TK.NGUOITHEODOI,  ''$.maNhanVienTheoDoi'' RETURNING NUMBER) = nv.ma_nhanvien '
                                || ' WHERE BA.ID = :P_ID';

EXECUTE IMMEDIATE p_sql_hosome
    INTO
                    v_tinhtrangoi,
                    v_tinhtrangoi_con,
                    v_tinhtrangoi_vo,
                    v_vooingay,
                    v_mausacnuocoi,
                    v_phuongphapde,
                    v_lydocanthiep,
                    v_tennguoidode,
                    v_makhoatheodoi
                    USING v_id_vbame;
EXCEPTION
                WHEN no_data_found THEN
                    v_tinhtrangoi := NULL;
                    v_tinhtrangoi_con := NULL;
                    v_tinhtrangoi_vo := NULL;
                    v_vooingay := NULL;
                    v_mausacnuocoi := NULL;
                    v_phuongphapde := NULL;
                    v_lydocanthiep := NULL;
                    v_tennguoidode := NULL;
                    v_makhoatheodoi := NULL;
END;

            v_phuongphapde := CASE WHEN v_phuongphapde = 1 then 1
                WHEN TO_NUMBER(v_phuongphapde) in (2,3,4,5,6) then 2
                ELSE null
END;
END IF;

BEGIN
OPEN cur FOR SELECT
                             ba.id,
                             ba.dvtt,
                             ba.lydovaovien,
                             ba.hoibenh,
                             nvl(ba.oivongay, v_vooingay) oivongay,
                             nvl(ba.mausac, v_mausacnuocoi) mausac,
                             nvl(ba.dethuong, v_phuongphapde) dethuong,
                             ba.dengay,
                             nvl(ba.lydocanthiep, v_lydocanthiep) lydocanthiep,
                             ba.radoikhocngay,
                             nvl(ba.hotennguoidode, v_tennguoidode) hotennguoidode,
                             ba.apgar1phut,
                             ba.apgar5phut,
                             ba.apgar10phut,
                             ba.apgarcannang,
                             ba.tinhtrangdinhduongsausinh,
                             ba.hutdich,
                             ba.datnoikhiquan,
                             ba.xoaboptim,
                             ba.bopbongo2,
                             ba.thoo2,
                             ba.phuongphapkhac,
                             ba.hotennguoichuyensosinh,
                             ba.ditatbamsinh,
                             ba.cohaumon,
                             ba.cutheditat,
                             ba.tinhhinhsosinhkhivaokhoa,
                             ba.cannang,
                             ba.chieucao,
                             ba.vongdau,
                             ba.tinhtrangtoanthan,
                             ba.nhietdo,
                             ba.nhiptho,
                             ba.honghao,
                             ba.mausacda,
                             ba.nhipthohohap,
                             ba.nghephoi,
                             ba.chiso,
                             ba.nhiptim,
                             ba.bung,
                             ba.coquansinhducngoai,
                             ba.xuongkhop,
                             ba.phanxa,
                             ba.truonglucco,
                             ba.cls,
                             ba.tomtatbenhan,
                             ba.chidinhtheodoi,
                             ba.ngay,
                             ba.hotenbacsi,
                             ba.silverman_score,
                             ba.mabacsilambenhan,
                             ba.ngaybslambenhan,
                             ba.makhoa,
                             ba.boxngoaivien,
                             nvl(ba.makhoadode, v_makhoatheodoi) makhoadode,
                             nvl(ba.tinhtrangoi, v_tinhtrangoi) tinhtrangoi,
                             nvl(ba.tinhtrangoi_con, v_tinhtrangoi_con) tinhtrangoi_con,
                             nvl(ba.tinhtrangoi_vo, v_tinhtrangoi_vo) tinhtrangoi_vo,
                             ba.makhoachuyensosinh,
                             concat(concat(cd.mota_chucdanh, '. '), nv.ten_nhanvien) AS nv_lambenhan,
                             CASE
                                 WHEN instr(ba.hotennguoidode, ' - ') > 0 THEN
                                     substr(ba.hotennguoidode, instr(ba.hotennguoidode, ' - ') + 3)
                                 ELSE
                                     ba.hotennguoidode
END AS tennguoidode,
                             CASE
                                 WHEN instr(ba.hotennguoichuyensosinh, ' - ') > 0 THEN
                                     substr(ba.hotennguoichuyensosinh, instr(ba.hotennguoichuyensosinh, ' - ') + 3)
                                 ELSE
                                     ba.hotennguoidode
END AS tennguoichuyensosinh,
                             v_ngay_lambenhan   AS ngay_lambenhan,
                             nvl(JSON_VALUE(ba.silverman_score, '$.BOXGNDIEUHOA' RETURNING VARCHAR2), ' ') AS longngucdieuhoa,
                             nvl(JSON_VALUE(ba.silverman_score, '$.BOXGNCOKEOCO1' RETURNING VARCHAR2), ' ') AS liensuonkhong,
                             nvl(JSON_VALUE(ba.silverman_score, '$.BOXGNCOKEOMUI1' RETURNING VARCHAR2), ' ') AS muiuckhong,
                             nvl(JSON_VALUE(ba.silverman_score, '$.BOXGNDAPCANHMUI1' RETURNING VARCHAR2), ' ') AS dapcanhmuikhong
                             ,
                             nvl(JSON_VALUE(ba.silverman_score, '$.BOXGNRENRI1' RETURNING VARCHAR2), ' ') AS renrikhong,
                             nvl(JSON_VALUE(ba.silverman_score, '$.BOXGNNHIPTHO' RETURNING VARCHAR2), ' ') AS longngucxedichdidongbung
                             ,
                             nvl(JSON_VALUE(ba.silverman_score, '$.BOXGNCOKEOCO2' RETURNING VARCHAR2), ' ') AS liensuoncoit,
                             nvl(JSON_VALUE(ba.silverman_score, '$.BOXGNCOKEOMUI2' RETURNING VARCHAR2), ' ') AS muiuccoit,
                             nvl(JSON_VALUE(ba.silverman_score, '$.BOXGNDAPCANHMUI2' RETURNING VARCHAR2), ' ') AS dapcanhmuinhe,
                             nvl(JSON_VALUE(ba.silverman_score, '$.BOXGNRENRI2' RETURNING VARCHAR2), ' ') AS renringhebangongnghe
                             ,
                             nvl(JSON_VALUE(ba.silverman_score, '$.BOXGNDIDONG' RETURNING VARCHAR2), ' ') AS longnguckhongdidong,
                             nvl(JSON_VALUE(ba.silverman_score, '$.BOXGNCOKEOCO3' RETURNING VARCHAR2), ' ') AS liensuonthayro,
                             nvl(JSON_VALUE(ba.silverman_score, '$.BOXGNCOKEOMUI3' RETURNING VARCHAR2), ' ') AS muiucthayro,
                             nvl(JSON_VALUE(ba.silverman_score, '$.BOXGNDAPCANHMUI3' RETURNING VARCHAR2), ' ') AS dapcanhmuiro,
                             nvl(JSON_VALUE(ba.silverman_score, '$.BOXGNRENRI3' RETURNING VARCHAR2), ' ') AS reritaithuongnghero,
                             v_sobenhan_me      sobenhan_hosome,
                             v_sovaovien_me     sovaovien_me,
                             v_thamso960616 ANCHUKY
                         FROM
                             his_manager.vba_sosinh        ba
                             JOIN his_manager.noitru_vobenhan   cha ON cha.id = ba.id
                             JOIN his_fw.dm_nhanvien            nv ON nv.ma_nhanvien = cha.manv_lambenhan
                             JOIN his_fw.dm_chucdanh_nhanvien   cd ON cd.ma_chucdanh = nv.chucdanh_nhanvien
                         WHERE
                             ba.id = p_id;

END;

    ELSIF p_loaibenhan = 'NOITRUYHCT' or p_loaibenhan = 'NGOAITRUYHCT' or p_loaibenhan = 'NOI'  THEN
BEGIN
            p_sql := 'SELECT BA.*, CONCAT(CONCAT(CD.MOTA_CHUCDANH,''. ''),  NV.TEN_NHANVIEN) AS NV_LAMBENHAN,'
                     || '       :v_ngay_lambenhan AS NGAY_LAMBENHAN'
                     || '       :v_thamso960616 AS ANCHUKY'
                     || ' FROM HIS_MANAGER.VBA_'
                     || p_loaibenhan
                     || ' BA'
                     || ' JOIN HIS_MANAGER.NOITRU_VOBENHAN CHA ON CHA.ID = BA.ID '
                     || ' JOIN HIS_FW.DM_NHANVIEN NV ON NV.MA_NHANVIEN = CHA.MANV_LAMBENHAN '
                     || ' JOIN HIS_FW.DM_CHUCDANH_NHANVIEN CD ON CD.MA_CHUCDANH = NV.CHUCDANH_NHANVIEN '
                     || ' WHERE BA.ID = :P_ID';

OPEN cur FOR p_sql
                USING v_ngay_lambenhan, v_thamso960616, p_id;

EXCEPTION
            WHEN no_data_found THEN
                OPEN cur FOR SELECT
                                 NULL
                             FROM
                                 dual;

END;
ELSIF p_loaibenhan = 'PHATHAI' THEN
    CMU_LOAD_VBA_PHATHAI_TR2(p_id, p_dvtt, cur);
ELSE
BEGIN
            p_sql := 'SELECT BA.*, CONCAT(CONCAT(CD.MOTA_CHUCDANH,''. ''),  NV.TEN_NHANVIEN) AS NV_LAMBENHAN,'
                     || '       :v_ngay_lambenhan AS NGAY_LAMBENHAN'
                     || ' , nvbs.TEN_NHANVIEN_CD TENBACSILAMBENHAN'
                     || ' ,     :v_thamso960616 AS ANCHUKY'
                     || ' FROM HIS_MANAGER.VBA_'
                     || p_loaibenhan
                     || ' BA'
                     || '    JOIN HIS_MANAGER.NOITRU_VOBENHAN CHA ON CHA.ID = BA.ID '
                     || '    JOIN HIS_FW.DM_NHANVIEN NV ON NV.MA_NHANVIEN = CHA.MANV_LAMBENHAN '
                     || '   JOIN HIS_FW.DM_CHUCDANH_NHANVIEN CD ON CD.MA_CHUCDANH = NV.CHUCDANH_NHANVIEN '
                     || ' left join HIS_FW.DM_NHANVIEN_CD nvbs on TO_CHAR(nvbs.MA_NHANVIEN) = TO_CHAR(BA.MABACSILAMBENHAN)'
                     || ' WHERE BA.ID = :P_ID';

OPEN cur FOR p_sql
                USING v_ngay_lambenhan, v_thamso960616, p_id;

EXCEPTION
            WHEN no_data_found THEN
                OPEN cur FOR SELECT
                                 NULL
                             FROM
                                 dual;

END;
END IF;

RETURN cur;
END;