CREATE OR REPLACE FUNCTION cls_ttpt_vltl_huyketqua_f (
    p_dvtt             IN                 VARCHAR2,
    p_noitru           IN                 NUMBER,
    p_sophieu          IN                 VARCHAR2,
    p_ma_kham_benh     IN                 VARCHAR2,
    p_stt_<PERSON>han       IN                 VARCHAR2,
    p_stt_dotdieutri   IN                 VARCHAR2,
    p_stt_dieutri      IN                 VARCHAR2
) RETURN NUMBER IS

    vcount           NUMBER(10) DEFAULT 0;
    v_sovaovien      VARCHAR(255) := '0';
    v_sovaovien_dt   VARCHAR(255) := '0';
    v_thamso960481   VARCHAR2(5) := his_manager.dm_tsdv_sl_mtso(p_dvtt, '960481');
    v_khoa           NUMBER(10) := 0;
BEGIN
    IF p_noitru = 0 THEN
SELECT
    sovaovien
INTO v_sovaovien
FROM
    kb_cd_dichvu
WHERE
    so_phieu_dichvu = p_sophieu
  AND dvtt = p_dvtt
  AND ma_kham_benh = p_ma_kham_benh;

IF v_thamso960481 = 1 THEN
SELECT
    COUNT(1)
INTO v_khoa
FROM
    kb_phieuthanhtoan
WHERE
    dvtt = p_dvtt
  AND ma_kham_benh = p_ma_kham_benh
  AND sovaovien = v_sovaovien
  AND khoa_thanhtoan = 1;

IF v_khoa > 0 THEN
                RETURN -1;
END IF;
END IF;

UPDATE kb_cd_dichvu_ct
SET
    da_chan_doan = 0,
    ket_qua = NULL,
    nguoi_thuc_hien = NULL
WHERE
    so_phieu_dichvu = p_sophieu
  AND dvtt = p_dvtt
  AND ma_kham_benh = p_ma_kham_benh;

UPDATE kb_cd_dichvu
SET
    trang_thai_dichvu = 0
WHERE
    so_phieu_dichvu = p_sophieu
  AND dvtt = p_dvtt
  AND ma_kham_benh = p_ma_kham_benh;

DELETE FROM cmu_xml3_dsbsttpt
WHERE
    dvtt = p_dvtt
  AND sophieu = p_sophieu
  AND sovaovien = v_sovaovien
  AND sovaovien_dt = v_sovaovien_dt;

DELETE FROM cmu_tienpt
WHERE
    dvtt = p_dvtt
  AND sophieu = p_sophieu
  AND sovaovien = v_sovaovien;

vcount := SQL%rowcount;
ELSE
SELECT
    sovaovien,
    sovaovien_dt
INTO
    v_sovaovien,
    v_sovaovien_dt
FROM
    noitru_cd_dichvu_ct
WHERE
    so_phieu_dichvu = p_sophieu
  AND dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri
  AND ROWNUM = 1;

IF v_thamso960481 = 1 THEN
SELECT
    COUNT(1)
INTO v_khoa
FROM
    noitru_phieuthanhtoan
WHERE
    dvtt = p_dvtt
  AND sovaovien = v_sovaovien
  AND sovaovien_dt = v_sovaovien_dt
  AND khoa_thanhtoan = 1;

IF v_khoa > 0 THEN
                RETURN -1;
END IF;
END IF;

UPDATE noitru_cd_dichvu
SET
    trang_thai_dichvu = 0
WHERE
    so_phieu_dichvu = p_sophieu
  AND dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri;

UPDATE noitru_cd_dichvu_ct
SET
    da_chan_doan = 0,
    ket_qua = NULL,
    nguoi_thuc_hien = NULL
WHERE
    so_phieu_dichvu = p_sophieu
  AND dvtt = p_dvtt
  AND stt_benhan = p_stt_benhan
  AND stt_dotdieutri = p_stt_dotdieutri
  AND stt_dieutri = p_stt_dieutri;

DELETE FROM cmu_tienpt
WHERE
    dvtt = p_dvtt
  AND sophieu = p_sophieu
  AND sovaovien = v_sovaovien;

DELETE FROM cmu_xml3_dsbsttpt
WHERE
    dvtt = p_dvtt
  AND sophieu = p_sophieu
  AND sovaovien = v_sovaovien
  AND sovaovien_dt = v_sovaovien_dt;

vcount := SQL%rowcount;
END IF;

DELETE FROM cmu_thoigian_nhanvien
WHERE
    dvtt = p_dvtt
  AND sovaovien = v_sovaovien
  AND noitru = p_noitru
  AND sophieu = p_sophieu;

RETURN vcount;
END;