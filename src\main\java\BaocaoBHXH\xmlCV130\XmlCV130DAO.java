package BaocaoBHXH.xmlCV130;

import BaocaoBHXH.xmlCV130.dto.*;

import java.util.List;
import java.util.Map;

/**
 * Created at 09:19:52 February 22, 2023,
 */
public interface XmlCV130DAO {

    List<CheckIn> getXmlCheckInCV130List(XuatXmlCv130Option option);

    List<Xml1> getXml1CV130List(XuatXmlCv130Option option);

    List<Xml2> getXml2CV130List(XuatXmlCv130Option option);

    List<Xml3> getXml3CV130List(XuatXmlCv130Option option);

    List<Xml4> getXml4CV130List(XuatXmlCv130Option option);

    List<Xml5> getXml5CV130List(XuatXmlCv130Option option);

    List<Xml6> getXml6CV130List(XuatXmlCv130Option option);

    List<Xml7> getXml7CV130List(XuatXmlCv130Option option);

    List<Xml8> getXml8CV130List(XuatXmlCv130Option option);

    List<Xml9> getXml9CV130List(XuatXmlCv130Option option);

    List<Xml10> getXml10CV130List(XuatXmlCv130Option option);

    List<Xml11> getXml11CV130List(XuatXmlCv130Option option);

    List<Xml12> getXml12CV130List(XuatXmlCv130Option option);

    List<Xml13> getXml13CV130List(XuatXmlCv130Option option);

    List<Xml14> getXml14CV130List(XuatXmlCv130Option option);

    List<Xml15> getXml15CV130List(XuatXmlCv130Option option);

    public List layDsBangMapMaTTDV(String dvtt);

    public List layDsDaMapMaTTDV(String dvtt);

    public String themMapMaTTDV(MapMaTTDVObj map);

    public int xoaMapMaTTDV(String dvtt, int stt, String nhanvienthuchien, String ngaygiothuchien);

    Map getSignedFileXML130(String loaiXML, XuatXmlCv130Option option);

    public String layMaDonViMoi(String sovaovien, String sovaovien_dt, String dvtt, String loai_kcb);

}
