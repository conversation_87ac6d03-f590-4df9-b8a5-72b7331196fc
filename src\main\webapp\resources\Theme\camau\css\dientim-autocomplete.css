/* CSS cho tính năng autocomplete của trường "Lời dặn bác sĩ" */

/* Styling cho textarea có autocomplete */
textarea[list] {
    position: relative;
}

/* Styling cho datalist options */
datalist {
    display: none;
}

/* Custom styling cho textarea khi có gợi ý */
textarea[name="data[LOIDANBACSI]"] {
    border: 2px solid #e3e6f0;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    line-height: 1.5;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

textarea[name="data[LOIDANBACSI]"]:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Placeholder styling */
textarea[name="data[LOIDANBACSI]"]::placeholder {
    color: #6c757d;
    opacity: 0.8;
    font-style: italic;
}

/* Thêm icon gợi ý */
.formio-component-LOIDANBACSI {
    position: relative;
}

.formio-component-LOIDANBACSI::after {
    content: "💡";
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    opacity: 0.6;
    font-size: 16px;
}

/* Responsive design */
@media (max-width: 768px) {
    textarea[name="data[LOIDANBACSI]"] {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Animation cho khi có gợi ý */
@keyframes suggestion-highlight {
    0% { background-color: transparent; }
    50% { background-color: #fff3cd; }
    100% { background-color: transparent; }
}

textarea[name="data[LOIDANBACSI]"].has-suggestions {
    animation: suggestion-highlight 2s ease-in-out;
}

/* Tooltip cho hướng dẫn sử dụng */
.loidanbacsi-tooltip {
    position: relative;
    display: inline-block;
}

.loidanbacsi-tooltip .tooltiptext {
    visibility: hidden;
    width: 300px;
    background-color: #555;
    color: white;
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -150px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.loidanbacsi-tooltip .tooltiptext::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #555 transparent transparent transparent;
}

.loidanbacsi-tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}
