<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rp_vobenhan_trang_1_2_PHATHAI_full" language="groovy" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" whenResourceMissingType="Empty" uuid="0c77309d-bb14-4cd7-8eb5-4a8b75b128de">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="361"/>
	<style name="s_center" hAlign="Center"/>
	<queryString>
		<![CDATA[select 1 from dual]]>
	</queryString>
	<field name="TEN_BENH_NHAN" class="java.lang.String"/>
	<field name="NGAYSINH" class="java.lang.String"/>
	<field name="TEN_DANTOC" class="java.lang.String"/>
	<field name="MA_DANTOC_4069_1" class="java.lang.String"/>
	<field name="MA_DANTOC_4069_2" class="java.lang.String"/>
	<field name="TEN_NGHE_NGHIEP" class="java.lang.String"/>
	<field name="MA_NGHE_NGHIEP_4069_1" class="java.lang.String"/>
	<field name="MA_NGHE_NGHIEP_4069_2" class="java.lang.String"/>
	<field name="NGOAI_KIEU" class="java.lang.String"/>
	<field name="THONPHO" class="java.lang.String"/>
	<field name="SONHA" class="java.lang.String"/>
	<field name="TEN_PHUONG_XA" class="java.lang.String"/>
	<field name="TEN_QUAN_HUYEN" class="java.lang.String"/>
	<field name="MA_QUAN_HUYEN_1" class="java.lang.String"/>
	<field name="MA_QUAN_HUYEN_2" class="java.lang.String"/>
	<field name="MA_QUAN_HUYEN_3" class="java.lang.String"/>
	<field name="TEN_TINH_THANH" class="java.lang.String"/>
	<field name="MA_TINH_THANH_1" class="java.lang.String"/>
	<field name="MA_TINH_THANH_2" class="java.lang.String"/>
	<field name="NOILAMVIEC" class="java.lang.String"/>
	<field name="SO_DIEN_THOAI_BN" class="java.lang.String"/>
	<field name="NGUOI_LIEN_HE" class="java.lang.String"/>
	<field name="SO_DIEN_THOAI" class="java.lang.String"/>
	<field name="NGAYNHAPVIEN_VARCHAR" class="java.lang.String"/>
	<field name="NOIGIOITHIEU" class="java.lang.String"/>
	<field name="NOIGIOITHIEU_RP" class="java.lang.String"/>
	<field name="CHANDOAN_NOIGIOITHIEU" class="java.lang.String"/>
	<field name="TEN_ICD_NHAPVIEN" class="java.lang.String"/>
	<field name="LYDOPHATHAI" class="java.lang.String"/>
	<field name="PARA" class="java.lang.String"/>
	<field name="SOCONHIENCO" class="java.lang.String"/>
	<field name="TRAI" class="java.lang.String"/>
	<field name="GAI" class="java.lang.String"/>
	<field name="DAPTLAYTHAI" class="java.lang.String"/>
	<field name="NAMPTLANCUOI" class="java.lang.String"/>
	<field name="CACPTTCKHAC" class="java.lang.String"/>
	<field name="NAMCACPTTCKHAC" class="java.lang.String"/>
	<field name="SOLANDAPHATHAI" class="java.lang.String"/>
	<field name="THANGPHAGANHAT" class="java.lang.String"/>
	<field name="NAMPHAGANHAT" class="java.lang.String"/>
	<field name="BIENPHAPTTLANNAY" class="java.lang.String"/>
	<field name="TIENSUBENH" class="java.lang.String"/>
	<field name="MACH" class="java.lang.String"/>
	<field name="NHIETDO" class="java.lang.String"/>
	<field name="NHIPTHO" class="java.lang.String"/>
	<field name="HUYETAP" class="java.lang.String"/>
	<field name="CANNANG" class="java.lang.String"/>
	<field name="KHAMTOANTHAN" class="java.lang.String"/>
	<field name="CACBOPHAN" class="java.lang.String"/>
	<field name="NGAYDAUKYKINHCUOI" class="java.lang.String"/>
	<field name="AMHO" class="java.lang.String"/>
	<field name="AMDAO" class="java.lang.String"/>
	<field name="COTUCUNG" class="java.lang.String"/>
	<field name="TUCUNG" class="java.lang.String"/>
	<field name="PHANPHUPHAI" class="java.lang.String"/>
	<field name="PHANPHUTRAI" class="java.lang.String"/>
	<field name="CACXNCANLAM" class="java.lang.String"/>
	<field name="TUOITHAI" class="java.lang.String"/>
	<field name="KLPPPHATHAI" class="java.lang.String"/>
	<field name="NGAYLAMBENHAN" class="java.lang.String"/>
	<field name="TENBACSILAMBENHAN" class="java.lang.String"/>
	<field name="NV_LAMBENHAN" class="java.lang.String"/>
	<group name="rp_vobenhan_trang_1_2_PHATHAI_full">
		<groupExpression><![CDATA[]]></groupExpression>
		<groupHeader>
			<band height="780" splitType="Immediate">
				<staticText>
					<reportElement positionType="Float" x="0" y="0" width="555" height="35" uuid="03298917-0d83-4f7b-bcdc-b979e1d8a261"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="16" isBold="true"/>
					</textElement>
					<text><![CDATA[BỆNH ÁN PHÁ THAI]]></text>
				</staticText>
				<staticText>
					<reportElement positionType="Float" x="0" y="40" width="555" height="15" uuid="7a498fd2-6945-41bf-8a88-d5ab03a2fecf"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="12" isBold="true"/>
					</textElement>
					<text><![CDATA[I. HÀNH CHÍNH:]]></text>
				</staticText>
				<frame>
					<reportElement x="0" y="60" width="555" height="195" uuid="bbc071e2-a327-4ef1-be89-1f0088949c07"/>
					<frame>
						<reportElement positionType="Float" x="0" y="0" width="555" height="15" uuid="600b4703-cb89-43d4-9838-33a9d1535ac7"/>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="0" y="0" width="310" height="15" uuid="84304898-37ff-4665-9c0c-349a2d784967"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["1. Họ và tên (chữ in hoa): " + $F{TEN_BENH_NHAN}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="315" y="0" width="240" height="15" uuid="6d78027c-efad-46bd-96a4-a6d39fd3c5f1"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["2. " + (
$F{NGAYSINH} == null ?
"Ngày ...... Tháng ...... Năm sinh ......" :
"Ngày " + $F{NGAYSINH}.split("/")[0] + 
" Tháng " + $F{NGAYSINH}.split("/")[1] + 
" Năm sinh " + $F{NGAYSINH}.split("/")[2]
)]]></textFieldExpression>
						</textField>
					</frame>
					<frame>
						<reportElement positionType="Float" x="0" y="20" width="555" height="15" uuid="f4a8b4f0-5ac3-43e2-8908-7967c1fdec2a"/>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="0" y="0" width="270" height="15" uuid="4b7c2941-22fe-47f5-a90e-f2d9614bcb5d"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["3. Dân tộc: " + $F{TEN_DANTOC}]]></textFieldExpression>
						</textField>
						<textField pattern="" isBlankWhenNull="true">
							<reportElement positionType="Float" mode="Transparent" x="295" y="0" width="15" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="1b30aa56-2971-4fbe-90b6-8050efb2b029"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
								<paragraph lineSpacing="Single"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{MA_DANTOC_4069_2}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="275" y="0" width="15" height="15" uuid="e4b91bce-0b61-410e-a472-947bdeb5bed8"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{MA_DANTOC_4069_1}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="315" y="0" width="200" height="15" uuid="5037c755-ff7d-4fc4-ba43-78a1d61fa5ee"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["4. Nghề nghiệp: " + $F{TEN_NGHE_NGHIEP}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="520" y="0" width="15" height="15" uuid="57cecefb-d8a9-44e5-a7ef-74fdc991bc25"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{MA_NGHE_NGHIEP_4069_1}]]></textFieldExpression>
						</textField>
						<textField pattern="" isBlankWhenNull="true">
							<reportElement positionType="Float" mode="Transparent" x="540" y="0" width="15" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="1d98236c-e1e1-4ae2-be57-09f46992e199"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
								<paragraph lineSpacing="Single"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{MA_NGHE_NGHIEP_4069_2}]]></textFieldExpression>
						</textField>
					</frame>
					<frame>
						<reportElement positionType="Float" x="0" y="40" width="555" height="15" uuid="9f61c134-837e-487c-92e0-92d42e517309"/>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="0" y="0" width="150" height="15" uuid="1ad72187-dfe1-4765-9509-d13c379c8a56"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["5. Quốc tịch: " + $F{NGOAI_KIEU}]]></textFieldExpression>
						</textField>
						<textField pattern="" isBlankWhenNull="true">
							<reportElement positionType="Float" mode="Transparent" x="175" y="0" width="15" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="a4f84418-5491-4db1-bd26-38566e263f20"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
								<paragraph lineSpacing="Single"/>
							</textElement>
							<textFieldExpression><![CDATA[""]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="155" y="0" width="15" height="15" uuid="463ae874-043f-48ae-950d-a39d4e6168f1"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[""]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="350" y="0" width="205" height="15" uuid="01d9d146-cc5e-4d48-8386-bd9ee7a50fd4"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
								<paragraph leftIndent="5"/>
							</textElement>
							<textFieldExpression><![CDATA["Thôn " + $F{THONPHO}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="195" y="0" width="150" height="15" uuid="f37e03ab-40a3-4368-bf69-ea0a300b887f"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
								<paragraph leftIndent="5"/>
							</textElement>
							<textFieldExpression><![CDATA["6. Địa chỉ: Số nhà " + $F{SONHA}]]></textFieldExpression>
						</textField>
					</frame>
					<frame>
						<reportElement positionType="Float" x="0" y="60" width="555" height="15" uuid="ba2bb445-1ff6-44d5-872d-97c9d78ff743"/>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="0" y="0" width="225" height="15" uuid="51df6598-aea8-4e5f-b2b6-db04d8af8177"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["Phố " + $F{THONPHO}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="230" y="0" width="325" height="15" uuid="b3d0990b-7e17-461c-8c27-64fb30abd1e5"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["Xã/phường " + $F{TEN_PHUONG_XA}]]></textFieldExpression>
						</textField>
					</frame>
					<frame>
						<reportElement positionType="Float" x="0" y="80" width="555" height="15" uuid="cb275684-5f8b-4747-9713-aae2f7996ed3"/>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="0" y="0" width="225" height="15" uuid="93a3456a-529f-4d59-a492-df56fcc3dfc2"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["Huyện (Q, Tx): " + $F{TEN_QUAN_HUYEN}]]></textFieldExpression>
						</textField>
						<textField pattern="" isBlankWhenNull="true">
							<reportElement positionType="Float" mode="Transparent" x="270" y="0" width="15" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="d0348755-2c07-44d2-90aa-709cb3f064df"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
								<paragraph lineSpacing="Single"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{MA_QUAN_HUYEN_3}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="250" y="0" width="15" height="15" uuid="f7629676-5510-4f11-adac-bf9738f7b438"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{MA_QUAN_HUYEN_2}]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="290" y="0" width="225" height="15" uuid="298fa390-d190-441a-b7f4-885f800e54b2"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["Tỉnh/thành phố: " + $F{TEN_TINH_THANH}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="230" y="0" width="15" height="15" uuid="94c46ed1-b90b-440c-a042-9b6101b97c0f"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{MA_QUAN_HUYEN_1}]]></textFieldExpression>
						</textField>
						<textField pattern="" isBlankWhenNull="true">
							<reportElement positionType="Float" mode="Transparent" x="540" y="0" width="15" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="6bbf64f0-fc28-4413-8bd7-42c8dc77fc54"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
								<paragraph lineSpacing="Single"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{MA_TINH_THANH_2}]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="520" y="0" width="15" height="15" uuid="63a62e23-96bf-42ac-8615-7b9ffec50ea9"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{MA_TINH_THANH_1}]]></textFieldExpression>
						</textField>
					</frame>
					<frame>
						<reportElement positionType="Float" x="0" y="100" width="555" height="15" uuid="8a82b606-51ab-4abd-9168-8e7b7f3b1263"/>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="0" y="0" width="310" height="15" uuid="a2638e75-b0dc-4101-9119-cee00aa6ae3b"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["7. Nơi làm việc: " + (
$F{NOILAMVIEC} == null ?
"" :
$F{NOILAMVIEC}
)]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="315" y="0" width="240" height="15" uuid="e43c5fbb-c212-4c1e-9f2a-6bfb3781a559"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["Số điện thoại liên lạc: " + (
$F{SO_DIEN_THOAI_BN} == null ?
"" :
$F{SO_DIEN_THOAI_BN}
)]]></textFieldExpression>
						</textField>
					</frame>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement positionType="Float" x="0" y="120" width="555" height="15" uuid="07ce3d87-3b00-4dc0-9945-8a63dd35cf0f"/>
						<box>
							<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["8. Họ và tên, địa chỉ người nhà khi cần báo tin: " + (
$F{NGUOI_LIEN_HE} == null ?
"......" :
$F{NGUOI_LIEN_HE}
) + " Số điện thoại " + (
$F{SO_DIEN_THOAI} == null ?
"" :
$F{SO_DIEN_THOAI}
)]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true">
						<reportElement positionType="Float" x="0" y="140" width="555" height="15" uuid="c56f1421-c4e7-401f-b82b-d24d0d96c2c6"/>
						<textElement verticalAlignment="Middle" markup="none">
							<font fontName="Times New Roman" size="12"/>
						</textElement>
						<textFieldExpression><![CDATA["9. Đến khám/vào viện hồi: " + (
($F{NGAYNHAPVIEN_VARCHAR} != null && !$F{NGAYNHAPVIEN_VARCHAR}.isEmpty()) ? 
$F{NGAYNHAPVIEN_VARCHAR} : 
" .......... giờ ....... phút, ngày ....... tháng ........ năm ..........."
)]]></textFieldExpression>
					</textField>
					<frame>
						<reportElement positionType="Float" x="0" y="160" width="555" height="15" uuid="096a388f-62f4-4bea-b95e-08fb331c9923"/>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="0" y="0" width="400" height="15" uuid="a51d97a2-4b9a-4c5a-af6e-27834c625365"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["10. Nơi giới thiệu: " + (
$F{NOIGIOITHIEU} == null ?
"" :
$F{NOIGIOITHIEU}
)]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement positionType="Float" x="405" y="0" width="50" height="15" uuid="2c6a3857-56d9-41c7-a707-6cd78a5b28bc"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isItalic="false"/>
							</textElement>
							<text><![CDATA[10a.Y tế]]></text>
						</staticText>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="455" y="0" width="15" height="15" uuid="11d70488-cce5-42d3-aa2f-406aef4bfe1e"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{NOIGIOITHIEU_RP}.equals("1")?"X":""]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement positionType="Float" x="475" y="0" width="65" height="15" uuid="3e9b73f6-a554-4860-b7d7-4e72ea9953d4"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isItalic="false"/>
							</textElement>
							<text><![CDATA[10b. Tự đến]]></text>
						</staticText>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="540" y="0" width="15" height="15" uuid="b95921cd-5216-4a5d-8825-9832120d2c67"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{NOIGIOITHIEU_RP}.equals("2")?"X":""]]></textFieldExpression>
						</textField>
					</frame>
					<textField isStretchWithOverflow="true">
						<reportElement positionType="Float" x="0" y="180" width="555" height="15" uuid="83846d26-6e88-4ae2-b991-fa997e35d17c"/>
						<textElement verticalAlignment="Middle" markup="none">
							<font fontName="Times New Roman" size="12"/>
						</textElement>
						<textFieldExpression><![CDATA["11. Chẩn đoán nơi giới thiệu: "+ (($F{CHANDOAN_NOIGIOITHIEU} != null && !$F{TEN_ICD_NHAPVIEN}.isEmpty())?$F{CHANDOAN_NOIGIOITHIEU}:"")]]></textFieldExpression>
					</textField>
				</frame>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" x="0" y="260" width="555" height="15" uuid="721c6b31-3a57-4c87-8e44-830a14d15993"/>
					<box>
						<bottomPen lineWidth="0.0" lineStyle="Dotted"/>
					</box>
					<textElement verticalAlignment="Middle" markup="styled">
						<font fontName="Times New Roman" size="12" isBold="false" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA["<b>II. LÝ DO PHÁ THAI:</b> " + (
($F{LYDOPHATHAI} != null && !$F{LYDOPHATHAI}.isEmpty()) ?
$F{LYDOPHATHAI} :
""
)]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement positionType="Float" x="0" y="280" width="555" height="15" uuid="58fdb7bb-d3ec-4465-825c-24da8aa13b71"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="12" isBold="true"/>
					</textElement>
					<text><![CDATA[III. TIỀN SỬ]]></text>
				</staticText>
				<frame>
					<reportElement positionType="Float" x="0" y="300" width="555" height="95" uuid="4208a863-9776-44e4-9d2b-f13d6a229452"/>
					<frame>
						<reportElement positionType="Float" x="0" y="0" width="555" height="15" uuid="10a87efa-0dc1-4657-bc4b-51c3a2a1157d"/>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="0" y="0" width="245" height="15" uuid="64b73dea-d5e8-43a3-9942-b82bf531de8e"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["1. Tiền sử sản phụ khoa: PARA: " + (
$F{PARA} == null ? 
"" :
$F{PARA} + ";"
)]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="250" y="0" width="145" height="15" uuid="64e0e19b-06db-4f48-b1f5-54092ab7b9bf"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["Số con hiện có: " + (
$F{SOCONHIENCO} == null ? 
"" :
$F{SOCONHIENCO}
)]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="400" y="0" width="75" height="15" uuid="c6fb24ac-235b-4526-98e4-c1e14f113a1a"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["Trai " + (
$F{TRAI} == null ? 
"" :
$F{TRAI} + ";"
)]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="480" y="0" width="75" height="15" uuid="1484eef2-fcba-4256-af83-4bc386b032d2"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["Gái " + (
$F{GAI} == null ? 
"" :
$F{GAI}
)]]></textFieldExpression>
						</textField>
					</frame>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement positionType="Float" x="0" y="20" width="555" height="15" uuid="5b17d390-5eb1-48c9-bbe9-818a06d6f36f"/>
						<box>
							<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
							<paragraph leftIndent="3"/>
						</textElement>
						<textFieldExpression><![CDATA["Đã phẫu thuật lấy thai " + (
$F{DAPTLAYTHAI} == null ? 
"      " :
$F{DAPTLAYTHAI}
) + " lần; Năm phẫu thuật lần cuối " + (
$F{NAMPTLANCUOI} == null ? 
"      " :
$F{NAMPTLANCUOI}
) + "; Các phẫu thuật TC khác: " + (
$F{CACPTTCKHAC} == null ? 
"      " :
$F{CACPTTCKHAC}
) + " năm " + (
$F{NAMCACPTTCKHAC} == null ? 
"      " :
$F{NAMCACPTTCKHAC}
) + " Số lần đã phá thai " + (
$F{SOLANDAPHATHAI} == null ? 
"      " :
$F{SOLANDAPHATHAI}
) + " lần; Lần phá thai gần nhất: tháng " + (
$F{THANGPHAGANHAT} == null ? 
"      " :
$F{THANGPHAGANHAT}
) + " năm " + (
$F{NAMPHAGANHAT} == null ? 
"      " :
$F{NAMPHAGANHAT}
)]]></textFieldExpression>
					</textField>
					<frame>
						<reportElement positionType="Float" x="0" y="40" width="555" height="15" uuid="66989cc4-90a7-4c24-a2fa-7b21f9345ae8"/>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="0" y="0" width="235" height="15" uuid="33301ac5-0bf7-4b62-966a-1434113ae088"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
								<paragraph leftIndent="3"/>
							</textElement>
							<textFieldExpression><![CDATA["Biện pháp TT đang sử dụng khi có thai lần này: "]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement positionType="Float" x="235" y="0" width="50" height="15" uuid="6102b64c-df80-4f42-8fbd-52d18a6bcf18"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isItalic="false"/>
							</textElement>
							<text><![CDATA[1. DCTC]]></text>
						</staticText>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="285" y="0" width="15" height="15" uuid="8ab6cac4-adcf-4ddd-9cd2-82c5ea8cc730"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{BIENPHAPTTLANNAY}.equals("1")?"X":""]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="370" y="0" width="15" height="15" uuid="409c24f6-35ba-4733-ad86-74188ac3fe87"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{BIENPHAPTTLANNAY}.equals("2")?"X":""]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement positionType="Float" x="305" y="0" width="65" height="15" uuid="f96275e2-65e2-4674-b71f-bc14c2218f87"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isItalic="false"/>
							</textElement>
							<text><![CDATA[2.Thuốc tiêm]]></text>
						</staticText>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="460" y="0" width="15" height="15" uuid="775c7c65-26f5-4ab3-b420-5a202dc0fb81"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{BIENPHAPTTLANNAY}.equals("3")?"X":""]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement positionType="Float" x="390" y="0" width="70" height="15" uuid="430b8f72-79c8-4bd8-9a03-b3f7a564f2bd"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isItalic="false"/>
							</textElement>
							<text><![CDATA[3.Thuốc uống]]></text>
						</staticText>
						<staticText>
							<reportElement positionType="Float" x="480" y="0" width="60" height="15" uuid="809d07c0-77fa-4875-aa1b-2a95d93eada0"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isItalic="false"/>
							</textElement>
							<text><![CDATA[4. Que cấy]]></text>
						</staticText>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="540" y="0" width="15" height="15" uuid="3cca5b4e-308c-45cd-9857-9ed4957880ad"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{BIENPHAPTTLANNAY}.equals("4")?"X":""]]></textFieldExpression>
						</textField>
					</frame>
					<frame>
						<reportElement positionType="Float" x="0" y="60" width="555" height="15" uuid="ad093028-6eb0-46bc-89b3-83c67c28e686"/>
						<staticText>
							<reportElement positionType="Float" x="0" y="0" width="75" height="15" uuid="c37b16e1-7998-4bc7-b929-b978fbd90281"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isItalic="false"/>
								<paragraph leftIndent="3"/>
							</textElement>
							<text><![CDATA[5. Bao cao su]]></text>
						</staticText>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="75" y="0" width="15" height="15" uuid="9b900d33-558f-4643-bd9b-23844ed44510"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{BIENPHAPTTLANNAY}.equals("5")?"X":""]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="235" y="0" width="15" height="15" uuid="86826604-22b8-4253-b0f1-5f73d3202610"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{BIENPHAPTTLANNAY}.equals("6")?"X":""]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement positionType="Float" x="95" y="0" width="140" height="15" uuid="8fe8ff60-7d9d-42c4-8dff-739db8c1b167"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isItalic="false"/>
							</textElement>
							<text><![CDATA[6. Thuốc tránh thai khẩn cấp]]></text>
						</staticText>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="350" y="0" width="15" height="15" uuid="d2b7b36c-5b13-4c4e-94fd-d196e0f20fa0"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{BIENPHAPTTLANNAY}.equals("7")?"X":""]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement positionType="Float" x="255" y="0" width="95" height="15" uuid="f8bb15c0-6bfd-4e4f-bfe6-0b205412bc67"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isItalic="false"/>
							</textElement>
							<text><![CDATA[7. Biện pháp khác]]></text>
						</staticText>
						<staticText>
							<reportElement positionType="Float" x="370" y="0" width="170" height="15" uuid="868225ee-3b0e-40cf-8f19-ceb7a578b958"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isItalic="false"/>
							</textElement>
							<text><![CDATA[8.Không sử dụng biện pháp nào]]></text>
						</staticText>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="540" y="0" width="15" height="15" uuid="0b048fef-6db4-408a-918f-719fb59547ad"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{BIENPHAPTTLANNAY}.equals("8")?"X":""]]></textFieldExpression>
						</textField>
					</frame>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement positionType="Float" x="0" y="80" width="555" height="15" uuid="07c615f4-4d69-4fd9-b773-b75f4be8e88e"/>
						<box>
							<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["2. Tiền sử bệnh (bệnh tám thần, tuần hoàn, hô hấp, tiêu hóa, tiết niệu, dị ứng) " + (
$F{TIENSUBENH} == null ?
"" :
$F{TIENSUBENH}
)]]></textFieldExpression>
					</textField>
				</frame>
				<frame>
					<reportElement positionType="Float" x="0" y="400" width="555" height="115" uuid="506e941a-9c33-4e00-b8cd-8a2f821b4e4f"/>
					<frame>
						<reportElement positionType="Float" x="0" y="0" width="390" height="15" uuid="bacb6eca-22c2-494f-954b-d8564869fab3"/>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="0" y="0" width="125" height="15" uuid="23a58f07-7328-48fc-aa60-26ddaf997f29"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA["3. Tình trạng hôn nhân: "]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement positionType="Float" x="125" y="0" width="50" height="15" uuid="cff91350-7f1d-41da-8de9-3809d013f794"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isItalic="false"/>
							</textElement>
							<text><![CDATA[Có chồng]]></text>
						</staticText>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="175" y="0" width="15" height="15" uuid="8dea9cfc-2c62-43ee-8453-e79620951473"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{BIENPHAPTTLANNAY}.equals("1")?"X":""]]></textFieldExpression>
						</textField>
						<textField isBlankWhenNull="true">
							<reportElement positionType="Float" x="315" y="0" width="15" height="15" uuid="9961d3b4-4368-479a-8c68-d505ec60ec27"/>
							<box>
								<pen lineWidth="0.5"/>
								<topPen lineWidth="0.5"/>
								<leftPen lineWidth="0.5"/>
								<bottomPen lineWidth="0.5"/>
								<rightPen lineWidth="0.5"/>
							</box>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{BIENPHAPTTLANNAY}.equals("2")?"X":""]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement positionType="Float" x="235" y="0" width="80" height="15" uuid="d234e846-d77b-4ec9-83e3-f8fc08919cdd"/>
							<textElement verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isItalic="false"/>
							</textElement>
							<text><![CDATA[Không có chồng]]></text>
						</staticText>
					</frame>
					<staticText>
						<reportElement positionType="Float" x="0" y="20" width="390" height="15" uuid="f4acec0a-1ad2-4cc8-8f3b-de1b3c54ad1d"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="true"/>
						</textElement>
						<text><![CDATA[IV. KHÁM BỆNH]]></text>
					</staticText>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement positionType="Float" x="0" y="40" width="390" height="15" uuid="9affadcc-ec71-4080-a23f-f57c5835ee3f"/>
						<box>
							<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["1. Toàn thân: " + (
$F{KHAMTOANTHAN} == null ?
"" :
$F{KHAMTOANTHAN}
)]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement positionType="Float" x="0" y="60" width="390" height="15" uuid="7429f113-be43-46c9-a754-a155091be291"/>
						<box>
							<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["2. Các bộ phận: " + (
$F{CACBOPHAN} == null ?
"" :
$F{CACBOPHAN}
)]]></textFieldExpression>
					</textField>
					<frame>
						<reportElement positionType="Float" x="395" y="0" width="160" height="115" uuid="70173dab-db9b-4006-ab9d-2b8ec1da5338"/>
						<box>
							<topPen lineWidth="1.0"/>
							<leftPen lineWidth="1.0"/>
							<bottomPen lineWidth="1.0"/>
							<rightPen lineWidth="1.0"/>
						</box>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="0" y="96" width="160" height="15" uuid="cbaea2ea-40f3-4975-b6a0-87636865e7cb"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle" markup="styled">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
								<paragraph leftIndent="3"/>
							</textElement>
							<textFieldExpression><![CDATA["Cân nặng: " + (
$F{CANNANG} == null ?
"      " :
$F{CANNANG}
) + " kg"]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="0" y="76" width="160" height="15" uuid="c88e6a7e-1523-426c-bd5a-6c8c3aea853b"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle" markup="styled">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
								<paragraph leftIndent="3"/>
							</textElement>
							<textFieldExpression><![CDATA["Nhịp thở: " + (
$F{NHIPTHO} == null ?
"      " :
$F{NHIPTHO}
) + " lần/ph"]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="0" y="56" width="160" height="15" uuid="dbb589d3-b5c2-4566-8c11-cd9f023c0cd3"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle" markup="styled">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
								<paragraph leftIndent="3"/>
							</textElement>
							<textFieldExpression><![CDATA["Huyết áp: " + (
$F{HUYETAP} == null ?
"      " :
$F{HUYETAP}
) + " mmHg"]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="0" y="36" width="160" height="15" uuid="790627bc-62a9-46cb-b6ca-593412efb5ac"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle" markup="styled">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
								<paragraph leftIndent="3"/>
							</textElement>
							<textFieldExpression><![CDATA["Nhiệt độ: " + (
$F{NHIETDO} == null ?
"      " :
$F{NHIETDO}
) + " <sup>o</sup>C"]]></textFieldExpression>
						</textField>
						<textField isStretchWithOverflow="true" isBlankWhenNull="true">
							<reportElement positionType="Float" x="0" y="16" width="160" height="15" uuid="aa05a806-47fd-4de7-8243-979a9995ff3d"/>
							<box>
								<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
							</box>
							<textElement verticalAlignment="Middle" markup="styled">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="true"/>
								<paragraph leftIndent="3"/>
							</textElement>
							<textFieldExpression><![CDATA["Mạch: " + (
$F{MACH} == null ?
"      " :
$F{MACH}
) + " lần/phút"]]></textFieldExpression>
						</textField>
					</frame>
				</frame>
				<frame>
					<reportElement positionType="Float" x="0" y="520" width="555" height="95" uuid="6458041d-c423-43fb-8712-3724f4dc0575"/>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement positionType="Float" x="0" y="0" width="555" height="15" uuid="f0591fc3-ef9b-49ee-a2ef-33551f256955"/>
						<box>
							<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["3. Khám phụ khoa"]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement positionType="Float" x="0" y="20" width="555" height="15" uuid="5cbe7a53-f9f5-4700-baa9-3f5e9276e7fa"/>
						<box>
							<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Ngày đầu kỳ kinh cuối cùng " + (
$F{NGAYDAUKYKINHCUOI} == null ?
"      /      /      " :
$F{NGAYDAUKYKINHCUOI}
) + " (Dương lịch)"]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement positionType="Float" x="0" y="40" width="555" height="15" uuid="c6ef48f1-c526-48ac-8f48-b51251b3b911"/>
						<box>
							<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Âm hộ " + (
$F{AMHO} == null ?
"      " :
$F{AMHO}
) + "; Âm đạo " + (
$F{AMDAO} == null ?
"      " :
$F{AMDAO}
) + "; Cổ tử cung " + (
$F{COTUCUNG} == null ?
"      " :
$F{COTUCUNG}
)]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement positionType="Float" x="0" y="60" width="555" height="15" uuid="82cd6204-3c2d-4b71-8b97-6c4c655bd870"/>
						<box>
							<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Tử cung (kích thước, mật độ, tư thế) " + (
$F{TUCUNG} == null ?
"" :
$F{TUCUNG}
)]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement positionType="Float" x="0" y="80" width="555" height="15" uuid="40ede6d4-53ee-44ba-baa7-aa2ec64c66fa"/>
						<box>
							<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Phần phụ phải " + (
$F{PHANPHUPHAI} == null ?
"" :
$F{PHANPHUPHAI}
) + "; Phần phụ trái " + (
$F{PHANPHUTRAI} == null ?
"" :
$F{PHANPHUTRAI}
)]]></textFieldExpression>
					</textField>
				</frame>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement positionType="Float" x="0" y="620" width="555" height="15" uuid="a12ca7e7-259d-46aa-bc23-e420d8b736f1"/>
					<box>
						<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Times New Roman" size="12" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA["4. Các xét nghiệm cần làm " + (
$F{CACXNCANLAM} == null ?
"" :
$F{CACXNCANLAM}
)]]></textFieldExpression>
				</textField>
				<frame>
					<reportElement positionType="Float" x="0" y="640" width="555" height="35" uuid="69893bb5-f941-46df-939b-ef31ae8af482"/>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement positionType="Float" x="90" y="0" width="465" height="15" uuid="7fd1ef22-3961-462a-8a57-2a8d3ea840b0"/>
						<box>
							<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["Chẩn đoán: Tuổi thai " + (
$F{TUOITHAI} == null ?
"      " :
$F{TUOITHAI}
) + " tuần"]]></textFieldExpression>
					</textField>
					<textField isStretchWithOverflow="true" isBlankWhenNull="true">
						<reportElement positionType="Float" x="0" y="20" width="555" height="15" uuid="987f1ef9-37f5-4391-9399-1ca0c5f81eeb"/>
						<box>
							<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
						</box>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="false"/>
						</textElement>
						<textFieldExpression><![CDATA["+ Phương pháp phá thai " + (
$F{KLPPPHATHAI} == null ?
"" :
$F{KLPPPHATHAI}
)]]></textFieldExpression>
					</textField>
					<staticText>
						<reportElement positionType="Float" x="0" y="0" width="90" height="15" uuid="a23857d8-062c-4bf6-a0a6-9970e176dc29"/>
						<textElement verticalAlignment="Middle">
							<font fontName="Times New Roman" size="12" isBold="true"/>
						</textElement>
						<text><![CDATA[V. KẾT LUẬN:]]></text>
					</staticText>
				</frame>
				<frame>
					<reportElement positionType="Float" x="0" y="680" width="555" height="100" uuid="996d0f8b-8649-4d79-9d08-bf8b724832cb"/>
					<box>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<frame>
						<reportElement x="0" y="0" width="280" height="100" uuid="186f2df5-73a0-4ba1-96bd-6548e7c871b9"/>
						<box>
							<rightPen lineWidth="0.0"/>
						</box>
					</frame>
					<frame>
						<reportElement x="280" y="0" width="275" height="100" uuid="cbb17c51-5d42-44ff-b8c9-7334a756897b"/>
						<textField isBlankWhenNull="true">
							<reportElement x="0" y="85" width="275" height="15" uuid="f937b3a8-027d-41e0-afaf-11175c6c423b"/>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12"/>
							</textElement>
							<textFieldExpression><![CDATA[(
$F{TENBACSILAMBENHAN} == null ?
$F{NV_LAMBENHAN} :
$F{TENBACSILAMBENHAN}
)]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement x="0" y="15" width="275" height="15" uuid="9a73af87-cb07-4490-bc80-9ce31abf6464"/>
							<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
								<font fontName="Times New Roman" size="12" isBold="true" isItalic="false"/>
							</textElement>
							<text><![CDATA[Người làm bệnh án]]></text>
						</staticText>
						<textField isBlankWhenNull="true">
							<reportElement x="0" y="0" width="275" height="15" uuid="8d462b56-4bff-48f8-80c3-116875a3d582"/>
							<box leftPadding="0"/>
							<textElement textAlignment="Center" verticalAlignment="Middle">
								<font fontName="Times New Roman" size="12" isBold="false"/>
							</textElement>
							<textFieldExpression><![CDATA[$F{NGAYLAMBENHAN}]]></textFieldExpression>
						</textField>
						<staticText>
							<reportElement x="0" y="30" width="275" height="15" uuid="1ea43f89-e84d-4831-b8bb-2579251eb2e2"/>
							<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
								<font fontName="Times New Roman" size="12" isBold="false" isItalic="false"/>
							</textElement>
							<text><![CDATA[(Ký tên, ghi rõ họ tên)]]></text>
						</staticText>
					</frame>
				</frame>
			</band>
		</groupHeader>
	</group>
</jasperReport>
