<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rp_vobenhan_trang_2" language="groovy" pageWidth="595" pageHeight="842" columnWidth="580" leftMargin="10" rightMargin="5" topMargin="10" bottomMargin="10" uuid="513ef620-30fb-4b13-96b1-94f754855041">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="759"/>
	<subDataset name="dataset1" uuid="7aa11af8-2d37-4137-b19e-c09052de7e0d"/>
	<queryString>
		<![CDATA[select 1 from dual]]>
	</queryString>
	<field name="ID" class="java.math.BigDecimal"/>
	<field name="DVTT" class="java.lang.String"/>
	<field name="LYDOVAOVIEN" class="java.lang.String"/>
	<field name="quatrinhbenhly" class="java.lang.String"/>
	<field name="TIENSUBANTHAN" class="java.lang.String"/>
	<field name="TIENSUGIADINH" class="java.lang.String"/>
	<field name="KHAMTOANTHAN" class="java.lang.String"/>
	<field name="MACH" class="java.lang.String"/>
	<field name="NHIETDO" class="java.lang.String"/>
	<field name="HUYETAPTREN" class="java.lang.String"/>
	<field name="HUYETAPDUOI" class="java.lang.String"/>
	<field name="NHIPTHO" class="java.lang.String"/>
	<field name="CANNANG" class="java.lang.String"/>
	<field name="CHIEUCAO" class="java.lang.String"/>
	<field name="BMI" class="java.lang.String"/>
	<field name="TUANHOAN" class="java.lang.String"/>
	<field name="HOHAP" class="java.lang.String"/>
	<field name="TIEUHOA" class="java.lang.String"/>
	<field name="xuongkhop" class="java.lang.String"/>
	<field name="THANKINH" class="java.lang.String"/>
	<field name="XUONGKHOP" class="java.lang.String"/>
	<field name="thantietnieu" class="java.lang.String"/>
	<field name="khac" class="java.lang.String"/>
	<field name="xetnghiem" class="java.lang.String"/>
	<field name="TOMTATBA" class="java.lang.String"/>
	<field name="BENHCHINH" class="java.lang.String"/>
	<field name="BENHPHU" class="java.lang.String"/>
	<field name="PHANBIET" class="java.lang.String"/>
	<field name="TIENLUONG" class="java.lang.String"/>
	<field name="HUONGDIEUTRI" class="java.lang.String"/>
	<field name="batdaukinhnam" class="java.lang.String"/>
	<field name="tuoicokinh" class="java.lang.String"/>
	<field name="tinhchatkinhnguyet" class="java.lang.String"/>
	<field name="chuky" class="java.lang.String"/>
	<field name="songaythaykinh" class="java.lang.String"/>
	<field name="luongkinh" class="java.lang.String"/>
	<field name="kinhlancuoingay" class="java.lang.String"/>
	<field name="boxdaubung" class="java.lang.String"/>
	<field name="boxtruoc" class="java.lang.String"/>
	<field name="boxtrong" class="java.lang.String"/>
	<field name="boxsau" class="java.lang.String"/>
	<field name="laychongnam" class="java.lang.String"/>
	<field name="tuoilaychong" class="java.lang.String"/>
	<field name="hetkinhnam" class="java.lang.String"/>
	<field name="tuoihetkinh" class="java.lang.String"/>
	<field name="benhphukhoa" class="java.lang.String"/>
	<field name="boxtienthaisinh" class="java.lang.String"/>
	<field name="boxtienthaisom" class="java.lang.String"/>
	<field name="boxtienthaisay" class="java.lang.String"/>
	<field name="boxtienthaisong" class="java.lang.String"/>
	<field name="danienmac" class="java.lang.String"/>
	<field name="hach" class="java.lang.String"/>
	<field name="vu" class="java.lang.String"/>
	<field name="cacdauhieu" class="java.lang.String"/>
	<field name="moilon" class="java.lang.String"/>
	<field name="moibe" class="java.lang.String"/>
	<field name="amvat" class="java.lang.String"/>
	<field name="amhot" class="java.lang.String"/>
	<field name="mangtrinh" class="java.lang.String"/>
	<field name="tangsinhmom" class="java.lang.String"/>
	<field name="amdao" class="java.lang.String"/>
	<field name="cotucung" class="java.lang.String"/>
	<field name="thantucung" class="java.lang.String"/>
	<field name="phanphu" class="java.lang.String"/>
	<field name="cactuicung" class="java.lang.String"/>
	<field name="BOXTIENTHAISINH" class="java.lang.String"/>
	<field name="BOXTIENTHAISOM" class="java.lang.String"/>
	<field name="BOXTIENTHAISAY" class="java.lang.String"/>
	<field name="BOXTIENTHAISONG" class="java.lang.String"/>
	<field name="BACSILAMBENHAN" class="java.lang.String"/>
	<field name="NGAYLAMBENHAN" class="java.lang.String"/>
	<field name="HETKINHNAM" class="java.lang.String"/>
	<field name="TUOIHETKINH" class="java.lang.String"/>
	<field name="TOMTATBENHAN" class="java.lang.String"/>
	<field name="DANIENMAC" class="java.lang.String"/>
	<field name="TENBACSILAMBENHAN" class="java.lang.String"/>
	<field name="ANCHUKY" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="603">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="113" y="182" width="467" height="15" uuid="3091da06-c3b6-4907-bfd0-07b726f9700c"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[((($F{batdaukinhnam}!=null)?$F{batdaukinhnam}:"") +" tuổi: "  + (($F{tuoicokinh}!=null)?$F{tuoicokinh}:""))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="113" y="197" width="467" height="15" uuid="7bd0fe17-20ca-4b41-a15b-8f8663cf2933"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tinhchatkinhnguyet}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="0" y="182" width="113" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="7eb4cf62-cf43-4373-92a7-061e7403f510"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[- Bắt đầu thấy kinh năm:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="0" y="197" width="113" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="17ac99dd-87f9-4072-a124-51f6728852af"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[- Tính chất kinh nguyệt:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="137" width="580" height="30" isPrintWhenDetailOverflows="true" uuid="8234dd4e-88ae-4304-9d69-5a6d8eb80a09"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["+ Gia đình: "+(($F{TIENSUGIADINH} != null && !$F{TIENSUGIADINH}.isEmpty())?$F{TIENSUGIADINH}:"")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="38" width="580" height="14" uuid="4c5fc3fa-2ef8-4cce-989d-ac9089e4f8e0"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[II. HỎI BỆNH]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" isPrintRepeatedValues="false" mode="Transparent" x="0" y="167" width="580" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="1f4152da-c9fc-49e0-aefe-a1be0a288716"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA["3. Tiền sử phụ khoa:"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="107" width="580" height="30" isPrintWhenDetailOverflows="true" uuid="eaa23349-d5f1-4184-9987-627db61ef275"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["+ Bản thân: "+"<style isItalic='true'>(những bệnh đã mắc, dị ứng, thói quen ăn uống, sinh hoạt, thuốc lá, rượu bia, ma tuý, khác  ...): </style>"+(($F{TIENSUBANTHAN} != null && !$F{TIENSUBANTHAN}.isEmpty())?$F{TIENSUBANTHAN}:"")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="580" height="14" uuid="8192e256-0a4e-43d0-957c-6e2571b194e9"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[A. BỆNH ÁN]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="13" width="580" height="25" uuid="885ed7ed-c3b5-4a35-8ba1-7388a8546caa"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dotted"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA["<style fontSize='12' isBold='true'>I. LÝ DO VÀO VIỆN: </style>"+(($F{LYDOVAOVIEN} != null && !$F{LYDOVAOVIEN}.isEmpty())?$F{LYDOVAOVIEN}:"")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="92" width="580" height="15" uuid="ec09fad5-1857-4d11-bbdb-1361ee412320"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["2. Tiền sử bệnh"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="52" width="580" height="40" isPrintWhenDetailOverflows="true" uuid="e0c62483-e0e7-49f1-92b3-4d2cafd44687"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["<style isBold='true'>1. Quá trình bệnh lý: </style>"+ ($F{quatrinhbenhly} != null ? $F{quatrinhbenhly} : "")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="212" width="180" height="15" uuid="376b97ec-2a99-4706-83ee-d96004d270a4"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[" Chu kỳ: " +(($F{chuky}!=null)?$F{chuky}:"") +" ngày. "]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="328" y="228" width="15" height="15" uuid="7e265bc4-e962-4e79-ae77-4e0c2fa6148c"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid"/>
					<leftPen lineWidth="0.75" lineStyle="Solid"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid"/>
					<rightPen lineWidth="0.75" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{boxdaubung}.equals("1")?"X":""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="274" y="228" width="53" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="129124f5-d0f1-46a1-9db0-c69dc6600954"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Đau bụng:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="259" width="580" height="25" uuid="111326ee-50a1-4dfb-a5cb-0e70963f0d1e"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Những bệnh phụ khoa đã điều trị :"+(($F{benhphukhoa} != null && !$F{benhphukhoa}.isEmpty())?$F{benhphukhoa}:"")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="0" y="244" width="114" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="4b25c9c3-a72a-465a-a720-c8dfe97091bf"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[- Lấy chồng năm:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="458" y="228" width="43" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="77325e76-14cd-4c6b-b9bd-302c5c9e0227"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[2. Trong]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="114" y="228" width="160" height="15" uuid="6652497b-0943-4397-b413-************"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{kinhlancuoingay}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="440" y="228" width="15" height="15" uuid="f87e5636-e777-4fa5-ac33-5fb35b0859b3"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid"/>
					<leftPen lineWidth="0.75" lineStyle="Solid"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid"/>
					<rightPen lineWidth="0.75" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{boxtruoc}.equals("1")?"X":""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="114" y="244" width="160" height="15" uuid="26f6f324-7cdb-4034-a0ce-4ca3585dd26a"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[((($F{laychongnam}!=null)?$F{laychongnam}:"") +"  tuổi: " + (($F{tuoilaychong}!=null)?$F{tuoilaychong}:""))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="501" y="228" width="15" height="15" uuid="5b7b9fa2-6efe-4416-9bc8-77ddbe436044"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid"/>
					<leftPen lineWidth="0.75" lineStyle="Solid"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid"/>
					<rightPen lineWidth="0.75" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{boxtrong}.equals("1")?"X":""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="340" y="212" width="240" height="15" uuid="f972ef92-13f2-47b5-b28a-642f1c3107af"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[" lượng kinh: " + (($F{luongkinh}!=null)?$F{luongkinh}:"") +" ngày. "]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="522" y="228" width="38" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="d766f963-099f-46a6-8eff-622c460bfa30"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[3. Sau]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="180" y="212" width="160" height="15" uuid="61fcf3fe-23a4-44dc-a2e7-5fbf5bc99031"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[" Số ngày thấy kinh: " +(($F{songaythaykinh}!=null)?$F{songaythaykinh}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="560" y="228" width="15" height="15" uuid="026dc045-aded-4abf-8683-cf5bcab1b99e"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid"/>
					<leftPen lineWidth="0.75" lineStyle="Solid"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid"/>
					<rightPen lineWidth="0.75" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{boxsau}.equals("1")?"X":""]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="274" y="244" width="66" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="14926f23-cce4-4755-ac58-2c4fba2f81f0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Hết kinh năm:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="0" y="228" width="114" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="55c2edd2-9852-4be7-89ec-11b54440abc6"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[- Kinh lần cuối ngày]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="347" y="228" width="50" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="a0547464-96bf-4421-817d-553326d3525a"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Thời gian:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="340" y="244" width="240" height="15" uuid="64229f5b-b68a-4e15-8224-f8ef8236877e"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[((($F{HETKINHNAM}!=null)?$F{HETKINHNAM}:"") +"  tuổi: " + (($F{TUOIHETKINH}!=null)?$F{TUOIHETKINH}:""))]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="397" y="228" width="42" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="28720ed0-46f6-4c4e-a529-629a7ddcd211"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[1. Trước]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="142" y="284" width="15" height="15" uuid="d42b3d90-6744-4133-9e9e-688e8dec74d1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[S]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="125" y="284" width="15" height="15" uuid="7d5ea3f0-2c39-487b-8c38-605fb6e9e40e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[S]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="159" y="300" width="15" height="15" uuid="1ae9e6e4-dea8-4e76-b85f-613a5d9fad67"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid"/>
					<leftPen lineWidth="0.75" lineStyle="Solid"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid"/>
					<rightPen lineWidth="0.75" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BOXTIENTHAISONG}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="159" y="284" width="15" height="15" uuid="21580cf5-93a8-42c8-a802-a4aa890d6ef8"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[S]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="0" y="284" width="108" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="3e84410d-f86a-47fb-bec6-11eaa3f414ff"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[4. Tiền sử sản khoa:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="0" y="299" width="108" height="15" uuid="face6592-48ab-49cd-a004-4d10c642f280"/>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="false"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[- Tiền thai (Para)]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="108" y="300" width="15" height="15" uuid="1c27a33d-9f53-4d97-9bb3-6a44f36888cc"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid"/>
					<leftPen lineWidth="0.75" lineStyle="Solid"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid"/>
					<rightPen lineWidth="0.75" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BOXTIENTHAISINH}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="142" y="300" width="15" height="15" uuid="ba941db6-1250-4bf3-85ed-6ce87425bd1b"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid"/>
					<leftPen lineWidth="0.75" lineStyle="Solid"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid"/>
					<rightPen lineWidth="0.75" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BOXTIENTHAISAY}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="108" y="284" width="15" height="15" uuid="26d9925a-086f-4278-947e-4721d6cb3847"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<text><![CDATA[S]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="174" y="300" width="406" height="15" uuid="c2c79fde-b97c-468d-8f5a-02f18b1eaed2"/>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false"/>
					<paragraph leftIndent="3"/>
				</textElement>
				<text><![CDATA[(Sinh (đủ tháng), Sớm (đẻ non), Sẩy (nạo, hút), Sống)]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="125" y="300" width="15" height="15" uuid="20e995c7-2cac-4f00-8d96-e93194419014"/>
				<box>
					<topPen lineWidth="0.75" lineStyle="Solid"/>
					<leftPen lineWidth="0.75" lineStyle="Solid"/>
					<bottomPen lineWidth="0.75" lineStyle="Solid"/>
					<rightPen lineWidth="0.75" lineStyle="Solid"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{BOXTIENTHAISOM}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement positionType="Float" x="399" y="331" width="181" height="80" uuid="f1c2ea52-410f-4afd-b2f4-71a7686b9d93"/>
				<graphicElement>
					<pen lineWidth="0.75"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="400" y="333" width="50" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="3d69a2fa-3d66-4f32-ac84-6410539cde40"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Mạch:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="400" y="348" width="50" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="43a060ec-14a5-47a9-aa8c-2872c83df2c4"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Nhiệt độ:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="400" y="363" width="50" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="ab12754a-ed8f-47e6-a86c-fe1c0e0bf3ec"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Huyết áp:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="400" y="378" width="50" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="3b519fad-1d37-485f-b635-044df2819c19"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Nhịp thở:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="400" y="393" width="50" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="b292c44e-ad71-4617-bbb9-b70a7b9ac568"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[Cân nặng:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="538" y="363" width="40" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="9bc7acae-792a-41cd-a1a0-06008aadfe72"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[mmHg]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="538" y="378" width="40" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="f5552c93-cecb-4ac8-854a-f15112a1c751"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[lần/ph]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="538" y="393" width="40" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="8e9fdc0a-2b0c-4ee4-8a47-a96429caf512"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[kg]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="558" y="348" width="20" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="11771278-a8d8-4448-95a7-c6fee66f6279"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[C]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="538" y="333" width="40" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="bcf0eb67-3d75-48ab-a35e-2c223a8bb36d"/>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[lần/ph]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="546" y="346" width="12" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="aa8d9ffa-39b9-460c-bbf7-239c6d1635e3"/>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="10" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[o]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" mode="Transparent" x="493" y="363" width="10" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="e4e0c8eb-23d2-48c1-907f-f525dd1ef30b"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<text><![CDATA[/]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="450" y="333" width="88" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="3eeec22d-0555-4ffa-a144-e0ed7bc5896a"/>
				<box rightPadding="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{MACH}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="450" y="348" width="88" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="c4c2f8b7-74ab-4444-8254-89701ff22a42"/>
				<box rightPadding="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NHIETDO}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="450" y="363" width="43" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="330860f6-2b41-4bf9-b825-28680d4bd166"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HUYETAPTREN}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="503" y="363" width="35" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="23dcd89d-3095-4d75-935d-b15937fa27f4"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{HUYETAPDUOI}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="450" y="378" width="88" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="30f03415-d7f1-44d0-bb85-079f0937470b"/>
				<box rightPadding="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NHIPTHO}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="Float" mode="Transparent" x="450" y="393" width="88" height="15" forecolor="#000000" backcolor="#FFFFFF" uuid="9133c180-fe21-444a-852b-21a9df2dd86e"/>
				<box rightPadding="10"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{CANNANG}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="316" width="580" height="15" uuid="db7e86f3-219e-4274-8a10-3c8509adee4a"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[III. KHÁM BỆNH:]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="0" y="331" width="397" height="30" uuid="f5dd96d0-f23f-46df-93bc-832311d8a8e5"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["<b>1. Toàn thân:</b> <i>da niêm mạc</i>" + (($F{DANIENMAC} != null && !$F{DANIENMAC}.isEmpty())?$F{DANIENMAC}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="360" width="397" height="25" uuid="d6064272-d104-4998-9db0-664c43bc2523"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Hạch: "+(($F{hach} != null && !$F{hach}.isEmpty())?$F{hach}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="385" width="397" height="25" uuid="0b71e3b2-ea19-4d73-b135-063bd81bd429"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Vú: "+(($F{vu} != null && !$F{vu}.isEmpty())?$F{vu}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="452" width="580" height="25" uuid="274abb28-e5df-4ffc-8b80-9b76950b7b35"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["+ Hô hấp: "+(($F{HOHAP} != null && !$F{HOHAP}.isEmpty())?$F{HOHAP}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="527" width="580" height="25" uuid="5a6032b1-c68f-4a15-9da7-840485def7bd"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["+ Cơ xương khớp: "+(($F{XUONGKHOP} != null && !$F{XUONGKHOP}.isEmpty())?$F{XUONGKHOP}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="501" width="580" height="25" uuid="7fbaa066-b2a7-4bb7-b03c-11e921707461"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["+ Thần kinh: "+(($F{THANKINH} != null && !$F{THANKINH}.isEmpty())?$F{THANKINH}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="476" width="580" height="25" uuid="4f9c850b-84e5-43c8-bda4-f7a92a8ef837"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["+ Tiêu hóa: "+(($F{TIEUHOA} != null && !$F{TIEUHOA}.isEmpty())?$F{TIEUHOA}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="578" width="580" height="25" uuid="c0854526-43e8-4820-ab5e-11bd5c87658a"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["+ Khác: "+(($F{khac} != null && !$F{khac}.isEmpty())?$F{khac}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="426" width="580" height="25" uuid="c5217b4e-c48c-44ad-88b1-9a0d8699550c"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["+ Tuần hoàn: "+(($F{TUANHOAN} != null && !$F{TUANHOAN}.isEmpty())?$F{TUANHOAN}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="0" y="553" width="580" height="25" uuid="8df2b3fb-de90-4d81-aa78-17a854416d1c"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["+ Thận - Tiết niệu - Sinh dục: "+(($F{thantietnieu} != null && !$F{thantietnieu}.isEmpty())?$F{thantietnieu}:"")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="411" width="580" height="15" uuid="51a2fbb4-10ef-4d28-9c3d-e844e7496ab2"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[2. Các cơ quan:]]></text>
			</staticText>
		</band>
	</title>
	<detail>
		<band height="171">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="111" width="580" height="20" uuid="9f81c2f2-dd5c-41dd-a961-************"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Âm hộ: "+(($F{amhot} != null && !$F{amhot}.isEmpty())?$F{amhot}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="91" width="580" height="20" uuid="ccb92fc6-931c-4351-8f63-2f45c923b1f0"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Âm vật: "+(($F{amvat} != null && !$F{amvat}.isEmpty())?$F{amvat}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="131" width="580" height="20" uuid="9bfb3253-7ba4-4546-8bf8-7d3316e3b9c9"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Màng trinh: "+(($F{mangtrinh} != null && !$F{mangtrinh}.isEmpty())?$F{mangtrinh}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="71" width="580" height="20" uuid="8aed7501-60cd-47bf-a458-d43c3b903360"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Môi bé: "+(($F{moibe} != null && !$F{moibe}.isEmpty())?$F{moibe}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="31" width="580" height="20" uuid="04bc4c78-e96a-43c3-9c9f-bde6a113772d"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Các dấu hiệu sinh dục thứ phát: "+(($F{cacdauhieu} != null && !$F{cacdauhieu}.isEmpty())?$F{cacdauhieu}:"")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="16" width="580" height="15" uuid="fa5671a5-257c-43d3-9017-164d8ca849f1"/>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[a. Khám ngoài:]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="51" width="580" height="20" uuid="902e318d-3376-4b9f-b5fa-936155010df3"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Môi lớn: "+(($F{moilon} != null && !$F{moilon}.isEmpty())?$F{moilon}:"")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="1" width="580" height="15" uuid="66948d3f-702f-4eab-9ab3-a0daf0617a1d"/>
				<textElement verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[3. Khám chuyên khoa]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="151" width="580" height="20" uuid="e9815114-46df-4342-a541-884e68eb1962"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Tầng sinh môn: "+(($F{tangsinhmom} != null && !$F{tangsinhmom}.isEmpty())?$F{tangsinhmom}:"")]]></textFieldExpression>
			</textField>
		</band>
		<band height="169">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="115" width="580" height="25" isPrintWhenDetailOverflows="true" uuid="b0b45a7c-665e-49ce-918d-65fb2ec9fd12"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Các túi cùng: "+(($F{cactuicung} != null && !$F{cactuicung}.isEmpty())?$F{cactuicung}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="65" width="580" height="25" isPrintWhenDetailOverflows="true" uuid="e07b732e-69df-485b-80a6-9fc470b7a3be"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Thân tử cung: "+(($F{thantucung} != null && !$F{thantucung}.isEmpty())?$F{thantucung}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="90" width="580" height="25" isPrintWhenDetailOverflows="true" uuid="d5ff6256-cfe8-4ce1-9719-a8dd1eff4834"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Phần phụ: "+(($F{phanphu} != null && !$F{phanphu}.isEmpty())?$F{phanphu}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="40" width="580" height="25" isPrintWhenDetailOverflows="true" uuid="d147b64a-cab5-4e9f-86ea-7813e14fc3ab"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Cổ tử cung: "+(($F{cotucung} != null && !$F{cotucung}.isEmpty())?$F{cotucung}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="15" width="580" height="25" isPrintWhenDetailOverflows="true" uuid="f3be52d2-d3c0-4783-8dfd-0b2f5c541b84"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["- Âm đạo: "+(($F{amdao} != null && !$F{amdao}.isEmpty())?$F{amdao}:"")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="0" width="580" height="15" uuid="2cff1209-3983-41ab-8e3d-663dfe67133d"/>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[b. Khám trong]]></text>
			</staticText>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="140" width="580" height="29" isPrintWhenDetailOverflows="true" uuid="2aba369c-3211-4f99-8022-d569716f890a"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["<style isBold='true'>4. Các xét nghiệm cận lâm sàng cần làm: </style>"+(($F{xetnghiem} != null && !$F{xetnghiem}.isEmpty())?$F{xetnghiem}:"")]]></textFieldExpression>
			</textField>
		</band>
		<band height="30">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" isPrintRepeatedValues="false" x="0" y="0" width="580" height="30" isPrintWhenDetailOverflows="true" uuid="9e8d90c8-cfcd-4150-b1a9-6791b11b5ef5"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["<style isBold='true'>5. Tóm tắt bệnh án:  </style>"+(($F{TOMTATBENHAN} != null && !$F{TOMTATBENHAN}.isEmpty())?$F{TOMTATBENHAN}:"")]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>
		<band height="225">
			<staticText>
				<reportElement positionType="Float" x="281" y="137" width="299" height="15" uuid="b9d02a8a-04ce-46c5-8d0f-179d0d1adefe"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Bác sĩ làm bệnh án]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="281" y="152" width="299" height="15" uuid="a758da51-d168-496a-bb32-8915d247c387"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Times New Roman" size="11" isBold="false" isItalic="true"/>
				</textElement>
				<text><![CDATA[(Ký, ghi rõ họ tên)]]></text>
			</staticText>
			<textField>
				<reportElement positionType="Float" x="280" y="122" width="299" height="15" uuid="4e01138b-9303-48d2-904c-715310fe4b0a"/>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{NGAYLAMBENHAN} != null ? $F{NGAYLAMBENHAN} : ""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="281" y="210" width="299" height="15" isPrintWhenDetailOverflows="true" uuid="ea3dcb0e-0c71-479e-8f42-9ec0a14493d3">
					<printWhenExpression><![CDATA[$F{ANCHUKY}.equals("0")]]></printWhenExpression>
				</reportElement>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dotted"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled">
					<font fontName="Times New Roman" size="11" isBold="false" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA["<i>Họ và tên: </i>"+ ($F{TENBACSILAMBENHAN} != null ? $F{TENBACSILAMBENHAN} : "")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="47" width="580" height="20" uuid="ee5e2b32-2392-4cea-aa9a-93f7302e5e17"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["+ Phân biệt: "+(($F{PHANBIET} != null && !$F{PHANBIET}.isEmpty())?$F{PHANBIET}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="15" width="580" height="12" uuid="ea36c503-ccd7-426f-80a7-994631654e0f"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["+ Bệnh chính: "+(($F{BENHCHINH} != null && !$F{BENHCHINH}.isEmpty())?$F{BENHCHINH}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="0" y="27" width="580" height="20" uuid="e63a50fc-19ef-46dd-86f8-b4e424c07804"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dashed"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA["+ Bệnh kèm theo (nếu có): "+(($F{BENHPHU} != null && !$F{BENHPHU}.isEmpty())?$F{BENHPHU}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" isPrintRepeatedValues="false" x="0" y="67" width="580" height="25" uuid="fff5e9ff-d56a-48e6-9f50-7c21ace4f735"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dotted"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA["<style fontSize='12' isBold='true'>V. TIÊN LƯỢNG: </style>"+(($F{TIENLUONG} != null && !$F{TIENLUONG}.isEmpty())?$F{TIENLUONG}:"")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" isPrintRepeatedValues="false" x="0" y="92" width="580" height="30" uuid="d95191be-71a1-47c7-b6c4-583a03184a7e"/>
				<box>
					<bottomPen lineWidth="0.0" lineStyle="Dotted"/>
				</box>
				<textElement verticalAlignment="Top" markup="styled">
					<font fontName="Times New Roman" size="10" isBold="false" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA["<style fontSize='12' isBold='true'>VI. HƯỚNG ĐIỀU TRỊ: </style>"+(($F{HUONGDIEUTRI} != null && !$F{HUONGDIEUTRI}.isEmpty())?$F{HUONGDIEUTRI}:"")]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="Float" x="0" y="1" width="580" height="14" uuid="743f85f0-ce66-475e-9b13-aad036e45eda"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[IV. Chẩn đoán khi vào khoa điều tri:]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
